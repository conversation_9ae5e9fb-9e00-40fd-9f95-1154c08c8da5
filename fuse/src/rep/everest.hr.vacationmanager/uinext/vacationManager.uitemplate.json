{"version": 2, "uimodel": {"nodes": {}}, "uiview": {"title": "Vacation Manager", "config": {"autoRefreshData": true, "stretch": true}, "i18n": "everest.python/python", "sections": [{"component": "H1", "content": "Vacation Manager", "size": "12"}, {"component": "Segments", "size": "12", "segments": [{"title": "Team Report", "sections": [{"component": "<PERSON><PERSON><PERSON>", "section": {"grid": {"size": 12}}, "props": {"src": "/api/apps/everest.hr.vacationmanager/py/team-report.streamlit?embed=true", "height": 2200}}]}, {"title": "Date Report", "sections": [{"component": "<PERSON><PERSON><PERSON>", "section": {"grid": {"size": 12}}, "props": {"src": "/api/apps/everest.hr.vacationmanager/py/date-report.streamlit?embed=true", "height": 2000}}]}, {"title": "Employee Report", "sections": [{"component": "<PERSON><PERSON><PERSON>", "section": {"grid": {"size": 12}}, "props": {"src": "/api/apps/everest.hr.vacationmanager/py/employee-report.streamlit?embed=true", "height": 2200}}]}, {"title": "Vacation Risk", "sections": [{"component": "<PERSON><PERSON><PERSON>", "section": {"grid": {"size": 12}}, "props": {"src": "/api/apps/everest.hr.vacationmanager/py/vacation-risk.streamlit?embed=true", "height": 2200}}]}, {"title": "Monthly Report", "sections": [{"component": "<PERSON><PERSON><PERSON>", "section": {"grid": {"size": 12}}, "props": {"src": "/api/apps/everest.hr.vacationmanager/py/monthly-report.streamlit?embed=true", "height": 2200}}]}]}]}}