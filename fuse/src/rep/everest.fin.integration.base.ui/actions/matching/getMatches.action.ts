import type { ISession } from '@everestsystems/content-core';
import type {
  FormattedMatch,
  GroupingMatch,
} from '@pkg/everest.fin.integration.base.ui/public/types/MatchingTypes';
import { configurationConnectivity } from '@pkg/everest.fin.integration.base.ui/types/interfaces/configurationConnectivity';
import type { EvstExtractionProviders } from '@pkg/everest.fin.integration.base/types/enums/ExtractionProviders';
import { Staging } from '@pkg/everest.fin.integration.base/types/Staging';

import getMatchingPrimitiveTypes from './getMatchingPrimitiveTypes.action';
import getRequiredMatchingFields from './getRequiredMatchingFields.action';
import { groupMatches } from './groupMatches';

export default async function getMatches(
  env: ISession,
  everestModel: string,
  providerName: EvstExtractionProviders,
  providerModel: string,
  requiredFieldsForAiMatching?: string[]
): Promise<GroupingMatch[]> {
  const client = await Staging.client(env);

  let matchingPrimitiveTypes = {};
  try {
    matchingPrimitiveTypes = await getMatchingPrimitiveTypes(
      env,
      providerName,
      providerModel
    );
  } catch {
    matchingPrimitiveTypes = {};
  }
  const multiMatchEverestModels =
    matchingPrimitiveTypes && typeof matchingPrimitiveTypes === 'object'
      ? Object.values(matchingPrimitiveTypes).map((type) => type.modelUrn)
      : [];

  const matches = await client.getMatchesInStaging({
    everestModel,
    providerModel,
    providerName,
    multiMatchEverestModels,
  });

  const requiredFields = getRequiredMatchingFields(
    env,
    matches,
    requiredFieldsForAiMatching
  );

  // This is where the sorting happens in formatMatchesV2
  const additionalMatchingFields =
    (await configurationConnectivity.formatMatches(
      env.connectivityLayer,
      { providerName },
      providerModel,
      matches
    )) as FormattedMatch[];

  // Create a map of requiredFields for easier lookup
  const requiredFieldsMap = new Map(
    requiredFields.map((item) => [item.id, item])
  );

  // Use the sorted additionalMatchingFields order, enriched with requiredFields data
  const formattedMatches = additionalMatchingFields.map((item) => {
    const matchingRequiredField = requiredFieldsMap.get(item.id);
    return {
      ...matchingRequiredField,
      ...item,
    };
  });

  const matchingModelUsingProviderModel = new Set(
    Object.keys(matchingPrimitiveTypes)
      .filter(
        (row) =>
          row !== 'everestId' && matchingPrimitiveTypes[row].providerModel
      )
      .map((row) => matchingPrimitiveTypes[row].providerModel)
  );

  const groupedMatches = groupMatches(
    formattedMatches,
    matchingModelUsingProviderModel.size > 0
  );

  return groupedMatches;
}
