import type { ISession } from '@everestsystems/content-core';
import { ConnectorEngine } from '@pkg/everest.connectorengine/types/ConnectorEngine';
import { ConnectorStatus } from '@pkg/everest.fin.integration.base.ui/public/enums/ConnectorStatus';
import type { GetConnector } from '@pkg/everest.fin.integration.base.ui/public/types/GetConnector';
import {
  getSelectedEntity,
  getSessionId,
} from '@pkg/everest.fin.integration.base.ui/public/utils/sessionVariable/sessionVariable';
import { EvstExtractionProviders } from '@pkg/everest.fin.integration.base/types/enums/ExtractionProviders';

export default async function getConnector(
  env: ISession,
  providerName: EvstExtractionProviders
): Promise<GetConnector> {
  let packageName = '';
  switch (providerName) {
    case EvstExtractionProviders.BillDotCom: {
      packageName = 'everest.fin.integration.billdotcom';
      break;
    }
    case EvstExtractionProviders.Airbase: {
      packageName = 'everest.fin.integration.airbase';
      break;
    }
    case EvstExtractionProviders.Gusto: {
      packageName = 'everest.fin.integration.gusto';
      break;
    }
    case EvstExtractionProviders.Quickbooks: {
      packageName = 'everest.fin.integration.quickbooks';
      break;
    }
    case EvstExtractionProviders.NetSuite: {
      packageName = 'everest.migration.netsuite';
      break;
    }
    case EvstExtractionProviders.Rippling: {
      packageName = 'everest.fin.integration.rippling';
      break;
    }
    case EvstExtractionProviders.Ruddr: {
      packageName = 'everest.fin.integration.ruddr';
      break;
    }
    case EvstExtractionProviders.Brex: {
      packageName = 'everest.fin.integration.brex';
      break;
    }
    case EvstExtractionProviders.NavanCSV:
    case EvstExtractionProviders.Intacct:
    case EvstExtractionProviders.Lexware: {
      return;
    }
    case EvstExtractionProviders.Divvy: {
      packageName = 'everest.fin.integration.divvy';
      break;
    }
    case EvstExtractionProviders.Expensify: {
      packageName = 'everest.fin.integration.expensify';
      break;
    }
    default: {
      throw new Error(`Unsupported provider: ${providerName}`);
    }
  }

  const connectors = await ConnectorEngine.query(
    env,
    { where: { package: packageName } },
    [
      'id',
      'name',
      'configValues',
      'extraData',
      'createdDate',
      'createdBy',
      'Connector-User.fullName',
      'Connector-IntegrationSession.id',
      'Connector-IntegrationSession.SessionVariables-IntegrationSession.configValue',
    ]
  );

  if (connectors.length > 1) {
    throw new Error(ConnectorStatus.MultipleConnector);
  } else if (connectors.length === 0) {
    return;
  }

  const [connector] = connectors;

  const userName = connector['Connector-User']?.fullName;

  const [sessionId, entity] = await Promise.all([
    getSessionId(env, packageName),
    getSelectedEntity(env, packageName),
  ]);

  const entityId = entity?.id;
  const entityName = entity?.entityName;

  return {
    id: connector.id,
    name: connector.name,
    createdDate: connector.createdDate,
    configValues: connector.configValues as Record<string, unknown>,
    extraData: connector.extraData as Record<string, unknown>,
    userName,
    sessionId,
    entityId,
    entityName,
  };
}
