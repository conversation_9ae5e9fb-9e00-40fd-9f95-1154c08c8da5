import type { CustomConfig } from '@pkg/everest.fin.integration.base.ui/public/types/CustomConfig';
import type { SyncOrder } from '@pkg/everest.fin.integration.base.ui/public/types/SyncOrder';
import type { UploadAlertValues } from '@pkg/everest.fin.integration.base.ui/public/types/UploadAlertValues';

export type OverviewConfig = {
  /** Integration title to be displayed at the top of the overview template */
  title: string;
  /** Test sync before executing it */
  testConnector?: boolean;
  /**
   *  Defines whether the connector is locked to one entity. For that to work
   *  with the templates, the 'entityId' must be stored as a session variable
   *  upon connector creation.
   */
  hasOneEntityPerConnector?: boolean;
  /** Link to template that opens when clicking 'Edit Connection Details' */
  editConnectorTemplateLink?: string;
  /** Display the Last Sync date as seperate column */
  showLastSyncColumn?: boolean;
  /** Display the Status as seperate column */
  showStatusColumn?: boolean;
  /** Run the test connection to given provider when open the overview page */
  testConnectionOnOverview?: boolean;
  /** Appends id=<id> to template that opens when clicking 'Edit Connection Details' */
  editConnectorTemplateLinkIsPresentation?: boolean;
  syncOrder?: SyncOrder[];
  /** Display Sync button over edit connection details, this performs a sync over one or multiple objects */
  showSyncOverview?: boolean;
  /** Show create connection in the overview page */
  showCreateConnectionOverview?: boolean;
  configurations?: {
    base?: {
      /** Sets a start date which is passed to 'syncData' */
      useStartDate?: boolean;
      /** Sets a date range which is passed to 'syncData' */
      useDateRange?: boolean;
      /** Skips deselected models as part of the synchronization */
      useModelExclusions?: boolean;
      /** Starts a recurring task to periodically execute the synchronization */
      useSyncScheduler?: boolean;
    };
    /** Allows the extension of integration-specific configurations */
    custom?: CustomConfig[];
  };
  useOverviewUpload?: boolean;
  uploadAlertValues?: UploadAlertValues;
};
