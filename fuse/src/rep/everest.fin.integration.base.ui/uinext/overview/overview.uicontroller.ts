// @i18n:integrationBase
import { UILifecycleHooks } from '@everestsystems/content-core';
import { setUIModelInSharedState } from '@pkg/everest.base/public/utils/ui/sharedStateUtils.ui';
import { ConnectorStatus } from '@pkg/everest.fin.integration.base.ui/public/enums/ConnectorStatus';
import { DataLoadMode } from '@pkg/everest.fin.integration.base.ui/public/enums/DataLoadMode';
import { MappingStatus } from '@pkg/everest.fin.integration.base.ui/public/enums/MappingStatus';
import { SingleFileMode } from '@pkg/everest.fin.integration.base.ui/public/enums/SingleFileMode';
import type { CustomButtonConfig } from '@pkg/everest.fin.integration.base.ui/public/types/CustomButtonConfig';
import type { CustomConfig } from '@pkg/everest.fin.integration.base.ui/public/types/CustomConfig';
import type { ETLInfo } from '@pkg/everest.fin.integration.base.ui/public/types/ETLInfo';
import type { FetchingArgs } from '@pkg/everest.fin.integration.base.ui/public/types/FetchingArgs';
import type { GetExtraction } from '@pkg/everest.fin.integration.base.ui/public/types/GetExtraction';
import type { GetIntegrationSettings } from '@pkg/everest.fin.integration.base.ui/public/types/GetSettings';
import type { MappingOptions } from '@pkg/everest.fin.integration.base.ui/public/types/MappingOptions';
import type { SyncDataMapping } from '@pkg/everest.fin.integration.base.ui/public/types/SyncDataMapping';
import {
  SESSSION_VARIABLE_CONFIG_TEMPLATE_PATH,
  TEMPLATE_PACKAGE_PATH,
} from '@pkg/everest.fin.integration.base.ui/public/utils/constants/constants';
import { EvstMappingTypes } from '@pkg/everest.fin.integration.base.ui/types/enums/MappingTypes';
import { IntegrationTemplateUI } from '@pkg/everest.fin.integration.base.ui/types/IntegrationTemplate.ui';
import type { OverviewUiTemplate } from '@pkg/everest.fin.integration.base.ui/types/uiTemplates/uinext/overview/overview.ui';
import { DataSyncTracking } from '@pkg/everest.fin.integration.base.ui/uinext/dataSyncTracking/dataSyncTracker.uicontroller';
import { isRunStatus } from '@pkg/everest.fin.integration.base.ui/uinext/dataSyncTracking/utils.uicontroller';
import type { StagingExecutionResponse } from '@pkg/everest.fin.integration.base/public/integrationTypes';
import type { EvstExtractionProviders } from '@pkg/everest.fin.integration.base/types/enums/ExtractionProviders';
import { EvstExtractionSource } from '@pkg/everest.fin.integration.base/types/enums/ExtractionSource';
import { EvstStagingDataStatus } from '@pkg/everest.fin.integration.base/types/enums/StagingDataStatus';
import { EvstStagingStatus } from '@pkg/everest.fin.integration.base/types/enums/StagingStatus';
import { slice } from 'lodash';

type OverviewContext = OverviewUiTemplate.OverviewContext;

type TableProps = {
  rowData: GetExtraction;
  value: string;
};

type Context = OverviewContext & {
  state: {
    providerName: EvstExtractionProviders;
    taskPollingInterval?: ReturnType<typeof setTimeout>;
    isTestingConnection: boolean;
    connectionStatus: string;
  };
};

// Define the two possible configuration item types
export type ConfigItem = ButtonConfigItem | SwitchConfigItem;

// Configuration item with button actions
type ButtonConfigItem = {
  label: string;
  result: string;
  description?: string;
  buttonActions: {
    shape: string;
    label: string;
    onClick: () => void;
    disabled?: boolean;
  }[];
};

// Configuration item with switch field
type SwitchConfigItem = {
  label: string;
  fieldProps: {
    name: string;
    component: 'Switch';
    value: boolean;
    direction: string;
    onChange: (checked: boolean) => Promise<void>;
  };
};

export function getTabTitle({ data }: Context) {
  return `Integration: ${data.config?.templates?.overview?.title}`;
}

UILifecycleHooks.onInit((context: Context) => {
  // Only executes when testing connection on overview is true
  if (shouldTestConnectionOnOverview(context)) {
    testConnection(context).catch(console.error);
  }
});

export async function testConnection({ data, actions, state }: Context) {
  state.connectionStatus = 'testing';
  state.isTestingConnection = true;

  try {
    const result = await actions.run({
      connector: {
        action: 'testConnection',
        data: {
          integrationName: data.connector?.configValues?.integrationName,
        },
      },
    });

    // Update the status based on the API response
    state.connectionStatus = result?.connector?.isConnected
      ? 'connected'
      : 'disconnected';
  } catch (error) {
    // Handle error case
    state.connectionStatus = 'error';

    console.error('Connection test failed:', error);
  } finally {
    // Always reset the testing state
    state.isTestingConnection = false;
  }
}

export function isLinkingVisible({ data }: Context) {
  return Boolean(
    data.config?.general?.enableSimpleLink ||
      data.config?.general?.enableManualLink ||
      data.config?.general?.enableAdvanceLink
  );
}

export function getConnectionStatusText(context: Context) {
  const { data } = context;

  if (shouldTestConnectionOnOverview(context)) {
    return getConnectionStatusMessage(context);
  }

  // Use the original function's logic for initial state
  if (
    !data.config?.executions?.metadata &&
    !data.config?.executions?.businessData
  ) {
    return;
  }

  const mappings = [
    ...data.config.executions.metadata,
    ...data.config.executions.businessData,
  ];
  const hasApiMappings = mappings.find((e) => e.mappings?.api);
  const hasCsvMappings = mappings.find((e) => e.mappings?.file);

  return hasApiMappings && isConnectorSet(context)
    ? '{{integrationBase.connected}}'
    : hasApiMappings
      ? '{{integrationBase.disconnected}}'
      : hasCsvMappings
        ? '{{integrationBase.fileUploadOnly}}'
        : '';
}

/**
 * Returns the appropriate connection status message based on current state
 * @returns {string} The translated status message
 */
function getConnectionStatusMessage({ state }: Context) {
  const message = {
    connected: '{{integrationBase.connected}}',
    disconnected: '{{integrationBase.disconnected}}',
    error: '{{integrationBase.connectionError}}',
    testingConnection: '{{integrationBase.testingConnection}}',
  };

  if (state.isTestingConnection) {
    return message.testingConnection;
  }

  return message[state.connectionStatus] || message.testingConnection;
}

export function shouldTestConnectionOnOverview({ data }: Context) {
  return Boolean(data?.config?.templates?.overview?.testConnectionOnOverview);
}

export function getIntegrationTitle({ data }: Context) {
  return data.config?.templates?.overview?.title ?? '';
}

// =========================================================
// Sidebar
// =========================================================

export function showEntity({ data }: Context) {
  return data.config?.templates?.overview?.hasOneEntityPerConnector ?? false;
}

export async function getEntityName({ data }: Context) {
  return data.connector?.entityName ?? '{{integrationBase.noEntityFound}}';
}

export function getUserName({ data }: Context) {
  return data.connector?.userName ?? ConnectorStatus.NoConnector;
}

export function getCreatedDate({ data }: Context) {
  return data.connector?.createdDate
    ? new Date(data.connector?.createdDate).toLocaleString('en-US')
    : ConnectorStatus.NoConnector;
}

export function getLastSyncDate({ data }: Context) {
  const syncDates = [];
  for (const extraction of data?.metadataExtractions ?? []) {
    if (extraction.lastSyncDate) {
      syncDates.push(new Date(extraction.lastSyncDate));
    }
  }
  for (const extraction of data?.businessDataExtractions ?? []) {
    if (extraction.lastSyncDate) {
      syncDates.push(new Date(extraction.lastSyncDate));
    }
  }

  // Sort the array of sync dates in descending order
  syncDates.sort((date1, date2) =>
    date1 < date2 ? 1 : date1 > date2 ? -1 : 0
  );
  const lastSyncDate = syncDates?.[0];
  return lastSyncDate
    ? new Date(lastSyncDate).toLocaleString('en-US')
    : '{{integrationBase.never}}';
}

export function showCreateConnection(context: Context) {
  const { data } = context;

  return Boolean(
    data.config?.templates?.overview?.showCreateConnectionOverview
  );
}

export function showSyncAll(context: Context) {
  return isSyncAllSet(context) && isConnectorSet(context);
}

export function isSyncAllSet(context: Context) {
  const overview = context.data.config?.templates?.overview;

  if (!overview) {
    return false;
  }

  const { syncOrder, showSyncOverview } = overview;

  return Boolean(syncOrder || showSyncOverview);
}

export function isInSync({ data }: Context) {
  return data.metadataExtractions?.every(
    (e) => e.status === MappingStatus.SyncInProgress
  );
}

export async function singleCSVMasterDataOnly(context: Context): Promise<void> {
  await openMultipleDataCSVModal(context, SingleFileMode.MasterData);
}

export async function singleCSVMasterAndTransactionalData(
  context: Context
): Promise<void> {
  await openMultipleDataCSVModal(
    context,
    SingleFileMode.MasterDataAndTransactionalData
  );
}

async function openMultipleDataCSVModal(
  context: Context,
  singleFileMode: SingleFileMode
): Promise<void> {
  const { data, helpers, actions } = context;
  const expertDataModeEnabled = await isExpertDataModeEnabled(context);

  helpers.openModal({
    title: `Import Multiple data from CSV`,
    template: `${TEMPLATE_PACKAGE_PATH}/upload/upload`,
    size: 'large',
    initialState: {
      mappingType: EvstMappingTypes.Technical,
      startDate: data.settings?.startDate,
      endDate: data.settings?.endDate,
      providerName: data.config?.general?.providerName,
      sessionId: data?.connector?.sessionId,
      uploadAlertValues: data.config.templates.overview?.uploadAlertValues,
      singleFileMode,
      expertDataModeEnabled,
    },
    onModalSubmit: async () => {
      await actions.refetchUiModelData();
    },
  });
}

export function getExtractionStatus(context: Context) {
  const { data } = context;
  if (!data.metadataExtractions && !data.businessDataExtractions) {
    return;
  }

  if (isInSync(context)) {
    return '{{expenseIntegration.syncInProgress}}';
  }

  const extractionStatuses = [
    ...data.metadataExtractions,
    ...data.businessDataExtractions,
  ].map((extraction) => ({
    status: extraction.status,
    providerModel: extraction.providerModel,
  }));

  if (
    !extractionStatuses ||
    extractionStatuses.every(({ status }) => status === MappingStatus.NotSynced)
  ) {
    return '{{expenseIntegration.waitingToSync}}';
  }

  // Filter out executions that are excluded by settings
  const executionsFiltered = getExecutionsNotExcluded<{
    status: MappingStatus;
    providerModel: string;
  }>(context, extractionStatuses);

  const hasFailures = executionsFiltered.some(
    ({ status }) =>
      status === MappingStatus.NeedsInteraction ||
      status === MappingStatus.NotSynced ||
      status === MappingStatus.MatchIncomplete ||
      status === MappingStatus.NoMapping
  );

  return hasFailures
    ? '{{expenseIntegration.incomplete}}'
    : '{{expenseIntegration.complete}}';
}

export async function activate(context: Context) {
  await syncInner(context, true);
}

export async function syncAll(context: Context) {
  await syncInner(context, false);
}

export async function syncInner(context: Context, activateOnly: boolean) {
  const { data, helpers } = context;
  const general = data.config?.general;

  if (
    !activateOnly &&
    general?.isStartDateRequired &&
    !data.settings?.startDate
  ) {
    helpers.showToast({
      type: 'error',
      title: 'Error',
      message: '{{integrationBase.setStartDate}}',
    });

    return;
  }

  // Sync metadata first
  const metadataRes = await syncMetadata(context, activateOnly);

  if (!metadataRes.success) {
    return;
  }

  if (metadataRes.pendingExecutions?.length > 0) {
    const businessDataRes = await syncBusinessData(context, activateOnly, true);

    const taskRes = await createDataloadTask(context, [
      ...metadataRes.pendingExecutions,
      ...businessDataRes.pendingExecutions,
    ]);

    if (taskRes.success) {
      helpers.showNotificationMessage({
        key: 'loading',
        type: 'success',
        message: '{{integrationBase.etlSuccess}}',
        duration: 2,
      });
    }

    return;
  }

  const businessDataRes = await syncBusinessData(context, activateOnly);

  if (businessDataRes.pendingExecutions?.length > 0) {
    const createDataLoadTaskRes = await createDataloadTask(
      context,
      businessDataRes.pendingExecutions
    );

    if (createDataLoadTaskRes.success) {
      helpers.showNotificationMessage({
        key: 'loading',
        type: 'success',
        message: '{{integrationBase.etlSuccess}}',
        duration: 2,
      });
    }

    return;
  }

  if (businessDataRes.success) {
    helpers.showNotificationMessage({
      key: 'loading',
      type: 'success',
      message: '{{integrationBase.etlSuccess}}',
      duration: 2,
    });
  }
}

async function syncMetadata(context: Context, activateOnly: boolean) {
  const { data, actions } = context;

  // Set all metadata extractions status to "Sync In Progress"
  updateExtractionStatus(context, MappingStatus.SyncInProgress); // TODO: send the rows to update filtered executions

  const executions = getExecutionsToSyncInOrder(
    context,
    data.config.executions.metadata
  );

  const result = await syncSubset(context, executions, activateOnly);

  await actions.refetchUiModelData({
    discardChanges: true,
    nodesToLoad: ['metadataExtractions'],
  });

  // Set all extractions status with calculated status
  updateExtractionStatus(context);

  return result;
}

async function syncBusinessData(
  context: Context,
  activateOnly: boolean,
  forceBackgroundExecution: boolean = false
) {
  const { data, actions } = context;

  const executions = getExecutionsToSyncInOrder(
    context,
    data.config.executions.businessData
  );

  // Set all business extractions status to "Sync In Progress"
  updateExtractionStatus(
    context,
    MappingStatus.SyncInProgress,
    undefined, // TODO: send the rows to update filtered executions
    'businessDataExtractions'
  );

  const collectedData = await getCollectedData(context, activateOnly);

  // Then sync business data
  const businessDataRes = await syncSubset(
    context,
    executions,
    activateOnly,
    collectedData,
    forceBackgroundExecution
  );

  await actions.refetchUiModelData({
    discardChanges: true,
    nodesToLoad: ['businessDataExtractions'],
  });

  return businessDataRes;
}

async function createDataloadTask(context: Context, executions: ETLInfo[]) {
  if (executions?.length === 0) {
    return { success: true, createdTask: false };
  }

  const { data, state, helpers } = context;
  const key = 'createDataloadTask';

  const jobRes = await IntegrationTemplateUI.createDataloadTask(context, {
    mappings: executions.map((execution) => ({
      ...execution,
      ...getMappingDetails(execution, {
        fileId: undefined,
        useOverviewUpload: data.config?.templates?.overview?.useOverviewUpload,
      }),
      providerName: state.providerName,
      currentProviderModel: execution.providerModel,
      entityId: data.connector?.entityId,
    })),
    fetching: {
      dataload: DataLoadMode.All,
      connectorId: data.connector?.id,
      configValues: data.connector?.configValues,
      sessionId: data.connector?.sessionId,
      startDate: data.settings?.startDate,
      endDate: data.settings?.endDate,
      extractionUUID: context.helpers.uuid(),
    },
  }).run('integration');

  if (jobRes?.error) {
    helpers.showNotificationMessage({
      key,
      type: 'error',
      message: '{{integrationBase.failedToCreateTask}}'.replace(
        '$errorMessage$',
        jobRes?.error?.message
      ),
      duration: 4,
    });

    return { success: false, createdTask: false };
  }

  if (jobRes?.integration?.jobId) {
    helpers.showNotificationMessage({
      key,
      type: 'success',
      message: 'Integration continues in background due to high data volume',
      duration: 2,
    });

    return { success: true, createdTask: true };
  }
}

async function syncSubset(
  context: Context,
  executions: ETLInfo[],
  activateOnly: boolean,
  collectedData?: Record<string, unknown>[] | Record<string, unknown>,
  forceBackgroundExecution: boolean = false
): Promise<{
  success: boolean;
  createdTask: boolean;
  pendingExecutions?: ETLInfo[];
}> {
  const { data, state, helpers } = context;

  const operation = activateOnly ? 'Activating' : 'Syncing';
  const key = 'syncSubset';
  let pendingExecutions: ETLInfo[] = [];

  if (forceBackgroundExecution) {
    return { success: true, createdTask: true, pendingExecutions: executions };
  }

  for (const [index, execution] of executions.entries()) {
    helpers.showNotificationMessage({
      key,
      type: 'loading',
      message: `${operation} ${execution.displayNamePlural}`,
      duration: 10,
    });

    const syncRes = await IntegrationTemplateUI.syncData(context, {
      mapping: {
        ...execution,
        ...getMappingDetails(execution, {
          fileId: undefined,
          useOverviewUpload:
            data.config?.templates?.overview?.useOverviewUpload,
        }),
        providerName: state.providerName,
        entityId: data.connector?.entityId,
      },
      fetching: {
        dataload: DataLoadMode.Single,
        connectorId: data.connector?.id,
        configValues: data.connector?.configValues,
        sessionId: data.connector?.sessionId,
        startDate: data.settings?.startDate,
        endDate: data.settings?.endDate,
        extractionUUID: context.helpers.uuid(),
        data: collectedData,
      },
    }).run('integration');

    // Handle standard sync errors
    if (syncRes?.error) {
      helpers.showNotificationMessage({
        key,
        type: 'error',
        message: '{{integrationBase.failedToSyncModel}}'
          .replace('$model$', execution.displayNamePlural)
          .replace('$errorMessage', syncRes?.error?.message),
        duration: 4,
      });

      return { success: false, createdTask: false };
    }

    // Handle data load continuation in background task
    if (syncRes?.integration?.continueInTask) {
      pendingExecutions = slice(executions, index);
      break;
    }
  }

  helpers.closeNotificationMessage(key);

  return { success: true, createdTask: false, pendingExecutions };
}

function getExecutionsToSyncInOrder(
  context: Context,
  executions: ETLInfo[]
): ETLInfo[] {
  const sortedExecutions = sortExecutions(context, executions);

  // Filter out executions that are excluded by settings
  const executionsFiltered = getExecutionsNotExcluded<ETLInfo>(
    context,
    sortedExecutions
  );

  return executionsFiltered;
}

function getMappingDetails(
  execution: ETLInfo,
  options: {
    useOverviewUpload: boolean;
    fileId?: string;
  }
): {
  mappingName: string;
  originalIdKey: string;
  extractionSource: EvstExtractionSource;
} {
  if (options.useOverviewUpload || options.fileId) {
    return {
      mappingName: execution.mappings?.file?.name,
      originalIdKey:
        execution?.mappings?.file?.originalIdKey ?? execution?.originalIdKey,
      extractionSource: EvstExtractionSource.File,
    };
  }

  return {
    mappingName: execution.mappings?.api?.name,
    originalIdKey: execution.mappings?.api?.originalIdKey,
    extractionSource: EvstExtractionSource.API,
  };
}

async function getCollectedData(context: Context, activateOnly: boolean) {
  const { helpers, data, state } = context;

  if (!activateOnly && data.config?.executions?.collectBusinessData) {
    const collectRes = await IntegrationTemplateUI.collectData(context, {
      providerName: state.providerName,
      fetching: {
        dataload: DataLoadMode.Single,
        connectorId: data.connector?.id,
        configValues: data.connector?.configValues,
        startDate: data.settings?.startDate as unknown as string,
        extractionUUID: context.helpers.uuid(),
        mappingType: EvstMappingTypes.Technical,
      },
    }).run('integration');

    if (collectRes?.error) {
      helpers.closeNotificationMessage('loading');
      return;
    }

    return collectRes?.integration?.data;
  }

  return [];
}

export function isActivateFailedEntriesDisabled(context: Context) {
  return !hasErrors(context) || isInSync(context);
}

export function isActivateFailedEntriesVisible(context: Context) {
  return hasErrors(context) && isOverviewUploadEnabled(context);
}

export function isOverviewUploadEnabled(context: Context): boolean {
  return context.data.config?.templates?.overview?.useOverviewUpload ?? false;
}

export function isSyncStatusDisabled(context: Context) {
  return isInSync(context) || isConnectorNotSet(context);
}

export function isConnectorNotSet(context: Context) {
  return context.data.connector?.id === undefined;
}

export function isConnectorSet(context: Context) {
  return Boolean(context.data.connector?.id);
}

export function createConnector({ helpers, actions, data }: Context) {
  helpers.openModal({
    title: '{{integrationBase.connectionDetails}}',
    template: data?.integrationConnector?.templateAddress,
    onClose: async () => {
      await actions.refetchUiModelData();
    },
  });
}

export function editConnectionDetails({ helpers, actions, data }: Context) {
  helpers.openModal({
    title: '{{integrationBase.connectionDetails}}',
    template: data.config.templates?.overview?.editConnectorTemplateLink,
    initialState: {
      connector: data.connector,
    },
    onClose: async () => {
      await actions.refetchUiModelData();
    },
  });
}

export function connectionStatus(context: Context) {
  return isConnectorNotSet(context) || isInSync(context);
}

// =========================================================
// Configuration
// =========================================================

export function showConfigurations({ data }: Context) {
  const configBase = data.config?.templates?.overview?.configurations?.base;

  return (
    configBase?.useStartDate ||
    configBase?.useDateRange ||
    data.config?.templates?.overview?.configurations?.custom?.length > 0
  );
}

export function getConfigurations(context: Context) {
  const configs: ConfigItem[] = [];

  const baseConfigs = createBaseConfigs(context);
  const customConfigs = createCustomConfigs(context);

  configs.push(...baseConfigs, ...customConfigs);

  return configs;
}

function createBaseConfigs(context: Context) {
  const configs: ConfigItem[] = [];
  const { data, state } = context;
  const configBase = data.config?.templates?.overview?.configurations?.base;

  const buttonProps = {
    shape: 'pill',
    label: '{{integrationBase.manage}}',
    disabled: isConfigurationActionDisabled(context),
  };

  if (!configBase) {
    return [];
  }

  if (configBase?.useDateRange) {
    configs.push({
      label: '{{integrationBase.dateRange}}',
      result: dateRangeStatus(context),
      buttonActions: [
        {
          ...buttonProps,
          onClick: () =>
            openConfigModal(
              context,
              '{{integrationBase.titleDateRange}}'.replace(
                '$title$',
                data.config?.templates?.overview?.title
              ),
              `${TEMPLATE_PACKAGE_PATH}/configurations/dateRange/dateRange`,
              {
                settingsId: data.settings?.settingsId,
                providerName: state.providerName,
                startDate: data.settings?.startDate,
                endDate: data.settings?.endDate,
                useStartDateOnly: configBase?.useStartDate,
              }
            ),
        },
      ],
    });
  }

  if (configBase?.useStartDate) {
    configs.push({
      label: '{{integrationBase.startDate}}',
      result: dateRangeStatus(context),
      buttonActions: [
        {
          ...buttonProps,
          onClick: () =>
            openConfigModal(
              context,
              '{{integrationBase.titleStartDate}}'.replace(
                '$title$',
                data.config?.templates?.overview?.title
              ),
              `${TEMPLATE_PACKAGE_PATH}/configurations/dateRange/dateRange`,
              {
                settingsId: data.settings?.settingsId,
                providerName: state.providerName,
                startDate: data.settings?.startDate,
                endDate: data.settings?.endDate,
                useStartDateOnly: configBase?.useStartDate,
              }
            ),
        },
      ],
    });
  }

  if (configBase?.useModelExclusions) {
    configs.push({
      label: '{{integrationBase.dataToSync}}',
      result: modelExclusionStatus(context),
      buttonActions: [
        {
          ...buttonProps,
          onClick: () =>
            openConfigModal(
              context,
              '{{integrationBase.titleDataToSync}}'.replace(
                '$title$',
                data.config?.templates?.overview?.title
              ),
              `${TEMPLATE_PACKAGE_PATH}/configurations/excludeModels/excludeModels`,
              {
                settingsId: data.settings?.settingsId,
                excludedModels: (data.settings as GetIntegrationSettings)
                  ?.excludedModels,
                businessData: data.config?.executions?.businessData,
              }
            ),
        },
      ],
    });
  }

  if (configBase?.useSyncScheduler) {
    configs.push({
      label: '{{integrationBase.syncScheduler}}',
      result: syncSchedulerStatus(context),
      buttonActions: [
        {
          ...buttonProps,
          onClick: () =>
            openConfigModal(
              context,
              '{{integrationBase.titleSyncScheduler}}'.replace(
                '$title$',
                data.config?.templates?.overview?.title
              ),
              `${TEMPLATE_PACKAGE_PATH}/configurations/syncScheduler/syncScheduler`,
              {
                settingsId: data.settings?.settingsId,
              }
            ),
        },
      ],
    });
  }

  return configs;
}

function createCustomConfigs(context: Context): ConfigItem[] {
  const { data } = context;

  const customConfigs =
    data.config?.templates?.overview?.configurations?.custom?.map((config) =>
      buildConfigLine(
        context,
        config,
        data.connector?.id,
        data.config?.templates?.overview?.title
      )
    );

  return customConfigs || [];
}

function buildConfigLine(
  context: Context,
  config: CustomConfig,
  connectorId?: number,
  title?: string
): ConfigItem {
  if ('component' in config && config.component === 'Switch') {
    const { name, key, label, direction, value } = config;

    return {
      label,
      fieldProps: {
        name,
        component: 'Switch' as const,
        value,
        direction: direction || 'horizontal-reverse',
        onChange: async (checked: boolean) => {
          const { actions } = context;

          await actions.run({
            config: {
              action: 'executeConfigSwitchChange',
              data: {
                providerName: context.state.providerName,
                value: checked,
                key,
              },
            },
          });
        },
      },
    };
  }

  if (!('component' in config)) {
    const { name, status, description, initialState, size } = config;

    return {
      label: name,
      result: status,
      description: description,
      buttonActions: [
        {
          shape: 'pill',
          label: '{{integrationBase.manage}}',
          disabled: isConfigurationActionDisabled(context),
          onClick: () =>
            openConfigModal(
              context,
              `${title}: ${name}`,
              config.templateUrl || SESSSION_VARIABLE_CONFIG_TEMPLATE_PATH,
              {
                ...initialState,
                packageName: getPackageName(context),
                connectorId,
              },
              size || 'large'
            ),
        },
      ],
    };
  }
}

export function isUsingConnector(context: Context): boolean {
  return !context.data.config?.general?.useWithoutConnector;
}

export function isConfigurationActionDisabled(context: Context): boolean {
  return (
    !context.data.config?.general?.useWithoutConnector &&
    isConnectorNotSet(context)
  );
}

export function syncSchedulerStatus({ data }: Context) {
  const interval = (data?.settings as GetIntegrationSettings)
    ?.syncSchedulerInterval;
  return interval
    ? '{{integrationBase.syncSchedulerActiveStatus}}'.replace(
        '$interval$',
        interval.toString()
      )
    : '{{integrationBase.disabled}}';
}

export function modelExclusionStatus({ data }: Context) {
  const excludedModels = (
    data?.settings as GetIntegrationSettings
  )?.excludedModels?.filter((m) => m.excluded);
  const exclusions = excludedModels?.length ?? 0;

  let status: string;
  if (exclusions > 0) {
    status = '{{integrationBase.exclusions}}'.replace(
      '$num$',
      exclusions.toString()
    );
  } else {
    const ALL = '{{integrationBase.all}}';
    status = ALL;
  }
  return status;
}

function openConfigModal(
  context: Context,
  title: string,
  templateUrl: string,
  initialState: Record<string, unknown>,
  size: CustomButtonConfig['size'] = 'xsmall'
) {
  const { helpers, actions } = context;

  helpers.openModal({
    title,
    template: templateUrl,
    size,
    initialState,
    onClose: async () => {
      await actions.refetchUiModelData();
    },
  });
}

export function dateRangeStatus({ data }: Context): string {
  const { startDate, endDate } = data.settings ?? {
    startDate: undefined,
    endDate: undefined,
  };

  const useStartDate =
    data.config?.templates?.overview?.configurations?.base?.useStartDate;

  if (!startDate && !endDate) {
    return '{{integrationBase.notSet}}';
  }

  if (startDate && endDate) {
    return `${startDate} - ${endDate}`;
  } else if (startDate) {
    if (useStartDate) {
      return `${startDate}`;
    }
    return `${startDate} - /`;
  } else if (endDate) {
    return `/ - ${endDate}`;
  }

  return '{{integrationBase.notSet}}';
}

export function hideExtraColumn({ data }: Context) {
  const currentMappings = [
    ...(data?.config?.executions?.metadata || []),
    ...(data?.config?.executions?.businessData || []),
  ];

  return !currentMappings.some((mapping) =>
    checkTypeOfMapping(mapping.mappings)
  );
}

export function hideMasterDataColumn(
  { data }: Context,
  columName: EvstStagingStatus
): boolean {
  return (
    data?.config?.templates?.masterData?.hideColumns?.includes(columName) ||
    false
  );
}

export function hideTransactionDataColumn(
  { data }: Context,
  columName: EvstStagingStatus
): boolean {
  return (
    data?.config?.templates?.transactionData?.hideColumns?.includes(
      columName
    ) || false
  );
}

export function hideLastSyncColumn({ data }: Context) {
  /** by default we hide the last sync column */
  return data?.config?.templates?.overview?.showLastSyncColumn
    ? !data?.config?.templates?.overview?.showLastSyncColumn
    : true;
}

export async function hideStatusColumn(
  context: Context,
  type: 'master' | 'transaction'
) {
  const { data } = context;
  const showStatusColumn = !data?.config?.templates?.overview?.showStatusColumn;
  const dataSyncTracking = await context.uiLoader.getSingletonInstanceOf({
    type: DataSyncTracking,
  });

  const shouldHideStatus = dataSyncTracking.shouldHideDataSyncStatus(type);

  if (!shouldHideStatus) {
    return false;
  }

  return showStatusColumn ?? true;
}

export function getLastSyncValue(
  _context: Context,
  rowData: { data: { lastSyncDate: Date } }
) {
  return rowData.data.lastSyncDate
    ? new Date(rowData.data.lastSyncDate.toString()).toLocaleString('en-US')
    : '{{integrationBase.never}}';
}

function checkTypeOfMapping(mappings: {
  api?: MappingOptions;
  file?: MappingOptions;
}): boolean {
  if (mappings?.api && mappings?.file) {
    return (
      (mappings.api.type?.semantic &&
        (mappings.api.type?.technical || mappings.file.type?.technical)) ||
      (mappings.file.type?.semantic &&
        (mappings.api.type?.technical || mappings.file.type?.technical))
    );
  } else if (mappings?.api) {
    return mappings.api.type?.semantic && mappings.api.type?.technical;
  } else if (mappings?.file) {
    return mappings.file.type?.semantic && mappings.file.type?.technical;
  }
}

// =========================================================
// Extractions
// =========================================================

export function showTable({ data }: Context, binding: string) {
  return data[binding]?.length > 0;
}

export function getTotalImportedHeader({ data }: Context) {
  return '{{integrationBase.objectsIn}}'.replace(
    '$title$',
    data.config?.templates?.overview?.title
  );
}

export function getMatchers(_: Context, { value, rowData }: TableProps) {
  const { status } = rowData;

  if (isRunStatus(value)) {
    return 'havelock-blue';
  }

  switch (status) {
    case MappingStatus.Integrated:
    case MappingStatus.Matched: {
      return 'shamrock';
    }
    case MappingStatus.NeedsInteraction:
    case MappingStatus.ConfirmMatch:
    case MappingStatus.MatchIncomplete: {
      return 'orchid';
    }
    case MappingStatus.NotSynced: {
      return 'grey';
    }
    case MappingStatus.SyncInProgress: {
      return 'jaffa';
    }
  }
}

function updateExtractionStatus(
  context: Context,
  status?: MappingStatus,
  rowData?: Record<'data', GetExtraction>,
  extractionRow:
    | 'metadataExtractions'
    | 'businessDataExtractions' = 'metadataExtractions'
) {
  const { data } = context;

  if (rowData) {
    const {
      _nodeReference,
      lastSyncDate,
      status: calculatedStatus,
    } = rowData.data;

    let rowStatus = MappingStatus.NotSynced;

    if (status) {
      rowStatus = status;
    } else if (lastSyncDate) {
      rowStatus = calculatedStatus;
    }

    setUIModelInSharedState(
      context,
      extractionRow,
      'status',
      rowStatus,
      _nodeReference
    );
    return;
  }

  for (const e of data[extractionRow]) {
    const { _nodeReference, lastSyncDate, status: calculatedStatus } = e;
    let rowStatus = MappingStatus.NotSynced;
    if (status) {
      rowStatus = status;
    } else if (lastSyncDate) {
      rowStatus = calculatedStatus;
    }
    setUIModelInSharedState(
      context,
      extractionRow,
      'status',
      rowStatus,
      _nodeReference
    );
  }
}

export function findMatchingMapping(
  context: Context,
  rowData: GetExtraction
): ETLInfo | undefined {
  const { data } = context;

  // Combine metadata and business data configurations
  const allMappings = [
    ...data.config.executions.metadata,
    ...data.config.executions.businessData,
  ];

  return allMappings.find((mapping) => {
    if (mapping.providerModel !== rowData?.providerModel) {
      return false;
    }

    const sourceMappings = mapping.mappings.file || mapping.mappings.api;

    return sourceMappings?.type?.semantic;
  });
}

export function findMapping(
  context: Context,
  rowData: GetExtraction,
  mappingType: EvstMappingTypes,
  extractionSource?: EvstExtractionSource
): ETLInfo | undefined {
  const { data } = context;

  // Combine metadata and business data configurations
  const allMappings = [
    ...data.config.executions.metadata,
    ...data.config.executions.businessData,
  ];

  // Determine which mapping type to look for based on input parameters
  const isSemantic = mappingType === EvstMappingTypes.Semantic;

  return allMappings.find((mapping) => {
    // Match by provider model
    if (mapping.providerModel !== rowData?.providerModel) {
      return false;
    }

    // Get the appropriate mapping based on extraction source and type
    const sourceMappings =
      extractionSource === EvstExtractionSource.File
        ? mapping.mappings.file
        : mapping.mappings.api;

    return isSemantic
      ? sourceMappings?.type?.semantic
      : sourceMappings?.type?.technical;
  });
}

export function getPackageName(context: Context): string {
  return context.data?.config?.general?.package ?? '';
}

/**
 * Determines if technical API actions should be hidden for master data rows
 */
export function hideMasterDataTechnicalApiActions(
  context: Context,
  { rowData }: Record<'rowData', GetExtraction>
) {
  return shouldHideApiActions(
    context,
    rowData,
    EvstMappingTypes.Technical,
    'masterData'
  );
}

/**
 * Determines if semantic API actions should be hidden for master data rows
 */
export function hideMasterDataSemanticApiActions(
  context: Context,
  { rowData }: Record<'rowData', GetExtraction>
) {
  return shouldHideApiActions(
    context,
    rowData,
    EvstMappingTypes.Semantic,
    'masterData'
  );
}

/**
 * Determines if API actions should be hidden for transaction data rows
 */
export function hideTransactionTechnicalApiActions(
  context: Context,
  { rowData }: Record<'rowData', GetExtraction>
) {
  return shouldHideApiActions(
    context,
    rowData,
    EvstMappingTypes.Technical,
    'transactionData'
  );
}

/**
 * Base function to determine if API actions should be hidden
 */
function shouldHideApiActions(
  context: Context,
  rowData: GetExtraction,
  mappingType: EvstMappingTypes,
  type?: string
) {
  const { data } = context;

  // If the actions column is marked as hidden in table configuration
  if (data.config?.templates?.[type]?.hideColumns?.includes('actions')) {
    return true;
  }

  // Hide if no mapping exists for this row
  return !findMapping(context, rowData, mappingType, EvstExtractionSource.API);
}

/**
 * Determines if file actions should be hidden for master data rows
 */
export function hideMasterDataFileActions(
  context: Context,
  { rowData }: Record<'rowData', GetExtraction>
) {
  return shouldHideFileActions(context, rowData, 'masterData');
}

/**
 * Determines if file actions should be hidden for transaction data rows
 */
export function hideTransactionTechnicalFileActions(
  context: Context,
  { rowData }: Record<'rowData', GetExtraction>
) {
  return shouldHideFileActions(context, rowData, 'transactionData');
}

/**
 * Determines if file actions should be hidden for transaction data rows
 */
export function hideMasterDataSemanticUploadActions(
  context: Context,
  { rowData }: Record<'rowData', GetExtraction>
) {
  return shouldHideFileActions(
    context,
    rowData,
    'masterData',
    EvstMappingTypes.Semantic
  );
}

/**
 * Base function to determine if file actions should be hidden
 */
function shouldHideFileActions(
  context: Context,
  rowData: GetExtraction,
  type: 'masterData' | 'transactionData',
  mappingTypes: EvstMappingTypes = EvstMappingTypes.Technical
) {
  const { data } = context;

  // If the actions column is marked as hidden in table configuration
  if (data.config?.templates?.[type]?.hideColumns?.includes('actions')) {
    return true;
  }

  const hasFileMapping = findMapping(
    context,
    rowData,
    mappingTypes,
    EvstExtractionSource.File
  );
  return !hasFileMapping;
}

/**
 * Base function to determine if masterdata semantic actions should be hidden
 */
export function hideMasterDataSemanticActions(
  context: Context,
  { rowData }: Record<'rowData', GetExtraction>
) {
  const type = 'masterData';
  const { data } = context;

  // If the actions column is marked as hidden in table configuration
  if (data.config?.templates?.[type]?.hideColumns?.includes('actions')) {
    return true;
  }

  const metadataExecutions = data.config.executions.metadata;

  return !metadataExecutions.some((execution) => {
    // Match by provider model
    if (execution.providerModel !== rowData?.providerModel) {
      return false;
    }

    return (
      execution.mappings?.file?.type?.semantic ||
      execution.mappings?.api?.type?.semantic
    );
  });
}

/**
 * Determines if run report actions should be hidden for master data rows
 */
export function hideMasterDataRunReportAction(
  context: Context,
  { rowData }: Record<'rowData', GetExtraction>
) {
  return shouldHideRunReportAction(context, rowData, 'masterData');
}

/**
 * Determines if run report actions should be hidden for transaction data rows
 */
export function hideTransactionRunReportAction(
  context: Context,
  { rowData }: Record<'rowData', GetExtraction>
) {
  return shouldHideRunReportAction(context, rowData, 'transactionData');
}

/**
 * Base function to determine if run report actions should be hidden
 */
function shouldHideRunReportAction(
  context: Context,
  rowData: GetExtraction,
  type: 'masterData' | 'transactionData'
) {
  const { data } = context;

  // If the actions column is marked as hidden in table configuration
  if (data.config?.templates?.[type]?.hideColumns?.includes('actions')) {
    return true;
  }

  return false;
}

async function getMappingId(context: Context, name: string, subject: string) {
  const res = await context.actions.run({
    mappings: {
      action: 'query',
      data: { where: { name, subject } },
    },
  });
  return res?.mappings?.[0]?.id;
}

export async function viewApiMapping(
  context: Context,
  rowData: Record<'data', GetExtraction>
) {
  await viewMapping(
    context,
    rowData,
    EvstMappingTypes.Technical,
    EvstExtractionSource.API
  );
}

export async function viewFileMapping(
  context: Context,
  rowData: Record<'data', GetExtraction>
) {
  await viewMapping(
    context,
    rowData,
    EvstMappingTypes.Technical,
    EvstExtractionSource.File
  );
}

export function hasSessionVariableRelatedUrn(
  contex: Context,
  everestModel: string,
  mappingType: EvstMappingTypes
) {
  const { data } = contex;
  return (
    data?.config?.templates?.overview?.configurations?.custom?.some(
      (row: CustomButtonConfig) => {
        return row?.initialState?.sessionVariablesLevels?.some((level) => {
          return level?.configs?.some(
            (config) =>
              config?.sessionVariableConfig?.relatedURNList?.includes(
                everestModel
              ) &&
              (!config?.sessionVariableConfig?.mode ||
                config?.sessionVariableConfig?.mode === mappingType)
          );
        });
      }
    ) || false
  );
}

export async function viewMapping(
  context: Context,
  rowData: Record<'data', GetExtraction>,
  mappingType: EvstMappingTypes,
  extractionSource: EvstExtractionSource
) {
  const { helpers, data } = context;

  const m = findMapping(context, rowData?.data, mappingType, extractionSource);
  const providerName = data?.config?.general?.providerName;
  const mappingName =
    extractionSource === EvstExtractionSource.API
      ? m?.mappings?.api && m?.mappings?.api?.name
      : m?.mappings?.file && m?.mappings?.file?.name;

  const mappingId = await getMappingId(context, mappingName, providerName);
  helpers.navigate({ to: `/mapper?id=${mappingId}` });
}

export async function upload(
  context: Context,
  rowData: Record<'data', GetExtraction>,
  mappingType: EvstMappingTypes = EvstMappingTypes.Technical
) {
  const { data, helpers, actions } = context;

  const providerName = data?.config?.general?.providerName;
  const providerModel = rowData?.data?.providerModel;
  const everestModel = rowData?.data?.everestModel;

  const hasSessionVariableConfig = hasSessionVariableRelatedUrn(
    context,
    everestModel,
    mappingType
  );

  // TODO: Remove the defaultValues
  if (hasSessionVariableConfig) {
    helpers.openModal({
      title: `${providerName} ${providerModel} Configuration`,
      size: 'small',
      template: `${TEMPLATE_PACKAGE_PATH}/sessionVariable/sessionVariable?feat-delta=true&feat-inspect=true`,
      initialState: {
        providerName: providerName,
        providerModel: everestModel,
      },
      onModalSubmit: async () => {
        await actions.refetchUiModelData();
        await openUploadModal(context, rowData, mappingType);
      },
    });
  } else {
    await openUploadModal(context, rowData, mappingType);
  }
}

export async function openUploadModal(
  context: Context,
  rowData: Record<'data', GetExtraction>,
  mappingType: EvstMappingTypes
) {
  const { data, helpers, actions } = context;
  const expertDataModeEnabled = await isExpertDataModeEnabled(context);

  const sessionId = data?.config?.general?.integrationSessionId;

  const { displayName } = [
    ...data.config.executions.metadata,
    ...data.config.executions.businessData,
  ].find((m) => m.providerModel === rowData?.data?.providerModel);

  const dataSyncTracking = await context.uiLoader.getSingletonInstanceOf({
    type: DataSyncTracking,
  });

  const mapping = findMapping(
    context,
    rowData.data,
    mappingType,
    EvstExtractionSource.File
  );

  const runTrackingKey = dataSyncTracking.getRunTrackingKey(rowData.data);

  helpers.openModal({
    title: `Import ${displayName} from CSV`,
    template: `${TEMPLATE_PACKAGE_PATH}/upload/upload`,
    size: 'large',
    initialState: {
      mappingType,
      mapping,
      startDate: data.settings?.startDate,
      endDate: data.settings?.endDate,
      providerName: data.config?.general?.providerName,
      providerModel: rowData.data.providerModel,
      runTrackingKey,
      sessionId: data?.connector?.sessionId ?? sessionId,
      expertDataModeEnabled,
    },
    onModalSubmit: async () => {
      await actions.refetchUiModelData();
    },
  });
}

export async function isExpertDataModeEnabled(context: Context) {
  const { state } = context;
  const providerName = state.providerName;
  if (!providerName) {
    return false;
  }
  const result = await IntegrationTemplateUI.isExpertDataModeEnabled(context, {
    providerName,
  }).run('integration');
  return result?.integration ?? false;
}

export async function syncData(
  context: Context,
  mapping: SyncDataMapping,
  fetching: FetchingArgs
) {
  return IntegrationTemplateUI.syncData(context, {
    mapping,
    fetching,
  }).run('integration');
}

export async function syncMatching(
  context: Context,
  rowData: Record<'data', GetExtraction>
) {
  await sync(context, rowData, EvstMappingTypes.Semantic);
}

export async function uploadMatching(
  context: Context,
  rowData: Record<'data', GetExtraction>
) {
  await upload(context, rowData, EvstMappingTypes.Semantic);
}

export async function sync(
  context: Context,
  rowData: Record<'data', GetExtraction>,
  mappingType: EvstMappingTypes = EvstMappingTypes.Technical
) {
  const { state, data, helpers, actions } = context;

  const mapping = findMapping(
    context,
    rowData.data,
    mappingType,
    EvstExtractionSource.API
  );

  const { enableExpertDataMode } = mapping;
  const providerName = data?.config?.general?.providerName;
  const providerModel = rowData?.data?.providerModel;
  const everestModel = rowData?.data?.everestModel;

  const hasSessionVariableConfig = hasSessionVariableRelatedUrn(
    context,
    everestModel,
    mappingType
  );
  let syncRes;

  if (
    !data.config?.templates?.overview?.testConnector &&
    !validateSyncRequirements(
      context,
      mapping.mappings?.api?.name,
      providerName
    )
  ) {
    return;
  }

  if (hasSessionVariableConfig) {
    helpers.openModal({
      title: `${providerName} ${providerModel} Configuration`,
      size: 'small',
      template: `${TEMPLATE_PACKAGE_PATH}/sessionVariable/sessionVariable?feat-delta=true&feat-inspect=true`,
      initialState: {
        providerName: providerName,
        providerModel: everestModel,
      },
      onModalSubmit: async () => {
        syncRes = await performSync(
          context,
          mapping,
          rowData,
          state,
          data,
          mappingType
        );
        await validateSyncResult(
          context,
          syncRes,
          mapping,
          mappingType,
          rowData
        );
      },
    });
  } else if (
    enableExpertDataMode?.[mappingType?.toLowerCase()] &&
    !enableExpertDataMode?.[mappingType?.toLowerCase()].linking
  ) {
    helpers.navigate({
      to: `${TEMPLATE_PACKAGE_PATH}/dataBrowser/dataBrowser?providerModel=${providerModel}&providerName=${providerName}&feat-delta=true&feat-inspect=true`,
      // This is hardcoded but will be updated in the next MR
      initialState: {
        model: enableExpertDataMode?.[mappingType?.toLowerCase()].modelUrn,
        entryPointFunctionName:
          enableExpertDataMode?.[mappingType?.toLowerCase()]
            .entryPointFunctionName,
        migrationMode: mappingType?.toLowerCase(),
      },
    });
  } else {
    syncRes = await performSync(
      context,
      mapping,
      rowData,
      state,
      data,
      mappingType
    );
    await validateSyncResult(context, syncRes, mapping, mappingType, rowData);
  }

  updateExtractionStatus(context, undefined, rowData);

  await actions.refetchUiModelData({
    discardChanges: true,
    nodesToLoad: ['metadataExtractions', 'businessDataExtractions'],
  });
}

export async function openAdvanceLinking(
  context: Context,
  {
    nodeInstance,
  }: Record<
    'nodeInstance',
    Record<'data', Record<'sourceModel' | 'targetModel', string>>
  >
) {
  const mappingType = EvstMappingTypes.Technical; // Assuming technical mapping type for linking
  const {
    state: { providerName },
    actions,
    data,
    helpers,
  } = context;
  const {
    data: { sourceModel, targetModel },
  } = nodeInstance;

  if (!sourceModel || !targetModel) {
    helpers.showNotificationMessage({
      key: 'loading',
      type: 'error',
      message: 'Provide source and target model for linking',
      duration: 4,
    });
    return;
  }

  const requiredData = data?.businessDataExtractions?.find((row) => {
    return row.providerModel === sourceModel;
  });

  const mappingData = data?.config?.executions?.businessData?.find((row) => {
    return row.providerModel === sourceModel;
  });

  const targetMappingData = data?.config?.executions?.businessData?.find(
    (row) => {
      return row.providerModel === targetModel;
    }
  );

  // To do: To be updated during linking revamp
  const defaultValues = targetMappingData?.defaultValues;
  const enableExpertDataMode = mappingData?.enableExpertDataMode;

  if (
    enableExpertDataMode?.[mappingType?.toLowerCase()] &&
    enableExpertDataMode?.[mappingType?.toLowerCase()]?.linking
  ) {
    const defaultFilters =
      defaultValues['semanticDefaultFieldsModal'] ??
      defaultValues['technicalDefaultFieldsModal'];
    if (defaultFilters) {
      // TODO remove the defaultFilters option when moved to session variable
      helpers.openModal({
        title: 'Provide filter values',
        size: 'medium',
        template: `${TEMPLATE_PACKAGE_PATH}/defaultFields/maintainDefaultValues?feat-delta=true&feat-inspect=true`,
        initialState: {
          defaultFieldsModal: defaultFilters,
        },
        onModalSubmit: async (defaultValues) => {
          await actions.refetchUiModelData();
          helpers.navigate({
            to: `${TEMPLATE_PACKAGE_PATH}/dataBrowser/dataBrowser?providerModel=${sourceModel}&providerName=${providerName}&feat-delta=true&feat-inspect=true`,
            queryParams: {
              linkingModel: targetModel,
            },
            initialState: {
              model:
                enableExpertDataMode?.[mappingType?.toLowerCase()].modelUrn,
              entryPointFunctionName:
                enableExpertDataMode?.[mappingType?.toLowerCase()]
                  .entryPointFunctionName,
              migrationMode: mappingType?.toLowerCase(),
              filterConfig: defaultValues,
            },
          });
        },
      });
    } else {
      helpers.navigate({
        to: `${TEMPLATE_PACKAGE_PATH}/dataBrowser/dataBrowser?providerModel=${sourceModel}&providerName=${requiredData?.providerName}&feat-delta=true&feat-inspect=true`,
        queryParams: {
          linkingModel: targetModel,
        },
        initialState: {
          model: enableExpertDataMode?.[mappingType?.toLowerCase()].modelUrn,
          entryPointFunctionName:
            enableExpertDataMode?.[mappingType?.toLowerCase()]
              .entryPointFunctionName,
          migrationMode: mappingType?.toLowerCase(),
        },
      });
    }
  }
}

function validateSyncRequirements(
  context: Context,
  mappingName: string | undefined,
  providerName: string | undefined
): boolean {
  const { data, helpers } = context;

  if (!mappingName || !providerName) {
    showErrorNotification(
      helpers,
      '{{integrationBase.errorMappingAndProvider}}'
    );
    return false;
  }

  if (!data.connector || !data.connector.id) {
    showErrorNotification(helpers, '{{integrationBase.errorNoConnector}}');
    return false;
  }

  return true;
}

function showErrorNotification(helpers: Context['helpers'], message: string) {
  helpers.showNotificationMessage({
    key: 'loading',
    type: 'error',
    message,
    duration: 4,
  });
}

async function performSync(
  context: Context,
  mapping: ReturnType<typeof findMapping>,
  rowData: Record<'data', GetExtraction>,
  state: Context['state'],
  data: Context['data'],
  mappingType: EvstMappingTypes
) {
  const dataSyncTracking = await context.uiLoader.getSingletonInstanceOf({
    type: DataSyncTracking,
  });
  const { helpers } = context;
  const general = data.config?.general;
  const runTrackingKey = dataSyncTracking.getRunTrackingKey(rowData.data);

  if (general.isStartDateRequired && !data.settings?.startDate) {
    helpers.showToast({
      type: 'error',
      title: 'Error',
      message: '{{integrationBase.setStartDate}}',
    });

    return;
  }
  return syncData(
    context,
    {
      ...mapping,
      ...getMappingDetails(mapping, {
        fileId: undefined,
        useOverviewUpload: data.config?.templates?.overview?.useOverviewUpload,
      }),
      providerName: state.providerName,
      entityId: data.connector?.entityId,
      runTrackingKey,
    },
    {
      mappingType,
      dataload: DataLoadMode.Single,
      connectorId: data.connector?.id,
      configValues: data.connector?.configValues,
      sessionId: data.connector?.sessionId,
      startDate: data.settings?.startDate,
      endDate: data.settings?.endDate,
      extractionUUID: context.helpers.uuid(),
    }
  );
}

export async function validateSyncResult(
  context: Context,
  syncRes: {
    integration?: Awaited<Promise<StagingExecutionResponse>>;
    error?: { message: string };
  },
  mapping: ETLInfo,
  mappingType: EvstMappingTypes,
  /** optional rowData as in case of syncAll we don't have a specific rowData */
  rowData?: Record<'data', GetExtraction>
) {
  const { helpers, actions, state, data } = context;
  if (syncRes?.error) {
    helpers.showNotificationMessage({
      key: 'loading',
      type: 'error',
      message: '{{integrationBase.failedToSyncModel}}'
        .replace('$model$', mapping.displayNamePlural)
        .replace('$errorMessage', syncRes?.error?.message),
      duration: 4,
    });
  } else if (syncRes?.integration?.errors?.length) {
    helpers.showNotificationMessage({
      key: 'loading',
      type: 'error',
      message: syncRes.integration.errors.map((e) => e.message).join('\n'),
      duration: 4,
    });
  }
  // Dataload detected. Move to background job.
  else if (syncRes?.integration?.continueInTask) {
    let runTrackingKey: string;

    if (rowData) {
      /** optional rowData as in case of syncAll we don't have a specific rowData */
      const dataSyncTracking = await context.uiLoader.getSingletonInstanceOf({
        type: DataSyncTracking,
      });
      runTrackingKey = dataSyncTracking.getRunTrackingKey(rowData.data);
    }

    const jobRes = await IntegrationTemplateUI.createDataloadTask(context, {
      mappings: [
        {
          ...mapping,
          providerName: state.providerName,
          entityId: data.connector?.entityId,
          runTrackingKey,
        },
      ],
      fetching: {
        dataload: DataLoadMode.All,
        mappingType,
        connectorId: data.connector?.id,
        configValues: data.connector?.configValues,
        sessionId: data.connector?.sessionId,
        startDate: data.settings?.startDate,
        endDate: data.settings?.endDate,
        extractionUUID: context.helpers.uuid(),
      },
    }).run('integration');

    if (jobRes?.error) {
      helpers.showNotificationMessage({
        key: 'loading',
        type: 'error',
        message: '{{integrationBase.failedToCreateTask}}'.replace(
          '$errorMessage$',
          jobRes?.error?.message
        ),
        duration: 4,
      });
    } else if (jobRes?.integration?.jobId) {
      helpers.showNotificationMessage({
        key: 'loading',
        type: 'success',
        message: 'Integration continues in background due to high data volume',
        duration: 2,
      });
    }
  } else if (
    syncRes.integration?.backgroundJobId ||
    syncRes.integration?.jobId ||
    syncRes.integration?.hasRunningSubTasks
  ) {
    helpers.showNotificationMessage({
      key: 'loading',
      type: 'info',
      message: 'Integration continues in background due to high data volume',
      duration: 2,
    });
  } else {
    helpers.showNotificationMessage({
      key: 'loading',
      type: 'success',
      message: 'Data successfully loaded into Everest',
      duration: 2,
    });
  }
  updateExtractionStatus(context);
  await actions.refetchUiModelData();
}

// Sort mappings based on provided order
function sortExecutions(context: Context, executions: ETLInfo[]): ETLInfo[] {
  const { data } = context;
  const migrationOrder = data?.config?.templates?.overview?.syncOrder;

  // If no migration order is defined, return the original executions
  if (!migrationOrder || migrationOrder.length === 0) {
    return executions;
  }

  const sortedExecutions = [];

  for (const migrationInfo of migrationOrder) {
    const selectedMapping = executions.find(
      (mapping) => mapping.providerModel === migrationInfo.providerModel
    );
    sortedExecutions.push(selectedMapping);
  }

  return sortedExecutions;
}

export function disableMatching({ data }: Context) {
  return data.businessDataExtractions
    .map((e) => e.status)
    .every((status) => status === MappingStatus.SyncInProgress);
}

export function openNewMatchingModal(
  context: Context,
  rowData: Record<'data', GetExtraction>
) {
  const { helpers, actions, data } = context;

  const execution = data.config.executions.metadata.find(
    (m) => m.providerModel === rowData.data.providerModel
  );
  const extraction = data.metadataExtractions.find(
    (e) => e.providerModel === rowData.data.providerModel
  );

  const everestModel = rowData?.data?.everestModel;
  const providerName = data?.config?.general?.providerName;
  const providerModel = rowData?.data?.providerModel;

  const mapping = findMatchingMapping(context, rowData.data);

  const hasSessionVariableConfig = hasSessionVariableRelatedUrn(
    context,
    everestModel,
    EvstMappingTypes.Semantic
  );

  if (hasSessionVariableConfig) {
    helpers.openModal({
      title: `${providerName} ${providerModel} Configuration`,
      size: 'small',
      template: `${TEMPLATE_PACKAGE_PATH}/sessionVariable/sessionVariable?feat-delta=true&feat-inspect=true`,
      initialState: {
        providerName: providerName,
        providerModel: everestModel,
      },
      onModalSubmit: async () => {
        await actions.refetchUiModelData();
        helpers.openModal({
          title: `${execution?.displayName} Mapping`,
          template: `${TEMPLATE_PACKAGE_PATH}/match/match`,
          size: 'large',
          initialState: {
            mapping: mapping,
            providerName: extraction.providerName,
            providerModel: extraction.providerModel,
            entityId: data.connector?.entityId,
            mappingType: EvstMappingTypes.Semantic,
            startDate: data.settings?.startDate,
            endDate: data.settings?.endDate,
          },
          onModalSubmit: actions.refetchUiModelData,
        });
      },
    });
  } else {
    helpers.openModal({
      title: `${execution?.displayName} Mapping`,
      template: `${TEMPLATE_PACKAGE_PATH}/match/match`,
      size: 'large',
      initialState: {
        mapping: mapping,
        providerName: extraction.providerName,
        providerModel: extraction.providerModel,
        entityId: data.connector?.entityId,
      },
      onModalSubmit: async () => {
        await actions.refetchUiModelData({
          discardChanges: true,
          nodesToLoad: ['businessDataExtractions', 'metadataExtractions'],
        });
      },
    });
  }
}

// To do: Should be removed after moving to new modal
export function openMatchingModal(
  context: Context,
  rowData: Record<'data', GetExtraction>
) {
  const { helpers, actions, data } = context;

  const execution = data.config.executions.metadata.find(
    (m) => m.providerModel === rowData.data.providerModel
  );

  const extraction = data.metadataExtractions.find(
    (e) => e.providerModel === rowData.data.providerModel
  );

  const providerModel = rowData?.data?.providerModel;
  const everestModel = rowData?.data?.everestModel;

  const mapping = findMapping(context, rowData.data, EvstMappingTypes.Semantic);
  const { defaultValues } = mapping;

  if (defaultValues) {
    helpers.openModal({
      title: 'Maintain default values',
      size: 'medium',
      template: `${TEMPLATE_PACKAGE_PATH}/defaultFields/maintainDefaultValues?feat-delta=true&feat-inspect=true`,
      initialState: {
        defaultFieldsModal: defaultValues?.semanticDefaultFieldsModal,
      },
      onModalSubmit: async (defaultValues) => {
        await actions.refetchUiModelData();
        helpers.openModal({
          title: `${execution?.displayName} Mapping`,
          template: `${TEMPLATE_PACKAGE_PATH}/match/match?mode=view`,
          size: 'large',
          initialState: {
            mapping: mapping,
            title: data.config?.templates?.overview?.title,
            extraction,
            connectorId: data.connector.id,
            entityId: data.connector?.entityId,
            filterConfig: defaultValues,
            mappingType: EvstMappingTypes.Semantic,
            startDate: data.settings?.startDate,
            endDate: data.settings?.endDate,
          },
          onModalSubmit: actions.refetchUiModelData,
        });
      },
    });
  } else {
    helpers.openModal({
      title: `${execution?.displayName} Mapping`,
      template: `${TEMPLATE_PACKAGE_PATH}/match/match?mode=view`,
      size: 'large',
      initialState: {
        mapping: mapping,
        title: data.config?.templates?.overview?.title,
        extraction,
        connectorId: data.connector.id,
        entityId: data.connector?.entityId,
        filterConfig: defaultValues,
      },
      onModalSubmit: async (count) => {
        const { _nodeReference } = extraction;
        if (count === 0) {
          setUIModelInSharedState(
            context,
            'metadataExtractions',
            'status',
            MappingStatus.Matched,
            _nodeReference
          );
        }
        if (count !== 0) {
          setUIModelInSharedState(
            context,
            'metadataExtractions',
            'status',
            MappingStatus.ConfirmMatch,
            _nodeReference
          );
        }
        await actions.refetchUiModelData();
      },
    });
  }
}

export function goToApplication(
  { data, helpers }: Context,
  rowData: Record<'data', Record<'providerModel', string>>
) {
  const { applicationUrl } = [
    ...data.config.executions.metadata,
    ...data.config.executions.businessData,
  ].find((m) => m.providerModel === rowData?.data?.providerModel);
  helpers.navigate({
    to: applicationUrl,
  });
}

export function disableApplicationLink(
  { data }: Context,
  row: {
    column: string;
    rowData: Record<'providerModel', string>;
  }
) {
  const { applicationUrl } = [
    ...data.config.executions.metadata,
    ...data.config.executions.businessData,
  ].find((m) => m.providerModel === row?.rowData?.providerModel);
  return applicationUrl ? false : true;
}

export function goToSkipped(
  context: Context,
  rowData: Record<'data', GetExtraction>
) {
  goToStaging(context, rowData.data, EvstStagingStatus.Skipped);
}

export function goToFailed(
  context: Context,
  rowData: Record<'data', GetExtraction>
) {
  goToStaging(context, rowData.data, EvstStagingStatus.FAILED);
}

export function goToDrafted(
  context: Context,
  rowData: Record<'data', GetExtraction>
) {
  goToStaging(context, rowData.data, EvstStagingStatus.Drafted);
}

export function goToNeedsInteraction(
  context: Context,
  rowData: Record<'data', GetExtraction>
) {
  goToStaging(context, rowData.data, EvstStagingStatus.NeedsInteraction);
}

export function goToIntegrated(
  context: Context,
  rowData: Record<'data', GetExtraction>
) {
  goToStaging(context, rowData.data, EvstStagingStatus.Integrated);
}

function goToStaging(
  context: Context,
  extractionData: GetExtraction,
  status: EvstStagingStatus
) {
  const { helpers, state, data } = context;

  const providerModel = extractionData.providerModel;

  const nodeParams = {
    staging: {
      filters: {
        providerName: {
          $in: [state.providerName],
        },
        providerModel: {
          $eq: providerModel,
        },
        status: {
          $in: [status],
        },
        dataStatus: {
          $in: [EvstStagingDataStatus.Latest],
        },
      },
    },
  };

  if (isBusinessUserStagingViewEnabled(context)) {
    const mapping = findMapping(
      context,
      extractionData,
      EvstMappingTypes.Technical
    );
    const mappingName =
      mapping?.mappings?.api?.name ?? mapping?.mappings?.file?.name;
    const sessionId = data?.connector?.sessionId;

    helpers.navigate({
      to: `${TEMPLATE_PACKAGE_PATH}/simpleStaging/listStaging`,
      nodeParams,
      initialState: {
        providerName: state.providerName,
        providerModel,
        mapping: mappingName,
        session: sessionId,
      },
    });
  } else {
    helpers.navigate({
      to: '/templates/everest.fin.integration.base/staging/uinext/list',
      nodeParams,
    });
  }
}

export function disableStagingLink(
  _context: Context,
  row: {
    column: Record<'field', string>;
    rowData: Record<
      'totalSkipped' | 'totalFailed' | 'totalIntegrated' | 'totalMatched',
      number
    >;
  }
) {
  return row?.rowData?.[row?.column?.field] ? false : true;
}

// =========================================================
// Linking
// =========================================================

export async function getProviderModels({ data }: Context) {
  if (data.config === undefined) {
    return;
  }

  const executions = [
    ...data.config.executions.metadata,
    ...data.config.executions.businessData,
  ];
  if (executions.length === 0) {
    return;
  }

  const models = [];
  for (const m of executions) {
    const { providerModel, displayName, everestModel } = m;
    models.push({
      providerModel,
      displayName,
      everestModel,
    });
  }
  return models;
}

export async function performLinking(
  context: Context,
  {
    nodeInstance,
  }: Record<
    'nodeInstance',
    Record<'data', Record<'sourceModel' | 'targetModel', string>>
  >
) {
  const {
    state: { providerName },
    data,
    helpers,
  } = context;
  const {
    data: { sourceModel, targetModel },
  } = nodeInstance;
  if (!sourceModel || !targetModel) {
    helpers.showNotificationMessage({
      key: 'loading',
      type: 'error',
      message: 'Provide source and target model for linking',
      duration: 4,
    });
    return;
  }

  const settingsId = data.settings?.settingsId;
  const linkingRes = await IntegrationTemplateUI.performLinking(context, {
    providerName,
    sourceModel,
    targetModel,
    settingsId,
  }).run('integration');
  if (
    linkingRes?.integration &&
    typeof linkingRes.integration === 'object' &&
    'continueInTask' in linkingRes.integration
  ) {
    helpers.showNotificationMessage({
      key: 'loading',
      type: 'success',
      message: 'Linking will be executed in a background task',
      duration: 3,
    });
    return;
  }

  if (!linkingRes?.error) {
    helpers.showNotificationMessage({
      key: 'loading',
      type: 'success',
      message: 'Data successfully loaded into Everest',
      duration: 2,
    });
    return;
  }
}

export async function openBusinessObjectLinking(
  context: Context,
  {
    nodeInstance,
  }: Record<
    'nodeInstance',
    Record<'data', Record<'sourceModel' | 'targetModel', string>>
  >
) {
  const { helpers, state } = context;

  const {
    data: { sourceModel, targetModel },
  } = nodeInstance;

  if (!sourceModel || !targetModel) {
    helpers.showNotificationMessage({
      key: 'loading',
      type: 'error',
      message: 'Provide source and target model for linking',
      duration: 4,
    });

    return;
  }

  const sourceUrn = getModelUrnByProviderModel(context, sourceModel);
  const targetUrn = getModelUrnByProviderModel(context, targetModel);

  helpers.openModal({
    title: `Link ${sourceModel} to ${targetModel}`,
    size: 'medium',
    template: `${TEMPLATE_PACKAGE_PATH}/linking/businessObjectLinking/businessObjectLinking?feat-delta=true&feat-inspect=true`,
    initialState: {
      sourceUrn,
      targetUrn,
      sourceModelName: sourceModel,
      targetModelName: targetModel,
      providerName: state.providerName,
    },
  });
}

export async function openManualLinking(
  context: Context,
  {
    nodeInstance,
  }: Record<
    'nodeInstance',
    Record<'data', Record<'sourceModel' | 'targetModel', string>>
  >
) {
  const { helpers, state } = context;

  const {
    data: { sourceModel, targetModel },
  } = nodeInstance;

  if (!sourceModel || !targetModel) {
    helpers.showNotificationMessage({
      key: 'loading',
      type: 'error',
      message: 'Provide source and target model for linking',
      duration: 4,
    });

    return;
  }
  helpers.openModal({
    title: `Link ${sourceModel} to ${targetModel}`,
    size: 'medium',
    template: `${TEMPLATE_PACKAGE_PATH}/linking/manualLinking/manualLinking?feat-delta=true&feat-inspect=true`,
    initialState: {
      sourceModel,
      targetModel,
      providerName: state.providerName,
    },
  });
}

export function getModelUrnByProviderModel(
  context: Context,
  providerModel: string
) {
  const { data } = context;

  const mappings = [
    ...data.config.executions.metadata,
    ...data.config.executions.businessData,
  ];

  return mappings.find((mapping) => mapping.providerModel === providerModel)
    ?.everestModel;
}

export function isBusinessObjectLinkingVisible(context: Context) {
  const { data } = context;

  return Boolean(data.config?.general?.enableSimpleLink);
}

export function isAdvanceLinkingVisible(context: Context) {
  const { data } = context;

  return Boolean(data.config?.general?.enableAdvanceLink);
}

export function isManualLinkingVisible(context: Context) {
  const { data } = context;

  return Boolean(data.config?.general?.enableManualLink);
}

export function isBusinessUserStagingViewEnabled(context: Context) {
  const { data } = context;

  return Boolean(data.config?.general?.enableBusinessUserStagingView);
}

// =========================================================
// Errors
// =========================================================

export function hasErrors({ data }: Context) {
  if (!data.metadataExtractions && !data.businessDataExtractions) {
    return;
  }
  return [...data.metadataExtractions, ...data.businessDataExtractions].some(
    (e) => e.totalFailed > 0
  );
}

export function getErrorsStatus({ data }: Context) {
  if (!data.metadataExtractions && !data.businessDataExtractions) {
    return;
  }
  const failedExtractions = [
    ...data.metadataExtractions,
    ...data.businessDataExtractions,
  ].filter((e) => e.totalFailed > 0)?.length;
  return '{{integrationBase.toResolve}}'.replace(
    '$num$',
    failedExtractions.toString()
  );
}

export function getErrors(context: Context): Error[] {
  const errorConfigs = [
    {
      key: 'semanticMetadataExtractions',
      errorMessage: '{{integrationBase.unmapped}}',
      action: openMatchingModal,
    },
    {
      key: 'semanticBusinessDataExtractions',
      errorMessage: '{{integrationBase.unmapped}}',
      action: openMatchingModal,
    },
    {
      key: 'metadataExtractions',
      errorMessage: '{{integrationBase.failedToIntegrate}}',
      action: goToFailed,
    },
    {
      key: 'businessDataExtractions',
      errorMessage: '{{integrationBase.failedToIntegrate}}',
      action: goToFailed,
    },
  ];

  return errorConfigs.flatMap(({ key, errorMessage, action }) => {
    const extractions = context.data[key] as GetExtraction[] | undefined;
    return extractions
      ? getErrorsInner(
          context,
          extractions.filter((e) => e.totalFailed > 0),
          errorMessage,
          action
        )
      : [];
  });
}

function getErrorsInner(
  context: Context,
  extractions: GetExtraction[],
  description: string,
  review: (
    context: Context,
    rowData: Record<'data', Record<'providerModel', string>>
  ) => void
) {
  const errors = [];
  for (const e of extractions) {
    errors.push({
      status: 'invalid',
      label: e.displayNamePlural,
      description: description.replace('$num$', e.totalFailed.toString()),
      buttonActions: [
        {
          shape: 'pill',
          label: '{{integrationBase.review}}',
          onClick: () =>
            review(context, { data: { providerModel: e.providerModel } }),
          disabled: disableMatching(context),
        },
      ],
    });
  }
  return errors;
}

// =========================================================
// Polling
// =========================================================

export async function getTaskInformation(context: Context) {
  const { state, data } = context;
  if (state.taskPollingInterval) {
    return;
  }

  if (data?.tasks?.hasRunningTask) {
    updateExtractionStatus(context, MappingStatus.SyncInProgress);
    poll(context);
  }
  return;
}

function poll(context: Context) {
  const { state } = context;

  state.taskPollingInterval = setInterval(() => {
    IntegrationTemplateUI.getTaskInformation(context, {
      providerName: state.providerName,
    })
      .run('integration')
      .then((response) => {
        if (response?.integration?.hasRunningTask) {
          triggerRefetch(context);
        }
      })
      .catch(() => {
        clearInterval(state.taskPollingInterval);
        state.taskPollingInterval = undefined;
      })
      .finally(() => {
        clearInterval(state.taskPollingInterval);
        state.taskPollingInterval = undefined;
        updateExtractionStatus(context);
        triggerRefetch(context);
      });
  }, 5000);
}

function triggerRefetch({ actions }: Context) {
  actions
    .refetchUiModelData()
    .then(() => ({}))
    .catch(() => ({}));
}

export function getLastExecutionDate(
  _context: Context,
  rowData: { data: { lastExecutionDate: Date } }
) {
  return rowData?.data?.lastExecutionDate
    ? new Date(rowData.data.lastExecutionDate.toString()).toLocaleString(
        'en-US'
      )
    : '{{integrationBase.never}}';
}

export function isLinkingDropdownDisabled(
  _context: Context,
  rowData: { rowData: { executed: boolean } }
) {
  return !rowData?.rowData?.executed;
}

function getExecutionsNotExcluded<T extends { providerModel: string }>(
  context: Context,
  executions: T[]
): T[] {
  const { data } = context;

  return executions.filter(
    (execution) =>
      !data.settings?.excludedModels?.some(
        (excludeModel) =>
          excludeModel.excluded &&
          excludeModel.providerModel === execution.providerModel
      )
  );
}
