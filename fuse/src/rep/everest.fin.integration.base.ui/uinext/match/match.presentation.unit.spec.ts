import type { ISession } from '@everestsystems/content-core';
import { IDENTIFIER } from '@everestsystems/content-core';
import { FileStorage } from '@pkg/everest.appserver/types/FileStorage';
import autoMatch from '@pkg/everest.fin.integration.base.ui/actions/ai/autoMatch.action';
import getMatches from '@pkg/everest.fin.integration.base.ui/actions/matching/getMatches.action';
import getMatchingCandidates from '@pkg/everest.fin.integration.base.ui/actions/matching/getMatchingCandidates.action';
import getMatchingPrimitiveTypes from '@pkg/everest.fin.integration.base.ui/actions/matching/getMatchingPrimitiveTypes.action';
import { EvstExtractionProviders } from '@pkg/everest.fin.integration.base/types/enums/ExtractionProviders';
import { EvstStagingStatus } from '@pkg/everest.fin.integration.base/types/enums/StagingStatus';
import { Staging } from '@pkg/everest.fin.integration.base/types/Staging';

import presentationImplementation, { DataSource } from './match.presentation';

jest.mock('@everestsystems/content-core', () => ({
  IDENTIFIER: Symbol('IDENTIFIER'),
  METADATA: Symbol('METADATA'),
  ModelUrn: {
    parse: jest.fn(),
    tryParse: jest.fn(),
  },
}));

jest.mock(
  '@pkg/everest.fin.integration.base.ui/actions/matching/getRequiredMatchingFields.action',
  () => ({
    __esModule: true,
    default: jest.fn().mockImplementation((env, matches) => matches),
  })
);

jest.mock('@pkg/everest.appserver/types/FileStorage', () => ({
  FileStorage: {
    client: jest.fn(),
  },
}));

jest.mock('@pkg/everest.fin.integration.base.ui/actions/ai/autoMatch.action');
jest.mock(
  '@pkg/everest.fin.integration.base.ui/actions/matching/getMatches.action'
);
jest.mock(
  '@pkg/everest.fin.integration.base.ui/actions/matching/getMatchingCandidates.action'
);
jest.mock(
  '@pkg/everest.fin.integration.base.ui/actions/matching/getMatchingPrimitiveTypes.action'
);
jest.mock('@pkg/everest.fin.integration.base/types/Staging');
jest.mock(
  '@pkg/everest.fin.integration.base.ui/types/interfaces/configurationConnectivity',
  () => ({
    configurationConnectivity: {
      formatMatches: jest.fn(),
    },
  })
);

describe('DataSource', () => {
  let dataSource: DataSource;
  let mockSession: ISession;

  beforeEach(() => {
    dataSource = new DataSource();
    mockSession = {} as ISession;
    jest.clearAllMocks();
  });

  describe('getDynamicFieldsMetadata', () => {
    it('should return empty object if providerName or providerModel is not provided', async () => {
      const result = await dataSource.getDynamicFieldsMetadata({
        session: mockSession,
        parameters: {},
      } as any);
      expect(result).toEqual({});
    });

    it('should return fields metadata', async () => {
      (getMatchingPrimitiveTypes as jest.Mock).mockResolvedValue({
        field1: { urn: 'urn1', name: 'Field1' },
        field2: { urn: 'urn2', name: 'Field2' },
      });

      const result = await dataSource.getDynamicFieldsMetadata({
        session: mockSession,
        parameters: { providerName: 'provider', providerModel: 'model' },
      } as any);

      expect(result).toHaveProperty('staticField');
      expect(result).toHaveProperty('originalId');
      expect(result).toHaveProperty('field1');
      expect(result).toHaveProperty('field2');
    });
  });

  describe('query', () => {
    it('should return filtered data when toggle is true', async () => {
      (getMatchingPrimitiveTypes as jest.Mock).mockResolvedValue({});
      (getMatches as jest.Mock).mockResolvedValue([
        { id: 1, name: 'Item 1', everestId: 1 },
        { id: 2, name: 'Item 2', everestId: undefined },
      ]);

      const result = await dataSource.query({
        session: mockSession,
        parameters: {
          providerName: 'provider',
          providerModel: 'model',
          toggle: true,
          mapping: { everestModel: 'model' },
        },
        queryInstruction: { page: { skip: 0, take: 10 } },
      } as any);

      expect(result).toHaveLength(1);
      expect(result[0].staticField).toBe('Item 2');
    });

    it('should preserve the order from formatMatches', async () => {
      const providerName = EvstExtractionProviders.Quickbooks;

      // Setup mock data with intentional sorting (A, B, C)
      const mockFormattedItems = [
        { id: 2, name: 'Item A' },
        { id: 3, name: 'Item B' },
        { id: 1, name: 'Item C' },
      ];

      // Mock getMatches to return our pre-sorted data
      (getMatches as jest.Mock).mockResolvedValue(mockFormattedItems);

      // Test if DataSource preserves the order
      dataSource = new DataSource();

      const result = await dataSource.query({
        session: mockSession,
        parameters: {
          providerName,
          providerModel: 'model',
          mapping: { everestModel: 'urn:model:Test' },
        },
        queryInstruction: { page: { skip: 0, take: 10 } },
      } as any);

      // Extract only the id and name (as staticField) for comparison
      const simplifiedResults = result.map((item) => ({
        id: item.id,
        name: item.staticField,
      }));

      // Verify the exact order and content
      expect(simplifiedResults).toStrictEqual([
        { id: 2, name: 'Item A' },
        { id: 3, name: 'Item B' },
        { id: 1, name: 'Item C' },
      ]);

      // Verify getMatches was called with the right parameters
      expect(getMatches).toHaveBeenCalledWith(
        mockSession,
        'urn:model:Test',
        providerName,
        'model',
        undefined
      );
    });
  });

  describe('update', () => {
    it('should update the specified field and mark as changed', async () => {
      dataSource['data'] = [
        { [IDENTIFIER]: 1, field1: 'old value', valueChanged: false },
      ];

      await dataSource.update({
        session: mockSession,
        parameters: { providerName: 'provider', providerModel: 'model' },
        path: [1],
        fieldName: 'field1',
        newFieldValue: 'new value',
      } as any);

      expect(dataSource['data'][0].field1).toBe('new value');
      expect(dataSource['data'][0].valueChanged).toBe(true);
    });
  });

  describe('execute_save', () => {
    it('should call rematchStagingItems for changed items', async () => {
      const mockRematch = jest.fn();
      (Staging.client as jest.Mock).mockResolvedValue({
        rematchStagingItems: mockRematch,
      });

      const providerName = 'provider';
      const providerModel = 'model';
      const everestModel = 'model';

      dataSource['data'] = [
        {
          [IDENTIFIER]: '1',
          originalId: '1',
          everestId: 1,
          status: EvstStagingStatus.MatchingFailed,
          valueChanged: true,
        },
        {
          [IDENTIFIER]: '2',
          originalId: '2',
          everestId: 2,
          status: EvstStagingStatus.Matched,
          valueChanged: true,
        },
        {
          [IDENTIFIER]: '3',
          originalId: '3',
          everestId: 3,
          status: EvstStagingStatus.NeedsInteraction,
          valueChanged: false, // This item should be ignored since valueChanged is false
        },
      ];

      await dataSource.execute_save({
        session: mockSession,
        parameters: {
          providerName,
          providerModel,
          mapping: { everestModel },
        },
      } as any);

      // Check that rematchStagingItems was called exactly twice (only for changed items)
      expect(mockRematch).toHaveBeenCalledTimes(2);

      // Check the parameters of the first call
      expect(mockRematch.mock.calls[0][0]).toEqual({
        providerName,
        providerModel,
        matches: [
          {
            originalId: dataSource['data'][0].originalId,
            everestNodeReference: {
              id: dataSource['data'][0].everestId,
              modelUrn: everestModel,
            },
            integratedItems: [],
          },
        ],
      });

      // Check the parameters of the second call
      expect(mockRematch.mock.calls[1][0]).toEqual({
        providerName,
        providerModel,
        matches: [
          {
            originalId: dataSource['data'][1].originalId,
            everestNodeReference: {
              id: dataSource['data'][1].everestId,
              modelUrn: everestModel,
            },
            integratedItems: [],
          },
        ],
      });
    });
  });

  describe('execute_autoMatchAI', () => {
    it('should perform auto-matching using AI', async () => {
      (getMatchingPrimitiveTypes as jest.Mock).mockResolvedValue({
        everestId: { modelUrn: 'urn:model:Test' },
      });
      (getMatchingCandidates as jest.Mock).mockResolvedValue([]);
      (autoMatch as jest.Mock).mockResolvedValue([
        { originalId: '1', everestId: 100 },
      ]);

      dataSource['data'] = [
        { [IDENTIFIER]: '1', originalId: '1', staticField: 'Item 1' },
      ];

      await dataSource.execute_autoMatchAI({
        session: mockSession,
        parameters: { providerName: 'provider', mapping: {} },
      } as any);

      expect(dataSource['data'][0].everestId).toBe(100);
    });
  });

  describe('execute_cleanDataForExport', () => {
    it('should clean data for export', async () => {
      dataSource['data'] = [
        { [IDENTIFIER]: '1', staticField: 'Item 1', field1: 'value1' },
      ];
      dataSource['allFields'] = {
        staticField: { urn: 'urn:staticField', label: 'Static Field' },
        field1: { urn: 'urn:field1', label: 'Field 1' },
      };

      const result = await dataSource.execute_cleanDataForExport();

      expect(result.cleanedData).toEqual([
        { 'Static Field': 'Item 1', 'Field 1': 'value1' },
      ]);
    });
  });

  describe('execute_importMatchings', () => {
    it('should import matchings from CSV', async () => {
      const mockProcessCSVFiles = jest
        .fn()
        .mockResolvedValue([
          { 'Static Field': 'Item 1', 'Field 1': 'Match 1' },
        ]);
      (FileStorage.client as jest.Mock).mockResolvedValue({
        processCSVFiles: mockProcessCSVFiles,
      });

      dataSource['allFields'] = {
        staticField: { urn: 'urn:staticField', label: 'Static Field' },
        field1: { urn: 'urn:field1', label: 'Field 1' },
      };
      dataSource['data'] = [
        { [IDENTIFIER]: '1', staticField: 'Item 1', field1: undefined },
      ];
      dataSource['matchingPrimitiveTypes'] = {
        everestId: {
          urn: 'urn:everestId',
          name: 'Everest Id',
          modelUrn: 'urn:model:Test',
        },
        field1: {
          urn: 'urn:field1',
          name: 'Field 1',
          modelUrn: 'urn:model:Test',
        },
      };
      (getMatchingCandidates as jest.Mock).mockResolvedValue([
        { id: 1, name: 'Match 1' },
      ]);

      await dataSource.execute_importMatchings({
        session: mockSession,
        input: { fileId: 'file1' },
        parameters: { providerName: 'provider' },
      } as any);

      expect(dataSource['data'][0].field1).toBe(1);
    });
  });
});

describe('presentationImplementation', () => {
  it('should return a DataSource instance', () => {
    const dataSource = presentationImplementation.dataSource();
    expect(dataSource).toBeInstanceOf(DataSource);
  });
});
