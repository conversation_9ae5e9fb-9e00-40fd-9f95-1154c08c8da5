{"version": 2, "uicontroller": "listStaging.uicontroller.ts", "uimodel": {"nodes": {"staging": {"type": "list", "pagination": true, "modelId": "everest.fin.integration.base/StagingModel.Staging", "fieldList": ["originalId", "dataVersion", "status", "dataStatus", "errorMessage", "providerModel", "providerName", "everestModel", "everestId", "dataExtractionId", "data", "isActive", "hierarchyLevel", "parentOriginalId", "description"]}}}, "uiview": {"layout": "everest.base.ui/layouts/uilayout/landings/hybridInput/hybridInput", "props": {"title": "@controller:getTitle()", "subtitle": "@controller:getSubtitle()", "tabTitle": "@controller:getTabTitle()", "headerConfig": {"size": "medium"}, "allowRefreshData": true, "autoRefreshData": true, "type": "secondary", "wrapWithGrid": true, "i18n": "everest.migration.base/migrationBase", "headerActions": {"direction": "vertical", "align": "right", "content": [{"variant": "primary", "label": "@controller:getNavigateToMigrationOverviewPageName()", "onClick": "@controller:navigateToMigrationOverviewPage"}]}, "mainBlock": {"visible": false}, "inputData": {"visible": false}, "customSections": [{"component": "Table", "section": {"customId": "stagingTable", "editing": false, "grid": {"size": "12"}}, "props": {"data": "@binding:staging", "variant": "light", "onRowClicked": "@controller:navigateToView", "columns": [{"headerName": "@controller:getIntegrationSourceIDName()", "field": "originalId"}, {"headerName": "{{migrationBase.everestId}}", "field": "everestId"}, {"headerName": "{{migrationBase.description}}", "field": "description", "minWidth": 550}, {"headerName": "{{migrationBase.IntegrationStatus}}", "field": "status", "cellVariant": {"variant": "badge", "matchers": "@controller:getStatusBadgeColor"}}, {"headerName": "{{migrationBase.errorMessageDetailed}}", "field": "errorMessage", "minWidth": 550}]}}]}}}