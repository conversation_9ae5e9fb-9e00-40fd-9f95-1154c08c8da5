{"version": 2, "uicontroller": "viewStaging.uicontroller.ts", "uimodel": {"nodes": {"staging": {"type": "struct", "modelId": "everest.fin.integration.base/StagingModel.Staging", "read": {"where": {"id": "@state:param.id"}}, "fieldList": ["id", "originalId", "providerName", "providerModel", "everestId", "everestModel", "data", "errorMessage", "transformedData", "status", "dataStatus", "dataVersion", "extraData", "description"]}, "integration": {"type": "struct", "modelId": "everest.fin.integration.base.ui/IntegrationTemplateModel.IntegrationTemplate"}}}, "uiview": {"layout": "everest.base.ui/layouts/uilayout/landings/hybridInput/hybridInput", "props": {"title": "@controller:getTitle()", "subtitle": "@controller:getSubtitle()", "description": "@controller:getDescription()", "tabTitle": "@controller:getTabTitle()", "status": "@controller:getAlertTitle()", "headerConfig": {"size": "medium"}, "allowRefreshData": true, "autoRefreshData": true, "type": "secondary", "wrapWithGrid": true, "i18n": "everest.migration.base/migrationBase", "headerActions": {"direction": "vertical", "align": "right", "content": [{"variant": "primary", "label": "@controller:getPrimaryActionName()", "onClick": "@controller:executePrimaryAction", "disabled": "@controller:isPrimaryActionButtonDisabled()", "visible": "@controller:isPrimaryActionButtonVisible()"}, {"label": "{{migrationBase.MoreActions}}", "variant": "secondary", "visible": true, "actions": [{"actions": [{"variant": "secondary", "label": "@controller:getSecondaryActionName()", "onClick": "@controller:executeSecondaryAction", "disabled": "@controller:isSecondaryActionButtonDisabled()", "visible": "@controller:isSecondaryActionButtonVisible()"}, {"variant": "secondary", "label": "{{migrationBase.DeveloperView}}", "onClick": "@controller:navigateToStaging", "visible": true, "disabled": false}]}]}]}, "mainBlock": {"visible": false}, "inputData": {"visible": false}, "customSections": [{"component": "<PERSON><PERSON>", "section": {"grid": {"size": "12"}}, "props": {"variant": "@controller:getAlertVariant()", "title": "@controller:getAlertTitle()", "content": "@controller:get<PERSON>lertContent()"}}, {"title": "Staging Details", "component": "FieldGroup", "size": "12", "columns": 4, "elements": [{"label": "@controller:getIntegrationSourceIDName()", "value": "@binding:staging.originalId"}, {"label": "@controller:getIntegrationSourceModelName()", "value": "@binding:staging.providerModel"}, {"label": "{{migrationBase.everestId}}", "value": "@binding:staging.everestId", "link": "@controller:getLinkToIntegratedItemWrapper()"}, {"label": "{{migrationBase.everestModel}}", "value": "@binding:staging.everestModel"}, {"label:": "{{migrationBase.description}}", "value": "@binding:staging.description", "size": 2}, {"label:": "{{migrationBase.errorMessageDetailed}}", "value": "@binding:staging.errorMessage", "size": 2}]}, {"title": "{{migrationBase.StagingData}}", "component": "FieldGroup", "size": "12", "columns": 2, "elements": [{"component": "Editor", "size": "1", "label": "@controller:getExtractedDataEditorTitle()", "value": "@binding:staging.data", "action": "update", "isEditing": "@controller:isExtractedDataEditable()", "editorConfig": {"language": "json", "height": 900, "options": {"formatOnPaste": true, "formatOnType": true, "lineNumbers": "on", "folding": true}}}, {"component": "Editor", "size": "1", "label": "@controller:getTransformedDataEditorTitle()", "value": "@binding:staging.transformedData", "action": "update", "isEditing": "@controller:isTransformedDataEditable()", "isVisible": "@controller:isTransformedDataVisible()", "editorConfig": {"language": "json", "height": 900, "options": {"formatOnPaste": false, "formatOnType": false, "lineNumbers": "on", "folding": true}}}]}]}}}