import type { ISession } from '@everestsystems/content-core';
import { PlainDate } from '@everestsystems/datetime';
import type { CurrencyRevalDataType } from '@pkg/everest.fin.accounting/public/currencyRevaluation/types';
import { CurrencyRevaluation } from '@pkg/everest.fin.accounting/types/CurrencyRevaluation';
import type { CurrencyRevaluationTypes } from '@pkg/everest.fin.processor.common/public/types/currencyRevaluation';
import type { EvstEntityBook } from '@pkg/everest.fin.processor/types/composites/EntityBook';

import calculateNonCashAccountsRevaluation from './calculateNonCashAccountsRevaluation.action';

// Mock the CurrencyRevaluation module
jest.mock('@pkg/everest.fin.accounting/types/CurrencyRevaluation', () => ({
  CurrencyRevaluation: {
    client: jest.fn(),
  },
}));

describe('calculateNonCashAccountsRevaluation', () => {
  // Mock data for testing
  const mockSession = {} as ISession;
  const mockEntityBook = {
    entityId: 123,
    bookId: 456,
  } as EvstEntityBook;

  const mockPreviousStageResult =
    {} as CurrencyRevaluationTypes.PreviousStageResult;
  const mockProcessParams = {
    revaluationDate: PlainDate.from('2023-12-31'),
  } as CurrencyRevaluationTypes.ProcessParameters;

  // Sample revaluation data to return from mock
  const mockRevalData: CurrencyRevalDataType[] = [];

  const mockCalculateNonCashAccountsRevaluation = jest
    .fn()
    .mockResolvedValue(mockRevalData);
  const mockClient = {
    calculateNonCashAccountsRevaluation:
      mockCalculateNonCashAccountsRevaluation,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (CurrencyRevaluation.client as jest.Mock).mockResolvedValue(mockClient);
  });

  it('should call CurrencyRevaluation.client with session', async () => {
    await calculateNonCashAccountsRevaluation(
      mockSession,
      mockEntityBook,
      mockPreviousStageResult,
      mockProcessParams
    );

    expect(CurrencyRevaluation.client).toHaveBeenCalledWith(mockSession);
  });

  it('should call calculateNonCashAccountsRevaluation with correct parameters', async () => {
    await calculateNonCashAccountsRevaluation(
      mockSession,
      mockEntityBook,
      mockPreviousStageResult,
      mockProcessParams
    );

    expect(mockCalculateNonCashAccountsRevaluation).toHaveBeenCalledWith(
      mockEntityBook.entityId,
      mockProcessParams.revaluationDate,
      mockEntityBook.bookId
    );
  });

  it('should return revaluation data with correct options', async () => {
    const result = await calculateNonCashAccountsRevaluation(
      mockSession,
      mockEntityBook,
      mockPreviousStageResult,
      mockProcessParams
    );

    expect(result).toEqual({
      revalData: mockRevalData,
      options: {
        recognizeUnrealizedGain: true,
        recognizeUnrealizedLoss: true,
      },
    });
  });
});
