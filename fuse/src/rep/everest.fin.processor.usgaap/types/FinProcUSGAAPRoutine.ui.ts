/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// ******************************************************************************
// Generated Everest Types
// ******************************************************************************

import type { Decimal, RoundingMode } from '@everestsystems/decimal';
import type {
  ArgType,
  Association,
  AssociationManagementArgs,
  AsyncResult,
  Controller,
  ControllerClientProvider,
  CreateOptionsArg,
  DataWithMetadata,
  DeleteOptionsArg,
  FieldSelector,
  Filter,
  flattenFieldSelector,
  GraphInstanceEntry,
  KernelRequestOptions,
  NonAssociationFields,
  Optional,
  PurgeArgs,
  ReadOptionsArg,
  ReadReturnType as ReadReturnTypeGeneric,
  ReadReturnTypeWithAssociations,
  RequireAtLeastOne,
  SemanticSearchArg,
  SemanticSearchRequestOptions,
  TypeSafeQueryArgType,
  UpdateManyOptionsArg,
  UpdateOptionsArg,
  UpsertOptionsArg,
  WriteReturnType,
  WriteReturnTypeWithAssociations,
} from '@everestsystems/content-core';

import { UIExecutionContext } from '@everestsystems/content-core';
import type { FinProcUSGAAPRoutine as everest_fin_processor_usgaap_model_node_FinProcUSGAAPRoutine } from "@pkg/everest.fin.processor.usgaap/types/FinProcUSGAAPRoutine";
import type orig_calculateNonCashAccountsRevaluation from "@pkg/everest.fin.processor.usgaap/FinProcUSGAAPRoutine/algorithms/CurrencyRevaluation/calculateNonCashAccountsRevaluation.action";
import type orig_calculateCashAccountsRevaluation from "@pkg/everest.fin.processor.usgaap/FinProcUSGAAPRoutine/algorithms/CurrencyRevaluation/calculateCashAccountsRevaluation.action";

/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */

type RequiredContext = {
  data: Record<string, any>;
  actions: {
    run: (...args: any[]) => any;
  };
};

type ApiError = {
  message: string;
  name: string;
  errorCode: string;
  stack?: string;
};

type InnerPromiseType<T> = T extends Promise<infer R> ? R : T;

type ActionResultOrError<T1 extends string | number | symbol, T2> = {[P in T1]?: T2} & {error?: ApiError};

type ArrayIfNeeded<C extends RequiredContext, K extends keyof C['data'], T> = C['data'][K] extends UIExecutionContext.UIModelNodeListType<any> ? T[] : T;

export namespace FinProcUSGAAPRoutineUI {
  /**
   * Financial Processor USGAAP Routine
   */
  export type FinProcUSGAAPRoutineWithAssociation = FinProcUSGAAPRoutine & {

    };
  export interface IControllerClient extends everest_fin_processor_usgaap_model_node_FinProcUSGAAPRoutine.IControllerClient {}

  export type FinProcUSGAAPRoutine = everest_fin_processor_usgaap_model_node_FinProcUSGAAPRoutine.FinProcUSGAAPRoutine;
  export type UniqueFields = Pick<FinProcUSGAAPRoutine, 'id'>;
  export type UniqueWhereInput = (RequireAtLeastOne<UniqueFields>) & Partial<FinProcUSGAAPRoutine>;
  export type ReadReturnType<U extends string | number | symbol = keyof FinProcUSGAAPRoutine> = ReadReturnTypeGeneric<FinProcUSGAAPRoutine, U>;

  class ActionRequest<C extends RequiredContext, FuncReturnType> {
    constructor(private readonly context: C, private readonly partialPayload: Record<string, any>) {
    }

    private async __run__(payload: any) {
      return this.context.actions.run(payload);
    }

    payload<K extends keyof C['data']>(nodeAlias: K): Record<string, any> {
      return {[nodeAlias]: this.partialPayload};
    }

    run<K extends keyof C['data']>(nodeAlias: K): Promise<ActionResultOrError<K, ArrayIfNeeded<C, K, InnerPromiseType<FuncReturnType>>>> {
      const payload = this.payload(nodeAlias);
      return this.__run__(payload);
    }
  }

  type func_calculateNonCashAccountsRevaluation = typeof orig_calculateNonCashAccountsRevaluation;
  /** @deprecated use ```client.calculateNonCashAccountsRevaluation``` instead */
  export function calculateNonCashAccountsRevaluation<C extends RequiredContext>(context: C, args: { entityBook: Parameters<func_calculateNonCashAccountsRevaluation>[1]; previousStageResult: Parameters<func_calculateNonCashAccountsRevaluation>[2]; processParams: Parameters<func_calculateNonCashAccountsRevaluation>[3] }): ActionRequest<C, ReturnType<func_calculateNonCashAccountsRevaluation>> {
    const partialPayload = {action: 'calculateNonCashAccountsRevaluation', data: args}

    return new ActionRequest<C, ReturnType<func_calculateNonCashAccountsRevaluation>>(context, partialPayload);
  }
  type func_calculateCashAccountsRevaluation = typeof orig_calculateCashAccountsRevaluation;
  /** @deprecated use ```client.calculateCashAccountsRevaluation``` instead */
  export function calculateCashAccountsRevaluation<C extends RequiredContext>(context: C, args: { entityBook: Parameters<func_calculateCashAccountsRevaluation>[1]; previousStageResult: Parameters<func_calculateCashAccountsRevaluation>[2]; processParams: Parameters<func_calculateCashAccountsRevaluation>[3] }): ActionRequest<C, ReturnType<func_calculateCashAccountsRevaluation>> {
    const partialPayload = {action: 'calculateCashAccountsRevaluation', data: args}

    return new ActionRequest<C, ReturnType<func_calculateCashAccountsRevaluation>>(context, partialPayload);
  }
  /** @deprecated - use {@link MODEL_URN} instead */
  export const name = 'urn:evst:everest:fin/processor/usgaap:model/node:FinProcUSGAAPRoutine';
  export const MODEL_URN = 'urn:evst:everest:fin/processor/usgaap:model/node:FinProcUSGAAPRoutine';
  /** @deprecated - use {@link MODEL_URN} instead */
  export const MODEL_ID = 'everest.fin.processor.usgaap/FinProcUSGAAPRoutineModel.FinProcUSGAAPRoutine';
  export const MODEL_UUID = 'd21a0323-79c2-4a46-9778-5a05a85d965a';


  export namespace client {
    /** [US GAAP] Calculates non cash accounts for revaluation */
    export declare function calculateNonCashAccountsRevaluation(args: { entityBook: Parameters<func_calculateNonCashAccountsRevaluation>[1]; previousStageResult: Parameters<func_calculateNonCashAccountsRevaluation>[2]; processParams: Parameters<func_calculateNonCashAccountsRevaluation>[3] }): ReturnType<func_calculateNonCashAccountsRevaluation>
    /** [US GAAP] Calculates cash accounts for revaluation */
    export declare function calculateCashAccountsRevaluation(args: { entityBook: Parameters<func_calculateCashAccountsRevaluation>[1]; previousStageResult: Parameters<func_calculateCashAccountsRevaluation>[2]; processParams: Parameters<func_calculateCashAccountsRevaluation>[3] }): ReturnType<func_calculateCashAccountsRevaluation>

  }
}
