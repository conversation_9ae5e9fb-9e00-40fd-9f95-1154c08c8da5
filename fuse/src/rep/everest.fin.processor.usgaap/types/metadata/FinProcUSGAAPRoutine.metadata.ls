package everest.fin.processor.usgaap

node FinProcUSGAAPRoutine {
	description: 'Financial Processor USGAAP Routine'
	documentation: 'Financial Processor USGAAP Routine is an API entry point for routine algorithm actions'
	label: 'Financial Processor USGAAP Routine'
	transient
	query action calculateNonCashAccountsRevaluation(entityBook: composite<everest.fin.processor::EntityBook>, previousStageResult: UnknownObject, processParams: UnknownObject): UnknownObject
	query action calculateCashAccountsRevaluation(entityBook: composite<everest.fin.processor::EntityBook>, previousStageResult: UnknownObject, processParams: UnknownObject): UnknownObject
}
