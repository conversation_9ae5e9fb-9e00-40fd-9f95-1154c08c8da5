import type { ISession } from '@everestsystems/content-core';
import type { TestTeardown } from '@everestsystems/content-core/lib/tests';
import { setupTest } from '@everestsystems/content-core/lib/tests';
import { PlainDate } from '@everestsystems/datetime';
import { Decimal } from '@everestsystems/decimal';
import { Entity } from '@pkg/everest.base/types/Entity';
import { RejectedSuggestion } from '@pkg/everest.fin.accounting/types/RejectedSuggestion';
import { EvstBankTransactionMatchStatus } from '@pkg/everest.fin.base/types/enums/BankTransactionMatchStatus';
import { BankTransactionMatchTransient } from '@pkg/everest.fin.integration.bank.matching/types/BankTransactionMatchTransient';
import { BankTransactionNew } from '@pkg/everest.fin.integration.bank/types/BankTransactionNew';
import { MatchSourceBusinessObject } from '@pkg/everest.fin.integration.bank/types/MatchSourceBusinessObject';
import { MatchSourceFinancialDocument } from '@pkg/everest.fin.integration.bank/types/MatchSourceFinancialDocument';
import {
  createEmployee,
  createSampleBankTransaction,
  createSampleExpenseReport,
  createTestEntity,
} from '@pkg/everest.fin.processor.usgaap/tests/integration/bankTransactionMatching/createSampleData';
import type { Employee } from '@pkg/everest.hr.base/types/Employee';
describe('bank transaction match scenarios: expense report', () => {
  let session: ISession;
  let teardown: TestTeardown;

  let bankTransactionMatchClient: BankTransactionMatchTransient.IControllerClient;
  let sfdClient: MatchSourceFinancialDocument.IControllerClient;
  let bankTransactionClient: BankTransactionNew.IControllerClient;

  let createdSuggestions: Partial<MatchSourceFinancialDocument.MatchSourceFinancialDocument>[];
  let flowInc: Partial<Entity.Entity>;
  let employee: Partial<Employee.Employee>;
  beforeAll(async () => {
    ({ session, teardown } = await setupTest({
      withUser: 'test-user',
    }));

    bankTransactionMatchClient =
      await BankTransactionMatchTransient.client(session);
    sfdClient = await MatchSourceFinancialDocument.client(session);
    bankTransactionClient = await BankTransactionNew.client(session);

    const FLOW_INC = 'Flow Inc.';
    [flowInc] = await Entity.query(
      session,
      { where: { entityName: FLOW_INC }, take: 1 },
      ['id', 'currency', 'entityName']
    );
    const entity = await createTestEntity(session);
    employee = await createEmployee(
      session,
      entity.id,
      session.serverEnvironment.currentUser.email
    );
  });

  afterAll(() => teardown());

  describe('1:1', () => {
    it('should not suggest to an inbound bank transaction', async () => {
      const transactionDate = PlainDate.from('2024-11-29');
      const transactionAmount = new Decimal(1.1421);
      const bankTransaction = await createSampleBankTransaction(
        session,
        transactionAmount,
        transactionDate,
        'Wells Fargo Sweep'
      );

      await createSampleExpenseReport(
        session,
        { amount: transactionAmount },
        transactionDate,
        employee.name,
        flowInc.id
      );
      await bankTransactionMatchClient.createBankTransactionMatchSuggestions(
        bankTransaction
      );
      const [bankTransactionAfterSuggestion] =
        await bankTransactionClient.query(
          {
            where: {
              id: bankTransaction.id,
            },
          },
          ['id', 'matchId', 'matchStatus']
        );
      expect(bankTransactionAfterSuggestion.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Unmatched
      );
    });

    it('should suggest with entity with same currency', async () => {
      const transactionDate = PlainDate.from('2024-10-28');
      const transactionAmount = new Decimal(111.11);
      const bankTransaction = await createSampleBankTransaction(
        session,
        transactionAmount.times(-1), // make outbound
        transactionDate,
        'Wells Fargo Sweep'
      );

      const expenseReport = await createSampleExpenseReport(
        session,
        { amount: transactionAmount },
        transactionDate,
        employee.name,
        flowInc.id
      );
      await bankTransactionMatchClient.createBankTransactionMatchSuggestions(
        bankTransaction
      );

      const [bankTransactionAfterSuggestion] =
        await bankTransactionClient.query(
          {
            where: {
              id: bankTransaction.id,
            },
          },
          ['id', 'matchId', 'matchStatus']
        );
      expect(bankTransactionAfterSuggestion.matchId).toBeDefined();
      expect(bankTransactionAfterSuggestion.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Suggested
      );

      createdSuggestions = await sfdClient.query(
        {
          where: {
            bankTransactionMatchId: bankTransactionAfterSuggestion.matchId,
          },
        },
        ['id', 'sboNumber']
      );
      expect(createdSuggestions).toHaveLength(1);
      const expectedResult = [
        { id: expect.any(Number), sboNumber: expenseReport.expenseNumber },
      ];
      expect(createdSuggestions).toEqual(
        expect.arrayContaining(expectedResult)
      );
    });
  });

  describe('1:M', () => {
    it('should not match to an inbound bank transaction', async () => {
      const transactionDate = PlainDate.from('2024-09-27');
      const transactionAmount1 = new Decimal(123_659.11);
      const transactionAmount2 = new Decimal(5596.11);
      const bankTransaction = await createSampleBankTransaction(
        session,
        transactionAmount1.plus(transactionAmount2),
        transactionDate,
        'Wells Fargo Sweep'
      );

      await createSampleExpenseReport(
        session,
        { amount: transactionAmount1 },
        transactionDate,
        employee.name,
        flowInc.id
      );

      await createSampleExpenseReport(
        session,
        { amount: transactionAmount2 },
        transactionDate,
        employee.name,
        flowInc.id
      );

      await bankTransactionMatchClient.createBankTransactionMatchSuggestions(
        bankTransaction
      );
      const [bankTransactionAfterSuggestion] =
        await bankTransactionClient.query(
          {
            where: {
              id: bankTransaction.id,
            },
          },
          ['id', 'matchId', 'matchStatus']
        );
      expect(bankTransactionAfterSuggestion.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Unmatched
      );
    });

    it('should suggest one bank transaction with 2 bills with entity with same currency', async () => {
      const transactionDate = PlainDate.from('2024-08-26');
      const transactionAmount1 = new Decimal(11.11);
      const transactionAmount2 = new Decimal(111.11);
      const bankTransaction = await createSampleBankTransaction(
        session,
        transactionAmount1.plus(transactionAmount2).times(-1), // make outbound
        transactionDate,
        'Wells Fargo Sweep'
      );
      const expenseReport1 = await createSampleExpenseReport(
        session,
        { amount: transactionAmount1 },
        transactionDate,
        employee.name,
        flowInc.id
      );
      const expenseReport2 = await createSampleExpenseReport(
        session,
        { amount: transactionAmount2 },
        transactionDate,
        employee.name,
        flowInc.id
      );
      await bankTransactionMatchClient.createBankTransactionMatchSuggestions(
        bankTransaction
      );

      const [bankTransactionAfterSuggestion] =
        await bankTransactionClient.query(
          {
            where: {
              id: bankTransaction.id,
            },
          },
          ['id', 'matchId', 'matchStatus']
        );
      expect(bankTransactionAfterSuggestion.matchId).toBeDefined();
      expect(bankTransactionAfterSuggestion.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Suggested
      );

      createdSuggestions = await sfdClient.query(
        {
          where: {
            bankTransactionMatchId: bankTransactionAfterSuggestion.matchId,
          },
        },
        ['id', 'sboNumber']
      );
      expect(createdSuggestions).toHaveLength(2);
      const expectedResult = [
        { id: expect.any(Number), sboNumber: expenseReport1.expenseNumber },
        { id: expect.any(Number), sboNumber: expenseReport2.expenseNumber },
      ];
      expect(createdSuggestions).toEqual(
        expect.arrayContaining(expectedResult)
      );
    });
  });

  describe('rejection', () => {
    it('should reject a outbound payment suggestion', async () => {
      const transactionDate = PlainDate.from('2024-05-01');
      const transactionAmount = new Decimal(111.11);
      const bankTransaction = await createSampleBankTransaction(
        session,
        transactionAmount.times(-1), // make outbound
        transactionDate,
        'Wells Fargo Sweep'
      );

      const expenseReport = await createSampleExpenseReport(
        session,
        { amount: transactionAmount },
        transactionDate,
        employee.name,
        flowInc.id
      );

      await bankTransactionMatchClient.createBankTransactionMatchSuggestions(
        bankTransaction
      );

      const [bankTransactionAfterSuggestion] =
        await bankTransactionClient.query(
          {
            where: {
              id: bankTransaction.id,
            },
          },
          ['id', 'matchId', 'matchStatus']
        );
      expect(bankTransactionAfterSuggestion.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Suggested
      );

      createdSuggestions = await sfdClient.query(
        {
          where: {
            bankTransactionMatchId: bankTransactionAfterSuggestion.matchId,
          },
        },
        ['id', 'sfdId', 'sourceJournalEntryLineId', 'bankTransactionMatchId']
      );
      expect(createdSuggestions.length).toBeGreaterThan(0);

      // await bankTransactionMatchClient.clearMatches([]);  todo
      await bankTransactionMatchClient.rejectSuggestion(
        // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
        createdSuggestions[0] as MatchSourceFinancialDocument.MatchSourceFinancialDocument
      );
      const rejectedSuggestions = await RejectedSuggestion.query(
        session,
        { where: { sboId: expenseReport.id, sboType: 'expenseReport' } },
        ['id']
      );
      expect(rejectedSuggestions).toHaveLength(1);
      const sbosAfterRejection = await MatchSourceBusinessObject.query(
        session,
        {
          where: {
            sboId: expenseReport.id,
          },
        },
        ['id']
      );
      expect(sbosAfterRejection).toHaveLength(0);
    });
  });
});
