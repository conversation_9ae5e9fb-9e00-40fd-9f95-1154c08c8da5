/* eslint-disable jest/no-disabled-tests */
import type { ISession } from '@everestsystems/content-core';
import type { TestTeardown } from '@everestsystems/content-core/lib/tests';
import { setupTest } from '@everestsystems/content-core/lib/tests';
import { PlainDate } from '@everestsystems/datetime';
import { Decimal } from '@everestsystems/decimal';
import { EvstJournalEntryType } from '@pkg/everest.fin.accounting/types/enums/JournalEntryType';
import { JournalEntryLine } from '@pkg/everest.fin.accounting/types/JournalEntryLine';
import { RejectedSuggestion } from '@pkg/everest.fin.accounting/types/RejectedSuggestion';
import { EvstBankTransactionMatchStatus } from '@pkg/everest.fin.base/types/enums/BankTransactionMatchStatus';
import type { OutboundPaymentHeaderBase } from '@pkg/everest.fin.expense/types/OutboundPaymentHeaderBase';
import { BankTransactionMatchTransient } from '@pkg/everest.fin.integration.bank.matching/types/BankTransactionMatchTransient';
import { BankTransactionNew } from '@pkg/everest.fin.integration.bank/types/BankTransactionNew';
import { MatchSourceFinancialDocument } from '@pkg/everest.fin.integration.bank/types/MatchSourceFinancialDocument';
import {
  createEmployee,
  createSampleBankTransaction,
  createSampleOutboundPayment,
  createTestEntity,
} from '@pkg/everest.fin.processor.usgaap/tests/integration/bankTransactionMatching/createSampleData';
describe('bank transaction match scenarios: outbound payment', () => {
  let session: ISession;
  let teardown: TestTeardown;

  let bankTransactionMatchClient: BankTransactionMatchTransient.IControllerClient;
  let sfdClient: MatchSourceFinancialDocument.IControllerClient;
  let bankTransactionClient: BankTransactionNew.IControllerClient;

  let createdSuggestions: Partial<MatchSourceFinancialDocument.MatchSourceFinancialDocument>[];

  beforeAll(async () => {
    ({ session, teardown } = await setupTest({
      withUser: 'test-user',
    }));

    bankTransactionMatchClient =
      await BankTransactionMatchTransient.client(session);
    sfdClient = await MatchSourceFinancialDocument.client(session);
    bankTransactionClient = await BankTransactionNew.client(session);
    const entity = await createTestEntity(session);
    await createEmployee(
      session,
      entity.id,
      session.serverEnvironment.currentUser.email
    );
  });

  afterAll(() => teardown());

  describe('1:1', () => {
    let outboundPaymentHeader: Pick<
      OutboundPaymentHeaderBase.OutboundPaymentHeaderBase,
      'id' | 'paymentId' | 'outboundPaymentHeaderNumber'
    >;
    const transactionDate: PlainDate = PlainDate.from('2024-01-01');
    const transactionAmount: Decimal = new Decimal(1.1421);
    it('@UA-13-6 should not match to an inbound bank transaction', async () => {
      const bankTransaction = await createSampleBankTransaction(
        session,
        transactionAmount,
        transactionDate,
        'Wells Fargo Sweep'
      );

      outboundPaymentHeader = await createSampleOutboundPayment(
        session,
        session.util.uuid.v4(),
        transactionAmount,
        transactionDate,
        bankTransaction.coaAccountId
      );
      await bankTransactionMatchClient.createBankTransactionMatchSuggestions(
        bankTransaction
      );
      const [bankTransactionAfterSuggestion] =
        await bankTransactionClient.query(
          {
            where: {
              id: bankTransaction.id,
            },
          },
          ['id', 'matchId', 'matchStatus']
        );
      expect(bankTransactionAfterSuggestion.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Unmatched
      );
    });

    it('should offer a possible match with entity with same currency and match', async () => {
      // this depends on the previous test. This reduces the execution time
      const bankTransaction = await createSampleBankTransaction(
        session,
        transactionAmount.times(-1), // make outbound
        transactionDate,
        'Wells Fargo Sweep'
      );

      await bankTransactionMatchClient.createBankTransactionMatchSuggestions(
        bankTransaction
      );

      const [bankTransactionAfterSuggestion] =
        await bankTransactionClient.query(
          {
            where: {
              id: bankTransaction.id,
            },
          },
          ['id', 'matchId', 'matchStatus']
        );
      expect(bankTransactionAfterSuggestion.matchId).toBeDefined();
      expect(bankTransactionAfterSuggestion.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Suggested
      );

      createdSuggestions = await sfdClient.query(
        {
          where: {
            bankTransactionMatchId: bankTransactionAfterSuggestion.matchId,
            sfdId: outboundPaymentHeader.id,
          },
        },
        ['id', 'sfdId', 'sfdNumber', 'sboNumber']
      );
      expect(createdSuggestions).toHaveLength(1);
      const expectedResult = [
        {
          id: expect.any(Number),
          sfdId: outboundPaymentHeader.id,
          sfdNumber: outboundPaymentHeader.outboundPaymentHeaderNumber,
          sboNumber: expect.any(String),
        },
      ];
      expect(createdSuggestions).toEqual(
        expect.arrayContaining(expectedResult)
      );

      // Match
      await bankTransactionMatchClient.matchBankTransactions(
        [bankTransaction],
        createdSuggestions as unknown as MatchSourceFinancialDocument.MatchSourceFinancialDocument[]
      );

      const [bankTransactionAfterMatch] = await bankTransactionClient.query(
        {
          where: {
            id: bankTransaction.id,
          },
        },
        ['id', 'matchId', 'matchStatus']
      );
      expect(bankTransactionAfterMatch.matchId).toBeDefined();
      expect(bankTransactionAfterMatch.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Matched
      );
      const afterMatch = await sfdClient.query(
        {
          where: { bankTransactionMatchId: bankTransactionAfterMatch.matchId },
        },
        [
          'id',
          'sboNumber',
          'MatchSourceFinancialDocument-BankTransactionMatch.status',
        ]
      );
      expect(afterMatch).toHaveLength(1);
      expect(
        afterMatch[0]['MatchSourceFinancialDocument-BankTransactionMatch']
          .status
      ).toEqual(EvstBankTransactionMatchStatus.Matched);

      // TODO: **#** check the following
      // 1. check if matchId is on other bank transactions -> shouldn't
      // 2. check if the sfdId of the matched items is on other bank transactions -> shouldn't
    });

    it('should offer a possible match with entity with different currency and match', async () => {
      // This implicity test the following scenario, because same amount and date are used
      // Scenario: should not suggest a matched item
      const actualExchangeRate = new Decimal(1.01);
      const bankTransaction = await createSampleBankTransaction(
        session,
        transactionAmount.times(-1).times(actualExchangeRate), // make outbound
        transactionDate,
        'Wells Fargo Sweep',
        'USD'
      );

      const payment = await createSampleOutboundPayment(
        session,
        session.util.uuid.v4(),
        transactionAmount,
        transactionDate,
        bankTransaction.coaAccountId,
        true,
        'EUR',
        actualExchangeRate
      );
      await bankTransactionMatchClient.createBankTransactionMatchSuggestions(
        bankTransaction
      );

      const [bankTransactionAfterSuggestion] =
        await bankTransactionClient.query(
          {
            where: {
              id: bankTransaction.id,
            },
          },
          ['id', 'matchId', 'matchStatus']
        );
      expect(bankTransactionAfterSuggestion.matchId).toBeDefined();
      expect(bankTransactionAfterSuggestion.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Suggested
      );
      createdSuggestions = await sfdClient.query(
        {
          where: {
            bankTransactionMatchId: bankTransactionAfterSuggestion.matchId,
          },
        },
        ['id', 'sfdId', 'sfdNumber']
      );
      expect(createdSuggestions).toHaveLength(1);
      const expectedResult = [
        {
          id: expect.any(Number),
          sfdId: payment.id,
          sfdNumber: payment.outboundPaymentHeaderNumber,
        },
      ];
      expect(createdSuggestions).toEqual(
        expect.arrayContaining(expectedResult)
      );

      // Match
      await bankTransactionMatchClient.matchBankTransactions(
        [bankTransaction],
        createdSuggestions as unknown as MatchSourceFinancialDocument.MatchSourceFinancialDocument[]
      );

      const [bankTransactionAfterMatch] = await bankTransactionClient.query(
        {
          where: {
            id: bankTransaction.id,
          },
        },
        ['id', 'matchId', 'matchStatus']
      );
      expect(bankTransactionAfterMatch.matchId).toBeDefined();
      expect(bankTransactionAfterMatch.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Matched
      );
      const afterMatch = await sfdClient.query(
        {
          where: { bankTransactionMatchId: bankTransactionAfterMatch.matchId },
        },
        [
          'id',
          'sboNumber',
          'MatchSourceFinancialDocument-BankTransactionMatch.status',
        ]
      );
      expect(afterMatch).toHaveLength(1);
      expect(
        afterMatch[0]['MatchSourceFinancialDocument-BankTransactionMatch']
          .status
      ).toEqual(EvstBankTransactionMatchStatus.Matched);
    });

    it('should offer a possible match with entity with different currency, but same currency of bank and match', async () => {
      // This implicity test the following scenario, because same amount and date are used
      // Scenario: should not suggest a matched item
      const currency = 'EUR';
      const bankTransaction = await createSampleBankTransaction(
        session,
        transactionAmount.times(-1), // make outbound
        transactionDate,
        'Deutsche Bank',
        currency
      );

      const payment = await createSampleOutboundPayment(
        session,
        session.util.uuid.v4(),
        transactionAmount,
        transactionDate,
        bankTransaction.coaAccountId,
        true,
        currency
      );

      await bankTransactionMatchClient.createBankTransactionMatchSuggestions(
        bankTransaction
      );

      const [bankTransactionAfterSuggestion] =
        await bankTransactionClient.query(
          {
            where: {
              id: bankTransaction.id,
            },
          },
          ['id', 'matchId', 'matchStatus']
        );
      expect(bankTransactionAfterSuggestion.matchId).toBeDefined();
      expect(bankTransactionAfterSuggestion.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Suggested
      );

      createdSuggestions = await sfdClient.query(
        {
          where: {
            bankTransactionMatchId: bankTransactionAfterSuggestion.matchId,
          },
        },
        ['id', 'sfdId', 'sfdNumber']
      );
      expect(createdSuggestions).toHaveLength(1);
      const expectedResult = [
        {
          id: expect.any(Number),
          sfdId: payment.id,
          sfdNumber: payment.outboundPaymentHeaderNumber,
        },
      ];
      expect(createdSuggestions).toEqual(
        expect.arrayContaining(expectedResult)
      );

      // Match
      await bankTransactionMatchClient.matchBankTransactions(
        [bankTransaction],
        createdSuggestions as unknown as MatchSourceFinancialDocument.MatchSourceFinancialDocument[]
      );
      const [bankTransactionAfterMatch] = await bankTransactionClient.query(
        {
          where: {
            id: bankTransaction.id,
          },
        },
        ['id', 'matchId', 'matchStatus']
      );
      expect(bankTransactionAfterMatch.matchId).toBeDefined();
      expect(bankTransactionAfterMatch.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Matched
      );
      const afterMatch = await sfdClient.query(
        {
          where: { bankTransactionMatchId: bankTransactionAfterMatch.matchId },
        },
        [
          'id',
          'sboNumber',
          'MatchSourceFinancialDocument-BankTransactionMatch.status',
        ]
      );
      expect(afterMatch).toHaveLength(1);
      expect(
        afterMatch[0]['MatchSourceFinancialDocument-BankTransactionMatch']
          .status
      ).toEqual(EvstBankTransactionMatchStatus.Matched);
    });
  });

  describe('1:M', () => {
    let outboundPaymentHeader1: Pick<
      OutboundPaymentHeaderBase.OutboundPaymentHeaderBase,
      'id' | 'paymentId' | 'outboundPaymentHeaderNumber'
    >;
    let outboundPaymentHeader2: Pick<
      OutboundPaymentHeaderBase.OutboundPaymentHeaderBase,
      'id' | 'paymentId' | 'outboundPaymentHeaderNumber'
    >;
    let transactionDate: PlainDate;
    let transactionAmount1: Decimal;
    let transactionAmount2: Decimal;

    it('should not suggest to an inbound bank transaction', async () => {
      transactionDate = PlainDate.from('2024-02-10');
      transactionAmount1 = new Decimal(123_659.11);
      transactionAmount2 = new Decimal(5596.11);
      const bankTransaction = await createSampleBankTransaction(
        session,
        transactionAmount1.plus(transactionAmount2),
        transactionDate,
        'Wells Fargo Sweep'
      );

      outboundPaymentHeader1 = await createSampleOutboundPayment(
        session,
        session.util.uuid.v4(),
        transactionAmount1,
        transactionDate,
        bankTransaction.coaAccountId
      );
      outboundPaymentHeader2 = await createSampleOutboundPayment(
        session,
        session.util.uuid.v4(),
        transactionAmount2,
        transactionDate,
        bankTransaction.coaAccountId
      );
      await bankTransactionMatchClient.createBankTransactionMatchSuggestions(
        bankTransaction
      );
      const [bankTransactionAfterSuggestion] =
        await bankTransactionClient.query(
          {
            where: {
              id: bankTransaction.id,
            },
          },
          ['id', 'matchId', 'matchStatus']
        );
      expect(bankTransactionAfterSuggestion.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Unmatched
      );
    });

    it('should suggest one bank transaction with 2 outbound payments with entity with same currency and match', async () => {
      // this depends on the previous test. This reduces the execution time
      const bankTransaction = await createSampleBankTransaction(
        session,
        transactionAmount1.plus(transactionAmount2).times(-1), // make outbound
        transactionDate,
        'Wells Fargo Sweep'
      );
      await bankTransactionMatchClient.createBankTransactionMatchSuggestions(
        bankTransaction
      );

      const [bankTransactionAfterSuggestion] =
        await bankTransactionClient.query(
          {
            where: {
              id: bankTransaction.id,
            },
          },
          ['id', 'matchId', 'matchStatus']
        );
      expect(bankTransactionAfterSuggestion.matchId).toBeDefined();
      expect(bankTransactionAfterSuggestion.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Suggested
      );

      createdSuggestions = await sfdClient.query(
        {
          where: {
            bankTransactionMatchId: bankTransactionAfterSuggestion.matchId,
            sfdId: {
              $in: [outboundPaymentHeader1.id, outboundPaymentHeader2.id],
            },
          },
        },
        ['id', 'sfdId']
      );
      expect(createdSuggestions).toHaveLength(2);
      const expectedResult = [
        { id: expect.any(Number), sfdId: outboundPaymentHeader1.id },
        { id: expect.any(Number), sfdId: outboundPaymentHeader2.id },
      ];
      expect(createdSuggestions).toEqual(
        expect.arrayContaining(expectedResult)
      );

      // Match
      await bankTransactionMatchClient.matchBankTransactions(
        [bankTransaction],
        createdSuggestions as unknown as MatchSourceFinancialDocument.MatchSourceFinancialDocument[]
      );

      const [bankTransactionAfterMatch] = await bankTransactionClient.query(
        {
          where: {
            id: bankTransaction.id,
          },
        },
        ['id', 'matchId', 'matchStatus']
      );
      expect(bankTransactionAfterMatch.matchId).toBeDefined();
      expect(bankTransactionAfterMatch.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Matched
      );
      const afterMatch = await sfdClient.query(
        {
          where: { bankTransactionMatchId: bankTransactionAfterMatch.matchId },
        },
        [
          'id',
          'sfdId',
          'sourceJournalEntryLineId',
          'MatchSourceFinancialDocument-BankTransactionMatch.status',
        ]
      );
      expect(afterMatch).toHaveLength(2);
      const jouranlEntryLines = await JournalEntryLine.query(
        session,
        {
          where: {
            'JournalEntryLine-JournalEntryHeader.sourceFinancialDocumentId': {
              $in: [outboundPaymentHeader1.id, outboundPaymentHeader2.id],
            },
            'JournalEntryLine-JournalEntryHeader.type':
              EvstJournalEntryType.OutboundPayment,
            accountId: bankTransaction.coaAccountId,
          },
        },
        [
          'id',
          'sourceTransLineId',
          'JournalEntryLine-JournalEntryHeader.sourceFinancialDocumentId',
        ]
      );
      const expectedResultAfterMatch = [
        expect.objectContaining({
          'MatchSourceFinancialDocument-BankTransactionMatch': {
            status: 'matched',
          },
          id: expect.any(Number),
          sfdId: outboundPaymentHeader1.id,
          sourceJournalEntryLineId: jouranlEntryLines.find(
            (line) =>
              line['JournalEntryLine-JournalEntryHeader']
                .sourceFinancialDocumentId === outboundPaymentHeader1.id
          ).id,
        }),
        expect.objectContaining({
          'MatchSourceFinancialDocument-BankTransactionMatch': {
            status: 'matched',
          },
          id: expect.any(Number),
          sfdId: outboundPaymentHeader2.id,
          sourceJournalEntryLineId: jouranlEntryLines.find(
            (line) =>
              line['JournalEntryLine-JournalEntryHeader']
                .sourceFinancialDocumentId === outboundPaymentHeader2.id
          ).id,
        }),
      ];
      expect(afterMatch).toEqual(
        expect.arrayContaining(expectedResultAfterMatch)
      );
    });

    it('should suggest one bank transaction with 2 outbound payments with entity with different currency and match', async () => {
      // This implicity test the following scenario, because same amount and date are used
      // Scenario: should not suggest a matched item
      const actualExchangeRate = new Decimal(1.01);
      const bankTransaction = await createSampleBankTransaction(
        session,
        transactionAmount1
          .plus(transactionAmount2)
          .times(-1)
          .times(actualExchangeRate), // make outbound
        transactionDate,
        'Wells Fargo Sweep',
        'USD'
      );

      const payment1 = await createSampleOutboundPayment(
        session,
        session.util.uuid.v4(),
        transactionAmount1,
        transactionDate,
        bankTransaction.coaAccountId,
        true,
        'EUR',
        actualExchangeRate
      );
      const payment2 = await createSampleOutboundPayment(
        session,
        session.util.uuid.v4(),
        transactionAmount2,
        transactionDate,
        bankTransaction.coaAccountId,
        true,
        'EUR',
        actualExchangeRate
      );
      await bankTransactionMatchClient.createBankTransactionMatchSuggestions(
        bankTransaction
      );

      const [bankTransactionAfterSuggestion] =
        await bankTransactionClient.query(
          {
            where: {
              id: bankTransaction.id,
            },
          },
          ['id', 'matchId', 'matchStatus']
        );
      expect(bankTransactionAfterSuggestion.matchId).toBeDefined();
      expect(bankTransactionAfterSuggestion.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Suggested
      );

      createdSuggestions = await sfdClient.query(
        {
          where: {
            bankTransactionMatchId: bankTransactionAfterSuggestion.matchId,
          },
        },
        ['id', 'sfdId', 'sfdNumber']
      );
      expect(createdSuggestions).toHaveLength(2);
      const expectedResult = [
        expect.objectContaining({
          id: expect.any(Number),
          sfdId: payment1.id,
          sfdNumber: payment1.outboundPaymentHeaderNumber,
        }),
        expect.objectContaining({
          id: expect.any(Number),
          sfdId: payment2.id,
          sfdNumber: payment2.outboundPaymentHeaderNumber,
        }),
      ];
      expect(createdSuggestions).toEqual(
        expect.arrayContaining(expectedResult)
      );

      // Match
      await bankTransactionMatchClient.matchBankTransactions(
        [bankTransaction],
        createdSuggestions as unknown as MatchSourceFinancialDocument.MatchSourceFinancialDocument[]
      );

      const [bankTransactionAfterMatch] = await bankTransactionClient.query(
        {
          where: {
            id: bankTransaction.id,
          },
        },
        ['id', 'matchId', 'matchStatus']
      );
      expect(bankTransactionAfterMatch.matchId).toBeDefined();
      expect(bankTransactionAfterMatch.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Matched
      );

      const afterMatch = await sfdClient.query(
        {
          where: { bankTransactionMatchId: bankTransactionAfterMatch.matchId },
        },
        [
          'id',
          'sfdId',
          'MatchSourceFinancialDocument-BankTransactionMatch.status',
        ]
      );
      expect(afterMatch).toHaveLength(2);
      const expectedResultAfterMatch = [
        expect.objectContaining({
          'MatchSourceFinancialDocument-BankTransactionMatch': {
            status: 'matched',
          },
          id: expect.any(Number),
          sfdId: payment1.id,
        }),
        expect.objectContaining({
          'MatchSourceFinancialDocument-BankTransactionMatch': {
            status: 'matched',
          },
          id: expect.any(Number),
          sfdId: payment2.id,
        }),
      ];
      expect(afterMatch).toEqual(
        expect.arrayContaining(expectedResultAfterMatch)
      );
    });

    it('should suggest one bank transaction with 2 outbound payments with entity with different currency, but same currency of bank and match', async () => {
      // This implicity test the following scenario, because same amount and date are used
      // Scenario: should not suggest a matched item
      const currency = 'EUR';
      const bankTransaction = await createSampleBankTransaction(
        session,
        transactionAmount1.plus(transactionAmount2).times(-1), // make outbound
        transactionDate,
        'Deutsche Bank',
        currency
      );

      const payment1 = await createSampleOutboundPayment(
        session,
        session.util.uuid.v4(),
        transactionAmount1,
        transactionDate,
        bankTransaction.coaAccountId,
        true,
        currency
      );
      const payment2 = await createSampleOutboundPayment(
        session,
        session.util.uuid.v4(),
        transactionAmount2,
        transactionDate,
        bankTransaction.coaAccountId,
        true,
        currency
      );
      await bankTransactionMatchClient.createBankTransactionMatchSuggestions(
        bankTransaction
      );

      const [bankTransactionAfterSuggestion] =
        await bankTransactionClient.query(
          {
            where: {
              id: bankTransaction.id,
            },
          },
          ['id', 'matchId', 'matchStatus']
        );
      expect(bankTransactionAfterSuggestion.matchId).toBeDefined();
      expect(bankTransactionAfterSuggestion.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Suggested
      );

      createdSuggestions = await sfdClient.query(
        {
          where: {
            bankTransactionMatchId: bankTransactionAfterSuggestion.matchId,
          },
        },
        ['id', 'sfdId']
      );
      expect(createdSuggestions).toHaveLength(2);
      const expectedResult = [
        { id: expect.any(Number), sfdId: payment1.id },
        { id: expect.any(Number), sfdId: payment2.id },
      ];
      expect(createdSuggestions).toEqual(
        expect.arrayContaining(expectedResult)
      );

      // Match
      await bankTransactionMatchClient.matchBankTransactions(
        [bankTransaction],
        createdSuggestions as unknown as MatchSourceFinancialDocument.MatchSourceFinancialDocument[]
      );

      const [bankTransactionAfterMatch] = await bankTransactionClient.query(
        {
          where: {
            id: bankTransaction.id,
          },
        },
        ['id', 'matchId', 'matchStatus']
      );
      expect(bankTransactionAfterMatch.matchId).toBeDefined();
      expect(bankTransactionAfterMatch.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Matched
      );
      const afterMatch = await sfdClient.query(
        {
          where: { bankTransactionMatchId: bankTransactionAfterMatch.matchId },
        },
        [
          'id',
          'sfdId',
          'MatchSourceFinancialDocument-BankTransactionMatch.status',
        ]
      );
      expect(afterMatch).toHaveLength(2);
      const expectedResultAfterMatch = [
        expect.objectContaining({
          'MatchSourceFinancialDocument-BankTransactionMatch': {
            status: 'matched',
          },
          id: expect.any(Number),
          sfdId: payment1.id,
        }),
        expect.objectContaining({
          'MatchSourceFinancialDocument-BankTransactionMatch': {
            status: 'matched',
          },
          id: expect.any(Number),
          sfdId: payment2.id,
        }),
      ];
      expect(afterMatch).toEqual(
        expect.arrayContaining(expectedResultAfterMatch)
      );
    });
  });

  describe('M:1', () => {
    let outboundPaymentHeader: Pick<
      OutboundPaymentHeaderBase.OutboundPaymentHeaderBase,
      'id' | 'paymentId' | 'outboundPaymentHeaderNumber'
    >;
    let transactionDate: PlainDate;
    let transactionAmount1: Decimal;
    let transactionAmount2: Decimal;

    it('should not match to an inbound bank transaction', async () => {
      transactionDate = PlainDate.from('2024-03-20');
      transactionAmount1 = new Decimal(12_659.11);
      transactionAmount2 = new Decimal(596.11);
      const bankTransactions = await Promise.all([
        createSampleBankTransaction(
          session,
          transactionAmount1,
          transactionDate,
          'Wells Fargo Sweep'
        ),
        createSampleBankTransaction(
          session,
          transactionAmount2,
          transactionDate,
          'Wells Fargo Sweep'
        ),
      ]);
      const bankTransactionIds = bankTransactions.map(({ id }) => id);
      outboundPaymentHeader = await createSampleOutboundPayment(
        session,
        session.util.uuid.v4(),
        transactionAmount1.plus(transactionAmount2),
        transactionDate,
        bankTransactions[0].coaAccountId
      );
      await bankTransactionMatchClient.createMultipleBankTransactionMatchSuggestions(
        bankTransactionIds
      );
      const bankTransactionsAfterSuggestion = await bankTransactionClient.query(
        {
          where: {
            id: { $in: bankTransactionIds },
          },
        },
        ['id', 'matchStatus']
      );
      expect(bankTransactionsAfterSuggestion[0].matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Unmatched
      );
      expect(bankTransactionsAfterSuggestion[1].matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Unmatched
      );
    });

    it('should suggest 2 bank transactions with 1 outbound payment with entity with same currency and match', async () => {
      // this depends on the previous test. This reduces the execution time
      const bankTransactions = await Promise.all([
        createSampleBankTransaction(
          session,
          transactionAmount1.times(-1),
          transactionDate,
          'Wells Fargo Sweep'
        ),
        createSampleBankTransaction(
          session,
          transactionAmount2.times(-1),
          transactionDate,
          'Wells Fargo Sweep'
        ),
      ]);
      const bankTransactionIds = bankTransactions.map(({ id }) => id);

      await bankTransactionMatchClient.createMultipleBankTransactionMatchSuggestions(
        bankTransactionIds
      );

      const bankTransactionsAfterSuggestion = await bankTransactionClient.query(
        {
          where: {
            id: { $in: bankTransactionIds },
          },
        },
        ['id', 'matchId', 'matchStatus']
      );
      expect(bankTransactionsAfterSuggestion[0].matchId).toEqual(
        bankTransactionsAfterSuggestion[1].matchId
      );
      expect(bankTransactionsAfterSuggestion[0].matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Suggested
      );
      expect(bankTransactionsAfterSuggestion[1].matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Suggested
      );

      createdSuggestions = await sfdClient.query(
        {
          where: {
            bankTransactionMatchId: bankTransactionsAfterSuggestion[0].matchId,
          },
        },
        ['id', 'sfdId']
      );
      expect(createdSuggestions).toHaveLength(1);
      const expectedResult = {
        id: expect.any(Number),
        sfdId: outboundPaymentHeader.id,
      };
      expect(createdSuggestions[0]).toEqual(
        expect.objectContaining(expectedResult)
      );

      // Match
      await bankTransactionMatchClient.matchBankTransactions(
        bankTransactions,
        createdSuggestions as unknown as MatchSourceFinancialDocument.MatchSourceFinancialDocument[]
      );

      const bankTransactionsAfterMatch = await bankTransactionClient.query(
        {
          where: {
            id: { $in: bankTransactionIds },
          },
        },
        ['id', 'matchId', 'matchStatus']
      );
      expect(bankTransactionsAfterMatch[0].matchId).toEqual(
        bankTransactionsAfterMatch[1].matchId
      );
      expect(bankTransactionsAfterMatch[0].matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Matched
      );
      const afterMatch = await sfdClient.query(
        {
          where: {
            bankTransactionMatchId: bankTransactionsAfterMatch[0].matchId,
          },
        },
        [
          'id',
          'sboNumber',
          'MatchSourceFinancialDocument-BankTransactionMatch.status',
        ]
      );
      expect(afterMatch).toHaveLength(1);
      expect(
        afterMatch[0]['MatchSourceFinancialDocument-BankTransactionMatch']
          .status
      ).toEqual(EvstBankTransactionMatchStatus.Matched);
    });

    it.skip('should match 2 bank transactions with 1 outbound payment with entity with different currency', async () => {
      transactionDate = PlainDate.from('2024-04-10');
      const transactionAmount1 = new Decimal(1659.11);
      const transactionAmount2 = new Decimal(596.11);
      const actualExchangeRate = new Decimal(1.01);
      const bankTransactions = await Promise.all([
        createSampleBankTransaction(
          session,
          transactionAmount1.times(-1),
          transactionDate,
          'Wells Fargo Sweep',
          'USD'
        ),
        createSampleBankTransaction(
          session,
          transactionAmount2.times(-1),
          transactionDate,
          'Wells Fargo Sweep',
          'USD'
        ),
      ]);
      const bankTransactionIds = bankTransactions.map(({ id }) => id);
      const payment = await createSampleOutboundPayment(
        session,
        session.util.uuid.v4(),
        transactionAmount1.plus(transactionAmount2),
        transactionDate,
        bankTransactions[0].coaAccountId,
        true,
        'EUR',
        actualExchangeRate
      );

      await bankTransactionMatchClient.createMultipleBankTransactionMatchSuggestions(
        bankTransactionIds
      );

      const bankTransactionsAfterSuggestion = await bankTransactionClient.query(
        {
          where: {
            id: { $in: bankTransactionIds },
          },
        },
        ['id', 'matchId', 'matchStatus']
      );
      expect(bankTransactionsAfterSuggestion[0].matchId).toEqual(
        bankTransactionsAfterSuggestion[1].matchId
      );
      expect(bankTransactionsAfterSuggestion[0].matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Suggested
      );
      expect(bankTransactionsAfterSuggestion[1].matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Suggested
      );

      createdSuggestions = await sfdClient.query(
        {
          where: {
            bankTransactionMatchId: bankTransactionsAfterSuggestion[0].matchId,
            sfdId: payment.id,
          },
        },
        ['id', 'sfdId']
      );
      expect(createdSuggestions).toHaveLength(1);
      const expectedResult = {
        id: expect.any(Number),
        sfdId: payment.id,
      };
      expect(createdSuggestions[0]).toEqual(
        expect.objectContaining(expectedResult)
      );
    });

    it('should suggest 2 bank transactions with 1 outbound payment with entity with different currency, but same currency of bank and match', async () => {
      // This implicity test the following scenario, because same amount and date are used
      // Scenario: should not suggest a matched item
      const currency = 'EUR';
      const bankTransactions = await Promise.all([
        createSampleBankTransaction(
          session,
          transactionAmount1.times(-1),
          transactionDate,
          'Deutsche Bank',
          currency
        ),
        createSampleBankTransaction(
          session,
          transactionAmount2.times(-1),
          transactionDate,
          'Deutsche Bank',
          currency
        ),
      ]);
      const bankTransactionIds = bankTransactions.map(({ id }) => id);
      const payment = await createSampleOutboundPayment(
        session,
        session.util.uuid.v4(),
        transactionAmount1.plus(transactionAmount2),
        transactionDate,
        bankTransactions[0].coaAccountId,
        true,
        currency
      );

      await bankTransactionMatchClient.createMultipleBankTransactionMatchSuggestions(
        bankTransactionIds
      );

      const bankTransactionsAfterSuggestion = await bankTransactionClient.query(
        {
          where: {
            id: { $in: bankTransactionIds },
          },
        },
        ['id', 'matchId', 'matchStatus']
      );
      expect(bankTransactionsAfterSuggestion[0].matchId).toEqual(
        bankTransactionsAfterSuggestion[1].matchId
      );
      expect(bankTransactionsAfterSuggestion[0].matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Suggested
      );
      expect(bankTransactionsAfterSuggestion[1].matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Suggested
      );

      createdSuggestions = await sfdClient.query(
        {
          where: {
            bankTransactionMatchId: bankTransactionsAfterSuggestion[0].matchId,
          },
        },
        ['id', 'sfdId']
      );
      expect(createdSuggestions).toHaveLength(1);
      const expectedResult = {
        id: expect.any(Number),
        sfdId: payment.id,
      };
      expect(createdSuggestions[0]).toEqual(
        expect.objectContaining(expectedResult)
      );

      // Match
      await bankTransactionMatchClient.matchBankTransactions(
        bankTransactions,
        createdSuggestions as unknown as MatchSourceFinancialDocument.MatchSourceFinancialDocument[]
      );

      const bankTransactionsAfterMatch = await bankTransactionClient.query(
        {
          where: {
            id: { $in: bankTransactionIds },
          },
        },
        ['id', 'matchId', 'matchStatus']
      );
      expect(bankTransactionsAfterMatch[0].matchId).toEqual(
        bankTransactionsAfterMatch[1].matchId
      );
      expect(bankTransactionsAfterMatch[0].matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Matched
      );
      const afterMatch = await sfdClient.query(
        {
          where: {
            bankTransactionMatchId: bankTransactionsAfterMatch[0].matchId,
          },
        },
        [
          'id',
          'sboNumber',
          'MatchSourceFinancialDocument-BankTransactionMatch.status',
        ]
      );
      expect(afterMatch).toHaveLength(1);
      expect(
        afterMatch[0]['MatchSourceFinancialDocument-BankTransactionMatch']
          .status
      ).toEqual(EvstBankTransactionMatchStatus.Matched);
    });
  });

  describe('rejection', () => {
    it('should reject a outbound payment suggestion', async () => {
      const transactionDate = PlainDate.from('2024-05-01');
      const transactionAmount = new Decimal(111.11);
      const bankTransaction = await createSampleBankTransaction(
        session,
        transactionAmount.times(-1), // make outbound
        transactionDate,
        'Wells Fargo Sweep'
      );

      await createSampleOutboundPayment(
        session,
        session.util.uuid.v4(),
        transactionAmount,
        transactionDate,
        bankTransaction.coaAccountId
      );
      await bankTransactionMatchClient.createBankTransactionMatchSuggestions(
        bankTransaction
      );
      createdSuggestions = await sfdClient.query(
        {
          where: {
            'MatchSourceFinancialDocument-BankTransactionMatch.BankTransaction-BankTransactionMatch.id':
              bankTransaction.id,
          },
        },
        ['id', 'sfdId', 'sourceJournalEntryLineId', 'bankTransactionMatchId']
      );
      expect(createdSuggestions.length).toBeGreaterThan(0);

      const [{ sourceJournalEntryLineId }] = createdSuggestions;
      await bankTransactionMatchClient.clearMatches([sourceJournalEntryLineId]);
      await bankTransactionMatchClient.rejectSuggestion(
        createdSuggestions[0] as unknown as MatchSourceFinancialDocument.MatchSourceFinancialDocument
      );
      const rejectedSuggestions = await RejectedSuggestion.query(
        session,
        { where: { sourceJournalEntryLineId } },
        ['id']
      );
      expect(rejectedSuggestions).toHaveLength(1);
      const rejectedSBOs = await RejectedSuggestion.query(
        session,
        { where: { sboType: 'vendorBill' } },
        ['id']
      );
      expect(rejectedSBOs.length).toBeGreaterThan(0);
      const suggestionsAfterRejection = await sfdClient.query(
        {
          where: {
            sourceJournalEntryLineId,
          },
        },
        ['id']
      );
      expect(suggestionsAfterRejection).toHaveLength(0);
    });
  });

  describe('auto match', () => {
    it.skip('should auto match for high confident suggestion', async () => {
      const transactionDate = PlainDate.from('2024-05-10');
      const transactionAmount = new Decimal(111.11);
      const bankTransaction = await createSampleBankTransaction(
        session,
        transactionAmount.times(-1), // make outbound
        transactionDate,
        'Wells Fargo Sweep',
        undefined,
        'USD',
        ['id', 'coaAccountId'],
        'Test payment for auto match',
        'Porsche'
      );

      const payment = await createSampleOutboundPayment(
        session,
        session.util.uuid.v4(),
        transactionAmount,
        transactionDate,
        bankTransaction.coaAccountId,
        true,
        'USD',
        new Decimal(1),
        'Porsche',
        'Test payment for auto match'
      );
      await bankTransactionMatchClient.createBankTransactionMatchSuggestions(
        bankTransaction
      );
      createdSuggestions = await sfdClient.query(
        { where: { bankTransactionId: bankTransaction.id } },
        [
          'id',
          'sfdId',
          'MatchSourceFinancialDocument-BankTransactionMatch.status',
          'suggestionConfidence',
          'amount',
        ]
      );
      expect(createdSuggestions).toHaveLength(1);
      const expectedResult = [
        {
          id: expect.any(Number),
          sfdId: payment.id,
          'MatchSourceFinancialDocument-BankTransactionMatch': {
            status: EvstBankTransactionMatchStatus.AutoMatched,
          },
          suggestionConfidence: expect.anything(),
          amount: expect.anything(),
        },
      ];
      expect(createdSuggestions).toEqual(
        expect.arrayContaining(expectedResult)
      );
    });
  });

  describe.skip('deletion and status update', () => {
    it.skip('should unmatch when outbound payment is deleted', async () => {
      const transactionDate = PlainDate.from('2024-05-10');
      const transactionAmount = new Decimal(111.11);
      const bankTransaction = await createSampleBankTransaction(
        session,
        transactionAmount.times(-1), // make outbound
        transactionDate,
        'Wells Fargo Sweep',
        undefined,
        'USD',
        ['id', 'coaAccountId'],
        'Test payment for auto match',
        'Porsche'
      );

      const payment = await createSampleOutboundPayment(
        session,
        session.util.uuid.v4(),
        transactionAmount,
        transactionDate,
        bankTransaction.coaAccountId,
        true,
        'USD',
        new Decimal(1),
        'Porsche',
        'Test payment for auto match'
      );
      await bankTransactionMatchClient.createBankTransactionMatchSuggestions(
        bankTransaction
      );
      createdSuggestions = await sfdClient.query(
        { where: { bankTransactionId: bankTransaction.id } },
        [
          'id',
          'sfdId',
          'MatchSourceFinancialDocument-BankTransactionMatch.status',
          'suggestionConfidence',
        ]
      );
      expect(createdSuggestions).toHaveLength(1);
      const expectedResult = [
        {
          id: expect.any(Number),
          sfdId: payment.id,
          'MatchSourceFinancialDocument-BankTransactionMatch': {
            status: EvstBankTransactionMatchStatus.AutoMatched,
          },
          suggestionConfidence: expect.anything(),
        },
      ];
      expect(createdSuggestions).toEqual(
        expect.arrayContaining(expectedResult)
      );
    });
  });
});
