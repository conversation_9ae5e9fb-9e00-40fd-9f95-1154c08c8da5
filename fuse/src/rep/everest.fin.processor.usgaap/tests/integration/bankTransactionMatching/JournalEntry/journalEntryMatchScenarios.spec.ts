import type { ISession } from '@everestsystems/content-core';
import type { TestTeardown } from '@everestsystems/content-core/lib/tests';
import { setupTest } from '@everestsystems/content-core/lib/tests';
import { PlainDate } from '@everestsystems/datetime';
import { Decimal } from '@everestsystems/decimal';
import { Entity } from '@pkg/everest.base/types/Entity';
import { EvstJournalEntryStatus } from '@pkg/everest.fin.accounting/types/enums/JournalEntryStatus';
import { EvstJournalEntryType } from '@pkg/everest.fin.accounting/types/enums/JournalEntryType';
import type { JournalEntryHeader } from '@pkg/everest.fin.accounting/types/JournalEntryHeader';
import type { JournalEntryLine } from '@pkg/everest.fin.accounting/types/JournalEntryLine';
import { EvstBankTransactionMatchStatus } from '@pkg/everest.fin.base/types/enums/BankTransactionMatchStatus';
import { BankTransactionMatchTransient } from '@pkg/everest.fin.integration.bank.matching/types/BankTransactionMatchTransient';
import { BankTransactionNew } from '@pkg/everest.fin.integration.bank/types/BankTransactionNew';
import { MatchSourceFinancialDocument } from '@pkg/everest.fin.integration.bank/types/MatchSourceFinancialDocument';
import {
  createEmployee,
  createSampleBankTransaction,
  createSampleJournalEntry,
  createSampleVendorBill,
  createTestEntity,
  reverseJournalEntry,
} from '@pkg/everest.fin.processor.usgaap/tests/integration/bankTransactionMatching/createSampleData';
describe('bank transaction match scenarios: journal entry', () => {
  let session: ISession;
  let teardown: TestTeardown;

  let bankTransactionMatchClient: BankTransactionMatchTransient.IControllerClient;
  let sfdClient: MatchSourceFinancialDocument.IControllerClient;
  let bankTransactionClient: BankTransactionNew.IControllerClient;

  let createdSuggestions: Partial<MatchSourceFinancialDocument.MatchSourceFinancialDocument>[];
  let flowInc: Partial<Entity.Entity>;

  beforeAll(async () => {
    ({ session, teardown } = await setupTest({
      withUser: 'test-user',
    }));

    bankTransactionMatchClient =
      await BankTransactionMatchTransient.client(session);
    sfdClient = await MatchSourceFinancialDocument.client(session);
    bankTransactionClient = await BankTransactionNew.client(session);

    const FLOW_INC = 'Flow Inc.';
    [flowInc] = await Entity.query(
      session,
      { where: { entityName: FLOW_INC }, take: 1 },
      ['id', 'currency', 'entityName']
    );
    await createTestEntity(session);
    await createEmployee(
      session,
      flowInc.id,
      session.serverEnvironment.currentUser.email
    );
  });

  afterAll(() => teardown());

  describe('1:1', () => {
    let journalEntryId: number;
    let transactionDate: PlainDate;
    let transactionAmount: Decimal;
    let bankTransaction: Pick<
      BankTransactionNew.BankTransactionNew,
      'id' | 'coaAccountId'
    >;

    it('should offer a possible match with entity with same currency and match', async () => {
      transactionDate = PlainDate.from('2024-11-29');
      transactionAmount = new Decimal(1000.01);
      bankTransaction = await createSampleBankTransaction(
        session,
        transactionAmount.times(-1),
        transactionDate,
        'Wells Fargo Sweep'
      );

      journalEntryId = await createSampleJournalEntry(
        session,
        {
          journalEntryName: 'Test Journal Entry',
          description: 'Test Description',
          type: EvstJournalEntryType.Manual,
          journalEntryStatus: EvstJournalEntryStatus.Posted,
          isIntercompany: false,
          entityIds: [flowInc.id],
          transactionalCurrencies: [flowInc.currency],
          postingDate: transactionDate,
        } as unknown as JournalEntryHeader.JournalEntryHeader,
        [
          {
            accountId: bankTransaction.coaAccountId,
            transactionalCurrency: flowInc.currency,
            debit: null,
            credit: { amount: transactionAmount },
            entityId: flowInc.id,
          },
          {
            accountId: bankTransaction.coaAccountId,
            transactionalCurrency: flowInc.currency,
            debit: { amount: transactionAmount },
            credit: null,
            entityId: flowInc.id,
          },
        ] as unknown as JournalEntryLine.JournalEntryLine[]
      );

      await bankTransactionMatchClient.createBankTransactionMatchSuggestions(
        bankTransaction
      );

      const [bankTransactionAfterSuggestion] =
        await bankTransactionClient.query(
          {
            where: {
              id: bankTransaction.id,
            },
          },
          ['id', 'matchId', 'matchStatus']
        );
      expect(bankTransactionAfterSuggestion.matchId).toBeDefined();
      expect(bankTransactionAfterSuggestion.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Suggested
      );

      createdSuggestions = await sfdClient.query(
        {
          where: {
            bankTransactionMatchId: bankTransactionAfterSuggestion.matchId,
          },
        },
        ['id', 'sfdId', 'amount']
      );

      expect(createdSuggestions).toHaveLength(1);
      const expectedResult = expect.objectContaining({
        id: expect.any(Number),
        sfdId: journalEntryId,
        amount: transactionAmount.times(-1),
      });
      expect(createdSuggestions).toEqual(
        expect.arrayContaining([expectedResult])
      );

      await bankTransactionMatchClient.matchBankTransactions(
        [bankTransaction] as BankTransactionNew.BankTransactionNew[],
        createdSuggestions as unknown as MatchSourceFinancialDocument.MatchSourceFinancialDocument[]
      );

      const [bankTransactionAfterMatch] = await bankTransactionClient.query(
        {
          where: {
            id: bankTransaction.id,
          },
        },
        ['id', 'matchId', 'matchStatus']
      );
      expect(bankTransactionAfterMatch.matchId).toBeDefined();
      expect(bankTransactionAfterMatch.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Matched
      );
      const afterMatch = await sfdClient.query(
        {
          where: { bankTransactionMatchId: bankTransactionAfterMatch.matchId },
        },
        [
          'id',
          'sboNumber',
          'MatchSourceFinancialDocument-BankTransactionMatch.status',
        ]
      );
      expect(afterMatch).toHaveLength(1);
      expect(
        afterMatch[0]['MatchSourceFinancialDocument-BankTransactionMatch']
          .status
      ).toEqual(EvstBankTransactionMatchStatus.Matched);
    });

    it('should offer a possible match with a reversal', async () => {
      transactionDate = PlainDate.from('2023-05-01');
      transactionAmount = new Decimal(123);
      bankTransaction = await createSampleBankTransaction(
        session,
        transactionAmount,
        transactionDate,
        'Wells Fargo Sweep'
      );

      journalEntryId = await createSampleJournalEntry(
        session,
        {
          journalEntryName: 'Test Journal Entry',
          description: 'Test Description',
          type: EvstJournalEntryType.Manual,
          journalEntryStatus: EvstJournalEntryStatus.Posted,
          isIntercompany: false,
          entityIds: [flowInc.id],
          transactionalCurrencies: [flowInc.currency],
          postingDate: transactionDate,
        } as unknown as JournalEntryHeader.JournalEntryHeader,
        [
          {
            accountId: bankTransaction.coaAccountId,
            transactionalCurrency: flowInc.currency,
            debit: null,
            credit: { amount: transactionAmount },
            entityId: flowInc.id,
          },
          {
            accountId: bankTransaction.coaAccountId,
            transactionalCurrency: flowInc.currency,
            debit: { amount: transactionAmount },
            credit: null,
            entityId: flowInc.id,
          },
        ] as unknown as JournalEntryLine.JournalEntryLine[]
      );

      const reversedJournalEntryId = await reverseJournalEntry(
        session,
        journalEntryId,
        transactionDate
      );

      await bankTransactionMatchClient.createBankTransactionMatchSuggestions(
        bankTransaction
      );

      const [bankTransactionAfterSuggestion] =
        await bankTransactionClient.query(
          {
            where: {
              id: bankTransaction.id,
            },
          },
          ['id', 'matchId', 'matchStatus']
        );
      expect(bankTransactionAfterSuggestion.matchId).toBeDefined();
      expect(bankTransactionAfterSuggestion.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Suggested
      );

      createdSuggestions = await sfdClient.query(
        {
          where: {
            bankTransactionMatchId: bankTransactionAfterSuggestion.matchId,
          },
        },
        ['id', 'sfdId', 'amount']
      );

      expect(createdSuggestions).toHaveLength(2);
      const expectedResult = expect.arrayContaining([
        expect.objectContaining({
          id: expect.any(Number),
          sfdId: journalEntryId,
          amount: transactionAmount,
        }),
        expect.objectContaining({
          id: expect.any(Number),
          sfdId: reversedJournalEntryId,
          amount: transactionAmount,
        }),
      ]);
      expect(createdSuggestions).toEqual(expectedResult);

      await bankTransactionMatchClient.matchBankTransactions(
        [bankTransaction] as BankTransactionNew.BankTransactionNew[],
        createdSuggestions.filter(
          (cs) => cs.sfdId === reversedJournalEntryId
        ) as unknown as MatchSourceFinancialDocument.MatchSourceFinancialDocument[]
      );

      const [bankTransactionAfterMatch] = await bankTransactionClient.query(
        {
          where: {
            id: bankTransaction.id,
          },
        },
        ['id', 'matchId', 'matchStatus']
      );
      expect(bankTransactionAfterMatch.matchId).toBeDefined();
      expect(bankTransactionAfterMatch.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Matched
      );
      const afterMatch = await sfdClient.query(
        {
          where: { bankTransactionMatchId: bankTransactionAfterMatch.matchId },
        },
        [
          'id',
          'sboNumber',
          'MatchSourceFinancialDocument-BankTransactionMatch.status',
        ]
      );
      expect(afterMatch).toHaveLength(1);
      expect(
        afterMatch[0]['MatchSourceFinancialDocument-BankTransactionMatch']
          .status
      ).toEqual(EvstBankTransactionMatchStatus.Matched);
    });

    it('should offer both journal entry and vendor bills as suggestions', async () => {
      const transactionDate = PlainDate.from('2024-05-15');
      const transactionAmount = new Decimal(500);
      const bankTransaction = await createSampleBankTransaction(
        session,
        transactionAmount.times(-1), // make outbound
        transactionDate,
        'Wells Fargo Sweep'
      );

      // Create a journal entry
      const journalEntryId = await createSampleJournalEntry(
        session,
        {
          journalEntryName: 'Test Journal Entry',
          description: 'Test Description',
          type: EvstJournalEntryType.Manual,
          journalEntryStatus: EvstJournalEntryStatus.Posted,
          isIntercompany: false,
          entityIds: [flowInc.id],
          transactionalCurrencies: [flowInc.currency],
          postingDate: transactionDate,
        } as unknown as JournalEntryHeader.JournalEntryHeader,
        [
          {
            accountId: bankTransaction.coaAccountId,
            transactionalCurrency: flowInc.currency,
            debit: null,
            credit: { amount: transactionAmount },
            entityId: flowInc.id,
          },
          {
            accountId: bankTransaction.coaAccountId,
            transactionalCurrency: flowInc.currency,
            debit: { amount: transactionAmount },
            credit: null,
            entityId: flowInc.id,
          },
        ] as unknown as JournalEntryLine.JournalEntryLine[]
      );

      // Create a vendor bill
      const billNumber = session.util.uuid.v4();
      const bill = await createSampleVendorBill(
        session,
        transactionAmount,
        transactionDate,
        'USD',
        billNumber,
        'Porsche',
        flowInc.id,
        true
      );

      // Generate suggestions
      await bankTransactionMatchClient.createBankTransactionMatchSuggestions(
        bankTransaction
      );

      const [bankTransactionAfterSuggestion] =
        await bankTransactionClient.query(
          {
            where: {
              id: bankTransaction.id,
            },
          },
          ['id', 'matchId', 'matchStatus']
        );

      expect(bankTransactionAfterSuggestion.matchId).toBeDefined();
      expect(bankTransactionAfterSuggestion.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Suggested
      );

      // Get suggestions
      const suggestions = await sfdClient.query(
        {
          where: {
            bankTransactionMatchId: bankTransactionAfterSuggestion.matchId,
          },
        },
        ['id', 'sfdId', 'sboNumber', 'amount']
      );

      // Verify we got both types of suggestions
      expect(suggestions.length).toBe(2);

      const journalEntrySuggestion = suggestions.find(
        (s) => s.sfdId === journalEntryId
      );
      const vendorBillSuggestion = suggestions.find(
        (s) => s.sboNumber === bill.vendorBillHeaderNumber
      );

      expect(journalEntrySuggestion).toBeDefined();
      expect(vendorBillSuggestion).toBeDefined();
    });
  });

  describe('1:M', () => {
    let journalEntryId: number;
    let transactionDate: PlainDate;
    let transactionAmount1: Decimal;
    let transactionAmount2: Decimal;

    it('should suggest a journal entry with multiple lines matching to a single bank transaction', async () => {
      transactionDate = PlainDate.from('2024-03-20');
      transactionAmount1 = new Decimal(12_659.11);
      transactionAmount2 = new Decimal(596.11);
      const totalAmount = transactionAmount1.plus(transactionAmount2);

      const bankTransaction = await createSampleBankTransaction(
        session,
        totalAmount.times(-1), // make outbound
        transactionDate,
        'Wells Fargo Sweep'
      );

      journalEntryId = await createSampleJournalEntry(
        session,
        {
          journalEntryName: 'Test Multi-Line Journal Entry',
          description: 'Test Description for Multi-Line Entry',
          type: EvstJournalEntryType.Manual,
          journalEntryStatus: EvstJournalEntryStatus.Posted,
          isIntercompany: false,
          entityIds: [flowInc.id],
          transactionalCurrencies: [flowInc.currency],
          postingDate: transactionDate,
        } as unknown as JournalEntryHeader.JournalEntryHeader,
        [
          {
            accountId: bankTransaction.coaAccountId,
            transactionalCurrency: flowInc.currency,
            debit: null,
            credit: { amount: transactionAmount1 },
            entityId: flowInc.id,
          },
          {
            accountId: bankTransaction.coaAccountId,
            transactionalCurrency: flowInc.currency,
            debit: null,
            credit: { amount: transactionAmount2 },
            entityId: flowInc.id,
          },
          {
            accountId: bankTransaction.coaAccountId,
            transactionalCurrency: flowInc.currency,
            debit: { amount: totalAmount },
            credit: null,
            entityId: flowInc.id,
          },
        ] as unknown as JournalEntryLine.JournalEntryLine[]
      );

      await bankTransactionMatchClient.createBankTransactionMatchSuggestions(
        bankTransaction
      );

      const [bankTransactionAfterSuggestion] =
        await bankTransactionClient.query(
          {
            where: {
              id: bankTransaction.id,
            },
          },
          ['id', 'matchId', 'matchStatus']
        );
      expect(bankTransactionAfterSuggestion.matchId).toBeDefined();
      expect(bankTransactionAfterSuggestion.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Suggested
      );

      createdSuggestions = await sfdClient.query(
        {
          where: {
            bankTransactionMatchId: bankTransactionAfterSuggestion.matchId,
          },
        },
        ['id', 'sfdId', 'amount']
      );
      const expectedResult = [
        expect.objectContaining({
          id: expect.any(Number),
          sfdId: journalEntryId,
          amount: transactionAmount1.times(-1),
        }),
        expect.objectContaining({
          id: expect.any(Number),
          sfdId: journalEntryId,
          amount: transactionAmount2.times(-1),
        }),
      ];

      expect(createdSuggestions).toHaveLength(2);
      expect(createdSuggestions).toEqual(
        expect.arrayContaining(expectedResult)
      );

      // Match
      await bankTransactionMatchClient.matchBankTransactions(
        [bankTransaction],
        createdSuggestions as unknown as MatchSourceFinancialDocument.MatchSourceFinancialDocument[]
      );

      const [bankTransactionAfterMatch] = await bankTransactionClient.query(
        {
          where: {
            id: bankTransaction.id,
          },
        },
        ['id', 'matchId', 'matchStatus']
      );
      expect(bankTransactionAfterMatch.matchId).toBeDefined();
      expect(bankTransactionAfterMatch.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Matched
      );
      const afterMatch = await sfdClient.query(
        {
          where: { bankTransactionMatchId: bankTransactionAfterMatch.matchId },
        },
        [
          'id',
          'sboNumber',
          'MatchSourceFinancialDocument-BankTransactionMatch.status',
        ]
      );
      expect(afterMatch).toHaveLength(2);
      expect(
        afterMatch[0]['MatchSourceFinancialDocument-BankTransactionMatch']
          .status
      ).toEqual(EvstBankTransactionMatchStatus.Matched);
    });
  });

  describe('suggestion persistence', () => {
    it('should not clear suggestions on other bank transactions after a match', async () => {
      // Create two bank transactions
      const transactionDate = PlainDate.from('2024-08-15');
      const transactionAmount = new Decimal(750.5);

      const bankTransaction1 = await createSampleBankTransaction(
        session,
        transactionAmount.times(-1),
        transactionDate,
        'Wells Fargo Sweep'
      );

      const bankTransaction2 = await createSampleBankTransaction(
        session,
        transactionAmount.times(-1),
        transactionDate,
        'Wells Fargo Sweep'
      );

      // Create two journal entries
      const journalEntry1Id = await createSampleJournalEntry(
        session,
        {
          journalEntryName: 'Test Journal Entry 1',
          description: 'Test Description 1',
          type: EvstJournalEntryType.Manual,
          journalEntryStatus: EvstJournalEntryStatus.Posted,
          isIntercompany: false,
          entityIds: [flowInc.id],
          transactionalCurrencies: [flowInc.currency],
          postingDate: transactionDate,
        } as unknown as JournalEntryHeader.JournalEntryHeader,
        [
          {
            accountId: bankTransaction1.coaAccountId,
            transactionalCurrency: flowInc.currency,
            debit: null,
            credit: { amount: transactionAmount },
            entityId: flowInc.id,
          },
          {
            accountId: bankTransaction1.coaAccountId,
            transactionalCurrency: flowInc.currency,
            debit: { amount: transactionAmount },
            credit: null,
            entityId: flowInc.id,
          },
        ] as unknown as JournalEntryLine.JournalEntryLine[]
      );

      const journalEntry2Id = await createSampleJournalEntry(
        session,
        {
          journalEntryName: 'Test Journal Entry 2',
          description: 'Test Description 2',
          type: EvstJournalEntryType.Manual,
          journalEntryStatus: EvstJournalEntryStatus.Posted,
          isIntercompany: false,
          entityIds: [flowInc.id],
          transactionalCurrencies: [flowInc.currency],
          postingDate: transactionDate,
        } as unknown as JournalEntryHeader.JournalEntryHeader,
        [
          {
            accountId: bankTransaction2.coaAccountId,
            transactionalCurrency: flowInc.currency,
            debit: null,
            credit: { amount: transactionAmount },
            entityId: flowInc.id,
          },
          {
            accountId: bankTransaction2.coaAccountId,
            transactionalCurrency: flowInc.currency,
            debit: { amount: transactionAmount },
            credit: null,
            entityId: flowInc.id,
          },
        ] as unknown as JournalEntryLine.JournalEntryLine[]
      );

      await bankTransactionMatchClient.createBankTransactionMatchSuggestions({
        id: { $in: [bankTransaction1.id, bankTransaction2.id] },
      });

      // Verify initial suggestions exist for both transactions
      const bankTransactionsAfterSuggestion = await bankTransactionClient.query(
        { where: { id: { $in: [bankTransaction1.id, bankTransaction2.id] } } },
        ['id', 'matchId', 'matchStatus']
      );

      expect(bankTransactionsAfterSuggestion[0].matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Suggested
      );
      expect(bankTransactionsAfterSuggestion[1].matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Suggested
      );
      expect(bankTransactionsAfterSuggestion[0].matchId).not.toEqual(
        bankTransactionsAfterSuggestion[1].matchId
      );

      // Verify both transactions have both journal entries as suggestions
      const suggestionsForTransaction1 = await sfdClient.query(
        {
          where: {
            bankTransactionMatchId: bankTransactionsAfterSuggestion[0].matchId,
          },
        },
        ['id', 'sfdId', 'amount']
      );
      expect(suggestionsForTransaction1).toHaveLength(2);
      expect(suggestionsForTransaction1).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            sfdId: journalEntry1Id,
            amount: transactionAmount.times(-1),
          }),
          expect.objectContaining({
            sfdId: journalEntry2Id,
            amount: transactionAmount.times(-1),
          }),
        ])
      );
      const suggestionsForTransaction2 = await sfdClient.query(
        {
          where: {
            bankTransactionMatchId: bankTransactionsAfterSuggestion[1].matchId,
          },
        },
        ['id', 'sfdId', 'amount']
      );
      expect(suggestionsForTransaction2).toHaveLength(2);
      expect(suggestionsForTransaction2).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            sfdId: journalEntry1Id,
            amount: transactionAmount.times(-1),
          }),
          expect.objectContaining({
            sfdId: journalEntry2Id,
            amount: transactionAmount.times(-1),
          }),
        ])
      );

      // Match bankTransaction1 with journalEntry1
      const je1Suggestion = suggestionsForTransaction1.filter(
        (s) => s.sfdId === journalEntry1Id
      );
      await bankTransactionMatchClient.matchBankTransactions(
        [bankTransaction1] as BankTransactionNew.BankTransactionNew[],
        je1Suggestion as unknown as MatchSourceFinancialDocument.MatchSourceFinancialDocument[]
      );

      // Verify first transaction is matched
      const [bt1AfterMatch] = await bankTransactionClient.query(
        { where: { id: bankTransaction1.id } },
        ['matchStatus']
      );
      expect(bt1AfterMatch.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Matched
      );

      // Verify second transaction still has only journalEntry2 as suggestion
      const [bt2AfterMatch] = await bankTransactionClient.query(
        { where: { id: bankTransaction2.id } },
        ['matchStatus', 'matchId']
      );
      expect(bt2AfterMatch.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Suggested
      );

      const suggestions2 = await sfdClient.query(
        {
          where: {
            bankTransactionMatchId: bt2AfterMatch.matchId,
          },
        },
        ['id', 'sfdId', 'amount']
      );

      expect(suggestions2).toHaveLength(1);
      expect(suggestions2[0]).toEqual(
        expect.objectContaining({
          sfdId: journalEntry2Id,
          amount: transactionAmount.times(-1),
        })
      );
    });
  });
});
