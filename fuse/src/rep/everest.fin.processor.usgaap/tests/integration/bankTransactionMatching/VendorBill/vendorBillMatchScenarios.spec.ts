/* eslint-disable jest/no-disabled-tests */
import type { ISession } from '@everestsystems/content-core';
import type { TestTeardown } from '@everestsystems/content-core/lib/tests';
import { setupTest } from '@everestsystems/content-core/lib/tests';
import { PlainDate } from '@everestsystems/datetime';
import { Decimal } from '@everestsystems/decimal';
import { DraftV2 } from '@pkg/everest.appserver/types/metadata/DraftV2';
import { ApprovalPolicyHeader } from '@pkg/everest.base.approvals/types/ApprovalPolicyHeader';
import { Entity } from '@pkg/everest.base/types/Entity';
import { RejectedSuggestion } from '@pkg/everest.fin.accounting/types/RejectedSuggestion';
import { EvstBankTransactionMatchStatus } from '@pkg/everest.fin.base/types/enums/BankTransactionMatchStatus';
import { EvstStatusOfPayment } from '@pkg/everest.fin.base/types/enums/StatusOfPayment';
import { EvstVendorBillStatus } from '@pkg/everest.fin.base/types/enums/VendorBillStatus';
import { VendorBillHeaderBase } from '@pkg/everest.fin.expense/types/VendorBillHeaderBase';
import { VendorBillItemBase } from '@pkg/everest.fin.expense/types/VendorBillItemBase';
import { BankTransactionMatchTransient } from '@pkg/everest.fin.integration.bank.matching/types/BankTransactionMatchTransient';
import { BankTransactionNew } from '@pkg/everest.fin.integration.bank/types/BankTransactionNew';
import { MatchSourceBusinessObject } from '@pkg/everest.fin.integration.bank/types/MatchSourceBusinessObject';
import { MatchSourceFinancialDocument } from '@pkg/everest.fin.integration.bank/types/MatchSourceFinancialDocument';
import { createApprovalPolicyData, createEmployee, createSampleBankEntry, createSampleBankTransaction, createSampleVendorBill, createTestEntity, postVendorBill } from '@pkg/everest.fin.processor.usgaap/tests/integration/bankTransactionMatching/createSampleData';
import type { Employee } from '@pkg/everest.hr.base/types/Employee';
// NOTE: The tests within a describe block should be executed in order of their definition.
describe('bank transaction match scenarios: vendor bill', () => {
  let session: ISession;
  let teardown: TestTeardown;

  let bankTransactionMatchClient: BankTransactionMatchTransient.IControllerClient;
  let sfdClient: MatchSourceFinancialDocument.IControllerClient;
  let bankTransactionClient: BankTransactionNew.IControllerClient;

  let createdSuggestions: Partial<MatchSourceFinancialDocument.MatchSourceFinancialDocument>[];
  let flowInc: Partial<Entity.Entity>;
  let testEmployee: Partial<Employee.Employee>;
  beforeAll(async () => {
    ({ session, teardown } = await setupTest({
      withUser: 'test-user',
    }));

    bankTransactionMatchClient =
      await BankTransactionMatchTransient.client(session);
    sfdClient = await MatchSourceFinancialDocument.client(session);
    bankTransactionClient = await BankTransactionNew.client(session);

    const FLOW_INC = 'Flow Inc.';
    [flowInc] = await Entity.query(
      session,
      { where: { entityName: FLOW_INC }, take: 1 },
      ['id', 'currency', 'entityName']
    );
    const entity = await createTestEntity(session);
    testEmployee = await createEmployee(
      session,
      entity.id,
      session.serverEnvironment.currentUser.email
    );

    const aphClient = await ApprovalPolicyHeader.client(session);
    await aphClient.cleanAllApprovalPolicies();
  });

  afterAll(() => teardown());

  describe('1:1', () => {
    let bill: Pick<
      VendorBillHeaderBase.VendorBillHeaderBase,
      'id' | 'vendorBillHeaderNumber'
    >;

    let transactionDate: PlainDate;
    let transactionAmount: Decimal;
    let bankTransaction: Pick<
      BankTransactionNew.BankTransactionNew,
      'id' | 'coaAccountId'
    >;
    let bankTransaction2: Pick<
      BankTransactionNew.BankTransactionNew,
      'id' | 'coaAccountId'
    >;
    it('should not suggest to an inbound bank transaction', async () => {
      transactionDate = PlainDate.from('2024-11-29');
      transactionAmount = new Decimal(1.1421);
      const bankTransaction = await createSampleBankTransaction(
        session,
        transactionAmount,
        transactionDate,
        'Wells Fargo Sweep'
      );

      const billNumber = session.util.uuid.v4();
      bill = await createSampleVendorBill(
        session,
        transactionAmount,
        transactionDate,
        'USD',
        billNumber,
        'Porsche',
        flowInc.id,
        true
      );
      await bankTransactionMatchClient.createBankTransactionMatchSuggestions(
        bankTransaction
      );
      const [bankTransactionAfterSuggestion] =
        await bankTransactionClient.query(
          {
            where: {
              id: bankTransaction.id,
            },
          },
          ['id', 'matchId', 'matchStatus']
        );
      expect(bankTransactionAfterSuggestion.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Unmatched
      );
    });

    it('should suggest with entity with same currency', async () => {
      // this depends on the previous test. This reduces the execution time
      bankTransaction = await createSampleBankTransaction(
        session,
        transactionAmount.times(-1), // make outbound
        transactionDate,
        'Wells Fargo Sweep'
      );
      bankTransaction2 = await createSampleBankTransaction(
        session,
        transactionAmount.times(-1), // make outbound
        transactionDate,
        'Wells Fargo Sweep'
      );

      await bankTransactionMatchClient.createBankTransactionMatchSuggestions({
        id: { $in: [bankTransaction.id, bankTransaction2.id] },
      });

      const [bankTransactionAfterSuggestion] =
        await bankTransactionClient.query(
          {
            where: {
              id: bankTransaction.id,
            },
          },
          ['id', 'matchId', 'matchStatus']
        );
      expect(bankTransactionAfterSuggestion.matchId).toBeDefined();
      expect(bankTransactionAfterSuggestion.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Suggested
      );

      createdSuggestions = await sfdClient.query(
        {
          where: {
            bankTransactionMatchId: bankTransactionAfterSuggestion.matchId,
          },
        },
        ['id', 'sboNumber', 'bankTransactionMatchId', 'amount']
      );
      expect(createdSuggestions).toHaveLength(1);
      const expectedResult = [
        expect.objectContaining({
          id: expect.any(Number),
          sboNumber: bill.vendorBillHeaderNumber,
          bankTransactionMatchId: expect.any(Number),
          amount: expect.anything(),
        }),
      ];
      expect(createdSuggestions).toEqual(
        expect.arrayContaining(expectedResult)
      );
    });

    it('should create a payment for the suggested bill, match and unmatch with other bank transaction', async () => {
      const [bankTransaction2BeforeMatching] =
        await bankTransactionClient.query(
          {
            where: {
              id: bankTransaction2.id,
            },
          },
          ['id', 'matchId', 'matchStatus']
        );
      expect(bankTransaction2BeforeMatching.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Suggested
      );
      await bankTransactionMatchClient.createSFDForMatching(
        [{ id: bankTransaction.id }] as BankTransactionNew.BankTransactionNew[],
        createdSuggestions as unknown as MatchSourceFinancialDocument.MatchSourceFinancialDocument[]
      );
      await bankTransactionMatchClient.matchBankTransactions(
        [{ id: bankTransaction.id }] as BankTransactionNew.BankTransactionNew[],
        createdSuggestions as unknown as MatchSourceFinancialDocument.MatchSourceFinancialDocument[]
      );
      const [matchedBankTransaction] = await bankTransactionClient.query(
        { where: { id: bankTransaction.id } },
        ['matchStatus']
      );
      expect(matchedBankTransaction.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Matched
      );

      const [bankTransaction2AfterMatching] = await bankTransactionClient.query(
        {
          where: {
            id: bankTransaction2.id,
          },
        },
        ['id', 'matchId', 'matchStatus']
      );
      expect(bankTransaction2AfterMatching.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Unmatched
      );
    });

    it.skip('should not clear suggestions on other bank transactions after a match', async () => {
      // Create two bank transactions
      const transactionDate = PlainDate.from('2022-06-15');
      const transactionAmount = new Decimal(750.5);

      const bankTransaction1 = await createSampleBankTransaction(
        session,
        transactionAmount.times(-1),
        transactionDate,
        'Wells Fargo Sweep'
      );

      const bankTransaction2 = await createSampleBankTransaction(
        session,
        transactionAmount.times(-1),
        transactionDate,
        'Wells Fargo Sweep'
      );

      // Create two vendor bills
      const bill1 = await createSampleVendorBill(
        session,
        transactionAmount,
        transactionDate,
        'USD',
        session.util.uuid.v4(),
        'Porsche',
        flowInc.id,
        true
      );

      const bill2 = await createSampleVendorBill(
        session,
        transactionAmount,
        transactionDate,
        'USD',
        session.util.uuid.v4(),
        'Porsche',
        flowInc.id,
        true
      );

      await bankTransactionMatchClient.createBankTransactionMatchSuggestions({
        id: { $in: [bankTransaction1.id, bankTransaction2.id] },
      });

      // Verify initial suggestions exist for both transactions
      const bankTransactionsAfterSuggestion = await bankTransactionClient.query(
        { where: { id: { $in: [bankTransaction1.id, bankTransaction2.id] } } },
        ['id', 'matchId', 'matchStatus']
      );

      expect(bankTransactionsAfterSuggestion[0].matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Suggested
      );
      expect(bankTransactionsAfterSuggestion[1].matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Suggested
      );
      expect(bankTransactionsAfterSuggestion[0].matchId).not.toEqual(
        bankTransactionsAfterSuggestion[1].matchId
      );

      // Verify both transactions have both vendor bills as suggestions
      const suggestionsForTransaction1 = await sfdClient.query(
        {
          where: {
            bankTransactionMatchId: bankTransactionsAfterSuggestion[0].matchId,
          },
        },
        ['id', 'sboNumber', 'amount']
      );
      expect(suggestionsForTransaction1).toHaveLength(2);
      expect(suggestionsForTransaction1).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            sboNumber: bill1.vendorBillHeaderNumber,
            amount: transactionAmount.times(-1),
          }),
          expect.objectContaining({
            sboNumber: bill2.vendorBillHeaderNumber,
            amount: transactionAmount.times(-1),
          }),
        ])
      );

      const suggestionsForTransaction2 = await sfdClient.query(
        {
          where: {
            bankTransactionMatchId: bankTransactionsAfterSuggestion[1].matchId,
          },
        },
        ['id', 'sboNumber', 'amount']
      );
      expect(suggestionsForTransaction2).toHaveLength(2);
      expect(suggestionsForTransaction2).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            sboNumber: bill1.vendorBillHeaderNumber,
            amount: transactionAmount.times(-1),
          }),
          expect.objectContaining({
            sboNumber: bill2.vendorBillHeaderNumber,
            amount: transactionAmount.times(-1),
          }),
        ])
      );

      // Match bankTransaction1 with bill1
      const bill1Suggestion = suggestionsForTransaction1.filter(
        (s) => s.sboNumber === bill1.vendorBillHeaderNumber
      );
      await bankTransactionMatchClient.matchBankTransactions(
        [bankTransaction1] as BankTransactionNew.BankTransactionNew[],
        bill1Suggestion as unknown as MatchSourceFinancialDocument.MatchSourceFinancialDocument[]
      );

      // Verify first transaction is matched
      const [bt1AfterMatch] = await bankTransactionClient.query(
        { where: { id: bankTransaction1.id } },
        ['matchStatus']
      );
      expect(bt1AfterMatch.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Pending
      );

      // Verify second transaction still has only bill2 as suggestion
      const [bt2AfterMatch] = await bankTransactionClient.query(
        { where: { id: bankTransaction2.id } },
        ['matchStatus', 'matchId']
      );
      expect(bt2AfterMatch.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Suggested
      );

      const suggestions2 = await sfdClient.query(
        {
          where: {
            bankTransactionMatchId: bt2AfterMatch.matchId,
          },
        },
        ['id', 'sboNumber', 'amount']
      );

      expect(suggestions2).toHaveLength(1);
      expect(suggestions2[0]).toEqual(
        expect.objectContaining({
          sboNumber: bill2.vendorBillHeaderNumber,
          amount: transactionAmount.times(-1),
        })
      );
    });
  });

  describe('1:M', () => {
    it('should not match to an inbound bank transaction', async () => {
      const transactionDate = PlainDate.from('2024-09-27');
      const transactionAmount1 = new Decimal(123_659.11);
      const transactionAmount2 = new Decimal(5596.11);
      const bankTransaction = await createSampleBankTransaction(
        session,
        transactionAmount1.plus(transactionAmount2),
        transactionDate,
        'Wells Fargo Sweep'
      );

      await createSampleVendorBill(
        session,
        transactionAmount1,
        transactionDate,
        'USD',
        session.util.uuid.v4(),
        'Porsche',
        flowInc.id,
        true
      );
      await createSampleVendorBill(
        session,
        transactionAmount2,
        transactionDate,
        'USD',
        session.util.uuid.v4(),
        'Porsche',
        flowInc.id,
        true
      );
      await bankTransactionMatchClient.createBankTransactionMatchSuggestions(
        bankTransaction
      );
      const [bankTransactionAfterSuggestion] =
        await bankTransactionClient.query(
          {
            where: {
              id: bankTransaction.id,
            },
          },
          ['id', 'matchId', 'matchStatus']
        );
      expect(bankTransactionAfterSuggestion.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Unmatched
      );
    });

    it('should suggest one bank transaction with 2 bills with entity with same currency', async () => {
      const transactionDate = PlainDate.from('2024-08-26');
      const transactionAmount1 = new Decimal(11.11);
      const transactionAmount2 = new Decimal(111.11);
      const bankTransaction = await createSampleBankTransaction(
        session,
        transactionAmount1.plus(transactionAmount2).times(-1), // make outbound
        transactionDate,
        'Wells Fargo Sweep'
      );
      const bill1 = await createSampleVendorBill(
        session,
        transactionAmount1,
        transactionDate,
        'USD',
        session.util.uuid.v4(),
        'Porsche',
        flowInc.id,
        true
      );
      const bill2 = await createSampleVendorBill(
        session,
        transactionAmount2,
        transactionDate,
        'USD',
        session.util.uuid.v4(),
        'Porsche',
        flowInc.id,
        true
      );
      await bankTransactionMatchClient.createBankTransactionMatchSuggestions(
        bankTransaction
      );

      const [bankTransactionAfterSuggestion] =
        await bankTransactionClient.query(
          {
            where: {
              id: bankTransaction.id,
            },
          },
          ['id', 'matchId', 'matchStatus']
        );
      expect(bankTransactionAfterSuggestion.matchId).toBeDefined();
      expect(bankTransactionAfterSuggestion.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Suggested
      );
      createdSuggestions = await sfdClient.query(
        {
          where: {
            bankTransactionMatchId: bankTransactionAfterSuggestion.matchId,
          },
        },
        ['id', 'sboNumber']
      );
      expect(createdSuggestions).toHaveLength(2);
      const expectedResult = [
        { id: expect.any(Number), sboNumber: bill1.vendorBillHeaderNumber },
        { id: expect.any(Number), sboNumber: bill2.vendorBillHeaderNumber },
      ];
      expect(createdSuggestions).toEqual(
        expect.arrayContaining(expectedResult)
      );
    });

    it('should match a vendor bill and a BE with a transaction', async () => {
      const transactionDate = PlainDate.from('2023-08-26');
      const transactionAmount1 = new Decimal(100);
      const transactionAmount2 = new Decimal(1);
      const bankTransaction = await createSampleBankTransaction(
        session,
        transactionAmount1.plus(transactionAmount2).times(-1), // make outbound
        transactionDate,
        'Wells Fargo Sweep',
        undefined,
        undefined,
        ['id', 'coaAccountId', 'entityId', 'currency']
      );
      const bill = await createSampleVendorBill(
        session,
        transactionAmount1,
        transactionDate,
        'USD',
        session.util.uuid.v4(),
        'Porsche',
        flowInc.id,
        true
      );
      await bankTransactionMatchClient.createBankTransactionMatchSuggestions(
        bankTransaction
      );

      const [bankTransactionAfterSuggestion] =
        await bankTransactionClient.query(
          {
            where: {
              id: bankTransaction.id,
            },
          },
          ['id', 'matchId', 'matchStatus']
        );
      expect(bankTransactionAfterSuggestion.matchId).toBeDefined();
      expect(bankTransactionAfterSuggestion.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Suggested
      );

      const bankEntry = await createSampleBankEntry(
        session,
        transactionDate,
        bankTransaction.coaAccountId,
        flowInc.id,
        'USD',
        transactionAmount2.times(-1)
      );
      await bankTransactionMatchClient.createBankEntrySuggestion(
        { id: bankTransaction.id },
        bankEntry.id
      );
      createdSuggestions = await sfdClient.query(
        {
          where: {
            bankTransactionMatchId: bankTransactionAfterSuggestion.matchId,
          },
        },
        ['id', 'sboNumber', 'bankTransactionMatchId', 'amount']
      );
      expect(createdSuggestions).toHaveLength(2);
      expect(
        createdSuggestions.find(
          (s) => s.sboNumber === bill.vendorBillHeaderNumber
        ).id
      ).toBeDefined();

      // create payment
      await bankTransactionMatchClient.createSFDForMatching(
        [{ id: bankTransaction.id }] as BankTransactionNew.BankTransactionNew[],
        createdSuggestions as unknown as MatchSourceFinancialDocument.MatchSourceFinancialDocument[]
      );
      try {
        await bankTransactionMatchClient.matchBankTransactions(
          [
            { id: bankTransaction.id },
          ] as BankTransactionNew.BankTransactionNew[],
          createdSuggestions as unknown as MatchSourceFinancialDocument.MatchSourceFinancialDocument[]
        );
      } catch (error) {
        const suggestionString = JSON.stringify(createdSuggestions);
        throw new Error(
          `Error matching bank transactions: ${error.message} with suggestions: ${suggestionString}`
        );
      }
      const [matchedBankTransaction] = await bankTransactionClient.query(
        { where: { id: bankTransaction.id } },
        ['matchStatus']
      );
      expect(matchedBankTransaction.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Matched
      );
    });
  });

  describe('M:1', () => {
    it('should not suggest to an inbound bank transaction', async () => {
      const transactionDate = PlainDate.from('2024-07-25');
      const transactionAmount1 = new Decimal(12_659.11);
      const transactionAmount2 = new Decimal(596.11);
      const bankTransactions = await Promise.all([
        createSampleBankTransaction(
          session,
          transactionAmount1,
          transactionDate,
          'Wells Fargo Sweep'
        ),
        createSampleBankTransaction(
          session,
          transactionAmount2,
          transactionDate,
          'Wells Fargo Sweep'
        ),
      ]);
      const bankTransactionIds = bankTransactions.map(({ id }) => id);
      const billNumber = session.util.uuid.v4();
      const vendorBill = await createSampleVendorBill(
        session,
        transactionAmount1.plus(transactionAmount2),
        transactionDate,
        'USD',
        billNumber,
        'Porsche',
        flowInc.id,
        true
      );
      await bankTransactionMatchClient.createMultipleBankTransactionMatchSuggestions(
        bankTransactionIds
      );
      const bankTransactionsAfterSuggestion = await bankTransactionClient.query(
        {
          where: {
            id: { $in: bankTransactionIds },
          },
        },
        ['id', 'matchStatus']
      );
      expect(bankTransactionsAfterSuggestion[0].matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Unmatched
      );
      expect(bankTransactionsAfterSuggestion[1].matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Unmatched
      );
    });

    // todo: next release
    it.skip('should match 2 bank transactions with 1 vendor bill with entity with same currency', async () => {
      const transactionDate = PlainDate.from('2024-06-25');
      const transactionAmount1 = new Decimal(12_659.11);
      const transactionAmount2 = new Decimal(596.11);
      const bankTransactions = await Promise.all([
        createSampleBankTransaction(
          session,
          transactionAmount1.times(-1),
          transactionDate,
          'Wells Fargo Sweep'
        ),
        createSampleBankTransaction(
          session,
          transactionAmount2.times(-1),
          transactionDate,
          'Wells Fargo Sweep'
        ),
      ]);
      const bankTransactionIds = bankTransactions.map(({ id }) => id);
      const billNumber = session.util.uuid.v4();
      const bill = await createSampleVendorBill(
        session,
        transactionAmount1.plus(transactionAmount2),
        transactionDate,
        'USD',
        billNumber,
        'Porsche',
        flowInc.id,
        true
      );

      await bankTransactionMatchClient.createMultipleBankTransactionMatchSuggestions(
        bankTransactionIds
      );
      createdSuggestions = await sfdClient.query(
        { where: { bankTransactionId: { $in: bankTransactionIds } } },
        ['id', 'bankTransactionId', 'sboNumber']
      );
      expect(createdSuggestions).toHaveLength(2);
      const expectedResult = bankTransactionIds.map((bankTransactionId) => ({
        id: expect.any(Number),
        sboNumber: bill.vendorBillHeaderNumber,
        bankTransactionId,
      }));
      expect(createdSuggestions).toEqual(
        expect.arrayContaining(expectedResult)
      );
    });
  });

  describe('rejection', () => {
    it('should reject a outbound payment suggestion', async () => {
      const transactionDate = PlainDate.from('2024-05-01');
      const transactionAmount = new Decimal(111.11);
      const bankTransaction = await createSampleBankTransaction(
        session,
        transactionAmount.times(-1), // make outbound
        transactionDate,
        'Wells Fargo Sweep'
      );

      const billNumber = session.util.uuid.v4();
      const bill = await createSampleVendorBill(
        session,
        transactionAmount,
        transactionDate,
        'USD',
        billNumber,
        'Porsche',
        flowInc.id,
        true
      );

      await bankTransactionMatchClient.createBankTransactionMatchSuggestions(
        bankTransaction
      );
      const [bankTransactionAfterSuggestion] =
        await bankTransactionClient.query(
          {
            where: {
              id: bankTransaction.id,
            },
          },
          ['id', 'matchId', 'matchStatus']
        );
      expect(bankTransactionAfterSuggestion.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Suggested
      );
      createdSuggestions = await sfdClient.query(
        {
          where: {
            bankTransactionMatchId: bankTransactionAfterSuggestion.matchId,
          },
        },
        ['id', 'bankTransactionMatchId', 'sourceJournalEntryLineId']
      );

      // await bankTransactionMatchClient.clearMatches([]);  todo
      await bankTransactionMatchClient.rejectSuggestion(
        createdSuggestions[0] as unknown as MatchSourceFinancialDocument.MatchSourceFinancialDocument
      );
      const rejectedSuggestions = await RejectedSuggestion.query(
        session,
        { where: { sboId: bill.id, sboType: 'vendorBill' } },
        ['id']
      );
      expect(rejectedSuggestions).toHaveLength(1);
      const sbosAfterRejection = await MatchSourceBusinessObject.query(
        session,
        {
          where: {
            sboId: bill.id,
          },
        },
        ['id']
      );
      expect(sbosAfterRejection).toHaveLength(0);
    });
  });

  describe.skip('auto match', () => {
    // this is skiped because the auto match criteria are not defined
    it.skip('should auto match for high confident suggestion', async () => {
      const transactionDate = PlainDate.from('2024-05-24');
      const transactionAmount = new Decimal(111.11);

      const billNumber = session.util.uuid.v4();
      const bankTransactionDescription = `Payment for bill ${billNumber}`;
      const bankTransaction = await createSampleBankTransaction(
        session,
        transactionAmount.times(-1), // make outbound
        transactionDate,
        'Wells Fargo Sweep',
        undefined,
        'USD',
        ['id', 'coaAccountId'],
        bankTransactionDescription,
        'Porsche'
      );
      const bill = await createSampleVendorBill(
        session,
        transactionAmount,
        transactionDate,
        'USD',
        billNumber,
        'Porsche',
        flowInc.id,
        true
      );
      await bankTransactionMatchClient.createBankTransactionMatchSuggestions(
        bankTransaction
      );
      createdSuggestions = await sfdClient.query(
        { where: { bankTransactionId: bankTransaction.id } },
        [
          'id',
          'sboNumber',
          'MatchSourceFinancialDocument-BankTransactionMatch.status',
          'suggestionConfidence',
          'amount',
        ]
      );
      expect(createdSuggestions).toHaveLength(1);
      const expectedResult = [
        {
          id: expect.any(Number),
          sboNumber: bill.vendorBillHeaderNumber,
          'MatchSourceFinancialDocument-BankTransactionMatch': {
            status: EvstBankTransactionMatchStatus.AutoMatched,
          },
          suggestionConfidence: expect.anything(),
          amount: expect.anything(),
        },
      ];
      expect(createdSuggestions).toEqual(
        expect.arrayContaining(expectedResult)
      );
    });
  });

  describe('Incomplete vendor bills', () => {
    describe('draft bill', () => {
      let bill: Pick<
        VendorBillHeaderBase.VendorBillHeaderBase,
        'id' | 'vendorBillHeaderNumber'
      >;
      let bankTransaction: Pick<BankTransactionNew.BankTransactionNew, 'id'>;
      let suggestions: Pick<
        MatchSourceFinancialDocument.MatchSourceFinancialDocumentWithAssociation,
        | 'id'
        | 'sboNumber'
        | 'MatchSourceBusinessObject-MatchSourceFinancialDocument'
      >[];

      it('should suggest a draft vendor bill', async () => {
        const transactionDate = PlainDate.from('2023-05-04');
        const transactionAmount = new Decimal(564.11);
        bankTransaction = await createSampleBankTransaction(
          session,
          transactionAmount.times(-1), // make outbound
          transactionDate,
          'Wells Fargo Sweep'
        );

        const billNumber = session.util.uuid.v4();
        bill = await createSampleVendorBill(
          session,
          transactionAmount,
          transactionDate,
          'USD',
          billNumber,
          'Porsche',
          flowInc.id,
          true,
          false,
          true
        );
        await bankTransactionMatchClient.createBankTransactionMatchSuggestions(
          bankTransaction
        );

        const [bankTransactionAfterSuggestion] =
          await bankTransactionClient.query(
            {
              where: {
                id: bankTransaction.id,
              },
            },
            ['id', 'matchId', 'matchStatus']
          );
        expect(bankTransactionAfterSuggestion.matchId).toBeDefined();
        expect(bankTransactionAfterSuggestion.matchStatus).toEqual(
          EvstBankTransactionMatchStatus.Suggested
        );

        suggestions = await sfdClient.query(
          {
            where: {
              bankTransactionMatchId: bankTransactionAfterSuggestion.matchId,
            },
            draft: 'include',
          },
          [
            'id',
            'sboNumber',
            'MatchSourceBusinessObject-MatchSourceFinancialDocument.sboId',
          ]
        );
        expect(suggestions).toHaveLength(1);
        const expectedResult = [
          {
            id: expect.any(Number),
            sboNumber: 'Draft',
            'MatchSourceBusinessObject-MatchSourceFinancialDocument': [
              {
                sboId: bill.id,
              },
            ],
            $metadata: {
              isDraft: false,
            },
          },
        ];
        expect(suggestions).toEqual(expectedResult);
      });

      it('successfully match a draft bill', async () => {
        await bankTransactionMatchClient.matchBankTransactions(
          [
            { id: bankTransaction.id },
          ] as unknown as BankTransactionNew.BankTransactionNew[],
          suggestions as unknown as MatchSourceFinancialDocument.MatchSourceFinancialDocument[]
        );

        const [bankTransactionAfterMatch] = await bankTransactionClient.query(
          {
            where: {
              id: bankTransaction.id,
            },
          },
          ['id', 'matchId', 'matchStatus']
        );
        expect(bankTransactionAfterMatch.matchId).toBeDefined();
        expect(bankTransactionAfterMatch.matchStatus).toEqual(
          EvstBankTransactionMatchStatus.Pending
        );
        const afterMatch = await sfdClient.query(
          {
            where: {
              bankTransactionMatchId: bankTransactionAfterMatch.matchId,
            },
          },
          ['id', 'sboNumber']
        );
        expect(afterMatch).toHaveLength(1);
      });
      it('should change status to Matched after bill creation', async () => {
        const billLines = await VendorBillItemBase.query(
          session,
          { where: { vendorBillHeaderId: bill.id }, draft: 'include' },
          ['id']
        );
        const draftV2Client = await DraftV2.client(session);
        await draftV2Client.activate({
          [VendorBillHeaderBase.MODEL_URN]: [bill.id],
          [VendorBillItemBase.MODEL_URN]: billLines.map(({ id }) => id),
        });
        const vendorBillClient = await VendorBillHeaderBase.client(session);
        const createdBill = await vendorBillClient.approveVendorBill(bill.id, [
          'id',
          'vendorBillHeaderNumber',
          'status',
          'paymentStatus',
        ]);
        expect(createdBill.status).toEqual(EvstVendorBillStatus.Approved);
        expect(createdBill.paymentStatus).toEqual(
          EvstStatusOfPayment.FullyPaid
        );
        const [matchedBankTransaction] = await bankTransactionClient.query(
          { where: { id: bankTransaction.id } },
          [
            'id',
            'matchStatus',
            'matchingTransaction',
            'BankTransaction-BankTransactionMatch.MatchSourceFinancialDocument-BankTransactionMatch.sourceJournalEntryLineId',
          ]
        );
        expect(matchedBankTransaction.matchStatus).toEqual(
          EvstBankTransactionMatchStatus.Matched
        );
        expect(matchedBankTransaction.matchingTransaction).toContain('OPAY-');
      });

      it('should not suggest a matched bill', async () => {
        const transactionDate = PlainDate.from('2023-05-04');
        const transactionAmount = new Decimal(564.11);
        const newBankTransaction = await createSampleBankTransaction(
          session,
          transactionAmount.times(-1), // make outbound
          transactionDate,
          'Wells Fargo Sweep'
        );

        await bankTransactionMatchClient.createBankTransactionMatchSuggestions(
          newBankTransaction
        );
        const [bankTransactionAfterSuggestion] =
          await bankTransactionClient.query(
            {
              where: {
                id: newBankTransaction.id,
              },
            },
            ['id', 'matchId', 'matchStatus']
          );
        expect(bankTransactionAfterSuggestion.matchStatus).toEqual(
          EvstBankTransactionMatchStatus.Unmatched
        );
      });

      it('should not suggest a partial match with a draft bill', async () => {
        const transactionDate = PlainDate.from('2023-05-04');
        const transactionAmount = new Decimal(565_345.11);
        const bankTransactionWithDifferentAmount =
          await createSampleBankTransaction(
            session,
            transactionAmount.times(-1), // make outbound
            transactionDate,
            'Wells Fargo Sweep'
          );

        const bill = await createSampleVendorBill(
          session,
          transactionAmount.plus(1),
          transactionDate,
          'USD',
          session.util.uuid.v4(),
          'Porsche',
          flowInc.id,
          true,
          false,
          true
        );
        await bankTransactionMatchClient.createBankTransactionMatchSuggestions(
          bankTransactionWithDifferentAmount
        );

        const [bankTransactionAfterSuggestion] =
          await bankTransactionClient.query(
            {
              where: {
                id: bankTransactionWithDifferentAmount.id,
              },
            },
            ['id', 'matchId', 'matchStatus']
          );
        expect(bankTransactionAfterSuggestion.matchStatus).toEqual(
          EvstBankTransactionMatchStatus.Unmatched
        );
        // const suggestions = await MatchSourceBusinessObject.query(
        //   session,
        //   {
        //     where: {
        //       'MatchSourceBusinessObject-MatchSourceFinancialDocument.bankTransactionId':
        //         bankTransactionWithDifferentAmount.id,
        //       sboId: bill.id,
        //     },
        //   },
        //   ['id']
        // );
        // expect(suggestions).toHaveLength(0);
      });
    });

    describe('unposted bill', () => {
      let unpostedBill: Pick<
        VendorBillHeaderBase.VendorBillHeaderBase,
        'id' | 'vendorBillHeaderNumber'
      >;
      let bankTransaction: Pick<BankTransactionNew.BankTransactionNew, 'id'>;
      let suggestions: Pick<
        MatchSourceFinancialDocument.MatchSourceFinancialDocumentWithAssociation,
        | 'id'
        | 'sboNumber'
        | 'MatchSourceBusinessObject-MatchSourceFinancialDocument'
        | 'amount'
        | 'bankTransactionMatchId'
      >[];
      it('should suggest an unposted vendor bill', async () => {
        const transactionDate = PlainDate.from('2023-02-01');
        const transactionAmount = new Decimal(100.01);
        bankTransaction = await createSampleBankTransaction(
          session,
          transactionAmount.times(-1), // make outbound
          transactionDate,
          'Wells Fargo Sweep'
        );

        const billNumber = session.util.uuid.v4();
        unpostedBill = await createSampleVendorBill(
          session,
          transactionAmount,
          transactionDate,
          'USD',
          billNumber,
          'Porsche',
          flowInc.id,
          false
        );
        await bankTransactionMatchClient.createBankTransactionMatchSuggestions(
          bankTransaction
        );

        const [bankTransactionAfterSuggestion] =
          await bankTransactionClient.query(
            {
              where: {
                id: bankTransaction.id,
              },
            },
            ['id', 'matchId', 'matchStatus']
          );
        expect(bankTransactionAfterSuggestion.matchStatus).toEqual(
          EvstBankTransactionMatchStatus.Suggested
        );
        suggestions = await sfdClient.query(
          {
            where: {
              bankTransactionMatchId: bankTransactionAfterSuggestion.matchId,
            },
          },
          ['id', 'sboNumber', 'bankTransactionMatchId', 'amount']
        );
        expect(suggestions).toHaveLength(1);
        const expectedResult = [
          {
            id: expect.any(Number),
            sboNumber: unpostedBill.vendorBillHeaderNumber,
            bankTransactionMatchId: expect.any(Number),
            amount: transactionAmount.times(-1),
          },
        ];
        expect(suggestions).toEqual(expect.arrayContaining(expectedResult));
      });

      it('should create a payment for the unposted bill and match', async () => {
        await bankTransactionMatchClient.createSFDForMatching(
          [
            { id: bankTransaction.id },
          ] as BankTransactionNew.BankTransactionNew[],
          suggestions as MatchSourceFinancialDocument.MatchSourceFinancialDocument[]
        );
        await bankTransactionMatchClient.matchBankTransactions(
          [
            { id: bankTransaction.id },
          ] as BankTransactionNew.BankTransactionNew[],
          suggestions as MatchSourceFinancialDocument.MatchSourceFinancialDocument[]
        );
        const [matchedBankTransaction] = await bankTransactionClient.query(
          { where: { id: bankTransaction.id } },
          ['matchStatus', 'matchingTransaction']
        );
        expect(matchedBankTransaction.matchStatus).toEqual(
          EvstBankTransactionMatchStatus.Pending
        );
        expect(matchedBankTransaction.matchingTransaction).toContain('OPAY-');
      });

      it('should change status to Matched after bill posting', async () => {
        await postVendorBill(session, unpostedBill.id);
        const matchedBankTransaction = await bankTransactionClient.query(
          { where: { id: bankTransaction.id } },
          ['id', 'matchStatus']
        );
        expect(matchedBankTransaction[0].matchStatus).toEqual(
          EvstBankTransactionMatchStatus.Matched
        );
      });
    });

    describe('unapproved bill', () => {
      let bill: Pick<
        VendorBillHeaderBase.VendorBillHeaderBase,
        'id' | 'vendorBillHeaderNumber'
      >;
      let bankTransaction: Pick<BankTransactionNew.BankTransactionNew, 'id'>;
      let suggestions: Pick<
        MatchSourceFinancialDocument.MatchSourceFinancialDocumentWithAssociation,
        | 'id'
        | 'sboNumber'
        | 'MatchSourceBusinessObject-MatchSourceFinancialDocument'
      >[];
      it('should suggest an unapproved vendor bill', async () => {
        const transactionDate = PlainDate.from('2023-04-02');
        const transactionAmount = new Decimal(202.02);
        bankTransaction = await createSampleBankTransaction(
          session,
          transactionAmount.times(-1), // make outbound
          transactionDate,
          'Wells Fargo Sweep'
        );

        await createApprovalPolicyData(session, testEmployee.id);
        const billNumber = session.util.uuid.v4();
        bill = await createSampleVendorBill(
          session,
          transactionAmount,
          transactionDate,
          'USD',
          billNumber,
          'Porsche',
          flowInc.id,
          true,
          false
        );
        await bankTransactionMatchClient.createBankTransactionMatchSuggestions(
          bankTransaction
        );

        const [bankTransactionAfterSuggestion] =
          await bankTransactionClient.query(
            {
              where: {
                id: bankTransaction.id,
              },
            },
            ['id', 'matchId', 'matchStatus']
          );
        expect(bankTransactionAfterSuggestion.matchId).toBeDefined();
        expect(bankTransactionAfterSuggestion.matchStatus).toEqual(
          EvstBankTransactionMatchStatus.Suggested
        );
        suggestions = await sfdClient.query(
          {
            where: {
              bankTransactionMatchId: bankTransactionAfterSuggestion.matchId,
            },
          },
          ['id', 'sboNumber']
        );
        expect(suggestions).toHaveLength(1);
        const expectedResult = [
          { id: expect.any(Number), sboNumber: bill.vendorBillHeaderNumber },
        ];
        expect(suggestions).toEqual(expect.arrayContaining(expectedResult));
      });

      it('successfully match a unapproved bill', async () => {
        await bankTransactionMatchClient.matchBankTransactions(
          [
            { id: bankTransaction.id },
          ] as unknown as BankTransactionNew.BankTransactionNew[],
          suggestions as unknown as MatchSourceFinancialDocument.MatchSourceFinancialDocument[]
        );
        const [bankTransactionAfterMatch] = await bankTransactionClient.query(
          {
            where: {
              id: bankTransaction.id,
            },
          },
          ['id', 'matchId', 'matchStatus']
        );
        expect(bankTransactionAfterMatch.matchId).toBeDefined();
        expect(bankTransactionAfterMatch.matchStatus).toEqual(
          EvstBankTransactionMatchStatus.Pending
        );
      });

      it('should change status to Matched after bill approval', async () => {
        const vendorBillClient = await VendorBillHeaderBase.client(session);
        const createdBill = await vendorBillClient.approveVendorBill(
          bill.id,
          ['id', 'vendorBillHeaderNumber', 'status', 'paymentStatus'],
          { employeeId: testEmployee.id, autoApprove: true }
        );
        expect(createdBill.status).toEqual(EvstVendorBillStatus.Approved);
        expect(createdBill.paymentStatus).toEqual(
          EvstStatusOfPayment.FullyPaid
        );
        const matchedBankTransaction = await BankTransactionNew.query(
          session,
          { where: { id: bankTransaction.id } },
          ['id', 'matchStatus']
        );
        expect(matchedBankTransaction[0].matchStatus).toEqual(
          EvstBankTransactionMatchStatus.Matched
        );

        // cleanup all approval policies
        const aphClient = await ApprovalPolicyHeader.client(session);
        await aphClient.cleanAllApprovalPolicies();
      });
    });
  });
});
