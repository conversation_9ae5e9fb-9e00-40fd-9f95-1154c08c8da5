/* eslint-disable jest/no-disabled-tests */
import type { ISession } from '@everestsystems/content-core';
import type { TestTeardown } from '@everestsystems/content-core/lib/tests';
import { setupTest } from '@everestsystems/content-core/lib/tests';
import { PlainDate } from '@everestsystems/datetime';
import { Decimal } from '@everestsystems/decimal';
import { ApprovalPolicyHeader } from '@pkg/everest.base.approvals/types/ApprovalPolicyHeader';
import { Entity } from '@pkg/everest.base/types/Entity';
import { EvstPaymentType } from '@pkg/everest.fin.accounting/types/enums/PaymentType';
import type { Invoice } from '@pkg/everest.fin.accounting/types/Invoice';
import { RejectedSuggestion } from '@pkg/everest.fin.accounting/types/RejectedSuggestion';
import { EvstBankTransactionMatchStatus } from '@pkg/everest.fin.base/types/enums/BankTransactionMatchStatus';
import { BankTransactionMatchTransient } from '@pkg/everest.fin.integration.bank.matching/types/BankTransactionMatchTransient';
import { BankTransactionNew } from '@pkg/everest.fin.integration.bank/types/BankTransactionNew';
import { MatchSourceBusinessObject } from '@pkg/everest.fin.integration.bank/types/MatchSourceBusinessObject';
import { MatchSourceFinancialDocument } from '@pkg/everest.fin.integration.bank/types/MatchSourceFinancialDocument';
import {
  createSampleBankTransaction,
  createSampleInvoice,
} from '@pkg/everest.fin.processor.usgaap/tests/integration/bankTransactionMatching/createSampleData';

// NOTE: The tests within a describe block should be executed in order of their definition.
describe('bank transaction match scenarios: invoice', () => {
  let session: ISession;
  let teardown: TestTeardown;

  let bankTransactionMatchClient: BankTransactionMatchTransient.IControllerClient;
  let sfdClient: MatchSourceFinancialDocument.IControllerClient;
  let bankTransactionClient: BankTransactionNew.IControllerClient;

  let createdSuggestions: Partial<MatchSourceFinancialDocument.MatchSourceFinancialDocument>[];
  let flowInc: Partial<Entity.Entity>;

  beforeAll(async () => {
    ({ session, teardown } = await setupTest({
      withUser: 'test-user',
    }));

    bankTransactionMatchClient =
      await BankTransactionMatchTransient.client(session);
    sfdClient = await MatchSourceFinancialDocument.client(session);
    bankTransactionClient = await BankTransactionNew.client(session);

    const FLOW_INC = 'Flow Inc.';
    [flowInc] = await Entity.query(
      session,
      { where: { entityName: FLOW_INC }, take: 1 },
      ['id', 'currency', 'entityName']
    );

    const aphClient = await ApprovalPolicyHeader.client(session);
    await aphClient.cleanAllApprovalPolicies();
  });

  afterAll(() => teardown());

  describe('1:1', () => {
    const BANK_FEE_AMOUNT = new Decimal(10);

    let invoice: Pick<
      Invoice.Invoice,
      'id' | 'invoiceNumber' | 'totalAmountDue'
    >;

    let transactionDate: PlainDate;
    let bankTransaction: Pick<
      BankTransactionNew.BankTransactionNew,
      'id' | 'coaAccountId'
    >;
    it('should not suggest to an outbound bank transaction', async () => {
      transactionDate = PlainDate.from('2025-01-25');

      invoice = await createSampleInvoice(session, transactionDate);

      const bankTransaction = await createSampleBankTransaction(
        session,
        invoice.totalAmountDue.amount.times(-1),
        transactionDate,
        'Wells Fargo Sweep'
      );

      await bankTransactionMatchClient.createBankTransactionMatchSuggestions(
        bankTransaction
      );
      const [bankTransactionAfterSuggestion] =
        await bankTransactionClient.query(
          {
            where: {
              id: bankTransaction.id,
            },
          },
          ['id', 'matchId', 'matchStatus']
        );
      expect(bankTransactionAfterSuggestion.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Unmatched
      );
    });

    it('should suggest with entity and with invoice number in description', async () => {
      // this depends on the previous test. This reduces the execution time
      bankTransaction = await createSampleBankTransaction(
        session,
        invoice.totalAmountDue.amount.minus(BANK_FEE_AMOUNT),
        transactionDate,
        'Wells Fargo Sweep',
        undefined,
        undefined,
        ['id', 'amount', 'currency', 'description', 'createdDate'],
        invoice.invoiceNumber
      );

      await bankTransactionMatchClient.createBankTransactionMatchSuggestions(
        bankTransaction
      );

      const [bankTransactionAfterSuggestion] =
        await bankTransactionClient.query(
          {
            where: {
              id: bankTransaction.id,
            },
          },
          ['id', 'matchId', 'matchStatus']
        );
      expect(bankTransactionAfterSuggestion.matchId).toBeDefined();
      expect(bankTransactionAfterSuggestion.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Suggested
      );

      createdSuggestions = await sfdClient.query(
        {
          where: {
            bankTransactionMatchId: bankTransactionAfterSuggestion.matchId,
          },
        },
        ['id', 'sboNumber', 'bankTransactionMatchId', 'amount']
      );
      expect(createdSuggestions).toHaveLength(1);
      const expectedResult = [
        expect.objectContaining({
          id: expect.any(Number),
          sboNumber: invoice.invoiceNumber,
          bankTransactionMatchId: expect.any(Number),
          amount: expect.anything(),
        }),
      ];
      expect(createdSuggestions).toEqual(
        expect.arrayContaining(expectedResult)
      );
    });

    it('should create a payment for the suggested invoice and match', async () => {
      await bankTransactionMatchClient.createSFDForMatching(
        [{ id: bankTransaction.id }] as BankTransactionNew.BankTransactionNew[],
        createdSuggestions as unknown as MatchSourceFinancialDocument.MatchSourceFinancialDocument[],
        {
          bankFeeAmount: { amount: BANK_FEE_AMOUNT },
          paymentType: EvstPaymentType.Check,
        } as any
      );
      await bankTransactionMatchClient.matchBankTransactions(
        [{ id: bankTransaction.id }] as BankTransactionNew.BankTransactionNew[],
        createdSuggestions as unknown as MatchSourceFinancialDocument.MatchSourceFinancialDocument[]
      );
      const [matchedBankTransaction] = await bankTransactionClient.query({
        where: { id: bankTransaction.id },
      });

      expect(matchedBankTransaction.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Matched
      );
      expect(matchedBankTransaction.amount.amount).toEqual(
        invoice.totalAmountDue.amount.minus(BANK_FEE_AMOUNT)
      );
    });
  });

  describe('1:M', () => {
    let bankTransaction: Pick<
      BankTransactionNew.BankTransactionNew,
      'id' | 'coaAccountId'
    >;
    it('should suggest one bank transaction with 2 invoices with entity, same currency and invoice numbers being in description', async () => {
      const transactionDate = PlainDate.from('2025-02-26');

      const invoice1 = await createSampleInvoice(session, transactionDate, '4');
      const invoice2 = await createSampleInvoice(session, transactionDate, '7');
      const bankTransactionAmount = invoice1.totalAmountDue.amount.plus(
        invoice2.totalAmountDue.amount
      );

      bankTransaction = await createSampleBankTransaction(
        session,
        bankTransactionAmount,
        transactionDate,
        'Wells Fargo Sweep',
        undefined,
        undefined,
        undefined,
        `${invoice1.invoiceNumber}, ${invoice2.invoiceNumber}`
      );

      await bankTransactionMatchClient.createBankTransactionMatchSuggestions(
        bankTransaction
      );

      const [bankTransactionAfterSuggestion] =
        await bankTransactionClient.query(
          {
            where: {
              id: bankTransaction.id,
            },
          },
          ['id', 'matchId', 'matchStatus']
        );
      expect(bankTransactionAfterSuggestion.matchId).toBeDefined();
      expect(bankTransactionAfterSuggestion.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Suggested
      );
      createdSuggestions = await sfdClient.query(
        {
          where: {
            bankTransactionMatchId: bankTransactionAfterSuggestion.matchId,
          },
        },
        ['id', 'sboNumber', 'bankTransactionMatchId', 'amount']
      );

      const expectedResult = [
        {
          id: expect.any(Number),
          bankTransactionMatchId: expect.any(Number),
          amount: invoice1.totalAmountDue.amount,
          sboNumber: invoice1.invoiceNumber,
        },
        {
          id: expect.any(Number),
          bankTransactionMatchId: expect.any(Number),
          amount: invoice2.totalAmountDue.amount,
          sboNumber: invoice2.invoiceNumber,
        },
      ];
      expect(createdSuggestions).toEqual(
        expect.arrayContaining(expectedResult)
      );
    });
    it('should create one payment when both invoices are selected to match', async () => {
      await bankTransactionMatchClient.createSFDForMatching(
        [{ id: bankTransaction.id }] as BankTransactionNew.BankTransactionNew[],
        createdSuggestions as unknown as MatchSourceFinancialDocument.MatchSourceFinancialDocument[],
        {
          bankFeeAmount: { amount: new Decimal(0) },
          paymentType: EvstPaymentType.Check,
        } as any
      );
      await bankTransactionMatchClient.matchBankTransactions(
        [{ id: bankTransaction.id }] as BankTransactionNew.BankTransactionNew[],
        createdSuggestions as unknown as MatchSourceFinancialDocument.MatchSourceFinancialDocument[]
      );
      const [matchedBankTransaction] = await bankTransactionClient.query({
        where: { id: bankTransaction.id },
      });

      expect(matchedBankTransaction.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Matched
      );
      for (const item of createdSuggestions) {
        expect(matchedBankTransaction.matchingTransaction).toContain(
          item.sboNumber
        );
      }
      const suggestionsAfterMatch = await sfdClient.query(
        {
          where: {
            bankTransactionMatchId: matchedBankTransaction.matchId,
          },
        },
        ['sourceJournalEntryLineId', 'id']
      );

      expect(suggestionsAfterMatch).toHaveLength(1);
    });
  });

  describe('M:1', () => {
    it('should suggest invoice if two bank transactions match one invoice amount, date and customer name', async () => {
      const transactionDate = PlainDate.from('2025-02-01');
      const invoice = await createSampleInvoice(session, transactionDate, '2');
      const transactionAmount = invoice.totalAmountDue.amount.dividedBy(2);

      const bankTransactions = await Promise.all([
        createSampleBankTransaction(
          session,
          transactionAmount,
          transactionDate,
          'Wells Fargo Sweep',
          undefined,
          undefined,
          undefined,
          undefined,
          'Flow Junkie'
        ),
        createSampleBankTransaction(
          session,
          transactionAmount,
          transactionDate,
          'Wells Fargo Sweep',
          undefined,
          undefined,
          undefined,
          undefined,
          'Flow Junkie'
        ),
      ]);
      const bankTransactionIds = bankTransactions.map(({ id }) => id);

      await bankTransactionMatchClient.createMultipleBankTransactionMatchSuggestions(
        bankTransactionIds
      );
      const bankTransactionsAfterSuggestion = await bankTransactionClient.query(
        {
          where: {
            id: { $in: bankTransactionIds },
          },
        },
        ['id', 'matchStatus']
      );
      expect(bankTransactionsAfterSuggestion[0].matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Suggested
      );
      expect(bankTransactionsAfterSuggestion[1].matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Suggested
      );
    });
  });

  describe('rejection', () => {
    it('should reject a customer payment suggestion', async () => {
      const transactionDate = PlainDate.from('2025-01-01');
      const invoice = await createSampleInvoice(session, transactionDate, '1');
      const bankTransaction = await createSampleBankTransaction(
        session,
        invoice.totalAmountDue.amount,
        transactionDate,
        'Wells Fargo Sweep',
        undefined,
        undefined,
        ['id', 'amount', 'currency', 'description', 'createdDate'],
        invoice.invoiceNumber
      );

      await bankTransactionMatchClient.createBankTransactionMatchSuggestions(
        bankTransaction
      );
      const [bankTransactionAfterSuggestion] =
        await bankTransactionClient.query(
          {
            where: {
              id: bankTransaction.id,
            },
          },
          ['id', 'matchId', 'matchStatus']
        );
      expect(bankTransactionAfterSuggestion.matchStatus).toEqual(
        EvstBankTransactionMatchStatus.Suggested
      );
      createdSuggestions = await sfdClient.query(
        {
          where: {
            bankTransactionMatchId: bankTransactionAfterSuggestion.matchId,
          },
        },
        ['id', 'bankTransactionMatchId', 'sourceJournalEntryLineId']
      );

      await bankTransactionMatchClient.rejectSuggestion(
        createdSuggestions[0] as unknown as MatchSourceFinancialDocument.MatchSourceFinancialDocument
      );
      const rejectedSuggestions = await RejectedSuggestion.query(
        session,
        { where: { sboId: invoice.id, sboType: 'invoice' } },
        ['id']
      );
      expect(rejectedSuggestions).toHaveLength(1);
      const sbosAfterRejection = await MatchSourceBusinessObject.query(
        session,
        {
          where: {
            sboId: invoice.id,
          },
        },
        ['id']
      );
      expect(sbosAfterRejection).toHaveLength(0);
    });
  });
});
