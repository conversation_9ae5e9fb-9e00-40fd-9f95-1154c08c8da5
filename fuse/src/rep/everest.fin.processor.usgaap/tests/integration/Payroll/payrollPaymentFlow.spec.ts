import type { ISession } from '@everestsystems/content-core';
import type { TestTeardown } from '@everestsystems/content-core/lib/tests';
import { setupTest } from '@everestsystems/content-core/lib/tests';
import { JournalEntryHeader } from '@pkg/everest.fin.accounting/types/JournalEntryHeader';
import { EvstOutboundPaymentType } from '@pkg/everest.fin.base/types/enums/OutboundPaymentType';
import { EvstPaymentStatus } from '@pkg/everest.fin.base/types/enums/PaymentStatus';
import { EvstPayrollDetailStatus } from '@pkg/everest.fin.base/types/enums/PayrollDetailStatus';
import { OutboundPaymentItemBase } from '@pkg/everest.fin.expense/types/OutboundPaymentItemBase';
import { PayrollDetail } from '@pkg/everest.fin.expense/types/PayrollDetail';
import { PayrollTemplate } from '@pkg/everest.fin.expense/types/PayrollTemplate';

import * as utilities from './utilities';

describe('Payroll Payment integration test - Manual Payment', () => {
  /*
   * Manual Creation
   * --------------------------------
   * Here are the flow steps:
   * - Create PD with Payroll Provider Payee amount 903.00
   * - Create first partial payment of 500 for Payroll Provider Payee
   * - Create second partial payment of 500 for Payroll Provider Payee -> it should throw error as 500.00 > 403.00
   * - Create same partial payment as draft -> it should not fail
   * - Try to activate that same partial payment -> it should throw error as 500.00 > 403.00
   * - Update draft payment amount to 403 and activate it -> it should not fail
   * - Try to update to invalid amount -> it should throw error
   * - Try to do normal update -> it should not fail
   */

  let session: ISession;
  let teardown: TestTeardown;

  beforeAll(async () => {
    ({ session, teardown } = await setupTest({
      withUser: 'test-user',
    }));
  });

  afterAll(async () => {
    await teardown();
  });

  it('should create or update manual payment for Payroll Provider amounts', async () => {
    const payrollDetailClient = await PayrollDetail.client(session);
    const outboundPaymentItemClient =
      await OutboundPaymentItemBase.client(session);
    const journalEntryHeaderClient = await JournalEntryHeader.client(session);
    await PayrollTemplate.deleteAll(session);

    // Create test payroll detail
    await payrollDetailClient.upsertTestPayrollDetail();

    const testPDs = await payrollDetailClient.query(
      {
        where: {
          description: 'Test Payroll Detail',
          totalAmountPaid: { amount: 0 },
        },
      },
      ['id', 'payrollDetailNumber', 'entityId', 'status']
    );
    const payrollDetail = testPDs[0];
    expect(payrollDetail).toBeDefined();
    expect(payrollDetail.status).toEqual(EvstPayrollDetailStatus.Approved);

    const journalEntries = await journalEntryHeaderClient.query(
      {
        where: {
          sourceFinancialDocumentId: payrollDetail.id,
          sourceFinancialDocumentType: 'PayrollDetail',
        },
      },
      ['id', 'transactionalCurrencies', 'postingDate']
    );

    expect(journalEntries).toHaveLength(1);
    expect(journalEntries[0].transactionalCurrencies[0]).toEqual('USD');

    // Get sample data
    const vendorId = await utilities.getVendorByName(session, 'Audi');
    const accountId = await utilities.getAccountByNumber(session, '213105');
    const paymentAccountId = await utilities.getAccountByNumber(
      session,
      '111107'
    );
    const payeeId = await utilities.getPayeeByName(session, 'payrollProvider');

    const paymentHeader = {
      accountId: paymentAccountId,
      entityId: testPDs[0].entityId,
      paymentDate: '2023-05-01',
      commonDescription: 'Payroll Test Payment',
      singlePayment: true,
    };

    // Create first partial payment of 500 because total payable amount is 903 for Payroll Provider Payee
    const payrollPaymentApplications = [
      {
        accountId,
        vendorId,
        payrollDetailId: testPDs[0].id,
        payeeId,
        amountPaid: { amount: 500 },
        description: 'Test Payment',
      },
    ];

    const payrollPayment = await payrollDetailClient.upsertPayrollPayments(
      paymentHeader as any,
      payrollPaymentApplications as any,
      undefined,
      EvstOutboundPaymentType.Manual,
      ['id'],
      { draft: false }
    );

    expect(payrollPayment.id).toBeDefined();

    const payrollOutboundPaymentItems = await outboundPaymentItemClient.query(
      {
        where: {
          outboundPaymentHeaderId: payrollPayment.id,
        },
        draft: 'exclude',
      },
      ['id', 'sourcePaymentDocumentId', 'description']
    );

    expect(payrollOutboundPaymentItems[0].id).toBeDefined();
    expect(payrollOutboundPaymentItems[0].description).toStrictEqual(
      'Test Payment'
    );

    const paymentJournalEntry = await journalEntryHeaderClient.query(
      {
        where: {
          sourceFinancialDocumentId: payrollPayment.id,
          sourceFinancialDocumentType: 'OutboundPaymentHeaderBase',
        },
      },
      ['id', 'transactionalCurrencies', 'postingDate']
    );

    expect(paymentJournalEntry).toHaveLength(1);

    // Create another payment excedding total due amount
    const payrollPaymentApplications2 = [
      {
        accountId,
        vendorId,
        payrollDetailId: testPDs[0].id,
        payeeId,
        amountPaid: { amount: 500 },
      },
    ];

    // It should throw error
    await expect(
      payrollDetailClient.upsertPayrollPayments(
        paymentHeader as any,
        payrollPaymentApplications2 as any,
        undefined,
        EvstOutboundPaymentType.Manual,
        ['id'],
        { draft: false }
      )
    ).rejects.toThrow(
      `Total amount paid 500.00 for payroll ${payrollDetail.payrollDetailNumber} exceeds its total amount due 403.00 for payee type Payroll Provider.`
    );

    // Try to create draft payment, it should not throw error
    const draftPayment = await payrollDetailClient.upsertPayrollPayments(
      paymentHeader as any,
      payrollPaymentApplications2 as any,
      undefined,
      EvstOutboundPaymentType.Manual,
      ['id', 'status'],
      { draft: true }
    );

    expect(draftPayment.id).toBeDefined();
    expect(draftPayment.status).toBeUndefined();

    const draftPaymentJournalEntry = await journalEntryHeaderClient.query(
      {
        where: {
          sourceFinancialDocumentId: draftPayment.id,
          sourceFinancialDocumentType: 'OutboundPaymentHeaderBase',
        },
      },
      ['id', 'transactionalCurrencies', 'postingDate']
    );

    // It should not have JE
    expect(draftPaymentJournalEntry).toHaveLength(0);

    const draftOutboundPaymentItems = await outboundPaymentItemClient.query(
      {
        where: {
          outboundPaymentHeaderId: draftPayment.id,
        },
        draft: 'include', // It is a draft
      },
      ['id', 'sourcePaymentDocumentId']
    );

    // It should throw error, while activating the draft payment because payment amount is more than amount due
    await expect(
      payrollDetailClient.upsertPayrollPayments(
        { id: draftPayment.id, ...paymentHeader } as any,
        [
          {
            id: draftOutboundPaymentItems[0].sourcePaymentDocumentId,
            ...payrollPaymentApplications2[0],
          },
        ] as any,
        undefined,
        EvstOutboundPaymentType.Manual,
        ['id'],
        { draft: true, activateDraft: true }
      )
    ).rejects.toThrow(
      `Total amount paid 500.00 for payroll ${payrollDetail.payrollDetailNumber} exceeds its total amount due 403.00 for payee type Payroll Provider.`
    );

    // Update draft payment to have correct amount and activate it
    const activatedPayment = await payrollDetailClient.upsertPayrollPayments(
      { id: draftPayment.id, ...paymentHeader } as any,
      [
        {
          ...payrollPaymentApplications2[0],
          id: draftOutboundPaymentItems[0].sourcePaymentDocumentId,
          amountPaid: { amount: 403 },
        },
      ] as any,
      undefined,
      EvstOutboundPaymentType.Manual,
      ['id', 'status'],
      { draft: true, activateDraft: true }
    );

    expect(activatedPayment.id).toStrictEqual(draftPayment.id);
    expect(activatedPayment.status).toStrictEqual(
      EvstPaymentStatus.ManuallyRecorded
    );

    const activatedPaymentJournalEntry = await journalEntryHeaderClient.query(
      {
        where: {
          sourceFinancialDocumentId: draftPayment.id,
          sourceFinancialDocumentType: 'OutboundPaymentHeaderBase',
        },
      },
      ['id']
    );

    // It should have JE now
    expect(activatedPaymentJournalEntry).toHaveLength(1);

    // It should throw error, while updating the payment if payment amount is more than current call amount due i.e. ((903-905)+405) = 403
    await expect(
      payrollDetailClient.upsertPayrollPayments(
        { id: draftPayment.id, ...paymentHeader } as any,
        [
          {
            ...payrollPaymentApplications2[0],
            id: draftOutboundPaymentItems[0].sourcePaymentDocumentId,
            amountPaid: { amount: 405 },
          },
        ] as any,
        undefined,
        EvstOutboundPaymentType.Manual,
        ['id'],
        { draft: false, activateDraft: false }
      )
    ).rejects.toThrow(
      `Total amount paid 405.00 for payroll ${payrollDetail.payrollDetailNumber} exceeds its total amount due 403.00 for payee type Payroll Provider.`
    );

    // It should not throw error for normal update
    const updatedPayment = await payrollDetailClient.upsertPayrollPayments(
      { id: draftPayment.id, ...paymentHeader } as any,
      [
        {
          ...payrollPaymentApplications2[0],
          id: draftOutboundPaymentItems[0].sourcePaymentDocumentId,
          amountPaid: { amount: 403 },
        },
      ] as any,
      undefined,
      EvstOutboundPaymentType.Manual,
      ['id'],
      { draft: false, activateDraft: false }
    );
    expect(updatedPayment.id).toStrictEqual(draftPayment.id);
  });
});
