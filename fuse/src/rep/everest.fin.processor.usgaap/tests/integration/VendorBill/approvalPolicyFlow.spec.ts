/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable jest/no-standalone-expect */
/* eslint-disable jest/unbound-method */
import { getTranslations, type ISession } from '@everestsystems/content-core';
import type { TestTeardown } from '@everestsystems/content-core/lib/tests';
import { setupTest } from '@everestsystems/content-core/lib/tests';
import { Decimal } from '@everestsystems/decimal';
import { Notification } from '@pkg/everest.appserver/types/Notification';
import type { EvaluateMatchingPoliciesReturn } from '@pkg/everest.base.approvals/public/types';
import { GenericApprovalPolicy } from '@pkg/everest.base.approvals/public/types';
import { ApprovalAction } from '@pkg/everest.base.approvals/types/ApprovalAction';
import { ApprovalNotificationConfig } from '@pkg/everest.base.approvals/types/ApprovalNotificationConfig';
import type { ApprovalPolicyApprover } from '@pkg/everest.base.approvals/types/ApprovalPolicyApprover';
import { ApprovalPolicyExclusionLine } from '@pkg/everest.base.approvals/types/ApprovalPolicyExclusionLine';
import { ApprovalPolicyHeader } from '@pkg/everest.base.approvals/types/ApprovalPolicyHeader';
import { ApprovalPolicyLine } from '@pkg/everest.base.approvals/types/ApprovalPolicyLine';
import { EvstApproverType } from '@pkg/everest.base.approvals/types/enums/ApproverType';
import { BusinessPartner } from '@pkg/everest.fin.base/types/BusinessPartner';
import { EvstVendorBillStatus } from '@pkg/everest.fin.base/types/enums/VendorBillStatus';
import {
  address,
  employee,
  expenseApprover,
  person,
} from '@pkg/everest.fin.expense/public/testUtils/approvalPolicyMocks';
import {
  createRequiredTestingData,
  createTestEmployeeExpenseApprover,
  createVendorBill,
} from '@pkg/everest.fin.expense/public/testUtils/mocks';
import { vendorSample2 } from '@pkg/everest.fin.expense/public/testUtils/vendorMocks';
import { Vendor } from '@pkg/everest.fin.expense/types/Vendor';
import { VendorBillHeaderBase } from '@pkg/everest.fin.expense/types/VendorBillHeaderBase';
import { Employee } from '@pkg/everest.hr.base/types/Employee';
import { ExpenseApprover } from '@pkg/everest.hr.base/types/ExpenseApprover';

describe('integrationTests/completeApprovalPolicyFlow', () => {
  /**
   * This testsuite shall cover complete flow of Approval Policy for VB, such that the backend logic
   * can be verified on API level instead of the UI level.
   *
   * - Create a policy header with no nodes passed
   * - Create a policy header with no entity passed
   * - Create a policy line with no criteria passed
   * - Create a policy line with no operator passed
   * - Create a policy line with no requireAction passed
   * - Create a policy line with no Step passed
   * - Create a policy line with no value passed
   * - Create a policyLine with duplicate step number
   * - Create a policy and delete
   * - Create a policyHeader with a line and update it
   * - Create a policy line with no assosciated policyHeader
   * - Create an approval policy for ALL entities with 1 Policy Line, with criteria: Total >=1000 with 2 OR approvers, and create a VB with amount > 1000, and approve with 1 user
   * - Create an approval policy for Specific entity with 1 Policy Line also, Upon Creation with 2 AND approvers, then make VB and approve with one user
   * - Create an approval policy for Specific entity with 2 Policy Line also, Dept and Vendor with 2 OR approvers, then make VB and approve with one user
   * - Create policies and a VB that does not match any of the policies
   *
   */

  let session: ISession;
  let teardown: TestTeardown;
  let testEmployeeId: number;
  let testEmployeeNotApprovingId: number;

  let entityId: number;
  let categoryId: number;
  let accountId: number;
  let postingPeriodId: number;
  const vendorName = 'Test Vendor';
  const deptName = 'Test Dept';
  const path = `${VendorBillHeaderBase.MODEL_URN}.id`;

  beforeAll(async () => {
    ({ session, teardown } = await setupTest({
      withUser: 'test-user',
    }));

    await ApprovalPolicyHeader.deleteAll(session);
    await ApprovalPolicyLine.deleteAll(session);

    const employeeClient = await Employee.client(session);
    const bpClient = await BusinessPartner.client(session);
    const vendorClient = await Vendor.client(session);
    const ExpenseApproverClient = await ExpenseApprover.client(session);

    // Create Business Partner
    const businessPartner = await bpClient.create(
      {
        name: vendorName,
      },
      ['id']
    );
    expect(businessPartner).toBeDefined();
    // Generate required testing data
    const testingData = await createRequiredTestingData(session, [
      'entityId',
      'categoryId',
      'accountId',
    ]);

    categoryId = testingData.categoryId;
    entityId = testingData.entityId;
    accountId = testingData.accountId;
    postingPeriodId = testingData.postingPeriodId;

    expect(accountId).toBeGreaterThan(0);
    expect(categoryId).toBeGreaterThan(0);
    expect(postingPeriodId).toBeGreaterThan(0);
    expect(entityId).toBeGreaterThan(0);

    vendorSample2.vendorEntityId = entityId;

    // Create Vendor
    const vendor = await vendorClient.create(
      {
        ...vendorSample2,
        vendorName: vendorName,
        businessPartnerId: businessPartner.id,
      },
      ['id']
    );

    expect(vendor).toBeDefined();
    // vendorId = vendor.id;

    testEmployeeNotApprovingId = await createTestEmployeeExpenseApprover(
      session,
      address,
      {
        ...person,
        name: 'test-user-not-used',
        email: '<EMAIL>',
      },
      employee,
      expenseApprover
    );

    testEmployeeId = await createTestEmployeeExpenseApprover(
      session,
      address,
      { ...person, name: 'test-user', email: '<EMAIL>' },
      employee,
      expenseApprover
    );

    const emp = await employeeClient.loadEmployeeProfileFromId(testEmployeeId, [
      'id',
    ]);
    expect(emp).toBeDefined();

    const expApprover = await ExpenseApproverClient.query(
      { where: { employeeId: emp.id } },
      ['id']
    );
    expect(expApprover).toHaveLength(1);
  });

  afterAll(() => teardown());

  afterEach(async () => {
    await ApprovalPolicyHeader.deleteAll(session);
    await ApprovalPolicyLine.deleteAll(session);
    await ApprovalPolicyExclusionLine.deleteAll(session);
  });

  /*
   * Creates a policy for VendorBill with Total Amount >= 1000
   * Has 2 OR approvers
   * Verifies policy creation and deletion
   */
  it('Create a policy and delete', async () => {
    const approvalPolicyHeaderClient =
      await ApprovalPolicyHeader.client(session);

    const policyHeaderData = {
      nodes: 'VendorBill',
      startDate: null,
      endDate: null,
      isAllEntities: true,
      entityId: null,
    } as ApprovalPolicyHeader.ApprovalPolicyHeader;

    const policyLineData = [
      {
        criteria: 'Total Amount',
        operator: GenericApprovalPolicy.Operator.gte,
        requireAction: GenericApprovalPolicy.RequireAction.any,
        step: 1,
        value: ['1000'],
        valueType: GenericApprovalPolicy.ValueType.number,
      },
    ] as ApprovalPolicyLine.ApprovalPolicyLine[];

    const policyApprovers = [
      {
        approverType: EvstApproverType.Employee,
        approverId: testEmployeeId,
        approvalPolicyLineId: 0,
      },
      {
        approverType: EvstApproverType.Employee,
        approverId: testEmployeeNotApprovingId,
        approvalPolicyLineId: 1,
      },
    ] as ApprovalPolicyApprover.ApprovalPolicyApprover[];
    const policyExclusionLines = [];

    const response = await approvalPolicyHeaderClient.upsertApprovalPolicy(
      policyHeaderData,
      policyLineData,
      policyExclusionLines,
      policyApprovers
    );

    expect(response).toBeDefined();
    expect(response.id).toBeDefined();
    const createdPolicyId = response.id;

    // Fetch the created policy to ensure it exists
    const fetchedPolicy = await ApprovalPolicyHeader.read(
      session,
      { id: createdPolicyId },
      ['id', 'nodes', 'isAllEntities']
    );
    expect(fetchedPolicy).toBeDefined();
    expect(fetchedPolicy.nodes).toBe('VendorBill');
    expect(fetchedPolicy.isAllEntities).toBe(true);

    // Fetch the policy lines
    const policyLines = await ApprovalPolicyLine.query(
      session,
      { where: { policyHeaderId: createdPolicyId } },
      ['id', 'criteria', 'operator', 'value']
    );
    expect(policyLines).toHaveLength(1);
    expect(policyLines[0].criteria).toBe('Total Amount');
    expect(policyLines[0].operator).toBe(GenericApprovalPolicy.Operator.gte);
    expect(policyLines[0].value).toEqual(['1000']);

    // Delete the policy

    await approvalPolicyHeaderClient.deleteExpensePolicy([createdPolicyId]);

    // Verify the policy has been deleted
    await expect(
      ApprovalPolicyHeader.read(session, { id: createdPolicyId }, ['id'])
    ).resolves.toBeUndefined();
  });

  /*
   * Attempts to create a policy header without any policy lines
   * Expects an error to be thrown
   */
  it('Should throw an error trying to create a policy header with no associated policy lines', async () => {
    const approvalPolicyHeaderClient =
      await ApprovalPolicyHeader.client(session);

    const policyHeaderData = {
      nodes: 'VendorBill',
      startDate: null,
      endDate: null,
      isAllEntities: true,
      entityId: null,
    } as ApprovalPolicyHeader.ApprovalPolicyHeader;

    await expect(
      approvalPolicyHeaderClient.upsertApprovalPolicy(
        policyHeaderData,
        [],
        [],
        []
      )
    ).rejects.toThrow();

    // Verify that no policy was created
    const policies = await ApprovalPolicyHeader.query(
      session,
      { where: { nodes: 'VendorBill' } },
      ['id']
    );
    expect(policies).toHaveLength(0);
  });

  /*
   * Creates a policy for all entities
   * Policy: Total Amount > 0 with 2 OR approvers
   * Creates a VendorBill
   * Expects VendorBill to be approved by one user
   */
  it('should create an approval policy for ALL entities with 1 Policy Line, with criteria: Total >=1000 with 2 OR approvers, and create a VB with amount > 1000, and approve with 1 user', async () => {
    const vbhClient = await VendorBillHeaderBase.client(session);
    const approvalPolicyHeaderClient =
      await ApprovalPolicyHeader.client(session);

    // Create the policy
    const policyHeaderData = {
      nodes: 'VendorBill',
      startDate: null,
      endDate: null,
      isAllEntities: true,
      entityId: null,
    } as ApprovalPolicyHeader.ApprovalPolicyHeader;

    const policyLineData = [
      {
        criteria: 'Total Amount',
        operator: GenericApprovalPolicy.Operator.gt,
        requireAction: GenericApprovalPolicy.RequireAction.any,
        step: 1,
        value: ['0'],
        valueType: GenericApprovalPolicy.ValueType.number,
      },
    ] as ApprovalPolicyLine.ApprovalPolicyLine[];

    const policyApprovers = [
      {
        approverType: EvstApproverType.Employee,
        approverId: testEmployeeId,
        approvalPolicyLineId: 0,
      },
      {
        approverType: EvstApproverType.Employee,
        approverId: testEmployeeNotApprovingId,
        approvalPolicyLineId: 1,
      },
    ] as ApprovalPolicyApprover.ApprovalPolicyApprover[];
    const policyExclusionLines = [];

    const response = await approvalPolicyHeaderClient.upsertApprovalPolicy(
      policyHeaderData,
      policyLineData,
      policyExclusionLines,
      policyApprovers
    );

    expect(response).toBeDefined();
    expect(response.id).toBeDefined();

    // Create a vendor bill
    const createAccountAndDepartment = true;

    const vendorBillId = await createVendorBill(
      session,
      entityId,
      postingPeriodId,
      accountId,
      createAccountAndDepartment
    );

    const vendorBill = await vbhClient.query({ where: { id: vendorBillId } }, [
      'id',
    ]);
    expect(vendorBill[0]).toBeDefined();

    // Calculate matching policies
    const { matchingPolicyLines } =
      (await approvalPolicyHeaderClient.evaluateMatchingPolicies(
        'VendorBill',
        vendorBillId
      )) as EvaluateMatchingPoliciesReturn;
    expect(matchingPolicyLines).toHaveLength(1);

    // Approve the bill
    await approvalPolicyHeaderClient.approve({
      businessObjectType:
        GenericApprovalPolicy.ApprovalPolicyBusinessObject.VendorBill,
      businessObjectId: vendorBillId,
      path,
    } as unknown as ApprovalAction.ApprovalAction);

    // Check bill status after approval
    const vendorBillAfterApproval = await vbhClient.query(
      { where: { id: vendorBillId } },
      ['id', 'status']
    );
    expect(vendorBillAfterApproval[0]).toBeDefined();
    expect(vendorBillAfterApproval[0].status).toBe(
      EvstVendorBillStatus.Approved
    );

    // Check approval action
    const approvalActionAfterApproval = await ApprovalAction.query(
      session,
      {
        where: {
          businessObjectType: 'VendorBill',
          businessObjectId: vendorBillId,
          action: GenericApprovalPolicy.Action.approve,
          employeeId: testEmployeeId,
        },
      },
      ['id', 'employeeId']
    );
    expect(approvalActionAfterApproval[0]).toBeDefined();
  });

  /*
   * Creates two policies:
   *   1. All entities: Total Amount >= 1000 with 2 OR approvers
   *   2. Specific entity: Upon Creation with 2 AND approvers
   * Creates a VendorBill for the specific entity
   * Expects VendorBill to be approved by one user
   */
  it('should create an approval policy for Specific entity with 1 Policy Line also, Upon Creation with 2 AND approvers, then make VB and approve with one user', async () => {
    const vbhClient = await VendorBillHeaderBase.client(session);
    const approvalPolicyHeaderClient =
      await ApprovalPolicyHeader.client(session);

    // Create the first policy (ALL entities)
    const policyHeaderData1 = {
      nodes: 'VendorBill',
      startDate: null,
      endDate: null,
      isAllEntities: true,
      entityId: null,
    } as ApprovalPolicyHeader.ApprovalPolicyHeader;

    const policyLineData1 = [
      {
        criteria: 'Total Amount',
        operator: GenericApprovalPolicy.Operator.gte,
        requireAction: GenericApprovalPolicy.RequireAction.any,
        step: 1,
        value: ['1000'],
        valueType: GenericApprovalPolicy.ValueType.number,
      },
    ] as ApprovalPolicyLine.ApprovalPolicyLine[];

    const policyApprovers = [
      {
        approverType: EvstApproverType.Employee,
        approverId: testEmployeeId,
        approvalPolicyLineId: 0,
      },
      {
        approverType: EvstApproverType.Employee,
        approverId: testEmployeeNotApprovingId,
        approvalPolicyLineId: 0,
      },
    ] as ApprovalPolicyApprover.ApprovalPolicyApprover[];
    const approvalExclusionLines = [];

    const response1 = await approvalPolicyHeaderClient.upsertApprovalPolicy(
      policyHeaderData1,
      policyLineData1,
      approvalExclusionLines,
      policyApprovers
    );
    expect(response1).toBeDefined();
    expect(response1.id).toBeDefined();

    // Create the second policy (Specific entity)
    const policyHeaderData2 = {
      nodes: 'VendorBill',
      startDate: null,
      endDate: null,
      isAllEntities: false,
      entityId: entityId,
    } as ApprovalPolicyHeader.ApprovalPolicyHeader;

    const policyLineData2 = [
      {
        criteria: 'Upon Creation',
        operator: GenericApprovalPolicy.Operator.null,
        requireAction: GenericApprovalPolicy.RequireAction.all,
        step: 1,
        value: ['-'],
        valueType: GenericApprovalPolicy.ValueType.noValue,
      },
    ] as ApprovalPolicyLine.ApprovalPolicyLine[];

    const response2 = await approvalPolicyHeaderClient.upsertApprovalPolicy(
      policyHeaderData2,
      policyLineData2,
      approvalExclusionLines,
      policyApprovers
    );
    expect(response2).toBeDefined();
    expect(response2.id).toBeDefined();

    // Create a vendor bill
    const createAccountAndDepartment = true;
    const additionalInfo = {
      totalAmount: new Decimal(500),
      departmentName: deptName,
    };

    const vendorBillId = await createVendorBill(
      session,
      entityId,
      postingPeriodId,
      accountId,
      createAccountAndDepartment,
      additionalInfo
    );

    const vendorBill = await vbhClient.query({ where: { id: vendorBillId } }, [
      'id',
      'status',
    ]);
    expect(vendorBill[0]).toBeDefined();

    // Calculate matching policies
    const { matchingPolicyLines } =
      (await approvalPolicyHeaderClient.evaluateMatchingPolicies(
        'VendorBill',
        vendorBillId
      )) as EvaluateMatchingPoliciesReturn;
    expect(matchingPolicyLines).toHaveLength(1);

    // Approve the bill
    await approvalPolicyHeaderClient.approve({
      businessObjectType:
        GenericApprovalPolicy.ApprovalPolicyBusinessObject.VendorBill,
      businessObjectId: vendorBillId,
      path,
    } as unknown as ApprovalAction.ApprovalAction);

    // Check bill status after approval
    const vendorBillAfterApproval = await vbhClient.query(
      { where: { id: vendorBillId } },
      ['id', 'status']
    );
    expect(vendorBillAfterApproval[0]).toBeDefined();
    expect(vendorBillAfterApproval[0].status).toBe(
      EvstVendorBillStatus.Approved
    );

    // Check approval action
    const approvalActionAfterApproval = await ApprovalAction.query(
      session,
      {
        where: {
          businessObjectType: 'VendorBill',
          businessObjectId: vendorBillId,
          action: GenericApprovalPolicy.Action.approve,
          employeeId: testEmployeeId,
        },
      },
      ['id', 'employeeId']
    );
    expect(approvalActionAfterApproval[0]).toBeDefined();
  });

  /*
   * Creates two policies:
   *   1. All entities: Total Amount >= 1000 with 2 OR approvers
   *   2. Specific entity: Department and Vendor match with 2 OR approvers each
   * Creates a VendorBill matching the specific entity policy
   * Expects VendorBill to be approved by one user
   */
  it('should create an approval policy for Specific entity with 2 Policy Lines, Dept and Vendor with 2 OR approvers, then make VB and approve with one user', async () => {
    const vbhClient = await VendorBillHeaderBase.client(session);
    const approvalPolicyHeaderClient =
      await ApprovalPolicyHeader.client(session);

    // Create the first policy (ALL entities)
    const policyHeaderData1 = {
      nodes: 'VendorBill',
      startDate: null,
      endDate: null,
      isAllEntities: true,
      entityId: null,
    } as ApprovalPolicyHeader.ApprovalPolicyHeader;

    const policyLineData1 = [
      {
        criteria: 'Total Amount',
        operator: GenericApprovalPolicy.Operator.gte,
        requireAction: GenericApprovalPolicy.RequireAction.any,
        step: 1,
        value: ['1000'],
        valueType: GenericApprovalPolicy.ValueType.number,
      },
    ] as ApprovalPolicyLine.ApprovalPolicyLine[];

    const policyApprovers = [
      {
        approverType: EvstApproverType.Employee,
        approverId: testEmployeeId,
        approvalPolicyLineId: 0,
      },
      {
        approverType: EvstApproverType.Employee,
        approverId: testEmployeeNotApprovingId,
        approvalPolicyLineId: 0,
      },
    ] as ApprovalPolicyApprover.ApprovalPolicyApprover[];
    const policyExclusionLines = [];

    const response1 = await approvalPolicyHeaderClient.upsertApprovalPolicy(
      policyHeaderData1,
      policyLineData1,
      policyExclusionLines,
      policyApprovers
    );
    expect(response1).toBeDefined();
    expect(response1.id).toBeDefined();

    // Create the second policy (Specific entity)
    const policyHeaderData2 = {
      nodes: 'VendorBill',
      startDate: null,
      endDate: null,
      isAllEntities: false,
      entityId: entityId,
    } as ApprovalPolicyHeader.ApprovalPolicyHeader;

    const policyLineData2 = [
      {
        criteria: 'Department',
        operator: GenericApprovalPolicy.Operator.equal,
        requireAction: GenericApprovalPolicy.RequireAction.any,
        step: 1,
        value: [deptName],
        valueType: GenericApprovalPolicy.ValueType.multivalue,
      },
      {
        criteria: 'Vendor',
        operator: GenericApprovalPolicy.Operator.equal,
        requireAction: GenericApprovalPolicy.RequireAction.any,
        step: 2,
        value: [vendorName],
        valueType: GenericApprovalPolicy.ValueType.multivalue,
      },
    ] as ApprovalPolicyLine.ApprovalPolicyLine[];

    const policyApprovers1 = [
      {
        approverType: EvstApproverType.Employee,
        approverId: testEmployeeId,
        approvalPolicyLineId: 0,
      },
      {
        approverType: EvstApproverType.Employee,
        approverId: testEmployeeNotApprovingId,
        approvalPolicyLineId: 0,
      },
      {
        approverType: EvstApproverType.Employee,
        approverId: testEmployeeId,
        approvalPolicyLineId: 1,
      },
      {
        approverType: EvstApproverType.Employee,
        approverId: testEmployeeNotApprovingId,
        approvalPolicyLineId: 1,
      },
    ] as ApprovalPolicyApprover.ApprovalPolicyApprover[];

    const response2 = await approvalPolicyHeaderClient.upsertApprovalPolicy(
      policyHeaderData2,
      policyLineData2,
      policyExclusionLines,
      policyApprovers1
    );
    expect(response2).toBeDefined();
    expect(response2.id).toBeDefined();

    // Create a vendor bill
    const createAccountAndDepartment = true;
    const additionalInfo = {
      departmentName: deptName,
      totalAmount: new Decimal(500),
    };

    const vendorBillId = await createVendorBill(
      session,
      entityId,
      postingPeriodId,
      accountId,
      createAccountAndDepartment,
      additionalInfo
    );

    const vendorBill = await vbhClient.query({ where: { id: vendorBillId } }, [
      'id',
      'status',
    ]);
    expect(vendorBill[0]).toBeDefined();

    // Calculate matching policies
    const { matchingPolicyLines } =
      (await approvalPolicyHeaderClient.evaluateMatchingPolicies(
        'VendorBill',
        vendorBillId
      )) as EvaluateMatchingPoliciesReturn;
    expect(matchingPolicyLines).toHaveLength(1);

    // Approve the bill
    await approvalPolicyHeaderClient.approve({
      businessObjectType:
        GenericApprovalPolicy.ApprovalPolicyBusinessObject.VendorBill,
      businessObjectId: vendorBillId,
      path,
    } as unknown as ApprovalAction.ApprovalAction);

    // Check bill status after approval
    const vendorBillAfterApproval = await vbhClient.query(
      { where: { id: vendorBillId } },
      ['id', 'status']
    );
    expect(vendorBillAfterApproval[0]).toBeDefined();
    expect(vendorBillAfterApproval[0].status).toBe(
      EvstVendorBillStatus.Approved
    );

    // Check approval action
    const approvalActionAfterApproval = await ApprovalAction.query(
      session,
      {
        where: {
          businessObjectType: 'VendorBill',
          businessObjectId: vendorBillId,
          action: GenericApprovalPolicy.Action.approve,
          employeeId: testEmployeeId,
        },
      },
      ['id', 'employeeId']
    );
    expect(approvalActionAfterApproval[0]).toBeDefined();
  });

  /*
   * Creates two policies:
   *   1. All entities: Total Amount >= 1000 with 2 OR approvers
   *   2. Specific entity: Department and Vendor match with 2 AND approvers each
   * Creates a VendorBill that doesn't match any policy
   * Expects VendorBill to be auto-approved without creating an approval action
   */
  it('create policies and a VB that does not match any of the policies, it should be auto approve but not creating approvalAction', async () => {
    const vbhClient = await VendorBillHeaderBase.client(session);
    const approvalPolicyHeaderClient =
      await ApprovalPolicyHeader.client(session);

    // Create the first policy (ALL entities)
    const policyHeaderData1 = {
      nodes: 'VendorBill',
      startDate: null,
      endDate: null,
      isAllEntities: true,
      entityId: null,
    } as ApprovalPolicyHeader.ApprovalPolicyHeader;

    const policyLineData1 = [
      {
        criteria: 'Total Amount',
        operator: GenericApprovalPolicy.Operator.gte,
        requireAction: GenericApprovalPolicy.RequireAction.any,
        step: 1,
        value: ['1000'],
        valueType: GenericApprovalPolicy.ValueType.number,
      },
    ] as ApprovalPolicyLine.ApprovalPolicyLine[];

    const policyApprovers = [
      {
        approverType: EvstApproverType.Employee,
        approverId: testEmployeeId,
        approvalPolicyLineId: 0,
      },
      {
        approverType: EvstApproverType.Employee,
        approverId: testEmployeeNotApprovingId,
        approvalPolicyLineId: 0,
      },
    ] as ApprovalPolicyApprover.ApprovalPolicyApprover[];
    const policyExclusionLines = [];

    const response1 = await approvalPolicyHeaderClient.upsertApprovalPolicy(
      policyHeaderData1,
      policyLineData1,
      policyExclusionLines,
      policyApprovers
    );
    expect(response1).toBeDefined();
    expect(response1.id).toBeDefined();

    // Create the second policy (Specific entity)
    const policyHeaderData2 = {
      nodes: 'VendorBill',
      startDate: null,
      endDate: null,
      isAllEntities: false,
      entityId: entityId,
    } as ApprovalPolicyHeader.ApprovalPolicyHeader;

    const policyLineData2 = [
      {
        criteria: 'Department',
        operator: GenericApprovalPolicy.Operator.equal,
        requireAction: GenericApprovalPolicy.RequireAction.all,
        step: 1,
        value: [deptName],
        valueType: GenericApprovalPolicy.ValueType.multivalue,
      },
      {
        criteria: 'Vendor',
        operator: GenericApprovalPolicy.Operator.equal,
        requireAction: GenericApprovalPolicy.RequireAction.all,
        step: 2,
        value: [vendorName],
        valueType: GenericApprovalPolicy.ValueType.multivalue,
      },
    ] as ApprovalPolicyLine.ApprovalPolicyLine[];

    const policyApprovers2 = [
      {
        approverType: EvstApproverType.Employee,
        approverId: testEmployeeId,
        approvalPolicyLineId: 0,
      },
      {
        approverType: EvstApproverType.Employee,
        approverId: testEmployeeNotApprovingId,
        approvalPolicyLineId: 0,
      },
      {
        approverType: EvstApproverType.Employee,
        approverId: testEmployeeId,
        approvalPolicyLineId: 1,
      },
      {
        approverType: EvstApproverType.Employee,
        approverId: testEmployeeNotApprovingId,
        approvalPolicyLineId: 1,
      },
    ] as ApprovalPolicyApprover.ApprovalPolicyApprover[];

    const response2 = await approvalPolicyHeaderClient.upsertApprovalPolicy(
      policyHeaderData2,
      policyLineData2,
      policyExclusionLines,
      policyApprovers2
    );
    expect(response2).toBeDefined();
    expect(response2.id).toBeDefined();

    // Create a vendor bill that doesn't match any policy
    const newDeptName = 'new dept';
    const createAccountAndDepartment = true;
    const additionalInfo = {
      departmentName: newDeptName,
      totalAmount: new Decimal(100),
    };

    const vendorBillId = await createVendorBill(
      session,
      entityId,
      postingPeriodId,
      accountId,
      createAccountAndDepartment,
      additionalInfo
    );

    const vendorBill = await vbhClient.query({ where: { id: vendorBillId } }, [
      'id',
    ]);
    expect(vendorBill[0]).toBeDefined();

    // Calculate matching policies
    const { matchingPolicyLines } =
      (await approvalPolicyHeaderClient.evaluateMatchingPolicies(
        'VendorBill',
        vendorBillId
      )) as EvaluateMatchingPoliciesReturn;
    expect(matchingPolicyLines).toHaveLength(0);

    // Try to approve the bill
    await expect(
      approvalPolicyHeaderClient.approve({
        businessObjectType:
          GenericApprovalPolicy.ApprovalPolicyBusinessObject.VendorBill,
        businessObjectId: vendorBillId,
        path,
      } as unknown as ApprovalAction.ApprovalAction)
    ).resolves.toBeTruthy();

    // Check bill status after attempted approval
    const vendorBillAfterApproval = await vbhClient.query(
      { where: { id: vendorBillId } },
      ['id', 'status']
    );
    expect(vendorBillAfterApproval[0]).toBeDefined();
    expect(vendorBillAfterApproval[0].status).toBe(
      EvstVendorBillStatus.Approved
    );

    // Verify no approval action was created
    const approvalActionAfterApproval = await ApprovalAction.query(
      session,
      {
        where: {
          businessObjectType: 'VendorBill',
          businessObjectId: vendorBillId,
          action: GenericApprovalPolicy.Action.approve,
        },
      },
      ['id']
    );
    expect(approvalActionAfterApproval).toHaveLength(0);
  });

  /*
   * Creates one policy:
   *   1. Upon Creation with one approver
   * Creates a VendorBill for the specific entity
   * Approver should get native notification for review
   * Approver Approves bill
   * Submitter should get notification that his bill is approved now.
   */
  it('should create an approval policy with 1 Policy Line along with native notification enabled, Upon Creation with 1 approvers, then make VB and approve with one user', async () => {
    const vbhClient = await VendorBillHeaderBase.client(session);
    const approvalPolicyHeaderClient =
      await ApprovalPolicyHeader.client(session);

    // Create the first policy (ALL entities)
    const policyHeaderData1 = {
      nodes: 'VendorBill',
      startDate: null,
      endDate: null,
      isAllEntities: true,
      entityId: null,
    } as ApprovalPolicyHeader.ApprovalPolicyHeader;

    const policyLineData1 = [
      {
        criteria: 'Upon Creation',
        operator: GenericApprovalPolicy.Operator.null,
        requireAction: GenericApprovalPolicy.RequireAction.all,
        step: 1,
        value: ['-'],
        valueType: GenericApprovalPolicy.ValueType.noValue,
      },
    ] as ApprovalPolicyLine.ApprovalPolicyLine[];

    const policyApprovers = [
      {
        approverType: EvstApproverType.Employee,
        approverId: testEmployeeId,
        approvalPolicyLineId: 0,
      },
    ] as ApprovalPolicyApprover.ApprovalPolicyApprover[];
    const policyExclusionLines = [];

    const notificationConfig = {
      emailNotification: false,
      slackNotification: false,
      nativeNotification: true, // Enable native notification
    } as ApprovalNotificationConfig.ApprovalNotificationConfig;

    const response1 = await approvalPolicyHeaderClient.upsertApprovalPolicy(
      policyHeaderData1,
      policyLineData1,
      policyExclusionLines,
      policyApprovers,
      notificationConfig
    );

    expect(response1).toBeDefined();
    expect(response1.id).toBeDefined();

    const createdNotificationConfig = await ApprovalNotificationConfig.read(
      session,
      { approvalPolicyHeaderId: response1.id },
      ['nativeNotification', 'slackNotification', 'emailNotification']
    );

    expect(createdNotificationConfig.nativeNotification).toBe(true);
    expect(createdNotificationConfig.slackNotification).toBe(false);
    expect(createdNotificationConfig.emailNotification).toBe(false);

    // Create a vendor bill
    const createAccountAndDepartment = true;
    const additionalInfo = {
      totalAmount: new Decimal(1500),
      departmentName: deptName,
    };

    const vendorBillId = await createVendorBill(
      session,
      entityId,
      postingPeriodId,
      accountId,
      createAccountAndDepartment,
      additionalInfo
    );

    const vendorBill = await vbhClient.query({ where: { id: vendorBillId } }, [
      'id',
      'status',
      'vendorBillHeaderNumber',
    ]);
    expect(vendorBill[0]).toBeDefined();
    const documentNumber = vendorBill[0].vendorBillHeaderNumber;

    // Get translations for notification subjects
    const translations = await getTranslations(
      session,
      [
        'vendorMgmt.approval.review.message',
        'vendorMgmt.approval.success.message',
      ],
      'everest.fin.expense/vendorMgmt'
    );
    // Create expected notification subjects
    const reviewMessage = translations[0].replace(
      '$vendorBillHeaderNumber$',
      `(${documentNumber})`
    );
    const approveMessage = translations[1].replace(
      '$vendorBillHeaderNumber$',
      `(${documentNumber})`
    );

    // Calculate matching policies
    const { matchingPolicyLines } =
      (await approvalPolicyHeaderClient.evaluateMatchingPolicies(
        'VendorBill',
        vendorBillId
      )) as EvaluateMatchingPoliciesReturn;
    expect(matchingPolicyLines).toHaveLength(1);

    // Call this to trigger first notification for approver
    await approvalPolicyHeaderClient.approveIfNoMatchingPolicy({
      businessObjectType: 'VendorBill',
      businessObjectId: vendorBillId,
      path,
    } as unknown as ApprovalAction.ApprovalAction);

    // Approve the bill, this will trigger second notification for submitter
    await approvalPolicyHeaderClient.approve({
      businessObjectType:
        GenericApprovalPolicy.ApprovalPolicyBusinessObject.VendorBill,
      businessObjectId: vendorBillId,
      path,
    } as unknown as ApprovalAction.ApprovalAction);

    const notifications = await Notification.query(
      session,
      { where: { notificationCategory: 'System Message' } },
      ['subject']
    );

    // Check appropriate native notifications are trigerred or not
    expect(notifications).toHaveLength(2);
    expect(notifications).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          subject: reviewMessage,
        }),
        expect.objectContaining({
          subject: approveMessage,
        }),
      ])
    );

    // Check bill status after approval
    const vendorBillAfterApproval = await vbhClient.query(
      { where: { id: vendorBillId } },
      ['id', 'status']
    );
    expect(vendorBillAfterApproval[0]).toBeDefined();
    expect(vendorBillAfterApproval[0].status).toBe(
      EvstVendorBillStatus.Approved
    );

    // Check approval action
    const approvalActionAfterApproval = await ApprovalAction.query(
      session,
      {
        where: {
          businessObjectType: 'VendorBill',
          businessObjectId: vendorBillId,
          action: GenericApprovalPolicy.Action.approve,
          employeeId: testEmployeeId,
        },
      },
      ['id', 'employeeId']
    );
    expect(approvalActionAfterApproval[0]).toBeDefined();
  });

  /*
   * Creates one policy:
   *   1. Upon Creation with one approver
   * Creates a VendorBill for the specific entity
   * Approver should get native notification for review
   * Approver Rejects bill
   * Submitter should get notification that his bill is rejected now.
   */
  it('should create an approval policy with 1 Policy Line along with native notification enabled, Upon Creation with 1 approvers, then make VB and reject with one user', async () => {
    const vbhClient = await VendorBillHeaderBase.client(session);
    const approvalPolicyHeaderClient =
      await ApprovalPolicyHeader.client(session);

    // Clear past notifications
    await Notification.deleteAll(session);

    // Create the first policy (ALL entities)
    const policyHeaderData1 = {
      nodes: 'VendorBill',
      startDate: null,
      endDate: null,
      isAllEntities: true,
      entityId: null,
    } as ApprovalPolicyHeader.ApprovalPolicyHeader;

    const policyLineData1 = [
      {
        criteria: 'Upon Creation',
        operator: GenericApprovalPolicy.Operator.null,
        requireAction: GenericApprovalPolicy.RequireAction.all,
        step: 1,
        value: ['-'],
        valueType: GenericApprovalPolicy.ValueType.noValue,
      },
    ] as ApprovalPolicyLine.ApprovalPolicyLine[];

    const policyApprovers = [
      {
        approverType: EvstApproverType.Employee,
        approverId: testEmployeeId,
        approvalPolicyLineId: 0,
      },
    ] as ApprovalPolicyApprover.ApprovalPolicyApprover[];
    const policyExclusionLines = [];

    const notificationConfig = {
      emailNotification: false,
      slackNotification: false,
      nativeNotification: true, // Enable native notification
    } as ApprovalNotificationConfig.ApprovalNotificationConfig;

    const response1 = await approvalPolicyHeaderClient.upsertApprovalPolicy(
      policyHeaderData1,
      policyLineData1,
      policyExclusionLines,
      policyApprovers,
      notificationConfig
    );

    expect(response1).toBeDefined();
    expect(response1.id).toBeDefined();

    const createdNotificationConfig = await ApprovalNotificationConfig.read(
      session,
      { approvalPolicyHeaderId: response1.id },
      ['nativeNotification', 'slackNotification', 'emailNotification']
    );

    expect(createdNotificationConfig.nativeNotification).toBe(true);
    expect(createdNotificationConfig.slackNotification).toBe(false);
    expect(createdNotificationConfig.emailNotification).toBe(false);

    // Create a vendor bill
    const createAccountAndDepartment = true;
    const additionalInfo = {
      totalAmount: new Decimal(1500),
      departmentName: deptName,
    };

    const vendorBillId = await createVendorBill(
      session,
      entityId,
      postingPeriodId,
      accountId,
      createAccountAndDepartment,
      additionalInfo
    );

    const vendorBill = await vbhClient.query({ where: { id: vendorBillId } }, [
      'id',
      'status',
      'vendorBillHeaderNumber',
    ]);
    expect(vendorBill[0]).toBeDefined();
    const documentNumber = vendorBill[0].vendorBillHeaderNumber;

    // Get translations for notification subjects
    const translations = await getTranslations(
      session,
      [
        'vendorMgmt.approval.review.message',
        'vendorMgmt.approval.rejected.message',
      ],
      'everest.fin.expense/vendorMgmt'
    );

    // Create expected notification subjects
    const reviewMessage = translations[0].replace(
      '$vendorBillHeaderNumber$',
      `(${documentNumber})`
    );
    const rejectMessage = translations[1].replace(
      '$vendorBillHeaderNumber$',
      `(${documentNumber})`
    );

    // Calculate matching policies
    const { matchingPolicyLines } =
      (await approvalPolicyHeaderClient.evaluateMatchingPolicies(
        'VendorBill',
        vendorBillId
      )) as EvaluateMatchingPoliciesReturn;
    expect(matchingPolicyLines).toHaveLength(1);

    // Call this to trigger first notification for approver
    await approvalPolicyHeaderClient.approveIfNoMatchingPolicy({
      businessObjectType: 'VendorBill',
      businessObjectId: vendorBillId,
      path,
    } as unknown as ApprovalAction.ApprovalAction);

    // Reject the bill, this will trigger second notification for submitter
    await approvalPolicyHeaderClient.reject({
      businessObjectType:
        GenericApprovalPolicy.ApprovalPolicyBusinessObject.VendorBill,
      businessObjectId: vendorBillId,
      path,
    } as unknown as ApprovalAction.ApprovalAction);

    const notifications = await Notification.query(
      session,
      { where: { notificationCategory: 'System Message' } },
      ['subject']
    );

    // Check appropriate native notifications are trigerred or not
    // Check appropriate native notifications are trigerred or not
    expect(notifications).toHaveLength(2);
    expect(notifications).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          subject: reviewMessage,
        }),
        expect.objectContaining({
          subject: rejectMessage,
        }),
      ])
    );

    // Check bill status after rejection
    const vendorBillAfterApproval = await vbhClient.query(
      { where: { id: vendorBillId } },
      ['id', 'status']
    );
    expect(vendorBillAfterApproval[0]).toBeDefined();
    expect(vendorBillAfterApproval[0].status).toBe(
      EvstVendorBillStatus.Rejected
    );

    // Check approval action
    const approvalActionAfterApproval = await ApprovalAction.query(
      session,
      {
        where: {
          businessObjectType: 'VendorBill',
          businessObjectId: vendorBillId,
          action: GenericApprovalPolicy.Action.reject,
          employeeId: testEmployeeId,
        },
      },
      ['id', 'employeeId']
    );
    expect(approvalActionAfterApproval[0]).toBeDefined();
  });

  /*
   * Creates:
   * 1. Policy with amount > 0 and an exclusion for Department = IT
   * 2. Creates VB for IT dept
   * Expects VB to be auto-approved due to exclusion rule
   */
  it('should auto-approve when document matches exclusion criteria', async () => {
    const vbhClient = await VendorBillHeaderBase.client(session);
    const approvalPolicyHeaderClient =
      await ApprovalPolicyHeader.client(session);

    // Create policy with exclusion
    const policyHeaderData = {
      nodes: 'VendorBill',
      startDate: null,
      endDate: null,
      isAllEntities: true,
      entityId: null,
    } as ApprovalPolicyHeader.ApprovalPolicyHeader;

    const policyLineData = [
      {
        criteria: 'Total Amount',
        operator: GenericApprovalPolicy.Operator.gt,
        requireAction: GenericApprovalPolicy.RequireAction.any,
        step: 1,
        value: ['0'],
        valueType: GenericApprovalPolicy.ValueType.number,
      },
    ] as ApprovalPolicyLine.ApprovalPolicyLine[];

    const policyExclusionLines = [
      {
        criteria: 'Department',
        operator: GenericApprovalPolicy.Operator.equal,
        value: ['IT'],
        valueType: GenericApprovalPolicy.ValueType.value,
      },
    ] as ApprovalPolicyExclusionLine.ApprovalPolicyExclusionLine[];

    const policyApprovers = [
      {
        approverType: EvstApproverType.Employee,
        approverId: testEmployeeId,
        approvalPolicyLineId: 0,
      },
    ] as ApprovalPolicyApprover.ApprovalPolicyApprover[];

    const response = await approvalPolicyHeaderClient.upsertApprovalPolicy(
      policyHeaderData,
      policyLineData,
      policyExclusionLines,
      policyApprovers
    );

    expect(response).toBeDefined();
    expect(response.id).toBeDefined();

    // Create vendor bill for IT department
    const additionalInfo = {
      departmentName: 'IT',
      totalAmount: new Decimal(1500),
    };

    const vendorBillId = await createVendorBill(
      session,
      entityId,
      postingPeriodId,
      accountId,
      true,
      additionalInfo
    );

    const vendorBill = await vbhClient.query({ where: { id: vendorBillId } }, [
      'id',
      'status',
    ]);
    expect(vendorBill[0]).toBeDefined();

    // Check exclusion is matched
    const { matchingPolicyLines, matchingPolicyExclusionLines } =
      (await approvalPolicyHeaderClient.evaluateMatchingPolicies(
        'VendorBill',
        vendorBillId
      )) as EvaluateMatchingPoliciesReturn;

    expect(matchingPolicyLines).toHaveLength(1);
    expect(matchingPolicyExclusionLines).toHaveLength(1);

    // Try to approve - should be auto-approved
    await approvalPolicyHeaderClient.approve({
      businessObjectType:
        GenericApprovalPolicy.ApprovalPolicyBusinessObject.VendorBill,
      businessObjectId: vendorBillId,
      path,
    } as unknown as ApprovalAction.ApprovalAction);

    // Verify bill status
    const vendorBillAfter = await vbhClient.query(
      { where: { id: vendorBillId } },
      ['id', 'status']
    );
    expect(vendorBillAfter[0].status).toBe(EvstVendorBillStatus.Approved);

    // Verify no approval action created
    const actions = await ApprovalAction.query(
      session,
      {
        where: {
          businessObjectType: 'VendorBill',
          businessObjectId: vendorBillId,
        },
      },
      ['id']
    );
    expect(actions).toHaveLength(0);
  });

  /*
   * Creates:
   * 1. Policy with exclusion for multiple departments
   * Tests that exclusion works with multiple values
   */
  it('should handle multiple values in exclusion criteria', async () => {
    const vbhClient = await VendorBillHeaderBase.client(session);
    const approvalPolicyHeaderClient =
      await ApprovalPolicyHeader.client(session);

    const policyHeaderData = {
      nodes: 'VendorBill',
      startDate: null,
      endDate: null,
      isAllEntities: true,
      entityId: null,
    } as ApprovalPolicyHeader.ApprovalPolicyHeader;

    const policyLineData = [
      {
        criteria: 'Total Amount',
        operator: GenericApprovalPolicy.Operator.gt,
        requireAction: GenericApprovalPolicy.RequireAction.any,
        step: 1,
        value: ['0'],
        valueType: GenericApprovalPolicy.ValueType.number,
      },
    ] as ApprovalPolicyLine.ApprovalPolicyLine[];

    const policyExclusionLines = [
      {
        criteria: 'Department',
        operator: GenericApprovalPolicy.Operator.equal,
        value: ['IT', 'Finance'],
        valueType: GenericApprovalPolicy.ValueType.multivalue,
      },
    ] as ApprovalPolicyExclusionLine.ApprovalPolicyExclusionLine[];

    const policyApprovers = [
      {
        approverType: EvstApproverType.Employee,
        approverId: testEmployeeId,
        approvalPolicyLineId: 0,
      },
    ] as ApprovalPolicyApprover.ApprovalPolicyApprover[];

    await approvalPolicyHeaderClient.upsertApprovalPolicy(
      policyHeaderData,
      policyLineData,
      policyExclusionLines,
      policyApprovers
    );

    // Create vendor bills for both departments
    const itVendorBillId = await createVendorBill(
      session,
      entityId,
      postingPeriodId,
      accountId,
      true,
      {
        departmentName: 'IT',
        totalAmount: new Decimal(100),
      }
    );

    const financeVendorBillId = await createVendorBill(
      session,
      entityId,
      postingPeriodId,
      accountId,
      true,
      {
        departmentName: 'Finance',
        totalAmount: new Decimal(100),
      }
    );

    // Verify both match exclusion criteria and are auto-approved
    for (const billId of [itVendorBillId, financeVendorBillId]) {
      const { matchingPolicyExclusionLines } =
        (await approvalPolicyHeaderClient.evaluateMatchingPolicies(
          'VendorBill',
          billId
        )) as EvaluateMatchingPoliciesReturn;

      expect(matchingPolicyExclusionLines).toHaveLength(1);

      // Expect bill to be auto-approved when matching exclusion criteria
      const billAfter = await vbhClient.query({ where: { id: billId } }, [
        'status',
      ]);
      expect(billAfter[0].status).toBe(EvstVendorBillStatus.Approved);

      // Verify no approval actions were created since it was auto-approved
      const actions = await ApprovalAction.query(
        session,
        {
          where: {
            businessObjectType: 'VendorBill',
            businessObjectId: billId,
          },
        },
        ['id']
      );
      expect(actions).toHaveLength(0);
    }
  });
});
