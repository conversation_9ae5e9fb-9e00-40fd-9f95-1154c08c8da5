import type { ISession } from '@everestsystems/content-core';
import type { TestTeardown } from '@everestsystems/content-core/lib/tests';
import { setupTest } from '@everestsystems/content-core/lib/tests';
import { PlainDate } from '@everestsystems/datetime';
import { Decimal } from '@everestsystems/decimal';
import { Entity } from '@pkg/everest.base/types/Entity';
import { ExchangeRate } from '@pkg/everest.base/types/ExchangeRate';
import { getDefaultAccountingBook } from '@pkg/everest.fin.accounting/public/accountingBook/getDefaultAccountingBook';
import { toPlainDate } from '@pkg/everest.fin.accounting/public/utils/Date/date';
import { Account } from '@pkg/everest.fin.accounting/types/Account';
import { AccountingPeriod } from '@pkg/everest.fin.accounting/types/AccountingPeriod';
import { CurrencyRevaluation } from '@pkg/everest.fin.accounting/types/CurrencyRevaluation';
import type { EntityBook } from '@pkg/everest.fin.accounting/types/EntityBook';
import { JournalEntryHeader } from '@pkg/everest.fin.accounting/types/JournalEntryHeader';
import { JournalEntryLine } from '@pkg/everest.fin.accounting/types/JournalEntryLine';
import { BusinessPartner } from '@pkg/everest.fin.base/types/BusinessPartner';
import {
  getSourcePaymentDocumentPath,
  SourcePaymentDocumentType,
} from '@pkg/everest.fin.expense/public/outboundPayment';
import {
  createEmployee,
  createTestEntity,
} from '@pkg/everest.fin.expense/public/testUtils/mocks';
import { OutboundPaymentHeaderBase } from '@pkg/everest.fin.expense/types/OutboundPaymentHeaderBase';
import type { OutboundPaymentItemBase } from '@pkg/everest.fin.expense/types/OutboundPaymentItemBase';
import { Vendor } from '@pkg/everest.fin.expense/types/Vendor';
import { VendorBillHeaderBase } from '@pkg/everest.fin.expense/types/VendorBillHeaderBase';
import type { VendorBillItemBase } from '@pkg/everest.fin.expense/types/VendorBillItemBase';
import { Department } from '@pkg/everest.hr.base/types/Department';

describe('currencyRevaluation UAT contact R2R for failures and questions', () => {
  let session: ISession;
  let teardown: TestTeardown;
  let client: VendorBillHeaderBase.IControllerClient;

  let jeLineClient: JournalEntryLine.IControllerClient;
  let jeHeaderClient: JournalEntryHeader.IControllerClient;

  let crClient: CurrencyRevaluation.IControllerClient;
  let acctPeriodClient: AccountingPeriod.IControllerClient;
  let billdate: Date;
  let duedate: Date;
  let billdate2: Date;
  let duedate2: Date;
  let bp: Pick<BusinessPartner.BusinessPartner, 'id'>;
  let vendor: Pick<Vendor.Vendor, 'id'>;
  let entity;
  let accountingPeriod: { id: number };
  let accountingPeriod2: { id: number };
  let bookId: number;

  let department: Pick<Department.Department, 'id'>;
  let account: Pick<Account.Account, 'id'>;
  let accountsPayableAccount: Pick<Account.Account, 'id'>;
  let chaseCheckingAccount: Pick<Account.Account, 'id'>;
  let commerzbankAccount: Pick<Account.Account, 'id'>;

  let testEntityId: number;
  const revaluationDate = new PlainDate(2023, 11, 30);
  beforeAll(async () => {
    ({ session, teardown } = await setupTest({
      withUser: 'test-user',
    }));

    ({ id: bookId } = await getDefaultAccountingBook(session));
    const entityTest = await createTestEntity(session);
    testEntityId = entityTest.id;
    await createEmployee(
      session,
      testEntityId,
      session.serverEnvironment.currentUser.email
    );

    client = await VendorBillHeaderBase.client(session);

    jeLineClient = await JournalEntryLine.client(session);
    jeHeaderClient = await JournalEntryHeader.client(session);
    crClient = await CurrencyRevaluation.client(session);
    acctPeriodClient = await AccountingPeriod.client(session);

    jest
      .spyOn(ExchangeRate, 'query')
      .mockResolvedValueOnce([{ value: new Decimal(1.06) }])
      .mockResolvedValueOnce([{ value: new Decimal(0.78) }]);
    billdate = new Date('2023-10-01T00:00:00.000Z');
    duedate = new Date('2023-10-31T00:00:00.000Z');

    billdate2 = new Date('2020-04-01T00:00:00.000Z');
    duedate2 = new Date('2020-04-30T00:00:00.000Z');

    [bp] = await BusinessPartner.query(
      session,
      { where: { name: 'Audi' }, take: 1 },
      ['id']
    );
    [vendor] = await Vendor.query(
      session,
      { where: { vendorName: 'Audi' }, take: 1 },
      ['id']
    );
    [entity] = await Entity.query(
      session,
      { where: { entityName: 'Flow Inc.' }, take: 1 },
      ['id', 'EntityBook.chartOfAccountsId', 'EntityBook.id']
    );

    const chartOfAccountsId = entity['EntityBook'][0].chartOfAccountsId;
    accountingPeriod = (await acctPeriodClient.findAccountingPeriod(
      entity.id,
      toPlainDate(billdate),
      true,
      ['id']
    )) as { id: number };
    accountingPeriod2 = (await acctPeriodClient.findAccountingPeriod(
      entity.id,
      toPlainDate(billdate2),
      true,
      ['id']
    )) as { id: number };
    [department] = await Department.query(
      session,
      { where: { departmentName: 'Testing' } },
      ['id']
    );

    [
      account,
      accountsPayableAccount,
      chaseCheckingAccount,
      commerzbankAccount,
    ] = await Promise.all(
      ['114102', '211101', '111101', '111112'].map((accountNumber: string) =>
        Account.read(session, { accountNumber, chartOfAccountsId }, ['id'])
      )
    );
  });

  afterAll(() => teardown?.());
  beforeEach(() => {
    jest
      .spyOn(ExchangeRate, 'query')
      .mockResolvedValueOnce([{ value: new Decimal(1.06) }])
      .mockResolvedValueOnce([{ value: new Decimal(0.78) }])
      .mockResolvedValueOnce([{ value: new Decimal(1.06) }])
      .mockResolvedValueOnce([{ value: new Decimal(0.78) }])
      .mockResolvedValueOnce([{ value: new Decimal(1.06) }])
      .mockResolvedValueOnce([{ value: new Decimal(0.78) }])
      .mockResolvedValueOnce([{ value: new Decimal(1.06) }])
      .mockResolvedValueOnce([{ value: new Decimal(0.78) }]);
  });
  it('UAT-INT: Currency Revaluation', async () => {
    const vendorBillHeader = {
      vendorId: vendor.id,
      billNumber: '00001',
      entityId: entity.id,
      currency: 'EUR',
      billDate: PlainDate.from(billdate.toISOString()),
      postingPeriodId: accountingPeriod.id,
      paymentTerms: 'net30',
      dueDate: PlainDate.from(duedate.toISOString()),
    } as unknown as VendorBillHeaderBase.VendorBillHeaderBase;

    const vendorBillItems = [
      {
        departmentId: department.id,
        accountId: account.id,
        amount: { amount: new Decimal(1000) },
      },
    ] as VendorBillItemBase.VendorBillItemBase[];

    const bill1 = await client.upsertVendorBillV2(
      vendorBillHeader,
      vendorBillItems
    );
    await client.approveVendorBill(bill1.id, ['id']);

    // @UA-87-15 - Run Currency Revaluation and confirm unpaid foreign currency bills show up on report
    let result = (await jeLineClient.currencyRevaluation(
      entity.id,
      bookId,
      revaluationDate
    )) as { rowData: any[] };

    expect(
      result.rowData.map(
        (x) =>
          x.data?.['JournalEntryLine-JournalEntryHeader']
            ?.sourceFinancialDocumentId
      )
    ).toEqual(expect.arrayContaining([bill1.id]));

    // Partially pay selected bill

    const oubtoundPaymentHeaderExClient =
      await OutboundPaymentHeaderBase.client(session);
    const payment1 =
      await oubtoundPaymentHeaderExClient.upsertManualOutboundPayment(
        {
          paymentDate: PlainDate.from('2023-11-26'),
          accountId: account.id,
          entityId: entity.id,
          actualExchangeRate: new Decimal(1.079_75),
          systemExchangeRate: new Decimal(1.079_75),
          singlePayment: false,
        } as OutboundPaymentHeaderBase.OutboundPaymentHeaderBase,
        [
          {
            entityId: entity.id,
            paymentAmount: { amount: new Decimal(800) },
            description: 'N/A',
            sourcePaymentDocumentId: bill1.id,
            sourcePaymentDocumentPath: getSourcePaymentDocumentPath(
              SourcePaymentDocumentType.VendorBillHeaderBase
            ),
          } as OutboundPaymentItemBase.OutboundPaymentItemBase,
        ],
        SourcePaymentDocumentType.VendorBillHeaderBase,
        {
          fieldList: ['id', 'outboundPaymentHeaderNumber'],
        }
      );

    // @UA-87-17 - Run Currency Revaluation and confirm partially paid foreign currency bills and payments show up in the report detail
    result = (await jeLineClient.currencyRevaluation(
      entity.id,
      bookId,
      revaluationDate
    )) as { rowData: any[] };

    expect(
      result.rowData.map(
        (x) =>
          x.data?.['JournalEntryLine-JournalEntryHeader']
            ?.sourceFinancialDocumentId
      )
    ).toEqual(expect.arrayContaining([bill1.id]));

    expect(result.rowData).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          data: expect.objectContaining({
            transaction: payment1.outboundPaymentHeaderNumber,
          }),
        }),
      ])
    );

    // pay the rest
    const payment2 =
      await oubtoundPaymentHeaderExClient.upsertManualOutboundPayment(
        {
          paymentDate: PlainDate.from('2023-11-26'),
          accountId: account.id,
          entityId: entity.id,
          actualExchangeRate: new Decimal(1.079_75),
          systemExchangeRate: new Decimal(1.079_75),
          singlePayment: false,
        } as OutboundPaymentHeaderBase.OutboundPaymentHeaderBase,
        [
          {
            entityId: entity.id,
            paymentAmount: { amount: new Decimal(200) },
            description: 'N/A',
            sourcePaymentDocumentId: bill1.id,
            sourcePaymentDocumentPath: getSourcePaymentDocumentPath(
              SourcePaymentDocumentType.VendorBillHeaderBase
            ),
          } as OutboundPaymentItemBase.OutboundPaymentItemBase,
        ],
        SourcePaymentDocumentType.VendorBillHeaderBase,
        {
          fieldList: ['id', 'outboundPaymentHeaderNumber'],
        }
      );

    // @UA-87-16 - Run Currency Revaluation and confirm fully paid foreign currency bills and payments against those bills do not show up on the report
    result = (await jeLineClient.currencyRevaluation(
      entity.id,
      bookId,
      revaluationDate
    )) as { rowData: any[] };

    expect(
      result.rowData.map(
        (x) =>
          x.data?.['JournalEntryLine-JournalEntryHeader']
            ?.sourceFinancialDocumentId
      )
    ).not.toEqual(expect.arrayContaining([bill1.id]));

    expect(result.rowData).not.toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          data: expect.objectContaining({
            transaction: payment1.outboundPaymentHeaderNumber,
          }),
        }),
      ])
    );
    expect(result.rowData).not.toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          data: expect.objectContaining({
            transaction: payment2.outboundPaymentHeaderNumber,
          }),
        }),
      ])
    );

    // Post currency revaluation and fetch currency revaluation again
    await crClient.postCurrencyRevaluations(
      [
        {
          bookId,
          entityId: entity.id,
          revaluationDate,
        } as CurrencyRevaluation.CurrencyRevaluation,
      ],
      ['id']
    );

    result = (await jeLineClient.currencyRevaluation(
      entity.id,
      bookId,
      revaluationDate
    )) as { rowData: any[] };
    expect(
      result.rowData.map(
        (x) =>
          x.data?.['JournalEntryLine-JournalEntryHeader']
            ?.sourceFinancialDocumentId
      )
    ).not.toEqual(expect.arrayContaining([bill1.id]));
    expect(result.rowData).not.toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          data: expect.objectContaining({
            transaction: payment1.outboundPaymentHeaderNumber,
          }),
        }),
      ])
    );
    expect(result.rowData).not.toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          data: expect.objectContaining({
            transaction: payment2.outboundPaymentHeaderNumber,
          }),
        }),
      ])
    );
  });

  it('UAT-INT: Currency Revaluation journals', async () => {
    const foreignCurrJe = await jeHeaderClient.createManualJournalEntry(
      {
        journalEntryName: 'test2',
        description: '',
        isIntercompany: false,
        entityIds: [entity.id],
        transactionalCurrencies: ['EUR'],
        postingDate: toPlainDate('2023-09-01T00:00:00.000Z'),
      } as any,
      [
        {
          revaluation: true,
          elimination: false,
          accountId: accountsPayableAccount.id,
          transactionalCurrency: 'EUR',
          exchangeRate: new Decimal(1.5),
          debit: null,
          credit: { amount: new Decimal(1000) },
          entityId: entity.id,
          postingPeriod: 'Sep 2023',
          glDebit: null,
          glCredit: { amount: new Decimal(1500) },
          manualExchangeRate: false,
          entityName: 'Flow Inc.',
        } as any,
        {
          revaluation: false,
          elimination: false,
          transactionalCurrency: 'EUR',
          exchangeRate: new Decimal(1.5),
          accountId: chaseCheckingAccount.id,
          debit: { amount: new Decimal(500) },
          credit: null,
          entityId: entity.id,
          postingPeriod: 'Sep 2023',
          glDebit: { amount: new Decimal(750) },
          glCredit: null,
          manualExchangeRate: false,
          entityName: 'Flow Inc.',
        } as any,
        {
          revaluation: true,
          elimination: false,
          transactionalCurrency: 'EUR',
          exchangeRate: new Decimal(1.5),
          accountId: commerzbankAccount.id,
          debit: { amount: new Decimal(500) },
          credit: null,
          entityId: entity.id,
          postingPeriod: 'Sep 2023',
          glDebit: { amount: new Decimal(750) },
          glCredit: null,
          manualExchangeRate: false,
          entityName: 'Flow Inc.',
        } as any,
      ],
      []
    );

    const localCurreJe = await jeHeaderClient.createManualJournalEntry(
      {
        journalEntryName: 'test2',
        description: '',
        isIntercompany: false,
        entityIds: [entity.id],
        transactionalCurrencies: ['USD'],
        postingDate: toPlainDate('2023-09-01T00:00:00.000Z'),
      } as any,
      [
        {
          revaluation: true,
          elimination: false,
          accountId: accountsPayableAccount.id,
          transactionalCurrency: 'USD',
          exchangeRate: new Decimal(1),
          debit: { amount: new Decimal(1000) },
          credit: null,
          entityId: entity.id,
          postingPeriod: 'Sep 2023',
          glDebit: { amount: new Decimal(1000) },
          glCredit: null,
          manualExchangeRate: false,
          entityName: 'Flow Inc.',
        } as any,
        {
          revaluation: false,
          elimination: false,
          transactionalCurrency: 'USD',
          exchangeRate: new Decimal(1),
          accountId: chaseCheckingAccount.id,
          debit: null,
          credit: { amount: new Decimal(1000) },
          entityId: entity.id,
          postingPeriod: 'Sep 2023',
          glDebit: null,
          glCredit: { amount: new Decimal(1000) },
          manualExchangeRate: false,
          entityName: 'Flow Inc.',
        } as any,
      ],
      []
    );

    const resultBanks = (await jeLineClient.currencyRevaluationBankAndCards(
      entity.id,
      bookId,
      revaluationDate
    )) as { rowData: any };

    // @UA-87-14 - Add Foreign Currency bank for Flow Inc, load balance with JE, check if bank shows
    expect(resultBanks.rowData).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          account: 'Commerzbank',
          bankBalance: { amount: new Decimal(500), currency: 'EUR' },
          accountId: commerzbankAccount.id,
        }),
      ])
    );
    // @UA-87-4 - Run Currency Revaluation and confirm local currency banks (Chase Checking) do not show up for Flow Inc
    expect(
      resultBanks.rowData.filter((x) => x.accountId === chaseCheckingAccount.id)
    ).toHaveLength(0);
    const result = (await jeLineClient.currencyRevaluation(
      entity.id,
      bookId,
      revaluationDate
    )) as { rowData: any };

    // @UA-87-18 - Run Currency Revaluation and confirm local currency transactions do not show up on the report
    expect(
      result.rowData.filter((x) => x.journalEntryHeaderId === localCurreJe)
    ).toHaveLength(0);

    // @UA-87-11 - Create Foreign Currency Journal and verify it appears in Currency Revaluation Report
    const foreignCR = result.rowData.find(
      (x) => x.data.journalEntryHeaderId === foreignCurrJe
    );
    expect(foreignCR).not.toBeUndefined();
    expect(foreignCR.data).toEqual(
      expect.objectContaining({
        glBalance: { amount: new Decimal(-1500), currency: 'USD' },
        bankBalance: { amount: new Decimal(-1000), currency: 'EUR' },
      })
    );
  });

  it('should update currency revaluation and its reversal properly', async () => {
    const vendorBillHeader = {
      vendorId: vendor.id,
      billNumber: 'REV00001',
      entityId: entity.id,
      currency: 'EUR',
      billDate: PlainDate.from(billdate2.toISOString()),
      postingPeriodId: accountingPeriod2.id,
      paymentTerms: 'net30',
      dueDate: PlainDate.from(duedate2.toISOString()),
    } as unknown as VendorBillHeaderBase.VendorBillHeaderBase;
    const vendorBillItems = [
      {
        departmentId: department.id,
        accountId: account.id,
        amount: { amount: new Decimal(1000) },
      },
    ] as VendorBillItemBase.VendorBillItemBase[];
    const bill1 = await client.upsertVendorBillV2(
      vendorBillHeader,
      vendorBillItems
    );
    await client.approveVendorBill(bill1.id, ['id']);
    const [currencyRevaluation] = await crClient.postCurrencyRevaluations(
      [
        {
          bookId,
          entityId: entity.id,
          revaluationDate,
        } as CurrencyRevaluation.CurrencyRevaluation,
      ],
      ['id']
    );

    const [result] = await crClient.query(
      { where: { id: currencyRevaluation.id } },
      ['unrealizedJournalEntryId']
    );

    const [reversalJournalEntry] = await jeHeaderClient.query(
      { where: { sourceFinancialDocumentId: result.unrealizedJournalEntryId } },
      ['id']
    );

    let jeLines = await jeLineClient.query(
      { where: { journalEntryHeaderId: result.unrealizedJournalEntryId } },
      ['id', 'glDebit', 'glCredit']
    );
    let reversalJeLines = await jeLineClient.query(
      { where: { journalEntryHeaderId: reversalJournalEntry.id } },
      ['id', 'glDebit', 'glCredit']
    );
    // check if reversalJeLines is reversal of jeLines
    expect(jeLines).toHaveLength(reversalJeLines.length);
    for (const [i, jeLine] of jeLines.entries()) {
      expect(jeLine.glDebit).toEqual(reversalJeLines[i].glCredit);
      expect(jeLine.glCredit).toEqual(reversalJeLines[i].glDebit);
    }

    const vendorBillHeader2 = {
      vendorId: vendor.id,
      billNumber: 'REV00002',
      entityId: entity.id,
      currency: 'EUR',
      billDate: PlainDate.from(billdate2.toISOString()),
      postingPeriodId: accountingPeriod2.id,
      paymentTerms: 'net30',
      dueDate: PlainDate.from(duedate2.toISOString()),
    } as unknown as VendorBillHeaderBase.VendorBillHeaderBase;
    const vendorBillItems2 = [
      {
        departmentId: department.id,
        accountId: account.id,
        amount: { amount: new Decimal(2000) },
      },
    ] as VendorBillItemBase.VendorBillItemBase[];
    const bill2 = await client.upsertVendorBillV2(
      vendorBillHeader2,
      vendorBillItems2
    );
    await client.approveVendorBill(bill2.id, ['id']);
    await crClient.postCurrencyRevaluations(
      [
        {
          bookId,
          entityId: entity.id,
          revaluationDate,
        } as CurrencyRevaluation.CurrencyRevaluation,
      ],
      ['id']
    );

    jeLines = await jeLineClient.query(
      { where: { journalEntryHeaderId: result.unrealizedJournalEntryId } },
      ['id', 'glDebit', 'glCredit']
    );
    reversalJeLines = await jeLineClient.query(
      { where: { journalEntryHeaderId: reversalJournalEntry.id } },
      ['id', 'glDebit', 'glCredit']
    );
    // check if reversalJeLines is reversal of jeLines
    expect(jeLines).toHaveLength(reversalJeLines.length);
    for (const [i, jeLine] of jeLines.entries()) {
      expect(jeLine.glDebit).toEqual(reversalJeLines[i].glCredit);
      expect(jeLine.glCredit).toEqual(reversalJeLines[i].glDebit);
    }
  });
});
