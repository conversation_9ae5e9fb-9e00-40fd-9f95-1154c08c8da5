import type { ISession } from '@everestsystems/content-core';
import {
  BusinessError,
  DATA,
  getTranslation,
  METADATA,
  ModelUrn,
  parallel,
  TransportClass,
} from '@everestsystems/content-core';
import { EvstPermissionRoleType } from '@pkg/everest.appserver/types/enums/PermissionRoleType';
import { EverestPackageDependency } from '@pkg/everest.appserver/types/metadata/EverestPackageDependency';
import { EverestSeedData } from '@pkg/everest.appserver/types/metadata/EverestSeedData';
import { PermissionFlow } from '@pkg/everest.appserver/types/permission/PermissionFlow';
import { PermissionFlowAssignment } from '@pkg/everest.appserver/types/permission/PermissionFlowAssignment';
import { PermissionRole } from '@pkg/everest.appserver/types/permission/PermissionRole';
import { PermissionRoleAssignment } from '@pkg/everest.appserver/types/permission/PermissionRoleAssignment';
import { Policy } from '@pkg/everest.appserver/types/permission/Policy';
import { PolicyGroup } from '@pkg/everest.appserver/types/permission/PolicyGroup';
import { PolicyGroupPolicy } from '@pkg/everest.appserver/types/permission/PolicyGroupPolicy';
import { PermissionManagement } from '@pkg/everest.base.ui/types/PermissionManagement';
import type { roleDetailPresentation } from '@pkg/everest.base.ui/types/presentations/permission/role/detail/roleDetail';
import { FLOW_ASSIGNMENT_PATH } from '@pkg/everest.base/public/permissionAssignmentConstants';
import { EvstRowDiffState } from '@pkg/everest.base/types/enums/RowDiffState';
import { chunk, isEmpty, omit, set, uniq } from 'lodash';

import type { ExportResult } from '../../export/diffTypes';
import {
  APPSERVER_PACKAGES,
  RUNTIME_PACKAGE,
} from '../../uinext/permissionControllerBase';
import {
  loadAssignedPermissionCollectionsInternal,
  loadAssignedPermissionFlowsInternal,
  loadAssignedPermissionPoliciesInternal,
  loadPermissionRoleInternal,
} from '../helper/detailLoaderHelper';
import { basicDeterminePermissionActionExecution } from '../helper/permissionMetadataHelper';

type ReadyForExport = {
  toCreate: EverestSeedData.CreationFields[];
  toUpdate: EverestSeedData.CreationFields[];
  toDelete: string[];
};

const BASE_PACKAGE = 'everest.base' as const;
const ModelIdModelUUIDMap = new Map<string, string>([
  [PermissionRole.MODEL_ID, PermissionRole.MODEL_UUID],
  [PermissionRoleAssignment.MODEL_ID, PermissionRoleAssignment.MODEL_UUID],
  [PermissionFlow.MODEL_ID, PermissionFlow.MODEL_UUID],
  [PermissionFlowAssignment.MODEL_ID, PermissionFlowAssignment.MODEL_UUID],
  [PolicyGroup.MODEL_ID, PolicyGroup.MODEL_UUID],
  [PolicyGroupPolicy.MODEL_ID, PolicyGroupPolicy.MODEL_UUID],
  [Policy.MODEL_ID, Policy.MODEL_UUID],
]);

export class RoleDataSource
  implements roleDetailPresentation.dataSources.role.implementation
{
  private _role: roleDetailPresentation.dataSources.role.callbacks.query.output =
    {
      roleType: EvstPermissionRoleType.Default,
      Flows: [],
      Collections: [],
      Policies: [],
    };
  private _roleMetadata: roleDetailPresentation.dataSources.role.callbacks.query.queryMetadata =
    {};
  private _permissionIsTransportable: boolean = false;

  async setUp({
    session,
  }: roleDetailPresentation.dataSources.role.callbacks.setUp.input): Promise<roleDetailPresentation.dataSources.role.callbacks.setUp.output> {
    const policyNode = await session.modelMetadata.getNodeByUrn(
      ModelUrn.parse(Policy.MODEL_URN)
    );
    this._permissionIsTransportable =
      policyNode.transportClass !== TransportClass.None;
  }

  async query({
    session,
    mode,
    queryInstruction: { fields: roleFields, Flows, Collections, Policies },
    parameters: { uuid },
  }: roleDetailPresentation.dataSources.role.callbacks.query.input): Promise<roleDetailPresentation.dataSources.role.callbacks.query.combinedOutput> {
    // Return always cache in edit mode
    if (mode === 'edit' && this._role.id > 0) {
      this._roleMetadata ??= {
        active: {
          editable: true,
        },
        name: {
          editable: true,
        },
        description: {
          editable: true,
        },
      };
      return {
        [DATA]: this._role,
        [METADATA]: this._roleMetadata,
      };
    }

    // Load the role itself
    const { role, metadata } = await loadPermissionRoleInternal(
      { session, roleFields, uuid, mode },
      this._role,
      this._roleMetadata
    );
    this._role = role;
    this._roleMetadata = metadata;

    // Load assigned permission flows
    if (Flows?.fields !== undefined) {
      const { count, flows } = await loadAssignedPermissionFlowsInternal({
        session,
        uuid,
        queryInstructions: Flows,
      });
      this._role.flowCount = count;
      this._role.Flows = flows;
    }

    // Load assigned policy collections
    if (Collections?.fields !== undefined) {
      const { count, collections } =
        await loadAssignedPermissionCollectionsInternal({
          session,
          uuid,
          queryInstructions: Collections,
        });
      this._role.collectionCount = count;
      this._role.hasCollections = count > 0;
      this._role.Collections = collections;
    }

    // Load assigned policies
    if (Policies?.fields !== undefined) {
      const { count, policies } = await loadAssignedPermissionPoliciesInternal({
        session,
        uuid,
        queryInstructions: Policies,
      });
      this._role.policyCount = count;
      this._role.hasPolicies = count > 0;
      this._role.Policies = policies;
    }

    // Return loaded role and assignments with metadata
    return {
      [DATA]: this._role,
      [METADATA]: this._roleMetadata,
    };
  }

  async update({
    fieldName,
    newFieldValue,
  }: roleDetailPresentation.dataSources.role.callbacks.update.input): Promise<roleDetailPresentation.dataSources.role.callbacks.update.output> {
    switch (fieldName) {
      case 'name':
      case 'description':
      case 'active': {
        set(this._role, fieldName, newFieldValue);
        break;
      }
    }
  }

  async determine_assign?({
    session,
  }: roleDetailPresentation.dataSources.role.routines.assign.determineInput): Promise<roleDetailPresentation.dataSources.role.routines.assign.determineOutput> {
    return {
      visible: roleDeterminePermissionActionExecution({
        session,
        permission: this._role,
        allowForAppserver: true,
        allowForRuntime: true,
      }),
    };
  }
  execute_assign(
    _input: roleDetailPresentation.dataSources.role.routines.assign.executeInput
  ): Promise<roleDetailPresentation.dataSources.role.routines.assign.executeOutput> {
    // Wrapper, to handle button behavior (selection modal)
    return Promise.resolve();
  }

  async determine_assignFlows?({
    session,
  }: roleDetailPresentation.dataSources.role.routines.assignFlows.determineInput): Promise<roleDetailPresentation.dataSources.role.routines.assignFlows.determineOutput> {
    return {
      visible: roleDeterminePermissionActionExecution({
        session,
        permission: this._role,
        allowForAppserver: true,
        allowForRuntime: true,
      }),
    };
  }

  async execute_assignFlows({
    session,
    input: { selected },
  }: roleDetailPresentation.dataSources.role.routines.assignFlows.executeInput): Promise<roleDetailPresentation.dataSources.role.routines.assignFlows.executeOutput> {
    const compatiblePackages = await calculateAllowedPackages(
      session,
      this._role.package
    );

    const toCreate: PermissionRoleAssignment.CreationFields[] = selected.map(
      (flow) => ({
        permissionRoleUUID: this._role.uuid,
        relatedNodePath: FLOW_ASSIGNMENT_PATH,
        relatedNodeUUID: flow.uuid,
        package: mapAssignmentPackage(
          this._role.package,
          flow.package,
          compatiblePackages
        ),
      })
    );

    // Create all assignments
    await PermissionRoleAssignment.createMany(session, toCreate);
  }

  async determine_edit?({
    session,
  }: roleDetailPresentation.dataSources.role.routines.edit.determineInput): Promise<roleDetailPresentation.dataSources.role.routines.edit.determineOutput> {
    return {
      visible: roleDeterminePermissionActionExecution({
        session,
        permission: this._role,
        allowForAppserver: false,
        allowForRuntime: true,
      }),
    };
  }
  execute_edit(
    _input: roleDetailPresentation.dataSources.role.routines.edit.executeInput
  ): Promise<roleDetailPresentation.dataSources.role.routines.edit.executeOutput> {
    // Wrapper, to handle button behavior (switching to edit mode)
    return Promise.resolve();
  }

  async determine_exportToSeedData?({
    session,
  }: roleDetailPresentation.dataSources.role.routines.exportToSeedData.determineInput): Promise<roleDetailPresentation.dataSources.role.routines.exportToSeedData.determineOutput> {
    return {
      visible:
        !this._permissionIsTransportable &&
        roleDeterminePermissionActionExecution({
          session,
          permission: this._role,
          allowForAppserver: true,
          allowForRuntime: false,
        }),
    };
  }
  async execute_exportToSeedData({
    session,
  }: roleDetailPresentation.dataSources.role.routines.exportToSeedData.executeInput): Promise<roleDetailPresentation.dataSources.role.routines.exportToSeedData.executeOutput> {
    // Get involved packages
    const packagesToExport = uniq([
      this._role.package,
      ...this._role.Flows.map((f) => f.package),
    ]);

    const permissionClient = await PermissionManagement.client(session);
    const seedClient = await EverestSeedData.client(session);

    for (const packageName of packagesToExport) {
      if (APPSERVER_PACKAGES.has(packageName)) {
        // Appserver packages are not exported to seed data
        continue;
      }

      const permissionDiff = (await permissionClient.queryPackagePermissionDiff(
        packageName,
        false,
        true
      )) as ExportResult;

      const permissionsToExport = convertToSeedDataChanges(permissionDiff);

      if (!hasDataToExport(permissionsToExport)) {
        // No data to export
        continue;
      }

      // Delete
      await executeInBatch(permissionsToExport.toDelete, (toDelete) =>
        seedClient.deleteMany({ uuid: { $in: toDelete } })
      );

      // Create
      await executeInBatch(permissionsToExport.toCreate, (toCreate) =>
        seedClient.createMany(toCreate, ['uuid'])
      );

      // Update
      if (permissionsToExport.toUpdate.length > 0) {
        await parallel(permissionsToExport.toUpdate, async (toUpdate) =>
          seedClient.update({ uuid: toUpdate.uuid }, toUpdate, ['uuid'])
        );
      }
    }
  }

  async determine_move?(
    _input: roleDetailPresentation.dataSources.role.routines.move.determineInput
  ): Promise<roleDetailPresentation.dataSources.role.routines.move.determineOutput> {
    return {
      visible: false, // TODO: Implement move for roles
    };
    // return {
    //   visible: roleDeterminePermissionActionExecution({
    //     session,
    //     permission: this._role,
    //     allowForAppserver: true,
    //     allowForRuntime: false,
    //   }),
    // };
  }
  async validate_move?(
    _input: roleDetailPresentation.dataSources.role.routines.move.validateInput
  ): Promise<roleDetailPresentation.dataSources.role.routines.move.validateOutput> {
    return;
  }
  execute_move(
    _input: roleDetailPresentation.dataSources.role.routines.move.executeInput
  ): Promise<roleDetailPresentation.dataSources.role.routines.move.executeOutput> {
    // Wrapper, to handle button behavior (select the target package)
    return Promise.resolve();
  }

  async determine_moveFlowsToPackage?({
    session,
  }: roleDetailPresentation.dataSources.role.routines.moveFlowsToPackage.determineInput): Promise<roleDetailPresentation.dataSources.role.routines.moveFlowsToPackage.determineOutput> {
    return {
      visible: roleDeterminePermissionActionExecution({
        session,
        permission: this._role,
        allowForAppserver: true,
        allowForRuntime: false,
      }),
    };
  }
  async asyncvalidate_moveFlowsToPackage?({
    input: { selected },
  }: roleDetailPresentation.dataSources.role.routines.moveFlowsToPackage.validateInput): Promise<roleDetailPresentation.dataSources.role.routines.moveFlowsToPackage.validateOutput> {
    if (selected.length === 0) {
      return false;
    }

    // TODO: Check that all selected assignments can be moved to target package
  }
  execute_moveFlowsToPackage(
    _input: roleDetailPresentation.dataSources.role.routines.moveFlowsToPackage.executeInput
  ): Promise<roleDetailPresentation.dataSources.role.routines.moveFlowsToPackage.executeOutput> {
    // TODO: Implement the move (delete from old package and create in new package)
    throw new Error('Method not implemented.');
  }

  async determine_removeFlows?({
    session,
  }: roleDetailPresentation.dataSources.role.routines.removeFlows.determineInput): Promise<roleDetailPresentation.dataSources.role.routines.removeFlows.determineOutput> {
    return {
      visible: roleDeterminePermissionActionExecution({
        session,
        permission: this._role,
        allowForAppserver: true,
        allowForRuntime: true,
      }),
    };
  }
  async validate_removeFlows?({
    session,
    input: { selected },
  }: roleDetailPresentation.dataSources.role.routines.removeFlows.validateInput): Promise<roleDetailPresentation.dataSources.role.routines.removeFlows.validateOutput> {
    if (selected.length === 0) {
      return false;
    }

    // Removal of flows assigned in appserver is not allowed
    if (selected.some((flow) => APPSERVER_PACKAGES.has(flow.package))) {
      const message = await getTranslation(
        session,
        'permission.role.removeFlows.validation.appserverPackage',
        'flow'
      );
      return {
        passed: false,
        message,
      };
    }
  }
  async execute_removeFlows({
    session,
    input: { selected },
  }: roleDetailPresentation.dataSources.role.routines.removeFlows.executeInput): Promise<roleDetailPresentation.dataSources.role.routines.removeFlows.executeOutput> {
    await PermissionRoleAssignment.deleteMany(session, {
      uuid: { $in: selected.map((s) => s.uuid) },
    });
  }

  async determine_save?({
    session,
  }: roleDetailPresentation.dataSources.role.routines.save.determineInput): Promise<roleDetailPresentation.dataSources.role.routines.save.determineOutput> {
    return {
      visible: roleDeterminePermissionActionExecution({
        session,
        permission: this._role,
        allowForAppserver: false,
        allowForRuntime: true,
      }),
    };
  }
  async validate_save?({
    session,
    context,
  }: roleDetailPresentation.dataSources.role.routines.save.validateInput): Promise<roleDetailPresentation.dataSources.role.routines.save.validateOutput> {
    if (isEmpty(this._role.name)) {
      const message = await getTranslation(
        session,
        'genaral.validation.required.name',
        'policy'
      );
      context.addError([], message, 'name');
    }
  }
  async execute_save({
    session,
    mode,
  }: roleDetailPresentation.dataSources.role.routines.save.executeInput): Promise<roleDetailPresentation.dataSources.role.routines.save.executeOutput> {
    if (mode !== 'edit') {
      return;
    }

    const { name, description, active, uuid } = this._role;
    const roleChanges = {
      name,
      description,
      active,
    };
    await PermissionRole.update(session, { uuid }, roleChanges, ['uuid']);
  }
}

function roleDeterminePermissionActionExecution(input: {
  session: roleDetailPresentation.dataSources.role.callbacks.query.input['session'];
  permission?:
    | Pick<
        roleDetailPresentation.dataSources.role.callbacks.query.output,
        'isGenerated' | 'isDeprecated' | 'package' | 'roleType'
      >
    | null
    | undefined;
  allowForAppserver: boolean;
  allowForRuntime?: boolean;
}): boolean {
  const { session, permission, allowForAppserver, allowForRuntime } = input;
  return (
    permission?.roleType !== EvstPermissionRoleType.Autosync &&
    basicDeterminePermissionActionExecution({
      session,
      permission,
      allowForAppserver,
      allowForRuntime,
    })
  );
}

async function calculateAllowedPackages(
  session: ISession,
  packageName: string
): Promise<
  | { dependentOn: ReadonlySet<string>; dependencyOf: ReadonlySet<string> }
  | 'ALL'
> {
  // Runtime package can select all packages for assignment
  if (packageName === RUNTIME_PACKAGE) {
    return 'ALL';
  }

  // Appserver packages can select all packages for linking
  if (APPSERVER_PACKAGES.has(packageName)) {
    return 'ALL';
  }

  // All others
  // Assign: Current package or direct dependencies
  // Link: Direct dependants of the current package
  const filter = [
    {
      dependency: packageName, // given package is dependency of others
    },
    {
      package: packageName, // given package depends on others
    },
  ];
  const [dependants, dependencies] = await parallel(filter, async (where) =>
    EverestPackageDependency.query(
      session,
      {
        where,
      },
      ['package', 'dependency']
    )
  );

  return {
    dependentOn: new Set(dependencies.map((dep) => dep.dependency)),
    dependencyOf: new Set(dependants.map((dep) => dep.package)),
  };
}

function mapAssignmentPackage(
  rolePackage: string,
  flowPackage: string,
  compatiblePackages:
    | { dependentOn: ReadonlySet<string>; dependencyOf: ReadonlySet<string> }
    | 'ALL'
): string {
  if (rolePackage === RUNTIME_PACKAGE) {
    // Runtime role can only have runtime assignments
    return RUNTIME_PACKAGE;
  }

  if (APPSERVER_PACKAGES.has(rolePackage)) {
    // Appserver role only links, so all assignments belong to "flow" package (or base)
    return APPSERVER_PACKAGES.has(flowPackage) ? BASE_PACKAGE : flowPackage;
  }

  if (rolePackage === flowPackage) {
    // Same package => assignment
    return rolePackage;
  }

  if (compatiblePackages === 'ALL') {
    throw new BusinessError('Role should have a list of compatible packages');
  }

  if (compatiblePackages.dependentOn.has(flowPackage)) {
    // Flow package is a dependency of the role package => assignment
    return rolePackage;
  }

  if (compatiblePackages.dependencyOf.has(flowPackage)) {
    // Flow package is a dependency of the role package => link
    return flowPackage;
  }

  // Should never happen. Selection already filter out incompatible packages. Only possibly, if data were manipulated.
  throw new BusinessError(
    `Flow package ${flowPackage} is not compatible with role package ${rolePackage}`
  );
}

function hasDataToExport(data: ReadyForExport): boolean {
  return (
    data.toCreate.length > 0 ||
    data.toUpdate.length > 0 ||
    data.toDelete.length > 0
  );
}

function convertToSeedDataChanges(data: ExportResult): ReadyForExport {
  const toExport: ReadyForExport = {
    toCreate: [],
    toUpdate: [],
    toDelete: [],
  };

  for (const exportable of [
    ...data.roles,
    ...data.roleAssignments,
    ...data.flows,
    ...data.flowAssignments,
    ...data.collections,
    ...data.collectionAssignments,
    ...data.policies,
  ]) {
    const state = exportable.diffState;
    if (state === EvstRowDiffState.Unchanged) {
      continue; // Skip unchanged items
    }

    const cleanedExportable = omit(exportable, [
      'diffState',
      'shouldExport',
      'targetId',
      'displayValues',
      'lookup',
    ]) as EverestSeedData.CreationFields;
    if (!cleanedExportable.modelUUID) {
      cleanedExportable.modelUUID = ModelIdModelUUIDMap.get(
        cleanedExportable.modelId
      );
    }
    switch (state) {
      case EvstRowDiffState.Created: {
        toExport.toCreate.push(cleanedExportable);
        break;
      }
      case EvstRowDiffState.Changed: {
        toExport.toUpdate.push(cleanedExportable);
        break;
      }
      case EvstRowDiffState.Removed: {
        toExport.toDelete.push(cleanedExportable.uuid);
        break;
      }
    }
  }

  return toExport;
}

async function executeInBatch<TData, TOut>(
  data: TData[],
  func: (input: TData[]) => Promise<TOut[]>,
  batchSize = 200
): Promise<TOut[]> {
  // No data
  if (data.length === 0) {
    return [];
  }

  // Less than batch size
  if (data.length <= batchSize) {
    return func(data);
  }

  // Execute in batches
  const batches = chunk(data, batchSize);
  const batchResult = await parallel(batches, (input) => func(input));
  return batchResult.flat();
}
