// @i18n:policy
import { EvstPermissionRoleType } from '@pkg/everest.appserver/types/enums/PermissionRoleType';
import type { roleDetailPresentationUI } from '@pkg/everest.base.ui/types/presentations/permission/role/detail/roleDetail.ui';
import type { selectPresentationUI } from '@pkg/everest.base.ui/types/presentations/permission/selectPermissions/select.ui';
import { APPSERVER_PACKAGES } from '@pkg/everest.base/public/permissionAssignmentConstants';
import type { MessageSpecification } from '@pkg/everest.base/public/standard/ui/presentation/index';
import {
  getPresentationActionErrorMessage,
  withFeedback,
} from '@pkg/everest.base/public/standard/ui/presentation/index';
import {
  __showDependantsFor,
  __showDependenciesFor,
} from '../../dependency/dependencyHelper.ui';

type Context = roleDetailPresentationUI.context;

const tabTitle = {
  edit: '{{permissionRole.title.edit}}',
  view: '{{permissionRole.title}}',
};

// UI Text
export function getTabTitle(context: Context) {
  return tabTitle[_getMode(context)];
}
export async function getAssignedFlowsTitleWithCount(context: Context) {
  return `{{permissionRole.assignedFlows}} (${
    context.data?.role?.flowCount ?? 0
  })`;
}

export async function getAssignedCollectionTitleWithCount(context: Context) {
  return `{{permissionRole.assignedGroups}} (${
    context.data?.role?.collectionCount ?? 0
  })`;
}

export async function getAssignedPoliciesTitleWithCount(context: Context) {
  return `{{permissionFlow.assignedPolicies}} (${
    context.data?.role?.policyCount ?? 0
  })`;
}

export function isNotEditing({ data }: Context): boolean {
  return data['metadata<role>']?.['editing'] !== true;
}
export function isRowSelectionDisabled(
  { data }: Context,
  row?: { rowData?: { package?: string } }
): boolean {
  return (
    data['metadata<role>']?.['editing'] === true ||
    data.role?.roleType === EvstPermissionRoleType.Autosync ||
    (APPSERVER_PACKAGES.has(data.role?.package) &&
      APPSERVER_PACKAGES.has(row?.rowData?.package))
  );
}

export function openAssignFlowModal(context: Context) {
  const packageName = context.data?.role?.package;

  context.helpers.openModal({
    template: '/templates/everest.base.ui/permission/selectPermissions/select',
    size: 'medium',
    initialState: {
      mode: 'flows',
      packageName,
    },
    onModalSubmit: async (
      selected: selectPresentationUI.actions.select.output
    ) =>
      withFeedback(
        context,
        async () => context.presentationClient.assignFlows(selected),
        {
          onLoading: {
            notificationMessage: () =>
              '{{permissionRole.message.assigningFlows}}',
          },

          onSuccess: {
            notificationMessage: () => '{{permissionRole.message.assigned}}',
          },
          onError: {
            toast: (error: unknown): MessageSpecification => ({
              title: '{{permissionRole.error.assignFlows.title}}',
              message: getPresentationActionErrorMessage(error),
            }),
          },
        }
      ),
  });
}

export async function exportToSeedData(context: Context) {
  await withFeedback(
    context,
    async () => context.presentationClient.exportToSeedData(),
    {
      onLoading: {
        notificationMessage: () =>
          '{{permissionRole.message.exportingToSeedData}}',
      },

      onSuccess: {
        notificationMessage: () => '{{permissionRole.message.exportFinished}}',
      },
      onError: {
        toast: (error: unknown): MessageSpecification => ({
          title: '{{permissionRole.error.export.title}}',
          message: getPresentationActionErrorMessage(error),
        }),
      },
    }
  );
}

export function openExpertMode({ helpers, data }: Context) {
  helpers.navigate({
    to: '/templates/everest.base.ui/permission/role/detailExpert/roleDetail',
    queryParams: {
      mode: 'view',
      id: data.role?.uuid,
    },
  });
}

// Modals
export function showDependencies(context: Context) {
  if (!context.data.role) {
    return;
  }
  __showDependenciesFor(context, {
    uuid: context.data.role.uuid,
    kind: 'role',
  });
}

export function showDependents(context: Context) {
  if (!context.data.role) {
    return;
  }
  __showDependantsFor(context, {
    uuid: context.data.role.uuid,
    kind: 'role',
  });
}

function _getMode(context: Context): string {
  return context.state['param']?.['mode'];
}
