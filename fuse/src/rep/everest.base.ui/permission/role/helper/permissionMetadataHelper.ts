import type { ISession } from '@everestsystems/content-core';
import {
  APPSERVER_PACKAGES,
  RUNTIME_PACKAGE,
} from '@pkg/everest.base/public/permissionAssignmentConstants';
import { isOnMain } from '@pkg/everest.base/public/utils/presentation/sandboxUtils';

export function generatedDeprecatedVisible(
  permission?:
    | { isGenerated?: boolean; isDeprecated?: boolean }
    | null
    | undefined
): boolean {
  return permission?.isGenerated !== true && permission?.isDeprecated !== true;
}

export function basicDeterminePermissionActionExecution(input: {
  session: ISession;
  permission?:
    | { isGenerated?: boolean; isDeprecated?: boolean; package?: string }
    | null
    | undefined;
  allowForAppserver: boolean;
  allowForRuntime?: boolean;
}): boolean {
  const {
    session,
    permission,
    allowForAppserver = false,
    allowForRuntime = false,
  } = input;
  // Do not allow for generated or deprectated permissions
  if (!generatedDeprecatedVisible(permission)) {
    return false;
  }

  // Do not allow for appserver packages
  if (!allowForAppserver && APPSERVER_PACKAGES.has(permission?.package ?? '')) {
    return false;
  }

  // Allow for runtime permissions also be changed on MAIN
  if (allowForRuntime && RUNTIME_PACKAGE === permission?.package) {
    return true;
  }

  // Do not allow to change permissions on MAIN
  if (isOnMain(session)) {
    return false;
  }

  return true;
}
