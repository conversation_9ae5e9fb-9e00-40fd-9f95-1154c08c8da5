import type { Filter } from '@everestsystems/content-core';
import { BusinessError, IDENTIFIER } from '@everestsystems/content-core';
import { PermissionFlow } from '@pkg/everest.appserver/types/permission/PermissionFlow';
import { PermissionRole } from '@pkg/everest.appserver/types/permission/PermissionRole';
import { PermissionRoleAssignment } from '@pkg/everest.appserver/types/permission/PermissionRoleAssignment';
import { Policy } from '@pkg/everest.appserver/types/permission/Policy';
import { PolicyGroup } from '@pkg/everest.appserver/types/permission/PolicyGroup';
import type { roleDetailPresentation } from '@pkg/everest.base.ui/types/presentations/permission/role/detail/roleDetail';
import {
  COLLECTION_ASSIGNMENT_PATH,
  FLOW_ASSIGNMENT_PATH,
  POLICY_ASSIGNMENT_PATH,
} from '@pkg/everest.base/public/permissionAssignmentConstants';
import {
  combineFilters,
  filterAndMapFields,
  filterAndMapFilters,
  filterAndMapOrders,
} from '@pkg/everest.base/public/utils/presentation/queryInstructionUtils';
import { isEmpty, isNil, orderBy } from 'lodash';

type FlowFieldType =
  keyof roleDetailPresentation.dataSources.role.levels['Flows'];
type PossibleAssignmentsType =
  | roleDetailPresentation.dataSources.role.levels['Flows']
  | roleDetailPresentation.dataSources.role.levels['Collections']
  | roleDetailPresentation.dataSources.role.levels['Policies'];

type RoleQueryParameters = {
  session: roleDetailPresentation.dataSources.role.callbacks.query.input['session'];
  roleFields?: roleDetailPresentation.dataSources.role.callbacks.query.input['queryInstruction']['fields'];
  uuid?: roleDetailPresentation.dataSources.role.callbacks.query.input['parameters']['uuid'];
  mode: roleDetailPresentation.dataSources.role.callbacks.query.input['mode'];
};
type FlowAssignmentsQueryParameters = {
  session: roleDetailPresentation.dataSources.role.callbacks.query.input['session'];
  uuid: roleDetailPresentation.dataSources.role.callbacks.query.input['parameters']['uuid'];
  queryInstructions: roleDetailPresentation.dataSources.role.callbacks.query.input['queryInstruction']['Flows'];
};
type CollectionAssignmentsQueryParameters = {
  session: roleDetailPresentation.dataSources.role.callbacks.query.input['session'];
  uuid: roleDetailPresentation.dataSources.role.callbacks.query.input['parameters']['uuid'];
  queryInstructions: roleDetailPresentation.dataSources.role.callbacks.query.input['queryInstruction']['Collections'];
};
type PolicyAssignmentsQueryParameters = {
  session: roleDetailPresentation.dataSources.role.callbacks.query.input['session'];
  uuid: roleDetailPresentation.dataSources.role.callbacks.query.input['parameters']['uuid'];
  queryInstructions: roleDetailPresentation.dataSources.role.callbacks.query.input['queryInstruction']['Policies'];
};

const _roleFiledsToExclude: ReadonlySet<
  keyof roleDetailPresentation.dataSources.role.levels['']
> = new Set([
  'flowCount',
  'collectionCount',
  'policyCount',
  'isDeprecated',
  'isGenerated',
  'hasCollections',
  'hasPolicies',
]);
const _flowAssignmentFieldsToExclude: ReadonlySet<FlowFieldType> = new Set([
  'name',
  'description',
]);
const _assignmentFieldsToExclude: ReadonlySet<FlowFieldType> = new Set([
  'package',
  'relatedNodePath',
  'relatedNodeUUID',
]);
const _collectionAssignmentFieldsToExclude: ReadonlySet<
  keyof roleDetailPresentation.dataSources.role.levels['Collections']
> = new Set(['name', 'description', 'generatedFor']);
const _policyAssignmentFieldsToExclude: ReadonlySet<
  keyof roleDetailPresentation.dataSources.role.levels['Policies']
> = new Set([
  'name',
  'description',
  'actions',
  'notActions',
  'resources',
  'accessEffect',
  'calculatedActions',
]);

export async function loadPermissionRoleInternal(
  input: RoleQueryParameters,
  cachedRole?: roleDetailPresentation.dataSources.role.callbacks.query.output,
  cachedMetadata?: roleDetailPresentation.dataSources.role.callbacks.query.queryMetadata
): Promise<{
  role: roleDetailPresentation.dataSources.role.callbacks.query.output;
  metadata: roleDetailPresentation.dataSources.role.callbacks.query.queryMetadata;
}> {
  const { session, roleFields, uuid, mode } = input;

  if (isEmpty(uuid)) {
    throw new BusinessError('UUID is required to load a role');
  }

  if (!roleFields) {
    // Return cache,, if role loading is not requested
    return {
      role: cachedRole,
      metadata: cachedMetadata,
    };
  }

  const fieldList = filterAndMapFields(roleFields, _roleFiledsToExclude);

  const where: Filter<PermissionRole.PermissionRoleWithAssociation> = {
    uuid,
  };

  const roles = await PermissionRole.query(session, { where }, fieldList);

  if (roles.length !== 1) {
    throw new BusinessError('Role not found');
  }

  const { flowCount, collectionCount, policyCount } = cachedRole ?? {};
  const role = {
    ...roles[0],
    isDeprecated: !isNil(roles[0].deprecation?.deprecatedAt),
    isGenerated: !isNil(roles[0].generation?.modelUrn),
    hasCollections: collectionCount > 0,
    hasPolicies: policyCount > 0,
    flowCount,
    collectionCount,
    policyCount,
  };

  const editable = mode === 'edit';
  const metadata = editable
    ? {
        active: {
          editable,
        },
        name: {
          editable,
        },
        description: {
          editable,
        },
      }
    : undefined;

  return {
    role,
    metadata,
  };
}

export async function loadAssignedPermissionFlowsInternal(
  input: FlowAssignmentsQueryParameters
): Promise<{
  count: number;
  flows: roleDetailPresentation.dataSources.role.callbacks.query.output['Flows'];
}> {
  const {
    session,
    uuid,
    queryInstructions: { fields, filters, orders, page },
  } = input;

  const baseFilter = getBaseFilter(uuid, FLOW_ASSIGNMENT_PATH);
  const allAssignments = await getAssignments(
    session,
    baseFilter,
    fields,
    filters,
    orders,
    _flowAssignmentFieldsToExclude
  );

  if (allAssignments.length === 0) {
    return {
      count: 0,
      flows: [],
    };
  }

  // 2. Load all flow data
  const flowFieldList = filterAndMapFields(fields, _assignmentFieldsToExclude);
  const flowWhere = combineFilters(
    filterAndMapFilters(filters, _assignmentFieldsToExclude),
    {
      uuid: { $in: allAssignments.map((m) => m.relatedNodeUUID) },
    }
  );

  const flows = await PermissionFlow.query(
    session,
    {
      where: flowWhere as Record<string, unknown>,
    },
    flowFieldList
  );

  // 3. Map assignment data with flow data
  const mappedFlows: roleDetailPresentation.dataSources.role.callbacks.query.output['Flows'] =
    [];
  for (const assignment of allAssignments) {
    const flow = flows.find((f) => f.uuid === assignment.relatedNodeUUID);
    if (!flow) {
      continue;
    }

    mappedFlows.push({
      [IDENTIFIER]: assignment.id,
      id: assignment.id,
      uuid: assignment.uuid,
      package: assignment.package,
      relatedNodePath: assignment.relatedNodePath,
      relatedNodeUUID: assignment.relatedNodeUUID,
      name: flow.name,
      description: flow.description,
    });
  }

  return {
    count: await getCount(session, baseFilter),
    flows: getOrderedPaginated(mappedFlows, orders, page),
  };
}

export async function loadAssignedPermissionCollectionsInternal(
  input: CollectionAssignmentsQueryParameters
): Promise<{
  count: number;
  collections: roleDetailPresentation.dataSources.role.callbacks.query.output['Collections'];
}> {
  const {
    session,
    uuid,
    queryInstructions: { fields, filters, orders, page },
  } = input;
  const baseFilter = getBaseFilter(uuid, COLLECTION_ASSIGNMENT_PATH);
  const allAssignments = await getAssignments(
    session,
    baseFilter,
    fields,
    filters,
    orders,
    _collectionAssignmentFieldsToExclude
  );

  if (allAssignments.length === 0) {
    return {
      count: 0,
      collections: [],
    };
  }

  // 2. Load all collection data
  const collectionFieldList = filterAndMapFields(
    fields,
    _assignmentFieldsToExclude
  );
  const collectionWhere = combineFilters(
    filterAndMapFilters(filters, _assignmentFieldsToExclude),
    {
      uuid: { $in: allAssignments.map((m) => m.relatedNodeUUID) },
    }
  );

  const collections = await PolicyGroup.query(
    session,
    {
      where: collectionWhere as Record<string, unknown>,
    },
    collectionFieldList
  );

  // 3. Map assignment data with flow data
  const mappedCollections: roleDetailPresentation.dataSources.role.callbacks.query.output['Collections'] =
    [];
  for (const assignment of allAssignments) {
    const collection = collections.find(
      (f) => f.uuid === assignment.relatedNodeUUID
    );
    if (!collection) {
      continue;
    }

    mappedCollections.push({
      [IDENTIFIER]: assignment.id,
      id: assignment.id,
      uuid: assignment.uuid,
      package: assignment.package,
      relatedNodePath: assignment.relatedNodePath,
      relatedNodeUUID: assignment.relatedNodeUUID,
      name: collection.name,
      description: collection.description,
      generatedFor: collection.generatedFor,
    });
  }

  return {
    count: await getCount(session, baseFilter),
    collections: getOrderedPaginated(mappedCollections, orders, page),
  };
}

export async function loadAssignedPermissionPoliciesInternal(
  input: PolicyAssignmentsQueryParameters
): Promise<{
  count: number;
  policies: roleDetailPresentation.dataSources.role.callbacks.query.output['Policies'];
}> {
  const {
    session,
    uuid,
    queryInstructions: { fields, filters, orders, page },
  } = input;
  const baseFilter = getBaseFilter(uuid, POLICY_ASSIGNMENT_PATH);
  const allAssignments = await getAssignments(
    session,
    baseFilter,
    fields,
    filters,
    orders,
    _policyAssignmentFieldsToExclude
  );

  if (allAssignments.length === 0) {
    return {
      count: 0,
      policies: [],
    };
  }

  // 2. Load all policies data
  const fieldList = filterAndMapFields(fields, _assignmentFieldsToExclude);
  const where = combineFilters(
    filterAndMapFilters(filters, _assignmentFieldsToExclude),
    {
      uuid: { $in: allAssignments.map((m) => m.relatedNodeUUID) },
    }
  );

  const policies = await Policy.query(
    session,
    {
      where: where as Record<string, unknown>,
    },
    fieldList
  );

  // 3. Map assignment data with flow data
  const mappedPolicies: roleDetailPresentation.dataSources.role.callbacks.query.output['Policies'] =
    [];
  for (const assignment of allAssignments) {
    const policy = policies.find((f) => f.uuid === assignment.relatedNodeUUID);
    if (!policy) {
      continue;
    }

    mappedPolicies.push({
      [IDENTIFIER]: assignment.id,
      id: assignment.id,
      uuid: assignment.uuid,
      package: assignment.package,
      relatedNodePath: assignment.relatedNodePath,
      relatedNodeUUID: assignment.relatedNodeUUID,
      name: policy.name,
      description: policy.description,
      actions: policy.actions,
      notActions: policy.notActions,
      calculatedActions: policy.calculatedActions,
      resources: policy.resources,
      accessEffect: policy.accessEffect,
    });
  }

  return {
    count: await getCount(session, baseFilter),
    policies: getOrderedPaginated(mappedPolicies, orders, page),
  };
}

function getBaseFilter(
  permissionRoleUUID: string,
  relatedNodePath: string
): Filter<PermissionRoleAssignment.PermissionRoleAssignmentWithAssociation> {
  return {
    permissionRoleUUID,
    relatedNodePath,
  };
}

function getAssignments<T extends PossibleAssignmentsType>(
  session: roleDetailPresentation.dataSources.role.callbacks.query.input['session'],
  baseFilter: Filter<PermissionRoleAssignment.PermissionRoleAssignmentWithAssociation>,
  fields: ReadonlySet<Extract<keyof T, string>> | undefined,
  filters: Record<Extract<keyof T, string>, unknown> | undefined,
  orders:
    | ReadonlyArray<{
        field: Extract<keyof T, string>;
        ordering: 'asc' | 'desc';
      }>
    | undefined,
  excludedFields: ReadonlySet<Extract<keyof T, string>>
): Promise<
  PermissionRoleAssignment.ReadReturnType<Extract<keyof T, string>>[]
> {
  // TODO: Kernel currently do not support dyn. associations
  // in URN format for the same package, so we have workaround this
  // 1. Load all assignments
  const assignmentFieldList = filterAndMapFields(fields, excludedFields);
  const assignmentFilter = combineFilters(
    isEmpty(filters?.['package']) ? undefined : { package: filters['package'] },
    baseFilter
  );
  const assignmentOrder = filterAndMapOrders(orders, excludedFields);
  return PermissionRoleAssignment.query(
    session,
    {
      where: assignmentFilter as Record<string, unknown>,
      orderBy: assignmentOrder,
    },
    assignmentFieldList
  );
}

async function getCount(
  session: roleDetailPresentation.dataSources.role.callbacks.query.input['session'],
  baseFilter: Filter<PermissionRoleAssignment.PermissionRoleAssignmentWithAssociation>
): Promise<number> {
  const countResult = await PermissionRoleAssignment.query(
    session,
    {
      where: baseFilter,
      aggregations: [{ func: 'COUNT', field: 'uuid', alias: 'itemCount' }],
    },
    ['itemCount']
  );

  return countResult.length === 0 ? 0 : (countResult[0].itemCount as number);
}

function getOrderedPaginated<T>(
  data: T[],
  orders:
    | ReadonlyArray<{ field: keyof T; ordering: 'asc' | 'desc' }>
    | undefined,
  page: { take: number; skip: number } | undefined
): T[] {
  // Order and paginate in memory
  const ordered =
    (orders ?? []).length === 0
      ? data
      : orderBy(
          data,
          orders.map((m) => m.field),
          orders.map((m) => m.ordering)
        );
  return isEmpty(page)
    ? ordered
    : ordered.slice(page.skip, page.skip + page.take);
}
