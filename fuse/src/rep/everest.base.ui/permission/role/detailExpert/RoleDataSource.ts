import type { RoutineValidationResult } from '@everestsystems/content-core';
import { DATA, getTranslation, METADATA } from '@everestsystems/content-core';
import { EvstPermissionRoleType } from '@pkg/everest.appserver/types/enums/PermissionRoleType';
import { PermissionRole } from '@pkg/everest.appserver/types/permission/PermissionRole';
import { PermissionRoleAssignment } from '@pkg/everest.appserver/types/permission/PermissionRoleAssignment';
import type { roleDetailPresentation } from '@pkg/everest.base.ui/types/presentations/permission/role/detailExpert/roleDetail';
import {
  APPSERVER_PACKAGES,
  RUNTIME_PACKAGE,
} from '@pkg/everest.base/public/permissionAssignmentConstants';
import { isOnMain } from '@pkg/everest.base/public/utils/presentation/sandboxUtils';
import { EvstPermissionAssignmentType } from '@pkg/everest.base/types/enums/PermissionAssignmentType';
import { isEmpty, set } from 'lodash';

import {
  loadAssignedPermissionCollectionsInternal,
  loadAssignedPermissionFlowsInternal,
  loadAssignedPermissionPoliciesInternal,
  loadPermissionRoleInternal,
} from '../helper/detailLoaderHelper';

type SessionType =
  roleDetailPresentation.dataSources.role.callbacks.query.input['session'];

export class RoleDataSource
  implements roleDetailPresentation.dataSources.role.implementation
{
  private _role: roleDetailPresentation.dataSources.role.callbacks.query.output =
    {
      roleType: EvstPermissionRoleType.Default,
      Flows: [],
      Collections: [],
      Policies: [],
    };
  private _roleMetadata: roleDetailPresentation.dataSources.role.callbacks.query.queryMetadata =
    {};

  async query({
    session,
    mode,
    parameters: { id: uuid },
    queryInstruction: { fields: roleFields, Flows, Collections, Policies },
  }: roleDetailPresentation.dataSources.role.callbacks.query.input): Promise<roleDetailPresentation.dataSources.role.callbacks.query.combinedOutput> {
    if (mode === 'edit' && this._role.id > 0) {
      this._roleMetadata ??= {
        active: {
          editable: true,
        },
        name: {
          editable: true,
        },
        description: {
          editable: true,
        },
      };
      return {
        [DATA]: this._role,
        [METADATA]: this._roleMetadata,
      };
    }

    // Load the role itself
    const { role, metadata } = await loadPermissionRoleInternal(
      { session, roleFields, uuid, mode },
      this._role,
      this._roleMetadata
    );
    this._role = role;
    this._roleMetadata = metadata;

    // Load assigned permission flows
    if (Flows?.fields !== undefined) {
      const flowResult = await loadAssignedPermissionFlowsInternal({
        session,
        uuid: this._role.uuid,
        queryInstructions: Flows,
      });
      this._role.flowCount = flowResult.count;
      this._role.Flows = flowResult.flows;
    }

    // Load assigned policy collections
    if (Collections?.fields !== undefined) {
      const collectionResult = await loadAssignedPermissionCollectionsInternal({
        session,
        uuid: this._role.uuid,
        queryInstructions: Collections,
      });
      this._role.collectionCount = collectionResult.count;
      this._role.Collections = collectionResult.collections;
    }

    // Load assigned policies
    if (Policies?.fields !== undefined) {
      const policyResult = await loadAssignedPermissionPoliciesInternal({
        session,
        uuid: this._role.uuid,
        queryInstructions: Policies,
      });
      this._role.policyCount = policyResult.count;
      this._role.Policies = policyResult.policies;
    }

    return {
      [DATA]: this._role,
      [METADATA]: this._roleMetadata,
    };
  }

  async update({
    fieldName,
    newFieldValue,
  }: roleDetailPresentation.dataSources.role.callbacks.update.input): Promise<roleDetailPresentation.dataSources.role.callbacks.update.output> {
    switch (fieldName) {
      case 'name':
      case 'description':
      case 'active': {
        set(this._role, fieldName, newFieldValue);
        break;
      }
    }
  }

  async determine_save?({
    session,
  }: roleDetailPresentation.dataSources.role.routines.edit.determineInput): Promise<roleDetailPresentation.dataSources.role.routines.edit.determineOutput> {
    return { visible: determineBasicEditing(session, this._role) };
  }
  async validate_save?({
    session,
    context,
  }: roleDetailPresentation.dataSources.role.routines.save.validateInput): Promise<roleDetailPresentation.dataSources.role.routines.save.validateOutput> {
    if (isEmpty(this._role.name)) {
      const message = await getTranslation(
        session,
        'genaral.validation.required.name',
        'everest.base.ui/policy'
      );
      context.addError([], message, 'name');
    }

    if (isEmpty(this._role.package)) {
      const message = await getTranslation(
        session,
        'genaral.validation.required.package',
        'everest.base.ui/policy'
      );
      context.addError([], message, 'package');
    }
  }
  async execute_save({
    session,
    mode,
  }: roleDetailPresentation.dataSources.role.routines.save.executeInput): Promise<roleDetailPresentation.dataSources.role.routines.save.executeOutput> {
    if (mode === 'edit') {
      const { name, description, active, uuid } = this._role;
      const roleChanges = {
        name,
        description,
        active,
      };
      await PermissionRole.update(session, { uuid }, roleChanges, ['uuid']);
    }
  }

  async determine_edit?({
    session,
  }: roleDetailPresentation.dataSources.role.routines.edit.determineInput): Promise<roleDetailPresentation.dataSources.role.routines.edit.determineOutput> {
    return { visible: determineBasicEditing(session, this._role) };
  }
  execute_edit(
    _input: roleDetailPresentation.dataSources.role.routines.edit.executeInput
  ): Promise<roleDetailPresentation.dataSources.role.routines.edit.executeOutput> {
    return Promise.resolve();
  }

  async determine_assign?({
    session,
  }: roleDetailPresentation.dataSources.role.routines.assign.determineInput): Promise<roleDetailPresentation.dataSources.role.routines.assign.determineOutput> {
    return { visible: determineBasicEditing(session, this._role) };
  }
  execute_assign(
    _input: roleDetailPresentation.dataSources.role.routines.assign.executeInput
  ): Promise<roleDetailPresentation.dataSources.role.routines.assign.executeOutput> {
    return Promise.resolve();
  }

  async determine_attach?({
    session,
  }: roleDetailPresentation.dataSources.role.routines.assign.determineInput): Promise<roleDetailPresentation.dataSources.role.routines.assign.determineOutput> {
    return {
      visible: determineBasicEditing(session, this._role, true, false),
    };
  }
  execute_attach(
    _input: roleDetailPresentation.dataSources.role.routines.assign.executeInput
  ): Promise<roleDetailPresentation.dataSources.role.routines.assign.executeOutput> {
    return Promise.resolve();
  }

  /* FLOWS */
  async determine_assignFlows({
    session,
  }: roleDetailPresentation.dataSources.role.routines.assignFlows.determineInput): Promise<roleDetailPresentation.dataSources.role.routines.assignFlows.determineOutput> {
    return { visible: determineBasicEditing(session, this._role) };
  }
  validate_assignFlows({
    session,
    input: { permissionUUIDs },
  }: roleDetailPresentation.dataSources.role.routines.assignFlows.validateInput): Promise<roleDetailPresentation.dataSources.role.routines.assignFlows.validateOutput> {
    return validateAssignInternal(session, permissionUUIDs);
  }
  async execute_assignFlows({
    session,
    input: { permissionUUIDs },
  }: roleDetailPresentation.dataSources.role.routines.assignFlows.executeInput): Promise<roleDetailPresentation.dataSources.role.routines.assignFlows.executeOutput> {
    await this.executeAssignAttachInternal({
      session,
      permissionUUIDs,
      dynPath:
        EvstPermissionAssignmentType[
          'Urn:evst:everest:appserver:model/node:permission/PermissionFlow.uuid'
        ],
    });
  }

  async determine_attachFlows?({
    session,
  }: roleDetailPresentation.dataSources.role.routines.attachFlows.determineInput): Promise<roleDetailPresentation.dataSources.role.routines.attachFlows.determineOutput> {
    return { visible: determineBasicEditing(session, this._role, true, false) };
  }
  validate_attachFlows?({
    session,
    input,
  }: roleDetailPresentation.dataSources.role.routines.attachFlows.validateInput): Promise<roleDetailPresentation.dataSources.role.routines.attachFlows.validateOutput> {
    return validateAttachInternal(session, input, this._role.package);
  }
  async execute_attachFlows({
    session,
    input: { packageName, permissionUUIDs },
  }: roleDetailPresentation.dataSources.role.routines.attachFlows.executeInput): Promise<roleDetailPresentation.dataSources.role.routines.attachFlows.executeOutput> {
    await this.executeAssignAttachInternal({
      session,
      permissionUUIDs,
      packageName,
      dynPath:
        EvstPermissionAssignmentType[
          'Urn:evst:everest:appserver:model/node:permission/PermissionFlow.uuid'
        ],
    });
  }

  async determine_removeFlows?({
    session,
  }: roleDetailPresentation.dataSources.role.routines.removeFlows.determineInput): Promise<roleDetailPresentation.dataSources.role.routines.removeFlows.determineOutput> {
    return { visible: determineBasicEditing(session, this._role, true) };
  }
  validate_removeFlows?({
    session,
    input: { selected },
  }: roleDetailPresentation.dataSources.role.routines.removeFlows.validateInput): Promise<roleDetailPresentation.dataSources.role.routines.removeFlows.validateOutput> {
    return validateRemoveInternal(session, selected, this._role.package);
  }
  execute_removeFlows({
    session,
    input: { selected },
  }: roleDetailPresentation.dataSources.role.routines.removeFlows.executeInput): Promise<roleDetailPresentation.dataSources.role.routines.removeFlows.executeOutput> {
    return this.executeRemoveInternal(
      session,
      selected,
      EvstPermissionAssignmentType[
        'Urn:evst:everest:appserver:model/node:permission/PermissionFlow.uuid'
      ]
    );
  }

  /* COLLECTIONS */
  async determine_assignCollections?({
    session,
  }: roleDetailPresentation.dataSources.role.routines.assignCollections.determineInput): Promise<roleDetailPresentation.dataSources.role.routines.assignCollections.determineOutput> {
    return { visible: determineBasicEditing(session, this._role) };
  }
  validate_assignCollections?({
    session,
    input: { permissionUUIDs },
  }: roleDetailPresentation.dataSources.role.routines.assignCollections.validateInput): Promise<roleDetailPresentation.dataSources.role.routines.assignCollections.validateOutput> {
    return validateAssignInternal(session, permissionUUIDs);
  }
  async execute_assignCollections({
    session,
    input: { permissionUUIDs },
  }: roleDetailPresentation.dataSources.role.routines.assignCollections.executeInput): Promise<roleDetailPresentation.dataSources.role.routines.assignCollections.executeOutput> {
    await this.executeAssignAttachInternal({
      session,
      permissionUUIDs,
      dynPath:
        EvstPermissionAssignmentType[
          'Urn:evst:everest:appserver:model/node:permission/PolicyGroup.uuid'
        ],
    });
  }

  async determine_attachCollections?({
    session,
  }: roleDetailPresentation.dataSources.role.routines.attachCollections.determineInput): Promise<roleDetailPresentation.dataSources.role.routines.attachCollections.determineOutput> {
    return { visible: determineBasicEditing(session, this._role, true, false) };
  }
  validate_attachCollections?({
    session,
    input,
  }: roleDetailPresentation.dataSources.role.routines.attachCollections.validateInput): Promise<roleDetailPresentation.dataSources.role.routines.attachCollections.validateOutput> {
    return validateAttachInternal(session, input, this._role.package);
  }
  async execute_attachCollections({
    session,
    input: { packageName, permissionUUIDs },
  }: roleDetailPresentation.dataSources.role.routines.attachCollections.executeInput): Promise<roleDetailPresentation.dataSources.role.routines.attachCollections.executeOutput> {
    await this.executeAssignAttachInternal({
      session,
      permissionUUIDs,
      packageName,
      dynPath:
        EvstPermissionAssignmentType[
          'Urn:evst:everest:appserver:model/node:permission/PolicyGroup.uuid'
        ],
    });
  }

  async determine_removeCollections?({
    session,
  }: roleDetailPresentation.dataSources.role.routines.removeCollections.determineInput): Promise<roleDetailPresentation.dataSources.role.routines.removeCollections.determineOutput> {
    return { visible: determineBasicEditing(session, this._role, true) };
  }
  validate_removeCollections?({
    session,
    input: { selected },
  }: roleDetailPresentation.dataSources.role.routines.removeCollections.validateInput): Promise<roleDetailPresentation.dataSources.role.routines.removeCollections.validateOutput> {
    return validateRemoveInternal(session, selected, this._role.package);
  }
  execute_removeCollections({
    session,
    input: { selected },
  }: roleDetailPresentation.dataSources.role.routines.removeCollections.executeInput): Promise<roleDetailPresentation.dataSources.role.routines.removeCollections.executeOutput> {
    return this.executeRemoveInternal(
      session,
      selected,
      EvstPermissionAssignmentType[
        'Urn:evst:everest:appserver:model/node:permission/PolicyGroup.uuid'
      ]
    );
  }

  /* POLICIES */
  async determine_assignPolicies?({
    session,
  }: roleDetailPresentation.dataSources.role.routines.assignCollections.determineInput): Promise<roleDetailPresentation.dataSources.role.routines.assignCollections.determineOutput> {
    return { visible: determineBasicEditing(session, this._role) };
  }
  validate_assignPolicies?({
    session,
    input: { permissionUUIDs },
  }: roleDetailPresentation.dataSources.role.routines.assignCollections.validateInput): Promise<roleDetailPresentation.dataSources.role.routines.assignCollections.validateOutput> {
    return validateAssignInternal(session, permissionUUIDs);
  }
  async execute_assignPolicies({
    session,
    input: { permissionUUIDs },
  }: roleDetailPresentation.dataSources.role.routines.assignCollections.executeInput): Promise<roleDetailPresentation.dataSources.role.routines.assignCollections.executeOutput> {
    await this.executeAssignAttachInternal({
      session,
      permissionUUIDs,
      dynPath:
        EvstPermissionAssignmentType[
          'Urn:evst:everest:appserver:model/node:permission/Policy.uuid'
        ],
    });
  }

  async determine_attachPolicies?({
    session,
  }: roleDetailPresentation.dataSources.role.routines.attachCollections.determineInput): Promise<roleDetailPresentation.dataSources.role.routines.attachCollections.determineOutput> {
    return { visible: determineBasicEditing(session, this._role, true, false) };
  }
  validate_attachPolicies?({
    session,
    input,
  }: roleDetailPresentation.dataSources.role.routines.attachCollections.validateInput): Promise<roleDetailPresentation.dataSources.role.routines.attachCollections.validateOutput> {
    return validateAttachInternal(session, input, this._role.package);
  }
  async execute_attachPolicies({
    session,
    input: { packageName, permissionUUIDs },
  }: roleDetailPresentation.dataSources.role.routines.attachCollections.executeInput): Promise<roleDetailPresentation.dataSources.role.routines.attachCollections.executeOutput> {
    await this.executeAssignAttachInternal({
      session,
      permissionUUIDs,
      packageName,
      dynPath:
        EvstPermissionAssignmentType[
          'Urn:evst:everest:appserver:model/node:permission/Policy.uuid'
        ],
    });
  }

  async determine_removePolicies?({
    session,
  }: roleDetailPresentation.dataSources.role.routines.removeCollections.determineInput): Promise<roleDetailPresentation.dataSources.role.routines.removeCollections.determineOutput> {
    return { visible: determineBasicEditing(session, this._role, true) };
  }
  validate_removePolicies?({
    session,
    input: { selected },
  }: roleDetailPresentation.dataSources.role.routines.removeCollections.validateInput): Promise<roleDetailPresentation.dataSources.role.routines.removeCollections.validateOutput> {
    return validateRemoveInternal(session, selected, this._role.package);
  }
  execute_removePolicies({
    session,
    input: { selected },
  }: roleDetailPresentation.dataSources.role.routines.removeCollections.executeInput): Promise<roleDetailPresentation.dataSources.role.routines.removeCollections.executeOutput> {
    return this.executeRemoveInternal(
      session,
      selected,
      EvstPermissionAssignmentType[
        'Urn:evst:everest:appserver:model/node:permission/Policy.uuid'
      ]
    );
  }

  /* PRIVATE */
  private async executeAssignAttachInternal(input: {
    session: SessionType;
    permissionUUIDs: string[];
    dynPath: EvstPermissionAssignmentType;
    packageName?: string;
  }) {
    const {
      session,
      permissionUUIDs,
      packageName,
      dynPath: relatedNodePath,
    } = input;
    await PermissionRoleAssignment.createMany(
      session,
      permissionUUIDs.map((p) => ({
        package: packageName ?? this._role.package,
        permissionRoleUUID: this._role.uuid,
        relatedNodeUUID: p,
        relatedNodePath,
      })),
      ['uuid']
    );
  }

  private async executeRemoveInternal(
    session: SessionType,
    selected: { uuid: string }[],
    dynPath: EvstPermissionAssignmentType
  ): Promise<void> {
    await PermissionRoleAssignment.deleteMany(session, {
      permissionRoleUUID: this._role.uuid,
      uuid: { $in: selected.map((m) => m.uuid) },
      relatedNodePath: dynPath,
    });
  }
}

async function validateAssignInternal(
  session: SessionType,
  permissionUUIDs: string[]
): Promise<RoutineValidationResult> {
  if ((permissionUUIDs ?? []).length === 0) {
    const message = await getTranslation(
      session,
      'autoSync.validation.noPermissionsSelected',
      'everest.base.ui/policy'
    );
    return {
      passed: false,
      message,
    };
  }
}

async function validateAttachInternal(
  session: SessionType,
  selected: { packageName: string; permissionUUIDs: string[] },
  rolePackage: string
): Promise<RoutineValidationResult> {
  const { packageName, permissionUUIDs } = selected;
  if (isEmpty(packageName)) {
    const message = await getTranslation(
      session,
      'genaral.validation.required.package',
      'everest.base.ui/policy'
    );
    return {
      passed: false,
      message,
    };
  }

  if (packageName === rolePackage) {
    const message = await getTranslation(
      session,
      'general.validation.attach.samePackage',
      'everest.base.ui/policy'
    );
    return {
      passed: false,
      message,
    };
  }

  if ((permissionUUIDs ?? []).length === 0) {
    const message = await getTranslation(
      session,
      'autoSync.validation.noPermissionsSelected',
      'everest.base.ui/policy'
    );
    return {
      passed: false,
      message,
    };
  }
}

async function validateRemoveInternal(
  session: SessionType,
  selected: { package: string; uuid: string }[],
  rolePackage: string
): Promise<RoutineValidationResult> {
  if ((selected ?? []).length === 0) {
    const message = await getTranslation(
      session,
      'autoSync.validation.noPermissionsSelected',
      'everest.base.ui/policy'
    );
    return {
      passed: false,
      message,
    };
  }

  if (
    APPSERVER_PACKAGES.has(rolePackage) &&
    selected.some((s) => APPSERVER_PACKAGES.has(s.package))
  ) {
    const message = await getTranslation(
      session,
      'general.validation.appserver.remove.assigned',
      'everest.base.ui/policy'
    );
    return {
      passed: false,
      message,
    };
  }
}

function determineBasicEditing(
  session: SessionType,
  role: roleDetailPresentation.dataSources.role.callbacks.query.output,
  allowForAppserver = false,
  allowForRuntime = true
): boolean {
  if (isOnMain(session)) {
    return false;
  }

  if (!allowForAppserver && APPSERVER_PACKAGES.has(role?.package ?? '')) {
    return false;
  }

  if (role?.roleType === EvstPermissionRoleType.Autosync) {
    return false;
  }

  if (role.isGenerated === true) {
    return false;
  }

  if (!allowForRuntime && role?.package === RUNTIME_PACKAGE) {
    return false;
  }

  return true;
}
