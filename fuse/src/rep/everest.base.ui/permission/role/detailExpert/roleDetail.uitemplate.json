{"version": 3, "uicontroller": ["roleDetail.uicontroller.ts"], "uimodel": {"state": {"param": {"mode": null, "id": null}}, "presentation": {"urn": "urn:evst:everest:base/ui:presentation:permission/role/detailExpert/roleDetail", "parameters": {"mode": "@state:param.mode", "id": "@state:param.id"}}}, "uiview": {"i18n": ["policy"], "title": "@controller:getTabTitle()", "config": {"allowRefreshData": "@controller:isNotEditing()", "autoRefreshData": true}, "header": {"margin": 0, "globalAlerts": {"alerts": [{"key": "depre<PERSON><PERSON><PERSON><PERSON>", "variant": "warning", "visible": "@binding:role.isDeprecated", "title": "{{permissionRole.message.deprecationTitle}}", "content": "@binding:role.deprecation.message"}, {"key": "generationInfo", "variant": "info", "visible": "@binding:role.isGenerated", "title": "{{permissionRole.message.generationTitle}}", "content": "{{permissionRole.message.generationMessage}}"}]}, "content": {"title": "@binding:role.name", "description": "@binding:role.description"}, "placeholders": {"title": "{{policy.name}}", "description": "{{policy.description}}"}, "editing": {"title": "@binding:metadata<role>.editing", "description": "@binding:metadata<role>.editing"}}, "actions": {"content": [{"variant": "primary", "label": "{{policy.action.edit}}", "presentationAction": "@action:edit"}, {"variant": "primary", "label": "{{policy.action.save}}", "presentationAction": "@action:save"}, {"variant": "secondary", "label": "{{policy.action.cancel}}", "presentationAction": "@action:cancel"}, {"variant": "tertiary", "label": "{{policy.action.more}}", "visible": "@controller:isNotEditing()", "actions": [{"label": "{{policy.action.showDependencies}}", "onClick": "@controller:showDependencies"}, {"label": "{{policy.action.showDependents}}", "onClick": "@controller:showDependents"}]}]}, "sections": {"content": [{"component": "FieldGroup", "section": {"title": "{{general.title.basicData}}", "grid": {"size": "12"}, "editing": "@binding:metadata<role>.editing"}, "props": {"data": "@binding:role", "variant": "light", "fields": ["package", "active"]}}, {"component": "FieldGroup", "section": {"title": "{{general.title.generatedData}}", "grid": {"size": "12"}}, "props": {"data": "@binding:role", "variant": "light", "fields": ["roleType", "id", "uuid"]}}, {"component": "Segments", "section": {"grid": {"size": "12"}, "disabled": "@binding:metadata<role>.editing"}, "props": {"variant": "secondary", "segments": [{"title": "@controller:getAssignedFlowsTitleWithCount()", "sections": [{"component": "Table", "section": {"grid": {"size": "12"}, "actions": [{"label": "{{general.action.assign}}", "variant": "secondary", "presentationAction": "@action:assign", "onClick": "@controller:openAssignFlowModal"}, {"label": "{{general.action.attach}}", "variant": "secondary", "presentationAction": "@action:attach", "onClick": "@controller:openLinkFlowModal"}]}, "customId": "assignedFlowsTable", "props": {"presentationDataSet": "@dataSet:flows", "addRows": false, "rowNavigation": {"url": "/templates/everest.base.ui/permission/flow/detail/flowDetail?mode=view", "idProp": "relatedNodeUUID"}, "fixedFilters": ["name"], "columns": ["name", "package", "description"], "rowSelection": true, "deselectAllOnDataChange": true, "rowSelectionDisabled": "@controller:isRowSelectionDisabled", "bulkActions": {"actions": [{"label": "{{general.action.delete}}", "variant": "primary", "presentationAction": "@action:removeFlows", "confirmation": {"description": "{{permissionRole.confirmationMessage.deleteSelected}}", "confirmLabel": "{{general.action.delete}}"}}]}}}]}, {"title": "@controller:getAssignedCollectionTitleWithCount()", "sections": [{"component": "Table", "section": {"grid": {"size": "12"}, "actions": [{"label": "{{general.action.assign}}", "variant": "secondary", "presentationAction": "@action:assign", "onClick": "@controller:openAssignCollectionModal"}, {"label": "{{general.action.attach}}", "variant": "secondary", "presentationAction": "@action:attach", "onClick": "@controller:openLinkCollectionModal"}]}, "customId": "assignedCollectionsTable", "props": {"presentationDataSet": "@dataSet:collections", "addRows": false, "rowNavigation": {"url": "/templates/everest.base.ui/permission/collection/policyCollection?mode=view", "idProp": "relatedNodeUUID"}, "fixedFilters": ["name"], "columns": ["name", "package", "description"], "rowSelection": true, "deselectAllOnDataChange": true, "rowSelectionDisabled": "@controller:isRowSelectionDisabled", "bulkActions": {"actions": [{"label": "{{general.action.delete}}", "variant": "primary", "presentationAction": "@action:removeCollections", "confirmation": {"description": "{{permissionRole.confirmationMessage.deleteSelected}}", "confirmLabel": "{{general.action.delete}}"}}]}}}]}, {"title": "@controller:getAssignedPoliciesTitleWithCount()", "sections": [{"component": "Table", "section": {"grid": {"size": "12"}, "actions": [{"label": "{{general.action.assign}}", "variant": "secondary", "presentationAction": "@action:assign", "onClick": "@controller:openAssignPolicyModal"}, {"label": "{{general.action.attach}}", "variant": "secondary", "presentationAction": "@action:attach", "onClick": "@controller:openLinkPolicyModal"}]}, "customId": "assignedPoliciesTable", "props": {"data": "@binding:policies", "addRows": false, "rowNavigation": {"url": "/templates/everest.base.ui/permission/policy/policy?mode=view", "idProp": "relatedNodeUUID"}, "fixedFilters": ["name"], "columns": ["name", "package", "accessEffect", "resources", "calculatedActions"], "rowSelection": true, "deselectAllOnDataChange": true, "rowSelectionDisabled": "@controller:isRowSelectionDisabled", "bulkActions": {"actions": [{"label": "{{general.action.delete}}", "variant": "primary", "presentationAction": "@action:removePolicies", "confirmation": {"description": "{{permissionRole.confirmationMessage.deleteSelected}}", "confirmLabel": "{{general.action.delete}}"}}]}}}]}]}}]}}}