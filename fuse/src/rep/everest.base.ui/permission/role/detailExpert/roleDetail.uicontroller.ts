// @i18n:policy

import { EvstPermissionRoleType } from '@pkg/everest.appserver/types/enums/PermissionRoleType';
import {
  __showDependantsFor,
  __showDependenciesFor,
} from '@pkg/everest.base.ui/permission/dependency/dependencyHelper.ui';
import type {
  OpenPermissionSelectionConfigType,
  SelectPayload,
} from '@pkg/everest.base.ui/permission/multiSelectPermission/permissionSelectionHelper.uicontroller';
import { __openPermissionSelection } from '@pkg/everest.base.ui/permission/multiSelectPermission/permissionSelectionHelper.uicontroller';
import type { roleDetailPresentationUI } from '@pkg/everest.base.ui/types/presentations/permission/role/detailExpert/roleDetail.ui';
import { APPSERVER_PACKAGES } from '@pkg/everest.base/public/permissionAssignmentConstants';
import type { MessageSpecification } from '@pkg/everest.base/public/standard/ui/presentation/index';
import {
  getPresentationActionErrorMessage,
  withFeedback,
} from '@pkg/everest.base/public/standard/ui/presentation/index';

type Context = roleDetailPresentationUI.context;

// Constants
const tabTitle = {
  edit: '{{permissionRole.title.edit}}',
  view: '{{permissionRole.title}}',
};

// UI Text
export function getTabTitle(context: Context) {
  return tabTitle[_getMode(context)];
}
export async function getAssignedFlowsTitleWithCount(context: Context) {
  return `{{permissionRole.assignedFlows}} (${
    context.data?.role?.flowCount ?? 0
  })`;
}

export async function getAssignedCollectionTitleWithCount(context: Context) {
  return `{{permissionRole.assignedGroups}} (${
    context.data?.role?.collectionCount ?? 0
  })`;
}

export async function getAssignedPoliciesTitleWithCount(context: Context) {
  return `{{permissionFlow.assignedPolicies}} (${
    context.data?.role?.policyCount ?? 0
  })`;
}

export function isNotEditing({ data }: Context): boolean {
  return data['metadata<role>']?.['editing'] !== true;
}

export function isRowSelectionDisabled(
  { data }: Context,
  row?: { rowData?: { package?: string } }
): boolean {
  return (
    data['metadata<role>']?.['editing'] === true ||
    data.role?.roleType === EvstPermissionRoleType.Autosync ||
    (APPSERVER_PACKAGES.has(data.role?.package) &&
      APPSERVER_PACKAGES.has(row?.rowData?.package))
  );
}

// Modals
export function showDependencies(context: Context) {
  if (!context.data.role) {
    return;
  }
  __showDependenciesFor(context, {
    uuid: context.data.role.uuid,
    kind: 'role',
  });
}

export function showDependents(context: Context) {
  if (!context.data.role) {
    return;
  }
  __showDependantsFor(context, {
    uuid: context.data.role.uuid,
    kind: 'role',
  });
}

export function openAssignFlowModal(context: Context) {
  openPermissionSelectionModal(context, 'flow', false, async (selected) =>
    context.presentationClient.assignFlows({
      permissionUUIDs: selected.selectedPermissions.map((m) => m.uuid),
    })
  );
}

export function openLinkFlowModal(context: Context) {
  openPermissionSelectionModal(context, 'flow', true, async (selected) =>
    context.presentationClient.attachFlows({
      permissionUUIDs: selected.selectedPermissions.map((m) => m.uuid),
      packageName: selected.packageName,
    })
  );
}

export function openAssignCollectionModal(context: Context) {
  openPermissionSelectionModal(context, 'collection', false, async (selected) =>
    context.presentationClient.assignCollections({
      permissionUUIDs: selected.selectedPermissions.map((m) => m.uuid),
    })
  );
}

export function openLinkCollectionModal(context: Context) {
  openPermissionSelectionModal(context, 'collection', true, async (selected) =>
    context.presentationClient.attachCollections({
      permissionUUIDs: selected.selectedPermissions.map((m) => m.uuid),
      packageName: selected.packageName,
    })
  );
}

export function openAssignPolicyModal(context: Context) {
  openPermissionSelectionModal(context, 'policy', false, async (selected) =>
    context.presentationClient.assignPolicies({
      permissionUUIDs: selected.selectedPermissions.map((m) => m.uuid),
    })
  );
}

export function openLinkPolicyModal(context: Context) {
  openPermissionSelectionModal(context, 'policy', true, async (selected) =>
    context.presentationClient.attachPolicies({
      permissionUUIDs: selected.selectedPermissions.map((m) => m.uuid),
      packageName: selected.packageName,
    })
  );
}

function _getMode(context: Context): string {
  return context.state['param']?.['mode'];
}

function openPermissionSelectionModal(
  context: Context,
  target: 'flow' | 'collection' | 'policy',
  shouldLink: boolean,
  assignmentCallback: (selected: SelectPayload) => Promise<void>
) {
  const config: OpenPermissionSelectionConfigType = {
    target,
    addOrLink: shouldLink ? 'link' : 'add',
    packageName: context.data?.role?.package,
    checkExists: () => false,
    assignNew: async (selection) =>
      withFeedback(context, async () => assignmentCallback(selection), {
        onError: {
          toast: (error: unknown): MessageSpecification => ({
            title: `{{general.title.error}}`,
            message:
              getPresentationActionErrorMessage(error) ?? error?.['message'],
          }),
        },
      }),
  };
  __openPermissionSelection(context, config);
}
