import type {
  ISession,
  RoutineDeterminationResult,
  RoutineValidationContext,
  RoutineValidationResult,
} from '@everestsystems/content-core';
import { EvstPermissionRoleType } from '@pkg/everest.appserver/types/enums/PermissionRoleType';
import { PermissionRole } from '@pkg/everest.appserver/types/permission/PermissionRole';
import { PermissionRoleAssignment } from '@pkg/everest.appserver/types/permission/PermissionRoleAssignment';
import { RoleDataSource } from '@pkg/everest.base.ui/permission/role/detailExpert/RoleDataSource';
import type { roleDetailPresentation } from '@pkg/everest.base.ui/types/presentations/permission/role/detailExpert/roleDetail';
import {
  APPSERVER_PACKAGES,
  RUNTIME_PACKAGE,
} from '@pkg/everest.base/public/permissionAssignmentConstants';
import { EvstPermissionAssignmentType } from '@pkg/everest.base/types/enums/PermissionAssignmentType';

/* TYPES */
type ValidationAttachSelectedType =
  roleDetailPresentation.dataSources.role.routines.attachFlows.validateInput['input'];
type BaseTestDataType<TInput, TOutput> = {
  name: string;
  callbackFn: (dataSource: RoleDataSource, input: TInput) => Promise<TOutput>;
};
type WithDynPath = {
  dynPath: EvstPermissionAssignmentType;
};
type DetermineTestDataType = BaseTestDataType<
  roleDetailPresentation.dataSources.role.routines.assignFlows.determineInput,
  RoutineDeterminationResult
> & {
  visibleForAppserver?: boolean;
  visibleForRuntime?: boolean;
};
type DetermineRemoveTestDataType = BaseTestDataType<
  roleDetailPresentation.dataSources.role.routines.removeFlows.determineInput,
  RoutineDeterminationResult
>;
type ValidateAssignTestDataType = BaseTestDataType<
  roleDetailPresentation.dataSources.role.routines.assignFlows.validateInput,
  RoutineValidationResult
>;
type ValidateAttachTestDataType = BaseTestDataType<
  roleDetailPresentation.dataSources.role.routines.attachFlows.validateInput,
  RoutineValidationResult
>;
type RemoveTestDataType = BaseTestDataType<
  roleDetailPresentation.dataSources.role.routines.removeFlows.executeInput,
  void
> &
  WithDynPath;
type AssignTestDataType = BaseTestDataType<
  roleDetailPresentation.dataSources.role.routines.assignFlows.executeInput,
  void
> &
  WithDynPath;
type AttachTestDataType = BaseTestDataType<
  roleDetailPresentation.dataSources.role.routines.attachFlows.executeInput,
  void
> &
  WithDynPath;

/* MOCKS */
jest.mock(
  '@pkg/everest.appserver/types/permission/PermissionRoleAssignment',
  () => ({
    PermissionRoleAssignment: {
      deleteMany: jest.fn(),
      createMany: jest.fn(),
    },
  })
);
jest.mock('@pkg/everest.appserver/types/permission/PermissionRole', () => ({
  PermissionRole: {
    update: jest.fn(),
  },
}));

describe('Role Detail data source', () => {
  const session = {
    sandboxService: {
      currentSandbox: { id: 101 },
    },
    i18nService: {
      getTranslation: async (key: string) => key,
    },
  } as unknown as ISession;
  const mode = 'view';
  const parameters = { id: '1001' };
  const baseRole = {
    id: 1001,
    uuid: '96326eb7-e82e-43dc-a0fe-0968ed61009d',
    package: 'everest.base',
    roleType: EvstPermissionRoleType.Default,
    name: 'Role name',
    description: 'Role description',
    active: false,
  };

  beforeEach(() => jest.resetAllMocks());

  describe('determine', () => {
    const parameters = { id: '1001' };
    const testData: DetermineTestDataType[] = [
      {
        name: 'Save',
        callbackFn: async (dataSource, input) =>
          dataSource.determine_save(input),
      },
      {
        name: 'Edit',
        callbackFn: async (dataSource, input) =>
          dataSource.determine_edit(input),
      },
      {
        name: 'Assign',
        callbackFn: async (dataSource, input) =>
          dataSource.determine_assign(input),
      },
      {
        name: 'Attach',
        callbackFn: async (dataSource, input) =>
          dataSource.determine_attach(input),
        visibleForAppserver: true,
        visibleForRuntime: false,
      },
      {
        name: 'Assign Flows',
        callbackFn: async (dataSource, input) =>
          dataSource.determine_assignFlows(input),
      },
      {
        name: 'Attach Flows',
        callbackFn: async (dataSource, input) =>
          dataSource.determine_attachFlows(input),
        visibleForAppserver: true,
        visibleForRuntime: false,
      },
      {
        name: 'Assign Collections',
        callbackFn: async (dataSource, input) =>
          dataSource.determine_assignCollections(input),
      },
      {
        name: 'Attach Collections',
        callbackFn: async (dataSource, input) =>
          dataSource.determine_attachCollections(input),
        visibleForAppserver: true,
        visibleForRuntime: false,
      },
      {
        name: 'Assign Policies',
        callbackFn: async (dataSource, input) =>
          dataSource.determine_assignPolicies(input),
      },
      {
        name: 'Attach Policies',
        callbackFn: async (dataSource, input) =>
          dataSource.determine_attachPolicies(input),
        visibleForAppserver: true,
        visibleForRuntime: false,
      },
    ];

    describe('MAIN', () => {
      it.each(testData)('$name invisible on main', async ({ callbackFn }) => {
        // arrange
        const session = {
          sandboxService: {},
          i18nService: {
            getTranslation: async (key: string) => key,
          },
        } as unknown as ISession;
        const dataSource = new RoleDataSource();
        dataSource['_role'] = {
          package: 'everest.base',
          roleType: EvstPermissionRoleType.Default,
        };

        // act
        const result = await callbackFn(dataSource, {
          session,
          mode,
          parameters,
        });

        // assert
        expect(result.visible).toBe(false);
      });
    });

    describe('AutoSync', () => {
      it.each(testData)(
        '$name invisible for auto sync roles',
        async ({ callbackFn }) => {
          // arrange
          const dataSource = new RoleDataSource();
          dataSource['_role'] = {
            package: 'everest.base',
            roleType: EvstPermissionRoleType.Autosync,
          };

          // act
          const result = await callbackFn(dataSource, {
            session,
            mode,
            parameters,
          });

          // assert
          expect(result.visible).toBe(false);
        }
      );
    });

    describe('Generated', () => {
      it.each(testData)(
        '$name invisible for auto sync roles',
        async ({ callbackFn }) => {
          // arrange
          const dataSource = new RoleDataSource();
          dataSource['_role'] = {
            package: 'everest.base',
            roleType: EvstPermissionRoleType.Default,
            isGenerated: true,
          };

          // act
          const result = await callbackFn(dataSource, {
            session,
            mode,
            parameters,
          });

          // assert
          expect(result.visible).toBe(false);
        }
      );
    });

    describe.each([...APPSERVER_PACKAGES])('Appserver %s', (packageName) => {
      it.each(testData.filter((f) => f.visibleForAppserver !== true))(
        '$name invisible for appserver roles',
        async ({ callbackFn }) => {
          // arrange
          const dataSource = new RoleDataSource();
          dataSource['_role'] = {
            package: packageName,
            roleType: EvstPermissionRoleType.Default,
          };

          // act
          const result = await callbackFn(dataSource, {
            session,
            mode,
            parameters,
          });

          // assert
          expect(result.visible).toBe(false);
        }
      );

      it.each(testData.filter((f) => f.visibleForAppserver === true))(
        '$name visible for appserver roles',
        async ({ callbackFn }) => {
          // arrange
          const dataSource = new RoleDataSource();
          dataSource['_role'] = {
            package: packageName,
            roleType: EvstPermissionRoleType.Default,
          };

          // act
          const result = await callbackFn(dataSource, {
            session,
            mode,
            parameters,
          });

          // assert
          expect(result.visible).toBe(true);
        }
      );
    });

    describe('Runntime', () => {
      it.each(testData.filter((f) => f.visibleForRuntime !== false))(
        '$name visible for runtime package',
        async ({ callbackFn }) => {
          // arrange
          const dataSource = new RoleDataSource();
          dataSource['_role'] = {
            package: RUNTIME_PACKAGE,
            roleType: EvstPermissionRoleType.Default,
          };

          // act
          const result = await callbackFn(dataSource, {
            session,
            mode,
            parameters,
          });

          // assert
          expect(result.visible).toBe(true);
        }
      );

      it.each(testData.filter((f) => f.visibleForRuntime === false))(
        '$name invisible for runtime package',
        async ({ callbackFn }) => {
          // arrange
          const dataSource = new RoleDataSource();
          dataSource['_role'] = {
            package: RUNTIME_PACKAGE,
            roleType: EvstPermissionRoleType.Default,
          };

          // act
          const result = await callbackFn(dataSource, {
            session,
            mode,
            parameters,
          });

          // assert
          expect(result.visible).toBe(false);
        }
      );
    });

    describe('Visible', () => {
      it.each(testData)('$name is visible', async ({ callbackFn }) => {
        // arrange
        const dataSource = new RoleDataSource();
        dataSource['_role'] = {
          package: 'everest.base',
          roleType: EvstPermissionRoleType.Default,
        };

        // act
        const result = await callbackFn(dataSource, {
          session,
          mode,
          parameters,
        });

        // assert
        expect(result.visible).toBe(true);
      });
    });
  });

  describe('determine remove', () => {
    const selected: roleDetailPresentation.dataSources.role.routines.removeFlows.determineInput['input']['selected'] =
      [
        {
          package: 'everest.base',
          uuid: 'fad690a8-2ed6-4afb-b6c3-02c4276e672f',
        },
        {
          package: 'everest.base',
          uuid: 'efb9ac13-7d4e-4356-a1e2-a993631a5c5c',
        },
      ];
    const testData: DetermineRemoveTestDataType[] = [
      {
        name: 'Flows',
        callbackFn: async (dataSource, input) =>
          dataSource.determine_removeFlows(input),
      },
      {
        name: 'Collections',
        callbackFn: async (dataSource, input) =>
          dataSource.determine_removeCollections(input),
      },
      {
        name: 'Policies',
        callbackFn: async (dataSource, input) =>
          dataSource.determine_removePolicies(input),
      },
    ];

    describe('MAIN', () => {
      it.each(testData)(
        'Remove $name invisible on MAIN',
        async ({ callbackFn }) => {
          // arrange
          const session = {
            sandboxService: {},
            i18nService: {
              getTranslation: async (key: string) => key,
            },
          } as unknown as ISession;
          const dataSource = new RoleDataSource();
          dataSource['_role'] = {
            package: 'everest.base',
            roleType: EvstPermissionRoleType.Default,
          };

          // act
          const result = await callbackFn(dataSource, {
            session,
            mode,
            parameters,
            input: { selected },
          });

          // assert
          expect(result.visible).toBe(false);
        }
      );
    });

    describe('AutoSync', () => {
      it.each(testData)(
        'Remove $name invisible for auto sync roles',
        async ({ callbackFn }) => {
          // arrange
          const dataSource = new RoleDataSource();
          dataSource['_role'] = {
            package: 'everest.base',
            roleType: EvstPermissionRoleType.Autosync,
          };

          // act
          const result = await callbackFn(dataSource, {
            session,
            mode,
            parameters,
            input: { selected },
          });

          // assert
          expect(result.visible).toBe(false);
        }
      );
    });

    describe('Generated', () => {
      it.each(testData)(
        'Remove $name invisible for auto sync roles',
        async ({ callbackFn }) => {
          // arrange
          const dataSource = new RoleDataSource();
          dataSource['_role'] = {
            package: 'everest.base',
            roleType: EvstPermissionRoleType.Default,
            isGenerated: true,
          };

          // act
          const result = await callbackFn(dataSource, {
            session,
            mode,
            parameters,
            input: { selected },
          });

          // assert
          expect(result.visible).toBe(false);
        }
      );
    });

    describe.each([...APPSERVER_PACKAGES])('Appserver %s', (packageName) => {
      it.each(testData)(
        'Remove $name visible for appserver roles',
        async ({ callbackFn }) => {
          // arrange
          const dataSource = new RoleDataSource();
          dataSource['_role'] = {
            package: packageName,
            roleType: EvstPermissionRoleType.Default,
          };

          // act
          const result = await callbackFn(dataSource, {
            session,
            mode,
            parameters,
            input: { selected },
          });

          // assert
          expect(result.visible).toBe(true);
        }
      );
    });

    describe('Runntime', () => {
      it.each(testData)(
        'Remove $name visible for runtime package',
        async ({ callbackFn }) => {
          // arrange
          const dataSource = new RoleDataSource();
          dataSource['_role'] = {
            package: RUNTIME_PACKAGE,
            roleType: EvstPermissionRoleType.Default,
          };

          // act
          const result = await callbackFn(dataSource, {
            session,
            mode,
            parameters,
            input: { selected },
          });

          // assert
          expect(result.visible).toBe(true);
        }
      );
    });

    describe('Visible', () => {
      it.each(testData)('Romove $name is visible', async ({ callbackFn }) => {
        // arrange
        const dataSource = new RoleDataSource();
        dataSource['_role'] = {
          package: 'everest.base',
          roleType: EvstPermissionRoleType.Default,
        };

        // act
        const result = await callbackFn(dataSource, {
          session,
          mode,
          parameters,
          input: { selected },
        });

        // assert
        expect(result.visible).toBe(true);
      });
    });
  });

  describe('validate_save', () => {
    const mockedAddError = jest.fn();
    const context = {
      addError: mockedAddError,
    } as unknown as RoutineValidationContext;

    beforeEach(() => jest.resetAllMocks());

    it('require name', async () => {
      // arrange
      const dataSource = new RoleDataSource();
      dataSource['_role'] = {
        ...baseRole,
        name: '',
      };

      // act
      await dataSource.validate_save({
        session,
        mode,
        parameters,
        validateReason: 'earlyEvaluation',
        context,
      });

      // assert
      expect(mockedAddError).toHaveBeenCalledWith(
        [],
        'genaral.validation.required.name',
        'name'
      );
    });

    it('require package', async () => {
      // arrange
      const dataSource = new RoleDataSource();
      dataSource['_role'] = {
        ...baseRole,
        package: '',
      };

      // act
      await dataSource.validate_save({
        session,
        mode,
        parameters,
        validateReason: 'earlyEvaluation',
        context,
      });

      // assert
      expect(mockedAddError).toHaveBeenCalledWith(
        [],
        'genaral.validation.required.package',
        'package'
      );
    });

    it('valid', async () => {
      // arrange
      const dataSource = new RoleDataSource();
      dataSource['_role'] = {
        ...baseRole,
      };

      // act
      const result = await dataSource.validate_save({
        session,
        mode,
        parameters,
        validateReason: 'earlyEvaluation',
        context,
      });

      // assert
      expect(mockedAddError).toHaveBeenCalledTimes(0);
      expect(result).toBeUndefined();
    });
  });

  describe('validate assign', () => {
    const mockedAddError = jest.fn();
    const context = {
      addError: mockedAddError,
    } as unknown as RoutineValidationContext;
    const testData: ValidateAssignTestDataType[] = [
      {
        name: 'Flows',
        callbackFn: async (dataSource, input) =>
          dataSource.validate_assignFlows(input),
      },
      {
        name: 'Collections',
        callbackFn: async (dataSource, input) =>
          dataSource.validate_assignCollections(input),
      },
      {
        name: 'Policies',
        callbackFn: async (dataSource, input) =>
          dataSource.validate_assignPolicies(input),
      },
    ];

    beforeEach(() => jest.resetAllMocks());

    it.each(testData)(
      'Invalid assign $name for empty selection',
      async ({ callbackFn }) => {
        // arrange
        const dataSource = new RoleDataSource();
        dataSource['_role'] = {
          ...baseRole,
        };
        const permissionUUIDs: string[] = [];

        // act
        const result = await callbackFn(dataSource, {
          session,
          mode,
          parameters,
          validateReason: 'earlyEvaluation',
          context,
          input: { permissionUUIDs },
        });

        // assert
        expect(mockedAddError).toHaveBeenCalledTimes(0);
        expect(result).toEqual({
          passed: false,
          message: 'autoSync.validation.noPermissionsSelected',
        });
      }
    );

    it.each(testData)('Valid assign $name', async ({ callbackFn }) => {
      // arrange
      const dataSource = new RoleDataSource();
      dataSource['_role'] = {
        ...baseRole,
      };
      const permissionUUIDs: string[] = [
        'f7a86dd3-6b37-4f48-a075-468d3203805c',
        '11d2d2ec-afbe-4902-b9cb-75f1e4e7efa5',
      ];

      // act
      const result = await callbackFn(dataSource, {
        session,
        mode,
        parameters,
        validateReason: 'earlyEvaluation',
        context,
        input: { permissionUUIDs },
      });

      // assert
      expect(mockedAddError).toHaveBeenCalledTimes(0);
      expect(result).toBeUndefined();
    });
  });

  describe('validate attach', () => {
    const mockedAddError = jest.fn();
    const context = {
      addError: mockedAddError,
    } as unknown as RoutineValidationContext;
    const testData: ValidateAttachTestDataType[] = [
      {
        name: 'Flows',
        callbackFn: async (dataSource, input) =>
          dataSource.validate_attachFlows(input),
      },
      {
        name: 'Collections',
        callbackFn: async (dataSource, input) =>
          dataSource.validate_attachCollections(input),
      },
      {
        name: 'Policies',
        callbackFn: async (dataSource, input) =>
          dataSource.validate_attachPolicies(input),
      },
    ];

    beforeEach(() => jest.resetAllMocks());

    it.each(testData)(
      'Invalid attach $name for empty selection',
      async ({ callbackFn }) => {
        // arrange
        const dataSource = new RoleDataSource();
        dataSource['_role'] = {
          ...baseRole,
        };
        const input: ValidationAttachSelectedType = {
          packageName: 'everest.base.ui',
          permissionUUIDs: [],
        };

        // act
        const result = await callbackFn(dataSource, {
          session,
          mode,
          parameters,
          validateReason: 'earlyEvaluation',
          context,
          input,
        });

        // assert
        expect(mockedAddError).toHaveBeenCalledTimes(0);
        expect(result).toEqual({
          passed: false,
          message: 'autoSync.validation.noPermissionsSelected',
        });
      }
    );

    it.each(testData)(
      'Invalid attach $name for empty package',
      async ({ callbackFn }) => {
        // arrange
        const dataSource = new RoleDataSource();
        dataSource['_role'] = {
          ...baseRole,
        };
        const input: ValidationAttachSelectedType = {
          packageName: '',
          permissionUUIDs: [
            '255b1c58-8c17-4eb3-83ef-67df3a602f0c',
            'fc9f6166-4e75-49fa-8513-79bab2997219',
          ],
        };

        // act
        const result = await callbackFn(dataSource, {
          session,
          mode,
          parameters,
          validateReason: 'earlyEvaluation',
          context,
          input,
        });

        // assert
        expect(mockedAddError).toHaveBeenCalledTimes(0);
        expect(result).toEqual({
          passed: false,
          message: 'genaral.validation.required.package',
        });
      }
    );

    it.each(testData)(
      'Invalid attach $name for same package as role',
      async ({ callbackFn }) => {
        // arrange
        const dataSource = new RoleDataSource();
        dataSource['_role'] = {
          ...baseRole,
        };
        const input: ValidationAttachSelectedType = {
          packageName: 'everest.base',
          permissionUUIDs: [
            '255b1c58-8c17-4eb3-83ef-67df3a602f0c',
            'fc9f6166-4e75-49fa-8513-79bab2997219',
          ],
        };

        // act
        const result = await callbackFn(dataSource, {
          session,
          mode,
          parameters,
          validateReason: 'earlyEvaluation',
          context,
          input,
        });

        // assert
        expect(mockedAddError).toHaveBeenCalledTimes(0);
        expect(result).toEqual({
          passed: false,
          message: 'general.validation.attach.samePackage',
        });
      }
    );

    it.each(testData)('Valid attach $name', async ({ callbackFn }) => {
      // arrange
      const dataSource = new RoleDataSource();
      dataSource['_role'] = {
        ...baseRole,
      };
      const input: ValidationAttachSelectedType = {
        packageName: 'everest.base.ui',
        permissionUUIDs: [
          'f7a86dd3-6b37-4f48-a075-468d3203805c',
          '11d2d2ec-afbe-4902-b9cb-75f1e4e7efa5',
        ],
      };

      // act
      const result = await callbackFn(dataSource, {
        session,
        mode,
        parameters,
        validateReason: 'earlyEvaluation',
        context,
        input,
      });

      // assert
      expect(mockedAddError).toHaveBeenCalledTimes(0);
      expect(result).toBeUndefined();
    });
  });

  describe('execute remove', () => {
    const testData: RemoveTestDataType[] = [
      {
        name: 'Flows',
        dynPath:
          EvstPermissionAssignmentType[
            'Urn:evst:everest:appserver:model/node:permission/PermissionFlow.uuid'
          ],
        callbackFn: async (dataSource, input) =>
          dataSource.execute_removeFlows(input),
      },
      {
        name: 'Collections',
        dynPath:
          EvstPermissionAssignmentType[
            'Urn:evst:everest:appserver:model/node:permission/PolicyGroup.uuid'
          ],
        callbackFn: async (dataSource, input) =>
          dataSource.execute_removeCollections(input),
      },
      {
        name: 'Policies',
        dynPath:
          EvstPermissionAssignmentType[
            'Urn:evst:everest:appserver:model/node:permission/Policy.uuid'
          ],
        callbackFn: async (dataSource, input) =>
          dataSource.execute_removePolicies(input),
      },
    ];

    it.each(testData)('Remove $name', async ({ callbackFn, dynPath }) => {
      // arrange
      const dataSource = new RoleDataSource();
      dataSource['_role'] = {
        ...baseRole,
      };
      const deleteMock = jest.mocked(PermissionRoleAssignment.deleteMany);
      const selected: roleDetailPresentation.dataSources.role.routines.removeFlows.executeInput['input']['selected'] =
        [
          {
            package: 'everest.base',
            uuid: 'fad690a8-2ed6-4afb-b6c3-02c4276e672f',
          },
          {
            package: 'everest.base.ui',
            uuid: 'efb9ac13-7d4e-4356-a1e2-a993631a5c5c',
          },
        ];

      // act
      await callbackFn(dataSource, {
        session,
        mode,
        parameters,
        input: { selected },
      });

      // assert
      expect(deleteMock).toHaveBeenCalledTimes(1);
      expect(deleteMock).toHaveBeenCalledWith(session, {
        permissionRoleUUID: baseRole.uuid,
        relatedNodePath: dynPath,
        uuid: {
          $in: [
            'fad690a8-2ed6-4afb-b6c3-02c4276e672f',
            'efb9ac13-7d4e-4356-a1e2-a993631a5c5c',
          ],
        },
      });
    });
  });

  describe('execute save', () => {
    it('save role execution', async () => {
      // arrange
      const dataSource = new RoleDataSource();
      dataSource['_role'] = {
        ...baseRole,
      };
      const updateMock = jest.mocked(PermissionRole.update);

      // act
      await dataSource.execute_save({ session, mode: 'edit', parameters });

      // assert
      expect(updateMock).toHaveBeenCalledTimes(1);
      expect(updateMock).toHaveBeenCalledWith(
        session,
        { uuid: baseRole.uuid },
        {
          name: baseRole.name,
          description: baseRole.description,
          active: baseRole.active,
        },
        ['uuid']
      );
    });
  });

  describe('execute assign', () => {
    const testData: AssignTestDataType[] = [
      {
        name: 'Flows',
        dynPath:
          EvstPermissionAssignmentType[
            'Urn:evst:everest:appserver:model/node:permission/PermissionFlow.uuid'
          ],
        callbackFn: async (dataSource, input) =>
          dataSource.execute_assignFlows(input),
      },
      {
        name: 'Collections',
        dynPath:
          EvstPermissionAssignmentType[
            'Urn:evst:everest:appserver:model/node:permission/PolicyGroup.uuid'
          ],
        callbackFn: async (dataSource, input) =>
          dataSource.execute_assignCollections(input),
      },
      {
        name: 'Policies',
        dynPath:
          EvstPermissionAssignmentType[
            'Urn:evst:everest:appserver:model/node:permission/Policy.uuid'
          ],
        callbackFn: async (dataSource, input) =>
          dataSource.execute_assignPolicies(input),
      },
    ];

    it.each(testData)(
      'Assign $name execution',
      async ({ callbackFn, dynPath }) => {
        // arrange
        const dataSource = new RoleDataSource();
        dataSource['_role'] = {
          ...baseRole,
        };
        const createMock = jest.mocked(PermissionRoleAssignment.createMany);
        const permissionUUIDs: string[] = [
          '36967818-ad90-433a-9a5f-622c55899146',
          '391cbfc6-eaba-4f0f-8f29-c90d9a710702',
        ];

        // act
        await callbackFn(dataSource, {
          session,
          mode,
          parameters,
          input: { permissionUUIDs },
        });

        // assert
        expect(createMock).toHaveBeenCalledTimes(1);
        expect(createMock).toHaveBeenCalledWith(
          session,
          [
            {
              package: baseRole.package,
              permissionRoleUUID: baseRole.uuid,
              relatedNodePath: dynPath,
              relatedNodeUUID: '36967818-ad90-433a-9a5f-622c55899146',
            },
            {
              package: baseRole.package,
              permissionRoleUUID: baseRole.uuid,
              relatedNodePath: dynPath,
              relatedNodeUUID: '391cbfc6-eaba-4f0f-8f29-c90d9a710702',
            },
          ],
          ['uuid']
        );
      }
    );
  });

  describe('execute assign', () => {
    const testData: AttachTestDataType[] = [
      {
        name: 'Flows',
        dynPath:
          EvstPermissionAssignmentType[
            'Urn:evst:everest:appserver:model/node:permission/PermissionFlow.uuid'
          ],
        callbackFn: async (dataSource, input) =>
          dataSource.execute_attachFlows(input),
      },
      {
        name: 'Collections',
        dynPath:
          EvstPermissionAssignmentType[
            'Urn:evst:everest:appserver:model/node:permission/PolicyGroup.uuid'
          ],
        callbackFn: async (dataSource, input) =>
          dataSource.execute_attachCollections(input),
      },
      {
        name: 'Policies',
        dynPath:
          EvstPermissionAssignmentType[
            'Urn:evst:everest:appserver:model/node:permission/Policy.uuid'
          ],
        callbackFn: async (dataSource, input) =>
          dataSource.execute_attachPolicies(input),
      },
    ];

    it.each(testData)(
      'Attach $name execution',
      async ({ callbackFn, dynPath }) => {
        // arrange
        const dataSource = new RoleDataSource();
        dataSource['_role'] = {
          ...baseRole,
        };
        const createMock = jest.mocked(PermissionRoleAssignment.createMany);
        const permissionUUIDs: string[] = [
          '36967818-ad90-433a-9a5f-622c55899146',
          '391cbfc6-eaba-4f0f-8f29-c90d9a710702',
        ];

        // act
        await callbackFn(dataSource, {
          session,
          mode,
          parameters,
          input: { packageName: 'everest.base.ui', permissionUUIDs },
        });

        // assert
        expect(createMock).toHaveBeenCalledTimes(1);
        expect(createMock).toHaveBeenCalledWith(
          session,
          [
            {
              package: 'everest.base.ui',
              permissionRoleUUID: baseRole.uuid,
              relatedNodePath: dynPath,
              relatedNodeUUID: '36967818-ad90-433a-9a5f-622c55899146',
            },
            {
              package: 'everest.base.ui',
              permissionRoleUUID: baseRole.uuid,
              relatedNodePath: dynPath,
              relatedNodeUUID: '391cbfc6-eaba-4f0f-8f29-c90d9a710702',
            },
          ],
          ['uuid']
        );
      }
    );
  });
});
