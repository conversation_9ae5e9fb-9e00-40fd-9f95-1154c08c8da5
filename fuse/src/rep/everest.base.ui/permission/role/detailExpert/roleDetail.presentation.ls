// @i18n:policy
package everest.base.ui

template presentation roleDetail {

    state {
        id: Text
    }

    object data-source role with(id=state.id){
        shape {
            id (label: '{{policy.id}}', editable: false): field<everest.appserver::permission:PermissionRole.id>
            uuid (label: '{{policy.uuid}}', editable: false): field<everest.appserver::permission:PermissionRole.uuid>
            name: field<everest.appserver::permission:PermissionRole.name>
            `description`: field<everest.appserver::permission:PermissionRole.`description`>
            `package` (editable: false): field<everest.appserver::permission:PermissionRole.`package`>
            generation (editable: false): field<everest.appserver::permission:PermissionRole.generation>
            deprecation (editable: false): field<everest.appserver::permission:PermissionRole.deprecation>
            roleType (editable: false): field<everest.appserver::permission:PermissionRole.roleType>
            active: field<everest.appserver::permission:PermissionRole.active>

            isDeprecated: TrueFalse
            isGenerated: TrueFalse
            flowCount: Number<Int>
            collectionCount: Number<Int>
            policyCount: Number<Int>

            children Flows {
                id (filterable: false): field<everest.appserver::permission:PermissionRoleAssignment.id>
                uuid (filterable: false): field<everest.appserver::permission:PermissionRoleAssignment.uuid>
                `package`: field<everest.appserver::permission:PermissionRoleAssignment.`package`>
                relatedNodePath (filterable: false): field<everest.appserver::permission:PermissionRoleAssignment.relatedNodePath>
                relatedNodeUUID (filterable: false): field<everest.appserver::permission:PermissionRoleAssignment.relatedNodeUUID>
                name: field<everest.appserver::permission:PermissionFlow.name>
                `description`: field<everest.appserver::permission:PermissionFlow.`description`>
            }

            children Collections {
                id (filterable: false): field<everest.appserver::permission:PermissionRoleAssignment.id>
                uuid (filterable: false): field<everest.appserver::permission:PermissionRoleAssignment.uuid>
                `package`: field<everest.appserver::permission:PermissionRoleAssignment.`package`>
                relatedNodePath (filterable: false): field<everest.appserver::permission:PermissionRoleAssignment.relatedNodePath>
                relatedNodeUUID (filterable: false): field<everest.appserver::permission:PermissionRoleAssignment.relatedNodeUUID>
                name: field<everest.appserver::permission:PolicyGroup.name>
                `description`: field<everest.appserver::permission:PolicyGroup.`description`>
                generatedFor: field<everest.appserver::permission:PolicyGroup.generatedFor>
            }

            children Policies {
                id (filterable: false): field<everest.appserver::permission:PermissionRoleAssignment.id>
                uuid (filterable: false): field<everest.appserver::permission:PermissionRoleAssignment.uuid>
                `package`: field<everest.appserver::permission:PermissionRoleAssignment.`package`>
                relatedNodePath (filterable: false): field<everest.appserver::permission:PermissionRoleAssignment.relatedNodePath>
                relatedNodeUUID (filterable: false): field<everest.appserver::permission:PermissionRoleAssignment.relatedNodeUUID>
                name: field<everest.appserver::permission:Policy.name>
                `description`: field<everest.appserver::permission:Policy.`description`>
                accessEffect: field<everest.appserver::permission:Policy.accessEffect>
                `actions`: field<everest.appserver::permission:Policy.`actions`>
                resources: field<everest.appserver::permission:Policy.resources>
                calculatedActions: field<everest.appserver::permission:Policy.calculatedActions>
                notActions: field<everest.appserver::permission:Policy.notActions>
            }
        }
        modifications {
            on root support update
        }
        routine edit {
            properties {
                side-effects false
            }
        }
        routine save {
            properties {
                side-effects true
            }
        }
        routine assign {
            properties {
                side-effects false
            }
        }
        routine attach {
            properties {
                side-effects false
            }
        }
        routine assignFlows {
            inputs {
                permissionUUIDs: array<field<everest.appserver::permission:PermissionFlow.uuid>>
            }
            properties {
                side-effects true
            }
        }
        routine attachFlows {
            inputs {
                packageName: field<everest.appserver::permission:PermissionRoleAssignment.`package`>
                permissionUUIDs: array<field<everest.appserver::permission:PermissionFlow.uuid>>
            }
            properties {
                side-effects true
            }
        }
        routine removeFlows {
            inputs {
                selected: array<object<{
                    uuid: field<everest.appserver::permission:PermissionRoleAssignment.uuid>
                    `package`: field<everest.appserver::permission:PermissionRoleAssignment.`package`>
                }>>
            }
            properties {
                side-effects true
            }
        }
        routine assignCollections {
            inputs {
                permissionUUIDs: array<field<everest.appserver::permission:PolicyGroup.uuid>>
            }
            properties {
                side-effects true
            }
        }
        routine attachCollections {
            inputs {
                packageName: field<everest.appserver::permission:PermissionRoleAssignment.`package`>
                permissionUUIDs: array<field<everest.appserver::permission:PolicyGroup.uuid>>
            }
            properties {
                side-effects true
            }
        }
        routine removeCollections {
            inputs {
                selected: array<object<{
                    uuid: field<everest.appserver::permission:PermissionRoleAssignment.uuid>
                    `package`: field<everest.appserver::permission:PermissionRoleAssignment.`package`>
                }>>
            }
            properties {
                side-effects true
            }
        }
        routine assignPolicies {
            inputs {
                permissionUUIDs: array<field<everest.appserver::permission:PolicyGroup.uuid>>
            }
            properties {
                side-effects true
            }
        }
        routine attachPolicies {
            inputs {
                packageName: field<everest.appserver::permission:PermissionRoleAssignment.`package`>
                permissionUUIDs: array<field<everest.appserver::permission:PolicyGroup.uuid>>
            }
            properties {
                side-effects true
            }
        }
        routine removePolicies {
            inputs {
                selected: array<object<{
                    uuid: field<everest.appserver::permission:PermissionRoleAssignment.uuid>
                    `package`: field<everest.appserver::permission:PermissionRoleAssignment.`package`>
                }>>
            }
            properties {
                side-effects true
            }
        }
    }

    struct role {
        data role
        fields *
    }

    data-set flows {
        component Table {
            pagination = true
        }

        data role.Flows
        fields *
    }

    data-set collections {
        component Table {
            pagination = true
        }

        data role.Collections
        fields *
    }

    data-set policies {
        component Table {
            pagination = true
        }

        data role.Policies
        fields *
    }

    delegate action edit to data-source<role>.edit transitions-to edit

    delegate action save to data-source<role>.save transitions-to view {
        properties {
            affected-data-sets role
        }
    }

    delegate action assign to data-source<role>.assign

    delegate action attach to data-source<role>.attach

    transition action cancel transitions-to view

    delegate action assignFlows to data-source<role>.assignFlows {
        properties {
            affected-data-sets flows
        }
    }

    delegate action attachFlows to data-source<role>.attachFlows {
        properties {
            affected-data-sets flows
        }
    }

    delegate action removeFlows to data-source<role>.removeFlows {
        inputs {
            selected = '$flows[selected].{uuid,package}$'
        }
        properties {
            affected-data-sets flows
        }
    }

    delegate action assignCollections to data-source<role>.assignCollections {
        properties {
            affected-data-sets collections
        }
    }

    delegate action attachCollections to data-source<role>.attachCollections {
        properties {
            affected-data-sets collections
        }
    }

    delegate action removeCollections to data-source<role>.removeCollections {
        inputs {
            selected = '$collections[selected].{uuid,package}$'
        }
        properties {
            affected-data-sets collections
        }
    }

    delegate action assignPolicies to data-source<role>.assignPolicies {
        properties {
            affected-data-sets policies
        }
    }

    delegate action attachPolicies to data-source<role>.attachPolicies {
        properties {
            affected-data-sets policies
        }
    }

    delegate action removePolicies to data-source<role>.removePolicies {
        inputs {
            selected = '$policies[selected].{uuid,package}$'
        }
        properties {
            affected-data-sets policies
        }
    }

    mode view {
        on all-data-sets allow view
        allow actions edit, assign, attach,
            assignFlows, attachFlows, removeFlows,
            assignCollections, attachCollections, removeCollections,
            assignPolicies, attachPolicies, removePolicies
    }

    mode edit {
        on all-data-sets allow view
        on role allow change
        allow actions cancel, save
    }
}
