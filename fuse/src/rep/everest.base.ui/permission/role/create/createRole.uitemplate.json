{"version": 3, "uimodel": {"presentation": {"urn": "urn:evst:everest:base/ui:presentation:permission/role/create/createRole", "parameters": {"mode": "create"}}}, "uicontroller": ["createRole.uicontroller.ts"], "uiview": {"i18n": ["policy"], "title": "{{permissionRole.title.new}}", "sections": {"content": [{"component": "FieldGroup", "section": {"grid": {"size": "12"}}, "props": {"presentationDataSet": "@dataSet:role", "variant": "secondary", "columns": "1", "compact": true, "fields": ["name", {"field": "description", "fieldProps": {"multiline": true}}, {"field": "package", "fieldProps": {"semanticTypeUiParams": {"includeBuiltin": false, "includeRuntime": true}}}]}}]}, "actions": {"content": [{"label": "{{policy.action.close}}", "variant": "secondary", "onClick": "@controller:close"}, {"label": "{{policy.action.save}}", "variant": "primary", "presentationAction": "@action:save", "onClick": "@controller:save"}]}}}