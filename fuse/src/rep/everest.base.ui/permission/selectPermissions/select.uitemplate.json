{"version": 3, "uicontroller": ["select.uicontroller.ts"], "uimodel": {"state": {"mode": null, "packageName": null}, "presentation": {"urn": "urn:evst:everest:base/ui:presentation:permission/selectPermissions/select", "parameters": {"mode": "@state:mode", "packageName": "@state:packageName"}}}, "uiview": {"i18n": ["selectPermissions"], "title": "@controller:getTitle()", "config": {"allowRefreshData": false, "autoRefreshData": false, "hideIcons": true}, "actions": {"content": [{"variant": "secondary", "label": "{{selectPermissions.action.cancel}}", "onClick": "@controller:cancel"}, {"variant": "primary", "label": "{{selectPermissions.action.select}}", "presentationAction": "@action:select", "onClick": "@controller:select"}]}, "sections": {"content": [{"component": "Table", "customId": "PermissionList", "section": {"grid": {"size": "12"}, "header": {"content": [{"component": "Switch", "props": {"label": "{{permissionSelection.label.includeAppserverPackage}}", "value": "@binding:selection.includeAppserver", "editing": true, "direction": "horizontal-reverse"}}, {"component": "Switch", "props": {"label": "{{permissionSelection.label.onlyCompatiblePackages}}", "value": "@binding:selection.onlyCompatible", "editing": true, "direction": "horizontal-reverse"}}]}}, "props": {"presentationDataSet": "@dataSet:permissions", "rowSelection": true, "rowSelectionDisabled": "@controller:isRowSelectionDisabled", "rowBackgroundGetter": "@controller:getRowBackgroundMatcher", "fixedFilters": ["name", "package", "description"]}}]}}}