import type { Filter, ISession } from '@everestsystems/content-core';
import {
  DATA,
  DYNAMIC_FIELDS,
  IDENTIFIER,
  METADATA,
} from '@everestsystems/content-core';
import { EverestPackageDependency } from '@pkg/everest.appserver/types/metadata/EverestPackageDependency';
import { PermissionFlow } from '@pkg/everest.appserver/types/permission/PermissionFlow';
import { PermissionRole } from '@pkg/everest.appserver/types/permission/PermissionRole';
import { PolicyGroup } from '@pkg/everest.appserver/types/permission/PolicyGroup';
import type { selectPresentation } from '@pkg/everest.base.ui/types/presentations/permission/selectPermissions/select';
import {
  APPSERVER_PACKAGES,
  RUNTIME_PACKAGE,
} from '@pkg/everest.base/public/permissionAssignmentConstants';
import {
  combineFilters,
  filterAndMapFields,
  filterAndMapFilters,
  filterAndMapOrders,
} from '@pkg/everest.base/public/utils/presentation/queryInstructionUtils';
import { set } from 'lodash';

const exludedFields = new Set(['isSelectable'] as const);
const defaultOrders: ReadonlyArray<{
  field: string;
  ordering: 'asc' | 'desc';
}> = [
  { field: 'package', ordering: 'asc' },
  { field: 'name', ordering: 'asc' },
];

export class PermissionsDataSource
  implements selectPresentation.dataSources.selection.implementation
{
  private _metadata: selectPresentation.dataSources.selection.callbacks.query.queryMetadata;
  private _data: selectPresentation.dataSources.selection.callbacks.query.output =
    {
      includeAppserver: false,
      onlyCompatible: true,
    };
  private _allowedPackages: Set<string> | 'ALL';

  async query(
    input: selectPresentation.dataSources.selection.callbacks.query.input
  ): Promise<selectPresentation.dataSources.selection.callbacks.query.combinedOutput> {
    const {
      mode,
      session,
      parameters: { packageName },
      queryInstruction: {
        fields,
        Permissions: { fields: permissionFields },
      },
    } = input;
    if (!this._metadata) {
      // Set dynamic fields metadata
      switch (mode) {
        case 'roles': {
          this._metadata = {
            Permissions: {
              [DYNAMIC_FIELDS]: {
                roleType: {
                  type: {
                    urn: 'urn:evst:everest:appserver:field/node:permission/PermissionRole.roleType',
                  },
                  hidden: true,
                },
              },
            },
          };
          break;
        }
        case 'policies': {
          this._metadata = {
            Permissions: {
              [DYNAMIC_FIELDS]: {
                accessEffect: {
                  type: {
                    urn: 'urn:evst:everest:appserver:field/node:permission/Policy.accessEffect',
                  },
                  hidden: true,
                },
                actions: {
                  type: {
                    urn: 'urn:evst:everest:appserver:field/node:permission/Policy.actions',
                  },
                  hidden: true,
                },
                notActions: {
                  type: {
                    urn: 'urn:evst:everest:appserver:field/node:permission/Policy.notActions',
                  },
                  hidden: true,
                },
                resources: {
                  type: {
                    urn: 'urn:evst:everest:appserver:field/node:permission/Policy.resources',
                  },
                  hidden: true,
                },
              },
            },
          };
          break;
        }
        default: {
          this._metadata = {};
          break;
        }
      }
    }

    // Load allowed packages
    if (!this._allowedPackages) {
      this._allowedPackages = await calculateAllowedPackages(
        session,
        packageName
      );
    }

    // Load permissions
    if (fields || permissionFields) {
      let loadedData: selectPresentation.dataSources.selection.callbacks.query.output['Permissions'];
      const baseFilter = calculateBaseFilter({
        includeAppserver: this._data.includeAppserver,
        onlyCompatible: this._data.onlyCompatible,
        allowedPackages: this._allowedPackages,
      });
      switch (mode) {
        case 'roles': {
          loadedData = await loadRolesInternal(input, baseFilter);
          break;
        }
        case 'flows': {
          loadedData = await loadFlowsInternal(input, baseFilter);
          break;
        }
        case 'collections': {
          loadedData = await loadCollectionsInternal(input, baseFilter);
          break;
        }
        case 'policies': {
          loadedData = await loadPoliciesInternal(input, baseFilter);
          break;
        }
      }

      this._data.Permissions = loadedData.map((permission) => ({
        ...permission,
        [IDENTIFIER]: permission.id,
        isSelectable:
          this._allowedPackages === 'ALL' ||
          this._allowedPackages.has(permission.package),
      }));
    }

    return {
      [DATA]: this._data,
      [METADATA]: this._metadata,
    };
  }

  async update({
    fieldName,
    newFieldValue,
  }: selectPresentation.dataSources.selection.callbacks.update.input): Promise<selectPresentation.dataSources.selection.callbacks.update.output> {
    switch (fieldName) {
      case 'includeAppserver':
      case 'onlyCompatible': {
        set(this._data, fieldName, newFieldValue);
      }
    }
  }

  async validate_select?({
    input,
  }: selectPresentation.dataSources.selection.routines.select.validateInput): Promise<selectPresentation.dataSources.selection.routines.select.validateOutput> {
    return input.selected.length > 0;
  }
  execute_select({
    input,
  }: selectPresentation.dataSources.selection.routines.select.executeInput): Promise<selectPresentation.dataSources.selection.routines.select.executeOutput> {
    return Promise.resolve(input);
  }
}

function calculateBaseFilter(input: {
  includeAppserver?: boolean;
  onlyCompatible?: boolean;
  allowedPackages: Set<string> | 'ALL';
}): Record<string, unknown> {
  const { includeAppserver, onlyCompatible, allowedPackages } = input;
  const resultFilter: Filter<{ package: string }> = {};
  if (allowedPackages === 'ALL') {
    // All packages are compatible
    // we can only exclude appserver, if user wants to
    if (!includeAppserver) {
      resultFilter.package = { $notIn: [...APPSERVER_PACKAGES] };
    }
  } else {
    // Limited compatible packages
    if (onlyCompatible) {
      // Only compatible
      resultFilter.package = {
        $in: [
          ...allowedPackages,
          // Include appserver, if requested
          ...(includeAppserver ? APPSERVER_PACKAGES : []),
        ],
      };
    } else if (!includeAppserver) {
      // Exclude appserver, if not requested
      resultFilter.package = { $notIn: [...APPSERVER_PACKAGES] };
    }
  }

  return resultFilter;
}

async function calculateAllowedPackages(
  session: ISession,
  packageName: string
): Promise<Set<string> | 'ALL'> {
  // Runtime package can select all packages for assignment
  if (packageName === RUNTIME_PACKAGE) {
    return 'ALL';
  }

  // Appserver packages can select all packages for linking
  if (APPSERVER_PACKAGES.has(packageName)) {
    return 'ALL';
  }

  // All others
  // Assign: Current package or direct dependencies
  // Link: Direct dependants of the current package
  const dependencyPackages = await EverestPackageDependency.query(
    session,
    {
      where: {
        $or: [
          {
            dependency: packageName, // given package is dependency of others
          },
          {
            package: packageName, // given package depends on others
          },
        ],
      },
    },
    ['package', 'dependency']
  );

  return new Set([
    packageName,
    ...dependencyPackages.map((d) => d.dependency),
    ...dependencyPackages.map((d) => d.package),
  ]);
}

// Load permissions
function calcualteFieldsFiltersAndOrders(
  queryInstruction: selectPresentation.dataSources.selection.callbacks.query.input['queryInstruction']['Permissions'],
  baseFilter: Record<string, unknown>
): selectPresentation.dataSources.selection.callbacks.query.input['queryInstruction']['Permissions'] {
  const { fields, filters, orders, page } = queryInstruction;

  const fieldList = filterAndMapFields(fields, exludedFields);
  const orderBy = filterAndMapOrders(
    orders,
    exludedFields,
    undefined,
    defaultOrders
  );
  const where = combineFilters(
    filterAndMapFilters(filters, exludedFields),
    baseFilter
  );

  return {
    fields: new Set(fieldList),
    filters: where,
    orders: orderBy,
    page,
  };
}

async function loadRolesInternal(
  input: selectPresentation.dataSources.selection.callbacks.query.input,
  baseFilter: Record<string, unknown>
): Promise<
  selectPresentation.dataSources.selection.callbacks.query.output['Permissions']
> {
  const { session, queryInstruction } = input;
  const { fields, filters, orders, page } = calcualteFieldsFiltersAndOrders(
    queryInstruction.Permissions,
    baseFilter
  );

  const flows = await PermissionRole.query(
    session,
    { where: filters, orderBy: orders, skip: page?.skip, take: page?.take },
    [...fields]
  );

  return flows;
}

async function loadFlowsInternal(
  input: selectPresentation.dataSources.selection.callbacks.query.input,
  baseFilter: Record<string, unknown>
): Promise<
  selectPresentation.dataSources.selection.callbacks.query.output['Permissions']
> {
  const { session, queryInstruction } = input;
  const { fields, filters, orders, page } = calcualteFieldsFiltersAndOrders(
    queryInstruction.Permissions,
    baseFilter
  );

  const flows = await PermissionFlow.query(
    session,
    { where: filters, orderBy: orders, skip: page?.skip, take: page?.take },
    [...fields]
  );

  return flows;
}

async function loadCollectionsInternal(
  input: selectPresentation.dataSources.selection.callbacks.query.input,
  baseFilter: Record<string, unknown>
): Promise<
  selectPresentation.dataSources.selection.callbacks.query.output['Permissions']
> {
  const { session, queryInstruction } = input;
  const { fields, filters, orders, page } = calcualteFieldsFiltersAndOrders(
    queryInstruction.Permissions,
    baseFilter
  );

  const flows = await PolicyGroup.query(
    session,
    { where: filters, orderBy: orders, skip: page?.skip, take: page?.take },
    [...fields]
  );

  return flows;
}

async function loadPoliciesInternal(
  input: selectPresentation.dataSources.selection.callbacks.query.input,
  baseFilter: Record<string, unknown>
): Promise<
  selectPresentation.dataSources.selection.callbacks.query.output['Permissions']
> {
  const { session, queryInstruction } = input;
  const { fields, filters, orders, page } = calcualteFieldsFiltersAndOrders(
    queryInstruction.Permissions,
    baseFilter
  );

  const flows = await PolicyGroup.query(
    session,
    { where: filters, orderBy: orders, skip: page?.skip, take: page?.take },
    [...fields]
  );

  return flows;
}
