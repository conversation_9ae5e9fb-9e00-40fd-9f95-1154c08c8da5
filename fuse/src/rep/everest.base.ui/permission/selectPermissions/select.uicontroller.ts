// @i18n:selectPermissions
import type { selectPresentationUI } from '@pkg/everest.base.ui/types/presentations/permission/selectPermissions/select.ui';
import type { MessageSpecification } from '@pkg/everest.base/public/standard/ui/presentation/index';
import {
  getPresentationActionErrorMessage,
  withFeedback,
} from '@pkg/everest.base/public/standard/ui/presentation/index';

type Context = selectPresentationUI.context & {
  data: selectPresentationUI.context['data'] & {
    state?: {
      mode?: ModeType;
    };
  };
};
type ModeType = 'roles' | 'flows' | 'collections' | 'policies';
type RowDataType = {
  rowData?: selectPresentationUI.dataSets.permissions.instance;
};

const titleMap: Record<ModeType, string> = {
  roles: '{{selectPermissions.title.roles}}',
  flows: '{{selectPermissions.title.flows}}',
  collections: '{{selectPermissions.title.collections}}',
  policies: '{{selectPermissions.title.policies}}',
};

export function getTitle({ data }: Context): string {
  return titleMap[_getMode(data) ?? ''];
}

export function isRowSelectionDisabled(
  _context: Context,
  row?: RowDataType
): boolean {
  return row?.rowData?.isSelectable !== true;
}

export function getRowBackgroundMatcher(
  _context: Context,
  row?: RowDataType
): string | undefined {
  return row?.rowData?.isSelectable === true ? undefined : 'true-grey';
}

export async function select(context: Context) {
  const selectedPermissions = await withFeedback(
    context,
    async () => context.presentationClient.select(),
    {
      onError: {
        toast: (error: unknown): MessageSpecification => ({
          title: '{{permissionSelection.error.title}}',
          message: getPresentationActionErrorMessage(error),
        }),
      },
    }
  );

  context.helpers.currentModalSubmitCallback(selectedPermissions);
  context.helpers.closeModal();
}

export function cancel({ helpers }: Context) {
  helpers.closeModal();
}

function _getMode(data: Context['data']): ModeType | undefined {
  return data?.state?.mode;
}
