// @i18n:selectPermissions
package everest.base.ui

template presentation select {

  state {
    packageName: PackageName
  }

  object data-source selection with (packageName=state.packageName) {
    shape {
      includeAppserver (editable: true, label: '{{permissionSelection.label.includeAppserverPackage}}'): TrueFalse
      onlyCompatible (editable: true, label: '{{permissionSelection.label.onlyCompatiblePackages}}'): TrueFalse
      children Permissions {
        id (hidden: true): field<everest.appserver::permission:PermissionRole.id>
        uuid (hidden: true): field<everest.appserver::permission:PermissionRole.uuid>
        isSelectable (visible: false, filterable: false): TrueFalse
        name: field<everest.appserver::permission:PermissionRole.name>
        `description`: field<everest.appserver::permission:PermissionRole.`description`>
        `package` (visible: true): field<everest.appserver::permission:PermissionRole.`package`>
        active (hidden: true): field<everest.appserver::permission:PermissionRole.active>
        dynamic-fields
      }
    }
    modifications {
      on root support update
    }
    routine select {
      inputs {
        selected: array<object<{
          `package`: PackageName
          uuid: UUID
        }>>
      }
      outputs {
        selected: array<object<{
          `package`: PackageName
          uuid: UUID
        }>>
      }
      properties {
        side-effects false
      }
    }
  }

  struct selection {
    data selection
    fields *
  }

  data-set permissions {
    component Table {
      pagination = true
    }

    data selection.Permissions
    fields *
  }

  delegate action select to data-source<selection>.select {
    inputs {
      selected = '$permissions[selected].{uuid,package}$'
    }
  }

  mode roles {
    on all-data-sets allow view
    on selection allow change
    allow actions select
  }

  mode flows {
    on all-data-sets allow view
    on selection allow change
    allow actions select
  }

  mode collections {
    on all-data-sets allow view
    on selection allow change
    allow actions select
  }

  mode policies {
    on all-data-sets allow view
    on selection allow change
    allow actions select
  }
}