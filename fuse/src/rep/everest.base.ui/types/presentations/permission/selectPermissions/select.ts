/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

// ******************************************************************************
// Generated Everest Presentation Types
// ******************************************************************************

import type {
  ActionValidationResult,
  COMPONENT,
  CONFIGURATIONS,
  DATA,
  DynamicFieldsMetadata,
  GetElementType,
  DynamicFieldDefinition,
  DATA_SETS,
  DYNAMIC_FIELDS,
  FieldInstanceMetadata,
  FieldLevelMetadata,
  IDENTIFIER,
  ISession,
  METADATA,
  QueryInput,
  QueryReason,
  RoutineDeterminationResult,
  RoutineValidationContext,
  RoutineValidationResult,
  TableComponent,
  ValidateReason
} from '@everestsystems/content-core';

import type { EvstPackageName as everest_appserver_primitive_metadata_PackageName } from "@pkg/everest.appserver/types/primitives/metadata/PackageName";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { PermissionRole as everest_appserver_model_node_permission_PermissionRole } from "@pkg/everest.appserver/types/permission/PermissionRole";
import type { EvstUUID as everest_appserver_primitive_UUID } from "@pkg/everest.appserver/types/primitives/UUID";

export namespace selectPresentation {
  export type mode = 'roles' | 'flows' | 'collections' | 'policies';

  export namespace dataSources {
    export namespace selection {
      export type levels = {
        '': {
          includeAppserver?: everest_appserver_primitive_TrueFalse | undefined | null;
          onlyCompatible?: everest_appserver_primitive_TrueFalse | undefined | null;
        };
        Permissions: {
          active?: everest_appserver_model_node_permission_PermissionRole.PermissionRole["active"] | undefined | null;
          description?: everest_appserver_model_node_permission_PermissionRole.PermissionRole["description"] | undefined | null;
          id?: everest_appserver_model_node_permission_PermissionRole.PermissionRole["id"] | undefined | null;
          isSelectable?: everest_appserver_primitive_TrueFalse | undefined | null;
          name?: everest_appserver_model_node_permission_PermissionRole.PermissionRole["name"] | undefined | null;
          package?: everest_appserver_model_node_permission_PermissionRole.PermissionRole["package"] | undefined | null;
          uuid?: everest_appserver_model_node_permission_PermissionRole.PermissionRole["uuid"] | undefined | null;
        } & Record<string, unknown>;
      };

      export type instanceMetadata = {
        '': {
          includeAppserver?: FieldInstanceMetadata | undefined;
          onlyCompatible?: FieldInstanceMetadata | undefined;
        };
        Permissions: {
          active?: FieldInstanceMetadata | undefined;
          description?: FieldInstanceMetadata | undefined;
          id?: FieldInstanceMetadata | undefined;
          isSelectable?: FieldInstanceMetadata | undefined;
          name?: FieldInstanceMetadata | undefined;
          package?: FieldInstanceMetadata | undefined;
          uuid?: FieldInstanceMetadata | undefined;
        } & Record<string, FieldInstanceMetadata | undefined>;
      };

      export type levelMetadata = {
        '': {
          includeAppserver?: FieldLevelMetadata | undefined;
          onlyCompatible?: FieldLevelMetadata | undefined;
        };
        Permissions: {
          [DYNAMIC_FIELDS]?: Record<Exclude<string, 'id' | 'uuid' | 'isSelectable' | 'name' | 'description' | 'package' | 'active'>, DynamicFieldDefinition> | undefined;
          active?: FieldLevelMetadata | undefined;
          description?: FieldLevelMetadata | undefined;
          id?: FieldLevelMetadata | undefined;
          isSelectable?: FieldLevelMetadata | undefined;
          name?: FieldLevelMetadata | undefined;
          package?: FieldLevelMetadata | undefined;
          uuid?: FieldLevelMetadata | undefined;
        };
      };

      export type levelConfigurations = {
        '': never;
        Permissions: {
          [DATA_SETS]?: {        
            permissions?: {
              [COMPONENT]?: TableComponent.ComponentConfiguration | undefined;
              active?: TableComponent.FieldConfiguration | undefined;
              description?: TableComponent.FieldConfiguration | undefined;
              id?: TableComponent.FieldConfiguration | undefined;
              isSelectable?: TableComponent.FieldConfiguration | undefined;
              name?: TableComponent.FieldConfiguration | undefined;
              package?: TableComponent.FieldConfiguration | undefined;
              uuid?: TableComponent.FieldConfiguration | undefined;
            } & Record<string, TableComponent.FieldConfiguration | undefined>;
          };
        };
      };

      export type parameters = {
        packageName: everest_appserver_primitive_metadata_PackageName | undefined;
      };

      export namespace callbacks {
        export namespace setUp {
          export type input = {
            session: ISession;
            mode: mode;
            parameters: parameters;
          };

          export type output = void;
        }

        export namespace query {
          export type queryParameters = never;

          export type queryMetadata = levelMetadata[''] & {
            Permissions?: levelMetadata['Permissions'];
          };

          export type queryConfigurations = {
            Permissions?: levelConfigurations['Permissions'];
          };

          export type queryData = levels[''] & {
            Permissions?: (levels['Permissions'] & {
              [IDENTIFIER]?: number | string;
              [METADATA]?: instanceMetadata['Permissions'] | undefined;
            })[];
            [METADATA]?: instanceMetadata[''] | undefined;
          };

          export type input = {
            session: ISession;
            mode: mode;
            parameters: parameters;
            queryReason: QueryReason;
            queryInput: QueryInput;
            queryInstruction: {
              fields: ReadonlySet<keyof levels['']> | undefined;
              Permissions?: {
                fields: ReadonlySet<keyof levels['Permissions']> | undefined;
                filters: Record<keyof levels['Permissions'], unknown> | undefined;
                orders: ReadonlyArray<{
                  field: keyof levels['Permissions'];
                  ordering: 'asc' | 'desc';
                }> | undefined;
                page: {
                  skip: number;
                  take: number;
                } | undefined;
              };
            };
          };

          export type output = queryData;

          export type combinedOutput = queryData | {
            [DATA]: queryData;
            [METADATA]?: queryMetadata | undefined;
            [CONFIGURATIONS]?: queryConfigurations | undefined;
            [key: string]: never;
          };
        }

        export namespace update {
          export type input = {
            session: ISession;
            mode: mode;
            parameters: parameters;
            fieldName: keyof levels[''];
            oldFieldValue: unknown;
            newFieldValue: unknown;
          };

          export type output = void;
        }

        export namespace getDynamicFieldsMetadata_Permissions {
          export type input = {
            session: ISession;
            mode: mode;
            parameters: parameters;
          };

          export type output = DynamicFieldsMetadata;
        }
      }

      export namespace routines {
        export namespace select {
          export type input = {
            selected: {
              package: everest_appserver_primitive_metadata_PackageName;
              uuid: everest_appserver_primitive_UUID;
            }[];
          };

          export type output = {
            selected: {
              package: everest_appserver_primitive_metadata_PackageName;
              uuid: everest_appserver_primitive_UUID;
            }[];
          };

          export type determineInput = {
            session: ISession;
            mode: mode;
            parameters: parameters;
            input: Pick<input, 'selected'>;
          };

          export type determineOutput = RoutineDeterminationResult;

          export type validateInput = {
            session: ISession;
            mode: mode;
            parameters: parameters;
            validateReason: ValidateReason;
            context: RoutineValidationContext;
            input: input;
          };

          export type validateOutput = RoutineValidationResult;

          export type executeInput = {
            session: ISession;
            mode: mode;
            parameters: parameters;
            input: input;
          };

          export type executeOutput = output;
        }
      }

      export interface implementation {
        setUp?(input: callbacks.setUp.input): Promise<callbacks.setUp.output>;

        query(input: callbacks.query.input): Promise<callbacks.query.combinedOutput>;

        update(input: callbacks.update.input): Promise<callbacks.update.output>;

        /**
         * @deprecated Use the query method to drive the dynamic fields
         */
        getDynamicFieldsMetadata_Permissions?(input: callbacks.getDynamicFieldsMetadata_Permissions.input): Promise<callbacks.getDynamicFieldsMetadata_Permissions.output>;

        determine_select?(input: routines.select.determineInput): Promise<routines.select.determineOutput>;

        validate_select?(input: routines.select.validateInput): Promise<routines.select.validateOutput>;

        execute_select(input: routines.select.executeInput): Promise<routines.select.executeOutput>;
      }
    }
  }

  export type implementation = {
    selection(): dataSources.selection.implementation;
  };
}
