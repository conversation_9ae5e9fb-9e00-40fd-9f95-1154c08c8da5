/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

// ******************************************************************************
// Generated Everest Presentation UI Types
// ******************************************************************************

import type {
  GetElementType,
  UIExecutionContext
} from '@everestsystems/content-core';

import type { PermissionRole as everest_appserver_model_node_permission_PermissionRole } from "@pkg/everest.appserver/types/permission/PermissionRole";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { EvstPackageName as everest_appserver_primitive_metadata_PackageName } from "@pkg/everest.appserver/types/primitives/metadata/PackageName";
import type { EvstUUID as everest_appserver_primitive_UUID } from "@pkg/everest.appserver/types/primitives/UUID";

export namespace selectPresentationUI {
  export namespace dataSets {
    export namespace permissions {
      export type instance = {
        active?: everest_appserver_model_node_permission_PermissionRole.PermissionRole["active"] | undefined | null;
        description?: everest_appserver_model_node_permission_PermissionRole.PermissionRole["description"] | undefined | null;
        id?: everest_appserver_model_node_permission_PermissionRole.PermissionRole["id"] | undefined | null;
        isSelectable?: everest_appserver_primitive_TrueFalse | undefined | null;
        name?: everest_appserver_model_node_permission_PermissionRole.PermissionRole["name"] | undefined | null;
        package?: everest_appserver_model_node_permission_PermissionRole.PermissionRole["package"] | undefined | null;
        uuid?: everest_appserver_model_node_permission_PermissionRole.PermissionRole["uuid"] | undefined | null;
        /** Unique UI identifier of the node instance */
        _nodeReference: string;
      } & Record<string, unknown>;

      export type data = instance[];
    }

    export namespace selection {
      export type instance = {
        includeAppserver?: everest_appserver_primitive_TrueFalse | undefined | null;
        onlyCompatible?: everest_appserver_primitive_TrueFalse | undefined | null;
        /** Unique UI identifier of the node instance */
        _nodeReference: string;
      };

      export type data = instance;
    }
  }

  export namespace actions {
    export namespace select {
      export type input = never;
      export type output = {
        selected: {
          package: everest_appserver_primitive_metadata_PackageName;
          uuid: everest_appserver_primitive_UUID;
        }[];
      };
    }
  }

  export type context = UIExecutionContext.BasicExecutionContext & {
    data: {
      permissions: dataSets.permissions.data;
      selection: dataSets.selection.data;
    };
    state: Record<string, unknown>;
    presentationClient: {
      select: () => Promise<actions.select.output>;
    };
  }
}
