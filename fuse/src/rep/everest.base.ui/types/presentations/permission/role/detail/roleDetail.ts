/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

// ******************************************************************************
// Generated Everest Presentation Types
// ******************************************************************************

import type {
  ActionValidationResult,
  COMPONENT,
  CONFIGURATIONS,
  DATA,
  DynamicFieldsMetadata,
  GetElementType,
  DynamicFieldDefinition,
  DATA_SETS,
  DYNAMIC_FIELDS,
  FieldInstanceMetadata,
  FieldLevelMetadata,
  IDENTIFIER,
  ISession,
  METADATA,
  QueryInput,
  QueryReason,
  RoutineDeterminationResult,
  RoutineValidationContext,
  RoutineValidationResult,
  TableComponent,
  ValidateReason
} from '@everestsystems/content-core';

import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { PermissionRole as everest_appserver_model_node_permission_PermissionRole } from "@pkg/everest.appserver/types/permission/PermissionRole";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { PolicyGroup as everest_appserver_model_node_permission_PolicyGroup } from "@pkg/everest.appserver/types/permission/PolicyGroup";
import type { PermissionRoleAssignment as everest_appserver_model_node_permission_PermissionRoleAssignment } from "@pkg/everest.appserver/types/permission/PermissionRoleAssignment";
import type { PermissionFlow as everest_appserver_model_node_permission_PermissionFlow } from "@pkg/everest.appserver/types/permission/PermissionFlow";
import type { Policy as everest_appserver_model_node_permission_Policy } from "@pkg/everest.appserver/types/permission/Policy";
import type { EvstPackageName as everest_appserver_primitive_metadata_PackageName } from "@pkg/everest.appserver/types/primitives/metadata/PackageName";

export namespace roleDetailPresentation {
  export type mode = 'view' | 'edit';

  export namespace dataSources {
    export namespace role {
      export type levels = {
        '': {
          active?: everest_appserver_model_node_permission_PermissionRole.PermissionRole["active"] | undefined | null;
          collectionCount?: everest_appserver_primitive_Number | undefined | null;
          deprecation?: everest_appserver_model_node_permission_PermissionRole.PermissionRole["deprecation"] | undefined | null;
          description?: everest_appserver_model_node_permission_PermissionRole.PermissionRole["description"] | undefined | null;
          flowCount?: everest_appserver_primitive_Number | undefined | null;
          generation?: everest_appserver_model_node_permission_PermissionRole.PermissionRole["generation"] | undefined | null;
          hasCollections?: everest_appserver_primitive_TrueFalse | undefined | null;
          hasPolicies?: everest_appserver_primitive_TrueFalse | undefined | null;
          id?: everest_appserver_model_node_permission_PermissionRole.PermissionRole["id"] | undefined | null;
          isDeprecated?: everest_appserver_primitive_TrueFalse | undefined | null;
          isGenerated?: everest_appserver_primitive_TrueFalse | undefined | null;
          name?: everest_appserver_model_node_permission_PermissionRole.PermissionRole["name"] | undefined | null;
          package?: everest_appserver_model_node_permission_PermissionRole.PermissionRole["package"] | undefined | null;
          policyCount?: everest_appserver_primitive_Number | undefined | null;
          roleType?: everest_appserver_model_node_permission_PermissionRole.PermissionRole["roleType"] | undefined | null;
          uuid?: everest_appserver_model_node_permission_PermissionRole.PermissionRole["uuid"] | undefined | null;
        };
        Collections: {
          description?: everest_appserver_model_node_permission_PolicyGroup.PolicyGroup["description"] | undefined | null;
          generatedFor?: everest_appserver_model_node_permission_PolicyGroup.PolicyGroup["generatedFor"] | undefined | null;
          id?: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["id"] | undefined | null;
          name?: everest_appserver_model_node_permission_PolicyGroup.PolicyGroup["name"] | undefined | null;
          package?: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["package"] | undefined | null;
          relatedNodePath?: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["relatedNodePath"] | undefined | null;
          relatedNodeUUID?: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["relatedNodeUUID"] | undefined | null;
          uuid?: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["uuid"] | undefined | null;
        };
        Flows: {
          description?: everest_appserver_model_node_permission_PermissionFlow.PermissionFlow["description"] | undefined | null;
          id?: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["id"] | undefined | null;
          name?: everest_appserver_model_node_permission_PermissionFlow.PermissionFlow["name"] | undefined | null;
          package?: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["package"] | undefined | null;
          relatedNodePath?: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["relatedNodePath"] | undefined | null;
          relatedNodeUUID?: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["relatedNodeUUID"] | undefined | null;
          uuid?: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["uuid"] | undefined | null;
        };
        Policies: {
          accessEffect?: everest_appserver_model_node_permission_Policy.Policy["accessEffect"] | undefined | null;
          actions?: everest_appserver_model_node_permission_Policy.Policy["actions"] | undefined | null;
          calculatedActions?: everest_appserver_model_node_permission_Policy.Policy["calculatedActions"] | undefined | null;
          description?: everest_appserver_model_node_permission_Policy.Policy["description"] | undefined | null;
          id?: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["id"] | undefined | null;
          name?: everest_appserver_model_node_permission_Policy.Policy["name"] | undefined | null;
          notActions?: everest_appserver_model_node_permission_Policy.Policy["notActions"] | undefined | null;
          package?: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["package"] | undefined | null;
          relatedNodePath?: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["relatedNodePath"] | undefined | null;
          relatedNodeUUID?: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["relatedNodeUUID"] | undefined | null;
          resources?: everest_appserver_model_node_permission_Policy.Policy["resources"] | undefined | null;
          uuid?: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["uuid"] | undefined | null;
        };
      };

      export type instanceMetadata = {
        '': {
          active?: FieldInstanceMetadata | undefined;
          collectionCount?: FieldInstanceMetadata | undefined;
          deprecation?: FieldInstanceMetadata | undefined;
          description?: FieldInstanceMetadata | undefined;
          flowCount?: FieldInstanceMetadata | undefined;
          generation?: FieldInstanceMetadata | undefined;
          hasCollections?: FieldInstanceMetadata | undefined;
          hasPolicies?: FieldInstanceMetadata | undefined;
          id?: FieldInstanceMetadata | undefined;
          isDeprecated?: FieldInstanceMetadata | undefined;
          isGenerated?: FieldInstanceMetadata | undefined;
          name?: FieldInstanceMetadata | undefined;
          package?: FieldInstanceMetadata | undefined;
          policyCount?: FieldInstanceMetadata | undefined;
          roleType?: FieldInstanceMetadata | undefined;
          uuid?: FieldInstanceMetadata | undefined;
        };
        Collections: {
          description?: FieldInstanceMetadata | undefined;
          generatedFor?: FieldInstanceMetadata | undefined;
          id?: FieldInstanceMetadata | undefined;
          name?: FieldInstanceMetadata | undefined;
          package?: FieldInstanceMetadata | undefined;
          relatedNodePath?: FieldInstanceMetadata | undefined;
          relatedNodeUUID?: FieldInstanceMetadata | undefined;
          uuid?: FieldInstanceMetadata | undefined;
        };
        Flows: {
          description?: FieldInstanceMetadata | undefined;
          id?: FieldInstanceMetadata | undefined;
          name?: FieldInstanceMetadata | undefined;
          package?: FieldInstanceMetadata | undefined;
          relatedNodePath?: FieldInstanceMetadata | undefined;
          relatedNodeUUID?: FieldInstanceMetadata | undefined;
          uuid?: FieldInstanceMetadata | undefined;
        };
        Policies: {
          accessEffect?: FieldInstanceMetadata | undefined;
          actions?: FieldInstanceMetadata | undefined;
          calculatedActions?: FieldInstanceMetadata | undefined;
          description?: FieldInstanceMetadata | undefined;
          id?: FieldInstanceMetadata | undefined;
          name?: FieldInstanceMetadata | undefined;
          notActions?: FieldInstanceMetadata | undefined;
          package?: FieldInstanceMetadata | undefined;
          relatedNodePath?: FieldInstanceMetadata | undefined;
          relatedNodeUUID?: FieldInstanceMetadata | undefined;
          resources?: FieldInstanceMetadata | undefined;
          uuid?: FieldInstanceMetadata | undefined;
        };
      };

      export type levelMetadata = {
        '': {
          active?: FieldLevelMetadata | undefined;
          collectionCount?: FieldLevelMetadata | undefined;
          deprecation?: FieldLevelMetadata | undefined;
          description?: FieldLevelMetadata | undefined;
          flowCount?: FieldLevelMetadata | undefined;
          generation?: FieldLevelMetadata | undefined;
          hasCollections?: FieldLevelMetadata | undefined;
          hasPolicies?: FieldLevelMetadata | undefined;
          id?: FieldLevelMetadata | undefined;
          isDeprecated?: FieldLevelMetadata | undefined;
          isGenerated?: FieldLevelMetadata | undefined;
          name?: FieldLevelMetadata | undefined;
          package?: FieldLevelMetadata | undefined;
          policyCount?: FieldLevelMetadata | undefined;
          roleType?: FieldLevelMetadata | undefined;
          uuid?: FieldLevelMetadata | undefined;
        };
        Collections: {
          description?: FieldLevelMetadata | undefined;
          generatedFor?: FieldLevelMetadata | undefined;
          id?: FieldLevelMetadata | undefined;
          name?: FieldLevelMetadata | undefined;
          package?: FieldLevelMetadata | undefined;
          relatedNodePath?: FieldLevelMetadata | undefined;
          relatedNodeUUID?: FieldLevelMetadata | undefined;
          uuid?: FieldLevelMetadata | undefined;
        };
        Flows: {
          description?: FieldLevelMetadata | undefined;
          id?: FieldLevelMetadata | undefined;
          name?: FieldLevelMetadata | undefined;
          package?: FieldLevelMetadata | undefined;
          relatedNodePath?: FieldLevelMetadata | undefined;
          relatedNodeUUID?: FieldLevelMetadata | undefined;
          uuid?: FieldLevelMetadata | undefined;
        };
        Policies: {
          accessEffect?: FieldLevelMetadata | undefined;
          actions?: FieldLevelMetadata | undefined;
          calculatedActions?: FieldLevelMetadata | undefined;
          description?: FieldLevelMetadata | undefined;
          id?: FieldLevelMetadata | undefined;
          name?: FieldLevelMetadata | undefined;
          notActions?: FieldLevelMetadata | undefined;
          package?: FieldLevelMetadata | undefined;
          relatedNodePath?: FieldLevelMetadata | undefined;
          relatedNodeUUID?: FieldLevelMetadata | undefined;
          resources?: FieldLevelMetadata | undefined;
          uuid?: FieldLevelMetadata | undefined;
        };
      };

      export type levelConfigurations = {
        '': never;
        Collections: {
          [DATA_SETS]?: {        
            collections?: {
              [COMPONENT]?: TableComponent.ComponentConfiguration | undefined;
              description?: TableComponent.FieldConfiguration | undefined;
              generatedFor?: TableComponent.FieldConfiguration | undefined;
              id?: TableComponent.FieldConfiguration | undefined;
              name?: TableComponent.FieldConfiguration | undefined;
              package?: TableComponent.FieldConfiguration | undefined;
              relatedNodePath?: TableComponent.FieldConfiguration | undefined;
              relatedNodeUUID?: TableComponent.FieldConfiguration | undefined;
              uuid?: TableComponent.FieldConfiguration | undefined;
            };
          };
        };
        Flows: {
          [DATA_SETS]?: {        
            flows?: {
              [COMPONENT]?: TableComponent.ComponentConfiguration | undefined;
              description?: TableComponent.FieldConfiguration | undefined;
              id?: TableComponent.FieldConfiguration | undefined;
              name?: TableComponent.FieldConfiguration | undefined;
              package?: TableComponent.FieldConfiguration | undefined;
              relatedNodePath?: TableComponent.FieldConfiguration | undefined;
              relatedNodeUUID?: TableComponent.FieldConfiguration | undefined;
              uuid?: TableComponent.FieldConfiguration | undefined;
            };
          };
        };
        Policies: {
          [DATA_SETS]?: {        
            policies?: {
              [COMPONENT]?: TableComponent.ComponentConfiguration | undefined;
              accessEffect?: TableComponent.FieldConfiguration | undefined;
              actions?: TableComponent.FieldConfiguration | undefined;
              calculatedActions?: TableComponent.FieldConfiguration | undefined;
              description?: TableComponent.FieldConfiguration | undefined;
              id?: TableComponent.FieldConfiguration | undefined;
              name?: TableComponent.FieldConfiguration | undefined;
              notActions?: TableComponent.FieldConfiguration | undefined;
              package?: TableComponent.FieldConfiguration | undefined;
              relatedNodePath?: TableComponent.FieldConfiguration | undefined;
              relatedNodeUUID?: TableComponent.FieldConfiguration | undefined;
              resources?: TableComponent.FieldConfiguration | undefined;
              uuid?: TableComponent.FieldConfiguration | undefined;
            };
          };
        };
      };

      export type parameters = {
        uuid: everest_appserver_primitive_Text | undefined;
      };

      export namespace callbacks {
        export namespace setUp {
          export type input = {
            session: ISession;
            mode: mode;
            parameters: parameters;
          };

          export type output = void;
        }

        export namespace query {
          export type queryParameters = never;

          export type queryMetadata = levelMetadata[''] & {
            Collections?: levelMetadata['Collections'];
            Flows?: levelMetadata['Flows'];
            Policies?: levelMetadata['Policies'];
          };

          export type queryConfigurations = {
            Collections?: levelConfigurations['Collections'];
            Flows?: levelConfigurations['Flows'];
            Policies?: levelConfigurations['Policies'];
          };

          export type queryData = levels[''] & {
            Collections?: (levels['Collections'] & {
              [IDENTIFIER]?: number | string;
              [METADATA]?: instanceMetadata['Collections'] | undefined;
            })[];
            Flows?: (levels['Flows'] & {
              [IDENTIFIER]?: number | string;
              [METADATA]?: instanceMetadata['Flows'] | undefined;
            })[];
            Policies?: (levels['Policies'] & {
              [IDENTIFIER]?: number | string;
              [METADATA]?: instanceMetadata['Policies'] | undefined;
            })[];
            [METADATA]?: instanceMetadata[''] | undefined;
          };

          export type input = {
            session: ISession;
            mode: mode;
            parameters: parameters;
            queryReason: QueryReason;
            queryInput: QueryInput;
            queryInstruction: {
              fields: ReadonlySet<keyof levels['']> | undefined;
              Collections?: {
                fields: ReadonlySet<keyof levels['Collections']> | undefined;
                filters: Record<keyof levels['Collections'], unknown> | undefined;
                orders: ReadonlyArray<{
                  field: keyof levels['Collections'];
                  ordering: 'asc' | 'desc';
                }> | undefined;
                page: {
                  skip: number;
                  take: number;
                } | undefined;
              };
              Flows?: {
                fields: ReadonlySet<keyof levels['Flows']> | undefined;
                filters: Record<keyof levels['Flows'], unknown> | undefined;
                orders: ReadonlyArray<{
                  field: keyof levels['Flows'];
                  ordering: 'asc' | 'desc';
                }> | undefined;
                page: {
                  skip: number;
                  take: number;
                } | undefined;
              };
              Policies?: {
                fields: ReadonlySet<keyof levels['Policies']> | undefined;
                filters: Record<keyof levels['Policies'], unknown> | undefined;
                orders: ReadonlyArray<{
                  field: keyof levels['Policies'];
                  ordering: 'asc' | 'desc';
                }> | undefined;
                page: {
                  skip: number;
                  take: number;
                } | undefined;
              };
            };
          };

          export type output = queryData;

          export type combinedOutput = queryData | {
            [DATA]: queryData;
            [METADATA]?: queryMetadata | undefined;
            [CONFIGURATIONS]?: queryConfigurations | undefined;
            [key: string]: never;
          };
        }

        export namespace update {
          export type input = {
            session: ISession;
            mode: mode;
            parameters: parameters;
            fieldName: keyof levels[''];
            oldFieldValue: unknown;
            newFieldValue: unknown;
          };

          export type output = void;
        }
      }

      export namespace routines {
        export namespace assign {
          export type input = never;

          export type output = void;

          export type determineInput = {
            session: ISession;
            mode: mode;
            parameters: parameters;
          };

          export type determineOutput = RoutineDeterminationResult;

          export type validateInput = {
            session: ISession;
            mode: mode;
            parameters: parameters;
            validateReason: ValidateReason;
            context: RoutineValidationContext;
          };

          export type validateOutput = RoutineValidationResult;

          export type executeInput = {
            session: ISession;
            mode: mode;
            parameters: parameters;
          };

          export type executeOutput = output;
        }

        export namespace assignFlows {
          export type input = {
            selected: {
              package: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["package"];
              uuid: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["uuid"];
            }[];
          };

          export type output = void;

          export type determineInput = {
            session: ISession;
            mode: mode;
            parameters: parameters;
          };

          export type determineOutput = RoutineDeterminationResult;

          export type validateInput = {
            session: ISession;
            mode: mode;
            parameters: parameters;
            validateReason: ValidateReason;
            context: RoutineValidationContext;
            input: input;
          };

          export type validateOutput = RoutineValidationResult;

          export type executeInput = {
            session: ISession;
            mode: mode;
            parameters: parameters;
            input: input;
          };

          export type executeOutput = output;
        }

        export namespace edit {
          export type input = never;

          export type output = void;

          export type determineInput = {
            session: ISession;
            mode: mode;
            parameters: parameters;
          };

          export type determineOutput = RoutineDeterminationResult;

          export type validateInput = {
            session: ISession;
            mode: mode;
            parameters: parameters;
            validateReason: ValidateReason;
            context: RoutineValidationContext;
          };

          export type validateOutput = RoutineValidationResult;

          export type executeInput = {
            session: ISession;
            mode: mode;
            parameters: parameters;
          };

          export type executeOutput = output;
        }

        export namespace exportToSeedData {
          export type input = never;

          export type output = void;

          export type determineInput = {
            session: ISession;
            mode: mode;
            parameters: parameters;
          };

          export type determineOutput = RoutineDeterminationResult;

          export type validateInput = {
            session: ISession;
            mode: mode;
            parameters: parameters;
            validateReason: ValidateReason;
            context: RoutineValidationContext;
          };

          export type validateOutput = RoutineValidationResult;

          export type executeInput = {
            session: ISession;
            mode: mode;
            parameters: parameters;
          };

          export type executeOutput = output;
        }

        export namespace move {
          export type input = {
            selected: {
              package: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["package"];
              uuid: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["uuid"];
            }[];
          };

          export type output = void;

          export type determineInput = {
            session: ISession;
            mode: mode;
            parameters: parameters;
            input: Pick<input, 'selected'>;
          };

          export type determineOutput = RoutineDeterminationResult;

          export type validateInput = {
            session: ISession;
            mode: mode;
            parameters: parameters;
            validateReason: ValidateReason;
            context: RoutineValidationContext;
            input: input;
          };

          export type validateOutput = RoutineValidationResult;

          export type executeInput = {
            session: ISession;
            mode: mode;
            parameters: parameters;
            input: input;
          };

          export type executeOutput = output;
        }

        export namespace moveFlowsToPackage {
          export type input = {
            selected: {
              package: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["package"];
              uuid: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["uuid"];
            }[];
            targetPackage: everest_appserver_primitive_metadata_PackageName;
          };

          export type output = void;

          export type determineInput = {
            session: ISession;
            mode: mode;
            parameters: parameters;
            input: Pick<input, 'selected'>;
          };

          export type determineOutput = RoutineDeterminationResult;

          export type validateInput = {
            session: ISession;
            mode: mode;
            parameters: parameters;
            validateReason: ValidateReason;
            context: RoutineValidationContext;
            input: input;
          };

          export type validateOutput = RoutineValidationResult;

          export type executeInput = {
            session: ISession;
            mode: mode;
            parameters: parameters;
            input: input;
          };

          export type executeOutput = output;
        }

        export namespace removeFlows {
          export type input = {
            selected: {
              package: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["package"];
              uuid: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["uuid"];
            }[];
          };

          export type output = void;

          export type determineInput = {
            session: ISession;
            mode: mode;
            parameters: parameters;
            input: Pick<input, 'selected'>;
          };

          export type determineOutput = RoutineDeterminationResult;

          export type validateInput = {
            session: ISession;
            mode: mode;
            parameters: parameters;
            validateReason: ValidateReason;
            context: RoutineValidationContext;
            input: input;
          };

          export type validateOutput = RoutineValidationResult;

          export type executeInput = {
            session: ISession;
            mode: mode;
            parameters: parameters;
            input: input;
          };

          export type executeOutput = output;
        }

        export namespace save {
          export type input = never;

          export type output = void;

          export type determineInput = {
            session: ISession;
            mode: mode;
            parameters: parameters;
          };

          export type determineOutput = RoutineDeterminationResult;

          export type validateInput = {
            session: ISession;
            mode: mode;
            parameters: parameters;
            validateReason: ValidateReason;
            context: RoutineValidationContext;
          };

          export type validateOutput = RoutineValidationResult;

          export type executeInput = {
            session: ISession;
            mode: mode;
            parameters: parameters;
          };

          export type executeOutput = output;
        }
      }

      export interface implementation {
        setUp?(input: callbacks.setUp.input): Promise<callbacks.setUp.output>;

        query(input: callbacks.query.input): Promise<callbacks.query.combinedOutput>;

        update(input: callbacks.update.input): Promise<callbacks.update.output>;

        determine_assign?(input: routines.assign.determineInput): Promise<routines.assign.determineOutput>;

        validate_assign?(input: routines.assign.validateInput): Promise<routines.assign.validateOutput>;

        execute_assign(input: routines.assign.executeInput): Promise<routines.assign.executeOutput>;

        determine_assignFlows?(input: routines.assignFlows.determineInput): Promise<routines.assignFlows.determineOutput>;

        validate_assignFlows?(input: routines.assignFlows.validateInput): Promise<routines.assignFlows.validateOutput>;

        execute_assignFlows(input: routines.assignFlows.executeInput): Promise<routines.assignFlows.executeOutput>;

        determine_edit?(input: routines.edit.determineInput): Promise<routines.edit.determineOutput>;

        validate_edit?(input: routines.edit.validateInput): Promise<routines.edit.validateOutput>;

        execute_edit(input: routines.edit.executeInput): Promise<routines.edit.executeOutput>;

        determine_exportToSeedData?(input: routines.exportToSeedData.determineInput): Promise<routines.exportToSeedData.determineOutput>;

        validate_exportToSeedData?(input: routines.exportToSeedData.validateInput): Promise<routines.exportToSeedData.validateOutput>;

        execute_exportToSeedData(input: routines.exportToSeedData.executeInput): Promise<routines.exportToSeedData.executeOutput>;

        determine_move?(input: routines.move.determineInput): Promise<routines.move.determineOutput>;

        validate_move?(input: routines.move.validateInput): Promise<routines.move.validateOutput>;

        execute_move(input: routines.move.executeInput): Promise<routines.move.executeOutput>;

        determine_moveFlowsToPackage?(input: routines.moveFlowsToPackage.determineInput): Promise<routines.moveFlowsToPackage.determineOutput>;

        validate_moveFlowsToPackage?(input: routines.moveFlowsToPackage.validateInput): Promise<routines.moveFlowsToPackage.validateOutput>;

        execute_moveFlowsToPackage(input: routines.moveFlowsToPackage.executeInput): Promise<routines.moveFlowsToPackage.executeOutput>;

        determine_removeFlows?(input: routines.removeFlows.determineInput): Promise<routines.removeFlows.determineOutput>;

        validate_removeFlows?(input: routines.removeFlows.validateInput): Promise<routines.removeFlows.validateOutput>;

        execute_removeFlows(input: routines.removeFlows.executeInput): Promise<routines.removeFlows.executeOutput>;

        determine_save?(input: routines.save.determineInput): Promise<routines.save.determineOutput>;

        validate_save?(input: routines.save.validateInput): Promise<routines.save.validateOutput>;

        execute_save(input: routines.save.executeInput): Promise<routines.save.executeOutput>;
      }
    }
  }

  export type implementation = {
    role(): dataSources.role.implementation;
  };
}
