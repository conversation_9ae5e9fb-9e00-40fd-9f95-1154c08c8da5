/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

// ******************************************************************************
// Generated Everest Presentation UI Types
// ******************************************************************************

import type {
  GetElementType,
  UIExecutionContext
} from '@everestsystems/content-core';

import type { PolicyGroup as everest_appserver_model_node_permission_PolicyGroup } from "@pkg/everest.appserver/types/permission/PolicyGroup";
import type { PermissionRoleAssignment as everest_appserver_model_node_permission_PermissionRoleAssignment } from "@pkg/everest.appserver/types/permission/PermissionRoleAssignment";
import type { PermissionFlow as everest_appserver_model_node_permission_PermissionFlow } from "@pkg/everest.appserver/types/permission/PermissionFlow";
import type { Policy as everest_appserver_model_node_permission_Policy } from "@pkg/everest.appserver/types/permission/Policy";
import type { PermissionRole as everest_appserver_model_node_permission_PermissionRole } from "@pkg/everest.appserver/types/permission/PermissionRole";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";

export namespace roleDetailPresentationUI {
  export namespace dataSets {
    export namespace collections {
      export type instance = {
        description?: everest_appserver_model_node_permission_PolicyGroup.PolicyGroup["description"] | undefined | null;
        generatedFor?: everest_appserver_model_node_permission_PolicyGroup.PolicyGroup["generatedFor"] | undefined | null;
        id?: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["id"] | undefined | null;
        name?: everest_appserver_model_node_permission_PolicyGroup.PolicyGroup["name"] | undefined | null;
        package?: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["package"] | undefined | null;
        relatedNodePath?: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["relatedNodePath"] | undefined | null;
        relatedNodeUUID?: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["relatedNodeUUID"] | undefined | null;
        uuid?: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["uuid"] | undefined | null;
        /** Unique UI identifier of the node instance */
        _nodeReference: string;
      };

      export type data = instance[];
    }

    export namespace flows {
      export type instance = {
        description?: everest_appserver_model_node_permission_PermissionFlow.PermissionFlow["description"] | undefined | null;
        id?: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["id"] | undefined | null;
        name?: everest_appserver_model_node_permission_PermissionFlow.PermissionFlow["name"] | undefined | null;
        package?: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["package"] | undefined | null;
        relatedNodePath?: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["relatedNodePath"] | undefined | null;
        relatedNodeUUID?: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["relatedNodeUUID"] | undefined | null;
        uuid?: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["uuid"] | undefined | null;
        /** Unique UI identifier of the node instance */
        _nodeReference: string;
      };

      export type data = instance[];
    }

    export namespace policies {
      export type instance = {
        accessEffect?: everest_appserver_model_node_permission_Policy.Policy["accessEffect"] | undefined | null;
        actions?: everest_appserver_model_node_permission_Policy.Policy["actions"] | undefined | null;
        calculatedActions?: everest_appserver_model_node_permission_Policy.Policy["calculatedActions"] | undefined | null;
        description?: everest_appserver_model_node_permission_Policy.Policy["description"] | undefined | null;
        id?: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["id"] | undefined | null;
        name?: everest_appserver_model_node_permission_Policy.Policy["name"] | undefined | null;
        notActions?: everest_appserver_model_node_permission_Policy.Policy["notActions"] | undefined | null;
        package?: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["package"] | undefined | null;
        relatedNodePath?: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["relatedNodePath"] | undefined | null;
        relatedNodeUUID?: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["relatedNodeUUID"] | undefined | null;
        resources?: everest_appserver_model_node_permission_Policy.Policy["resources"] | undefined | null;
        uuid?: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["uuid"] | undefined | null;
        /** Unique UI identifier of the node instance */
        _nodeReference: string;
      };

      export type data = instance[];
    }

    export namespace role {
      export type instance = {
        active?: everest_appserver_model_node_permission_PermissionRole.PermissionRole["active"] | undefined | null;
        collectionCount?: everest_appserver_primitive_Number | undefined | null;
        deprecation?: everest_appserver_model_node_permission_PermissionRole.PermissionRole["deprecation"] | undefined | null;
        description?: everest_appserver_model_node_permission_PermissionRole.PermissionRole["description"] | undefined | null;
        flowCount?: everest_appserver_primitive_Number | undefined | null;
        generation?: everest_appserver_model_node_permission_PermissionRole.PermissionRole["generation"] | undefined | null;
        id?: everest_appserver_model_node_permission_PermissionRole.PermissionRole["id"] | undefined | null;
        isDeprecated?: everest_appserver_primitive_TrueFalse | undefined | null;
        isGenerated?: everest_appserver_primitive_TrueFalse | undefined | null;
        name?: everest_appserver_model_node_permission_PermissionRole.PermissionRole["name"] | undefined | null;
        package?: everest_appserver_model_node_permission_PermissionRole.PermissionRole["package"] | undefined | null;
        policyCount?: everest_appserver_primitive_Number | undefined | null;
        roleType?: everest_appserver_model_node_permission_PermissionRole.PermissionRole["roleType"] | undefined | null;
        uuid?: everest_appserver_model_node_permission_PermissionRole.PermissionRole["uuid"] | undefined | null;
        /** Unique UI identifier of the node instance */
        _nodeReference: string;
      };

      export type data = instance;
    }
  }

  export namespace actions {
    export namespace assign {
      export type input = never;
      export type output = void;
    }

    export namespace assignCollections {
      export type input = {
        permissionUUIDs: everest_appserver_model_node_permission_PolicyGroup.PolicyGroup["uuid"][];
      };
      export type output = void;
    }

    export namespace assignFlows {
      export type input = {
        permissionUUIDs: everest_appserver_model_node_permission_PermissionFlow.PermissionFlow["uuid"][];
      };
      export type output = void;
    }

    export namespace assignPolicies {
      export type input = {
        permissionUUIDs: everest_appserver_model_node_permission_PolicyGroup.PolicyGroup["uuid"][];
      };
      export type output = void;
    }

    export namespace attach {
      export type input = never;
      export type output = void;
    }

    export namespace attachCollections {
      export type input = {
        packageName: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["package"];
        permissionUUIDs: everest_appserver_model_node_permission_PolicyGroup.PolicyGroup["uuid"][];
      };
      export type output = void;
    }

    export namespace attachFlows {
      export type input = {
        packageName: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["package"];
        permissionUUIDs: everest_appserver_model_node_permission_PermissionFlow.PermissionFlow["uuid"][];
      };
      export type output = void;
    }

    export namespace attachPolicies {
      export type input = {
        packageName: everest_appserver_model_node_permission_PermissionRoleAssignment.PermissionRoleAssignment["package"];
        permissionUUIDs: everest_appserver_model_node_permission_PolicyGroup.PolicyGroup["uuid"][];
      };
      export type output = void;
    }

    export namespace cancel {
      export type input = never;
      export type output = void;
    }

    export namespace edit {
      export type input = never;
      export type output = void;
    }

    export namespace removeCollections {
      export type input = never;
      export type output = void;
    }

    export namespace removeFlows {
      export type input = never;
      export type output = void;
    }

    export namespace removePolicies {
      export type input = never;
      export type output = void;
    }

    export namespace save {
      export type input = never;
      export type output = void;
    }
  }

  export type context = UIExecutionContext.BasicExecutionContext & {
    data: {
      collections: dataSets.collections.data;
      flows: dataSets.flows.data;
      policies: dataSets.policies.data;
      role: dataSets.role.data;
    };
    state: Record<string, unknown>;
    presentationClient: {
      assign: () => Promise<actions.assign.output>;
      assignCollections: (input: actions.assignCollections.input) => Promise<actions.assignCollections.output>;
      assignFlows: (input: actions.assignFlows.input) => Promise<actions.assignFlows.output>;
      assignPolicies: (input: actions.assignPolicies.input) => Promise<actions.assignPolicies.output>;
      attach: () => Promise<actions.attach.output>;
      attachCollections: (input: actions.attachCollections.input) => Promise<actions.attachCollections.output>;
      attachFlows: (input: actions.attachFlows.input) => Promise<actions.attachFlows.output>;
      attachPolicies: (input: actions.attachPolicies.input) => Promise<actions.attachPolicies.output>;
      cancel: () => Promise<actions.cancel.output>;
      edit: () => Promise<actions.edit.output>;
      removeCollections: () => Promise<actions.removeCollections.output>;
      removeFlows: () => Promise<actions.removeFlows.output>;
      removePolicies: () => Promise<actions.removePolicies.output>;
      save: () => Promise<actions.save.output>;
    };
  }
}
