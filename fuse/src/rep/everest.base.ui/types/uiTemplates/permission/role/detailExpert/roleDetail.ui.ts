/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable no-trailing-spaces */
/* eslint-disable no-multiple-empty-lines */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// ******************************************************************************
// Generated Everest Ui Template Types
// ******************************************************************************
import { ReadReturnTypeWithAssociations, UIExecutionContext } from '@everestsystems/content-core';

type BasicExecutionContext = UIExecutionContext.BasicExecutionContext;
type InnerPromiseType<T> = T extends Promise<infer R> ? R : T;
type UIModelNodeListType<T> =  UIExecutionContext.UIModelNodeListType<T>
type ListElementType<T> = T extends (infer R)[] ? R : unknown;

export namespace RoleDetailUiTemplate {


export type RoleDetailData = {};

export type RoleDetailContext = BasicExecutionContext
  & {
    data: RoleDetailData;
    state: {
        'param'?: unknown;
    } & Record<string, unknown>;
  }
  ;


type OverrideDataPayload = {
  [K in keyof RoleDetailData]: RoleDetailData[K] extends UIModelNodeListType<any>
    ? UIModelNodeListType<any>
    : any;
};

/** 
 * This Helper Type allows you to override the type of the data properties or extend them.
  * if the node you are overriding is of type list in the ui-template then
  * you need to wrap your type in {UIExecutionContext.UIModelNodeListType<T>} instead of normal array or list
  */
export type OverrideRoleDetailData<
  Payload extends Record<string, unknown> & Partial<OverrideDataPayload>
> = Omit<RoleDetailContext, 'data'> & {
  data: {
    [K in keyof RoleDetailData | keyof Payload]: K extends keyof Payload
      ? Payload[K]
      : K extends keyof RoleDetailData
      ? RoleDetailData[K]
      : unknown;
  };
};


}
 /** @deprecated use `RoleDetailUiTemplate.RoleDetailData` instead. */ export type RoleDetailData = RoleDetailUiTemplate.RoleDetailData;
 /** @deprecated use `RoleDetailUiTemplate.RoleDetailContext` instead. */ export type RoleDetailContext = RoleDetailUiTemplate.RoleDetailContext;

type OverrideDataPayload = {
  [K in keyof RoleDetailUiTemplate.RoleDetailData]: RoleDetailUiTemplate.RoleDetailData[K] extends UIModelNodeListType<any>
    ? UIModelNodeListType<any>
    : any;
};

/** @deprecated use `RoleDetailUiTemplate.OverrideRoleDetailData` instead.
 * This Helper Type allows you to override the type of the data properties or extend them.
  * if the node you are overriding is of type list in the ui-template then
  * you need to wrap your type in {UIExecutionContext.UIModelNodeListType<T>} instead of normal array or list
  */
export type OverrideRoleDetailData<
  Payload extends Record<string, unknown> & Partial<OverrideDataPayload>
> = Omit<RoleDetailUiTemplate.RoleDetailContext, 'data'> & {
  data: {
    [K in keyof RoleDetailUiTemplate.RoleDetailData | keyof Payload]: K extends keyof Payload
      ? Payload[K]
      : K extends keyof RoleDetailUiTemplate.RoleDetailData
      ? RoleDetailUiTemplate.RoleDetailData[K]
      : unknown;
  };
};
