/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable no-trailing-spaces */
/* eslint-disable no-multiple-empty-lines */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// ******************************************************************************
// Generated Everest Ui Template Types
// ******************************************************************************
import { ReadReturnTypeWithAssociations, UIExecutionContext } from '@everestsystems/content-core';

type BasicExecutionContext = UIExecutionContext.BasicExecutionContext;
type InnerPromiseType<T> = T extends Promise<infer R> ? R : T;
type UIModelNodeListType<T> =  UIExecutionContext.UIModelNodeListType<T>
type ListElementType<T> = T extends (infer R)[] ? R : unknown;

export namespace SelectUiTemplate {


export type SelectData = {};

export type SelectContext = BasicExecutionContext
  & {
    data: SelectData;
    state: {
        'mode'?: unknown;
        'packageName'?: unknown;
    } & Record<string, unknown>;
  }
  ;


type OverrideDataPayload = {
  [K in keyof SelectData]: SelectData[K] extends UIModelNodeListType<any>
    ? UIModelNodeListType<any>
    : any;
};

/** 
 * This Helper Type allows you to override the type of the data properties or extend them.
  * if the node you are overriding is of type list in the ui-template then
  * you need to wrap your type in {UIExecutionContext.UIModelNodeListType<T>} instead of normal array or list
  */
export type OverrideSelectData<
  Payload extends Record<string, unknown> & Partial<OverrideDataPayload>
> = Omit<SelectContext, 'data'> & {
  data: {
    [K in keyof SelectData | keyof Payload]: K extends keyof Payload
      ? Payload[K]
      : K extends keyof SelectData
      ? SelectData[K]
      : unknown;
  };
};


}
 /** @deprecated use `SelectUiTemplate.SelectData` instead. */ export type SelectData = SelectUiTemplate.SelectData;
 /** @deprecated use `SelectUiTemplate.SelectContext` instead. */ export type SelectContext = SelectUiTemplate.SelectContext;

type OverrideDataPayload = {
  [K in keyof SelectUiTemplate.SelectData]: SelectUiTemplate.SelectData[K] extends UIModelNodeListType<any>
    ? UIModelNodeListType<any>
    : any;
};

/** @deprecated use `SelectUiTemplate.OverrideSelectData` instead.
 * This Helper Type allows you to override the type of the data properties or extend them.
  * if the node you are overriding is of type list in the ui-template then
  * you need to wrap your type in {UIExecutionContext.UIModelNodeListType<T>} instead of normal array or list
  */
export type OverrideSelectData<
  Payload extends Record<string, unknown> & Partial<OverrideDataPayload>
> = Omit<SelectUiTemplate.SelectContext, 'data'> & {
  data: {
    [K in keyof SelectUiTemplate.SelectData | keyof Payload]: K extends keyof Payload
      ? Payload[K]
      : K extends keyof SelectUiTemplate.SelectData
      ? SelectUiTemplate.SelectData[K]
      : unknown;
  };
};
