import type { ISession } from '@everestsystems/content-core';
import type { Account } from '@pkg/everest.fin.accounting/types/Account';
import type { PaymentTerm } from '@pkg/everest.fin.accounting/types/PaymentTerm';
import type { Vendor } from '@pkg/everest.fin.expense/types/Vendor';
import { BillProviderModel } from '@pkg/everest.fin.integration.billdotcom/utils/api/types';
import type { GetMatchingCandidatesResponse } from '@pkg/everest.fin.integration.expense/public/types';
import type { Department } from '@pkg/everest.hr.base/types/Department';
import type { Employee } from '@pkg/everest.hr.base/types/Employee';

import getAccountCandidatesV2 from './getAccountCandidatesV2';
import getDepartmentCandidatesV2 from './getDepartmentCandidatesV2';
import getEmployeeCandidatesV2 from './getEmployeeCandidatesV2';
import getPaymentTermCandidatesV2 from './getPaymentTermCandidatesV2';
import getVendorCandidatesV2 from './getVendorCandidatesV2';

export default async function getMatchingCandidatesV2(
  env: ISession,
  providerModel: BillProviderModel,
  requiredFieldsForAiMatching?: string[]
): GetMatchingCandidatesResponse {
  switch (providerModel) {
    case BillProviderModel.ChartOfAccount: {
      return getAccountCandidatesV2(
        env,
        requiredFieldsForAiMatching as Array<
          keyof Account.AccountWithAssociation
        >
      );
    }
    case BillProviderModel.Department: {
      return getDepartmentCandidatesV2(
        env,
        requiredFieldsForAiMatching as Array<
          keyof Department.DepartmentWithAssociation
        >
      );
    }
    case BillProviderModel.Employee: {
      return getEmployeeCandidatesV2(
        env,
        requiredFieldsForAiMatching as Array<
          keyof Employee.EmployeeWithAssociation
        >
      );
    }
    case BillProviderModel.Vendor: {
      return getVendorCandidatesV2(
        env,
        requiredFieldsForAiMatching as Array<keyof Vendor.VendorWithAssociation>
      );
    }
    case BillProviderModel.PaymentTerm: {
      return getPaymentTermCandidatesV2(
        env,
        requiredFieldsForAiMatching as Array<
          keyof PaymentTerm.PaymentTermWithAssociation
        >
      );
    }
    default: {
      throw new Error(`Unknown provider model: ${providerModel}`);
    }
  }
}
