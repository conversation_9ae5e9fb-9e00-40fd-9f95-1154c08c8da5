import type { ISession } from '@everestsystems/content-core';
import type { MatchingPrimitiveTypes } from '@pkg/everest.fin.integration.base.ui/public/types/MatchingTypes';
import { BillProviderModel } from '@pkg/everest.fin.integration.billdotcom/utils/api/types';

import getAccountPrimitiveTypesV2 from './getAccountPrimitiveTypesV2';
import getDepartmentPrimitiveTypesV2 from './getDepartmentPrimitiveTypesV2';
import getEmployeePrimitiveTypesV2 from './getEmployeePrimitiveTypesV2';
import getPaymentTermPrimitiveTypesV2 from './getPaymentTermPrimitiveTypesV2';
import getVendorPrimitiveTypesV2 from './getVendorPrimitiveTypesV2';

export default function getMatchingPrimitiveTypesV2(
  env: ISession,
  providerModel: BillProviderModel
): MatchingPrimitiveTypes {
  switch (providerModel) {
    case BillProviderModel.ChartOfAccount: {
      return getAccountPrimitiveTypesV2(env);
    }
    case BillProviderModel.Department: {
      return getDepartmentPrimitiveTypesV2(env);
    }
    case BillProviderModel.Employee: {
      return getEmployeePrimitiveTypesV2(env);
    }
    case BillProviderModel.Vendor: {
      return getVendorPrimitiveTypesV2(env);
    }
    case BillProviderModel.PaymentTerm: {
      return getPaymentTermPrimitiveTypesV2(env);
    }
    default: {
      throw new Error(`Unknown provider model: ${providerModel}`);
    }
  }
}
