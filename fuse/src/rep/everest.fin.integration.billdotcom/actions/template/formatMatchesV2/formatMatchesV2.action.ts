import type { ISession } from '@everestsystems/content-core';
import type { FormattedMatch } from '@pkg/everest.fin.integration.base.ui/public/types/MatchingTypes';
import type { Staging } from '@pkg/everest.fin.integration.base/types/Staging';
import { BillProviderModel } from '@pkg/everest.fin.integration.billdotcom/utils/api/types';

import formatAccountsV2 from './formatAccountsV2';
import formatDepartmentsV2 from './formatDepartmentsV2';
import formatEmployeesV2 from './formatEmployeesV2';
import formatPaymentTermsV2 from './formatPaymentTermsV2';
import formatVendorsV2 from './formatVendorsV2';

export default function formatMatchesV2(
  _env: ISession,
  providerModel: BillProviderModel,
  matches: Staging.Staging[]
): FormattedMatch[] {
  switch (providerModel) {
    case BillProviderModel.ChartOfAccount: {
      return formatAccountsV2(matches);
    }
    case BillProviderModel.Department: {
      return formatDepartmentsV2(matches);
    }
    case BillProviderModel.Employee: {
      return formatEmployeesV2(matches);
    }
    case BillProviderModel.Vendor: {
      return formatVendorsV2(matches);
    }
    case BillProviderModel.PaymentTerm: {
      return formatPaymentTermsV2(matches);
    }
  }
}
