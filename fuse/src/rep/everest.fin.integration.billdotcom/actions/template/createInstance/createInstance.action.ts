import type { ISession } from '@everestsystems/content-core';
import type { LoadOrMatchFunctionReturnType } from '@pkg/everest.fin.integration.base/public/types/etlTypes';
import createPayment from '@pkg/everest.fin.integration.billdotcom/actions/template/createInstance/createPayment';
import createVendorCreditBill from '@pkg/everest.fin.integration.billdotcom/actions/template/createInstance/createVendorCreditBill';
import { BillProviderModel } from '@pkg/everest.fin.integration.billdotcom/utils/api/types';
import type {
  TransformedPaymentData,
  TransformedVendorCreditBillData,
} from '@pkg/everest.fin.integration.billdotcom/utils/transformedData';

export default async function createInstance(
  env: ISession,
  providerModel: BillProviderModel,
  transformedData: Record<string, unknown>,
  everestId?: number
): LoadOrMatchFunctionReturnType {
  switch (providerModel) {
    case BillProviderModel.SentPay: {
      return createPayment(
        env,
        transformedData as TransformedPaymentData,
        everestId
      );
    }
    case BillProviderModel.BillCredit: {
      return createVendorCreditBill(
        env,
        transformedData as TransformedVendorCreditBillData,
        everestId
      );
    }
  }
}
