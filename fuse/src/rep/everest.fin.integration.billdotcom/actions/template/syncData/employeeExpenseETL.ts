import type { ISession } from '@everestsystems/content-core';
import type { EvstUnknownObject } from '@pkg/everest.appserver/types/primitives/UnknownObject';
import { EvstMigrationIntegrationContext } from '@pkg/everest.base/types/enums/MigrationIntegrationContext';
import executeETL from '@pkg/everest.fin.integration.base/public/executeETL2';
import generateExtractionName from '@pkg/everest.fin.integration.base/public/template/generateExtractionName';
import updateDelta from '@pkg/everest.fin.integration.base/public/template/updateDelta';
import type { EvstLoadOrMatchFunctionReturnType } from '@pkg/everest.fin.integration.base/types/composites/LoadOrMatchFunctionReturnType';
import { EvstExtractionProviders } from '@pkg/everest.fin.integration.base/types/enums/ExtractionProviders';
import { EvstStagingStatus } from '@pkg/everest.fin.integration.base/types/enums/StagingStatus';
import { LoadOrMatchFunction } from '@pkg/everest.fin.integration.base/types/LoadOrMatchFunction';
import { Staging } from '@pkg/everest.fin.integration.base/types/Staging';
import {
  BILLDOTCOM_PACKAGE,
  DELTA_VARIABLE,
} from '@pkg/everest.fin.integration.billdotcom/public/config';
import { BillDotComSettings } from '@pkg/everest.fin.integration.billdotcom/types/BillDotComSettings';
import type { BillLoginRequest } from '@pkg/everest.fin.integration.billdotcom/utils/api/types';
import { BillProviderModel } from '@pkg/everest.fin.integration.billdotcom/utils/api/types';
import getData from '@pkg/everest.fin.integration.billdotcom/utils/syncData/fetching/getData';
import type {
  ExpenseETLExecutionResponse,
  FetchingArgs,
  MappingArgs,
} from '@pkg/everest.fin.integration.expense/public/types';

type ExpenseReportData = {
  vendorId: string;
  invoiceNumber: string;
  isActive: string;
  approvalStatus: string;
  billDate: Date;
  localAmount: number;
  currency: string;
  description: string;
  invoiceDate: Date;
  billLineItems: {
    chartOfAccountId: string;
    invoiceDate: Date;
    description: string;
    currency: string;
    merchant: string;
  }[];
};

export default async function employeeExpenseETL(
  env: ISession,
  mapping: MappingArgs,
  fetching: FetchingArgs
): ExpenseETLExecutionResponse {
  const {
    providerModel,
    providerName,
    originalIdKey,
    everestModel,
    mappingName,
    extractionSource,
    entityId,
  } = mapping;
  const { connectorId, configValues, dataload, startDate } = fetching;

  try {
    const billSettings = await BillDotComSettings.read(env, { connectorId }, [
      'useExpensify',
      'matchWithQuickbooks',
    ]);
    if (!billSettings?.useExpensify && !billSettings?.matchWithQuickbooks) {
      return {
        errors: [],
        continueInTask: false,
      };
    }

    const { continueInTask, data, deltaValue } = await getData(
      env,
      {
        id: connectorId,
        configValues: configValues as unknown as BillLoginRequest,
        entityId,
      },
      {
        providerModel: providerModel as BillProviderModel,
        extractionSource,
        dataload,
        startDate,
      }
    );
    if (continueInTask) {
      return { continueInTask };
    }

    const employees = await getEmployees(env);
    const processedReports = processReports(
      data as ExpenseReportData[],
      employees
    );

    const matchClient = await LoadOrMatchFunction.client(env);
    await executeETL(
      env,
      {
        extractFn: async () => processedReports,
        mediationFns: [excludeInactiveReports, excludeUnapprovedReports],
        descriptionFn: (data: ExpenseReportData) => data.invoiceNumber,
        providerModel,
        providerName,
        originalIdKey,
        everestModel,
        extractionSource,
        extractionName: generateExtractionName(
          providerName,
          providerModel,
          extractionSource
        ),
        isHierarchy: false,
        retryItemsStatuses: [
          EvstStagingStatus.FAILED,
          EvstStagingStatus.PENDING,
        ],
      },
      [{ name: mappingName }],
      {
        loadFunction: async (
          transformedData: EvstUnknownObject,
          everestId?: number
        ) => {
          const result = await matchClient.matchAndLoadWrapper(
            [
              {
                transformedData: transformedData as unknown as Record<
                  string,
                  unknown
                >,
                everestId,
              },
            ],
            {
              context: EvstMigrationIntegrationContext.Integration,
            },
            'urn:evst:everest:fin/expense:composite:EmployeeExpenseTransformedData',
            'header.id',
            {
              match: true,
              create: true,
              update: true,
            }
          );
          return result?.[0] as unknown as Promise<EvstLoadOrMatchFunctionReturnType>;
        },
      }
    );

    await updateDelta(env, {
      pckage: BILLDOTCOM_PACKAGE,
      providerModel,
      extractionSource,
      deltaVariable: DELTA_VARIABLE,
      deltaValue,
    });
  } catch (error) {
    throw new Error(error.message);
  }
}

async function getEmployees(env: ISession) {
  const client = await Staging.client(env);
  return client.getStagingItemsByProviderModel(
    EvstExtractionProviders.BillDotCom,
    BillProviderModel.Employee
  );
}

function processReports(
  data: ExpenseReportData[],
  employees: Staging.Staging[]
) {
  return data
    .filter((report: ExpenseReportData) =>
      employees.some((e) => e.originalId === report.vendorId)
    )
    .map((report: ExpenseReportData) => {
      const employee = employees.find((e) => e.originalId === report.vendorId);
      report.currency = report.localAmount
        ? (employee.data as Record<'billCurrency', string>)?.billCurrency
        : 'USD';

      report.billLineItems = report.billLineItems?.map((expenseItem) => ({
        ...expenseItem,
        vendorId: report.vendorId,
        merchant: report.invoiceNumber || 'BILL Expensify',
        invoiceDate: report.invoiceDate,
        currency: report.currency,
        description: `${report.description ?? ''} ${
          expenseItem.description ? ' - '.concat(expenseItem.description) : ''
        }`.trim(),
        chartOfAccountId:
          expenseItem?.chartOfAccountId === '00000000000000000000'
            ? null
            : expenseItem.chartOfAccountId,
      }));

      return report;
    });
}

function excludeInactiveReports(data: ExpenseReportData[]) {
  return {
    data: data.filter((r) => r.isActive === '1'),
    message: 'Inactive expense report',
  };
}

function excludeUnapprovedReports(data: ExpenseReportData[]) {
  return {
    data: data.filter((r) => r.approvalStatus === '3'),
    message: 'Unapproved expense report',
  };
}
