import type { ISession } from '@everestsystems/content-core';
import type { EvstUnknownObject } from '@pkg/everest.appserver/types/primitives/UnknownObject';
import { EvstMigrationIntegrationContext } from '@pkg/everest.base/types/enums/MigrationIntegrationContext';
import executeETL from '@pkg/everest.fin.integration.base/public/executeETL2';
import generateExtractionName from '@pkg/everest.fin.integration.base/public/template/generateExtractionName';
import updateDelta from '@pkg/everest.fin.integration.base/public/template/updateDelta';
import type { EvstLoadOrMatchFunctionReturnType } from '@pkg/everest.fin.integration.base/types/composites/LoadOrMatchFunctionReturnType';
import { EvstStagingStatus } from '@pkg/everest.fin.integration.base/types/enums/StagingStatus';
import { LoadOrMatchFunction } from '@pkg/everest.fin.integration.base/types/LoadOrMatchFunction';
import {
  BILLDOTCOM_PACKAGE,
  DELTA_VARIABLE,
} from '@pkg/everest.fin.integration.billdotcom/public/config';
import { BillDotComSettings } from '@pkg/everest.fin.integration.billdotcom/types/BillDotComSettings';
import type {
  BillLoginRequest,
  BillProviderModel,
} from '@pkg/everest.fin.integration.billdotcom/utils/api/types';
import getData from '@pkg/everest.fin.integration.billdotcom/utils/syncData/fetching/getData';
import type {
  ExpenseETLExecutionResponse,
  FetchingArgs,
  MappingArgs,
} from '@pkg/everest.fin.integration.expense/public/types';

type VendorData = {
  id: string;
  name: string;
};

export default async function vendorETL(
  env: ISession,
  mapping: MappingArgs,
  fetching: FetchingArgs
): ExpenseETLExecutionResponse {
  const {
    providerModel,
    providerName,
    originalIdKey,
    everestModel,
    mappingName,
    extractionSource,
    entityId,
  } = mapping;
  const { connectorId, configValues, dataload } = fetching;

  try {
    const { continueInTask, data, deltaValue } = await getData(
      env,
      {
        id: connectorId,
        configValues: configValues as unknown as BillLoginRequest,
        entityId,
      },
      {
        providerModel: providerModel as BillProviderModel,
        extractionSource,
        dataload,
      }
    );
    if (continueInTask) {
      return { continueInTask };
    }

    const vendors = await filterVendors(env, connectorId, data as VendorData[]);

    const matchClient = await LoadOrMatchFunction.client(env);
    await executeETL(
      env,
      {
        extractFn: async () => vendors,
        mediationFns: [excludeSpecificVendors, excludeHSAVendors],
        descriptionFn: (data: VendorData) => data.name,
        providerModel,
        providerName,
        originalIdKey,
        everestModel,
        extractionSource,
        extractionName: generateExtractionName(
          providerName,
          providerModel,
          extractionSource
        ),
        isHierarchy: false,
        retryItemsStatuses: [
          EvstStagingStatus.FAILED,
          EvstStagingStatus.PENDING,
        ],
      },
      [{ name: mappingName }],
      {
        loadFunction: async (
          transformedData: EvstUnknownObject,
          everestId?: number
        ) => {
          const result = await matchClient.matchAndLoadWrapper(
            [
              {
                transformedData: transformedData as unknown as Record<
                  string,
                  unknown
                >,
                everestId,
              },
            ],
            {
              context: EvstMigrationIntegrationContext.Integration,
            },
            'urn:evst:everest:fin/expense:composite:VendorTransformedData',
            'id',
            {
              match: true,
              create: true,
              update: true,
            }
          );
          return result?.[0] as unknown as Promise<EvstLoadOrMatchFunctionReturnType>;
        },
      }
    );

    await updateDelta(env, {
      pckage: BILLDOTCOM_PACKAGE,
      providerModel,
      extractionSource,
      deltaVariable: DELTA_VARIABLE,
      deltaValue,
    });
  } catch (error) {
    throw new Error(error.message);
  }
}

async function filterVendors(
  env: ISession,
  connectorId: number,
  data: VendorData[]
) {
  const billSettings = await BillDotComSettings.read(env, { connectorId }, [
    'useExpensify',
  ]);
  return billSettings?.useExpensify
    ? data.filter((o) => !o.name.endsWith('{expenses}'))
    : data;
}

function excludeSpecificVendors(data: VendorData[]) {
  const excludedIds = new Set([
    '00901FDUEMSNXM256tb6',
    '00901VMFGGEUSR1ybyp1',
    '00901MKNYWCXOZ375j9i',
  ]);
  return {
    data: data.filter((v) => !excludedIds.has(v.id)),
    message: 'Explicitly excluded vendor',
  };
}

function excludeHSAVendors(data: VendorData[]) {
  return {
    data: data.filter((v) => !v.name.endsWith('{HSA}')),
    message: 'Vendor ends with {HSA}',
  };
}
