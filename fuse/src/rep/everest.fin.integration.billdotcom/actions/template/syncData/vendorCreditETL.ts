import type { ISession } from '@everestsystems/content-core';
import type { EvstUnknownObject } from '@pkg/everest.appserver/types/primitives/UnknownObject';
import { EvstMigrationIntegrationContext } from '@pkg/everest.base/types/enums/MigrationIntegrationContext';
import executeETL from '@pkg/everest.fin.integration.base/public/executeETL2';
import generateExtractionName from '@pkg/everest.fin.integration.base/public/template/generateExtractionName';
import updateDelta from '@pkg/everest.fin.integration.base/public/template/updateDelta';
import type { EvstLoadOrMatchFunctionReturnType } from '@pkg/everest.fin.integration.base/types/composites/LoadOrMatchFunctionReturnType';
import type { EvstExtractionProviders } from '@pkg/everest.fin.integration.base/types/enums/ExtractionProviders';
import { EvstStagingStatus } from '@pkg/everest.fin.integration.base/types/enums/StagingStatus';
import { LoadOrMatchFunction } from '@pkg/everest.fin.integration.base/types/LoadOrMatchFunction';
import { Staging } from '@pkg/everest.fin.integration.base/types/Staging';
import {
  BILLDOTCOM_PACKAGE,
  DELTA_VARIABLE,
} from '@pkg/everest.fin.integration.billdotcom/public/config';
import { BillDotComSettings } from '@pkg/everest.fin.integration.billdotcom/types/BillDotComSettings';
import type { BillLoginRequest } from '@pkg/everest.fin.integration.billdotcom/utils/api/types';
import { BillProviderModel } from '@pkg/everest.fin.integration.billdotcom/utils/api/types';
import getData from '@pkg/everest.fin.integration.billdotcom/utils/syncData/fetching/getData';
import type {
  ExpenseETLExecutionResponse,
  FetchingArgs,
  MappingArgs,
} from '@pkg/everest.fin.integration.expense/public/types';

type VendorCreditData = {
  isActive: string;
  approvalStatus: string;
  refNumber: string;
  localAmount: string | null;
  vendorId: string;
  vendorCreditLineItems: {
    departmentId: string;
    chartOfAccountId: string;
    actgClassId: string;
  }[];
};

export default async function vendorCreditETL(
  env: ISession,
  mapping: MappingArgs,
  fetching: FetchingArgs
): ExpenseETLExecutionResponse {
  const {
    providerModel,
    providerName,
    originalIdKey,
    everestModel,
    mappingName,
    extractionSource,
    entityId,
  } = mapping;
  const { connectorId, configValues, dataload, startDate } = fetching;

  try {
    const { continueInTask, data, deltaValue } = await getData(
      env,
      {
        id: connectorId,
        configValues: configValues as unknown as BillLoginRequest,
        entityId,
      },
      {
        providerModel: providerModel as BillProviderModel,
        extractionSource,
        dataload,
        startDate,
      }
    );
    if (continueInTask) {
      return { continueInTask };
    }

    const vendors = await getVendors(
      env,
      providerName as EvstExtractionProviders
    );
    const credits = await processVendorCredits(
      env,
      connectorId,
      data as VendorCreditData[],
      vendors
    );

    const matchClient = await LoadOrMatchFunction.client(env);
    await executeETL(
      env,
      {
        extractFn: async () => credits,
        mediationFns: [
          excludeInactiveVendorCredits,
          excludeUnapprovedVendorCredits,
        ],
        descriptionFn: (data: VendorCreditData) => data.refNumber,
        providerModel,
        providerName,
        originalIdKey,
        everestModel,
        extractionSource,
        extractionName: generateExtractionName(
          providerName,
          providerModel,
          extractionSource
        ),
        isHierarchy: false,
        retryItemsStatuses: [
          EvstStagingStatus.FAILED,
          EvstStagingStatus.PENDING,
        ],
      },
      [{ name: mappingName }],
      {
        loadFunction: async (
          transformedData: EvstUnknownObject,
          everestId?: number
        ) => {
          const result = await matchClient.matchAndLoadWrapper(
            [
              {
                transformedData: transformedData as unknown as Record<
                  string,
                  unknown
                >,
                everestId,
              },
            ],
            {
              context: EvstMigrationIntegrationContext.Integration,
            },
            'urn:evst:everest:fin/expense:composite:VendorCreditTransformedData',
            'vendorCreditHeader.id',
            {
              match: true,
              create: true,
              update: true,
            }
          );
          return result?.[0] as unknown as Promise<EvstLoadOrMatchFunctionReturnType>;
        },
      }
    );

    await updateDelta(env, {
      pckage: BILLDOTCOM_PACKAGE,
      providerModel,
      extractionSource,
      deltaVariable: DELTA_VARIABLE,
      deltaValue,
    });
  } catch (error) {
    throw new Error(error.message);
  }
}

async function getVendors(
  env: ISession,
  providerName: EvstExtractionProviders
) {
  const client = await Staging.client(env);
  return client.getStagingItemsByProviderModel(
    providerName,
    BillProviderModel.Vendor
  );
}

async function processVendorCredits(
  env: ISession,
  connectorId: number,
  data: VendorCreditData[],
  vendors: Staging.Staging[]
) {
  const billSettings = await BillDotComSettings.read(env, { connectorId }, [
    'useAccountingClass',
  ]);

  return data.map((credit) => {
    const vendor = vendors.find((v) => v.originalId === credit.vendorId);
    const currency =
      credit.localAmount === null || credit.localAmount === undefined
        ? 'USD'
        : (vendor.data as Record<'billCurrency', string>).billCurrency;
    return {
      ...credit,
      currency,
      vendorCreditLineItems: credit.vendorCreditLineItems.map((creditLine) => {
        if (billSettings?.useAccountingClass) {
          creditLine.departmentId = creditLine.actgClassId;
        }
        return {
          ...creditLine,
          currency,
          departmentId:
            creditLine.departmentId === '00000000000000000000'
              ? null
              : creditLine.departmentId,
          chartOfAccountId:
            creditLine.chartOfAccountId === '00000000000000000000'
              ? null
              : creditLine.chartOfAccountId,
        };
      }),
    };
  });
}

function excludeInactiveVendorCredits(data: VendorCreditData[]) {
  return {
    data: data.filter((c) => c.isActive === '1'),
    message: 'Inactive vendor credit',
  };
}

function excludeUnapprovedVendorCredits(data: VendorCreditData[]) {
  return {
    data: data.filter((c) => c.approvalStatus === '3'),
    message: 'Unapproved vendor credit',
  };
}
