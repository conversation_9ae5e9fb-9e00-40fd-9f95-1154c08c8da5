import type { ISession } from '@everestsystems/content-core';
import { Account } from '@pkg/everest.fin.accounting/types/Account';
import { FundTransferHeader } from '@pkg/everest.fin.accounting/types/FundTransferHeader';
import { ExpenseReportBase } from '@pkg/everest.fin.expense/types/ExpenseReportBase';
import { OutboundPaymentHeaderBase } from '@pkg/everest.fin.expense/types/OutboundPaymentHeaderBase';
import { Vendor } from '@pkg/everest.fin.expense/types/Vendor';
import { VendorBillHeaderBase } from '@pkg/everest.fin.expense/types/VendorBillHeaderBase';
import { VendorCreditBill } from '@pkg/everest.fin.expense/types/VendorCreditBill';
import { VendorCreditHeader } from '@pkg/everest.fin.expense/types/VendorCreditHeader';
import { VendorPaymentTerm } from '@pkg/everest.fin.expense/types/VendorPaymentTerm';
import type { IntegrationConfig } from '@pkg/everest.fin.integration.base.ui/public/types/IntegrationConfig';
import { EvstExtractionProviders } from '@pkg/everest.fin.integration.base/types/enums/ExtractionProviders';
import { EvstStagingStatus } from '@pkg/everest.fin.integration.base/types/enums/StagingStatus';
import { BILLDOTCOM_PACKAGE } from '@pkg/everest.fin.integration.billdotcom/public/config';
import { BillDotComSettings } from '@pkg/everest.fin.integration.billdotcom/types/BillDotComSettings';
import { BillProviderModel } from '@pkg/everest.fin.integration.billdotcom/utils/api/types';
import { Department } from '@pkg/everest.hr.base/types/Department';
import { Employee } from '@pkg/everest.hr.base/types/Employee';

export default async function getIntegrationConfiguration(
  env: ISession
): Promise<IntegrationConfig> {
  // TODO: Once connector panel is available, use 'connectorId' to query right BILL settings
  const [billSettings] = await BillDotComSettings.query(env, { where: {} }, [
    'useExpensify',
    'useAccountingClass',
    'importUnapprovedBills',
    'matchMigratedTransactions',
    'matchWithQuickbooks',
    'createJournalEntriesForUnapprovedBills',
    'enableOutboundSync',
  ]);
  const billConfig: IntegrationConfig = {
    general: {
      package: BILLDOTCOM_PACKAGE,
      providerName: EvstExtractionProviders.BillDotCom,
      isStartDateRequired: true,
      enableDataSyncRunTracking: true,
    },
    templates: {
      masterData: {
        hideColumns: [
          EvstStagingStatus.NeedsInteraction,
          EvstStagingStatus.FAILED,
          EvstStagingStatus.Drafted,
        ],
      },
      transactionData: {
        hideColumns: [EvstStagingStatus.NeedsInteraction, 'actions'],
      },
      overview: {
        title: 'BILL',
        hasOneEntityPerConnector: true,
        showStatusColumn: true,
        showSyncOverview: true,
        showCreateConnectionOverview: true,
        editConnectorTemplateLink:
          '/templates/everest.fin.integration.billdotcom/uinext/connector',
        configurations: {
          base: {
            useModelExclusions: true,
            useStartDate: true,
            useSyncScheduler: true,
          },
          custom: [
            {
              name: 'Using Expensify with BILL',
              status: billSettings?.useExpensify ? 'Yes' : 'No',
              templateUrl:
                '/templates/everest.fin.integration.billdotcom/uinext/template/expensify',
              size: 'xsmall',
            },
            {
              name: 'Using Quickbooks with BILL',
              status: billSettings?.matchWithQuickbooks ? 'Yes' : 'No',
              templateUrl:
                '/templates/everest.fin.integration.billdotcom/uinext/template/matchWithQuickbooks',
              size: 'xsmall',
            },
            {
              name: 'Match Migrated Transactions',
              status: billSettings?.matchMigratedTransactions ? 'Yes' : 'No',
              templateUrl:
                '/templates/everest.fin.integration.billdotcom/uinext/template/matchMigratedTransactions',
              size: 'xsmall',
            },
            {
              name: 'Using Accounting Classes as Departments',
              status: billSettings?.useAccountingClass ? 'Yes' : 'No',
              templateUrl:
                '/templates/everest.fin.integration.billdotcom/uinext/template/accountingClass',
              size: 'xsmall',
            },
            {
              name: 'Import Unapproved Vendor Bills',
              status: billSettings?.importUnapprovedBills ? 'Yes' : 'No',
              templateUrl:
                '/templates/everest.fin.integration.billdotcom/uinext/template/unapprovedBills',
              size: 'xsmall',
            },
            {
              name: 'Create Journal Entries for Unapproved Vendor Bills',
              status: billSettings?.createJournalEntriesForUnapprovedBills
                ? 'Yes'
                : 'No',
              templateUrl:
                '/templates/everest.fin.integration.billdotcom/uinext/template/createJournalEntries',
              size: 'xsmall',
            },
            {
              name: 'Outbound Sync',
              status: billSettings?.enableOutboundSync ? 'Enabled' : 'Disabled',
              templateUrl:
                '/templates/everest.fin.integration.billdotcom/uinext/template/outboundSync',
              size: 'xsmall',
            },
          ],
        },
      },
    },
    executions: {
      metadata: [
        {
          displayName: 'Chart of Accounts',
          displayNamePlural: 'Chart of Accounts',
          everestModel: Account.MODEL_URN,
          providerModel: BillProviderModel.ChartOfAccount,
          mappings: {
            api: {
              name: 'BDC API ChartOfAccounts Mapping',
              originalIdKey: 'id',
              type: {
                semantic: true,
              },
            },
          },
          exportOptions: {
            additionalFields: [
              {
                headerName: 'Account Type',
                field: 'accountType',
              },
            ],
          },
          aiOptions: {
            rules: [
              'If a source has accountType === 16, consider only targets with groupingAccount: true',
            ],
          },
          applicationUrl:
            '/templates/everest.fin.accounting/uinext/chartOfAccountsList',
        },
        {
          displayName: 'Department',
          displayNamePlural: 'Departments',
          everestModel: Department.MODEL_URN,
          providerModel: BillProviderModel.Department,
          mappings: {
            api: {
              name: 'BDC API Department Mapping',
              originalIdKey: 'id',
              type: {
                semantic: true,
              },
            },
          },
          applicationUrl:
            '/templates/everest.hr.base/uinext/department/departmentHierarchy',
        },
        {
          displayName: 'Employee',
          displayNamePlural: 'Employees',
          everestModel: Employee.MODEL_URN,
          providerModel: BillProviderModel.Employee,
          mappings: {
            api: {
              name: 'BDC API Employee Mapping',
              originalIdKey: 'id',
              type: {
                semantic: true,
              },
            },
          },
          applicationUrl:
            '/templates/everest.hr.base/uinext/employee/employeeAdminList',
        },
        {
          displayName: 'Payment Term',
          displayNamePlural: 'Payment Terms',
          everestModel: VendorPaymentTerm.MODEL_URN,
          providerModel: BillProviderModel.PaymentTerm,
          mappings: {
            api: {
              name: 'BDC API Payment Term Mapping',
              originalIdKey: 'id',
              type: {
                semantic: true,
              },
            },
          },
        },
        {
          displayName: 'Vendor',
          displayNamePlural: 'Vendors',
          everestModel: Vendor.MODEL_URN,
          providerModel: BillProviderModel.Vendor,
          mappings: {
            api: {
              name: 'BDC API Vendor Mapping',
              originalIdKey: 'id',
              type: {
                semantic: true,
              },
            },
          },
          applicationUrl:
            '/templates/everest.fin.expense/uinext/vendorMgmt/vendors',
        },
      ],
      businessData: [
        {
          displayName: 'Vendor Bill',
          displayNamePlural: 'Vendor Bills',
          everestModel: VendorBillHeaderBase.MODEL_URN,
          providerModel: BillProviderModel.Bill,
          mappings: {
            api: {
              name: 'BDC API VendorBill Mapping',
              originalIdKey: 'id',
              type: {
                technical: true,
              },
            },
          },
          applicationUrl:
            '/templates/everest.fin.expense/uinext/vendorMgmt/vendorBills',
        },
        {
          displayName: 'Outbound Payment',
          displayNamePlural: 'Outbound Payments',
          everestModel: OutboundPaymentHeaderBase.MODEL_URN,
          providerModel: BillProviderModel.SentPay,
          mappings: {
            api: {
              name: 'BDC API OutboundPayment Mapping',
              originalIdKey: 'id',
              type: {
                technical: true,
              },
            },
          },
          concurrency: 1,
          applicationUrl:
            '/templates/everest.fin.expense/payment/uinext/paymentsHistory',
        },
        {
          displayName: 'Vendor Credit',
          displayNamePlural: 'Vendor Credits',
          everestModel: VendorCreditHeader.MODEL_URN,
          providerModel: BillProviderModel.VendorCredit,
          mappings: {
            api: {
              name: 'BDC API VendorCredit Mapping',
              originalIdKey: 'id',
              type: {
                technical: true,
              },
            },
          },
          applicationUrl:
            '/templates/everest.fin.expense/uinext/vendorMgmt/applyVendorCredit',
        },
        {
          displayName: 'Credit Application',
          displayNamePlural: 'Credit Applications',
          everestModel: VendorCreditBill.MODEL_URN,
          providerModel: BillProviderModel.BillCredit,
          mappings: {
            api: {
              name: 'BDC API VendorCreditBill Mapping',
              originalIdKey: 'id',
              type: {
                technical: true,
              },
            },
          },
          applicationUrl:
            '/templates/everest.fin.expense/uinext/vendorMgmt/applyVendorCredit',
        },
        {
          displayName: 'Money Movement',
          displayNamePlural: 'Money Movements',
          everestModel: FundTransferHeader.MODEL_URN,
          providerModel: BillProviderModel.MoneyMovement,
          mappings: {
            api: {
              name: 'BDC API FundTransfer Mapping',
              originalIdKey: 'id',
              type: {
                technical: true,
              },
            },
          },
          applicationUrl:
            '/templates/everest.fin.integration.bank/uinext/fundTransfer/fundTransfers',
        },
      ],
    },
  };
  if (billSettings?.useAccountingClass) {
    // Keep Department API Mapping and do not use the Department CSV Mapping
    delete billConfig.executions.metadata[1].mappings.file;
  }
  if (billSettings?.useExpensify) {
    billConfig.executions.businessData.splice(
      6,
      0,
      {
        displayName: 'Expense Report',
        displayNamePlural: 'Expense Reports',
        everestModel: ExpenseReportBase.MODEL_URN,
        providerModel: BillProviderModel.EmployeeExpense,
        mappings: {
          api: {
            name: 'BDC API EmployeeExpense Mapping',
            originalIdKey: 'id',
            type: {
              technical: true,
            },
          },
        },
        applicationUrl:
          '/templates/everest.fin.expense/uinext/expenseMgmt/employeeExpenses',
      },
      {
        displayName: 'Expense Report Payment',
        displayNamePlural: 'Expense Report Payments',
        everestModel: OutboundPaymentHeaderBase.MODEL_URN,
        providerModel: BillProviderModel.EmployeeExpensePayment,
        mappings: {
          api: {
            name: 'BDC API EmployeeExpensePayment Mapping',
            originalIdKey: 'id',
            type: {
              technical: true,
            },
          },
        },
        concurrency: 1,
        applicationUrl:
          '/templates/everest.fin.expense/payment/uinext/paymentsHistory',
      }
    );
  }
  return billConfig;
}
