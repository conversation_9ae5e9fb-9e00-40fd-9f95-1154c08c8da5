{"version": 2, "uicontroller": "matchMigratedTransactions.uicontroller.ts", "uimodel": {"nodes": {"settings": {"type": "struct", "modelId": "everest.fin.integration.billdotcom/BillDotComSettingsModel.BillDotComSettings", "query": {"where": {"connectorId": "@state:connectorId"}}, "fieldList": ["id", "matchMigratedTransactions"]}}}, "uiview": {"templateType": "list", "i18n": ["everest.fin.integration.expense/expenseIntegration"], "grid": {"size": "12"}, "actions": {"content": [{"variant": "secondary", "label": "{{expenseIntegration.cancel}}", "onClick": "@controller:cancel"}, {"variant": "primary", "label": "{{expenseIntegration.save}}", "onClick": "@controller:save"}]}, "sections": {"content": [{"component": "ActionGroup", "section": {"editing": true, "grid": {"size": "12"}}, "props": {"variant": "radio", "versionNew": true, "onChangedValue": "@controller:updateSettings", "lines": "@controller:getLines()"}}]}}}