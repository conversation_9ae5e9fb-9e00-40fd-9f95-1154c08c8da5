// @i18n:everest.connectorengine/connectorEngine
import type { BillLoginRequest } from '@pkg/everest.fin.integration.billdotcom/utils/api/types';
import type { GetConnector } from '@pkg/everest.fin.integration.expense/public/types';

import { SANDBOX_BASE_URL } from '../public/config';
import { BillDotComUI } from '../types/BillDotCom.ui';
import type { ConnectorUiTemplate } from '../types/uiTemplates/uinext/connector.ui';

type ConnectorContext = ConnectorUiTemplate.ConnectorContext;

export type Context = ConnectorContext & {
  state: {
    connector: GetConnector & { configValues: BillLoginRequest };
    selectedEntityId: number;
    isSandbox: boolean;
    response: string;
  };
};

export function isEditing({ state }: Context) {
  const isEditing = state.connector ? true : false;
  if (isEditing && !state.isSandbox) {
    state.isSandbox =
      state.connector?.configValues?.url.includes(SANDBOX_BASE_URL);
  }
  return isEditing;
}

export function getPrimaryLabel(ctx: Context) {
  return isEditing(ctx)
    ? '{{connectorEngine.save}}'
    : '{{connectorEngine.connect}}';
}

export function getName({ state }: Context) {
  return state.connector?.name || '';
}

export function getUserName({ state }: Context) {
  return state.connector?.configValues?.body?.userName || '';
}

export function getOrganization({ state }: Context) {
  return state.connector?.configValues?.body?.orgId || '';
}

export function getSelectedEntity({ data, state }: Context) {
  const entity = data.entities?.find((e) => e.id === state.connector?.entityId);
  return entity?.entityName ?? '';
}

export function selectEntity({ state }: Context, value: number) {
  state.selectedEntityId = value;
}

export function setScope({ state }: Context, currentValue: boolean) {
  state.isSandbox = currentValue;
}

export function cancel({ helpers }: Context) {
  helpers.closeModal();
}

export async function save(ctx: Context) {
  const { helpers, form, state } = ctx;
  const { name, userName, password, orgId } = form.getFormValues();
  const { isSandbox, selectedEntityId: entityId, connector } = state;

  await BillDotComUI.saveConnector(ctx, {
    connector: {
      name,
      userName,
      password,
      orgId,
      isSandbox,
      entityId,
    },
    edit: {
      connectorId: connector?.id,
      sessionId: connector?.sessionId,
    },
  }).run('billDotCom');
  if (connector) {
    helpers.closeModal();
  } else {
    helpers.navigate({
      to: '/templates/everest.fin.integration.expense/uinext/overview?providerName=BillDotCom',
      closeCurrentTab: true,
    });
  }
}

export async function test(ctx: Context) {
  const { form, state, helpers } = ctx;
  const { userName, password, orgId } = form.getFormValues();
  const { isSandbox } = state;
  if (!orgId) {
    helpers.showNotificationMessage({
      key: 'orgId is required',
      message: 'Organization Id is required for testing the connector',
      duration: 2,
      type: 'warning',
    });
  }
  const res = await BillDotComUI.testConnector(ctx, {
    userName,
    password,
    isSandbox,
    orgId,
  }).run('billDotCom');
  state.response = res?.billDotCom?.result;
}
export async function getOrganizations(ctx: Context) {
  const { form, state } = ctx;
  const { userName, password } = form.getFormValues();
  const { isSandbox } = state;

  const res = await BillDotComUI.testConnector(ctx, {
    userName,
    password,
    isSandbox,
  }).run('billDotCom');
  state.response = res?.billDotCom?.result;
}
