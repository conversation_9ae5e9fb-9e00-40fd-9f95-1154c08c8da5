{"version": 2, "uicontroller": "connector.uicontroller.ts", "uimodel": {"nodes": {"billDotCom": {"type": "struct", "modelId": "everest.fin.integration.billdotcom/BillDotComModel.BillDotCom"}, "secrets": {"type": "struct", "modelId": "everest.fin.integration.billdotcom/BillDotComModel.BillDotCom", "getSecrets": {"url": "@state:connector.configValues.url", "orgId": "@state:connector.configValues.body.orgId"}}, "entities": {"type": "list", "modelId": "everest.base/EntityModel.Entity", "query": {}, "fieldList": ["id", "entityName"]}}}, "uiview": {"templateType": "details", "i18n": ["everest.connectorengine/connectorEngine", "billDotCom"], "title": "{{connectorEngine.create}}", "actions": {"content": [{"label": "{{connectorEngine.testConnection}}", "onClick": "@controller:test", "align": "left"}, {"label": "{{connectorEngine.getOrganizations}}", "onClick": "@controller:getOrganizations", "align": "left"}, {"variant": "secondary", "label": "{{connectorEngine.cancel}}", "onClick": "@controller:cancel", "align": "right", "visible": "@controller:isEditing()"}, {"variant": "primary", "label": "@controller:getPrimaryLabel()", "onClick": "@controller:save", "align": "right"}]}, "sections": {"content": [{"component": "Block", "customId": "credentialsBlockFieldGroup", "section": {"grid": {"size": "12"}}, "props": {"type": "secondary", "elements": [{"component": "Input", "label": "{{billDotCom.name}}", "name": "name", "value": "@controller:getName()", "isEditing": true}, {"component": "Input", "label": "{{billDotCom.username}}", "name": "userName", "value": "@controller:getUserName()", "isEditing": true}, {"component": "Input", "label": "{{billDotCom.password}}", "name": "password", "type": "password", "isEditing": true}, {"component": "Input", "label": "{{billDotCom.orgId}}", "name": "orgId", "value": "@controller:getOrganization()", "isEditing": true}, {"component": "Select", "label": "{{billDotCom.mappedEntity}}", "idProp": "id", "textProp": "entityName", "value": "@controller:getSelectedEntity()", "isEditing": true, "list": "@binding:entities", "onChange": "@controller:selectEntity"}, {"component": "Checkbox", "isEditing": true, "isDisabled": "@controller:isEditing()", "label": "{{billDotCom.scope}}", "text": "{{billDotCom.billSandbox}}", "value": "@state:isSandbox", "onChange": "@controller:setScope"}]}}, {"component": "Block", "customId": "responseBlockFieldGroup", "section": {"grid": {"size": "12"}}, "props": {"elements": [{"component": "Editor", "label": "{{billDotCom.response}}", "value": "@state:response", "action": "create", "isEditing": false, "editorConfig": {"language": "json", "height": 300}}]}}]}}}