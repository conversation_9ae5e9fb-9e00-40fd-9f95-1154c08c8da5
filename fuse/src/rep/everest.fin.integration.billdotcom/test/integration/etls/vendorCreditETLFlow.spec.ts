/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable jest/expect-expect */
import type { ISession } from '@everestsystems/content-core';
import type { TestTeardown } from '@everestsystems/content-core/lib/tests';
import { setupTest } from '@everestsystems/content-core/lib/tests';
import { Decimal } from '@everestsystems/decimal';
import type { Entity } from '@pkg/everest.base/types/Entity';
import { Account } from '@pkg/everest.fin.accounting/types/Account';
import type { BusinessPartner } from '@pkg/everest.fin.base/types/BusinessPartner';
import { Vendor } from '@pkg/everest.fin.expense/types/Vendor';
import { VendorBillHeaderBase } from '@pkg/everest.fin.expense/types/VendorBillHeaderBase';
import { VendorCreditHeader } from '@pkg/everest.fin.expense/types/VendorCreditHeader';
import { VendorPaymentTerm } from '@pkg/everest.fin.expense/types/VendorPaymentTerm';
import { EvstExtractionProviders } from '@pkg/everest.fin.integration.base/types/enums/ExtractionProviders';
import { EvstExtractionSource } from '@pkg/everest.fin.integration.base/types/enums/ExtractionSource';
import { EvstStagingDataStatus } from '@pkg/everest.fin.integration.base/types/enums/StagingDataStatus';
import { EvstStagingStatus } from '@pkg/everest.fin.integration.base/types/enums/StagingStatus';
import vendorCreditETL from '@pkg/everest.fin.integration.billdotcom/actions/template/syncData/vendorCreditETL';
import {
  createStagingData,
  createTestDepartment,
  createTestEntityNodes,
  createTestVendor,
  createTestVendorPaymentTerm,
  createVendorBill,
} from '@pkg/everest.fin.integration.billdotcom/test/utils/createMocks';
import { BillDotComSettings } from '@pkg/everest.fin.integration.billdotcom/types/BillDotComSettings';
import { BillProviderModel } from '@pkg/everest.fin.integration.billdotcom/utils/api/types';
import getAPIData from '@pkg/everest.fin.integration.billdotcom/utils/syncData/fetching/getAPIData';
import type { MappingArgs } from '@pkg/everest.fin.integration.expense/public/types';
import { Department } from '@pkg/everest.hr.base/types/Department';
import { cloneDeep } from 'lodash';

import { mockConnector } from '../../fixtures/connectorsData';
import { mockVendorCreditExtracted } from '../../fixtures/vendorCreditData';
import {
  getMapping,
  getStagingData,
  getVendorCreditHeaderByVendorId,
  getVendorCreditLineByVendorHeaderId,
} from '../../utils/commonQuery';
import {
  ORIGIN,
  TEST_CHART_OF_ACCOUNT_ID,
  TEST_DEPARTMENT_ID,
  TEST_PAYMENT_TERM_ID,
  TEST_VENDOR_BILL_ID,
  TEST_VENDOR_ID,
} from '../../utils/constant';

jest.mock(
  '@pkg/everest.fin.integration.billdotcom/utils/syncData/fetching/getAPIData'
);
jest.mock('@pkg/everest.fin.integration.billdotcom/types/BillDotComSettings');

describe('Integration Test for Vendor Credit ETL from Billdotcom', () => {
  let session: ISession;
  let teardown: TestTeardown;
  let mapping: MappingArgs;
  let entityId: Entity.Entity['id'];
  let businessPartnerId: BusinessPartner.BusinessPartner['id'];
  let vendorId: Vendor.Vendor['id'];
  let vendorBillId: VendorBillHeaderBase.VendorBillHeaderBase['id'];
  let vendorCreditHeader: VendorCreditHeader.VendorCreditHeader;
  let vendorCreditHeaderId: VendorCreditHeader.VendorCreditHeader['id'];
  let paymentTermId: VendorPaymentTerm.VendorPaymentTerm['id'];
  let paymentTermCode: VendorPaymentTerm.VendorPaymentTerm['code'];
  let departmentId: Department.Department['id'];
  let accountId: Account.Account['id'];

  beforeAll(async () => {
    ({ teardown, session } = await setupTest({
      withUser: 'test-user',
    }));

    // Create Entity Nodes
    const entityNodes = await createTestEntityNodes(session);
    entityId = entityNodes.entityId;
    accountId = entityNodes.accountId;
    businessPartnerId = entityNodes.businessPartnerId;

    // Create a Payment Term for Vendor and Vendor Credit
    const paymentTerm = await createTestVendorPaymentTerm(session);
    paymentTermId = paymentTerm.id;
    paymentTermCode = paymentTerm.code;

    // Create a department for Vendor Credit
    const department = await createTestDepartment(session);
    departmentId = department.id;

    // Create Vendor for Vendor Credit
    const vendor = await createTestVendor(
      session,
      entityId,
      paymentTermCode,
      businessPartnerId
    );
    vendorId = vendor.id;

    const vendorBill = await createVendorBill(
      session,
      entityId,
      accountId,
      departmentId,
      vendorId,
      paymentTermCode
    );
    vendorBillId = vendorBill.id;

    // Add all staging status for created data
    const stagingData = [
      {
        originalId: TEST_VENDOR_ID,
        providerName: EvstExtractionProviders.BillDotCom,
        providerModel: BillProviderModel.Vendor,
        everestModel: Vendor.MODEL_URN,
        status: EvstStagingStatus.Integrated,
        everestId: vendorId,
        dataExtractionId: 1,
        data: {
          billCurrency: 'USD',
        },
      },
      {
        originalId: TEST_VENDOR_BILL_ID,
        providerName: EvstExtractionProviders.BillDotCom,
        providerModel: BillProviderModel.Bill,
        everestModel: VendorBillHeaderBase.MODEL_URN,
        status: EvstStagingStatus.Integrated,
        everestId: vendorBillId,
        dataExtractionId: 1,
      },
      {
        originalId: TEST_CHART_OF_ACCOUNT_ID,
        providerName: EvstExtractionProviders.BillDotCom,
        providerModel: BillProviderModel.ChartOfAccount,
        everestModel: Account.MODEL_URN,
        dataStatus: EvstStagingDataStatus.Latest,
        status: EvstStagingStatus.Matched,
        everestId: accountId,
        dataExtractionId: 1,
      },
      {
        originalId: TEST_PAYMENT_TERM_ID,
        providerName: EvstExtractionProviders.BillDotCom,
        providerModel: BillProviderModel.PaymentTerm,
        everestModel: VendorPaymentTerm.MODEL_URN,
        dataStatus: EvstStagingDataStatus.Latest,
        status: EvstStagingStatus.Integrated,
        everestId: paymentTermId,
        dataExtractionId: 1,
      },
      {
        originalId: TEST_DEPARTMENT_ID,
        providerName: EvstExtractionProviders.BillDotCom,
        providerModel: BillProviderModel.Department,
        everestModel: Department.MODEL_URN,
        dataStatus: EvstStagingDataStatus.Latest,
        status: EvstStagingStatus.Matched,
        everestId: departmentId,
        dataExtractionId: 1,
      },
    ];
    await createStagingData(session, stagingData);

    // Mock the query importUnapprovedBills from Billdotcom function
    (BillDotComSettings.read as jest.Mock).mockImplementation(() => {
      return Promise.resolve({ id: 123 });
    });
    (BillDotComSettings.query as jest.Mock).mockImplementation(() => {
      return Promise.resolve([{ useExpensify: true }]);
    });
    // Mock the getAPIData from Billdotcom function
    (getAPIData as jest.Mock).mockImplementation(() => {
      mockVendorCreditExtracted.data[0].entityId = entityId;
      return Promise.resolve(mockVendorCreditExtracted);
    });

    // Get mapping for VendorCreditETL
    mapping = await getMapping(
      session,
      'businessData',
      BillProviderModel.VendorCredit,
      VendorCreditHeader.MODEL_URN,
      EvstExtractionSource.API,
      entityId
    );

    // Call VendorCreditETL
    await vendorCreditETL(session, mapping, mockConnector);

    // Get Vendor Credit Header by Vendor Id
    const vendorCreditHeaders = await getVendorCreditHeaderByVendorId(
      session,
      vendorId
    );
    vendorCreditHeader = vendorCreditHeaders[0];
    vendorCreditHeaderId = vendorCreditHeader.id;
  });

  afterEach(async () => {
    jest.clearAllMocks();
  });

  afterAll(async () => {
    jest.clearAllMocks();
    await teardown?.();
  });

  const validateStaging = async (
    referData: typeof mockVendorCreditExtracted,
    status: EvstStagingStatus,
    dataVersion = 1
  ) => {
    const updatedCredits = await VendorCreditHeader.query(
      session,
      { where: { vendorId: vendorId } },
      ['id', 'vendorId']
    );
    expect(updatedCredits).toHaveLength(1);

    const vendorCreditStagingdata = await getStagingData(
      session,
      BillProviderModel.VendorCredit,
      referData.data[0].vendorCreditLineItems[0].vendorCreditId
    );

    expect(vendorCreditStagingdata.status).toEqual(status);
    expect(vendorCreditStagingdata.dataVersion).toEqual(dataVersion);
  };

  const validateVendorCredit = async (
    referData: typeof mockVendorCreditExtracted,
    vendorCreditHeader: VendorCreditHeader.VendorCreditHeader
  ) => {
    expect(vendorCreditHeader.id).toBeDefined();
    expect(vendorCreditHeader.status).toEqual('unapplied');
    expect(vendorCreditHeader.active).toBeTruthy();
    expect(vendorCreditHeader.currency).toEqual(referData.data[0].currency);
    expect(vendorCreditHeader.entityId).toEqual(entityId);
    expect(vendorCreditHeader.origin).toEqual(ORIGIN);
    expect(vendorCreditHeader.periodName).toEqual('Dec 2024');
    expect(vendorCreditHeader.postingStatus).toEqual('posted');
    expect(vendorCreditHeader.referenceNumber).toEqual(
      referData.data[0].refNumber
    );
    expect(vendorCreditHeader.totalAmount.amount).toEqual(
      new Decimal(referData.data[0].vendorCreditLineItems[0].amount)
    );
    expect(vendorCreditHeader.vendorId).toEqual(vendorId);
    expect(vendorCreditHeader.creditDate).toEqual(
      vendorCreditHeader.creditDate
    );
  };

  const validateVendorCreditLine = async (
    referData: typeof mockVendorCreditExtracted,
    vendorCreditHeaderId: VendorCreditHeader.VendorCreditHeader['id']
  ) => {
    const vendorCreditLines = await getVendorCreditLineByVendorHeaderId(
      session,
      vendorCreditHeaderId
    );
    const vendorCreditLine = vendorCreditLines[0];
    expect(vendorCreditLine.id).toBeDefined();
    expect(vendorCreditLine.accountId).toEqual(accountId);
    expect(vendorCreditLine.active).toBeTruthy();
    expect(vendorCreditLine.amount.amount).toEqual(
      new Decimal(referData.data[0].vendorCreditLineItems[0].amount)
    );
    expect(vendorCreditLine.description).toEqual(
      referData.data[0].vendorCreditLineItems[0].description
    );
    expect(vendorCreditLine.departmentId).toEqual(departmentId);
    expect(vendorCreditLine.origin).toEqual(ORIGIN);
  };

  it('should sync a Vendor Credit successfully from Bill to Everest', async () => {
    await validateStaging(
      mockVendorCreditExtracted,
      EvstStagingStatus.Integrated,
      1
    );
    await validateVendorCredit(mockVendorCreditExtracted, vendorCreditHeader);
    await validateVendorCreditLine(
      mockVendorCreditExtracted,
      vendorCreditHeaderId
    );
  });

  it('should resync successfully a Vendor Bill without any changed data from Bill to Everest', async () => {
    // Recall vendorCreditETL with no changed data from BILL
    await vendorCreditETL(session, mapping, mockConnector);
    await validateStaging(
      mockVendorCreditExtracted,
      EvstStagingStatus.Integrated,
      1
    );
    await validateVendorCredit(mockVendorCreditExtracted, vendorCreditHeader);
    await validateVendorCreditLine(
      mockVendorCreditExtracted,
      vendorCreditHeaderId
    );
  });

  it('should resync successfully a Vendor Bill with data changed from Bill to Everest', async () => {
    const updatedVendorCreditHeaderDescription =
      'Updated generic amount credit';
    const updatedVendorCreditLineDescription = 'Updated generic amount credit';
    const updatedMockVendorCreditExtracted = cloneDeep(
      mockVendorCreditExtracted
    );
    (getAPIData as jest.Mock).mockImplementationOnce(() => {
      updatedMockVendorCreditExtracted.data[0].description =
        updatedVendorCreditHeaderDescription;
      updatedMockVendorCreditExtracted.data[0].vendorCreditLineItems[0].description =
        updatedVendorCreditLineDescription;
      return Promise.resolve(updatedMockVendorCreditExtracted);
    });
    // Recall vendorCreditETL with no changed data from BILL
    await vendorCreditETL(session, mapping, mockConnector);
    await validateStaging(
      updatedMockVendorCreditExtracted,
      EvstStagingStatus.Integrated,
      2
    );
    await validateVendorCredit(
      updatedMockVendorCreditExtracted,
      vendorCreditHeader
    );
    await validateVendorCreditLine(
      updatedMockVendorCreditExtracted,
      vendorCreditHeaderId
    );
  });
});
