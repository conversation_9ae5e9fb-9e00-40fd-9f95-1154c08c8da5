import type { Decimal } from '@everestsystems/decimal';
import type { Account } from '@pkg/everest.fin.accounting/types/Account';
import type { OutboundPaymentHeaderBase } from '@pkg/everest.fin.expense/types/OutboundPaymentHeaderBase';
import type { OutboundPaymentItemBase } from '@pkg/everest.fin.expense/types/OutboundPaymentItemBase';
import type { VendorCreditBill } from '@pkg/everest.fin.expense/types/VendorCreditBill';
import type { Department } from '@pkg/everest.hr.base/types/Department';
import type { Employee } from '@pkg/everest.hr.base/types/Employee';

export type TransformedAccountData = {
  Account: Partial<Account.Account>;
  accountType: string;
  evstAccount?: string;
  billAccount?: string;
};

export type TransformedDepartmentData = {
  Department: Partial<Department.Department>;
  evstDepartment?: string;
};

export type TransformedEmployeeData = {
  Employee: Partial<Employee.Employee>;
};

export type TransformedPaymentData = {
  PaymentHeader: Partial<OutboundPaymentHeaderBase.OutboundPaymentHeaderBase>;
  bdcLocalAmount: Decimal;
  bdcAmount: Decimal;
  bdcExchangeRate: number;
  bdcVendorId: number;
  bdcPaymentItems: TransformedPaymentItemData[];
};

export type TransformedPaymentItemData = {
  PaymentItem: Partial<OutboundPaymentItemBase.OutboundPaymentItemBase>;
  bdcId: string;
};

export type TransformedVendorCreditBillData = {
  VendorCreditBill: Partial<VendorCreditBill.VendorCreditBill>;
};
