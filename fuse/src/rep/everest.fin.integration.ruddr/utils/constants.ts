/** Package Name */
export const RUDDR_PACKAGE_NAME = 'everest.fin.integration.ruddr';

/** Member to Employee Mapping */
export const EMPLOYEE_MAPPING = 'Ruddr Member';
export const EMPLOYEE_PROVIDER_MODEL = 'Member';
export const EMPLOYEE_EVEREST_MODEL =
  'urn:evst:everest:hr/base:model/node:Employee';
export const EMPLOYEE_ORIGINAL_ID = 'id';
export const EMPLOYEE_FLOW_NAME = 'Member to Employee';

/** Expense Categories */
export const EXPENSE_CATEGORIES_MAPPING = 'Ruddr Expense Categories to Account';
export const EXPENSE_CATEGORIES_PROVIDER_MODEL = 'ExpenseCategories';
export const EXPENSE_CATEGORIES_EVEREST_MODEL =
  'urn:evst:everest:fin/accounting:model/node:Account';
export const EXPENSE_CATEGORIES_ORIGINAL_ID = 'id';
export const EXPENSE_CATEGORIES_FLOW_NAME = 'Expense Categories to Accounts';

/** Expense Mapping */
export const EXPENSE_MAPPING = 'Ruddr Expenses';
export const EXPENSE_PROVIDER_MODEL = 'ExpenseReports';
export const EXPENSE_EVEREST_MODEL =
  'urn:evst:everest:fin/expense:model/node:ExpenseReportBase';
export const EXPENSE_ORIGINAL_ID = 'id';
export const EXPENSE_FLOW_NAME = 'Expense Report to Expense';

/** Customer Mapping */
export const CUSTOMER_MAPPING = 'Ruddr Clients';
export const CUSTOMER_PROVIDER_MODEL = 'Customers';
export const CUSTOMER_EVEREST_MODEL =
  'urn:evst:everest:fin/accounting:composite:CustomerUpsertPayload';
export const CUSTOMER_ORIGINAL_ID = 'id';
export const CUSTOMER_FLOW_NAME = 'Client to Customer';

/** Provider Models */
export enum ProviderModel {
  Member = EMPLOYEE_PROVIDER_MODEL,
  ExpenseReports = EXPENSE_PROVIDER_MODEL,
  ExpenseCategories = EXPENSE_CATEGORIES_PROVIDER_MODEL,
  Customers = CUSTOMER_PROVIDER_MODEL,
}

/** Ruddr Base URL */
export const RUDDR_BASE_URL = 'https://www.ruddr.io/api/workspace/clients';

/** Entity Session Variable */
export const ENTITY_SESSION_VARIABLE_NAME = 'Entity Id';
export const ENTITY_SESSION_VARIABLE_CONFIG_KEY = '$entityId';

/** Ruddr Secret Key */
export const RUDDR_SECRET_KEY = 'ruddr-secret-key';
