import type { ISession } from '@everestsystems/content-core';
import { IntegrationFlowActions } from '@pkg/everest.fin.integration.base/types/IntegrationFlowActions';
import { ProviderModel } from '@pkg/everest.fin.integration.ruddr/utils/constants';

export default async function syncData(
  env: ISession,
  providerModel: ProviderModel,
  mapping: { mappings: { api: { flowName: string } } },
  fetching: { sessionId: number }
) {
  const { sessionId } = fetching;

  const client = await IntegrationFlowActions.client(env);
  switch (providerModel) {
    case ProviderModel.ExpenseCategories:
    case ProviderModel.ExpenseReports:
    case ProviderModel.Customers:
    case ProviderModel.Member: {
      return client.executeIntegrationFlow(
        {
          name: mapping?.mappings?.api?.flowName,
        },
        {
          sessionId,
        }
      );
    }
    default: {
      const asNever: never = providerModel;
      throw new Error(`Unknown provider model: ${asNever}`);
    }
  }
}
