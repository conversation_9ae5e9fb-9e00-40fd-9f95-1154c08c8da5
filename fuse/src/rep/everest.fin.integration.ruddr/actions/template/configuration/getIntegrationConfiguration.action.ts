import type { ISession } from '@everestsystems/content-core';
import type { IntegrationConfig } from '@pkg/everest.fin.integration.base.ui/public/types/IntegrationConfig';
import {
  getSessionId,
  getSessionVariableConfigValueOrDefault,
} from '@pkg/everest.fin.integration.base.ui/public/utils/sessionVariable/sessionVariable';
import { EvstExtractionProviders } from '@pkg/everest.fin.integration.base/types/enums/ExtractionProviders';
import { EvstStagingStatus } from '@pkg/everest.fin.integration.base/types/enums/StagingStatus';
import {
  CUSTOMER_EVEREST_MODEL,
  CUSTOMER_FLOW_NAME,
  CUSTOMER_MAPPING,
  CUSTOMER_ORIGINAL_ID,
  CUSTOMER_PROVIDER_MODEL,
  EMPLOYEE_EVEREST_MODEL,
  EMPLOYEE_FLOW_NAME,
  EMPLOYEE_MAPPING,
  EMPLOYEE_ORIGINAL_ID,
  EMPLOYEE_PROVIDER_MODEL,
  EXPENSE_CATEGORIES_EVEREST_MODEL,
  EXPENSE_CATEGORIES_FLOW_NAME,
  EXPENSE_CATEGORIES_MAPPING,
  EXPENSE_CATEGORIES_ORIGINAL_ID,
  EXPENSE_CATEGORIES_PROVIDER_MODEL,
  EXPENSE_EVEREST_MODEL,
  EXPENSE_FLOW_NAME,
  EXPENSE_MAPPING,
  EXPENSE_ORIGINAL_ID,
  EXPENSE_PROVIDER_MODEL,
  RUDDR_PACKAGE_NAME,
} from '@pkg/everest.fin.integration.ruddr/utils/constants';
import { entitySessionVariable } from '@pkg/everest.fin.integration.ruddr/utils/entitySessionVariable';

export default async function getConfiguration(
  env: ISession
): Promise<IntegrationConfig> {
  const sessionId = await getSessionId(env, RUDDR_PACKAGE_NAME);

  const entityId = await getSessionVariableConfigValueOrDefault(
    env,
    sessionId,
    entitySessionVariable.configKey
  );

  const ruddrConfig: IntegrationConfig = {
    general: {
      package: RUDDR_PACKAGE_NAME,
      providerName: EvstExtractionProviders.Ruddr,
      enableDataSyncRunTracking: true,
      enableBusinessUserStagingView: true,
    },
    templates: {
      masterData: {
        hideColumns: [EvstStagingStatus.NeedsInteraction],
      },
      overview: {
        title: 'Ruddr',
        hasOneEntityPerConnector: true,
        editConnectorTemplateLink:
          '/templates/everest.fin.integration.ruddr/uinext/connector',
        showLastSyncColumn: true,
        showStatusColumn: true,
        configurations: {
          custom: [
            {
              name: 'General',
              description: 'Entity',
              initialState: {
                sessionId,
                sessionVariablesLevels: [
                  {
                    title: 'Entity',
                    configs: [
                      {
                        sessionVariableConfig: entitySessionVariable,
                        value: entityId,
                      },
                    ],
                  },
                ],
              },
            },
          ],
        },
      },
    },
    executions: {
      metadata: [
        {
          displayName: 'Customer',
          displayNamePlural: 'Customers',
          everestModel: CUSTOMER_EVEREST_MODEL,
          providerModel: CUSTOMER_PROVIDER_MODEL,
          originalIdKey: CUSTOMER_ORIGINAL_ID,
          mappings: {
            api: {
              flowName: CUSTOMER_FLOW_NAME,
              name: CUSTOMER_MAPPING,
              type: {
                technical: true,
                semantic: false,
              },
            },
          },
          applicationUrl:
            '/templates/everest.hr.base/uinext/employee/employeeAdminList',
        },
        {
          displayName: 'Employee',
          displayNamePlural: 'Employees',
          everestModel: EMPLOYEE_EVEREST_MODEL,
          providerModel: EMPLOYEE_PROVIDER_MODEL,
          originalIdKey: EMPLOYEE_ORIGINAL_ID,
          mappings: {
            api: {
              flowName: EMPLOYEE_FLOW_NAME,
              name: EMPLOYEE_MAPPING,
              type: {
                technical: true,
                semantic: false,
              },
            },
          },
          applicationUrl:
            '/templates/everest.hr.base/uinext/employee/employeeAdminList',
        },
        {
          displayName: 'Customer',
          displayNamePlural: 'Customers',
          everestModel: CUSTOMER_EVEREST_MODEL,
          providerModel: CUSTOMER_PROVIDER_MODEL,
          originalIdKey: CUSTOMER_ORIGINAL_ID,
          mappings: {
            api: {
              flowName: CUSTOMER_FLOW_NAME,
              name: CUSTOMER_MAPPING,
              type: {
                technical: true,
                semantic: false,
              },
            },
          },
          applicationUrl:
            '/templates/everest.hr.base/uinext/employee/employeeAdminList',
        },
        {
          displayName: 'Expense Category to Account',
          displayNamePlural: 'Expense Categories to Account',
          everestModel: EXPENSE_CATEGORIES_EVEREST_MODEL,
          providerModel: EXPENSE_CATEGORIES_PROVIDER_MODEL,
          originalIdKey: EXPENSE_CATEGORIES_ORIGINAL_ID,
          mappings: {
            api: {
              flowName: EXPENSE_CATEGORIES_FLOW_NAME,
              name: EXPENSE_CATEGORIES_MAPPING,
              type: {
                technical: false,
                semantic: true,
              },
            },
          },
          // applicationUrl:
          //   '/templates/everest.hr.base/uinext/employee/employeeAdminList',
        },
      ],
      businessData: [
        {
          displayName: 'Expense',
          displayNamePlural: 'Expenses',
          everestModel: EXPENSE_EVEREST_MODEL,
          providerModel: EXPENSE_PROVIDER_MODEL,
          originalIdKey: EXPENSE_ORIGINAL_ID,
          mappings: {
            api: {
              flowName: EXPENSE_FLOW_NAME,
              name: EXPENSE_MAPPING,
              type: {
                technical: true,
                semantic: false,
              },
            },
          },
          applicationUrl:
            '/templates/everest.fin.expense/uinext/expenseMgmt/employeeExpenses',
        },
      ],
    },
  };
  return ruddrConfig;
}
