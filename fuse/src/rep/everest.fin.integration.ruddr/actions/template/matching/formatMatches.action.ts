import type { ISession } from '@everestsystems/content-core';
import type { FormattedMatch } from '@pkg/everest.fin.integration.base.ui/public/types/MatchingTypes';
import type { Staging } from '@pkg/everest.fin.integration.base/types/Staging';
import { ProviderModel } from '@pkg/everest.fin.integration.ruddr/utils/constants';

export default function formatMatches(
  _env: ISession,
  providerModel: ProviderModel,
  matches: Staging.Staging[]
): FormattedMatch[] {
  switch (providerModel) {
    case ProviderModel.ExpenseCategories: {
      return formatRuddrExpenseCategories(matches);
    }
    case ProviderModel.Member:
    case ProviderModel.Customers:
    case ProviderModel.ExpenseReports: {
      throw new Error(
        `Semantic mapping not supported for provider model: ${providerModel}`
      );
    }
    default: {
      const asNever: never = providerModel;
      throw new Error(`Unknown provider model: ${asNever}`);
    }
  }
}

type RuddrExpenseCategory = {
  id: string;
  name: string;
};

function formatRuddrExpenseCategories(
  matches: Staging.Staging[]
): FormattedMatch[] {
  return matches.map((match) => {
    const { id, everestId } = match;
    const data = match.data as RuddrExpenseCategory;
    return {
      id,
      account: everestId,
      name: data?.name,
    };
  });
}
