import type { ISession } from '@everestsystems/content-core';
import { Account } from '@pkg/everest.fin.accounting/types/Account';
import type { MatchingPrimitiveTypes } from '@pkg/everest.fin.integration.base.ui/public/types/MatchingTypes';
import { ProviderModel } from '@pkg/everest.fin.integration.ruddr/utils/constants';

export default function getMatchingPrimitiveTypes(
  env: ISession,
  providerModel: ProviderModel
): MatchingPrimitiveTypes {
  switch (providerModel) {
    case ProviderModel.ExpenseCategories: {
      return getAccountPrimitiveTypes(env);
    }
    case ProviderModel.Member:
    case ProviderModel.Customers:
    case ProviderModel.ExpenseReports: {
      throw new Error(
        `Semantic mapping not supported for provider model: ${providerModel}`
      );
    }
    default: {
      const asNever: never = providerModel;
      throw new Error(`Unknown provider model: ${asNever}`);
    }
  }
}

function getAccountPrimitiveTypes(_env: ISession): MatchingPrimitiveTypes {
  return {
    everestId: {
      urn: 'urn:evst:everest:fin/accounting:primitive:Account',
      name: 'Account',
      params: {
        chartOfAccountsId: undefined,
        groupingAccount: 'No',
      },
      modelUrn: Account.MODEL_URN,
    },
  };
}
