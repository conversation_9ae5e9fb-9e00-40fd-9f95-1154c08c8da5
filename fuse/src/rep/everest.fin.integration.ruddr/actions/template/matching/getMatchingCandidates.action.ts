import type { ISession } from '@everestsystems/content-core';
import { Account } from '@pkg/everest.fin.accounting/types/Account';
import { EntityBook } from '@pkg/everest.fin.accounting/types/EntityBook';
import type { MatchingCandidate } from '@pkg/everest.fin.integration.base.ui/public/types/MatchingTypes';
import {
  getSessionId,
  getSessionVariableConfigValueOrDefault,
} from '@pkg/everest.fin.integration.base.ui/public/utils/sessionVariable/sessionVariable';
import {
  ProviderModel,
  RUDDR_PACKAGE_NAME,
} from '@pkg/everest.fin.integration.ruddr/utils/constants';
import { entitySessionVariable } from '@pkg/everest.fin.integration.ruddr/utils/entitySessionVariable';
import { isNil } from 'lodash';

export default async function getMatchingCandidatesV2(
  env: ISession,
  providerModel: ProviderModel,
  requiredFieldsForAiMatching?: string[]
): Promise<MatchingCandidate[]> {
  switch (providerModel) {
    case ProviderModel.ExpenseCategories: {
      return getAccountMatchingCandidates(
        env,
        requiredFieldsForAiMatching as Array<
          keyof Account.AccountWithAssociation
        >
      );
    }
    case ProviderModel.Member:
    case ProviderModel.Customers:
    case ProviderModel.ExpenseReports: {
      throw new Error(
        `Semantic mapping not supported for provider model: ${providerModel}`
      );
    }
    default: {
      const asNever: never = providerModel;
      throw new Error(`Unknown provider model: ${asNever}`);
    }
  }
}

async function getAccountMatchingCandidates(
  env: ISession,
  requiredFieldsForAiMatching: Array<keyof Account.AccountWithAssociation>
): Promise<MatchingCandidate[]> {
  // step 1: get selected or integrated entity
  const sessionId = await getSessionId(env, RUDDR_PACKAGE_NAME);
  const entityId = await getSessionVariableConfigValueOrDefault(
    env,
    sessionId,
    entitySessionVariable.configKey
  );

  // step 2: get assigned chart of accounts for selected or integrated entity
  // note that every entity can only have one CoA assigned
  const [assignedEntityBook] = await EntityBook.query(
    env,
    {
      where: { entityId: +entityId },
    },
    ['chartOfAccountsId']
  );
  if (
    isNil(assignedEntityBook) ||
    isNil(assignedEntityBook.chartOfAccountsId)
  ) {
    // no CoA assigned to the entity which means no matching candidates
    return [];
  }

  const accounts = await Account.query(
    env,
    {
      where: {
        chartOfAccountsId: assignedEntityBook.chartOfAccountsId,
        groupingAccount: false,
      },
      orderBy: ['accountNumber'],
    },
    ['id', 'accountNumber', 'accountName', ...requiredFieldsForAiMatching]
  );
  const candidates = accounts.map((account) => {
    const { id, accountNumber, accountName } = account;
    const displayName = `${accountNumber} -- ${accountName}`;
    return { ...account, id, displayName, name: displayName };
  });

  return candidates;
}
