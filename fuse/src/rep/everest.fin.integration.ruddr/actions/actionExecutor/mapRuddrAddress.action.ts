import type { ISession } from '@everestsystems/content-core';
import { EvstAddressTransformedData } from '@pkg/everest.base/types/composites/AddressTransformedData';
import type { SessionVariables } from '@pkg/everest.fin.integration.base/types/SessionVariables';
// Define the input type
type InputType = Record<string, unknown>;
// Define the output type
type OutputType = Record<string, unknown>;

export default async function mapRuddrAddress(
  env: ISession,
  input: InputType,
  _sessionVariables?: SessionVariables.SessionVariables[]
) {
  const output: EvstAddressTransformedData[] = [];
  // Write the logic here

  // "Dr.-Karl-Lenz-Strasse 66\n87700 Memmingen"
  const address: EvstAddressTransformedData = {
    city: 'Memmingen',
    line1: 'Dr.-Karl-Lenz-Strasse 66',
    zipCode: '87700',
    country: 'DE',
  };

  output.push(address);

  return output;
}
