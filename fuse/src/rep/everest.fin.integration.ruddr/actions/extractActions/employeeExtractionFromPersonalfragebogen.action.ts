import type { ISession } from '@everestsystems/content-core';
import { BaseAI } from '@pkg/everest.base.ai/types/BaseAI';
import type { EvstextractActionInput } from '@pkg/everest.fin.integration.base/types/composites/extractActionInput';
type OutputType = Record<string, unknown>[];
// import {
//   addIntegrationContext,
//   getIntegrationContext,
// } from '@pkg/everest.fin.integration.base/inbound/ETL/integrationContext';

export default async function employeeExtractionFromPersonalfragebogen(
  env: ISession,
  input?: EvstextractActionInput
) {
  const output: OutputType = [];
  const fileId = input?.fileId;
  if (!fileId) {
    throw new Error('File ID is required');
  }

  const fields = {
    lastName: {
      type: 'string',
      description: 'Last name of the employee',
    },
    firstName: {
      type: 'string',
      description: 'First name of the employee',
    },
    streetAndHouseNumber: {
      type: 'string',
      description: 'Street and House Number of the employee',
    },
    zipCodeAndCity: {
      type: 'string',
      description: 'ZIP Code and City of the employee',
    },
    zipCode: {
      type: 'string',
      description: 'ZIP Code of the employee',
    },
    city: {
      type: 'string',
      description: 'City of the employee',
    },
    socialSecurityNumber: {
      type: 'string',
      description: 'Social Security Number of the employee',
    },
    dateOfBirth: {
      type: 'string',
      description: 'Date of Birth of the employee',
    },
    gender: {
      type: 'string',
      description: 'Gender of the employee',
    },
    iban: {
      type: 'string',
      description: 'IBAN of the employee',
    },
    bic: {
      type: 'number',
      description: 'BIC of the employee',
    },
    personTaxId: {
      type: 'number',
      description: 'Tax ID (in german "Identifikationsnummer") of the employee',
    },
  };
  const example = {
    lastName: 'Moritz',
    firstName: 'Müller',
    streetAndHouseNumber: 'Musterstr. 123',
    zipCodeAndCity: '73433 Aalen',
    socialSecurityNumber: '30042022300420223',
    dateOfBirth: '2007-04-20',
    gender: 'male',
    iban: 'DE89 3704 0044 0532 0130 00',
    bic: '37040044',
    personTaxId: '52159893125',
  };

  const response = await extractTextFromFile(
    env,
    fileId,
    fields,
    'Invoice Bill',
    example,
    'extract_text_bill'
  );

  if (isExtractionError(response)) {
    return { error: response.Error };
  }

  if (isTextExtractionDataError(response)) {
    return { error: response.data.response.error };
  }

  return response.data.response;
}

export async function extractTextFromFile(
  session: ISession,
  fileId: string,
  fields: unknown,
  description: string,
  example: Record<string, unknown>,
  tag: string
): Promise<TextExtractionResponse> {
  const file_content = await downloadFile(session, fileId);

  const baseAIClient = await BaseAI.client(session);
  const file_format = await getFileExtensionFromFileId(session, fileId);
  return (await baseAIClient.documentExtraction(
    {
      file_format,
      file_content,
      fields,
      draft_description: description,
      output_example: example,
      timeoutInterval: 90_000,
      vision: false,
      model: 'us.anthropic.claude-3-7-sonnet-20250219-v1:0',
      provider: 'bedrock',
      rules: [
        '1. format dates to ISO format YYYY-MM-DD, example: 2024-07-01',
        '2. format amount to do not have any thousand separator and use the dot as floating point separator. example: 12333.12',
        '3. format currency to be the ISO code of that currency instead of using the sign such as "€". example: EUR',
        '4. when extracting the gender (e.g. "männlich"), use the english equivalent starting with capital letters (e.g. "Male")',
      ].join('\n'),
    },
    tag
  )) as TextExtractionResponse;
}

export async function downloadFile(
  session: ISession,
  fileId: string
): Promise<string> {
  const file = await session.fileStorage.downloadFile(fileId);

  // Convert file raw data to base64 encoded string
  return session.util.encoding.toBase64(file);
}

export async function getFileExtensionFromFileId(
  env: ISession,
  fileId: string
) {
  const [meta] = await env.fileStorage.getMetadata([fileId]);
  if (!meta) {
    throw new Error(`No such file ${fileId}`);
  }
  const { fileName } = meta;
  const fSplit = fileName.split('.');
  const extension = fSplit[fSplit.length - 1];
  return extension;
}

export type TextExtractionError = {
  Error: string;
};

export type TextExtractionData = {
  data: { response?: Record<string, string | number> };
};

export type TextExtractionResponse = TextExtractionError | TextExtractionData;

export function isExtractionError(
  response: TextExtractionResponse
): response is TextExtractionError {
  return 'Error' in response;
}

export function isTextExtractionDataError(
  response: TextExtractionData
): response is { data: { response: { error: string } } } {
  return 'error' in response.data;
}
