{"description": "Loading action for load functions", "applicableStatus": "", "categories": [], "supportedModels": ["urn:evst:everest:fin/integration/ruddr:model/node:RuddrIntegration"], "inputParameters": [{"name": "input", "type": "urn:evst:everest:fin/integration/base:composite:LoadOrMatchInputType"}], "outputValues": [{"name": "output", "type": "urn:evst:everest:fin/integration/base:composite:LoadOrMatchFunctionReturnType"}], "exposeApi": true, "useTransaction": "always"}