import type { ISession } from '@everestsystems/content-core';
import { EvstMigrationIntegrationConfig } from '@pkg/everest.base/types/composites/MigrationIntegrationConfig';
import { EvstMigrationIntegrationContext } from '@pkg/everest.base/types/enums/MigrationIntegrationContext';
import { EvstBankAccountNumberType } from '@pkg/everest.fin.base/types/enums/BankAccountNumberType';
import { EvstBankAccountType } from '@pkg/everest.fin.base/types/enums/BankAccountType';
// import {
//   addIntegrationContext,
//   getIntegrationContext,
// } from '@pkg/everest.fin.integration.base/inbound/ETL/integrationContext';
import type { EvstLoadOrMatchFunctionReturnType } from '@pkg/everest.fin.integration.base/types/composites/LoadOrMatchFunctionReturnType';
import type { EvstLoadOrMatchInputType } from '@pkg/everest.fin.integration.base/types/composites/LoadOrMatchInputType';
import { EvstStagingStatus } from '@pkg/everest.fin.integration.base/types/enums/StagingStatus';
import type { EvstEmployeeTransformedData } from '@pkg/everest.hr.base/types/composites/EmployeeTransformedData';
import { Employee } from '@pkg/everest.hr.base/types/Employee';
// Define the input type
type InputType = EvstLoadOrMatchInputType;
// Define the output type
// If the action is a mediation action, you can return a mediation message
// utilizing the 'message' property of the output type
type OutputType = EvstLoadOrMatchFunctionReturnType;

export default async function loadEmployee(
  env: ISession,
  input: InputType
): Promise<OutputType> {
  const output: OutputType = { status: EvstStagingStatus.Integrated };
  const client = await Employee.client(env);

  const transformedInput: EvstEmployeeTransformedData[] = [];

  const employee = input.data as {
    lastName: string;
    firstName: string;
    streetAndHouseNumber: string;
    zipCodeAndCity: string;
    zipCode: string;
    city: string;
    socialSecurityNumber: string;
    dateOfBirth: string;
    gender: string;
    iban: string;
    bic: string;
    personTaxId: string;
  };

  // transform input to transformedInput
  const employeeDb = await client.read({ id: input.everestId });
  const inp: EvstEmployeeTransformedData = {
    id: input.everestId,
    personId: employeeDb.personId,
    // jobId: employeeDb.
    personEmail: employeeDb.email, // mandatory field
    personLastName: employeeDb.lastName, // mandatory field
    personFirstName: employeeDb.firstName, // mandatory field
    // personEmail: '', // mandatory field
    // personLastName: '', // mandatory field
    // personFirstName: '', // mandatory field
    personSocialSecurityNumber: employee.socialSecurityNumber,
    personDateOfBirth: new Date(employee.dateOfBirth),
    personGender: employee.gender,
    personTaxId: employee.personTaxId,
    homeAddress: {
      line1: employee.streetAndHouseNumber,
      city: employee.city,
      country: 'DE',
      zipCode: employee.zipCode,
    },
    employeeBankDetails: {
      bankAccountNumber: employee.iban,
      bankAccountHolderName: `${employee.firstName} ${employee.lastName}`,
      bankAccountNumberType: EvstBankAccountNumberType.Iban,
      bankAccountType: EvstBankAccountType.Checking,
    },
  };
  inp.personSocialSecurityNumber = employee.socialSecurityNumber;
  transformedInput.push(inp);

  const cfg: EvstMigrationIntegrationConfig = {
    context: EvstMigrationIntegrationContext.Migration,
  };
  const result = await client.updateEmployee(transformedInput, cfg);

  // TODO: Proper error handling

  return output;
}
