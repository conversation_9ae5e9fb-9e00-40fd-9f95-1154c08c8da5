package everest.fin.integration.ruddr

node RuddrIntegration {
	description: 'Ruddr Integration'
	transient
	action memberExtraction(optional input: composite<everest.fin.integration.base::extractActionInput>): UnknownObject
	action loadMembers(input: composite<everest.fin.integration.base::LoadOrMatchInputType>): composite<everest.fin.integration.base::LoadOrMatchFunctionReturnType>
	action loadExpenses(input: composite<everest.fin.integration.base::LoadOrMatchInputType>): composite<everest.fin.integration.base::LoadOrMatchFunctionReturnType>
	action expenseExtraction(optional input: composite<everest.fin.integration.base::extractActionInput>): Array<UnknownObject>
	action loadEmployee(input: composite<everest.fin.integration.base::LoadOrMatchInputType>): composite<everest.fin.integration.base::LoadOrMatchFunctionReturnType>
	action employeeExtractionFromPersonalfragebogen(optional input: composite<everest.fin.integration.base::extractActionInput>): Array<UnknownObject>
	action getIntegrationConfiguration(): JSON
	action syncData(providerModel: Text, mapping: JSON, fetching: JSON): JSON
	action saveConnector(args: JSON): JSON
	action getSecretToken(): Text
	action expenseCategoriesExtraction(optional input: composite<everest.fin.integration.base::extractActionInput>): Array<UnknownObject>
	action formatMatches(providerModel: Text, matches: JSON): JSON
	action getMatchingCandidates(providerModel: Text, optional requiredFieldsForAiMatching: Array<Text>): JSON
	query action getMatchingPrimitiveTypes(providerModel: Text): JSON
	action loadAccount(input: composite<everest.fin.integration.base::LoadOrMatchInputType>): composite<everest.fin.integration.base::LoadOrMatchFunctionReturnType>
	action loadClient(input: composite<everest.fin.integration.base::LoadOrMatchInputType>): composite<everest.fin.integration.base::LoadOrMatchFunctionReturnType>
	action invoiceItemExtraction(optional input: composite<everest.fin.integration.base::extractActionInput>): Array<UnknownObject>
	action invoiceExtraction(optional input: composite<everest.fin.integration.base::extractActionInput>): Array<UnknownObject>
	action paymentExtraction(optional input: composite<everest.fin.integration.base::extractActionInput>): Array<UnknownObject>
	action mapRuddrAddress(input: JSON, optional sessionVariables: Array<UnknownObject>): JSON
	action paymentTermsExtraction(optional input: composite<everest.fin.integration.base::extractActionInput>): Array<UnknownObject>
	action employeeExtractionFromDatev(optional input: composite<everest.fin.integration.base::extractActionInput>): Array<UnknownObject>
	action loadClients(input: composite<everest.fin.integration.base::LoadOrMatchInputType>): composite<everest.fin.integration.base::LoadOrMatchFunctionReturnType>
	action practiceExtraction(optional input: composite<everest.fin.integration.base::extractActionInput>): Array<UnknownObject>
	action timeEntryExtraction(optional input: composite<everest.fin.integration.base::extractActionInput>): Array<UnknownObject>
	action loadAbsences(input: composite<everest.fin.integration.base::LoadOrMatchInputType>): composite<everest.fin.integration.base::LoadOrMatchFunctionReturnType>
	action timeOffTypeExtraction(optional input: composite<everest.fin.integration.base::extractActionInput>): Array<UnknownObject>
}
