/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// ******************************************************************************
// Generated Everest Types
// ******************************************************************************

import type { Decimal, RoundingMode } from '@everestsystems/decimal';
import type {
  ArgType,
  Association,
  AssociationManagementArgs,
  AsyncResult,
  Controller,
  ControllerClientProvider,
  CreateOptionsArg,
  DataWithMetadata,
  DeleteOptionsArg,
  FieldSelector,
  Filter,
  flattenFieldSelector,
  GraphInstanceEntry,
  KernelRequestOptions,
  NonAssociationFields,
  Optional,
  PurgeArgs,
  ReadOptionsArg,
  ReadReturnType as ReadReturnTypeGeneric,
  ReadReturnTypeWithAssociations,
  RequireAtLeastOne,
  SemanticSearchArg,
  SemanticSearchRequestOptions,
  TypeSafeQueryArgType,
  UpdateManyOptionsArg,
  UpdateOptionsArg,
  UpsertOptionsArg,
  WriteReturnType,
  WriteReturnTypeWithAssociations,
} from '@everestsystems/content-core';

import { UIExecutionContext } from '@everestsystems/content-core';
import type { RuddrIntegration as everest_fin_integration_ruddr_model_node_RuddrIntegration } from "@pkg/everest.fin.integration.ruddr/types/RuddrIntegration";
import type orig_memberExtraction from "@pkg/everest.fin.integration.ruddr/actions/extractActions/memberExtraction.action";
import type orig_loadMembers from "@pkg/everest.fin.integration.ruddr/actions/loading/loadMembers.action";
import type orig_loadExpenses from "@pkg/everest.fin.integration.ruddr/actions/loading/loadExpenses.action";
import type orig_expenseExtraction from "@pkg/everest.fin.integration.ruddr/actions/extractActions/expenseExtraction.action";
import type orig_loadEmployee from "@pkg/everest.fin.integration.ruddr/actions/loading/loadEmployee.action";
import type orig_employeeExtractionFromPersonalfragebogen from "@pkg/everest.fin.integration.ruddr/actions/extractActions/employeeExtractionFromPersonalfragebogen.action";
import type orig_getIntegrationConfiguration from "@pkg/everest.fin.integration.ruddr/actions/template/configuration/getIntegrationConfiguration.action";
import type orig_syncData from "@pkg/everest.fin.integration.ruddr/actions/template/syncData/syncData.action";
import type orig_saveConnector from "@pkg/everest.fin.integration.ruddr/actions/connector/saveConnector.action";
import type orig_getSecretToken from "@pkg/everest.fin.integration.ruddr/actions/secret/getSecretToken.action";
import type orig_expenseCategoriesExtraction from "@pkg/everest.fin.integration.ruddr/actions/extractActions/expenseCategoriesExtraction.action";
import type orig_formatMatches from "@pkg/everest.fin.integration.ruddr/actions/template/matching/formatMatches.action";
import type orig_getMatchingCandidates from "@pkg/everest.fin.integration.ruddr/actions/template/matching/getMatchingCandidates.action";
import type orig_getMatchingPrimitiveTypes from "@pkg/everest.fin.integration.ruddr/actions/template/matching/getMatchingPrimitiveTypes.action";
import type orig_loadAccount from "@pkg/everest.fin.integration.ruddr/actions/loading/loadAccount.action";
import type orig_loadClient from "@pkg/everest.fin.integration.ruddr/actions/loading/loadClient.action";
import type orig_invoiceItemExtraction from "@pkg/everest.fin.integration.ruddr/actions/extractActions/invoiceItemExtraction.action";
import type orig_invoiceExtraction from "@pkg/everest.fin.integration.ruddr/actions/extractActions/invoiceExtraction.action";
import type orig_paymentExtraction from "@pkg/everest.fin.integration.ruddr/actions/extractActions/paymentExtraction.action";
import type orig_mapRuddrAddress from "@pkg/everest.fin.integration.ruddr/actions/actionExecutor/mapRuddrAddress.action";
import type orig_paymentTermsExtraction from "@pkg/everest.fin.integration.ruddr/actions/extractActions/paymentTermsExtraction.action";
import type orig_employeeExtractionFromDatev from "@pkg/everest.fin.integration.ruddr/actions/extractActions/employeeExtractionFromDatev.action";
import type orig_loadClients from "@pkg/everest.fin.integration.ruddr/actions/loading/loadClients.action";
import type orig_practiceExtraction from "@pkg/everest.fin.integration.ruddr/actions/extractActions/practiceExtraction.action";
import type orig_timeEntryExtraction from "@pkg/everest.fin.integration.ruddr/actions/extractActions/timeEntryExtraction.action";
import type orig_loadAbsences from "@pkg/everest.fin.integration.ruddr/actions/loading/loadAbsences.action";
import type orig_timeOffTypeExtraction from "@pkg/everest.fin.integration.ruddr/actions/extractActions/timeOffTypeExtraction.action";

/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */

type RequiredContext = {
  data: Record<string, any>;
  actions: {
    run: (...args: any[]) => any;
  };
};

type ApiError = {
  message: string;
  name: string;
  errorCode: string;
  stack?: string;
};

type InnerPromiseType<T> = T extends Promise<infer R> ? R : T;

type ActionResultOrError<T1 extends string | number | symbol, T2> = {[P in T1]?: T2} & {error?: ApiError};

type ArrayIfNeeded<C extends RequiredContext, K extends keyof C['data'], T> = C['data'][K] extends UIExecutionContext.UIModelNodeListType<any> ? T[] : T;

export namespace RuddrIntegrationUI {
  /**
   * Ruddr Integration
   */
  export type RuddrIntegrationWithAssociation = RuddrIntegration & {

    };
  export interface IControllerClient extends everest_fin_integration_ruddr_model_node_RuddrIntegration.IControllerClient {}

  export type RuddrIntegration = everest_fin_integration_ruddr_model_node_RuddrIntegration.RuddrIntegration;
  export type CreationFields = Pick<RuddrIntegration, 'externalId' | 'active'>;
  export type UniqueFields = Pick<RuddrIntegration, 'id'>;
  export type UniqueWhereInput = (RequireAtLeastOne<UniqueFields>) & Partial<RuddrIntegration>;
  export type ReadReturnType<U extends string | number | symbol = keyof RuddrIntegration> = ReadReturnTypeGeneric<RuddrIntegration, U>;

  class ActionRequest<C extends RequiredContext, FuncReturnType> {
    constructor(private readonly context: C, private readonly partialPayload: Record<string, any>) {
    }

    private async __run__(payload: any) {
      return this.context.actions.run(payload);
    }

    payload<K extends keyof C['data']>(nodeAlias: K): Record<string, any> {
      return {[nodeAlias]: this.partialPayload};
    }

    run<K extends keyof C['data']>(nodeAlias: K): Promise<ActionResultOrError<K, ArrayIfNeeded<C, K, InnerPromiseType<FuncReturnType>>>> {
      const payload = this.payload(nodeAlias);
      return this.__run__(payload);
    }
  }

  type func_memberExtraction = typeof orig_memberExtraction;
  /** @deprecated use ```client.memberExtraction``` instead */
  export function memberExtraction<C extends RequiredContext>(context: C, args: { input?: Parameters<func_memberExtraction>[1] }): ActionRequest<C, ReturnType<func_memberExtraction>> {
    const partialPayload = {action: 'memberExtraction', data: args}

    return new ActionRequest<C, ReturnType<func_memberExtraction>>(context, partialPayload);
  }
  type func_loadMembers = typeof orig_loadMembers;
  /** @deprecated use ```client.loadMembers``` instead */
  export function loadMembers<C extends RequiredContext>(context: C, args: { input: Parameters<func_loadMembers>[1] }): ActionRequest<C, ReturnType<func_loadMembers>> {
    const partialPayload = {action: 'loadMembers', data: args}

    return new ActionRequest<C, ReturnType<func_loadMembers>>(context, partialPayload);
  }
  type func_loadExpenses = typeof orig_loadExpenses;
  /** @deprecated use ```client.loadExpenses``` instead */
  export function loadExpenses<C extends RequiredContext>(context: C, args: { input: Parameters<func_loadExpenses>[1] }): ActionRequest<C, ReturnType<func_loadExpenses>> {
    const partialPayload = {action: 'loadExpenses', data: args}

    return new ActionRequest<C, ReturnType<func_loadExpenses>>(context, partialPayload);
  }
  type func_expenseExtraction = typeof orig_expenseExtraction;
  /** @deprecated use ```client.expenseExtraction``` instead */
  export function expenseExtraction<C extends RequiredContext>(context: C, args: { input?: Parameters<func_expenseExtraction>[1] }): ActionRequest<C, ReturnType<func_expenseExtraction>> {
    const partialPayload = {action: 'expenseExtraction', data: args}

    return new ActionRequest<C, ReturnType<func_expenseExtraction>>(context, partialPayload);
  }
  type func_loadEmployee = typeof orig_loadEmployee;
  /** @deprecated use ```client.loadEmployee``` instead */
  export function loadEmployee<C extends RequiredContext>(context: C, args: { input: Parameters<func_loadEmployee>[1] }): ActionRequest<C, ReturnType<func_loadEmployee>> {
    const partialPayload = {action: 'loadEmployee', data: args}

    return new ActionRequest<C, ReturnType<func_loadEmployee>>(context, partialPayload);
  }
  type func_employeeExtractionFromPersonalfragebogen = typeof orig_employeeExtractionFromPersonalfragebogen;
  /** @deprecated use ```client.employeeExtractionFromPersonalfragebogen``` instead */
  export function employeeExtractionFromPersonalfragebogen<C extends RequiredContext>(context: C, args: { input?: Parameters<func_employeeExtractionFromPersonalfragebogen>[1] }): ActionRequest<C, ReturnType<func_employeeExtractionFromPersonalfragebogen>> {
    const partialPayload = {action: 'employeeExtractionFromPersonalfragebogen', data: args}

    return new ActionRequest<C, ReturnType<func_employeeExtractionFromPersonalfragebogen>>(context, partialPayload);
  }
  type func_getIntegrationConfiguration = typeof orig_getIntegrationConfiguration;
  /** @deprecated use ```client.getIntegrationConfiguration``` instead */
  export function getIntegrationConfiguration<C extends RequiredContext>(context: C): ActionRequest<C, ReturnType<func_getIntegrationConfiguration>> {
    const partialPayload = {action: 'getIntegrationConfiguration', data: {}}

    return new ActionRequest<C, ReturnType<func_getIntegrationConfiguration>>(context, partialPayload);
  }
  type func_syncData = typeof orig_syncData;
  /** @deprecated use ```client.syncData``` instead */
  export function syncData<C extends RequiredContext>(context: C, args: { providerModel: Parameters<func_syncData>[1]; mapping: Parameters<func_syncData>[2]; fetching: Parameters<func_syncData>[3] }): ActionRequest<C, ReturnType<func_syncData>> {
    const partialPayload = {action: 'syncData', data: args}

    return new ActionRequest<C, ReturnType<func_syncData>>(context, partialPayload);
  }
  type func_saveConnector = typeof orig_saveConnector;
  /** @deprecated use ```client.saveConnector``` instead */
  export function saveConnector<C extends RequiredContext>(context: C, args: { args: Parameters<func_saveConnector>[1] }): ActionRequest<C, ReturnType<func_saveConnector>> {
    const partialPayload = {action: 'saveConnector', data: args}

    return new ActionRequest<C, ReturnType<func_saveConnector>>(context, partialPayload);
  }
  type func_getSecretToken = typeof orig_getSecretToken;
  /** @deprecated use ```client.getSecretToken``` instead */
  export function getSecretToken<C extends RequiredContext>(context: C): ActionRequest<C, ReturnType<func_getSecretToken>> {
    const partialPayload = {action: 'getSecretToken', data: {}}

    return new ActionRequest<C, ReturnType<func_getSecretToken>>(context, partialPayload);
  }
  type func_expenseCategoriesExtraction = typeof orig_expenseCategoriesExtraction;
  /** @deprecated use ```client.expenseCategoriesExtraction``` instead */
  export function expenseCategoriesExtraction<C extends RequiredContext>(context: C, args: { input?: Parameters<func_expenseCategoriesExtraction>[1] }): ActionRequest<C, ReturnType<func_expenseCategoriesExtraction>> {
    const partialPayload = {action: 'expenseCategoriesExtraction', data: args}

    return new ActionRequest<C, ReturnType<func_expenseCategoriesExtraction>>(context, partialPayload);
  }
  type func_formatMatches = typeof orig_formatMatches;
  /** @deprecated use ```client.formatMatches``` instead */
  export function formatMatches<C extends RequiredContext>(context: C, args: { providerModel: Parameters<func_formatMatches>[1]; matches: Parameters<func_formatMatches>[2] }): ActionRequest<C, ReturnType<func_formatMatches>> {
    const partialPayload = {action: 'formatMatches', data: args}

    return new ActionRequest<C, ReturnType<func_formatMatches>>(context, partialPayload);
  }
  type func_getMatchingCandidates = typeof orig_getMatchingCandidates;
  /** @deprecated use ```client.getMatchingCandidates``` instead */
  export function getMatchingCandidates<C extends RequiredContext>(context: C, args: { providerModel: Parameters<func_getMatchingCandidates>[1]; requiredFieldsForAiMatching?: Parameters<func_getMatchingCandidates>[2] }): ActionRequest<C, ReturnType<func_getMatchingCandidates>> {
    const partialPayload = {action: 'getMatchingCandidates', data: args}

    return new ActionRequest<C, ReturnType<func_getMatchingCandidates>>(context, partialPayload);
  }
  type func_getMatchingPrimitiveTypes = typeof orig_getMatchingPrimitiveTypes;
  /** @deprecated use ```client.getMatchingPrimitiveTypes``` instead */
  export function getMatchingPrimitiveTypes<C extends RequiredContext>(context: C, args: { providerModel: Parameters<func_getMatchingPrimitiveTypes>[1] }): ActionRequest<C, ReturnType<func_getMatchingPrimitiveTypes>> {
    const partialPayload = {action: 'getMatchingPrimitiveTypes', data: args}

    return new ActionRequest<C, ReturnType<func_getMatchingPrimitiveTypes>>(context, partialPayload);
  }
  type func_loadAccount = typeof orig_loadAccount;
  /** @deprecated use ```client.loadAccount``` instead */
  export function loadAccount<C extends RequiredContext>(context: C, args: { input: Parameters<func_loadAccount>[1] }): ActionRequest<C, ReturnType<func_loadAccount>> {
    const partialPayload = {action: 'loadAccount', data: args}

    return new ActionRequest<C, ReturnType<func_loadAccount>>(context, partialPayload);
  }
  type func_loadClient = typeof orig_loadClient;
  /** @deprecated use ```client.loadClient``` instead */
  export function loadClient<C extends RequiredContext>(context: C, args: { input: Parameters<func_loadClient>[1] }): ActionRequest<C, ReturnType<func_loadClient>> {
    const partialPayload = {action: 'loadClient', data: args}

    return new ActionRequest<C, ReturnType<func_loadClient>>(context, partialPayload);
  }
  type func_invoiceItemExtraction = typeof orig_invoiceItemExtraction;
  /** @deprecated use ```client.invoiceItemExtraction``` instead */
  export function invoiceItemExtraction<C extends RequiredContext>(context: C, args: { input?: Parameters<func_invoiceItemExtraction>[1] }): ActionRequest<C, ReturnType<func_invoiceItemExtraction>> {
    const partialPayload = {action: 'invoiceItemExtraction', data: args}

    return new ActionRequest<C, ReturnType<func_invoiceItemExtraction>>(context, partialPayload);
  }
  type func_invoiceExtraction = typeof orig_invoiceExtraction;
  /** @deprecated use ```client.invoiceExtraction``` instead */
  export function invoiceExtraction<C extends RequiredContext>(context: C, args: { input?: Parameters<func_invoiceExtraction>[1] }): ActionRequest<C, ReturnType<func_invoiceExtraction>> {
    const partialPayload = {action: 'invoiceExtraction', data: args}

    return new ActionRequest<C, ReturnType<func_invoiceExtraction>>(context, partialPayload);
  }
  type func_paymentExtraction = typeof orig_paymentExtraction;
  /** @deprecated use ```client.paymentExtraction``` instead */
  export function paymentExtraction<C extends RequiredContext>(context: C, args: { input?: Parameters<func_paymentExtraction>[1] }): ActionRequest<C, ReturnType<func_paymentExtraction>> {
    const partialPayload = {action: 'paymentExtraction', data: args}

    return new ActionRequest<C, ReturnType<func_paymentExtraction>>(context, partialPayload);
  }
  type func_mapRuddrAddress = typeof orig_mapRuddrAddress;
  /** @deprecated use ```client.mapRuddrAddress``` instead */
  export function mapRuddrAddress<C extends RequiredContext>(context: C, args: { input: Parameters<func_mapRuddrAddress>[1]; sessionVariables?: Parameters<func_mapRuddrAddress>[2] }): ActionRequest<C, ReturnType<func_mapRuddrAddress>> {
    const partialPayload = {action: 'mapRuddrAddress', data: args}

    return new ActionRequest<C, ReturnType<func_mapRuddrAddress>>(context, partialPayload);
  }
  type func_paymentTermsExtraction = typeof orig_paymentTermsExtraction;
  /** @deprecated use ```client.paymentTermsExtraction``` instead */
  export function paymentTermsExtraction<C extends RequiredContext>(context: C, args: { input?: Parameters<func_paymentTermsExtraction>[1] }): ActionRequest<C, ReturnType<func_paymentTermsExtraction>> {
    const partialPayload = {action: 'paymentTermsExtraction', data: args}

    return new ActionRequest<C, ReturnType<func_paymentTermsExtraction>>(context, partialPayload);
  }
  type func_employeeExtractionFromDatev = typeof orig_employeeExtractionFromDatev;
  /** @deprecated use ```client.employeeExtractionFromDatev``` instead */
  export function employeeExtractionFromDatev<C extends RequiredContext>(context: C, args: { input?: Parameters<func_employeeExtractionFromDatev>[1] }): ActionRequest<C, ReturnType<func_employeeExtractionFromDatev>> {
    const partialPayload = {action: 'employeeExtractionFromDatev', data: args}

    return new ActionRequest<C, ReturnType<func_employeeExtractionFromDatev>>(context, partialPayload);
  }
  type func_loadClients = typeof orig_loadClients;
  /** @deprecated use ```client.loadClients``` instead */
  export function loadClients<C extends RequiredContext>(context: C, args: { input: Parameters<func_loadClients>[1] }): ActionRequest<C, ReturnType<func_loadClients>> {
    const partialPayload = {action: 'loadClients', data: args}

    return new ActionRequest<C, ReturnType<func_loadClients>>(context, partialPayload);
  }
  type func_practiceExtraction = typeof orig_practiceExtraction;
  /** @deprecated use ```client.practiceExtraction``` instead */
  export function practiceExtraction<C extends RequiredContext>(context: C, args: { input?: Parameters<func_practiceExtraction>[1] }): ActionRequest<C, ReturnType<func_practiceExtraction>> {
    const partialPayload = {action: 'practiceExtraction', data: args}

    return new ActionRequest<C, ReturnType<func_practiceExtraction>>(context, partialPayload);
  }
  type func_timeEntryExtraction = typeof orig_timeEntryExtraction;
  /** @deprecated use ```client.timeEntryExtraction``` instead */
  export function timeEntryExtraction<C extends RequiredContext>(context: C, args: { input?: Parameters<func_timeEntryExtraction>[1] }): ActionRequest<C, ReturnType<func_timeEntryExtraction>> {
    const partialPayload = {action: 'timeEntryExtraction', data: args}

    return new ActionRequest<C, ReturnType<func_timeEntryExtraction>>(context, partialPayload);
  }
  type func_loadAbsences = typeof orig_loadAbsences;
  /** @deprecated use ```client.loadAbsences``` instead */
  export function loadAbsences<C extends RequiredContext>(context: C, args: { input: Parameters<func_loadAbsences>[1] }): ActionRequest<C, ReturnType<func_loadAbsences>> {
    const partialPayload = {action: 'loadAbsences', data: args}

    return new ActionRequest<C, ReturnType<func_loadAbsences>>(context, partialPayload);
  }
  type func_timeOffTypeExtraction = typeof orig_timeOffTypeExtraction;
  /** @deprecated use ```client.timeOffTypeExtraction``` instead */
  export function timeOffTypeExtraction<C extends RequiredContext>(context: C, args: { input?: Parameters<func_timeOffTypeExtraction>[1] }): ActionRequest<C, ReturnType<func_timeOffTypeExtraction>> {
    const partialPayload = {action: 'timeOffTypeExtraction', data: args}

    return new ActionRequest<C, ReturnType<func_timeOffTypeExtraction>>(context, partialPayload);
  }
  /** @deprecated - use {@link MODEL_URN} instead */
  export const name = 'urn:evst:everest:fin/integration/ruddr:model/node:RuddrIntegration';
  export const MODEL_URN = 'urn:evst:everest:fin/integration/ruddr:model/node:RuddrIntegration';
  /** @deprecated - use {@link MODEL_URN} instead */
  export const MODEL_ID = 'everest.fin.integration.ruddr/RuddrIntegrationModel.RuddrIntegration';
  export const MODEL_UUID = '0737a97e-1c67-4e61-81cc-7ef7243d8250';

  /** @deprecated use ```client``` instead */
  export function create<C extends RequiredContext, U extends keyof RuddrIntegration>(context: C, input: CreationFields, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<RuddrIntegration, U>>> {
    const convertedArgs = {data: input, fieldList: fieldlist, options: options} as Record<string, any>
    const partialPayload = {action: 'create', ...convertedArgs}

    return new ActionRequest<C, Promise<WriteReturnType<RuddrIntegration, U>>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function createMany<C extends RequiredContext, U extends keyof RuddrIntegration>(context: C, input: CreationFields[], fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<RuddrIntegration, U>[]>> {
    const convertedArgs = {data: input, fieldList: fieldlist, options: options} as Record<string, any>
    const partialPayload = {action: 'createMany', ...convertedArgs}

    return new ActionRequest<C, Promise<WriteReturnType<RuddrIntegration, U>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function Delete<C extends RequiredContext>(context: C, where: UniqueWhereInput, options?: undefined): ActionRequest<C, Promise<Partial<RuddrIntegration>[]>> {
    const convertedArgs = {where: where, options} as Record<string, any>
    const partialPayload = {action: 'delete', ...convertedArgs}

    return new ActionRequest<C, Promise<Partial<RuddrIntegration>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function deleteAll<C extends RequiredContext>(context: C): ActionRequest<C, Promise<Partial<RuddrIntegration>[]>> {
    const convertedArgs = {data: {}} as Record<string, any>
    const partialPayload = {action: 'deleteAll', ...convertedArgs}

    return new ActionRequest<C, Promise<Partial<RuddrIntegration>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function deleteMany<C extends RequiredContext>(context: C, where: Filter<RuddrIntegrationWithAssociation>): ActionRequest<C, Promise<Partial<RuddrIntegration>[]>>;
  /** @deprecated use ```client``` instead */
  export function deleteMany<C extends RequiredContext>(context: C, where: Partial<RuddrIntegration>): ActionRequest<C, Promise<Partial<RuddrIntegration>[]>>;
  /** @deprecated use ```client``` instead */
  export function deleteMany<C extends RequiredContext>(context: C, where: Filter<RuddrIntegration> | Partial<RuddrIntegration>, options?: undefined): ActionRequest<C, Promise<Partial<RuddrIntegration>[]>> {
    const convertedArgs = {data: {where: where}, options} as Record<string, any>
    const partialPayload = {action: 'deleteMany', ...convertedArgs}

    return new ActionRequest<C, Promise<Partial<RuddrIntegration>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function query<C extends RequiredContext, U extends FieldSelector<RuddrIntegrationWithAssociation>>(context: C, args: Omit<TypeSafeQueryArgType<RuddrIntegrationWithAssociation>, 'draft'>, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<ReadReturnTypeWithAssociations<RuddrIntegrationWithAssociation, U>[]>>;
  /** @deprecated use ```client``` instead */
  export function query<C extends RequiredContext, U extends keyof RuddrIntegration, V extends string = 'ALL_FIELDS'>(context: C, args: Omit<TypeSafeQueryArgType<RuddrIntegration>, 'draft'> | Omit<Omit<TypeSafeQueryArgType<RuddrIntegration>, 'draft'>, 'where'> & { where?: Partial<RuddrIntegration> }, fieldlist: ReadonlyArray<U | V> | 'ALL_FIELDS'): ActionRequest<C, Promise<ReadReturnType<V>[]>>;
  /** @deprecated use ```client``` instead */
  export function query<C extends RequiredContext>(context: C, args: Omit<TypeSafeQueryArgType<RuddrIntegration>, 'draft'>, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS'): ActionRequest<C, Promise<unknown[]>> {
    const convertedArgs = {data: {...args, fieldList: fieldlist}} as Record<string, any>
    const partialPayload = {action: 'query', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function read<C extends RequiredContext, U extends FieldSelector<RuddrIntegrationWithAssociation>>(context: C, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): ActionRequest<C, Promise<ReadReturnTypeWithAssociations<RuddrIntegrationWithAssociation, U>>>;
  /** @deprecated use ```client``` instead */
  export function read<C extends RequiredContext, U extends keyof RuddrIntegration>(context: C, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): ActionRequest<C, Promise<ReadReturnType<U>>>;
  /** @deprecated use ```client``` instead */
  export function read<C extends RequiredContext>(context: C, where: UniqueWhereInput, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): ActionRequest<C, Promise<unknown>> {
    const convertedArgs = {data: {where: where, fieldList: fieldlist}, options: options} as Record<string, any>
    const partialPayload = {action: 'read', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function update<C extends RequiredContext, U extends FieldSelector<RuddrIntegrationWithAssociation>>(context: C, where: UniqueWhereInput, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<WriteReturnTypeWithAssociations<RuddrIntegrationWithAssociation, U>>>;
  /** @deprecated use ```client``` instead */
  export function update<C extends RequiredContext, U extends keyof RuddrIntegration>(context: C, where: Partial<RuddrIntegration>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<WriteReturnType<RuddrIntegration, U>>>;
  /** @deprecated use ```client``` instead */
  export function update<C extends RequiredContext>(context: C, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: undefined): ActionRequest<C, Promise<unknown>> {
    const convertedArgs = {where: where, data: data, fieldList: fieldlist, options} as Record<string, any>
    const partialPayload = {action: 'update', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function updateMany<C extends RequiredContext, U extends FieldSelector<RuddrIntegrationWithAssociation>>(context: C, where: Filter<RuddrIntegrationWithAssociation>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<WriteReturnTypeWithAssociations<RuddrIntegrationWithAssociation, U>[]>>;
  /** @deprecated use ```client``` instead */
  export function updateMany<C extends RequiredContext, U extends keyof RuddrIntegration>(context: C, where: Partial<RuddrIntegration>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<WriteReturnType<RuddrIntegration, U>[]>>;
  /** @deprecated use ```client``` instead */
  export function updateMany<C extends RequiredContext>(context: C, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: undefined): ActionRequest<C, Promise<unknown[]>> {
    const convertedArgs = {data: {where: where, data: data, fieldList: fieldlist}, options} as Record<string, any>
    const partialPayload = {action: 'updateMany', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function upsert<C extends RequiredContext, U extends keyof RuddrIntegration & string>(context: C, data: Partial<RuddrIntegration>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<RuddrIntegration, U>>>;
  /** @deprecated use ```client``` instead */
  export function upsert<C extends RequiredContext, U extends keyof RuddrIntegration & string>(context: C, where: Partial<RuddrIntegration>, data: Partial<RuddrIntegration>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<RuddrIntegration, U>>>;
  /** @deprecated use ```client``` instead */
  export function upsert<C extends RequiredContext, U extends keyof RuddrIntegration & string>(context: C, whereOrData: Partial<RuddrIntegration>, dataOrFieldList?: Partial<RuddrIntegration> | ReadonlyArray<U> | 'ALL_FIELDS', fieldlistOrOptions?: ReadonlyArray<U> | 'ALL_FIELDS' | Omit<UpsertOptionsArg, 'draft'>, maybeOptions?: Omit<UpsertOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<RuddrIntegration, U>>> {
    const convertedArgs = {data: {where: whereOrData, data: dataOrFieldList, fieldList: fieldlistOrOptions}, options: maybeOptions} as Record<string, any>
    const partialPayload = {action: 'upsert', ...convertedArgs}

    return new ActionRequest<C, Promise<WriteReturnType<RuddrIntegration, U>>>(context, partialPayload);
  }
  export namespace client {
    /** This is an extraction action */
    export declare function memberExtraction(args: { input?: Parameters<func_memberExtraction>[1] }): ReturnType<func_memberExtraction>
    /** Loading action for load functions */
    export declare function loadMembers(args: { input: Parameters<func_loadMembers>[1] }): ReturnType<func_loadMembers>
    /** Loading action for load functions */
    export declare function loadExpenses(args: { input: Parameters<func_loadExpenses>[1] }): ReturnType<func_loadExpenses>
    /** This is an extraction action */
    export declare function expenseExtraction(args: { input?: Parameters<func_expenseExtraction>[1] }): ReturnType<func_expenseExtraction>
    /** Loading action for load functions */
    export declare function loadEmployee(args: { input: Parameters<func_loadEmployee>[1] }): ReturnType<func_loadEmployee>
    /** This is an extraction action */
    export declare function employeeExtractionFromPersonalfragebogen(args: { input?: Parameters<func_employeeExtractionFromPersonalfragebogen>[1] }): ReturnType<func_employeeExtractionFromPersonalfragebogen>
    /** Get Ruddr integration configuration for integration.base.ui */
    export declare function getIntegrationConfiguration(): ReturnType<func_getIntegrationConfiguration>
    /** Executes ETL for a given model */
    export declare function syncData(args: { providerModel: Parameters<func_syncData>[1]; mapping: Parameters<func_syncData>[2]; fetching: Parameters<func_syncData>[3] }): ReturnType<func_syncData>
    /** Save a Ruddr connector */
    export declare function saveConnector(args: { args: Parameters<func_saveConnector>[1] }): ReturnType<func_saveConnector>
    /** Get Secret Token */
    export declare function getSecretToken(): ReturnType<func_getSecretToken>
    /** This is an extraction action */
    export declare function expenseCategoriesExtraction(args: { input?: Parameters<func_expenseCategoriesExtraction>[1] }): ReturnType<func_expenseCategoriesExtraction>
    /** Formats matches */
    export declare function formatMatches(args: { providerModel: Parameters<func_formatMatches>[1]; matches: Parameters<func_formatMatches>[2] }): ReturnType<func_formatMatches>
    /** Gets matching candidates */
    export declare function getMatchingCandidates(args: { providerModel: Parameters<func_getMatchingCandidates>[1]; requiredFieldsForAiMatching?: Parameters<func_getMatchingCandidates>[2] }): ReturnType<func_getMatchingCandidates>
    /** Gets matching primitive types */
    export declare function getMatchingPrimitiveTypes(args: { providerModel: Parameters<func_getMatchingPrimitiveTypes>[1] }): ReturnType<func_getMatchingPrimitiveTypes>
    /** Loading action for load functions */
    export declare function loadAccount(args: { input: Parameters<func_loadAccount>[1] }): ReturnType<func_loadAccount>
    /** Loading action for load functions */
    export declare function loadClient(args: { input: Parameters<func_loadClient>[1] }): ReturnType<func_loadClient>
    /** This is an extraction action */
    export declare function invoiceItemExtraction(args: { input?: Parameters<func_invoiceItemExtraction>[1] }): ReturnType<func_invoiceItemExtraction>
    /** This is an extraction action */
    export declare function invoiceExtraction(args: { input?: Parameters<func_invoiceExtraction>[1] }): ReturnType<func_invoiceExtraction>
    /** This is an extraction action */
    export declare function paymentExtraction(args: { input?: Parameters<func_paymentExtraction>[1] }): ReturnType<func_paymentExtraction>
    /** Action for ActionExecutor transformation operator */
    export declare function mapRuddrAddress(args: { input: Parameters<func_mapRuddrAddress>[1]; sessionVariables?: Parameters<func_mapRuddrAddress>[2] }): ReturnType<func_mapRuddrAddress>
    /** This is an extraction action */
    export declare function paymentTermsExtraction(args: { input?: Parameters<func_paymentTermsExtraction>[1] }): ReturnType<func_paymentTermsExtraction>
    /** This is an extraction action */
    export declare function employeeExtractionFromDatev(args: { input?: Parameters<func_employeeExtractionFromDatev>[1] }): ReturnType<func_employeeExtractionFromDatev>
    /** Loading action for load functions */
    export declare function loadClients(args: { input: Parameters<func_loadClients>[1] }): ReturnType<func_loadClients>
    /** This is an extraction action */
    export declare function practiceExtraction(args: { input?: Parameters<func_practiceExtraction>[1] }): ReturnType<func_practiceExtraction>
    /** This is an extraction action */
    export declare function timeEntryExtraction(args: { input?: Parameters<func_timeEntryExtraction>[1] }): ReturnType<func_timeEntryExtraction>
    /** Loading action for load functions */
    export declare function loadAbsences(args: { input: Parameters<func_loadAbsences>[1] }): ReturnType<func_loadAbsences>
    /** This is an extraction action */
    export declare function timeOffTypeExtraction(args: { input?: Parameters<func_timeOffTypeExtraction>[1] }): ReturnType<func_timeOffTypeExtraction>
    /** write a new object to the database. */
    export declare function create<U extends keyof RuddrIntegration>(input: CreationFields, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): Promise<WriteReturnType<RuddrIntegration, U>>

    /** write new objects to the database. */
    export declare function createMany<U extends keyof RuddrIntegration>(input: CreationFields[], fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): Promise<WriteReturnType<RuddrIntegration, U>[]>

    /** delete the object that is uniquely identified by the where condition. */
    export declare function Delete(where: UniqueWhereInput, options?: undefined): Promise<Partial<RuddrIntegration>[]>

    /** delete all objects. */
    export declare function deleteAll(): Promise<Partial<RuddrIntegration>[]>

    /** delete the objects that are identified by the where condition. */
    export declare function deleteMany(where: Filter<RuddrIntegrationWithAssociation>): Promise<Partial<RuddrIntegration>[]>;
    /** @deprecated use the alternative overloaded function */
    export declare function deleteMany(where: Partial<RuddrIntegration>): Promise<Partial<RuddrIntegration>[]>;
    export declare function deleteMany(where: Filter<RuddrIntegration> | Partial<RuddrIntegration>, options?: undefined): Promise<Partial<RuddrIntegration>[]>

    /** returns a list of all objects matching the requirements. */
    export declare function query<U extends FieldSelector<RuddrIntegrationWithAssociation>>(args: Omit<TypeSafeQueryArgType<RuddrIntegrationWithAssociation>, 'draft'>, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<ReadReturnTypeWithAssociations<RuddrIntegrationWithAssociation, U>[]>;
    /** @deprecated Use the alternative overloaded method for querying associations. */
    export declare function query<U extends keyof RuddrIntegration, V extends string = 'ALL_FIELDS'>(args: Omit<TypeSafeQueryArgType<RuddrIntegration>, 'draft'> | Omit<Omit<TypeSafeQueryArgType<RuddrIntegration>, 'draft'>, 'where'> & { where?: Partial<RuddrIntegration> }, fieldlist: ReadonlyArray<U | V> | 'ALL_FIELDS'): Promise<ReadReturnType<V>[]>;
    export declare function query(args: Omit<TypeSafeQueryArgType<RuddrIntegration>, 'draft'>, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown[]>

    /** reads the object that is uniquely identified by the where condition. */
    export declare function read<U extends FieldSelector<RuddrIntegrationWithAssociation>>(where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnTypeWithAssociations<RuddrIntegrationWithAssociation, U>>;
    /** @deprecated Use the alternative overloaded method for reading associations. */
    export declare function read<U extends keyof RuddrIntegration>(where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnType<U>>;
    export declare function read(where: UniqueWhereInput, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<unknown>

    /** writes modified values back to the database for a given node instance. */
    export declare function update<U extends FieldSelector<RuddrIntegrationWithAssociation>>(where: UniqueWhereInput, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnTypeWithAssociations<RuddrIntegrationWithAssociation, U>>;
    /** @deprecated Use the alternative overloaded method for writing with associations. */
    export declare function update<U extends keyof RuddrIntegration>(where: Partial<RuddrIntegration>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnType<RuddrIntegration, U>>;
    export declare function update(where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: undefined): Promise<unknown>

    /** writes modified values back to the database for selected node instances. */
    export declare function updateMany<U extends FieldSelector<RuddrIntegrationWithAssociation>>(where: Filter<RuddrIntegrationWithAssociation>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnTypeWithAssociations<RuddrIntegrationWithAssociation, U>[]>;
    /** @deprecated Use the alternative overloaded method for writing with associations. */
    export declare function updateMany<U extends keyof RuddrIntegration>(where: Partial<RuddrIntegration>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnType<RuddrIntegration, U>[]>;
    export declare function updateMany(where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: undefined): Promise<unknown[]>

    /** creates or updates object identified by where. */
    export declare function upsert<U extends keyof RuddrIntegration & string>(data: Partial<RuddrIntegration>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<RuddrIntegration, U>>;
    /** @deprecated use the overload without an explicit where clause */
    export declare function upsert<U extends keyof RuddrIntegration & string>(where: Partial<RuddrIntegration>, data: Partial<RuddrIntegration>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<RuddrIntegration, U>>;
    export declare function upsert<U extends keyof RuddrIntegration & string>(whereOrData: Partial<RuddrIntegration>, dataOrFieldList?: Partial<RuddrIntegration> | ReadonlyArray<U> | 'ALL_FIELDS', fieldlistOrOptions?: ReadonlyArray<U> | 'ALL_FIELDS' | Omit<UpsertOptionsArg, 'draft'>, maybeOptions?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<RuddrIntegration, U>>
  }
}
