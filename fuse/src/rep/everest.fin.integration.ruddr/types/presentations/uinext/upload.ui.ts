/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

// ******************************************************************************
// Generated Everest Presentation UI Types
// ******************************************************************************

import type {
  GetElementType,
  UIExecutionContext
} from '@everestsystems/content-core';

import type { EvstBlobRef as everest_appserver_composite_BlobRef } from "@pkg/everest.appserver/types/composites/BlobRef";
import type { EvstEmployeeType as everest_hr_base_primitive_EmployeeType } from "@pkg/everest.hr.base/types/primitives/EmployeeType";
import type { EvstPersonalQuestionnaireFileType as everest_fin_integration_ruddr_enum_PersonalQuestionnaireFileType } from "@pkg/everest.fin.integration.ruddr/types/enums/PersonalQuestionnaireFileType";

export namespace uploadPresentationUI {
  export namespace dataSets {
    export namespace employee {
      export type instance = {
        employeeDetails?: everest_appserver_composite_BlobRef | undefined | null;
        employeeId?: everest_hr_base_primitive_EmployeeType | undefined | null;
        fileType?: everest_fin_integration_ruddr_enum_PersonalQuestionnaireFileType | undefined | null;
        /** Unique UI identifier of the node instance */
        _nodeReference: string;
      };

      export type data = instance;
    }
  }

  export namespace actions {
    export namespace extractData {
      export type input = never;
      export type output = void;
    }
  }

  export type context = UIExecutionContext.BasicExecutionContext & {
    data: {
      employee: dataSets.employee.data;
    };
    state: Record<string, unknown>;
    presentationClient: {
      extractData: () => Promise<actions.extractData.output>;
    };
  }
}
