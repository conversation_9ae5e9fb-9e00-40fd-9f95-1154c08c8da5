/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

// ******************************************************************************
// Generated Everest Presentation Types
// ******************************************************************************

import type {
  ActionValidationResult,
  COMPONENT,
  CONFIGURATIONS,
  DATA,
  DynamicFieldsMetadata,
  GetElementType,
  DynamicFieldDefinition,
  DATA_SETS,
  DYNAMIC_FIELDS,
  FieldInstanceMetadata,
  FieldLevelMetadata,
  IDENTIFIER,
  ISession,
  METADATA,
  QueryInput,
  QueryReason,
  RoutineDeterminationResult,
  RoutineValidationContext,
  RoutineValidationResult,
  TableComponent,
  ValidateReason
} from '@everestsystems/content-core';

import type { EvstBlobRef as everest_appserver_composite_BlobRef } from "@pkg/everest.appserver/types/composites/BlobRef";
import type { EvstEmployeeType as everest_hr_base_primitive_EmployeeType } from "@pkg/everest.hr.base/types/primitives/EmployeeType";
import type { EvstPersonalQuestionnaireFileType as everest_fin_integration_ruddr_enum_PersonalQuestionnaireFileType } from "@pkg/everest.fin.integration.ruddr/types/enums/PersonalQuestionnaireFileType";

export namespace uploadPresentation {
  export type mode = 'view';

  export namespace dataSources {
    export namespace EmployeeExtraction {
      export type levels = {
        '': {
          employeeDetails?: everest_appserver_composite_BlobRef | undefined | null;
          employeeId?: everest_hr_base_primitive_EmployeeType | undefined | null;
          fileType?: everest_fin_integration_ruddr_enum_PersonalQuestionnaireFileType | undefined | null;
        };
      };

      export type instanceMetadata = {
        '': {
          employeeDetails?: FieldInstanceMetadata | undefined;
          employeeId?: FieldInstanceMetadata | undefined;
          fileType?: FieldInstanceMetadata | undefined;
        };
      };

      export type levelMetadata = {
        '': {
          employeeDetails?: FieldLevelMetadata | undefined;
          employeeId?: FieldLevelMetadata | undefined;
          fileType?: FieldLevelMetadata | undefined;
        };
      };

      export type levelConfigurations = {
        '': never;
      };

      export type parameters = never;

      export namespace callbacks {
        export namespace setUp {
          export type input = {
            session: ISession;
            mode: mode;
          };

          export type output = void;
        }

        export namespace query {
          export type queryParameters = never;

          export type queryMetadata = levelMetadata[''];

          export type queryConfigurations = levelConfigurations[''];

          export type queryData = levels[''] & {
            [METADATA]?: instanceMetadata[''] | undefined;
          };

          export type input = {
            session: ISession;
            mode: mode;
            queryReason: QueryReason;
            queryInput: QueryInput;
            queryInstruction: {
              fields: ReadonlySet<keyof levels['']> | undefined;
            };
          };

          export type output = queryData;

          export type combinedOutput = queryData | {
            [DATA]: queryData;
            [METADATA]?: queryMetadata | undefined;
            [CONFIGURATIONS]?: queryConfigurations | undefined;
            [key: string]: never;
          };
        }

        export namespace update {
          export type input = {
            session: ISession;
            mode: mode;
            fieldName: keyof levels[''];
            oldFieldValue: unknown;
            newFieldValue: unknown;
          };

          export type output = void;
        }
      }

      export namespace routines {
        export namespace extractData {
          export type input = never;

          export type output = void;

          export type determineInput = {
            session: ISession;
            mode: mode;
          };

          export type determineOutput = RoutineDeterminationResult;

          export type validateInput = {
            session: ISession;
            mode: mode;
            validateReason: ValidateReason;
            context: RoutineValidationContext;
          };

          export type validateOutput = RoutineValidationResult;

          export type executeInput = {
            session: ISession;
            mode: mode;
          };

          export type executeOutput = output;
        }
      }

      export interface implementation {
        setUp?(input: callbacks.setUp.input): Promise<callbacks.setUp.output>;

        query(input: callbacks.query.input): Promise<callbacks.query.combinedOutput>;

        update(input: callbacks.update.input): Promise<callbacks.update.output>;

        determine_extractData?(input: routines.extractData.determineInput): Promise<routines.extractData.determineOutput>;

        validate_extractData?(input: routines.extractData.validateInput): Promise<routines.extractData.validateOutput>;

        execute_extractData(input: routines.extractData.executeInput): Promise<routines.extractData.executeOutput>;
      }
    }
  }

  export type implementation = {
    EmployeeExtraction(): dataSources.EmployeeExtraction.implementation;
  };
}
