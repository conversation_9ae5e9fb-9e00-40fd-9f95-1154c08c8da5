/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// ******************************************************************************
// Generated Everest Types
// ******************************************************************************

import type { Decimal, RoundingMode } from '@everestsystems/decimal';
import type {
  ArgType,
  Association,
  AssociationManagementArgs,
  AsyncResult,
  Controller,
  ControllerClientProvider,
  CreateOptionsArg,
  DataWithMetadata,
  DeleteOptionsArg,
  FieldSelector,
  Filter,
  flattenFieldSelector,
  GraphInstanceEntry,
  KernelRequestOptions,
  NonAssociationFields,
  Optional,
  PurgeArgs,
  ReadOptionsArg,
  ReadReturnType as ReadReturnTypeGeneric,
  ReadReturnTypeWithAssociations,
  RequireAtLeastOne,
  SemanticSearchArg,
  SemanticSearchRequestOptions,
  TypeSafeQueryArgType,
  UpdateManyOptionsArg,
  UpdateOptionsArg,
  UpsertOptionsArg,
  WriteReturnType,
  WriteReturnTypeWithAssociations,
} from '@everestsystems/content-core';
import type { EvstUnknownObject as everest_appserver_primitive_UnknownObject } from "@pkg/everest.appserver/types/primitives/UnknownObject";
import type { EvstextractActionInput as everest_fin_integration_base_composite_extractActionInput } from "@pkg/everest.fin.integration.base/types/composites/extractActionInput";
import type { EvstLoadOrMatchFunctionReturnType as everest_fin_integration_base_composite_LoadOrMatchFunctionReturnType } from "@pkg/everest.fin.integration.base/types/composites/LoadOrMatchFunctionReturnType";
import type { EvstLoadOrMatchInputType as everest_fin_integration_base_composite_LoadOrMatchInputType } from "@pkg/everest.fin.integration.base/types/composites/LoadOrMatchInputType";
import type { EvstJSON as everest_appserver_primitive_JSON } from "@pkg/everest.appserver/types/primitives/JSON";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { EvstUserEmail as everest_appserver_usermgmt_primitive_user_UserEmail } from "@pkg/everest.appserver.usermgmt/types/primitives/user/UserEmail";
import type { EvstDateTime as everest_appserver_primitive_DateTime } from "@pkg/everest.appserver/types/primitives/DateTime";

/**
 * Types for RuddrIntegration
 *
 * @everest-node-metadata='{"visibility":"public"}'
 */
export namespace RuddrIntegration {
  export type CreationFields = Pick<RuddrIntegration, 'externalId' | 'active'>;
  export type UniqueFields = Pick<RuddrIntegration, 'id'>;
  export type UniqueWhereInput = (RequireAtLeastOne<UniqueFields>) & Partial<RuddrIntegration>;
  export type ReadReturnType<U extends string | number | symbol = keyof RuddrIntegration> = ReadReturnTypeGeneric<RuddrIntegration, U>;

  export interface IControllerClient extends Omit<Controller<RuddrIntegration>, 'all' | 'explainRead' | 'getHistory' | 'lookup' | 'purge' | 'queryWithMetadata' | 'semanticSearch' | 'upsertMany'> {
    /** This is an extraction action */
    memberExtraction(input?: everest_fin_integration_base_composite_extractActionInput): Promise<everest_appserver_primitive_UnknownObject>;
    /** Loading action for load functions */
    loadMembers(input: everest_fin_integration_base_composite_LoadOrMatchInputType): Promise<everest_fin_integration_base_composite_LoadOrMatchFunctionReturnType>;
    /** Loading action for load functions */
    loadExpenses(input: everest_fin_integration_base_composite_LoadOrMatchInputType): Promise<everest_fin_integration_base_composite_LoadOrMatchFunctionReturnType>;
    /** This is an extraction action */
    expenseExtraction(input?: everest_fin_integration_base_composite_extractActionInput): Promise<everest_appserver_primitive_UnknownObject[]>;
    /** Loading action for load functions */
    loadEmployee(input: everest_fin_integration_base_composite_LoadOrMatchInputType): Promise<everest_fin_integration_base_composite_LoadOrMatchFunctionReturnType>;
    /** This is an extraction action */
    employeeExtractionFromPersonalfragebogen(input?: everest_fin_integration_base_composite_extractActionInput): Promise<everest_appserver_primitive_UnknownObject[]>;
    /** Get Ruddr integration configuration for integration.base.ui */
    getIntegrationConfiguration(): Promise<everest_appserver_primitive_JSON>;
    /** Executes ETL for a given model */
    syncData(providerModel: everest_appserver_primitive_Text, mapping: everest_appserver_primitive_JSON, fetching: everest_appserver_primitive_JSON): Promise<everest_appserver_primitive_JSON>;
    /** Save a Ruddr connector */
    saveConnector(args: everest_appserver_primitive_JSON): Promise<everest_appserver_primitive_JSON>;
    /** Get Secret Token */
    getSecretToken(): Promise<everest_appserver_primitive_Text>;
    /** This is an extraction action */
    expenseCategoriesExtraction(input?: everest_fin_integration_base_composite_extractActionInput): Promise<everest_appserver_primitive_UnknownObject[]>;
    /** Formats matches */
    formatMatches(providerModel: everest_appserver_primitive_Text, matches: everest_appserver_primitive_JSON): Promise<everest_appserver_primitive_JSON>;
    /** Gets matching candidates */
    getMatchingCandidates(providerModel: everest_appserver_primitive_Text, requiredFieldsForAiMatching?: everest_appserver_primitive_Text[]): Promise<everest_appserver_primitive_JSON>;
    /** Gets matching primitive types */
    getMatchingPrimitiveTypes(providerModel: everest_appserver_primitive_Text): Promise<everest_appserver_primitive_JSON>;
    /** Loading action for load functions */
    loadAccount(input: everest_fin_integration_base_composite_LoadOrMatchInputType): Promise<everest_fin_integration_base_composite_LoadOrMatchFunctionReturnType>;
    /** Loading action for load functions */
    loadClient(input: everest_fin_integration_base_composite_LoadOrMatchInputType): Promise<everest_fin_integration_base_composite_LoadOrMatchFunctionReturnType>;
    /** This is an extraction action */
    invoiceItemExtraction(input?: everest_fin_integration_base_composite_extractActionInput): Promise<everest_appserver_primitive_UnknownObject[]>;
    /** This is an extraction action */
    invoiceExtraction(input?: everest_fin_integration_base_composite_extractActionInput): Promise<everest_appserver_primitive_UnknownObject[]>;
    /** This is an extraction action */
    paymentExtraction(input?: everest_fin_integration_base_composite_extractActionInput): Promise<everest_appserver_primitive_UnknownObject[]>;
    /** Action for ActionExecutor transformation operator */
    mapRuddrAddress(input: everest_appserver_primitive_JSON, sessionVariables?: everest_appserver_primitive_UnknownObject[]): Promise<everest_appserver_primitive_JSON>;
    /** This is an extraction action */
    paymentTermsExtraction(input?: everest_fin_integration_base_composite_extractActionInput): Promise<everest_appserver_primitive_UnknownObject[]>;
    /** This is an extraction action */
    employeeExtractionFromDatev(input?: everest_fin_integration_base_composite_extractActionInput): Promise<everest_appserver_primitive_UnknownObject[]>;
    /** Loading action for load functions */
    loadClients(input: everest_fin_integration_base_composite_LoadOrMatchInputType): Promise<everest_fin_integration_base_composite_LoadOrMatchFunctionReturnType>;
    /** This is an extraction action */
    practiceExtraction(input?: everest_fin_integration_base_composite_extractActionInput): Promise<everest_appserver_primitive_UnknownObject[]>;
    /** This is an extraction action */
    timeEntryExtraction(input?: everest_fin_integration_base_composite_extractActionInput): Promise<everest_appserver_primitive_UnknownObject[]>;
    /** Loading action for load functions */
    loadAbsences(input: everest_fin_integration_base_composite_LoadOrMatchInputType): Promise<everest_fin_integration_base_composite_LoadOrMatchFunctionReturnType>;
    /** This is an extraction action */
    timeOffTypeExtraction(input?: everest_fin_integration_base_composite_extractActionInput): Promise<everest_appserver_primitive_UnknownObject[]>;
  }

  /**
   * Ruddr Integration
   */
  export type RuddrIntegration = {
    /** Database generated identifier of this record. */
    id?: number;
    /** Database generated version of this record. */
    version?: number;
    /**
     * ID of the system from which this record was sourced
     */
    externalId?: everest_appserver_primitive_Text | null;
    /**
     * Indicates whether this record is usable for business users
     */
    active?: everest_appserver_primitive_TrueFalse | null;
    /**
     * User that created this record
     */
    createdBy: everest_appserver_usermgmt_primitive_user_UserEmail;
    /**
     * Date/time when the record was created
     */
    createdDate: everest_appserver_primitive_DateTime;
    /**
     * User that last modified the record
     */
    lastModifiedBy: everest_appserver_usermgmt_primitive_user_UserEmail;
    /**
     * Date/time of the last modification to the record
     */
    lastModifiedDate: everest_appserver_primitive_DateTime;
    };
  /**
   * Ruddr Integration
   */
  export type RuddrIntegrationWithAssociation = RuddrIntegration & {

    };
  /** @deprecated - use {@link MODEL_URN} instead */
  export const name = 'urn:evst:everest:fin/integration/ruddr:model/node:RuddrIntegration';
  export const MODEL_URN = 'urn:evst:everest:fin/integration/ruddr:model/node:RuddrIntegration';
  /** @deprecated - use {@link MODEL_URN} instead */
  export const MODEL_ID = 'everest.fin.integration.ruddr/RuddrIntegrationModel.RuddrIntegration';
  export const MODEL_UUID = '0737a97e-1c67-4e61-81cc-7ef7243d8250';

  /** @return a model controller instance for RuddrIntegration. */
  export function client(env: ControllerClientProvider) {
    return env.controllerClient<RuddrIntegration.IControllerClient>(MODEL_URN);
  }

  /** write a new object to the database. */
  export async function create<U extends keyof RuddrIntegration>(env: ControllerClientProvider, input: CreationFields, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): Promise<WriteReturnType<RuddrIntegration, U>> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).create(input, fields, options);
  }

  /** write new objects to the database. */
  export async function createMany<U extends keyof RuddrIntegration>(env: ControllerClientProvider, input: CreationFields[], fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): Promise<WriteReturnType<RuddrIntegration, U>[]> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).createMany(input, fields, options);
  }

  /** delete the object that is uniquely identified by the where condition. */
  export async function Delete(env: ControllerClientProvider, where: UniqueWhereInput): Promise<Partial<RuddrIntegration>[]> {
    return (await client(env)).delete(where);
  }

  /** delete all objects. */
  export async function deleteAll(env: ControllerClientProvider): Promise<Partial<RuddrIntegration>[]> {
    return (await client(env)).deleteAll();
  }

  /** delete the objects that are identified by the where condition. */
  export async function deleteMany(env: ControllerClientProvider, where: Filter<RuddrIntegrationWithAssociation>): Promise<Partial<RuddrIntegration>[]>;
  /** @deprecated use the alternative overloaded function */
  export async function deleteMany(env: ControllerClientProvider, where: Partial<RuddrIntegration>): Promise<Partial<RuddrIntegration>[]>;
  export async function deleteMany(env: ControllerClientProvider, where: Filter<RuddrIntegration> | Partial<RuddrIntegration>): Promise<Partial<RuddrIntegration>[]> {
    return (await client(env)).deleteMany(where as Filter<RuddrIntegration>);
  }

  /** returns a list of all objects matching the requirements. */
  export async function query<U extends FieldSelector<RuddrIntegrationWithAssociation>>(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<RuddrIntegrationWithAssociation>, 'draft'>, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<ReadReturnTypeWithAssociations<RuddrIntegrationWithAssociation, U>[]>;
  /** @deprecated Use the alternative overloaded method for querying associations. */
  export async function query<U extends keyof RuddrIntegration, V extends string = 'ALL_FIELDS'>(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<RuddrIntegration>, 'draft'> | Omit<Omit<TypeSafeQueryArgType<RuddrIntegration>, 'draft'>, 'where'> & { where?: Partial<RuddrIntegration> }, fieldlist: ReadonlyArray<U | V> | 'ALL_FIELDS'): Promise<ReadReturnType<V>[]>;
  export async function query(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<RuddrIntegration>, 'draft'>, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown[]> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).query(args, fields as ReadonlyArray<string>);
  }

  /** reads the object that is uniquely identified by the where condition. */
  export async function read<U extends FieldSelector<RuddrIntegrationWithAssociation>>(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnTypeWithAssociations<RuddrIntegrationWithAssociation, U>>;
  /** @deprecated Use the alternative overloaded method for reading associations. */
  export async function read<U extends keyof RuddrIntegration>(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnType<U>>;
  export async function read(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<unknown> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).read(where, fields as ReadonlyArray<keyof RuddrIntegration>, options);
  }

  /** writes modified values back to the database for a given node instance. */
  export async function update<U extends FieldSelector<RuddrIntegrationWithAssociation>>(env: ControllerClientProvider, where: UniqueWhereInput, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnTypeWithAssociations<RuddrIntegrationWithAssociation, U>>;
  /** @deprecated Use the alternative overloaded method for writing with associations. */
  export async function update<U extends keyof RuddrIntegration>(env: ControllerClientProvider, where: Partial<RuddrIntegration>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnType<RuddrIntegration, U>>;
  export async function update(env: ControllerClientProvider, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).update(where, data, fields as ReadonlyArray<keyof RuddrIntegration>);
  }

  /** writes modified values back to the database for selected node instances. */
  export async function updateMany<U extends FieldSelector<RuddrIntegrationWithAssociation>>(env: ControllerClientProvider, where: Filter<RuddrIntegrationWithAssociation>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnTypeWithAssociations<RuddrIntegrationWithAssociation, U>[]>;
  /** @deprecated Use the alternative overloaded method for writing with associations. */
  export async function updateMany<U extends keyof RuddrIntegration>(env: ControllerClientProvider, where: Partial<RuddrIntegration>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnType<RuddrIntegration, U>[]>;
  export async function updateMany(env: ControllerClientProvider, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown[]> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).updateMany(where, data, fields as ReadonlyArray<keyof RuddrIntegration>);
  }

  /** creates or updates object identified by where. */
  export async function upsert<U extends keyof RuddrIntegration & string>(env: ControllerClientProvider, data: Partial<RuddrIntegration>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<RuddrIntegration, U>>;
  /** @deprecated use the overload without an explicit where clause */
  export async function upsert<U extends keyof RuddrIntegration & string>(env: ControllerClientProvider, where: Partial<RuddrIntegration>, data: Partial<RuddrIntegration>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<RuddrIntegration, U>>;
  export async function upsert<U extends keyof RuddrIntegration & string>(env: ControllerClientProvider, whereOrData: Partial<RuddrIntegration>, dataOrFieldList?: Partial<RuddrIntegration> | ReadonlyArray<U> | 'ALL_FIELDS', fieldlistOrOptions?: ReadonlyArray<U> | 'ALL_FIELDS' | Omit<UpsertOptionsArg, 'draft'>, maybeOptions?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<RuddrIntegration, U>> {
    return (await client(env)).upsert(whereOrData, dataOrFieldList, fieldlistOrOptions, maybeOptions);
  }
}
