{"version": 2, "uicontroller": "connector.uicontroller.ts", "uimodel": {"nodes": {"ruddr": {"type": "struct", "model": "urn:evst:everest:fin/integration/ruddr:model/node:RuddrIntegration"}}}, "uiview": {"templateType": "details", "i18n": ["everest.connectorengine/connectorEngine", "ruddr"], "title": "{{connectorEngine.create}}", "actions": {"content": [{"variant": "secondary", "label": "{{connectorEngine.cancel}}", "onClick": "@controller:cancel", "align": "right", "visible": "@controller:isEditing()"}, {"variant": "primary", "label": "@controller:getPrimaryLabel()", "onClick": "@controller:save", "align": "right"}]}, "sections": {"content": [{"component": "Block", "section": {"grid": {"size": "12"}}, "props": {"type": "secondary", "elements": [{"component": "Input", "label": "{{ruddr.name}}", "name": "name", "value": "@controller:getName()", "isEditing": true}, {"component": "Input", "label": "{{ruddr.token}}", "name": "token", "type": "password", "value": "@controller:getToken()", "isEditing": true}]}}]}}}