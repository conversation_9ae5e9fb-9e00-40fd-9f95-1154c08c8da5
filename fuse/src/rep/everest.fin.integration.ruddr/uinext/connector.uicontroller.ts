// @i18n:everest.connectorengine/connectorEngine
import { RuddrIntegrationUI } from '@pkg/everest.fin.integration.ruddr/types/RuddrIntegration.ui';

import type { ConnectorUiTemplate } from '../types/uiTemplates/uinext/connector.ui';

type ConnectorContext = ConnectorUiTemplate.ConnectorContext;

export type Context = ConnectorContext & {
  state: {
    connector: Record<string, unknown> & {
      configValues: Record<string, unknown>;
      id: number;
    };
  };
};

export function isEditing({ state }: Context) {
  return state.connector !== undefined;
}

export function getPrimaryLabel(ctx: Context) {
  return isEditing(ctx)
    ? '{{connectorEngine.save}}'
    : '{{connectorEngine.connect}}';
}

export function getName({ state }: Context) {
  return state.connector?.name || '';
}

export async function getToken(ctx: Context) {
  const { state } = ctx;
  const { connector } = state;

  let token: string = '';
  if (connector) {
    const res = await RuddrIntegrationUI.getSecretToken(ctx).run('ruddr');
    token = res.ruddr;
  }
  return token;
}

export function cancel({ helpers }: Context) {
  helpers.closeModal();
}

export async function save(ctx: Context) {
  const { helpers, form, state } = ctx;
  const { name, token } = form.getFormValues();
  const { connector } = state;

  await RuddrIntegrationUI.saveConnector(ctx, {
    args: {
      name,
      token,
      connectorId: connector?.id,
    },
  }).run('ruddr');
  if (connector) {
    helpers.closeModal();
  } else {
    helpers.navigate({
      to: '/templates/everest.fin.integration.base.ui/uinext/overview/overview?providerName=Ruddr',
      closeCurrentTab: true,
    });
  }
}
