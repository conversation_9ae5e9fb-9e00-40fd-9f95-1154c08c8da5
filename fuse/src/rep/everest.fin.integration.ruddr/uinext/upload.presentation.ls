
package everest.fin.integration.ruddr

template presentation upload {

    object data-source EmployeeExtraction {
        shape {
            fileType (label: 'File Type', required: true): enum<PersonalQuestionnaireFileType>
            employeeId (label: 'Employee', required: true): primitive<everest.hr.base::EmployeeType>
            employeeDetails (label: 'Employee Details', required: true): composite<everest.appserver::BlobRef>
        }
        modifications {
            on root support update
        }
        routine extractData {
            properties {
                side-effects true
            }
        }
    }

    struct employee {
        data EmployeeExtraction
        fields *
    }

    delegate action extractData to data-source<EmployeeExtraction>.extractData

    mode view {
        on all-data-sets allow view, change
        allow actions extractData
    }
}
