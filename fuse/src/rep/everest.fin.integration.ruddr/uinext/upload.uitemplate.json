{"version": 3, "uicontroller": [], "uimodel": {"presentation": {"urn": "urn:evst:everest:fin/integration/ruddr:presentation:uinext/upload", "parameters": {"mode": "view"}}}, "uiview": {"title": "Upload", "config": {"allowRefreshData": true, "stretch": true}, "i18n": [], "header": {"content": {"title": "Upload"}}, "sections": {"background": "grey", "content": [{"component": "FieldGroup", "section": {"editing": true, "grid": {"size": "12"}}, "props": {"columns": 3, "presentationDataSet": "@dataSet:employee", "fields": ["employeeId", "fileType"]}}, {"component": "Attachment", "section": {"title": "Employee Details", "grid": {"size": "4"}}, "props": {"editing": true, "temporaryFile": true, "multipleFiles": false, "acceptedFileTypes": [".pdf"], "value": "@binding:employee.employeeDetails"}}, {"component": "ButtonGroup", "section": {"grid": {"size": "12"}}, "props": {"direction": "horizontal", "actions": [{"label": "Extract", "variant": "primary", "presentationAction": "@action:extractData"}]}}]}}}