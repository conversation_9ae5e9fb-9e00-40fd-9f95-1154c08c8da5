import { DATA } from '@everestsystems/content-core';
import type { EvstLoadOrMatchInputType } from '@pkg/everest.fin.integration.base/types/composites/LoadOrMatchInputType';
import { EvstPersonalQuestionnaireFileType } from '@pkg/everest.fin.integration.ruddr/types/enums/PersonalQuestionnaireFileType';
import type { uploadPresentation } from '@pkg/everest.fin.integration.ruddr/types/presentations/uinext/upload';
import { RuddrIntegration } from '@pkg/everest.fin.integration.ruddr/types/RuddrIntegration';
import { EmployeeAttachment } from '@pkg/everest.hr.base/types/EmployeeAttachment';
import { set } from 'lodash';

class EmployeeExtractionDataSource
  implements uploadPresentation.dataSources.EmployeeExtraction.implementation
{
  private data: uploadPresentation.dataSources.EmployeeExtraction.callbacks.query.queryData | null =
    null;

  public async query(
    input: uploadPresentation.dataSources.EmployeeExtraction.callbacks.query.input
  ): Promise<uploadPresentation.dataSources.EmployeeExtraction.callbacks.query.combinedOutput> {
    const { queryReason } = input;
    if (this.data === null || queryReason === 'externalRequest') {
      this.data = {};
    }

    return {
      [DATA]: this.data,
    };
  }

  public async update(
    input: uploadPresentation.dataSources.EmployeeExtraction.callbacks.update.input
  ): Promise<uploadPresentation.dataSources.EmployeeExtraction.callbacks.update.output> {
    const { fieldName, newFieldValue } = input;

    set(this.data, fieldName, newFieldValue);
  }

  public async validate_extractData(
    input: uploadPresentation.dataSources.EmployeeExtraction.routines.extractData.validateInput
  ): Promise<uploadPresentation.dataSources.EmployeeExtraction.routines.extractData.validateOutput> {
    // Run validation here
  }

  public async execute_extractData(
    input: uploadPresentation.dataSources.EmployeeExtraction.routines.extractData.executeInput
  ): Promise<void> {
    const { session } = input;

    // this.data.fileType

    const client = await RuddrIntegration.client(session);
    let ip: EvstLoadOrMatchInputType;

    const fileName =
      this.data.fileType === EvstPersonalQuestionnaireFileType.Personalfragebogen
        ? 'Personalfragebogen.pdf'
        : 'Datev-Datenblatt.pdf';

    // if (this.data.fileType === EvstPersonalQuestionnaireFileType.Datev) {
    //   // do it
    // } else {
    const result = await client.employeeExtractionFromPersonalfragebogen({
      fileId: this.data.employeeDetails.blobId,
    });

    // call load function using result
    // eslint-disable-next-line prefer-const
    ip = {
      data: result,
      everestId: this.data.employeeId,
    };
    // }
    await client.loadEmployee(ip);

    await EmployeeAttachment.create(session, {
      employeeId: this.data.employeeId,
      fileId: this.data.employeeDetails.blobId,
      fileName: fileName,
    });
  }
}

const presentationImplementation: uploadPresentation.implementation = {
  EmployeeExtraction: () => new EmployeeExtractionDataSource(),
};

export default presentationImplementation;
