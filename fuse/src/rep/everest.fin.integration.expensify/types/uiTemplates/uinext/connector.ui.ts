/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable no-trailing-spaces */
/* eslint-disable no-multiple-empty-lines */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// ******************************************************************************
// Generated Everest Ui Template Types
// ******************************************************************************
import { ReadReturnTypeWithAssociations, UIExecutionContext } from '@everestsystems/content-core';

import {ExpensifyUI as ExpensifyNamespace } from '@pkg/everest.fin.integration.expensify/types/Expensify.ui'

type BasicExecutionContext = UIExecutionContext.BasicExecutionContext;
type InnerPromiseType<T> = T extends Promise<infer R> ? R : T;
type UIModelNodeListType<T> =  UIExecutionContext.UIModelNodeListType<T>
type ListElementType<T> = T extends (infer R)[] ? R : unknown;

export namespace ConnectorUiTemplate {



export type ExpensifyWithAssociation = ExpensifyNamespace.ExpensifyWithAssociation
export type Expensify = ExpensifyNamespace.Expensify



export type CommonDataType = { uuid?: string; _nodeReference: string; /** @deprecated use _nodeParentReference */ _parentReference?: string; _nodeParentReference?: string; $metadata?: { isDraft?: boolean } }
export type SingleNodeItemType = {
        divvy: Expensify & CommonDataType;
      }
      
export type ConnectorData = {
        divvy?: SingleNodeItemType['divvy'];
      }
      

export type ConnectorContext = BasicExecutionContext
  & {
    data: ConnectorData;
    state: Record<string, unknown>;
  }
  ;


type OverrideDataPayload = {
  [K in keyof ConnectorData]: ConnectorData[K] extends UIModelNodeListType<any>
    ? UIModelNodeListType<any>
    : any;
};

/** 
 * This Helper Type allows you to override the type of the data properties or extend them.
  * if the node you are overriding is of type list in the ui-template then
  * you need to wrap your type in {UIExecutionContext.UIModelNodeListType<T>} instead of normal array or list
  */
export type OverrideConnectorData<
  Payload extends Record<string, unknown> & Partial<OverrideDataPayload>
> = Omit<ConnectorContext, 'data'> & {
  data: {
    [K in keyof ConnectorData | keyof Payload]: K extends keyof Payload
      ? Payload[K]
      : K extends keyof ConnectorData
      ? ConnectorData[K]
      : unknown;
  };
};


}
 /** @deprecated use `ConnectorUiTemplate.ExpensifyWithAssociation` instead. */ export type ExpensifyWithAssociation = ConnectorUiTemplate.ExpensifyWithAssociation;
 /** @deprecated use `ConnectorUiTemplate.Expensify` instead. */ export type Expensify = ConnectorUiTemplate.Expensify;
 /** @deprecated use `ConnectorUiTemplate.CommonDataType` instead. */ export type CommonDataType = ConnectorUiTemplate.CommonDataType;
 /** @deprecated use `ConnectorUiTemplate.SingleNodeItemType` instead. */ export type SingleNodeItemType = ConnectorUiTemplate.SingleNodeItemType;
 /** @deprecated use `ConnectorUiTemplate.ConnectorData` instead. */ export type ConnectorData = ConnectorUiTemplate.ConnectorData;
 /** @deprecated use `ConnectorUiTemplate.ConnectorContext` instead. */ export type ConnectorContext = ConnectorUiTemplate.ConnectorContext;

type OverrideDataPayload = {
  [K in keyof ConnectorUiTemplate.ConnectorData]: ConnectorUiTemplate.ConnectorData[K] extends UIModelNodeListType<any>
    ? UIModelNodeListType<any>
    : any;
};

/** @deprecated use `ConnectorUiTemplate.OverrideConnectorData` instead.
 * This Helper Type allows you to override the type of the data properties or extend them.
  * if the node you are overriding is of type list in the ui-template then
  * you need to wrap your type in {UIExecutionContext.UIModelNodeListType<T>} instead of normal array or list
  */
export type OverrideConnectorData<
  Payload extends Record<string, unknown> & Partial<OverrideDataPayload>
> = Omit<ConnectorUiTemplate.ConnectorContext, 'data'> & {
  data: {
    [K in keyof ConnectorUiTemplate.ConnectorData | keyof Payload]: K extends keyof Payload
      ? Payload[K]
      : K extends keyof ConnectorUiTemplate.ConnectorData
      ? ConnectorUiTemplate.ConnectorData[K]
      : unknown;
  };
};
