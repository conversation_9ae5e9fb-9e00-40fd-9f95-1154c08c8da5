{"extends": "../../../../tsconfig.json", "references": [{"path": "../everest.base.translation/tsconfig_public.json"}, {"path": "../everest.connectorengine/tsconfig_public.json"}, {"path": "../everest.fin.integration.base/tsconfig_public.json"}, {"path": "../everest.fin.integration.base.ui/tsconfig_public.json"}, {"path": "../../pkg_types/tsconfig.json"}], "compilerOptions": {"jsx": "preserve", "composite": true}, "include": ["public/**/*.ts", "public/**/*.tsx", "types/**/*.ts", "**/*.js", "**/*.json"]}