import type { ISession } from '@everestsystems/content-core';
import type { IntegrationConfig } from '@pkg/everest.fin.integration.base.ui/public/types/IntegrationConfig';
import { EvstExtractionProviders } from '@pkg/everest.fin.integration.base/types/enums/ExtractionProviders';
import { EXPENSIFY_PACKAGE } from '@pkg/everest.fin.integration.expensify/public/config';

export default async function getIntegrationConfiguration(
  env: ISession
): Promise<IntegrationConfig> {
  const expensifyConfig: IntegrationConfig = {
    general: {
      package: EXPENSIFY_PACKAGE,
      providerName: EvstExtractionProviders.Expensify,
      enableDataSyncRunTracking: true,
      useWithoutConnector: true,
      enableBusinessUserStagingView: true,
      enableActivateFailures: true,
    },
    templates: {
      overview: {
        title: 'Expensify',
        showLastSyncColumn: true,
        showStatusColumn: true,
        configurations: {
          custom: [],
        },
      },
    },
    executions: {
      metadata: [],
      businessData: [],
    },
  };
  return expensifyConfig;
}
