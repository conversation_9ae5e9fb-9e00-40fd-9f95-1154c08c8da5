{"version": 2, "uicontroller": "connector.uicontroller.ts", "uimodel": {"nodes": {"divvy": {"type": "struct", "model": "urn:evst:everest:fin/integration/expensify:model/node:Expensify"}}}, "uiview": {"templateType": "details", "i18n": ["everest.connectorengine/connectorEngine", "expensify"], "title": "{{connectorEngine.create}}", "actions": {"content": [{"variant": "secondary", "label": "{{connectorEngine.cancel}}", "onClick": "@controller:cancel", "align": "right", "visible": "@controller:isEditing()"}, {"variant": "primary", "label": "@controller:getPrimaryLabel()", "onClick": "@controller:save", "align": "right"}]}, "sections": {"content": [{"component": "Block", "section": {"grid": {"size": "12"}}, "props": {"type": "secondary", "elements": [{"component": "Input", "label": "{{expensify.name}}", "name": "name", "value": "@controller:getName()", "isEditing": true}, {"component": "Input", "label": "{{expensify.token}}", "name": "token", "type": "password", "isEditing": true}, {"component": "Checkbox", "name": "isSandbox", "label": "{{expensify.scope}}", "text": "{{expensify.expensifySandbox}}", "value": "@controller:isSandbox()", "isEditing": true}]}}]}}}