import React, { useMemo, useState, useCallback, memo } from 'react';
import { Card, Table, Skeleton } from 'antd';

import {
  Typography,
  Icon,
  Button,
  DataGrid,
} from '@everestsystems/design-system';
import { ColDef } from 'ag-grid-community';
import { formatEvstCurrencyAmount } from '@pkg/everest.base/public/utils/templatingUtils';
import { quoteCreateFormStore } from '@pkg/everest.crm/modules/pages/quotes/components/templates/steps/store/form.store.ui';

import ProductDetailsDrawer from './ProductDetailsDrawer.ui';
import { useSnapshot } from 'valtio';
import {
  getSubscriptionProducts,
  getNonRecurringProducts,
} from '@pkg/everest.crm/modules/pages/quotes/helpers/product.ui';
import { EvstPriceModel } from '@pkg/everest.fin.accounting/types/enums/PriceModel';
import {
  Quantity,
  ListPrice,
  DiscountPercentage,
  DiscountAmount,
} from '@pkg/everest.crm/modules/pages/quotes/components/ui/form-fields/form-fields.ui';
import Label from '@pkg/everest.crm/modules/pages/quotes/components/ui/label/label.ui';
interface ProductLinesTableProps {
  editable?: boolean;
}

const ProductLinesTable: React.FC<ProductLinesTableProps> = ({
  editable = true,
}) => {
  const { isSubscriptionFieldDisabled, form } = quoteCreateFormStore;

  const snap = useSnapshot(quoteCreateFormStore);

  const loadingLines = snap.loadingLines;
  console.log({ loadingLines });
  const quoteProducts = snap.quoteProductsFieldArray.fields;
  const subscriptions = snap.subscriptionsFieldArray.fields;
  const quoteProductLines = snap.quoteProductLinesFieldArray.fields;

  // State for ProductDetailsDrawer
  const [isDrawerVisible, setIsDrawerVisible] = useState(false);
  const [selectedProductLineId, setSelectedProductLineId] = useState<
    number | null
  >(null);

  // State for expanded rows - initialize with all products expanded
  const [subscriptionExpandedRows, setSubscriptionExpandedRows] = useState<
    React.Key[]
  >([]);
  const [nonRecurringExpandedRows, setNonRecurringExpandedRows] = useState<
    React.Key[]
  >([]);
  // For now, treat all products as non-recurring since we don't have isSubscription field
  // You can modify this logic based on your actual product structure
  const subscriptionProducts = useMemo(() => {
    return getSubscriptionProducts(quoteProducts, quoteProductLines);
  }, [quoteProducts, quoteProductLines]);
  const nonRecurringProducts = useMemo(() => {
    return getNonRecurringProducts(quoteProducts, quoteProductLines);
  }, [quoteProducts, quoteProductLines]);

  // Effect to set all products as expanded by default
  React.useEffect(() => {
    if (subscriptionProducts.length > 0) {
      setSubscriptionExpandedRows(
        subscriptionProducts.map((product) => product.productId)
      );
    }
    if (nonRecurringProducts.length > 0) {
      setNonRecurringExpandedRows(
        nonRecurringProducts.map((product) => product.productId)
      );
    }
  }, []);

  // Group product lines by product
  const groupedProductLines = useMemo(() => {
    const groups: { [key: string]: any[] } = {};

    for (const productLine of quoteProductLines) {
      const productTempId = productLine.productId;
      if (!groups[productTempId]) {
        groups[productTempId] = [];
      }
      groups[productTempId].push(productLine);
    }

    return groups;
  }, [quoteProductLines]);
  // Handlers for ProductDetailsDrawer
  const handleOpenProductDetails = (productLineId: number) => {
    setSelectedProductLineId(productLineId);
    setIsDrawerVisible(true);
  };

  const handleCloseProductDetails = () => {
    setIsDrawerVisible(false);
    setSelectedProductLineId(null);
  };

  // Handlers for expand/collapse
  const handleExpandAllSubscription = () => {
    if (subscriptionExpandedRows.length > 0) {
      setSubscriptionExpandedRows([]);
    } else {
      setSubscriptionExpandedRows(
        subscriptionProducts.map((product) => product.productId)
      );
    }
  };

  const handleExpandAllNonRecurring = () => {
    if (nonRecurringExpandedRows.length > 0) {
      setNonRecurringExpandedRows([]);
    } else {
      setNonRecurringExpandedRows(
        nonRecurringProducts.map((product) => product.productId)
      );
    }
  };

  // Column definitions for main products table (Ant Design format)
  const getColumnsForProductType = useCallback(
    (isSubscriptionProduct: boolean) => {
      const baseColumns = [
        {
          title: 'Product Name',
          dataIndex: 'productName',
          key: 'productName',
          render: (_: any, record: any) => {
            const productDetails = getProduct(record.productId);
            return (
              <div>
                <Typography.H4>
                  {productDetails?.productName || 'Unknown Product'}
                </Typography.H4>
                <Typography.Span color="cool-grey-600" fontSize="small">
                  {productDetails?.productCode || 'N/A'}
                </Typography.Span>
              </div>
            );
          },
        },
        {
          title: 'Description',
          dataIndex: 'description',
          key: 'description',
          render: (_: any, record: any) => {
            const productDetails = getProduct(record.productId);
            return (
              <Typography.P color="stone-grey">
                {productDetails?.description || 'No description available'}
              </Typography.P>
            );
          },
        },
      ];

      // Add subscription column only for subscription products
      if (isSubscriptionProduct) {
        // baseColumns.push({
        //   title: 'Subscription',
        //   dataIndex: 'subscription',
        //   key: 'subscription',
        //   width: 200,
        //   render: (_: any, record: any) => {
        //     const index = quoteProducts.findIndex(
        //       (product) => product.productId === record.productId
        //     );
        //     return (
        //       <Controller
        //         key={record.tempSubscriptionId}
        //         control={control}
        //         name={`quoteProducts.${index}.tempSubscriptionId` as const}
        //         render={({ field }) => (
        //           <Select
        //             disabled
        //             style={{ width: '100%' }}
        //             {...field}
        //             placeholder="No subscription assigned"
        //             options={subscriptions.map((sub) => ({
        //               value: sub.tempId,
        //               label: `Subscription assigned`,
        //             }))}
        //             onChange={(value) => {
        //               field.onChange(value);
        //               // const subscription = subscriptions.find(
        //               //   (subscription) => subscription.tempId === value
        //               // );
        //               // if (subscription) {
        //               // quoteCreateFormStore.updateProductLinesBySubscription(
        //               //   subscription,
        //               //   value
        //               // );
        //               // }
        //             }}
        //           />
        //         )}
        //       />
        //     );
        //   },
        // } as any);
      }

      baseColumns.push({
        title: 'Product Lines',
        dataIndex: 'productLines',
        key: 'productLines',
        render: (_: any, record: any) => {
          const productLines = groupedProductLines[record.productId] || [];
          return (
            <Typography.Span
              color={'shamrock' as any}
              fontSize="small"
              fontWeight="bold"
            >
              {productLines.length} line(s)
            </Typography.Span>
          );
        },
      });

      return baseColumns;
    },
    [subscriptions]
  );
  // Column definitions for product lines table

  const getProduct = useCallback(
    (id: number) => {
      return quoteProducts.find((product) => product.productId === id);
    },
    [quoteProducts]
  );

  // Column definitions for product lines table (ag-grid format)
  const productLineColumns: ColDef[] = useMemo(
    () => [
      {
        headerName: 'Product Line',
        field: 'productLineName',
        flex: 2,
        minWidth: 200,
        cellRenderer: (params: any) => (
          <div className="flex items-center justify-center h-full pr-4">
            <Typography.P
              fontSize="regular"
              color="sky-blue"
              fontWeight="semibold"
            >
              {params.value || 'Unknown Product Line'}
            </Typography.P>
          </div>
        ),
      },
      {
        headerName: 'Quantity',
        field: 'quantity',
        flex: 1,
        minWidth: 120,
        cellRenderer: (params: any) => {
          const index = quoteProductLines.findIndex(
            (line) => line.productLineId === params.data.productLineId
          );

          const line = quoteProductLines[index];
          const isFlatPriceModel = line?.priceModel === EvstPriceModel.Flat;
          const isFieldDisabled = isSubscriptionFieldDisabled(
            line,
            quoteProducts
          );

          if (!line.pricingTerm) {
            return (
              <div className="flex items-center justify-center h-full pr-4">
                <Label as="text" />
              </div>
            );
          }

          // If flat price model, show as read-only with value 1
          if (isFlatPriceModel) {
            return (
              <div className="flex items-center justify-center h-full pr-4">
                <Typography.Span
                  fontWeight="semibold"
                  fontSize="small"
                  color="cool-grey-600"
                >
                  1
                </Typography.Span>
              </div>
            );
          }

          return (
            <div className="flex items-center justify-center h-full pr-4">
              <Quantity
                hideLabel
                editable={editable}
                isFieldDisabled={isFieldDisabled}
                lineKey={`quoteProductLines.${index}`}
                form={editable ? form : undefined}
                value={params.value}
                keys={
                  editable
                    ? {
                        quantity:
                          `quoteProductLines.${index}.quantity` as const,
                        totalAmount:
                          `quoteProductLines.${index}.totalPrice.amount` as const,
                      }
                    : undefined
                }
              />
            </div>
          );
        },
      },
      {
        headerName: 'List Price',
        field: 'listPrice',
        flex: 1,
        minWidth: 150,
        cellRenderer: (params: any) => {
          const index = quoteProductLines.findIndex(
            (line) => line.productLineId === params.data.productLineId
          );
          const line = quoteProductLines[index];

          // If not editable or enforceListPrice is true, show as read-only
          if (!editable || line?.enforceListPrice) {
            return (
              <div className="flex items-center justify-center h-full pr-4">
                <Label as="currency" label={line?.listPrice} />
              </div>
            );
          }

          // If editable and enforceListPrice is false, show as editable input
          return (
            <div className="flex items-center justify-center h-full pr-4">
              <ListPrice
                isFieldDisabled={false}
                lineKey={`quoteProductLines.${index}`}
                form={form}
                keys={{
                  listPrice:
                    `quoteProductLines.${index}.listPrice.amount` as const,
                  discountPercentage:
                    `quoteProductLines.${index}.discountPercentage` as const,
                  discountAmount:
                    `quoteProductLines.${index}.discountAmount.amount` as const,
                  unitPrice:
                    `quoteProductLines.${index}.unitPrice.amount` as const,
                  totalAmount:
                    `quoteProductLines.${index}.totalPrice.amount` as const,
                }}
              />
            </div>
          );
        },
      },
      {
        headerName: 'Discount %',
        field: 'discountPercentage',
        flex: 1,
        minWidth: 120,
        cellRenderer: (params: any) => {
          const index = quoteProductLines.findIndex(
            (line) => line.productLineId === params.data.productLineId
          );

          const line = quoteProductLines[index];
          const isFieldDisabled = isSubscriptionFieldDisabled(
            line,
            quoteProducts
          );

          return (
            <div className="flex items-center justify-center h-full pr-4">
              <DiscountPercentage
                hideLabel
                editable={editable}
                isFieldDisabled={isFieldDisabled}
                lineKey={`quoteProductLines.${index}`}
                form={editable ? form : undefined}
                value={params.value}
                keys={
                  editable
                    ? {
                        discountPercentage:
                          `quoteProductLines.${index}.discountPercentage` as const,
                        listPrice:
                          `quoteProductLines.${index}.listPrice.amount` as const,
                        discountAmount:
                          `quoteProductLines.${index}.discountAmount.amount` as const,
                        unitPrice:
                          `quoteProductLines.${index}.unitPrice.amount` as const,
                        totalAmount:
                          `quoteProductLines.${index}.totalPrice.amount` as const,
                      }
                    : undefined
                }
              />
            </div>
          );
        },
      },
      {
        headerName: 'Unit Price',
        field: 'unitPrice',
        flex: 1,
        minWidth: 150,
        cellRenderer: (params: any) => {
          const line = quoteProductLines.find(
            (line) => line.productLineId === params.data.productLineId
          );

          return (
            <div className="flex items-center justify-center h-full pr-4">
              <Label as="currency" label={line?.unitPrice} />
            </div>
          );
        },
      },
      {
        headerName: 'Total Price',
        field: 'totalPrice',
        flex: 1,
        minWidth: 150,
        cellRenderer: (params: any) => {
          const line = quoteProductLines.find(
            (line) => line.productLineId === params.data.productLineId
          );

          if (!params.data.pricingTerm) {
            return (
              <div className="flex items-center justify-center h-full pr-4">
                <Label as="text" />
              </div>
            );
          }
          if (loadingLines.includes(line.productLineId)) {
            return (
              <div className="flex items-center justify-center h-full pr-4">
                <Skeleton.Node active style={{ width: 100, height: 28 }} />
              </div>
            );
          }

          return (
            <div className="flex items-center justify-center h-full pr-4">
              <Label as="currency" label={line?.totalPrice} />
            </div>
          );
        },
      },
      ...(editable
        ? [
            {
              headerName: 'Actions',
              field: 'actions',
              flex: 1,
              minWidth: 150,
              cellRenderer: (params: any) => {
                const index = quoteProductLines.findIndex(
                  (line) => line.productLineId === params.data.productLineId
                );
                const line = quoteProductLines[index];
                const isFieldDisabled = isSubscriptionFieldDisabled(
                  line,
                  quoteProducts
                );

                return (
                  <div className="flex items-center justify-center h-full pr-4">
                    <Button
                      disabled={isFieldDisabled}
                      size="small"
                      variant="secondary"
                      onClick={() =>
                        handleOpenProductDetails(params.data.productLineId)
                      }
                      icon={<Icon size="small" type="info" useMaterialIcon />}
                    >
                      Details
                    </Button>
                  </div>
                );
              },
            },
          ]
        : []),
    ],
    [quoteProductLines, subscriptions, quoteProducts, loadingLines, editable]
  );

  if (quoteProducts.length === 0) {
    return (
      <>
        <Card
          className="rounded-lg shadow-sm h-full"
          title={
            <Typography.H3 fontWeight="bold">
              Products & Product Lines
            </Typography.H3>
          }
        >
          <div className="text-center py-8">
            <Typography.P color="cool-grey-600">
              No products selected yet. Please select products from the previous
              step.
            </Typography.P>
          </div>
        </Card>
      </>
    );
  }
  const expandableConfig = useCallback(
    (isSubscriptionProduct: boolean) => {
      return {
        expandedRowRender: (record: {
          productLines?: any[];
          productId: string;
        }) => {
          return (
            <div className="p-2">
              {record.productLines && record.productLines.length > 0 ? (
                <DataGrid
                  rowData={record.productLines}
                  columnDefs={productLineColumns}
                  defaultColDef={{
                    resizable: true,
                    sortable: true,
                    filter: true,
                    suppressKeyboardEvent: () => true,
                  }}
                  domLayout="autoHeight"
                  suppressHorizontalScroll={false}
                />
              ) : (
                <div className="p-2 text-gray-500">
                  No product lines available for this product.
                </div>
              )}
            </div>
          );
        },
        expandedRowKeys: isSubscriptionProduct
          ? subscriptionExpandedRows
          : nonRecurringExpandedRows,

        onExpandedRowsChange: (expandedKeys: React.Key[]) => {
          if (isSubscriptionProduct) {
            setSubscriptionExpandedRows(expandedKeys);
          } else {
            setNonRecurringExpandedRows(expandedKeys);
          }
        },
      };
    },
    [productLineColumns, subscriptionExpandedRows, nonRecurringExpandedRows]
  );

  return (
    <>
      <div className="flex flex-col gap-4">
        <Typography.H3 fontWeight="bold">
          Products & Product Lines
        </Typography.H3>

        {/* Recurring Products Section */}
        <div className="flex flex-col gap-5">
          {subscriptionProducts.length > 0 && (
            <Card
              className="mb-4 rounded-lg shadow-sm"
              headStyle={{ padding: '8px 16px' }}
              title={
                <div className="flex justify-between items-center">
                  <span className="font-bold">Recurring Products</span>
                  <Button
                    onClick={handleExpandAllSubscription}
                    icon={
                      <Icon
                        name={
                          subscriptionExpandedRows.length > 0
                            ? 'minus-square'
                            : 'plus-square'
                        }
                      />
                    }
                    type="button"
                    size="small"
                  >
                    {subscriptionExpandedRows.length > 0
                      ? 'Collapse All'
                      : 'Expand All'}
                  </Button>
                </div>
              }
              bodyStyle={{ padding: '0px' }}
            >
              <Table
                dataSource={subscriptionProducts.map((product) => {
                  const productDetails = getProduct(product.productId);
                  return {
                    ...product,
                    isSubscriptionProduct: true,
                    productName:
                      productDetails?.productName || 'Unknown Product',
                    productCode: productDetails?.productCode || 'N/A',
                    description:
                      productDetails?.description || 'No description available',
                    productLines: groupedProductLines[product.productId] || [],
                  };
                })}
                columns={getColumnsForProductType(true)}
                pagination={false}
                rowKey="productId"
                className="rounded-lg"
                expandable={expandableConfig(true)}
                size="small"
                locale={{ emptyText: 'No subscription products added' }}
              />
            </Card>
          )}

          {/* Non Recurring Products Section */}
          {nonRecurringProducts.length > 0 && (
            <Card
              className="mb-4 rounded-lg shadow-sm"
              headStyle={{ padding: '8px 16px' }}
              title={
                <div className="flex justify-between items-center">
                  <span className="font-bold">Non Recurring Products</span>
                  <Button
                    onClick={handleExpandAllNonRecurring}
                    icon={
                      <Icon
                        name={
                          nonRecurringExpandedRows.length > 0
                            ? 'minus-square'
                            : 'plus-square'
                        }
                      />
                    }
                    type="button"
                    size="small"
                  >
                    {nonRecurringExpandedRows.length > 0
                      ? 'Collapse All'
                      : 'Expand All'}
                  </Button>
                </div>
              }
              bodyStyle={{ padding: '0px' }}
            >
              <Table
                dataSource={nonRecurringProducts.map((product) => {
                  const productDetails = getProduct(product.productId);
                  return {
                    ...product,
                    productName:
                      productDetails?.productName || 'Unknown Product',
                    productCode: productDetails?.productCode || 'N/A',
                    description:
                      productDetails?.description || 'No description available',
                    productLines: groupedProductLines[product.productId] || [],
                  };
                })}
                columns={getColumnsForProductType(false)}
                pagination={false}
                rowKey="productId"
                className="rounded-lg"
                expandable={expandableConfig(false)}
                size="small"
                locale={{ emptyText: 'No non-recurring products added' }}
              />
            </Card>
          )}
        </div>
      </div>

      {editable && (
        <ProductDetailsDrawer
          visible={isDrawerVisible}
          onClose={handleCloseProductDetails}
          productLineId={selectedProductLineId}
        />
      )}
    </>
  );
};

export default memo(ProductLinesTable);
