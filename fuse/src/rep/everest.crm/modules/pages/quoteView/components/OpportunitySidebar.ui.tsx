import React from 'react';
import { Card } from 'antd';
import { useTranslation } from 'react-i18next';
import { Typography } from '@everestsystems/design-system';
import { useGetQuoteViewDetails } from '@pkg/everest.crm/modules/hooks/quoteView/useGetQuoteViewDetails.ui';

interface OpportunitySidebarProps {
  quoteId: number;
  onOpportunityClick: () => void;
}

/**
 * OpportunitySidebar component displays only the opportunity details
 */
const OpportunitySidebar: React.FC<OpportunitySidebarProps> = ({
  quoteId,
  onOpportunityClick,
}) => {
  const { t } = useTranslation();

  const { quoteDetails } = useGetQuoteViewDetails(quoteId);

  const opportunity = quoteDetails?.opportunity;

  return (
    <Card title={t('Opportunity Details')} bodyStyle={{ padding: 12 }}>
      <div>
        <div style={{ marginBottom: 8 }}>
          <Typography.P color="cool-grey-600">
            {t('Opportunity Name')}
          </Typography.P>
          <Typography.P fontWeight="bold">
            {opportunity?.opportunityName || 'N/A'}
          </Typography.P>
        </div>
        <div style={{ marginBottom: 8 }}>
          <Typography.P color="cool-grey-600">
            {t('Opportunity Number')}
          </Typography.P>
          <Typography.P
            style={{ color: '#1890ff' }}
            onClick={onOpportunityClick}
            className="cursor-pointer"
          >
            {opportunity?.opportunityNumber || 'N/A'}
          </Typography.P>
        </div>
        <div style={{ marginBottom: 8 }}>
          <Typography.P color="cool-grey-600">{t('Currency')}</Typography.P>
          <Typography.P>{opportunity?.currency || 'N/A'}</Typography.P>
        </div>
        <div style={{ marginBottom: 8 }}>
          <Typography.P color="cool-grey-600">
            {t('Opportunity Type')}
          </Typography.P>
          <Typography.P>
            {opportunity?.opportunityTypeText || 'N/A'}
          </Typography.P>
        </div>
        <div style={{ marginBottom: 8 }}>
          <Typography.P color="cool-grey-600">{t('Stage')}</Typography.P>
          <Typography.P>{opportunity?.stage || 'N/A'}</Typography.P>
        </div>
      </div>
    </Card>
  );
};

export default OpportunitySidebar;
