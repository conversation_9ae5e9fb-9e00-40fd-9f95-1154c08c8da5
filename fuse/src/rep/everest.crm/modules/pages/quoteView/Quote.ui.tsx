import React, { useEffect, useState } from 'react';
import { Card, Spin, Row, Col } from 'antd';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { EvstQuoteApprovalStatus } from '@pkg/everest.crm/types/enums/QuoteApprovalStatus';

// Import custom hooks

import { useSetQuoteApprovalStatus } from '@pkg/everest.crm/modules/hooks/quoteView/useSetQuoteApprovalStatus.ui';
import { useUpdateQuoteMemo } from '@pkg/everest.crm/modules/hooks/quoteView/useUpdateQuoteMemo.ui';

// Import components
import { ErrorState } from './components/HelperComponents.ui';
import QuoteHeader from './components/QuoteHeader.ui';
import QuoteDetails from './components/QuoteDetails.ui';
import QuoteNotes from './components/QuoteNotes.ui';
import ProductsTab from './components/ProductsTab.ui';
import SubscriptionsTab from './components/SubscriptionsTab.ui';
import OpportunitySidebar from './components/OpportunitySidebar.ui';
import QuoteSummary from './components/QuoteSummary.ui';

import { useGetQuoteViewDetails } from '@pkg/everest.crm/modules/hooks/quoteView/useGetQuoteViewDetails.ui';

// Define the schema for quote notes validation
const quoteNotesSchema = z.object({
  notes: z.string().optional(),
});

type QuoteNotesFormValues = z.infer<typeof quoteNotesSchema>;

/**
 * NewQuotePage component displays the quote details page
 * with all the information about a quote
 */

const NewQuotePage = ({ pageState }: { pageState: any }) => {
  const [isEditingNotes, setIsEditingNotes] = useState<boolean>(false);

  // Initialize the form with react-hook-form and zod resolver
  const form = useForm<QuoteNotesFormValues>({
    resolver: zodResolver(quoteNotesSchema),
    defaultValues: {
      notes: '',
    },
  });
  const notes = form.watch('notes');

  // Set page title
  pageState.tabTitle = 'Quote Details';

  // Get quote ID from URL params

  const { search } = useLocation();
  const quoteId = Number(new URLSearchParams(search).get('id'));

  // Hooks
  const { t } = useTranslation();
  // Fetch quote data
  const {
    quoteDetails: quoteDetails,
    isLoading: isLoadingQuote,
    error: quoteError,
    refetch: refetchQuote,
  } = useGetQuoteViewDetails(quoteId);

  useEffect(() => {
    if (!notes) {
      form.setValue('notes', quoteDetails?.quote?.memo || '');
    }
  }, [quoteDetails?.quote?.memo]);
  console.log({ quoteDetails });
  // Quote approval mutation
  const { setQuoteApprovalStatus, isLoading: isApproving } =
    useSetQuoteApprovalStatus();
  // Quote memo update mutation
  const { updateQuoteMemo } = useUpdateQuoteMemo();

  // Handle saving quote notes
  const handleSaveNotes = () => {
    // Get the current notes value from the form
    const notes = form.getValues('notes');

    // Update the quote memo using our hook
    updateQuoteMemo({
      quoteId: quoteId,
      memo: notes || '',
    });

    // Exit editing mode
    setIsEditingNotes(false);
  };
  const handleApproveQuote = async () => {
    try {
      await setQuoteApprovalStatus({
        quoteId: quoteDetails?.quote.id,
        newStatus: EvstQuoteApprovalStatus.Approved,
      });

      // Refetch quote data to reflect the changes
      refetchQuote();
    } catch (error) {
      console.error('Error approving quote:', error);
    }
  };

  const handleEditQuoteNotes = () => {
    setIsEditingNotes(!isEditingNotes);
  };

  const handleEditQuote = () => {
    // Toggle editing mode for notes
  };

  const handleCancelEditNotes = () => {
    // Exit editing mode
    setIsEditingNotes(false);
  };
  // Loading state
  if (isLoadingQuote) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Spin size="large" />
      </div>
    );
  }

  // Error state
  if (quoteError) {
    return <ErrorState error={quoteError} onRetry={refetchQuote} />;
  }

  return (
    <div className="p-6 min-h-screen flex flex-col gap-6">
      {/* Header with Quote information */}
      <Card title={<QuoteHeader quoteId={quoteId} onEdit={handleEditQuote} />}>
        {/* Quote Details with Opportunity sidebar integrated */}
        <Row gutter={[24, 24]}>
          <Col span={18}>
            <div className="flex flex-col justify-between h-full">
              <QuoteDetails {...{ quoteId }} />

              <QuoteNotes
                control={form.control}
                isEditing={isEditingNotes}
                onSave={handleSaveNotes}
                onCancel={handleCancelEditNotes}
                onEdit={handleEditQuoteNotes}
                watchedNotes={form.watch('notes')}
              />
            </div>
          </Col>
          <Col span={6}>
            <OpportunitySidebar
              {...{ quoteId }}
              onOpportunityClick={() => {}}
            />
          </Col>
        </Row>
      </Card>
      {/* Products Sections Side-by-Side */}
      <Row gutter={[24, 24]}>
        {/* Non Recurring Products Section */}
        <Col span={24}>
          <Card
            title={
              <h3 className="text-lg font-medium">
                {t('Non Recurring Products')}
              </h3>
            }
          >
            <ProductsTab {...{ quoteId }} />
          </Card>
        </Col>

        {/* Subscription Products Section */}
        <Col span={24}>
          <Card
            title={
              <h3 className="text-lg font-medium">
                {t('Subscription Products')}
              </h3>
            }
          >
            <SubscriptionsTab {...{ quoteId }} />
          </Card>
        </Col>
      </Row>

      {/* Quote Summary at the end with Approve button */}
      <QuoteSummary
        quoteId={quoteId}
        onApprove={handleApproveQuote}
        isApproving={isApproving}
      />
    </div>
  );
};

export default NewQuotePage;
