import OpportunityDetailsCard from '@pkg/everest.crm/modules/pages/opportunity/components/ui/OpportunityDetailsCard.ui';
import useOpportunityDetails from '@pkg/everest.crm/modules/hooks/opportunity/useOpportunityDetails.ui';
import useGetPrimaryQuoteProducts from '@pkg/everest.crm/modules/hooks/quote/useGetPrimaryQuoteProducts.ui';
import useSetQuotePrime from '@pkg/everest.crm/modules/hooks/quote/useSetQuotePrime.ui';
import { useOpportunityQuotes } from '@pkg/everest.crm/modules/hooks/opportunity/useOpportunityQuotes.ui';
import useCreateSalesOrder from '@pkg/everest.crm/modules/hooks/opportunity/useCreateSalesOrder.ui';
import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Icon,
  Typography,
  Button,
  DataGrid,
  AlertProps,
} from '@everestsystems/design-system';
import { useHistory, useLocation } from 'react-router-dom';

import { Card, Row, Col, Spin } from 'antd';
import { CREATE_QUOTE_URL } from '@pkg/everest.crm/modules/pages/constants';
import { ColDef } from 'ag-grid-community';
import Label from '@pkg/everest.crm/modules/pages/quotes/components/ui/label/label.ui';
import QuoteProductsList from '@pkg/everest.crm/modules/pages/quotes/components/QuoteProductsList.ui';
import AppLayout from '@pkg/everest.crm/layouts/app/AppLayout.ui';
import { AppLayoutProps } from '@pkg/everest.crm/layouts/app/types.ui';
import { EvstOpportunityStageType } from '@pkg/everest.crm/types/enums/OpportunityStageType';
const { P } = Typography;
import { formatDate } from '@pkg/everest.base/public/utils/templatingUtils';
import StatusTag from '@pkg/everest.crm/modules/pages/quotes/components/ui/status-tag/status-tag.ui';

const NewOpportunity = ({ pageState }) => {
  const history = useHistory();
  const { search } = useLocation();
  const opportunityId = Number(new URLSearchParams(search).get('id'));

  pageState.tabTitle = `Opportunity OP-${opportunityId}`;

  // Get opportunity data for title and status
  const { data: opportunityData, isLoading: isLoadingOpportunityData } =
    useOpportunityDetails(opportunityId);

  const [primaryQuote] = useState<any>(null);

  // Fetch quotes using the useOpportunityQuotes hook
  const { data: quotes, isLoading: isLoadingQuotes } =
    useOpportunityQuotes(opportunityId);

  // Filter out empty objects from quotes array
  const filteredQuotes = useMemo(() => {
    if (!Array.isArray(quotes)) {
      return [];
    }

    return quotes;
  }, [quotes]);
  const {
    data: primaryQuoteProducts,
    isFetching: isLoadingPrimaryQuoteProducts,
  } = useGetPrimaryQuoteProducts(opportunityId);

  // Use the specialized hook for SetQuotePrime
  const { executeAction: setQuotePrimeAction, isLoading: isSettingPrimary } =
    useSetQuotePrime();

  // Use the specialized hook for CreateSalesOrder
  const { createSalesOrder, isLoading: isCreatingSalesOrder } =
    useCreateSalesOrder();

  const { t } = useTranslation();

  const showNotFound =
    (!isLoadingOpportunityData && !opportunityData) || !opportunityId;

  const handleCreateQuote = () => {
    history.push(`${CREATE_QUOTE_URL}?opportunityId=${opportunityId}`);
  };

  // Handler for setting a quote as primary
  const handleSetPrimary = async (quoteId: number) => {
    try {
      await setQuotePrimeAction({
        quoteId: quoteId,
        opportunityId: opportunityId,
      });
    } catch (error) {
      console.error('Error setting quote as primary:', error);
    }
  };
  const quoteColumns: ColDef[] = [
    {
      headerName: 'Number',
      field: 'quoteNumber',
      flex: 1,
      cellRenderer: (params: any) => {
        if (!params.data?.quoteNumber) {
          return '';
        }
        return (
          <div className="flex h-full items-center">
            <Button
              variant="link"
              onClick={() => {
                history.push(
                  `/content/everest.crm/modules/pages/quoteView/Quote?id=${params.data.id}`
                );
              }}
            >
              {params.data.quoteNumber}
            </Button>
          </div>
        );
      },
    },
    {
      headerName: 'Quote Date',
      field: 'quoteDate',
      flex: 1,
      valueFormatter: (params) => {
        return params.value ? formatDate(params.value) : '';
      },
    },
    {
      headerName: 'Service Start',
      field: 'serviceStartDate',
      flex: 1,
      valueFormatter: (params) => {
        return params.value ? formatDate(params.value) : '';
      },
    },
    {
      flex: 1,
      field: 'approvalStatusText',
      headerName: 'Status',
      minWidth: 150,
      cellRenderer: (params: any) => {
        return (
          <StatusTag
            label={params.data.approvalStatusText}
            value={params.data.approvalStatus}
          />
        );
      },
    },
    {
      field: 'totalPrice',
      headerName: 'Total Price',
      flex: 1,
      cellRenderer: (params: any) => {
        if (!params.data?.totalPrice) {
          return '';
        }
        return (
          <div className="h-full flex items-center">
            <Label as="currency" label={params.data.totalPrice} />
          </div>
        );
      },
    },
    {
      width: 150,
      headerName: 'Actions',
      field: 'actions',
      cellRenderer: (params: any) => {
        if (!params.data) {
          return null;
        }
        const isPrimary = params.data.isPrimary;

        return (
          <div className="h-full flex items-center">
            {isPrimary ? (
              <div className="flex items-center text-green-600">
                <Icon type="check" size="small" color="#52c41a" />
                <span className="ml-1">{t('Primary')}</span>
              </div>
            ) : (
              <Button
                size="small"
                variant="primary"
                onClick={() => handleSetPrimary(params.data.id)}
                disabled={isSettingPrimary}
              >
                {isSettingPrimary ? t('Setting...') : t('Set Primary')}
              </Button>
            )}
          </div>
        );
      },
    },
  ];

  const title = `${opportunityData?.opportunityNumber} ${
    opportunityData?.opportunityName
      ? `:${opportunityData.opportunityName}`
      : ''
  }`;

  const status = opportunityData?.stage;

  const handleRetrySalesOrderCreation = () => {
    createSalesOrder(opportunityId);
  };

  const actions: AppLayoutProps['actions'] = [
    {
      label: t('Create Quote'),
      onClick: handleCreateQuote,
      variant: 'primary',
      disabled: opportunityData?.stageType !== EvstOpportunityStageType.Open,
    },
    ...(opportunityData?.stageType === EvstOpportunityStageType.ClosedWon &&
    !opportunityData?.salesOrderId
      ? [
          {
            label: t('Retry Sales Order Creation'),
            onClick: handleRetrySalesOrderCreation,
            variant: 'secondary' as const,
            disabled: isCreatingSalesOrder,
          },
        ]
      : []),
  ];
  const shouldShowSalesOrderError =
    opportunityData?.stageType === EvstOpportunityStageType.ClosedWon &&
    (!opportunityData?.salesOrderId || opportunityData?.salesOrderIsDraft);

  const alert: AlertProps = shouldShowSalesOrderError
    ? { children: opportunityData.salesOrderCreationError, variant: 'warning' }
    : undefined;

  return (
    <AppLayout
      {...{
        title,
        status,
        actions,
        alert,
        loading: isLoadingOpportunityData,
        notFound: {
          show: showNotFound,
          props: {
            title: t('Opportunity Not Found'),
            message: t(
              'The opportunity you are trying to access does not exist or has been deleted.'
            ),
          },
        },
      }}
    >
      {/* Left Column - Opportunity Details and Quotes */}
      <Col xs={24} lg={18}>
        <Row gutter={[16, 16]}>
          {/* Opportunity Details Card */}
          <Col span={24}>
            <OpportunityDetailsCard opportunityId={opportunityId} />
          </Col>

          {/* Quotes Card */}
          <Col span={24}>
            <Spin spinning={isLoadingQuotes}>
              <Card title={t('Quotes')}>
                {filteredQuotes.length > 0 ? (
                  <DataGrid
                    rowData={filteredQuotes}
                    columnDefs={quoteColumns}
                    defaultColDef={{
                      resizable: true,
                      sortable: true,
                      filter: true,
                    }}
                    onCellClicked={(params) => {
                      if (
                        !params.data?.id ||
                        params.colDef.field === 'actions'
                      ) {
                        return;
                      }

                      history.push(
                        `/content/everest.crm/modules/pages/quoteView/Quote?id=${params.data.id}`
                      );
                    }}
                  />
                ) : (
                  <div className="text-center p-4">
                    <Icon
                      type="inbox"
                      color="#7F8792"
                      className="text-xl mb-4"
                    />
                    <P color="cool-grey-600">{t('No quotes found')}</P>
                  </div>
                )}
              </Card>
            </Spin>
          </Col>
        </Row>
      </Col>

      {/* Right Column - Products in Primary Quote */}
      <Col xs={24} lg={6}>
        <Spin spinning={isLoadingPrimaryQuoteProducts}>
          <Card
            className="mb-4"
            title={
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Icon
                    type="shopping_cart"
                    size="small"
                    color="#7F8792"
                    useMaterialIcon
                  />
                  <span>{t('Primary Quote Products')}</span>
                </div>
                {primaryQuote && (
                  <Typography.Span color="amaranth">
                    {primaryQuote.quoteNumber}
                  </Typography.Span>
                )}
              </div>
            }
          >
            {primaryQuoteProducts && primaryQuoteProducts.length > 0 ? (
              <QuoteProductsList products={primaryQuoteProducts} />
            ) : (
              <div className="text-center p-4">
                <Icon type="inbox" color="#7F8792" className="text-xl mb-4" />
                <P color="cool-grey-600">{t('No products found')}</P>
              </div>
            )}
          </Card>
        </Spin>
      </Col>
    </AppLayout>
  );
};

export default NewOpportunity;
