import type { ISession } from '@everestsystems/content-core';
import { Decimal } from '@everestsystems/decimal';
import { Account } from '@pkg/everest.fin.accounting/types/Account';
import { EvstAccountStatus } from '@pkg/everest.fin.accounting/types/enums/AccountStatus';
import { EvstAccountSubType } from '@pkg/everest.fin.accounting/types/enums/AccountSubType';
import type {
  PaymentHeaderForUpsert,
  PaymentLineForUpsert,
} from '@pkg/everest.fin.expense/public/outboundPayment';
import {
  getSourcePaymentDocumentPath,
  SourcePaymentDocumentType,
} from '@pkg/everest.fin.expense/public/outboundPayment';
import { OutboundPaymentHeaderBase } from '@pkg/everest.fin.expense/types/OutboundPaymentHeaderBase';
import type { OutboundPaymentItemBase } from '@pkg/everest.fin.expense/types/OutboundPaymentItemBase';
import type { LoadOrMatchFunctionReturnType } from '@pkg/everest.fin.integration.base/public/types/etlTypes';
import { EvstStagingStatus } from '@pkg/everest.fin.integration.base/types/enums/StagingStatus';
import { RampDefaultAccountsUtil } from '@pkg/everest.fin.integration.ramp/actions/expenseTemplate/utils/defaultAccounts';
import { VendorBillUtil } from '@pkg/everest.fin.integration.ramp/actions/expenseTemplate/utils/vendorBill';
import { EvstRampOperationalModes } from '@pkg/everest.fin.integration.ramp/types/enums/RampOperationalModes';
import { RampConnector } from '@pkg/everest.fin.integration.ramp/utils/connector';
import { RampAPIConsts } from '@pkg/everest.fin.integration.ramp/utils/consts';
import { RampSettingsAPI } from '@pkg/everest.fin.integration.ramp/utils/settings';
import type { RampTransformedTypes } from '@pkg/everest.fin.integration.ramp/utils/transformedTypes';

const documentType = SourcePaymentDocumentType.VendorBillHeaderBase;

export async function loadBillPaymentsData(
  env: ISession,
  transformedData: RampTransformedTypes.BillPayment,
  everestId: number | undefined,
  connectorId?: number
): LoadOrMatchFunctionReturnType {
  if (everestId) {
    const currentRampOperationalMode =
      await RampSettingsAPI.getCurrentOperationalMode(env, connectorId);
    if (currentRampOperationalMode === EvstRampOperationalModes.SideBySide) {
      return {
        everestId,
        status: EvstStagingStatus.Matched,
      };
    } else {
      throw new Error('Not expecting to load this again');
    }
  }

  let paymentAccountId: number;
  let paymentCurrency: string;
  if (
    [
      RampAPIConsts.BillPaymentMethod.ACH,
      RampAPIConsts.BillPaymentMethod.INTERNATIONAL,
    ].includes(transformedData.paymentMethod)
  ) {
    paymentAccountId = await RampDefaultAccountsUtil.getDefaultAccount(
      env,
      connectorId,
      transformedData.entityId,
      RampDefaultAccountsUtil.DefaultAccount.BillPayBank
    );
    if (
      transformedData.paymentMethod ===
        RampAPIConsts.BillPaymentMethod.INTERNATIONAL &&
      VendorBillUtil.validateInternationalBankPayment(transformedData)
    ) {
      paymentCurrency = transformedData.paymentInfo.amount.currency_code;
    }
  } else {
    throw new Error('this type of payment is not supported');
  }

  const bankAccount = await Account.read(env, { id: paymentAccountId }, [
    'id',
    'accountName',
    'accountNumber',
    'accountStatus',
    'accountSubType',
  ]);
  if (bankAccount.accountStatus !== EvstAccountStatus.Enabled) {
    throw new Error(
      `cannot accept "${bankAccount.accountName} ${bankAccount.accountNumber}" account as it is disabled`
    );
  }
  if (bankAccount.accountSubType !== EvstAccountSubType.Bank) {
    throw new Error(
      `cannot accept "${bankAccount.accountName} ${bankAccount.accountNumber}" account as it is not of type bank`
    );
  }

  const connectivityTemplate =
    await RampConnector.getConnectivityTemplateByConnectorId(env, connectorId);

  const outboundPaymentClient = await OutboundPaymentHeaderBase.client(env);
  let sumAmount = new Decimal(0);
  let aggLineDescription = null;

  for (const lineItem of transformedData.lineItems) {
    sumAmount = sumAmount.plus(lineItem.amount);
    aggLineDescription = aggLineDescription || lineItem.description;
  }
  const paymentItem: Required<
    Omit<PaymentLineForUpsert, 'id' | 'receiverName'>
  > = {
    entityId: transformedData.entityId,
    paymentAmount: {
      amount: sumAmount,
      ...(paymentCurrency && { currency: paymentCurrency }),
    },
    sourcePaymentDocumentId: transformedData.sourcePaymentDocumentId,
    sourcePaymentDocumentPath: getSourcePaymentDocumentPath(documentType),
    description: (aggLineDescription ?? '') || 'N/A',
  };
  const paymentHeader: PaymentHeaderForUpsert = {
    paymentDate: transformedData.paidAt,
    accountId: paymentAccountId,
    entityId: transformedData.entityId,
    commonDescription: transformedData.description || '',
    origin: 'Ramp',
    externalUrl: `${connectivityTemplate.baseWebsiteUrl}${RampAPIConsts.WebsiteLanding.Bill}${transformedData.id}`,
  };

  const newPayment = await outboundPaymentClient.upsertManualOutboundPayment(
    paymentHeader as OutboundPaymentHeaderBase.OutboundPaymentHeaderBase,
    [paymentItem] as OutboundPaymentItemBase.OutboundPaymentItemBase[],
    documentType,
    { fieldList: ['id'] }
  );

  return {
    everestId: newPayment.id,
    status: EvstStagingStatus.Integrated,
  };
}
