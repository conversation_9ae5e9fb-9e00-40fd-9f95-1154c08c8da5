{"version": 3, "uicontroller": ["connector.uicontroller.ts"], "uimodel": {"state": {"id": null, "state": "", "code": "", "mode": "view"}, "presentation": {"urn": "urn:evst:everest:fin/integration/ramp:presentation:uinext/connector", "parameters": {"mode": "@state:mode", "id": "@state:id", "code": "@state:code", "state": "@state:state"}}}, "uiview": {"i18n": ["everest.connectorengine/connectorEngine", "ramp"], "title": "{{ramp.connector.manage}}", "config": {"allowRefreshData": true, "autoRefreshData": true}, "header": {"content": {"title": "{{ramp.connector.manage}}"}}, "sections": {"content": [{"component": "<PERSON><PERSON>", "section": {"grid": {"size": "12"}}, "visible": "@binding:connectorHelper.isAnotherConnectorPresent", "props": {"variant": "warning", "title": "{{ramp.connector.exist.title}}", "content": "{{ramp.connector.exist.content}}"}}, {"component": "FieldGroup", "customId": "rampConnectorMain", "section": {"grid": {"size": "12"}, "title": "{{ramp.connector.main}}"}, "props": {"columns": "1", "presentationDataSet": "@dataSet:connectorMain", "fields": [{"field": "name", "fieldProps": {"label": "{{ramp.connector.name}}"}}, {"field": "integrationName", "fieldProps": {"label": "{{ramp.connector.integrationName}}", "component": "Select", "idProp": "value", "textProp": "text", "list": "@binding:connectorHelper.integrationNamesList"}}]}}, {"component": "FieldGroup", "customId": "rampConnectorConnectionSettings", "section": {"grid": {"size": "12"}, "title": "{{ramp.connector.connectionSettings}}"}, "props": {"columns": "2", "presentationDataSet": "@dataSet:connectorSettings", "fields": [{"field": "baseUrl", "fieldProps": {"label": "{{ramp.connector.baseUrl}}"}}, {"field": "authorizationUrl", "fieldProps": {"label": "{{ramp.connector.authorizationUrl}}"}}, {"field": "clientId", "fieldProps": {"label": "{{ramp.connector.clientId}}"}}, {"field": "clientSecret", "fieldProps": {"label": "{{ramp.connector.clientSecret}}", "type": "@controller:ConnectorUIController.getClientSecretFieldType()"}}]}}]}, "actions": {"content": [{"variant": "primary", "label": "{{connectorEngine.save}}", "presentationAction": "@action:saveConnector", "disabled": "@controller:ConnectorUIController.isSaveConnectorDisabled()", "onClick": "@controller:ConnectorUIController.saveConnectorOnClick"}, {"variant": "secondary", "label": "{{connectorEngine.test}}", "presentationAction": "@action:testConnector", "onClick": "@controller:ConnectorUIController.testConnectorOnClick"}, {"variant": "primary", "presentationAction": "@action:generateAuthorisationState", "onClick": "@controller:ConnectorUIController.authenticateOnClick"}]}}}