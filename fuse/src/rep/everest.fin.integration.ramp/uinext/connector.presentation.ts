import {
  DATA,
  getTranslation,
  type ISession,
  METADATA,
} from '@everestsystems/content-core';
import type { connectorPresentation } from '@pkg/everest.fin.integration.ramp/types/presentations/uinext/connector';
import { Ramp } from '@pkg/everest.fin.integration.ramp/types/Ramp';
import { RampConnector } from '@pkg/everest.fin.integration.ramp/utils/connector';
import { RAMP_CONNECTOR_NAME } from '@pkg/everest.fin.integration.ramp/utils/consts';
import { RampOAuth } from '@pkg/everest.fin.integration.ramp/utils/oAuth';
import { RampSettingsAPI } from '@pkg/everest.fin.integration.ramp/utils/settings';
import type { StateConnectivityInfoType } from '@pkg/everest.fin.integration.ramp/utils/types';
import { isEmpty, omit, pick, set } from 'lodash';

export class ConnectorClass
  implements connectorPresentation.dataSources.Connector.implementation
{
  // INTERNAL DATA

  #connector:
    | connectorPresentation.dataSources.Connector.levels['']
    | undefined = undefined;

  // PRIVATE METHODS

  private async fetchData(
    session: ISession,
    parameters: connectorPresentation.dataSources.Connector.callbacks.query.input['parameters']
  ) {
    if (parameters.id) {
      const existingConnectorData = await RampConnector.getRampActiveConnector(
        session,
        parameters.id,
        false
      );

      if (!existingConnectorData) {
        throw new Error('Connector not found!');
      }

      const connectivity = await RampConnector.getConnectivity(
        session,
        parameters.id
      );
      const client = await RampOAuth.initOAuthClient(
        session,
        existingConnectorData,
        connectivity
      );

      const integrationNamesList = [
        {
          value: connectivity.integrationName,
          text: connectivity.integrationName,
        },
      ];

      this.#connector = {
        id: existingConnectorData.id,
        name: existingConnectorData.name,
        integrationName: connectivity.integrationName,
        baseUrl: connectivity.baseUrl,
        authorizationUrl: connectivity.authorizationUrl,
        clientId: client.configuration.clientId,
        clientSecret: client.configuration.clientSecret,
        integrationNamesList,
        isStateCorrect: false,
        isAnotherConnectorPresent: false,
        isPerviousSettingsExist: false,
      };
    } else if (parameters.state && parameters.code) {
      let existingConnectorData: RampConnector.ConnectorEngineData;
      try {
        existingConnectorData =
          await RampConnector.getRampActiveConnector(session);
      } catch {
        // noop
      }

      const stateConnectivity = await RampOAuth.getOAuthStateConnectivity(
        session,
        parameters.state
      );
      const settings = await RampSettingsAPI.getRampSettings(session);
      if (stateConnectivity) {
        const integrationNamesList = [
          {
            value: stateConnectivity.integrationName,
            text: stateConnectivity.integrationName,
          },
        ];

        this.#connector = {
          name: stateConnectivity.connectorName,
          integrationName: stateConnectivity.integrationName,
          baseUrl: stateConnectivity.baseUrl,
          authorizationUrl: stateConnectivity.authURL,
          clientId: stateConnectivity.clientId,
          clientSecret: stateConnectivity.clientSecret,
          integrationNamesList,
          state: parameters.state,
          code: parameters.code,
          isStateCorrect: true,
          isAnotherConnectorPresent:
            existingConnectorData?.id !== stateConnectivity.connectorId,
          isPerviousSettingsExist: !isEmpty(settings),
          isExistingConnectorReauthenticating:
            stateConnectivity.connectorId &&
            existingConnectorData?.id === stateConnectivity.connectorId,
        };
      } else {
        throw new Error('State is invalid!');
      }
    } else {
      let existingConnectorData: RampConnector.ConnectorEngineData;
      try {
        existingConnectorData =
          await RampConnector.getRampActiveConnector(session);
      } catch {
        // noop
      }
      const settings = await RampSettingsAPI.getRampSettings(session);
      const integrationNamesList = RampConnector.getIntegrationNamesList().map(
        (e) => ({
          value: e,
          text: e,
        })
      );

      const defaultConnectivity =
        await RampConnector.getDefaultConnectivity(session);

      this.#connector = {
        name: RAMP_CONNECTOR_NAME,
        integrationName: defaultConnectivity.integrationName,
        baseUrl: defaultConnectivity.baseUrl,
        authorizationUrl: defaultConnectivity.authURL,
        integrationNamesList,
        isStateCorrect: false,
        isAnotherConnectorPresent: !isEmpty(existingConnectorData),
        isPerviousSettingsExist: !isEmpty(settings),
      };
    }
  }

  private async getDataWithMetadata(
    _session: ISession
  ): Promise<connectorPresentation.dataSources.Connector.callbacks.query.combinedOutput> {
    // we don't send clientSecret to frontend if we edit connector or OAuth state is correct
    return {
      [DATA]: {
        ...omit(this.#connector, ['clientSecret']),
        ...(this.#connector.id || this.#connector.isStateCorrect
          ? { clientSecret: '****' }
          : pick(this.#connector, ['clientSecret'])),
      },
      [METADATA]: {
        integrationName: {
          editable: !this.#connector.isStateCorrect && !this.#connector.id,
          fieldProps: {
            component: 'Select',
            idProp: 'value',
            textProp: 'text',
            list: RampConnector.getIntegrationNamesList().map((e) => ({
              value: e,
              text: e,
            })),
          },
        },
      },
    };
  }

  // PUBLIC IMPLEMENTATION

  async query(
    input: connectorPresentation.dataSources.Connector.callbacks.query.input
  ): Promise<connectorPresentation.dataSources.Connector.callbacks.query.combinedOutput> {
    if (!this.#connector || input.queryReason === 'externalRequest') {
      await this.fetchData(input.session, input.parameters);
    }
    return this.getDataWithMetadata(input.session);
  }

  async update(
    input: connectorPresentation.dataSources.Connector.callbacks.update.input
  ): Promise<connectorPresentation.dataSources.Connector.callbacks.update.output> {
    set(this.#connector, input.fieldName, input.newFieldValue);

    if (input.fieldName === 'integrationName') {
      const connectivity = RampConnector.getConnectivityTemplateByName(
        input.newFieldValue as string
      );
      this.#connector.baseUrl = connectivity?.baseUrl;
      this.#connector.authorizationUrl = connectivity?.authURL;
      this.#connector.clientId =
        connectivity?.clientId ?? this.#connector.clientId;
      this.#connector.clientSecret =
        connectivity?.clientSecret ?? this.#connector.clientSecret;
    }
  }

  async validate_generateAuthorisationState(
    _input: connectorPresentation.dataSources.Connector.routines.generateAuthorisationState.validateInput
  ): Promise<connectorPresentation.dataSources.Connector.routines.generateAuthorisationState.validateOutput> {
    if (
      !this.#connector.baseUrl ||
      !this.#connector.authorizationUrl ||
      !this.#connector.clientId ||
      !this.#connector.clientSecret ||
      this.#connector.isAnotherConnectorPresent
    ) {
      return false;
    }

    return true;
  }

  public async determine_generateAuthorisationState(
    input: connectorPresentation.dataSources.Connector.routines.generateAuthorisationState.determineInput
  ): Promise<connectorPresentation.dataSources.Connector.routines.generateAuthorisationState.determineOutput> {
    const label = await (input.mode === 'view'
      ? getTranslation(
          input.session,
          'connectorEngine.reauthenticate',
          'everest.connectorengine/connectorEngine'
        )
      : getTranslation(
          input.session,
          'connectorEngine.authenticate',
          'everest.connectorengine/connectorEngine'
        ));

    return {
      label,
    };
  }

  async execute_generateAuthorisationState(
    input: connectorPresentation.dataSources.Connector.routines.generateAuthorisationState.executeInput
  ): Promise<connectorPresentation.dataSources.Connector.routines.generateAuthorisationState.executeOutput> {
    const connectivityInfo: StateConnectivityInfoType = {
      connectorId: this.#connector.id,
      connectorName: this.#connector.name,
      integrationName: this.#connector.integrationName,
      authURL: this.#connector.authorizationUrl,
      baseUrl: this.#connector.baseUrl,
      clientId: this.#connector.clientId,
      clientSecret: this.#connector.clientSecret,
    };

    const oauthState = await RampOAuth.createNewOAuthState(
      input.session,
      connectivityInfo
    );

    const authorizationUrl = await RampOAuth.createAuthorizationUrl(
      input.session,
      oauthState,
      connectivityInfo
    );

    return {
      authorizationUrl,
    };
  }

  async validate_saveConnector(
    _input: connectorPresentation.dataSources.Connector.routines.saveConnector.validateInput
  ): Promise<connectorPresentation.dataSources.Connector.routines.saveConnector.validateOutput> {
    return this.#connector.isStateCorrect;
  }

  async execute_saveConnector(
    input: connectorPresentation.dataSources.Connector.routines.saveConnector.executeInput
  ): Promise<connectorPresentation.dataSources.Connector.routines.saveConnector.executeOutput> {
    const id = await Ramp.client(input.session).then((client) =>
      client.saveRampConnector(
        this.#connector.name,
        this.#connector.integrationName,
        this.#connector.baseUrl,
        this.#connector.authorizationUrl,
        this.#connector.clientId,
        this.#connector.clientSecret,
        this.#connector.code,
        input.input.reuseSettings
      )
    );

    await RampOAuth.clearOAuthSate(input.session, this.#connector.state);

    this.#connector = undefined;
    input.parameters.state = undefined;
    input.parameters.code = undefined;

    return {
      id,
    };
  }

  async execute_testConnector(
    input: connectorPresentation.dataSources.Connector.routines.testConnector.executeInput
  ): Promise<connectorPresentation.dataSources.Connector.routines.testConnector.executeOutput> {
    try {
      await RampOAuth.refreshToken(input.session, this.#connector.id);
    } catch (error: unknown) {
      return {
        error: true,
        message: error instanceof Error ? error.message : String(error),
      };
    }

    return {
      error: false,
      message: '',
    };
  }
}

export const connectorImplementation: connectorPresentation.implementation = {
  Connector: () => new ConnectorClass(),
};

export default connectorImplementation;
