{"version": 3, "uimodel": {"state": {"connectorId": null}, "presentation": {"urn": "urn:evst:everest:fin/integration/ramp:presentation:uinext/configuration/outboundSync", "parameters": {"mode": "edit", "connectorId": "@state:connectorId"}}}, "uicontroller": ["outboundSync.uicontroller.ts"], "uiview": {"i18n": ["everest.fin.integration.expense/expenseIntegration", "ramp"], "actions": {"content": [{"variant": "primary", "label": "{{expenseIntegration.save}}", "presentationAction": "@action:save", "onClick": "@controller:OutboundSyncUIController.save"}, {"variant": "secondary", "label": "{{expenseIntegration.cancel}}", "onClick": "@controller:OutboundSyncUIController.cancel"}]}, "sections": {"content": [{"component": "FieldGroup", "customId": "enableOutboundSyncSelector", "section": {"grid": {"size": "12"}}, "props": {"columns": "1", "presentationDataSet": "@dataSet:OutboundSyncSettingsGeneral", "fields": [{"field": "enableOutboundSync", "fieldProps": {"label": "{{ramp.outboundSync.label}}", "component": "Switch", "direction": "horizontal-reverse"}}]}}, {"component": "Table", "customId": "selectedOutboundModelId", "section": {"grid": {"size": "12"}, "actions": [{"label": "{{ramp.outboundSync.selectAll}}", "presentationAction": "@action:selectAll"}]}, "props": {"presentationDataSet": "@dataSet:OutboundSyncSettingsModels", "suppressDelete": true, "hideFilters": true, "variant": "light", "columns": [{"field": "selected", "headerName": "{{ramp.outboundSync.selected}}", "pinned": "left", "suppressMovable": true, "fieldProps": {"component": "Checkbox"}}, {"field": "modelName", "headerName": "{{ramp.outboundSync.modelName}}"}]}}]}}}