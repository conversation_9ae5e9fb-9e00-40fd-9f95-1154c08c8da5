{"version": 3, "uimodel": {"state": {"connectorId": null}, "presentation": {"urn": "urn:evst:everest:fin/integration/ramp:presentation:uinext/configuration/userOutboundWhitelist", "parameters": {"connectorId": "@state:connectorId"}}}, "uicontroller": ["userOutboundWhitelist.uicontroller.ts"], "uiview": {"i18n": ["everest.fin.integration.expense/expenseIntegration", "ramp"], "actions": {"content": [{"variant": "primary", "label": "{{expenseIntegration.save}}", "presentationAction": "@action:save", "onClick": "@controller:UserOutboundWhitelistUIController.save"}, {"variant": "secondary", "label": "{{expenseIntegration.cancel}}", "onClick": "@controller:UserOutboundWhitelistUIController.cancel"}]}, "sections": {"content": [{"component": "Table", "customId": "employeesTableId", "section": {"grid": {"size": "12"}}, "props": {"presentationDataSet": "@dataSet:EmployeeList", "suppressDelete": true, "hideFilters": true, "variant": "light", "columns": [{"field": "selected", "headerName": "{{ramp.userOutboundWhitelist.selectColumnHeader}}", "suppressMovable": true, "fieldProps": {"component": "Checkbox"}, "maxWidth": 70}, {"field": "name", "headerName": "{{ramp.userOutboundWhitelist.employeeName}}"}, {"field": "email", "headerName": "{{ramp.userOutboundWhitelist.employeeEmail}}"}]}}]}}}