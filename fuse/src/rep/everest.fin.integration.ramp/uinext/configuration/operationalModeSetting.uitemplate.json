{"version": 3, "uimodel": {"state": {"connectorId": null}, "presentation": {"urn": "urn:evst:everest:fin/integration/ramp:presentation:uinext/configuration/operationalModeSetting", "parameters": {"mode": "edit", "connectorId": "@state:connectorId"}}}, "uicontroller": ["operationalModeSetting.uicontroller.ts"], "uiview": {"i18n": ["everest.fin.integration.expense/expenseIntegration", "ramp"], "title": "{{ramp.operationalModeSettings.title}}", "actions": {"content": [{"variant": "primary", "label": "{{expenseIntegration.save}}", "presentationAction": "@action:save", "onClick": "@controller:OperationalModeSettingUIController.save"}, {"variant": "secondary", "label": "{{expenseIntegration.cancel}}", "onClick": "@controller:OperationalModeSettingUIController.cancel"}, {"variant": "secondary", "label": "Create Connection", "presentationAction": "@action:activateAccountingConnection", "onClick": "@controller:OperationalModeSettingUIController.activateAccountingConnection"}]}, "sections": {"content": [{"component": "FieldGroup", "customId": "operationalModeSetting", "section": {"grid": {"size": "12"}, "alert": {"content": "{{ramp.operationalModeSettings.enabledOutboundSyncAlert}}", "variant": "warning", "visible": "@binding:rampSettingsData.enableOutboundSync"}}, "props": {"presentationDataSet": "@dataSet:rampSettingsData", "columns": "1", "compact": true, "fields": [{"field": "operationalMode", "fieldProps": {"label": "{{ramp.operationalModeSettings.operationalModeSetting}}"}}]}}]}}}