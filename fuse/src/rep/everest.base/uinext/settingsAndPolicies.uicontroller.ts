// @i18n:everest.base.ui/userManagement
// @i18n:settingsAndPolicies
import { type UIExecutionContext } from '@everestsystems/content-core';
import { AccessTokenStatus } from '@pkg/everest.base/public/accessTokenStatus';
import { replaceInStringTemplate } from '@pkg/everest.base/public/utils/templatingUtils';
import { BusinessUnitUI } from '@pkg/everest.base/types/BusinessUnit.ui';
import type {
  SettingsAndPoliciesData,
  SettingsAndPoliciesUiTemplate,
} from '@pkg/everest.base/types/uiTemplates/uinext/settingsAndPolicies.ui';
import { groupBy, isEmpty, omit, uniqBy } from 'lodash';

import { EvstEntityStatus } from '../types/enums/EntityStatus';
import { EvstEntityType } from '../types/enums/EntityType';
import {
  MigrationGroup,
  ProcureToPayGroup,
  QueryChecks,
  QuoteToCashGroup,
  RecordToReportGroup,
} from './settingsAndPolicies.permissions.uicontroller';

type SettingsAndPoliciesContext =
  SettingsAndPoliciesUiTemplate.SettingsAndPoliciesContext;
type SingleNodeItemType = SettingsAndPoliciesUiTemplate.SingleNodeItemType;

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type Any = any;
type BookMappingLineReturn = Array<{
  label: string;
  description: string;
  buttonActions;
}>;
type BookMapping = {
  bookId: number;
  bookName: string;
  coaId: number;
  coaName: string;
  entityId: number;
};

let isArrSettingSaveInProgress: boolean = false;

const ACCOUNTING_PACKAGE_NAME = 'everest.fin.accounting';
const AISpecifyPackageName = 'everest.aispecify';
const ALLOW_SALES_ORDER_MASS_APPROVAL = 'AllowSalesOrderMassApprovalOption';
const ALLOW_AI_SPECIFY_BUILD = 'AllowAISpecifyBuild';
const ALLOW_MODIFY_CANCELLED_SUBSCRIPTION =
  'AllowModifyCancelledSubscriptionOption';
const REVENUE_CLOSE_PROCESS_REVERSE_JOURNAL =
  'RevenueCloseProcessReverseJournalEntryOption';
const SUPPRESS_REVENUE_PLAN_CREATION = 'SuppressRevenuePlanCreationOption';

export type PreferenceFields =
  | 'email'
  | 'password'
  | 'language'
  | 'dateFormat'
  | 'timeFormat'
  | 'currencyFormat';

export type Context = SettingsAndPoliciesContext & {
  state: {
    activeSetting: number;
    entityPicker: {
      [k: string]: unknown;
    };
    enableBook: boolean;
  };
  data: SettingsAndPoliciesData & {
    entityBooks: {
      bookId: number;
      Book: {
        name: string;
      };
      chartOfAccountsId: number;
      ChartOfAccounts: {
        name: string;
      };
      entityId: number;
    }[];
  };
};

export function getCurrentEntity(context: Context) {
  return context.state.entityPicker.entityId;
}

export function getUserQuery({ user }: Context) {
  return user?.currentUserData?.id
    ? { where: { id: user?.currentUserData?.id } }
    : undefined;
}

export function getAvatarSrc(context: Context) {
  const { data } = context;

  if (data.employee?.photo) {
    return `/api/app/storage/${data.employee.photo}`;
  }
}

export function getUserDetailsResult(context: Context, type: PreferenceFields) {
  const userConfigValue = getUserConfigValue(context, type);

  const result: Record<PreferenceFields, string> = {
    language: context.languageOptions.find(
      (option) => option.value === context.language
    )?.label,
    dateFormat: context.localeList.find(
      (locale) => locale.code === userConfigValue
    )?.dateFormat,
    timeFormat: context.timeFormat.find(
      (locale) => locale.value === userConfigValue
    )?.label,
    currencyFormat: (() => {
      const locale = context.localeList.find(
        (item) => item.code === userConfigValue
      );

      if (!locale) {
        return;
      }

      return `${locale.label} (${locale.currencyDisplay})`;
    })(),
    password: '••••••••••',
    email: context.data.user?.email,
  };

  return result[type] || '{{settingsAndPolicies.notDefined}}';
}

export function getUserDetailsDescription(context: Context) {
  if (isPWUser(context)) {
    return;
  }

  return '{{settingsAndPolicies.ssoManaged}}';
}

export function getEmployeeLink({ data }: Context) {
  if (!data.employee?.id) {
    return '@uicode:employees';
  }
  return `@uicode:employee?id=${data.employee.id}`;
}

export function isPWUser(context: Context) {
  /**
   * TODO:
   *  should return true if user is password authenticated, instead of social login, for now we return false
   *  due to lack of how to check
   * by which means the user created his account
   */
  const { ssoProvider } = context.data?.user ?? {};

  return ssoProvider === 'local';
}

export function getEditUserDetailsButtonActionsPosition(context: Context) {
  if (isPWUser(context)) {
    return 'right';
  }
}

export function isEditingUserDetails(context: Context, type: PreferenceFields) {
  return context.state.editingUserDetails === type;
}

export function isEditing(context: Context) {
  return context.state.editingUserDetails !== false;
}

export function getLanguagesList(context: Context) {
  return context.languageOptions;
}
export function getLocaleList(context: Context) {
  return context.localeList as unknown as Context['localeList'];
}

export function getUserConfigValue(context: Context, type: PreferenceFields) {
  const configValue = context.data.userConfig?.find((item) => item.key === type)
    ?.value;

  return configValue?.[type];
}

export function getLanguageDefaultValue(context: Context) {
  return context.language;
}

export function getEditUserDetailsAccountButtonActions(
  context: Context,
  type: PreferenceFields
) {
  if (!isPWUser(context)) {
    return [];
  }

  return getEditUserDetailsButtonActions(context, type);
}

export function isInsideSandbox(context: Context) {
  return context.sandbox.enabled;
}

export function isCreateTokenDisabled(context: Context) {
  return !isInsideSandbox(context);
}

export function getCreateTokenDisabledTooltip(context: Context) {
  const createTokenDisabled = isCreateTokenDisabled(context);

  if (createTokenDisabled) {
    return '{{accessToken.message.disabled}}';
  }

  return;
}

export function getLanguageLabel(context: Context, value: string) {
  return context.languageOptions.find((item) => item.value === value)?.label;
}

function getTokenExpirationDetails(context: Context, expireAt: Date) {
  const isTokenExpired =
    context.helpers.dates.compareAsc(new Date(), expireAt) === 1;

  const timezone = new Intl.DateTimeFormat().resolvedOptions().timeZone;

  const formattedDate = context.helpers.parseValue({
    parseAs: 'date',
    value: expireAt,
    format: 'MMM d, yyyy h:mm a zzz',
    timezone,
  } as unknown as UIExecutionContext.ParseValueProps);
  if (!isTokenExpired) {
    return {
      description: replaceInStringTemplate(`{{user.accessToken.expires}}`, {
        DATE: `${formattedDate}`,
      }),
    };
  }

  const expiredString = replaceInStringTemplate(
    `{{user.accessToken.expiredWithDate}}`,
    { DATE: `${formattedDate}` }
  );
  return {
    description: expiredString,
    tag: {
      text: `{{user.accessToken.expired}}`,
      color: 'disabled',
      tooltip: {
        text: '{{user.accessToken.tag.Expired}}',
      },
    },
  };
}

export function getTokenLines(context: Context) {
  return context.data?.tokenManagement?.map((token) => {
    const usableStatuses = new Set([
      AccessTokenStatus.Value.Ready,
      AccessTokenStatus.Value.ExpiringSoon,
      // This technically isn't a usable status but there's no good way to check permissions in bulk.
      // For simplicity we treat this as usable but add an * to the Usable tag and include permission info in the tooltip.
      AccessTokenStatus.Value.NoPermissionsAssigned,
    ]);
    const statuses = AccessTokenStatus.getValues(
      token as unknown as AccessTokenStatus.TokenData
    );
    const tokenUsable = statuses.every((status) => usableStatuses.has(status));
    const expiringSoon = statuses.includes(
      AccessTokenStatus.Value.ExpiringSoon
    );
    return {
      label: {
        text: `{%%${token.name}%%}`,
        onClick: () => {
          context.helpers.navigate({
            to: `/templates/everest.base.ui/userManagement/personalTokenDetails/personalTokenDetails`,
            queryParams: {
              id: token.id,
              mode: 'view',
            },
          });
        },
      },
      value: token.tokenId,
      // If the token is expired, this tag will be overwritten by the below

      tag: {
        text: tokenUsable
          ? expiringSoon
            ? '{{user.accessToken.expiringSoon}}'
            : '{{user.accessToken.usable}}'
          : '{{user.accessToken.actionNeeded}}',
        color: tokenUsable
          ? expiringSoon
            ? 'havelock-blue'
            : 'success'
          : 'warning',
        tooltip: {
          text: tokenUsable
            ? expiringSoon
              ? '{{user.accessToken.tag.ExpiringSoon}}'
              : '{{user.accessToken.tag.Usable}}'
            : '{{user.accessToken.tag.ActionNeeded}}',
        },
      },
      ...getTokenExpirationDetails(context, new Date(`${token.expireAt}`)),
      buttonActions: [
        {
          variant: 'icon-primary',
          icon: 'trash',
          onClick: async ({ rowData }) => {
            context.helpers.openDialog({
              variant: 'warning',
              title: '{{user.accessToken.message.confirmRevokeToken}}',
              onConfirm: async () => {
                await context.actions.submit({
                  transform: () => ({
                    tokenManagement: {
                      revokePersonalToken: {
                        tokenId: rowData.value,
                      },
                    },
                  }),
                  loadingMessage: '{{user.accessToken.message.revoking}}',
                  successMessage: '{{user.accessToken.message.tokenRevoked}}',
                });
              },
            });
          },
        },
      ],
    };
  });
}

export function createToken(context: Context) {
  context.helpers.openModal({
    size: 'xsmall',
    initialState: {
      type: 'PAT',
    },
    template: '/templates/everest.base.ui/accessToken/newUI/createToken',
    onModalSubmit: async () => {
      await context.actions.refetchUiModelData();
    },
  });
}

export function getDisabledTooltip(context: Context) {
  if (!isInsideSandbox(context)) {
    return;
  }

  return '{{settingsAndPolicies.userPrefencesCannotBeChanged}}';
}

export function getPreferencesModalData(
  context: Context,
  type: PreferenceFields
): Partial<UIExecutionContext.ModalConfig> {
  const preferencesModalData: Record<
    PreferenceFields,
    Partial<UIExecutionContext.ModalConfig>
  > = {
    language: {
      title: '{{settingsAndPolicies.userSettings.preferences.language}}',
      initialState: {
        type,
        selectedOption: context.language,
      },
    },
    dateFormat: {
      title: '{{settingsAndPolicies.userSettings.preferences.dateFormat}}',
      initialState: {
        type,
        selectedOption: getUserConfigValue(context, type),
      },
    },
    timeFormat: {
      title: '{{settingsAndPolicies.userSettings.preferences.timeFormat}}',
      initialState: {
        type,
        selectedOption: getUserConfigValue(context, type),
      },
    },
    currencyFormat: {
      title: '{{settingsAndPolicies.userSettings.preferences.currencyFormat}}',
      initialState: {
        type,
        selectedOption: context.language,
      },
    },
    email: {
      title: '{{settingsAndPolicies.email}}',
      initialState: {
        type,
        selectedOption: context.language,
      },
    },
    password: {
      title: '{{settingsAndPolicies.password}}',
      initialState: {
        type,
        selectedOption: context.language,
      },
    },
  };

  return preferencesModalData[type] || {};
}

export function getEditUserDetailsButtonActions(
  context: Context,
  type: PreferenceFields
) {
  const { helpers, actions } = context;

  return [
    {
      tooltip: type !== 'language' && getDisabledTooltip(context),
      disabled: isInsideSandbox(context) && type !== 'language',
      label: '{{settingsAndPolicies.edit}}',
      onClick: () => {
        helpers.openModal({
          ...getPreferencesModalData(context, type),
          template: '/everest.base/user/uinext/preferencesModal',
          size: 'small',
          onClose: async () => {
            await actions.refetchUiModelData();
          },
        });
      },
    },
  ];
}

export function getEmployeeLabel({ data }: Context) {
  if (!data.employee?.employeeNumber) {
    return '{{settingsAndPolicies.noEmployee}}';
  }
  return `${data.employee.employeeNumber}: ${data.employee.displayName}`;
}

export function navigateToEmployeePage(context: Context) {
  const { helpers } = context;
  return helpers.navigate({ to: getEmployeeLink(context) });
}

export function openLink(context: Context, link: string, initialState: Any) {
  const { helpers } = context;

  helpers.navigate({
    to: link,
    initialState,
  });
}

export function onInvoiceConfigurationClick(context: Context) {
  openLink(
    context,
    '/templates/everest.fin.accounting/qtc/invoiceConfiguration/uinext/settings?feat-delta=true',
    {
      entityId: getCurrentEntity(context),
    }
  );
}

export function onInvoiceEmailClick(context: Context) {
  const { helpers } = context;
  helpers.openModal({
    template:
      '/templates/everest.fin.accounting/qtc/invoiceConfiguration/uinext/invoiceEmailSetting',
    size: 'xsmall',
  });
}

export function onInvoicePoliciesClick(context: Context) {
  openLink(
    context,
    '/templates/everest.fin.accounting/qtc/invoicePolicy/uinext/invoicePolicies',
    {
      entityId: getCurrentEntity(context),
    }
  );
}

export function onInvoiceTemplateClick(context: Context) {
  openLink(
    context,
    '/templates/everest.fin.accounting/qtc/invoiceTemplate/uinext/invoiceTemplates?feat-delta=true',
    {
      entityId: getCurrentEntity(context),
    }
  );
}

export function onManageTaxEngineClick(context: Context) {
  const { helpers } = context;

  helpers.openModal({
    template: '/templates/everest.fin.tax/uinext/taxEngineSetting',
    size: 'xsmall',
    initialState: {
      entityId: getCurrentEntity(context),
    },
  });
}

export function onCreditMemoConfigurationClick(context: Context) {
  const { helpers } = context;

  helpers.openModal({
    template:
      '/templates/everest.fin.accounting/qtc/creditMemo/uinext/creditMemoConfiguration',
    size: 'medium',
    initialState: {
      entityId: getCurrentEntity(context),
    },
  });
}

export function onPaymentSettingsClick(context: Context) {
  const { helpers } = context;

  helpers.openModal({
    template:
      '/templates/everest.fin.accounting/qtc/paymentAccounts/uinext/paymentConfiguration',
    size: 'medium',
    initialState: {
      entityId: getCurrentEntity(context),
    },
  });
}

export function onProductAssignementClick(context: Context) {
  openLink(
    context,
    '/templates/everest.fin.accounting/qtc/revenue/settings/uinext/revenueConfiguration',
    { activeSegment: 0 }
  );
}

export function goToRevenueSettings(context: Context) {
  openLink(
    context,
    '/templates/everest.fin.accounting/qtc/revenue/settings/uinext/revenueConfiguration',
    { activeSegment: 3 }
  );
}

export function onSSPRangesPercentageClick(context: Context) {
  openLink(
    context,
    '/templates/everest.fin.accounting/qtc/revenue/sspRange/uinext/sspRanges',
    { activeSegment: 0 }
  );
}

export function onSSPRangesAmountClick(context: Context) {
  openLink(
    context,
    '/templates/everest.fin.accounting/qtc/revenue/sspRange/uinext/sspRanges',
    { activeSegment: 1 }
  );
}

export function onAmortizationMethodsClick(context: Context) {
  openLink(
    context,
    '/templates/everest.fin.accounting/qtc/revenue/settings/uinext/revenueConfiguration',
    { activeSegment: 2 }
  );
}

export function onRevenueSchedulingTemplatesClick(context: Context) {
  openLink(
    context,
    '/templates/everest.fin.accounting/qtc/revenue/settings/uinext/revenueConfiguration',
    { activeSegment: 2 }
  );
}

export function getChartOfAccountsQuery(context: Context) {
  return QueryChecks.canQueryChartOfAccounts(context)
    ? { where: {} }
    : undefined;
}

export function getBusinessUnitsQuery(context: Context) {
  return QueryChecks.canQueryBusinessUnit(context)
    ? { where: {}, orderBy: ['name'] }
    : undefined;
}

export function getNonLegalReportingEntityQuery(context: Context) {
  return QueryChecks.canQueryEntities(context)
    ? {
        where: { entityType: EvstEntityType.NonLegalReportingEntity },
      }
    : undefined;
}

export function getBankFeeSettingMenuLines(
  context: Context,
  settingType: string
) {
  const { data } = context;
  const { chartOfAccounts = [] } = data ?? {};

  return chartOfAccounts
    .filter((coa) => coa.entityNames?.length > 0)
    .map((coa) => {
      return {
        label: coa.name,
        description: `Used by ${coa.entityNames}`,
        buttonActions: [
          {
            label: '{{settingsAndPolicies.manage}}',
            onClick: () =>
              configureAccountOrDepartmentSetting(context, settingType, coa),
          },
        ],
      };
    });
}

export function getOffsetAccountSettingMenuLines(
  context: Context,
  accountProcessKey: string
) {
  const { data } = context;
  const { chartOfAccounts = [], entityCoAs = [] } = data ?? {};
  const selectedEntityId = getCurrentEntity(context);
  const entityCoa =
    entityCoAs.find((e) => e.entityId === selectedEntityId) ?? {};

  return chartOfAccounts
    .filter((coa) => coa.id === entityCoa.chartOfAccountsId)
    .map((coa) => {
      return {
        label: coa.name,
        description: `Used by ${coa.entityNames}`,
        buttonActions: [
          {
            label: '{{settingsAndPolicies.manage}}',
            onClick: () =>
              configureExpenseAccountSetting(context, coa, accountProcessKey),
          },
        ],
      };
    });
}

export function isPaymentOffsetAccountSettingVisible(context: Context) {
  return getOutboundPaymentSettingsValue(context);
}

export function openManageConnectionsModal({ helpers }: Context) {
  helpers.openModal({
    template:
      '/templates/everest.fin.integration.bank/uinext/manageConnections',
    size: 'xsmall',
    initialState: {
      fromSettingsPage: true,
    },
    onModalSubmit: () => {
      helpers.showNotificationMessage({
        key: 'rules',
        message: '{{settingsAndPolicies.bankFee.settingSaved.message}}',
        duration: 5,
      });
    },
  });
}

export function openMatchingRulesModal({ helpers }: Context) {
  helpers.openModal({
    template:
      '/templates/everest.fin.integration.bank/uinext/settings/configureMatchingRules?feat-delta=true',
    size: 'xsmall',
    initialState: {},
    onModalSubmit: () => {
      helpers.showNotificationMessage({
        key: 'rules',
        message: '{{settingsAndPolicies.bankFee.settingSaved.message}}',
        duration: 5,
      });
    },
  });
}

export function configureAccountOrDepartmentSetting(
  context: Context,
  settingType: string,
  coa: { id: number; name: string }
) {
  const { helpers } = context;
  const title =
    settingType === 'accountSetting'
      ? `Bank Fee Account - ${coa.name}`
      : `Bank Fee Department - ${coa.name}`;
  helpers.openModal({
    title,
    template:
      '/templates/everest.fin.integration.bank/uinext/fundTransfer/configureBankFeeSettingsModal',
    size: 'xsmall',
    initialState: {
      coaId: coa.id,
      settingType,
      entityId: getCurrentEntity(context),
    },
    onModalSubmit: () => {
      helpers.showNotificationMessage({
        key: 'upsertJE',
        message: '{{settingsAndPolicies.bankFee.settingSaved.message}}',
        duration: 5,
      });
    },
  });
}

export function configureExpenseAccountSetting(
  context: Context,
  coa: { id: number; name: string },
  accountProcessKey: string
) {
  const { helpers } = context;
  const title = '{{settingsAndPolicies.procureToPay.offsetAccount.title}}';

  helpers.openModal({
    title,
    template:
      '/templates/everest.fin.expense/uinext/settingsAndPolicies/configureOffsetAccountsModal',
    size: 'xsmall',
    initialState: {
      chartOfAccountsId: coa.id,
      accountProcessKey,
    },
    onModalSubmit: () => {
      helpers.showNotificationMessage({
        key: 'upsertAccounts',
        message: '{{settingsAndPolicies.bankFee.settingSaved.message}}',
        duration: 5,
      });
    },
  });
}

export function getVendorSettingsQuery(context: Context) {
  return ProcureToPayGroup.vendorSettingVisible(context)
    ? { where: { key: 'unique' } }
    : undefined;
}

export function getOutboundPaymentSettingsQuery(context: Context) {
  return ProcureToPayGroup.vendorSettingVisible(context)
    ? { where: { key: 'showAdditionalOutboundPaymentSettings' } }
    : undefined;
}

export function getVendorUniqueness({ data }: Context) {
  return data?.vendorSettings?.value === 'true';
}

export function getOutboundPaymentSettingsValue({ data }: Context) {
  return data?.outboundPaymentSettings?.value === 'true';
}

export function getArrSettingQuery(context: Context) {
  return QuoteToCashGroup.arrSettingsVisible(context)
    ? {
        where: {
          entityId: getCurrentEntity(context),
        },
      }
    : undefined;
}

export function getArrSetting({ data }: Context) {
  return Boolean(data?.arrSetting?.id);
}

export function isArrSettingsDisabled({
  isFetchingUiModelData,
  form: {
    formState: { isSubmitting },
  },
  sharedState,
}: Context) {
  return (
    isFetchingUiModelData ||
    isSubmitting ||
    sharedState.isLoadingData('arrSetting') ||
    isArrSettingSaveInProgress
  );
}

export async function setArrSetting(context: Context, newValue: boolean) {
  const { actions } = context;

  isArrSettingSaveInProgress = true;

  const action = Boolean(newValue);
  await (action
    ? actions.run({
        arrSetting: {
          action: 'upsert',
          data: {
            entityId: getCurrentEntity(context),
          },
        },
      })
    : actions.run({
        arrSetting: {
          action: 'delete',
          where: {
            id: context.data?.arrSetting?.id,
          },
        },
      }));

  await actions.refetchUiModelData({
    nodesToLoad: ['arrSetting'],
  });

  isArrSettingSaveInProgress = false;
}

export async function setVendorUniqueness(context: Context, newValue: boolean) {
  const { actions } = context;
  const value = String(newValue);
  await actions.run({
    vendorSettings: {
      action: 'upsert',
      data: {
        where: {
          key: 'unique',
        },
        data: {
          key: 'unique',
          value,
        },
      },
    },
  });
  await actions.refetchUiModelData({
    nodesToLoad: ['vendorSettings'],
  });
}

export async function navigateToPerDiemPolicies(context: Context) {
  openLink(
    context,
    '/templates/everest.fin.expense/uinext/expenseMgmt/perDiemMgmt/perDiemPolicies',
    {}
  );
}

export async function setOutboundPaymentSettings(
  context: Context,
  newValue: boolean
) {
  const { actions } = context;
  const value = String(newValue);
  await actions.run({
    outboundPaymentSettings: {
      action: 'upsert',
      data: {
        where: {
          key: 'showAdditionalOutboundPaymentSettings',
        },
        data: {
          key: 'showAdditionalOutboundPaymentSettings',
          value,
        },
      },
    },
  });
  await actions.refetchUiModelData({
    nodesToLoad: ['outboundPaymentSettings'],
  });
}

export function onClickVatBookingAccountConfiguration(context: Context) {
  openLink(context, '/templates/everest.fin.expense/uinext/VAT/settings', {
    entityId: getCurrentEntity(context),
  });
}

export function onClickAmortizationConfiguration(context: Context) {
  openLink(
    context,
    '/templates/everest.fin.accounting/uinext/amortization/amortizationConfig',
    {
      entityId: getCurrentEntity(context),
    }
  );
}

export function onClickBusinessUnitCreate(context: Context) {
  const { helpers } = context;
  helpers.openModal({
    template: '/templates/everest.base/uinext/businessUnitUpsertModal',
    size: 'xsmall',
    onClose: async () => {
      await refetchBusinessUnitData(context);
    },
  });
}

export function onClickBusinessUnitUpdate(context: Context, id) {
  const { helpers } = context;

  return () => {
    helpers.openModal({
      template: '/templates/everest.base/uinext/businessUnitUpsertModal',
      size: 'xsmall',
      initialState: {
        id,
      },
      onClose: async () => {
        await refetchBusinessUnitData(context);
      },
    });
  };
}

export function onClickBusinessUnitDelete(context: Context, id: number) {
  const { helpers } = context;

  return async () => {
    helpers.showNotificationMessage({
      type: 'loading',
      message: '{{businessUnit.deleteLoading}}',
      key: 'deletingBusinessUnit',
    });
    const result = await BusinessUnitUI.deleteMany(context, {
      id,
    }).run('businessUnits');

    if ('error' in (result ?? {})) {
      helpers.closeNotificationMessage('deletingBusinessUnit');
      return;
    }

    helpers.showNotificationMessage({
      type: 'success',
      message: '{{businessUnit.deleteSuccess}}',
      key: 'deletingBusinessUnit',
    });

    await refetchBusinessUnitData(context);
  };
}

async function refetchBusinessUnitData(context: Context) {
  const { actions } = context;
  await actions.refetchUiModelData({
    nodesToLoad: ['businessUnits'],
  });
}

export function onEnablePaymentsClick(context: Context) {
  const { helpers } = context;

  helpers.openModal({
    template:
      '/templates/everest.fin.integration.bank.astra/uinext/enablePayments',
    size: 'large',
    initialState: {
      entityId: getCurrentEntity(context),
    },
    onModalSubmit: () => {
      helpers.showNotificationMessage({
        key: 'enablePayments',
        message: '{{settingsAndPolicies.procureToPay.payments.enabledMessage}}',
        duration: 5,
      });
    },
  });
}

export function onConfigurePaymentProviderClick(context: Context) {
  const { helpers } = context;

  helpers.openModal({
    template:
      '/templates/everest.fin.integration.bank.astra/uinext/configurePaymentProvider',
    size: 'xsmall',
  });
}

export function onClickPayrollTemplate(context: Context) {
  openLink(
    context,
    '/templates/everest.fin.expense/uinext/payrollMgmt/payrollTemplates',
    {}
  );
}

export function onClickBoardingTemplate(context: Context) {
  openLink(
    context,
    '/templates/everest.hr.base/uinext/boarding/boardingTemplateList',
    {}
  );
}

export function onClickTimeTrackingPolicies(context: Context) {
  openLink(
    context,
    '/templates/everest.hr.base/uinext/timesheet/policy/list',
    {}
  );
}

export function onClickAbsencePolicies(context: Context) {
  openLink(
    context,
    '/templates/everest.hr.base/uinext/absencePolicy/absencePolicies',
    {}
  );
}
export function onClickHolidayCalendar(context: Context) {
  openLink(
    context,
    '/templates/everest.hr.base/uinext/holidayCalendar/holidayCalendars',
    {}
  );
}
export function onClickApplicantStageTemplate(context: Context) {
  openLink(
    context,
    '/templates/everest.hr.base/uinext/recruitment/templateTypes',
    {}
  );
}
export function onClickSetEmail(context: Context) {
  const { helpers } = context;
  helpers.openModal({
    title: '{{settingsAndPolicies.people.recruiting.recruitementEmail.title}}',
    template: '/templates/everest.hr.base/uinext/applicant/setEmailSource',
    size: 'xsmall',
  });
}

export namespace DataEncryptionSetting {
  export function onDataEncryptionConfigClick(_context: Context) {
    console.log('onDataEncryptionConfigClick');
  }
  export function onConsentClick(context: Context) {
    openLink(
      context,
      '/templates/everest.base.dataprotection/uinext/consent/list',
      {}
    );
  }
  export function onConsentAgreementClick(context: Context) {
    openLink(
      context,
      '/templates/everest.base.dataprotection/uinext/agreement/list',
      {}
    );
  }
  export function onDataConfigClick(context: Context) {
    openLink(
      context,
      '/templates/everest.base.dataprotection/uinext/dataConfig/list',
      {}
    );
  }
  export function onPurposeClick(context: Context) {
    openLink(
      context,
      '/templates/everest.base.dataprotection/uinext/purpose/list',
      {}
    );
  }
  export function onActivateConfigurationClick(context: Context) {
    const { helpers } = context;
    helpers.openModal({
      template:
        '/templates/everest.base.dataprotection/uinext/activateConfiguration',
      onModalSubmit() {
        helpers.closeModal();
      },
    });
  }
  export function onExpiredListClick(context: Context) {
    openLink(
      context,
      '/templates/everest.base.dataprotection/uinext/agreement/expiredList',
      {}
    );
  }
}

// EVE - General Model Configuration
export function onGeneralModelConfigurationClick(context: Context) {
  const { helpers } = context;

  helpers.openModal({
    template: '/templates/everest.base.ai/uinext/config/generalModel',
    size: 'xsmall',
  });
}

// EVE - CustomEve Model Configuration
export function onCustomEveModelConfigurationClick(context: Context) {
  const { helpers } = context;

  helpers.openModal({
    template: '/templates/everest.eve/uinext/config/customEveModel',
    size: 'xsmall',
  });
}

// EVE - CustomEve Model Fallback Configuration
export function onCustomEveModelFallbackConfigurationClick(context: Context) {
  const { helpers } = context;

  helpers.openModal({
    template: '/templates/everest.eve/uinext/config/customEveFallbackModel',
    size: 'xsmall',
  });
}

// EVE - Custom Eve Allow Openai Access
export function onCustomEveCodeInterpreterModelConfigurationClick(
  context: Context
) {
  const { helpers } = context;

  helpers.openModal({
    template:
      '/templates/everest.eve/uinext/config/customEveCodeInterpreterModel',
    size: 'xsmall',
  });
}

// EVE - Chat Model Configuration
export function onChatModelConfigurationClick(context: Context) {
  const { helpers } = context;

  helpers.openModal({
    template: '/templates/everest.eve/uinext/config/chatModel',
    size: 'xsmall',
  });
}

// EVE - Chat Configuration
export function onChatConfigurationClick(context: Context) {
  const { helpers } = context;

  helpers.openModal({
    template: '/templates/everest.eve/uinext/config/chat',
    size: 'xsmall',
  });
}

// EVE - Custom Eve Prompt Improvement Model
export function onPromptImprovementModelConfigurationClick(context: Context) {
  const { helpers } = context;

  helpers.openModal({
    template: '/templates/everest.eve/uinext/config/promptImprovementModel',
    size: 'xsmall',
  });
}

// EVE - Custom Eve Number of Attempts
export function onCustomEveAttemptsClick(context: Context) {
  const { helpers } = context;

  helpers.openModal({
    template: '/templates/everest.eve/uinext/config/customEveAttempts',
    size: 'xsmall',
  });
}

// EVE - OCR Model Configuration
export function onOCRModelConfigurationClick(context: Context) {
  const { helpers } = context;

  helpers.openModal({
    template: '/templates/everest.eve/uinext/config/ocrModel',
    size: 'xsmall',
  });
}

// EVE - OCR Model Configuration
export function onOCROverrideConfigurationClick(context: Context) {
  const { helpers } = context;

  helpers.openModal({
    template: '/templates/everest.eve/uinext/config/ocrOverride',
    size: 'xsmall',
  });
}

// EVE - OCR Vision Configuration
export function onOCRVisionConfigurationClick(context: Context) {
  const { helpers } = context;

  helpers.openModal({
    template: '/templates/everest.eve/uinext/config/ocrVision',
    size: 'xsmall',
  });
}

// EVE - Malicoius validation Configuration
export function onMaliciousEveValidatorConfigurationClick(context: Context) {
  const { helpers } = context;

  helpers.openModal({
    template: '/templates/everest.eve/uinext/config/customEveValidation',
    size: 'xsmall',
  });
}

export function getPostAllocations(context: Context) {
  const { data, helpers, actions } = context;
  const {
    entityCoAs = [],
    postAllocationAccounts = [],
    entities = [],
  } = data ?? {};
  return entities.map((e) => {
    const postAllocation: SingleNodeItemType['postAllocationAccounts'] =
      postAllocationAccounts.find((poa) => poa.entityId === e.id);
    return {
      label: e.entityName,
      description: postAllocation
        ? `${postAllocation['PostAllocationAccount-AllocationIn']['accountNumber']} - {{settingsAndPolicies.recordToReport.allocation.postAllocation.allocationIn}}, ${postAllocation['PostAllocationAccount-AllocationOut']['accountNumber']} - {{settingsAndPolicies.recordToReport.allocation.postAllocation.allocationOut}}`
        : '{{settingsAndPolicies.recordToReport.allocation.postAllocation.sourceAccount}}',
      buttonActions: [
        {
          label: '{{settingsAndPolicies.edit}}',
          onClick: () => {
            helpers.openModal({
              title:
                `{{settingsAndPolicies.recordToReport.allocation.postAllocationTo}}` +
                ` - ${e.entityName}`,
              template:
                '/templates/everest.fin.accounting/r2r/allocation/uinext/postAllocationAccount',
              size: 'xsmall',
              onModalSubmit: async () => {
                await actions.refetchUiModelData({
                  nodesToLoad: ['postAllocationAccounts'],
                });
              },
              initialState: {
                entityId: e.id,
                id: postAllocation?.id,
                chartOfAccountsId: entityCoAs.find(
                  (coa) => coa.entityId === e.id
                )?.chartOfAccountsId,
              },
            });
          },
        },
      ],
    };
  });
}

export function getBusinessUnits(context: Context) {
  const { data } = context;

  if (!data?.businessUnits) {
    return [];
  }

  return data.businessUnits.map(({ id, name }) => ({
    label: name,
    buttonActions: [
      {
        shape: 'pill',
        label: '{{settingsAndPolicies.actions}}',
        actions: [
          {
            label: '{{settingsAndPolicies.rename}}',
            disabled: RecordToReportGroup.businessUnitRenameDisabled(context),
            onClick: onClickBusinessUnitUpdate(context, id),
          },
          {
            label: '{{settingsAndPolicies.delete}}',
            disabled: RecordToReportGroup.businessUnitDeleteDisabled(context),
            onClick: onClickBusinessUnitDelete(context, id),
          },
        ],
      },
    ],
  }));
}

export function getAllocationPolicies(context: Context) {
  const { data } = context;
  const { chartOfAccounts = [] } = data ?? {};
  return chartOfAccounts.map((coa) => {
    return {
      label: coa.name,
      result: {
        variant: 'link',
        content: coa['AllocationPolicy-ChartOfAccounts']?.map((ap) => ({
          label: ap.policyName,
          onClick: () =>
            openAllocationPolicyWith(
              context,
              `{{settingsAndPolicies.recordToReport.allocation.allocationPolicy}} - ${ap.policyName}`,
              coa.id,
              { id: ap.id }
            ),
        })),
      },
      buttonActions: [
        {
          label: '{{settingsAndPolicies.create}}',
          onClick: () =>
            openAllocationPolicyWith(
              context,
              `{{settingsAndPolicies.recordToReport.allocation.createAllocationPolicy}}`,
              coa?.id
            ),
        },
      ],
    };
  });
}

function openAllocationPolicyWith(
  context: Context,
  title: string,
  chartOfAccountsId: number,
  optionalState: Record<string, unknown> = {}
) {
  const { helpers, actions } = context;
  helpers.openModal({
    title,
    template:
      '/templates/everest.fin.accounting/r2r/allocation/uinext/allocationPolicy',
    size: 'xsmall',
    onModalSubmit: async () => {
      await actions.refetchUiModelData({
        nodesToLoad: ['chartOfAccounts'],
      });
    },
    initialState: {
      chartOfAccountsId,
      ...optionalState,
    },
  });
}

export function getEntityBookQuery(context: Context) {
  const { data } = context;
  if (
    QueryChecks.canQueryEntityBook(context) &&
    data?.entities &&
    data?.entities?.length > 0
  ) {
    return {
      where: {
        entityId: { $in: data?.entities?.map((e) => e.id) },
      },
    };
  }
}

export function getPostAllocationAccountQuery(context: Context) {
  const { data } = context;
  if (
    RecordToReportGroup.canQueryPostAllocationAccount(context) &&
    data?.entities &&
    data?.entities?.length > 0
  ) {
    return {
      where: {
        entityId: { $in: data?.entities?.map((e) => e.id) },
      },
    };
  }
}

export function getDefaultEntityPreferenceSettingValueText(_context: Context) {
  return '';
}

export function openDefaultEntityPreferenceModal(context: Context) {
  const { actions, helpers } = context;

  helpers.openModal({
    title: '{{settingsAndPolicies.recordToReport.preferences.defaultEntity}}',
    template:
      '/templates/everest.base/entity/uinext/defaultEntityPreferenceSettingsModal',
    size: 'xsmall',
    onModalSubmit: async () => {
      await actions.refetchUiModelData();
    },
    onClose: async () => {
      await actions.refetchUiModelData();
    },
  });
}

export function openDefaultTransactionalBookPreferenceModal(context: Context) {
  const { actions, helpers } = context;

  helpers.openModal({
    title:
      '{{settingsAndPolicies.recordToReport.preferences.transactionalBookContext}}',
    template:
      '/templates/everest.fin.accounting/accountingBook/userPreferences/uinext/defaultTransactionalEntityBookPreferenceSettingsModal',
    size: 'xsmall',
    onModalSubmit: async () => {
      await actions.refetchUiModelData();
    },
  });
}

export function openDefaultAccountingPeriodPreferenceModal(context: Context) {
  const { actions, helpers } = context;

  helpers.openModal({
    title:
      '{{settingsAndPolicies.recordToReport.preferences.defaultAccountingPeriod}}',
    template:
      '/templates//everest.fin.accounting/uinext/common/defaultPeriodPreferencesSettingsModal',
    size: 'xsmall',
    onModalSubmit: async () => {
      await actions.refetchUiModelData();
    },
  });
}

function nonLegalReportingEntityExists(context: Context): boolean {
  const { data } = context;
  return Boolean(data.nonLegalReportingEntity?.id);
}

export function editNonLegalEntitySetting(context: Context) {
  const { actions, helpers } = context;

  helpers.openModal({
    title: '{{settingsAndPolicies.recordToReport.entities.rollUpLabel}}',
    template:
      '/templates/everest.base/entity/uinext/nonLegalReportingEntitySetting',
    size: 'xsmall',
    initialState: {
      switchEnabled: nonLegalReportingEntityExists(context),
    },
    onModalSubmit: async (result: { success: boolean } | undefined) => {
      if (!result?.success) {
        return; // Modal was canceled
      }

      helpers.showNotificationMessage({
        key: 'rollUpSetting',
        type: 'success',
        message:
          '{{settingsAndPolicies.recordToReport.entities.rollUpSuccess}}',
        duration: 1,
      });
    },
    onClose: async () => {
      await actions.refetchUiModelData();
    },
  });
}

export function getNonLegalEntitySettingValueText(context: Context) {
  return nonLegalReportingEntityExists(context)
    ? '{{settingsAndPolicies.on}}'
    : '{{settingsAndPolicies.off}}';
}

export function getAllowSalesOrderMassApprovalOptionQuery(context: Context) {
  return MigrationGroup.canQueryFeatureFlag(context)
    ? {
        where: {
          packageName: ACCOUNTING_PACKAGE_NAME,
          name: ALLOW_SALES_ORDER_MASS_APPROVAL,
        },
      }
    : undefined;
}

export function getAllowAISpecifyBuildQuery(context: Context) {
  return MigrationGroup.canQueryFeatureFlag(context)
    ? {
        where: {
          packageName: AISpecifyPackageName,
          name: ALLOW_AI_SPECIFY_BUILD,
        },
      }
    : undefined;
}

export function getAllowModifyCancelledSubscriptionOptionQuery(
  context: Context
) {
  return MigrationGroup.canQueryFeatureFlag(context)
    ? {
        where: {
          packageName: ACCOUNTING_PACKAGE_NAME,
          name: ALLOW_MODIFY_CANCELLED_SUBSCRIPTION,
        },
      }
    : undefined;
}

export function getMigrationRevenueCloseProcessReverseOptionQuery(
  context: Context
) {
  return MigrationGroup.canQueryFeatureFlag(context)
    ? {
        where: {
          packageName: ACCOUNTING_PACKAGE_NAME,
          name: REVENUE_CLOSE_PROCESS_REVERSE_JOURNAL,
        },
      }
    : undefined;
}

export function getMigrationSuppressRevenuePlanCreationOptionQuery(
  context: Context
) {
  return MigrationGroup.canQueryFeatureFlag(context)
    ? {
        where: {
          packageName: ACCOUNTING_PACKAGE_NAME,
          name: SUPPRESS_REVENUE_PLAN_CREATION,
        },
      }
    : undefined;
}

export async function getAllowSalesOrderMassApprovalOption(context: Context) {
  const { data } = context;
  return Boolean(data?.allowSalesOrderMassApprovalOption?.enabled);
}

export async function getSpecifyBuildEnabled(context: Context) {
  const { data } = context;
  return Boolean(data?.specifyBuildEnabledOption?.enabled || false);
}

export async function getAllowModifyCancelledSubscriptionOption(
  context: Context
) {
  const { data } = context;
  return Boolean(data?.allowModifyCancelledSubscriptionOption?.enabled);
}

export async function setAllowSalesOrderMassApprovalOption(
  context: Context,
  newValue: boolean
) {
  const { actions } = context;

  await actions.run({
    allowSalesOrderMassApprovalOption: {
      action: 'upsert',
      data: {
        packageName: ACCOUNTING_PACKAGE_NAME,
        name: ALLOW_SALES_ORDER_MASS_APPROVAL,
        enabled: newValue,
      },
    },
  });

  await actions.refetchUiModelData({
    nodesToLoad: ['allowSalesOrderMassApprovalOption'],
  });
}

export async function setSpecifyBuildEnabled(
  context: Context,
  newValue: boolean
) {
  const { actions } = context;

  await actions.run({
    specifyBuildEnabledOption: {
      action: 'upsert',
      data: {
        packageName: AISpecifyPackageName,
        name: ALLOW_AI_SPECIFY_BUILD,
        enabled: newValue,
      },
    },
  });

  await actions.refetchUiModelData({
    nodesToLoad: ['specifyBuildEnabledOption'],
  });
}

export async function setAllowModifyCancelledSubscriptionOption(
  context: Context,
  newValue: boolean
) {
  const { actions } = context;

  await actions.run({
    allowModifyCancelledSubscriptionOption: {
      action: 'upsert',
      data: {
        packageName: ACCOUNTING_PACKAGE_NAME,
        name: ALLOW_MODIFY_CANCELLED_SUBSCRIPTION,
        enabled: newValue,
      },
    },
  });

  await actions.refetchUiModelData({
    nodesToLoad: ['allowModifyCancelledSubscriptionOption'],
  });
}

export async function getMigrationRevenueCloseProcessReverseOption(
  context: Context
) {
  const { data } = context;
  return Boolean(data?.migrationRevenueCloseProcessReverseOption?.enabled);
}

export async function getMigrationSuppressRevenuePlanCreationOption(
  context: Context
) {
  const { data } = context;
  return Boolean(data?.migrationSuppressRevenuePlanCreationOption?.enabled);
}

export async function setMigrationRevenueCloseProcessReverseOption(
  context: Context,
  newValue: boolean
) {
  const { actions } = context;

  await actions.run({
    migrationRevenueCloseProcessReverseOption: {
      action: 'upsert',
      data: {
        packageName: ACCOUNTING_PACKAGE_NAME,
        name: REVENUE_CLOSE_PROCESS_REVERSE_JOURNAL,
        enabled: newValue,
      },
    },
  });

  await actions.refetchUiModelData({
    nodesToLoad: ['migrationRevenueCloseProcessReverseOption'],
  });
}

export async function setMigrationSuppressRevenuePlanCreationOption(
  context: Context,
  newValue: boolean
) {
  const { actions } = context;

  await actions.run({
    migrationRevenueCloseProcessReverseOption: {
      action: 'upsert',
      data: {
        packageName: ACCOUNTING_PACKAGE_NAME,
        name: SUPPRESS_REVENUE_PLAN_CREATION,
        enabled: newValue,
      },
    },
  });

  await actions.refetchUiModelData({
    nodesToLoad: ['migrationSuppressRevenuePlanCreationOption'],
  });
}

export function openUserEditDetailsModal({ helpers, actions }: Context) {
  helpers.openModal({
    template: `@uicode:userEditDetails`,
    size: 'xsmall',
    onModalSubmit: () => actions.refetchUiModelData(),
  });
}

export function openEmailSettingsModal({ helpers }: Context) {
  helpers.openModal({
    title: '{{settingsAndPolicies.userSettings.general.editUserDetails.title}}',
    template: `@uicode:emailSettingsModal`,
    size: 'xsmall',
  });
}

export function openSlackSettings({ helpers }: Context): void {
  helpers.navigate({
    to: '/templates/everest.appserver/slackIntegration/uinext/slackIntegration',
  });
}

export function openAutoRevenueCloseProcessModal({ helpers }: Context) {
  helpers.openModal({
    title: '{{settingsAndPolicies.general.autoRevenueCloseProcessModal.title}}',
    template: `@uicode:autoRevenueCloseProcess`,
    size: 'small',
  });
}

export function openSaasMetricsExpenseTypeMappingModal({ helpers }: Context) {
  helpers.openModal({
    title: '{{settingsAndPolicies.general.saasMetricsConfiguration}}',
    template: `/templates/everest.reports.saas/ui/saasMetricsConfigurationModal?mode=edit&expenseType=sales`,
    size: 'medium',
  });
}

export function openSalesOrderMetricsRecalculationModal({ helpers }: Context) {
  helpers.openModal({
    title:
      '{{settingsAndPolicies.general.saasMetrics.recalculateSalesOrderMetrics}}',
    template:
      '/templates/everest.fin.accounting/qtc/salesOrder/uinext/salesOrderMetricsRecalculationModal',
    size: 'xsmall',
  });
}

export function navigateToPDFTemplates(context: Context) {
  openLink(context, '@uicode:pdfTemplates', {});
}

export function navigateToEmailTemplates(context: Context) {
  openLink(
    context,
    '/templates/everest.base.notifications/uinext/notifications',
    {}
  );
}

export function onClickGoogleCalendarConfig(context: Context) {
  openLink(
    context,
    '/templates/everest.hr.base/uinext/calendar/configuration',
    {}
  );
}

export function onClickEmployeeTraining(context: Context) {
  openLink(
    context,
    '/templates/everest.hr.base/uinext/training/trainingList',
    {}
  );
}

export function navigateToSystemNotificationTopics(context: Context) {
  openLink(
    context,
    '/templates/everest.base.notifications/uinext/notificationTopicList',
    {}
  );
}

export function onClickEmpDocConfig(context: Context) {
  openLink(
    context,
    '/templates/everest.hr.base/uinext/employee/manageDocuments',
    {}
  );
}

// EVE - Chat Use Everest streamlit components configuration
export function onEveUseEverestStreamlitComponents(context: Context) {
  const { helpers } = context;

  helpers.openModal({
    template:
      '/templates/everest.eve/uinext/config/customEveEverestStreamlitComponents',
    size: 'xsmall',
  });
}

// Accounting
export function hasAccountingBookPermission(context: Context) {
  return RecordToReportGroup.accountingBookVisible(context);
}

export function getAccountingBooksQuery(
  context: Context
): Record<string, never> | undefined {
  return QueryChecks.canQueryBook(context) ? {} : undefined;
}

export function getDefaultTransactionBooksQuery(context: Context) {
  return QueryChecks.canGetDefaultTransactionalBookIdByEntity(context)
    ? {}
    : undefined;
}

export function getIsMultiBookEnabledQuery(context: Context) {
  return RecordToReportGroup.canIsMultiBookEnabledEntityBook(context)
    ? {}
    : undefined;
}

export function getMultibookStatus(context: Context): string {
  const { data } = context;

  return data?.isAccountingBookEnabled
    ? '{{settingsAndPolicies.recordToReport.books.actionButtonStatusTurnOff}}'
    : '{{settingsAndPolicies.recordToReport.books.actionButtonStatusTurnOn}}';
}

export function getMultibookOnOff(context: Context): string {
  const { data } = context;

  return data?.isAccountingBookEnabled
    ? '{{settingsAndPolicies.on}}'
    : '{{settingsAndPolicies.off}}';
}

export async function toggleAccountingBook(context: Context): Promise<void> {
  const { actions, data } = context;

  await actions.run({
    entityBook: {
      action: 'saveAccountingBookConfiguration',
      data: {
        enable: data?.isAccountingBookEnabled ? false : true,
      },
    },
  });

  await actions.refetchUiModelData({
    nodesToLoad: ['isAccountingBookEnabled'],
  });
}

export function canShowAccountingBookList(context: Context): boolean {
  const { data } = context;
  if (!RecordToReportGroup.accountingBookVisible(context)) {
    return false;
  }

  if (!data?.isAccountingBookEnabled) {
    return false;
  }

  return true;
}

export async function getAccountingBooks(
  context: Context
): Promise<Record<string, unknown>[]> {
  const { data } = context;

  if (isEmpty(data.accountingBooks)) {
    return [];
  }

  const bookTypesMap = new Map<string, string>();
  for (const bookType of data.bookTypes ?? []) {
    bookTypesMap.set(bookType.codeValue, bookType.text);
  }

  return data.accountingBooks.map((book) => {
    const entities = getFormattedEntities(book);
    const description = createBookDescription(book, bookTypesMap);

    return {
      label: book.name,
      result: entities,
      description,
      buttonActions: [
        {
          label: '{{settingsAndPolicies.actions}}',
          actions: [
            createViewButton(context, book),
            createEditButton(context, book),
            createDeleteButton(context, book),
          ],
        },
      ],
    };
  });
}

export function isMultiBookStatusButtonDisabled(context: Context) {
  const { data } = context;

  if (isEmpty(data.accountingBooks)) {
    return true;
  }
  return data.accountingBooks.length > 1;
}

export function getMultiBookStatusButtonDisabledTooltip(context: Context) {
  return (
    isMultiBookStatusButtonDisabled(context) &&
    '{{settingsAndPolicies.recordToReport.books.multiBookStatusButtonDisabledTooltip}}'
  );
}

function getFormattedEntities(book) {
  if (book.allEntities) {
    return '{{settingsAndPolicies.recordToReport.books.allEntities}}';
  }

  if (isEmpty(book.EntityBook)) {
    return '';
  }

  return book.EntityBook.map(({ Entity: entity }) => entity?.entityName).join(
    ', '
  );
}

function createBookDescription(book, bookTypesMap) {
  const bookType = bookTypesMap.get(book.type);
  return book.description ? `${bookType}, ${book.description}` : bookType;
}

function createEditButton(context, book) {
  return {
    label: '{{settingsAndPolicies.edit}}',
    shape: 'pill',
    onClick: () => openModalAccountingBookEdit(context, book),
  };
}

function createDeleteButton(context, book) {
  const hasTransactionCreatedByCurentBook = book.EntityBook.some(
    ({ hasTransactionCreated }) => hasTransactionCreated
  );
  return {
    label: '{{settingsAndPolicies.delete}}',
    shape: 'pill',
    tooltip:
      hasTransactionCreatedByCurentBook &&
      '{{settingsAndPolicies.recordToReport.books.deleteBtnDisabled}}',
    disabled: hasTransactionCreatedByCurentBook,
    onClick: async () => deleteAccountingBook(context, book.id),
  };
}

async function deleteAccountingBook(context: Context, bookId: number) {
  const { actions, helpers } = context;

  helpers.showNotificationMessage({
    key: 'loading',
    type: 'loading',
    message: '{{settingsAndPolicies.recordToReport.books.deletingBook}}',
    duration: 0,
  });

  try {
    await actions.run({
      accountingBooks: {
        action: 'deleteAccountingBook',
        data: {
          bookId,
        },
      },
    });

    helpers.showNotificationMessage({
      key: 'loading',
      type: 'success',
      message:
        '{{settingsAndPolicies.recordToReport.books.deletingBookSuccess}}',
      duration: 2,
    });
  } catch {
    helpers.showNotificationMessage({
      key: 'loading',
      type: 'error',
      message: '{{settingsAndPolicies.recordToReport.books.deletingBookError}}',
      duration: 2,
    });
  }

  await actions.refetchUiModelData({
    nodesToLoad: ['accountingBooks', 'entityCoAs'],
  });
}

function createViewButton(context, book) {
  return {
    label: '{{settingsAndPolicies.viewDetails}}',
    shape: 'pill',
    onClick: () => openModalAccountingBookEdit(context, book, true),
  };
}

export function openModalAccountingBookEdit(
  context: Context,
  book: Record<string, unknown>,
  viewMode?: boolean
): void {
  const { actions, helpers } = context;

  helpers.openModal({
    title: `${book.name}`,
    template:
      '/templates/everest.fin.accounting/accountingBook/uinext/bookModal',
    size: 'xsmall',
    initialState: {
      bookId: book.id,
      viewMode,
    },
    onClose: async () => {
      await actions.refetchUiModelData({
        nodesToLoad: ['accountingBooks', 'entityCoAs'],
      });
    },
  });
}
export function getAccountingBookMappings(
  context: Context
): BookMappingLineReturn {
  const { data, helpers } = context;

  const mappingsMap = new Map<
    string,
    {
      coaName1: string;
      coaName2: string;
      entityIds: Set<number>;
      bookNames: Set<string>;
      source: BookMapping;
      target: BookMapping;
    }
  >();

  const grouped = groupBy(data.entityCoAs, 'entityId');
  if (isEmpty(grouped)) {
    return [];
  }

  for (const coaList of Object.values(grouped)) {
    const uniqueCoas = uniqBy(coaList, 'chartOfAccountsId').sort(
      (x, y) => x.chartOfAccountsId - y.chartOfAccountsId
    );

    for (let i = 0; i < uniqueCoas.length - 1; i++) {
      for (let j = i + 1; j < uniqueCoas.length; j++) {
        const a = uniqueCoas[i];
        const b = uniqueCoas[j];

        const key = `${a.chartOfAccountsId}-${b.chartOfAccountsId}`;

        if (!mappingsMap.has(key)) {
          mappingsMap.set(key, {
            coaName1: a.ChartOfAccounts.name,
            coaName2: b.ChartOfAccounts.name,
            entityIds: new Set<number>(),
            bookNames: new Set<string>(),
            source: {
              bookId: a.bookId,
              bookName: a.Book.name,
              coaId: a.chartOfAccountsId,
              coaName: a.ChartOfAccounts.name,
              entityId: a.entityId,
            },
            target: {
              bookId: b.bookId,
              bookName: b.Book.name,
              coaId: b.chartOfAccountsId,
              coaName: b.ChartOfAccounts.name,
              entityId: b.entityId,
            },
          });
        }

        const record = mappingsMap.get(key);
        record.entityIds.add(a.entityId);
        record.bookNames.add(a.Book.name);
        record.bookNames.add(b.Book.name);
      }
    }
  }

  const mappings: BookMappingLineReturn = [];
  for (const [, value] of mappingsMap) {
    const description = helpers.template('{{settingsAndPolicies.bookNames}}', {
      bookNames: [...value.bookNames].join(', '),
    });
    mappings.push({
      label: `${value.coaName1} ↔ ${value.coaName2}`,
      description,
      buttonActions: [
        {
          label: '{{settingsAndPolicies.edit}}',
          shape: 'pill',
          onClick: () =>
            openModalBookMappings(context, {
              source: value.source,
              target: value.target,
              bookNames: value.bookNames,
            }),
        },
      ],
    });
  }

  return mappings;
}
export function canShowBookAccountMappingList(context: Context) {
  const mappings = getAccountingBookMappings(context);
  return !isEmpty(mappings);
}
export function openModalBookMappings(
  context: Context,
  { source, target, bookNames }
): void {
  const { actions, helpers } = context;

  const modalTitle = helpers.template(
    '{{settingsAndPolicies.bookMappingsCoa}}',
    {
      sourceCoaName: source.coaName,
      targetCoaName: target.coaName,
    }
  );

  helpers.openModal({
    title: modalTitle,
    template:
      '/templates/everest.fin.accounting/accountingBook/uinext/bookAccountMappingModal',
    size: 'large',
    initialState: {
      bookMappings: { source, target, bookNames },
    },
    onClose: async () => {
      await actions.refetchUiModelData({
        nodesToLoad: ['accountingBooks'],
      });
    },
  });
}
export function openModalAccountingBookCreate(context: Context): void {
  const { actions, helpers } = context;

  helpers.openModal({
    title: `{{settingsAndPolicies.recordToReport.books.newBook}}`,
    template:
      '/templates/everest.fin.accounting/accountingBook/uinext/bookModal',
    size: 'xsmall',
    onClose: async () => {
      await actions.refetchUiModelData({
        nodesToLoad: ['accountingBooks'],
      });
    },
  });
}

export function onClickHrAdminManagement(context: Context) {
  openLink(
    context,
    '/templates/everest.hr.base/uinext/manager/hrAdminManagement',
    {}
  );
}

export function onClickHrEmployeeCategory(context: Context) {
  openLink(
    context,
    '/templates/everest.hr.base/uinext/employmentCategory/employmentCategoryList',
    {}
  );
}

export function onClickSkillManagement(context: Context) {
  openLink(
    context,
    '/templates/everest.hr.skillmanagement/uinext/skillManagement',
    {}
  );
}

export function onClickSkillLevels(context: Context) {
  openLink(
    context,
    '/templates/everest.hr.skillmanagement/uinext/skillLevel/skillLevels',
    {}
  );
}

export function onClickNavigateROKeyManagement(context: Context) {
  openLink(
    context,
    '/templates/everest.whistleblower/uinext/createROMasterKey',
    {}
  );
}

export function onClickNavigateWhistleblowerUserCreation(context: Context) {
  context.helpers.openModal({
    size: 'xsmall',
    template: '/templates/everest.whistleblower/uinext/manageWhistleblowerUser',
    onModalSubmit: async () => {
      await context.actions.refetchUiModelData();
    },
  });
}

export function onClickNavigateReportingOfficerManagement(context: Context) {
  openLink(
    context,
    '/templates/everest.whistleblower/uinext/reportingOfficerManagement',
    {}
  );
}

// function canQueryFeatureToggle(context: Context) {
//   return _hasPermissionFor(
//     context,
//     'urn:evst:everest:base:model/node:FeatureToggle',
//     'query'
//   );
// }

export async function createMultiBookStructure(context: Context) {
  const { actions, helpers } = context;

  helpers.showNotificationMessage({
    key: 'loading',
    type: 'loading',
    message: 'Loading',
    duration: 0,
  });

  try {
    await actions.run({
      accountingBooks: {
        action: 'setupMultiBookScenario',
        data: {},
      },
    });

    helpers.showNotificationMessage({
      key: 'loading',
      type: 'success',
      message: 'The multi-book scenario has been created',
      duration: 2,
    });
  } catch {
    helpers.showNotificationMessage({
      key: 'loading',
      type: 'error',
      message: 'Failed to create multi-book scenario',
      duration: 2,
    });
  }

  await actions.refetchUiModelData();
}

// #region Entities
export function onCreateEntity(context: Context) {
  const { helpers } = context;

  helpers.navigate({
    to: '@uicode:createEntity',
  });
}

export function openEntityPage({ context, rowData }) {
  const { helpers } = context;
  const entity = rowData?.data;

  if (!entity) {
    return;
  }

  helpers.navigate({
    to: '@uicode:entity?id=' + entity.id,
    initialState: {
      mode: 'view',
    },
  });
}

export function getEntityActionLines(context: Context) {
  const { data } = context;
  const { entities = [] } = data;

  return entities.map((entity) => {
    const { entityName, status } = entity;
    return {
      label: entityName,
      tag: {
        text: getEntityStatusText(context, status),
        color: getEntityStatusColor(status),
      },
      buttonActions: [
        {
          label: '{{settingsAndPolicies.open}}',
          onClick: () => openEntityPage({ context, rowData: { data: entity } }),
        },
      ],
    };
  });
}

export function getEntityStatusText(
  context: Context,
  status: EvstEntityStatus
) {
  const { data } = context;
  const { entityStatusValueHelp = [] } = data;

  return entityStatusValueHelp.find(({ codeValue }) => codeValue === status)
    ?.text;
}

export function getEntityStatusColor(status: EvstEntityStatus) {
  switch (status) {
    case EvstEntityStatus.Active: {
      return 'success';
    }
    case EvstEntityStatus.Inactive: {
      return 'mystic-grey';
    }
    case EvstEntityStatus.Created: {
      return 'jaffa';
    }
    default: {
      return 'info';
    }
  }
}

export function getUserLanguage(context: Context) {
  return context.language;
}

// #endregion

export function getDefaultEntityBookPreferenceValueText(context: Context) {
  const {
    data: { defaultEntityBookSettings },
  } = context;
  const values = Object.values(
    omit(defaultEntityBookSettings ?? {}, ['_nodeReference'])
  );
  const isSameBookForAllEntities = new Set(values).size === 1;
  return isSameBookForAllEntities
    ? '{{settingsAndPolicies.recordToReport.preferences.sameBookForAllEntities}}'
    : '{{settingsAndPolicies.recordToReport.preferences.differentBooksPerEntities}}';
}
