{"version": 2, "uicontroller": "sidebar.uicontroller.ts", "uimodel": {"state": {"employeeId": null, "employeeLoading": false}, "nodes": {"permissions": {"type": "list", "modelId": "everest.appserver/permission/PolicyModel.Policy", "model": "urn:evst:everest:appserver:model/node:permission/Policy", "checkPermissionsInBatch": {"resources": [{"resource": "urn:evst:everest:hr/base:model/node:Employee", "action": "getCurrentEmployee"}, {"resource": "urn:evst:everest:hr/base:model/node:Employee", "action": "getDirectReportsByEmployeeId"}, {"resource": "urn:evst:everest:hr/base:model/node:Employee", "action": "query"}, {"resource": "urn:evst:everest:hr/base:model/node:Department", "action": "query"}, {"resource": "urn:evst:everest:fin/accounting:uitemplate:qtc/invoiceConfiguration/uinext/settings", "action": "template"}, {"resource": "urn:evst:everest:fin/accounting:uitemplate:qtc/creditMemo/uinext/creditMemoConfiguration", "action": "template"}, {"resource": "urn:evst:everest:fin/accounting:uitemplate:qtc/paymentAccounts/uinext/settings", "action": "template"}, {"resource": "urn:evst:everest:fin/accounting:uitemplate:qtc/revenue/settings/uinext/revenueConfiguration", "action": "template"}, {"resource": "urn:evst:everest:fin/accounting:model/node:Payment", "action": "upsertPayment"}, {"resource": "urn:evst:everest:hr/base:model/node:Recruitment", "action": "update"}, {"resource": "urn:evst:everest:appserver:model/node:metadata/EverestMetadataCommit", "action": "reloadMetadata"}, {"resource": "urn:evst:everest:whistleblower:model/node:WhistleblowerMaster<PERSON>ey", "action": "getMasterKey"}, {"resource": "urn:evst:everest:whistleblower:uitemplate:uinext/whistleblower", "action": "template"}], "checkConditions": true}, "fieldList": []}, "employee": {"type": "struct", "modelId": "everest.hr.base/EmployeeModel.Employee", "getCurrentEmployee": "@controller:getEmployeeAction()", "fieldList": ["id"]}, "directReports": {"type": "list", "modelId": "everest.hr.base/JobModel.Job", "getDirectReportsByEmployeeId": "@controller:getDirectReportsQuery()", "fieldList": []}, "departments": {"modelId": "everest.hr.base/DepartmentModel.Department", "type": "list", "query": "@controller:getDepartmentsAction()", "fieldList": ["id", "startDate", "endDate"]}, "enableHGBLinkConfig": {"modelId": "everest.appserver/ConfigurationModel.Configuration", "type": "struct", "getValueOrDefault": {"name": "ENABLE_HGB_SIDEBAR_LINK", "packageName": "everest.fin.accounting", "defaultValue": false}, "fieldList": ["value"]}, "everestApplications": {"modelId": "everest.appserver/ApplicationEngineModel.ApplicationEngine", "type": "struct", "calculateSidebarSection": {"menuSectionUrn": "urn:evst:everest:base:menu-element:EverestApplications"}, "fieldList": []}, "customApplications": {"modelId": "everest.appserver/ApplicationEngineModel.ApplicationEngine", "type": "struct", "calculateSidebarSection": {"menuSectionUrn": "urn:evst:everest:base:menu-element:CustomApplications"}, "fieldList": []}}}, "uiview": {"templateType": "sidebar", "i18n": "sidebar", "sections": [{"icon": "dashboard", "label": "{{sidebar.dashboards}}", "linkTo": "/", "submenu": {"title": "{{sidebar.dashboards}}", "linkGroups": [{"title": "{{sidebar.general}}", "groupItems": [{"label": "{{sidebar.quickActions}}", "linkTo": "/"}, {"label": "{{sidebar.helpCenter}}", "linkTo": "@uicode:helpCenter"}, {"label": "{{sidebar.executiveDashboard}} (beta)", "linkTo": "/templates/everest.simplifieddashboard/uinext/executiveDashboard"}]}, {"title": "{{sidebar.quoteToCash}}", "groupItems": [{"label": "{{sidebar.reports.arAging}}", "linkTo": "/templates/everest.fin.accounting/arAging/uinext/arAging"}]}, {"title": "{{sidebar.processes}}", "groupItems": [{"label": "{{sidebar.procureToPay}}", "linkTo": "@uicode:p2pDashboard"}, {"label": "{{sidebar.recordToReport}}", "linkTo": "/templates/everest.appserver/uinext/disabled"}, {"label": "{{sidebar.people}}", "linkTo": "@controller:getDashboardLink()"}, {"label": "{{sidebar.cloudCosts.dashboard}}", "linkTo": "/templates/everest.cloudcosts/uinext/dashboard"}]}, {"title": "{{sidebar.saasMetrics}}", "groupItems": [{"label": "{{sidebar.reports.arrDashboard}}", "linkTo": "/templates/everest.fin.accounting/qtc/arr/uinext/arrDashboard"}, {"label": "{{sidebar.saas.revenueAndGrowth}}", "linkTo": "/templates/everest.reports.saas/ui/revenueAndGrowth/dashboard", "visible": true}, {"label": "{{sidebar.saas.financialEfficiencyAndProfitability}}", "linkTo": "/templates/everest.reports.saas/ui/financialEfficiencyAndProfitability/dashboard", "visible": true}]}]}}, {"icon": "reports", "label": "{{sidebar.reports}}", "submenu": {"title": "{{sidebar.reports}}", "linkGroups": [{"title": "{{sidebar.reports.operational}}", "groupItems": [{"label": "{{sidebar.generalLedger}}", "linkTo": "@uicode:generalLedger"}, {"label": "{{sidebar.currencyRevaluation}}", "linkTo": "/templates/everest.fin.accounting/currencyRevaluation/uinext/currencyRevaluation"}, {"label": "{{sidebar.intercompanyReconciliation}}", "linkTo": "/templates/everest.fin.accounting/journalEntry/uinext/intercompanyReconciliation"}, {"label": "{{sidebar.trialBalance}}", "linkTo": "/templates/everest.fin.accounting/trialBalance/uinext/trialBalance"}, {"label": "{{sidebar.reports.prepaidWaterfall}} (beta)", "linkTo": "/templates/everest.fin.accounting/uinext/amortization/prepaidWaterfall"}]}, {"title": "{{sidebar.reports.revenue}}", "groupItems": [{"label": "{{sidebar.reports.deferredRevenueWaterfall}}", "linkTo": "/templates/everest.fin.accounting/qtc/revenue/deferred/uinext/deferredRevenueWaterfall"}, {"label": "{{sidebar.reports.revenueWaterfall}}", "linkTo": "@uicode:revenueWaterfall"}, {"label": "{{sidebar.revenueSegmentReport}}", "linkTo": "@uicode:revenueSegment"}]}, {"title": "{{sidebar.reports.arAging}}", "groupItems": [{"label": "{{sidebar.reports.agingReport}}", "linkTo": "/templates/everest.fin.accounting/arAging/uinext/agingReport"}]}, {"title": "{{sidebar.reports.apAging}}", "groupItems": [{"label": "{{sidebar.reports.apAgingReport}}", "linkTo": "/templates/everest.fin.expense/uinext/vendorMgmt/apAgingDetailedReport"}]}, {"title": "{{sidebar.reports.arr}}", "groupItems": [{"label": "{{sidebar.reports.arrReport}}", "linkTo": "/templates/everest.fin.accounting/qtc/arr/uinext/arrReport"}]}, {"title": "{{sidebar.reports.automatedReconciliation}}", "groupItems": [{"label": "{{sidebar.reports.automatedReconciliationList}}", "linkTo": "/templates/everest.automated-reconciliation/uinext/automatedReconciliationList"}]}, {"title": "{{sidebar.reports.adhoc}}", "groupItems": [{"label": "{{sidebar.reports.adhocDashboard}}", "linkTo": "@uicode:reporting"}]}]}}, {"icon": "cloud-subscriptions", "label": "{{sidebar.cloudAndSubscriptions}}", "submenu": {"title": "{{sidebar.cloudAndSubscriptionManagement}}", "linkGroups": [{"title": "{{sidebar.manage}}", "groupItems": [{"label": "{{sidebar.connections}}", "linkTo": "/templates/everest.cloudcosts/uinext/cloudProviderAccountList"}, {"label": "{{sidebar.subscriptions}}", "linkTo": "/templates/everest.inv.itam/uinext/softwareEntitlement/softwareEntitlementList?feat-delta=true&mode=view"}, {"label": "{{sidebar.labels}}", "linkTo": "/templates/everest.cloudcosts/uinext/labelGroupTable"}, {"label": "{{sidebar.banking.reconciliation}}", "linkTo": "/templates/everest.cloudcosts/uinext/reconciliation/cloudProviderVendorBillList"}, {"label": "{{sidebar.cloudCosts.journey}}", "linkTo": "/templates/everest.cloudcosts/uinext/user/journey"}, {"label": "{{sidebar.cloudCosts.tenants}}", "linkTo": "/templates/everest.tenant.management/uinext/tenantList"}]}, {"title": "{{sidebar.reports}}", "groupItems": [{"label": "{{sidebar.dashboard}}", "linkTo": "/templates/everest.cloudcosts/uinext/dashboard"}, {"label": "{{sidebar.costExplorer}}", "linkTo": "/templates/everest.cloudcosts/uinext/cloudCostExplorer"}, {"label": "{{sidebar.budgets}}", "linkTo": "/templates/everest.cloudcosts/uinext/budgets/list"}, {"label": "{{sidebar.usage}}", "linkTo": "/templates/everest.cloudcosts/uinext/cloudProviderAccountList?activeSegment=1"}, {"label": "{{sidebar.cloudCosts.deltaAnalysis}}", "linkTo": "/templates/everest.cloudcosts/uinext/delta/table"}]}]}}, {"icon": "bank-management", "label": "{{sidebar.banking}}", "submenu": {"title": "{{sidebar.banking}}", "linkGroups": [{"title": "{{sidebar.banking.manage}}", "groupItems": [{"label": "{{sidebar.banking.accounts}}", "linkTo": "/templates/everest.fin.integration.bank/uinext/dashboard"}, {"label": "{{sidebar.banking.integration}}", "linkTo": "/templates/everest.fin.integration.bank/uinext/integrations"}]}, {"title": "{{sidebar.banking.matching}}", "groupItems": [{"label": "{{sidebar.banking.bankTransactions}}", "linkTo": "/templates/everest.fin.integration.bank/uinext/dashboard?activeSegment=1"}, {"label": "{{sidebar.banking.cardTransactions}}", "linkTo": "/templates/everest.fin.integration.bank/uinext/dashboard?activeSegment=2"}, {"label": "{{sidebar.banking.bankEntries}}", "linkTo": "/templates/everest.fin.integration.bank/uinext/bankEntry/bankEntries?feat-delta=true"}, {"label": "{{sidebar.banking.creditCardEntries}}", "linkTo": "/templates/everest.fin.integration.bank/uinext/creditCardEntry/creditCardEntries"}, {"label": "{{sidebar.banking.fundTransfers}}", "linkTo": "/templates/everest.fin.integration.bank/uinext/fundTransfer/fundTransfers"}, {"label": "{{sidebar.banking.reconciliation}}", "linkTo": "/templates/everest.fin.integration.bank/uinext/reconciliation/accountReconciliationList"}]}, {"title": "{{sidebar.banking.reports}}", "groupItems": [{"label": "{{sidebar.banking.cashFlowReport}}", "linkTo": "/templates/everest.appserver/uinext/disabled"}, {"label": "{{sidebar.banking.reconciliationStatements}}", "linkTo": "/templates/everest.appserver/uinext/disabled"}]}]}}, {"icon": "accounts-receivable", "label": "{{sidebar.quoteToCash}}", "submenu": {"title": "{{sidebar.quoteToCash}}", "linkGroups": [{"title": "{{sidebar.sales}}", "groupItems": [{"label": "{{sidebar.customers}}", "linkTo": "/templates/everest.fin.accounting/qtc/customer/uinext/customers"}, {"label": "{{sidebar.salesArrangements}}", "linkTo": "@uicode:salesArrangements"}, {"label": "{{sidebar.salesOrders}}", "linkTo": "/templates/everest.fin.accounting/qtc/salesOrder/uinext/salesOrders"}, {"label": "{{sidebar.subscriptions}}", "linkTo": "/templates/everest.fin.accounting/qtc/subscription/uinext/subscriptions"}, {"label": "{{sidebar.usageWorkbench}}", "linkTo": "/templates/everest.fin.accounting/qtc/usage/uinext/usageWorkbench"}]}, {"title": "{{sidebar.masterData}}", "groupItems": [{"label": "{{sidebar.productCatalog}}", "linkTo": "/templates/everest.fin.accounting/qtc/masterData/uinext/products?feat-delta=true"}, {"label": "{{sidebar.priceBooks}}", "linkTo": "/templates/everest.fin.accounting/qtc/masterData/uinext/priceBooks"}]}, {"title": "{{sidebar.invoicing}}", "groupItems": [{"label": "{{sidebar.invoiceScheduler}}", "linkTo": "/templates/everest.fin.accounting/qtc/invoicing/uinext/invoicing"}, {"label": "{{sidebar.invoices}}", "linkTo": "/templates/everest.fin.accounting/qtc/invoicing/uinext/invoices"}, {"label": "{{sidebar.creditMemos}}", "linkTo": "/templates/everest.fin.accounting/qtc/creditMemo/uinext/creditMemos"}, {"label": "{{sidebar.debitMemos}}", "linkTo": "/templates/everest.fin.accounting/qtc/debitMemo/uinext/debitMemos"}, {"label": "{{sidebar.dunning}}", "linkTo": "/templates/everest.fin.accounting/qtc/dunning/uinext/dunning"}, {"label": "{{sidebar.invoicingWorkbench}}", "linkTo": "/templates/everest.fin.accounting/qtc/invoicing/uinext/invoicingWorkbench"}]}, {"title": "{{sidebar.collections}}", "groupItems": [{"label": "{{sidebar.cashApplication}}", "visible": "@controller:isCashApplicationVisible()", "linkTo": "/templates/everest.fin.accounting/qtc/payment/uinext/payment?mode=create"}, {"label": "{{sidebar.payments}}", "linkTo": "/templates/everest.fin.accounting/qtc/payment/uinext/payments"}, {"label": "{{sidebar.refunds}}", "linkTo": "@uicode:refunds"}, {"label": "{{sidebar.paymentWorkbench}}", "linkTo": "/templates/everest.fin.accounting.reconciliation/paymentWorkbench/uinext/paymentWorkbench"}]}, {"title": "{{sidebar.revenue}}", "groupItems": [{"label": "{{sidebar.revenueContracts}}", "linkTo": "/templates/everest.fin.accounting/qtc/revenue/contract/uinext/revenueContracts"}, {"label": "{{sidebar.revenueCloseProcess}}", "linkTo": "/templates/everest.fin.accounting/qtc/revenue/contract/uinext/revenueCloseProcess"}, {"label": "{{sidebar.manualRevenueRelease}}", "linkTo": "/templates/everest.fin.accounting/qtc/revenue/scheduling/uinext/manualRevenueRelease"}]}]}}, {"icon": "accounts-payable", "label": "{{sidebar.procureToPay}}", "submenu": {"title": "{{sidebar.procureToPay}}", "linkGroups": [{"title": "{{sidebar.setUp}}", "groupItems": [{"label": "{{sidebar.vendors}}", "linkTo": "/templates/everest.fin.expense/uinext/vendorMgmt/vendors"}, {"label": "{{sidebar.cloudCosts.providers}}", "linkTo": "/templates/everest.cloudcosts/uinext/cloudProviderAccountList"}, {"label": "{{sidebar.expenseCategories}}", "linkTo": "/templates/everest.fin.expense/uinext/expenseMgmt/expenseCategories"}]}, {"title": "{{sidebar.expenses}}", "groupItems": [{"label": "{{sidebar.purchaseOrders}}", "linkTo": "/templates/everest.fin.procurement/uinext/purchaseOrders/purchaseOrders"}, {"label": "{{sidebar.vendorBills}}", "linkTo": "/templates/everest.fin.expense/uinext/vendorMgmt/vendorBills"}, {"label": "{{sidebar.vendorCredits}}", "linkTo": "/templates/everest.fin.expense/uinext/vendorMgmt/vendorCredits"}, {"label": "{{sidebar.employeeExpenses}}", "linkTo": "/templates/everest.fin.expense/uinext/expenseMgmt/employeeExpenses"}, {"label": "{{sidebar.cloudCosts.explorer}}", "linkTo": "/templates/everest.cloudcosts/uinext/cloudCostExplorer"}, {"label": "{{sidebar.apClose}}", "linkTo": "/templates/everest.fin.expense/uinext/apClose/apCloseProcess"}, {"label": "{{sidebar.billableExpenses}}", "linkTo": "/templates/everest.fin.expense/uinext/billableExpenseMgmt/billableExpenses"}]}, {"title": "{{sidebar.payments}}", "groupItems": [{"label": "{{sidebar.paymentsQueue}}", "linkTo": "/templates/everest.fin.expense/payment/uinext/payments"}, {"label": "{{sidebar.paymentsHistory}}", "linkTo": "/templates/everest.fin.expense/payment/uinext/paymentsHistory"}, {"label": "{{sidebar.creditApplication}}", "linkTo": "/templates/everest.fin.expense/uinext/vendorMgmt/applyVendorCredit"}, {"label": "{{sidebar.paymentReport}}", "linkTo": "/templates/everest.fin.expense/uinext/reports/paymentReport"}, {"label": "{{sidebar.purchaseOrderMatching}}", "linkTo": "/templates/everest.fin.procurement/uinext/matching/purchaseOrderMatching"}]}]}}, {"icon": "revenue-management", "label": "{{sidebar.recordToReport}}", "submenu": {"title": "{{sidebar.recordToReport}}", "linkGroups": [{"title": "{{sidebar.setUp}}", "groupItems": [{"label": "{{sidebar.entities}}", "linkTo": "@uicode:entities"}, {"label": "{{sidebar.chartOfAccounts}}", "linkTo": "/templates/everest.fin.accounting/uinext/chartOfAccountsList"}, {"label": "{{sidebar.fiscalCalendars}}", "linkTo": "/templates/everest.fin.accounting/accountingPeriod/uinext/fiscalCalendars"}, {"label": "{{sidebar.manageCurrency}}", "linkTo": "/templates/everest.base/uinext/activeCurrency"}, {"label": "{{sidebar.businessPartners}}", "linkTo": "/templates/everest.fin.base/uinext/businessPartners"}, {"label": "{{sidebar.intercompanySettings}}", "linkTo": "/templates/everest.fin.accounting/journalEntry/uinext/intercompanySettings"}, {"label": "{{sidebar.taxPolicy}}", "linkTo": "/templates/everest.appserver/uinext/disabled"}]}, {"title": "{{sidebar.journalEntries}}", "groupItems": [{"label": "{{sidebar.journalEntries}}", "linkTo": "/templates/everest.fin.accounting/uinext/journalEntries"}]}, {"title": "{{sidebar.closingTheBooks}}", "groupItems": [{"label": "{{sidebar.manageAccountingPeriods}}", "linkTo": "/templates/everest.fin.accounting/accountingPeriod/uinext/manageAccountingPeriodList"}, {"label": "{{sidebar.recordAmortization}}", "linkTo": "/templates/everest.fin.accounting/uinext/amortization/recordAmortization"}, {"label": "{{sidebar.amortizationSchedules}}", "linkTo": "/templates/everest.fin.accounting/uinext/amortization/amortizations"}, {"label": "{{sidebar.accruedLiabilities}} (beta)", "linkTo": "/templates/everest.fin.accounting/accruedLiability/uinext/accruedLiabilities"}, {"label": "{{sidebar.consolidation}}", "linkTo": "/templates/everest.appserver/uinext/disabled"}]}]}}, {"icon": "financials", "label": "{{sidebar.financialStatements}}", "submenu": {"title": "{{sidebar.financialStatements}}", "linkGroups": [{"title": "{{sidebar.financials}}", "groupItems": [{"label": "{{sidebar.incomeStatement}}", "linkTo": "/templates/everest.fin.accounting/uinext/incomeStatement"}, {"label": "{{sidebar.balanceSheet}}", "linkTo": "/templates/everest.fin.accounting/uinext/balanceSheet"}, {"label": "{{sidebar.statementOfCashFlow}} (beta)", "linkTo": "@uicode:cashFlow"}, {"label": "{{sidebar.gaapIncomeStatement}}", "linkTo": "@uicode:financialStatement?type=gaapIncomeStatement"}, {"label": "{{sidebar.hgbIncomeStatement}}", "linkTo": "@uicode:financialStatement?type=hgbIncomeStatement", "visible": "@controller:isHGBLinkVisible()"}, {"label": "{{sidebar.hgbBalanceSheet}}", "linkTo": "@uicode:financialStatement?type=hgbBalanceSheet", "visible": "@controller:isHGBLinkVisible()"}]}, {"title": "{{sidebar.budgets}}", "groupItems": [{"label": "{{sidebar.budgets}}", "linkTo": "/templates/everest.fin.accounting/budgets/uinext/budgetList?feat-delta=true"}]}]}}, {"icon": "people", "label": "{{sidebar.people}}", "submenu": {"title": "{{sidebar.people}}", "linkGroups": [{"title": "{{sidebar.hr.database}}", "groupItems": [{"label": "{{sidebar.employees}}", "linkTo": "@controller:getEmployeesLink()", "visible": "@controller:canGetAllEmployees()"}, {"label": "{{sidebar.departments}}", "linkTo": "/templates/everest.hr.base/uinext/department/departmentHierarchy"}, {"label": "{{sidebar.teams}}", "linkTo": "/templates/everest.hr.base/uinext/team/teams"}, {"label": "{{sidebar.job.profiles}}", "linkTo": "/templates/everest.hr.base/uinext/jobProfile/list"}, {"label": "{{sidebar.positions}}", "linkTo": "/templates/everest.hr.base/uinext/position/positions"}, {"label": "{{sidebar.locations}}", "linkTo": "/templates/everest.hr.base/uinext/location/locationHierarchy"}, {"label": "{{sidebar.regions}}", "linkTo": "/templates/everest.hr.base/uinext/region/regionHierarchy"}, {"label": "{{sidebar.projects}}", "linkTo": "@controller:getProjectsLink()"}, {"label": "{{sidebar.onboardings}}", "linkTo": "/templates/everest.hr.base/uinext/boarding/boardings?activeSegment=0"}]}, {"title": "{{sidebar.policies}}", "groupItems": [{"label": "{{sidebar.timesheet.policy}}", "linkTo": "/templates/everest.hr.base/uinext/timesheet/policy/list"}, {"label": "{{sidebar.absence.policy}}", "linkTo": "/templates/everest.hr.base/uinext/absencePolicy/absencePolicies"}, {"label": "{{sidebar.holiday.calendar}}", "linkTo": "/templates/everest.hr.base/uinext/holidayCalendar/holidayCalendars"}, {"label": "{{sidebar.personnel.training}}", "linkTo": "/templates/everest.hr.base/uinext/training/trainingList"}, {"label": "{{sidebar.position.permissions}}", "linkTo": "/templates/everest.hr.base/uinext/securityManagement/positionPermissions"}]}, {"title": "{{sidebar.payroll}}", "groupItems": [{"label": "{{sidebar.payrollDetails}}", "linkTo": "/templates/everest.fin.expense/uinext/payrollMgmt/payrolls"}, {"label": "{{sidebar.payrollLiabilities}}", "linkTo": "/templates/everest.fin.expense/uinext/payrollMgmt/payrollLiabilities"}]}, {"title": "{{sidebar.your.profile}}", "groupItems": [{"label": "{{sidebar.employee.record}}", "linkTo": "@controller:getProfileLink()"}, {"label": "{{sidebar.your.direct.reports}}", "linkTo": "@controller:getDirectReportsLink()", "visible": "@controller:setDirectReportVisibility()"}, {"label": "{{sidebar.timesheet.timetracking}}", "linkTo": "@controller:getTimeTrackingLink()", "visible": "@controller:canGetCurrentEmployee()"}, {"label": "{{sidebar.request.absence}}", "linkTo": "@controller:getYourAbsencesLink()", "visible": "@controller:canGetCurrentEmployee()"}, {"label": "{{sidebar.employee.expenses}}", "linkTo": "@controller:getYourExpensesLink()", "visible": "@controller:canGetCurrentEmployee()"}, {"label": "{{sidebar.calendar}}", "linkTo": "@controller:getYourCalendarLink()", "visible": "@controller:canGetCurrentEmployee()"}, {"label": "{{sidebar.your.trainings}}", "linkTo": "/templates/everest.hr.base/uinext/training/yourTrainings", "visible": "@controller:canGetCurrentEmployee()"}, {"label": "{{sidebar.yourDevices}}", "linkTo": "/templates/everest.inv.itam/uinext/userDevices/yourDevices"}, {"label": "{{sidebar.your.skills}}", "linkTo": "/templates/everest.hr.skillmanagement/uinext/yourSkills/yourSkills", "visible": "@controller:canGetCurrentEmployee()"}, {"label": "{{sidebar.manager.requests}}", "linkTo": "/templates/everest.hr.base/uinext/manager/requestsToApprove", "visible": "@controller:canGetCurrentEmployee()"}]}, {"title": "{{sidebar.reports}}", "groupItems": [{"label": "{{sidebar.orgchart}}", "linkTo": "/templates/everest.hr.base/uinext/orgChart/orgChart"}, {"label": "{{sidebar.employee.directory}}", "linkTo": "/templates/everest.hr.base/uinext/employee/employeeOverviewList"}, {"label": "{{sidebar.timesheet.reports}}", "linkTo": "/templates/everest.hr.base/uinext/timesheet/record/reports"}, {"label": "{{team.absence.calendar}}", "linkTo": "/templates/everest.hr.base/uinext/manager/managerCalendar", "visible": "@controller:setDirectReportVisibility()"}, {"label": "{{sidebar.headcountanalysis}}", "linkTo": "/templates/everest.hr.base/uinext/headcountAnalysis/headcount", "visible": "@controller:canGetAllEmployees()"}, {"label": "{{sidebar.headcountPlanning}}", "linkTo": "/templates/everest.hr.planning/uinext/headcountPlanList", "visible": "@controller:canGetAllEmployees()"}, {"label": "{{sidebar.compBand}}", "linkTo": "/templates/everest.hr.base/uinext/compensationBand/compBandList", "visible": "@controller:canGetAllEmployees()"}, {"label": "{{sidebar.financialPlanner}}", "linkTo": "/templates/everest.hr.planning/uinext/financialPlanner/financialPlanner"}, {"label": "{{sidebar.businessConfigLab}}", "linkTo": "/templates/everest.hr.planning/uinext/businessConfigLab/businessConfigLab"}]}, {"title": "{{sidebar.recruitment}}", "groupItems": [{"label": "{{sidebar.applicants}}", "linkTo": "/templates/everest.hr.base/uinext/applicant/applicants"}, {"label": "{{sidebar.recruitments}}", "linkTo": "/templates/everest.hr.base/uinext/recruitment/recruitments", "visible": "@controller:canGetRecruitments()"}, {"label": "{{sidebar.applicant.stage.templates}}", "linkTo": "/templates/everest.hr.base/uinext/recruitment/templateTypes"}]}]}}, {"icon": "@controller:getFirstEverestApplicationIcon()", "label": "@controller:getFirstEverestApplicationLabel()", "visible": "@controller:getFirstEverestApplicationVisible()", "submenu": "@controller:getFirstEverestApplicationSubmenu()"}, {"icon": "asset-management", "label": "{{sidebar.assetManagement}}", "submenu": {"title": "{{sidebar.assetManagement}} (beta)", "linkGroups": [{"title": "{{sidebar.setUp}}", "groupItems": [{"label": "{{sidebar.manageFixedAssetCategories}}", "linkTo": "/templates/everest.inv.fad/uinext/fixedAssets/fixedAssetCategories"}, {"label": "{{sidebar.manageFixedAssetTypes}}", "linkTo": "/templates/everest.inv.fad/uinext/fixedAssets/fixedAssetTypes"}, {"label": "{{sidebar.depreciationPolicies}}", "linkTo": "/templates/everest.inv.fad/uinext/depreciation/depreciationPolicies"}]}, {"title": "{{sidebar.fixedAssets}}", "groupItems": [{"label": "{{sidebar.manageFixedAssets}}", "linkTo": "@uicode:fixedAssets"}]}, {"title": "{{sidebar.depreciation}}", "groupItems": [{"label": "{{sidebar.depreciationSchedules}}", "linkTo": "/templates/everest.inv.fad/uinext/depreciation/depreciationSchedules"}, {"label": "{{sidebar.recordDepreciation}}", "linkTo": "/templates/everest.inv.fad/uinext/depreciation/recordDepreciation"}]}, {"title": "{{sidebar.itAssetManagement}}", "groupItems": [{"label": "{{sidebar.setUp}}", "linkTo": "/templates/everest.inv.itam/uinext/setup/deviceConfigManager?mode=view"}, {"label": "{{sidebar.assetTypes}}", "linkTo": "/templates/everest.inv.itam/uinext/assetType/assetTypeList?mode=view"}, {"label": "{{sidebar.assetLocations}}", "linkTo": "/templates/everest.inv.itam/uinext/assetLocation/assetLocationList?mode=view"}, {"label": "{{sidebar.assetModels}}", "linkTo": "/templates/everest.inv.itam/uinext/assetModel/assetModelList?mode=view"}, {"label": "{{sidebar.manufacturers}}", "linkTo": "/templates/everest.inv.itam/uinext/manufacturer/manufacturerList?mode=view"}, {"label": "{{sidebar.adminView}}", "linkTo": "/templates/everest.inv.itam/uinext/adminDevices/adminDevices?mode=view"}, {"label": "{{sidebar.personalDevices}}", "linkTo": "/templates/everest.inv.itam/uinext/personalDevices/personalDevices"}, {"label": "{{sidebar.assetMaintenanceTask}}", "linkTo": "/templates/everest.inv.itam/uinext/assetMaintenanceTask/assetMaintenanceTaskList?mode=view", "visible": false}, {"label": "{{sidebar.assetMaintenanceSchedule}}", "linkTo": "/templates/everest.inv.itam/uinext/assetMaintenanceSchedule/assetMaintenanceScheduleList?mode=view", "visible": false}, {"label": "{{sidebar.maintenanceManagement}}", "linkTo": "/templates/everest.inv.itam/uinext/maintenanceManagement/maintenanceManagement?mode=view", "visible": false}, {"label": "{{sidebar.assetEOLStages}}", "linkTo": "/templates/everest.inv.itam/uinext/assetEOLStage/assetEOLStageList?mode=view", "visible": false}, {"label": "{{sidebar.eolWorkflow}}", "linkTo": "/templates/everest.inv.itam/uinext/assetEOLWorkflow/assetEOLWorkflowList?mode=view", "visible": true}]}, {"title": "{{sidebar.softwareAssetManagement}}", "groupItems": [{"label": "{{sidebar.softwarePublishers}}", "linkTo": "/templates/everest.inv.itam/uinext/softwarePublisher/softwarePublisherList?mode=view", "visible": false}, {"label": "{{sidebar.softwareCategories}}", "linkTo": "/templates/everest.inv.itam/uinext/softwareCategory/softwareCategoryList?mode=view", "visible": false}, {"label": "{{sidebar.softwareModels}}", "linkTo": "/templates/everest.inv.itam/uinext/softwareModel/softwareModelList?mode=view", "visible": false}, {"label": "{{sidebar.softwareEntitlements}}", "linkTo": "/templates/everest.inv.itam/uinext/softwareEntitlement/softwareEntitlementList?mode=view", "visible": true}, {"label": "{{sidebar.assignmentList}}", "linkTo": "/templates/everest.inv.itam/uinext/softwareEntitlement/assignmentList?mode=view", "visible": true}, {"label": "{{sidebar.seatAssignment}}", "linkTo": "/templates/everest.inv.itam/uinext/softwareEntitlement/seatAssignment?mode=view", "visible": true}, {"label": "{{sidebar.assignmentRecommendations}}", "linkTo": "/templates/everest.inv.itam/uinext/softwareEntitlement/licenseRecommendations?mode=view", "visible": true}, {"label": "{{sidebar.samDashboard}}", "linkTo": "/templates/everest.inv.itam/uinext/softwareEntitlement/softwareAssetDashboard?mode=view", "visible": true}]}]}}, {"icon": "cloud-subscriptions", "label": "{{sidebar.aitools}}", "submenu": {"title": "{{sidebar.specManager}}", "linkGroups": [{"title": "", "groupItems": [{"label": "{{sidebar.spec.dashboard.v2}}", "linkTo": "@uicode:specV2dashboard"}, {"label": "{{sidebar.spec.dashboard}}", "linkTo": "@uicode:specdashboard"}, {"label": "{{sidebar.security.template}}", "linkTo": "@uicode:aiSpecSecurityTemplate"}]}]}}, {"icon": "action", "label": "{{sidebar.inventoryManagement}}", "visible": false, "submenu": {"title": "{{sidebar.inventoryManagement}}", "linkGroups": [{"title": "{{sidebar.general}}", "groupItems": [{"label": "{{sidebar.dashboard}}", "linkTo": "/content/everest.inventory/pages/dashboard/dashboard"}]}, {"title": "{{sidebar.setUp}}", "groupItems": [{"label": "{{sidebar.unitOfMeasure}}", "linkTo": "/content/everest.inventory/pages/uom/list"}, {"label": "{{sidebar.unitOfMeasureGroup}}", "linkTo": "/content/everest.inventory/pages/uomGroup/list"}, {"label": "{{sidebar.categories}}", "linkTo": "/content/everest.inventory/pages/categories/list"}, {"label": "{{sidebar.warehouse}}", "linkTo": "/content/everest.inventory/pages/warehouseLocations/list"}, {"label": "{{sidebar.item}}", "linkTo": "/content/everest.inventory/pages/items/list"}, {"label": "{{sidebar.receipts}}", "linkTo": "/content/everest.inventory/pages/receipts/list"}]}, {"visible": false, "title": "{{sidebar.documents}}", "groupItems": [{"label": "{{sidebar.inventoryIncrease}}", "linkTo": "/templates/everest.inv.base/inventoryTransaction/documents/uinext/inventoryIncreaseList"}, {"label": "{{sidebar.inventoryDecrease}}", "linkTo": "/templates/everest.inv.base/inventoryTransaction/documents/uinext/inventoryDecreaseList"}]}, {"visible": false, "title": "{{sidebar.inventoryPurchase}}", "groupItems": [{"label": "{{sidebar.inventoryPurchaseOrder}}", "linkTo": "/templates/everest.inv.base/inventoryTransaction/documents/purchase/order/orderList?feat-delta=true&mode=view"}, {"label": "{{sidebar.inventoryPurchaseDelivery}}", "linkTo": "/templates/everest.inv.base/inventoryTransaction/documents/purchase/delivery/deliveryList?feat-delta=true&mode=view"}, {"label": "{{sidebar.inventoryPurchaseReturn}}", "linkTo": "/templates/everest.inv.base/inventoryTransaction/documents/purchase/return/returnList?feat-delta=true&mode=view"}, {"label": "{{sidebar.inventoryPurchaseInvoice}}", "linkTo": "/templates/everest.inv.base/inventoryTransaction/documents/purchase/invoice/invoiceList?feat-delta=true&mode=view"}, {"label": "{{sidebar.inventoryPurchaseCreditMemo}}", "linkTo": "/templates/everest.inv.base/inventoryTransaction/documents/purchase/creditMemo/creditMemoList?feat-delta=true&mode=view"}]}, {"visible": false, "title": "{{sidebar.inventorySales}}", "groupItems": [{"label": "{{sidebar.inventoryPurchaseOrder}}", "linkTo": "/templates/everest.inv.base/inventoryTransaction/documents/sales/order/orderList?feat-delta=true&mode=view"}, {"label": "{{sidebar.inventoryPurchaseDelivery}}", "linkTo": "/templates/everest.inv.base/inventoryTransaction/documents/sales/delivery/deliveryList?feat-delta=true&mode=view"}, {"label": "{{sidebar.inventoryPurchaseReturn}}", "linkTo": "/templates/everest.inv.base/inventoryTransaction/documents/sales/return/returnList?feat-delta=true&mode=view"}, {"label": "{{sidebar.inventoryPurchaseInvoice}}", "linkTo": "/templates/everest.inv.base/inventoryTransaction/documents/sales/invoice/invoiceList?feat-delta=true&mode=view"}, {"label": "{{sidebar.inventoryPurchaseCreditMemo}}", "linkTo": "/templates/everest.inv.base/inventoryTransaction/documents/sales/creditMemo/creditMemoList?feat-delta=true&mode=view"}]}, {"visible": false, "title": "{{sidebar.reports}}", "groupItems": [{"label": "{{sidebar.inventoryTransactions}}", "linkTo": "/templates/everest.inv.base/reports/inventoryTransaction/uinext/inventoryTransactionReport"}]}]}}, {"visible": "@binding:customApplications.visible", "title": "@binding:customApplications.title", "groupItems": "@binding:customApplications.groupItems"}, {"title": "{{sidebar.configurations}}", "groupItems": [{"icon": "configurations", "label": "{{sidebar.settingsPolicies}}", "submenu": {"title": "{{sidebar.settingsPolicies}}", "linkGroups": [{"title": "{{sidebar.applicationSettings}}", "groupItems": [{"label": "{{sidebar.organization}}", "linkTo": "/templates/everest.base/uinext/settingsAndPolicies?activeSetting=organization"}, {"label": "{{sidebar.general}}", "linkTo": "/templates/everest.base/uinext/settingsAndPolicies?activeSetting=0"}, {"label": "{{sidebar.banking}}", "linkTo": "/templates/everest.base/uinext/settingsAndPolicies?activeSetting=1"}, {"label": "{{sidebar.quoteToCash}}", "visible": "@controller:isConfigVisible()", "linkTo": "/templates/everest.base/uinext/settingsAndPolicies?activeSetting=2"}, {"label": "{{sidebar.procureToPay}}", "linkTo": "/templates/everest.base/uinext/settingsAndPolicies?activeSetting=3"}, {"label": "{{sidebar.recordToReport}}", "linkTo": "/templates/everest.base/uinext/settingsAndPolicies?activeSetting=4"}, {"label": "{{sidebar.people}}", "linkTo": "/templates/everest.base/uinext/settingsAndPolicies?activeSetting=5"}, {"label": "{{sidebar.dataProtection}}", "visible": "@controller:isDataProtectionAdmin()", "linkTo": "/templates/everest.base/uinext/settingsAndPolicies?activeSetting=6"}, {"label": "{{sidebar.eve.ai}}", "linkTo": "/templates/everest.base/uinext/settingsAndPolicies?activeSetting=7"}, {"label": "{{sidebar.approvalPolicies}}", "linkTo": "/templates/everest.base.approvals/uinext/billApprovalPolicies"}, {"label": "{{sidebar.migration}}", "linkTo": "/templates/everest.base/uinext/settingsAndPolicies?activeSetting=8"}, {"label": "{{sidebar.whistleblowing}}", "linkTo": "/templates/everest.base/uinext/settingsAndPolicies?activeSetting=9", "visible": "@controller:isWBKeyManagementAllowed()"}]}, {"title": "{{sidebar.userSettings}}", "groupItems": [{"label": "{{sidebar.general}}", "linkTo": "/templates/everest.base/uinext/settingsAndPolicies?activeSetting=userSettings-title"}]}, {"title": "{{sidebar.compliance}}", "visible": false, "groupItems": [{"label": "{{sidebar.dataProtection}}", "linkTo": "/templates/everest.base/uinext/settingsAndPolicies?activeSetting=6"}]}]}}, {"icon": "integrations", "label": "{{sidebar.integrations}}", "submenu": {"title": "{{sidebar.integrations}}", "linkGroups": [{"title": "{{sidebar.manage}}", "groupItems": [{"label": "{{sidebar.integration.connectorEngine}}", "linkTo": "/templates/everest.connectorengine/uinext/list"}, {"label": "{{sidebar.integration.sessions}}", "linkTo": "/templates/everest.fin.integration.base/sessions/uinext/list"}, {"label": "{{sidebar.integration.mappingEngine}}", "linkTo": "/templates/everest.fin.integration.base/mappings/uinext/list"}, {"label": "{{sidebar.integration.actionExecutor}}", "linkTo": "/templates/everest.fin.integration.base/actionExecutor/uinext/list"}, {"label": "{{sidebar.integration.postExtraction}}", "linkTo": "/templates/everest.fin.integration.base/postExtraction/uinext/list"}, {"label": "{{sidebar.integration.mediationFunctions}}", "linkTo": "/templates/everest.fin.integration.base/mediation/uinext/list"}, {"label": "{{sidebar.integration.loadMatchingFunctions}}", "linkTo": "/templates/everest.fin.integration.base/loadingMatching/uinext/list"}, {"label": "{{sidebar.integration.mappingValidationFunctions}}", "linkTo": "/templates/everest.fin.integration.base/mappings/validationFunction/uinext/list"}, {"label": "{{sidebar.integration.valueMapper}}", "linkTo": "/templates/everest.fin.integration.base/mappings/uinext/valueMapperList"}, {"label": "{{sidebar.integration.valueHelpMapper}}", "linkTo": "/templates/everest.fin.integration.base/mappings/uinext/valueHelpMapperList"}, {"label": "{{sidebar.integration.outboundRegistry}}", "linkTo": "/templates/everest.fin.integration.base/outbound/registry/uinext/list"}, {"label": "{{sidebar.integration.outboundStagingSync}}", "linkTo": "/templates/everest.fin.integration.base/outbound/staging/uinext/list"}, {"label": "{{sidebar.integration.outboundStagingLog}}", "linkTo": "/templates/everest.fin.integration.base/outbound/staging/uinext/log"}, {"label": "{{sidebar.integration.outgoingWebhooks}}", "linkTo": "/templates/everest.fin.integration.selfserve/uinext/webhookReceiverList"}, {"label": "{{sidebar.integration.outgoingWebhookSpecs}}", "linkTo": "/templates/everest.fin.integration.selfserve/uinext/webhookSpecList"}, {"label": "{{sidebar.integration.statistics}}", "linkTo": "/templates/everest.fin.integration.base/statistics/uinext/list"}, {"label": "{{sidebar.integration.documentation}}", "linkTo": "/templates/everest.fin.integration.base/documentation/uinext/docs"}, {"label": "{{sidebar.integration.accessToken}}", "linkTo": "/templates/everest.base.ui/accessToken/uinext/systemTokenList"}, {"label": "{{sidebar.integration.integrationLandscape}}", "linkTo": "@uicode:integrationsLandscape"}]}, {"title": "{{sidebar.dataExtraction}}", "groupItems": [{"label": "{{sidebar.dataExtraction.adp}}", "linkTo": "/templates/everest.fin.integration.adp/uinext/overview?providerName=ADP"}, {"label": "{{sidebar.dataExtraction.airbase}}", "linkTo": "/templates/everest.fin.integration.expense/uinext/overview?providerName=Airbase"}, {"label": "{{sidebar.dataExtraction.avalara}}", "linkTo": "/templates/everest.fin.integration.avalara/uinext/setting"}, {"label": "{{sidebar.dataExtraction.anrok}}", "linkTo": "/templates/everest.fin.integration.anrok/uinext/anrokSetting"}, {"label": "{{sidebar.dataExtraction.billDotCom}}", "linkTo": "/templates/everest.fin.integration.expense/uinext/overview?providerName=BillDotCom"}, {"label": "{{sidebar.dataExtraction.Brex}}", "linkTo": "/templates/everest.fin.integration.expense/uinext/overview?providerName=Brex"}, {"label": "{{sidebar.dataExtraction.csv}}", "linkTo": "/templates/everest.fin.integration.base/extractedData/uinext/list?providerName=CSV"}, {"label": "{{sidebar.dataExtraction.Expensify}}", "linkTo": "/templates/everest.fin.integration.base.ui/uinext/overview/overview?providerName=Expensify"}, {"label": "{{sidebar.dataExtraction.gobd}}", "linkTo": "/templates/everest.fin.integration.taxation.germany/goBDExport/uinext/goBD"}, {"label": "{{sidebar.dataExtraction.gusto}}", "linkTo": "/templates/everest.fin.integration.expense/uinext/overview?providerName=Gusto"}, {"label": "{{sidebar.dataExtraction.intacct}}", "linkTo": "/templates/everest.fin.integration.base.ui/uinext/overview/overview?providerName=Intacct"}, {"label": "{{sidebar.dataExtraction.lexware}}", "linkTo": "/templates/everest.fin.integration.base.ui/uinext/overview/overview?providerName=Lexware"}, {"label": "{{sidebar.dataExtraction.navan}}", "linkTo": "/templates/everest.fin.integration.expense/uinext/overview?providerName=NavanCSV"}, {"label": "{{sidebar.dataExtraction.netsuite}}", "linkTo": "/templates/everest.migration.base/uinext/overview?providerName=NetSuite"}, {"label": "{{sidebar.dataExtraction.quickbooks}}", "linkTo": "/templates/everest.migration.base/uinext/overview?providerName=Quickbooks"}, {"label": "{{sidebar.dataExtraction.ramp}}", "linkTo": "/templates/everest.fin.integration.expense/uinext/overview?providerName=Ramp"}, {"label": "{{sidebar.dataExtraction.rippling}}", "linkTo": "/templates/everest.migration.base/uinext/overview?providerName=Rippling"}, {"label": "{{sidebar.dataExtraction.ruddr}}", "linkTo": "/templates/everest.fin.integration.base.ui/uinext/overview/overview?providerName=Ruddr"}, {"label": "{{sidebar.dataExtraction.salesforce}}", "linkTo": "/templates/everest.fin.integration.salesforce/uinext/overview"}, {"label": "{{sidebar.dataExtraction.stripe.overview}}", "linkTo": "/templates/everest.fin.integration.stripe/uinext/overview"}, {"label": "{{sidebar.dataExtraction.xml}}", "linkTo": "/templates/everest.fin.integration.base/extractedData/uinext/list?providerName=XML"}, {"label": "{{sidebar.dataExtraction.overview}}", "linkTo": "/templates/everest.fin.integration.base/extractedData/uinext/list"}]}, {"title": "{{sidebar.configurations}}", "groupItems": [{"label": "{{sidebar.integration.settings}}", "linkTo": "/templates/everest.fin.integration.base/uinext/settings"}, {"label": "{{sidebar.integration.extractionActions}}", "linkTo": "/templates/everest.fin.integration.base/inbound/uinext/extractionAction/list"}, {"label": "{{sidebar.integration.setDescriptionActions}}", "linkTo": "/templates/everest.fin.integration.base/inbound/uinext/description/list"}, {"label": "{{sidebar.integration.unifiedLoadFunctionConfigurations}}", "linkTo": "/templates/everest.fin.integration.base/inbound/uinext/unifiedLoadFunctionConfiguration/list"}, {"label": "{{sidebar.integration.etlConfigurations}}", "linkTo": "/templates/everest.fin.integration.base/inbound/uinext/etlConfiguration/list"}, {"label": "{{sidebar.integration.integrationCollections}}", "linkTo": "/templates/everest.fin.integration.base/inbound/uinext/collections/list"}, {"label": "{{sidebar.integration.externalIntegrationSettings}}", "linkTo": "/templates/everest.fin.integration.base/uinext/list"}]}, {"title": "AI", "groupItems": [{"label": "Extraction EVE", "linkTo": "/templates/everest.fin.integration.base/mappings/uinext/extractionAgent"}]}, {"title": "{{sidebar.selfServe}}", "groupItems": [{"label": "{{sidebar.apiEndpoints}}", "linkTo": "/templates/everest.fin.integration.selfserve/uinext/endpoints"}]}]}}]}, {"title": "{{sidebar.customerAdminGroup}}", "groupItems": [{"icon": "group", "label": "{{sidebar.customerAdmin.userList}}", "linkTo": "@uicode:customerUserList"}]}, {"title": "{{sidebar.devZone}}", "groupItems": [{"icon": "asset-management", "label": "{{sidebar.adminTools}}", "submenu": {"title": "{{sidebar.adminTools}}", "linkGroups": [{"title": "{{sidebar.application}}", "groupItems": [{"label": "{{sidebar.adminTool}}", "linkTo": "@uicode:admintool"}, {"label": "{{sidebar.jobScheduler}}", "linkTo": "/templates/everest.base.ui/jobScheduler/uinext/backgroundJob/list"}, {"label": "{{sidebar.tasks}}", "linkTo": "/templates/everest.base.ui/jobSchedulerV2/uinext/adminPlane"}, {"label": "{{sidebar.monitoring}}", "linkTo": "@uicode:monitoring"}, {"label": "{{sidebar.notifications}}", "linkTo": "/templates/everest.base.notifications/uinext/notifications"}, {"label": "{{sidebar.notificationTopics}}", "linkTo": "/templates/everest.base.notifications/uinext/notificationTopicList"}, {"label": "{{sidebar.pdfTemplates}}", "linkTo": "@uicode:pdfTemplates"}, {"label": "{{sidebar.systemConfiguration}}", "linkTo": "/templates/everest.appserver/systemConfiguration/list"}, {"label": "{{sidebar.systemHealth}}", "linkTo": "/templates/everest.base.ui/systemHealth/uinext/list"}]}, {"title": "{{sidebar.permissionManagement}}", "groupItems": [{"label": "{{sidebar.permissionManagement.permissions}}", "linkTo": "/templates/everest.base.ui/permission/uinext/overview"}, {"label": "{{sidebar.permissionManagement.allowList}}", "linkTo": "/templates/everest.base.ui/permission/availablePermissions/list/list"}, {"label": "{{sidebar.permissionmanagement.rowLevelSecurity}}", "linkTo": "/templates/everest.base.ui/rls/overview/rlsOverview"}]}, {"title": "{{sidebar.userManagement}}", "groupItems": [{"label": "{{sidebar.userManagement}}", "linkTo": "@uicode:activeUsersList"}, {"label": "{{sidebar.trialUserManagement}}", "linkTo": "/templates/everest.base.ui/userManagement/trialUsers/list/listTrialUsers"}, {"label": "{{sidebar.userManagementRoleReport}}", "linkTo": "/templates/everest.base.ui/userManagement/userListWithRoles/userRoleReport?feat-delta=true"}, {"label": "{{sidebar.userSession}}", "linkTo": "/templates/everest.base/session/uinext/userSessionList"}]}, {"title": "{{sidebar.metadata.others}}", "groupItems": [{"label": "{{sidebar.runningOperations}}", "linkTo": "/templates/everest.appserver/uinext/runningOperations"}, {"label": "{{sidebar.glExports}}", "linkTo": "/templates/everest.base/uinext/exports"}, {"label": "{{sidebar.masterKey.title}}", "linkTo": "/templates/everest.base.encryption/uinext/masterKeyMgmt"}, {"label": "{{sidebar.label.management}}", "linkTo": "/templates/everest.hr.base/uinext/labels/labels"}]}, {"title": "{{sidebar.admin.trusteddebugging}}", "groupItems": [{"label": "{{sidebar..admin.trusteddebugging.scenarios}}", "linkTo": "@uicode:debuggable-sandbox-scenarios"}, {"label": "{{sidebar.position.debuggableSessions}}", "linkTo": "@uicode:debuggableSessions"}]}, {"title": "{{sidebar.admin.internalEngines}}", "groupItems": [{"label": "{{sidebar.admin.accountingStandardList}}", "linkTo": "@uicode:accountingStandardList"}, {"label": "{{sidebar.admin.finDataProcessor}}", "linkTo": "@uicode:finDataProcWorkbench"}]}]}}, {"icon": "code-editor", "label": "{{sidebar.developer.title}}", "submenu": {"title": "{{sidebar.developer.title}}", "linkGroups": [{"title": "{{sidebar.developer.content}}", "groupItems": [{"label": "{{sidebar.codeChanges}}", "linkTo": "@uicode:sandboxCodeChanges"}, {"label": "{{sidebar.liveTemplate}}", "linkTo": "@uicode:liveTemplate"}, {"label": "{{sidebar.testUi}}", "linkTo": "@uicode:testui"}, {"label": "{{sidebar.validationEditor}}", "linkTo": "@uicode:validationEditor"}, {"label": "{{sidebar.observability}}", "linkTo": "/templates/everest.base.ui/observability/uinext/configurations"}, {"label": "{{sidebar.profiling}}", "linkTo": "/templates/everest.base.ui/profiling/uinext/profiling"}, {"label": "{{sidebar.uiNextSamples}}", "linkTo": "/templates/everest.base/uinext/samples"}, {"label": "{{sidebar.pythonPlayground}}", "linkTo": "/templates/everest.python.playground/uinext/playground"}, {"label": "{{sidebar.formulaPlayground}}", "linkTo": "/templates/everest.application-sdk.test/formulaPlayground/ui/playground?feat-delta=true"}, {"label": "{{sidebar.dataGenerators}}", "linkTo": "/templates/everest.base.ui/dataGenerators/uinext/dataGenerators"}, {"label": "{{sidebar.testDataRecorder}}", "linkTo": "/templates/everest.base.macrorecorder/uinext/macroRecorderHeaders"}, {"label": "{{sidebar.formulaDeveloper}}", "linkTo": "/templates/everest.base/uinext/formula"}, {"label": "Ad-hoc-Flow Editor", "linkTo": "@uicode:red"}]}, {"title": "{{sidebar.storybook}}", "groupItems": [{"label": "{{sidebar.templateStorybook}}", "linkTo": "/templates/everest.base.ui/sample/uinext/storybook"}, {"label": "{{sidebar.fieldGroupElementsStorybook}}", "linkTo": "/templates/everest.base.ui/sample/uinext/fieldGroupElementsStorybook"}, {"label": "{{sidebar.tableStorybook}}", "linkTo": "/templates/everest.base.ui/sample/uinext/tableStorybook"}, {"label": "{{sidebar.iconsStorybook}}", "linkTo": "/templates/everest.base.ui/sample/uinext/iconsStorybook"}, {"label": "{{sidebar.layoutsExamples}}", "linkTo": "/templates/everest.base.ui/sample/uinext/layoutStorybook"}, {"label": "{{sidebar.analytics}}", "linkTo": "/templates/everest.analytics.test/uinext/storybook"}, {"label": "{{sidebar.pythonApi}}", "linkTo": "/templates/everest.python/uinext/pythonApi"}, {"label": "{{sidebar.customTypes}}", "linkTo": "/templates/everest.base.ui/sample/uinext/customTypes"}, {"label": "{{sidebar.uiCodes}}", "linkTo": "/templates/everest.base.ui/sample/uinext/uicodes"}, {"label": "{{sidebar.uiFragments}}", "linkTo": "/templates/everest.base.ui/sample/uinext/uifragments"}, {"label": "{{sidebar.lotseDemo}}", "linkTo": "/templates/everest.base.ui/sample/uinext/applicationEngineStorybookDemo?feat-delta=true"}, {"label": "{{sidebar.presentations}}", "linkTo": "/templates/everest.base.presentation.demo/storybook/start"}]}, {"title": "{{sidebar.developer.mergeRequests}}", "groupItems": [{"label": "{{sidebar.mergeRequests}}", "linkTo": "@uicode:mergeRequests"}, {"label": "{{sidebar.mergesToMain}}", "linkTo": "/templates/everest.base.ui/merges/uinext/mergesToMain"}, {"label": "{{sidebar.mergeRequestLifecycle}}", "linkTo": "/templates/everest.base.ui/mergeRequestLifecycle/uinext/view"}, {"label": "{{sidebar.mergerRequestIssues}}", "linkTo": "/templates/everest.base.qa/uinext/requests/issues"}]}, {"title": "{{sidebar.developer.sos}}", "groupItems": [{"label": "{{sidebar.contextHelp}}", "linkTo": "/templates/everest.base.ui/sample/uinext/contexthelp"}]}]}}, {"icon": "tree", "label": "{{sidebar.metadata}}", "submenu": {"title": "{{sidebar.metadata}}", "linkGroups": [{"title": "{{sidebar.metadata.global}}", "groupItems": [{"label": "{{sidebar.everestArtifact}}", "linkTo": "/templates/everest.appserver/metadata/uinext/everestArtifact/list"}, {"label": "{{sidebar.everestAssociationType}}", "linkTo": "/templates/everest.appserver/metadata/uinext/everestAssociationType/list"}, {"label": "{{sidebar.everestMetadataCommit}}", "linkTo": "/templates/everest.appserver/metadata/uinext/everestMetadataCommit/list"}, {"label": "{{sidebar.everestPackageManagement}}", "linkTo": "@uicode:packageManagement"}, {"label": "{{sidebar.everestUiCodes}}", "linkTo": "/templates/everest.appserver/metadata/uinext/everestUICode/list"}, {"label": "{{sidebar.everestTenants}}", "linkTo": "/templates/everest.appserver/metadata/uinext/everestTenant/list"}]}, {"title": "{{sidebar.metadata.package}}", "groupItems": [{"label": "{{sidebar.everestAssociation}}", "linkTo": "/templates/everest.appserver/metadata/uinext/everestAssociation/list"}, {"label": "{{sidebar.everestAssociationTarget}}", "linkTo": "/templates/everest.appserver/metadata/uinext/everestAssociationTarget/list"}, {"label": "{{sidebar.everestNode}}", "linkTo": "/templates/everest.appserver/metadata/uinext/everestNode/list"}, {"label": "{{sidebar.everestNumberRange}}", "linkTo": "/templates/everest.appserver/metadata/uinext/everestNumberRange/list"}, {"label": "{{sidebar.everestRelationshipTarget}}", "linkTo": "/templates/everest.appserver/metadata/uinext/everestRelationshipTarget/list"}, {"label": "{{sidebar.everestSampleDataHeader}}", "linkTo": "/templates/everest.appserver/metadata/uinext/everestSampleDataHeader/list"}, {"label": "{{sidebar.everestSeedDataHeader}}", "linkTo": "/templates/everest.appserver/metadata/uinext/everestSeedDataHeader/list"}, {"label": "{{sidebar.everestValueHelp}}", "linkTo": "/templates/everest.appserver/metadata/uinext/everestValueHelp/list"}, {"label": "{{sidebar.translations}}", "linkTo": "/templates/everest.base.translation/uinext/translationContext/list"}, {"label": "{{sidebar.workflows}}", "linkTo": "/templates/everest.appserver/uinext/flows"}, {"label": "{{sidebar.processes}}", "linkTo": "/templates/everest.base.process/uinext/processDefinitions"}, {"label": "{{sidebar.packagedSandboxes}}", "linkTo": "/templates/everest.base.ui/packagedSandbox/uinext/list"}]}, {"title": "{{sidebar.metadata.others}}", "groupItems": [{"label": "{{sidebar.everestFormulaFunction}}", "linkTo": "/templates/everest.base.ui/everestFormulaFunction/uinext/list"}, {"label": "{{sidebar.nodeCleanup}}", "linkTo": "/templates/everest.base.ui/nodeCleanup/uinext/nodeCleanup"}, {"label": "{{sidebar.expansions.dashboard}}", "linkTo": "/templates/everest.base.expansions/uinext/expansions"}, {"label": "{{sidebar.connectivityLayer}}", "linkTo": "/templates/everest.appserver/metadata/uinext/everestPackageInterface/list"}]}, {"title": "{{sidebar.metadata.typesystem}}", "groupItems": [{"label": "{{sidebar.metadata.typesystem.primitiveTypes}}", "linkTo": "/templates/everest.appserver/metadatacatalog/uinext/primitiveType/list"}, {"label": "{{sidebar.metadata.typesystem.compositeTypes}}", "linkTo": "/templates/everest.appserver/metadatacatalog/uinext/compositeType/list"}]}]}}, {"icon": "accounts-receivable", "label": "{{sidebar.testing.overview}}", "submenu": {"title": "{{sidebar.testing.overview}}", "linkGroups": [{"title": "{{sidebar.testing.static}}", "groupItems": [{"label": "{{sidebar.testing.integration.lint}}", "linkTo": "/templates/everest.base.test/uinext/static/lintCheckRuns"}, {"label": "{{sidebar.testing.integration.permission}}", "linkTo": "/templates/everest.base.test/uinext/static/permissionTestRuns"}]}, {"title": "{{sidebar.testing.integration}}", "groupItems": [{"label": "{{sidebar.testing.integration.integration}}", "linkTo": "/templates/everest.base.test/uinext/integration/integrationRuns"}, {"label": "{{sidebar.testing.integration.execution}}", "linkTo": "/templates/everest.base.test/uinext/integration/migrationTestRuns"}, {"label": "{{sidebar.testing.integration.metrics}}", "linkTo": "/templates/everest.base.test/uinext/integration/integration_metrics"}]}, {"title": "{{sidebar.testing.e2e}}", "groupItems": [{"label": "{{sidebar.testing.e2e.execution}}", "linkTo": "/templates/everest.base.test/uinext/executionOverview"}, {"label": "{{sidebar.testing.e2e.suites}}", "linkTo": "/templates/everest.base.test/uinext/overview"}, {"label": "{{sidebar.testing.e2e.tags}}", "linkTo": "/templates/everest.base.test/uinext/test_tag"}, {"label": "{{sidebar.testing.e2e.mrConfig}}", "linkTo": "/templates/everest.base.test/uinext/config/view"}, {"label": "{{sidebar.testing.e2e.quickExecutionTools}}", "linkTo": "/templates/everest.base.test/uinext/quickAccess/overview"}, {"label": "{{sidebar.testing.tracking.uat}}", "linkTo": "/templates/everest.base.qa.uat/uinext/catalog/catalogList"}]}, {"title": "{{sidebar.testing.automation.plans}}", "groupItems": [{"label": "{{sidebar.testing.automation.lint}}", "linkTo": "/templates/everest.base.test/uinext/schedulingv2/overview?type=Lint"}, {"label": "{{sidebar.testing.automation.permission}}", "linkTo": "/templates/everest.base.test/uinext/schedulingv2/overview?type=Permission"}, {"label": "{{sidebar.testing.automation.integration}}", "linkTo": "/templates/everest.base.test/uinext/schedulingv2/overview?type=Integration"}, {"label": "{{sidebar.testing.automation.uicontroller}}", "linkTo": "/templates/everest.base.test/uinext/schedulingv2/overview?type=UIController"}, {"label": "{{sidebar.testing.automation.e2e}}", "linkTo": "/templates/everest.base.test/uinext/schedulingv2/overview?type=E2E"}]}, {"title": "{{sidebar.testing.tracking}}", "groupItems": [{"label": "{{sidebar.testing.tracking.applicationDashboard}}", "linkTo": "/templates/everest.base.qa/uinext/dashboard/ApplicationSummary"}, {"label": "{{sidebar.testing.e2e.getFlakyE2E}}", "linkTo": "/templates/everest.base.test/uinext/flakiness/getFlakyE2ETestRuns"}, {"label": "{{sidebar.testing.tracking.scalingStatus}}", "linkTo": "/templates/everest.benchmark/uinext/scalingStatus"}]}, {"title": "{{sidebar.testing.interactive}}", "groupItems": [{"label": "{{sidebar.testing.interactive.lotse.testValidationsUI}}", "linkTo": "/templates/everest.base.lotse/uinext/testValidationsUI"}, {"label": "{{sidebar.testing.e2e.aiTools.pomGenerator}}", "linkTo": "/templates/everest.base.qa.gen/uinext/experimentalPomGen"}, {"label": "{{sidebar.testing.e2e.aiTools.fullGenFlow}}", "linkTo": "/templates/everest.base.qa.gen/uinext/experimentalContextGen"}, {"label": "{{sidebar.testing.e2e.aiTools.results}}", "linkTo": "/templates/everest.base.qa.gen/uinext/results/list"}, {"label": "{{sidebar.testing.e2e.aiTools.corrections}}", "linkTo": "/templates/everest.base.qa.gen/uinext/corrections/list"}]}]}}, {"icon": "asset-management", "label": "{{sidebar.releasing}}", "submenu": {"title": "{{sidebar.releasing}}", "linkGroups": [{"title": "{{sidebar.baselines}}", "groupItems": [{"label": "{{sidebar.testing.baseline.releases}}", "linkTo": "/templates/everest.baseline/uinext/release/list"}, {"label": "{{sidebar.testing.baseline.baselines}}", "linkTo": "/templates/everest.baseline/uinext/baseline/list"}]}, {"title": "{{sidebar.fingerprints}}", "groupItems": [{"label": "{{sidebar.testing.baseline.fingerprints}}", "linkTo": "/templates/everest.baseline/uinext/fingerprint/list"}]}, {"title": "{{sidebar.deployments}}", "groupItems": [{"label": "{{sidebar.testing.baseline.deploymentTargets}}", "linkTo": "/templates/everest.baseline/uinext/deployments/list"}]}]}}, {"title": "{{sidebar.eve.title}}", "groupItems": [{"icon": "people", "label": "{{sidebar.eve.ai}}", "submenu": {"title": "{{sidebar.eve.ai}}", "linkGroups": [{"title": "{{sidebar.eve.data}}", "groupItems": [{"label": "{{sidebar.eve.documents.dashboard}}", "linkTo": "/templates/everest.eve/uinext/dashboard"}, {"label": "{{sidebar.eve.drive}}", "linkTo": "/templates/everest.eve/uinext/googleDrive/googleDrive"}, {"label": "{{sidebar.eve.github.documents.sidebar}}", "linkTo": "/templates/everest.eve/uinext/gitHub/gitHub"}]}, {"title": "{{sidebar.eve.dev}}", "groupItems": [{"label": "{{sidebar.eve.dev.tools}}", "linkTo": "/templates/everest.eve/uinext/dev/devTools"}, {"label": "{{sidebar.eve.dev.chatcompare}}", "linkTo": "/templates/everest.eve/uinext/dev/llmSpam"}, {"label": "{{sidebar.eve.continue.documents.sidebar}}", "linkTo": "/templates/everest.eve/uinext/continue/continueDocuments"}, {"label": "{{sidebar.eve.continue.codebase.sidebar}}", "linkTo": "/templates/everest.eve/uinext/continue/continueCodebase"}]}, {"title": "{{sidebar.eve.promptmgmt}}", "groupItems": [{"label": "{{sidebar.eve.promptmgmt.managment}}", "linkTo": "/templates/everest.eve/uinext/prompts"}, {"label": "{{sidebar.eve.reportmgmt.sidebar}}", "linkTo": "/templates/everest.eve/uinext/reportMgmt"}, {"label": "{{sidebar.eve.snippetmgmt}}", "linkTo": "/templates/everest.eve/uinext/prompts/snippets"}, {"label": "{{sidebar.eve.reportmgmt.testing}}", "linkTo": "/templates/everest.eve/uinext/internalTesting/reports"}]}, {"title": "{{sidebar.eve.chat}}", "groupItems": [{"label": "{{sidebar.eve.chatmgmt.sidebar}}", "linkTo": "/templates/everest.eve/uinext/chatMgmt"}]}]}}]}, {"title": "{{sidebar.compliance}}", "visible": "@controller:isW<PERSON><PERSON>blower<PERSON>ser()", "groupItems": [{"icon": "shield", "label": "{{sidebar.whistleblowing}}", "submenu": {"title": "{{sidebar.whistleblowing}}", "linkGroups": [{"title": "Incidents", "groupItems": [{"label": "{{sidebar.Whistleblower}}", "linkTo": "/templates/everest.whistleblower/uinext/whistleblower"}]}, {"groupItems": [{"label": "{{sidebar.reporting.officer}}", "linkTo": "/templates/everest.whistleblower/uinext/caseList"}]}]}}]}]}]}}