{"version": 2, "uicontroller": ["settingsAndPolicies.uicontroller.ts", "settingsAndPolicies.permissions.uicontroller.ts"], "uimodel": {"nodes": {"permissions": {"type": "list", "model": "urn:evst:everest:appserver:model/node:permission/Policy", "checkPermissionsInBatch": "@controller:PermissionQuery.getPermissionRequest()", "fieldList": []}, "entities": {"type": "list", "modelId": "everest.base/EntityModel.Entity", "fieldList": ["id", "entityName"]}, "entityStatusValueHelp": {"type": "list", "model": "urn:evst:everest:appserver:model/node:ValueHelp", "query": {"where": {"urn": "urn:evst:everest:base:enum:EntityStatus", "language": "@controller:getUserLanguage()"}}, "fieldList": ["codeValue", "text"]}, "entityCoAs": {"type": "list", "query": "@controller:getEntityBookQuery()", "modelId": "everest.fin.accounting/EntityBookModel.EntityBook", "fieldList": ["id", "entityId", "chartOfAccountsId", "bookId", "Book.name", "ChartOfAccounts.name"]}, "chartOfAccounts": {"type": "list", "query": "@controller:getChartOfAccountsQuery()", "modelId": "everest.fin.accounting/ChartOfAccountsModel.ChartOfAccounts", "fieldList": ["id", "name", "entityNames", "AllocationPolicy-ChartOfAccounts.id", "AllocationPolicy-ChartOfAccounts.policyName"]}, "postAllocationAccounts": {"type": "list", "query": "@controller:getPostAllocationAccountQuery()", "modelId": "everest.fin.accounting/PostAllocationAccountModel.PostAllocationAccount", "fieldList": ["id", "entityId", "allocationIn", "allocationOut", "PostAllocationAccount-AllocationIn.accountNumber", "PostAllocationAccount-AllocationIn.accountName", "PostAllocationAccount-AllocationOut.accountName", "PostAllocationAccount-AllocationOut.accountNumber"]}, "specifyBuildEnabledOption": {"type": "struct", "modelId": "everest.base/FeatureToggleModel.FeatureToggle", "query": "@controller:getAllowAISpecifyBuildQuery()", "fieldList": ["enabled"]}, "allowSalesOrderMassApprovalOption": {"type": "struct", "modelId": "everest.base/FeatureToggleModel.FeatureToggle", "query": "@controller:getAllowSalesOrderMassApprovalOptionQuery()", "fieldList": ["enabled"]}, "allowModifyCancelledSubscriptionOption": {"type": "struct", "modelId": "everest.base/FeatureToggleModel.FeatureToggle", "query": "@controller:getAllowModifyCancelledSubscriptionOptionQuery()", "fieldList": ["enabled"]}, "migrationRevenueCloseProcessReverseOption": {"type": "struct", "modelId": "everest.base/FeatureToggleModel.FeatureToggle", "query": "@controller:getMigrationRevenueCloseProcessReverseOptionQuery()", "fieldList": ["enabled"]}, "migrationSuppressRevenuePlanCreationOption": {"type": "struct", "modelId": "everest.base/FeatureToggleModel.FeatureToggle", "query": "@controller:getMigrationSuppressRevenuePlanCreationOptionQuery()", "fieldList": ["enabled"]}, "vendorSettings": {"type": "struct", "query": "@controller:getVendorSettingsQuery()", "modelId": "everest.fin.expense/VendorSettingsModel.VendorSettings", "fieldList": ["key", "value"]}, "outboundPaymentSettings": {"type": "struct", "query": "@controller:getOutboundPaymentSettingsQuery()", "modelId": "everest.fin.expense/VendorSettingsModel.VendorSettings", "fieldList": ["key", "value"]}, "arrSetting": {"type": "struct", "modelId": "everest.fin.accounting/SubscriptionARRAdjustmentSettingModel.SubscriptionARRAdjustmentSetting", "query": "@controller:getArrSettingQuery()"}, "businessUnits": {"type": "list", "model": "urn:evst:everest:base:model/node:BusinessUnit", "query": "@controller:getBusinessUnitsQuery()", "fieldList": ["id", "name"]}, "nonLegalReportingEntity": {"type": "struct", "modelId": "everest.base/EntityModel.Entity", "query": "@controller:getNonLegalReportingEntityQuery()", "fieldList": ["id"]}, "businessUnitEnabled": {"modelId": "everest.appserver/ConfigurationModel.Configuration", "type": "struct", "getValueOrDefault": {"name": "BUSINESS_UNIT_ENABLED", "packageName": "everest.base", "defaultValue": true}, "fieldList": ["value"]}, "isAccountingBookEnabled": {"modelId": "everest.fin.accounting/EntityBookModel.EntityBook", "type": "struct", "isMultiBookEnabled": "@controller:getIsMultiBookEnabledQuery()"}, "accountingBooks": {"modelId": "everest.fin.accounting/BookModel.Book", "type": "list", "query": "@controller:getAccounting<PERSON><PERSON>s<PERSON>uery()", "fieldList": ["id", "name", "type", "description", "allEntities", "EntityBook.Entity.entityName", "EntityBook.hasTransactionCreated"]}, "entityBook": {"modelId": "everest.fin.accounting/EntityBookModel.EntityBook", "type": "struct"}, "user": {"modelId": "everest.appserver.usermgmt/UserModel.User", "type": "struct", "query": "@controller:getUserQuery()", "fieldList": ["id", "email", "fullName", "userId", "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "employee": {"type": "struct", "model": "urn:evst:everest:hr/base:model/node:Employee", "getCurrentEmployee": {}, "fieldList": ["id", "employeeNumber", "displayName", "photo"]}, "userConfig": {"type": "list", "modelId": "everest.appserver/userConfig/UserConfigModel.UserConfig", "queryUserConfig": {}}, "tokenManagement": {"type": "list", "modelId": "everest.appserver.usermgmt/AccessTokenManagementModel.AccessTokenManagement", "model": "urn:evst:everest:appserver/usermgmt:model/node:AccessTokenManagement", "queryPersonalTokens": {"orderBy": ["name"]}, "fieldList": ["id", "name", "expireAt", "tokenId", "allowedServices"]}, "bookTypes": {"type": "list", "query": {"where": {"dataType": "BookType", "language": "en"}}, "model": "urn:evst:everest:appserver:model/node:ValueHelp", "fieldList": ["codeValue", "text"]}, "defaultEntityBookSettings": {"modelId": "everest.fin.accounting/DefaultEntityBookSettingsModel.DefaultEntityBookSettings", "type": "struct", "getDefaultTransactionBookIds": "@controller:getDefaultTransactionBooksQuery()"}}}, "uiview": {"i18n": ["settingsAndPolicies", "everest.base.ui/userManagement"], "tabTitle": "{{settingsAndPolicies.tabTitle}}", "config": {"stretch": true, "allowRefreshData": true}, "header": {"content": {"title": "{{settingsAndPolicies.title}}"}, "keyDataConfig": {"@uifragment": {"path": "everest.base/entity/uinext/entityPicker.uifragment.json", "props": {"listSingleEntities": true, "listConsolidatedEntities": false, "displayCurrency": false, "displaySectionLabels": false, "fieldsList": ["country"]}}}}, "sections": {"content": [{"component": "Settings", "customId": "settingsAndPolicies", "section": {"editing": true}, "props": {"activeSetting": "@state:activeSetting", "items": [{"title": "{{settingsAndPolicies.userSettings.title}}", "groups": [{"title": "{{settingsAndPolicies.general}}", "id": "userSettings-title", "groups": [{"title": "{{settingsAndPolicies.userSettings.account}}", "id": "userSettings-account", "settings": [{"section": {"title": "{{settingsAndPolicies.userSettings.account.userDetails}}", "editing": "@controller:isPWUser()"}, "actions": {"variant": "default", "editing": true, "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "rightSide", "buttonActionsPosition": "right", "lines": [{"avatar": {"src": "@controller:getAvatarSrc()", "name": "@binding:user.fullName"}, "label": "@binding:user.fullName", "buttonActions": [{"visible": "@controller:UserSettingGroup.editUserDetailVisible()", "label": "{{settingsAndPolicies.edit}}", "disabled": "@controller:isInsideSandbox()", "tooltip": "@controller:getDisabledTooltip()", "onClick": "@controller:openUserEditDetailsModal"}], "subActionGroup": {"avatar": false, "descriptionPosition": "rightSide", "buttonActionsPosition": "@controller:getEditUserDetailsButtonActionsPosition()", "lines": [{"label": "{{settingsAndPolicies.email}}", "result": "@controller:getUserDetailsResult('email')", "description": "@controller:getUserDetailsDescription()", "buttonActions": "@controller:getEditUserDetailsAccountButtonActions('email')"}, {"label": "{{settingsAndPolicies.password}}", "result": "@controller:getUserDetailsResult('password')", "description": "@controller:getUserDetailsDescription()", "buttonActions": "@controller:getEditUserDetailsAccountButtonActions('password')"}]}}]}}, {"visible": "@controller:UserSettingGroup.employeePageVisible()", "section": {"title": "{{settingsAndPolicies.userSettings.account.assignments}}"}, "actions": {"variant": "default", "editing": true, "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": [{"label": "{{settingsAndPolicies.employeeRecord}}", "result": {"variant": "link", "content": [{"label": "@controller:getEmployeeLabel()", "to": "@controller:getEmployeeLink()"}]}, "buttonActions": [{"label": "{{settingsAndPolicies.open}}", "onClick": "@controller:navigateToEmployeePage"}]}]}}]}, {"title": "{{settingsAndPolicies.userSettings.preferences}}", "id": "userSettings-preferences", "visible": "@controller:UserSettingGroup.preferenceGroupVisible()", "settings": [{"visible": "@controller:UserSettingGroup.preferenceModalVisible()", "section": {"title": "{{settingsAndPolicies.userSettings.preferences.localization}}"}, "actions": {"variant": "default", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "rightSide", "buttonActionsPosition": "right", "lines": [{"label": "{{settingsAndPolicies.userSettings.preferences.language}}", "result": "@controller:getUserDetailsResult('language')", "buttonActions": "@controller:getEditUserDetailsButtonActions('language')"}]}}, {"visible": "@controller:UserSettingGroup.defaultEntityModalVisible()", "section": {"title": "{{settingsAndPolicies.recordToReport.preferences.reporting}}"}, "actions": {"variant": "default", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "buttonActionsPosition": "right", "lines": [{"label": "{{settingsAndPolicies.recordToReport.preferences.defaultEntity}}", "description": "{{settingsAndPolicies.recordToReport.preferences.defaultEntityDescription}}", "result": "@controller:getDefaultEntityPreferenceSettingValueText()", "buttonActions": [{"label": "{{settingsAndPolicies.edit}}", "onClick": "@controller:openDefaultEntityPreferenceModal"}]}]}}, {"visible": "@controller:UserSettingGroup.isTransactionalBookSettingsVisible()", "section": {"title": "{{settingsAndPolicies.recordToReport.preferences.multibookTransactions}}"}, "actions": {"variant": "default", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "buttonActionsPosition": "right", "lines": [{"label": "{{settingsAndPolicies.recordToReport.preferences.transactionalBookContext}}", "description": "{{settingsAndPolicies.recordToReport.preferences.selectBookForFinancialDocuments}}", "result": "@controller:getDefaultEntityBookPreferenceValueText()", "buttonActions": [{"label": "{{settingsAndPolicies.edit}}", "onClick": "@controller:openDefaultTransactionalBookPreferenceModal"}]}]}}, {"visible": "@controller:UserSettingGroup.preferenceFieldsVisible()", "section": {"title": "{{settingsAndPolicies.userSettings.preferences.formatting}}"}, "actions": {"variant": "default", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "rightSide", "buttonActionsPosition": "right", "lines": [{"label": "{{settingsAndPolicies.userSettings.preferences.dateFormat}}", "result": "@controller:getUserDetailsResult('dateFormat')", "buttonActions": "@controller:getEditUserDetailsButtonActions('dateFormat')"}, {"label": "{{settingsAndPolicies.userSettings.preferences.timeFormat}}", "result": "@controller:getUserDetailsResult('timeFormat')", "buttonActions": "@controller:getEditUserDetailsButtonActions('timeFormat')"}, {"label": "{{settingsAndPolicies.userSettings.preferences.currencyFormat}}", "result": "@controller:getUserDetailsResult('currencyFormat')", "buttonActions": "@controller:getEditUserDetailsButtonActions('currencyFormat')"}]}}]}, {"title": "{{settingsAndPolicies.userSettings.security}}", "id": "userSettings-security", "visible": "@controller:UserSettingGroup.createTokenModalVisible()", "settings": [{"section": {"title": "{{settingsAndPolicies.userSettings.security.personalAccessTokens}}", "actions": [{"label": "{{settingsAndPolicies.create}}", "variant": "primary", "disabled": "@controller:isCreateTokenDisabled()", "tooltip": "@controller:getCreateTokenDisabledTooltip()", "onClick": "@controller:createToken"}]}, "actions": {"variant": "default", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "rightSide", "buttonActionsPosition": "right", "lines": "@controller:getTokenLines()"}}]}]}]}, {"title": "{{settingsAndPolicies.applicationSettings}}", "visible": "@controller:ApplicationSettings.applicationSettingsGroupVisible()", "groups": [{"title": "{{settingsAndPolicies.organization}}", "visible": "@controller:OrganizationGroup.orgGroupVisible()", "id": "organization", "groups": [{"title": "{{settingsAndPolicies.organization}}", "visible": "@controller:OrganizationGroup.orgGroupVisible()", "id": "organization.entities", "settings": [{"section": {"title": "{{settingsAndPolicies.recordToReport.configuration.title}}"}, "visible": "@controller:OrganizationGroup.configurationVisible()", "actions": {"variant": "default", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "buttonActionsPosition": "right", "lines": [{"label": "{{settingsAndPolicies.recordToReport.entities.rollUpLabel}}", "description": "{{settingsAndPolicies.recordToReport.entities.rollUpDescription}}", "result": "@controller:getNonLegalEntitySettingValueText()", "buttonActions": [{"label": "{{settingsAndPolicies.edit}}", "onClick": "@controller:editNonLegalEntitySetting"}]}, {"label": "{{settingsAndPolicies.recordToReport.books.multibook}}", "description": "{{settingsAndPolicies.recordToReport.books.multibookDescription}}", "result": "@controller:getMultibookOnOff()", "visible": "@controller:hasAccountingBookPermission()", "buttonActions": [{"label": "{{settingsAndPolicies.actions}}", "shape": "pill", "actions": [{"label": "@controller:getMultibookStatus()", "disabled": "@controller:isMultiBookStatusButtonDisabled()", "onClick": "@controller:toggle<PERSON><PERSON>unting<PERSON><PERSON>", "tooltip": "@controller:getMultiBookStatusButtonDisabledTooltip()"}]}]}, {"label": "Create multi-book scenario (for test purpose only)", "description": "⚠️ Please, run it ONCE only after the 'Clean import for Selected and Dependant Packages' in the Package Management page has been executed.", "visible": "@controller:canShowAccountingBookList()", "buttonActions": [{"label": "{{settingsAndPolicies.actions}}", "shape": "pill", "actions": [{"label": "Execute", "onClick": "@controller:createMultiBookStructure"}]}]}]}}, {"title": "", "id": "books", "visible": "@controller:canShowAccountingBookList()", "section": {"title": "{{settingsAndPolicies.recordToReport.books.title}}", "actions": [{"label": "{{settingsAndPolicies.create}}", "variant": "primary", "onClick": "@controller:openModalAccountingBookCreate"}]}, "actions": {"variant": "default", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": "@controller:getAccountingBooks()"}}, {"visible": "@controller:canShowBookAccountMappingList()", "section": {"editing": false, "title": "{{settingsAndPolicies.bookMappings}}", "actions": []}, "actions": {"variant": "default", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": "@controller:getAccountingBookMappings()"}}, {"section": {"title": "{{settingsAndPolicies.organization.entities}}", "actions": [{"label": "{{settingsAndPolicies.create}}", "variant": "primary", "onClick": "@controller:onCreateEntity"}]}, "visible": "@controller:OrganizationGroup.entityVisible()", "actions": {"variant": "default", "border": "none", "buttonActionsPosition": "right", "lines": "@controller:getEntityActionLines()"}}]}]}, {"title": "{{settingsAndPolicies.general}}", "visible": "@controller:GeneralGroup.generalGroupVisible()", "id": "0", "groups": [{"title": "{{settingsAndPolicies.general.communication}}", "visible": "@controller:GeneralGroup.communicationGroupVisible()", "id": "0.1", "settings": [{"section": {"title": "{{settingsAndPolicies.general.communication.email}}"}, "actions": {"variant": "default", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": [{"label": "{{settingsAndPolicies.general.communication.email.emailSettings}}", "description": "{{settingsAndPolicies.general.communication.email.emailSettings.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "disabled": "@controller:GeneralGroup.emailSettingsModalDisabled()", "onClick": "@controller:openEmailSettingsModal"}]}, {"label": "{{settingsAndPolicies.general.communication.email.pdfTemplates}}", "description": "{{settingsAndPolicies.general.communication.email.pdfTemplates.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "disabled": "@controller:GeneralGroup.pdfTemplateDisabled()", "onClick": "@controller:navigateToPDFTemplates"}]}, {"label": "{{settingsAndPolicies.general.communication.email.emailTemplates}}", "description": "{{settingsAndPolicies.general.communication.email.emailTemplates.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "disabled": "@controller:GeneralGroup.emailTemplateDisabled()", "onClick": "@controller:navigateToEmailTemplates"}]}, {"label": "{{settingsAndPolicies.general.communication.systemNotificationTopics}}", "description": "{{settingsAndPolicies.general.communication.manageSystemNotificationTopics}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "disabled": "@controller:GeneralGroup.notificationListDisabled()", "onClick": "@controller:navigateToSystemNotificationTopics"}]}]}}, {"section": {"title": "{{settingsAndPolicies.general.communication.slack}}"}, "actions": {"variant": "default", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": [{"label": "{{settingsAndPolicies.general.communication.slack.slackSettings}}", "description": "{{settingsAndPolicies.general.communication.email.slackSettings.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "disabled": "@controller:GeneralGroup.slackIntegrationDisabled()", "onClick": "@controller:openSlackSettings"}]}]}}]}, {"title": "{{settingsAndPolicies.general.saasMetricsSettings}}", "visible": "@controller:GeneralGroup.saasMetricsVisible()", "id": "0.2", "settings": [{"section": {"title": "{{settingsAndPolicies.general.saasMetrics.configurationModal}}"}, "actions": {"variant": "default", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": [{"label": "{{settingsAndPolicies.general.saasMetrics.configurationModal}}", "description": "{{settingsAndPolicies.general.saasMetrics.configurationModal.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "onClick": "@controller:openSaasMetricsExpenseTypeMappingModal"}]}, {"label": "{{settingsAndPolicies.general.saasMetrics.recalculateSalesOrderMetrics}}", "description": "{{settingsAndPolicies.general.saasMetrics.recalculateSalesOrderMetrics.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "onClick": "@controller:openSalesOrderMetricsRecalculationModal"}]}]}}]}]}, {"title": "{{settingsAndPolicies.banking}}", "id": "1", "visible": "@controller:BankingGroup.bankingGroupVisible()", "groups": [{"title": "{{settingsAndPolicies.banking.connections}}", "visible": "@controller:BankingGroup.connectionsGroupVisible()", "id": "1.1", "settings": [{"title": "{{settingsAndPolicies.banking.connections}}", "actions": {"variant": "avatar", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": [{"label": "{{settingsAndPolicies.banking.manageConnections}}", "buttonActions": [{"label": "{{settingsAndPolicies.banking.manage}}", "disabled": "@controller:BankingGroup.manageConnectionDisabled()", "onClick": "@controller:openManageConnectionsModal"}]}]}}]}, {"title": "{{settingsAndPolicies.banking.matching}}", "visible": "@controller:BankingGroup.matchingGroupVisible()", "id": "1.2", "settings": [{"title": "{{settingsAndPolicies.banking.matching}}", "actions": {"variant": "avatar", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": [{"label": "{{settingsAndPolicies.banking.transactionMatchingRules}}", "description": "{{settingsAndPolicies.banking.rulesDescription}}", "buttonActions": [{"label": "{{settingsAndPolicies.banking.manage}}", "disabled": "@controller:BankingGroup.configureMatchingRulesDisabled()", "onClick": "@controller:openMatchingRulesModal"}]}]}}]}, {"title": "{{settingsAndPolicies.banking.bankFees}}", "visible": "@controller:BankingGroup.feesGroupVisible()", "id": "1.3", "settings": [{"title": "{{settingsAndPolicies.banking.bankFeeAccounts}}", "section": {"title": "{{settingsAndPolicies.banking.bankFeeAccounts}}"}, "actions": {"variant": "avatar", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": "@controller:getBankFeeSettingMenuLines('accountSetting')"}}, {"title": "{{settingsAndPolicies.banking.bankFeeDepartments}}", "section": {"title": "{{settingsAndPolicies.banking.bankFeeDepartments}}"}, "actions": {"variant": "avatar", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": "@controller:getBankFeeSettingMenuLines('departmentSetting')"}}]}]}, {"title": "{{settingsAndPolicies.quoteToCash}}", "id": "2", "visible": "@controller:QuoteToCashGroup.qtcGroupVisible()", "groups": [{"title": "{{settingsAndPolicies.quoteToCash.subscription}}", "id": "2.1", "visible": "@controller:QuoteToCashGroup.arrSettingsVisible()", "settings": [{"title": "{{settingsAndPolicies.quoteToCash.subscription.settings}}", "section": {"editing": "@controller:QuoteToCashGroup.arrSettingEditable()", "title": "{{settingsAndPolicies.quoteToCash.subscription.settings}}"}, "actions": {"variant": "default", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": [{"label": "{{settingsAndPolicies.quoteToCash.arrAdjustment}}", "fieldProps": {"name": "ArrSetting", "component": "Switch", "value": "@controller:getArrSetting()", "direction": "horizontal-reverse", "onChange": "@controller:setArrSetting", "isDisabled": "@controller:isArrSettingsDisabled()"}}]}}]}, {"title": "{{settingsAndPolicies.quoteToCash.invoiceConfig}}", "id": "2.2", "visible": "@controller:QuoteToCashGroup.invoiceSessionVisible()", "settings": [{"title": "{{settingsAndPolicies.quoteToCash.invoiceConfig.defaultSettings}}", "section": {"editing": true, "title": "{{settingsAndPolicies.quoteToCash.invoiceConfig.defaultSettings}}"}, "actions": {"variant": "avatar", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": [{"label": "{{settingsAndPolicies.quoteToCash.invoiceConfig.defaultSettings.emailSendingAutomation.title}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "onClick": "@controller:onInvoiceEmailClick"}]}, {"label": "{{settingsAndPolicies.quoteToCash.invoiceConfig.defaultSettings.invoiceSettings.title}}", "description": "{{settingsAndPolicies.quoteToCash.invoiceConfig.defaultSettings.invoiceSettings.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "onClick": "@controller:onInvoiceConfigurationClick"}]}, {"label": "{{settingsAndPolicies.quoteToCash.invoiceConfig.defaultSettings.invoicePolicies.title}}", "description": "{{settingsAndPolicies.quoteToCash.invoiceConfig.defaultSettings.invoicePolicies.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "onClick": "@controller:onInvoicePoliciesClick"}]}, {"label": "{{settingsAndPolicies.quoteToCash.invoiceConfig.defaultSettings.invoiceTemplate.title}}", "description": "{{settingsAndPolicies.quoteToCash.invoiceConfig.defaultSettings.invoiceTemplate.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "onClick": "@controller:onInvoiceTemplateClick"}]}, {"label": "{{settingsAndPolicies.quoteToCash.invoiceConfig.defaultSettings.tax.title}}", "description": "{{settingsAndPolicies.quoteToCash.invoiceConfig.defaultSettings.tax.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "onClick": "@controller:onManageTaxEngineClick"}]}]}}]}, {"title": "{{settingsAndPolicies.quoteToCash.creditMemoConfig}}", "id": "2.3", "visible": "@controller:QuoteToCashGroup.creditCardMemoVisible()", "settings": [{"actions": {"variant": "avatar", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": [{"label": "{{settingsAndPolicies.quoteToCash.creditMemoConfig.defaultSettings.title}}", "description": "{{settingsAndPolicies.quoteToCash.creditMemoConfig.defaultSettings.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "onClick": "@controller:onCreditMemoConfigurationClick"}]}]}}]}, {"title": "{{settingsAndPolicies.quoteToCash.paymentSettings.paymentConfiguration}}", "id": "2.4", "visible": "@controller:QuoteToCashGroup.paymentConfigVisible()", "settings": [{"actions": {"variant": "avatar", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": [{"label": "{{settingsAndPolicies.quoteToCash.paymentSettings.paymentConfiguration}}", "description": "{{settingsAndPolicies.quoteToCash.paymentSettings.defaultSettings.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "onClick": "@controller:onPaymentSettingsClick"}]}]}}]}, {"title": "{{settingsAndPolicies.quoteToCash.revenuePolicies}}", "id": "2.5", "visible": "@controller:QuoteToCashGroup.revenueGroupVisible()", "settings": [{"title": "{{settingsAndPolicies.quoteToCash.revenuePolicies.perfomanceObligations}}", "section": {"title": "{{settingsAndPolicies.quoteToCash.revenuePolicies.perfomanceObligations}}"}, "actions": {"variant": "avatar", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": [{"label": "{{settingsAndPolicies.quoteToCash.revenuePolicies.perfomanceObligations.productAssignment.title}}", "description": "{{settingsAndPolicies.quoteToCash.revenuePolicies.perfomanceObligations.productAssignment.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "onClick": "@controller:onProductAssignementClick"}], "visible": "@controller:QuoteToCashGroup.revenueConfigVisible()"}, {"label": "{{settingsAndPolicies.quoteToCash.revenuePolicies.perfomanceObligations.revenueAccounts.title}}", "description": "{{settingsAndPolicies.quoteToCash.revenuePolicies.perfomanceObligations.revenueAccounts.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "onClick": "@controller:goToRevenueSettings"}], "visible": "@controller:QuoteToCashGroup.revenueConfigVisible()"}, {"label": "{{settingsAndPolicies.quoteToCash.revenuePolicies.perfomanceObligations.SspPercentage.title}}", "description": "{{settingsAndPolicies.quoteToCash.revenuePolicies.perfomanceObligations.SspPercentage.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "onClick": "@controller:onSSPRangesPercentageClick"}], "visible": "@controller:QuoteToCashGroup.sspRangeVisible()"}, {"label": "{{settingsAndPolicies.quoteToCash.revenuePolicies.perfomanceObligations.SspAmount.title}}", "description": "{{settingsAndPolicies.quoteToCash.revenuePolicies.perfomanceObligations.SspAmount.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "onClick": "@controller:onSSPRangesAmountClick"}], "visible": "@controller:QuoteToCashGroup.sspRangeVisible()"}, {"label": "{{settingsAndPolicies.quoteToCash.revenuePolicies.perfomanceObligations.amortizationMethods.title}}", "description": "{{settingsAndPolicies.quoteToCash.revenuePolicies.perfomanceObligations.amortizationMethods.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "onClick": "@controller:onAmortizationMethodsClick"}], "visible": "@controller:QuoteToCashGroup.revenueConfigVisible()"}]}}, {"title": "{{settingsAndPolicies.quoteToCash.revenuePolicies.revenueScheduling}}", "section": {"title": "{{settingsAndPolicies.quoteToCash.revenuePolicies.revenueScheduling}}"}, "actions": {"variant": "avatar", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": [{"label": "{{settingsAndPolicies.quoteToCash.revenuePolicies.revenueScheduling.schedulingTemplates.title}}", "description": "{{settingsAndPolicies.quoteToCash.revenuePolicies.revenueScheduling.schedulingTemplates.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "onClick": "@controller:onRevenueSchedulingTemplatesClick"}], "visible": "@controller:QuoteToCashGroup.revenueConfigVisible()"}]}}, {"title": "{{settingsAndPolicies.quoteToCash.revenuePolicies.revenueSettings}}", "section": {"title": "{{settingsAndPolicies.quoteToCash.revenuePolicies.revenueSettings}}"}, "actions": {"variant": "avatar", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": [{"label": "{{settingsAndPolicies.quoteToCash.revenueSettings.revenueSettings.title}}", "description": "{{settingsAndPolicies.quoteToCash.revenueSettings.revenueSettings.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "onClick": "@controller:goToRevenueSettings"}], "visible": "@controller:QuoteToCashGroup.revenueConfigVisible()"}]}}]}]}, {"title": "{{settingsAndPolicies.procureToPay}}", "visible": "@controller:ProcureToPayGroup.p2pGroupVisible()", "id": "3", "groups": [{"title": "{{settingsAndPolicies.procureToPay.offsetAccount.title}}", "visible": "@controller:ProcureToPayGroup.defaultOffsetGroupVisible()", "id": "3.1", "settings": [{"section": {"title": "{{settingsAndPolicies.procureToPay.offsetAccount.expense.title}}", "description": "{{settingsAndPolicies.procureToPay.offsetAccount.expense.description}}"}, "actions": {"variant": "default", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": "@controller:getOffsetAccountSettingMenuLines('expenseReportsAP')"}}, {"section": {"title": "{{settingsAndPolicies.procureToPay.offsetAccount.bills.title}}", "description": "{{settingsAndPolicies.procureToPay.offsetAccount.bills.description}}"}, "actions": {"variant": "default", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": "@controller:getOffsetAccountSettingMenuLines('vendorBillsAP')"}}, {"section": {"title": "{{settingsAndPolicies.procureToPay.offsetAccount.credits.title}}", "description": "{{settingsAndPolicies.procureToPay.offsetAccount.credits.description}}"}, "actions": {"variant": "default", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": "@controller:getOffsetAccountSettingMenuLines('vendorCreditsAP')"}}, {"section": {"title": "{{settingsAndPolicies.procureToPay.offsetAccount.payments.title}}", "description": "{{settingsAndPolicies.procureToPay.offsetAccount.payments.description}}"}, "actions": {"variant": "default", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": "@controller:getOffsetAccountSettingMenuLines('outboundPaymentsAP')"}}]}, {"title": "{{settingsAndPolicies.procureToPay.vendors}}", "id": "3.2", "visible": "@controller:ProcureToPayGroup.vendorSettingVisible()", "settings": [{"title": "{{settingsAndPolicies.procureToPay.vendorSettings}}", "section": {"editing": "@controller:ProcureToPayGroup.vendorSettingEditable()", "title": "{{settingsAndPolicies.procureToPay.vendorSettings}}"}, "actions": {"variant": "default", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": [{"label": "{{settingsAndPolicies.procureToPay.uniqueVendorNames}}", "description": "{{settingsAndPolicies.procureToPay.vendorDescription}}", "fieldProps": {"component": "Switch", "value": "@controller:getVendorUniqueness()", "direction": "horizontal-reverse", "onChange": "@controller:setVendorUniqueness"}}]}}]}, {"title": "{{settingsAndPolicies.procureToPay.vat.title}}", "visible": "@controller:ProcureToPayGroup.vendorSettingVisible()", "id": "3.3", "settings": [{"title": "{{settingsAndPolicies.procureToPay.vat}}", "section": {"title": "{{settingsAndPolicies.procureToPay.vat}}"}, "actions": {"variant": "avatar", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": [{"label": "{{settingsAndPolicies.procureToPay.vat.bookingAccount}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "onClick": "@controller:onClickVatBookingAccountConfiguration"}]}]}}]}, {"title": "{{settingsAndPolicies.procureToPay.payments}}", "visible": "@controller:QuoteToCashGroup.paymentConfigVisible()", "id": "3.4", "settings": [{"title": "{{settingsAndPolicies.procureToPay.outboundPaymentSettings}}", "section": {"editing": "@controller:ProcureToPayGroup.outboundPaymentSettingEditable()", "title": "{{settingsAndPolicies.procureToPay.outboundPaymentSettings}}"}, "actions": {"variant": "default", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": [{"label": "{{settingsAndPolicies.procureToPay.showAdditionalOutboundPaymentSettings}}", "description": "{{settingsAndPolicies.procureToPay.showAdditionalOutboundPaymentSettingsMessage}}", "fieldProps": {"component": "Switch", "value": "@controller:getOutboundPaymentSettingsValue()", "direction": "horizontal-reverse", "onChange": "@controller:setOutboundPaymentSettings"}}, {"label": "{{settingsAndPolicies.procureToPay.achPaymentSettings}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "onClick": "@controller:onEnablePaymentsClick"}]}]}}]}, {"title": "{{settingsAndPolicies.procureToPay.perDiemPolicies.title}}", "visible": "@controller:ProcureToPayGroup.perDiemSettingsVisible()", "id": "3.5", "settings": [{"section": {"title": "{{settingsAndPolicies.procureToPay.perDiemPolicies.title}}"}, "actions": {"variant": "avatar", "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": [{"label": "{{settingsAndPolicies.procureToPay.perDiemPolicies.title}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "onClick": "@controller:navigateToPerDiemPolicies", "disabled": "@controller:ProcureToPayGroup.perDiemSettingsDisabled()"}]}]}}]}]}, {"title": "{{settingsAndPolicies.recordToReport}}", "visible": "@controller:RecordToReportGroup.r2rGroupVisible()", "id": "4", "groups": [{"title": "{{settingsAndPolicies.recordToReport.preferences}}", "visible": "@controller:RecordToReportGroup.periodPreferencesVisible()", "id": "4.1", "settings": [{"section": {"title": "{{settingsAndPolicies.recordToReport.preferences.reporting}}"}, "actions": {"variant": "default", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "buttonActionsPosition": "right", "lines": [{"label": "{{settingsAndPolicies.recordToReport.preferences.defaultAccountingPeriod}}", "description": "{{settingsAndPolicies.recordToReport.preferences.defaultAccountingPeriodDescription}}", "result": "", "buttonActions": [{"label": "{{settingsAndPolicies.edit}}", "onClick": "@controller:openDefaultAccountingPeriodPreferenceModal"}]}]}}]}, {"title": "{{settingsAndPolicies.recordToReport.businessUnits.title}}", "id": "4.2", "settings": [{"section": {"actions": [{"label": "{{settingsAndPolicies.create}}", "onClick": "@controller:onClickBusinessUnitCreate"}]}, "actions": {"variant": "avatar", "editing": true, "component": "ActionGroup", "size": "12", "border": "none", "descriptionPosition": "rightSide", "buttonActionsPosition": "right", "lines": "@controller:getBusinessUnits()"}}]}, {"title": "{{settingsAndPolicies.recordToReport.amortization.title}}", "id": "4.3", "settings": [{"actions": {"variant": "avatar", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": [{"label": "{{settingsAndPolicies.recordToReport.amortizationConfig.title}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "onClick": "@controller:onClickAmortizationConfiguration"}]}]}}]}, {"title": "{{settingsAndPolicies.recordToReport.allocation}} (beta)", "id": "4.4", "settings": [{"title": "{{settingsAndPolicies.recordToReport.allocation.postAllocationTo}}", "visible": "@controller:RecordToReportGroup.postAllocationVisible()", "section": {"title": "{{settingsAndPolicies.recordToReport.allocation.postAllocationTo}}"}, "actions": {"variant": "avatar", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "rightSide", "buttonActionsPosition": "right", "lines": "@controller:getPostAllocations()"}}, {"title": "{{settingsAndPolicies.recordToReport.allocation.allocationPolicies}}", "visible": "@controller:RecordToReportGroup.allocationPolicyVisible()", "section": {"title": "{{settingsAndPolicies.recordToReport.allocation.allocationPolicies}}"}, "actions": {"variant": "avatar", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "rightSide", "buttonActionsPosition": "right", "lines": "@controller:getAllocationPolicies()"}}]}]}, {"title": "{{settingsAndPolicies.people}}", "visible": "@controller:PeopleGroup.peopleGroupVisible()", "id": "5", "groups": [{"title": "{{settingsAndPolicies.people.hrManagement}}", "visible": "@controller:PeopleGroup.hrGroupVisible()", "id": "5.1", "settings": [{"section": {"title": "{{settingsAndPolicies.hr.policies.title}}", "description": "{{settingsAndPolicies.hr.policies.description}}"}, "visible": "@controller:PeopleGroup.hrPoliciesVisible()", "actions": {"variant": "default", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": [{"label": "{{settingsAndPolicies.people.policies.defaultSettings.absencePolicies.title}}", "description": "{{settingsAndPolicies.people.policies.defaultSettings.absencePolicies.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "disabled": "@controller:PeopleGroup.absencePolicyDisabled()", "onClick": "@controller:onClickAbsencePolicies"}]}, {"label": "{{settingsAndPolicies.people.policies.defaultSettings.timeTrackingPolicies.title}}", "description": "{{settingsAndPolicies.people.policies.defaultSettings.timeTrackingPolicies.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "disabled": "@controller:PeopleGroup.timesheetPolicyListDisabled()", "onClick": "@controller:onClickTimeTrackingPolicies"}]}]}}, {"section": {"title": "{{settingsAndPolicies.people.policies.defaultSettings.hrAdminManagement.settings.title}}", "description": "{{settingsAndPolicies.people.policies.defaultSettings.hrAdminManagement.settings.description}}"}, "visible": "@controller:PeopleGroup.hrAdminSettingsVisible()", "actions": {"variant": "default", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": [{"label": "{{settingsAndPolicies.people.policies.defaultSettings.hrAdminManagement.title}}", "description": "{{settingsAndPolicies.people.policies.defaultSettings.hrAdminManagement.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "disabled": "@controller:PeopleGroup.hrAdminManagementDisabled()", "onClick": "@controller:onClickHrAdminManagement"}]}, {"label": "{{settingsAndPolicies.people.policies.defaultSettings.employeeCategory.title}}", "description": "{{settingsAndPolicies.people.policies.defaultSettings.employeeCategory.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "disabled": "@controller:PeopleGroup.hrEmployeeCategoryDisabled()", "onClick": "@controller:onClickHrEmployeeCategory"}]}, {"label": "{{settingsAndPolicies.people.policies.defaultSettings.holidayCalendars.title}}", "description": "{{settingsAndPolicies.people.policies.defaultSettings.holidayCalendars.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "disabled": "@controller:PeopleGroup.hrHolidayCalendarDisabled()", "onClick": "@controller:onClickHolidayCalendar"}]}, {"label": "{{settingsAndPolicies.people.boarding.settings.title}}", "description": "{{settingsAndPolicies.people.policies.defaultSettings.boarding.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "disabled": "@controller:PeopleGroup.hrBoardingDisabled()", "onClick": "@controller:onClickBoardingTemplate"}]}, {"label": "{{settingsAndPolicies.people.googleCalendar.settings.title}}", "description": "{{settingsAndPolicies.people.policies.defaultSettings.googleCalendar.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "disabled": "@controller:PeopleGroup.hrCalendarDisabled()", "onClick": "@controller:onClickGoogleCalendarConfig"}]}, {"label": "{{settingsAndPolicies.people.employeeTraining.settings.title}}", "description": "{{settingsAndPolicies.people.policies.defaultSettings.employeeTraining.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "disabled": "@controller:PeopleGroup.hrTrainingDisabled()", "onClick": "@controller:onClickEmployeeTraining"}]}, {"label": "{{settingsAndPolicies.people.empDocConfig.settings.title}}", "description": "{{settingsAndPolicies.people.policies.defaultSettings.empDocConfig.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "disabled": "@controller:PeopleGroup.hrDocumentsDisabled()", "onClick": "@controller:onClickEmpDocConfig"}]}]}}]}, {"title": "{{settingsAndPolicies.people.payroll}}", "visible": "@controller:PeopleGroup.payrollVisible()", "id": "5.3", "settings": [{"actions": {"variant": "avatar", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": [{"label": "{{settingsAndPolicies.people.payroll.defaultSettings.payrollTemplate.title}}", "description": "{{settingsAndPolicies.people.payroll.defaultSettings.payrollTemplate.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "onClick": "@controller:onClickPayrollTemplate"}]}]}}]}, {"title": "{{settingsAndPolicies.people.recruiting.defaultSettings}}", "visible": "@controller:PeopleGroup.recruitingVisible()", "id": "5.4", "settings": [{"actions": {"variant": "avatar", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": [{"label": "{{settingsAndPolicies.people.policies.defaultSettings.applicantStageTemplate.title}}", "description": "{{settingsAndPolicies.people.policies.defaultSettings.applicantStageTemplate.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "disabled": "@controller:PeopleGroup.applicantStageDisabled()", "onClick": "@controller:onClickApplicantStageTemplate"}]}, {"label": "{{settingsAndPolicies.people.recruiting.recruitementEmail.title}}", "description": "{{settingsAndPolicies.people.recruiting.recruitementEmail.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "disabled": "@controller:PeopleGroup.recruitingVisible()", "onClick": "@controller:onClickSetEmail"}]}]}}]}, {"title": "Skill Management", "visible": "@controller:PeopleGroup.skillManagementVisible()", "id": "5.5", "settings": [{"actions": {"variant": "avatar", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": [{"label": "{{settingsAndPolicies.people.policies.defaultSettings.skillManagement.title}}", "description": "{{settingsAndPolicies.people.policies.defaultSettings.skillManagement.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "onClick": "@controller:onClickSkillManagement"}]}, {"label": "{{settingsAndPolicies.people.policies.defaultSettings.skillManagement.skillLevels.title}}", "description": "{{settingsAndPolicies.people.policies.defaultSettings.skillManagement.skillLevels.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "onClick": "@controller:onClickSkillLevels"}]}]}}]}]}, {"title": "{{settingsAndPolicies.dataProtection}}", "id": "6", "visible": "@controller:DataProtectionGroup.gdprGroupVisible()", "settings": [{"actions": {"variant": "avatar", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": [{"label": "{{settingsAndPolicies.dataProtection.Purpose.title}}", "description": "{{settingsAndPolicies.dataProtection.Purpose.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "onClick": "@controller:DataEncryptionSetting.onPurposeClick"}]}, {"label": "{{settingsAndPolicies.dataProtection.consent.title}}", "description": "{{settingsAndPolicies.dataProtection.consent.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "onClick": "@controller:DataEncryptionSetting.onConsentClick"}]}, {"label": "{{settingsAndPolicies.dataProtection.agreement.title}}", "description": "{{settingsAndPolicies.dataProtection.agreement.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "onClick": "@controller:DataEncryptionSetting.onConsentAgreementClick"}]}, {"label": "{{settingsAndPolicies.dataProtection.dataConfig.title}}", "description": "{{settingsAndPolicies.dataProtection.dataConfig.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "onClick": "@controller:DataEncryptionSetting.onDataConfigClick"}]}, {"label": "{{settingsAndPolicies.dataProtection.dateEncryption.title}}", "description": "{{settingsAndPolicies.dataProtection.dateEncryption.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "onClick": "@controller:DataEncryptionSetting.onDataEncryptionConfigClick"}]}, {"label": "{{settingsAndPolicies.dataProtection.activateConfiguration.title}}", "description": "{{settingsAndPolicies.dataProtection.activateConfiguration.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "onClick": "@controller:DataEncryptionSetting.onActivateConfigurationClick"}]}, {"label": "{{settingsAndPolicies.dataProtection.expiredList.title}}", "description": "{{settingsAndPolicies.dataProtection.expiredList.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "onClick": "@controller:DataEncryptionSetting.onExpiredListClick"}]}]}}]}, {"title": "{{settingsAndPolicies.eve}}", "visible": "@controller:AiAssistantGroup.aiAssistantGroupVisible()", "id": "7", "groups": [{"title": "{{settingsAndPolicies.eve.general}}", "id": "7.1", "visible": "@controller:AiAssistantGroup.generalModelModalVisible()", "settings": [{"actions": {"variant": "avatar", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": [{"label": "{{settingsAndPolicies.eve.general.model}}", "description": "{{settingsAndPolicies.eve.general.model.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "onClick": "@controller:onGeneralModelConfigurationClick"}]}]}}]}, {"title": "{{settingsAndPolicies.eve.chat}}", "visible": "@controller:AiAssistantGroup.chatConfigVisible()", "id": "7.2", "settings": [{"actions": {"variant": "avatar", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": [{"label": "{{settingsAndPolicies.eve.chat.streaming}}", "description": "{{settingsAndPolicies.eve.chat.streaming.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "disabled": "@controller:AiAssistantGroup.chatConfigDisabled()", "onClick": "@controller:onChatConfigurationClick"}]}, {"label": "{{settingsAndPolicies.eve.chat.model}}", "description": "{{settingsAndPolicies.eve.chat.model.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "disabled": "@controller:AiAssistantGroup.chatModelDisabled()", "onClick": "@controller:onChatModelConfigurationClick"}]}]}}]}, {"title": "{{settingsAndPolicies.eve.customEve}}", "visible": "@controller:AiAssistantGroup.aiAgentVisible()", "id": "7.3", "settings": [{"actions": {"variant": "avatar", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": [{"label": "{{settingsAndPolicies.eve.customEve.reportModel}}", "description": "{{settingsAndPolicies.eve.customEve.reportModel.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "disabled": "@controller:AiAssistantGroup.aiAgentModelDisabled()", "onClick": "@controller:onCustomEveModelConfigurationClick"}]}, {"label": "{{settingsAndPolicies.eve.customEve.reportModelFallback}}", "description": "{{settingsAndPolicies.eve.customEve.reportModelFallback.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "disabled": "@controller:AiAssistantGroup.aiAgentFallabckModelDisabled()", "onClick": "@controller:onCustomEveModelFallbackConfigurationClick"}]}, {"label": "{{settingsAndPolicies.eve.customEve.codeInterpreterModel}}", "description": "{{settingsAndPolicies.eve.customEve.codeInterpreterModel.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "disabled": "@controller:AiAssistantGroup.aiAgentCodeModelDisabled()", "onClick": "@controller:onCustomEveCodeInterpreterModelConfigurationClick"}]}, {"label": "{{settingsAndPolicies.eve.customEve.promptImproveModel}}", "description": "{{settingsAndPolicies.eve.customEve.promptImproveModel.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "disabled": "@controller:AiAssistantGroup.aiAgentPromptModelDisabled()", "onClick": "@controller:onPromptImprovementModelConfigurationClick"}]}, {"label": "{{settingsAndPolicies.eve.customEve.maliciousCodeValidator}}", "description": "{{settingsAndPolicies.eve.customEve.maliciousCodeValidator.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "disabled": "@controller:AiAssistantGroup.aiAgentValidationDisabled()", "onClick": "@controller:onMaliciousEveValidatorConfigurationClick"}]}, {"label": "{{settingsAndPolicies.eve.customEve.Attempts}}", "description": "{{settingsAndPolicies.eve.customEve.Attempts.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "disabled": "@controller:AiAssistantGroup.aiAgentAttemptDisabled()", "onClick": "@controller:onCustomEveAttemptsClick"}]}, {"label": "{{settingsAndPolicies.eve.customEve.useEverestStreamlitComponents}}", "description": "{{settingsAndPolicies.eve.customEve.useEverestStreamlitComponents.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "disabled": "@controller:AiAssistantGroup.aiAgentStreamlitDisabled()", "onClick": "@controller:onEveUseEverestStreamlitComponents"}]}]}}]}, {"title": "{{settingsAndPolicies.eve.ocr}}", "visible": "@controller:AiAssistantGroup.aiOcrVisible()", "id": "7.4", "settings": [{"actions": {"variant": "avatar", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": [{"label": "{{settingsAndPolicies.eve.ocr.override}}", "description": "{{settingsAndPolicies.eve.chat.streaming.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "disabled": "@controller:AiAssistantGroup.aiOcrOverrrideDisabled()", "onClick": "@controller:onOCROverrideConfigurationClick"}]}, {"label": "{{settingsAndPolicies.eve.ocr.model}}", "description": "{{settingsAndPolicies.eve.ocr.model.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "disabled": "@controller:AiAssistantGroup.aiOcrModelDisabled()", "onClick": "@controller:onOCRModelConfigurationClick"}]}, {"label": "{{settingsAndPolicies.eve.ocr.vision}}", "description": "{{settingsAndPolicies.eve.chat.vision.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "disabled": "@controller:AiAssistantGroup.aiOcrVisionDisabled()", "onClick": "@controller:onOCRVisionConfigurationClick"}]}]}}]}, {"title": "{{settingsAndPolicies.eve.aispecify}}", "visible": true, "id": "7.5", "settings": [{"title": "{{settingsAndPolicies.eve.aispecify}}", "section": {"editing": true, "title": "{{settingsAndPolicies.eve.aispecify}}"}, "actions": {"variant": "default", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": [{"label": "{{settingsAndPolicies.eve.specifyBuildEnabled}}", "fieldProps": {"name": "specifyBuildEnabled", "component": "Switch", "value": "@controller:getSpecifyBuildEnabled()", "direction": "horizontal-reverse", "onChange": "@controller:setSpecifyBuildEnabled"}}]}}]}]}, {"title": "{{settingsAndPolicies.general.migration}}", "visible": "@controller:MigrationGroup.migrationGroupVisible()", "id": "8", "groups": [{"title": "{{settingsAndPolicies.general.migration.salesOrder}}", "visible": "@controller:MigrationGroup.featureSettingVisible()", "id": "8.1", "settings": [{"title": "{{settingsAndPolicies.general.migration.salesOrder}}", "section": {"editing": true, "title": "{{settingsAndPolicies.general.migration.salesOrder}}"}, "actions": {"variant": "default", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": [{"label": "{{settingsAndPolicies.general.migration.salesOrder.allowSalesOrderMassApproval}}", "fieldProps": {"name": "allowSalesOrderMassApproval", "component": "Switch", "value": "@controller:getAllowSalesOrderMassApprovalOption()", "direction": "horizontal-reverse", "onChange": "@controller:setAllowSalesOrderMassApprovalOption"}}]}}]}, {"title": "{{settingsAndPolicies.general.migration.subscription}}", "visible": "@controller:MigrationGroup.featureSettingVisible()", "id": "8.2", "settings": [{"title": "{{settingsAndPolicies.general.migration.subscription}}", "section": {"editing": true, "title": "{{settingsAndPolicies.general.migration.subscription}}"}, "actions": {"variant": "default", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": [{"label": "{{settingsAndPolicies.general.migration.salesOrder.allowModifyCancelledSubscription}}", "fieldProps": {"name": "allowModifyCancelledSubscription", "component": "Switch", "value": "@controller:getAllowModifyCancelledSubscriptionOption()", "direction": "horizontal-reverse", "onChange": "@controller:setAllowModifyCancelledSubscriptionOption"}}]}}]}, {"title": "{{settingsAndPolicies.general.migration.revenue}}", "visible": "@controller:MigrationGroup.featureSettingVisible()", "id": "8.3", "settings": [{"title": "{{settingsAndPolicies.general.migration.revenueCloseProcess}}", "section": {"editing": true, "title": "{{settingsAndPolicies.general.migration.revenueCloseProcess}}"}, "actions": {"variant": "default", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": [{"label": "{{settingsAndPolicies.general.migration.revenue.revenueCloseProcessReverseOptionName}}", "fieldProps": {"name": "migrationRevenueCloseProcessReverseOptionName", "component": "Switch", "value": "@controller:getMigrationRevenueCloseProcessReverseOption()", "direction": "horizontal-reverse", "onChange": "@controller:setMigrationRevenueCloseProcessReverseOption"}}, {"label": "{{settingsAndPolicies.general.migration.revenue.autoRevenueCloseProcess}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "disabled": "@controller:MigrationGroup.autoRevenueDisabled()", "onClick": "@controller:openAutoRevenueCloseProcessModal"}]}]}}, {"title": "{{settingsAndPolicies.general.migration.revenuePlan}}", "section": {"editing": true, "title": "{{settingsAndPolicies.general.migration.revenuePlan}}"}, "actions": {"variant": "default", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": [{"label": "{{settingsAndPolicies.general.migration.revenue.suppressRevenuePlanCreationOptionName}}", "fieldProps": {"name": "migrationSuppressRevenuePlanCreationOptionName", "component": "Switch", "value": "@controller:getMigrationSuppressRevenuePlanCreationOption()", "direction": "horizontal-reverse", "onChange": "@controller:setMigrationSuppressRevenuePlanCreationOption"}}]}}]}]}, {"title": "{{settingsAndPolicies.compliance}}", "id": "9", "visible": "@controller:WihistleblowerGroup.whistleblowerGroupVisible()", "groups": [{"title": "{{whistleblowing}}", "id": "9.1", "settings": [{"actions": {"variant": "avatar", "editing": true, "component": "ActionGroup", "size": "12", "versionNew": true, "border": "none", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": [{"label": "{{reporting.officer.key.management}}", "description": "{{ro.key.management.description}}", "buttonActions": [{"label": "{{master.key.setup}}", "onClick": "@controller:onClickNavigateROKeyManagement"}]}, {"label": "{{reporting.officer.management}}", "description": "{{ro.management.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "onClick": "@controller:onClickNavigateReportingOfficerManagement"}]}, {"label": "{{whistleblower.user}}", "description": "{{user.creation.description}}", "buttonActions": [{"label": "{{settingsAndPolicies.manage}}", "onClick": "@controller:onClickNavigateWhistleblowerUserCreation"}]}]}}]}]}]}]}}]}}}