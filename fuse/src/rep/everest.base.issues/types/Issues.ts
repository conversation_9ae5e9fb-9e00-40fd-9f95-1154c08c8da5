/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// ******************************************************************************
// Generated Everest Types
// ******************************************************************************

import type { Decimal, RoundingMode } from '@everestsystems/decimal';
import type {
  ArgType,
  Association,
  AssociationManagementArgs,
  AsyncResult,
  Controller,
  ControllerClientProvider,
  CreateOptionsArg,
  DataWithMetadata,
  DeleteOptionsArg,
  FieldSelector,
  Filter,
  flattenFieldSelector,
  GraphInstanceEntry,
  KernelRequestOptions,
  NonAssociationFields,
  Optional,
  PurgeArgs,
  ReadOptionsArg,
  ReadReturnType as ReadReturnTypeGeneric,
  ReadReturnTypeWithAssociations,
  RequireAtLeastOne,
  SemanticSearchArg,
  SemanticSearchRequestOptions,
  TypeSafeQueryArgType,
  UpdateManyOptionsArg,
  UpdateOptionsArg,
  UpsertOptionsArg,
  WriteReturnType,
  WriteReturnTypeWithAssociations,
} from '@everestsystems/content-core';
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstJSON as everest_appserver_primitive_JSON } from "@pkg/everest.appserver/types/primitives/JSON";
import type { EvstCasePriority as everest_base_issues_enum_CasePriority } from "@pkg/everest.base.issues/types/enums/CasePriority";
import type { EvstCaseApplicationLabel as everest_base_issues_enum_CaseApplicationLabel } from "@pkg/everest.base.issues/types/enums/CaseApplicationLabel";
import type { EvstCaseType as everest_base_issues_enum_CaseType } from "@pkg/everest.base.issues/types/enums/CaseType";
import type { EvstExternalId as everest_appserver_primitive_ExternalId } from "@pkg/everest.appserver/types/primitives/ExternalId";
import type { EvstCaseStatus as everest_base_issues_enum_CaseStatus } from "@pkg/everest.base.issues/types/enums/CaseStatus";
import type { EvstUUID as everest_appserver_primitive_UUID } from "@pkg/everest.appserver/types/primitives/UUID";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { EvstUserEmail as everest_appserver_usermgmt_primitive_user_UserEmail } from "@pkg/everest.appserver.usermgmt/types/primitives/user/UserEmail";
import type { EvstDateTime as everest_appserver_primitive_DateTime } from "@pkg/everest.appserver/types/primitives/DateTime";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { IssueComment as everest_base_issues_model_node_IssueComment } from "@pkg/everest.base.issues/types/IssueComment";

/**
 * Types for Issues
 *
 * @everest-node-metadata='{"visibility":"public"}'
 */
export namespace Issues {
  export type CreationFields = Pick<Issues, 'uuid' | 'externalId' | 'active' | 'applicationArea' | 'body' | 'comment' | 'isAssigned' | 'issueId' | 'issueUrl' | 'priority' | 'seen' | 'sourceTenant' | 'status' | 'title' | 'token' | 'type' | 'userId'>;
  export type UniqueFields = Pick<Issues, 'id' | 'uuid' | 'issueId'>;
  export type UniqueWhereInput = (RequireAtLeastOne<UniqueFields>) & Partial<Issues>;
  export type ReadReturnType<U extends string | number | symbol = keyof Issues> = ReadReturnTypeGeneric<Issues, U>;

  export interface IControllerClient extends Omit<Controller<Issues>, 'all' | 'getHistory' | 'lookup' | 'purge' | 'queryWithMetadata' | 'semanticSearch' | 'upsertMany'> {
    /** create new github issue */
    createIssue(title: everest_appserver_primitive_Text, body: everest_appserver_primitive_Text, files: everest_appserver_primitive_JSON[], priority: everest_base_issues_enum_CasePriority, applicationArea: everest_base_issues_enum_CaseApplicationLabel, type: everest_base_issues_enum_CaseType, clientMetadata: everest_appserver_primitive_JSON): Promise<void>;
    /** fetch comments of respected issue */
    fetchIssueComment(id: everest_appserver_primitive_ExternalId): Promise<void>;
    /** some description */
    deleteIssues(selectedIssueIds: everest_appserver_primitive_Text[]): Promise<void>;
    /** update an issue */
    updateIssues(selectedIssueIds: everest_appserver_primitive_Text[]): Promise<void>;
    /** Synchronize all issues with GitHub. */
    updateAllIssues(issues: Issues[]): Promise<void>;
    /** update an issue */
    updateIssue(issueId: everest_appserver_primitive_ExternalId, title: everest_appserver_primitive_Text, body: everest_appserver_primitive_Text, files: everest_appserver_primitive_JSON[], priority: everest_base_issues_enum_CasePriority, applicationArea: everest_base_issues_enum_CaseApplicationLabel, type: everest_base_issues_enum_CaseType, status: everest_base_issues_enum_CaseStatus): Promise<void>;
    /** Optimized action to sync support cases for a tenant with GitHub. Uses staggered execution (0-4 min random delay) to avoid rate limits and attempts to sync only recently updated issues first before falling back to full sync. */
    supportCaseSyncHook(): Promise<void>;
  }

  /**
   * Customer Cases - Initially this has been named Issue. However we decided to rename it to "case". We will rename this node at a later point
   */
  export type Issues = {
    /** Database generated identifier of this record. */
    id: number;
    /** Database generated version of this record. */
    version?: number;
    uuid?: everest_appserver_primitive_UUID | null;
    /**
     * ID of the system from which this record was sourced
     */
    externalId?: everest_appserver_primitive_Text | null;
    /**
     * Indicates whether this record is usable for business users
     */
    active?: everest_appserver_primitive_TrueFalse | null;
    /**
     * User that created this record
     */
    createdBy: everest_appserver_usermgmt_primitive_user_UserEmail;
    /**
     * Date/time when the record was created
     */
    createdDate: everest_appserver_primitive_DateTime;
    /**
     * User that last modified the record
     */
    lastModifiedBy: everest_appserver_usermgmt_primitive_user_UserEmail;
    /**
     * Date/time of the last modification to the record
     */
    lastModifiedDate: everest_appserver_primitive_DateTime;
    applicationArea: everest_base_issues_enum_CaseApplicationLabel;
    body: everest_appserver_primitive_Text;
    comment?: everest_appserver_primitive_Text | null;
    isAssigned?: everest_appserver_primitive_TrueFalse | null;
    issueId: everest_appserver_primitive_Number;
    issueUrl?: everest_appserver_primitive_Text | null;
    priority: everest_base_issues_enum_CasePriority;
    seen?: everest_appserver_primitive_TrueFalse | null;
    sourceTenant?: everest_appserver_primitive_Text | null;
    status: everest_base_issues_enum_CaseStatus;
    title: everest_appserver_primitive_Text;
    token?: everest_appserver_primitive_Text | null;
    type: everest_base_issues_enum_CaseType;
    userId?: everest_appserver_primitive_Text | null;
    };
  /**
   * Customer Cases - Initially this has been named Issue. However we decided to rename it to "case". We will rename this node at a later point
   */
  export type IssuesWithAssociation = Issues & {
    ["Issue-Comment"]?: Association<everest_base_issues_model_node_IssueComment.IssueCommentWithAssociation>[];
    };
  /** @deprecated - use {@link MODEL_URN} instead */
  export const name = 'urn:evst:everest:base/issues:model/node:Issues';
  export const MODEL_URN = 'urn:evst:everest:base/issues:model/node:Issues';
  /** @deprecated - use {@link MODEL_URN} instead */
  export const MODEL_ID = 'everest.base.issues/IssuesModel.Issues';
  export const MODEL_UUID = 'db9b8e04-ab36-47cb-b3ec-d9bc820a9595';

  /** @return a model controller instance for Issues. */
  export function client(env: ControllerClientProvider) {
    return env.controllerClient<Issues.IControllerClient>(MODEL_URN);
  }

  /** write a new object to the database. */
  export async function create<U extends keyof Issues>(env: ControllerClientProvider, input: CreationFields, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): Promise<WriteReturnType<Issues, U>> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).create(input, fields, options);
  }

  /** write new objects to the database. */
  export async function createMany<U extends keyof Issues>(env: ControllerClientProvider, input: CreationFields[], fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): Promise<WriteReturnType<Issues, U>[]> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).createMany(input, fields, options);
  }

  /** delete the object that is uniquely identified by the where condition. */
  export async function Delete(env: ControllerClientProvider, where: UniqueWhereInput): Promise<Partial<Issues>[]> {
    return (await client(env)).delete(where);
  }

  /** delete all objects. */
  export async function deleteAll(env: ControllerClientProvider): Promise<Partial<Issues>[]> {
    return (await client(env)).deleteAll();
  }

  /** delete the objects that are identified by the where condition. */
  export async function deleteMany(env: ControllerClientProvider, where: Filter<IssuesWithAssociation>): Promise<Partial<Issues>[]>;
  /** @deprecated use the alternative overloaded function */
  export async function deleteMany(env: ControllerClientProvider, where: Partial<Issues>): Promise<Partial<Issues>[]>;
  export async function deleteMany(env: ControllerClientProvider, where: Filter<Issues> | Partial<Issues>): Promise<Partial<Issues>[]> {
    return (await client(env)).deleteMany(where as Filter<Issues>);
  }

  /** returns a list of all objects matching the requirements. */
  export async function query<U extends FieldSelector<IssuesWithAssociation>>(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<IssuesWithAssociation>, 'draft'>, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<ReadReturnTypeWithAssociations<IssuesWithAssociation, U>[]>;
  /** @deprecated Use the alternative overloaded method for querying associations. */
  export async function query<U extends keyof Issues, V extends string = 'ALL_FIELDS'>(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<Issues>, 'draft'> | Omit<Omit<TypeSafeQueryArgType<Issues>, 'draft'>, 'where'> & { where?: Partial<Issues> }, fieldlist: ReadonlyArray<U | V> | 'ALL_FIELDS'): Promise<ReadReturnType<V>[]>;
  export async function query(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<Issues>, 'draft'>, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown[]> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).query(args, fields as ReadonlyArray<string>);
  }

  /** reads the object that is uniquely identified by the where condition. */
  export async function read<U extends FieldSelector<IssuesWithAssociation>>(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnTypeWithAssociations<IssuesWithAssociation, U>>;
  /** @deprecated Use the alternative overloaded method for reading associations. */
  export async function read<U extends keyof Issues>(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnType<U>>;
  export async function read(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<unknown> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).read(where, fields as ReadonlyArray<keyof Issues>, options);
  }

  /** writes modified values back to the database for a given node instance. */
  export async function update<U extends FieldSelector<IssuesWithAssociation>>(env: ControllerClientProvider, where: UniqueWhereInput, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnTypeWithAssociations<IssuesWithAssociation, U>>;
  /** @deprecated Use the alternative overloaded method for writing with associations. */
  export async function update<U extends keyof Issues>(env: ControllerClientProvider, where: Partial<Issues>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnType<Issues, U>>;
  export async function update(env: ControllerClientProvider, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).update(where, data, fields as ReadonlyArray<keyof Issues>);
  }

  /** writes modified values back to the database for selected node instances. */
  export async function updateMany<U extends FieldSelector<IssuesWithAssociation>>(env: ControllerClientProvider, where: Filter<IssuesWithAssociation>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnTypeWithAssociations<IssuesWithAssociation, U>[]>;
  /** @deprecated Use the alternative overloaded method for writing with associations. */
  export async function updateMany<U extends keyof Issues>(env: ControllerClientProvider, where: Partial<Issues>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnType<Issues, U>[]>;
  export async function updateMany(env: ControllerClientProvider, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown[]> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).updateMany(where, data, fields as ReadonlyArray<keyof Issues>);
  }

  /** creates or updates object identified by where. */
  export async function upsert<U extends keyof Issues & string>(env: ControllerClientProvider, data: Partial<Issues>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<Issues, U>>;
  /** @deprecated use the overload without an explicit where clause */
  export async function upsert<U extends keyof Issues & string>(env: ControllerClientProvider, where: Partial<Issues>, data: Partial<Issues>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<Issues, U>>;
  export async function upsert<U extends keyof Issues & string>(env: ControllerClientProvider, whereOrData: Partial<Issues>, dataOrFieldList?: Partial<Issues> | ReadonlyArray<U> | 'ALL_FIELDS', fieldlistOrOptions?: ReadonlyArray<U> | 'ALL_FIELDS' | Omit<UpsertOptionsArg, 'draft'>, maybeOptions?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<Issues, U>> {
    return (await client(env)).upsert(whereOrData, dataOrFieldList, fieldlistOrOptions, maybeOptions);
  }
}
