/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// ******************************************************************************
// Generated Everest Types
// ******************************************************************************

import type { Decimal, RoundingMode } from '@everestsystems/decimal';
import type {
  ArgType,
  Association,
  AssociationManagementArgs,
  AsyncResult,
  Controller,
  ControllerClientProvider,
  CreateOptionsArg,
  DataWithMetadata,
  DeleteOptionsArg,
  FieldSelector,
  Filter,
  flattenFieldSelector,
  GraphInstanceEntry,
  KernelRequestOptions,
  NonAssociationFields,
  Optional,
  PurgeArgs,
  ReadOptionsArg,
  ReadReturnType as ReadReturnTypeGeneric,
  ReadReturnTypeWithAssociations,
  RequireAtLeastOne,
  SemanticSearchArg,
  SemanticSearchRequestOptions,
  TypeSafeQueryArgType,
  UpdateManyOptionsArg,
  UpdateOptionsArg,
  UpsertOptionsArg,
  WriteReturnType,
  WriteReturnTypeWithAssociations,
} from '@everestsystems/content-core';

import { UIExecutionContext } from '@everestsystems/content-core';
import type { IssueComment as everest_base_issues_model_node_IssueComment } from "@pkg/everest.base.issues/types/IssueComment";
import type { Issues as everest_base_issues_model_node_Issues } from "@pkg/everest.base.issues/types/Issues";
import type orig_createIssue from "@pkg/everest.base.issues/actions/createIssue.action";
import type orig_fetchIssueComment from "@pkg/everest.base.issues/actions/fetchIssueComment.action";
import type orig_deleteIssues from "@pkg/everest.base.issues/actions/deleteIssues.action";
import type orig_updateIssues from "@pkg/everest.base.issues/actions/updateIssues.action";
import type orig_updateAllIssues from "@pkg/everest.base.issues/actions/updateAllIssues.action";
import type orig_updateIssue from "@pkg/everest.base.issues/actions/updateIssue.action";
import type orig_supportCaseSyncHook from "@pkg/everest.base.issues/actions/supportCaseSyncHook.action";

/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */

type RequiredContext = {
  data: Record<string, any>;
  actions: {
    run: (...args: any[]) => any;
  };
};

type ApiError = {
  message: string;
  name: string;
  errorCode: string;
  stack?: string;
};

type InnerPromiseType<T> = T extends Promise<infer R> ? R : T;

type ActionResultOrError<T1 extends string | number | symbol, T2> = {[P in T1]?: T2} & {error?: ApiError};

type ArrayIfNeeded<C extends RequiredContext, K extends keyof C['data'], T> = C['data'][K] extends UIExecutionContext.UIModelNodeListType<any> ? T[] : T;

export namespace IssuesUI {
  /**
   * Customer Cases - Initially this has been named Issue. However we decided to rename it to "case". We will rename this node at a later point
   */
  export type IssuesWithAssociation = Issues & {
    ["Issue-Comment"]?: Association<everest_base_issues_model_node_IssueComment.IssueCommentWithAssociation>[];
    };
  export interface IControllerClient extends everest_base_issues_model_node_Issues.IControllerClient {}

  export type Issues = everest_base_issues_model_node_Issues.Issues;
  export type CreationFields = Pick<Issues, 'uuid' | 'externalId' | 'active' | 'applicationArea' | 'body' | 'comment' | 'isAssigned' | 'issueId' | 'issueUrl' | 'priority' | 'seen' | 'sourceTenant' | 'status' | 'title' | 'token' | 'type' | 'userId'>;
  export type UniqueFields = Pick<Issues, 'id' | 'uuid' | 'issueId'>;
  export type UniqueWhereInput = (RequireAtLeastOne<UniqueFields>) & Partial<Issues>;
  export type ReadReturnType<U extends string | number | symbol = keyof Issues> = ReadReturnTypeGeneric<Issues, U>;

  class ActionRequest<C extends RequiredContext, FuncReturnType> {
    constructor(private readonly context: C, private readonly partialPayload: Record<string, any>) {
    }

    private async __run__(payload: any) {
      return this.context.actions.run(payload);
    }

    payload<K extends keyof C['data']>(nodeAlias: K): Record<string, any> {
      return {[nodeAlias]: this.partialPayload};
    }

    run<K extends keyof C['data']>(nodeAlias: K): Promise<ActionResultOrError<K, ArrayIfNeeded<C, K, InnerPromiseType<FuncReturnType>>>> {
      const payload = this.payload(nodeAlias);
      return this.__run__(payload);
    }
  }

  type func_createIssue = typeof orig_createIssue;
  /** @deprecated use ```client.createIssue``` instead */
  export function createIssue<C extends RequiredContext>(context: C, args: { title: Parameters<func_createIssue>[1]; body: Parameters<func_createIssue>[2]; files: Parameters<func_createIssue>[3]; priority: Parameters<func_createIssue>[4]; applicationArea: Parameters<func_createIssue>[5]; type: Parameters<func_createIssue>[6]; clientMetadata: Parameters<func_createIssue>[7] }): ActionRequest<C, ReturnType<func_createIssue>> {
    const partialPayload = {action: 'createIssue', data: args}

    return new ActionRequest<C, ReturnType<func_createIssue>>(context, partialPayload);
  }
  type func_fetchIssueComment = typeof orig_fetchIssueComment;
  /** @deprecated use ```client.fetchIssueComment``` instead */
  export function fetchIssueComment<C extends RequiredContext>(context: C, args: { id: Parameters<func_fetchIssueComment>[1] }): ActionRequest<C, ReturnType<func_fetchIssueComment>> {
    const partialPayload = {action: 'fetchIssueComment', data: args}

    return new ActionRequest<C, ReturnType<func_fetchIssueComment>>(context, partialPayload);
  }
  type func_deleteIssues = typeof orig_deleteIssues;
  /** @deprecated use ```client.deleteIssues``` instead */
  export function deleteIssues<C extends RequiredContext>(context: C, args: { selectedIssueIds: Parameters<func_deleteIssues>[1] }): ActionRequest<C, ReturnType<func_deleteIssues>> {
    const partialPayload = {action: 'deleteIssues', data: args}

    return new ActionRequest<C, ReturnType<func_deleteIssues>>(context, partialPayload);
  }
  type func_updateIssues = typeof orig_updateIssues;
  /** @deprecated use ```client.updateIssues``` instead */
  export function updateIssues<C extends RequiredContext>(context: C, args: { selectedIssueIds: Parameters<func_updateIssues>[1] }): ActionRequest<C, ReturnType<func_updateIssues>> {
    const partialPayload = {action: 'updateIssues', data: args}

    return new ActionRequest<C, ReturnType<func_updateIssues>>(context, partialPayload);
  }
  type func_updateAllIssues = typeof orig_updateAllIssues;
  /** @deprecated use ```client.updateAllIssues``` instead */
  export function updateAllIssues<C extends RequiredContext>(context: C, args: { issues: Parameters<func_updateAllIssues>[1] }): ActionRequest<C, ReturnType<func_updateAllIssues>> {
    const partialPayload = {action: 'updateAllIssues', data: args}

    return new ActionRequest<C, ReturnType<func_updateAllIssues>>(context, partialPayload);
  }
  type func_updateIssue = typeof orig_updateIssue;
  /** @deprecated use ```client.updateIssue``` instead */
  export function updateIssue<C extends RequiredContext>(context: C, args: { issueId: Parameters<func_updateIssue>[1]; title: Parameters<func_updateIssue>[2]; body: Parameters<func_updateIssue>[3]; files: Parameters<func_updateIssue>[4]; priority: Parameters<func_updateIssue>[5]; applicationArea: Parameters<func_updateIssue>[6]; type: Parameters<func_updateIssue>[7]; status: Parameters<func_updateIssue>[8] }): ActionRequest<C, ReturnType<func_updateIssue>> {
    const partialPayload = {action: 'updateIssue', data: args}

    return new ActionRequest<C, ReturnType<func_updateIssue>>(context, partialPayload);
  }
  type func_supportCaseSyncHook = typeof orig_supportCaseSyncHook;
  /** @deprecated use ```client.supportCaseSyncHook``` instead */
  export function supportCaseSyncHook<C extends RequiredContext>(context: C): ActionRequest<C, ReturnType<func_supportCaseSyncHook>> {
    const partialPayload = {action: 'supportCaseSyncHook', data: {}}

    return new ActionRequest<C, ReturnType<func_supportCaseSyncHook>>(context, partialPayload);
  }
  /** @deprecated - use {@link MODEL_URN} instead */
  export const name = 'urn:evst:everest:base/issues:model/node:Issues';
  export const MODEL_URN = 'urn:evst:everest:base/issues:model/node:Issues';
  /** @deprecated - use {@link MODEL_URN} instead */
  export const MODEL_ID = 'everest.base.issues/IssuesModel.Issues';
  export const MODEL_UUID = 'db9b8e04-ab36-47cb-b3ec-d9bc820a9595';

  /** @deprecated use ```client``` instead */
  export function create<C extends RequiredContext, U extends keyof Issues>(context: C, input: CreationFields, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<Issues, U>>> {
    const convertedArgs = {data: input, fieldList: fieldlist, options: options} as Record<string, any>
    const partialPayload = {action: 'create', ...convertedArgs}

    return new ActionRequest<C, Promise<WriteReturnType<Issues, U>>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function createMany<C extends RequiredContext, U extends keyof Issues>(context: C, input: CreationFields[], fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<Issues, U>[]>> {
    const convertedArgs = {data: input, fieldList: fieldlist, options: options} as Record<string, any>
    const partialPayload = {action: 'createMany', ...convertedArgs}

    return new ActionRequest<C, Promise<WriteReturnType<Issues, U>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function Delete<C extends RequiredContext>(context: C, where: UniqueWhereInput, options?: undefined): ActionRequest<C, Promise<Partial<Issues>[]>> {
    const convertedArgs = {where: where, options} as Record<string, any>
    const partialPayload = {action: 'delete', ...convertedArgs}

    return new ActionRequest<C, Promise<Partial<Issues>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function deleteAll<C extends RequiredContext>(context: C): ActionRequest<C, Promise<Partial<Issues>[]>> {
    const convertedArgs = {data: {}} as Record<string, any>
    const partialPayload = {action: 'deleteAll', ...convertedArgs}

    return new ActionRequest<C, Promise<Partial<Issues>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function deleteMany<C extends RequiredContext>(context: C, where: Filter<IssuesWithAssociation>): ActionRequest<C, Promise<Partial<Issues>[]>>;
  /** @deprecated use ```client``` instead */
  export function deleteMany<C extends RequiredContext>(context: C, where: Partial<Issues>): ActionRequest<C, Promise<Partial<Issues>[]>>;
  /** @deprecated use ```client``` instead */
  export function deleteMany<C extends RequiredContext>(context: C, where: Filter<Issues> | Partial<Issues>, options?: undefined): ActionRequest<C, Promise<Partial<Issues>[]>> {
    const convertedArgs = {data: {where: where}, options} as Record<string, any>
    const partialPayload = {action: 'deleteMany', ...convertedArgs}

    return new ActionRequest<C, Promise<Partial<Issues>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function query<C extends RequiredContext, U extends FieldSelector<IssuesWithAssociation>>(context: C, args: Omit<TypeSafeQueryArgType<IssuesWithAssociation>, 'draft'>, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<ReadReturnTypeWithAssociations<IssuesWithAssociation, U>[]>>;
  /** @deprecated use ```client``` instead */
  export function query<C extends RequiredContext, U extends keyof Issues, V extends string = 'ALL_FIELDS'>(context: C, args: Omit<TypeSafeQueryArgType<Issues>, 'draft'> | Omit<Omit<TypeSafeQueryArgType<Issues>, 'draft'>, 'where'> & { where?: Partial<Issues> }, fieldlist: ReadonlyArray<U | V> | 'ALL_FIELDS'): ActionRequest<C, Promise<ReadReturnType<V>[]>>;
  /** @deprecated use ```client``` instead */
  export function query<C extends RequiredContext>(context: C, args: Omit<TypeSafeQueryArgType<Issues>, 'draft'>, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS'): ActionRequest<C, Promise<unknown[]>> {
    const convertedArgs = {data: {...args, fieldList: fieldlist}} as Record<string, any>
    const partialPayload = {action: 'query', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function read<C extends RequiredContext, U extends FieldSelector<IssuesWithAssociation>>(context: C, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): ActionRequest<C, Promise<ReadReturnTypeWithAssociations<IssuesWithAssociation, U>>>;
  /** @deprecated use ```client``` instead */
  export function read<C extends RequiredContext, U extends keyof Issues>(context: C, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): ActionRequest<C, Promise<ReadReturnType<U>>>;
  /** @deprecated use ```client``` instead */
  export function read<C extends RequiredContext>(context: C, where: UniqueWhereInput, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): ActionRequest<C, Promise<unknown>> {
    const convertedArgs = {data: {where: where, fieldList: fieldlist}, options: options} as Record<string, any>
    const partialPayload = {action: 'read', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function update<C extends RequiredContext, U extends FieldSelector<IssuesWithAssociation>>(context: C, where: UniqueWhereInput, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<WriteReturnTypeWithAssociations<IssuesWithAssociation, U>>>;
  /** @deprecated use ```client``` instead */
  export function update<C extends RequiredContext, U extends keyof Issues>(context: C, where: Partial<Issues>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<WriteReturnType<Issues, U>>>;
  /** @deprecated use ```client``` instead */
  export function update<C extends RequiredContext>(context: C, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: undefined): ActionRequest<C, Promise<unknown>> {
    const convertedArgs = {where: where, data: data, fieldList: fieldlist, options} as Record<string, any>
    const partialPayload = {action: 'update', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function updateMany<C extends RequiredContext, U extends FieldSelector<IssuesWithAssociation>>(context: C, where: Filter<IssuesWithAssociation>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<WriteReturnTypeWithAssociations<IssuesWithAssociation, U>[]>>;
  /** @deprecated use ```client``` instead */
  export function updateMany<C extends RequiredContext, U extends keyof Issues>(context: C, where: Partial<Issues>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<WriteReturnType<Issues, U>[]>>;
  /** @deprecated use ```client``` instead */
  export function updateMany<C extends RequiredContext>(context: C, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: undefined): ActionRequest<C, Promise<unknown[]>> {
    const convertedArgs = {data: {where: where, data: data, fieldList: fieldlist}, options} as Record<string, any>
    const partialPayload = {action: 'updateMany', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function upsert<C extends RequiredContext, U extends keyof Issues & string>(context: C, data: Partial<Issues>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<Issues, U>>>;
  /** @deprecated use ```client``` instead */
  export function upsert<C extends RequiredContext, U extends keyof Issues & string>(context: C, where: Partial<Issues>, data: Partial<Issues>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<Issues, U>>>;
  /** @deprecated use ```client``` instead */
  export function upsert<C extends RequiredContext, U extends keyof Issues & string>(context: C, whereOrData: Partial<Issues>, dataOrFieldList?: Partial<Issues> | ReadonlyArray<U> | 'ALL_FIELDS', fieldlistOrOptions?: ReadonlyArray<U> | 'ALL_FIELDS' | Omit<UpsertOptionsArg, 'draft'>, maybeOptions?: Omit<UpsertOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<Issues, U>>> {
    const convertedArgs = {data: {where: whereOrData, data: dataOrFieldList, fieldList: fieldlistOrOptions}, options: maybeOptions} as Record<string, any>
    const partialPayload = {action: 'upsert', ...convertedArgs}

    return new ActionRequest<C, Promise<WriteReturnType<Issues, U>>>(context, partialPayload);
  }
  export namespace client {
    /** create new github issue */
    export declare function createIssue(args: { title: Parameters<func_createIssue>[1]; body: Parameters<func_createIssue>[2]; files: Parameters<func_createIssue>[3]; priority: Parameters<func_createIssue>[4]; applicationArea: Parameters<func_createIssue>[5]; type: Parameters<func_createIssue>[6]; clientMetadata: Parameters<func_createIssue>[7] }): ReturnType<func_createIssue>
    /** fetch comments of respected issue */
    export declare function fetchIssueComment(args: { id: Parameters<func_fetchIssueComment>[1] }): ReturnType<func_fetchIssueComment>
    /** some description */
    export declare function deleteIssues(args: { selectedIssueIds: Parameters<func_deleteIssues>[1] }): ReturnType<func_deleteIssues>
    /** update an issue */
    export declare function updateIssues(args: { selectedIssueIds: Parameters<func_updateIssues>[1] }): ReturnType<func_updateIssues>
    /** Synchronize all issues with GitHub. */
    export declare function updateAllIssues(args: { issues: Parameters<func_updateAllIssues>[1] }): ReturnType<func_updateAllIssues>
    /** update an issue */
    export declare function updateIssue(args: { issueId: Parameters<func_updateIssue>[1]; title: Parameters<func_updateIssue>[2]; body: Parameters<func_updateIssue>[3]; files: Parameters<func_updateIssue>[4]; priority: Parameters<func_updateIssue>[5]; applicationArea: Parameters<func_updateIssue>[6]; type: Parameters<func_updateIssue>[7]; status: Parameters<func_updateIssue>[8] }): ReturnType<func_updateIssue>
    /** Optimized action to sync support cases for a tenant with GitHub. Uses staggered execution (0-4 min random delay) to avoid rate limits and attempts to sync only recently updated issues first before falling back to full sync. */
    export declare function supportCaseSyncHook(): ReturnType<func_supportCaseSyncHook>
    /** write a new object to the database. */
    export declare function create<U extends keyof Issues>(input: CreationFields, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): Promise<WriteReturnType<Issues, U>>

    /** write new objects to the database. */
    export declare function createMany<U extends keyof Issues>(input: CreationFields[], fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): Promise<WriteReturnType<Issues, U>[]>

    /** delete the object that is uniquely identified by the where condition. */
    export declare function Delete(where: UniqueWhereInput, options?: undefined): Promise<Partial<Issues>[]>

    /** delete all objects. */
    export declare function deleteAll(): Promise<Partial<Issues>[]>

    /** delete the objects that are identified by the where condition. */
    export declare function deleteMany(where: Filter<IssuesWithAssociation>): Promise<Partial<Issues>[]>;
    /** @deprecated use the alternative overloaded function */
    export declare function deleteMany(where: Partial<Issues>): Promise<Partial<Issues>[]>;
    export declare function deleteMany(where: Filter<Issues> | Partial<Issues>, options?: undefined): Promise<Partial<Issues>[]>

    /** returns a list of all objects matching the requirements. */
    export declare function query<U extends FieldSelector<IssuesWithAssociation>>(args: Omit<TypeSafeQueryArgType<IssuesWithAssociation>, 'draft'>, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<ReadReturnTypeWithAssociations<IssuesWithAssociation, U>[]>;
    /** @deprecated Use the alternative overloaded method for querying associations. */
    export declare function query<U extends keyof Issues, V extends string = 'ALL_FIELDS'>(args: Omit<TypeSafeQueryArgType<Issues>, 'draft'> | Omit<Omit<TypeSafeQueryArgType<Issues>, 'draft'>, 'where'> & { where?: Partial<Issues> }, fieldlist: ReadonlyArray<U | V> | 'ALL_FIELDS'): Promise<ReadReturnType<V>[]>;
    export declare function query(args: Omit<TypeSafeQueryArgType<Issues>, 'draft'>, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown[]>

    /** reads the object that is uniquely identified by the where condition. */
    export declare function read<U extends FieldSelector<IssuesWithAssociation>>(where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnTypeWithAssociations<IssuesWithAssociation, U>>;
    /** @deprecated Use the alternative overloaded method for reading associations. */
    export declare function read<U extends keyof Issues>(where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnType<U>>;
    export declare function read(where: UniqueWhereInput, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<unknown>

    /** writes modified values back to the database for a given node instance. */
    export declare function update<U extends FieldSelector<IssuesWithAssociation>>(where: UniqueWhereInput, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnTypeWithAssociations<IssuesWithAssociation, U>>;
    /** @deprecated Use the alternative overloaded method for writing with associations. */
    export declare function update<U extends keyof Issues>(where: Partial<Issues>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnType<Issues, U>>;
    export declare function update(where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: undefined): Promise<unknown>

    /** writes modified values back to the database for selected node instances. */
    export declare function updateMany<U extends FieldSelector<IssuesWithAssociation>>(where: Filter<IssuesWithAssociation>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnTypeWithAssociations<IssuesWithAssociation, U>[]>;
    /** @deprecated Use the alternative overloaded method for writing with associations. */
    export declare function updateMany<U extends keyof Issues>(where: Partial<Issues>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnType<Issues, U>[]>;
    export declare function updateMany(where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: undefined): Promise<unknown[]>

    /** creates or updates object identified by where. */
    export declare function upsert<U extends keyof Issues & string>(data: Partial<Issues>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<Issues, U>>;
    /** @deprecated use the overload without an explicit where clause */
    export declare function upsert<U extends keyof Issues & string>(where: Partial<Issues>, data: Partial<Issues>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<Issues, U>>;
    export declare function upsert<U extends keyof Issues & string>(whereOrData: Partial<Issues>, dataOrFieldList?: Partial<Issues> | ReadonlyArray<U> | 'ALL_FIELDS', fieldlistOrOptions?: ReadonlyArray<U> | 'ALL_FIELDS' | Omit<UpsertOptionsArg, 'draft'>, maybeOptions?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<Issues, U>>
  }
}
