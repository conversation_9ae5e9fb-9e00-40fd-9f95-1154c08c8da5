import type { EvstCaseApplicationLabel } from '@pkg/everest.base.issues/types/enums/CaseApplicationLabel';
import type { EvstCasePriority } from '@pkg/everest.base.issues/types/enums/CasePriority';
import type { EvstCaseType } from '@pkg/everest.base.issues/types/enums/CaseType';

export const HTTP_OK = 200;
export const HTTP_CREATED = 201;

export interface GitHubResponse {
  status: number;
  data: {
    result: Record<string, string>;
  };
}

export interface GitHubCommentResponse {
  status: number;
  data: {
    result:
      | {
          commentId: number;
          commentUrl: string;
        }
      | Record<string, string>;
  };
}

export interface GitHubDeleteResponse {
  status: number;
}

export interface GitHubIssueComment {
  id: number;
  body: string;
}

export interface GitHubCommentFetchResponse {
  status: number;
  data: {
    result: GitHubIssueComment[];
  };
}

export interface GitHubRecentIssuesResponse {
  status: number;
  statusText?: string;
  data?: Array<{ number: number; [key: string]: unknown }>;
}

export interface GitHubIssue {
  number: number;
  state: string;
  title: string;
  body: string;
  labels: Array<{ name: string }>;
}

export interface ClientMetadata {
  url: string;
  currentPage: string;
  browser: {
    userAgent: string;
    language: string;
    platform: string;
    screenResolution: string;
    windowSize: string;
  };
  timestamp: string;
  timezone: {
    offset: number;
    timeZoneName: string;
    localTimestamp: string;
  };
  isInsideSandbox: boolean;
  sandboxId: string;
}

export interface SystemMetadata {
  sessionId: string;
  grafanaBaseUrl?: string;
  grafanaInstanceUrl?: string;
  environment: {
    instanceId: string;
    tenantId: string;
    podIp: string;
    fqdn: string;
    protocol: 'https' | 'http';
    port: number;
    systemType: string;
  };
  user: {
    id: string;
    email?: string;
    firstName?: string;
    lastName?: string;
  };
  context: {
    isWithinBackgroundWorker: boolean;
    isDuringSandboxMerge: boolean;
    initState: string;
  };
  timestamp: string;
}

export type UploadFile = {
  fileId: string;
  fileName: string;
  fileContent: string; // Base64 encoded file content
};

export interface CreateIssueParams {
  title: string;
  body: string;
  files: UploadFile[];
  priority: EvstCasePriority;
  applicationArea: EvstCaseApplicationLabel;
  type: EvstCaseType;
  clientMetadata?: ClientMetadata;
}
