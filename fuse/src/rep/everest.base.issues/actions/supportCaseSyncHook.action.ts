import {
  type ISession,
  log,
  recordGauge,
  withDurationMetric,
} from '@everestsystems/content-core';
import { customerIssuesConnectivity } from '@pkg/everest.base.issues.interface/types/CustomerIssuesConnectivity';

import { Issues } from '../types/Issues';
import type { GitHubRecentIssuesResponse } from './interfaces';
import updateAllIssues from './updateAllIssues.action';
import { calculateCaseCounts } from './utils/calculateCaseCounts';
import { getTenantNameFromFQDN } from './utils/tenantName';

/**
 * Updates issues in the issue portal for a specific tenant using optimized approach
 *
 * @param env - Session environment containing server configuration and authentication details
 * @returns Promise that resolves when the update is complete
 *
 * @remarks
 * Optimized approach:
 * 1. Adds random stagger delay (0-4 minutes) to avoid all tenants hitting GitHub at 2:00 AM exactly
 * 2. First tries to sync only recently updated issues from GitHub (reduces GraphQL node complexity)
 * 3. Falls back to full sync if recent issues approach fails
 * 4. This dramatically reduces GitHub API load and query complexity
 */
export default async function supportCaseSyncHook(
  env: ISession
): Promise<void> {
  const tenantNameFromFqdn = getTenantNameFromFQDN(env.serverEnvironment?.FQDN);

  // add random stagger delay (0-4 minutes) to spread tenant executions over time
  // this prevents all tenants from hitting GitHub API at exactly 2:00 AM
  // reduced from 10 minutes to 4 minutes to stay under task queue timeout (~5 minutes)
  const staggerDelayMs = Math.floor(Math.random() * 4 * 60 * 1000); // 0-4 minutes in ms
  const delayMinutes = Math.floor(staggerDelayMs / 60_000);
  const delaySeconds = Math.floor((staggerDelayMs % 60_000) / 1000);

  log.info(
    `[supportCaseSyncHook] ${tenantNameFromFqdn}: Adding random stagger delay of ${delayMinutes}m ${delaySeconds}s to avoid rate limit spikes`
  );
  await env.util.sleep(staggerDelayMs / 1000); // convert ms to seconds

  await withDurationMetric(
    'support_case_sync_hook_action',
    'Duration of the supportCaseSyncHook custom action',
    'everest.base.issues',
    async () => {
      try {
        // optimization: try recently updated issues from GitHub first (much lighter on GraphQL nodes)
        const recentSyncResult = await tryRecentlyUpdatedFromGitHub(
          env,
          tenantNameFromFqdn
        );

        if (recentSyncResult.success) {
          log.info({
            msg: 'Successfully completed optimized sync using recently updated GitHub issues',
            tenant: tenantNameFromFqdn,
            recentIssuesProcessed: recentSyncResult.issuesProcessed,
          });
        } else {
          // fallback to traditional full sync
          log.info(
            `[supportCaseSyncHook] ${tenantNameFromFqdn}: Falling back to full sync - ${recentSyncResult.reason}`
          );
          await performTraditionalSync(env, tenantNameFromFqdn);
        }
      } catch (error) {
        log.error({
          msg: 'Support case sync operation failed with unexpected error',
          tenant: tenantNameFromFqdn,
          error: error instanceof Error ? error.message : JSON.stringify(error),
        });
        // Don't re-throw to allow metrics recording to complete
      }
    }
  );

  // record tenant stats (keeping original functionality)
  await recordTenantStats(env, tenantNameFromFqdn);
}

// optimization: try to sync only recently updated issues from GitHub first
async function tryRecentlyUpdatedFromGitHub(
  env: ISession,
  tenantName: string
): Promise<{ success: boolean; reason?: string; issuesProcessed?: number }> {
  try {
    log.info(
      `[supportCaseSyncHook] ${tenantName}: Attempting optimized sync with recently updated GitHub issues`
    );

    // fetch recently updated issues from GitHub (last 48 hours to be safe with timezones)
    // this uses GitHub's search API which is much lighter than fetching all issues
    try {
      const options = {
        providerName: 'CasGithub',
      };

      const recentIssuesResponse =
        (await customerIssuesConnectivity.fetchRecentlyUpdatedIssues(
          env.connectivityLayer,
          options,
          48 // hours back
        )) as GitHubRecentIssuesResponse;

      if (recentIssuesResponse.status !== 200 || !recentIssuesResponse.data) {
        return {
          success: false,
          reason: `Failed to fetch recent issues from GitHub (status: ${recentIssuesResponse.status})`,
        };
      }

      const recentGitHubIssues = recentIssuesResponse.data;

      if (
        !Array.isArray(recentGitHubIssues) ||
        recentGitHubIssues.length === 0
      ) {
        return {
          success: false,
          reason: 'No recently updated issues found on GitHub',
        };
      }

      log.info(
        `[supportCaseSyncHook] ${tenantName}: Found ${recentGitHubIssues.length} recently updated GitHub issues`
      );

      // get the corresponding local issues for this tenant
      const recentIssueNumbers = (
        recentGitHubIssues as Array<{ number: number }>
      ).map((issue) => issue.number);
      const localIssues = await Issues.query(
        env,
        {
          where: {
            sourceTenant: tenantName,
            issueId: { $in: recentIssueNumbers },
          },
        },
        ['id', 'issueId', 'status', 'priority']
      );

      if (localIssues.length === 0) {
        // no local issues match the recently updated GitHub issues
        // this could happen if the tenant has no issues or they're all old
        return {
          success: false,
          reason: `No local issues found matching ${recentGitHubIssues.length} recent GitHub issues`,
        };
      }

      log.info(
        `[supportCaseSyncHook] ${tenantName}: Syncing ${localIssues.length} local issues that were recently updated on GitHub`
      );

      // update only the issues that were recently changed on GitHub
      const result = await updateAllIssues(env, localIssues as Issues.Issues[]);

      if (!result.updated && result.error) {
        log.error({
          msg: 'Optimized sync failed during updateAllIssues',
          errorMsg: result.error,
          status: result.status ?? 'Unknown Status',
          tenant: tenantName,
        });
        return {
          success: false,
          reason: `updateAllIssues failed: ${result.error}`,
        };
      }

      return {
        success: true,
        issuesProcessed: localIssues.length,
      };
    } catch (connectivityError) {
      // if fetchRecentlyUpdatedIssues doesn't exist or fails, fall back
      log.warn(
        `[supportCaseSyncHook] ${tenantName}: fetchRecentlyUpdatedIssues failed, falling back to traditional sync`,
        connectivityError
      );
      return {
        success: false,
        reason: 'fetchRecentlyUpdatedIssues method failed',
      };
    }
  } catch (error) {
    const message = error instanceof Error ? error.message : String(error);
    log.error(
      `[tryRecentlyUpdatedFromGitHub] ${tenantName}: Error during optimized sync`,
      error
    );
    return {
      success: false,
      reason: `Error during optimized sync: ${message}`,
    };
  }
}

// fallback: traditional full sync (original behavior)
async function performTraditionalSync(
  env: ISession,
  tenantName: string
): Promise<void> {
  const issues = await Issues.query(
    env,
    {
      where: {
        sourceTenant: tenantName,
      },
    },
    ['id', 'issueId', 'status', 'priority']
  );

  log.info(
    `[supportCaseSyncHook] ${tenantName}: Performing traditional full sync for ${issues.length} issues`
  );

  const result = await updateAllIssues(env, issues as Issues.Issues[]);

  if (!result.updated && result.error) {
    log.error({
      msg: 'Could not perform the regular update of the Support Portal',
      errorMsg: result.error,
      status: result.status ?? 'Unknown Status',
      tenant: tenantName,
    });
  }
}

// record tenant statistics (original functionality preserved)
async function recordTenantStats(
  env: ISession,
  tenantName: string
): Promise<void> {
  const issues = await Issues.query(
    env,
    {
      where: {
        sourceTenant: tenantName,
      },
    },
    ['id', 'issueId', 'status', 'priority']
  );

  const counts = calculateCaseCounts(issues);
  await Promise.all([
    recordGauge(
      'number_of_open_cases_on_tenant',
      'Current open cases count',
      'everest.base.issues',
      counts.open
    ),
    recordGauge(
      'number_of_open_cases_emergency_on_tenant',
      'Current open blocker cases count',
      'everest.base.issues',
      counts.openEmergency
    ),
    recordGauge(
      'number_of_open_cases_very_high_on_tenant',
      'Current open urgent cases count',
      'everest.base.issues',
      counts.openVeryHigh
    ),
    recordGauge(
      'number_of_open_cases_high_on_tenant',
      'Current open high priority cases count',
      'everest.base.issues',
      counts.openHigh
    ),
    recordGauge(
      'number_of_in_progress_cases_on_tenant',
      'Current in-progress cases count',
      'everest.base.issues',
      counts.inProgress
    ),
    recordGauge(
      'number_of_in_closed_cases_on_tenant',
      'Current closed cases count',
      'everest.base.issues',
      counts.closed
    ),
  ]);
}
