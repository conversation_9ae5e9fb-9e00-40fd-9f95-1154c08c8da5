import type { ISession } from '@everestsystems/content-core';
import { log, recordGauge } from '@everestsystems/content-core';
import { customerIssuesConnectivity } from '@pkg/everest.base.issues.interface/types/CustomerIssuesConnectivity';

import { EvstCasePriority } from '../types/enums/CasePriority';
import { EvstCaseStatus } from '../types/enums/CaseStatus';
import { Issues } from '../types/Issues';
import supportCaseSyncHook from './supportCaseSyncHook.action';
import updateAllIssues from './updateAllIssues.action';
import { calculateCaseCounts } from './utils/calculateCaseCounts';
import { getTenantNameFromFQDN } from './utils/tenantName';

// Mock dependencies
jest.mock('@everestsystems/content-core', () => ({
  log: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
  },
  recordGauge: jest.fn(),
  withDurationMetric: jest
    .fn()
    .mockImplementation(async (name, desc, pkg, fn) => {
      // Directly call the wrapped function in tests
      return await fn();
    }),
}));

jest.mock('../types/Issues', () => ({
  Issues: {
    query: jest.fn(),
  },
}));
jest.mock('./updateAllIssues.action');
jest.mock('./utils/calculateCaseCounts');
jest.mock('./utils/tenantName');
jest.mock(
  '@pkg/everest.base.issues.interface/types/CustomerIssuesConnectivity',
  () => ({
    customerIssuesConnectivity: {
      fetchRecentlyUpdatedIssues: jest.fn(),
    },
  })
);

// Mock setTimeout to avoid actual delays in tests
jest.useFakeTimers();

describe('supportCaseSyncHook', () => {
  const mockEnv = {
    serverEnvironment: { FQDN: 'test-tenant.example.com' },
    connectivityLayer: {},
    util: {
      sleep: jest.fn().mockResolvedValue(undefined),
    },
  } as unknown as ISession;

  const mockTenantName = 'test-tenant';

  const mockIssue1 = {
    id: 1,
    issueId: 101,
    status: EvstCaseStatus.Open,
    priority: EvstCasePriority.High,
  };
  const mockIssue2 = {
    id: 2,
    issueId: 102,
    status: EvstCaseStatus.InProgress,
    priority: EvstCasePriority.Medium,
  };
  const mockIssues = [mockIssue1, mockIssue2];

  const mockRecentGitHubIssues = [
    { number: 101, title: 'Issue 1' },
    { number: 102, title: 'Issue 2' },
  ];

  const mockCounts = {
    open: 1,
    openEmergency: 0,
    openVeryHigh: 0,
    openHigh: 1,
    inProgress: 1,
    closed: 0,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    jest.clearAllTimers();

    // Setup default mocks for happy path
    (getTenantNameFromFQDN as jest.Mock).mockReturnValue(mockTenantName);
    (Issues.query as jest.Mock).mockResolvedValue(mockIssues);
    (updateAllIssues as jest.Mock).mockResolvedValue({ updated: true });
    (calculateCaseCounts as jest.Mock).mockReturnValue(mockCounts);

    // Mock successful recent issues fetch
    (
      customerIssuesConnectivity.fetchRecentlyUpdatedIssues as jest.Mock
    ).mockResolvedValue({
      status: 200,
      data: mockRecentGitHubIssues,
    });
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
    jest.useFakeTimers();
  });

  describe('optimized sync flow', () => {
    it('should use recently updated issues when available', async () => {
      const syncPromise = supportCaseSyncHook(mockEnv);

      // Fast-forward the stagger delay
      jest.advanceTimersByTime(4 * 60 * 1000); // max 4 minutes

      await syncPromise;

      // Verify tenant name extraction
      expect(getTenantNameFromFQDN).toHaveBeenCalledWith(
        'test-tenant.example.com'
      );

      // Verify recent issues fetch was attempted
      expect(
        customerIssuesConnectivity.fetchRecentlyUpdatedIssues
      ).toHaveBeenCalledWith(
        mockEnv.connectivityLayer,
        { providerName: 'CasGithub' },
        48
      );

      // Verify local issues query for recent issues
      expect(Issues.query).toHaveBeenCalledWith(
        mockEnv,
        {
          where: {
            sourceTenant: mockTenantName,
            issueId: { $in: [101, 102] },
          },
        },
        ['id', 'issueId', 'status', 'priority']
      );

      // Verify updateAllIssues was called
      expect(updateAllIssues).toHaveBeenCalledWith(mockEnv, mockIssues);

      // Verify success log
      expect(log.info).toHaveBeenCalledWith({
        msg: 'Successfully completed optimized sync using recently updated GitHub issues',
        tenant: mockTenantName,
        recentIssuesProcessed: mockIssues.length,
      });

      // Verify gauges were recorded
      expect(recordGauge).toHaveBeenCalledTimes(6);
    });

    it('should fall back to traditional sync when fetchRecentlyUpdatedIssues fails', async () => {
      // Mock fetchRecentlyUpdatedIssues to fail
      (
        customerIssuesConnectivity.fetchRecentlyUpdatedIssues as jest.Mock
      ).mockResolvedValue({
        status: 500,
        data: null,
      });

      const syncPromise = supportCaseSyncHook(mockEnv);
      jest.advanceTimersByTime(4 * 60 * 1000);
      await syncPromise;

      // Verify recent issues fetch was attempted
      expect(
        customerIssuesConnectivity.fetchRecentlyUpdatedIssues
      ).toHaveBeenCalled();

      // Verify fallback to traditional sync (queries all issues)
      expect(Issues.query).toHaveBeenCalledWith(
        mockEnv,
        {
          where: {
            sourceTenant: mockTenantName,
          },
        },
        ['id', 'issueId', 'status', 'priority']
      );

      // Verify fallback log
      expect(log.info).toHaveBeenCalledWith(
        expect.stringContaining('Falling back to full sync')
      );

      expect(updateAllIssues).toHaveBeenCalledWith(mockEnv, mockIssues);
    });

    it('should fall back when fetchRecentlyUpdatedIssues throws an error', async () => {
      // Mock fetchRecentlyUpdatedIssues to throw an error
      (
        customerIssuesConnectivity.fetchRecentlyUpdatedIssues as jest.Mock
      ).mockRejectedValue(new Error('Connectivity error'));

      const syncPromise = supportCaseSyncHook(mockEnv);
      jest.advanceTimersByTime(4 * 60 * 1000);
      await syncPromise;

      // Verify fallback to traditional sync
      expect(Issues.query).toHaveBeenCalledWith(
        mockEnv,
        {
          where: {
            sourceTenant: mockTenantName,
          },
        },
        ['id', 'issueId', 'status', 'priority']
      );

      expect(log.info).toHaveBeenCalledWith(
        expect.stringContaining('Falling back to full sync')
      );
    });

    it('should fall back when no local issues match recent GitHub issues', async () => {
      // Mock Issues.query to return empty array for recent issues
      (Issues.query as jest.Mock)
        .mockResolvedValueOnce([]) // for recent issues query
        .mockResolvedValueOnce(mockIssues) // for traditional sync
        .mockResolvedValueOnce(mockIssues); // for stats

      const syncPromise = supportCaseSyncHook(mockEnv);
      jest.advanceTimersByTime(4 * 60 * 1000);
      await syncPromise;

      // Verify attempted recent issues query
      expect(Issues.query).toHaveBeenCalledWith(
        mockEnv,
        {
          where: {
            sourceTenant: mockTenantName,
            issueId: { $in: [101, 102] },
          },
        },
        ['id', 'issueId', 'status', 'priority']
      );

      // Verify fallback to traditional sync
      expect(Issues.query).toHaveBeenCalledWith(
        mockEnv,
        {
          where: {
            sourceTenant: mockTenantName,
          },
        },
        ['id', 'issueId', 'status', 'priority']
      );

      expect(log.info).toHaveBeenCalledWith(
        expect.stringContaining('Falling back to full sync')
      );
    });
  });

  describe('traditional sync flow', () => {
    it('should perform traditional sync when optimization is not available', async () => {
      // Mock env without connectivity layer
      const envWithoutConnectivity = {
        ...mockEnv,
        connectivityLayer: {},
      } as unknown as ISession;

      const syncPromise = supportCaseSyncHook(envWithoutConnectivity);
      jest.advanceTimersByTime(4 * 60 * 1000);
      await syncPromise;

      // Should query all issues for the tenant
      expect(Issues.query).toHaveBeenCalledWith(
        envWithoutConnectivity,
        {
          where: {
            sourceTenant: mockTenantName,
          },
        },
        ['id', 'issueId', 'status', 'priority']
      );

      expect(updateAllIssues).toHaveBeenCalledWith(
        envWithoutConnectivity,
        mockIssues
      );
    });

    it('should log error when traditional sync fails', async () => {
      // Mock env without connectivity layer to force traditional sync
      const envWithoutConnectivity = {
        ...mockEnv,
        connectivityLayer: {},
      } as unknown as ISession;

      // Mock updateAllIssues to fail
      const failureResult = {
        updated: false,
        error: 'GitHub API error',
        status: 500,
      };
      (updateAllIssues as jest.Mock).mockResolvedValue(failureResult);

      const syncPromise = supportCaseSyncHook(envWithoutConnectivity);
      jest.advanceTimersByTime(4 * 60 * 1000);
      await syncPromise;

      expect(log.error).toHaveBeenCalledWith({
        msg: 'Could not perform the regular update of the Support Portal',
        errorMsg: failureResult.error,
        status: failureResult.status,
        tenant: mockTenantName,
      });

      // Verify gauges are still recorded
      expect(recordGauge).toHaveBeenCalledTimes(6);
    });
  });

  describe('error handling', () => {
    it('should handle unexpected errors during sync operation', async () => {
      const errorMessage = 'Unexpected network error';
      (updateAllIssues as jest.Mock).mockRejectedValue(new Error(errorMessage));

      const syncPromise = supportCaseSyncHook(mockEnv);
      jest.advanceTimersByTime(4 * 60 * 1000);
      await syncPromise;

      expect(log.error).toHaveBeenCalledWith({
        msg: 'Support case sync operation failed with unexpected error',
        tenant: mockTenantName,
        error: errorMessage,
      });

      // Verify gauges are still recorded
      expect(recordGauge).toHaveBeenCalledTimes(6);
    });

    it('should handle non-Error exceptions during sync operation', async () => {
      const errorObject = {
        code: 'NETWORK_ERROR',
        message: 'Connection failed',
      };
      (updateAllIssues as jest.Mock).mockRejectedValue(errorObject);

      const syncPromise = supportCaseSyncHook(mockEnv);
      jest.advanceTimersByTime(4 * 60 * 1000);
      await syncPromise;

      expect(log.error).toHaveBeenCalledWith({
        msg: 'Support case sync operation failed with unexpected error',
        tenant: mockTenantName,
        error: JSON.stringify(errorObject),
      });
    });
  });

  describe('stagger delay', () => {
    it('should add random stagger delay between 0-4 minutes', async () => {
      // Mock Math.random to return a predictable value (2 minutes)
      const originalMathRandom = Math.random;
      Math.random = jest.fn(() => 0.5); // Will result in 2 minute delay

      const syncPromise = supportCaseSyncHook(mockEnv);

      // Should not proceed immediately
      expect(
        customerIssuesConnectivity.fetchRecentlyUpdatedIssues
      ).not.toHaveBeenCalled();

      // Run all pending timers (more reliable than advanceTimersByTime in integration)
      jest.runAllTimers();

      await syncPromise;

      // Should have proceeded after the delay
      expect(
        customerIssuesConnectivity.fetchRecentlyUpdatedIssues
      ).toHaveBeenCalled();

      // Verify stagger delay log (should show 2m 0s for our mocked random)
      expect(log.info).toHaveBeenCalledWith(
        expect.stringContaining('Adding random stagger delay of 2m 0s')
      );

      // Restore original Math.random
      Math.random = originalMathRandom;
    });
  });

  describe('metrics and gauges', () => {
    it('should record all tenant statistics after sync', async () => {
      const syncPromise = supportCaseSyncHook(mockEnv);
      jest.advanceTimersByTime(4 * 60 * 1000);
      await syncPromise;

      // Verify counts calculation
      expect(calculateCaseCounts).toHaveBeenCalled();

      // Verify all gauge recordings
      expect(recordGauge).toHaveBeenCalledTimes(6);
      expect(recordGauge).toHaveBeenCalledWith(
        'number_of_open_cases_on_tenant',
        expect.any(String),
        'everest.base.issues',
        mockCounts.open
      );
      expect(recordGauge).toHaveBeenCalledWith(
        'number_of_open_cases_emergency_on_tenant',
        expect.any(String),
        'everest.base.issues',
        mockCounts.openEmergency
      );
      expect(recordGauge).toHaveBeenCalledWith(
        'number_of_open_cases_very_high_on_tenant',
        expect.any(String),
        'everest.base.issues',
        mockCounts.openVeryHigh
      );
      expect(recordGauge).toHaveBeenCalledWith(
        'number_of_open_cases_high_on_tenant',
        expect.any(String),
        'everest.base.issues',
        mockCounts.openHigh
      );
      expect(recordGauge).toHaveBeenCalledWith(
        'number_of_in_progress_cases_on_tenant',
        expect.any(String),
        'everest.base.issues',
        mockCounts.inProgress
      );
      expect(recordGauge).toHaveBeenCalledWith(
        'number_of_in_closed_cases_on_tenant',
        expect.any(String),
        'everest.base.issues',
        mockCounts.closed
      );
    });
  });
});
