/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// ******************************************************************************
// Generated Everest Types
// ******************************************************************************

import type { Decimal, RoundingMode } from '@everestsystems/decimal';
import type {
  ArgType,
  Association,
  AssociationManagementArgs,
  AsyncResult,
  Controller,
  ControllerClientProvider,
  CreateOptionsArg,
  DataWithMetadata,
  DeleteOptionsArg,
  FieldSelector,
  Filter,
  flattenFieldSelector,
  GraphInstanceEntry,
  KernelRequestOptions,
  NonAssociationFields,
  Optional,
  PurgeArgs,
  ReadOptionsArg,
  ReadReturnType as ReadReturnTypeGeneric,
  ReadReturnTypeWithAssociations,
  RequireAtLeastOne,
  SemanticSearchArg,
  SemanticSearchRequestOptions,
  TypeSafeQueryArgType,
  UpdateManyOptionsArg,
  UpdateOptionsArg,
  UpsertOptionsArg,
  WriteReturnType,
  WriteReturnTypeWithAssociations,
} from '@everestsystems/content-core';

import { UIExecutionContext } from '@everestsystems/content-core';
import type { DataProtection as everest_base_dataprotection_model_node_DataProtection } from "@pkg/everest.base.dataprotection/types/DataProtection";
import type orig_personalDataExpirationHandler from "@pkg/everest.base.dataprotection/actions/personalDataExpirationHandler.action";
import type orig_deletePersonalData from "@pkg/everest.base.dataprotection/actions/deletePersonalData.action";
import type orig_getPersonalData from "@pkg/everest.base.dataprotection/actions/getPersonalData.action";
import type orig_activateConfiguration from "@pkg/everest.base.dataprotection/actions/activateConfiguration.action";
import type orig_findConsentByScope from "@pkg/everest.base.dataprotection/actions/findConsentByScope.action";
import type orig_createConsentAgreement from "@pkg/everest.base.dataprotection/actions/createConsentAgreement.action";
import type orig_getScopeList from "@pkg/everest.base.dataprotection/actions/getScopeList.action";
import type orig_getPreConfigurationList from "@pkg/everest.base.dataprotection/actions/getPreConfigurationList.action";
import type orig_getPurposeListWithScope from "@pkg/everest.base.dataprotection/actions/getPurposeListWithScope.action";

/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */

type RequiredContext = {
  data: Record<string, any>;
  actions: {
    run: (...args: any[]) => any;
  };
};

type ApiError = {
  message: string;
  name: string;
  errorCode: string;
  stack?: string;
};

type InnerPromiseType<T> = T extends Promise<infer R> ? R : T;

type ActionResultOrError<T1 extends string | number | symbol, T2> = {[P in T1]?: T2} & {error?: ApiError};

type ArrayIfNeeded<C extends RequiredContext, K extends keyof C['data'], T> = C['data'][K] extends UIExecutionContext.UIModelNodeListType<any> ? T[] : T;

export namespace DataProtectionUI {
  /**
   * Node that hold actions for Data Protection
   */
  export type DataProtectionWithAssociation = DataProtection & {

    };
  export interface IControllerClient extends everest_base_dataprotection_model_node_DataProtection.IControllerClient {}

  export type DataProtection = everest_base_dataprotection_model_node_DataProtection.DataProtection;
  export type CreationFields = Pick<DataProtection, 'uuid' | 'externalId' | 'active'>;
  export type UniqueFields = Pick<DataProtection, 'id' | 'uuid'>;
  export type UniqueWhereInput = (RequireAtLeastOne<UniqueFields>) & Partial<DataProtection>;
  export type ReadReturnType<U extends string | number | symbol = keyof DataProtection> = ReadReturnTypeGeneric<DataProtection, U>;

  class ActionRequest<C extends RequiredContext, FuncReturnType> {
    constructor(private readonly context: C, private readonly partialPayload: Record<string, any>) {
    }

    private async __run__(payload: any) {
      return this.context.actions.run(payload);
    }

    payload<K extends keyof C['data']>(nodeAlias: K): Record<string, any> {
      return {[nodeAlias]: this.partialPayload};
    }

    run<K extends keyof C['data']>(nodeAlias: K): Promise<ActionResultOrError<K, ArrayIfNeeded<C, K, InnerPromiseType<FuncReturnType>>>> {
      const payload = this.payload(nodeAlias);
      return this.__run__(payload);
    }
  }

  type func_personalDataExpirationHandler = typeof orig_personalDataExpirationHandler;
  /** @deprecated use ```client.personalDataExpirationHandler``` instead */
  export function personalDataExpirationHandler<C extends RequiredContext>(context: C): ActionRequest<C, ReturnType<func_personalDataExpirationHandler>> {
    const partialPayload = {action: 'personalDataExpirationHandler', data: {}}

    return new ActionRequest<C, ReturnType<func_personalDataExpirationHandler>>(context, partialPayload);
  }
  type func_deletePersonalData = typeof orig_deletePersonalData;
  /** @deprecated use ```client.deletePersonalData``` instead */
  export function deletePersonalData<C extends RequiredContext>(context: C, args: { payload: Parameters<func_deletePersonalData>[1] }): ActionRequest<C, ReturnType<func_deletePersonalData>> {
    const partialPayload = {action: 'deletePersonalData', data: args}

    return new ActionRequest<C, ReturnType<func_deletePersonalData>>(context, partialPayload);
  }
  type func_getPersonalData = typeof orig_getPersonalData;
  /** @deprecated use ```client.getPersonalData``` instead */
  export function getPersonalData<C extends RequiredContext>(context: C, args: { payload: Parameters<func_getPersonalData>[1] }): ActionRequest<C, ReturnType<func_getPersonalData>> {
    const partialPayload = {action: 'getPersonalData', data: args}

    return new ActionRequest<C, ReturnType<func_getPersonalData>>(context, partialPayload);
  }
  type func_activateConfiguration = typeof orig_activateConfiguration;
  /** @deprecated use ```client.activateConfiguration``` instead */
  export function activateConfiguration<C extends RequiredContext>(context: C, args: { payload: Parameters<func_activateConfiguration>[1] }): ActionRequest<C, ReturnType<func_activateConfiguration>> {
    const partialPayload = {action: 'activateConfiguration', data: args}

    return new ActionRequest<C, ReturnType<func_activateConfiguration>>(context, partialPayload);
  }
  type func_findConsentByScope = typeof orig_findConsentByScope;
  /** @deprecated use ```client.findConsentByScope``` instead */
  export function findConsentByScope<C extends RequiredContext>(context: C, args: { payload: Parameters<func_findConsentByScope>[1] }): ActionRequest<C, ReturnType<func_findConsentByScope>> {
    const partialPayload = {action: 'findConsentByScope', data: args}

    return new ActionRequest<C, ReturnType<func_findConsentByScope>>(context, partialPayload);
  }
  type func_createConsentAgreement = typeof orig_createConsentAgreement;
  /** @deprecated use ```client.createConsentAgreement``` instead */
  export function createConsentAgreement<C extends RequiredContext>(context: C, args: { payload: Parameters<func_createConsentAgreement>[1] }): ActionRequest<C, ReturnType<func_createConsentAgreement>> {
    const partialPayload = {action: 'createConsentAgreement', data: args}

    return new ActionRequest<C, ReturnType<func_createConsentAgreement>>(context, partialPayload);
  }
  type func_getScopeList = typeof orig_getScopeList;
  /** @deprecated use ```client.getScopeList``` instead */
  export function getScopeList<C extends RequiredContext>(context: C, args: { payload: Parameters<func_getScopeList>[1] }): ActionRequest<C, ReturnType<func_getScopeList>> {
    const partialPayload = {action: 'getScopeList', data: args}

    return new ActionRequest<C, ReturnType<func_getScopeList>>(context, partialPayload);
  }
  type func_getPreConfigurationList = typeof orig_getPreConfigurationList;
  /** @deprecated use ```client.getPreConfigurationList``` instead */
  export function getPreConfigurationList<C extends RequiredContext>(context: C, args: { payload: Parameters<func_getPreConfigurationList>[1] }): ActionRequest<C, ReturnType<func_getPreConfigurationList>> {
    const partialPayload = {action: 'getPreConfigurationList', data: args}

    return new ActionRequest<C, ReturnType<func_getPreConfigurationList>>(context, partialPayload);
  }
  type func_getPurposeListWithScope = typeof orig_getPurposeListWithScope;
  /** @deprecated use ```client.getPurposeListWithScope``` instead */
  export function getPurposeListWithScope<C extends RequiredContext>(context: C): ActionRequest<C, ReturnType<func_getPurposeListWithScope>> {
    const partialPayload = {action: 'getPurposeListWithScope', data: {}}

    return new ActionRequest<C, ReturnType<func_getPurposeListWithScope>>(context, partialPayload);
  }
  /** @deprecated - use {@link MODEL_URN} instead */
  export const name = 'urn:evst:everest:base/dataprotection:model/node:DataProtection';
  export const MODEL_URN = 'urn:evst:everest:base/dataprotection:model/node:DataProtection';
  /** @deprecated - use {@link MODEL_URN} instead */
  export const MODEL_ID = 'everest.base.dataprotection/DataProtectionModel.DataProtection';
  export const MODEL_UUID = '6363dc54-85e8-4c9e-bd32-920831c43e59';

  /** @deprecated use ```client``` instead */
  export function create<C extends RequiredContext, U extends keyof DataProtection>(context: C, input: CreationFields, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<DataProtection, U>>> {
    const convertedArgs = {data: input, fieldList: fieldlist, options: options} as Record<string, any>
    const partialPayload = {action: 'create', ...convertedArgs}

    return new ActionRequest<C, Promise<WriteReturnType<DataProtection, U>>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function createMany<C extends RequiredContext, U extends keyof DataProtection>(context: C, input: CreationFields[], fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<DataProtection, U>[]>> {
    const convertedArgs = {data: input, fieldList: fieldlist, options: options} as Record<string, any>
    const partialPayload = {action: 'createMany', ...convertedArgs}

    return new ActionRequest<C, Promise<WriteReturnType<DataProtection, U>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function Delete<C extends RequiredContext>(context: C, where: UniqueWhereInput, options?: undefined): ActionRequest<C, Promise<Partial<DataProtection>[]>> {
    const convertedArgs = {where: where, options} as Record<string, any>
    const partialPayload = {action: 'delete', ...convertedArgs}

    return new ActionRequest<C, Promise<Partial<DataProtection>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function deleteAll<C extends RequiredContext>(context: C): ActionRequest<C, Promise<Partial<DataProtection>[]>> {
    const convertedArgs = {data: {}} as Record<string, any>
    const partialPayload = {action: 'deleteAll', ...convertedArgs}

    return new ActionRequest<C, Promise<Partial<DataProtection>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function deleteMany<C extends RequiredContext>(context: C, where: Filter<DataProtectionWithAssociation>): ActionRequest<C, Promise<Partial<DataProtection>[]>>;
  /** @deprecated use ```client``` instead */
  export function deleteMany<C extends RequiredContext>(context: C, where: Partial<DataProtection>): ActionRequest<C, Promise<Partial<DataProtection>[]>>;
  /** @deprecated use ```client``` instead */
  export function deleteMany<C extends RequiredContext>(context: C, where: Filter<DataProtection> | Partial<DataProtection>, options?: undefined): ActionRequest<C, Promise<Partial<DataProtection>[]>> {
    const convertedArgs = {data: {where: where}, options} as Record<string, any>
    const partialPayload = {action: 'deleteMany', ...convertedArgs}

    return new ActionRequest<C, Promise<Partial<DataProtection>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function query<C extends RequiredContext, U extends FieldSelector<DataProtectionWithAssociation>>(context: C, args: Omit<TypeSafeQueryArgType<DataProtectionWithAssociation>, 'draft'>, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<ReadReturnTypeWithAssociations<DataProtectionWithAssociation, U>[]>>;
  /** @deprecated use ```client``` instead */
  export function query<C extends RequiredContext, U extends keyof DataProtection, V extends string = 'ALL_FIELDS'>(context: C, args: Omit<TypeSafeQueryArgType<DataProtection>, 'draft'> | Omit<Omit<TypeSafeQueryArgType<DataProtection>, 'draft'>, 'where'> & { where?: Partial<DataProtection> }, fieldlist: ReadonlyArray<U | V> | 'ALL_FIELDS'): ActionRequest<C, Promise<ReadReturnType<V>[]>>;
  /** @deprecated use ```client``` instead */
  export function query<C extends RequiredContext>(context: C, args: Omit<TypeSafeQueryArgType<DataProtection>, 'draft'>, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS'): ActionRequest<C, Promise<unknown[]>> {
    const convertedArgs = {data: {...args, fieldList: fieldlist}} as Record<string, any>
    const partialPayload = {action: 'query', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function read<C extends RequiredContext, U extends FieldSelector<DataProtectionWithAssociation>>(context: C, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): ActionRequest<C, Promise<ReadReturnTypeWithAssociations<DataProtectionWithAssociation, U>>>;
  /** @deprecated use ```client``` instead */
  export function read<C extends RequiredContext, U extends keyof DataProtection>(context: C, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): ActionRequest<C, Promise<ReadReturnType<U>>>;
  /** @deprecated use ```client``` instead */
  export function read<C extends RequiredContext>(context: C, where: UniqueWhereInput, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): ActionRequest<C, Promise<unknown>> {
    const convertedArgs = {data: {where: where, fieldList: fieldlist}, options: options} as Record<string, any>
    const partialPayload = {action: 'read', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function update<C extends RequiredContext, U extends FieldSelector<DataProtectionWithAssociation>>(context: C, where: UniqueWhereInput, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<WriteReturnTypeWithAssociations<DataProtectionWithAssociation, U>>>;
  /** @deprecated use ```client``` instead */
  export function update<C extends RequiredContext, U extends keyof DataProtection>(context: C, where: Partial<DataProtection>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<WriteReturnType<DataProtection, U>>>;
  /** @deprecated use ```client``` instead */
  export function update<C extends RequiredContext>(context: C, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: undefined): ActionRequest<C, Promise<unknown>> {
    const convertedArgs = {where: where, data: data, fieldList: fieldlist, options} as Record<string, any>
    const partialPayload = {action: 'update', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function updateMany<C extends RequiredContext, U extends FieldSelector<DataProtectionWithAssociation>>(context: C, where: Filter<DataProtectionWithAssociation>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<WriteReturnTypeWithAssociations<DataProtectionWithAssociation, U>[]>>;
  /** @deprecated use ```client``` instead */
  export function updateMany<C extends RequiredContext, U extends keyof DataProtection>(context: C, where: Partial<DataProtection>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<WriteReturnType<DataProtection, U>[]>>;
  /** @deprecated use ```client``` instead */
  export function updateMany<C extends RequiredContext>(context: C, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: undefined): ActionRequest<C, Promise<unknown[]>> {
    const convertedArgs = {data: {where: where, data: data, fieldList: fieldlist}, options} as Record<string, any>
    const partialPayload = {action: 'updateMany', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function upsert<C extends RequiredContext, U extends keyof DataProtection & string>(context: C, data: Partial<DataProtection>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<DataProtection, U>>>;
  /** @deprecated use ```client``` instead */
  export function upsert<C extends RequiredContext, U extends keyof DataProtection & string>(context: C, where: Partial<DataProtection>, data: Partial<DataProtection>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<DataProtection, U>>>;
  /** @deprecated use ```client``` instead */
  export function upsert<C extends RequiredContext, U extends keyof DataProtection & string>(context: C, whereOrData: Partial<DataProtection>, dataOrFieldList?: Partial<DataProtection> | ReadonlyArray<U> | 'ALL_FIELDS', fieldlistOrOptions?: ReadonlyArray<U> | 'ALL_FIELDS' | Omit<UpsertOptionsArg, 'draft'>, maybeOptions?: Omit<UpsertOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<DataProtection, U>>> {
    const convertedArgs = {data: {where: whereOrData, data: dataOrFieldList, fieldList: fieldlistOrOptions}, options: maybeOptions} as Record<string, any>
    const partialPayload = {action: 'upsert', ...convertedArgs}

    return new ActionRequest<C, Promise<WriteReturnType<DataProtection, U>>>(context, partialPayload);
  }
  export namespace client {
    /** Handle expiration date of all consent agreements */
    export declare function personalDataExpirationHandler(): ReturnType<func_personalDataExpirationHandler>
    /** Delete Expired Consent Agreements */
    export declare function deletePersonalData(args: { payload: Parameters<func_deletePersonalData>[1] }): ReturnType<func_deletePersonalData>
    /** Get Expired Personal Data */
    export declare function getPersonalData(args: { payload: Parameters<func_getPersonalData>[1] }): ReturnType<func_getPersonalData>
    /** Activate a specific configuration for a scope */
    export declare function activateConfiguration(args: { payload: Parameters<func_activateConfiguration>[1] }): ReturnType<func_activateConfiguration>
    /** Find list of Consents for a scope and a collection purpose */
    export declare function findConsentByScope(args: { payload: Parameters<func_findConsentByScope>[1] }): ReturnType<func_findConsentByScope>
    /** Create consent agreement for a person in a scope */
    export declare function createConsentAgreement(args: { payload: Parameters<func_createConsentAgreement>[1] }): ReturnType<func_createConsentAgreement>
    /** Get Scope List */
    export declare function getScopeList(args: { payload: Parameters<func_getScopeList>[1] }): ReturnType<func_getScopeList>
    /** get list of pre configurations */
    export declare function getPreConfigurationList(args: { payload: Parameters<func_getPreConfigurationList>[1] }): ReturnType<func_getPreConfigurationList>
    /** Find list of Consents for a scope and a collection purpose */
    export declare function getPurposeListWithScope(): ReturnType<func_getPurposeListWithScope>
    /** write a new object to the database. */
    export declare function create<U extends keyof DataProtection>(input: CreationFields, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): Promise<WriteReturnType<DataProtection, U>>

    /** write new objects to the database. */
    export declare function createMany<U extends keyof DataProtection>(input: CreationFields[], fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): Promise<WriteReturnType<DataProtection, U>[]>

    /** delete the object that is uniquely identified by the where condition. */
    export declare function Delete(where: UniqueWhereInput, options?: undefined): Promise<Partial<DataProtection>[]>

    /** delete all objects. */
    export declare function deleteAll(): Promise<Partial<DataProtection>[]>

    /** delete the objects that are identified by the where condition. */
    export declare function deleteMany(where: Filter<DataProtectionWithAssociation>): Promise<Partial<DataProtection>[]>;
    /** @deprecated use the alternative overloaded function */
    export declare function deleteMany(where: Partial<DataProtection>): Promise<Partial<DataProtection>[]>;
    export declare function deleteMany(where: Filter<DataProtection> | Partial<DataProtection>, options?: undefined): Promise<Partial<DataProtection>[]>

    /** returns a list of all objects matching the requirements. */
    export declare function query<U extends FieldSelector<DataProtectionWithAssociation>>(args: Omit<TypeSafeQueryArgType<DataProtectionWithAssociation>, 'draft'>, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<ReadReturnTypeWithAssociations<DataProtectionWithAssociation, U>[]>;
    /** @deprecated Use the alternative overloaded method for querying associations. */
    export declare function query<U extends keyof DataProtection, V extends string = 'ALL_FIELDS'>(args: Omit<TypeSafeQueryArgType<DataProtection>, 'draft'> | Omit<Omit<TypeSafeQueryArgType<DataProtection>, 'draft'>, 'where'> & { where?: Partial<DataProtection> }, fieldlist: ReadonlyArray<U | V> | 'ALL_FIELDS'): Promise<ReadReturnType<V>[]>;
    export declare function query(args: Omit<TypeSafeQueryArgType<DataProtection>, 'draft'>, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown[]>

    /** reads the object that is uniquely identified by the where condition. */
    export declare function read<U extends FieldSelector<DataProtectionWithAssociation>>(where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnTypeWithAssociations<DataProtectionWithAssociation, U>>;
    /** @deprecated Use the alternative overloaded method for reading associations. */
    export declare function read<U extends keyof DataProtection>(where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnType<U>>;
    export declare function read(where: UniqueWhereInput, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<unknown>

    /** writes modified values back to the database for a given node instance. */
    export declare function update<U extends FieldSelector<DataProtectionWithAssociation>>(where: UniqueWhereInput, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnTypeWithAssociations<DataProtectionWithAssociation, U>>;
    /** @deprecated Use the alternative overloaded method for writing with associations. */
    export declare function update<U extends keyof DataProtection>(where: Partial<DataProtection>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnType<DataProtection, U>>;
    export declare function update(where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: undefined): Promise<unknown>

    /** writes modified values back to the database for selected node instances. */
    export declare function updateMany<U extends FieldSelector<DataProtectionWithAssociation>>(where: Filter<DataProtectionWithAssociation>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnTypeWithAssociations<DataProtectionWithAssociation, U>[]>;
    /** @deprecated Use the alternative overloaded method for writing with associations. */
    export declare function updateMany<U extends keyof DataProtection>(where: Partial<DataProtection>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnType<DataProtection, U>[]>;
    export declare function updateMany(where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: undefined): Promise<unknown[]>

    /** creates or updates object identified by where. */
    export declare function upsert<U extends keyof DataProtection & string>(data: Partial<DataProtection>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<DataProtection, U>>;
    /** @deprecated use the overload without an explicit where clause */
    export declare function upsert<U extends keyof DataProtection & string>(where: Partial<DataProtection>, data: Partial<DataProtection>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<DataProtection, U>>;
    export declare function upsert<U extends keyof DataProtection & string>(whereOrData: Partial<DataProtection>, dataOrFieldList?: Partial<DataProtection> | ReadonlyArray<U> | 'ALL_FIELDS', fieldlistOrOptions?: ReadonlyArray<U> | 'ALL_FIELDS' | Omit<UpsertOptionsArg, 'draft'>, maybeOptions?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<DataProtection, U>>
  }
}
