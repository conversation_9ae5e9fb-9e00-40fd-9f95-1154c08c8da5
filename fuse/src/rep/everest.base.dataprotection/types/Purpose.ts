/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// ******************************************************************************
// Generated Everest Types
// ******************************************************************************

import type { Decimal, RoundingMode } from '@everestsystems/decimal';
import type {
  ArgType,
  Association,
  AssociationManagementArgs,
  AsyncResult,
  Controller,
  ControllerClientProvider,
  CreateOptionsArg,
  DataWithMetadata,
  DeleteOptionsArg,
  FieldSelector,
  Filter,
  flattenFieldSelector,
  GraphInstanceEntry,
  KernelRequestOptions,
  NonAssociationFields,
  Optional,
  PurgeArgs,
  ReadOptionsArg,
  ReadReturnType as ReadReturnTypeGeneric,
  ReadReturnTypeWithAssociations,
  RequireAtLeastOne,
  SemanticSearchArg,
  SemanticSearchRequestOptions,
  TypeSafeQueryArgType,
  UpdateManyOptionsArg,
  UpdateOptionsArg,
  UpsertOptionsArg,
  WriteReturnType,
  WriteReturnTypeWithAssociations,
} from '@everestsystems/content-core';
import type { EvstUUID as everest_appserver_primitive_UUID } from "@pkg/everest.appserver/types/primitives/UUID";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { EvstUserEmail as everest_appserver_usermgmt_primitive_user_UserEmail } from "@pkg/everest.appserver.usermgmt/types/primitives/user/UserEmail";
import type { EvstDateTime as everest_appserver_primitive_DateTime } from "@pkg/everest.appserver/types/primitives/DateTime";
import type { EvstRichText as everest_appserver_primitive_RichText } from "@pkg/everest.appserver/types/primitives/RichText";
import type { EvstCollectionPurposeType as everest_appserver_enum_CollectionPurposeType } from "@pkg/everest.appserver/types/enums/CollectionPurposeType";
import type { EvstNavigationPath as everest_base_dataprotection_composite_NavigationPath } from "@pkg/everest.base.dataprotection/types/composites/NavigationPath";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { EvstModelUrn as everest_appserver_primitive_metadata_ModelUrn } from "@pkg/everest.appserver/types/primitives/metadata/ModelUrn";
import type { DataConfiguration as everest_base_dataprotection_model_node_DataConfiguration } from "@pkg/everest.base.dataprotection/types/DataConfiguration";
import type { ConsentCollection as everest_base_dataprotection_model_node_ConsentCollection } from "@pkg/everest.base.dataprotection/types/ConsentCollection";
import type { LegalBasis as everest_base_dataprotection_model_node_LegalBasis } from "@pkg/everest.base.dataprotection/types/LegalBasis";

/**
 * Types for Purpose
 *
 * @everest-node-metadata='{"visibility":"public"}'
 */
export namespace Purpose {
  export type CreationFields = Pick<Purpose, 'uuid' | 'externalId' | 'active' | 'content' | 'name' | 'purposeNumber' | 'purposeType' | 'retentionAnchorPath' | 'retentionTimeInDays' | 'scopeModelURN' | 'scopePath' | 'scopeUUID'>;
  export type UniqueFields = Pick<Purpose, 'id' | 'uuid' | 'purposeNumber'>;
  export type UniqueWhereInput = (RequireAtLeastOne<UniqueFields>) & Partial<Purpose>;
  export type ReadReturnType<U extends string | number | symbol = keyof Purpose> = ReadReturnTypeGeneric<Purpose, U>;

  export interface IControllerClient extends Omit<Controller<Purpose>, 'all' | 'getHistory' | 'lookup' | 'purge' | 'queryWithMetadata' | 'semanticSearch' | 'upsertMany'> {}

  /**
   * Purpose for data collection
   */
  export type Purpose = {
    /** Database generated identifier of this record. */
    id: number;
    /** Database generated version of this record. */
    version?: number;
    uuid?: everest_appserver_primitive_UUID | null;
    /**
     * ID of the system from which this record was sourced
     */
    externalId?: everest_appserver_primitive_Text | null;
    /**
     * Indicates whether this record is usable for business users
     */
    active?: everest_appserver_primitive_TrueFalse | null;
    /**
     * User that created this record
     */
    createdBy: everest_appserver_usermgmt_primitive_user_UserEmail;
    /**
     * Date/time when the record was created
     */
    createdDate: everest_appserver_primitive_DateTime;
    /**
     * User that last modified the record
     */
    lastModifiedBy: everest_appserver_usermgmt_primitive_user_UserEmail;
    /**
     * Date/time of the last modification to the record
     */
    lastModifiedDate: everest_appserver_primitive_DateTime;
    /**
     * Full (legal) text for the purpose
     */
    content?: everest_appserver_primitive_RichText | null;
    /**
     * Short name for the purpose
     */
    name: everest_appserver_primitive_Text;
    /**
     * Unique purpose number
     */
    purposeNumber?: everest_appserver_primitive_Text | null;
    /**
     * Purpose type for the data collection
     */
    purposeType?: everest_appserver_enum_CollectionPurposeType | null;
    /**
     * EFL or association path to date field, that should be used as starting point for the retention time
     */
    retentionAnchorPath: everest_base_dataprotection_composite_NavigationPath;
    /**
     * Retention time of data bound to this purpose in days
     */
    retentionTimeInDays: everest_appserver_primitive_Number;
    scopeModelURN?: everest_appserver_primitive_metadata_ModelUrn | null;
    scopePath?: everest_base_dataprotection_composite_NavigationPath | null;
    scopeUUID?: everest_appserver_primitive_UUID | null;
    };
  /**
   * Purpose for data collection
   */
  export type PurposeWithAssociation = Purpose & {
    ["DataConfiguration-Purpose"]?: Association<everest_base_dataprotection_model_node_DataConfiguration.DataConfigurationWithAssociation>[];
    ["Consent-Purpose"]?: Association<everest_base_dataprotection_model_node_ConsentCollection.ConsentCollectionWithAssociation>[];
    ["Purpose-LegalBasis"]?: Association<everest_base_dataprotection_model_node_LegalBasis.LegalBasisWithAssociation>[];
    };
  /** @deprecated - use {@link MODEL_URN} instead */
  export const name = 'urn:evst:everest:base/dataprotection:model/node:Purpose';
  export const MODEL_URN = 'urn:evst:everest:base/dataprotection:model/node:Purpose';
  /** @deprecated - use {@link MODEL_URN} instead */
  export const MODEL_ID = 'everest.base.dataprotection/PurposeModel.Purpose';
  export const MODEL_UUID = '5796b9b4-4ec8-4649-98df-0582eb54b238';

  /** @return a model controller instance for Purpose. */
  export function client(env: ControllerClientProvider) {
    return env.controllerClient<Purpose.IControllerClient>(MODEL_URN);
  }

  /** write a new object to the database. */
  export async function create<U extends keyof Purpose>(env: ControllerClientProvider, input: CreationFields, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): Promise<WriteReturnType<Purpose, U>> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).create(input, fields, options);
  }

  /** write new objects to the database. */
  export async function createMany<U extends keyof Purpose>(env: ControllerClientProvider, input: CreationFields[], fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): Promise<WriteReturnType<Purpose, U>[]> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).createMany(input, fields, options);
  }

  /** delete the object that is uniquely identified by the where condition. */
  export async function Delete(env: ControllerClientProvider, where: UniqueWhereInput): Promise<Partial<Purpose>[]> {
    return (await client(env)).delete(where);
  }

  /** delete all objects. */
  export async function deleteAll(env: ControllerClientProvider): Promise<Partial<Purpose>[]> {
    return (await client(env)).deleteAll();
  }

  /** delete the objects that are identified by the where condition. */
  export async function deleteMany(env: ControllerClientProvider, where: Filter<PurposeWithAssociation>): Promise<Partial<Purpose>[]>;
  /** @deprecated use the alternative overloaded function */
  export async function deleteMany(env: ControllerClientProvider, where: Partial<Purpose>): Promise<Partial<Purpose>[]>;
  export async function deleteMany(env: ControllerClientProvider, where: Filter<Purpose> | Partial<Purpose>): Promise<Partial<Purpose>[]> {
    return (await client(env)).deleteMany(where as Filter<Purpose>);
  }

  /** returns a list of all objects matching the requirements. */
  export async function query<U extends FieldSelector<PurposeWithAssociation>>(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<PurposeWithAssociation>, 'draft'>, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<ReadReturnTypeWithAssociations<PurposeWithAssociation, U>[]>;
  /** @deprecated Use the alternative overloaded method for querying associations. */
  export async function query<U extends keyof Purpose, V extends string = 'ALL_FIELDS'>(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<Purpose>, 'draft'> | Omit<Omit<TypeSafeQueryArgType<Purpose>, 'draft'>, 'where'> & { where?: Partial<Purpose> }, fieldlist: ReadonlyArray<U | V> | 'ALL_FIELDS'): Promise<ReadReturnType<V>[]>;
  export async function query(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<Purpose>, 'draft'>, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown[]> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).query(args, fields as ReadonlyArray<string>);
  }

  /** reads the object that is uniquely identified by the where condition. */
  export async function read<U extends FieldSelector<PurposeWithAssociation>>(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnTypeWithAssociations<PurposeWithAssociation, U>>;
  /** @deprecated Use the alternative overloaded method for reading associations. */
  export async function read<U extends keyof Purpose>(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnType<U>>;
  export async function read(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<unknown> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).read(where, fields as ReadonlyArray<keyof Purpose>, options);
  }

  /** writes modified values back to the database for a given node instance. */
  export async function update<U extends FieldSelector<PurposeWithAssociation>>(env: ControllerClientProvider, where: UniqueWhereInput, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnTypeWithAssociations<PurposeWithAssociation, U>>;
  /** @deprecated Use the alternative overloaded method for writing with associations. */
  export async function update<U extends keyof Purpose>(env: ControllerClientProvider, where: Partial<Purpose>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnType<Purpose, U>>;
  export async function update(env: ControllerClientProvider, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).update(where, data, fields as ReadonlyArray<keyof Purpose>);
  }

  /** writes modified values back to the database for selected node instances. */
  export async function updateMany<U extends FieldSelector<PurposeWithAssociation>>(env: ControllerClientProvider, where: Filter<PurposeWithAssociation>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnTypeWithAssociations<PurposeWithAssociation, U>[]>;
  /** @deprecated Use the alternative overloaded method for writing with associations. */
  export async function updateMany<U extends keyof Purpose>(env: ControllerClientProvider, where: Partial<Purpose>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnType<Purpose, U>[]>;
  export async function updateMany(env: ControllerClientProvider, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown[]> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).updateMany(where, data, fields as ReadonlyArray<keyof Purpose>);
  }

  /** creates or updates object identified by where. */
  export async function upsert<U extends keyof Purpose & string>(env: ControllerClientProvider, data: Partial<Purpose>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<Purpose, U>>;
  /** @deprecated use the overload without an explicit where clause */
  export async function upsert<U extends keyof Purpose & string>(env: ControllerClientProvider, where: Partial<Purpose>, data: Partial<Purpose>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<Purpose, U>>;
  export async function upsert<U extends keyof Purpose & string>(env: ControllerClientProvider, whereOrData: Partial<Purpose>, dataOrFieldList?: Partial<Purpose> | ReadonlyArray<U> | 'ALL_FIELDS', fieldlistOrOptions?: ReadonlyArray<U> | 'ALL_FIELDS' | Omit<UpsertOptionsArg, 'draft'>, maybeOptions?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<Purpose, U>> {
    return (await client(env)).upsert(whereOrData, dataOrFieldList, fieldlistOrOptions, maybeOptions);
  }
}
