package everest.base.dataprotection

node Agreement {
	description: 'Consent agreements of a person'

	field agreed TrueFalse {
		editable
		persisted
		label: 'Agreed'
		description: 'Flag that the person agreed to the linked consent'
	}

	field agreedAt DateTime {
		editable
		persisted
		label: 'Agreed At'
		description: 'Timestamp of agreement / disagreement'
	}

	field consentUUID UUID {
		required
		editable
		persisted
		label: 'Consent UUID'
		description: 'Link to the consent to agree'
	}

	field personBaseUUID UUID {
		required
		editable
		persisted
		label: 'Person Base UUID'
		description: 'Link to the person who agree'
	}

	field expiryDate DateTime {
		editable
		persisted
		label: 'Expiry Date'
		description: 'An optional field that could be used to withdrawal or block removing of data'
	}

	generated association alias agreement for PersonBase-Agreement

	generated association alias consent for Agreement-Consent
}

association PersonBase-Agreement {
	source: Agreement
	sourceField: personBaseUUID
	target: BasePerson
	targetField: uuid
	kind: static
	type: reference
	multiplicity: many-to-one
	description: ''
}

association Agreement-Consent {
	source: Agreement
	sourceField: consentUUID
	target: ConsentCollection
	targetField: uuid
	kind: static
	type: reference
	multiplicity: many-to-one
	description: ''
}
