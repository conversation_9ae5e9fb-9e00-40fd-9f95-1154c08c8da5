package everest.base.dataprotection

node DataProtection {
	description: 'Node that hold actions for Data Protection'
	action personalDataExpirationHandler(): JSON
	action deletePersonalData(payload: JSON): JSON
	action getPersonalData(payload: JSON): JSON
	action activateConfiguration(payload: JSON): JSON
	action findConsentByScope(payload: JSON): JSON
	action createConsentAgreement(payload: JSON): JSON
	action getScopeList(payload: JSON): JSON
	action getPreConfigurationList(payload: JSON): JSON
	query action getPurposeListWithScope(): JSON
}
