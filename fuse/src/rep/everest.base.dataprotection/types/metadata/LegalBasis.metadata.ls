package everest.base.dataprotection

node LegalBasis {
	description: 'A mapping between person and legal purposes'
	label: 'Legal Basis'

	field personBaseUUID UUID {
		required
		editable
		persisted
		label: 'Person Base UUID'
	}

	field purposeUUID UUID {
		required
		editable
		persisted
		label: 'Purpose UUID'
	}

	field expiryDate Date {
		editable
		persisted
		label: 'Expiry Date'
	}

	generated association alias baseperson for BasePerson-LegalBasis

	generated association alias purpose for Purpose-LegalBasis
}

association BasePerson-LegalBasis {
	source: LegalBasis
	sourceField: personBaseUUID
	target: Base<PERSON>erson
	targetField: uuid
	kind: static
	type: reference
	multiplicity: many-to-one
	description: ''
}

association Purpose-LegalBasis {
	source: LegalBasis
	sourceField: purposeUUID
	target: Purpose
	targetField: uuid
	kind: static
	type: reference
	multiplicity: many-to-one
	description: ''
}
