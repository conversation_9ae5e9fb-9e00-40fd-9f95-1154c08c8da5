package everest.base.dataprotection

node Purpose {
	description: 'Purpose for data collection'

	field purposeNumber number-range<PurposeNumber>{
		editable
		unique
		persisted
		label: 'Purpose Number'
		description: 'Unique purpose number'
	}

	field purposeType enum<everest.appserver::CollectionPurposeType>{
		editable
		persisted
		label: 'Purpose Type'
		description: 'Purpose type for the data collection'
	}

	field name Text {
		required
		editable
		persisted
		label: 'Name'
		description: 'Short name for the purpose'
	}

	field content RichText {
		editable
		persisted
		label: 'Content'
		description: 'Full (legal) text for the purpose'
	}

	field retentionTimeInDays Number<Int> {
		required
		editable
		persisted
		label: 'Retention Time in Days'
		description: 'Retention time of data bound to this purpose in days'
	}

	field retentionAnchorPath composite<NavigationPath>{
		required
		editable
		persisted
		label: 'Retention Anchor Path'
		description: 'EFL or association path to date field, that should be used as starting point for the retention time'
	}

	field scopeUUID UUID {
		editable
		persisted
		label: 'Scope UUID'
	}

	field scopePath composite<NavigationPath>{
		editable
		persisted
		label: 'Scope Path'
	}

	field scopeModelURN ModelUrn {
		editable
		persisted
		label: 'Scope Model URN'
	}

	generated association alias dataconfiguration for DataConfiguration-Purpose

	generated association alias consent for Consent-Purpose

	generated association alias legalbasis for Purpose-LegalBasis
}
