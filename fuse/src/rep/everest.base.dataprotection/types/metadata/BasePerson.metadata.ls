package everest.base.dataprotection

node <PERSON><PERSON><PERSON> {
	description: 'Node that contains base personal information'
	draftable

	field firstName Text {
		editable
		persisted
		label: 'First Name'
	}

	field lastName Text {
		editable
		persisted
		label: 'Last Name'
	}

	field gender Text {
		editable
		persisted
		label: 'Gender'
	}

	field dateOfBirth Date {
		editable
		persisted
		label: 'Date Of Birth'
	}

	field email Email {
		editable
		persisted
		label: 'Email'
	}

	field middleName Text {
		editable
		persisted
		label: 'Middle Name'
	}

	generated association alias personbase for PersonBase-Agreement

	generated association alias legalbasis for BasePerson-LegalBasis
}
