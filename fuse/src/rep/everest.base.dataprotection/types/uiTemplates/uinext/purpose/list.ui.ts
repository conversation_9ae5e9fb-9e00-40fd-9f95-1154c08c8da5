/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable no-trailing-spaces */
/* eslint-disable no-multiple-empty-lines */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// ******************************************************************************
// Generated Everest Ui Template Types
// ******************************************************************************
import { ReadReturnTypeWithAssociations, UIExecutionContext } from '@everestsystems/content-core';

import Purpose_getPurposeListWithScope from '@pkg/everest.base.dataprotection/actions/getPurposeListWithScope.action'
import {DataProtectionUI as DataProtectionNamespace } from '@pkg/everest.base.dataprotection/types/DataProtection.ui'

type BasicExecutionContext = UIExecutionContext.BasicExecutionContext;
type InnerPromiseType<T> = T extends Promise<infer R> ? R : T;
type UIModelNodeListType<T> =  UIExecutionContext.UIModelNodeListType<T>
type ListElementType<T> = T extends (infer R)[] ? R : unknown;

export namespace ListUiTemplate {



export type DataProtectionWithAssociation = DataProtectionNamespace.DataProtectionWithAssociation
export type DataProtection = DataProtectionNamespace.DataProtection



export type CommonDataType = { uuid?: string; _nodeReference: string; /** @deprecated use _nodeParentReference */ _parentReference?: string; _nodeParentReference?: string; $metadata?: { isDraft?: boolean } }
export type SingleNodeItemType = {
        Purpose: ListElementType<InnerPromiseType<ReturnType<typeof Purpose_getPurposeListWithScope>>> & CommonDataType;
      }
      
export type ListData = {
        Purpose?: UIModelNodeListType<SingleNodeItemType['Purpose']>;
      }
      

export type ListContext = BasicExecutionContext
  & {
    data: ListData;
    state: Record<string, unknown>;
  }
  ;


type OverrideDataPayload = {
  [K in keyof ListData]: ListData[K] extends UIModelNodeListType<any>
    ? UIModelNodeListType<any>
    : any;
};

/** 
 * This Helper Type allows you to override the type of the data properties or extend them.
  * if the node you are overriding is of type list in the ui-template then
  * you need to wrap your type in {UIExecutionContext.UIModelNodeListType<T>} instead of normal array or list
  */
export type OverrideListData<
  Payload extends Record<string, unknown> & Partial<OverrideDataPayload>
> = Omit<ListContext, 'data'> & {
  data: {
    [K in keyof ListData | keyof Payload]: K extends keyof Payload
      ? Payload[K]
      : K extends keyof ListData
      ? ListData[K]
      : unknown;
  };
};


}
 /** @deprecated use `ListUiTemplate.DataProtectionWithAssociation` instead. */ export type DataProtectionWithAssociation = ListUiTemplate.DataProtectionWithAssociation;
 /** @deprecated use `ListUiTemplate.DataProtection` instead. */ export type DataProtection = ListUiTemplate.DataProtection;
 /** @deprecated use `ListUiTemplate.CommonDataType` instead. */ export type CommonDataType = ListUiTemplate.CommonDataType;
 /** @deprecated use `ListUiTemplate.SingleNodeItemType` instead. */ export type SingleNodeItemType = ListUiTemplate.SingleNodeItemType;
 /** @deprecated use `ListUiTemplate.ListData` instead. */ export type ListData = ListUiTemplate.ListData;
 /** @deprecated use `ListUiTemplate.ListContext` instead. */ export type ListContext = ListUiTemplate.ListContext;

type OverrideDataPayload = {
  [K in keyof ListUiTemplate.ListData]: ListUiTemplate.ListData[K] extends UIModelNodeListType<any>
    ? UIModelNodeListType<any>
    : any;
};

/** @deprecated use `ListUiTemplate.OverrideListData` instead.
 * This Helper Type allows you to override the type of the data properties or extend them.
  * if the node you are overriding is of type list in the ui-template then
  * you need to wrap your type in {UIExecutionContext.UIModelNodeListType<T>} instead of normal array or list
  */
export type OverrideListData<
  Payload extends Record<string, unknown> & Partial<OverrideDataPayload>
> = Omit<ListUiTemplate.ListContext, 'data'> & {
  data: {
    [K in keyof ListUiTemplate.ListData | keyof Payload]: K extends keyof Payload
      ? Payload[K]
      : K extends keyof ListUiTemplate.ListData
      ? ListUiTemplate.ListData[K]
      : unknown;
  };
};
