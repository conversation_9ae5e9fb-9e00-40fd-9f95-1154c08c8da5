/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// ******************************************************************************
// Generated Everest Types
// ******************************************************************************

import type { Decimal, RoundingMode } from '@everestsystems/decimal';
import type {
  ArgType,
  Association,
  AssociationManagementArgs,
  AsyncResult,
  Controller,
  ControllerClientProvider,
  CreateOptionsArg,
  DataWithMetadata,
  DeleteOptionsArg,
  FieldSelector,
  Filter,
  flattenFieldSelector,
  GraphInstanceEntry,
  KernelRequestOptions,
  NonAssociationFields,
  Optional,
  PurgeArgs,
  ReadOptionsArg,
  ReadReturnType as ReadReturnTypeGeneric,
  ReadReturnTypeWithAssociations,
  RequireAtLeastOne,
  SemanticSearchArg,
  SemanticSearchRequestOptions,
  TypeSafeQueryArgType,
  UpdateManyOptionsArg,
  UpdateOptionsArg,
  UpsertOptionsArg,
  WriteReturnType,
  WriteReturnTypeWithAssociations,
} from '@everestsystems/content-core';
import type { EvstUUID as everest_appserver_primitive_UUID } from "@pkg/everest.appserver/types/primitives/UUID";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { EvstUserEmail as everest_appserver_usermgmt_primitive_user_UserEmail } from "@pkg/everest.appserver.usermgmt/types/primitives/user/UserEmail";
import type { EvstDateTime as everest_appserver_primitive_DateTime } from "@pkg/everest.appserver/types/primitives/DateTime";
import type { EvstDate as everest_appserver_primitive_Date } from "@pkg/everest.appserver/types/primitives/Date";
import type { EvstEmail as everest_appserver_primitive_Email } from "@pkg/everest.appserver/types/primitives/Email";
import type { Agreement as everest_base_dataprotection_model_node_Agreement } from "@pkg/everest.base.dataprotection/types/Agreement";
import type { LegalBasis as everest_base_dataprotection_model_node_LegalBasis } from "@pkg/everest.base.dataprotection/types/LegalBasis";

/**
 * Types for BasePerson
 *
 * @everest-node-metadata='{"visibility":"public"}'
 */
export namespace BasePerson {
  export type CreationFields = Pick<BasePerson, 'uuid' | 'externalId' | 'active' | 'dateOfBirth' | 'email' | 'firstName' | 'gender' | 'lastName' | 'middleName'>;
  export type UniqueFields = Pick<BasePerson, 'id' | 'uuid'>;
  export type UniqueWhereInput = (RequireAtLeastOne<UniqueFields>) & Partial<BasePerson>;
  export type ReadReturnType<U extends string | number | symbol = keyof BasePerson> = ReadReturnTypeGeneric<BasePerson, U>;

  export interface IControllerClient extends Omit<Controller<BasePerson>, 'all' | 'getHistory' | 'lookup' | 'purge' | 'queryWithMetadata' | 'semanticSearch' | 'upsertMany'> {}

  /**
   * Node that contains base personal information
   */
  export type BasePerson = {
    /** Database generated identifier of this record. */
    id: number;
    /** Database generated version of this record. */
    version?: number;
    uuid?: everest_appserver_primitive_UUID | null;
    /**
     * ID of the system from which this record was sourced
     */
    externalId?: everest_appserver_primitive_Text | null;
    /**
     * Indicates whether this record is usable for business users
     */
    active?: everest_appserver_primitive_TrueFalse | null;
    /**
     * User that created this record
     */
    createdBy: everest_appserver_usermgmt_primitive_user_UserEmail;
    /**
     * Date/time when the record was created
     */
    createdDate: everest_appserver_primitive_DateTime;
    /**
     * User that last modified the record
     */
    lastModifiedBy: everest_appserver_usermgmt_primitive_user_UserEmail;
    /**
     * Date/time of the last modification to the record
     */
    lastModifiedDate: everest_appserver_primitive_DateTime;
    dateOfBirth?: everest_appserver_primitive_Date | null;
    email?: everest_appserver_primitive_Email | null;
    firstName?: everest_appserver_primitive_Text | null;
    gender?: everest_appserver_primitive_Text | null;
    lastName?: everest_appserver_primitive_Text | null;
    middleName?: everest_appserver_primitive_Text | null;
    };
  /**
   * Node that contains base personal information
   */
  export type BasePersonWithAssociation = BasePerson & {
    ["PersonBase-Agreement"]?: Association<everest_base_dataprotection_model_node_Agreement.AgreementWithAssociation>[];
    ["BasePerson-LegalBasis"]?: Association<everest_base_dataprotection_model_node_LegalBasis.LegalBasisWithAssociation>[];
    };
  /** @deprecated - use {@link MODEL_URN} instead */
  export const name = 'urn:evst:everest:base/dataprotection:model/node:BasePerson';
  export const MODEL_URN = 'urn:evst:everest:base/dataprotection:model/node:BasePerson';
  /** @deprecated - use {@link MODEL_URN} instead */
  export const MODEL_ID = 'everest.base.dataprotection/BasePersonModel.BasePerson';
  export const MODEL_UUID = 'dcc4e44d-75f4-4e67-9bdf-06a280e6ecff';

  /** @return a model controller instance for BasePerson. */
  export function client(env: ControllerClientProvider) {
    return env.controllerClient<BasePerson.IControllerClient>(MODEL_URN);
  }

  /** write a new object to the database. */
  export async function create<U extends keyof BasePerson>(env: ControllerClientProvider, input: CreationFields, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: CreateOptionsArg): Promise<WriteReturnType<BasePerson, U>> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).create(input, fields, options);
  }

  /** write new objects to the database. */
  export async function createMany<U extends keyof BasePerson>(env: ControllerClientProvider, input: CreationFields[], fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: CreateOptionsArg): Promise<WriteReturnType<BasePerson, U>[]> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).createMany(input, fields, options);
  }

  /** delete the object that is uniquely identified by the where condition. */
  export async function Delete(env: ControllerClientProvider, where: UniqueWhereInput, options?: DeleteOptionsArg): Promise<Partial<BasePerson>[]> {
    return (await client(env)).delete(where, options);
  }

  /** delete all objects. */
  export async function deleteAll(env: ControllerClientProvider): Promise<Partial<BasePerson>[]> {
    return (await client(env)).deleteAll();
  }

  /** delete the objects that are identified by the where condition. */
  export async function deleteMany(env: ControllerClientProvider, where: Filter<BasePersonWithAssociation>, options?: DeleteOptionsArg): Promise<Partial<BasePerson>[]>;
  /** @deprecated use the alternative overloaded function */
  export async function deleteMany(env: ControllerClientProvider, where: Partial<BasePerson>, options?: DeleteOptionsArg): Promise<Partial<BasePerson>[]>;
  export async function deleteMany(env: ControllerClientProvider, where: Filter<BasePerson> | Partial<BasePerson>, options?: DeleteOptionsArg): Promise<Partial<BasePerson>[]> {
    return (await client(env)).deleteMany(where, options);
  }

  /** returns a list of all objects matching the requirements. */
  export async function query<U extends FieldSelector<BasePersonWithAssociation>>(env: ControllerClientProvider, args: TypeSafeQueryArgType<BasePersonWithAssociation>, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<ReadReturnTypeWithAssociations<BasePersonWithAssociation, U>[]>;
  /** @deprecated Use the alternative overloaded method for querying associations. */
  export async function query<U extends keyof BasePerson, V extends string = 'ALL_FIELDS'>(env: ControllerClientProvider, args: TypeSafeQueryArgType<BasePerson> | Omit<TypeSafeQueryArgType<BasePerson>, 'where'> & { where?: Partial<BasePerson> }, fieldlist: ReadonlyArray<U | V> | 'ALL_FIELDS'): Promise<ReadReturnType<V>[]>;
  export async function query(env: ControllerClientProvider, args: TypeSafeQueryArgType<BasePerson>, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown[]> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).query(args, fields as ReadonlyArray<string>);
  }

  /** reads the object that is uniquely identified by the where condition. */
  export async function read<U extends FieldSelector<BasePersonWithAssociation>>(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | ReadOptionsArg): Promise<ReadReturnTypeWithAssociations<BasePersonWithAssociation, U>>;
  /** @deprecated Use the alternative overloaded method for reading associations. */
  export async function read<U extends keyof BasePerson>(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | ReadOptionsArg): Promise<ReadReturnType<U>>;
  export async function read(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: number | Date | ReadOptionsArg): Promise<unknown> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).read(where, fields as ReadonlyArray<keyof BasePerson>, options);
  }

  /** writes modified values back to the database for a given node instance. */
  export async function update<U extends FieldSelector<BasePersonWithAssociation>>(env: ControllerClientProvider, where: UniqueWhereInput, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: UpdateOptionsArg): Promise<WriteReturnTypeWithAssociations<BasePersonWithAssociation, U>>;
  /** @deprecated Use the alternative overloaded method for writing with associations. */
  export async function update<U extends keyof BasePerson>(env: ControllerClientProvider, where: Partial<BasePerson>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: UpdateOptionsArg): Promise<WriteReturnType<BasePerson, U>>;
  export async function update(env: ControllerClientProvider, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: UpdateOptionsArg): Promise<unknown> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).update(where, data, fields as ReadonlyArray<keyof BasePerson>, options);
  }

  /** writes modified values back to the database for selected node instances. */
  export async function updateMany<U extends FieldSelector<BasePersonWithAssociation>>(env: ControllerClientProvider, where: Filter<BasePersonWithAssociation>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: UpdateManyOptionsArg): Promise<WriteReturnTypeWithAssociations<BasePersonWithAssociation, U>[]>;
  /** @deprecated Use the alternative overloaded method for writing with associations. */
  export async function updateMany<U extends keyof BasePerson>(env: ControllerClientProvider, where: Partial<BasePerson>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: UpdateManyOptionsArg): Promise<WriteReturnType<BasePerson, U>[]>;
  export async function updateMany(env: ControllerClientProvider, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: UpdateManyOptionsArg): Promise<unknown[]> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).updateMany(where, data, fields as ReadonlyArray<keyof BasePerson>, options);
  }

  /** creates or updates object identified by where. */
  export async function upsert<U extends keyof BasePerson & string>(env: ControllerClientProvider, data: Partial<BasePerson>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: UpsertOptionsArg): Promise<WriteReturnType<BasePerson, U>>;
  /** @deprecated use the overload without an explicit where clause */
  export async function upsert<U extends keyof BasePerson & string>(env: ControllerClientProvider, where: Partial<BasePerson>, data: Partial<BasePerson>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: UpsertOptionsArg): Promise<WriteReturnType<BasePerson, U>>;
  export async function upsert<U extends keyof BasePerson & string>(env: ControllerClientProvider, whereOrData: Partial<BasePerson>, dataOrFieldList?: Partial<BasePerson> | ReadonlyArray<U> | 'ALL_FIELDS', fieldlistOrOptions?: ReadonlyArray<U> | 'ALL_FIELDS' | UpsertOptionsArg, maybeOptions?: UpsertOptionsArg): Promise<WriteReturnType<BasePerson, U>> {
    return (await client(env)).upsert(whereOrData, dataOrFieldList, fieldlistOrOptions, maybeOptions);
  }
}
