/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// ******************************************************************************
// Generated Everest Types
// ******************************************************************************

import type { Decimal, RoundingMode } from '@everestsystems/decimal';
import type {
  ArgType,
  Association,
  AssociationManagementArgs,
  AsyncResult,
  Controller,
  ControllerClientProvider,
  CreateOptionsArg,
  DataWithMetadata,
  DeleteOptionsArg,
  FieldSelector,
  Filter,
  flattenFieldSelector,
  GraphInstanceEntry,
  KernelRequestOptions,
  NonAssociationFields,
  Optional,
  PurgeArgs,
  ReadOptionsArg,
  ReadReturnType as ReadReturnTypeGeneric,
  ReadReturnTypeWithAssociations,
  RequireAtLeastOne,
  SemanticSearchArg,
  SemanticSearchRequestOptions,
  TypeSafeQueryArgType,
  UpdateManyOptionsArg,
  UpdateOptionsArg,
  UpsertOptionsArg,
  WriteReturnType,
  WriteReturnTypeWithAssociations,
} from '@everestsystems/content-core';

import { UIExecutionContext } from '@everestsystems/content-core';
import type { Agreement as everest_base_dataprotection_model_node_Agreement } from "@pkg/everest.base.dataprotection/types/Agreement";
import type { LegalBasis as everest_base_dataprotection_model_node_LegalBasis } from "@pkg/everest.base.dataprotection/types/LegalBasis";
import type { BasePerson as everest_base_dataprotection_model_node_BasePerson } from "@pkg/everest.base.dataprotection/types/BasePerson";

/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */

type RequiredContext = {
  data: Record<string, any>;
  actions: {
    run: (...args: any[]) => any;
  };
};

type ApiError = {
  message: string;
  name: string;
  errorCode: string;
  stack?: string;
};

type InnerPromiseType<T> = T extends Promise<infer R> ? R : T;

type ActionResultOrError<T1 extends string | number | symbol, T2> = {[P in T1]?: T2} & {error?: ApiError};

type ArrayIfNeeded<C extends RequiredContext, K extends keyof C['data'], T> = C['data'][K] extends UIExecutionContext.UIModelNodeListType<any> ? T[] : T;

export namespace BasePersonUI {
  /**
   * Node that contains base personal information
   */
  export type BasePersonWithAssociation = BasePerson & {
    ["PersonBase-Agreement"]?: Association<everest_base_dataprotection_model_node_Agreement.AgreementWithAssociation>[];
    ["BasePerson-LegalBasis"]?: Association<everest_base_dataprotection_model_node_LegalBasis.LegalBasisWithAssociation>[];
    };
  export interface IControllerClient extends everest_base_dataprotection_model_node_BasePerson.IControllerClient {}

  export type BasePerson = everest_base_dataprotection_model_node_BasePerson.BasePerson;
  export type CreationFields = Pick<BasePerson, 'uuid' | 'externalId' | 'active' | 'dateOfBirth' | 'email' | 'firstName' | 'gender' | 'lastName' | 'middleName'>;
  export type UniqueFields = Pick<BasePerson, 'id' | 'uuid'>;
  export type UniqueWhereInput = (RequireAtLeastOne<UniqueFields>) & Partial<BasePerson>;
  export type ReadReturnType<U extends string | number | symbol = keyof BasePerson> = ReadReturnTypeGeneric<BasePerson, U>;

  class ActionRequest<C extends RequiredContext, FuncReturnType> {
    constructor(private readonly context: C, private readonly partialPayload: Record<string, any>) {
    }

    private async __run__(payload: any) {
      return this.context.actions.run(payload);
    }

    payload<K extends keyof C['data']>(nodeAlias: K): Record<string, any> {
      return {[nodeAlias]: this.partialPayload};
    }

    run<K extends keyof C['data']>(nodeAlias: K): Promise<ActionResultOrError<K, ArrayIfNeeded<C, K, InnerPromiseType<FuncReturnType>>>> {
      const payload = this.payload(nodeAlias);
      return this.__run__(payload);
    }
  }


  /** @deprecated - use {@link MODEL_URN} instead */
  export const name = 'urn:evst:everest:base/dataprotection:model/node:BasePerson';
  export const MODEL_URN = 'urn:evst:everest:base/dataprotection:model/node:BasePerson';
  /** @deprecated - use {@link MODEL_URN} instead */
  export const MODEL_ID = 'everest.base.dataprotection/BasePersonModel.BasePerson';
  export const MODEL_UUID = 'dcc4e44d-75f4-4e67-9bdf-06a280e6ecff';

  /** @deprecated use ```client``` instead */
  export function create<C extends RequiredContext, U extends keyof BasePerson>(context: C, input: CreationFields, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: CreateOptionsArg): ActionRequest<C, Promise<WriteReturnType<BasePerson, U>>> {
    const convertedArgs = {data: input, fieldList: fieldlist, options: options} as Record<string, any>
    const partialPayload = {action: 'create', ...convertedArgs}

    return new ActionRequest<C, Promise<WriteReturnType<BasePerson, U>>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function createMany<C extends RequiredContext, U extends keyof BasePerson>(context: C, input: CreationFields[], fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: CreateOptionsArg): ActionRequest<C, Promise<WriteReturnType<BasePerson, U>[]>> {
    const convertedArgs = {data: input, fieldList: fieldlist, options: options} as Record<string, any>
    const partialPayload = {action: 'createMany', ...convertedArgs}

    return new ActionRequest<C, Promise<WriteReturnType<BasePerson, U>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function Delete<C extends RequiredContext>(context: C, where: UniqueWhereInput, options?: DeleteOptionsArg): ActionRequest<C, Promise<Partial<BasePerson>[]>> {
    const convertedArgs = {where: where, options} as Record<string, any>
    const partialPayload = {action: 'delete', ...convertedArgs}

    return new ActionRequest<C, Promise<Partial<BasePerson>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function deleteAll<C extends RequiredContext>(context: C): ActionRequest<C, Promise<Partial<BasePerson>[]>> {
    const convertedArgs = {data: {}} as Record<string, any>
    const partialPayload = {action: 'deleteAll', ...convertedArgs}

    return new ActionRequest<C, Promise<Partial<BasePerson>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function deleteMany<C extends RequiredContext>(context: C, where: Filter<BasePersonWithAssociation>, options?: DeleteOptionsArg): ActionRequest<C, Promise<Partial<BasePerson>[]>>;
  /** @deprecated use ```client``` instead */
  export function deleteMany<C extends RequiredContext>(context: C, where: Partial<BasePerson>, options?: DeleteOptionsArg): ActionRequest<C, Promise<Partial<BasePerson>[]>>;
  /** @deprecated use ```client``` instead */
  export function deleteMany<C extends RequiredContext>(context: C, where: Filter<BasePerson> | Partial<BasePerson>, options?: DeleteOptionsArg): ActionRequest<C, Promise<Partial<BasePerson>[]>> {
    const convertedArgs = {data: {where: where}, options} as Record<string, any>
    const partialPayload = {action: 'deleteMany', ...convertedArgs}

    return new ActionRequest<C, Promise<Partial<BasePerson>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function query<C extends RequiredContext, U extends FieldSelector<BasePersonWithAssociation>>(context: C, args: TypeSafeQueryArgType<BasePersonWithAssociation>, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<ReadReturnTypeWithAssociations<BasePersonWithAssociation, U>[]>>;
  /** @deprecated use ```client``` instead */
  export function query<C extends RequiredContext, U extends keyof BasePerson, V extends string = 'ALL_FIELDS'>(context: C, args: TypeSafeQueryArgType<BasePerson> | Omit<TypeSafeQueryArgType<BasePerson>, 'where'> & { where?: Partial<BasePerson> }, fieldlist: ReadonlyArray<U | V> | 'ALL_FIELDS'): ActionRequest<C, Promise<ReadReturnType<V>[]>>;
  /** @deprecated use ```client``` instead */
  export function query<C extends RequiredContext>(context: C, args: TypeSafeQueryArgType<BasePerson>, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS'): ActionRequest<C, Promise<unknown[]>> {
    const convertedArgs = {data: {...args, fieldList: fieldlist}} as Record<string, any>
    const partialPayload = {action: 'query', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function read<C extends RequiredContext, U extends FieldSelector<BasePersonWithAssociation>>(context: C, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | ReadOptionsArg): ActionRequest<C, Promise<ReadReturnTypeWithAssociations<BasePersonWithAssociation, U>>>;
  /** @deprecated use ```client``` instead */
  export function read<C extends RequiredContext, U extends keyof BasePerson>(context: C, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | ReadOptionsArg): ActionRequest<C, Promise<ReadReturnType<U>>>;
  /** @deprecated use ```client``` instead */
  export function read<C extends RequiredContext>(context: C, where: UniqueWhereInput, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: number | Date | ReadOptionsArg): ActionRequest<C, Promise<unknown>> {
    const convertedArgs = {data: {where: where, fieldList: fieldlist}, options: options} as Record<string, any>
    const partialPayload = {action: 'read', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function update<C extends RequiredContext, U extends FieldSelector<BasePersonWithAssociation>>(context: C, where: UniqueWhereInput, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: UpdateOptionsArg): ActionRequest<C, Promise<WriteReturnTypeWithAssociations<BasePersonWithAssociation, U>>>;
  /** @deprecated use ```client``` instead */
  export function update<C extends RequiredContext, U extends keyof BasePerson>(context: C, where: Partial<BasePerson>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: UpdateOptionsArg): ActionRequest<C, Promise<WriteReturnType<BasePerson, U>>>;
  /** @deprecated use ```client``` instead */
  export function update<C extends RequiredContext>(context: C, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: UpdateOptionsArg): ActionRequest<C, Promise<unknown>> {
    const convertedArgs = {where: where, data: data, fieldList: fieldlist, options} as Record<string, any>
    const partialPayload = {action: 'update', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function updateMany<C extends RequiredContext, U extends FieldSelector<BasePersonWithAssociation>>(context: C, where: Filter<BasePersonWithAssociation>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: UpdateManyOptionsArg): ActionRequest<C, Promise<WriteReturnTypeWithAssociations<BasePersonWithAssociation, U>[]>>;
  /** @deprecated use ```client``` instead */
  export function updateMany<C extends RequiredContext, U extends keyof BasePerson>(context: C, where: Partial<BasePerson>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: UpdateManyOptionsArg): ActionRequest<C, Promise<WriteReturnType<BasePerson, U>[]>>;
  /** @deprecated use ```client``` instead */
  export function updateMany<C extends RequiredContext>(context: C, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: UpdateManyOptionsArg): ActionRequest<C, Promise<unknown[]>> {
    const convertedArgs = {data: {where: where, data: data, fieldList: fieldlist}, options} as Record<string, any>
    const partialPayload = {action: 'updateMany', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function upsert<C extends RequiredContext, U extends keyof BasePerson & string>(context: C, data: Partial<BasePerson>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: UpsertOptionsArg): ActionRequest<C, Promise<WriteReturnType<BasePerson, U>>>;
  /** @deprecated use ```client``` instead */
  export function upsert<C extends RequiredContext, U extends keyof BasePerson & string>(context: C, where: Partial<BasePerson>, data: Partial<BasePerson>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: UpsertOptionsArg): ActionRequest<C, Promise<WriteReturnType<BasePerson, U>>>;
  /** @deprecated use ```client``` instead */
  export function upsert<C extends RequiredContext, U extends keyof BasePerson & string>(context: C, whereOrData: Partial<BasePerson>, dataOrFieldList?: Partial<BasePerson> | ReadonlyArray<U> | 'ALL_FIELDS', fieldlistOrOptions?: ReadonlyArray<U> | 'ALL_FIELDS' | UpsertOptionsArg, maybeOptions?: UpsertOptionsArg): ActionRequest<C, Promise<WriteReturnType<BasePerson, U>>> {
    const convertedArgs = {data: {where: whereOrData, data: dataOrFieldList, fieldList: fieldlistOrOptions}, options: maybeOptions} as Record<string, any>
    const partialPayload = {action: 'upsert', ...convertedArgs}

    return new ActionRequest<C, Promise<WriteReturnType<BasePerson, U>>>(context, partialPayload);
  }
  export namespace client {

    /** write a new object to the database. */
    export declare function create<U extends keyof BasePerson>(input: CreationFields, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: CreateOptionsArg): Promise<WriteReturnType<BasePerson, U>>

    /** write new objects to the database. */
    export declare function createMany<U extends keyof BasePerson>(input: CreationFields[], fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: CreateOptionsArg): Promise<WriteReturnType<BasePerson, U>[]>

    /** delete the object that is uniquely identified by the where condition. */
    export declare function Delete(where: UniqueWhereInput, options?: DeleteOptionsArg): Promise<Partial<BasePerson>[]>

    /** delete all objects. */
    export declare function deleteAll(): Promise<Partial<BasePerson>[]>

    /** delete the objects that are identified by the where condition. */
    export declare function deleteMany(where: Filter<BasePersonWithAssociation>, options?: DeleteOptionsArg): Promise<Partial<BasePerson>[]>;
    /** @deprecated use the alternative overloaded function */
    export declare function deleteMany(where: Partial<BasePerson>, options?: DeleteOptionsArg): Promise<Partial<BasePerson>[]>;
    export declare function deleteMany(where: Filter<BasePerson> | Partial<BasePerson>, options?: DeleteOptionsArg): Promise<Partial<BasePerson>[]>

    /** returns a list of all objects matching the requirements. */
    export declare function query<U extends FieldSelector<BasePersonWithAssociation>>(args: TypeSafeQueryArgType<BasePersonWithAssociation>, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<ReadReturnTypeWithAssociations<BasePersonWithAssociation, U>[]>;
    /** @deprecated Use the alternative overloaded method for querying associations. */
    export declare function query<U extends keyof BasePerson, V extends string = 'ALL_FIELDS'>(args: TypeSafeQueryArgType<BasePerson> | Omit<TypeSafeQueryArgType<BasePerson>, 'where'> & { where?: Partial<BasePerson> }, fieldlist: ReadonlyArray<U | V> | 'ALL_FIELDS'): Promise<ReadReturnType<V>[]>;
    export declare function query(args: TypeSafeQueryArgType<BasePerson>, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown[]>

    /** reads the object that is uniquely identified by the where condition. */
    export declare function read<U extends FieldSelector<BasePersonWithAssociation>>(where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | ReadOptionsArg): Promise<ReadReturnTypeWithAssociations<BasePersonWithAssociation, U>>;
    /** @deprecated Use the alternative overloaded method for reading associations. */
    export declare function read<U extends keyof BasePerson>(where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | ReadOptionsArg): Promise<ReadReturnType<U>>;
    export declare function read(where: UniqueWhereInput, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: number | Date | ReadOptionsArg): Promise<unknown>

    /** writes modified values back to the database for a given node instance. */
    export declare function update<U extends FieldSelector<BasePersonWithAssociation>>(where: UniqueWhereInput, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: UpdateOptionsArg): Promise<WriteReturnTypeWithAssociations<BasePersonWithAssociation, U>>;
    /** @deprecated Use the alternative overloaded method for writing with associations. */
    export declare function update<U extends keyof BasePerson>(where: Partial<BasePerson>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: UpdateOptionsArg): Promise<WriteReturnType<BasePerson, U>>;
    export declare function update(where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: UpdateOptionsArg): Promise<unknown>

    /** writes modified values back to the database for selected node instances. */
    export declare function updateMany<U extends FieldSelector<BasePersonWithAssociation>>(where: Filter<BasePersonWithAssociation>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: UpdateManyOptionsArg): Promise<WriteReturnTypeWithAssociations<BasePersonWithAssociation, U>[]>;
    /** @deprecated Use the alternative overloaded method for writing with associations. */
    export declare function updateMany<U extends keyof BasePerson>(where: Partial<BasePerson>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: UpdateManyOptionsArg): Promise<WriteReturnType<BasePerson, U>[]>;
    export declare function updateMany(where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: UpdateManyOptionsArg): Promise<unknown[]>

    /** creates or updates object identified by where. */
    export declare function upsert<U extends keyof BasePerson & string>(data: Partial<BasePerson>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: UpsertOptionsArg): Promise<WriteReturnType<BasePerson, U>>;
    /** @deprecated use the overload without an explicit where clause */
    export declare function upsert<U extends keyof BasePerson & string>(where: Partial<BasePerson>, data: Partial<BasePerson>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: UpsertOptionsArg): Promise<WriteReturnType<BasePerson, U>>;
    export declare function upsert<U extends keyof BasePerson & string>(whereOrData: Partial<BasePerson>, dataOrFieldList?: Partial<BasePerson> | ReadonlyArray<U> | 'ALL_FIELDS', fieldlistOrOptions?: ReadonlyArray<U> | 'ALL_FIELDS' | UpsertOptionsArg, maybeOptions?: UpsertOptionsArg): Promise<WriteReturnType<BasePerson, U>>
  }
}
