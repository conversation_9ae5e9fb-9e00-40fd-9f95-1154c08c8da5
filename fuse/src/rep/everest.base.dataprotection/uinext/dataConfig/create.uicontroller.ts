// @i18n:dataProtection
import { UILifecycleHooks } from '@everestsystems/content-core';

import type { CreateUiTemplate } from '../../types/uiTemplates/uinext/dataConfig/create.ui';

type CreateDataConfigContext = CreateUiTemplate.CreateContext;

export type Context = CreateDataConfigContext & {
  state: {
    modelURN: string;
    personBaseNavigationPath: {
      startNodeURN: string;
      associationPath: string;
      formula: string;
      fieldName: string;
      draft: string;
    };
    lastAssociationNodeURN: string;
    associationList: unknown[];
  };
};

UILifecycleHooks.onInit((context: Context) => {
  const {
    data: { Associations },
    state,
  } = context;
  state.associationList = Associations.filter(({ sourceNode, targetNode }) =>
    [sourceNode, targetNode].includes(
      'urn:evst:everest:base/dataprotection:model/node:BasePerson'
    )
  );
});

export async function createConfig(context: Context) {
  const { helpers, actions, sharedState, data, state } = context;
  const nodeInstance = sharedState.getNodeInstance(
    data.DataConfig?._nodeReference
  );
  helpers.set(
    nodeInstance,
    'data.personBaseNavigationPath',
    state.personBaseNavigationPath
  );
  await actions.submit({
    successMessage: '{{config.create.success}}',
    onSuccess: () => {
      helpers.closeModal();
    },
    onError: () => {
      helpers.showToast({
        title: '{{error}}',
        type: 'error',
        message: '{{config.create.error}}',
      });
    },
  });
}

function setPersonBaseNavigationPath(
  context: Context,
  key: string,
  value: string
) {
  const { state } = context;
  state.personBaseNavigationPath = {
    ...state.personBaseNavigationPath,
    [key]: value ?? null,
  };
}

export function setModelURN(context: Context, e) {
  const { helpers, state, sharedState, data } = context;
  state.modelURN = e ?? null;
  const nodeInstance = sharedState.getNodeInstance(
    data.DataConfig?._nodeReference
  );
  helpers.set(nodeInstance, 'data.fieldName', null);
}

export function setFormula(context: Context, newValue) {
  setPersonBaseNavigationPath(context, 'formula', newValue ?? '');
}

export function setFieldName(context: Context, newValue) {
  setPersonBaseNavigationPath(context, 'fieldName', newValue ?? '');
}

export function setDraft(context: Context, newValue) {
  setPersonBaseNavigationPath(context, 'draft', newValue ?? '');
}

export function setStartNode(context: Context, newValue) {
  setPersonBaseNavigationPath(context, 'startNodeURN', newValue ?? null);
  const {
    state,
    data: { Associations },
  } = context;
  state.associationList = Associations.filter(({ sourceNode, targetNode }) =>
    [sourceNode, targetNode].includes(newValue)
  );
  state.lastAssociationNodeURN = newValue ?? '';
}

export function setAssociationPath(context: Context, newValue) {
  const {
    state,
    data: { Associations },
  } = context;
  const selectedAssociations = newValue.map((uuid) =>
    Associations.find((association) => association.uuid === uuid)
  );
  selectedAssociations.reverse();
  setPersonBaseNavigationPath(
    context,
    'associationPath',
    selectedAssociations.map(({ name }) => name).join('.')
  );
  const lastAssociation = selectedAssociations[selectedAssociations.length - 1];
  state.lastAssociationNodeURN = lastAssociation
    ? [lastAssociation.sourceNode, lastAssociation.targetNode].find(
        (node) => node !== state.lastAssociationNodeURN
      )
    : state.personBaseNavigationPath.startNodeURN;
  state.associationList = Associations.filter(({ sourceNode, targetNode }) =>
    [sourceNode, targetNode].includes(state.lastAssociationNodeURN)
  ).filter(
    ({ name }) => !selectedAssociations.map(({ name }) => name).includes(name)
  );
}

export function getFieldQuery(context: Context) {
  const {
    state,
    data: { Nodes },
  } = context;
  if (state.modelURN) {
    return {
      modelUUID: Nodes.find((node) => node.modelUrn === state.modelURN).uuid,
    };
  }
  return {};
}
