{"version": 2, "uicontroller": ["create.uicontroller.ts"], "uimodel": {"state": {"modelURN": "", "personBaseNavigationPath": {"startNodeURN": "urn:evst:everest:base/dataprotection:model/node:BasePerson", "associationPath": "", "formula": "", "fieldName": "", "draft": "exclude"}, "lastAssociationNodeURN": "urn:evst:everest:base/dataprotection:model/node:BasePerson", "associationList": []}, "nodes": {"DataConfig": {"type": "struct", "modelId": "everest.base.dataprotection/DataConfigurationModel.DataConfiguration", "model": "urn:evst:everest:base/dataprotection:model/node:DataConfiguration", "fieldList": ["id", "purposeUUID", "personBaseNavigation<PERSON>ath", "modelURN", "fieldName"]}, "Purposes": {"type": "list", "query": {}, "model": "urn:evst:everest:base/dataprotection:model/node:Purpose", "modelId": "everest.base.dataprotection/PurposeModel.Purpose", "fieldList": ["uuid", "name"]}, "Associations": {"type": "list", "query": {"where": {}}, "model": "urn:evst:everest:appserver:model/node:metadata/EverestAssociation", "modelId": "everest.appserver/metadata/EverestAssociationModel.EverestAssociation", "fieldList": ["uuid", "name", "sourceNode", "targetNode"]}, "Nodes": {"type": "list", "query": {}, "modelId": "everest.appserver/metadata/EverestNodeModel.EverestNode", "model": "urn:evst:everest:appserver:model/node:metadata/EverestNode", "fieldList": ["name", "package", "modelUrn"]}, "NodeFields": {"type": "list", "query": {"where": "@controller:getFieldQuery()"}, "model": "urn:evst:everest:appserver:model/node:metadata/EverestField", "modelId": "everest.appserver/metadata/EverestFieldModel.EverestField", "fieldList": ["name"]}}}, "uiview": {"templateType": "details", "i18n": "dataProtection", "title": "{{create.data.config}}", "sections": {"content": [{"component": "FieldGroup", "section": {"grid": {"size": "12"}}, "props": {"elements": [{"component": "Select", "list": "@binding:Purposes", "idProp": "uuid", "textProp": "name", "value": "@binding:DataConfig.purposeUUID", "label": "{{purpose}}", "isEditing": true, "action": "create"}, {"component": "Select", "list": "@binding:Nodes", "idProp": "modelUrn", "textProp": "name", "value": "@binding:DataConfig.modelURN", "label": "Node with Personal Data", "onChange": "@controller:setModelURN", "isEditing": true, "action": "create"}, {"component": "Select", "list": "@binding:<PERSON><PERSON><PERSON>ields", "idProp": "name", "textProp": "name", "value": "@binding:DataConfig.fieldName", "label": "Field with Personal Data", "isEditing": true, "action": "create"}]}}, {"component": "FieldGroup", "section": {"title": "{{person.id.path}}", "grid": {"size": "12"}}, "props": {"columns": "12", "elements": [{"label": "{{start.node}}", "component": "Select", "idProp": "modelUrn", "textProp": "name", "list": "@binding:Nodes", "value": "@state:personBaseNavigationPath.startNodeURN", "onChange": "@controller:setStartNode", "size": "6"}, {"label": "{{associationPath}}", "component": "Select", "mode": "multiple", "idProp": "uuid", "textProp": "name", "list": "@state:associationList", "value": "@state:personBaseNavigationPath.associationPath", "onChange": "@controller:setAssociationPath", "size": "6", "isEditing": true}, {"label": "{{fieldName}}", "component": "Select", "idProp": "name", "textProp": "name", "list": "@binding:<PERSON><PERSON><PERSON>ields", "value": "@state:personBaseNavigationPath.fieldName", "onChange": "@controller:setFieldName", "size": "6", "isEditing": true}, {"label": "{{draft}}", "component": "Select", "idProp": "name", "textProp": "name", "list": [{"name": "exclude"}, {"name": "include"}, {"name": "only"}], "value": "@state:personBaseNavigationPath.draft", "onChange": "@controller:setDraft", "size": "6", "isEditing": true}, {"label": "Formula (EFL)", "component": "Input", "multiline": "true", "value": "@state:personBaseNavigationPath.formula", "onChange": "@controller:setFormula", "size": "12", "isEditing": true}]}}]}, "actions": {"content": [{"label": "{{create}}", "onClick": "@controller:createConfig", "variant": "primary"}]}}}