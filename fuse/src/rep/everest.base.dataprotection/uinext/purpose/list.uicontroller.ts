import type { ListUiTemplate } from '../../types/uiTemplates/uinext/purpose/list.ui';

type PurposeListContext = ListUiTemplate.ListContext;

export type Context = PurposeListContext & {
  state: {
    //
  };
};

export function openCreateModal(context: Context) {
  context.helpers.openModal({
    template: '/templates/everest.base.dataprotection/uinext/purpose/create',
    size: 'small',
    onClose: context.actions.refetchUiModelData,
  });
}

export function openPurpose(context: Context, row) {
  const { helpers, actions } = context;
  helpers.openModal({
    template: `/templates/everest.base.dataprotection/uinext/purpose/view?id=${row.id}`,
    size: 'medium',
    onClose: actions.refetchUiModelData,
  });
}

export function onActivateConfigurationClick(context: Context) {
  const { helpers, actions } = context;
  helpers.openModal({
    template:
      '/templates/everest.base.dataprotection/uinext/activateConfiguration',
    onModalSubmit() {
      actions
        .refetchUiTemplate()
        .then(() => {
          helpers.closeModal();
        })
        .catch(console.error);
    },
  });
}
