// @i18n:dataProtection
import { UILifecycleHooks } from '@everestsystems/content-core';

import type { CreateUiTemplate } from '../../types/uiTemplates/uinext/purpose/create.ui';

type CreatePurposeContext = CreateUiTemplate.CreateContext;

export type Context = CreatePurposeContext & {
  state: {
    retentionAnchor: {
      startNodeURN: string;
      associationPath: string;
      formula: string;
      fieldName: string;
    };
    scopePath: {
      startNodeURN: string;
      associationPath: string;
      formula: string;
      fieldName: string;
      draft: string;
    };
    lastAssociationNode: string;
    associationList: unknown[];
    lastScopePathAssociationNode: string;
    scopePathassociationList: unknown[];
    scopeUUID: string;
  };
};

UILifecycleHooks.onInit((context: Context) => {
  const {
    data: { Associations },
    state,
  } = context;
  state.associationList = Associations.filter(({ sourceNode, targetNode }) =>
    [sourceNode, targetNode].includes(
      'urn:evst:everest:base/dataprotection:model/node:BasePerson'
    )
  );
});

export async function createPurpose(context: Context) {
  const { helpers, actions, sharedState, data, state } = context;
  const nodeInstance = sharedState.getNodeInstance(
    data.Purpose?._nodeReference
  );
  helpers.set(nodeInstance, 'data.retentionAnchorPath', state.retentionAnchor);
  helpers.set(nodeInstance, 'data.scopePath', state.scopePath);
  helpers.set(nodeInstance, 'data.scopeUUID', state.scopeUUID);
  helpers.set(
    nodeInstance,
    'data.scopeModelURN',
    state.lastScopePathAssociationNode
  );
  await actions.submit({
    successMessage: '{{purpose.create.success}}',
    onSuccess: async () => {
      helpers.closeModal();
    },
    onError: () => {
      helpers.showToast({
        title: '{{error}}',
        type: 'error',
        message: '{{purpose.create.error}}',
      });
    },
  });
}

function setRetentionAnchor(context: Context, key: string, value: string) {
  const { state } = context;
  state.retentionAnchor = {
    ...state.retentionAnchor,
    [key]: value ?? null,
  };
}

export function setFormula(context: Context, newValue) {
  setRetentionAnchor(context, 'formula', newValue ?? '');
}

export function setFieldName(context: Context, newValue) {
  setRetentionAnchor(context, 'fieldName', newValue ?? '');
}

export function setStartNode(context: Context, newValue) {
  setRetentionAnchor(context, 'startNodeURN', newValue ?? null);
  const {
    state,
    data: { Associations },
  } = context;
  state.associationList = Associations.filter(({ sourceNode, targetNode }) =>
    [sourceNode, targetNode].includes(newValue)
  );
  state.lastAssociationNode = newValue ?? '';
}

export function setAssociationPath(context: Context, newValue: string[]) {
  const {
    state,
    data: { Associations },
  } = context;
  const selectedAssociations = newValue.map((uuid) =>
    Associations.find((association) => association.uuid === uuid)
  );
  selectedAssociations.reverse();
  setRetentionAnchor(
    context,
    'associationPath',
    selectedAssociations.map(({ name }) => name).join('.')
  );
  const lastAssociation = selectedAssociations[selectedAssociations.length - 1];
  state.lastAssociationNode = lastAssociation
    ? [lastAssociation.sourceNode, lastAssociation.targetNode].find(
        (node) => node !== state.lastAssociationNode
      )
    : state.retentionAnchor.startNodeURN;
  state.associationList = Associations.filter(({ sourceNode, targetNode }) =>
    [sourceNode, targetNode].includes(state.lastAssociationNode)
  ).filter(
    ({ name }) => !selectedAssociations.map(({ name }) => name).includes(name)
  );
}

export function setScopeUUID(context: Context, newValue) {
  const { state } = context;
  state.scopeUUID = newValue ?? '';
}

function setScopePath(context: Context, key: string, value: string) {
  const { state } = context;
  state.scopePath = {
    ...state.scopePath,
    [key]: value ?? null,
  };
}

export function setScopePathFormula(context: Context, newValue) {
  setScopePath(context, 'formula', newValue ?? '');
}

export function setScopePathFieldName(context: Context, newValue) {
  setScopePath(context, 'fieldName', newValue ?? '');
}

export function setScopePathDraft(context: Context, newValue) {
  setScopePath(context, 'draft', newValue ?? '');
}

export function setScopePathStartNode(context: Context, newValue) {
  setScopePath(context, 'startNodeURN', newValue ?? null);
  const {
    state,
    data: { Associations },
  } = context;
  state.scopePathassociationList = Associations.filter(
    ({ sourceNode, targetNode }) => [sourceNode, targetNode].includes(newValue)
  );
  state.lastScopePathAssociationNode = newValue ?? '';
}

export function setScopePathAssociationPath(context: Context, newValue) {
  const {
    state,
    data: { Associations },
  } = context;
  const selectedAssociations = newValue.map((uuid) =>
    Associations.find((association) => association.uuid === uuid)
  );
  selectedAssociations.reverse();
  setScopePath(
    context,
    'associationPath',
    selectedAssociations.map(({ name }) => name).join('.')
  );
  const lastAssociation = selectedAssociations[selectedAssociations.length - 1];
  state.lastScopePathAssociationNode = lastAssociation
    ? [lastAssociation.sourceNode, lastAssociation.targetNode].find(
        (node) => node !== state.lastScopePathAssociationNode
      )
    : state.retentionAnchor.startNodeURN;
  state.scopePathassociationList = Associations.filter(
    ({ sourceNode, targetNode }) =>
      [sourceNode, targetNode].includes(state.lastScopePathAssociationNode)
  ).filter(
    ({ name }) => !selectedAssociations.map(({ name }) => name).includes(name)
  );
}

export async function getScopeList(context: Context) {
  const { actions, state } = context;
  if (state.lastScopePathAssociationNode && state.scopePath.fieldName) {
    try {
      const reuslt = await actions.run({
        DataProtection: {
          action: 'getScopeList',
          data: {
            payload: {
              modelURN: state.lastScopePathAssociationNode,
              fieldName: state.scopePath.fieldName,
            },
          },
        },
      });
      return reuslt.DataProtection;
    } catch {
      return [];
    }
  }
  return [];
}
