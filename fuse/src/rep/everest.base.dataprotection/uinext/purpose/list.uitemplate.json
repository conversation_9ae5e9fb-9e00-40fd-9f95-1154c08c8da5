{"version": 2, "uicontroller": ["list.uicontroller.ts"], "uimodel": {"nodes": {"Purpose": {"type": "list", "getPurposeListWithScope": {}, "model": "urn:evst:everest:base/dataprotection:model/node:DataProtection", "modelId": "everest.base.dataprotection/DataProtectionModel.DataProtection", "fieldList": []}}}, "uiview": {"layout": "everest.base.ui/layouts/uilayout/list", "props": {"i18n": "dataProtection", "title": "{{purposes}}", "onRowClicked": "@controller:openPurpose", "node": "@binding:Purpose", "onCreateClick": "@controller:openCreateModal", "columns": [{"headerName": "{{id}}", "field": "purposeNumber"}, {"headerName": "{{name}}", "field": "name"}, {"headerName": "{{purposeType}}", "field": "purposeType"}, {"headerName": "{{retentionTimeInDays}}", "field": "retentionTimeInDays"}, {"headerName": "{{retentionAnchorPath}}", "field": "retentionAnchor<PERSON>ath"}, {"headerName": "{{scope}}", "field": "scopeName"}], "customActions": [{"label": "{{activate.configuration}}", "onClick": "@controller:onActivateConfigurationClick"}]}}}