{"version": 2, "uicontroller": ["create.uicontroller.ts"], "uimodel": {"state": {"retentionAnchor": {"startNodeURN": "urn:evst:everest:base/dataprotection:model/node:BasePerson", "associationPath": "", "formula": "", "fieldName": ""}, "scopePath": {"startNodeURN": "", "associationPath": "", "formula": "", "fieldName": "", "draft": "exclude"}, "lastAssociationNode": "urn:evst:everest:base/dataprotection:model/node:BasePerson", "associationList": [], "lastScopePathAssociationNode": "", "scopePathassociationList": [], "scopeUUID": ""}, "nodes": {"Purpose": {"type": "struct", "model": "urn:evst:everest:base/dataprotection:model/node:Purpose", "modelId": "everest.base.dataprotection/PurposeModel.Purpose", "fieldList": ["id", "purposeNumber", "purposeType", "name", "content", "retentionTimeInDays", "retentionAnchor<PERSON>ath", "scopeUUID", "scopePath", "scopeModelURN"]}, "DataProtection": {"type": "struct", "modelId": "everest.base.dataprotection/DataProtectionModel.DataProtection"}, "Associations": {"type": "list", "query": {"where": {}}, "model": "urn:evst:everest:appserver:model/node:metadata/EverestAssociation", "modelId": "everest.appserver/metadata/EverestAssociationModel.EverestAssociation", "fieldList": ["uuid", "name", "sourceNode", "targetNode"]}, "Nodes": {"type": "list", "query": {}, "modelId": "everest.appserver/metadata/EverestNodeModel.EverestNode", "model": "urn:evst:everest:appserver:model/node:metadata/EverestNode", "fieldList": ["name", "package", "modelUrn", "uuid"]}}}, "uiview": {"templateType": "details", "i18n": "dataProtection", "title": "{{create.purpose}}", "sections": {"content": [{"component": "FieldGroup", "section": {"grid": {"size": "12"}}, "props": {"fields": [{"component": "Input", "field": "name", "fieldProps": {"label": "{{name}}", "isEditing": true, "action": "create"}}, {"component": "Select", "field": "purposeType", "fieldProps": {"idProp": "codeValue", "textProp": "text", "label": "{{purpose.type}}", "isEditing": true, "action": "create"}}, {"component": "Input", "field": "retentionTimeInDays", "fieldProps": {"label": "{{retention.time.days}}", "isEditing": true, "action": "create"}}], "data": "@binding:Purpose"}}, {"component": "FieldGroup", "section": {"title": "{{retention.anchor.path}}", "grid": {"size": "12"}}, "props": {"columns": "12", "elements": [{"label": "{{start.node}}", "component": "Select", "idProp": "modelUrn", "textProp": "name", "list": "@binding:Nodes", "value": "@state:retentionAnchor.startNodeURN", "onChange": "@controller:setStartNode", "size": "6"}, {"label": "{{associationPath}}", "component": "Select", "mode": "multiple", "idProp": "uuid", "textProp": "name", "list": "@state:associationList", "value": "@state:retentionAnchor.associationPath", "onChange": "@controller:setAssociationPath", "size": "6", "isEditing": true}, {"label": "{{fieldName}}", "component": "Input", "value": "@state:retentionAnchor.fieldName", "onChange": "@controller:setFieldName", "size": "6", "isEditing": true}, {"label": "Formula (EFL)", "component": "Input", "value": "@state:retentionAnchor.formula", "onChange": "@controller:setFormula", "size": "6", "multiline": true, "isEditing": true}]}}, {"component": "FieldGroup", "section": {"title": "{{scope.path}}", "grid": {"size": "12"}}, "props": {"columns": "12", "elements": [{"label": "{{start.node}}", "component": "Select", "idProp": "modelUrn", "textProp": "name", "list": "@binding:Nodes", "value": "@state:scopePath.startNodeURN", "onChange": "@controller:setScopePathStartNode", "size": "6", "isEditing": true}, {"label": "{{associationPath}}", "component": "Select", "mode": "multiple", "idProp": "uuid", "textProp": "name", "list": "@state:scopePathassociationList", "value": "@state:scopePath.associationPath", "onChange": "@controller:setScopePathAssociationPath", "size": "6", "isEditing": true}, {"label": "{{display.field.name}}", "component": "Input", "value": "@state:scopePath.fieldName", "onChange": "@controller:setScopePathFieldName", "size": "6", "isEditing": true}, {"label": "{{draft}}", "component": "Select", "idProp": "name", "textProp": "name", "list": [{"name": "exclude"}, {"name": "include"}, {"name": "only"}], "value": "@state:scopePath.draft", "onChange": "@controller:setScopePathDraft", "size": "6", "isEditing": true}, {"label": "{{scope}}", "component": "Select", "list": "@controller:getScopeList()", "idProp": "uuid", "textProp": "@state:scopePath.fieldName", "value": "@state:scopeUUID", "onChange": "@controller:setScopeUUID", "size": "6", "isEditing": true}, {"label": "Formula (EFL)", "component": "Input", "value": "@state:scopePath.formula", "onChange": "@controller:setScopePathFormula", "multiline": true, "size": "6", "isEditing": true}]}}, {"component": "FieldGroup", "section": {"grid": {"size": "12"}}, "props": {"elements": [{"component": "RichText", "value": "@binding:Purpose.content", "isEditing": true, "action": "create"}]}}]}, "actions": {"content": [{"label": "{{create}}", "onClick": "@controller:createPurpose", "variant": "primary"}]}}}