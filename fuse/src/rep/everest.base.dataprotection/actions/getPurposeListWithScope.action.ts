import { type ISession, ModelUrn } from '@everestsystems/content-core';

import { Purpose } from '../types/Purpose';

export default async function getPurposeListWithScope(session: ISession) {
  const purposeList = await Purpose.query(
    session,
    {
      where: {},
    },
    [
      'id',
      'purposeNumber',
      'purposeType',
      'name',
      'content',
      'retentionTimeInDays',
      'retentionAnchorPath',
      'scopeUUID',
      'scopeModelURN',
      'scopePath',
    ]
  );
  const result = [];
  for (const purpose of purposeList) {
    try {
      const client = await session.controllerClient(
        ModelUrn.parse(purpose.scopeModelURN)
      );
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const scopeName = await (client as any).read(
        {
          uuid: purpose.scopeUUID,
        },
        ['uuid', purpose.scopePath.fieldName]
      );
      if (scopeName) {
        result.push({
          ...purpose,
          scopeName: scopeName[purpose.scopePath.fieldName],
        });
      }
    } catch {
      result.push({ ...purpose, scopeName: '' });
    }
  }
  return result;
}
