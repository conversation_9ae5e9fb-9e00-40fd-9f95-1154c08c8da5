// eslint-disable-next-line eslint-comments/disable-enable-pair
/* eslint-disable @typescript-eslint/no-explicit-any */
import type { draftFlags, ISession } from '@everestsystems/content-core';
import { ModelUrn } from '@everestsystems/content-core';
import { getRetentionTime } from '@pkg/everest.base.dataprotection/public/utils/getRetentionTime';
import { Agreement } from '@pkg/everest.base.dataprotection/types/Agreement';
import { BasePerson } from '@pkg/everest.base.dataprotection/types/BasePerson';
import { LegalBasis } from '@pkg/everest.base.dataprotection/types/LegalBasis';
import { forOwn, isArray, isObject } from 'lodash';

type AnyObject = Record<string, unknown>;

function findAllOccurrences(
  obj: AnyObject | AnyObject[] | unknown,
  fieldName: string,
  results: unknown[] = []
) {
  if (isArray(obj)) {
    for (const item of obj) {
      findAllOccurrences(item, fieldName, results);
    } // Traverse arrays
  } else if (isObject(obj)) {
    forOwn(obj, (value, key) => {
      if (key === fieldName) {
        results.push(value);
      }
      findAllOccurrences(value, fieldName, results); // Recursively search nested objects/arrays
    });
  }

  return results;
}

export async function expirationHandler(env: ISession) {
  let result: unknown[] = [];
  // First get list of all consentAgreements
  const agreementList = await getAgreementList(env);
  const legalBasisList = await getLegalBasisList(env);
  for (const contract of [...agreementList, ...legalBasisList]) {
    const { anchorQuery, dataConfigList, personBaseUUID, retentionFormula } =
      contract;
    for (const dataConfig of dataConfigList.filter(
      (config) => config.personBaseNavigationPath
    )) {
      const { associationPath, formula, draft } =
        dataConfig.personBaseNavigationPath;
      const [personBaseNavigationResult] = await BasePerson.query(
        env,
        {
          where: {
            ...anchorQuery,
            dataSelector: retentionFormula ? true : undefined,
            uuid: personBaseUUID,
          },
          draft: (draft as draftFlags) ?? 'exclude',
          calculate: retentionFormula
            ? [
                {
                  formula: `if ${retentionFormula} { true } else { none }`,
                  name: 'dataSelector',
                  type: 'boolean',
                },
              ]
            : [],
        },
        [`${associationPath ? `${associationPath}.id` : 'id'}`]
      );

      const idList = findAllOccurrences(personBaseNavigationResult, 'id');

      const modelURN = ModelUrn.parse(dataConfig.modelURN);

      const nodeName = modelURN.name;

      const fieldNameTobeRemoved = dataConfig.fieldName;

      const client = await env.controllerClient(modelURN);
      const dataTobeDeleted = await (client as any).query(
        {
          where: {
            dataSelector: formula ? true : undefined,
            id: { $in: idList },
          },
          calculate: formula
            ? [
                {
                  formula: `if ${formula} { true } else { none }`,
                  name: 'dataSelector',
                  type: 'boolean',
                },
              ]
            : [],
          draft: draft ?? 'exclude',
        },
        ['id', ...(fieldNameTobeRemoved ? [fieldNameTobeRemoved] : [])]
      );

      result = [
        ...result,
        ...dataTobeDeleted.map((data) => ({
          ...data,
          modelURN,
          nodeName,
          fieldName: fieldNameTobeRemoved, // currently no use case for this, it should anonynise the field
        })),
      ];
    }
  }

  return result;
}

async function getAgreementList(env: ISession) {
  // First get list of all consentAgreements
  const agreementList = await Agreement.query(
    env,
    {
      where: {
        agreed: true,
      },
    },
    [
      'personBaseUUID',
      'expiryDate',
      {
        'Agreement-Consent': [
          {
            'Consent-Purpose': [
              'retentionAnchorPath',
              'retentionTimeInDays',
              {
                'DataConfiguration-Purpose': [
                  'personBaseNavigationPath',
                  'modelURN',
                  'fieldName',
                ],
              },
            ],
          },
        ],
      },
    ]
  );
  return (agreementList || [])
    .map((agreement) => {
      const { associationPath, fieldName, formula } =
        agreement?.['Agreement-Consent']?.['Consent-Purpose']
          ?.retentionAnchorPath || {};

      if (!fieldName) {
        return null;
      }

      // generate the anchor query that been called from BasePerson
      let anchorQuery = {};
      // use expiryDate if it exists (for withdrawal or block removing of data)
      if (agreement?.expiryDate) {
        anchorQuery = {
          ['PersonBase-Agreement.expiryDate']: { $lte: new Date() },
        };
      } else {
        // use retention anchor configured in purpose
        // add association path to the field name if it exists
        const anchorField = `${
          associationPath ? `${associationPath}.${fieldName}` : fieldName
        }`;
        const retentionTimeInDays =
          agreement?.['Agreement-Consent']?.['Consent-Purpose']
            ?.retentionTimeInDays;
        const retentionTime = getRetentionTime(retentionTimeInDays);
        anchorQuery = {
          [anchorField]: { $lte: retentionTime },
        };
      }

      const dataConfigList =
        agreement?.['Agreement-Consent']?.['Consent-Purpose']?.[
          'DataConfiguration-Purpose'
        ] ?? [];

      return {
        dataConfigList,
        anchorQuery,
        retentionFormula: formula,
        personBaseUUID: agreement.personBaseUUID,
      };
    })
    .filter(Boolean);
}

async function getLegalBasisList(env: ISession) {
  // First get list of all consentAgreements
  const legalBasisList = await LegalBasis.query(
    env,
    {
      where: {},
    },
    [
      'personBaseUUID',
      'expiryDate',
      {
        'Purpose-LegalBasis': [
          'retentionAnchorPath',
          'retentionTimeInDays',
          {
            'DataConfiguration-Purpose': [
              'personBaseNavigationPath',
              'modelURN',
              'fieldName',
            ],
          },
        ],
      },
    ]
  );
  return (legalBasisList || [])
    .map((legalBasis) => {
      const { associationPath, fieldName, formula } =
        legalBasis?.['Purpose-LegalBasis']?.retentionAnchorPath || {};

      if (!fieldName) {
        return null;
      }

      // generate the anchor query that been called from BasePerson
      let anchorQuery = {};
      // use expiryDate if it exists (for withdrawal or block removing of data)
      if (legalBasis?.expiryDate) {
        anchorQuery = {
          ['BasePerson-LegalBasis.expiryDate']: { $lte: new Date() },
        };
      } else {
        // use retention anchor configured in purpose
        // add association path to the field name if it exists
        const anchorField = `${
          associationPath ? `${associationPath}.${fieldName}` : fieldName
        }`;
        const retentionTimeInDays =
          legalBasis?.['Purpose-LegalBasis']?.retentionTimeInDays;
        const retentionTime = getRetentionTime(retentionTimeInDays);
        anchorQuery = {
          [anchorField]: { $lte: retentionTime },
        };
      }

      const dataConfigList =
        legalBasis?.['Purpose-LegalBasis']?.['DataConfiguration-Purpose'] ?? [];

      return {
        dataConfigList,
        anchorQuery,
        retentionFormula: formula,
        personBaseUUID: legalBasis.personBaseUUID,
      };
    })
    .filter(Boolean);
}
