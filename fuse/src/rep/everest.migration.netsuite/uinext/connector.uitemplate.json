{"version": 2, "uicontroller": "connector.uicontroller.ts", "uimodel": {"nodes": {"integrationConnectivity": {"type": "list", "modelId": "everest.connectorengine/IntegrationConnectivityModel.IntegrationConnectivity", "fieldList": ["integrationName"], "query": {"where": {"package": "everest.migration.netsuite"}}}, "selectedIntegrationConnectivity": {"type": "struct", "modelId": "everest.connectorengine/IntegrationConnectivityModel.IntegrationConnectivity", "fieldList": ["id", "acceptHeader", "authTokenParamsInBody", "authorizationUrl", "baseUrl", "httpRequestMethodCallback", "noClientIdAndClientSecretParamsInBody", "integrationName", "internalRedirectUrl", "refreshTokenParamsInBody", "responseTypes", "secret<PERSON>ey", "tokenEndpoint"], "query": "@controller:buildIntegrationConnectivityQueryArguments()"}, "secret": {"type": "struct", "model": "urn:evst:everest:appserver:model/node:Secret", "read": "@controller:buildSecretReadArguments()"}, "selectedConnector": {"type": "struct", "modelId": "everest.connectorengine/ConnectorEngineModel.ConnectorEngine", "fieldList": ["id", "config<PERSON><PERSON><PERSON>"], "query": {"where": {"package": "everest.migration.netsuite"}}}, "integrationSession": {"type": "struct", "model": "urn:evst:everest:fin/integration/base:model/node:IntegrationSession", "query": {"where": {"providerName": "@controller:getSessionProviderName()"}}, "fieldList": ["id"]}, "sessionVariables": {"type": "list", "model": "urn:evst:everest:fin/integration/base:model/node:SessionVariables", "parent": "integrationSession", "joinKey": "id-sessionId", "fieldList": ["config<PERSON><PERSON>", "config<PERSON><PERSON><PERSON>"]}, "netsuite": {"type": "struct", "model": "urn:evst:everest:migration/netsuite:model/node:NetSuite"}}}, "uiview": {"i18n": ["everest.connectorengine/connectorEngine", "netsuite"], "title": "{{connectorEngine.create}}", "sections": [{"component": "<PERSON><PERSON>", "section": {"grid": {"size": "12"}}, "visible": "@controller:isSandbox()", "props": {"variant": "warning", "title": "Warning", "content": "{{netsuite.leave-sandbox}}"}}, {"component": "Block", "size": "12", "type": "secondary", "columns": 4, "elements": [{"component": "Select", "label": "{{netsuite.select-connection}}", "idProp": "integrationName", "textProp": "integrationName", "value": "@controller:getSelectedConnection()", "isEditing": true, "list": "@binding:integrationConnectivity", "onChange": "@controller:selectConnection", "name": "selectedConnectionName", "size": 4, "bottomActions": [{"label": "{{netsuite.create-connection}}", "onClick": "@controller:createNewConnection"}]}, {"label": "Client ID", "name": "client_id", "value": "@controller:getClientId()", "isEditing": true, "size": 2}, {"label": "Client Secret", "name": "client_secret", "type": "password", "value": "@controller:getClientSecret()", "isEditing": true, "size": 2}, {"label": "Account ID", "name": "netsuiteAccountId", "value": "@controller:geNetsuiteAccountId()", "isEditing": true, "size": 1}, {"label": "Authorization URL", "value": "@binding:selectedIntegrationConnectivity.authorizationUrl", "isEditing": false, "visible": "@controller:isExpertView()", "size": 1}, {"label": "Base URL", "value": "@binding:selectedIntegrationConnectivity.baseUrl", "isEditing": false, "visible": "@controller:isExpertView()", "size": 1}, {"label": "HTTP Request Callback", "value": "@binding:selectedIntegrationConnectivity.httpRequestMethodCallback", "isEditing": true, "visible": "@controller:isExpertView()", "size": 1}, {"label": "Response Types", "value": "@binding:selectedIntegrationConnectivity.responseTypes", "isEditing": true, "visible": "@controller:isExpertView()", "size": 1}, {"label": "Internal Redirect URL", "value": "@binding:selectedIntegrationConnectivity.internalRedirectUrl", "isEditing": true, "visible": "@controller:isExpertView()", "size": 2}, {"label": "Accept Header", "value": "@binding:selectedIntegrationConnectivity.acceptHeader", "isEditing": true, "visible": "@controller:isExpertView()", "size": 1}, {"label": "No Client in Body", "value": "@binding:selectedIntegrationConnectivity.noClientIdAndClientSecretParamsInBody", "isEditing": true, "visible": "@controller:isExpertView()", "size": 1}, {"label": "Token Endpoint", "value": "@binding:selectedIntegrationConnectivity.tokenEndpoint", "isEditing": false, "visible": "@controller:isExpertView()", "size": 2}, {"label": "Refresk Token in Body", "value": "@binding:selectedIntegrationConnectivity.refreshTokenParamsInBody", "isEditing": true, "visible": "@controller:isExpertView()", "size": 1}, {"label": "<PERSON><PERSON> Token in Body", "value": "@binding:selectedIntegrationConnectivity.authTokenParamsInBody", "isEditing": true, "visible": "@controller:isExpertView()", "size": 1}, {"label": "@controller:getTestLabel()", "component": "ProgressBar", "progress": "@controller:getTestProgressValue()", "variant": "@controller:getTestVariant()", "started": "@controller:getTestStarted()", "size": 4}, {"component": "Editor", "label": "{{netsuite.test}}", "value": "@state:response", "action": "create", "isEditing": false, "editorConfig": {"language": "json", "height": 150}, "isVisible": "@controller:isExpertView()", "size": 4}]}, {"component": "ButtonGroup", "direction": "horizontal", "actions": [{"variant": "secondary", "label": "@controller:expert<PERSON>iewButtonName()", "onClick": "@controller:switchExpertView"}, {"variant": "secondary", "label": "{{connectorEngine.test}}", "onClick": "@controller:test"}, {"variant": "primary", "label": "{{connectorEngine.save}}", "onClick": "@controller:save"}, {"variant": "primary", "label": "{{connectorEngine.authenticate}}", "onClick": "@controller:authorize", "disabled": "@controller:isSandbox()"}]}]}}