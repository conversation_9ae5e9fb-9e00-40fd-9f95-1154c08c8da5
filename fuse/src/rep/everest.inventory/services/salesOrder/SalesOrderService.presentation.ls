package everest.inventory

odata presentation SalesOrderService {

  /**
   * EntitySet for Customers.
   */

  data-set Customers {
    supported-operations view
    field id (editable: false): Id
    field name: Text
    field customerNumber: Text
    field email: Text
    field phone: Text
    field address: Text
    field city: Text
    field stateProvince: Text
    field postalCode: Text
    field country: Text
    field customerType: field<everest.fin.accounting::Customer.customerType>
    field status: field<everest.fin.accounting::Customer.status>
    field createdDate (editable: false): Date
    field lastModifiedDate (editable: false): Date
  }

  /**
   * EntitySet for SalesOrders.
   */

  data-set SalesOrders {
    supported-operations view, add, change,
      remove
    field id (editable: false): field<everest.inventory::IMSalesOrder.id>
    field uuid (editable: false): field<everest.inventory::IMSalesOrder.uuid>
    field documentNumber (editable: false): Text
    field externalId: field<everest.inventory::IMSalesOrder.externalId>
    field active: field<everest.inventory::IMSalesOrder.active>
    field carrier: field<everest.inventory::IMSalesOrder.carrier>
    field fulfillmentStatus (editable: false): Text
    field plannedShipDate: PlainDate
    field requestedDeliveryDate: PlainDate
    field salesOrderId: field<everest.inventory::IMSalesOrder.salesOrderId>
    field shippingAddressId: field<everest.inventory::IMSalesOrder.shippingAddressId>
    field businessPartnerId: field<everest.inventory::IMSalesOrder.businessPartnerId>
    field shippingMemo: Text
    field shippingMethod: Text
    field createdDate (editable: false): Date
    field createdBy (editable: false): Text
    field lastModifiedDate (editable: false): Date
    field lastModifiedBy (editable: false): Text
    field lines: array<object<{
      id: field<everest.inventory::IMSalesOrderLine.id>
      headerId: field<everest.inventory::IMSalesOrderLine.headerId>
      itemId: field<everest.inventory::IMSalesOrderLine.itemId>
      itemCode: Text
      itemName: Text
      locationId: field<everest.inventory::IMSalesOrderLine.locationId>
      locationName: Text
      unitId: field<everest.inventory::IMSalesOrderLine.unitId>
      unitCode: Text
      quantity: Number<Decimal>
      allocatedQuantity: Number<Decimal>
      openAllocatedQuantity: Number<Decimal>
      openQuantity: Number<Decimal>
      backorderedQuantity: Number<Decimal>
      pickedQuantity: Number<Decimal>
      shippedQuantity: Number<Decimal>
      fulfillmentStatus: Text
      actualShipDate: PlainDate
      trackingNumber: Text
      salesOrderProductLineId: field<everest.inventory::IMSalesOrderLine.salesOrderProductLineId>
    }>>
  }

  /**
   * Action to allocate inventory for a sales order line.
   */
  action AllocateInventory {
    inputs {
      salesOrderId: field<everest.inventory::IMSalesOrder.id>
      salesOrderLineId: field<everest.inventory::IMSalesOrderLine.id>
      quantity: Number<Decimal>
      expirationDate: Date
      locationId: field<everest.inventory::IMWarehouseLocation.id>
    }
    outputs {
      success: TrueFalse
      reservationId: Number<Int>
      reservationNumber: Text
      allocatedQuantity: Number<Decimal>
      shortageQuantity: Number<Decimal>
      errorMessage: Text
      salesOrderId: Id
    }
    properties {
      side-effects true
    }
  }

  /**
   * Action to release an allocation.
   */
  action ReleaseAllocation {
    inputs {
      reservationId: Number<Int>
    }
    outputs {
      success: TrueFalse
      errorMessage: Text
    }
    properties {
      side-effects true
    }
  }

  /**
   * Action to process backorders when inventory becomes available.
   */
  action ProcessBackorders {
    inputs {
      salesOrderId: field<everest.inventory::IMSalesOrder.id>
      itemId: field<everest.inventory::ItemMaster.id>
      locationId: field<everest.inventory::IMWarehouseLocation.id>
      expirationDate: PlainDate
      prioritizeByOrderDate: TrueFalse
      prioritizeByCustomerPriority: TrueFalse
      prioritizeByQuantity: TrueFalse
    }
    outputs {
      success: TrueFalse
      processedBackorders: Number<Int>
      totalAllocatedQuantity: Number<Decimal>
      remainingBackorders: Number<Int>
      results: array<object<{
        salesOrderId: field<everest.inventory::IMSalesOrder.id>
        salesOrderLineId: field<everest.inventory::IMSalesOrderLine.id>
        processedQuantity: Number<Decimal>
        remainingBackorderedQuantity: Number<Decimal>
        success: TrueFalse
        errorMessage: Text
      }>>
    }
    properties {
      side-effects true
    }
  }

  /**
   * Action to create a pick list from a sales order.
   */
  action CreatePickList {
    inputs {
      salesOrderId: field<everest.inventory::IMSalesOrder.id>
      locationId: field<everest.inventory::IMWarehouseLocation.id>
      assignedUserId: field<everest.inventory::IMPickList.assignedPickerId>
      pickDate: Date
      priority: Number<Int>
      notes: Text
      expectedCompletionDate: Date
    }
    outputs {
      success: TrueFalse
      pickListId: field<everest.inventory::IMPickList.id>
      pickListNumber: Text
      salesOrderId: field<everest.inventory::IMSalesOrder.id>
      warnings: array<Text>
      errors: array<Text>
    }
    properties {
      side-effects true
    }
  }

  /**
   * Action to create a sales delivery from a sales order.
   * Supports both package-based and direct delivery.
   */
  action CreateSalesDelivery {
    inputs {
      deliveryDate: Date
      notes: Text
      lines: array<object<{
        packageLineId: field<everest.inventory::IMPackageLine.id>
        salesOrderLineId: field<everest.inventory::IMSalesOrderLine.id>
        itemId: field<everest.inventory::ItemMaster.id>
        quantity: Number<Decimal>
        unitId: field<everest.inventory::UnitOfMeasure.id>
        unitCode: Text
        status: Text
      }>>
    }
    outputs {
      success: TrueFalse
      deliveryId: field<everest.inventory::IMInventorySalesDelivery.id>
      deliveryNumber: Text
      salesOrderId: field<everest.inventory::IMSalesOrder.id>
      warnings: array<Text>
      errors: array<Text>
    }
    properties {
      side-effects true
    }
  }

  /**
   * Action to create a sales delivery from pick list lines.
   */
  action CreateSalesDeliveryFromPickList {
    inputs {
      deliveryDate: Date
      notes: Text
      lines: array<object<{
        pickListLineId: field<everest.inventory::IMPickListLine.id>
        salesOrderLineId: field<everest.inventory::IMSalesOrderLine.id>
        itemId: field<everest.inventory::ItemMaster.id>
        quantity: Number<Decimal>
        unitId: field<everest.inventory::UnitOfMeasure.id>
        unitCode: Text
        status: Text
      }>>
    }
    outputs {
      success: TrueFalse
      deliveryId: field<everest.inventory::IMInventorySalesDelivery.id>
      deliveryNumber: Text
      salesOrderId: field<everest.inventory::IMSalesOrder.id>
      warnings: array<Text>
      errors: array<Text>
    }
    properties {
      side-effects true
    }
  }

  /**
   * Action to create a sales order.
   */
  action CreateSalesOrder {
    inputs {
      externalId: field<everest.inventory::IMSalesOrder.externalId>
      active: field<everest.inventory::IMSalesOrder.active>
      carrier: field<everest.inventory::IMSalesOrder.carrier>
      plannedShipDate: PlainDate
      requestedDeliveryDate: PlainDate
      salesOrderId: field<everest.inventory::IMSalesOrder.salesOrderId> // External system reference
      shippingAddressId: field<everest.inventory::IMSalesOrder.shippingAddressId>
      businessPartnerId: field<everest.inventory::IMSalesOrder.businessPartnerId> // (required: true)
      shippingMemo: Text
      shippingMethod: Text
      documentNumber: Text // Optional: if provided; otherwise, may be auto-generated
      lines: array<object<{
        itemId: field<everest.inventory::IMSalesOrderLine.itemId> // (required: true)
        locationId: field<everest.inventory::IMSalesOrderLine.locationId>
        unitId: field<everest.inventory::IMSalesOrderLine.unitId> // (required: true)
        quantity: Number<Decimal> // (required: true)
        salesOrderProductLineId: field<everest.inventory::IMSalesOrderLine.salesOrderProductLineId>
      }>>
    }
    outputs {
      success: TrueFalse
      salesOrderId: field<everest.inventory::IMSalesOrder.id> // PK of the created IMSalesOrder
      documentNumber: Text
      errorMessage: Text
    }
    properties {
      side-effects true
    }
  }

  /**
   * Action to update a sales order.
   */
  action UpdateSalesOrder {
    inputs {
      id: field<everest.inventory::IMSalesOrder.id> // PK of the IMSalesOrder to update (required: true)
      externalId: field<everest.inventory::IMSalesOrder.externalId>
      active: field<everest.inventory::IMSalesOrder.active>
      carrier: field<everest.inventory::IMSalesOrder.carrier>
      plannedShipDate: PlainDate
      requestedDeliveryDate: PlainDate
      salesOrderId: field<everest.inventory::IMSalesOrder.salesOrderId> // External system reference
      shippingAddressId: field<everest.inventory::IMSalesOrder.shippingAddressId>
      businessPartnerId: field<everest.inventory::IMSalesOrder.businessPartnerId>
      shippingMemo: Text
      shippingMethod: Text
      // documentNumber is typically not updatable directly via action if auto-managed
      lines: array<object<{
        id: field<everest.inventory::IMSalesOrderLine.id> // Optional: PK for existing lines to update
        itemId: field<everest.inventory::IMSalesOrderLine.itemId>
        locationId: field<everest.inventory::IMSalesOrderLine.locationId>
        unitId: field<everest.inventory::IMSalesOrderLine.unitId>
        quantity: Number<Decimal>
        salesOrderProductLineId: field<everest.inventory::IMSalesOrderLine.salesOrderProductLineId>
        // _delete: TrueFalse // Optional: To explicitly mark lines for deletion
      }>>
      linesToRemove: array<field<everest.inventory::IMSalesOrderLine.id>> // Optional: Array of line IDs to remove
    }
    outputs {
      success: TrueFalse
      salesOrderId: field<everest.inventory::IMSalesOrder.id> // PK of the updated IMSalesOrder
      documentNumber: Text
      errorMessage: Text
    }
    properties {
      side-effects true
    }
  }

  /**
   * Function to get a customer by ID.
   */
  action GetCustomerById {
    inputs {
      customerId: Id
    }
    outputs {
      customer: object<{
        id: Id
        name: Text
        customerNumber: Text
        email: Text
        phone: Text
        address: Text
        city: Text
        stateProvince: Text
        postalCode: Text
        country: Text
        customerType: field<everest.fin.accounting::Customer.customerType>
        status: field<everest.fin.accounting::Customer.status>
        createdDate: Date
        lastModifiedDate: Date
      }>
    }
    properties {
      side-effects false
    }
  }

  /**
   * Function to get a sales order with its lines.
   */
  action GetSalesOrderWithLines {
    inputs {
      salesOrderId: field<everest.inventory::IMSalesOrder.id>
    }
    outputs {
      salesOrder: object<{
        id: field<everest.inventory::IMSalesOrder.id>
        uuid: field<everest.inventory::IMSalesOrder.uuid>
        orderDate: PlainDate
        documentNumber: Text
        customerName: Text
        externalId: field<everest.inventory::IMSalesOrder.externalId>
        active: field<IMSalesOrder.active>
        carrier: field<IMSalesOrder.carrier>
        fulfillmentStatus: Text
        plannedShipDate: PlainDate
        requestedDeliveryDate: PlainDate
        salesOrderId: field<everest.inventory::IMSalesOrder.salesOrderId>
        shippingAddressId: field<everest.inventory::IMSalesOrder.shippingAddressId>
        businessPartnerId: field<IMSalesOrder.businessPartnerId>
        documentDate: PlainDate
        status: Text
        notes: Text
        shippingMemo: Text
        shippingMethod: Text
        createdDate: Date
        createdBy: Text
        lastModifiedDate: Date
        lastModifiedBy: Text
        lines: array<object<{
          id: field<everest.inventory::IMSalesOrderLine.id>
          headerId: field<everest.inventory::IMSalesOrderLine.headerId>
          itemId: field<everest.inventory::IMSalesOrderLine.itemId>
          itemCode: Text
          itemName: Text
          locationId: field<everest.inventory::IMSalesOrderLine.locationId>
          locationName: Text
          unitId: field<everest.inventory::IMSalesOrderLine.unitId>
          unitCode: Text
          unitPrice: Number<Decimal>
          notes: Text
          quantity: Number<Decimal>
          allocatedQuantity: Number<Decimal>
          openAllocatedQuantity: Number<Decimal>
          openQuantity: Number<Decimal>
          backorderedQuantity: Number<Decimal>
          pickedQuantity: Number<Decimal>
          shippedQuantity: Number<Decimal>
          fulfillmentStatus: Text
          actualShipDate: PlainDate
          trackingNumber: Text
          salesOrderProductLineId: field<everest.inventory::IMSalesOrderLine.salesOrderProductLineId>
        }>>
      }>
      
    }
    properties {
      side-effects false
    }
  }

  /**
   * Function to get sales order lines that can be delivered directly.
   */
  action GetSalesOrderLinesForDelivery {
    inputs {
      salesOrderId: field<everest.inventory::IMSalesOrder.id>
    }
    outputs {
      lines: array<object<{
        id: field<everest.inventory::IMSalesOrderLine.id>
        salesOrderProductLineId: field<everest.inventory::IMSalesOrderLine.salesOrderProductLineId>
        itemId: field<everest.inventory::IMSalesOrderLine.itemId>
        itemCode: Text
        itemName: Text
        unitId: field<everest.inventory::IMSalesOrderLine.unitId>
        unitCode: Text
        quantity: Number<Decimal>
        shippedQuantity: Number<Decimal>
        fulfillmentStatus: Text
      }>>
    }
    properties {
      side-effects false
    }
  }
}
