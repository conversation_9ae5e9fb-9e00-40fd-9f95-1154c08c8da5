import { log, ValidationError } from '@everestsystems/content-core';
import { PlainDate } from '@everestsystems/datetime';
import { Decimal } from '@everestsystems/decimal';
import { Customer } from '@pkg/everest.fin.accounting/types/Customer';
import { EvstCustomerPartnerResellerType } from '@pkg/everest.fin.accounting/types/enums/CustomerPartnerResellerType';
import { EvstCustomerStatus } from '@pkg/everest.fin.accounting/types/enums/CustomerStatus';
import { AllocationService } from '@pkg/everest.inventory/services/allocation/AllocationService';
import { BackorderService } from '@pkg/everest.inventory/services/backorder/BackorderService';
import { PickListService } from '@pkg/everest.inventory/services/picking/PickListService';
import { IMInventorySalesDelivery } from '@pkg/everest.inventory/types/IMInventorySalesDelivery';
import { IMInventorySalesDeliveryLine } from '@pkg/everest.inventory/types/IMInventorySalesDeliveryLine';
import { IMPackageLine } from '@pkg/everest.inventory/types/IMPackageLine';
import { IMPickListLine } from '@pkg/everest.inventory/types/IMPickListLine';
import { IMReservation } from '@pkg/everest.inventory/types/IMReservation';
import { IMReservationLine } from '@pkg/everest.inventory/types/IMReservationLine';
import { IMSalesOrder } from '@pkg/everest.inventory/types/IMSalesOrder';
import { IMSalesOrderLine } from '@pkg/everest.inventory/types/IMSalesOrderLine';
import type { SalesOrderService } from '@pkg/everest.inventory/types/presentations/services/salesOrder/SalesOrderService';
import { omit } from 'lodash';

import { SalesOrderEnhancementService } from './SalesOrderEnhancementService';

export default {
  // SalesOrders EntitySet implementation
  SalesOrders: {
    query: {
      async execute({
        session,
        where,
        orderBy,
        skip,
        take,
        count,
        fieldList,
      }: SalesOrderService.EntitySets.SalesOrders.Query.Execute.Request): Promise<SalesOrderService.EntitySets.SalesOrders.Query.Execute.Response> {
        try {
          log.debug('Querying SalesOrders with parameters', {
            where,
            orderBy,
            skip,
            take,
            count,
          });

          const fields = omit(fieldList, 'lines');

          const salesOrders = await IMSalesOrder.query(
            session,
            {
              where: where, //caseInsensitiveQuery(where),
              orderBy,
              skip,
              take,
            },
            Array.from(fields)
          );

          // Enhance the sales orders with additional information
          const enhancedSalesOrders = await Promise.all(
            salesOrders.map(async (salesOrder) => {
              // Get the sales order lines
              const lines = await IMSalesOrderLine.query(
                session,
                { where: { headerId: salesOrder.id } },
                [
                  'id',
                  'headerId',
                  'itemId',
                  'locationId',
                  'unitId',
                  'quantity',
                  'allocatedQuantity',
                  'openQuantity',
                  'backorderedQuantity',
                  'pickedQuantity',
                  'shippedQuantity',
                  'fulfillmentStatus',
                  'actualShipDate',
                  'trackingNumber',
                  'salesOrderProductLineId',
                  'createdDate',
                  'lastModifiedDate',
                  'createdBy',
                  'lastModifiedBy',
                  {
                    ItemMaster: ['itemCode', 'itemName'],
                  },
                  {
                    IMWarehouseLocation: ['name'],
                  },
                  {
                    UnitOfMeasure: ['unitCode'],
                  },
                ]
              );

              // Get active reservations for this sales order
              const activeReservations = await IMReservation.query(
                session,
                {
                  where: {
                    referenceDocumentId: salesOrder.id,
                    referenceDocumentType: 'SALES_ORDER',
                    status: 'ACTIVE',
                  },
                },
                ['id']
              );

              // Get reservation line IDs for all active reservations
              const reservationIds = activeReservations.map((r) => r.id);

              // Get all reservation lines for active reservations
              const reservationLines =
                reservationIds.length > 0
                  ? await IMReservationLine.query(
                      session,
                      {
                        where: {
                          headerId: { $in: reservationIds },
                        },
                      },
                      ['sourceDocumentLineId', 'quantity']
                    )
                  : [];

              // Create a map of sales order line ID to total open allocated quantity
              const openAllocatedQuantityMap = new Map<number, Decimal>();
              for (const reservationLine of reservationLines) {
                if (reservationLine.sourceDocumentLineId) {
                  const currentQuantity =
                    openAllocatedQuantityMap.get(
                      reservationLine.sourceDocumentLineId
                    ) || new Decimal(0);
                  openAllocatedQuantityMap.set(
                    reservationLine.sourceDocumentLineId,
                    currentQuantity.plus(reservationLine.quantity)
                  );
                }
              }

              // Process and enhance each line
              const enhancedLines = await Promise.all(
                lines.map(async (line) => {
                  // Get the open allocated quantity for this line
                  const openAllocatedQuantity =
                    openAllocatedQuantityMap.get(line.id) || new Decimal(0);

                  // Ensure all required fields have values
                  return {
                    id: line.id,
                    headerId: line.headerId,
                    itemId: line.itemId,
                    itemCode: line.ItemMaster?.itemCode || '',
                    itemName: line.ItemMaster?.itemName || '',
                    locationId: line.locationId,
                    locationName: line.IMWarehouseLocation?.name || '',
                    unitId: line.unitId,
                    unitCode: line.UnitOfMeasure?.unitCode || '',
                    quantity: line.quantity,
                    allocatedQuantity: line.allocatedQuantity,
                    openAllocatedQuantity: openAllocatedQuantity,
                    openQuantity: line.openQuantity,
                    backorderedQuantity: line.backorderedQuantity,
                    pickedQuantity: line.pickedQuantity,
                    shippedQuantity: line.shippedQuantity,
                    fulfillmentStatus: line.fulfillmentStatus || '',
                    actualShipDate: line.actualShipDate,
                    trackingNumber: line.trackingNumber || '',
                    salesOrderProductLineId: line.salesOrderProductLineId,
                    createdDate: line.createdDate,
                    lastModifiedDate: line.lastModifiedDate,
                    createdBy: line.createdBy || '',
                    lastModifiedBy: line.lastModifiedBy || '',
                  };
                })
              );

              return {
                ...salesOrder,
                createdBy: salesOrder.createdBy || '',
                lines: enhancedLines,
              };
            })
          );

          return {
            instances: enhancedSalesOrders,
            count: count ? salesOrders.length : undefined,
          };
        } catch (error) {
          log.error('Error querying SalesOrders ', error);
          throw error;
        }
      },
    },
    create: {
      async execute({
        session,
        inputs,
      }: SalesOrderService.EntitySets.SalesOrders.Create.Execute.Request): Promise<SalesOrderService.EntitySets.SalesOrders.Create.Execute.Response> {
        try {
          // Extract data from the first input
          const data = inputs[0];
          log.debug('Creating SalesOrder via EntitySet', { data });

          // Create the sales order
          const salesOrder = await IMSalesOrder.create(session, data);

          // If lines are provided, create them
          if (
            data.lines &&
            Array.isArray(data.lines) &&
            data.lines.length > 0
          ) {
            for (const line of data.lines) {
              await IMSalesOrderLine.create(session, {
                ...line,
                headerId: salesOrder.id,
              });
            }
          }

          return { instances: [salesOrder] };
        } catch (error) {
          log.error('Error creating SalesOrder via EntitySet', error);
          throw error;
        }
      },
    },
    update: {
      async execute({
        session,
        id,
        data,
      }: SalesOrderService.EntitySets.SalesOrders.Update.Execute.Request): Promise<SalesOrderService.EntitySets.SalesOrders.Update.Execute.Response> {
        try {
          log.debug('Updating SalesOrder via EntitySet', { id, data });

          // Update the sales order
          const salesOrder = await IMSalesOrder.update(session, { id }, data);

          // If lines are provided, update them
          if (
            data.lines &&
            Array.isArray(data.lines) &&
            data.lines.length > 0
          ) {
            for (const line of data.lines) {
              line.id
                ? // Update existing line
                  await IMSalesOrderLine.update(session, { id: line.id }, line)
                : // Create new line
                  await IMSalesOrderLine.create(session, {
                    ...line,
                    headerId: salesOrder.id,
                  });
            }
          }

          return;
        } catch (error) {
          log.error('Error updating SalesOrder via EntitySet', error);
          throw error;
        }
      },
    },
    delete: {
      async execute({
        session,
        ids,
      }: SalesOrderService.EntitySets.SalesOrders.Delete.Execute.Request): Promise<SalesOrderService.EntitySets.SalesOrders.Delete.Execute.Response> {
        try {
          log.debug('Deleting SalesOrder via EntitySet', { ids });

          // Get the sales order lines
          const lines = await IMSalesOrderLine.query(
            session,
            { where: { headerId: { $in: ids } } },
            ['id']
          );

          // Delete the sales order lines
          for (const line of lines) {
            await IMSalesOrderLine.Delete(session, { id: line.id });
          }

          // Delete the sales orders one by one
          const deletedSalesOrders = [];
          for (const id of ids) {
            const result = await IMSalesOrder.Delete(session, { id });
            deletedSalesOrders.push(...result);
          }

          // Return void as required by the type definition
          return;
        } catch (error) {
          log.error('Error deleting SalesOrder via EntitySet', error);
          throw error;
        }
      },
    },
  },

  // Custom actions
  AllocateInventory: {
    async execute({
      session,
      input: {
        salesOrderId,
        salesOrderLineId,
        quantity,
        expirationDate,
        locationId,
      },
    }): Promise<SalesOrderService.Actions.AllocateInventory.Execute.Response> {
      try {
        log.debug('Allocating inventory', {
          salesOrderId,
          salesOrderLineId,
          quantity,
          expirationDate,
          locationId,
        });

        // Convert quantity to Decimal if it's not already
        const quantityDecimal =
          quantity instanceof Decimal ? quantity : new Decimal(quantity);

        // Call the allocation service with the user-selected locationId
        const result = await AllocationService.allocateInventory(
          session,
          salesOrderId,
          salesOrderLineId,
          quantityDecimal,
          PlainDate.from(expirationDate.toISOString()),
          locationId
        );

        return {
          output: {
            success: result.success,
            reservationId: result.reservationId,
            reservationNumber: result.reservationNumber,
            allocatedQuantity: result.allocatedQuantity,
            shortageQuantity: result.shortageQuantity,
            errorMessage: result.errorMessage,
            salesOrderId,
          },
        };
      } catch (error) {
        log.error('Error allocating inventory', error);
        return {
          output: {
            success: false,
            errorMessage:
              error instanceof Error ? error.message : String(error),
            reservationId: 0,
            reservationNumber: '',
            allocatedQuantity: new Decimal(0),
            shortageQuantity: new Decimal(0),
            salesOrderId,
          },
        };
      }
    },
  },

  ReleaseAllocation: {
    async execute({ session, input: { reservationId } }) {
      try {
        log.debug('Releasing allocation', { reservationId });

        // Call the allocation service
        const result = await AllocationService.releaseAllocation(
          session,
          reservationId
        );

        return {
          output: {
            success: result,
            errorMessage: '',
          },
        };
      } catch (error) {
        log.error('Error releasing allocation', error);
        return {
          output: {
            success: false,
            errorMessage:
              error instanceof Error ? error.message : String(error),
          },
        };
      }
    },
  },

  ProcessBackorders: {
    async execute({
      session,
      input: {
        salesOrderId,
        itemId,
        locationId,
        expirationDate,
        prioritizeByOrderDate,
        prioritizeByCustomerPriority,
        prioritizeByQuantity,
      },
    }) {
      try {
        log.debug('Processing backorders', {
          salesOrderId,
          itemId,
          locationId,
          expirationDate,
          prioritizeByOrderDate,
          prioritizeByCustomerPriority,
          prioritizeByQuantity,
        });

        // Identify backorders
        const backorders = await BackorderService.identifyBackorders(
          session,
          salesOrderId,
          itemId,
          locationId
        );

        if (backorders.length === 0) {
          return {
            output: {
              success: true,
              processedBackorders: 0,
              totalAllocatedQuantity: new Decimal('0'),
              remainingBackorders: 0,
              results: [],
            },
          };
        }

        // Prioritize backorders
        const prioritizedBackorders =
          await BackorderService.prioritizeBackorders(session, backorders, {
            prioritizeByOrderDate,
            prioritizeByCustomerPriority,
            prioritizeByQuantity,
          });

        // Process backorders
        const processingResults = await BackorderService.processBackorders(
          session,
          prioritizedBackorders,
          expirationDate
        );

        // Calculate totals
        let totalAllocatedQuantity = new Decimal('0');
        let remainingBackorders = 0;

        // Map results to ensure all required properties are present
        const formattedResults = processingResults.map((result) => ({
          salesOrderId: result.salesOrderId,
          salesOrderLineId: result.salesOrderLineId,
          processedQuantity: result.processedQuantity,
          remainingBackorderedQuantity: result.remainingBackorderedQuantity,
          success: result.success,
          errorMessage: result.errorMessage || '',
        }));

        for (const result of processingResults) {
          if (result.processedQuantity) {
            totalAllocatedQuantity = totalAllocatedQuantity.plus(
              result.processedQuantity
            );
          }
          if (
            result.remainingBackorderedQuantity &&
            !result.remainingBackorderedQuantity.equals(new Decimal('0'))
          ) {
            remainingBackorders++;
          }
        }

        return {
          output: {
            success: true,
            processedBackorders: processingResults.length,
            totalAllocatedQuantity,
            remainingBackorders,
            results: formattedResults,
          },
        };
      } catch (error) {
        log.error('Error processing backorders', error);
        return {
          output: {
            success: false,
            processedBackorders: 0,
            totalAllocatedQuantity: new Decimal('0'),
            remainingBackorders: 0,
            results: [],
          },
        };
      }
    },
  },
  // Create a pick list from a sales order
  CreatePickList: {
    async execute({
      session,
      input: {
        salesOrderId,
        locationId,
        assignedUserId,
        pickDate,
        priority,
        notes,
        expectedCompletionDate,
      },
    }) {
      try {
        log.debug('Creating pick list for sales order', {
          salesOrderId,
          locationId,
          assignedUserId,
          pickDate,
          priority,
          notes,
          expectedCompletionDate,
        });

        // Convert pickDate string to PlainDate if provided
        let pickDateObj: PlainDate | undefined;
        if (pickDate) {
          const date = new Date(pickDate.toISOString());
          pickDateObj = PlainDate.from({
            year: date.getFullYear(),
            month: date.getMonth() + 1,
            day: date.getDate(),
          });
        }

        // Create the pick list using the PickListService with location filtering
        // Use locationId as both warehouse and specific location
        const result =
          await PickListService.createPickListForSalesOrderWithLocation(
            session,
            salesOrderId,
            locationId, // warehouseId
            locationId, // specific locationId for filtering
            assignedUserId,
            pickDateObj,
            priority,
            notes,
            PlainDate.from(expectedCompletionDate.toISOString())
          );

        // Validate that the pick list has lines
        if (!result.pickListLines || result.pickListLines.length === 0) {
          throw new Error(
            `No items available for picking at the selected location. Please ensure items are allocated to this location before creating a pick list.`
          );
        }

        return {
          output: {
            success: true,
            pickListId: result.pickListHeader.id,
            pickListNumber: result.pickListHeader.pickListNumber,
            salesOrderId,
            warnings: result.warnings || [],
            errors: [],
          },
        };
      } catch (error) {
        log.error('Error creating pick list', error);
        if (error instanceof ValidationError) {
          throw error;
        }
        return {
          output: {
            success: false,
            pickListId: 0,
            pickListNumber: '',
            salesOrderId,
            warnings: [],
            errors: [error instanceof Error ? error.message : String(error)],
          },
        };
      }
    },
  },

  // Create a sales delivery from a sales order
  CreateSalesDelivery: {
    async execute({ session, input: { deliveryDate, notes, lines } }) {
      try {
        log.debug('Creating sales delivery', {
          deliveryDate,
          notes,
          lines,
        });

        // We need at least one line to create a sales delivery
        if (!lines || lines.length === 0) {
          throw new Error(
            'At least one line is required to create a sales delivery'
          );
        }

        // Check if this is package-based or direct delivery
        const hasPackageLines = lines.some((line) => line.packageLineId);

        let salesOrderId: number;

        if (hasPackageLines) {
          // Package-based delivery - get sales order from package line
          const firstPackageLine = await IMPackageLine.read(
            session,
            { id: lines[0].packageLineId },
            ['pickListLineId']
          );

          if (!firstPackageLine || !firstPackageLine.pickListLineId) {
            throw new Error('Package line must have a pickListLineId');
          }

          // Get the pick list line to find the sales order
          const pickListLine = await IMPickListLine.read(
            session,
            { id: firstPackageLine.pickListLineId },
            ['sourceTransLineId', 'sourceTransLineType']
          );

          if (
            !pickListLine ||
            pickListLine.sourceTransLineType !== 'SALES_ORDER_LINE' ||
            !pickListLine.sourceTransLineId
          ) {
            throw new Error('Pick list line must reference a sales order line');
          }

          // Get the sales order line to find the sales order ID
          const salesOrderLine = await IMSalesOrderLine.read(
            session,
            { id: pickListLine.sourceTransLineId },
            ['headerId']
          );

          if (!salesOrderLine || !salesOrderLine.headerId) {
            throw new Error('Could not find sales order for the package line');
          }

          salesOrderId = salesOrderLine.headerId;
        } else {
          // Direct delivery - get sales order from the first line
          const firstSalesOrderLine = await IMSalesOrderLine.read(
            session,
            { id: lines[0].salesOrderLineId },
            ['headerId']
          );

          if (!firstSalesOrderLine || !firstSalesOrderLine.headerId) {
            throw new Error('Could not find sales order for the delivery line');
          }

          salesOrderId = firstSalesOrderLine.headerId;
        }

        // Get the sales order to retrieve necessary information
        const salesOrder = await IMSalesOrder.read(
          session,
          { id: salesOrderId },
          ['id', 'documentNumber', 'businessPartnerId']
        );

        if (!salesOrder) {
          throw new Error(`Sales order with ID ${salesOrderId} not found`);
        }

        // Convert date string to PlainDate if provided
        let deliveryDateObj: PlainDate | undefined;
        if (deliveryDate) {
          const date = new Date(deliveryDate.toISOString());
          deliveryDateObj = PlainDate.from({
            year: date.getFullYear(),
            month: date.getMonth() + 1,
            day: date.getDate(),
          });
        }

        // Get current date as PlainDate
        const today = PlainDate.from({
          year: new Date().getFullYear(),
          month: new Date().getMonth() + 1,
          day: new Date().getDate(),
        });

        // Get entity ID - we'll get it from the first line's location
        let entityId: number | undefined;

        if (hasPackageLines) {
          // For package-based delivery, get entity from the first sales order line
          const firstSalesOrderLine = await IMSalesOrderLine.read(
            session,
            { id: lines[0].salesOrderLineId },
            ['locationId', { IMWarehouseLocation: ['entityId'] }]
          );
          entityId = firstSalesOrderLine?.IMWarehouseLocation?.entityId;
        } else {
          // For direct delivery, get entity from the first sales order line
          const firstSalesOrderLine = await IMSalesOrderLine.read(
            session,
            { id: lines[0].salesOrderLineId },
            ['locationId', { IMWarehouseLocation: ['entityId'] }]
          );
          entityId = firstSalesOrderLine?.IMWarehouseLocation?.entityId;
        }

        // Create the inventory sales delivery
        const inventorySalesDelivery = await IMInventorySalesDelivery.create(
          session,
          {
            documentNumber: `SD-${salesOrder.documentNumber}`,
            documentDate: today,
            status: 'DRAFT',
            entityId: entityId,
            businessPartnerId: salesOrder.businessPartnerId,
            deliveryDate: deliveryDateObj,
            notes: notes,
            active: true,
          }
        );

        // Create the inventory sales delivery lines
        const createdLines = [];

        // Process each line to create delivery lines
        for (const line of lines) {
          if (hasPackageLines) {
            // Package-based delivery
            if (!line.packageLineId) {
              continue;
            }

            // Get the package line to find the pickListLineId
            const packageLine = await IMPackageLine.read(
              session,
              { id: line.packageLineId },
              ['pickListLineId', 'itemId', 'quantity', 'unitId']
            );

            if (!packageLine || !packageLine.pickListLineId) {
              continue;
            }

            // Get the pick list line to find the sourceTransLineId (sales order line ID)
            const pickListLine = await IMPickListLine.read(
              session,
              { id: packageLine.pickListLineId },
              ['sourceTransLineId', 'sourceTransLineType', 'pickedBinId']
            );

            if (
              !pickListLine ||
              pickListLine.sourceTransLineType !== 'SALES_ORDER_LINE' ||
              !pickListLine.sourceTransLineId
            ) {
              continue;
            }

            // Get the sales order line to get its details
            const salesOrderLine = await IMSalesOrderLine.read(
              session,
              { id: pickListLine.sourceTransLineId },
              [
                'itemId',
                'unitId',
                'allocatedQuantity',
                'pickedQuantity',
                'locationId',
                {
                  SalesOrderProductLine: ['unitPrice'],
                },
              ]
            );

            if (!salesOrderLine) {
              continue;
            }

            // Use the location from the sales order line
            const lineLocationId = salesOrderLine.locationId;

            // Get the unit price from SalesOrderProductLine
            let unitPriceAmount: Decimal;
            if (salesOrderLine.SalesOrderProductLine?.unitPrice?.amount) {
              unitPriceAmount =
                salesOrderLine.SalesOrderProductLine.unitPrice.amount instanceof
                Decimal
                  ? salesOrderLine.SalesOrderProductLine.unitPrice.amount
                  : new Decimal(
                      salesOrderLine.SalesOrderProductLine.unitPrice.amount
                    );
            } else {
              unitPriceAmount = new Decimal('0');
            }

            // Get the currency from SalesOrderProductLine or default to USD
            const unitPriceCurrency =
              salesOrderLine.SalesOrderProductLine?.unitPrice?.currency ||
              'USD';

            // Create the delivery line
            const deliveryLine = await IMInventorySalesDeliveryLine.create(
              session,
              {
                headerId: inventorySalesDelivery.id,
                itemId: salesOrderLine.itemId,
                quantity: packageLine.quantity,
                inventoryQuantity: packageLine.quantity,
                unitId: salesOrderLine.unitId,
                locationId: lineLocationId,
                status: 'PACKED',
                sourceDocumentLineId: pickListLine.sourceTransLineId,
                sourceDocumentLineType: 'IMSalesOrderLine',
                unitPrice: {
                  amount: unitPriceAmount,
                  currency: unitPriceCurrency,
                },
              }
            );

            createdLines.push({
              inventorySalesDeliveryLineId: deliveryLine.id,
              itemId: salesOrderLine.itemId,
              quantity: packageLine.quantity,
              pickedQuantity: salesOrderLine.pickedQuantity || new Decimal('0'),
            });

            // Update the sales order line status to SHIPPED
            await IMSalesOrderLine.update(
              session,
              { id: pickListLine.sourceTransLineId },
              { fulfillmentStatus: 'SHIPPED' }
            );

            // Update the pick list line status to SHIPPED
            await IMPickListLine.update(
              session,
              { id: packageLine.pickListLineId },
              { status: 'SHIPPED' }
            );

            // Update the package line status to SHIPPED
            await IMPackageLine.update(
              session,
              { id: line.packageLineId },
              { status: 'SHIPPED' }
            );
          } else {
            // Direct delivery - no packages involved
            if (!line.salesOrderLineId) {
              continue;
            }

            // Get the sales order line directly
            const salesOrderLine = await IMSalesOrderLine.read(
              session,
              { id: line.salesOrderLineId },
              [
                'itemId',
                'unitId',
                'allocatedQuantity',
                'pickedQuantity',
                'locationId',
                {
                  SalesOrderProductLine: ['unitPrice'],
                },
              ]
            );

            if (!salesOrderLine) {
              continue;
            }

            // Use the location from the sales order line
            const lineLocationId = salesOrderLine.locationId;

            // Get the unit price from SalesOrderProductLine
            let unitPriceAmount: Decimal;
            if (salesOrderLine.SalesOrderProductLine?.unitPrice?.amount) {
              unitPriceAmount =
                salesOrderLine.SalesOrderProductLine.unitPrice.amount instanceof
                Decimal
                  ? salesOrderLine.SalesOrderProductLine.unitPrice.amount
                  : new Decimal(
                      salesOrderLine.SalesOrderProductLine.unitPrice.amount
                    );
            } else {
              unitPriceAmount = new Decimal('0');
            }

            // Get the currency from SalesOrderProductLine or default to USD
            const unitPriceCurrency =
              salesOrderLine.SalesOrderProductLine?.unitPrice?.currency ||
              'USD';

            // Create the delivery line
            const deliveryLine = await IMInventorySalesDeliveryLine.create(
              session,
              {
                headerId: inventorySalesDelivery.id,
                itemId: salesOrderLine.itemId,
                quantity: line.quantity,
                inventoryQuantity: line.quantity,
                unitId: salesOrderLine.unitId,
                locationId: lineLocationId,
                status: 'DRAFT',
                sourceDocumentLineId: line.salesOrderLineId,
                sourceDocumentLineType: 'IMSalesOrderLine',
                unitPrice: {
                  amount: unitPriceAmount,
                  currency: unitPriceCurrency,
                },
              }
            );

            createdLines.push({
              inventorySalesDeliveryLineId: deliveryLine.id,
              itemId: salesOrderLine.itemId,
              quantity: line.quantity,
              pickedQuantity: salesOrderLine.pickedQuantity || new Decimal('0'),
            });

            // Update the sales order line status to SHIPPED
            await IMSalesOrderLine.update(
              session,
              { id: line.salesOrderLineId },
              { fulfillmentStatus: 'SHIPPED' }
            );
          }
        }

        return {
          output: {
            success: true,
            deliveryId: inventorySalesDelivery.id,
            deliveryNumber: inventorySalesDelivery.documentNumber,
            salesOrderId,
            warnings: [],
            errors: [],
          },
        };
      } catch (error) {
        log.error('Error creating sales delivery', error);
        return {
          output: {
            success: false,
            deliveryId: 0,
            deliveryNumber: '',
            salesOrderId: 0,
            warnings: [],
            errors: [error instanceof Error ? error.message : String(error)],
          },
        };
      }
    },
  },

  // Create a sales delivery from pick list lines
  CreateSalesDeliveryFromPickList: {
    async execute({ session, input: { deliveryDate, notes, lines } }) {
      try {
        log.debug('Creating sales delivery from pick list', {
          deliveryDate,
          notes,
          lines,
        });

        // We need at least one pick list line to create a sales delivery
        if (!lines || lines.length === 0 || !lines[0].pickListLineId) {
          throw new Error(
            'At least one pick list line is required to create a sales delivery'
          );
        }

        // Get the first pick list line to find the sourceTransLineId (sales order line ID)
        const firstPickListLine = await IMPickListLine.read(
          session,
          { id: lines[0].pickListLineId },
          ['sourceTransLineId', 'sourceTransLineType', 'pickedBinId']
        );

        if (
          !firstPickListLine ||
          firstPickListLine.sourceTransLineType !== 'SALES_ORDER_LINE' ||
          !firstPickListLine.sourceTransLineId
        ) {
          throw new Error('Pick list line must reference a sales order line');
        }

        // Get the sales order line to find the sales order ID and location
        const firstSalesOrderLine = await IMSalesOrderLine.read(
          session,
          { id: firstPickListLine.sourceTransLineId },
          ['headerId', 'locationId', { IMWarehouseLocation: ['entityId'] }]
        );

        if (!firstSalesOrderLine || !firstSalesOrderLine.headerId) {
          throw new Error('Could not find sales order for the pick list line');
        }

        const salesOrderId = firstSalesOrderLine.headerId;

        // Get the sales order to retrieve necessary information
        const salesOrder = await IMSalesOrder.read(
          session,
          { id: salesOrderId },
          ['id', 'documentNumber', 'businessPartnerId']
        );

        if (!salesOrder) {
          throw new Error(`Sales order with ID ${salesOrderId} not found`);
        }

        // Convert date string to PlainDate if provided
        let deliveryDateObj: PlainDate | undefined;
        if (deliveryDate) {
          const date = new Date(deliveryDate.toISOString());
          deliveryDateObj = PlainDate.from({
            year: date.getFullYear(),
            month: date.getMonth() + 1,
            day: date.getDate(),
          });
        }

        // Get current date as PlainDate
        const today = PlainDate.from({
          year: new Date().getFullYear(),
          month: new Date().getMonth() + 1,
          day: new Date().getDate(),
        });

        // Get entity ID from the sales order line's location
        const entityId = firstSalesOrderLine.IMWarehouseLocation?.entityId;

        // Create the inventory sales delivery
        const inventorySalesDelivery = await IMInventorySalesDelivery.create(
          session,
          {
            documentNumber: `SD-${salesOrder.documentNumber}`,
            documentDate: today,
            status: 'DRAFT',
            entityId: entityId,
            businessPartnerId: salesOrder.businessPartnerId,
            deliveryDate: deliveryDateObj,
            notes: notes,
            active: true,
          }
        );

        // Create the inventory sales delivery lines
        const createdLines = [];

        // Process each pick list line to create delivery lines
        for (const line of lines) {
          if (!line.pickListLineId) {
            continue;
          }

          // Get the pick list line to find the sourceTransLineId (sales order line ID)
          const pickListLine = await IMPickListLine.read(
            session,
            { id: line.pickListLineId },
            [
              'sourceTransLineId',
              'sourceTransLineType',
              'pickedBinId',
              'pickedQuantity',
            ]
          );

          if (
            !pickListLine ||
            pickListLine.sourceTransLineType !== 'SALES_ORDER_LINE' ||
            !pickListLine.sourceTransLineId
          ) {
            continue;
          }

          // Get the sales order line to get its details
          const salesOrderLine = await IMSalesOrderLine.read(
            session,
            { id: pickListLine.sourceTransLineId },
            [
              'itemId',
              'unitId',
              'allocatedQuantity',
              'pickedQuantity',
              'locationId',
              {
                SalesOrderProductLine: ['unitPrice'],
              },
            ]
          );

          if (!salesOrderLine) {
            continue;
          }

          // Use the location from the sales order line
          const lineLocationId = salesOrderLine.locationId;

          // Get the unit price from SalesOrderProductLine
          let unitPriceAmount: Decimal;
          if (salesOrderLine.SalesOrderProductLine?.unitPrice?.amount) {
            unitPriceAmount =
              salesOrderLine.SalesOrderProductLine.unitPrice.amount instanceof
              Decimal
                ? salesOrderLine.SalesOrderProductLine.unitPrice.amount
                : new Decimal(
                    salesOrderLine.SalesOrderProductLine.unitPrice.amount
                  );
          } else {
            unitPriceAmount = new Decimal('0');
          }

          // Get the currency from SalesOrderProductLine or default to USD
          const unitPriceCurrency =
            salesOrderLine.SalesOrderProductLine?.unitPrice?.currency || 'USD';

          // Use the picked quantity from the pick list line, or the line quantity if not available
          const deliveryQuantity = pickListLine.pickedQuantity || line.quantity;

          // Create the delivery line
          const deliveryLine = await IMInventorySalesDeliveryLine.create(
            session,
            {
              headerId: inventorySalesDelivery.id,
              itemId: salesOrderLine.itemId,
              quantity: deliveryQuantity,
              inventoryQuantity: deliveryQuantity,
              unitId: salesOrderLine.unitId,
              locationId: lineLocationId,
              status: 'PICKED',
              sourceDocumentLineId: pickListLine.sourceTransLineId,
              sourceDocumentLineType: 'IMSalesOrderLine',
              unitPrice: {
                amount: unitPriceAmount,
                currency: unitPriceCurrency,
              },
            }
          );

          createdLines.push({
            inventorySalesDeliveryLineId: deliveryLine.id,
            itemId: salesOrderLine.itemId,
            quantity: deliveryQuantity,
            pickedQuantity: salesOrderLine.pickedQuantity || new Decimal('0'),
          });

          // Update the pick list line status to DELIVERED
          await IMPickListLine.update(
            session,
            { id: line.pickListLineId },
            { status: 'DELIVERED' }
          );
        }

        return {
          output: {
            success: true,
            deliveryId: inventorySalesDelivery.id,
            deliveryNumber: inventorySalesDelivery.documentNumber,
            salesOrderId,
            warnings: [],
            errors: [],
          },
        };
      } catch (error) {
        log.error('Error creating sales delivery from pick list', error);
        return {
          output: {
            success: false,
            deliveryId: 0,
            deliveryNumber: '',
            salesOrderId: 0,
            warnings: [],
            errors: [error instanceof Error ? error.message : String(error)],
          },
        };
      }
    },
  },

  CreateSalesOrder: {
    async execute({
      session,
      input,
    }: SalesOrderService.Actions.CreateSalesOrder.Execute.Request): Promise<SalesOrderService.Actions.CreateSalesOrder.Execute.Response> {
      try {
        log.debug('Creating SalesOrder via action', { input });
        const { lines, ...headerData } = input;

        // Create the sales order header
        const salesOrder = await IMSalesOrder.create(session, headerData);

        // If lines are provided, create them
        if (lines && Array.isArray(lines) && lines.length > 0) {
          for (const line of lines) {
            await IMSalesOrderLine.create(session, {
              ...line,
              headerId: salesOrder.id,
            });
          }
        }

        return {
          output: {
            success: true,
            salesOrderId: salesOrder.id,
            documentNumber: salesOrder.documentNumber || '',
            errorMessage: '',
          },
        };
      } catch (error) {
        log.error('Error creating SalesOrder via action', error);
        return {
          output: {
            success: false,
            salesOrderId: 0,
            documentNumber: '',
            errorMessage:
              error instanceof Error ? error.message : String(error),
          },
        };
      }
    },
  },

  UpdateSalesOrder: {
    async execute({
      session,
      input,
    }: SalesOrderService.Actions.UpdateSalesOrder.Execute.Request): Promise<SalesOrderService.Actions.UpdateSalesOrder.Execute.Response> {
      try {
        const { id, lines, linesToRemove, ...headerData } = input;
        log.debug('Updating SalesOrder via action', {
          id,
          headerData,
          lines,
          linesToRemove,
        });

        // Update the sales order header
        // Ensure IMSalesOrder.update can handle an empty headerData object if only lines are updated
        const salesOrder =
          Object.keys(headerData).length > 0
            ? await IMSalesOrder.update(session, { id }, headerData)
            : await IMSalesOrder.read(session, { id }, [
                'id',
                'documentNumber',
              ]);

        if (!salesOrder) {
          throw new Error(`SalesOrder with id ${id} not found for update.`);
        }

        // Remove lines if specified
        if (
          linesToRemove &&
          Array.isArray(linesToRemove) &&
          linesToRemove.length > 0
        ) {
          for (const lineId of linesToRemove) {
            await IMSalesOrderLine.Delete(session, { id: lineId });
          }
        }

        // If lines are provided, update or create them
        if (lines && Array.isArray(lines) && lines.length > 0) {
          for (const line of lines) {
            await (line.id
              ? IMSalesOrderLine.update(session, { id: line.id }, line)
              : IMSalesOrderLine.create(session, {
                  ...line,
                  headerId: id,
                  openQuantity: line.quantity,
                }));
          }
        }

        // Re-fetch to get potentially updated documentNumber if not returned by update
        const updatedSalesOrder = await IMSalesOrder.read(session, { id }, [
          'id',
          'documentNumber',
        ]);

        return {
          output: {
            success: true,
            salesOrderId: updatedSalesOrder.id,
            documentNumber: updatedSalesOrder.documentNumber || '',
            errorMessage: '',
          },
        };
      } catch (error) {
        log.error('Error updating SalesOrder via action', error);
        const salesOrderIdFromInput = 'id' in input ? input.id : 0;
        return {
          output: {
            success: false,
            salesOrderId: salesOrderIdFromInput,
            documentNumber: '',
            errorMessage:
              error instanceof Error ? error.message : String(error),
          },
        };
      }
    },
  },

  // Get a sales order with its lines
  GetSalesOrderWithLines: {
    async execute({ session, input: { salesOrderId } }) {
      try {
        log.debug('Getting sales order with lines', { salesOrderId });

        // Get the sales order header
        const salesOrderData =
          await SalesOrderEnhancementService.getSalesOrderHeader(
            session,
            salesOrderId
          );

        if (!salesOrderData) {
          return {
            output: {
              salesOrder: null,
            },
          };
        }

        // Get enhanced sales order lines
        const enhancedLines =
          await SalesOrderEnhancementService.getEnhancedSalesOrderLines(
            session,
            salesOrderId
          );

        // Create a properly formatted salesOrder object that matches the expected type
        const salesOrder = {
          id: salesOrderData.id,
          uuid: salesOrderData.uuid || '',
          documentNumber: salesOrderData.documentNumber || '',
          customerName:
            salesOrderData.SalesOrder?.['SalesOrder-Customer']?.customerName ||
            '',
          orderDate: salesOrderData.SalesOrder?.bookDate,
          externalId: salesOrderData.externalId || '',
          active: salesOrderData.active || false,
          carrier: salesOrderData.carrier || '',
          fulfillmentStatus: salesOrderData.fulfillmentStatus || '',
          plannedShipDate: salesOrderData.plannedShipDate,
          requestedDeliveryDate: salesOrderData.requestedDeliveryDate,
          salesOrderId: salesOrderData.salesOrderId,
          shippingAddressId: salesOrderData.shippingAddressId,
          shippingMemo: salesOrderData.shippingMemo || '',
          shippingMethod: salesOrderData.shippingMethod || '',
          createdDate: salesOrderData.createdDate,
          createdBy: salesOrderData.createdBy || '',
          lastModifiedDate: salesOrderData.lastModifiedDate,
          lastModifiedBy: salesOrderData.lastModifiedBy || '',
          businessPartnerId: salesOrderData.businessPartnerId,
          documentDate: salesOrderData.SalesOrder?.bookDate,
          status: salesOrderData.fulfillmentStatus || '',
          notes: '',
        };

        const preparedLines = enhancedLines.map((line) => ({
          id: line.id,
          headerId: line.headerId,
          itemId: line.itemId,
          itemCode: line.itemCode || '',
          itemName: line.itemName || '',
          locationId: line.locationId,
          locationName: line.locationName || '',
          unitId: line.unitId,
          unitCode: line.unitCode || '',
          unitPrice: line.unitPrice || new Decimal('0'),
          notes: line.notes || '',
          quantity: line.quantity || new Decimal('0'),
          allocatedQuantity: line.allocatedQuantity || new Decimal('0'),
          openAllocatedQuantity: line.openAllocatedQuantity || new Decimal('0'),
          openQuantity: line.openQuantity || new Decimal('0'),
          backorderedQuantity: line.backorderedQuantity || new Decimal('0'),
          pickedQuantity: line.pickedQuantity || new Decimal('0'),
          shippedQuantity: line.shippedQuantity || new Decimal('0'),
          fulfillmentStatus: line.fulfillmentStatus || '',
          actualShipDate: line.actualShipDate || null,
          trackingNumber: line.trackingNumber || '',
          salesOrderProductLineId: line.salesOrderProductLineId,
        }));
        return {
          output: {
            salesOrder: { ...salesOrder, lines: preparedLines },
          },
        };
      } catch (error) {
        log.error('Error getting sales order with lines', error);
        throw error;
      }
    },
  },

  // GetCustomerById action implementation
  GetCustomerById: {
    async execute({ session, input: { customerId } }) {
      try {
        log.debug('Getting customer by ID', { customerId });

        // Fetch the customer from fin.accounting
        const customerData = await Customer.read(session, { id: customerId }, [
          'id',
          'customerName',
          'customerNumber',
          'mainContactEmail',
          'mainContactName',
          'billingEmail',
          'country',
          'stateProvince',
          'customerType',
          'status',
          'createdDate',
          'lastModifiedDate',
        ]);

        if (!customerData) {
          throw new Error(`Customer with ID ${customerId} not found`);
        }

        // Map the customer data to the expected format
        const customer = {
          id: customerData.id,
          name: customerData.customerName || '',
          customerNumber: customerData.customerNumber || '',
          email:
            customerData.mainContactEmail || customerData.billingEmail || '',
          phone: '', // Not directly available in Customer model
          address: '', // Address details are in CustomerAddress entity
          city: '',
          stateProvince: customerData.stateProvince || '',
          postalCode: '',
          country: customerData.country || '',
          customerType:
            customerData.customerType ||
            EvstCustomerPartnerResellerType.Default,
          status: customerData.status || EvstCustomerStatus.Active,
          createdDate: customerData.createdDate,
          lastModifiedDate: customerData.lastModifiedDate,
        };

        return {
          output: {
            customer,
          },
        };
      } catch (error) {
        log.error('Error getting customer by ID', error);
        throw error;
      }
    },
  },

  // Get sales order lines that can be delivered directly
  GetSalesOrderLinesForDelivery: {
    async execute({ session, input: { salesOrderId } }) {
      try {
        log.debug('Getting sales order lines for delivery', { salesOrderId });

        // Get enhanced sales order lines
        const enhancedLines =
          await SalesOrderEnhancementService.getEnhancedSalesOrderLines(
            session,
            salesOrderId
          );

        if (!enhancedLines || enhancedLines.length === 0) {
          return {
            output: {
              lines: [],
            },
          };
        }

        // Filter lines that can be delivered (have open quantity)
        const deliverableLines =
          SalesOrderEnhancementService.filterDeliverableLines(enhancedLines);

        return {
          output: {
            lines: deliverableLines,
          },
        };
      } catch (error) {
        log.error('Error getting sales order lines for delivery', error);
        throw error;
      }
    },
  },

  // Customers EntitySet implementation
  Customers: {
    query: {
      async execute({ session, where, orderBy, skip, take, count }) {
        try {
          log.debug('Querying Customers EntitySet with parameters', {
            where,
            orderBy,
            skip,
            take,
            count,
          });

          // Query customers from fin.accounting
          const customers = await Customer.query(
            session,
            {
              where: where, //caseInsensitiveQuery(where),
              orderBy: orderBy || [{ field: 'customerName', ordering: 'asc' }],
              skip,
              take,
            },
            [
              'id',
              'customerName',
              'customerNumber',
              'mainContactEmail',
              'mainContactName',
              'billingEmail',
              'country',
              'stateProvince',
              'customerType',
              'status',
              'createdDate',
              'lastModifiedDate',
            ]
          );

          // Map the customer data to the expected format
          const mappedCustomers = customers.map((customerData) => ({
            id: customerData.id,
            name: customerData.customerName || '',
            customerNumber: customerData.customerNumber || '',
            email:
              customerData.mainContactEmail || customerData.billingEmail || '',
            phone: '', // Not directly available in Customer model
            address: '', // Address details are in CustomerAddress entity
            city: '',
            stateProvince: customerData.stateProvince || '',
            postalCode: '',
            country: customerData.country || '',
            customerType:
              customerData.customerType ||
              EvstCustomerPartnerResellerType.Default,
            status: customerData.status || EvstCustomerStatus.Active,
            createdDate: customerData.createdDate,
            lastModifiedDate: customerData.lastModifiedDate,
          }));

          const response = {
            instances: mappedCustomers,
            count: 0,
          };

          if (count) {
            const queryResult = await Customer.query(session, { where }, [
              'id',
            ]);

            response.count = queryResult.length;
          }

          return response;
        } catch (error) {
          log.error('Error querying Customers EntitySet', error);
          throw error;
        }
      },
    },
  },
} satisfies SalesOrderService.Implementation;
