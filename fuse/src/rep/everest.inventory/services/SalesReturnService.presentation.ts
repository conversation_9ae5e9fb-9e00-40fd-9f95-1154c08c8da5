import { Filter, type ISession, log } from '@everestsystems/content-core';
import { PlainDate } from '@everestsystems/datetime';
import { Decimal } from '@everestsystems/decimal';
import { Customer } from '@pkg/everest.fin.accounting/types/Customer';
import { EvstCustomerPartnerResellerType } from '@pkg/everest.fin.accounting/types/enums/CustomerPartnerResellerType';
import { EvstCustomerStatus } from '@pkg/everest.fin.accounting/types/enums/CustomerStatus';
import { IMInventorySalesReturn } from '@pkg/everest.inventory/types/IMInventorySalesReturn';
import { IMInventorySalesReturnBatch } from '@pkg/everest.inventory/types/IMInventorySalesReturnBatch';
import { IMInventorySalesReturnBinLocation } from '@pkg/everest.inventory/types/IMInventorySalesReturnBinLocation';
import { IMInventorySalesReturnLine } from '@pkg/everest.inventory/types/IMInventorySalesReturnLine';
import { IMInventorySalesReturnSerial } from '@pkg/everest.inventory/types/IMInventorySalesReturnSerial';
import { IMWarehouseLocation } from '@pkg/everest.inventory/types/IMWarehouseLocation';
import type { SalesReturnService } from '@pkg/everest.inventory/types/presentations/services/SalesReturnService';
import { omit } from 'lodash';

import { convertToBaseUoM } from '../common/conversion/UnitOfMeasureConverter';
import { createInventoryTransactions } from '../common/createInventoryTransactions';
import type { InventoryTransactionInput } from '../common/types/TransactionTypes';
import { updateSalesDeliveryLineReturnedQuantity } from '../common/document/SourceDocumentService';
import { IMInventorySalesReturnInspectionDetail } from '../types/IMInventorySalesReturnInspectionDetail';
import { SalesReturnNumberGenerator } from './salesReturn/helpers/SalesReturnNumberGenerator';
import { SalesReturnUpdateService } from './salesReturn/SalesReturnUpdateService';
import {
  SalesReturnLineUpdateDto,
  SalesReturnUpdateDto,
} from '../dtos/salesReturn/SalesReturnDTOs';

export default {
  // Lines EntitySet implementation
  Lines: {
    query: {
      async execute({
        session,
        where,
        orderBy,
        skip,
        take,
        count,
        fieldList,
      }: SalesReturnService.EntitySets.Lines.Query.Execute.Request): Promise<SalesReturnService.EntitySets.Lines.Query.Execute.Response> {
        try {
          log.debug('Querying SalesReturn Lines with parameters', {
            where,
            orderBy,
            skip,
            take,
            count,
          });

          const lines = await IMInventorySalesReturnLine.query(
            session,
            {
              where: where,
              orderBy,
              skip,
              take,
            },
            Array.from(fieldList)
          );

          return {
            instances: lines,
            count: count ? lines.length : undefined,
          };
        } catch (error) {
          log.error('Error querying SalesReturn Lines', error);
          throw error;
        }
      },
    },
    create: {
      async execute({
        session,
        inputs,
      }: SalesReturnService.EntitySets.Lines.Create.Execute.Request): Promise<SalesReturnService.EntitySets.Lines.Create.Execute.Response> {
        try {
          log.debug('Creating SalesReturn Lines via EntitySet', { inputs });

          const createdLines = [];
          for (const data of inputs) {
            // Calculate inventory quantity by converting from transactional UoM to base UoM
            let inventoryQuantity: Decimal;
            try {
              inventoryQuantity = await convertToBaseUoM(
                session,
                new Decimal(data.quantity.toString()),
                data.unitId,
                data.itemId
              );
            } catch (error) {
              log.warn(
                `Could not convert UoM for item ${data.itemId}: ${
                  error instanceof Error ? error.message : String(error)
                }. Using transactional quantity as inventory quantity.`
              );
              // Fall back to using the transactional quantity
              inventoryQuantity = new Decimal(data.quantity.toString());
            }

            // Create the sales return line
            const line = await IMInventorySalesReturnLine.create(session, {
              headerId: data.headerId,
              itemId: data.itemId,
              locationId: data.locationId,
              unitId: data.unitId,
              sourceDocumentLineId: data.sourceDocumentLineId,
              sourceDocumentLineType: data.sourceDocumentLineType,
              quantity: data.quantity,
              inventoryQuantity: inventoryQuantity,
              openQuantity: data.openQuantity,
              unitPrice: data.unitPrice,
              status: data.status || 'PENDING',
              active: data.active !== false,
            });

            createdLines.push(line);
          }

          return { instances: createdLines };
        } catch (error) {
          log.error('Error creating SalesReturn Lines via EntitySet', error);
          throw error;
        }
      },
    },
    update: {
      async execute({
        session,
        id,
        data,
      }: SalesReturnService.EntitySets.Lines.Update.Execute.Request): Promise<SalesReturnService.EntitySets.Lines.Update.Execute.Response> {
        try {
          log.debug('Updating SalesReturn Line via EntitySet', { id, data });

          // Calculate inventory quantity by converting from transactional UoM to base UoM if needed
          let inventoryQuantity = data.inventoryQuantity;
          if (data.quantity && data.unitId && data.itemId) {
            try {
              inventoryQuantity = await convertToBaseUoM(
                session,
                new Decimal(data.quantity.toString()),
                data.unitId,
                data.itemId
              );
            } catch (error) {
              log.warn(
                `Could not convert UoM for item ${data.itemId}: ${
                  error instanceof Error ? error.message : String(error)
                }. Using provided inventory quantity or transactional quantity.`
              );
              // Fall back to using the provided inventory quantity or transactional quantity
              inventoryQuantity =
                data.inventoryQuantity || new Decimal(data.quantity.toString());
            }
          }

          // Update the sales return line
          await IMInventorySalesReturnLine.update(
            session,
            { id },
            {
              itemId: data.itemId,
              locationId: data.locationId,
              unitId: data.unitId,
              sourceDocumentLineId: data.sourceDocumentLineId,
              sourceDocumentLineType: data.sourceDocumentLineType,
              quantity: data.quantity,
              inventoryQuantity: inventoryQuantity,
              openQuantity: data.openQuantity,
              unitPrice: data.unitPrice,
              status: data.status,
              active: data.active,
            }
          );

          return;
        } catch (error) {
          log.error('Error updating SalesReturn Line via EntitySet', error);
          throw error;
        }
      },
    },
    delete: {
      async execute({
        session,
        ids,
      }: SalesReturnService.EntitySets.Lines.Delete.Execute.Request): Promise<SalesReturnService.EntitySets.Lines.Delete.Execute.Response> {
        try {
          log.debug('Deleting SalesReturn Lines via EntitySet', { ids });

          // Delete the sales return lines one by one
          for (const id of ids) {
            await IMInventorySalesReturnLine.Delete(session, { id });
          }

          return;
        } catch (error) {
          log.error('Error deleting SalesReturn Lines via EntitySet', error);
          throw error;
        }
      },
    },
  },

  // SalesReturns EntitySet implementation
  SalesReturns: {
    query: {
      async execute({
        session,
        where,
        orderBy,
        skip,
        take,
        count,
        fieldList,
      }: SalesReturnService.EntitySets.SalesReturns.Query.Execute.Request): Promise<SalesReturnService.EntitySets.SalesReturns.Query.Execute.Response> {
        try {
          log.debug('Querying SalesReturns with parameters', {
            where,
            orderBy,
            skip,
            take,
            count,
          });

          const includeLines = Array.from(fieldList).includes('lines');
          const fields = omit(fieldList, 'lines');

          const salesReturns = await IMInventorySalesReturn.query(
            session,
            {
              where: where,
              orderBy,
              skip,
              take,
            },
            Array.from(fields)
          );

          // Enhance the sales returns with additional information including lines
          const enhancedSalesReturns = [];

          for (const salesReturn of salesReturns) {
            let lines = [];

            // If lines are requested, fetch and enhance them
            if (includeLines) {
              const salesReturnLines = await IMInventorySalesReturnLine.query(
                session,
                { where: { headerId: salesReturn.id } },
                [
                  'id',
                  'headerId',
                  'itemId',
                  'locationId',
                  'unitId',
                  'sourceDocumentLineId',
                  'sourceDocumentLineType',
                  'quantity',
                  'inventoryQuantity',
                  'openQuantity',
                  'unitPrice',
                  'reasonCodeId',
                  'status',
                  {
                    ItemMaster: ['itemCode', 'itemName'],
                  },
                  {
                    IMWarehouseLocation: ['name'],
                  },
                  {
                    UnitOfMeasure: ['unitCode'],
                  },
                ]
              );

              // Map lines to the expected format
              lines = salesReturnLines.map((line, index) => ({
                id: line.id,
                headerId: line.headerId,
                lineNumber: index + 1, // Generate line number
                itemId: line.itemId,
                itemCode: line.ItemMaster?.itemCode || '',
                itemName: line.ItemMaster?.itemName || '',
                locationId: line.locationId,
                locationName: line.IMWarehouseLocation?.name || '',
                unitId: line.unitId,
                unitCode: line.UnitOfMeasure?.unitCode || '',
                sourceDocumentLineId: line.sourceDocumentLineId,
                sourceDocumentLineType: line.sourceDocumentLineType,
                quantity: line.quantity,
                inventoryQuantity: line.inventoryQuantity,
                openQuantity: line.openQuantity,
                receivedQuantity: new Decimal(0), // Default values for fields not in type
                inspectedQuantity: new Decimal(0),
                processedQuantity: new Decimal(0),
                unitPrice: line.unitPrice?.amount || new Decimal(0),
                totalPrice: (line.unitPrice?.amount || new Decimal(0)).times(
                  line.quantity || 0
                ),
                unitCost: new Decimal(0), // Not available in current type
                totalCost: new Decimal(0), // Not available in current type
                condition: '', // Not available in current type
                disposition: '', // Not available in current type
                reasonCodeId: line.reasonCodeId || 0,
                status: line.status || '',
                receivedDate: null, // Not available in current type
                inspectedDate: null, // Not available in current type
                processedDate: null, // Not available in current type
                notes: '', // Not available in current type
                inspectionNotes: '', // Not available in current type
              }));
            }

            enhancedSalesReturns.push({
              ...salesReturn,
              createdBy: salesReturn.createdBy || '',
              lines: lines,
            });
          }

          return {
            instances: enhancedSalesReturns,
            count: count ? salesReturns.length : undefined,
          };
        } catch (error) {
          log.error('Error querying SalesReturns ', error);
          throw error;
        }
      },
    },
    create: {
      async execute({
        session,
        inputs,
      }: SalesReturnService.EntitySets.SalesReturns.Create.Execute.Request): Promise<SalesReturnService.EntitySets.SalesReturns.Create.Execute.Response> {
        try {
          // Extract data from the first input
          const data = inputs[0];
          log.debug('Creating SalesReturn via EntitySet', { data });

          // Create the sales return
          const salesReturn = await IMInventorySalesReturn.create(session, {
            businessPartnerId: data.businessPartnerId || 0,
            documentDate:
              data.documentDate || PlainDate.from(new Date().toISOString()),
            documentNumber: data.documentNumber || '',
            entityId: data.entityId,
            notes: data.notes,
            status: data.status,
            active: data.active,
          } satisfies Partial<IMInventorySalesReturn.CreationFields>);

          // Lines are simplified in this implementation

          return { instances: [salesReturn] };
        } catch (error) {
          log.error('Error creating SalesReturn via EntitySet', error);
          throw error;
        }
      },
    },
    update: {
      async execute({
        session,
        id,
        data,
      }: SalesReturnService.EntitySets.SalesReturns.Update.Execute.Request): Promise<SalesReturnService.EntitySets.SalesReturns.Update.Execute.Response> {
        try {
          log.debug('Updating SalesReturn via EntitySet', { id, data });

          // Update the sales return
          await IMInventorySalesReturn.update(
            session,
            { id },
            {
              ...data,
            }
          );

          // Lines are simplified in this implementation

          return;
        } catch (error) {
          log.error('Error updating SalesReturn via EntitySet', error);
          throw error;
        }
      },
    },
    delete: {
      async execute({
        session,
        ids,
      }: SalesReturnService.EntitySets.SalesReturns.Delete.Execute.Request): Promise<SalesReturnService.EntitySets.SalesReturns.Delete.Execute.Response> {
        try {
          log.debug('Deleting SalesReturn via EntitySet', { ids });

          // Get the sales return lines
          const lines = await IMInventorySalesReturnLine.query(
            session,
            { where: { headerId: { $in: ids } } },
            ['id']
          );

          // Delete the sales return lines
          for (const line of lines) {
            await IMInventorySalesReturnLine.Delete(session, { id: line.id });
          }

          // Delete the sales returns one by one
          const deletedSalesReturns = [];
          for (const id of ids) {
            const result = await IMInventorySalesReturn.Delete(session, { id });
            deletedSalesReturns.push(...result);
          }

          // Return void as required by the type definition
          return;
        } catch (error) {
          log.error('Error deleting SalesReturn via EntitySet', error);
          throw error;
        }
      },
    },
  },

  // Custom actions
  CreateSalesReturn: {
    async execute({
      session,
      input,
    }: SalesReturnService.Actions.CreateSalesReturn.Execute.Request): Promise<SalesReturnService.Actions.CreateSalesReturn.Execute.Response> {
      try {
        log.info('Creating SalesReturn via action with lines', { input });
        const { lines, ...headerData } = input;

        // Generate document number if not provided
        let documentNumber = headerData.documentNumber;
        if (!documentNumber || documentNumber.trim() === '') {
          documentNumber =
            await SalesReturnNumberGenerator.generateReturnNumber(
              session as ISession
            );
          log.info('Generated document number for sales return', {
            documentNumber,
          });
        }

        // Derive entityId from warehouse location if not provided
        let entityId = headerData.entityId;
        if (!entityId && lines && lines.length > 0) {
          // Get the entityId from the first line's locationId
          const firstLineLocationId = lines[0].locationId;
          if (firstLineLocationId) {
            const warehouseLocation = await IMWarehouseLocation.read(
              session,
              { id: firstLineLocationId },
              ['entityId']
            );
            if (warehouseLocation) {
              entityId = warehouseLocation.entityId;
              log.info('Derived entityId from warehouse location', {
                locationId: firstLineLocationId,
                entityId,
              });
            }
          }
        }

        // Create the sales return header with status DRAFT
        const salesReturn = await IMInventorySalesReturn.create(session, {
          ...headerData,
          documentNumber,
          entityId,
          status: 'DRAFT',
          active: true,
        });

        const warnings: string[] = [];
        const errors: string[] = [];

        // Create sales return lines if provided
        if (lines && Array.isArray(lines) && lines.length > 0) {
          log.debug('Creating sales return lines', {
            salesReturnId: salesReturn.id,
            lineCount: lines.length,
          });

          for (const [index, lineData] of lines.entries()) {
            // Calculate inventory quantity by converting from transactional UoM to base UoM
            let inventoryQuantity: Decimal;
            try {
              inventoryQuantity = await convertToBaseUoM(
                session,
                new Decimal(lineData.quantity.toString()),
                lineData.unitId,
                lineData.itemId
              );
            } catch (error) {
              log.warn(
                `Could not convert UoM for item ${lineData.itemId}: ${
                  error instanceof Error ? error.message : String(error)
                }. Using transactional quantity as inventory quantity.`
              );
              // Fall back to using the transactional quantity
              inventoryQuantity = new Decimal(lineData.quantity.toString());
            }

            // Create the sales return line
            await IMInventorySalesReturnLine.create(session, {
              headerId: salesReturn.id,
              itemId: lineData.itemId,
              locationId: lineData.locationId,
              unitId: lineData.unitId,
              sourceDocumentLineId: lineData.sourceDocumentLineId,
              sourceDocumentLineType: lineData.sourceDocumentLineType,
              quantity: lineData.quantity,
              inventoryQuantity: inventoryQuantity,
              openQuantity: lineData.quantity, // Initially open quantity equals quantity
              unitPrice: lineData.unitPrice
                ? {
                    amount: new Decimal(lineData.unitPrice.toString()),
                    currency: 'USD', // TODO: Get from sales return or company settings
                  }
                : {
                    amount: new Decimal(0),
                    currency: 'USD',
                  },
              reasonCodeId: lineData.reasonCodeId,
              status: 'DRAFT',
              active: true,
            });

            log.debug('Created sales return line', {
              salesReturnId: salesReturn.id,
              lineNumber: lineData.lineNumber || index + 1,
              itemId: lineData.itemId,
            });
          }
        } else {
          log.debug('No lines provided for sales return', {
            salesReturnId: salesReturn.id,
          });
        }

        // If there were errors creating lines, include them in the response
        if (errors.length > 0) {
          warnings.push(`${errors.length} line(s) failed to create`);
        }

        return {
          output: {
            success: true,
            salesReturnId: salesReturn.id,
            documentNumber: salesReturn.documentNumber || '',
            warnings: warnings,
            errors: errors,
          },
        };
      } catch (error) {
        log.error('Error creating SalesReturn via action', error);
        throw error;
      }
    },
  },

  UpdateSalesReturn: {
    async execute({
      session,
      input,
    }: SalesReturnService.Actions.UpdateSalesReturn.Execute.Request): Promise<SalesReturnService.Actions.UpdateSalesReturn.Execute.Response> {
      try {
        const { id, lines, linesToRemove, ...headerData } = input;
        log.debug('Updating SalesReturn via action', {
          id,
          headerData,
          lines,
          linesToRemove,
        });

        // Use our comprehensive SalesReturnUpdateService

        // Transform the input to match our service's DTO structure
        const updateData = {
          ...headerData,
          lines: lines?.map(
            (line) =>
              ({
                lineId: line.lineId || line.id, // Use lineId if available, otherwise map 'id' to 'lineId' for our service
                itemId: line.itemId,
                locationId: line.locationId,
                unitId: line.unitId,
                returnQuantity: line.quantity, // Map to DTO field returnQuantity
                unitCost: {
                  amount: line.unitPrice,
                  currency: 'USD', // TODO: Get from sales return or company settings
                }, // Map to DTO field unitCost
                returnReasonCodeId: line.reasonCodeId, // Map to DTO field returnReasonCodeId
                originalSalesOrderLineId: line.sourceDocumentLineId, // Map to DTO field originalSalesOrderLineId
              }) satisfies SalesReturnLineUpdateDto
          ),
          linesToRemove: linesToRemove,
        } satisfies SalesReturnUpdateDto;

        // Call our comprehensive update service
        const result = await SalesReturnUpdateService.updateSalesReturn(
          session,
          id,
          updateData
        );

        return {
          output: {
            success: true,
            salesReturnId: result.salesReturn.id,
            documentNumber: result.salesReturn.returnNumber || '',
            errorMessage: '',
          },
        };
      } catch (error) {
        log.error('Error updating SalesReturn via action', error);
        throw error;
      }
    },
  },

  ApproveSalesReturn: {
    async execute({
      session,
      input: { salesReturnId, approvalNotes },
    }): Promise<SalesReturnService.Actions.ApproveSalesReturn.Execute.Response> {
      try {
        log.info('Approving sales return with automatic receipt processing', {
          salesReturnId,
          approvalNotes,
        });

        // Step 1: Update the sales return status to APPROVED
        await IMInventorySalesReturn.update(
          session,
          { id: salesReturnId },
          {
            status: 'APPROVED',
            notes: approvalNotes,
          }
        );

        // Step 2: Update all lines to APPROVED status
        const lines = await IMInventorySalesReturnLine.query(
          session,
          { where: { headerId: salesReturnId } },
          ['id', 'quantity']
        );

        for (const line of lines) {
          await IMInventorySalesReturnLine.update(
            session,
            { id: line.id },
            { status: 'APPROVED' }
          );
        }

        // Step 3: Automatically receive the returned goods to trigger inventory transaction
        // Create inventory transactions for returned goods (inbound)
        const inventoryTransactions: InventoryTransactionInput[] = [];
        const inventoryTransactionIds: number[] = [];

        for (const line of lines) {
          // Get tracking details for this line
          const batchDetails = await IMInventorySalesReturnBatch.query(
            session,
            { where: { documentLineId: line.id } },
            ['batchNumberId', 'quantity', 'inventoryQuantity']
          );

          const serialDetails = await IMInventorySalesReturnSerial.query(
            session,
            { where: { documentLineId: line.id } },
            ['serialNumberId', 'openQuantity']
          );

          const binLocationDetails =
            await IMInventorySalesReturnBinLocation.query(
              session,
              { where: { documentLineId: line.id } },
              ['binLocationId', 'quantity', 'inventoryQuantity']
            );

          // Get full line details for inventory transaction
          const fullLine = await IMInventorySalesReturnLine.read(
            session,
            { id: line.id },
            ['itemId', 'locationId', 'unitId', 'inventoryQuantity', 'unitPrice']
          );

          if (!fullLine) {
            continue;
          }

          // Build transaction input for inbound inventory (returned goods)
          const transactionInput: InventoryTransactionInput = {
            itemId: fullLine.itemId,
            locationId: fullLine.locationId,
            transactionDate: PlainDate.from(new Date().toISOString()),
            sourceTransId: salesReturnId,
            sourceTransLineId: line.id,
            sourceTransLineType: 'SalesReturnLine',
            inQuantity: fullLine.inventoryQuantity || line.quantity, // Use inventory quantity for base UOM
            unitId: fullLine.unitId,
            cost: fullLine.unitPrice
              ? {
                  amount: new Decimal(fullLine.unitPrice?.amount.toString()),
                  currency: 'USD', // TODO: Get from sales return or company settings
                }
              : undefined,
            isReturn: true,
          };

          // Add batch tracking details if present
          if (batchDetails.length > 0) {
            transactionInput.batchNumbers = batchDetails.map((batch) => ({
              batchNumberId: batch.batchNumberId,
              batchNumber: `Batch-${batch.batchNumberId}`, // TODO: Resolve actual batch number
              quantity: batch.inventoryQuantity || batch.quantity,
            }));
          }

          // Add serial tracking details if present
          if (serialDetails.length > 0) {
            transactionInput.serialNumbers = serialDetails.map((serial) => ({
              serialNumberId: serial.serialNumberId,
              serialNumber: `Serial-${serial.serialNumberId}`, // TODO: Resolve actual serial number
              quantity: new Decimal('1'), // Serial numbers are always quantity 1
            }));
          }

          // Add bin location details if present
          if (binLocationDetails.length > 0) {
            transactionInput.binLocations = binLocationDetails.map((bin) => ({
              binLocationId: bin.binLocationId,
              binCode: `Bin-${bin.binLocationId}`, // TODO: Resolve actual bin code
              quantity: bin.inventoryQuantity || bin.quantity,
            }));
          }

          inventoryTransactions.push(transactionInput);
        }

        // Create inventory transactions
        if (inventoryTransactions.length > 0) {
          const transactionResults = await createInventoryTransactions(
            session,
            inventoryTransactions,
            {
              allowNegativeInventory: false,
              skipAvailabilityCheck: true, // Skip for returns since they are inbound
              skipValidation: false,
              dryRun: false,
            }
          );

          inventoryTransactionIds.push(
            ...transactionResults.map((result) => result.transactionId)
          );
        }

        // Update status to RECEIVED after creating inventory transactions
        await IMInventorySalesReturn.update(
          session,
          { id: salesReturnId },
          {
            status: 'RECEIVED',
            notes: `${approvalNotes} - Automatically received upon approval`,
          }
        );

        // Update all lines to RECEIVED status
        for (const line of lines) {
          await IMInventorySalesReturnLine.update(
            session,
            { id: line.id },
            { status: 'RECEIVED' }
          );
        }

        // Update source delivery line returned quantities
        await updateSourceSalesDeliveryLineReturnedQuantities(
          session,
          salesReturnId,
          lines
        );

        log.info(
          'Sales return approved and automatically received with inventory transactions',
          {
            salesReturnId,
            inventoryTransactionCount: inventoryTransactionIds.length,
          }
        );

        return {
          output: {
            success: true,
            updatedStatus: 'RECEIVED', // Final status after auto-receipt
            inventoryTransactionIds,
            warnings: [],
            id: salesReturnId,
          },
        };
      } catch (error) {
        log.error('Error approving sales return', error);
        throw error;
      }
    },
  },

  ReceiveReturnedGoods: {
    async execute({
      session,
      input: { salesReturnId, receivedDate, notes, lines },
    }): Promise<SalesReturnService.Actions.ReceiveReturnedGoods.Execute.Response> {
      try {
        log.debug('Receiving returned goods with inventory transaction', {
          salesReturnId,
          receivedDate,
          notes,
          lines,
        });

        // Get the sales return header for validation
        const salesReturn = await IMInventorySalesReturn.read(
          session,
          { id: salesReturnId },
          ['id', 'status', 'documentNumber']
        );

        if (!salesReturn) {
          throw new Error(`Sales return with ID ${salesReturnId} not found`);
        }

        // Get all lines with detailed information for inventory transactions
        const returnLines = await IMInventorySalesReturnLine.query(
          session,
          { where: { headerId: salesReturnId } },
          [
            'id',
            'itemId',
            'locationId',
            'unitId',
            'quantity',
            'inventoryQuantity',
            'openQuantity',
            'unitPrice',
            'sourceDocumentLineId',
            'sourceDocumentLineType',
          ]
        );

        if (returnLines.length === 0) {
          throw new Error('Sales return has no lines to process');
        }

        // Create inventory transactions for returned goods (inbound)
        const inventoryTransactions: InventoryTransactionInput[] = [];
        const inventoryTransactionIds: number[] = [];

        for (const line of returnLines) {
          // Find the corresponding input line to get received quantity
          const inputLine = lines.find((l) => l.lineId === line.id);
          const receivedQuantity = inputLine?.receivedQuantity || line.quantity;

          // Get tracking details for this line
          const batchDetails = await IMInventorySalesReturnBatch.query(
            session,
            { where: { documentLineId: line.id } },
            ['batchNumberId', 'quantity', 'inventoryQuantity']
          );

          const serialDetails = await IMInventorySalesReturnSerial.query(
            session,
            { where: { documentLineId: line.id } },
            ['serialNumberId', 'openQuantity']
          );

          const binLocationDetails =
            await IMInventorySalesReturnBinLocation.query(
              session,
              { where: { documentLineId: line.id } },
              ['binLocationId', 'quantity', 'inventoryQuantity']
            );

          // Build transaction input for inbound inventory (returned goods)
          const transactionInput: InventoryTransactionInput = {
            itemId: line.itemId,
            locationId: line.locationId,
            transactionDate: receivedDate
              ? PlainDate.from(receivedDate.toISOString())
              : PlainDate.from(new Date().toISOString()),
            sourceTransId: salesReturnId,
            sourceTransLineId: line.id,
            sourceTransLineType: 'SalesReturnLine',
            inQuantity: line.inventoryQuantity || receivedQuantity, // Use inventory quantity for base UOM
            unitId: line.unitId,
            cost: line.unitPrice
              ? {
                  amount: new Decimal(line.unitPrice.toString()),
                  currency: 'USD', // TODO: Get from sales return or company settings
                }
              : undefined,
            isReturn: true,
          };

          // Add batch tracking details if present
          if (batchDetails.length > 0) {
            transactionInput.batchNumbers = batchDetails.map((batch) => ({
              batchNumberId: batch.batchNumberId,
              batchNumber: `Batch-${batch.batchNumberId}`, // TODO: Resolve actual batch number
              quantity: batch.inventoryQuantity || batch.quantity,
            }));
          }

          // Add serial tracking details if present
          if (serialDetails.length > 0) {
            transactionInput.serialNumbers = serialDetails.map((serial) => ({
              serialNumberId: serial.serialNumberId,
              serialNumber: `Serial-${serial.serialNumberId}`, // TODO: Resolve actual serial number
              quantity: new Decimal('1'), // Serial numbers are always quantity 1
            }));
          }

          // Add bin location details if present
          if (binLocationDetails.length > 0) {
            transactionInput.binLocations = binLocationDetails.map((bin) => ({
              binLocationId: bin.binLocationId,
              binCode: `Bin-${bin.binLocationId}`, // TODO: Resolve actual bin code
              quantity: bin.inventoryQuantity || bin.quantity,
            }));
          }

          inventoryTransactions.push(transactionInput);
        }

        // Create inventory transactions
        if (inventoryTransactions.length > 0) {
          const transactionResults = await createInventoryTransactions(
            session,
            inventoryTransactions,
            {
              allowNegativeInventory: false,
              skipAvailabilityCheck: true, // Skip for returns since they are inbound
              skipValidation: false,
              dryRun: false,
            }
          );

          inventoryTransactionIds.push(
            ...transactionResults.map((result) => result.transactionId)
          );
        }

        // Update the sales return header
        await IMInventorySalesReturn.update(
          session,
          { id: salesReturnId },
          {
            status: 'RECEIVED',
            notes: notes,
          }
        );

        // Update line status
        for (const lineInput of lines) {
          await IMInventorySalesReturnLine.update(
            session,
            { id: lineInput.lineId },
            {
              status: 'RECEIVED',
            }
          );
        }

        log.info(
          'Sales return goods received successfully with inventory transactions',
          {
            salesReturnId,
            returnNumber: salesReturn.documentNumber,
            inventoryTransactionCount: inventoryTransactionIds.length,
          }
        );

        return {
          output: {
            success: true,
            updatedStatus: 'RECEIVED',
            inventoryTransactionIds,
            warnings: [],
          },
        };
      } catch (error) {
        log.error('Error receiving returned goods', error);
        throw error;
      }
    },
  },

  InspectReturnedGoods: {
    async execute({
      session,
      input: { salesReturnId, inspectedDate, inspectorId, notes, lines },
    }): Promise<SalesReturnService.Actions.InspectReturnedGoods.Execute.Response> {
      try {
        log.debug(
          'Inspecting returned goods with multiple inspection details',
          {
            salesReturnId,
            inspectedDate,
            notes,
            lines,
          }
        );

        // Calculate totals for inspection results
        let totalInspected = new Decimal(0);
        let totalApproved = new Decimal(0);
        let totalRejected = new Decimal(0);
        let _detailsCreated = 0;

        // Update line status and inspection details
        for (const lineInput of lines) {
          const inspectedQty = new Decimal(lineInput.inspectedQuantity || 0);
          const approvedQty = new Decimal(lineInput.approvedQuantity || 0);
          const rejectedQty = new Decimal(lineInput.rejectedQuantity || 0);

          totalInspected = totalInspected.plus(inspectedQty);
          totalApproved = totalApproved.plus(approvedQty);
          totalRejected = totalRejected.plus(rejectedQty);

          // Handle multiple inspection details if provided
          interface InspectionDetail {
            sequenceNumber: number;
            inspectedQuantity: Decimal;
            approvalStatus: string; // 'APPROVED' | 'REJECTED'
            condition: string;
            disposition: string;
            qualityGrade: string;
            inspectionNotes: string;
            rejectionReason: string;
          }

          const inspectionDetails = (
            lineInput as typeof lineInput & {
              inspectionDetails?: InspectionDetail[];
            }
          ).inspectionDetails;

          if (inspectionDetails && inspectionDetails.length > 0) {
            // Process multiple inspection details for this line
            let lineInspectedTotal = new Decimal(0);
            let lineApprovedTotal = new Decimal(0);
            let lineRejectedTotal = new Decimal(0);

            // Calculate totals from details
            for (const detail of inspectionDetails) {
              const detailQty = new Decimal(detail.inspectedQuantity || 0);
              lineInspectedTotal = lineInspectedTotal.plus(detailQty);

              // Use explicit approval status instead of inferring from disposition
              if (detail.approvalStatus === 'APPROVED') {
                lineApprovedTotal = lineApprovedTotal.plus(detailQty);
              } else if (detail.approvalStatus === 'REJECTED') {
                lineRejectedTotal = lineRejectedTotal.plus(detailQty);
              }

              _detailsCreated++;
            }

            // Update line with aggregated data from details
            await IMInventorySalesReturnLine.update(
              session,
              { id: lineInput.lineId },
              {
                status: 'INSPECTED',
                inspectedQuantity: lineInspectedTotal,
                approvedQuantity: lineApprovedTotal,
                rejectedQuantity: lineRejectedTotal,
                condition: 'MIXED', // Mixed condition when multiple details
                disposition: 'MIXED', // Mixed disposition when multiple details
                qualityGrade: lineRejectedTotal.greaterThan(0)
                  ? 'CONDITIONAL_PASS'
                  : 'PASS',
                inspectionNotes: `Multiple inspection details: ${inspectionDetails.length} entries`,
                rejectionReason: lineRejectedTotal.greaterThan(0)
                  ? 'See inspection details'
                  : '',
                inspectedDate: inspectedDate
                  ? typeof inspectedDate === 'string'
                    ? PlainDate.from(inspectedDate)
                    : PlainDate.from(inspectedDate.toISOString())
                  : PlainDate.from(new Date().toISOString()),
                inspectorId: inspectorId || null,
              }
            );

            // Create IMInventorySalesReturnInspectionDetail records
            const createdDetails: Array<{ id: number }> = [];
            for (const detail of inspectionDetails) {
              try {
                const inspectionDetail =
                  await IMInventorySalesReturnInspectionDetail.create(
                    session,
                    {
                      documentLineId: lineInput.lineId,
                      inspectedQuantity: new Decimal(
                        detail.inspectedQuantity || 0
                      ),
                      approvalStatus: detail.approvalStatus || 'PENDING',
                      condition: detail.condition || 'UNKNOWN',
                      disposition: detail.disposition || 'PENDING',
                      qualityGrade: detail.qualityGrade || 'PENDING',
                      inspectionNotes: detail.inspectionNotes || '',
                      rejectionReason: detail.rejectionReason || '',
                    },
                    [
                      'id',
                      'documentLineId',
                      'inspectedQuantity',
                      'approvalStatus',
                    ]
                  );
                createdDetails.push(inspectionDetail);
                log.info(
                  `Created inspection detail ${inspectionDetail.id} for line ${lineInput.lineId}`
                );
              } catch (error) {
                log.error(
                  `Failed to create inspection detail for line ${lineInput.lineId}:`,
                  error
                );
                // Continue with other details even if one fails
              }
            }

            log.info(
              `Created ${createdDetails.length} inspection detail records for line ${lineInput.lineId}`
            );
          } else {
            // Single inspection detail (legacy behavior)
            await IMInventorySalesReturnLine.update(
              session,
              { id: lineInput.lineId },
              {
                status: 'INSPECTED',
                inspectedQuantity: inspectedQty,
                approvedQuantity: approvedQty,
                rejectedQuantity: rejectedQty,
                condition: lineInput.condition || 'GOOD',
                disposition: lineInput.disposition || 'RESTOCK',
                qualityGrade: lineInput.qualityGrade || 'A',
                inspectionNotes: lineInput.inspectionNotes || '',
                rejectionReason: lineInput.rejectionReason || '',
                inspectedDate: inspectedDate
                  ? typeof inspectedDate === 'string'
                    ? PlainDate.from(inspectedDate)
                    : PlainDate.from(inspectedDate.toISOString())
                  : PlainDate.from(new Date().toISOString()),
                inspectorId: inspectorId || null,
              }
            );
          }
        }

        // Determine overall inspection status
        // After inspection, status should be 'INSPECTED' regardless of approval/rejection decisions
        // The approval/rejection decisions are recorded but don't change the status until explicitly processed
        const overallStatus = 'INSPECTED';

        // Update the sales return header
        await IMInventorySalesReturn.update(
          session,
          { id: salesReturnId },
          {
            status: overallStatus,
            notes: notes,
          }
        );

        // Determine overall quality grade
        const overallGrade = totalRejected.greaterThan(0)
          ? 'CONDITIONAL_PASS'
          : totalApproved.equals(totalInspected)
            ? 'PASS'
            : 'PARTIAL';

        return {
          output: {
            success: true,
            updatedStatus: overallStatus,
            inspectionResults: {
              totalInspected,
              totalApproved,
              totalRejected,
              overallGrade,
            },
            warnings: totalRejected.greaterThan(0)
              ? [
                  `${totalRejected.toString()} units were rejected during inspection`,
                ]
              : [],
          },
        };
      } catch (error) {
        log.error('Error inspecting returned goods', error);
        throw error;
      }
    },
  },

  CreatePutAwayTasksFromInspection: {
    async execute({
      session,
      input: {
        salesReturnId,
        inspectionWarehouseId: _inspectionWarehouseId,
        createPutAwayTasks,
        assignedUserId: _assignedUserId,
        priority: _priority,
        strategy: _strategy,
        scheduledDate: _scheduledDate,
        dueDate: _dueDate,
        notes: _notes,
      },
    }): Promise<SalesReturnService.Actions.CreatePutAwayTasksFromInspection.Execute.Response> {
      try {
        log.info(
          `Creating putaway tasks from inspection for sales return ${salesReturnId}`
        );

        // Get the sales return with inspection data
        const salesReturn = await IMInventorySalesReturn.read(
          session,
          { id: salesReturnId },
          ['id', 'documentNumber', 'status']
        );

        if (!salesReturn) {
          throw new Error(`Sales return with ID ${salesReturnId} not found`);
        }

        // Verify the sales return has been inspected
        if (salesReturn.status !== 'INSPECTED') {
          throw new Error(
            `Sales return ${salesReturn.documentNumber} must be inspected before creating putaway tasks`
          );
        }

        // Get inspected lines with disposition data
        const inspectedLines = await IMInventorySalesReturnLine.query(
          session,
          { where: { headerId: salesReturnId } },
          [
            'id',
            'itemId',
            'locationId',
            'unitId',
            'approvedQuantity',
            'rejectedQuantity',
            'condition',
            'disposition',
            'qualityGrade',
            'inspectionNotes',
          ]
        );

        if (inspectedLines.length === 0) {
          throw new Error(
            `No inspected lines found for sales return ${salesReturn.documentNumber}`
          );
        }

        const putAwayTaskIds: number[] = [];
        let tasksCreated = 0;

        if (createPutAwayTasks) {
          // TODO: Implement putaway task creation
          // This requires proper integration with the putaway service
          log.info('Putaway task creation requested but not yet implemented');

          for (const line of inspectedLines) {
            // Only create putaway tasks for items that need to be moved
            if (
              line.approvedQuantity &&
              line.approvedQuantity.greaterThan(0) &&
              line.disposition
            ) {
              let toLocationId: number | undefined;

              // Determine destination based on disposition
              switch (line.disposition) {
                case 'RESTOCK': {
                  // Move to original location or main warehouse
                  toLocationId = line.locationId;
                  break;
                }
                case 'SCRAP': {
                  // Move to scrap location (would need to be configured)
                  toLocationId = 999; // Placeholder scrap location ID
                  break;
                }
                case 'REPAIR': {
                  // Move to repair location
                  toLocationId = 998; // Placeholder repair location ID
                  break;
                }
                case 'RETURN_TO_VENDOR': {
                  // Move to vendor return staging area
                  toLocationId = 997; // Placeholder vendor return location ID
                  break;
                }
                default: {
                  log.warn(
                    `Unknown disposition ${line.disposition} for line ${line.id}, skipping putaway task creation`
                  );
                  continue;
                }
              }

              if (toLocationId) {
                // TODO: Create putaway task here
                log.info(
                  `Would create putaway task for line ${line.id} with disposition ${line.disposition} to location ${toLocationId}`
                );
                tasksCreated++;
              }
            }
          }
        }

        return {
          output: {
            success: true,
            putAwayTasksCreated: tasksCreated,
            putAwayTaskIds,
            message: `Successfully created ${tasksCreated} putaway tasks for sales return ${salesReturn.documentNumber}`,
            warnings: [],
          },
        };
      } catch (error) {
        const errorMessage = `Error creating putaway tasks from inspection: ${
          error instanceof Error ? error.message : String(error)
        }`;
        log.error(errorMessage, error);
        throw error;
      }
    },
  },

  ProcessReturnDisposition: {
    async execute({
      session,
      input: { salesReturnId, processedDate, notes },
    }): Promise<SalesReturnService.Actions.ProcessReturnDisposition.Execute.Response> {
      try {
        log.debug('Processing return disposition (status update only)', {
          salesReturnId,
          processedDate,
          notes,
        });

        // Get the sales return header
        const salesReturn = await IMInventorySalesReturn.read(
          session,
          { id: salesReturnId },
          ['id', 'status', 'documentNumber']
        );

        if (!salesReturn) {
          throw new Error(`Sales return with ID ${salesReturnId} not found`);
        }

        // Validate current status allows processing
        if (salesReturn.status !== 'INSPECTED') {
          throw new Error(
            `Cannot process return in status: ${salesReturn.status}`
          );
        }

        // Get all lines for status update
        const lines = await IMInventorySalesReturnLine.query(
          session,
          { where: { headerId: salesReturnId } },
          ['id']
        );

        if (lines.length === 0) {
          throw new Error('Sales return has no lines to process');
        }

        // NOTE: Inventory transactions were already created during approval
        // This action only updates status to indicate processing is complete

        // Update the sales return header
        await IMInventorySalesReturn.update(
          session,
          { id: salesReturnId },
          {
            status: 'PROCESSED',
            notes: notes,
          }
        );

        // Update all lines to PROCESSED status
        for (const line of lines) {
          await IMInventorySalesReturnLine.update(
            session,
            { id: line.id },
            {
              status: 'PROCESSED',
            }
          );
        }

        log.info(
          'Sales return disposition processed successfully (status only)',
          {
            salesReturnId,
            returnNumber: salesReturn.documentNumber,
            note: 'Inventory transactions were created during approval',
          }
        );

        return {
          output: {
            success: true,
            updatedStatus: 'PROCESSED',
            inventoryTransactionIds: [], // No new transactions created
            warnings: [
              'Inventory transactions were already created during approval',
            ],
          },
        };
      } catch (error) {
        log.error('Error processing return disposition', error);
        throw error;
      }
    },
  },

  CompleteSalesReturn: {
    async execute({
      session,
      input: { salesReturnId, completionNotes },
    }): Promise<SalesReturnService.Actions.CompleteSalesReturn.Execute.Response> {
      try {
        log.debug('Completing sales return', {
          salesReturnId,
          completionNotes,
        });

        // Update the sales return header
        await IMInventorySalesReturn.update(
          session,
          { id: salesReturnId },
          {
            status: 'COMPLETED',
            notes: completionNotes,
          }
        );

        // Update all lines to COMPLETED status
        const lines = await IMInventorySalesReturnLine.query(
          session,
          { where: { headerId: salesReturnId } },
          ['id']
        );

        for (const line of lines) {
          await IMInventorySalesReturnLine.update(
            session,
            { id: line.id },
            { status: 'COMPLETED' }
          );
        }

        return {
          output: {
            success: true,
            updatedStatus: 'COMPLETED',
            creditMemoNumber: '', // Would be generated by accounting system
            warnings: [],
          },
        };
      } catch (error) {
        log.error('Error completing sales return', error);
        throw error;
      }
    },
  },

  GetSalesReturnWithDetails: {
    async execute({
      session,
      input: { salesReturnId },
    }): Promise<SalesReturnService.Actions.GetSalesReturnWithDetails.Execute.Response> {
      try {
        log.debug('Getting sales return with details', { salesReturnId });

        // Get the sales return
        const salesReturnData = await IMInventorySalesReturn.read(
          session,
          { id: salesReturnId },
          [
            'id',
            'uuid',
            'externalId',
            'active',
            'createdBy',
            'createdDate',
            'lastModifiedBy',
            'lastModifiedDate',
            'businessPartnerId',
            'documentDate',
            'documentNumber',
            'entityId',
            'notes',
            'status',
          ]
        );

        if (!salesReturnData) {
          throw new Error(`Sales return with ID ${salesReturnId} not found`);
        }

        // Get the sales return lines with inspection data
        const lines = await IMInventorySalesReturnLine.query(
          session,
          { where: { headerId: salesReturnId } },
          [
            'id',
            'headerId',
            'itemId',
            'locationId',
            'unitId',
            'sourceDocumentLineId',
            'sourceDocumentLineType',
            'quantity',
            'inventoryQuantity',
            'invoicedQuantity',
            'openQuantity',
            'unitPrice',
            'status',
            // Inspection fields
            'inspectedQuantity',
            'approvedQuantity',
            'rejectedQuantity',
            'condition',
            'disposition',
            'qualityGrade',
            'inspectionNotes',
            'rejectionReason',
            'inspectedDate',
            'inspectorId',
            {
              ItemMaster: ['itemCode', 'itemName'],
            },
            {
              IMWarehouseLocation: ['name'],
            },
            {
              UnitOfMeasure: ['unitCode'],
            },
          ]
        );

        // Create a properly formatted salesReturn object
        const salesReturn = {
          id: salesReturnData.id,
          uuid: salesReturnData.uuid || '',
          externalId: salesReturnData.externalId || '',
          active: salesReturnData.active || false,
          businessPartnerId: salesReturnData.businessPartnerId,
          documentDate: salesReturnData.documentDate,
          documentNumber: salesReturnData.documentNumber || '',
          entityId: salesReturnData.entityId,
          notes: salesReturnData.notes || '',
          status: salesReturnData.status || '',
          createdBy: salesReturnData.createdBy,
          createdDate: salesReturnData.createdDate,
          lastModifiedBy: salesReturnData.lastModifiedBy,
          lastModifiedDate: salesReturnData.lastModifiedDate,
          customerName: '', // Would need to be fetched from customer relationship
        };

        // Process and enhance each line with inspection data
        const enhancedLines = lines.map((line) => ({
          id: line.id,
          lineNumber: 0, // Not available in type
          itemId: line.itemId,
          itemCode: line.ItemMaster?.itemCode || '',
          itemName: line.ItemMaster?.itemName || '',
          locationId: line.locationId,
          locationName: line.IMWarehouseLocation?.name || '',
          unitId: line.unitId,
          unitCode: line.UnitOfMeasure?.unitCode || '',
          sourceDocumentLineId: line.sourceDocumentLineId,
          sourceDocumentLineType: line.sourceDocumentLineType,
          quantity: line.quantity,
          inventoryQuantity: line.inventoryQuantity,
          openQuantity: line.openQuantity,
          receivedQuantity: line.quantity, // Use quantity as received
          inspectedQuantity: line.inspectedQuantity || new Decimal(0),
          processedQuantity: new Decimal(0), // Not available in type
          unitPrice: line.unitPrice
            ? new Decimal(line.unitPrice.toString())
            : new Decimal(0),
          totalPrice:
            line.unitPrice && line.quantity
              ? new Decimal(line.unitPrice.toString()).times(line.quantity)
              : new Decimal(0),
          unitCost: new Decimal(0), // Not available in type
          totalCost: new Decimal(0), // Not available in type
          condition: line.condition || '',
          disposition: line.disposition || '',
          reasonCodeId: 0, // Not available in type
          reasonCodeDescription: '', // Not available in type
          status: line.status || '',
          receivedDate: null, // Not available in type
          inspectedDate: line.inspectedDate || null,
          processedDate: null, // Not available in type
          notes: '', // Not available in type
          inspectionNotes: line.inspectionNotes || '',
          active: false, // Not available in type
        }));

        return {
          output: {
            salesReturn,
            lines: enhancedLines,
          },
        };
      } catch (error) {
        log.error('Error getting sales return with details', error);
        throw error;
      }
    },
  },

  ListSalesReturnsWithFilters: {
    async execute({
      session,
      input: {
        status,
        businessPartnerId,
        dateFrom,
        dateTo,
        documentNumber,
        limit,
        offset,
      },
    }): Promise<SalesReturnService.Actions.ListSalesReturnsWithFilters.Execute.Response> {
      try {
        log.debug('Listing sales returns with filters', {
          status,
          businessPartnerId,
          dateFrom,
          dateTo,
          documentNumber,
          limit,
          offset,
        });

        // Build the where clause
        const where: Record<string, unknown> = {};
        if (status) {
          where.status = status;
        }
        if (businessPartnerId) {
          where.businessPartnerId = businessPartnerId;
        }
        if (documentNumber) {
          where.documentNumber = { $contains: documentNumber };
        }
        if (dateFrom || dateTo) {
          where.documentDate = {};
          if (dateFrom) {
            (where.documentDate as Record<string, unknown>).$gte = dateFrom;
          }
          if (dateTo) {
            (where.documentDate as Record<string, unknown>).$lte = dateTo;
          }
        }

        // Query sales returns
        const salesReturns = await IMInventorySalesReturn.query(
          session,
          {
            where,
            orderBy: [{ field: 'createdDate', ordering: 'desc' }],
            skip: offset,
            take: limit,
          },
          [
            'id',
            'uuid',
            'externalId',
            'active',
            'createdBy',
            'createdDate',
            'lastModifiedBy',
            'lastModifiedDate',
            'businessPartnerId',
            'documentDate',
            'documentNumber',
            'entityId',
            'notes',
            'status',
          ]
        );

        // Get total count
        const totalCount = await IMInventorySalesReturn.query(
          session,
          { where },
          ['id']
        ).then((result) => result.length);

        // Map the sales returns to the expected format
        const mappedSalesReturns = salesReturns.map((salesReturn) => ({
          id: salesReturn.id,
          documentNumber: salesReturn.documentNumber || '',
          status: salesReturn.status || '',
          businessPartnerId: salesReturn.businessPartnerId,
          customerName: '', // Would need to be fetched from customer relationship
          documentDate: salesReturn.documentDate,
          createdDate: salesReturn.createdDate,
          lastModifiedDate: salesReturn.lastModifiedDate,
        }));

        return {
          output: {
            salesReturns: mappedSalesReturns,
            totalCount: totalCount,
          },
        };
      } catch (error) {
        log.error('Error listing sales returns with filters', error);
        throw error;
      }
    },
  },

  // GetCustomerById action implementation
  GetCustomerById: {
    async execute({ session, input: { customerId } }) {
      try {
        log.debug('Getting customer by ID', { customerId });

        // Fetch the customer from fin.accounting
        const customerData = await Customer.read(session, { id: customerId }, [
          'id',
          'customerName',
          'customerNumber',
          'mainContactEmail',
          'mainContactName',
          'billingEmail',
          'country',
          'stateProvince',
          'customerType',
          'status',
          'createdDate',
          'lastModifiedDate',
        ]);

        if (!customerData) {
          throw new Error(`Customer with ID ${customerId} not found`);
        }

        // Map the customer data to the expected format
        const customer = {
          id: customerData.id,
          name: customerData.customerName || '',
          customerNumber: customerData.customerNumber || '',
          email:
            customerData.mainContactEmail || customerData.billingEmail || '',
          phone: '', // Not directly available in Customer model
          address: '', // Address details are in CustomerAddress entity
          city: '',
          stateProvince: customerData.stateProvince || '',
          postalCode: '',
          country: customerData.country || '',
          customerType:
            customerData.customerType ||
            EvstCustomerPartnerResellerType.Default,
          status: customerData.status || EvstCustomerStatus.Active,
          createdDate: customerData.createdDate,
          lastModifiedDate: customerData.lastModifiedDate,
        };

        return {
          output: {
            customer,
          },
        };
      } catch (error) {
        log.error('Error getting customer by ID', error);
        throw error;
      }
    },
  },

  // Customers EntitySet implementation
  Customers: {
    query: {
      async execute({ session, where, orderBy, skip, take, count }) {
        try {
          log.debug('Querying Customers EntitySet with parameters', {
            where,
            orderBy,
            skip,
            take,
            count,
          });

          // Query customers from fin.accounting
          const customers = await Customer.query(
            session,
            {
              where: where,
              orderBy: orderBy || [{ field: 'customerName', ordering: 'asc' }],
              skip,
              take,
            },
            [
              'id',
              'customerName',
              'customerNumber',
              'mainContactEmail',
              'mainContactName',
              'billingEmail',
              'country',
              'stateProvince',
              'customerType',
              'status',
              'createdDate',
              'lastModifiedDate',
            ]
          );

          // Map the customer data to the expected format
          const mappedCustomers = customers.map((customerData) => ({
            id: customerData.id,
            name: customerData.customerName || '',
            customerNumber: customerData.customerNumber || '',
            email:
              customerData.mainContactEmail || customerData.billingEmail || '',
            phone: '', // Not directly available in Customer model
            address: '', // Address details are in CustomerAddress entity
            city: '',
            stateProvince: customerData.stateProvince || '',
            postalCode: '',
            country: customerData.country || '',
            customerType:
              customerData.customerType ||
              EvstCustomerPartnerResellerType.Default,
            status: customerData.status || EvstCustomerStatus.Active,
            createdDate: customerData.createdDate,
            lastModifiedDate: customerData.lastModifiedDate,
          }));

          const response = {
            instances: mappedCustomers,
            count: 0,
          };

          if (count) {
            const queryResult = await Customer.query(session, { where }, [
              'id',
            ]);

            response.count = queryResult.length;
          }

          return response;
        } catch (error) {
          log.error('Error querying Customers EntitySet', error);
          throw error;
        }
      },
    },
  },

  // GetInspectionDetails action implementation
  GetInspectionDetails: {
    async execute({ session, input: { salesReturnLineId } }) {
      try {
        log.debug('Getting inspection details for sales return line', {
          salesReturnLineId,
        });

        // Fetch inspection details for the line
        const inspectionDetails =
          await IMInventorySalesReturnInspectionDetail.query(
            session,
            { where: { documentLineId: salesReturnLineId } },
            [
              'id',
              'documentLineId',
              'inspectedQuantity',
              'approvalStatus',
              'condition',
              'disposition',
              'qualityGrade',
              'inspectionNotes',
              'rejectionReason',
              'createdDate',
              'createdBy',
            ]
          );

        // Map the inspection details to the expected format
        const mappedDetails = inspectionDetails.map((detail) => ({
          id: detail.id,
          documentLineId: detail.documentLineId,
          inspectedQuantity: detail.inspectedQuantity, // Keep as Decimal
          approvalStatus: detail.approvalStatus || '',
          condition: detail.condition || '',
          disposition: detail.disposition || '',
          qualityGrade: detail.qualityGrade || '',
          inspectionNotes: detail.inspectionNotes || '',
          rejectionReason: detail.rejectionReason || '',
          createdDate: detail.createdDate,
          createdBy: detail.createdBy,
        }));

        return {
          output: {
            inspectionDetails: mappedDetails,
            totalDetails: mappedDetails.length,
          },
        };
      } catch (error) {
        log.error('Error getting inspection details', error);
        throw error;
      }
    },
  },

  // GetInspectionHistory action implementation
  GetInspectionHistory: {
    async execute({ session, input: { salesReturnId } }) {
      try {
        log.debug('Getting inspection history for sales return', {
          salesReturnId,
        });

        // Get all inspection records for this sales return
        const inspectionRecords = await IMInventorySalesReturnLine.query(
          session,
          {
            where: {
              headerId: salesReturnId,
              inspectedDate: { $ne: null },
            },
          },
          [
            'id',
            'headerId',
            'itemId',
            {
              ItemMaster: ['itemCode', 'itemName'],
            },
            'inspectedDate',
            'inspectorId',
            'inspectedQuantity',
            'approvedQuantity',
            'rejectedQuantity',
            'condition',
            'disposition',
            'qualityGrade',
            'inspectionNotes',
            'rejectionReason',
          ]
        );

        // Get detailed inspection records for each line
        const inspectionHistory = [];
        for (const record of inspectionRecords) {
          // Get inspection details for this line
          const inspectionDetails =
            await IMInventorySalesReturnInspectionDetail.query(
              session,
              {
                where: { documentLineId: record.id },
                orderBy: [{ field: 'sequenceNumber', ordering: 'asc' }],
              },
              [
                'id',
                'sequenceNumber',
                'inspectedQuantity',
                'approvalStatus',
                'condition',
                'disposition',
                'qualityGrade',
                'inspectionNotes',
                'rejectionReason',
              ]
            );

          inspectionHistory.push({
            id: record.id,
            lineId: record.id,
            itemCode: record.ItemMaster?.itemCode || '',
            itemName: record.ItemMaster?.itemName || '',
            inspectedDate: record.inspectedDate,
            inspectorName: 'Inspector', // TODO: Get from Employee table using inspectorId
            inspectedQuantity: record.inspectedQuantity,
            approvedQuantity: record.approvedQuantity,
            rejectedQuantity: record.rejectedQuantity,
            condition: record.condition || '',
            disposition: record.disposition || '',
            qualityGrade: record.qualityGrade || '',
            inspectionNotes: record.inspectionNotes || '',
            rejectionReason: record.rejectionReason || '',
            inspectionDetails: inspectionDetails.map((detail) => ({
              id: detail.id,
              sequenceNumber: detail.sequenceNumber,
              inspectedQuantity: detail.inspectedQuantity,
              approvalStatus: detail.approvalStatus || '',
              condition: detail.condition || '',
              disposition: detail.disposition || '',
              qualityGrade: detail.qualityGrade || '',
              inspectionNotes: detail.inspectionNotes || '',
              rejectionReason: detail.rejectionReason || '',
            })),
          });
        }

        log.debug('Retrieved inspection history', {
          salesReturnId,
          totalInspections: inspectionHistory.length,
        });

        return {
          output: {
            inspectionHistory,
            totalInspections: inspectionHistory.length,
          },
        };
      } catch (error) {
        log.error('Error getting inspection history', { error, salesReturnId });
        throw error;
      }
    },
  },
} satisfies SalesReturnService.Implementation;

/**
 * Updates source sales delivery line returned quantities for approved sales returns
 */
async function updateSourceSalesDeliveryLineReturnedQuantities(
  session: ISession,
  salesReturnId: number,
  returnLines: Array<{ id: number; quantity: Decimal }>
): Promise<void> {
  try {
    log.info('Updating source sales delivery line returned quantities', {
      salesReturnId,
    });

    // Get detailed line information including source document references
    const detailedLines = await IMInventorySalesReturnLine.query(
      session,
      { where: { headerId: salesReturnId } },
      [
        'id',
        'sourceDocumentLineId',
        'sourceDocumentLineType',
        'quantity',
        'inventoryQuantity',
        'status',
      ]
    );

    if (detailedLines.length === 0) {
      log.info('No sales return lines found to update', { salesReturnId });
      return;
    }

    let updatedLinesCount = 0;
    const errors: string[] = [];

    for (const line of detailedLines) {
      // Only process lines that have source document references and are processed
      if (
        line.sourceDocumentLineId &&
        line.sourceDocumentLineType === 'IMInventorySalesDeliveryLine' &&
        line.status === 'RECEIVED'
      ) {
        try {
          // Use the quantity that was actually returned (inventoryQuantity for base UoM)
          const returnedQuantity =
            line.inventoryQuantity || line.quantity || new Decimal('0');

          if (returnedQuantity.greaterThan('0')) {
            const updateResult = await updateSalesDeliveryLineReturnedQuantity(
              session,
              line.sourceDocumentLineId,
              returnedQuantity,
              true // Update status
            );

            if (updateResult.success) {
              updatedLinesCount++;
              log.info('Updated source sales delivery line returned quantity', {
                returnLineId: line.id,
                sourceDeliveryLineId: line.sourceDocumentLineId,
                returnedQuantity: returnedQuantity.toString(),
              });
            } else {
              const errorMessage = `Failed to update source delivery line ${line.sourceDocumentLineId}: ${updateResult.errorMessage}`;
              errors.push(errorMessage);
              log.warn(errorMessage);
            }
          } else {
            log.info('Skipping line with zero returned quantity', {
              returnLineId: line.id,
              returnedQuantity: returnedQuantity.toString(),
            });
          }
        } catch (error) {
          const errorMessage = `Error updating source delivery line for return line ${
            line.id
          }: ${error instanceof Error ? error.message : String(error)}`;
          errors.push(errorMessage);
          log.error(errorMessage, error);
        }
      } else {
        log.info(
          'Skipping line without source document reference or not processed',
          {
            returnLineId: line.id,
            sourceDocumentLineId: line.sourceDocumentLineId,
            sourceDocumentLineType: line.sourceDocumentLineType,
            status: line.status,
          }
        );
      }
    }

    log.info(
      'Completed updating source sales delivery line returned quantities',
      {
        salesReturnId,
        totalLines: detailedLines.length,
        updatedLines: updatedLinesCount,
        errors: errors.length,
      }
    );

    if (errors.length > 0) {
      log.warn('Some source delivery lines could not be updated', {
        salesReturnId,
        errors,
      });
      // Don't throw an error as this shouldn't fail the entire approval process
      // The sales return can still be approved even if source line updates fail
    }
  } catch (error) {
    log.error('Error updating source sales delivery line returned quantities', {
      error,
      salesReturnId,
    });
    // Don't throw an error as this shouldn't fail the entire approval process
    // Log the error but allow the sales return approval to continue
  }
}
