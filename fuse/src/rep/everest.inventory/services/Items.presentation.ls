package everest.inventory

odata presentation Items {

  data-set Items {
    supported-operations view, add, change,
      remove
    field id (required: false): field<ItemMaster.id>
    field itemCode (required: true): field<ItemMaster.itemCode>
    field itemName: field<ItemMaster.itemName>
    field itemType: field<ItemMaster.itemType>
    field status: field<ItemMaster.status>
    field categoryId (required: true): field<ItemMaster.categoryId>
    field unitGroupId (required: true): field<ItemMaster.unitGroupId>
    field inventoryUoMId: field<ItemMaster.inventoryUoMId>
    field salesUoMId: field<ItemMaster.salesUoMId>
    field purchaseUoMId: field<ItemMaster.purchaseUoMId>
    field valuationMethod: field<ItemMaster.valuationMethod>
    field standardCost: field<ItemMaster.standardCost>
    field isBatch: field<ItemMaster.isBatch>
    field isSerial: field<ItemMaster.isSerial>
    field isPerishable: field<ItemMaster.isPerishable>
    field shelfLifeDays: field<ItemMaster.shelfLifeDays>
    field primarySupplierId: field<ItemMaster.primarySupplierId>
    field primarySupplierSku: field<ItemMaster.primarySupplierSku>
    field primarySupplierLeadTimeDays: field<ItemMaster.primarySupplierLeadTimeDays>
    field notes: field<ItemMaster.notes>
    field customAttributes: field<ItemMaster.customAttributes>
    field parentItemId: field<ItemMaster.parentItemId>
    field variantAttributes: field<ItemMaster.variantAttributes>
    field countryOfOrigin: field<ItemMaster.countryOfOrigin>
    field estimatedCarbonFootprintKg: field<ItemMaster.estimatedCarbonFootprintKg>
    field sustainabilityCertifications: field<ItemMaster.sustainabilityCertifications>
  }

  action ImportItems {
    inputs {
      fileContent: Text
      fileFormat: Text
    }
    outputs {
      jobId: Text
      status: Text
      message: Text
    }
    properties {
      side-effects true
    }
  }

  action ExportItems {
    inputs {
      fileFormat: Text
      filter: Text
    }
    outputs {
      jobId: Text
      status: Text
      downloadUrl: Text
    }
    properties {
      side-effects true
    }
  }

  action CreateItemWithDetails {
    inputs {
      item: object<{
        itemCode: field<ItemMaster.itemCode>
        itemName: field<ItemMaster.itemName>
        itemType: field<ItemMaster.itemType>
        status: field<ItemMaster.status>
        categoryId: field<ItemMaster.categoryId>
        unitGroupId: field<ItemMaster.unitGroupId>
        inventoryUoMId: field<ItemMaster.inventoryUoMId>
        salesUoMId: field<ItemMaster.salesUoMId>
        purchaseUoMId: field<ItemMaster.purchaseUoMId>
        productId: field<ItemMaster.productId>
        valuationMethod: field<ItemMaster.valuationMethod>
        standardCost: field<ItemMaster.standardCost>
        isBatch: field<ItemMaster.isBatch>
        isSerial: field<ItemMaster.isSerial>
        isPerishable: field<ItemMaster.isPerishable>
        shelfLifeDays: field<ItemMaster.shelfLifeDays>
        notes: field<ItemMaster.notes>
        customAttributes: field<ItemMaster.customAttributes>
        parentItemId: field<ItemMaster.parentItemId>
        variantAttributes: field<ItemMaster.variantAttributes>
        countryOfOrigin: field<ItemMaster.countryOfOrigin>
        estimatedCarbonFootprintKg: field<ItemMaster.estimatedCarbonFootprintKg>
        sustainabilityCertifications: field<ItemMaster.sustainabilityCertifications>
      }>
      suppliers: array<object<{
        supplierId: field<ItemSupplier.supplierId>
        supplierSku: field<ItemSupplier.supplierSku>
        isPreferred: field<ItemSupplier.isPreferred>
        lastPurchasePrice: field<ItemSupplier.lastPurchasePrice>
        supplierLeadTimeDays: field<ItemSupplier.supplierLeadTimeDays>
      }>>
      barcodes: array<object<{
        barcodeType: field<ItemBarcode.barcodeType>
        barcodeValue: field<ItemBarcode.barcodeValue>
        uomId: field<ItemBarcode.uomId>
      }>>
    }
    outputs {
      itemId: field<ItemMaster.id>
      supplierIds: array<Number<Int>>
      barcodeIds: array<Number<Int>>
    }
    properties {
      side-effects true
    }
  }

  action ReadItemWithDetails {
    inputs {
      itemId: field<ItemMaster.id>
    }
    outputs {
      item: object<{
        id: field<ItemMaster.id>
        itemCode: field<ItemMaster.itemCode>
        itemName: field<ItemMaster.itemName>
        itemType: field<ItemMaster.itemType>
        status: field<ItemMaster.status>
        categoryId: field<ItemMaster.categoryId>
        unitGroupId: field<ItemMaster.unitGroupId>
        inventoryUoMId: field<ItemMaster.inventoryUoMId>
        salesUoMId: field<ItemMaster.salesUoMId>
        purchaseUoMId: field<ItemMaster.purchaseUoMId>
        valuationMethod: field<ItemMaster.valuationMethod>
        standardCost: field<ItemMaster.standardCost>
        isBatch: field<ItemMaster.isBatch>
        isSerial: field<ItemMaster.isSerial>
        isPerishable: field<ItemMaster.isPerishable>
        shelfLifeDays: field<ItemMaster.shelfLifeDays>
        notes: field<ItemMaster.notes>
        customAttributes: field<ItemMaster.customAttributes>
        parentItemId: field<ItemMaster.parentItemId>
        variantAttributes: field<ItemMaster.variantAttributes>
        countryOfOrigin: field<ItemMaster.countryOfOrigin>
        estimatedCarbonFootprintKg: field<ItemMaster.estimatedCarbonFootprintKg>
        sustainabilityCertifications: field<ItemMaster.sustainabilityCertifications>
        productId: field<ItemMaster.productId>
      }>
      suppliers: array<object<{
        id: field<ItemSupplier.id>
        supplierId: field<ItemSupplier.supplierId>
        supplierSku: field<ItemSupplier.supplierSku>
        isPreferred: field<ItemSupplier.isPreferred>
        lastPurchasePrice: field<ItemSupplier.lastPurchasePrice>
        supplierLeadTimeDays: field<ItemSupplier.supplierLeadTimeDays>
      }>>
      barcodes: array<object<{
        id: field<ItemBarcode.id>
        barcodeType: field<ItemBarcode.barcodeType>
        barcodeValue: field<ItemBarcode.barcodeValue>
        uomId: field<ItemBarcode.uomId>
      }>>
      inventoryLevels: array<object<{
        locationId: field<IMInventoryLevel.locationId>
        locationName: Text
        locationCode: Text
        onHandQuantity: field<IMInventoryLevel.onHandQuantity>
        allocatedQuantity: field<IMInventoryLevel.allocatedQuantity>
        reservedQuantity: field<IMInventoryLevel.reservedQuantity>
        qualityHoldQuantity: field<IMInventoryLevel.qualityHoldQuantity>
        damagedQuantity: field<IMInventoryLevel.damagedQuantity>
        inTransitQuantity: field<IMInventoryLevel.inTransitQuantity>
        availableQuantity: Number<Decimal>
        averageCost: field<IMInventoryLevel.averageCost>
        lastTransactionTimestamp: field<IMInventoryLevel.lastTransactionTimestamp>
      }>>
    }
    properties {
      side-effects false
    }
  }

  action UpdateItemWithDetails {
    inputs {
      item: object<{
        id: field<ItemMaster.id>
        itemCode: field<ItemMaster.itemCode>
        itemName: field<ItemMaster.itemName>
        itemType: field<ItemMaster.itemType>
        status: field<ItemMaster.status>
        categoryId: field<ItemMaster.categoryId>
        unitGroupId: field<ItemMaster.unitGroupId>
        inventoryUoMId: field<ItemMaster.inventoryUoMId>
        salesUoMId: field<ItemMaster.salesUoMId>
        purchaseUoMId: field<ItemMaster.purchaseUoMId>
        valuationMethod: field<ItemMaster.valuationMethod>
        standardCost: field<ItemMaster.standardCost>
        isBatch: field<ItemMaster.isBatch>
        isSerial: field<ItemMaster.isSerial>
        isPerishable: field<ItemMaster.isPerishable>
        shelfLifeDays: field<ItemMaster.shelfLifeDays>
        notes: field<ItemMaster.notes>
        customAttributes: field<ItemMaster.customAttributes>
        parentItemId: field<ItemMaster.parentItemId>
        variantAttributes: field<ItemMaster.variantAttributes>
        countryOfOrigin: field<ItemMaster.countryOfOrigin>
        estimatedCarbonFootprintKg: field<ItemMaster.estimatedCarbonFootprintKg>
        sustainabilityCertifications: field<ItemMaster.sustainabilityCertifications>
        productId: field<ItemMaster.productId>
      }>
      suppliers: array<object<{
        id: field<ItemSupplier.id>
        supplierId: field<ItemSupplier.supplierId>
        supplierSku: field<ItemSupplier.supplierSku>
        isPreferred: field<ItemSupplier.isPreferred>
        lastPurchasePrice: field<ItemSupplier.lastPurchasePrice>
        supplierLeadTimeDays: field<ItemSupplier.supplierLeadTimeDays>
      }>>
      newSuppliers: array<object<{
        supplierId: field<ItemSupplier.supplierId>
        supplierSku: field<ItemSupplier.supplierSku>
        isPreferred: field<ItemSupplier.isPreferred>
        lastPurchasePrice: field<ItemSupplier.lastPurchasePrice>
        supplierLeadTimeDays: field<ItemSupplier.supplierLeadTimeDays>
      }>>
      barcodes: array<object<{
        id: field<ItemBarcode.id>
        barcodeType: field<ItemBarcode.barcodeType>
        barcodeValue: field<ItemBarcode.barcodeValue>
        uomId: field<ItemBarcode.uomId>
      }>>
      newBarcodes: array<object<{
        barcodeType: field<ItemBarcode.barcodeType>
        barcodeValue: field<ItemBarcode.barcodeValue>
        uomId: field<ItemBarcode.uomId>
      }>>
      suppliersToDelete: array<Number<Int>>
      barcodesToDelete: array<Number<Int>>
    }
    outputs {
      success: TrueFalse
      message: Text
    }
    properties {
      side-effects true
    }
  }

  action CreateMultipleItemsWithDetails {
    inputs {
      items: array<object<{
        itemCode: field<ItemMaster.itemCode>
        itemName: field<ItemMaster.itemName>
        itemType: field<ItemMaster.itemType>
        status: field<ItemMaster.status>
        categoryId: field<ItemMaster.categoryId>
        unitGroupId: field<ItemMaster.unitGroupId>
        inventoryUoMId: field<ItemMaster.inventoryUoMId>
        salesUoMId: field<ItemMaster.salesUoMId>
        purchaseUoMId: field<ItemMaster.purchaseUoMId>
        valuationMethod: field<ItemMaster.valuationMethod>
        standardCost: field<ItemMaster.standardCost>
        isBatch: field<ItemMaster.isBatch>
        isSerial: field<ItemMaster.isSerial>
        isPerishable: field<ItemMaster.isPerishable>
        shelfLifeDays: field<ItemMaster.shelfLifeDays>
        notes: field<ItemMaster.notes>
        customAttributes: field<ItemMaster.customAttributes>
        parentItemId: field<ItemMaster.parentItemId>
        variantAttributes: field<ItemMaster.variantAttributes>
        countryOfOrigin: field<ItemMaster.countryOfOrigin>
        estimatedCarbonFootprintKg: field<ItemMaster.estimatedCarbonFootprintKg>
        sustainabilityCertifications: field<ItemMaster.sustainabilityCertifications>
        suppliers: array<object<{
          supplierId: field<ItemSupplier.supplierId>
          supplierSku: field<ItemSupplier.supplierSku>
          isPreferred: field<ItemSupplier.isPreferred>
          lastPurchasePrice: field<ItemSupplier.lastPurchasePrice>
          supplierLeadTimeDays: field<ItemSupplier.supplierLeadTimeDays>
        }>>
        barcodes: array<object<{
          barcodeType: field<ItemBarcode.barcodeType>
          barcodeValue: field<ItemBarcode.barcodeValue>
          uomId: field<ItemBarcode.uomId>
        }>>
      }>>
    }
    outputs {
      results: array<object<{
        itemCode: Text
        success: TrueFalse
        itemId: field<ItemMaster.id>
        supplierIds: array<Number<Int>>
        barcodeIds: array<Number<Int>>
        error: Text
        validationErrors: array<Text>
      }>>
      summary: object<{
        totalItems: Number<Int>
        successCount: Number<Int>
        errorCount: Number<Int>
        validationErrorCount: Number<Int>
      }>
    }
    properties {
      side-effects true
    }
  }
}
