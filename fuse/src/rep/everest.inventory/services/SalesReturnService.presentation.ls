package everest.inventory

odata presentation SalesReturnService {

  /**
   * EntitySet for Customers (reused from SalesOrderService)
   */
  data-set Customers {
    supported-operations view
    field id (editable: false): Id
    field name: Text
    field customerNumber: Text
    field email: Text
    field phone: Text
    field address: Text
    field city: Text
    field stateProvince: Text
    field postalCode: Text
    field country: Text
    field customerType: field<everest.fin.accounting::Customer.customerType>
    field status: field<everest.fin.accounting::Customer.status>
    field createdDate (editable: false): Date
    field lastModifiedDate (editable: false): Date
  }

  /**
   * EntitySet for SalesReturns
   */
  data-set SalesReturns {
    supported-operations view, add, change, remove
    field id (editable: false): field<everest.inventory::IMInventorySalesReturn.id>
    field uuid (editable: false): field<everest.inventory::IMInventorySalesReturn.uuid>
    field externalId: field<everest.inventory::IMInventorySalesReturn.externalId>
    field active: field<everest.inventory::IMInventorySalesReturn.active>
    field businessPartnerId: field<everest.inventory::IMInventorySalesReturn.businessPartnerId>
    field documentDate: field<everest.inventory::IMInventorySalesReturn.documentDate>
    field documentNumber: field<everest.inventory::IMInventorySalesReturn.documentNumber>
    field entityId: field<everest.inventory::IMInventorySalesReturn.entityId>
    field notes: field<everest.inventory::IMInventorySalesReturn.notes>
    field status: field<everest.inventory::IMInventorySalesReturn.status>
    field createdBy (editable: false): field<everest.inventory::IMInventorySalesReturn.createdBy>
    field createdDate (editable: false): field<everest.inventory::IMInventorySalesReturn.createdDate>
    field lastModifiedBy (editable: false): field<everest.inventory::IMInventorySalesReturn.lastModifiedBy>
    field lastModifiedDate (editable: false): field<everest.inventory::IMInventorySalesReturn.lastModifiedDate>
    field lines: array<object<{
      id: field<everest.inventory::IMInventorySalesReturnLine.id>
      lineId (optional:true): field<everest.inventory::IMInventorySalesReturnLine.id>
      headerId: field<everest.inventory::IMInventorySalesReturnLine.headerId>
      lineNumber: Number<Int>
      itemId: field<everest.inventory::IMInventorySalesReturnLine.itemId>
      itemCode: Text
      itemName: Text
      locationId: field<everest.inventory::IMInventorySalesReturnLine.locationId>
      locationName: Text
      unitId: field<everest.inventory::IMInventorySalesReturnLine.unitId>
      unitCode: Text
      sourceDocumentLineId: field<everest.inventory::IMInventorySalesReturnLine.sourceDocumentLineId>
      sourceDocumentLineType: field<everest.inventory::IMInventorySalesReturnLine.sourceDocumentLineType>
      quantity: Number<Decimal>
      inventoryQuantity: Number<Decimal>
      openQuantity: Number<Decimal>
      receivedQuantity: Number<Decimal>
      inspectedQuantity: Number<Decimal>
      processedQuantity: Number<Decimal>
      unitPrice: Number<Decimal>
      totalPrice: Number<Decimal>
      unitCost: Number<Decimal>
      totalCost: Number<Decimal>
      condition: Text
      disposition: Text
      reasonCodeId: field<everest.inventory::IMInventorySalesReturnLine.reasonCodeId>
      status: Text
      receivedDate: PlainDate
      inspectedDate: PlainDate
      processedDate: PlainDate
      notes: Text
      inspectionNotes: Text
    }>>
  }

  /**
   * EntitySet for SalesReturn Lines
   */
  data-set Lines {
    supported-operations view, add, change, remove
    field id (editable: false): field<everest.inventory::IMInventorySalesReturnLine.id>
    field headerId: field<everest.inventory::IMInventorySalesReturnLine.headerId>
    field itemId: field<everest.inventory::IMInventorySalesReturnLine.itemId>
    field locationId: field<everest.inventory::IMInventorySalesReturnLine.locationId>
    field unitId: field<everest.inventory::IMInventorySalesReturnLine.unitId>
    field sourceDocumentLineId: field<everest.inventory::IMInventorySalesReturnLine.sourceDocumentLineId>
    field sourceDocumentLineType: field<everest.inventory::IMInventorySalesReturnLine.sourceDocumentLineType>
    field quantity: field<everest.inventory::IMInventorySalesReturnLine.quantity>
    field inventoryQuantity: field<everest.inventory::IMInventorySalesReturnLine.inventoryQuantity>
    field openQuantity: field<everest.inventory::IMInventorySalesReturnLine.openQuantity>
    field unitPrice: field<everest.inventory::IMInventorySalesReturnLine.unitPrice>
    field status: field<everest.inventory::IMInventorySalesReturnLine.status>
    field active: field<everest.inventory::IMInventorySalesReturnLine.active>
    field createdBy (editable: false): field<everest.inventory::IMInventorySalesReturnLine.createdBy>
    field createdDate (editable: false): field<everest.inventory::IMInventorySalesReturnLine.createdDate>
    field lastModifiedBy (editable: false): field<everest.inventory::IMInventorySalesReturnLine.lastModifiedBy>
    field lastModifiedDate (editable: false): field<everest.inventory::IMInventorySalesReturnLine.lastModifiedDate>
  }

  /**
   * Action to create a sales return authorization
   */
  action CreateSalesReturn {
    inputs {
      businessPartnerId: field<everest.inventory::IMInventorySalesReturn.businessPartnerId>
      documentDate: field<everest.inventory::IMInventorySalesReturn.documentDate>
      documentNumber: field<everest.inventory::IMInventorySalesReturn.documentNumber>
      entityId: field<everest.inventory::IMInventorySalesReturn.entityId>
      notes: field<everest.inventory::IMInventorySalesReturn.notes>
      status: field<everest.inventory::IMInventorySalesReturn.status>
      lines: array<object<{
        lineNumber: Number<Int>
        itemId: field<everest.inventory::IMInventorySalesReturnLine.itemId>
        locationId: field<everest.inventory::IMInventorySalesReturnLine.locationId>
        unitId: field<everest.inventory::IMInventorySalesReturnLine.unitId>
        sourceDocumentLineId: field<everest.inventory::IMInventorySalesReturnLine.sourceDocumentLineId>
        sourceDocumentLineType: field<everest.inventory::IMInventorySalesReturnLine.sourceDocumentLineType>
        quantity: Number<Decimal>
        inventoryQuantity: Number<Decimal>
        unitPrice: Number<Decimal>
        unitCost: Number<Decimal>
        condition: Text
        disposition: Text
        reasonCodeId: field<everest.inventory::IMInventorySalesReturnLine.reasonCodeId>
        notes: Text
      }>>
    }
    outputs {
      success: TrueFalse
      salesReturnId: field<everest.inventory::IMInventorySalesReturn.id>
      documentNumber: Text
      warnings: array<Text>
      errors: array<Text>
    }
    properties {
      side-effects true
    }
  }

  /**
   * Action to update a sales return (draft only)
   */
  action UpdateSalesReturn {
    inputs {
      id: field<everest.inventory::IMInventorySalesReturn.id>
      businessPartnerId: field<everest.inventory::IMInventorySalesReturn.businessPartnerId>
      documentDate: field<everest.inventory::IMInventorySalesReturn.documentDate>
      documentNumber: field<everest.inventory::IMInventorySalesReturn.documentNumber>
      entityId: field<everest.inventory::IMInventorySalesReturn.entityId>
      notes: field<everest.inventory::IMInventorySalesReturn.notes>
      status: field<everest.inventory::IMInventorySalesReturn.status>
      externalId: field<everest.inventory::IMInventorySalesReturn.externalId>
      lines: array<object<{
        id: field<everest.inventory::IMInventorySalesReturnLine.id>
        lineId (optional: true): field<everest.inventory::IMInventorySalesReturnLine.id>
        lineNumber: Number<Int>
        itemId: field<everest.inventory::IMInventorySalesReturnLine.itemId>
        locationId: field<everest.inventory::IMInventorySalesReturnLine.locationId>
        unitId: field<everest.inventory::IMInventorySalesReturnLine.unitId>
        sourceDocumentLineId: field<everest.inventory::IMInventorySalesReturnLine.sourceDocumentLineId>
        sourceDocumentLineType: field<everest.inventory::IMInventorySalesReturnLine.sourceDocumentLineType>
        quantity: Number<Decimal>
        inventoryQuantity: Number<Decimal>
        unitPrice: Number<Decimal>
        condition: Text
        disposition: Text
        reasonCodeId: field<everest.inventory::IMInventorySalesReturnLine.reasonCodeId>
      }>>
      linesToRemove: array<field<everest.inventory::IMInventorySalesReturnLine.id>>
    }
    outputs {
      success: TrueFalse
      salesReturnId: field<everest.inventory::IMInventorySalesReturn.id>
      documentNumber: Text
      errorMessage: Text
    }
    properties {
      side-effects true
    }
  }

  /**
   * Action to approve a sales return
   */
  action ApproveSalesReturn {
    inputs {
      salesReturnId: field<everest.inventory::IMInventorySalesReturn.id>
      approvalNotes: Text
    }
    outputs {
      success: TrueFalse
      updatedStatus: Text
      inventoryTransactionIds: array<Number<Int>>
      warnings: array<Text>
      id: field<everest.inventory::IMInventorySalesReturn.id>
    }
    properties {
      side-effects true
    }
  }

  /**
   * Action to receive returned goods
   */
  action ReceiveReturnedGoods {
    inputs {
      salesReturnId: field<everest.inventory::IMInventorySalesReturn.id>
      receivedDate: Date
      notes: Text
      lines: array<object<{
        lineId: field<everest.inventory::IMInventorySalesReturnLine.id>
        receivedQuantity: Number<Decimal>
        notes: Text
      }>>
    }
    outputs {
      success: TrueFalse
      updatedStatus: Text
      inventoryTransactionIds: array<Number<Int>>
      warnings: array<Text>
    }
    properties {
      side-effects true
    }
  }

  /**
   * Action to inspect returned goods
   */
  action InspectReturnedGoods {
    inputs {
      salesReturnId: field<everest.inventory::IMInventorySalesReturn.id>
      inspectedDate: Date
      inspectorId: Number<Int>
      notes: Text
      lines: array<object<{
        lineId: field<everest.inventory::IMInventorySalesReturnLine.id>
        inspectedQuantity: Number<Decimal>
        approvedQuantity: Number<Decimal>
        rejectedQuantity: Number<Decimal>
        condition: Text
        disposition: Text
        qualityGrade: Text
        inspectionNotes: Text
        rejectionReason: Text
        inspectionDetails: array<object<{
          sequenceNumber: Number<Int>
          inspectedQuantity: Number<Decimal>
          approvalStatus: Text
          condition: Text
          disposition: Text
          qualityGrade: Text
          inspectionNotes: Text
          rejectionReason: Text
        }>>
      }>>
    }
    outputs {
      success: TrueFalse
      updatedStatus: Text
      inspectionResults: object<{
        totalInspected: Number<Decimal>
        totalApproved: Number<Decimal>
        totalRejected: Number<Decimal>
        overallGrade: Text
      }>
      warnings: array<Text>
    }
    properties {
      side-effects true
    }
  }

  action CreatePutAwayTasksFromInspection {
    inputs {
      salesReturnId: field<everest.inventory::IMInventorySalesReturn.id>
      inspectionWarehouseId: field<everest.inventory::IMWarehouseLocation.id>
      createPutAwayTasks: TrueFalse
      assignedUserId: Number<Int>
      priority: Text
      strategy: Text
      scheduledDate: Date
      dueDate: Date
      notes: Text
    }
    outputs {
      success: TrueFalse
      putAwayTasksCreated: Number<Int>
      putAwayTaskIds: array<Number<Int>>
      message: Text
      warnings: array<Text>
    }
    properties {
      side-effects true
    }
  }

  /**
   * Action to process return disposition
   */
  action ProcessReturnDisposition {
    inputs {
      salesReturnId: field<everest.inventory::IMInventorySalesReturn.id>
      processedDate: Date
      notes: Text
    }
    outputs {
      success: TrueFalse
      updatedStatus: Text
      inventoryTransactionIds: array<Number<Int>>
      warnings: array<Text>
    }
    properties {
      side-effects true
    }
  }

  /**
   * Action to complete a sales return
   */
  action CompleteSalesReturn {
    inputs {
      salesReturnId: field<everest.inventory::IMInventorySalesReturn.id>
      completionNotes: Text
    }
    outputs {
      success: TrueFalse
      updatedStatus: Text
      creditMemoNumber: Text
      warnings: array<Text>
    }
    properties {
      side-effects true
    }
  }

  /**
   * Action to get a sales return with all its details
   */
  action GetSalesReturnWithDetails {
    inputs {
      salesReturnId: field<everest.inventory::IMInventorySalesReturn.id>
    }
    outputs {
      salesReturn: object<{
        id: field<everest.inventory::IMInventorySalesReturn.id>
        uuid: field<everest.inventory::IMInventorySalesReturn.uuid>
        externalId: field<everest.inventory::IMInventorySalesReturn.externalId>
        active: field<everest.inventory::IMInventorySalesReturn.active>
        businessPartnerId: field<everest.inventory::IMInventorySalesReturn.businessPartnerId>
        documentDate: field<everest.inventory::IMInventorySalesReturn.documentDate>
        documentNumber: field<everest.inventory::IMInventorySalesReturn.documentNumber>
        entityId: field<everest.inventory::IMInventorySalesReturn.entityId>
        notes: field<everest.inventory::IMInventorySalesReturn.notes>
        status: field<everest.inventory::IMInventorySalesReturn.status>
        createdBy: field<everest.inventory::IMInventorySalesReturn.createdBy>
        createdDate: field<everest.inventory::IMInventorySalesReturn.createdDate>
        lastModifiedBy: field<everest.inventory::IMInventorySalesReturn.lastModifiedBy>
        lastModifiedDate: field<everest.inventory::IMInventorySalesReturn.lastModifiedDate>
        customerName: Text
      }>
      lines: array<object<{
        id: field<everest.inventory::IMInventorySalesReturnLine.id>
        lineNumber: Number<Int>
        itemId: field<everest.inventory::IMInventorySalesReturnLine.itemId>
        itemCode: Text
        itemName: Text
        locationId: field<everest.inventory::IMInventorySalesReturnLine.locationId>
        locationName: Text
        unitId: field<everest.inventory::IMInventorySalesReturnLine.unitId>
        unitCode: Text
        sourceDocumentLineId: field<everest.inventory::IMInventorySalesReturnLine.sourceDocumentLineId>
        sourceDocumentLineType: field<everest.inventory::IMInventorySalesReturnLine.sourceDocumentLineType>
        quantity: Number<Decimal>
        inventoryQuantity: Number<Decimal>
        openQuantity: Number<Decimal>
        receivedQuantity: Number<Decimal>
        inspectedQuantity: Number<Decimal>
        processedQuantity: Number<Decimal>
        unitPrice: Number<Decimal>
        totalPrice: Number<Decimal>
        unitCost: Number<Decimal>
        totalCost: Number<Decimal>
        condition: Text
        disposition: Text
        reasonCodeId: field<everest.inventory::IMInventorySalesReturnLine.reasonCodeId>
        reasonCodeDescription: Text
        status: Text
        receivedDate: PlainDate
        inspectedDate: PlainDate
        processedDate: PlainDate
        notes: Text
        inspectionNotes: Text
        active: TrueFalse
      }>>
    }
    properties {
      side-effects false
    }
  }

  /**
   * Action to list sales returns with filtering and pagination
   */
  action ListSalesReturnsWithFilters {
    inputs {
      status: Text
      businessPartnerId: Number<Int>
      dateFrom: Date
      dateTo: Date
      documentNumber: Text
      limit: Number<Int>
      offset: Number<Int>
    }
    outputs {
      salesReturns: array<object<{
        id: field<everest.inventory::IMInventorySalesReturn.id>
        documentNumber: Text
        status: Text
        businessPartnerId: field<everest.inventory::IMInventorySalesReturn.businessPartnerId>
        customerName: Text
        documentDate: field<everest.inventory::IMInventorySalesReturn.documentDate>
        createdDate: field<everest.inventory::IMInventorySalesReturn.createdDate>
        lastModifiedDate: field<everest.inventory::IMInventorySalesReturn.lastModifiedDate>
      }>>
      totalCount: Number<Int>
    }
    properties {
      side-effects false
    }
  }

  /**
   * Function to get a customer by ID (reused from SalesOrderService)
   */
  action GetCustomerById {
    inputs {
      customerId: Id
    }
    outputs {
      customer: object<{
        id: Id
        name: Text
        customerNumber: Text
        email: Text
        phone: Text
        address: Text
        city: Text
        stateProvince: Text
        postalCode: Text
        country: Text
        customerType: field<everest.fin.accounting::Customer.customerType>
        status: field<everest.fin.accounting::Customer.status>
        createdDate: Date
        lastModifiedDate: Date
      }>
    }
    properties {
      side-effects false
    }
  }

  /**
   * Action to get inspection details for a sales return line
   */
  action GetInspectionDetails {
    inputs {
      salesReturnLineId: field<everest.inventory::IMInventorySalesReturnLine.id>
    }
    outputs {
      inspectionDetails: array<object<{
        id: field<everest.inventory::IMInventorySalesReturnInspectionDetail.id>
        documentLineId: field<everest.inventory::IMInventorySalesReturnInspectionDetail.documentLineId>
        inspectedQuantity: Number<Decimal>
        approvalStatus: Text
        condition: Text
        disposition: Text
        qualityGrade: Text
        inspectionNotes: Text
        rejectionReason: Text
        createdDate: Date
        createdBy: Text
      }>>
      totalDetails: Number<Int>
    }
    properties {
      side-effects false
    }
  }

  /**
   * Action to get inspection history for a sales return
   */
  action GetInspectionHistory {
    inputs {
      salesReturnId: field<everest.inventory::IMInventorySalesReturn.id>
    }
    outputs {
      inspectionHistory: array<object<{
        id: Number<Int>
        lineId: field<everest.inventory::IMInventorySalesReturnLine.id>
        itemCode: Text
        itemName: Text
        inspectedDate: Date
        inspectorName: Text
        inspectedQuantity: Number<Decimal>
        approvedQuantity: Number<Decimal>
        rejectedQuantity: Number<Decimal>
        condition: Text
        disposition: Text
        qualityGrade: Text
        inspectionNotes: Text
        rejectionReason: Text
        inspectionDetails: array<object<{
          id: field<everest.inventory::IMInventorySalesReturnInspectionDetail.id>
          sequenceNumber: Number<Int>
          inspectedQuantity: Number<Decimal>
          approvalStatus: Text
          condition: Text
          disposition: Text
          qualityGrade: Text
          inspectionNotes: Text
          rejectionReason: Text
        }>>
      }>>
      totalInspections: Number<Int>
    }
    properties {
      side-effects false
    }
  }
}