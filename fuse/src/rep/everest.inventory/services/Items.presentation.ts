import { log } from '@everestsystems/content-core';
import { Decimal } from '@everestsystems/decimal';
import caseInsensitiveQuery from '@pkg/everest.inventory/helpers/query/caseInsensitiveQuery';
import { IMInventoryLevel } from '@pkg/everest.inventory/types/IMInventoryLevel';
import { IMWarehouseLocation } from '@pkg/everest.inventory/types/IMWarehouseLocation';
import { ItemBarcode } from '@pkg/everest.inventory/types/ItemBarcode';
import { ItemMaster } from '@pkg/everest.inventory/types/ItemMaster';
import { ItemSupplier } from '@pkg/everest.inventory/types/ItemSupplier';
import type { Items } from '@pkg/everest.inventory/types/presentations/services/Items';

import { enhanceValidationError } from './helpers/error-enhancement';

// Helper function to create a single item with details
const createSingleItemWithDetails = async (
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  session: any,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  itemData: any,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  suppliers?: any[],
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  barcodes?: any[]
) => {
  // Create the item first
  const itemResult = await ItemMaster.create(session, itemData, ['id']);
  const itemId = itemResult.id;

  // Create suppliers if provided
  const supplierIds: number[] = [];
  if (suppliers && suppliers.length > 0) {
    const supplierPromises = suppliers.map((supplier) => {
      return ItemSupplier.create(
        session,
        {
          ...supplier,
          itemId,
        },
        ['id']
      );
    });

    const supplierResults = await Promise.all(supplierPromises);
    for (const result of supplierResults) {
      supplierIds.push(result.id);
    }

    // If there's a preferred supplier, update the item's primarySupplierId
    const preferredSupplier = suppliers.find((s) => s.isPreferred);
    if (preferredSupplier) {
      await ItemMaster.update(
        session,
        { id: itemId },
        {
          primarySupplierId: preferredSupplier.supplierId,
          primarySupplierSku: preferredSupplier.supplierSku,
          primarySupplierLeadTimeDays: preferredSupplier.supplierLeadTimeDays,
        }
      );
    }
  }

  // Create barcodes if provided
  const barcodeIds: number[] = [];
  if (barcodes && barcodes.length > 0) {
    const barcodePromises = barcodes.map((barcode) => {
      return ItemBarcode.create(
        session,
        {
          ...barcode,
          itemId,
        },
        ['id']
      );
    });

    const barcodeResults = await Promise.all(barcodePromises);
    for (const result of barcodeResults) {
      barcodeIds.push(result.id);
    }
  }

  return {
    itemId,
    supplierIds,
    barcodeIds,
  };
};
export default {
  // Items EntitySet implementation
  Items: {
    query: {
      async execute({
        session,
        where,
        orderBy,
        skip,
        take,
        fieldList,
        count,
      }: Items.EntitySets.Items.Query.Execute.Request): Promise<Items.EntitySets.Items.Query.Execute.Response> {
        try {
          const instances = await ItemMaster.query(
            session,
            {
              where: caseInsensitiveQuery(where),
              orderBy,
              skip,
              take,
            },
            fieldList
          );

          const response: Items.EntitySets.Items.Query.Execute.Response = {
            instances,
          };

          if (count) {
            const queryResult = await ItemMaster.query(
              session,
              {
                where,
              },
              ['id']
            );

            response.count = queryResult.length;
          }

          return response;
        } catch (error) {
          log.error('Error querying Items', error);
          throw error;
        }
      },
    },

    create: {
      async execute({
        session,
        inputs,
      }: Items.EntitySets.Items.Create.Execute.Request): Promise<Items.EntitySets.Items.Create.Execute.Response> {
        try {
          const instances = await ItemMaster.createMany(session, inputs);

          return {
            instances,
          };
        } catch (error) {
          log.error('Error creating Items', error);
          throw error;
        }
      },
    },

    update: {
      async execute({
        session,
        id,
        data,
      }: Items.EntitySets.Items.Update.Execute.Request): Promise<Items.EntitySets.Items.Update.Execute.Response> {
        try {
          await ItemMaster.update(
            session,
            {
              id,
            },
            data
          );
        } catch (error) {
          log.error('Error updating Item', error);
          throw error;
        }
      },
    },

    delete: {
      async execute({
        session,
        ids,
      }: Items.EntitySets.Items.Delete.Execute.Request): Promise<Items.EntitySets.Items.Delete.Execute.Response> {
        try {
          await ItemMaster.deleteMany(session, {
            id: {
              $in: ids,
            },
          });
        } catch (error) {
          log.error('Error deleting Items', error);
          throw error;
        }
      },
    },
  },

  // Custom Actions
  ImportItems: {
    async execute({
      session: _session,
      input,
    }: Items.Actions.ImportItems.Execute.Request): Promise<Items.Actions.ImportItems.Execute.Response> {
      try {
        // Implementation for importing items
        // This would typically involve parsing the file content and creating items
        const { fileContent: _fileContent, fileFormat } = input;

        // Generate a job ID for tracking the import process
        const jobId = `import-${Date.now()}`;

        // In a real implementation, this would start an async job
        // For now, we'll just return a mock response
        return {
          output: {
            jobId,
            status: 'STARTED',
            message: `Started import job for ${fileFormat} file`,
          },
        };
      } catch (error) {
        log.error('Error importing items', error);
        throw error;
      }
    },
  },

  ExportItems: {
    async execute({
      session: _session,
      input,
    }: Items.Actions.ExportItems.Execute.Request): Promise<Items.Actions.ExportItems.Execute.Response> {
      try {
        // Implementation for exporting items
        const { fileFormat: _fileFormat, filter: _filter } = input;

        // Generate a job ID for tracking the export process
        const jobId = `export-${Date.now()}`;

        // In a real implementation, this would start an async job
        // For now, we'll just return a mock response
        return {
          output: {
            jobId,
            status: 'STARTED',
            downloadUrl: `/api/inventory/items/export/${jobId}/download`,
          },
        };
      } catch (error) {
        log.error('Error exporting items', error);
        throw error;
      }
    },
  },

  CreateItemWithDetails: {
    async execute({
      session,
      input,
    }: Items.Actions.CreateItemWithDetails.Execute.Request): Promise<Items.Actions.CreateItemWithDetails.Execute.Response> {
      try {
        const result = await createSingleItemWithDetails(
          session,
          input.item,
          input.suppliers,
          input.barcodes
        );

        return {
          output: result,
        };
      } catch (error) {
        log.error('Error creating item with details', error);
        throw error;
      }
    },
  },

  ReadItemWithDetails: {
    async execute({
      session,
      input,
    }: Items.Actions.ReadItemWithDetails.Execute.Request): Promise<Items.Actions.ReadItemWithDetails.Execute.Response> {
      try {
        const { itemId } = input;

        // Get the item details
        const item = await ItemMaster.read(
          session,
          { id: itemId },
          'ALL_FIELDS'
        );

        // Get all suppliers for this item
        const suppliersData = await ItemSupplier.query(
          session,
          {
            where: {
              itemId,
            },
          },
          [
            'id',
            'supplierId',
            'supplierSku',
            'isPreferred',
            'lastPurchasePrice',
            'supplierLeadTimeDays',
          ]
        );

        // Ensure suppliers conform to the expected type with all required fields
        const suppliers = suppliersData.map((supplier) => ({
          id: supplier.id,
          supplierId: supplier.supplierId,
          supplierSku: supplier.supplierSku || '',
          isPreferred: supplier.isPreferred || false,
          lastPurchasePrice: supplier.lastPurchasePrice,
          supplierLeadTimeDays: supplier.supplierLeadTimeDays || 0,
        }));

        // Get all barcodes for this item
        const barcodesData = await ItemBarcode.query(
          session,
          {
            where: {
              itemId,
            },
          },
          ['id', 'barcodeType', 'barcodeValue', 'uomId']
        );

        // Ensure barcodes conform to the expected type with all required fields
        const barcodes = barcodesData.map((barcode) => ({
          id: barcode.id,
          barcodeType: barcode.barcodeType || '',
          barcodeValue: barcode.barcodeValue || '',
          uomId: barcode.uomId,
        }));

        // Get inventory levels for this item across all locations
        const inventoryLevelsData = await IMInventoryLevel.query(
          session,
          {
            where: {
              itemId,
            },
          },
          [
            'locationId',
            'onHandQuantity',
            'allocatedQuantity',
            'reservedQuantity',
            'qualityHoldQuantity',
            'damagedQuantity',
            'inTransitQuantity',
            'averageCost',
            'lastTransactionTimestamp',
          ]
        );

        // Get location details for all locations with inventory
        const locationIds = inventoryLevelsData.map(
          (level) => level.locationId
        );
        const locationsData =
          locationIds.length > 0
            ? await IMWarehouseLocation.query(
                session,
                {
                  where: {
                    id: {
                      $in: locationIds,
                    },
                  },
                },
                ['id', 'name', 'locationCode']
              )
            : [];

        // Create a map of location details for quick lookup
        const locationMap = new Map();
        for (const location of locationsData) {
          locationMap.set(location.id, {
            name: location.name,
            locationCode: location.locationCode,
          });
        }

        // Process inventory levels with location details and calculate available quantity
        const inventoryLevels = inventoryLevelsData.map((level) => {
          const locationInfo = locationMap.get(level.locationId) || {
            name: '',
            locationCode: '',
          };

          // Calculate available quantity (onHand - allocated - reserved - qualityHold - damaged)
          const onHandQty = level.onHandQuantity
            ? new Decimal(level.onHandQuantity.toString())
            : new Decimal('0');
          const allocatedQty = level.allocatedQuantity
            ? new Decimal(level.allocatedQuantity.toString())
            : new Decimal('0');
          const reservedQty = level.reservedQuantity
            ? new Decimal(level.reservedQuantity.toString())
            : new Decimal('0');
          const qualityHoldQty = level.qualityHoldQuantity
            ? new Decimal(level.qualityHoldQuantity.toString())
            : new Decimal('0');
          const damagedQty = level.damagedQuantity
            ? new Decimal(level.damagedQuantity.toString())
            : new Decimal('0');

          const availableQty = onHandQty
            .minus(allocatedQty)
            .minus(reservedQty)
            .minus(qualityHoldQty)
            .minus(damagedQty);

          return {
            locationId: level.locationId,
            locationName: locationInfo.name,
            locationCode: locationInfo.locationCode,
            onHandQuantity: level.onHandQuantity,
            allocatedQuantity: level.allocatedQuantity,
            reservedQuantity: level.reservedQuantity,
            qualityHoldQuantity: level.qualityHoldQuantity,
            damagedQuantity: level.damagedQuantity,
            inTransitQuantity: level.inTransitQuantity,
            availableQuantity: availableQty,
            averageCost: level.averageCost,
            lastTransactionTimestamp: level.lastTransactionTimestamp,
          };
        });

        // Ensure the item object conforms to the expected type with all required fields
        const typedItem = {
          id: item.id,
          itemCode: item.itemCode || '',
          itemName: item.itemName || '',
          itemType: item.itemType || '',
          status: item.status || '',
          categoryId: item.categoryId,
          unitGroupId: item.unitGroupId,
          inventoryUoMId: item.inventoryUoMId,
          salesUoMId: item.salesUoMId,
          purchaseUoMId: item.purchaseUoMId,
          valuationMethod: item.valuationMethod,
          standardCost: item.standardCost,
          isBatch: item.isBatch,
          isSerial: item.isSerial,
          isPerishable: item.isPerishable,
          shelfLifeDays: item.shelfLifeDays,
          notes: item.notes || '',
          customAttributes: item.customAttributes || {},
          parentItemId: item.parentItemId,
          variantAttributes: item.variantAttributes || {},
          countryOfOrigin: item.countryOfOrigin || '',
          estimatedCarbonFootprintKg: item.estimatedCarbonFootprintKg,
          sustainabilityCertifications: item.sustainabilityCertifications || [],
          productId: item.productId,
        };

        return {
          output: {
            item: typedItem,
            suppliers,
            barcodes,
            inventoryLevels,
          },
        };
      } catch (error) {
        log.error('Error reading item with details', error);
        throw error;
      }
    },
  },

  UpdateItemWithDetails: {
    async execute({
      session,
      input,
    }: Items.Actions.UpdateItemWithDetails.Execute.Request): Promise<Items.Actions.UpdateItemWithDetails.Execute.Response> {
      try {
        const {
          item,
          suppliers,
          newSuppliers,
          barcodes,
          newBarcodes,
          suppliersToDelete,
          barcodesToDelete,
        } = input;

        // Update the item
        await ItemMaster.update(session, { id: item.id }, item);

        // Update existing suppliers
        if (suppliers && suppliers.length > 0) {
          const supplierPromises = suppliers.map((supplier) => {
            return ItemSupplier.update(session, { id: supplier.id }, supplier);
          });
          await Promise.all(supplierPromises);
        }

        // Create new suppliers
        if (newSuppliers && newSuppliers.length > 0) {
          const newSupplierPromises = newSuppliers.map((supplier) => {
            return ItemSupplier.create(session, {
              ...supplier,
              itemId: item.id,
            });
          });
          await Promise.all(newSupplierPromises);
        }

        // Update existing barcodes
        if (barcodes && barcodes.length > 0) {
          const barcodePromises = barcodes.map((barcode) => {
            return ItemBarcode.update(session, { id: barcode.id }, barcode);
          });
          await Promise.all(barcodePromises);
        }

        // Create new barcodes
        if (newBarcodes && newBarcodes.length > 0) {
          const newBarcodePromises = newBarcodes.map((barcode) => {
            return ItemBarcode.create(session, {
              ...barcode,
              itemId: item.id,
            });
          });
          await Promise.all(newBarcodePromises);
        }

        // Delete suppliers if specified
        if (suppliersToDelete && suppliersToDelete.length > 0) {
          await ItemSupplier.deleteMany(session, {
            id: {
              $in: suppliersToDelete,
            },
          });
        }

        // Delete barcodes if specified
        if (barcodesToDelete && barcodesToDelete.length > 0) {
          await ItemBarcode.deleteMany(session, {
            id: {
              $in: barcodesToDelete,
            },
          });
        }

        // If there's a preferred supplier among the updated or new suppliers, update the item's primarySupplierId
        const allSuppliers = [...(suppliers || []), ...(newSuppliers || [])];
        const preferredSupplier = allSuppliers.find((s) => s.isPreferred);
        if (preferredSupplier) {
          await ItemMaster.update(
            session,
            { id: item.id },
            {
              primarySupplierId: preferredSupplier.supplierId,
              primarySupplierSku: preferredSupplier.supplierSku,
              primarySupplierLeadTimeDays:
                preferredSupplier.supplierLeadTimeDays,
            }
          );
        }

        return {
          output: {
            success: true,
            message: 'Item and related details updated successfully',
          },
        };
      } catch (error) {
        log.error('Error updating item with details', error);
        return {
          output: {
            success: false,
            message: `Error updating item: ${
              error instanceof Error ? error.message : String(error)
            }`,
          },
        };
      }
    },
  },

  CreateMultipleItemsWithDetails: {
    async execute({
      session,
      input,
    }: Items.Actions.CreateMultipleItemsWithDetails.Execute.Request): Promise<Items.Actions.CreateMultipleItemsWithDetails.Execute.Response> {
      try {
        const { items } = input;
        const results = [];

        // Process all items sequentially - if any fail, the entire operation fails
        for (const item of items) {
          const { suppliers, barcodes, ...itemData } = item;
          const result = await createSingleItemWithDetails(
            session,
            itemData,
            suppliers,
            barcodes
          );

          results.push({
            itemCode: item.itemCode || 'UNKNOWN',
            success: true,
            itemId: result.itemId,
            supplierIds: result.supplierIds,
            barcodeIds: result.barcodeIds,
            error: '',
            validationErrors: [],
          });
        }

        return {
          output: {
            results,
            summary: {
              errorCount: 0,
              successCount: results.length,
              totalItems: results.length,
              validationErrorCount: 0,
            },
          },
        };
      } catch (error) {
        log.error('Error creating multiple items with details', error);
        throw enhanceValidationError(error);
      }
    },
  },
} as unknown as Items.Implementation;
