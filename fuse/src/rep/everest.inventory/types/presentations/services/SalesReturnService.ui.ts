/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

import type { EvstID as everest_appserver_primitive_ID } from "@pkg/everest.appserver/types/primitives/ID";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { Customer as everest_fin_accounting_model_node_Customer } from "@pkg/everest.fin.accounting/types/Customer";
import type { EvstDate as everest_appserver_primitive_Date } from "@pkg/everest.appserver/types/primitives/Date";
import type { IMInventorySalesReturn as everest_inventory_model_node_IMInventorySalesReturn } from "@pkg/everest.inventory/types/IMInventorySalesReturn";
import type { IMInventorySalesReturnLine as everest_inventory_model_node_IMInventorySalesReturnLine } from "@pkg/everest.inventory/types/IMInventorySalesReturnLine";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { EvstDecimal as everest_appserver_primitive_Decimal } from "@pkg/everest.appserver/types/primitives/Decimal";
import type { EvstPlainDate as everest_appserver_primitive_PlainDate } from "@pkg/everest.appserver/types/primitives/PlainDate";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { IMWarehouseLocation as everest_inventory_model_node_IMWarehouseLocation } from "@pkg/everest.inventory/types/IMWarehouseLocation";
import type { IMInventorySalesReturnInspectionDetail as everest_inventory_model_node_IMInventorySalesReturnInspectionDetail } from "@pkg/everest.inventory/types/IMInventorySalesReturnInspectionDetail";

import { createOdataClientRuntime } from '@everestsystems/content-core/lib/runtime/loader';

/**
 * Generated frontend types for OData presentation SalesReturnService.
 */
export namespace SalesReturnServiceUI {
  export namespace EntitySets {
    export namespace Customers {
      export namespace Get {
        export type Entity = {
          id?: everest_appserver_primitive_ID | undefined;
          name?: everest_appserver_primitive_Text | undefined;
          customerNumber?: everest_appserver_primitive_Text | undefined;
          email?: everest_appserver_primitive_Text | undefined;
          phone?: everest_appserver_primitive_Text | undefined;
          address?: everest_appserver_primitive_Text | undefined;
          city?: everest_appserver_primitive_Text | undefined;
          stateProvince?: everest_appserver_primitive_Text | undefined;
          postalCode?: everest_appserver_primitive_Text | undefined;
          country?: everest_appserver_primitive_Text | undefined;
          customerType?: everest_fin_accounting_model_node_Customer.Customer["customerType"] | undefined;
          status?: everest_fin_accounting_model_node_Customer.Customer["status"] | undefined;
          createdDate?: everest_appserver_primitive_Date | undefined;
          lastModifiedDate?: everest_appserver_primitive_Date | undefined;
        };

        export type Filter =
          | PropertyFilter
          | [Filter, 'and', Filter]
          | [Filter, 'or', Filter];

        /**
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), filters must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to filter by its amount component.
         */
        export type PropertyFilter = {
          property: string;
          comparator: 'eq' | 'ne' | 'lt' | 'le' | 'gt' | 'ge' | 'contains';
          value: unknown;
        };

        /**
         * Specifies sorting for a property in ascending or descending order.
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), sorting must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to sort by its amount component.
         */
        export type OrderbyItem = {
          property: string;
          ordering: 'asc' | 'desc';
        };

        export interface Request {
          setFilter(filter: Filter): this;

          setOrderby(orderby: ReadonlyArray<OrderbyItem>): this;

          setSkip(skip: number): this;

          setTop(top: number): this;

          setSelect(select: ReadonlyArray<keyof Entity>): this;

          setCount(count: boolean): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entities: Entity[];
          count?: number | undefined;
        };
      }
    }
    export namespace SalesReturns {
      export namespace Get {
        export type Entity = {
          id?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["id"] | undefined;
          uuid?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["uuid"] | undefined;
          externalId?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["externalId"] | undefined;
          active?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["active"] | undefined;
          businessPartnerId?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["businessPartnerId"] | undefined;
          documentDate?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["documentDate"] | undefined;
          documentNumber?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["documentNumber"] | undefined;
          entityId?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["entityId"] | undefined;
          notes?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["notes"] | undefined;
          status?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["status"] | undefined;
          createdBy?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["createdBy"] | undefined;
          createdDate?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["createdDate"] | undefined;
          lastModifiedBy?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["lastModifiedBy"] | undefined;
          lastModifiedDate?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["lastModifiedDate"] | undefined;
          lines?: {
            id: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["id"];
            lineId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["id"] | undefined;
            headerId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["headerId"];
            lineNumber: everest_appserver_primitive_Number;
            itemId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["itemId"];
            itemCode: everest_appserver_primitive_Text;
            itemName: everest_appserver_primitive_Text;
            locationId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["locationId"];
            locationName: everest_appserver_primitive_Text;
            unitId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["unitId"];
            unitCode: everest_appserver_primitive_Text;
            sourceDocumentLineId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineId"];
            sourceDocumentLineType: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineType"];
            quantity: everest_appserver_primitive_Decimal;
            inventoryQuantity: everest_appserver_primitive_Decimal;
            openQuantity: everest_appserver_primitive_Decimal;
            receivedQuantity: everest_appserver_primitive_Decimal;
            inspectedQuantity: everest_appserver_primitive_Decimal;
            processedQuantity: everest_appserver_primitive_Decimal;
            unitPrice: everest_appserver_primitive_Decimal;
            totalPrice: everest_appserver_primitive_Decimal;
            unitCost: everest_appserver_primitive_Decimal;
            totalCost: everest_appserver_primitive_Decimal;
            condition: everest_appserver_primitive_Text;
            disposition: everest_appserver_primitive_Text;
            reasonCodeId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["reasonCodeId"];
            status: everest_appserver_primitive_Text;
            receivedDate: everest_appserver_primitive_PlainDate;
            inspectedDate: everest_appserver_primitive_PlainDate;
            processedDate: everest_appserver_primitive_PlainDate;
            notes: everest_appserver_primitive_Text;
            inspectionNotes: everest_appserver_primitive_Text;
          }[] | undefined;
        };

        export type Filter =
          | PropertyFilter
          | [Filter, 'and', Filter]
          | [Filter, 'or', Filter];

        /**
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), filters must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to filter by its amount component.
         */
        export type PropertyFilter = {
          property: string;
          comparator: 'eq' | 'ne' | 'lt' | 'le' | 'gt' | 'ge' | 'contains';
          value: unknown;
        };

        /**
         * Specifies sorting for a property in ascending or descending order.
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), sorting must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to sort by its amount component.
         */
        export type OrderbyItem = {
          property: string;
          ordering: 'asc' | 'desc';
        };

        export interface Request {
          setFilter(filter: Filter): this;

          setOrderby(orderby: ReadonlyArray<OrderbyItem>): this;

          setSkip(skip: number): this;

          setTop(top: number): this;

          setSelect(select: ReadonlyArray<keyof Entity>): this;

          setCount(count: boolean): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entities: Entity[];
          count?: number | undefined;
        };
      }

      export namespace Post {
        export type Data = {
          externalId?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["externalId"] | undefined;
          active?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["active"] | undefined;
          businessPartnerId?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["businessPartnerId"] | undefined;
          documentDate?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["documentDate"] | undefined;
          documentNumber?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["documentNumber"] | undefined;
          entityId?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["entityId"] | undefined;
          notes?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["notes"] | undefined;
          status?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["status"] | undefined;
          lines?: {
            id: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["id"];
            lineId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["id"] | undefined;
            headerId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["headerId"];
            lineNumber: everest_appserver_primitive_Number;
            itemId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["itemId"];
            itemCode: everest_appserver_primitive_Text;
            itemName: everest_appserver_primitive_Text;
            locationId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["locationId"];
            locationName: everest_appserver_primitive_Text;
            unitId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["unitId"];
            unitCode: everest_appserver_primitive_Text;
            sourceDocumentLineId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineId"];
            sourceDocumentLineType: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineType"];
            quantity: everest_appserver_primitive_Decimal;
            inventoryQuantity: everest_appserver_primitive_Decimal;
            openQuantity: everest_appserver_primitive_Decimal;
            receivedQuantity: everest_appserver_primitive_Decimal;
            inspectedQuantity: everest_appserver_primitive_Decimal;
            processedQuantity: everest_appserver_primitive_Decimal;
            unitPrice: everest_appserver_primitive_Decimal;
            totalPrice: everest_appserver_primitive_Decimal;
            unitCost: everest_appserver_primitive_Decimal;
            totalCost: everest_appserver_primitive_Decimal;
            condition: everest_appserver_primitive_Text;
            disposition: everest_appserver_primitive_Text;
            reasonCodeId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["reasonCodeId"];
            status: everest_appserver_primitive_Text;
            receivedDate: everest_appserver_primitive_PlainDate;
            inspectedDate: everest_appserver_primitive_PlainDate;
            processedDate: everest_appserver_primitive_PlainDate;
            notes: everest_appserver_primitive_Text;
            inspectionNotes: everest_appserver_primitive_Text;
          }[] | undefined;
        };

        export type Entity = {
          id: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["id"];
          uuid?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["uuid"] | undefined;
          externalId?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["externalId"] | undefined;
          active?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["active"] | undefined;
          businessPartnerId?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["businessPartnerId"] | undefined;
          documentDate?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["documentDate"] | undefined;
          documentNumber?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["documentNumber"] | undefined;
          entityId?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["entityId"] | undefined;
          notes?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["notes"] | undefined;
          status?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["status"] | undefined;
          createdBy?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["createdBy"] | undefined;
          createdDate?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["createdDate"] | undefined;
          lastModifiedBy?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["lastModifiedBy"] | undefined;
          lastModifiedDate?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["lastModifiedDate"] | undefined;
          lines?: {
            id: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["id"];
            lineId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["id"] | undefined;
            headerId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["headerId"];
            lineNumber: everest_appserver_primitive_Number;
            itemId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["itemId"];
            itemCode: everest_appserver_primitive_Text;
            itemName: everest_appserver_primitive_Text;
            locationId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["locationId"];
            locationName: everest_appserver_primitive_Text;
            unitId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["unitId"];
            unitCode: everest_appserver_primitive_Text;
            sourceDocumentLineId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineId"];
            sourceDocumentLineType: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineType"];
            quantity: everest_appserver_primitive_Decimal;
            inventoryQuantity: everest_appserver_primitive_Decimal;
            openQuantity: everest_appserver_primitive_Decimal;
            receivedQuantity: everest_appserver_primitive_Decimal;
            inspectedQuantity: everest_appserver_primitive_Decimal;
            processedQuantity: everest_appserver_primitive_Decimal;
            unitPrice: everest_appserver_primitive_Decimal;
            totalPrice: everest_appserver_primitive_Decimal;
            unitCost: everest_appserver_primitive_Decimal;
            totalCost: everest_appserver_primitive_Decimal;
            condition: everest_appserver_primitive_Text;
            disposition: everest_appserver_primitive_Text;
            reasonCodeId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["reasonCodeId"];
            status: everest_appserver_primitive_Text;
            receivedDate: everest_appserver_primitive_PlainDate;
            inspectedDate: everest_appserver_primitive_PlainDate;
            processedDate: everest_appserver_primitive_PlainDate;
            notes: everest_appserver_primitive_Text;
            inspectionNotes: everest_appserver_primitive_Text;
          }[] | undefined;
        };

        export interface Request {
          setData(data: Data): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entity: Entity;
        };
      }

      export namespace Patch {
        export type Data = {
          externalId?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["externalId"] | null | undefined;
          active?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["active"] | null | undefined;
          businessPartnerId?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["businessPartnerId"] | null | undefined;
          documentDate?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["documentDate"] | null | undefined;
          documentNumber?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["documentNumber"] | null | undefined;
          entityId?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["entityId"] | null | undefined;
          notes?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["notes"] | null | undefined;
          status?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["status"] | null | undefined;
          lines?: {
            id: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["id"];
            lineId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["id"] | undefined;
            headerId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["headerId"];
            lineNumber: everest_appserver_primitive_Number;
            itemId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["itemId"];
            itemCode: everest_appserver_primitive_Text;
            itemName: everest_appserver_primitive_Text;
            locationId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["locationId"];
            locationName: everest_appserver_primitive_Text;
            unitId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["unitId"];
            unitCode: everest_appserver_primitive_Text;
            sourceDocumentLineId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineId"];
            sourceDocumentLineType: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineType"];
            quantity: everest_appserver_primitive_Decimal;
            inventoryQuantity: everest_appserver_primitive_Decimal;
            openQuantity: everest_appserver_primitive_Decimal;
            receivedQuantity: everest_appserver_primitive_Decimal;
            inspectedQuantity: everest_appserver_primitive_Decimal;
            processedQuantity: everest_appserver_primitive_Decimal;
            unitPrice: everest_appserver_primitive_Decimal;
            totalPrice: everest_appserver_primitive_Decimal;
            unitCost: everest_appserver_primitive_Decimal;
            totalCost: everest_appserver_primitive_Decimal;
            condition: everest_appserver_primitive_Text;
            disposition: everest_appserver_primitive_Text;
            reasonCodeId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["reasonCodeId"];
            status: everest_appserver_primitive_Text;
            receivedDate: everest_appserver_primitive_PlainDate;
            inspectedDate: everest_appserver_primitive_PlainDate;
            processedDate: everest_appserver_primitive_PlainDate;
            notes: everest_appserver_primitive_Text;
            inspectionNotes: everest_appserver_primitive_Text;
          }[] | null | undefined;
        };

        export interface Request {
          setId(id: number): this;

          setData(data: Data): this;

          execute(): Promise<Response>;
        }

        export type Response = Record<string, never>;
      }

      export namespace Delete {
        export interface Request {
          setId(id: number): this;

          execute(): Promise<Response>;
        }

        export type Response = Record<string, never>;
      }
    }
    export namespace Lines {
      export namespace Get {
        export type Entity = {
          id?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["id"] | undefined;
          headerId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["headerId"] | undefined;
          itemId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["itemId"] | undefined;
          locationId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["locationId"] | undefined;
          unitId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["unitId"] | undefined;
          sourceDocumentLineId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineId"] | undefined;
          sourceDocumentLineType?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineType"] | undefined;
          quantity?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["quantity"] | undefined;
          inventoryQuantity?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["inventoryQuantity"] | undefined;
          openQuantity?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["openQuantity"] | undefined;
          unitPrice?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["unitPrice"] | undefined;
          status?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["status"] | undefined;
          active?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["active"] | undefined;
          createdBy?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["createdBy"] | undefined;
          createdDate?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["createdDate"] | undefined;
          lastModifiedBy?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["lastModifiedBy"] | undefined;
          lastModifiedDate?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["lastModifiedDate"] | undefined;
        };

        export type Filter =
          | PropertyFilter
          | [Filter, 'and', Filter]
          | [Filter, 'or', Filter];

        /**
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), filters must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to filter by its amount component.
         */
        export type PropertyFilter = {
          property: string;
          comparator: 'eq' | 'ne' | 'lt' | 'le' | 'gt' | 'ge' | 'contains';
          value: unknown;
        };

        /**
         * Specifies sorting for a property in ascending or descending order.
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), sorting must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to sort by its amount component.
         */
        export type OrderbyItem = {
          property: string;
          ordering: 'asc' | 'desc';
        };

        export interface Request {
          setFilter(filter: Filter): this;

          setOrderby(orderby: ReadonlyArray<OrderbyItem>): this;

          setSkip(skip: number): this;

          setTop(top: number): this;

          setSelect(select: ReadonlyArray<keyof Entity>): this;

          setCount(count: boolean): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entities: Entity[];
          count?: number | undefined;
        };
      }

      export namespace Post {
        export type Data = {
          headerId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["headerId"] | undefined;
          itemId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["itemId"] | undefined;
          locationId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["locationId"] | undefined;
          unitId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["unitId"] | undefined;
          sourceDocumentLineId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineId"] | undefined;
          sourceDocumentLineType?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineType"] | undefined;
          quantity?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["quantity"] | undefined;
          inventoryQuantity?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["inventoryQuantity"] | undefined;
          openQuantity?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["openQuantity"] | undefined;
          unitPrice?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["unitPrice"] | undefined;
          status?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["status"] | undefined;
          active?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["active"] | undefined;
        };

        export type Entity = {
          id: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["id"];
          headerId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["headerId"] | undefined;
          itemId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["itemId"] | undefined;
          locationId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["locationId"] | undefined;
          unitId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["unitId"] | undefined;
          sourceDocumentLineId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineId"] | undefined;
          sourceDocumentLineType?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineType"] | undefined;
          quantity?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["quantity"] | undefined;
          inventoryQuantity?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["inventoryQuantity"] | undefined;
          openQuantity?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["openQuantity"] | undefined;
          unitPrice?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["unitPrice"] | undefined;
          status?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["status"] | undefined;
          active?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["active"] | undefined;
          createdBy?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["createdBy"] | undefined;
          createdDate?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["createdDate"] | undefined;
          lastModifiedBy?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["lastModifiedBy"] | undefined;
          lastModifiedDate?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["lastModifiedDate"] | undefined;
        };

        export interface Request {
          setData(data: Data): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entity: Entity;
        };
      }

      export namespace Patch {
        export type Data = {
          headerId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["headerId"] | null | undefined;
          itemId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["itemId"] | null | undefined;
          locationId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["locationId"] | null | undefined;
          unitId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["unitId"] | null | undefined;
          sourceDocumentLineId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineId"] | null | undefined;
          sourceDocumentLineType?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineType"] | null | undefined;
          quantity?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["quantity"] | null | undefined;
          inventoryQuantity?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["inventoryQuantity"] | null | undefined;
          openQuantity?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["openQuantity"] | null | undefined;
          unitPrice?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["unitPrice"] | null | undefined;
          status?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["status"] | null | undefined;
          active?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["active"] | null | undefined;
        };

        export interface Request {
          setId(id: number): this;

          setData(data: Data): this;

          execute(): Promise<Response>;
        }

        export type Response = Record<string, never>;
      }

      export namespace Delete {
        export interface Request {
          setId(id: number): this;

          execute(): Promise<Response>;
        }

        export type Response = Record<string, never>;
      }
    }
  }

  export namespace Actions {
    export namespace CreateSalesReturn {
      export namespace Execute {
        export type Input = {
          businessPartnerId: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["businessPartnerId"];
          documentDate: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["documentDate"];
          documentNumber: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["documentNumber"];
          entityId: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["entityId"];
          notes: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["notes"];
          status: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["status"];
          lines: {
            lineNumber: everest_appserver_primitive_Number;
            itemId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["itemId"];
            locationId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["locationId"];
            unitId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["unitId"];
            sourceDocumentLineId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineId"];
            sourceDocumentLineType: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineType"];
            quantity: everest_appserver_primitive_Decimal;
            inventoryQuantity: everest_appserver_primitive_Decimal;
            unitPrice: everest_appserver_primitive_Decimal;
            unitCost: everest_appserver_primitive_Decimal;
            condition: everest_appserver_primitive_Text;
            disposition: everest_appserver_primitive_Text;
            reasonCodeId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["reasonCodeId"];
            notes: everest_appserver_primitive_Text;
          }[];
        };

        export type Output = {
          success: everest_appserver_primitive_TrueFalse;
          salesReturnId: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["id"];
          documentNumber: everest_appserver_primitive_Text;
          warnings: everest_appserver_primitive_Text[];
          errors: everest_appserver_primitive_Text[];
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace UpdateSalesReturn {
      export namespace Execute {
        export type Input = {
          id: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["id"];
          businessPartnerId: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["businessPartnerId"];
          documentDate: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["documentDate"];
          documentNumber: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["documentNumber"];
          entityId: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["entityId"];
          notes: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["notes"];
          status: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["status"];
          externalId: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["externalId"];
          lines: {
            id: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["id"];
            lineId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["id"] | undefined;
            lineNumber: everest_appserver_primitive_Number;
            itemId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["itemId"];
            locationId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["locationId"];
            unitId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["unitId"];
            sourceDocumentLineId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineId"];
            sourceDocumentLineType: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineType"];
            quantity: everest_appserver_primitive_Decimal;
            inventoryQuantity: everest_appserver_primitive_Decimal;
            unitPrice: everest_appserver_primitive_Decimal;
            condition: everest_appserver_primitive_Text;
            disposition: everest_appserver_primitive_Text;
            reasonCodeId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["reasonCodeId"];
          }[];
          linesToRemove: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["id"][];
        };

        export type Output = {
          success: everest_appserver_primitive_TrueFalse;
          salesReturnId: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["id"];
          documentNumber: everest_appserver_primitive_Text;
          errorMessage: everest_appserver_primitive_Text;
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace ApproveSalesReturn {
      export namespace Execute {
        export type Input = {
          salesReturnId: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["id"];
          approvalNotes: everest_appserver_primitive_Text;
        };

        export type Output = {
          success: everest_appserver_primitive_TrueFalse;
          updatedStatus: everest_appserver_primitive_Text;
          inventoryTransactionIds: everest_appserver_primitive_Number[];
          warnings: everest_appserver_primitive_Text[];
          id: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["id"];
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace ReceiveReturnedGoods {
      export namespace Execute {
        export type Input = {
          salesReturnId: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["id"];
          receivedDate: everest_appserver_primitive_Date;
          notes: everest_appserver_primitive_Text;
          lines: {
            lineId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["id"];
            receivedQuantity: everest_appserver_primitive_Decimal;
            notes: everest_appserver_primitive_Text;
          }[];
        };

        export type Output = {
          success: everest_appserver_primitive_TrueFalse;
          updatedStatus: everest_appserver_primitive_Text;
          inventoryTransactionIds: everest_appserver_primitive_Number[];
          warnings: everest_appserver_primitive_Text[];
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace InspectReturnedGoods {
      export namespace Execute {
        export type Input = {
          salesReturnId: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["id"];
          inspectedDate: everest_appserver_primitive_Date;
          inspectorId: everest_appserver_primitive_Number;
          notes: everest_appserver_primitive_Text;
          lines: {
            lineId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["id"];
            inspectedQuantity: everest_appserver_primitive_Decimal;
            approvedQuantity: everest_appserver_primitive_Decimal;
            rejectedQuantity: everest_appserver_primitive_Decimal;
            condition: everest_appserver_primitive_Text;
            disposition: everest_appserver_primitive_Text;
            qualityGrade: everest_appserver_primitive_Text;
            inspectionNotes: everest_appserver_primitive_Text;
            rejectionReason: everest_appserver_primitive_Text;
            inspectionDetails: {
              sequenceNumber: everest_appserver_primitive_Number;
              inspectedQuantity: everest_appserver_primitive_Decimal;
              approvalStatus: everest_appserver_primitive_Text;
              condition: everest_appserver_primitive_Text;
              disposition: everest_appserver_primitive_Text;
              qualityGrade: everest_appserver_primitive_Text;
              inspectionNotes: everest_appserver_primitive_Text;
              rejectionReason: everest_appserver_primitive_Text;
            }[];
          }[];
        };

        export type Output = {
          success: everest_appserver_primitive_TrueFalse;
          updatedStatus: everest_appserver_primitive_Text;
          inspectionResults: {
            totalInspected: everest_appserver_primitive_Decimal;
            totalApproved: everest_appserver_primitive_Decimal;
            totalRejected: everest_appserver_primitive_Decimal;
            overallGrade: everest_appserver_primitive_Text;
          };
          warnings: everest_appserver_primitive_Text[];
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace CreatePutAwayTasksFromInspection {
      export namespace Execute {
        export type Input = {
          salesReturnId: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["id"];
          inspectionWarehouseId: everest_inventory_model_node_IMWarehouseLocation.IMWarehouseLocation["id"];
          createPutAwayTasks: everest_appserver_primitive_TrueFalse;
          assignedUserId: everest_appserver_primitive_Number;
          priority: everest_appserver_primitive_Text;
          strategy: everest_appserver_primitive_Text;
          scheduledDate: everest_appserver_primitive_Date;
          dueDate: everest_appserver_primitive_Date;
          notes: everest_appserver_primitive_Text;
        };

        export type Output = {
          success: everest_appserver_primitive_TrueFalse;
          putAwayTasksCreated: everest_appserver_primitive_Number;
          putAwayTaskIds: everest_appserver_primitive_Number[];
          message: everest_appserver_primitive_Text;
          warnings: everest_appserver_primitive_Text[];
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace ProcessReturnDisposition {
      export namespace Execute {
        export type Input = {
          salesReturnId: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["id"];
          processedDate: everest_appserver_primitive_Date;
          notes: everest_appserver_primitive_Text;
        };

        export type Output = {
          success: everest_appserver_primitive_TrueFalse;
          updatedStatus: everest_appserver_primitive_Text;
          inventoryTransactionIds: everest_appserver_primitive_Number[];
          warnings: everest_appserver_primitive_Text[];
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace CompleteSalesReturn {
      export namespace Execute {
        export type Input = {
          salesReturnId: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["id"];
          completionNotes: everest_appserver_primitive_Text;
        };

        export type Output = {
          success: everest_appserver_primitive_TrueFalse;
          updatedStatus: everest_appserver_primitive_Text;
          creditMemoNumber: everest_appserver_primitive_Text;
          warnings: everest_appserver_primitive_Text[];
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }
  }

  export namespace Functions {
    export namespace GetSalesReturnWithDetails {
      export namespace Execute {
        export type Input = {
          salesReturnId: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["id"];
        };

        export type Output = {
          salesReturn: {
            id: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["id"];
            uuid: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["uuid"];
            externalId: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["externalId"];
            active: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["active"];
            businessPartnerId: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["businessPartnerId"];
            documentDate: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["documentDate"];
            documentNumber: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["documentNumber"];
            entityId: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["entityId"];
            notes: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["notes"];
            status: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["status"];
            createdBy: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["createdBy"];
            createdDate: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["createdDate"];
            lastModifiedBy: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["lastModifiedBy"];
            lastModifiedDate: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["lastModifiedDate"];
            customerName: everest_appserver_primitive_Text;
          };
          lines: {
            id: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["id"];
            lineNumber: everest_appserver_primitive_Number;
            itemId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["itemId"];
            itemCode: everest_appserver_primitive_Text;
            itemName: everest_appserver_primitive_Text;
            locationId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["locationId"];
            locationName: everest_appserver_primitive_Text;
            unitId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["unitId"];
            unitCode: everest_appserver_primitive_Text;
            sourceDocumentLineId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineId"];
            sourceDocumentLineType: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineType"];
            quantity: everest_appserver_primitive_Decimal;
            inventoryQuantity: everest_appserver_primitive_Decimal;
            openQuantity: everest_appserver_primitive_Decimal;
            receivedQuantity: everest_appserver_primitive_Decimal;
            inspectedQuantity: everest_appserver_primitive_Decimal;
            processedQuantity: everest_appserver_primitive_Decimal;
            unitPrice: everest_appserver_primitive_Decimal;
            totalPrice: everest_appserver_primitive_Decimal;
            unitCost: everest_appserver_primitive_Decimal;
            totalCost: everest_appserver_primitive_Decimal;
            condition: everest_appserver_primitive_Text;
            disposition: everest_appserver_primitive_Text;
            reasonCodeId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["reasonCodeId"];
            reasonCodeDescription: everest_appserver_primitive_Text;
            status: everest_appserver_primitive_Text;
            receivedDate: everest_appserver_primitive_PlainDate;
            inspectedDate: everest_appserver_primitive_PlainDate;
            processedDate: everest_appserver_primitive_PlainDate;
            notes: everest_appserver_primitive_Text;
            inspectionNotes: everest_appserver_primitive_Text;
            active: everest_appserver_primitive_TrueFalse;
          }[];
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace ListSalesReturnsWithFilters {
      export namespace Execute {
        export type Input = {
          status: everest_appserver_primitive_Text;
          businessPartnerId: everest_appserver_primitive_Number;
          dateFrom: everest_appserver_primitive_Date;
          dateTo: everest_appserver_primitive_Date;
          documentNumber: everest_appserver_primitive_Text;
          limit: everest_appserver_primitive_Number;
          offset: everest_appserver_primitive_Number;
        };

        export type Output = {
          salesReturns: {
            id: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["id"];
            documentNumber: everest_appserver_primitive_Text;
            status: everest_appserver_primitive_Text;
            businessPartnerId: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["businessPartnerId"];
            customerName: everest_appserver_primitive_Text;
            documentDate: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["documentDate"];
            createdDate: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["createdDate"];
            lastModifiedDate: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["lastModifiedDate"];
          }[];
          totalCount: everest_appserver_primitive_Number;
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace GetCustomerById {
      export namespace Execute {
        export type Input = {
          customerId: everest_appserver_primitive_ID;
        };

        export type Output = {
          customer: {
            id: everest_appserver_primitive_ID;
            name: everest_appserver_primitive_Text;
            customerNumber: everest_appserver_primitive_Text;
            email: everest_appserver_primitive_Text;
            phone: everest_appserver_primitive_Text;
            address: everest_appserver_primitive_Text;
            city: everest_appserver_primitive_Text;
            stateProvince: everest_appserver_primitive_Text;
            postalCode: everest_appserver_primitive_Text;
            country: everest_appserver_primitive_Text;
            customerType: everest_fin_accounting_model_node_Customer.Customer["customerType"];
            status: everest_fin_accounting_model_node_Customer.Customer["status"];
            createdDate: everest_appserver_primitive_Date;
            lastModifiedDate: everest_appserver_primitive_Date;
          };
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace GetInspectionDetails {
      export namespace Execute {
        export type Input = {
          salesReturnLineId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["id"];
        };

        export type Output = {
          inspectionDetails: {
            id: everest_inventory_model_node_IMInventorySalesReturnInspectionDetail.IMInventorySalesReturnInspectionDetail["id"];
            documentLineId: everest_inventory_model_node_IMInventorySalesReturnInspectionDetail.IMInventorySalesReturnInspectionDetail["documentLineId"];
            inspectedQuantity: everest_appserver_primitive_Decimal;
            approvalStatus: everest_appserver_primitive_Text;
            condition: everest_appserver_primitive_Text;
            disposition: everest_appserver_primitive_Text;
            qualityGrade: everest_appserver_primitive_Text;
            inspectionNotes: everest_appserver_primitive_Text;
            rejectionReason: everest_appserver_primitive_Text;
            createdDate: everest_appserver_primitive_Date;
            createdBy: everest_appserver_primitive_Text;
          }[];
          totalDetails: everest_appserver_primitive_Number;
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace GetInspectionHistory {
      export namespace Execute {
        export type Input = {
          salesReturnId: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["id"];
        };

        export type Output = {
          inspectionHistory: {
            id: everest_appserver_primitive_Number;
            lineId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["id"];
            itemCode: everest_appserver_primitive_Text;
            itemName: everest_appserver_primitive_Text;
            inspectedDate: everest_appserver_primitive_Date;
            inspectorName: everest_appserver_primitive_Text;
            inspectedQuantity: everest_appserver_primitive_Decimal;
            approvedQuantity: everest_appserver_primitive_Decimal;
            rejectedQuantity: everest_appserver_primitive_Decimal;
            condition: everest_appserver_primitive_Text;
            disposition: everest_appserver_primitive_Text;
            qualityGrade: everest_appserver_primitive_Text;
            inspectionNotes: everest_appserver_primitive_Text;
            rejectionReason: everest_appserver_primitive_Text;
            inspectionDetails: {
              id: everest_inventory_model_node_IMInventorySalesReturnInspectionDetail.IMInventorySalesReturnInspectionDetail["id"];
              sequenceNumber: everest_appserver_primitive_Number;
              inspectedQuantity: everest_appserver_primitive_Decimal;
              approvalStatus: everest_appserver_primitive_Text;
              condition: everest_appserver_primitive_Text;
              disposition: everest_appserver_primitive_Text;
              qualityGrade: everest_appserver_primitive_Text;
              inspectionNotes: everest_appserver_primitive_Text;
              rejectionReason: everest_appserver_primitive_Text;
            }[];
          }[];
          totalInspections: everest_appserver_primitive_Number;
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }
  }

  export namespace Permission {
    export namespace Check {
      export type Result = {
        dataSets: {
          Customers: {
            isGetPermitted: boolean;
          };
          Lines: {
            isGetPermitted: boolean;
            isPostPermitted: boolean;
            isPatchPermitted: boolean;
            isDeletePermitted: boolean;
          };
          SalesReturns: {
            isGetPermitted: boolean;
            isPostPermitted: boolean;
            isPatchPermitted: boolean;
            isDeletePermitted: boolean;
          };
        };
        actions: {
          ApproveSalesReturn: {
            isExecutePermitted: boolean;
          };
          CompleteSalesReturn: {
            isExecutePermitted: boolean;
          };
          CreatePutAwayTasksFromInspection: {
            isExecutePermitted: boolean;
          };
          CreateSalesReturn: {
            isExecutePermitted: boolean;
          };
          GetCustomerById: {
            isExecutePermitted: boolean;
          };
          GetInspectionDetails: {
            isExecutePermitted: boolean;
          };
          GetInspectionHistory: {
            isExecutePermitted: boolean;
          };
          GetSalesReturnWithDetails: {
            isExecutePermitted: boolean;
          };
          InspectReturnedGoods: {
            isExecutePermitted: boolean;
          };
          ListSalesReturnsWithFilters: {
            isExecutePermitted: boolean;
          };
          ProcessReturnDisposition: {
            isExecutePermitted: boolean;
          };
          ReceiveReturnedGoods: {
            isExecutePermitted: boolean;
          };
          UpdateSalesReturn: {
            isExecutePermitted: boolean;
          };
        };
      };

      export interface Request {
        execute(): Promise<Response>;
      }

      export type Response = {
        result: Result;
      };
    }
  }

  export namespace Api {
    export type EntitySets = {
      Customers: {
        get: EntitySets.Customers.Get.Request;
      };
      SalesReturns: {
        get: EntitySets.SalesReturns.Get.Request;
        post: EntitySets.SalesReturns.Post.Request;
        patch: EntitySets.SalesReturns.Patch.Request;
        delete: EntitySets.SalesReturns.Delete.Request;
      };
      Lines: {
        get: EntitySets.Lines.Get.Request;
        post: EntitySets.Lines.Post.Request;
        patch: EntitySets.Lines.Patch.Request;
        delete: EntitySets.Lines.Delete.Request;
      };
    };

    export type Actions = {
      CreateSalesReturn: {
        execute: Actions.CreateSalesReturn.Execute.Request;
      };
      UpdateSalesReturn: {
        execute: Actions.UpdateSalesReturn.Execute.Request;
      };
      ApproveSalesReturn: {
        execute: Actions.ApproveSalesReturn.Execute.Request;
      };
      ReceiveReturnedGoods: {
        execute: Actions.ReceiveReturnedGoods.Execute.Request;
      };
      InspectReturnedGoods: {
        execute: Actions.InspectReturnedGoods.Execute.Request;
      };
      CreatePutAwayTasksFromInspection: {
        execute: Actions.CreatePutAwayTasksFromInspection.Execute.Request;
      };
      ProcessReturnDisposition: {
        execute: Actions.ProcessReturnDisposition.Execute.Request;
      };
      CompleteSalesReturn: {
        execute: Actions.CompleteSalesReturn.Execute.Request;
      };
    };

    export type Functions = {
      GetSalesReturnWithDetails: {
        execute: Functions.GetSalesReturnWithDetails.Execute.Request;
      };
      ListSalesReturnsWithFilters: {
        execute: Functions.ListSalesReturnsWithFilters.Execute.Request;
      };
      GetCustomerById: {
        execute: Functions.GetCustomerById.Execute.Request;
      };
      GetInspectionDetails: {
        execute: Functions.GetInspectionDetails.Execute.Request;
      };
      GetInspectionHistory: {
        execute: Functions.GetInspectionHistory.Execute.Request;
      };
    };

    export interface Client {
      createEntitySetGetRequest<T extends 'Customers' | 'SalesReturns' | 'Lines'>(entitySetName: T): Api.EntitySets[T]['get'];

      createEntitySetPostRequest<T extends 'SalesReturns' | 'Lines'>(entitySetName: T): Api.EntitySets[T]['post'];

      createEntitySetPatchRequest<T extends 'SalesReturns' | 'Lines'>(entitySetName: T): Api.EntitySets[T]['patch'];

      createEntitySetDeleteRequest<T extends 'SalesReturns' | 'Lines'>(entitySetName: T): Api.EntitySets[T]['delete'];

      createActionExecuteRequest<T extends 'CreateSalesReturn' | 'UpdateSalesReturn' | 'ApproveSalesReturn' | 'ReceiveReturnedGoods' | 'InspectReturnedGoods' | 'CreatePutAwayTasksFromInspection' | 'ProcessReturnDisposition' | 'CompleteSalesReturn'>(actionName: T): Api.Actions[T]['execute'];

      createFunctionExecuteRequest<T extends 'GetSalesReturnWithDetails' | 'ListSalesReturnsWithFilters' | 'GetCustomerById' | 'GetInspectionDetails' | 'GetInspectionHistory'>(functionName: T): Api.Functions[T]['execute'];

      createPermissionCheckRequest(): Permission.Check.Request;
    }
  }

  export const client = createOdataClientRuntime<Api.Client>('everest.inventory/services/SalesReturnService');
}

