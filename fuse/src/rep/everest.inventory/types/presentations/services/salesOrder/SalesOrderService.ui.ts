/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

import type { EvstID as everest_appserver_primitive_ID } from "@pkg/everest.appserver/types/primitives/ID";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { Customer as everest_fin_accounting_model_node_Customer } from "@pkg/everest.fin.accounting/types/Customer";
import type { EvstDate as everest_appserver_primitive_Date } from "@pkg/everest.appserver/types/primitives/Date";
import type { IMSalesOrder as everest_inventory_model_node_IMSalesOrder } from "@pkg/everest.inventory/types/IMSalesOrder";
import type { EvstPlainDate as everest_appserver_primitive_PlainDate } from "@pkg/everest.appserver/types/primitives/PlainDate";
import type { IMSalesOrderLine as everest_inventory_model_node_IMSalesOrderLine } from "@pkg/everest.inventory/types/IMSalesOrderLine";
import type { EvstDecimal as everest_appserver_primitive_Decimal } from "@pkg/everest.appserver/types/primitives/Decimal";
import type { IMWarehouseLocation as everest_inventory_model_node_IMWarehouseLocation } from "@pkg/everest.inventory/types/IMWarehouseLocation";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { ItemMaster as everest_inventory_model_node_ItemMaster } from "@pkg/everest.inventory/types/ItemMaster";
import type { IMPickList as everest_inventory_model_node_IMPickList } from "@pkg/everest.inventory/types/IMPickList";
import type { IMPackageLine as everest_inventory_model_node_IMPackageLine } from "@pkg/everest.inventory/types/IMPackageLine";
import type { UnitOfMeasure as everest_inventory_model_node_UnitOfMeasure } from "@pkg/everest.inventory/types/UnitOfMeasure";
import type { IMInventorySalesDelivery as everest_inventory_model_node_IMInventorySalesDelivery } from "@pkg/everest.inventory/types/IMInventorySalesDelivery";
import type { IMPickListLine as everest_inventory_model_node_IMPickListLine } from "@pkg/everest.inventory/types/IMPickListLine";

import { createOdataClientRuntime } from '@everestsystems/content-core/lib/runtime/loader';

/**
 * Generated frontend types for OData presentation SalesOrderService.
 */
export namespace SalesOrderServiceUI {
  export namespace EntitySets {
    export namespace Customers {
      export namespace Get {
        export type Entity = {
          id?: everest_appserver_primitive_ID | undefined;
          name?: everest_appserver_primitive_Text | undefined;
          customerNumber?: everest_appserver_primitive_Text | undefined;
          email?: everest_appserver_primitive_Text | undefined;
          phone?: everest_appserver_primitive_Text | undefined;
          address?: everest_appserver_primitive_Text | undefined;
          city?: everest_appserver_primitive_Text | undefined;
          stateProvince?: everest_appserver_primitive_Text | undefined;
          postalCode?: everest_appserver_primitive_Text | undefined;
          country?: everest_appserver_primitive_Text | undefined;
          customerType?: everest_fin_accounting_model_node_Customer.Customer["customerType"] | undefined;
          status?: everest_fin_accounting_model_node_Customer.Customer["status"] | undefined;
          createdDate?: everest_appserver_primitive_Date | undefined;
          lastModifiedDate?: everest_appserver_primitive_Date | undefined;
        };

        export type Filter =
          | PropertyFilter
          | [Filter, 'and', Filter]
          | [Filter, 'or', Filter];

        /**
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), filters must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to filter by its amount component.
         */
        export type PropertyFilter = {
          property: string;
          comparator: 'eq' | 'ne' | 'lt' | 'le' | 'gt' | 'ge' | 'contains';
          value: unknown;
        };

        /**
         * Specifies sorting for a property in ascending or descending order.
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), sorting must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to sort by its amount component.
         */
        export type OrderbyItem = {
          property: string;
          ordering: 'asc' | 'desc';
        };

        export interface Request {
          setFilter(filter: Filter): this;

          setOrderby(orderby: ReadonlyArray<OrderbyItem>): this;

          setSkip(skip: number): this;

          setTop(top: number): this;

          setSelect(select: ReadonlyArray<keyof Entity>): this;

          setCount(count: boolean): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entities: Entity[];
          count?: number | undefined;
        };
      }
    }
    export namespace SalesOrders {
      export namespace Get {
        export type Entity = {
          id?: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["id"] | undefined;
          uuid?: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["uuid"] | undefined;
          documentNumber?: everest_appserver_primitive_Text | undefined;
          externalId?: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["externalId"] | undefined;
          active?: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["active"] | undefined;
          carrier?: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["carrier"] | undefined;
          fulfillmentStatus?: everest_appserver_primitive_Text | undefined;
          plannedShipDate?: everest_appserver_primitive_PlainDate | undefined;
          requestedDeliveryDate?: everest_appserver_primitive_PlainDate | undefined;
          salesOrderId?: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["salesOrderId"] | undefined;
          shippingAddressId?: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["shippingAddressId"] | undefined;
          businessPartnerId?: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["businessPartnerId"] | undefined;
          shippingMemo?: everest_appserver_primitive_Text | undefined;
          shippingMethod?: everest_appserver_primitive_Text | undefined;
          createdDate?: everest_appserver_primitive_Date | undefined;
          createdBy?: everest_appserver_primitive_Text | undefined;
          lastModifiedDate?: everest_appserver_primitive_Date | undefined;
          lastModifiedBy?: everest_appserver_primitive_Text | undefined;
          lines?: {
            id: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["id"];
            headerId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["headerId"];
            itemId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["itemId"];
            itemCode: everest_appserver_primitive_Text;
            itemName: everest_appserver_primitive_Text;
            locationId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["locationId"];
            locationName: everest_appserver_primitive_Text;
            unitId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["unitId"];
            unitCode: everest_appserver_primitive_Text;
            quantity: everest_appserver_primitive_Decimal;
            allocatedQuantity: everest_appserver_primitive_Decimal;
            openAllocatedQuantity: everest_appserver_primitive_Decimal;
            openQuantity: everest_appserver_primitive_Decimal;
            backorderedQuantity: everest_appserver_primitive_Decimal;
            pickedQuantity: everest_appserver_primitive_Decimal;
            shippedQuantity: everest_appserver_primitive_Decimal;
            fulfillmentStatus: everest_appserver_primitive_Text;
            actualShipDate: everest_appserver_primitive_PlainDate;
            trackingNumber: everest_appserver_primitive_Text;
            salesOrderProductLineId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["salesOrderProductLineId"];
          }[] | undefined;
        };

        export type Filter =
          | PropertyFilter
          | [Filter, 'and', Filter]
          | [Filter, 'or', Filter];

        /**
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), filters must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to filter by its amount component.
         */
        export type PropertyFilter = {
          property: string;
          comparator: 'eq' | 'ne' | 'lt' | 'le' | 'gt' | 'ge' | 'contains';
          value: unknown;
        };

        /**
         * Specifies sorting for a property in ascending or descending order.
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), sorting must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to sort by its amount component.
         */
        export type OrderbyItem = {
          property: string;
          ordering: 'asc' | 'desc';
        };

        export interface Request {
          setFilter(filter: Filter): this;

          setOrderby(orderby: ReadonlyArray<OrderbyItem>): this;

          setSkip(skip: number): this;

          setTop(top: number): this;

          setSelect(select: ReadonlyArray<keyof Entity>): this;

          setCount(count: boolean): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entities: Entity[];
          count?: number | undefined;
        };
      }

      export namespace Post {
        export type Data = {
          externalId?: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["externalId"] | undefined;
          active?: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["active"] | undefined;
          carrier?: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["carrier"] | undefined;
          plannedShipDate?: everest_appserver_primitive_PlainDate | undefined;
          requestedDeliveryDate?: everest_appserver_primitive_PlainDate | undefined;
          salesOrderId?: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["salesOrderId"] | undefined;
          shippingAddressId?: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["shippingAddressId"] | undefined;
          businessPartnerId?: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["businessPartnerId"] | undefined;
          shippingMemo?: everest_appserver_primitive_Text | undefined;
          shippingMethod?: everest_appserver_primitive_Text | undefined;
          lines?: {
            id: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["id"];
            headerId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["headerId"];
            itemId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["itemId"];
            itemCode: everest_appserver_primitive_Text;
            itemName: everest_appserver_primitive_Text;
            locationId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["locationId"];
            locationName: everest_appserver_primitive_Text;
            unitId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["unitId"];
            unitCode: everest_appserver_primitive_Text;
            quantity: everest_appserver_primitive_Decimal;
            allocatedQuantity: everest_appserver_primitive_Decimal;
            openAllocatedQuantity: everest_appserver_primitive_Decimal;
            openQuantity: everest_appserver_primitive_Decimal;
            backorderedQuantity: everest_appserver_primitive_Decimal;
            pickedQuantity: everest_appserver_primitive_Decimal;
            shippedQuantity: everest_appserver_primitive_Decimal;
            fulfillmentStatus: everest_appserver_primitive_Text;
            actualShipDate: everest_appserver_primitive_PlainDate;
            trackingNumber: everest_appserver_primitive_Text;
            salesOrderProductLineId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["salesOrderProductLineId"];
          }[] | undefined;
        };

        export type Entity = {
          id: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["id"];
          uuid?: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["uuid"] | undefined;
          documentNumber?: everest_appserver_primitive_Text | undefined;
          externalId?: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["externalId"] | undefined;
          active?: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["active"] | undefined;
          carrier?: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["carrier"] | undefined;
          fulfillmentStatus?: everest_appserver_primitive_Text | undefined;
          plannedShipDate?: everest_appserver_primitive_PlainDate | undefined;
          requestedDeliveryDate?: everest_appserver_primitive_PlainDate | undefined;
          salesOrderId?: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["salesOrderId"] | undefined;
          shippingAddressId?: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["shippingAddressId"] | undefined;
          businessPartnerId?: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["businessPartnerId"] | undefined;
          shippingMemo?: everest_appserver_primitive_Text | undefined;
          shippingMethod?: everest_appserver_primitive_Text | undefined;
          createdDate?: everest_appserver_primitive_Date | undefined;
          createdBy?: everest_appserver_primitive_Text | undefined;
          lastModifiedDate?: everest_appserver_primitive_Date | undefined;
          lastModifiedBy?: everest_appserver_primitive_Text | undefined;
          lines?: {
            id: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["id"];
            headerId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["headerId"];
            itemId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["itemId"];
            itemCode: everest_appserver_primitive_Text;
            itemName: everest_appserver_primitive_Text;
            locationId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["locationId"];
            locationName: everest_appserver_primitive_Text;
            unitId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["unitId"];
            unitCode: everest_appserver_primitive_Text;
            quantity: everest_appserver_primitive_Decimal;
            allocatedQuantity: everest_appserver_primitive_Decimal;
            openAllocatedQuantity: everest_appserver_primitive_Decimal;
            openQuantity: everest_appserver_primitive_Decimal;
            backorderedQuantity: everest_appserver_primitive_Decimal;
            pickedQuantity: everest_appserver_primitive_Decimal;
            shippedQuantity: everest_appserver_primitive_Decimal;
            fulfillmentStatus: everest_appserver_primitive_Text;
            actualShipDate: everest_appserver_primitive_PlainDate;
            trackingNumber: everest_appserver_primitive_Text;
            salesOrderProductLineId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["salesOrderProductLineId"];
          }[] | undefined;
        };

        export interface Request {
          setData(data: Data): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entity: Entity;
        };
      }

      export namespace Patch {
        export type Data = {
          externalId?: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["externalId"] | null | undefined;
          active?: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["active"] | null | undefined;
          carrier?: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["carrier"] | null | undefined;
          plannedShipDate?: everest_appserver_primitive_PlainDate | null | undefined;
          requestedDeliveryDate?: everest_appserver_primitive_PlainDate | null | undefined;
          salesOrderId?: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["salesOrderId"] | null | undefined;
          shippingAddressId?: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["shippingAddressId"] | null | undefined;
          businessPartnerId?: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["businessPartnerId"] | null | undefined;
          shippingMemo?: everest_appserver_primitive_Text | null | undefined;
          shippingMethod?: everest_appserver_primitive_Text | null | undefined;
          lines?: {
            id: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["id"];
            headerId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["headerId"];
            itemId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["itemId"];
            itemCode: everest_appserver_primitive_Text;
            itemName: everest_appserver_primitive_Text;
            locationId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["locationId"];
            locationName: everest_appserver_primitive_Text;
            unitId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["unitId"];
            unitCode: everest_appserver_primitive_Text;
            quantity: everest_appserver_primitive_Decimal;
            allocatedQuantity: everest_appserver_primitive_Decimal;
            openAllocatedQuantity: everest_appserver_primitive_Decimal;
            openQuantity: everest_appserver_primitive_Decimal;
            backorderedQuantity: everest_appserver_primitive_Decimal;
            pickedQuantity: everest_appserver_primitive_Decimal;
            shippedQuantity: everest_appserver_primitive_Decimal;
            fulfillmentStatus: everest_appserver_primitive_Text;
            actualShipDate: everest_appserver_primitive_PlainDate;
            trackingNumber: everest_appserver_primitive_Text;
            salesOrderProductLineId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["salesOrderProductLineId"];
          }[] | null | undefined;
        };

        export interface Request {
          setId(id: number): this;

          setData(data: Data): this;

          execute(): Promise<Response>;
        }

        export type Response = Record<string, never>;
      }

      export namespace Delete {
        export interface Request {
          setId(id: number): this;

          execute(): Promise<Response>;
        }

        export type Response = Record<string, never>;
      }
    }
  }

  export namespace Actions {
    export namespace AllocateInventory {
      export namespace Execute {
        export type Input = {
          salesOrderId: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["id"];
          salesOrderLineId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["id"];
          quantity: everest_appserver_primitive_Decimal;
          expirationDate: everest_appserver_primitive_Date;
          locationId: everest_inventory_model_node_IMWarehouseLocation.IMWarehouseLocation["id"];
        };

        export type Output = {
          success: everest_appserver_primitive_TrueFalse;
          reservationId: everest_appserver_primitive_Number;
          reservationNumber: everest_appserver_primitive_Text;
          allocatedQuantity: everest_appserver_primitive_Decimal;
          shortageQuantity: everest_appserver_primitive_Decimal;
          errorMessage: everest_appserver_primitive_Text;
          salesOrderId: everest_appserver_primitive_ID;
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace ReleaseAllocation {
      export namespace Execute {
        export type Input = {
          reservationId: everest_appserver_primitive_Number;
        };

        export type Output = {
          success: everest_appserver_primitive_TrueFalse;
          errorMessage: everest_appserver_primitive_Text;
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace ProcessBackorders {
      export namespace Execute {
        export type Input = {
          salesOrderId: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["id"];
          itemId: everest_inventory_model_node_ItemMaster.ItemMaster["id"];
          locationId: everest_inventory_model_node_IMWarehouseLocation.IMWarehouseLocation["id"];
          expirationDate: everest_appserver_primitive_PlainDate;
          prioritizeByOrderDate: everest_appserver_primitive_TrueFalse;
          prioritizeByCustomerPriority: everest_appserver_primitive_TrueFalse;
          prioritizeByQuantity: everest_appserver_primitive_TrueFalse;
        };

        export type Output = {
          success: everest_appserver_primitive_TrueFalse;
          processedBackorders: everest_appserver_primitive_Number;
          totalAllocatedQuantity: everest_appserver_primitive_Decimal;
          remainingBackorders: everest_appserver_primitive_Number;
          results: {
            salesOrderId: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["id"];
            salesOrderLineId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["id"];
            processedQuantity: everest_appserver_primitive_Decimal;
            remainingBackorderedQuantity: everest_appserver_primitive_Decimal;
            success: everest_appserver_primitive_TrueFalse;
            errorMessage: everest_appserver_primitive_Text;
          }[];
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace CreatePickList {
      export namespace Execute {
        export type Input = {
          salesOrderId: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["id"];
          locationId: everest_inventory_model_node_IMWarehouseLocation.IMWarehouseLocation["id"];
          assignedUserId: everest_inventory_model_node_IMPickList.IMPickList["assignedPickerId"];
          pickDate: everest_appserver_primitive_Date;
          priority: everest_appserver_primitive_Number;
          notes: everest_appserver_primitive_Text;
          expectedCompletionDate: everest_appserver_primitive_Date;
        };

        export type Output = {
          success: everest_appserver_primitive_TrueFalse;
          pickListId: everest_inventory_model_node_IMPickList.IMPickList["id"];
          pickListNumber: everest_appserver_primitive_Text;
          salesOrderId: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["id"];
          warnings: everest_appserver_primitive_Text[];
          errors: everest_appserver_primitive_Text[];
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace CreateSalesDelivery {
      export namespace Execute {
        export type Input = {
          deliveryDate: everest_appserver_primitive_Date;
          notes: everest_appserver_primitive_Text;
          lines: {
            packageLineId: everest_inventory_model_node_IMPackageLine.IMPackageLine["id"];
            salesOrderLineId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["id"];
            itemId: everest_inventory_model_node_ItemMaster.ItemMaster["id"];
            quantity: everest_appserver_primitive_Decimal;
            unitId: everest_inventory_model_node_UnitOfMeasure.UnitOfMeasure["id"];
            unitCode: everest_appserver_primitive_Text;
            status: everest_appserver_primitive_Text;
          }[];
        };

        export type Output = {
          success: everest_appserver_primitive_TrueFalse;
          deliveryId: everest_inventory_model_node_IMInventorySalesDelivery.IMInventorySalesDelivery["id"];
          deliveryNumber: everest_appserver_primitive_Text;
          salesOrderId: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["id"];
          warnings: everest_appserver_primitive_Text[];
          errors: everest_appserver_primitive_Text[];
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace CreateSalesDeliveryFromPickList {
      export namespace Execute {
        export type Input = {
          deliveryDate: everest_appserver_primitive_Date;
          notes: everest_appserver_primitive_Text;
          lines: {
            pickListLineId: everest_inventory_model_node_IMPickListLine.IMPickListLine["id"];
            salesOrderLineId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["id"];
            itemId: everest_inventory_model_node_ItemMaster.ItemMaster["id"];
            quantity: everest_appserver_primitive_Decimal;
            unitId: everest_inventory_model_node_UnitOfMeasure.UnitOfMeasure["id"];
            unitCode: everest_appserver_primitive_Text;
            status: everest_appserver_primitive_Text;
          }[];
        };

        export type Output = {
          success: everest_appserver_primitive_TrueFalse;
          deliveryId: everest_inventory_model_node_IMInventorySalesDelivery.IMInventorySalesDelivery["id"];
          deliveryNumber: everest_appserver_primitive_Text;
          salesOrderId: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["id"];
          warnings: everest_appserver_primitive_Text[];
          errors: everest_appserver_primitive_Text[];
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace CreateSalesOrder {
      export namespace Execute {
        export type Input = {
          externalId: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["externalId"];
          active: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["active"];
          carrier: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["carrier"];
          plannedShipDate: everest_appserver_primitive_PlainDate;
          requestedDeliveryDate: everest_appserver_primitive_PlainDate;
          salesOrderId: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["salesOrderId"];
          shippingAddressId: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["shippingAddressId"];
          businessPartnerId: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["businessPartnerId"];
          shippingMemo: everest_appserver_primitive_Text;
          shippingMethod: everest_appserver_primitive_Text;
          documentNumber: everest_appserver_primitive_Text;
          lines: {
            itemId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["itemId"];
            locationId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["locationId"];
            unitId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["unitId"];
            quantity: everest_appserver_primitive_Decimal;
            salesOrderProductLineId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["salesOrderProductLineId"];
          }[];
        };

        export type Output = {
          success: everest_appserver_primitive_TrueFalse;
          salesOrderId: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["id"];
          documentNumber: everest_appserver_primitive_Text;
          errorMessage: everest_appserver_primitive_Text;
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace UpdateSalesOrder {
      export namespace Execute {
        export type Input = {
          id: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["id"];
          externalId: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["externalId"];
          active: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["active"];
          carrier: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["carrier"];
          plannedShipDate: everest_appserver_primitive_PlainDate;
          requestedDeliveryDate: everest_appserver_primitive_PlainDate;
          salesOrderId: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["salesOrderId"];
          shippingAddressId: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["shippingAddressId"];
          businessPartnerId: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["businessPartnerId"];
          shippingMemo: everest_appserver_primitive_Text;
          shippingMethod: everest_appserver_primitive_Text;
          lines: {
            id: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["id"];
            itemId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["itemId"];
            locationId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["locationId"];
            unitId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["unitId"];
            quantity: everest_appserver_primitive_Decimal;
            salesOrderProductLineId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["salesOrderProductLineId"];
          }[];
          linesToRemove: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["id"][];
        };

        export type Output = {
          success: everest_appserver_primitive_TrueFalse;
          salesOrderId: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["id"];
          documentNumber: everest_appserver_primitive_Text;
          errorMessage: everest_appserver_primitive_Text;
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }
  }

  export namespace Functions {
    export namespace GetCustomerById {
      export namespace Execute {
        export type Input = {
          customerId: everest_appserver_primitive_ID;
        };

        export type Output = {
          customer: {
            id: everest_appserver_primitive_ID;
            name: everest_appserver_primitive_Text;
            customerNumber: everest_appserver_primitive_Text;
            email: everest_appserver_primitive_Text;
            phone: everest_appserver_primitive_Text;
            address: everest_appserver_primitive_Text;
            city: everest_appserver_primitive_Text;
            stateProvince: everest_appserver_primitive_Text;
            postalCode: everest_appserver_primitive_Text;
            country: everest_appserver_primitive_Text;
            customerType: everest_fin_accounting_model_node_Customer.Customer["customerType"];
            status: everest_fin_accounting_model_node_Customer.Customer["status"];
            createdDate: everest_appserver_primitive_Date;
            lastModifiedDate: everest_appserver_primitive_Date;
          };
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace GetSalesOrderWithLines {
      export namespace Execute {
        export type Input = {
          salesOrderId: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["id"];
        };

        export type Output = {
          salesOrder: {
            id: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["id"];
            uuid: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["uuid"];
            orderDate: everest_appserver_primitive_PlainDate;
            documentNumber: everest_appserver_primitive_Text;
            customerName: everest_appserver_primitive_Text;
            externalId: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["externalId"];
            active: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["active"];
            carrier: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["carrier"];
            fulfillmentStatus: everest_appserver_primitive_Text;
            plannedShipDate: everest_appserver_primitive_PlainDate;
            requestedDeliveryDate: everest_appserver_primitive_PlainDate;
            salesOrderId: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["salesOrderId"];
            shippingAddressId: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["shippingAddressId"];
            businessPartnerId: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["businessPartnerId"];
            documentDate: everest_appserver_primitive_PlainDate;
            status: everest_appserver_primitive_Text;
            notes: everest_appserver_primitive_Text;
            shippingMemo: everest_appserver_primitive_Text;
            shippingMethod: everest_appserver_primitive_Text;
            createdDate: everest_appserver_primitive_Date;
            createdBy: everest_appserver_primitive_Text;
            lastModifiedDate: everest_appserver_primitive_Date;
            lastModifiedBy: everest_appserver_primitive_Text;
            lines: {
              id: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["id"];
              headerId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["headerId"];
              itemId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["itemId"];
              itemCode: everest_appserver_primitive_Text;
              itemName: everest_appserver_primitive_Text;
              locationId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["locationId"];
              locationName: everest_appserver_primitive_Text;
              unitId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["unitId"];
              unitCode: everest_appserver_primitive_Text;
              unitPrice: everest_appserver_primitive_Decimal;
              notes: everest_appserver_primitive_Text;
              quantity: everest_appserver_primitive_Decimal;
              allocatedQuantity: everest_appserver_primitive_Decimal;
              openAllocatedQuantity: everest_appserver_primitive_Decimal;
              openQuantity: everest_appserver_primitive_Decimal;
              backorderedQuantity: everest_appserver_primitive_Decimal;
              pickedQuantity: everest_appserver_primitive_Decimal;
              shippedQuantity: everest_appserver_primitive_Decimal;
              fulfillmentStatus: everest_appserver_primitive_Text;
              actualShipDate: everest_appserver_primitive_PlainDate;
              trackingNumber: everest_appserver_primitive_Text;
              salesOrderProductLineId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["salesOrderProductLineId"];
            }[];
          };
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace GetSalesOrderLinesForDelivery {
      export namespace Execute {
        export type Input = {
          salesOrderId: everest_inventory_model_node_IMSalesOrder.IMSalesOrder["id"];
        };

        export type Output = {
          lines: {
            id: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["id"];
            salesOrderProductLineId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["salesOrderProductLineId"];
            itemId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["itemId"];
            itemCode: everest_appserver_primitive_Text;
            itemName: everest_appserver_primitive_Text;
            unitId: everest_inventory_model_node_IMSalesOrderLine.IMSalesOrderLine["unitId"];
            unitCode: everest_appserver_primitive_Text;
            quantity: everest_appserver_primitive_Decimal;
            shippedQuantity: everest_appserver_primitive_Decimal;
            fulfillmentStatus: everest_appserver_primitive_Text;
          }[];
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }
  }

  export namespace Permission {
    export namespace Check {
      export type Result = {
        dataSets: {
          Customers: {
            isGetPermitted: boolean;
          };
          SalesOrders: {
            isGetPermitted: boolean;
            isPostPermitted: boolean;
            isPatchPermitted: boolean;
            isDeletePermitted: boolean;
          };
        };
        actions: {
          AllocateInventory: {
            isExecutePermitted: boolean;
          };
          CreatePickList: {
            isExecutePermitted: boolean;
          };
          CreateSalesDelivery: {
            isExecutePermitted: boolean;
          };
          CreateSalesDeliveryFromPickList: {
            isExecutePermitted: boolean;
          };
          CreateSalesOrder: {
            isExecutePermitted: boolean;
          };
          GetCustomerById: {
            isExecutePermitted: boolean;
          };
          GetSalesOrderLinesForDelivery: {
            isExecutePermitted: boolean;
          };
          GetSalesOrderWithLines: {
            isExecutePermitted: boolean;
          };
          ProcessBackorders: {
            isExecutePermitted: boolean;
          };
          ReleaseAllocation: {
            isExecutePermitted: boolean;
          };
          UpdateSalesOrder: {
            isExecutePermitted: boolean;
          };
        };
      };

      export interface Request {
        execute(): Promise<Response>;
      }

      export type Response = {
        result: Result;
      };
    }
  }

  export namespace Api {
    export type EntitySets = {
      Customers: {
        get: EntitySets.Customers.Get.Request;
      };
      SalesOrders: {
        get: EntitySets.SalesOrders.Get.Request;
        post: EntitySets.SalesOrders.Post.Request;
        patch: EntitySets.SalesOrders.Patch.Request;
        delete: EntitySets.SalesOrders.Delete.Request;
      };
    };

    export type Actions = {
      AllocateInventory: {
        execute: Actions.AllocateInventory.Execute.Request;
      };
      ReleaseAllocation: {
        execute: Actions.ReleaseAllocation.Execute.Request;
      };
      ProcessBackorders: {
        execute: Actions.ProcessBackorders.Execute.Request;
      };
      CreatePickList: {
        execute: Actions.CreatePickList.Execute.Request;
      };
      CreateSalesDelivery: {
        execute: Actions.CreateSalesDelivery.Execute.Request;
      };
      CreateSalesDeliveryFromPickList: {
        execute: Actions.CreateSalesDeliveryFromPickList.Execute.Request;
      };
      CreateSalesOrder: {
        execute: Actions.CreateSalesOrder.Execute.Request;
      };
      UpdateSalesOrder: {
        execute: Actions.UpdateSalesOrder.Execute.Request;
      };
    };

    export type Functions = {
      GetCustomerById: {
        execute: Functions.GetCustomerById.Execute.Request;
      };
      GetSalesOrderWithLines: {
        execute: Functions.GetSalesOrderWithLines.Execute.Request;
      };
      GetSalesOrderLinesForDelivery: {
        execute: Functions.GetSalesOrderLinesForDelivery.Execute.Request;
      };
    };

    export interface Client {
      createEntitySetGetRequest<T extends 'Customers' | 'SalesOrders'>(entitySetName: T): Api.EntitySets[T]['get'];

      createEntitySetPostRequest<T extends 'SalesOrders'>(entitySetName: T): Api.EntitySets[T]['post'];

      createEntitySetPatchRequest<T extends 'SalesOrders'>(entitySetName: T): Api.EntitySets[T]['patch'];

      createEntitySetDeleteRequest<T extends 'SalesOrders'>(entitySetName: T): Api.EntitySets[T]['delete'];

      createActionExecuteRequest<T extends 'AllocateInventory' | 'ReleaseAllocation' | 'ProcessBackorders' | 'CreatePickList' | 'CreateSalesDelivery' | 'CreateSalesDeliveryFromPickList' | 'CreateSalesOrder' | 'UpdateSalesOrder'>(actionName: T): Api.Actions[T]['execute'];

      createFunctionExecuteRequest<T extends 'GetCustomerById' | 'GetSalesOrderWithLines' | 'GetSalesOrderLinesForDelivery'>(functionName: T): Api.Functions[T]['execute'];

      createPermissionCheckRequest(): Permission.Check.Request;
    }
  }

  export const client = createOdataClientRuntime<Api.Client>('everest.inventory/services/salesOrder/SalesOrderService');
}

