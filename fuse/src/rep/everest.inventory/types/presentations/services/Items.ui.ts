/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

import type { ItemMaster as everest_inventory_model_node_ItemMaster } from "@pkg/everest.inventory/types/ItemMaster";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { ItemSupplier as everest_inventory_model_node_ItemSupplier } from "@pkg/everest.inventory/types/ItemSupplier";
import type { ItemBarcode as everest_inventory_model_node_ItemBarcode } from "@pkg/everest.inventory/types/ItemBarcode";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { IMInventoryLevel as everest_inventory_model_node_IMInventoryLevel } from "@pkg/everest.inventory/types/IMInventoryLevel";
import type { EvstDecimal as everest_appserver_primitive_Decimal } from "@pkg/everest.appserver/types/primitives/Decimal";

import { createOdataClientRuntime } from '@everestsystems/content-core/lib/runtime/loader';

/**
 * Generated frontend types for OData presentation Items.
 */
export namespace ItemsUI {
  export namespace EntitySets {
    export namespace Items {
      export namespace Get {
        export type Entity = {
          id?: everest_inventory_model_node_ItemMaster.ItemMaster["id"] | undefined;
          itemCode?: everest_inventory_model_node_ItemMaster.ItemMaster["itemCode"] | undefined;
          itemName?: everest_inventory_model_node_ItemMaster.ItemMaster["itemName"] | undefined;
          itemType?: everest_inventory_model_node_ItemMaster.ItemMaster["itemType"] | undefined;
          status?: everest_inventory_model_node_ItemMaster.ItemMaster["status"] | undefined;
          categoryId?: everest_inventory_model_node_ItemMaster.ItemMaster["categoryId"] | undefined;
          unitGroupId?: everest_inventory_model_node_ItemMaster.ItemMaster["unitGroupId"] | undefined;
          inventoryUoMId?: everest_inventory_model_node_ItemMaster.ItemMaster["inventoryUoMId"] | undefined;
          salesUoMId?: everest_inventory_model_node_ItemMaster.ItemMaster["salesUoMId"] | undefined;
          purchaseUoMId?: everest_inventory_model_node_ItemMaster.ItemMaster["purchaseUoMId"] | undefined;
          valuationMethod?: everest_inventory_model_node_ItemMaster.ItemMaster["valuationMethod"] | undefined;
          standardCost?: everest_inventory_model_node_ItemMaster.ItemMaster["standardCost"] | undefined;
          isBatch?: everest_inventory_model_node_ItemMaster.ItemMaster["isBatch"] | undefined;
          isSerial?: everest_inventory_model_node_ItemMaster.ItemMaster["isSerial"] | undefined;
          isPerishable?: everest_inventory_model_node_ItemMaster.ItemMaster["isPerishable"] | undefined;
          shelfLifeDays?: everest_inventory_model_node_ItemMaster.ItemMaster["shelfLifeDays"] | undefined;
          primarySupplierId?: everest_inventory_model_node_ItemMaster.ItemMaster["primarySupplierId"] | undefined;
          primarySupplierSku?: everest_inventory_model_node_ItemMaster.ItemMaster["primarySupplierSku"] | undefined;
          primarySupplierLeadTimeDays?: everest_inventory_model_node_ItemMaster.ItemMaster["primarySupplierLeadTimeDays"] | undefined;
          notes?: everest_inventory_model_node_ItemMaster.ItemMaster["notes"] | undefined;
          customAttributes?: everest_inventory_model_node_ItemMaster.ItemMaster["customAttributes"] | undefined;
          parentItemId?: everest_inventory_model_node_ItemMaster.ItemMaster["parentItemId"] | undefined;
          variantAttributes?: everest_inventory_model_node_ItemMaster.ItemMaster["variantAttributes"] | undefined;
          countryOfOrigin?: everest_inventory_model_node_ItemMaster.ItemMaster["countryOfOrigin"] | undefined;
          estimatedCarbonFootprintKg?: everest_inventory_model_node_ItemMaster.ItemMaster["estimatedCarbonFootprintKg"] | undefined;
          sustainabilityCertifications?: everest_inventory_model_node_ItemMaster.ItemMaster["sustainabilityCertifications"] | undefined;
        };

        export type Filter =
          | PropertyFilter
          | [Filter, 'and', Filter]
          | [Filter, 'or', Filter];

        /**
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), filters must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to filter by its amount component.
         */
        export type PropertyFilter = {
          property: string;
          comparator: 'eq' | 'ne' | 'lt' | 'le' | 'gt' | 'ge' | 'contains';
          value: unknown;
        };

        /**
         * Specifies sorting for a property in ascending or descending order.
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), sorting must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to sort by its amount component.
         */
        export type OrderbyItem = {
          property: string;
          ordering: 'asc' | 'desc';
        };

        export interface Request {
          setFilter(filter: Filter): this;

          setOrderby(orderby: ReadonlyArray<OrderbyItem>): this;

          setSkip(skip: number): this;

          setTop(top: number): this;

          setSelect(select: ReadonlyArray<keyof Entity>): this;

          setCount(count: boolean): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entities: Entity[];
          count?: number | undefined;
        };
      }

      export namespace Post {
        export type Data = {
          itemCode?: everest_inventory_model_node_ItemMaster.ItemMaster["itemCode"] | undefined;
          itemName?: everest_inventory_model_node_ItemMaster.ItemMaster["itemName"] | undefined;
          itemType?: everest_inventory_model_node_ItemMaster.ItemMaster["itemType"] | undefined;
          status?: everest_inventory_model_node_ItemMaster.ItemMaster["status"] | undefined;
          categoryId?: everest_inventory_model_node_ItemMaster.ItemMaster["categoryId"] | undefined;
          unitGroupId?: everest_inventory_model_node_ItemMaster.ItemMaster["unitGroupId"] | undefined;
          inventoryUoMId?: everest_inventory_model_node_ItemMaster.ItemMaster["inventoryUoMId"] | undefined;
          salesUoMId?: everest_inventory_model_node_ItemMaster.ItemMaster["salesUoMId"] | undefined;
          purchaseUoMId?: everest_inventory_model_node_ItemMaster.ItemMaster["purchaseUoMId"] | undefined;
          valuationMethod?: everest_inventory_model_node_ItemMaster.ItemMaster["valuationMethod"] | undefined;
          standardCost?: everest_inventory_model_node_ItemMaster.ItemMaster["standardCost"] | undefined;
          isBatch?: everest_inventory_model_node_ItemMaster.ItemMaster["isBatch"] | undefined;
          isSerial?: everest_inventory_model_node_ItemMaster.ItemMaster["isSerial"] | undefined;
          isPerishable?: everest_inventory_model_node_ItemMaster.ItemMaster["isPerishable"] | undefined;
          shelfLifeDays?: everest_inventory_model_node_ItemMaster.ItemMaster["shelfLifeDays"] | undefined;
          primarySupplierId?: everest_inventory_model_node_ItemMaster.ItemMaster["primarySupplierId"] | undefined;
          primarySupplierSku?: everest_inventory_model_node_ItemMaster.ItemMaster["primarySupplierSku"] | undefined;
          primarySupplierLeadTimeDays?: everest_inventory_model_node_ItemMaster.ItemMaster["primarySupplierLeadTimeDays"] | undefined;
          notes?: everest_inventory_model_node_ItemMaster.ItemMaster["notes"] | undefined;
          customAttributes?: everest_inventory_model_node_ItemMaster.ItemMaster["customAttributes"] | undefined;
          parentItemId?: everest_inventory_model_node_ItemMaster.ItemMaster["parentItemId"] | undefined;
          variantAttributes?: everest_inventory_model_node_ItemMaster.ItemMaster["variantAttributes"] | undefined;
          countryOfOrigin?: everest_inventory_model_node_ItemMaster.ItemMaster["countryOfOrigin"] | undefined;
          estimatedCarbonFootprintKg?: everest_inventory_model_node_ItemMaster.ItemMaster["estimatedCarbonFootprintKg"] | undefined;
          sustainabilityCertifications?: everest_inventory_model_node_ItemMaster.ItemMaster["sustainabilityCertifications"] | undefined;
        };

        export type Entity = {
          id: everest_inventory_model_node_ItemMaster.ItemMaster["id"];
          itemCode: everest_inventory_model_node_ItemMaster.ItemMaster["itemCode"];
          itemName?: everest_inventory_model_node_ItemMaster.ItemMaster["itemName"] | undefined;
          itemType?: everest_inventory_model_node_ItemMaster.ItemMaster["itemType"] | undefined;
          status?: everest_inventory_model_node_ItemMaster.ItemMaster["status"] | undefined;
          categoryId: everest_inventory_model_node_ItemMaster.ItemMaster["categoryId"];
          unitGroupId: everest_inventory_model_node_ItemMaster.ItemMaster["unitGroupId"];
          inventoryUoMId?: everest_inventory_model_node_ItemMaster.ItemMaster["inventoryUoMId"] | undefined;
          salesUoMId?: everest_inventory_model_node_ItemMaster.ItemMaster["salesUoMId"] | undefined;
          purchaseUoMId?: everest_inventory_model_node_ItemMaster.ItemMaster["purchaseUoMId"] | undefined;
          valuationMethod?: everest_inventory_model_node_ItemMaster.ItemMaster["valuationMethod"] | undefined;
          standardCost?: everest_inventory_model_node_ItemMaster.ItemMaster["standardCost"] | undefined;
          isBatch?: everest_inventory_model_node_ItemMaster.ItemMaster["isBatch"] | undefined;
          isSerial?: everest_inventory_model_node_ItemMaster.ItemMaster["isSerial"] | undefined;
          isPerishable?: everest_inventory_model_node_ItemMaster.ItemMaster["isPerishable"] | undefined;
          shelfLifeDays?: everest_inventory_model_node_ItemMaster.ItemMaster["shelfLifeDays"] | undefined;
          primarySupplierId?: everest_inventory_model_node_ItemMaster.ItemMaster["primarySupplierId"] | undefined;
          primarySupplierSku?: everest_inventory_model_node_ItemMaster.ItemMaster["primarySupplierSku"] | undefined;
          primarySupplierLeadTimeDays?: everest_inventory_model_node_ItemMaster.ItemMaster["primarySupplierLeadTimeDays"] | undefined;
          notes?: everest_inventory_model_node_ItemMaster.ItemMaster["notes"] | undefined;
          customAttributes?: everest_inventory_model_node_ItemMaster.ItemMaster["customAttributes"] | undefined;
          parentItemId?: everest_inventory_model_node_ItemMaster.ItemMaster["parentItemId"] | undefined;
          variantAttributes?: everest_inventory_model_node_ItemMaster.ItemMaster["variantAttributes"] | undefined;
          countryOfOrigin?: everest_inventory_model_node_ItemMaster.ItemMaster["countryOfOrigin"] | undefined;
          estimatedCarbonFootprintKg?: everest_inventory_model_node_ItemMaster.ItemMaster["estimatedCarbonFootprintKg"] | undefined;
          sustainabilityCertifications?: everest_inventory_model_node_ItemMaster.ItemMaster["sustainabilityCertifications"] | undefined;
        };

        export interface Request {
          setData(data: Data): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entity: Entity;
        };
      }

      export namespace Patch {
        export type Data = {
          itemCode?: everest_inventory_model_node_ItemMaster.ItemMaster["itemCode"] | null | undefined;
          itemName?: everest_inventory_model_node_ItemMaster.ItemMaster["itemName"] | null | undefined;
          itemType?: everest_inventory_model_node_ItemMaster.ItemMaster["itemType"] | null | undefined;
          status?: everest_inventory_model_node_ItemMaster.ItemMaster["status"] | null | undefined;
          categoryId?: everest_inventory_model_node_ItemMaster.ItemMaster["categoryId"] | null | undefined;
          unitGroupId?: everest_inventory_model_node_ItemMaster.ItemMaster["unitGroupId"] | null | undefined;
          inventoryUoMId?: everest_inventory_model_node_ItemMaster.ItemMaster["inventoryUoMId"] | null | undefined;
          salesUoMId?: everest_inventory_model_node_ItemMaster.ItemMaster["salesUoMId"] | null | undefined;
          purchaseUoMId?: everest_inventory_model_node_ItemMaster.ItemMaster["purchaseUoMId"] | null | undefined;
          valuationMethod?: everest_inventory_model_node_ItemMaster.ItemMaster["valuationMethod"] | null | undefined;
          standardCost?: everest_inventory_model_node_ItemMaster.ItemMaster["standardCost"] | null | undefined;
          isBatch?: everest_inventory_model_node_ItemMaster.ItemMaster["isBatch"] | null | undefined;
          isSerial?: everest_inventory_model_node_ItemMaster.ItemMaster["isSerial"] | null | undefined;
          isPerishable?: everest_inventory_model_node_ItemMaster.ItemMaster["isPerishable"] | null | undefined;
          shelfLifeDays?: everest_inventory_model_node_ItemMaster.ItemMaster["shelfLifeDays"] | null | undefined;
          primarySupplierId?: everest_inventory_model_node_ItemMaster.ItemMaster["primarySupplierId"] | null | undefined;
          primarySupplierSku?: everest_inventory_model_node_ItemMaster.ItemMaster["primarySupplierSku"] | null | undefined;
          primarySupplierLeadTimeDays?: everest_inventory_model_node_ItemMaster.ItemMaster["primarySupplierLeadTimeDays"] | null | undefined;
          notes?: everest_inventory_model_node_ItemMaster.ItemMaster["notes"] | null | undefined;
          customAttributes?: everest_inventory_model_node_ItemMaster.ItemMaster["customAttributes"] | null | undefined;
          parentItemId?: everest_inventory_model_node_ItemMaster.ItemMaster["parentItemId"] | null | undefined;
          variantAttributes?: everest_inventory_model_node_ItemMaster.ItemMaster["variantAttributes"] | null | undefined;
          countryOfOrigin?: everest_inventory_model_node_ItemMaster.ItemMaster["countryOfOrigin"] | null | undefined;
          estimatedCarbonFootprintKg?: everest_inventory_model_node_ItemMaster.ItemMaster["estimatedCarbonFootprintKg"] | null | undefined;
          sustainabilityCertifications?: everest_inventory_model_node_ItemMaster.ItemMaster["sustainabilityCertifications"] | null | undefined;
        };

        export interface Request {
          setId(id: number): this;

          setData(data: Data): this;

          execute(): Promise<Response>;
        }

        export type Response = Record<string, never>;
      }

      export namespace Delete {
        export interface Request {
          setId(id: number): this;

          execute(): Promise<Response>;
        }

        export type Response = Record<string, never>;
      }
    }
  }

  export namespace Actions {
    export namespace ImportItems {
      export namespace Execute {
        export type Input = {
          fileContent: everest_appserver_primitive_Text;
          fileFormat: everest_appserver_primitive_Text;
        };

        export type Output = {
          jobId: everest_appserver_primitive_Text;
          status: everest_appserver_primitive_Text;
          message: everest_appserver_primitive_Text;
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace ExportItems {
      export namespace Execute {
        export type Input = {
          fileFormat: everest_appserver_primitive_Text;
          filter: everest_appserver_primitive_Text;
        };

        export type Output = {
          jobId: everest_appserver_primitive_Text;
          status: everest_appserver_primitive_Text;
          downloadUrl: everest_appserver_primitive_Text;
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace CreateItemWithDetails {
      export namespace Execute {
        export type Input = {
          item: {
            itemCode: everest_inventory_model_node_ItemMaster.ItemMaster["itemCode"];
            itemName: everest_inventory_model_node_ItemMaster.ItemMaster["itemName"];
            itemType: everest_inventory_model_node_ItemMaster.ItemMaster["itemType"];
            status: everest_inventory_model_node_ItemMaster.ItemMaster["status"];
            categoryId: everest_inventory_model_node_ItemMaster.ItemMaster["categoryId"];
            unitGroupId: everest_inventory_model_node_ItemMaster.ItemMaster["unitGroupId"];
            inventoryUoMId: everest_inventory_model_node_ItemMaster.ItemMaster["inventoryUoMId"];
            salesUoMId: everest_inventory_model_node_ItemMaster.ItemMaster["salesUoMId"];
            purchaseUoMId: everest_inventory_model_node_ItemMaster.ItemMaster["purchaseUoMId"];
            productId: everest_inventory_model_node_ItemMaster.ItemMaster["productId"];
            valuationMethod: everest_inventory_model_node_ItemMaster.ItemMaster["valuationMethod"];
            standardCost: everest_inventory_model_node_ItemMaster.ItemMaster["standardCost"];
            isBatch: everest_inventory_model_node_ItemMaster.ItemMaster["isBatch"];
            isSerial: everest_inventory_model_node_ItemMaster.ItemMaster["isSerial"];
            isPerishable: everest_inventory_model_node_ItemMaster.ItemMaster["isPerishable"];
            shelfLifeDays: everest_inventory_model_node_ItemMaster.ItemMaster["shelfLifeDays"];
            notes: everest_inventory_model_node_ItemMaster.ItemMaster["notes"];
            customAttributes: everest_inventory_model_node_ItemMaster.ItemMaster["customAttributes"];
            parentItemId: everest_inventory_model_node_ItemMaster.ItemMaster["parentItemId"];
            variantAttributes: everest_inventory_model_node_ItemMaster.ItemMaster["variantAttributes"];
            countryOfOrigin: everest_inventory_model_node_ItemMaster.ItemMaster["countryOfOrigin"];
            estimatedCarbonFootprintKg: everest_inventory_model_node_ItemMaster.ItemMaster["estimatedCarbonFootprintKg"];
            sustainabilityCertifications: everest_inventory_model_node_ItemMaster.ItemMaster["sustainabilityCertifications"];
          };
          suppliers: {
            supplierId: everest_inventory_model_node_ItemSupplier.ItemSupplier["supplierId"];
            supplierSku: everest_inventory_model_node_ItemSupplier.ItemSupplier["supplierSku"];
            isPreferred: everest_inventory_model_node_ItemSupplier.ItemSupplier["isPreferred"];
            lastPurchasePrice: everest_inventory_model_node_ItemSupplier.ItemSupplier["lastPurchasePrice"];
            supplierLeadTimeDays: everest_inventory_model_node_ItemSupplier.ItemSupplier["supplierLeadTimeDays"];
          }[];
          barcodes: {
            barcodeType: everest_inventory_model_node_ItemBarcode.ItemBarcode["barcodeType"];
            barcodeValue: everest_inventory_model_node_ItemBarcode.ItemBarcode["barcodeValue"];
            uomId: everest_inventory_model_node_ItemBarcode.ItemBarcode["uomId"];
          }[];
        };

        export type Output = {
          itemId: everest_inventory_model_node_ItemMaster.ItemMaster["id"];
          supplierIds: everest_appserver_primitive_Number[];
          barcodeIds: everest_appserver_primitive_Number[];
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace UpdateItemWithDetails {
      export namespace Execute {
        export type Input = {
          item: {
            id: everest_inventory_model_node_ItemMaster.ItemMaster["id"];
            itemCode: everest_inventory_model_node_ItemMaster.ItemMaster["itemCode"];
            itemName: everest_inventory_model_node_ItemMaster.ItemMaster["itemName"];
            itemType: everest_inventory_model_node_ItemMaster.ItemMaster["itemType"];
            status: everest_inventory_model_node_ItemMaster.ItemMaster["status"];
            categoryId: everest_inventory_model_node_ItemMaster.ItemMaster["categoryId"];
            unitGroupId: everest_inventory_model_node_ItemMaster.ItemMaster["unitGroupId"];
            inventoryUoMId: everest_inventory_model_node_ItemMaster.ItemMaster["inventoryUoMId"];
            salesUoMId: everest_inventory_model_node_ItemMaster.ItemMaster["salesUoMId"];
            purchaseUoMId: everest_inventory_model_node_ItemMaster.ItemMaster["purchaseUoMId"];
            valuationMethod: everest_inventory_model_node_ItemMaster.ItemMaster["valuationMethod"];
            standardCost: everest_inventory_model_node_ItemMaster.ItemMaster["standardCost"];
            isBatch: everest_inventory_model_node_ItemMaster.ItemMaster["isBatch"];
            isSerial: everest_inventory_model_node_ItemMaster.ItemMaster["isSerial"];
            isPerishable: everest_inventory_model_node_ItemMaster.ItemMaster["isPerishable"];
            shelfLifeDays: everest_inventory_model_node_ItemMaster.ItemMaster["shelfLifeDays"];
            notes: everest_inventory_model_node_ItemMaster.ItemMaster["notes"];
            customAttributes: everest_inventory_model_node_ItemMaster.ItemMaster["customAttributes"];
            parentItemId: everest_inventory_model_node_ItemMaster.ItemMaster["parentItemId"];
            variantAttributes: everest_inventory_model_node_ItemMaster.ItemMaster["variantAttributes"];
            countryOfOrigin: everest_inventory_model_node_ItemMaster.ItemMaster["countryOfOrigin"];
            estimatedCarbonFootprintKg: everest_inventory_model_node_ItemMaster.ItemMaster["estimatedCarbonFootprintKg"];
            sustainabilityCertifications: everest_inventory_model_node_ItemMaster.ItemMaster["sustainabilityCertifications"];
            productId: everest_inventory_model_node_ItemMaster.ItemMaster["productId"];
          };
          suppliers: {
            id: everest_inventory_model_node_ItemSupplier.ItemSupplier["id"];
            supplierId: everest_inventory_model_node_ItemSupplier.ItemSupplier["supplierId"];
            supplierSku: everest_inventory_model_node_ItemSupplier.ItemSupplier["supplierSku"];
            isPreferred: everest_inventory_model_node_ItemSupplier.ItemSupplier["isPreferred"];
            lastPurchasePrice: everest_inventory_model_node_ItemSupplier.ItemSupplier["lastPurchasePrice"];
            supplierLeadTimeDays: everest_inventory_model_node_ItemSupplier.ItemSupplier["supplierLeadTimeDays"];
          }[];
          newSuppliers: {
            supplierId: everest_inventory_model_node_ItemSupplier.ItemSupplier["supplierId"];
            supplierSku: everest_inventory_model_node_ItemSupplier.ItemSupplier["supplierSku"];
            isPreferred: everest_inventory_model_node_ItemSupplier.ItemSupplier["isPreferred"];
            lastPurchasePrice: everest_inventory_model_node_ItemSupplier.ItemSupplier["lastPurchasePrice"];
            supplierLeadTimeDays: everest_inventory_model_node_ItemSupplier.ItemSupplier["supplierLeadTimeDays"];
          }[];
          barcodes: {
            id: everest_inventory_model_node_ItemBarcode.ItemBarcode["id"];
            barcodeType: everest_inventory_model_node_ItemBarcode.ItemBarcode["barcodeType"];
            barcodeValue: everest_inventory_model_node_ItemBarcode.ItemBarcode["barcodeValue"];
            uomId: everest_inventory_model_node_ItemBarcode.ItemBarcode["uomId"];
          }[];
          newBarcodes: {
            barcodeType: everest_inventory_model_node_ItemBarcode.ItemBarcode["barcodeType"];
            barcodeValue: everest_inventory_model_node_ItemBarcode.ItemBarcode["barcodeValue"];
            uomId: everest_inventory_model_node_ItemBarcode.ItemBarcode["uomId"];
          }[];
          suppliersToDelete: everest_appserver_primitive_Number[];
          barcodesToDelete: everest_appserver_primitive_Number[];
        };

        export type Output = {
          success: everest_appserver_primitive_TrueFalse;
          message: everest_appserver_primitive_Text;
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace CreateMultipleItemsWithDetails {
      export namespace Execute {
        export type Input = {
          items: {
            itemCode: everest_inventory_model_node_ItemMaster.ItemMaster["itemCode"];
            itemName: everest_inventory_model_node_ItemMaster.ItemMaster["itemName"];
            itemType: everest_inventory_model_node_ItemMaster.ItemMaster["itemType"];
            status: everest_inventory_model_node_ItemMaster.ItemMaster["status"];
            categoryId: everest_inventory_model_node_ItemMaster.ItemMaster["categoryId"];
            unitGroupId: everest_inventory_model_node_ItemMaster.ItemMaster["unitGroupId"];
            inventoryUoMId: everest_inventory_model_node_ItemMaster.ItemMaster["inventoryUoMId"];
            salesUoMId: everest_inventory_model_node_ItemMaster.ItemMaster["salesUoMId"];
            purchaseUoMId: everest_inventory_model_node_ItemMaster.ItemMaster["purchaseUoMId"];
            valuationMethod: everest_inventory_model_node_ItemMaster.ItemMaster["valuationMethod"];
            standardCost: everest_inventory_model_node_ItemMaster.ItemMaster["standardCost"];
            isBatch: everest_inventory_model_node_ItemMaster.ItemMaster["isBatch"];
            isSerial: everest_inventory_model_node_ItemMaster.ItemMaster["isSerial"];
            isPerishable: everest_inventory_model_node_ItemMaster.ItemMaster["isPerishable"];
            shelfLifeDays: everest_inventory_model_node_ItemMaster.ItemMaster["shelfLifeDays"];
            notes: everest_inventory_model_node_ItemMaster.ItemMaster["notes"];
            customAttributes: everest_inventory_model_node_ItemMaster.ItemMaster["customAttributes"];
            parentItemId: everest_inventory_model_node_ItemMaster.ItemMaster["parentItemId"];
            variantAttributes: everest_inventory_model_node_ItemMaster.ItemMaster["variantAttributes"];
            countryOfOrigin: everest_inventory_model_node_ItemMaster.ItemMaster["countryOfOrigin"];
            estimatedCarbonFootprintKg: everest_inventory_model_node_ItemMaster.ItemMaster["estimatedCarbonFootprintKg"];
            sustainabilityCertifications: everest_inventory_model_node_ItemMaster.ItemMaster["sustainabilityCertifications"];
            suppliers: {
              supplierId: everest_inventory_model_node_ItemSupplier.ItemSupplier["supplierId"];
              supplierSku: everest_inventory_model_node_ItemSupplier.ItemSupplier["supplierSku"];
              isPreferred: everest_inventory_model_node_ItemSupplier.ItemSupplier["isPreferred"];
              lastPurchasePrice: everest_inventory_model_node_ItemSupplier.ItemSupplier["lastPurchasePrice"];
              supplierLeadTimeDays: everest_inventory_model_node_ItemSupplier.ItemSupplier["supplierLeadTimeDays"];
            }[];
            barcodes: {
              barcodeType: everest_inventory_model_node_ItemBarcode.ItemBarcode["barcodeType"];
              barcodeValue: everest_inventory_model_node_ItemBarcode.ItemBarcode["barcodeValue"];
              uomId: everest_inventory_model_node_ItemBarcode.ItemBarcode["uomId"];
            }[];
          }[];
        };

        export type Output = {
          results: {
            itemCode: everest_appserver_primitive_Text;
            success: everest_appserver_primitive_TrueFalse;
            itemId: everest_inventory_model_node_ItemMaster.ItemMaster["id"];
            supplierIds: everest_appserver_primitive_Number[];
            barcodeIds: everest_appserver_primitive_Number[];
            error: everest_appserver_primitive_Text;
            validationErrors: everest_appserver_primitive_Text[];
          }[];
          summary: {
            totalItems: everest_appserver_primitive_Number;
            successCount: everest_appserver_primitive_Number;
            errorCount: everest_appserver_primitive_Number;
            validationErrorCount: everest_appserver_primitive_Number;
          };
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }
  }

  export namespace Functions {
    export namespace ReadItemWithDetails {
      export namespace Execute {
        export type Input = {
          itemId: everest_inventory_model_node_ItemMaster.ItemMaster["id"];
        };

        export type Output = {
          item: {
            id: everest_inventory_model_node_ItemMaster.ItemMaster["id"];
            itemCode: everest_inventory_model_node_ItemMaster.ItemMaster["itemCode"];
            itemName: everest_inventory_model_node_ItemMaster.ItemMaster["itemName"];
            itemType: everest_inventory_model_node_ItemMaster.ItemMaster["itemType"];
            status: everest_inventory_model_node_ItemMaster.ItemMaster["status"];
            categoryId: everest_inventory_model_node_ItemMaster.ItemMaster["categoryId"];
            unitGroupId: everest_inventory_model_node_ItemMaster.ItemMaster["unitGroupId"];
            inventoryUoMId: everest_inventory_model_node_ItemMaster.ItemMaster["inventoryUoMId"];
            salesUoMId: everest_inventory_model_node_ItemMaster.ItemMaster["salesUoMId"];
            purchaseUoMId: everest_inventory_model_node_ItemMaster.ItemMaster["purchaseUoMId"];
            valuationMethod: everest_inventory_model_node_ItemMaster.ItemMaster["valuationMethod"];
            standardCost: everest_inventory_model_node_ItemMaster.ItemMaster["standardCost"];
            isBatch: everest_inventory_model_node_ItemMaster.ItemMaster["isBatch"];
            isSerial: everest_inventory_model_node_ItemMaster.ItemMaster["isSerial"];
            isPerishable: everest_inventory_model_node_ItemMaster.ItemMaster["isPerishable"];
            shelfLifeDays: everest_inventory_model_node_ItemMaster.ItemMaster["shelfLifeDays"];
            notes: everest_inventory_model_node_ItemMaster.ItemMaster["notes"];
            customAttributes: everest_inventory_model_node_ItemMaster.ItemMaster["customAttributes"];
            parentItemId: everest_inventory_model_node_ItemMaster.ItemMaster["parentItemId"];
            variantAttributes: everest_inventory_model_node_ItemMaster.ItemMaster["variantAttributes"];
            countryOfOrigin: everest_inventory_model_node_ItemMaster.ItemMaster["countryOfOrigin"];
            estimatedCarbonFootprintKg: everest_inventory_model_node_ItemMaster.ItemMaster["estimatedCarbonFootprintKg"];
            sustainabilityCertifications: everest_inventory_model_node_ItemMaster.ItemMaster["sustainabilityCertifications"];
            productId: everest_inventory_model_node_ItemMaster.ItemMaster["productId"];
          };
          suppliers: {
            id: everest_inventory_model_node_ItemSupplier.ItemSupplier["id"];
            supplierId: everest_inventory_model_node_ItemSupplier.ItemSupplier["supplierId"];
            supplierSku: everest_inventory_model_node_ItemSupplier.ItemSupplier["supplierSku"];
            isPreferred: everest_inventory_model_node_ItemSupplier.ItemSupplier["isPreferred"];
            lastPurchasePrice: everest_inventory_model_node_ItemSupplier.ItemSupplier["lastPurchasePrice"];
            supplierLeadTimeDays: everest_inventory_model_node_ItemSupplier.ItemSupplier["supplierLeadTimeDays"];
          }[];
          barcodes: {
            id: everest_inventory_model_node_ItemBarcode.ItemBarcode["id"];
            barcodeType: everest_inventory_model_node_ItemBarcode.ItemBarcode["barcodeType"];
            barcodeValue: everest_inventory_model_node_ItemBarcode.ItemBarcode["barcodeValue"];
            uomId: everest_inventory_model_node_ItemBarcode.ItemBarcode["uomId"];
          }[];
          inventoryLevels: {
            locationId: everest_inventory_model_node_IMInventoryLevel.IMInventoryLevel["locationId"];
            locationName: everest_appserver_primitive_Text;
            locationCode: everest_appserver_primitive_Text;
            onHandQuantity: everest_inventory_model_node_IMInventoryLevel.IMInventoryLevel["onHandQuantity"];
            allocatedQuantity: everest_inventory_model_node_IMInventoryLevel.IMInventoryLevel["allocatedQuantity"];
            reservedQuantity: everest_inventory_model_node_IMInventoryLevel.IMInventoryLevel["reservedQuantity"];
            qualityHoldQuantity: everest_inventory_model_node_IMInventoryLevel.IMInventoryLevel["qualityHoldQuantity"];
            damagedQuantity: everest_inventory_model_node_IMInventoryLevel.IMInventoryLevel["damagedQuantity"];
            inTransitQuantity: everest_inventory_model_node_IMInventoryLevel.IMInventoryLevel["inTransitQuantity"];
            availableQuantity: everest_appserver_primitive_Decimal;
            averageCost: everest_inventory_model_node_IMInventoryLevel.IMInventoryLevel["averageCost"];
            lastTransactionTimestamp: everest_inventory_model_node_IMInventoryLevel.IMInventoryLevel["lastTransactionTimestamp"];
          }[];
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }
  }

  export namespace Permission {
    export namespace Check {
      export type Result = {
        dataSets: {
          Items: {
            isGetPermitted: boolean;
            isPostPermitted: boolean;
            isPatchPermitted: boolean;
            isDeletePermitted: boolean;
          };
        };
        actions: {
          CreateItemWithDetails: {
            isExecutePermitted: boolean;
          };
          CreateMultipleItemsWithDetails: {
            isExecutePermitted: boolean;
          };
          ExportItems: {
            isExecutePermitted: boolean;
          };
          ImportItems: {
            isExecutePermitted: boolean;
          };
          ReadItemWithDetails: {
            isExecutePermitted: boolean;
          };
          UpdateItemWithDetails: {
            isExecutePermitted: boolean;
          };
        };
      };

      export interface Request {
        execute(): Promise<Response>;
      }

      export type Response = {
        result: Result;
      };
    }
  }

  export namespace Api {
    export type EntitySets = {
      Items: {
        get: EntitySets.Items.Get.Request;
        post: EntitySets.Items.Post.Request;
        patch: EntitySets.Items.Patch.Request;
        delete: EntitySets.Items.Delete.Request;
      };
    };

    export type Actions = {
      ImportItems: {
        execute: Actions.ImportItems.Execute.Request;
      };
      ExportItems: {
        execute: Actions.ExportItems.Execute.Request;
      };
      CreateItemWithDetails: {
        execute: Actions.CreateItemWithDetails.Execute.Request;
      };
      UpdateItemWithDetails: {
        execute: Actions.UpdateItemWithDetails.Execute.Request;
      };
      CreateMultipleItemsWithDetails: {
        execute: Actions.CreateMultipleItemsWithDetails.Execute.Request;
      };
    };

    export type Functions = {
      ReadItemWithDetails: {
        execute: Functions.ReadItemWithDetails.Execute.Request;
      };
    };

    export interface Client {
      createEntitySetGetRequest<T extends 'Items'>(entitySetName: T): Api.EntitySets[T]['get'];

      createEntitySetPostRequest<T extends 'Items'>(entitySetName: T): Api.EntitySets[T]['post'];

      createEntitySetPatchRequest<T extends 'Items'>(entitySetName: T): Api.EntitySets[T]['patch'];

      createEntitySetDeleteRequest<T extends 'Items'>(entitySetName: T): Api.EntitySets[T]['delete'];

      createActionExecuteRequest<T extends 'ImportItems' | 'ExportItems' | 'CreateItemWithDetails' | 'UpdateItemWithDetails' | 'CreateMultipleItemsWithDetails'>(actionName: T): Api.Actions[T]['execute'];

      createFunctionExecuteRequest<T extends 'ReadItemWithDetails'>(functionName: T): Api.Functions[T]['execute'];

      createPermissionCheckRequest(): Permission.Check.Request;
    }
  }

  export const client = createOdataClientRuntime<Api.Client>('everest.inventory/services/Items');
}

