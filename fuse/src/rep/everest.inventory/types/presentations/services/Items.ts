/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

import type { ItemMaster as everest_inventory_model_node_ItemMaster } from "@pkg/everest.inventory/types/ItemMaster";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { ItemSupplier as everest_inventory_model_node_ItemSupplier } from "@pkg/everest.inventory/types/ItemSupplier";
import type { ItemBarcode as everest_inventory_model_node_ItemBarcode } from "@pkg/everest.inventory/types/ItemBarcode";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { IMInventoryLevel as everest_inventory_model_node_IMInventoryLevel } from "@pkg/everest.inventory/types/IMInventoryLevel";
import type { EvstDecimal as everest_appserver_primitive_Decimal } from "@pkg/everest.appserver/types/primitives/Decimal";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";

import type {
  Filter,
  PresentationSession,
} from '@everestsystems/content-core';

/**
 * Generated backend types for OData presentation Items.
 */
export namespace Items {
  export namespace EntitySets {
    export namespace Items {
      export namespace Query {
        export type Instance = {
          id?: everest_inventory_model_node_ItemMaster.ItemMaster["id"] | undefined;
          itemCode?: everest_inventory_model_node_ItemMaster.ItemMaster["itemCode"] | undefined;
          itemName?: everest_inventory_model_node_ItemMaster.ItemMaster["itemName"] | undefined;
          itemType?: everest_inventory_model_node_ItemMaster.ItemMaster["itemType"] | undefined;
          status?: everest_inventory_model_node_ItemMaster.ItemMaster["status"] | undefined;
          categoryId?: everest_inventory_model_node_ItemMaster.ItemMaster["categoryId"] | undefined;
          unitGroupId?: everest_inventory_model_node_ItemMaster.ItemMaster["unitGroupId"] | undefined;
          inventoryUoMId?: everest_inventory_model_node_ItemMaster.ItemMaster["inventoryUoMId"] | undefined;
          salesUoMId?: everest_inventory_model_node_ItemMaster.ItemMaster["salesUoMId"] | undefined;
          purchaseUoMId?: everest_inventory_model_node_ItemMaster.ItemMaster["purchaseUoMId"] | undefined;
          valuationMethod?: everest_inventory_model_node_ItemMaster.ItemMaster["valuationMethod"] | undefined;
          standardCost?: everest_inventory_model_node_ItemMaster.ItemMaster["standardCost"] | undefined;
          isBatch?: everest_inventory_model_node_ItemMaster.ItemMaster["isBatch"] | undefined;
          isSerial?: everest_inventory_model_node_ItemMaster.ItemMaster["isSerial"] | undefined;
          isPerishable?: everest_inventory_model_node_ItemMaster.ItemMaster["isPerishable"] | undefined;
          shelfLifeDays?: everest_inventory_model_node_ItemMaster.ItemMaster["shelfLifeDays"] | undefined;
          primarySupplierId?: everest_inventory_model_node_ItemMaster.ItemMaster["primarySupplierId"] | undefined;
          primarySupplierSku?: everest_inventory_model_node_ItemMaster.ItemMaster["primarySupplierSku"] | undefined;
          primarySupplierLeadTimeDays?: everest_inventory_model_node_ItemMaster.ItemMaster["primarySupplierLeadTimeDays"] | undefined;
          notes?: everest_inventory_model_node_ItemMaster.ItemMaster["notes"] | undefined;
          customAttributes?: everest_inventory_model_node_ItemMaster.ItemMaster["customAttributes"] | undefined;
          parentItemId?: everest_inventory_model_node_ItemMaster.ItemMaster["parentItemId"] | undefined;
          variantAttributes?: everest_inventory_model_node_ItemMaster.ItemMaster["variantAttributes"] | undefined;
          countryOfOrigin?: everest_inventory_model_node_ItemMaster.ItemMaster["countryOfOrigin"] | undefined;
          estimatedCarbonFootprintKg?: everest_inventory_model_node_ItemMaster.ItemMaster["estimatedCarbonFootprintKg"] | undefined;
          sustainabilityCertifications?: everest_inventory_model_node_ItemMaster.ItemMaster["sustainabilityCertifications"] | undefined;
        };

        export type Order = {
          field: keyof Instance;
          ordering: 'asc' | 'desc';
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            where: Filter<Instance>;
            orderBy: ReadonlyArray<Order>;
            skip: number | undefined;
            take: number | undefined;
            fieldList: ReadonlyArray<keyof Instance>;
            count: boolean;
          };

          export type Response = {
            instances: Instance[];

            /**
             * If count was set to true on the request object, this property
             * must be set, otherwise it must evaluate to undefined.
             */
            count?: number | undefined;
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export namespace Create {
        export type Input = {
          itemCode: everest_inventory_model_node_ItemMaster.ItemMaster["itemCode"];
          itemName?: everest_inventory_model_node_ItemMaster.ItemMaster["itemName"] | undefined;
          itemType?: everest_inventory_model_node_ItemMaster.ItemMaster["itemType"] | undefined;
          status?: everest_inventory_model_node_ItemMaster.ItemMaster["status"] | undefined;
          categoryId: everest_inventory_model_node_ItemMaster.ItemMaster["categoryId"];
          unitGroupId: everest_inventory_model_node_ItemMaster.ItemMaster["unitGroupId"];
          inventoryUoMId?: everest_inventory_model_node_ItemMaster.ItemMaster["inventoryUoMId"] | undefined;
          salesUoMId?: everest_inventory_model_node_ItemMaster.ItemMaster["salesUoMId"] | undefined;
          purchaseUoMId?: everest_inventory_model_node_ItemMaster.ItemMaster["purchaseUoMId"] | undefined;
          valuationMethod?: everest_inventory_model_node_ItemMaster.ItemMaster["valuationMethod"] | undefined;
          standardCost?: everest_inventory_model_node_ItemMaster.ItemMaster["standardCost"] | undefined;
          isBatch?: everest_inventory_model_node_ItemMaster.ItemMaster["isBatch"] | undefined;
          isSerial?: everest_inventory_model_node_ItemMaster.ItemMaster["isSerial"] | undefined;
          isPerishable?: everest_inventory_model_node_ItemMaster.ItemMaster["isPerishable"] | undefined;
          shelfLifeDays?: everest_inventory_model_node_ItemMaster.ItemMaster["shelfLifeDays"] | undefined;
          primarySupplierId?: everest_inventory_model_node_ItemMaster.ItemMaster["primarySupplierId"] | undefined;
          primarySupplierSku?: everest_inventory_model_node_ItemMaster.ItemMaster["primarySupplierSku"] | undefined;
          primarySupplierLeadTimeDays?: everest_inventory_model_node_ItemMaster.ItemMaster["primarySupplierLeadTimeDays"] | undefined;
          notes?: everest_inventory_model_node_ItemMaster.ItemMaster["notes"] | undefined;
          customAttributes?: everest_inventory_model_node_ItemMaster.ItemMaster["customAttributes"] | undefined;
          parentItemId?: everest_inventory_model_node_ItemMaster.ItemMaster["parentItemId"] | undefined;
          variantAttributes?: everest_inventory_model_node_ItemMaster.ItemMaster["variantAttributes"] | undefined;
          countryOfOrigin?: everest_inventory_model_node_ItemMaster.ItemMaster["countryOfOrigin"] | undefined;
          estimatedCarbonFootprintKg?: everest_inventory_model_node_ItemMaster.ItemMaster["estimatedCarbonFootprintKg"] | undefined;
          sustainabilityCertifications?: everest_inventory_model_node_ItemMaster.ItemMaster["sustainabilityCertifications"] | undefined;
        };

        export type Instance = {
          id: everest_inventory_model_node_ItemMaster.ItemMaster["id"];
          itemCode: everest_inventory_model_node_ItemMaster.ItemMaster["itemCode"];
          itemName?: everest_inventory_model_node_ItemMaster.ItemMaster["itemName"] | undefined;
          itemType?: everest_inventory_model_node_ItemMaster.ItemMaster["itemType"] | undefined;
          status?: everest_inventory_model_node_ItemMaster.ItemMaster["status"] | undefined;
          categoryId: everest_inventory_model_node_ItemMaster.ItemMaster["categoryId"];
          unitGroupId: everest_inventory_model_node_ItemMaster.ItemMaster["unitGroupId"];
          inventoryUoMId?: everest_inventory_model_node_ItemMaster.ItemMaster["inventoryUoMId"] | undefined;
          salesUoMId?: everest_inventory_model_node_ItemMaster.ItemMaster["salesUoMId"] | undefined;
          purchaseUoMId?: everest_inventory_model_node_ItemMaster.ItemMaster["purchaseUoMId"] | undefined;
          valuationMethod?: everest_inventory_model_node_ItemMaster.ItemMaster["valuationMethod"] | undefined;
          standardCost?: everest_inventory_model_node_ItemMaster.ItemMaster["standardCost"] | undefined;
          isBatch?: everest_inventory_model_node_ItemMaster.ItemMaster["isBatch"] | undefined;
          isSerial?: everest_inventory_model_node_ItemMaster.ItemMaster["isSerial"] | undefined;
          isPerishable?: everest_inventory_model_node_ItemMaster.ItemMaster["isPerishable"] | undefined;
          shelfLifeDays?: everest_inventory_model_node_ItemMaster.ItemMaster["shelfLifeDays"] | undefined;
          primarySupplierId?: everest_inventory_model_node_ItemMaster.ItemMaster["primarySupplierId"] | undefined;
          primarySupplierSku?: everest_inventory_model_node_ItemMaster.ItemMaster["primarySupplierSku"] | undefined;
          primarySupplierLeadTimeDays?: everest_inventory_model_node_ItemMaster.ItemMaster["primarySupplierLeadTimeDays"] | undefined;
          notes?: everest_inventory_model_node_ItemMaster.ItemMaster["notes"] | undefined;
          customAttributes?: everest_inventory_model_node_ItemMaster.ItemMaster["customAttributes"] | undefined;
          parentItemId?: everest_inventory_model_node_ItemMaster.ItemMaster["parentItemId"] | undefined;
          variantAttributes?: everest_inventory_model_node_ItemMaster.ItemMaster["variantAttributes"] | undefined;
          countryOfOrigin?: everest_inventory_model_node_ItemMaster.ItemMaster["countryOfOrigin"] | undefined;
          estimatedCarbonFootprintKg?: everest_inventory_model_node_ItemMaster.ItemMaster["estimatedCarbonFootprintKg"] | undefined;
          sustainabilityCertifications?: everest_inventory_model_node_ItemMaster.ItemMaster["sustainabilityCertifications"] | undefined;
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            inputs: Input[];
          };

          export type Response = {
            instances: Instance[];
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export namespace Update {
        export type Data = {
          itemCode?: everest_inventory_model_node_ItemMaster.ItemMaster["itemCode"] | null;
          itemName?: everest_inventory_model_node_ItemMaster.ItemMaster["itemName"] | null;
          itemType?: everest_inventory_model_node_ItemMaster.ItemMaster["itemType"] | null;
          status?: everest_inventory_model_node_ItemMaster.ItemMaster["status"] | null;
          categoryId?: everest_inventory_model_node_ItemMaster.ItemMaster["categoryId"] | null;
          unitGroupId?: everest_inventory_model_node_ItemMaster.ItemMaster["unitGroupId"] | null;
          inventoryUoMId?: everest_inventory_model_node_ItemMaster.ItemMaster["inventoryUoMId"] | null;
          salesUoMId?: everest_inventory_model_node_ItemMaster.ItemMaster["salesUoMId"] | null;
          purchaseUoMId?: everest_inventory_model_node_ItemMaster.ItemMaster["purchaseUoMId"] | null;
          valuationMethod?: everest_inventory_model_node_ItemMaster.ItemMaster["valuationMethod"] | null;
          standardCost?: everest_inventory_model_node_ItemMaster.ItemMaster["standardCost"] | null;
          isBatch?: everest_inventory_model_node_ItemMaster.ItemMaster["isBatch"] | null;
          isSerial?: everest_inventory_model_node_ItemMaster.ItemMaster["isSerial"] | null;
          isPerishable?: everest_inventory_model_node_ItemMaster.ItemMaster["isPerishable"] | null;
          shelfLifeDays?: everest_inventory_model_node_ItemMaster.ItemMaster["shelfLifeDays"] | null;
          primarySupplierId?: everest_inventory_model_node_ItemMaster.ItemMaster["primarySupplierId"] | null;
          primarySupplierSku?: everest_inventory_model_node_ItemMaster.ItemMaster["primarySupplierSku"] | null;
          primarySupplierLeadTimeDays?: everest_inventory_model_node_ItemMaster.ItemMaster["primarySupplierLeadTimeDays"] | null;
          notes?: everest_inventory_model_node_ItemMaster.ItemMaster["notes"] | null;
          customAttributes?: everest_inventory_model_node_ItemMaster.ItemMaster["customAttributes"] | null;
          parentItemId?: everest_inventory_model_node_ItemMaster.ItemMaster["parentItemId"] | null;
          variantAttributes?: everest_inventory_model_node_ItemMaster.ItemMaster["variantAttributes"] | null;
          countryOfOrigin?: everest_inventory_model_node_ItemMaster.ItemMaster["countryOfOrigin"] | null;
          estimatedCarbonFootprintKg?: everest_inventory_model_node_ItemMaster.ItemMaster["estimatedCarbonFootprintKg"] | null;
          sustainabilityCertifications?: everest_inventory_model_node_ItemMaster.ItemMaster["sustainabilityCertifications"] | null;
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            id: number;
            data: Data;
          };

          export type Response = void;
        }

        export interface Implementation {
          execute(input: Execute.Request): Promise<Execute.Response>;
        }
      }

      export namespace Delete {
        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            ids: number[];
          };

          export type Response = void;
        }

        export interface Implementation {
          execute(input: Execute.Request): Promise<Execute.Response>;
        }
      }

      export type Implementation = {
        query: Query.Implementation;
        create: Create.Implementation;
        update: Update.Implementation;
        delete: Delete.Implementation;
      };
    }
  }

  export namespace Actions {
    export namespace ImportItems {
      export type Input = {
        fileContent: everest_appserver_primitive_Text;
        fileFormat: everest_appserver_primitive_Text;
      };

      export type Output = {
        jobId: everest_appserver_primitive_Text;
        status: everest_appserver_primitive_Text;
        message: everest_appserver_primitive_Text;
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace ExportItems {
      export type Input = {
        fileFormat: everest_appserver_primitive_Text;
        filter: everest_appserver_primitive_Text;
      };

      export type Output = {
        jobId: everest_appserver_primitive_Text;
        status: everest_appserver_primitive_Text;
        downloadUrl: everest_appserver_primitive_Text;
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace CreateItemWithDetails {
      export type Input = {
        item: {
          itemCode: everest_inventory_model_node_ItemMaster.ItemMaster["itemCode"];
          itemName: everest_inventory_model_node_ItemMaster.ItemMaster["itemName"];
          itemType: everest_inventory_model_node_ItemMaster.ItemMaster["itemType"];
          status: everest_inventory_model_node_ItemMaster.ItemMaster["status"];
          categoryId: everest_inventory_model_node_ItemMaster.ItemMaster["categoryId"];
          unitGroupId: everest_inventory_model_node_ItemMaster.ItemMaster["unitGroupId"];
          inventoryUoMId: everest_inventory_model_node_ItemMaster.ItemMaster["inventoryUoMId"];
          salesUoMId: everest_inventory_model_node_ItemMaster.ItemMaster["salesUoMId"];
          purchaseUoMId: everest_inventory_model_node_ItemMaster.ItemMaster["purchaseUoMId"];
          productId: everest_inventory_model_node_ItemMaster.ItemMaster["productId"];
          valuationMethod: everest_inventory_model_node_ItemMaster.ItemMaster["valuationMethod"];
          standardCost: everest_inventory_model_node_ItemMaster.ItemMaster["standardCost"];
          isBatch: everest_inventory_model_node_ItemMaster.ItemMaster["isBatch"];
          isSerial: everest_inventory_model_node_ItemMaster.ItemMaster["isSerial"];
          isPerishable: everest_inventory_model_node_ItemMaster.ItemMaster["isPerishable"];
          shelfLifeDays: everest_inventory_model_node_ItemMaster.ItemMaster["shelfLifeDays"];
          notes: everest_inventory_model_node_ItemMaster.ItemMaster["notes"];
          customAttributes: everest_inventory_model_node_ItemMaster.ItemMaster["customAttributes"];
          parentItemId: everest_inventory_model_node_ItemMaster.ItemMaster["parentItemId"];
          variantAttributes: everest_inventory_model_node_ItemMaster.ItemMaster["variantAttributes"];
          countryOfOrigin: everest_inventory_model_node_ItemMaster.ItemMaster["countryOfOrigin"];
          estimatedCarbonFootprintKg: everest_inventory_model_node_ItemMaster.ItemMaster["estimatedCarbonFootprintKg"];
          sustainabilityCertifications: everest_inventory_model_node_ItemMaster.ItemMaster["sustainabilityCertifications"];
        };
        suppliers: {
          supplierId: everest_inventory_model_node_ItemSupplier.ItemSupplier["supplierId"];
          supplierSku: everest_inventory_model_node_ItemSupplier.ItemSupplier["supplierSku"];
          isPreferred: everest_inventory_model_node_ItemSupplier.ItemSupplier["isPreferred"];
          lastPurchasePrice: everest_inventory_model_node_ItemSupplier.ItemSupplier["lastPurchasePrice"];
          supplierLeadTimeDays: everest_inventory_model_node_ItemSupplier.ItemSupplier["supplierLeadTimeDays"];
        }[];
        barcodes: {
          barcodeType: everest_inventory_model_node_ItemBarcode.ItemBarcode["barcodeType"];
          barcodeValue: everest_inventory_model_node_ItemBarcode.ItemBarcode["barcodeValue"];
          uomId: everest_inventory_model_node_ItemBarcode.ItemBarcode["uomId"];
        }[];
      };

      export type Output = {
        itemId: everest_inventory_model_node_ItemMaster.ItemMaster["id"];
        supplierIds: everest_appserver_primitive_Number[];
        barcodeIds: everest_appserver_primitive_Number[];
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace ReadItemWithDetails {
      export type Input = {
        itemId: everest_inventory_model_node_ItemMaster.ItemMaster["id"];
      };

      export type Output = {
        item: {
          id: everest_inventory_model_node_ItemMaster.ItemMaster["id"];
          itemCode: everest_inventory_model_node_ItemMaster.ItemMaster["itemCode"];
          itemName: everest_inventory_model_node_ItemMaster.ItemMaster["itemName"];
          itemType: everest_inventory_model_node_ItemMaster.ItemMaster["itemType"];
          status: everest_inventory_model_node_ItemMaster.ItemMaster["status"];
          categoryId: everest_inventory_model_node_ItemMaster.ItemMaster["categoryId"];
          unitGroupId: everest_inventory_model_node_ItemMaster.ItemMaster["unitGroupId"];
          inventoryUoMId: everest_inventory_model_node_ItemMaster.ItemMaster["inventoryUoMId"];
          salesUoMId: everest_inventory_model_node_ItemMaster.ItemMaster["salesUoMId"];
          purchaseUoMId: everest_inventory_model_node_ItemMaster.ItemMaster["purchaseUoMId"];
          valuationMethod: everest_inventory_model_node_ItemMaster.ItemMaster["valuationMethod"];
          standardCost: everest_inventory_model_node_ItemMaster.ItemMaster["standardCost"];
          isBatch: everest_inventory_model_node_ItemMaster.ItemMaster["isBatch"];
          isSerial: everest_inventory_model_node_ItemMaster.ItemMaster["isSerial"];
          isPerishable: everest_inventory_model_node_ItemMaster.ItemMaster["isPerishable"];
          shelfLifeDays: everest_inventory_model_node_ItemMaster.ItemMaster["shelfLifeDays"];
          notes: everest_inventory_model_node_ItemMaster.ItemMaster["notes"];
          customAttributes: everest_inventory_model_node_ItemMaster.ItemMaster["customAttributes"];
          parentItemId: everest_inventory_model_node_ItemMaster.ItemMaster["parentItemId"];
          variantAttributes: everest_inventory_model_node_ItemMaster.ItemMaster["variantAttributes"];
          countryOfOrigin: everest_inventory_model_node_ItemMaster.ItemMaster["countryOfOrigin"];
          estimatedCarbonFootprintKg: everest_inventory_model_node_ItemMaster.ItemMaster["estimatedCarbonFootprintKg"];
          sustainabilityCertifications: everest_inventory_model_node_ItemMaster.ItemMaster["sustainabilityCertifications"];
          productId: everest_inventory_model_node_ItemMaster.ItemMaster["productId"];
        };
        suppliers: {
          id: everest_inventory_model_node_ItemSupplier.ItemSupplier["id"];
          supplierId: everest_inventory_model_node_ItemSupplier.ItemSupplier["supplierId"];
          supplierSku: everest_inventory_model_node_ItemSupplier.ItemSupplier["supplierSku"];
          isPreferred: everest_inventory_model_node_ItemSupplier.ItemSupplier["isPreferred"];
          lastPurchasePrice: everest_inventory_model_node_ItemSupplier.ItemSupplier["lastPurchasePrice"];
          supplierLeadTimeDays: everest_inventory_model_node_ItemSupplier.ItemSupplier["supplierLeadTimeDays"];
        }[];
        barcodes: {
          id: everest_inventory_model_node_ItemBarcode.ItemBarcode["id"];
          barcodeType: everest_inventory_model_node_ItemBarcode.ItemBarcode["barcodeType"];
          barcodeValue: everest_inventory_model_node_ItemBarcode.ItemBarcode["barcodeValue"];
          uomId: everest_inventory_model_node_ItemBarcode.ItemBarcode["uomId"];
        }[];
        inventoryLevels: {
          locationId: everest_inventory_model_node_IMInventoryLevel.IMInventoryLevel["locationId"];
          locationName: everest_appserver_primitive_Text;
          locationCode: everest_appserver_primitive_Text;
          onHandQuantity: everest_inventory_model_node_IMInventoryLevel.IMInventoryLevel["onHandQuantity"];
          allocatedQuantity: everest_inventory_model_node_IMInventoryLevel.IMInventoryLevel["allocatedQuantity"];
          reservedQuantity: everest_inventory_model_node_IMInventoryLevel.IMInventoryLevel["reservedQuantity"];
          qualityHoldQuantity: everest_inventory_model_node_IMInventoryLevel.IMInventoryLevel["qualityHoldQuantity"];
          damagedQuantity: everest_inventory_model_node_IMInventoryLevel.IMInventoryLevel["damagedQuantity"];
          inTransitQuantity: everest_inventory_model_node_IMInventoryLevel.IMInventoryLevel["inTransitQuantity"];
          availableQuantity: everest_appserver_primitive_Decimal;
          averageCost: everest_inventory_model_node_IMInventoryLevel.IMInventoryLevel["averageCost"];
          lastTransactionTimestamp: everest_inventory_model_node_IMInventoryLevel.IMInventoryLevel["lastTransactionTimestamp"];
        }[];
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace UpdateItemWithDetails {
      export type Input = {
        item: {
          id: everest_inventory_model_node_ItemMaster.ItemMaster["id"];
          itemCode: everest_inventory_model_node_ItemMaster.ItemMaster["itemCode"];
          itemName: everest_inventory_model_node_ItemMaster.ItemMaster["itemName"];
          itemType: everest_inventory_model_node_ItemMaster.ItemMaster["itemType"];
          status: everest_inventory_model_node_ItemMaster.ItemMaster["status"];
          categoryId: everest_inventory_model_node_ItemMaster.ItemMaster["categoryId"];
          unitGroupId: everest_inventory_model_node_ItemMaster.ItemMaster["unitGroupId"];
          inventoryUoMId: everest_inventory_model_node_ItemMaster.ItemMaster["inventoryUoMId"];
          salesUoMId: everest_inventory_model_node_ItemMaster.ItemMaster["salesUoMId"];
          purchaseUoMId: everest_inventory_model_node_ItemMaster.ItemMaster["purchaseUoMId"];
          valuationMethod: everest_inventory_model_node_ItemMaster.ItemMaster["valuationMethod"];
          standardCost: everest_inventory_model_node_ItemMaster.ItemMaster["standardCost"];
          isBatch: everest_inventory_model_node_ItemMaster.ItemMaster["isBatch"];
          isSerial: everest_inventory_model_node_ItemMaster.ItemMaster["isSerial"];
          isPerishable: everest_inventory_model_node_ItemMaster.ItemMaster["isPerishable"];
          shelfLifeDays: everest_inventory_model_node_ItemMaster.ItemMaster["shelfLifeDays"];
          notes: everest_inventory_model_node_ItemMaster.ItemMaster["notes"];
          customAttributes: everest_inventory_model_node_ItemMaster.ItemMaster["customAttributes"];
          parentItemId: everest_inventory_model_node_ItemMaster.ItemMaster["parentItemId"];
          variantAttributes: everest_inventory_model_node_ItemMaster.ItemMaster["variantAttributes"];
          countryOfOrigin: everest_inventory_model_node_ItemMaster.ItemMaster["countryOfOrigin"];
          estimatedCarbonFootprintKg: everest_inventory_model_node_ItemMaster.ItemMaster["estimatedCarbonFootprintKg"];
          sustainabilityCertifications: everest_inventory_model_node_ItemMaster.ItemMaster["sustainabilityCertifications"];
          productId: everest_inventory_model_node_ItemMaster.ItemMaster["productId"];
        };
        suppliers: {
          id: everest_inventory_model_node_ItemSupplier.ItemSupplier["id"];
          supplierId: everest_inventory_model_node_ItemSupplier.ItemSupplier["supplierId"];
          supplierSku: everest_inventory_model_node_ItemSupplier.ItemSupplier["supplierSku"];
          isPreferred: everest_inventory_model_node_ItemSupplier.ItemSupplier["isPreferred"];
          lastPurchasePrice: everest_inventory_model_node_ItemSupplier.ItemSupplier["lastPurchasePrice"];
          supplierLeadTimeDays: everest_inventory_model_node_ItemSupplier.ItemSupplier["supplierLeadTimeDays"];
        }[];
        newSuppliers: {
          supplierId: everest_inventory_model_node_ItemSupplier.ItemSupplier["supplierId"];
          supplierSku: everest_inventory_model_node_ItemSupplier.ItemSupplier["supplierSku"];
          isPreferred: everest_inventory_model_node_ItemSupplier.ItemSupplier["isPreferred"];
          lastPurchasePrice: everest_inventory_model_node_ItemSupplier.ItemSupplier["lastPurchasePrice"];
          supplierLeadTimeDays: everest_inventory_model_node_ItemSupplier.ItemSupplier["supplierLeadTimeDays"];
        }[];
        barcodes: {
          id: everest_inventory_model_node_ItemBarcode.ItemBarcode["id"];
          barcodeType: everest_inventory_model_node_ItemBarcode.ItemBarcode["barcodeType"];
          barcodeValue: everest_inventory_model_node_ItemBarcode.ItemBarcode["barcodeValue"];
          uomId: everest_inventory_model_node_ItemBarcode.ItemBarcode["uomId"];
        }[];
        newBarcodes: {
          barcodeType: everest_inventory_model_node_ItemBarcode.ItemBarcode["barcodeType"];
          barcodeValue: everest_inventory_model_node_ItemBarcode.ItemBarcode["barcodeValue"];
          uomId: everest_inventory_model_node_ItemBarcode.ItemBarcode["uomId"];
        }[];
        suppliersToDelete: everest_appserver_primitive_Number[];
        barcodesToDelete: everest_appserver_primitive_Number[];
      };

      export type Output = {
        success: everest_appserver_primitive_TrueFalse;
        message: everest_appserver_primitive_Text;
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace CreateMultipleItemsWithDetails {
      export type Input = {
        items: {
          itemCode: everest_inventory_model_node_ItemMaster.ItemMaster["itemCode"];
          itemName: everest_inventory_model_node_ItemMaster.ItemMaster["itemName"];
          itemType: everest_inventory_model_node_ItemMaster.ItemMaster["itemType"];
          status: everest_inventory_model_node_ItemMaster.ItemMaster["status"];
          categoryId: everest_inventory_model_node_ItemMaster.ItemMaster["categoryId"];
          unitGroupId: everest_inventory_model_node_ItemMaster.ItemMaster["unitGroupId"];
          inventoryUoMId: everest_inventory_model_node_ItemMaster.ItemMaster["inventoryUoMId"];
          salesUoMId: everest_inventory_model_node_ItemMaster.ItemMaster["salesUoMId"];
          purchaseUoMId: everest_inventory_model_node_ItemMaster.ItemMaster["purchaseUoMId"];
          valuationMethod: everest_inventory_model_node_ItemMaster.ItemMaster["valuationMethod"];
          standardCost: everest_inventory_model_node_ItemMaster.ItemMaster["standardCost"];
          isBatch: everest_inventory_model_node_ItemMaster.ItemMaster["isBatch"];
          isSerial: everest_inventory_model_node_ItemMaster.ItemMaster["isSerial"];
          isPerishable: everest_inventory_model_node_ItemMaster.ItemMaster["isPerishable"];
          shelfLifeDays: everest_inventory_model_node_ItemMaster.ItemMaster["shelfLifeDays"];
          notes: everest_inventory_model_node_ItemMaster.ItemMaster["notes"];
          customAttributes: everest_inventory_model_node_ItemMaster.ItemMaster["customAttributes"];
          parentItemId: everest_inventory_model_node_ItemMaster.ItemMaster["parentItemId"];
          variantAttributes: everest_inventory_model_node_ItemMaster.ItemMaster["variantAttributes"];
          countryOfOrigin: everest_inventory_model_node_ItemMaster.ItemMaster["countryOfOrigin"];
          estimatedCarbonFootprintKg: everest_inventory_model_node_ItemMaster.ItemMaster["estimatedCarbonFootprintKg"];
          sustainabilityCertifications: everest_inventory_model_node_ItemMaster.ItemMaster["sustainabilityCertifications"];
          suppliers: {
            supplierId: everest_inventory_model_node_ItemSupplier.ItemSupplier["supplierId"];
            supplierSku: everest_inventory_model_node_ItemSupplier.ItemSupplier["supplierSku"];
            isPreferred: everest_inventory_model_node_ItemSupplier.ItemSupplier["isPreferred"];
            lastPurchasePrice: everest_inventory_model_node_ItemSupplier.ItemSupplier["lastPurchasePrice"];
            supplierLeadTimeDays: everest_inventory_model_node_ItemSupplier.ItemSupplier["supplierLeadTimeDays"];
          }[];
          barcodes: {
            barcodeType: everest_inventory_model_node_ItemBarcode.ItemBarcode["barcodeType"];
            barcodeValue: everest_inventory_model_node_ItemBarcode.ItemBarcode["barcodeValue"];
            uomId: everest_inventory_model_node_ItemBarcode.ItemBarcode["uomId"];
          }[];
        }[];
      };

      export type Output = {
        results: {
          itemCode: everest_appserver_primitive_Text;
          success: everest_appserver_primitive_TrueFalse;
          itemId: everest_inventory_model_node_ItemMaster.ItemMaster["id"];
          supplierIds: everest_appserver_primitive_Number[];
          barcodeIds: everest_appserver_primitive_Number[];
          error: everest_appserver_primitive_Text;
          validationErrors: everest_appserver_primitive_Text[];
        }[];
        summary: {
          totalItems: everest_appserver_primitive_Number;
          successCount: everest_appserver_primitive_Number;
          errorCount: everest_appserver_primitive_Number;
          validationErrorCount: everest_appserver_primitive_Number;
        };
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }
  }

  export type Implementation = {
    Items: EntitySets.Items.Implementation;
    ImportItems: Actions.ImportItems.Implementation;
    ExportItems: Actions.ExportItems.Implementation;
    CreateItemWithDetails: Actions.CreateItemWithDetails.Implementation;
    ReadItemWithDetails: Actions.ReadItemWithDetails.Implementation;
    UpdateItemWithDetails: Actions.UpdateItemWithDetails.Implementation;
    CreateMultipleItemsWithDetails: Actions.CreateMultipleItemsWithDetails.Implementation;
  };
}

