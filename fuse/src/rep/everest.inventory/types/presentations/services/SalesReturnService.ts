/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

import type { EvstID as everest_appserver_primitive_ID } from "@pkg/everest.appserver/types/primitives/ID";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { Customer as everest_fin_accounting_model_node_Customer } from "@pkg/everest.fin.accounting/types/Customer";
import type { EvstDate as everest_appserver_primitive_Date } from "@pkg/everest.appserver/types/primitives/Date";
import type { IMInventorySalesReturn as everest_inventory_model_node_IMInventorySalesReturn } from "@pkg/everest.inventory/types/IMInventorySalesReturn";
import type { IMInventorySalesReturnLine as everest_inventory_model_node_IMInventorySalesReturnLine } from "@pkg/everest.inventory/types/IMInventorySalesReturnLine";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { EvstDecimal as everest_appserver_primitive_Decimal } from "@pkg/everest.appserver/types/primitives/Decimal";
import type { EvstPlainDate as everest_appserver_primitive_PlainDate } from "@pkg/everest.appserver/types/primitives/PlainDate";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { IMWarehouseLocation as everest_inventory_model_node_IMWarehouseLocation } from "@pkg/everest.inventory/types/IMWarehouseLocation";
import type { IMInventorySalesReturnInspectionDetail as everest_inventory_model_node_IMInventorySalesReturnInspectionDetail } from "@pkg/everest.inventory/types/IMInventorySalesReturnInspectionDetail";

import type {
  Filter,
  PresentationSession,
} from '@everestsystems/content-core';

/**
 * Generated backend types for OData presentation SalesReturnService.
 */
export namespace SalesReturnService {
  export namespace EntitySets {
    export namespace Customers {
      export namespace Query {
        export type Instance = {
          id?: everest_appserver_primitive_ID | undefined;
          name?: everest_appserver_primitive_Text | undefined;
          customerNumber?: everest_appserver_primitive_Text | undefined;
          email?: everest_appserver_primitive_Text | undefined;
          phone?: everest_appserver_primitive_Text | undefined;
          address?: everest_appserver_primitive_Text | undefined;
          city?: everest_appserver_primitive_Text | undefined;
          stateProvince?: everest_appserver_primitive_Text | undefined;
          postalCode?: everest_appserver_primitive_Text | undefined;
          country?: everest_appserver_primitive_Text | undefined;
          customerType?: everest_fin_accounting_model_node_Customer.Customer["customerType"] | undefined;
          status?: everest_fin_accounting_model_node_Customer.Customer["status"] | undefined;
          createdDate?: everest_appserver_primitive_Date | undefined;
          lastModifiedDate?: everest_appserver_primitive_Date | undefined;
        };

        export type Order = {
          field: keyof Instance;
          ordering: 'asc' | 'desc';
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            where: Filter<Instance>;
            orderBy: ReadonlyArray<Order>;
            skip: number | undefined;
            take: number | undefined;
            fieldList: ReadonlyArray<keyof Instance>;
            count: boolean;
          };

          export type Response = {
            instances: Instance[];

            /**
             * If count was set to true on the request object, this property
             * must be set, otherwise it must evaluate to undefined.
             */
            count?: number | undefined;
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export type Implementation = {
        query: Query.Implementation;
      };
    }
    export namespace SalesReturns {
      export namespace Query {
        export type Instance = {
          id?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["id"] | undefined;
          uuid?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["uuid"] | undefined;
          externalId?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["externalId"] | undefined;
          active?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["active"] | undefined;
          businessPartnerId?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["businessPartnerId"] | undefined;
          documentDate?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["documentDate"] | undefined;
          documentNumber?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["documentNumber"] | undefined;
          entityId?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["entityId"] | undefined;
          notes?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["notes"] | undefined;
          status?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["status"] | undefined;
          createdBy?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["createdBy"] | undefined;
          createdDate?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["createdDate"] | undefined;
          lastModifiedBy?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["lastModifiedBy"] | undefined;
          lastModifiedDate?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["lastModifiedDate"] | undefined;
          lines?: {
            id: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["id"];
            lineId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["id"] | undefined;
            headerId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["headerId"];
            lineNumber: everest_appserver_primitive_Number;
            itemId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["itemId"];
            itemCode: everest_appserver_primitive_Text;
            itemName: everest_appserver_primitive_Text;
            locationId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["locationId"];
            locationName: everest_appserver_primitive_Text;
            unitId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["unitId"];
            unitCode: everest_appserver_primitive_Text;
            sourceDocumentLineId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineId"];
            sourceDocumentLineType: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineType"];
            quantity: everest_appserver_primitive_Decimal;
            inventoryQuantity: everest_appserver_primitive_Decimal;
            openQuantity: everest_appserver_primitive_Decimal;
            receivedQuantity: everest_appserver_primitive_Decimal;
            inspectedQuantity: everest_appserver_primitive_Decimal;
            processedQuantity: everest_appserver_primitive_Decimal;
            unitPrice: everest_appserver_primitive_Decimal;
            totalPrice: everest_appserver_primitive_Decimal;
            unitCost: everest_appserver_primitive_Decimal;
            totalCost: everest_appserver_primitive_Decimal;
            condition: everest_appserver_primitive_Text;
            disposition: everest_appserver_primitive_Text;
            reasonCodeId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["reasonCodeId"];
            status: everest_appserver_primitive_Text;
            receivedDate: everest_appserver_primitive_PlainDate;
            inspectedDate: everest_appserver_primitive_PlainDate;
            processedDate: everest_appserver_primitive_PlainDate;
            notes: everest_appserver_primitive_Text;
            inspectionNotes: everest_appserver_primitive_Text;
          }[] | undefined;
        };

        export type Order = {
          field: keyof Instance;
          ordering: 'asc' | 'desc';
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            where: Filter<Instance>;
            orderBy: ReadonlyArray<Order>;
            skip: number | undefined;
            take: number | undefined;
            fieldList: ReadonlyArray<keyof Instance>;
            count: boolean;
          };

          export type Response = {
            instances: Instance[];

            /**
             * If count was set to true on the request object, this property
             * must be set, otherwise it must evaluate to undefined.
             */
            count?: number | undefined;
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export namespace Create {
        export type Input = {
          externalId?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["externalId"] | undefined;
          active?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["active"] | undefined;
          businessPartnerId?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["businessPartnerId"] | undefined;
          documentDate?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["documentDate"] | undefined;
          documentNumber?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["documentNumber"] | undefined;
          entityId?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["entityId"] | undefined;
          notes?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["notes"] | undefined;
          status?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["status"] | undefined;
          lines?: {
            id: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["id"];
            lineId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["id"] | undefined;
            headerId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["headerId"];
            lineNumber: everest_appserver_primitive_Number;
            itemId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["itemId"];
            itemCode: everest_appserver_primitive_Text;
            itemName: everest_appserver_primitive_Text;
            locationId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["locationId"];
            locationName: everest_appserver_primitive_Text;
            unitId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["unitId"];
            unitCode: everest_appserver_primitive_Text;
            sourceDocumentLineId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineId"];
            sourceDocumentLineType: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineType"];
            quantity: everest_appserver_primitive_Decimal;
            inventoryQuantity: everest_appserver_primitive_Decimal;
            openQuantity: everest_appserver_primitive_Decimal;
            receivedQuantity: everest_appserver_primitive_Decimal;
            inspectedQuantity: everest_appserver_primitive_Decimal;
            processedQuantity: everest_appserver_primitive_Decimal;
            unitPrice: everest_appserver_primitive_Decimal;
            totalPrice: everest_appserver_primitive_Decimal;
            unitCost: everest_appserver_primitive_Decimal;
            totalCost: everest_appserver_primitive_Decimal;
            condition: everest_appserver_primitive_Text;
            disposition: everest_appserver_primitive_Text;
            reasonCodeId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["reasonCodeId"];
            status: everest_appserver_primitive_Text;
            receivedDate: everest_appserver_primitive_PlainDate;
            inspectedDate: everest_appserver_primitive_PlainDate;
            processedDate: everest_appserver_primitive_PlainDate;
            notes: everest_appserver_primitive_Text;
            inspectionNotes: everest_appserver_primitive_Text;
          }[] | undefined;
        };

        export type Instance = {
          id: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["id"];
          uuid?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["uuid"] | undefined;
          externalId?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["externalId"] | undefined;
          active?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["active"] | undefined;
          businessPartnerId?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["businessPartnerId"] | undefined;
          documentDate?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["documentDate"] | undefined;
          documentNumber?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["documentNumber"] | undefined;
          entityId?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["entityId"] | undefined;
          notes?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["notes"] | undefined;
          status?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["status"] | undefined;
          createdBy?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["createdBy"] | undefined;
          createdDate?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["createdDate"] | undefined;
          lastModifiedBy?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["lastModifiedBy"] | undefined;
          lastModifiedDate?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["lastModifiedDate"] | undefined;
          lines?: {
            id: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["id"];
            lineId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["id"] | undefined;
            headerId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["headerId"];
            lineNumber: everest_appserver_primitive_Number;
            itemId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["itemId"];
            itemCode: everest_appserver_primitive_Text;
            itemName: everest_appserver_primitive_Text;
            locationId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["locationId"];
            locationName: everest_appserver_primitive_Text;
            unitId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["unitId"];
            unitCode: everest_appserver_primitive_Text;
            sourceDocumentLineId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineId"];
            sourceDocumentLineType: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineType"];
            quantity: everest_appserver_primitive_Decimal;
            inventoryQuantity: everest_appserver_primitive_Decimal;
            openQuantity: everest_appserver_primitive_Decimal;
            receivedQuantity: everest_appserver_primitive_Decimal;
            inspectedQuantity: everest_appserver_primitive_Decimal;
            processedQuantity: everest_appserver_primitive_Decimal;
            unitPrice: everest_appserver_primitive_Decimal;
            totalPrice: everest_appserver_primitive_Decimal;
            unitCost: everest_appserver_primitive_Decimal;
            totalCost: everest_appserver_primitive_Decimal;
            condition: everest_appserver_primitive_Text;
            disposition: everest_appserver_primitive_Text;
            reasonCodeId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["reasonCodeId"];
            status: everest_appserver_primitive_Text;
            receivedDate: everest_appserver_primitive_PlainDate;
            inspectedDate: everest_appserver_primitive_PlainDate;
            processedDate: everest_appserver_primitive_PlainDate;
            notes: everest_appserver_primitive_Text;
            inspectionNotes: everest_appserver_primitive_Text;
          }[] | undefined;
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            inputs: Input[];
          };

          export type Response = {
            instances: Instance[];
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export namespace Update {
        export type Data = {
          externalId?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["externalId"] | null;
          active?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["active"] | null;
          businessPartnerId?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["businessPartnerId"] | null;
          documentDate?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["documentDate"] | null;
          documentNumber?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["documentNumber"] | null;
          entityId?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["entityId"] | null;
          notes?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["notes"] | null;
          status?: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["status"] | null;
          lines?: {
            id: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["id"];
            lineId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["id"] | undefined;
            headerId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["headerId"];
            lineNumber: everest_appserver_primitive_Number;
            itemId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["itemId"];
            itemCode: everest_appserver_primitive_Text;
            itemName: everest_appserver_primitive_Text;
            locationId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["locationId"];
            locationName: everest_appserver_primitive_Text;
            unitId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["unitId"];
            unitCode: everest_appserver_primitive_Text;
            sourceDocumentLineId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineId"];
            sourceDocumentLineType: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineType"];
            quantity: everest_appserver_primitive_Decimal;
            inventoryQuantity: everest_appserver_primitive_Decimal;
            openQuantity: everest_appserver_primitive_Decimal;
            receivedQuantity: everest_appserver_primitive_Decimal;
            inspectedQuantity: everest_appserver_primitive_Decimal;
            processedQuantity: everest_appserver_primitive_Decimal;
            unitPrice: everest_appserver_primitive_Decimal;
            totalPrice: everest_appserver_primitive_Decimal;
            unitCost: everest_appserver_primitive_Decimal;
            totalCost: everest_appserver_primitive_Decimal;
            condition: everest_appserver_primitive_Text;
            disposition: everest_appserver_primitive_Text;
            reasonCodeId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["reasonCodeId"];
            status: everest_appserver_primitive_Text;
            receivedDate: everest_appserver_primitive_PlainDate;
            inspectedDate: everest_appserver_primitive_PlainDate;
            processedDate: everest_appserver_primitive_PlainDate;
            notes: everest_appserver_primitive_Text;
            inspectionNotes: everest_appserver_primitive_Text;
          }[] | null;
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            id: number;
            data: Data;
          };

          export type Response = void;
        }

        export interface Implementation {
          execute(input: Execute.Request): Promise<Execute.Response>;
        }
      }

      export namespace Delete {
        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            ids: number[];
          };

          export type Response = void;
        }

        export interface Implementation {
          execute(input: Execute.Request): Promise<Execute.Response>;
        }
      }

      export type Implementation = {
        query: Query.Implementation;
        create: Create.Implementation;
        update: Update.Implementation;
        delete: Delete.Implementation;
      };
    }
    export namespace Lines {
      export namespace Query {
        export type Instance = {
          id?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["id"] | undefined;
          headerId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["headerId"] | undefined;
          itemId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["itemId"] | undefined;
          locationId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["locationId"] | undefined;
          unitId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["unitId"] | undefined;
          sourceDocumentLineId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineId"] | undefined;
          sourceDocumentLineType?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineType"] | undefined;
          quantity?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["quantity"] | undefined;
          inventoryQuantity?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["inventoryQuantity"] | undefined;
          openQuantity?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["openQuantity"] | undefined;
          unitPrice?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["unitPrice"] | undefined;
          status?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["status"] | undefined;
          active?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["active"] | undefined;
          createdBy?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["createdBy"] | undefined;
          createdDate?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["createdDate"] | undefined;
          lastModifiedBy?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["lastModifiedBy"] | undefined;
          lastModifiedDate?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["lastModifiedDate"] | undefined;
        };

        export type Order = {
          field: keyof Instance;
          ordering: 'asc' | 'desc';
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            where: Filter<Instance>;
            orderBy: ReadonlyArray<Order>;
            skip: number | undefined;
            take: number | undefined;
            fieldList: ReadonlyArray<keyof Instance>;
            count: boolean;
          };

          export type Response = {
            instances: Instance[];

            /**
             * If count was set to true on the request object, this property
             * must be set, otherwise it must evaluate to undefined.
             */
            count?: number | undefined;
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export namespace Create {
        export type Input = {
          headerId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["headerId"] | undefined;
          itemId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["itemId"] | undefined;
          locationId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["locationId"] | undefined;
          unitId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["unitId"] | undefined;
          sourceDocumentLineId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineId"] | undefined;
          sourceDocumentLineType?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineType"] | undefined;
          quantity?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["quantity"] | undefined;
          inventoryQuantity?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["inventoryQuantity"] | undefined;
          openQuantity?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["openQuantity"] | undefined;
          unitPrice?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["unitPrice"] | undefined;
          status?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["status"] | undefined;
          active?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["active"] | undefined;
        };

        export type Instance = {
          id: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["id"];
          headerId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["headerId"] | undefined;
          itemId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["itemId"] | undefined;
          locationId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["locationId"] | undefined;
          unitId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["unitId"] | undefined;
          sourceDocumentLineId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineId"] | undefined;
          sourceDocumentLineType?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineType"] | undefined;
          quantity?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["quantity"] | undefined;
          inventoryQuantity?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["inventoryQuantity"] | undefined;
          openQuantity?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["openQuantity"] | undefined;
          unitPrice?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["unitPrice"] | undefined;
          status?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["status"] | undefined;
          active?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["active"] | undefined;
          createdBy?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["createdBy"] | undefined;
          createdDate?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["createdDate"] | undefined;
          lastModifiedBy?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["lastModifiedBy"] | undefined;
          lastModifiedDate?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["lastModifiedDate"] | undefined;
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            inputs: Input[];
          };

          export type Response = {
            instances: Instance[];
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export namespace Update {
        export type Data = {
          headerId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["headerId"] | null;
          itemId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["itemId"] | null;
          locationId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["locationId"] | null;
          unitId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["unitId"] | null;
          sourceDocumentLineId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineId"] | null;
          sourceDocumentLineType?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineType"] | null;
          quantity?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["quantity"] | null;
          inventoryQuantity?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["inventoryQuantity"] | null;
          openQuantity?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["openQuantity"] | null;
          unitPrice?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["unitPrice"] | null;
          status?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["status"] | null;
          active?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["active"] | null;
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            id: number;
            data: Data;
          };

          export type Response = void;
        }

        export interface Implementation {
          execute(input: Execute.Request): Promise<Execute.Response>;
        }
      }

      export namespace Delete {
        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            ids: number[];
          };

          export type Response = void;
        }

        export interface Implementation {
          execute(input: Execute.Request): Promise<Execute.Response>;
        }
      }

      export type Implementation = {
        query: Query.Implementation;
        create: Create.Implementation;
        update: Update.Implementation;
        delete: Delete.Implementation;
      };
    }
  }

  export namespace Actions {
    export namespace CreateSalesReturn {
      export type Input = {
        businessPartnerId: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["businessPartnerId"];
        documentDate: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["documentDate"];
        documentNumber: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["documentNumber"];
        entityId: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["entityId"];
        notes: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["notes"];
        status: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["status"];
        lines: {
          lineNumber: everest_appserver_primitive_Number;
          itemId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["itemId"];
          locationId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["locationId"];
          unitId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["unitId"];
          sourceDocumentLineId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineId"];
          sourceDocumentLineType: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineType"];
          quantity: everest_appserver_primitive_Decimal;
          inventoryQuantity: everest_appserver_primitive_Decimal;
          unitPrice: everest_appserver_primitive_Decimal;
          unitCost: everest_appserver_primitive_Decimal;
          condition: everest_appserver_primitive_Text;
          disposition: everest_appserver_primitive_Text;
          reasonCodeId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["reasonCodeId"];
          notes: everest_appserver_primitive_Text;
        }[];
      };

      export type Output = {
        success: everest_appserver_primitive_TrueFalse;
        salesReturnId: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["id"];
        documentNumber: everest_appserver_primitive_Text;
        warnings: everest_appserver_primitive_Text[];
        errors: everest_appserver_primitive_Text[];
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace UpdateSalesReturn {
      export type Input = {
        id: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["id"];
        businessPartnerId: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["businessPartnerId"];
        documentDate: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["documentDate"];
        documentNumber: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["documentNumber"];
        entityId: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["entityId"];
        notes: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["notes"];
        status: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["status"];
        externalId: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["externalId"];
        lines: {
          id: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["id"];
          lineId?: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["id"];
          lineNumber: everest_appserver_primitive_Number;
          itemId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["itemId"];
          locationId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["locationId"];
          unitId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["unitId"];
          sourceDocumentLineId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineId"];
          sourceDocumentLineType: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineType"];
          quantity: everest_appserver_primitive_Decimal;
          inventoryQuantity: everest_appserver_primitive_Decimal;
          unitPrice: everest_appserver_primitive_Decimal;
          condition: everest_appserver_primitive_Text;
          disposition: everest_appserver_primitive_Text;
          reasonCodeId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["reasonCodeId"];
        }[];
        linesToRemove: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["id"][];
      };

      export type Output = {
        success: everest_appserver_primitive_TrueFalse;
        salesReturnId: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["id"];
        documentNumber: everest_appserver_primitive_Text;
        errorMessage: everest_appserver_primitive_Text;
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace ApproveSalesReturn {
      export type Input = {
        salesReturnId: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["id"];
        approvalNotes: everest_appserver_primitive_Text;
      };

      export type Output = {
        success: everest_appserver_primitive_TrueFalse;
        updatedStatus: everest_appserver_primitive_Text;
        inventoryTransactionIds: everest_appserver_primitive_Number[];
        warnings: everest_appserver_primitive_Text[];
        id: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["id"];
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace ReceiveReturnedGoods {
      export type Input = {
        salesReturnId: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["id"];
        receivedDate: everest_appserver_primitive_Date;
        notes: everest_appserver_primitive_Text;
        lines: {
          lineId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["id"];
          receivedQuantity: everest_appserver_primitive_Decimal;
          notes: everest_appserver_primitive_Text;
        }[];
      };

      export type Output = {
        success: everest_appserver_primitive_TrueFalse;
        updatedStatus: everest_appserver_primitive_Text;
        inventoryTransactionIds: everest_appserver_primitive_Number[];
        warnings: everest_appserver_primitive_Text[];
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace InspectReturnedGoods {
      export type Input = {
        salesReturnId: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["id"];
        inspectedDate: everest_appserver_primitive_Date;
        inspectorId: everest_appserver_primitive_Number;
        notes: everest_appserver_primitive_Text;
        lines: {
          lineId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["id"];
          inspectedQuantity: everest_appserver_primitive_Decimal;
          approvedQuantity: everest_appserver_primitive_Decimal;
          rejectedQuantity: everest_appserver_primitive_Decimal;
          condition: everest_appserver_primitive_Text;
          disposition: everest_appserver_primitive_Text;
          qualityGrade: everest_appserver_primitive_Text;
          inspectionNotes: everest_appserver_primitive_Text;
          rejectionReason: everest_appserver_primitive_Text;
          inspectionDetails: {
            sequenceNumber: everest_appserver_primitive_Number;
            inspectedQuantity: everest_appserver_primitive_Decimal;
            approvalStatus: everest_appserver_primitive_Text;
            condition: everest_appserver_primitive_Text;
            disposition: everest_appserver_primitive_Text;
            qualityGrade: everest_appserver_primitive_Text;
            inspectionNotes: everest_appserver_primitive_Text;
            rejectionReason: everest_appserver_primitive_Text;
          }[];
        }[];
      };

      export type Output = {
        success: everest_appserver_primitive_TrueFalse;
        updatedStatus: everest_appserver_primitive_Text;
        inspectionResults: {
          totalInspected: everest_appserver_primitive_Decimal;
          totalApproved: everest_appserver_primitive_Decimal;
          totalRejected: everest_appserver_primitive_Decimal;
          overallGrade: everest_appserver_primitive_Text;
        };
        warnings: everest_appserver_primitive_Text[];
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace CreatePutAwayTasksFromInspection {
      export type Input = {
        salesReturnId: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["id"];
        inspectionWarehouseId: everest_inventory_model_node_IMWarehouseLocation.IMWarehouseLocation["id"];
        createPutAwayTasks: everest_appserver_primitive_TrueFalse;
        assignedUserId: everest_appserver_primitive_Number;
        priority: everest_appserver_primitive_Text;
        strategy: everest_appserver_primitive_Text;
        scheduledDate: everest_appserver_primitive_Date;
        dueDate: everest_appserver_primitive_Date;
        notes: everest_appserver_primitive_Text;
      };

      export type Output = {
        success: everest_appserver_primitive_TrueFalse;
        putAwayTasksCreated: everest_appserver_primitive_Number;
        putAwayTaskIds: everest_appserver_primitive_Number[];
        message: everest_appserver_primitive_Text;
        warnings: everest_appserver_primitive_Text[];
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace ProcessReturnDisposition {
      export type Input = {
        salesReturnId: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["id"];
        processedDate: everest_appserver_primitive_Date;
        notes: everest_appserver_primitive_Text;
      };

      export type Output = {
        success: everest_appserver_primitive_TrueFalse;
        updatedStatus: everest_appserver_primitive_Text;
        inventoryTransactionIds: everest_appserver_primitive_Number[];
        warnings: everest_appserver_primitive_Text[];
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace CompleteSalesReturn {
      export type Input = {
        salesReturnId: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["id"];
        completionNotes: everest_appserver_primitive_Text;
      };

      export type Output = {
        success: everest_appserver_primitive_TrueFalse;
        updatedStatus: everest_appserver_primitive_Text;
        creditMemoNumber: everest_appserver_primitive_Text;
        warnings: everest_appserver_primitive_Text[];
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace GetSalesReturnWithDetails {
      export type Input = {
        salesReturnId: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["id"];
      };

      export type Output = {
        salesReturn: {
          id: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["id"];
          uuid: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["uuid"];
          externalId: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["externalId"];
          active: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["active"];
          businessPartnerId: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["businessPartnerId"];
          documentDate: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["documentDate"];
          documentNumber: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["documentNumber"];
          entityId: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["entityId"];
          notes: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["notes"];
          status: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["status"];
          createdBy: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["createdBy"];
          createdDate: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["createdDate"];
          lastModifiedBy: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["lastModifiedBy"];
          lastModifiedDate: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["lastModifiedDate"];
          customerName: everest_appserver_primitive_Text;
        };
        lines: {
          id: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["id"];
          lineNumber: everest_appserver_primitive_Number;
          itemId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["itemId"];
          itemCode: everest_appserver_primitive_Text;
          itemName: everest_appserver_primitive_Text;
          locationId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["locationId"];
          locationName: everest_appserver_primitive_Text;
          unitId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["unitId"];
          unitCode: everest_appserver_primitive_Text;
          sourceDocumentLineId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineId"];
          sourceDocumentLineType: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["sourceDocumentLineType"];
          quantity: everest_appserver_primitive_Decimal;
          inventoryQuantity: everest_appserver_primitive_Decimal;
          openQuantity: everest_appserver_primitive_Decimal;
          receivedQuantity: everest_appserver_primitive_Decimal;
          inspectedQuantity: everest_appserver_primitive_Decimal;
          processedQuantity: everest_appserver_primitive_Decimal;
          unitPrice: everest_appserver_primitive_Decimal;
          totalPrice: everest_appserver_primitive_Decimal;
          unitCost: everest_appserver_primitive_Decimal;
          totalCost: everest_appserver_primitive_Decimal;
          condition: everest_appserver_primitive_Text;
          disposition: everest_appserver_primitive_Text;
          reasonCodeId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["reasonCodeId"];
          reasonCodeDescription: everest_appserver_primitive_Text;
          status: everest_appserver_primitive_Text;
          receivedDate: everest_appserver_primitive_PlainDate;
          inspectedDate: everest_appserver_primitive_PlainDate;
          processedDate: everest_appserver_primitive_PlainDate;
          notes: everest_appserver_primitive_Text;
          inspectionNotes: everest_appserver_primitive_Text;
          active: everest_appserver_primitive_TrueFalse;
        }[];
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace ListSalesReturnsWithFilters {
      export type Input = {
        status: everest_appserver_primitive_Text;
        businessPartnerId: everest_appserver_primitive_Number;
        dateFrom: everest_appserver_primitive_Date;
        dateTo: everest_appserver_primitive_Date;
        documentNumber: everest_appserver_primitive_Text;
        limit: everest_appserver_primitive_Number;
        offset: everest_appserver_primitive_Number;
      };

      export type Output = {
        salesReturns: {
          id: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["id"];
          documentNumber: everest_appserver_primitive_Text;
          status: everest_appserver_primitive_Text;
          businessPartnerId: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["businessPartnerId"];
          customerName: everest_appserver_primitive_Text;
          documentDate: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["documentDate"];
          createdDate: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["createdDate"];
          lastModifiedDate: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["lastModifiedDate"];
        }[];
        totalCount: everest_appserver_primitive_Number;
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace GetCustomerById {
      export type Input = {
        customerId: everest_appserver_primitive_ID;
      };

      export type Output = {
        customer: {
          id: everest_appserver_primitive_ID;
          name: everest_appserver_primitive_Text;
          customerNumber: everest_appserver_primitive_Text;
          email: everest_appserver_primitive_Text;
          phone: everest_appserver_primitive_Text;
          address: everest_appserver_primitive_Text;
          city: everest_appserver_primitive_Text;
          stateProvince: everest_appserver_primitive_Text;
          postalCode: everest_appserver_primitive_Text;
          country: everest_appserver_primitive_Text;
          customerType: everest_fin_accounting_model_node_Customer.Customer["customerType"];
          status: everest_fin_accounting_model_node_Customer.Customer["status"];
          createdDate: everest_appserver_primitive_Date;
          lastModifiedDate: everest_appserver_primitive_Date;
        };
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace GetInspectionDetails {
      export type Input = {
        salesReturnLineId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["id"];
      };

      export type Output = {
        inspectionDetails: {
          id: everest_inventory_model_node_IMInventorySalesReturnInspectionDetail.IMInventorySalesReturnInspectionDetail["id"];
          documentLineId: everest_inventory_model_node_IMInventorySalesReturnInspectionDetail.IMInventorySalesReturnInspectionDetail["documentLineId"];
          inspectedQuantity: everest_appserver_primitive_Decimal;
          approvalStatus: everest_appserver_primitive_Text;
          condition: everest_appserver_primitive_Text;
          disposition: everest_appserver_primitive_Text;
          qualityGrade: everest_appserver_primitive_Text;
          inspectionNotes: everest_appserver_primitive_Text;
          rejectionReason: everest_appserver_primitive_Text;
          createdDate: everest_appserver_primitive_Date;
          createdBy: everest_appserver_primitive_Text;
        }[];
        totalDetails: everest_appserver_primitive_Number;
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace GetInspectionHistory {
      export type Input = {
        salesReturnId: everest_inventory_model_node_IMInventorySalesReturn.IMInventorySalesReturn["id"];
      };

      export type Output = {
        inspectionHistory: {
          id: everest_appserver_primitive_Number;
          lineId: everest_inventory_model_node_IMInventorySalesReturnLine.IMInventorySalesReturnLine["id"];
          itemCode: everest_appserver_primitive_Text;
          itemName: everest_appserver_primitive_Text;
          inspectedDate: everest_appserver_primitive_Date;
          inspectorName: everest_appserver_primitive_Text;
          inspectedQuantity: everest_appserver_primitive_Decimal;
          approvedQuantity: everest_appserver_primitive_Decimal;
          rejectedQuantity: everest_appserver_primitive_Decimal;
          condition: everest_appserver_primitive_Text;
          disposition: everest_appserver_primitive_Text;
          qualityGrade: everest_appserver_primitive_Text;
          inspectionNotes: everest_appserver_primitive_Text;
          rejectionReason: everest_appserver_primitive_Text;
          inspectionDetails: {
            id: everest_inventory_model_node_IMInventorySalesReturnInspectionDetail.IMInventorySalesReturnInspectionDetail["id"];
            sequenceNumber: everest_appserver_primitive_Number;
            inspectedQuantity: everest_appserver_primitive_Decimal;
            approvalStatus: everest_appserver_primitive_Text;
            condition: everest_appserver_primitive_Text;
            disposition: everest_appserver_primitive_Text;
            qualityGrade: everest_appserver_primitive_Text;
            inspectionNotes: everest_appserver_primitive_Text;
            rejectionReason: everest_appserver_primitive_Text;
          }[];
        }[];
        totalInspections: everest_appserver_primitive_Number;
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }
  }

  export type Implementation = {
    Customers: EntitySets.Customers.Implementation;
    SalesReturns: EntitySets.SalesReturns.Implementation;
    Lines: EntitySets.Lines.Implementation;
    CreateSalesReturn: Actions.CreateSalesReturn.Implementation;
    UpdateSalesReturn: Actions.UpdateSalesReturn.Implementation;
    ApproveSalesReturn: Actions.ApproveSalesReturn.Implementation;
    ReceiveReturnedGoods: Actions.ReceiveReturnedGoods.Implementation;
    InspectReturnedGoods: Actions.InspectReturnedGoods.Implementation;
    CreatePutAwayTasksFromInspection: Actions.CreatePutAwayTasksFromInspection.Implementation;
    ProcessReturnDisposition: Actions.ProcessReturnDisposition.Implementation;
    CompleteSalesReturn: Actions.CompleteSalesReturn.Implementation;
    GetSalesReturnWithDetails: Actions.GetSalesReturnWithDetails.Implementation;
    ListSalesReturnsWithFilters: Actions.ListSalesReturnsWithFilters.Implementation;
    GetCustomerById: Actions.GetCustomerById.Implementation;
    GetInspectionDetails: Actions.GetInspectionDetails.Implementation;
    GetInspectionHistory: Actions.GetInspectionHistory.Implementation;
  };
}

