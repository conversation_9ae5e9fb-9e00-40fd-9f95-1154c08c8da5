{"version": 2, "uimodel": {"state": {}, "nodes": {"paymentProviders": {"type": "list", "query": {"where": {"active": true}}, "modelId": "everest.fin.payment/PaymentProviderModel.PaymentProvider", "fieldList": ["id", "providerName", "paymentMethodIntegrationFragment"]}}}, "uicontroller": ["manageProviderPaymentMethods.uicontroller.ts", "everest.fin.payment/common/uinext/general.uicontroller.ts"], "uiview": {"config": {"autoRefreshData": true}, "header": {"margin": "360px", "filter": {"defaultValue": "@controller:ManageProviderPaymentMethodsTemplate.getDefaultPaymentProviderName()", "items": "@controller:ManageProviderPaymentMethodsTemplate.getPaymentProvidersItems()", "onChange": "@controller:ManageProviderPaymentMethodsTemplate.onPaymentProviderChange"}}, "sections": {"content": [{"@uifragment": {"path": "everest.fin.payment/providerPaymentMethod/integration/uinext/stripePaymentMethod.uifragment.json", "props": {}}}]}}}