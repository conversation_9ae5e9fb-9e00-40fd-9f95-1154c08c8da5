{"version": 2, "uicontroller": "mediationFunction.uicontroller.ts", "uimodel": {"nodes": {"mediationFilters": {"type": "list", "model": "urn:evst:everest:appserver:model/node:ValueHelp", "fieldList": ["text", "codeValue"], "query": {"orderBy": [{"field": "text", "ordering": "asc"}], "where": {"urn": "urn:evst:everest:fin/integration/base:enum:MediationFilters", "language": "en"}}}, "intacct": {"type": "list", "modelId": "everest.fin.integration.intacct/IntacctModel.Intacct"}, "mediationFunctions": {"type": "list", "modelId": "everest.fin.integration.base/MediationFunctionModel.MediationFunction", "query": {"where": {"targetModel": "@state:intacctName"}}, "fieldList": ["name", "mediationFunction", "args"]}}}, "uiview": {"i18n": "everest.fin.integration.base/mapping", "sections": [{"component": "Table", "section": {"grid": {"size": "12"}}, "props": {"data": "@binding:mediationFunctions", "variant": "light", "columns": ["name", "mediationFunction", "args"], "rowActions": {"variant": "default", "columnPosition": -1, "pinned": "left", "actions": [{"content": "Delete", "onClick": "@controller:deleteRowClick"}]}}}, {"component": "Block", "size": "12", "type": "secondary", "elements": [{"component": "Input", "label": "{{mapping.key}}", "isEditing": true, "name": "key"}, {"component": "Select", "label": "{{mapping.mediationFilters}}", "list": "@binding:mediationFilters", "isEditing": true, "idProp": "codeValue", "textProp": "text", "name": "filterWith"}, {"component": "Input", "label": "{{mapping.value}}", "isEditing": true, "name": "value"}, {"component": "Input", "label": "{{mapping.name}}", "isEditing": true, "name": "name"}, {"component": "Checkbox", "label": "{{mapping.filterOut}}", "isEditing": true, "name": "filterOut"}]}, {"component": "ButtonGroup", "actions": [{"variant": "primary", "label": "{{mapping.create}}", "onClick": "@controller:getOnApply"}]}]}}