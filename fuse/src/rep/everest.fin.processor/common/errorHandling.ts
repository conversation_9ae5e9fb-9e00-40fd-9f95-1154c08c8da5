import type { ISession } from '@everestsystems/content-core';
import {
  BusinessError,
  getTranslations,
  log,
} from '@everestsystems/content-core';
import { replaceInStringTemplate } from '@pkg/everest.base/public/utils/templatingUtils';
import {
  FINANCIAL_DATA_PROCESSOR_ERROR_MESSAGES_MAP,
  type FINANCIAL_DATA_PROCESSOR_ERRORS,
} from '@pkg/everest.fin.processor/common/constants';
import { isEmpty } from 'lodash';

export namespace ErrorHandling {
  export async function getErrorMessages(
    session: ISession
  ): Promise<Map<string, string>> {
    const errorMessages = await getTranslations(
      session,
      [
        'action.client.exclusive.error',
        'action.runProcess.accountingStandardWrongConfigError',
        'action.runProcess.noAccountingStandardInstalledError',
        'action.runProcess.noEntityBookError',
        'action.runProcess.ruleHasWrongConfigError',
        'action.runRuleAlgorithm.providerNameError',
        'action.runProcess.noEntityBooksInSystemError',
        'action.runProcess.processScopeIsEmptyError',
        'action.runProcess.pipelineOperationalModeWrongConfigError',
      ],
      'everest.fin.processor/financialProcessor'
    );
    return new Map<string, string>([
      ['action.client.exclusive.error', errorMessages[0]],
      [
        'action.runProcess.accountingStandardWrongConfigError',
        errorMessages[1],
      ],
      [
        'action.runProcess.noAccountingStandardInstalledError',
        errorMessages[2],
      ],
      ['action.runProcess.noEntityBookError', errorMessages[3]],
      ['action.runProcess.ruleHasWrongConfigError', errorMessages[4]],
      ['action.runProcess.ruleHasWrongConfigError', errorMessages[5]],
      ['action.runProcess.noEntityBooksInSystemError', errorMessages[6]],
      ['action.runProcess.processScopeIsEmptyError', errorMessages[7]],
      [
        'action.runProcess.pipelineOperationalModeWrongConfigError',
        errorMessages[8],
      ],
    ]);
  }

  export async function getErrorInstance(
    session: ISession,
    errorCode: FINANCIAL_DATA_PROCESSOR_ERRORS.ERROR_001,
    messageParams?: Record<string, unknown>,
    logError?: boolean
  ): Promise<BusinessError>;
  export async function getErrorInstance(
    session: ISession,
    errorCode: FINANCIAL_DATA_PROCESSOR_ERRORS.ERROR_002,
    messageParams: {
      accountingStandard: string;
    },
    logError?: boolean
  ): Promise<BusinessError>;
  export async function getErrorInstance(
    session: ISession,
    errorCode: FINANCIAL_DATA_PROCESSOR_ERRORS.ERROR_003,
    messageParams: {
      accountingStandard: string;
    },
    logError?: boolean
  ): Promise<BusinessError>;
  export async function getErrorInstance(
    session: ISession,
    errorCode: FINANCIAL_DATA_PROCESSOR_ERRORS.ERROR_004,
    messageParams: {
      entityIds: number[];
    },
    logError?: boolean
  ): Promise<BusinessError>;
  export async function getErrorInstance(
    session: ISession,
    errorCode: FINANCIAL_DATA_PROCESSOR_ERRORS.ERROR_005,
    messageParams: {
      accountStandartRuleId: number;
    },
    logError?: boolean
  ): Promise<BusinessError>;
  export async function getErrorInstance(
    session: ISession,
    errorCode: FINANCIAL_DATA_PROCESSOR_ERRORS.ERROR_006,
    messageParams: {
      providerName: string;
    },
    logError?: boolean
  ): Promise<BusinessError>;
  export async function getErrorInstance(
    session: ISession,
    errorCode: FINANCIAL_DATA_PROCESSOR_ERRORS.ERROR_007,
    messageParams?: unknown,
    logError?: boolean
  ): Promise<BusinessError>;
  export async function getErrorInstance(
    session: ISession,
    errorCode: FINANCIAL_DATA_PROCESSOR_ERRORS.ERROR_008,
    messageParams?: unknown,
    logError?: boolean
  ): Promise<BusinessError>;
  export async function getErrorInstance(
    session: ISession,
    errorCode: FINANCIAL_DATA_PROCESSOR_ERRORS.ERROR_009,
    messageParams?: unknown,
    logError?: boolean
  ): Promise<BusinessError>;
  export async function getErrorInstance(
    session: ISession,
    errorCode: FINANCIAL_DATA_PROCESSOR_ERRORS.ERROR_010,
    messageParams?: unknown,
    logError?: boolean
  ): Promise<BusinessError>;
  export async function getErrorInstance(
    session: ISession,
    errorCode: FINANCIAL_DATA_PROCESSOR_ERRORS.ERROR_011,
    messageParams?: unknown,
    logError?: boolean
  ): Promise<BusinessError>;
  export async function getErrorInstance(
    session: ISession,
    errorCode: FINANCIAL_DATA_PROCESSOR_ERRORS.ERROR_012,
    messageParams?: unknown,
    logError?: boolean
  ): Promise<BusinessError>;
  export async function getErrorInstance(
    session: ISession,
    errorCode: FINANCIAL_DATA_PROCESSOR_ERRORS.ERROR_013,
    messageParams: {
      accountingStandard: string;
    },
    logError?: boolean
  ): Promise<BusinessError>;
  export async function getErrorInstance(
    session: ISession,
    errorCode: FINANCIAL_DATA_PROCESSOR_ERRORS.ERROR_014,
    messageParams: {
      accountingStandard: string;
    },
    logError?: boolean
  ): Promise<BusinessError>;
  export async function getErrorInstance(
    session: ISession,
    errorCode: FINANCIAL_DATA_PROCESSOR_ERRORS,
    messageParams: Record<string, unknown> = {},
    logError: boolean = true
  ): Promise<BusinessError> {
    const errorMessages = await getErrorMessages(session);
    const errorMessageCode =
      FINANCIAL_DATA_PROCESSOR_ERROR_MESSAGES_MAP.get(errorCode);
    let errorMessage = errorMessages.get(errorMessageCode);

    if (!isEmpty(messageParams)) {
      const messageParamsCoersed = Object.fromEntries(
        Object.entries(messageParams).map(([key, value]) => [
          key,
          String(value),
        ])
      );
      errorMessage = replaceInStringTemplate(
        errorMessage,
        messageParamsCoersed,
        '{{',
        '}}'
      );
    }

    if (logError) {
      log.error(
        {
          errorCode,
          errorMessage,
        },
        `Financial Data Processor Error: ${errorMessageCode}`
      );
    }

    return new BusinessError(errorMessage, errorCode);
  }
}
