package everest.fin.processor

@metadata {
	active: true
}
public enum FinancialRoutine {
	
	@metadata {
		description: 'Reconciliation'
		englishText: 'Reconciliation'
	}
	reconciliation
	@metadata {
		description: 'Calculates Currency Revaluation for Cash Accounts'
		englishText: 'Calculates Currency Revaluation for Cash Accounts'
	}
	currencyRevalCashAccountsCalculation
	@metadata {
		description: 'Calculates Currency Revaluation for Non Cash Accounts'
		englishText: 'Calculates Currency Revaluation for Non Cash Accounts'
	}
	currencyRevalNonCashAccountsCalculation
}
