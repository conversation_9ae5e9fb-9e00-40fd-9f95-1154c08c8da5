package everest.fin.processor

@metadata {
	active: true
}
public enum FinancialProcess {
	
	@metadata {
		englishText: 'Upsert Journal Entry'
	}
	upsertJournalEntry
	@metadata {
		englishText: 'Delete Journal Entry'
	}
	deleteJournalEntry
	@metadata {
		englishText: 'Void Journal Entry'
	}
	voidJournalEntry
	@metadata {
		englishText: 'Reverse Journal Entry'
	}
	reverseJournalEntry
	@metadata {
		englishText: 'Create Manual Journal Entry'
	}
	createManualJournalEntry
	@metadata {
		englishText: 'Calculate Cash Accounts Revaluation'
	}
	calculateCashAccountsRevaluation
	@metadata {
		englishText: 'Calculate Non Cash Accounts Revaluation'
	}
	calculateNonCashAccountsRevaluation
}
