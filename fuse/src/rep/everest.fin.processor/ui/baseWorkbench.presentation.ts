//@i18n:baseWorkbench
import type {
  FieldSelector,
  RoutineValidationResult,
  TypeSafeQueryArgType,
} from '@everestsystems/content-core';
import {
  DATA,
  getTranslations,
  IDENTIFIER,
  METADATA,
  ModelUrn,
} from '@everestsystems/content-core';
import { AccountingStandard } from '@pkg/everest.fin.base/types/AccountingStandard';
import type { EvstAlgorithmEndpoints } from '@pkg/everest.fin.processor/types/composites/AlgorithmEndpoints';
import { EvstBaseExecutionPoints } from '@pkg/everest.fin.processor/types/enums/BaseExecutionPoints';
import { FinancialCalculationRule } from '@pkg/everest.fin.processor/types/FinancialCalculationRule';
import { FinancialTransactionRule } from '@pkg/everest.fin.processor/types/FinancialTransactionRule';
import { InstalledAccountingStandard } from '@pkg/everest.fin.processor/types/InstalledAccountingStandard';
import { financialProcessorHelperConnectivity } from '@pkg/everest.fin.processor/types/interfaces/financialProcessorHelperConnectivity';
import type { baseWorkbenchPresentation } from '@pkg/everest.fin.processor/types/presentations/ui/baseWorkbench';
import { difference, get, isEmpty, pick, set } from 'lodash';

export namespace BaseWorkbenchDataSourceClassTypes {
  export type DataType = {
    InstalledAccountingStandards: (baseWorkbenchPresentation.dataSources.BaseWorkbenchDataSource.levels['InstalledAccountingStandards'] & {
      isDeleted: boolean;
      [IDENTIFIER]: string | number;
    })[];
    FinancialTransactionRules: (baseWorkbenchPresentation.dataSources.BaseWorkbenchDataSource.levels['FinancialTransactionRules'] & {
      isDeleted: boolean;
      [IDENTIFIER]: string | number;
    })[];
    FinancialCalculationRules: (baseWorkbenchPresentation.dataSources.BaseWorkbenchDataSource.levels['FinancialCalculationRules'] & {
      isDeleted: boolean;
      [IDENTIFIER]: string | number;
    })[];
  };
  export type PreparedDataInstalledAccountingStandards = {
    linesToDelete: DataType['InstalledAccountingStandards'][number]['id'][];
    linesToCreate: DataType['InstalledAccountingStandards'];
    linesToUpdate: DataType['InstalledAccountingStandards'];
  };
  export type PreparedDataFinancialTransactionRules = {
    linesToDelete: DataType['FinancialTransactionRules'][number]['id'][];
    linesToCreate: DataType['FinancialTransactionRules'];
    linesToUpdate: DataType['FinancialTransactionRules'];
  };
  export type PreparedDataFinancialCalculationRules = {
    linesToDelete: DataType['FinancialCalculationRules'][number]['id'][];
    linesToCreate: DataType['FinancialCalculationRules'];
    linesToUpdate: DataType['FinancialCalculationRules'];
  };
  export type PreparedData =
    | PreparedDataInstalledAccountingStandards
    | PreparedDataFinancialTransactionRules
    | PreparedDataFinancialCalculationRules;

  export interface SelectElement {
    value: string;
    label: string;
    decription?: string;
  }

  export type SelectList =
    Array<BaseWorkbenchDataSourceClassTypes.SelectElement>;
}

export class BaseWorkbenchDataSourceClass
  implements
    baseWorkbenchPresentation.dataSources.BaseWorkbenchDataSource
      .implementation
{
  // STAIC PROERTIES
  static financialTransactionRuleFieldlist = [
    'id',
    'isActive',
    'package',
    'accountingStandard',
    'financialObject',
    'processName',
    'baseExecutionPoint',
    'algorithmExecutionOrder',
    'algorithmContainer',
    'algorithmName',
    'resultSaveKey',
  ] as const satisfies FieldSelector<FinancialTransactionRule.FinancialTransactionRuleWithAssociation>[];

  static financialCalculationRuleFieldlist = [
    'id',
    'isActive',
    'package',
    'accountingStandard',
    'routineName',
    'algorithmExecutionOrder',
    'algorithmContainer',
    'algorithmName',
    'resultSaveKey',
  ] as const satisfies FieldSelector<FinancialCalculationRule.FinancialCalculationRuleWithAssociation>[];

  static accountingStandardsCacheFields = [
    'isBase',
    'package',
  ] as const satisfies (keyof AccountingStandard.AccountingStandard)[];

  static validateInstalledAccountingStandardFields = [
    'accountingStandard',
    'package',
    'providerName',
  ] as const satisfies FieldSelector<InstalledAccountingStandard.InstalledAccountingStandard>[];
  static validateFinancialTransactionRuleField = [
    'package',
    'accountingStandard',
    'processName',
    'financialObject',
    'baseExecutionPoint',
    'algorithmExecutionOrder',
    'algorithmContainer',
    'algorithmName',
  ] as const satisfies FieldSelector<FinancialTransactionRule.FinancialTransactionRule>[];
  static validateFinancialCalculationRuleField = [
    'package',
    'accountingStandard',
    'routineName',
    'algorithmExecutionOrder',
    'algorithmContainer',
    'algorithmName',
  ] as const satisfies FieldSelector<FinancialCalculationRule.FinancialCalculationRule>[];

  // PRIVATE PROPERTIES

  #data: BaseWorkbenchDataSourceClassTypes.DataType | undefined;

  #accountingStandardsCache: Map<
    AccountingStandard.AccountingStandard['name'],
    Pick<
      AccountingStandard.AccountingStandard,
      (typeof BaseWorkbenchDataSourceClass.accountingStandardsCacheFields)[number]
    >
  >;
  #processAlgorithmsCache: Map<
    FinancialTransactionRule.FinancialTransactionRule['package'],
    EvstAlgorithmEndpoints[]
  >;
  #routineAlgorithmsCache: Map<
    FinancialCalculationRule.FinancialCalculationRule['package'],
    EvstAlgorithmEndpoints[]
  >;

  #errorMessages: Record<string, string>;

  // CONSTRUCTOR

  constructor() {
    this.#data = undefined;
  }

  // PRIVATE METHODS

  private async loadAlgorithmsCache(
    input:
      | baseWorkbenchPresentation.dataSources.BaseWorkbenchDataSource.callbacks.query.input
      | baseWorkbenchPresentation.dataSources.BaseWorkbenchDataSource.callbacks.update_InstalledAccountingStandards.input
  ): Promise<void> {
    this.#processAlgorithmsCache = new Map<
      FinancialTransactionRule.FinancialTransactionRule['package'],
      EvstAlgorithmEndpoints[]
    >();
    this.#routineAlgorithmsCache = new Map<
      FinancialTransactionRule.FinancialTransactionRule['package'],
      EvstAlgorithmEndpoints[]
    >();

    const providerList =
      await financialProcessorHelperConnectivity.getProviderNames(
        input.session.connectivityLayer
      );

    for (const installedAccountingStandard of this.#data
      .InstalledAccountingStandards) {
      if (
        !installedAccountingStandard.providerName ||
        !installedAccountingStandard.package
      ) {
        continue;
      }

      if (!providerList.includes(installedAccountingStandard.providerName)) {
        this.#processAlgorithmsCache.set(
          installedAccountingStandard.package,
          []
        );
        this.#routineAlgorithmsCache.set(
          installedAccountingStandard.package,
          []
        );
      }

      try {
        const algorithmEndpoints =
          await financialProcessorHelperConnectivity.getExportedProcessAlgorithms(
            input.session.connectivityLayer,
            {
              providerName: installedAccountingStandard.providerName,
            }
          );

        this.#processAlgorithmsCache.set(
          installedAccountingStandard.package,
          algorithmEndpoints
        );
      } catch {
        this.#processAlgorithmsCache.set(
          installedAccountingStandard.package,
          []
        );
      }

      try {
        const algorithmEndpoints =
          await financialProcessorHelperConnectivity.getExportedRoutineAlgorithms(
            input.session.connectivityLayer,
            {
              providerName: installedAccountingStandard.providerName,
            }
          );

        this.#routineAlgorithmsCache.set(
          installedAccountingStandard.package,
          algorithmEndpoints
        );
      } catch {
        this.#routineAlgorithmsCache.set(
          installedAccountingStandard.package,
          []
        );
      }
    }
  }

  private async loadData(
    input: baseWorkbenchPresentation.dataSources.BaseWorkbenchDataSource.callbacks.query.input
  ): Promise<void> {
    const [
      installedAccountingStandards,
      financialTransactionRules,
      financialCalculationRules,
      accountingStandards,
    ] = await Promise.all([
      InstalledAccountingStandard.all(input.session),
      FinancialTransactionRule.query(
        input.session,
        {
          where: {
            ...(input.queryInstruction.FinancialTransactionRules
              ?.filters as TypeSafeQueryArgType<FinancialTransactionRule.FinancialTransactionRuleWithAssociation>['where']),
          },
          orderBy: input.queryInstruction.FinancialTransactionRules?.orders,
          take: input.queryInstruction.FinancialTransactionRules?.page?.take,
          skip: input.queryInstruction.FinancialTransactionRules?.page?.skip,
        },
        BaseWorkbenchDataSourceClass.financialTransactionRuleFieldlist
      ),
      FinancialCalculationRule.query(
        input.session,
        {
          where: {
            ...(input.queryInstruction.FinancialCalculationRules
              ?.filters as TypeSafeQueryArgType<FinancialCalculationRule.FinancialCalculationRuleWithAssociation>['where']),
          },
          orderBy: input.queryInstruction.FinancialCalculationRules?.orders,
          take: input.queryInstruction.FinancialCalculationRules?.page?.take,
          skip: input.queryInstruction.FinancialCalculationRules?.page?.skip,
        },
        BaseWorkbenchDataSourceClass.financialCalculationRuleFieldlist
      ),
      AccountingStandard.all(input.session),
    ]);

    this.#data = {
      InstalledAccountingStandards: installedAccountingStandards.map((e) => ({
        ...e,
        isDeleted: false,
        [IDENTIFIER]: e.id,
      })),
      FinancialTransactionRules: financialTransactionRules.map((e) => ({
        ...e,
        isDeleted: false,
        [IDENTIFIER]: e.id,
      })),
      FinancialCalculationRules: financialCalculationRules.map((e) => ({
        ...e,
        isDeleted: false,
        [IDENTIFIER]: e.id,
      })),
    };

    this.#accountingStandardsCache = new Map(
      accountingStandards.map((e) => [
        e.name,
        {
          isBase: e.isBase,
          package: installedAccountingStandards.find(
            (ias) => ias.accountingStandard === e.name
          )?.package,
        },
      ])
    );

    await this.loadAlgorithmsCache(input);
  }

  private getSelectList(
    callback: () => string[],
    isModel: boolean = false
  ): BaseWorkbenchDataSourceClassTypes.SelectList {
    return (
      callback()?.map((e) => ({
        value: e,
        label: isModel ? ModelUrn.parse(e).name : e,
        decription: isModel ? e : undefined,
      })) ?? []
    );
  }

  private getAlgorithmContainerSelectList(
    element: BaseWorkbenchDataSourceClassTypes.DataType['FinancialCalculationRules'][number],
    targetCache: Map<
      FinancialCalculationRule.FinancialCalculationRule['package'],
      EvstAlgorithmEndpoints[]
    >
  ): BaseWorkbenchDataSourceClassTypes.SelectList;
  private getAlgorithmContainerSelectList(
    element: BaseWorkbenchDataSourceClassTypes.DataType['FinancialTransactionRules'][number],
    targetCache: Map<
      FinancialTransactionRule.FinancialTransactionRule['package'],
      EvstAlgorithmEndpoints[]
    >
  ): BaseWorkbenchDataSourceClassTypes.SelectList;
  private getAlgorithmContainerSelectList(
    element:
      | BaseWorkbenchDataSourceClassTypes.DataType['FinancialCalculationRules'][number]
      | BaseWorkbenchDataSourceClassTypes.DataType['FinancialTransactionRules'][number],
    targetCache:
      | Map<
          FinancialTransactionRule.FinancialTransactionRule['package'],
          EvstAlgorithmEndpoints[]
        >
      | Map<
          FinancialCalculationRule.FinancialCalculationRule['package'],
          EvstAlgorithmEndpoints[]
        >
  ): BaseWorkbenchDataSourceClassTypes.SelectList {
    return this.getSelectList(() => {
      const packageEndpoints = targetCache.get(element.package) ?? [];

      const models: string[] = [];
      if (element.algorithmContainer) {
        models.push(element.algorithmContainer);
      }
      models.push(...packageEndpoints.map((ep) => ep.modelUrn));

      const modelsSet = new Set(models);
      return Array.from(modelsSet.values());
    }, true);
  }

  private getAlgorithmNameSelectList(
    element: BaseWorkbenchDataSourceClassTypes.DataType['FinancialCalculationRules'][number],
    targetCache: Map<
      FinancialCalculationRule.FinancialCalculationRule['package'],
      EvstAlgorithmEndpoints[]
    >
  ): BaseWorkbenchDataSourceClassTypes.SelectList;
  private getAlgorithmNameSelectList(
    element: BaseWorkbenchDataSourceClassTypes.DataType['FinancialTransactionRules'][number],
    targetCache: Map<
      FinancialTransactionRule.FinancialTransactionRule['package'],
      EvstAlgorithmEndpoints[]
    >
  ): BaseWorkbenchDataSourceClassTypes.SelectList;
  private getAlgorithmNameSelectList(
    element:
      | BaseWorkbenchDataSourceClassTypes.DataType['FinancialCalculationRules'][number]
      | BaseWorkbenchDataSourceClassTypes.DataType['FinancialTransactionRules'][number],
    targetCache:
      | Map<
          FinancialTransactionRule.FinancialTransactionRule['package'],
          EvstAlgorithmEndpoints[]
        >
      | Map<
          FinancialCalculationRule.FinancialCalculationRule['package'],
          EvstAlgorithmEndpoints[]
        >
  ): BaseWorkbenchDataSourceClassTypes.SelectList {
    return this.getSelectList(() => {
      const packageEndpoints = targetCache.get(element.package) ?? [];
      const targetEndpoint = packageEndpoints.find(
        (ep) => ep.modelUrn === element.algorithmContainer
      );

      const algorithms: string[] = [];
      if (element.algorithmName) {
        algorithms.push(element.algorithmName);
      }
      algorithms.push(...(targetEndpoint?.exposedActions ?? []));

      const algorithmsSet = new Set(algorithms);
      return Array.from(algorithmsSet.values());
    });
  }

  private async returnWithMetadata(
    _input: baseWorkbenchPresentation.dataSources.BaseWorkbenchDataSource.callbacks.query.input
  ): Promise<baseWorkbenchPresentation.dataSources.BaseWorkbenchDataSource.callbacks.query.combinedOutput> {
    const result = {
      [DATA]: {
        FinancialTransactionRules: this.#data.FinancialTransactionRules.map(
          (e) => {
            const validationResult = this.validateProcessRuleExecutionPoint(e);
            return {
              ...e,
              [METADATA]: {
                algorithmContainer: {
                  editable: Boolean(e.package),
                  typeParameters: {
                    packageName: e.package,
                  },
                  fieldProps: {
                    list: this.getAlgorithmContainerSelectList(
                      e,
                      this.#processAlgorithmsCache
                    ),
                  },
                },
                algorithmName: {
                  editable: Boolean(e.package),
                  fieldProps: {
                    list: this.getAlgorithmNameSelectList(
                      e,
                      this.#processAlgorithmsCache
                    ),
                  },
                },
                baseExecutionPoint: {
                  editable:
                    this.#accountingStandardsCache.get(e.accountingStandard)
                      ?.isBase ?? true,
                  fieldProps: {
                    customFieldMessages:
                      validationResult === true
                        ? undefined
                        : { fieldError: get(validationResult, 'message') },
                  },
                },
              },
            };
          }
        ),
        FinancialCalculationRules: this.#data.FinancialCalculationRules.map(
          (e) => ({
            ...e,
            [METADATA]: {
              algorithmContainer: {
                editable: Boolean(e.package),
                typeParameters: {
                  packageName: e.package,
                },
                fieldProps: {
                  list: this.getAlgorithmContainerSelectList(
                    e,
                    this.#routineAlgorithmsCache
                  ),
                },
              },
              algorithmName: {
                editable: Boolean(e.package),
                fieldProps: {
                  list: this.getAlgorithmNameSelectList(
                    e,
                    this.#routineAlgorithmsCache
                  ),
                },
              },
            },
          })
        ),
        InstalledAccountingStandards:
          this.#data.InstalledAccountingStandards.map((e) => ({
            ...e,
            [METADATA]: {},
          })),
      },
      [METADATA]: {
        FinancialTransactionRules:
          /**
           * @author: Oleh Pendrakovskyi
           * @todo: Temporary solution to avoid patch bug with filtering table
           */
          this.#data.FinancialTransactionRules.length > 0
            ? {
                algorithmContainer: {
                  fieldProps: {
                    component: 'Select',
                    idProp: 'value',
                    textProp: 'label',
                    descProp: 'decription',
                  },
                },
                algorithmName: {
                  fieldProps: {
                    component: 'Select',
                    idProp: 'value',
                    textProp: 'label',
                  },
                },
              }
            : undefined,
        FinancialCalculationRules:
          /**
           * @author: Oleh Pendrakovskyi
           * @todo: Temporary solution to avoid patch bug with filtering table
           */
          this.#data.FinancialCalculationRules.length > 0
            ? {
                algorithmContainer: {
                  fieldProps: {
                    component: 'Select',
                    idProp: 'value',
                    textProp: 'label',
                    descProp: 'decription',
                  },
                },
                algorithmName: {
                  fieldProps: {
                    component: 'Select',
                    idProp: 'value',
                    textProp: 'label',
                  },
                },
              }
            : undefined,
      },
    };

    return result;
  }

  private getIndexByIdentifyer(
    containerName: keyof BaseWorkbenchDataSourceClassTypes.DataType,
    identifier: string | number
  ): number | undefined {
    if (!identifier) {
      return;
    }

    const index = this.#data[containerName].findIndex(
      (e) => e[IDENTIFIER] === identifier
    );

    if (index === -1) {
      return;
    }

    return index;
  }

  private setPackage(
    containerName: keyof BaseWorkbenchDataSourceClassTypes.DataType,
    index: number,
    accountingStandardName: AccountingStandard.AccountingStandard['name']
  ): void {
    let accountingStandard: Pick<
      AccountingStandard.AccountingStandard,
      (typeof BaseWorkbenchDataSourceClass.accountingStandardsCacheFields)[number]
    >;
    if (accountingStandardName) {
      accountingStandard = this.#accountingStandardsCache.get(
        accountingStandardName
      );
    }

    if (accountingStandard) {
      set(
        this.#data,
        [containerName, `${index}`, 'package'],
        accountingStandard?.package
      );
    }
  }

  private markLineDeletion(
    containerName: keyof BaseWorkbenchDataSourceClassTypes.DataType,
    index: number,
    isDeleted: boolean
  ): void {
    set(this.#data, [containerName, `${index}`, 'isDeleted'], isDeleted);
  }

  private nullifyValues<T>(input: T): { [K in keyof T]: T[K] | null } {
    return Object.fromEntries(
      Object.entries(input).map(([key, value]) => [key, value ?? null])
    ) as { [K in keyof T]: T[K] | null };
  }

  private getPreparedLinesData(
    containerName: 'InstalledAccountingStandards'
  ): BaseWorkbenchDataSourceClassTypes.PreparedDataInstalledAccountingStandards;
  private getPreparedLinesData(
    containerName: 'FinancialTransactionRules'
  ): BaseWorkbenchDataSourceClassTypes.PreparedDataFinancialTransactionRules;
  private getPreparedLinesData(
    containerName: 'FinancialCalculationRules'
  ): BaseWorkbenchDataSourceClassTypes.PreparedDataFinancialCalculationRules;
  private getPreparedLinesData(
    containerName: keyof BaseWorkbenchDataSourceClassTypes.DataType
  ): BaseWorkbenchDataSourceClassTypes.PreparedData {
    const linesToDelete = this.#data[containerName]
      .filter((e) => e.isDeleted)
      .map((e) => e.id);
    const linesToCreate = this.#data[containerName]
      .filter((e) => !e.isDeleted && !e.id)
      .map((e) => this.nullifyValues(e));
    const linesToUpdate = this.#data[containerName]
      .filter((e) => !e.isDeleted && e.id)
      .map((e) => this.nullifyValues(e));

    return {
      linesToDelete,
      linesToCreate,
      linesToUpdate,
    };
  }

  private validateProcessRuleExecutionPoint(
    line: BaseWorkbenchDataSourceClassTypes.DataType['FinancialTransactionRules'][number]
  ): RoutineValidationResult {
    const accountingStandard = this.#accountingStandardsCache.get(
      line.accountingStandard
    );
    if (
      accountingStandard &&
      accountingStandard.isBase &&
      line.baseExecutionPoint === EvstBaseExecutionPoints.Specific
    ) {
      return {
        message:
          this.#errorMessages[
            'baseWorkbench.table.financialTransactionRules.baseExecutionPoint.error.accountingStandartIsBase'
          ],
        passed: false,
      };
    }

    return true;
  }

  private validateValuesByRules(
    linesToValidate: BaseWorkbenchDataSourceClassTypes.DataType[keyof BaseWorkbenchDataSourceClassTypes.DataType],
    validationFields: string[]
  ): boolean {
    for (const line of linesToValidate) {
      for (const validateField of validationFields) {
        if (!line[validateField as keyof typeof line]) {
          return false;
        }
      }
    }

    return true;
  }

  private validateAccountingStandardLines(): boolean {
    const installedAccountingStandardsPreparedData = this.getPreparedLinesData(
      'InstalledAccountingStandards'
    );

    return this.validateValuesByRules(
      [
        ...installedAccountingStandardsPreparedData.linesToCreate,
        ...installedAccountingStandardsPreparedData.linesToUpdate,
      ],
      BaseWorkbenchDataSourceClass.validateInstalledAccountingStandardFields
    );
  }

  private validateFinancialTransactionRulesLines(): baseWorkbenchPresentation.dataSources.BaseWorkbenchDataSource.routines.save.validateOutput {
    const financialTransactionRulesPreparedData = this.getPreparedLinesData(
      'FinancialTransactionRules'
    );

    const financialTransactionRulesLines = [
      ...financialTransactionRulesPreparedData.linesToCreate,
      ...financialTransactionRulesPreparedData.linesToUpdate,
    ];

    if (
      !this.validateValuesByRules(
        financialTransactionRulesLines,
        BaseWorkbenchDataSourceClass.validateFinancialTransactionRuleField
      )
    ) {
      return false;
    }

    for (const line of financialTransactionRulesLines) {
      const validationResult = this.validateProcessRuleExecutionPoint(line);
      if (!validationResult) {
        return validationResult;
      }
    }

    return true;
  }

  private validateFinancialCalculationRulesLines(): baseWorkbenchPresentation.dataSources.BaseWorkbenchDataSource.routines.save.validateOutput {
    const financialCalculationRulesPreparedData = this.getPreparedLinesData(
      'FinancialCalculationRules'
    );

    const financialCalculationRulesLines = [
      ...financialCalculationRulesPreparedData.linesToCreate,
      ...financialCalculationRulesPreparedData.linesToUpdate,
    ];

    if (
      !this.validateValuesByRules(
        financialCalculationRulesLines,
        BaseWorkbenchDataSourceClass.validateFinancialCalculationRuleField
      )
    ) {
      return false;
    }

    return true;
  }

  // PUBLIC IMPLEMENTATION

  async setUp(
    input: baseWorkbenchPresentation.dataSources.BaseWorkbenchDataSource.callbacks.setUp.input
  ): Promise<baseWorkbenchPresentation.dataSources.BaseWorkbenchDataSource.callbacks.setUp.output> {
    const errorMessages = await getTranslations(
      input.session,
      [
        'baseWorkbench.table.financialTransactionRules.baseExecutionPoint.error.accountingStandartIsBase',
      ],
      'everest.fin.processor/baseWorkbench'
    );

    this.#errorMessages = {
      'baseWorkbench.table.financialTransactionRules.baseExecutionPoint.error.accountingStandartIsBase':
        errorMessages[0],
    };
  }

  async query(
    input: baseWorkbenchPresentation.dataSources.BaseWorkbenchDataSource.callbacks.query.input
  ): Promise<baseWorkbenchPresentation.dataSources.BaseWorkbenchDataSource.callbacks.query.combinedOutput> {
    if (!this.#data || input.queryReason === 'externalRequest') {
      await this.loadData(input);
    }

    return this.returnWithMetadata(input);
  }

  async create_InstalledAccountingStandards(
    input: baseWorkbenchPresentation.dataSources.BaseWorkbenchDataSource.callbacks.create_InstalledAccountingStandards.input
  ): Promise<baseWorkbenchPresentation.dataSources.BaseWorkbenchDataSource.callbacks.create_InstalledAccountingStandards.output> {
    if (input.isRestore) {
      const index = this.getIndexByIdentifyer(
        'InstalledAccountingStandards',
        input.data[IDENTIFIER]
      );
      if (index === undefined) {
        return;
      }
      this.markLineDeletion('InstalledAccountingStandards', index, false);

      return input.data[IDENTIFIER];
    } else {
      const identifier = input.session.util.uuid.v4();
      const newLine: BaseWorkbenchDataSourceClassTypes.DataType['InstalledAccountingStandards'][number] =
        {
          [IDENTIFIER]: identifier,
          isDeleted: false,
        };

      this.#data.InstalledAccountingStandards.push(newLine);
      return identifier;
    }
  }

  async update_InstalledAccountingStandards(
    input: baseWorkbenchPresentation.dataSources.BaseWorkbenchDataSource.callbacks.update_InstalledAccountingStandards.input
  ): Promise<baseWorkbenchPresentation.dataSources.BaseWorkbenchDataSource.callbacks.update_InstalledAccountingStandards.output> {
    const { path, fieldName, newFieldValue } = input;
    if (!path || !fieldName) {
      return;
    }

    const index = this.getIndexByIdentifyer(
      'InstalledAccountingStandards',
      path[0] as string | number
    );
    if (index === undefined) {
      return;
    }

    set(
      this.#data,
      ['InstalledAccountingStandards', `${index}`, fieldName],
      newFieldValue
    );

    await this.loadAlgorithmsCache(input);
  }

  async delete_InstalledAccountingStandards(
    input: baseWorkbenchPresentation.dataSources.BaseWorkbenchDataSource.callbacks.delete_InstalledAccountingStandards.input
  ): Promise<baseWorkbenchPresentation.dataSources.BaseWorkbenchDataSource.callbacks.delete_InstalledAccountingStandards.output> {
    const { path } = input;
    if (!path) {
      return;
    }

    const index = this.getIndexByIdentifyer(
      'InstalledAccountingStandards',
      path[0] as string | number
    );
    if (index === undefined) {
      return;
    }

    if (input.isRestorable) {
      this.markLineDeletion('InstalledAccountingStandards', index, true);
    } else {
      this.#data.InstalledAccountingStandards.splice(index, 1);
    }
  }

  async create_FinancialTransactionRules(
    input: baseWorkbenchPresentation.dataSources.BaseWorkbenchDataSource.callbacks.create_FinancialTransactionRules.input
  ): Promise<baseWorkbenchPresentation.dataSources.BaseWorkbenchDataSource.callbacks.create_FinancialTransactionRules.output> {
    if (input.isRestore) {
      const index = this.getIndexByIdentifyer(
        'FinancialTransactionRules',
        input.data[IDENTIFIER]
      );
      if (index === undefined) {
        return;
      }
      this.markLineDeletion('FinancialTransactionRules', index, false);

      return input.data[IDENTIFIER];
    } else {
      const identifier = input.session.util.uuid.v4();
      const newLine: BaseWorkbenchDataSourceClassTypes.DataType['FinancialTransactionRules'][number] =
        {
          isActive: true,
          [IDENTIFIER]: identifier,
          isDeleted: false,
        };

      this.#data.FinancialTransactionRules.push(newLine);
      return identifier;
    }
  }

  async update_FinancialTransactionRules(
    input: baseWorkbenchPresentation.dataSources.BaseWorkbenchDataSource.callbacks.update_FinancialTransactionRules.input
  ): Promise<baseWorkbenchPresentation.dataSources.BaseWorkbenchDataSource.callbacks.update_FinancialTransactionRules.output> {
    const { path, fieldName, newFieldValue, oldFieldValue } = input;
    if (!path || !fieldName) {
      return;
    }

    const index = this.getIndexByIdentifyer(
      'FinancialTransactionRules',
      path[0] as string | number
    );
    if (index === undefined) {
      return;
    }

    set(
      this.#data,
      ['FinancialTransactionRules', `${index}`, fieldName],
      newFieldValue
    );

    // Handle accountingStandard field
    if (input.fieldName === 'accountingStandard') {
      this.setPackage(
        'FinancialTransactionRules',
        index,
        newFieldValue as AccountingStandard.AccountingStandard['name']
      );
      // Unset fields algorithmContainer and algorithmName if accountingStandard is changed
      if (oldFieldValue !== newFieldValue) {
        set(
          this.#data,
          ['FinancialTransactionRules', `${index}`, 'algorithmContainer'],
          null
        );
        set(
          this.#data,
          ['FinancialTransactionRules', `${index}`, 'algorithmName'],
          null
        );
      }

      // Set baseExecutionPoint to Specific if accountingStandard is not base and correct it if it is
      const accountingStandard = this.#accountingStandardsCache.get(
        newFieldValue as string
      );
      if (accountingStandard) {
        if (accountingStandard.isBase) {
          const baseExecutionPoint = get(this.#data, [
            'FinancialTransactionRules',
            `${index}`,
            'baseExecutionPoint',
          ]);
          if (baseExecutionPoint === EvstBaseExecutionPoints.Specific) {
            set(
              this.#data,
              ['FinancialTransactionRules', `${index}`, 'baseExecutionPoint'],
              null
            );
          }
        } else {
          set(
            this.#data,
            ['FinancialTransactionRules', `${index}`, 'baseExecutionPoint'],
            EvstBaseExecutionPoints.Specific
          );
        }
      }
    }
  }

  async delete_FinancialTransactionRules(
    input: baseWorkbenchPresentation.dataSources.BaseWorkbenchDataSource.callbacks.delete_FinancialTransactionRules.input
  ): Promise<baseWorkbenchPresentation.dataSources.BaseWorkbenchDataSource.callbacks.delete_FinancialTransactionRules.output> {
    const { path } = input;
    if (!path) {
      return;
    }

    const index = this.getIndexByIdentifyer(
      'FinancialTransactionRules',
      path[0] as string | number
    );
    if (index === undefined) {
      return;
    }

    if (input.isRestorable) {
      this.markLineDeletion('FinancialTransactionRules', index, true);
    } else {
      this.#data.FinancialTransactionRules.splice(index, 1);
    }
  }

  async create_FinancialCalculationRules(
    input: baseWorkbenchPresentation.dataSources.BaseWorkbenchDataSource.callbacks.create_FinancialCalculationRules.input
  ): Promise<baseWorkbenchPresentation.dataSources.BaseWorkbenchDataSource.callbacks.create_FinancialCalculationRules.output> {
    if (input.isRestore) {
      const index = this.getIndexByIdentifyer(
        'FinancialCalculationRules',
        input.data[IDENTIFIER]
      );
      if (index === undefined) {
        return;
      }
      this.markLineDeletion('FinancialCalculationRules', index, false);

      return input.data[IDENTIFIER];
    } else {
      const identifier = input.session.util.uuid.v4();
      const newLine: BaseWorkbenchDataSourceClassTypes.DataType['FinancialCalculationRules'][number] =
        {
          isActive: true,
          [IDENTIFIER]: identifier,
          isDeleted: false,
        };

      this.#data.FinancialCalculationRules.push(newLine);
      return identifier;
    }
  }

  async update_FinancialCalculationRules(
    input: baseWorkbenchPresentation.dataSources.BaseWorkbenchDataSource.callbacks.update_FinancialCalculationRules.input
  ): Promise<baseWorkbenchPresentation.dataSources.BaseWorkbenchDataSource.callbacks.update_FinancialCalculationRules.output> {
    const { path, fieldName, newFieldValue, oldFieldValue } = input;
    if (!path || !fieldName) {
      return;
    }

    const index = this.getIndexByIdentifyer(
      'FinancialCalculationRules',
      path[0] as string | number
    );
    if (index === undefined) {
      return;
    }

    set(
      this.#data,
      ['FinancialCalculationRules', `${index}`, fieldName],
      newFieldValue
    );

    // Handle accountingStandard field
    if (input.fieldName === 'accountingStandard') {
      this.setPackage(
        'FinancialCalculationRules',
        index,
        newFieldValue as AccountingStandard.AccountingStandard['name']
      );
      // Unset fields algorithmContainer and algorithmName if accountingStandard is changed
      if (oldFieldValue !== newFieldValue) {
        set(
          this.#data,
          ['FinancialCalculationRules', `${index}`, 'algorithmContainer'],
          null
        );
        set(
          this.#data,
          ['FinancialCalculationRules', `${index}`, 'algorithmName'],
          null
        );
      }
    }
  }

  async delete_FinancialCalculationRules(
    input: baseWorkbenchPresentation.dataSources.BaseWorkbenchDataSource.callbacks.delete_FinancialCalculationRules.input
  ): Promise<baseWorkbenchPresentation.dataSources.BaseWorkbenchDataSource.callbacks.delete_FinancialCalculationRules.output> {
    const { path } = input;
    if (!path) {
      return;
    }

    const index = this.getIndexByIdentifyer(
      'FinancialCalculationRules',
      path[0] as string | number
    );
    if (index === undefined) {
      return;
    }

    if (input.isRestorable) {
      this.markLineDeletion('FinancialCalculationRules', index, true);
    } else {
      this.#data.FinancialTransactionRules.splice(index, 1);
    }
  }

  async execute_reset(
    _input: baseWorkbenchPresentation.dataSources.BaseWorkbenchDataSource.routines.reset.executeInput
  ): Promise<baseWorkbenchPresentation.dataSources.BaseWorkbenchDataSource.routines.reset.executeOutput> {
    this.#data = undefined;
  }

  async validate_save(
    _input: baseWorkbenchPresentation.dataSources.BaseWorkbenchDataSource.routines.save.validateInput
  ): Promise<baseWorkbenchPresentation.dataSources.BaseWorkbenchDataSource.routines.save.validateOutput> {
    return (
      this.validateAccountingStandardLines() &&
      this.validateFinancialTransactionRulesLines() &&
      this.validateFinancialCalculationRulesLines()
    );
  }

  async execute_save(
    input: baseWorkbenchPresentation.dataSources.BaseWorkbenchDataSource.routines.save.executeInput
  ): Promise<baseWorkbenchPresentation.dataSources.BaseWorkbenchDataSource.routines.save.executeOutput> {
    const installedAccountingStandardsPreparedData = this.getPreparedLinesData(
      'InstalledAccountingStandards'
    );
    const financialTransactionRulesPreparedData = this.getPreparedLinesData(
      'FinancialTransactionRules'
    );
    const financialCalculationRulesPreparedData = this.getPreparedLinesData(
      'FinancialCalculationRules'
    );

    // Accounting Standards
    if (!isEmpty(installedAccountingStandardsPreparedData.linesToDelete)) {
      await InstalledAccountingStandard.deleteMany(input.session, {
        id: { $in: installedAccountingStandardsPreparedData.linesToDelete },
      });
    }
    if (!isEmpty(installedAccountingStandardsPreparedData.linesToUpdate)) {
      for (const line of installedAccountingStandardsPreparedData.linesToUpdate) {
        await InstalledAccountingStandard.update(
          input.session,
          {
            id: line.id,
          },
          {
            ...pick(
              line,
              difference(
                BaseWorkbenchDataSourceClass.accountingStandardsCacheFields,
                ['id']
              )
            ),
          },
          ['id']
        );
      }
    }
    if (!isEmpty(installedAccountingStandardsPreparedData.linesToCreate)) {
      await InstalledAccountingStandard.createMany(
        input.session,
        installedAccountingStandardsPreparedData.linesToCreate.map((e) => ({
          package: e.package,
          accountingStandard: e.accountingStandard,
          providerName: e.providerName,
        }))
      );
    }

    // Financial Calculation Rules
    if (!isEmpty(financialTransactionRulesPreparedData.linesToDelete)) {
      await FinancialTransactionRule.deleteMany(input.session, {
        id: { $in: financialTransactionRulesPreparedData.linesToDelete },
      });
    }
    if (!isEmpty(financialTransactionRulesPreparedData.linesToUpdate)) {
      for (const line of financialTransactionRulesPreparedData.linesToUpdate) {
        await FinancialTransactionRule.update(
          input.session,
          {
            id: line.id,
          },
          {
            ...pick(
              line,
              difference(
                BaseWorkbenchDataSourceClass.financialTransactionRuleFieldlist,
                ['id']
              )
            ),
          },
          ['id']
        );
      }
    }
    if (!isEmpty(financialTransactionRulesPreparedData.linesToCreate)) {
      await FinancialTransactionRule.createMany(
        input.session,
        financialTransactionRulesPreparedData.linesToCreate.map((e) => ({
          package: e.package,
          isActive: e.isActive,
          accountingStandard: e.accountingStandard,
          financialObject: e.financialObject,
          processName: e.processName,
          baseExecutionPoint: e.baseExecutionPoint,
          algorithmExecutionOrder: e.algorithmExecutionOrder,
          algorithmContainer: e.algorithmContainer,
          algorithmName: e.algorithmName,
          resultSaveKey: e.resultSaveKey,
        }))
      );
    }

    // Financial Calculation Rules
    if (!isEmpty(financialCalculationRulesPreparedData.linesToDelete)) {
      await FinancialTransactionRule.deleteMany(input.session, {
        id: { $in: financialCalculationRulesPreparedData.linesToDelete },
      });
    }
    if (!isEmpty(financialCalculationRulesPreparedData.linesToUpdate)) {
      for (const line of financialCalculationRulesPreparedData.linesToUpdate) {
        await FinancialCalculationRule.update(
          input.session,
          {
            id: line.id,
          },
          {
            ...pick(
              line,
              difference(
                BaseWorkbenchDataSourceClass.financialCalculationRuleFieldlist,
                ['id']
              )
            ),
          },
          ['id']
        );
      }
    }
    if (!isEmpty(financialCalculationRulesPreparedData.linesToCreate)) {
      await FinancialCalculationRule.createMany(
        input.session,
        financialCalculationRulesPreparedData.linesToCreate.map((e) => ({
          package: e.package,
          isActive: e.isActive,
          accountingStandard: e.accountingStandard,
          routineName: e.routineName,
          algorithmExecutionOrder: e.algorithmExecutionOrder,
          algorithmContainer: e.algorithmContainer,
          algorithmName: e.algorithmName,
          resultSaveKey: e.resultSaveKey,
        }))
      );
    }

    this.#data = undefined;
  }
}

export const baseWorkbenchPresentationImplementation: baseWorkbenchPresentation.implementation =
  {
    BaseWorkbenchDataSource: () => new BaseWorkbenchDataSourceClass(),
  };

export default baseWorkbenchPresentationImplementation;
