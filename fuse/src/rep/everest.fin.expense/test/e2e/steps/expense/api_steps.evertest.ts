import { ApiUtilities } from '@pkg/everest.base.test/test/e2e/utils/api/apiUtilities.evertest';
import {
  addVATConfigpayload,
  createExpenseItemPayload,
  createPaymentForBankMatchingTestPayload,
  createUponCreationApprovalPolicyWithApprovers,
  createVendorPayload,
  deleteOutboundPaymentPayload,
  createVendorBillPayload,
  preparePayrollTemplateForImportPDUATPayload,
  preparePayrollTemplateForImportPDUATPayloadGuso,
  upsertTestPayrollTemplatePayload,
  upsertTestPayrollTemplateForKostenEntitiesPayload,
} from '../../data/payload.evertest';
import { Step, Table, TableRow } from 'gauge-ts';
import { cloneDeep, isEmpty } from 'lodash';
import {
  saveValueToSpecStoreKey,
  getValueFromSpecStoreKey,
  roundTo,
  checkValidTableNames,
  generateRandomString,
} from '@pkg/everest.base.test/test/e2e/utils/utilities.evertest';
import {
  convertAllVariableInTableToValueV2,
  convertVariablesInStringV2,
} from '@pkg/everest.base.test/test/e2e/utils/data/convertDataUtils.evertest';
import {
  convertTableToObj,
  storeDataToReferenceKeyInTable,
  stripTable,
} from '@pkg/everest.base.test/test/e2e/utils/tableUtils.evertest';
import { ApiSerialization } from '@pkg/everest.base.test/test/e2e/utils/api/apiSerialization.evertest';
import { apiSteps as BaseAPISteps } from '@pkg/everest.base.test/test/e2e/steps/APISteps.evertest';
import { MODEL_ID } from '../../utils/constant.evertest';
import {
  convertInputDateToISOFormat,
  checkDateTimeFormat,
} from '@pkg/everest.base.test/test/e2e/utils/dateUtils.evertest';
import { baseSteps } from '@pkg/everest.base.test/test/e2e/steps/BaseSteps.evertest';
import { DATE_FORMAT } from '@pkg/everest.base.test/test/e2e/utils/constants/constant.evertest';
import { apiSteps as baseAPISteps } from '@pkg/everest.base.test/test/e2e/steps/APISteps.evertest';
import { ModelId } from '../../enums/enum.evertest';
import Decimal from 'decimal.js';

export default class APISteps {
  @Step(
    'Create Vendor by API then store to key <vendor key> with data <data table>'
  )
  public async createVendor(storedKey: string, dataTable: Table) {
    const payload = cloneDeep(createVendorPayload);
    dataTable = await convertAllVariableInTableToValueV2(dataTable);

    const vendorData = convertTableToObj(dataTable)[0];
    let {
      vendorName,
      vendorType,
      paymentTerms,
      entityName,
      preferredCurrency,
      status = 'active',
    } = vendorData;

    [vendorType, paymentTerms] = await Promise.all([
      ApiSerialization.getAPIValueFromUIValueNew(
        MODEL_ID.VENDOR,
        'vendorType',
        vendorType
      ),
      baseAPISteps.getPaymentTermAPIValueByUIValue(paymentTerms),
    ]);

    const vendorEntityId = await BaseAPISteps.getEntityIdByName(entityName);

    payload.uimodel.payload.businessPartner.data.name = vendorName;
    payload.uimodel.payload.vendor.data = {
      status,
      vendorType,
      vendorEntityId,
      paymentTerms,
      independentContractor: 'no',
    };

    if (preferredCurrency) {
      const currencyCode =
        await ApiSerialization.getCurrencyCodeByCurrencyTextViaAPI(
          preferredCurrency
        );
      payload.uimodel.payload.vendor.data.preferredCurrency = currencyCode;
    }

    const { content } =
      await ApiUtilities.callPostUIEngineRequestAndHandleError(
        payload,
        'create new vendor',
        { retries: 0 }
      );
    vendorData.vendorNumber = content?.vendor?.result?.data?.vendorNumber;
    saveValueToSpecStoreKey(storedKey, vendorData);
  }

  @Step(
    'Create a Purchase Expense Item via API then store data to key <expense item key> with data <data table>'
  )
  public async createPurchaseExpense(storedKey: string, dataTable: Table) {
    const payload = cloneDeep(createExpenseItemPayload);
    const [_, convertedObj] = await baseSteps.convertTableAndSaveReference(
      storedKey,
      dataTable
    );

    const { billDate, merchant, currency, amount, category, description } =
      convertedObj;

    const requiredColumns = [
      'billDate',
      'merchant',
      'currency',
      'amount',
      'category',
    ];

    if (
      !checkValidTableNames(dataTable.getColumnNames(), requiredColumns) ||
      !checkDateTimeFormat(billDate, DATE_FORMAT)
    )
      throw new Error(
        `Input table has not include required fields: ${requiredColumns.toString()}
        >> Data format: 
            - billDate (MM/DD/YYYY)
            - currency is currencyText`
      );

    const [categoryData, currencyCode] = await Promise.all([
      apiSteps.getExpenseCategoryDataByName(category as string),
      ApiSerialization.getCurrencyCodeByCurrencyTextViaAPI(currency as string),
    ]);

    const categoryId = categoryData[0]?.id;

    const expenseItemData: Record<
      string,
      string | number | boolean | { amount: string }
    > = {
      billDate: convertInputDateToISOFormat(billDate as string),
      merchant,
      categoryId,
      reimbursable: true,
      currency: currencyCode,
      amount: { amount },
      expenseType: 'Pur',
      description,
    };

    payload.uimodel.payload.expenseLine.data.expenseItem = expenseItemData;

    const { content } =
      await ApiUtilities.callPostUIEngineRequestAndHandleError(
        payload,
        `create Purchase Expense Item`,
        { retries: 0 }
      );
    const eiObject = getValueFromSpecStoreKey(storedKey);
    eiObject.expenseItemNumber =
      content?.expenseLine?.result?.data?.expenseItemNumber;
    saveValueToSpecStoreKey(storedKey, eiObject);
  }

  /**
   * Retrieves expense category data by name. Optionally filters the data by the specified category name.
   *
   * @param category - Optional. The category name used to filter the data.
   * @returns A promise that resolves to an array of expense category data matching the provided name.
   * @throws An error if no categories match the specified name.
   */
  public async getExpenseCategoryDataByName(category?: string) {
    const where = category ? { category } : {};
    const data = await BaseAPISteps.queryData(
      'list',
      ModelId.EXPENSE_CATEGORY,
      {
        where,
      }
    );
    if (isEmpty(data))
      throw new Error(`There is no category that has name "${category}"`);

    return data;
  }

  @Step(
    'Create a Travelled Expense Item via API then store data to key <expense item key> with data <data table>'
  )
  public async createTravelledExpenseItem(storedKey: string, dataTable: Table) {
    const payload = cloneDeep(createExpenseItemPayload);
    const [_, convertedObj] = await baseSteps.convertTableAndSaveReference(
      storedKey,
      dataTable
    );

    const { currency, rate, distance, unit, description, billDate } =
      convertedObj;

    const requiredColumns = [
      'currency',
      'rate',
      'distance',
      'unit',
      'billDate',
    ];
    if (
      !checkValidTableNames(dataTable.getColumnNames(), requiredColumns) ||
      isNaN(parseFloat(distance)) ||
      isNaN(parseFloat(rate)) ||
      !checkDateTimeFormat(billDate, DATE_FORMAT)
    )
      throw new Error(
        `Input table has not include required fields: ${requiredColumns.toString()}.
         >> Data format: 
            - billDate (MM/DD/YYYY)
            - currency is currencyText
            - rate and distance are number type`
      );

    const [categoryData, currencyCode] = await Promise.all([
      apiSteps.getExpenseCategoryDataByName(),
      ApiSerialization.getCurrencyCodeByCurrencyTextViaAPI(currency as string),
    ]);

    const { id, category } =
      categoryData.filter(
        (cat: Record<string, unknown>) => cat.categoryType == 'Dis'
      )[0] ?? {};
    const expenseAmount = roundTo(
      new Decimal(rate).times(new Decimal(distance)).toNumber(),
      2
    );

    const expenseItemData: Record<string, unknown> = {
      billDate: convertInputDateToISOFormat(billDate),
      merchant: 'Distance Travelled',
      categoryId: id,
      reimbursable: true,
      currency: currencyCode,
      amount: { amount: expenseAmount },
      unit,
      rate,
      distance,
      expenseType: 'Dis',
      description,
    };
    payload.uimodel.payload.expenseLine.data.expenseItem = expenseItemData;

    const reps = await ApiUtilities.callPostUIEngineRequestAndHandleError(
      payload,
      `create Travelled Expense Item`,
      { retries: 0 }
    );

    //store category name to expense item object data
    const ei = getValueFromSpecStoreKey(storedKey);
    ei.category = category;
    ei.amount = expenseAmount;
    ei.expenseItemNumber = reps?.content?.expenseLine?.result?.data?.expenseItemNumber;
    saveValueToSpecStoreKey(storedKey, ei);
  }

  @Step(
    'Create test payment with following data for Bank Matching Suggestion via API <data table>'
  )
  public async createBankTransactionReconciliationTestData(dataTable: Table) {
    let requiredColumns = [
      'Reference',
      'Bank Account Name',
      'Date',
      'Amount',
      'Currency',
      'Entity',
    ];

    if (!checkValidTableNames(dataTable.getColumnNames(), requiredColumns))
      throw new Error(
        `Input table does not contains required fields with order: ${requiredColumns}`
      );

    dataTable = await convertAllVariableInTableToValueV2(dataTable);
    // Save table data to Reference key
    storeDataToReferenceKeyInTable(dataTable);

    const keys = dataTable.getColumnValues(requiredColumns[0]);
    requiredColumns.shift(); // remove "Reference" from the list
    dataTable = stripTable(dataTable);

    const rows = dataTable.getTableRows();
    const promises = rows.map(async (row: TableRow, index: number) => {
      const [bankAccountName, date, amount, currency, entity] =
        requiredColumns.map((col) => row.getCell(col));
      const vendorName = row.getCell('Vendor Name') || null;
      const convertedAmount = parseFloat(amount);
      const payload = createPaymentForBankMatchingTestPayload({
        bankAccountName,
        date,
        amount: Math.abs(convertedAmount),
        paymentCurrencyCode: currency,
        entityName: entity,
        payerEntityName: entity,
        billNumber: `${Math.random()
          .toString(10)
          .substring(2, 8)}-${Date.now()} ${generateRandomString(5)}`,
        paymentId: `${Math.random()
          .toString(10)
          .substring(2, 8)}-${Date.now()}`,
        vendorName,
      });

      const {
        content: {
          bankTransactionReconciliationTest: { result },
        },
      } = await ApiUtilities.callPostUIEngineRequestAndHandleError(
        payload,
        `create test payment with following data for Bank Matching Suggestion`
      );

      const paymentDataObject = getValueFromSpecStoreKey(keys[index]) ?? {};
      paymentDataObject.paymentNumber =
        result[0]?.data[0]?.outboundPaymentHeaderNumber;
      paymentDataObject.id = result[0]?.data[0]?.id;

      saveValueToSpecStoreKey(keys[index], paymentDataObject);
    });
    await Promise.all(promises);
  }

  @Step('Create vendor bill with following data via API <data table>')
  public async createVendorBill(dataTable: Table) {
    dataTable = await convertAllVariableInTableToValueV2(dataTable);
    let requiredColumns = [
      'Reference',
      'currencyCode',
      'date',
      'amount',
      'entity',
      'vendorName',
    ];

    if (!checkValidTableNames(dataTable.getColumnNames(), requiredColumns))
      throw new Error(
        `Input table does not contains required fields with order: ${requiredColumns}`
      );

    dataTable = await convertAllVariableInTableToValueV2(dataTable);
    // Save table data to Reference key
    storeDataToReferenceKeyInTable(dataTable);

    const keys = dataTable.getColumnValues(requiredColumns[0]);
    requiredColumns.shift(); // remove "Reference" from the list
    dataTable = stripTable(dataTable);

    const rows = dataTable.getTableRows();
    const promises = rows.map(async (row: TableRow, index: number) => {
      const [currencyCode, date, amount, entity, vendorName] =
        requiredColumns.map((col) => row.getCell(col));
      const isDraft = row.getCell('isDraft') === 'true';
      const convertedAmount = parseFloat(amount);
      const payload = createVendorBillPayload({
        date,
        amount: Math.abs(convertedAmount),
        entityName: entity,
        billNumber: `${Math.random()
          .toString(10)
          .substring(2, 8)}-${Date.now()} ${generateRandomString(5)}`,
        vendorName,
        currencyCode,
        isDraft,
      });

      const {
        content: {
          bankTransactionReconciliationTest: { result },
        },
      } = await ApiUtilities.callPostUIEngineRequestAndHandleError(
        payload,
        `create vendor bill`
      );

      const paymentDataObject = getValueFromSpecStoreKey(keys[index]) ?? {};
      paymentDataObject.vendorBillNumber = result[0]?.data.billNumber;

      saveValueToSpecStoreKey(keys[index], paymentDataObject);
    });
    await Promise.all(promises);
  }

  @Step(
    'Create an upon creation approval policy for <bo> business object with <approvers> approvers'
  )
  public async createUponCreationApprovalPolicy(bo: string, approvers: string) {
    const approversArr = (await convertVariablesInStringV2(approvers))
      .split(',')
      .map((approver) => approver.trim());
    const payload = cloneDeep(
      createUponCreationApprovalPolicyWithApprovers(bo, approversArr)
    );
    await ApiUtilities.callPostUIEngineRequestAndHandleError(
      payload,
      `create upon creation approval policy for ${bo} business object with ${approvers} approvers`
    );
  }

  @Step(
    'Add new VAT Tax Config and store data to key <stored key> with data <data table>'
  )
  public async addNewVATTaxConfig(storedKey: string, dataTable: Table) {
    const [_, data] = await baseSteps.convertTableAndSaveReference(
      storedKey,
      dataTable
    );
    const payload = addVATConfigpayload(
      data as unknown as Parameters<typeof addVATConfigpayload>[0]
    );

    await ApiUtilities.callPostUIEngineRequestAndHandleError(
      payload,
      `add new VAT Tax Config data ${JSON.stringify(data)}`
    );
  }

  @Step('Delete these outbound payments by API <ids>')
  public async deleteOutboundPayments(ids: string) {
    const idArr = (await convertVariablesInStringV2(ids))
      .split(',')
      .map((id) => id.trim());
    await Promise.all(
      idArr.map(async (id) => {
        const payload = deleteOutboundPaymentPayload(parseInt(id));
        await ApiUtilities.callPostUIEngineRequestAndHandleError(
          payload,
          `delete outbound payment with id ${id}`
        );
      })
    );
  }

  @Step(
    'Prepare Payroll Template for Import Payroll Detail UAT via API <name> and store to key <storedKey>'
  )
  public async preparePayrollTemplateForImportPDUAT(
    name: string,
    storeKey: string
  ) {
    name = await convertVariablesInStringV2(name);
    const data = getValueFromSpecStoreKey(storeKey) ?? {};
    data.payrollTemplateName = name;
    saveValueToSpecStoreKey(storeKey, data);
    const payload = preparePayrollTemplateForImportPDUATPayload(name);
    await ApiUtilities.callPostUIEngineRequestAndHandleError(
      payload,
      `prepare payroll template for import PD UAT with name ${name}`
    );
  }

  @Step(
    'Prepare Gusto Payroll Template for Import Payroll Detail UAT via API <name> and store to key <storedKey>'
  )
  public async preparePayrollTemplateForImportPDUATGuso(
    name: string,
    storeKey: string
  ) {
    name = await convertVariablesInStringV2(name);
    const data = getValueFromSpecStoreKey(storeKey) ?? {};
    data.payrollTemplateName = name;
    saveValueToSpecStoreKey(storeKey, data);
    const payload = preparePayrollTemplateForImportPDUATPayloadGuso(name);
    await ApiUtilities.callPostUIEngineRequestAndHandleError(
      payload,
      `prepare payroll template for import PD UAT with name ${name}`
    );
  }

  @Step('Prepare Payroll Template for testing via API')
  public async preparePayrollTemplateForUAT() {
    await ApiUtilities.callPostUIEngineRequestAndHandleError(
      upsertTestPayrollTemplatePayload,
      `Prepare Payroll Template for UAT`,
      { retries: 2 }
    );
  }

  @Step(
    'Create Test Payroll Template for Kosten entities with name <name> and store to key <store key> via API'
  )
  public async createTestPayrollTemplate(name: string, storeKey: string) {
    name = await convertVariablesInStringV2(name);
    const data = getValueFromSpecStoreKey(storeKey) ?? {};
    data.payrollTemplateName = name;
    saveValueToSpecStoreKey(storeKey, data);
    const payload = upsertTestPayrollTemplateForKostenEntitiesPayload(name);
    await ApiUtilities.callPostUIEngineRequestAndHandleError(
      payload,
      `create test payroll template with name ${name} for Kosten entities`
    );
  }
}

export const apiSteps = new APISteps();
