# Logging in when there is no warm instance

## Create expense report with no approval policy from listview

| Receipt | Base Currency Code | Target Currency Code | Target Currency |
| ------- | ------------------ | -------------------- | --------------- |
| No      | USD                | CAD                  | Canadian Dollar |

* Create a Purchase Expense Item via API with currency "US Dollar" and bill date "09/01/2023" then store data to key "ei1"
* Go to the Your Expenses page
* Go to the Unsubmitted Expense page
* On the Your Expenses page, open the Review Expense Report page from Expense Item has data

|         ID         |       Date       |         Merchant/Description          |     Category     |  Receipt  |     Amount     |
| ------------------ | ---------------- | ------------------------------------- | ---------------- | --------- | -------------- |
| {{start-with:EI-}} | {{ei1.billDate}} | {{ei1.merchant}}: {{ei1.description}} | {{ei1.category}} | <Receipt> | {{ei1.amount}} |

* On the Create Expense Report page, fill information to submitting block and store data to key "er1"

|         Employee          |   Entity    |    Date    |
| ------------------------- | ----------- | ---------- |
| {{active-user-full-name}} | Flow Canada | 09/01/2023 |

* Get the Exchange Rate Data by API and save it to stored key

|       Date       |    Base Currency     |    Target Currency     | Stored Key |
| ---------------- | -------------------- | ---------------------- | ---------- |
| {{ei1.billDate}} | <Base Currency Code> | <Target Currency Code> | ei1        |

* Calculate Reimbursement Amount and Transaction Amount from Expense Item Amount "{{ei1.amount}}" and exchange rate "{{ei1.exchangeRate}}" then store to key "ei1"
* On the Create Expense Report page, check that the summary text displays correctly 

|        Total Amount         |
| --------------------------- |
| {{ei1.ReimbursementAmount}} |

* Check that the Unsubmitted table displays row

|         Number     |       Date       |         Merchant/Description          |     Category     |  Receipt  |          Amount           |   Exchange Rate     |    Reimbursement Amount     |
| ------------------ | ---------------- | ------------------------------------- | ---------------- | --------- | ------------------------- |-------------------- | --------------------------- |
| {{start-with:EI-}} | {{ei1.billDate}} | {{ei1.merchant}}: {{ei1.description}} | {{ei1.category}} | <Receipt> | {{ei1.TransactionAmount}} |{{ei1.exchangeRate}} | {{ei1.ReimbursementAmount}} |

* On the Create Expense Report page, click on the Submit button to submit a new Expense Report
* On Expense Report Details page, get Expense Report Number then save to "er1"
* On the header, check that the status "Approved, Unpaid" is displayed correctly
* On the Expense Report Details page, check that the summary text displays correctly 

|        Total Amount         |
| --------------------------- |
| {{ei1.ReimbursementAmount}} |
