{"version": 2, "uicontroller": "payments.uicontroller.ts", "uimodel": {"state": {"isVendorIdSet": false}, "nodes": {"payrollDetail": {"type": "struct", "modelId": "everest.fin.expense/PayrollDetailModel.PayrollDetail", "fieldList": []}, "expenseReports": {"type": "list", "query": "@controller:getExpenseReportsQuery()", "modelId": "everest.fin.expense/ExpenseReportBaseModel.ExpenseReportBase", "pagination": true, "fieldList": ["id", "expenseNumber", "status", "openAmount", "reimbursableTotal", "submittedDate", "entityId", "entityName", "reimbursementCurrency", "ExpenseReportBase-Employee.name", "employeeId", "origin"]}, "outboundPaymentHeaders": {"type": "list", "modelId": "everest.fin.expense/OutboundPaymentHeaderBaseModel.OutboundPaymentHeaderBase", "query": {}, "pagination": true, "fieldList": ["id", "OutboundPaymentItemBase-OutboundPaymentHeaderBase.sourcePaymentDocumentId", "OutboundPaymentItemBase-OutboundPaymentHeaderBase.sourcePaymentDocumentType"]}, "vendorBills": {"type": "list", "query": "@controller:getVendorBillsQuery()", "modelId": "everest.fin.expense/VendorBillHeaderBaseModel.VendorBillHeaderBase", "pagination": true, "fieldList": ["id", "vendorName", "vendorId", "bill<PERSON><PERSON><PERSON>", "entityId", "entityName", "currency", "billDate", "paymentStatus", "dueDate", "total", "status", "openAmount", "vendorBillHeaderNumber", "origin"]}, "getPayablesSummaryData": {"type": "struct", "getPayablesSummaryData": "@controller:getPayablesSummaryDataQuery()", "modelId": "everest.fin.expense/VendorModel.Vendor", "fieldList": []}, "vendors": {"type": "list", "modelId": "everest.fin.expense/VendorModel.Vendor", "query": {"where": {"id": {"$in": "@controller:getVendorIds()"}}}, "fieldList": ["id", "bankDetailsId", "vendorName"]}, "bankDetails": {"type": "list", "modelId": "everest.fin.base/BankDetailsModel.BankDetails", "query": {"where": {"id": {"$in": "@controller:getBankDetailsIds()"}}}, "fieldList": ["id", "bankAccountNumberType"]}, "vendorCredits": {"type": "list", "modelId": "everest.fin.expense/VendorCreditHeaderModel.VendorCreditHeader", "query": "@controller:getVendorCreditsQuery()", "fieldList": ["id", "vendorName", "vendorId", "currency", "totalAmount", "totalAmountApplied", "totalBalance"]}, "employees": {"type": "list", "modelId": "everest.hr.base/EmployeeModel.Employee", "loadCurrentActiveEmployees": "@controller:getEmployeeQuery()"}, "outboundPaymentSettings": {"type": "struct", "query": {"where": {"key": "showAdditionalOutboundPaymentSettings"}}, "modelId": "everest.fin.expense/VendorSettingsModel.VendorSettings", "fieldList": ["key", "value"]}}}, "uiview": {"layout": "everest.base.ui/layouts/uilayout/startPage/startPage", "props": {"i18n": "payment", "title": "@controller:getTitle()", "keyDataConfig": {"idProp": "id", "textProp": "entityName", "data": "@controller:getEntities()", "value": "@controller:getSelectedEntity()", "onValueChange": "@controller:refetchData"}, "firstRowContent": {"first": {"component": "Statistic", "customId": "statistic", "size": "12", "props": {"hideDivider": true, "columns": 4, "fields": [{"label": "{{payment.overdue}}", "value": "@controller:getOverDue()", "onClick": "@controller:payOverDue"}, {"label": "{{payment.due.within.seven}}", "value": "@controller:getDueWithinSeven()", "onClick": "@controller:payDueWithinSeven"}, {"label": "{{payment.open.vendor.bills}}", "value": "@controller:getOpenBills()", "onClick": "@controller:payAllVBs"}, {"label": "{{payment.open.expense.reports}}", "value": "@controller:getOpenExpenses()", "onClick": "@controller:payAllERs"}]}}}, "secondRowContent": [{"component": "<PERSON><PERSON>", "size": "12", "variant": "info", "title": "Info", "content": "{{payment.applyVendorCredit.alertMessage}}", "visible": "@controller:isApplyCreditAlertVisible()", "action": {"label": "{{payment.applyCredit}}", "onClick": "@controller:openAvailableVendorCreditsModal"}}], "customSections": [{"component": "Table", "customId": "openVendorBillsTable", "section": {"editing": false, "visible": true, "title": "{{payment.open.vendor.bills.title}}", "grid": {"size": "12"}, "actions": [{"variant": "primary", "label": "{{payment.create.makePayment}}", "actions": [{"label": "{{payment.initiatePayment}}", "onClick": "@controller:createBankBillPayment", "disabled": "@controller:isPaymentButtonDisabled('vendorBills')"}, {"label": "{{payment.recordPayment}}", "onClick": "@controller:createManualBillPayment", "disabled": "@controller:isPaymentButtonDisabled('vendorBills')"}, {"label": "{{payment.markAsPaid}}", "onClick": "@controller:<PERSON>VendorBillsAsPaid", "disabled": "@controller:isPaymentButtonDisabled('vendorBills')", "visible": "@controller:isMarkAsPaidVisible()"}]}], "filter": {"suppressFields": ["total", "vendorId"]}}, "props": {"showRowCount": true, "rowSelection": true, "onRowSelectionChanged": "@controller:_onRowSelectionChangedVendorBills", "columns": [{"headerName": "{{payment.paymentStatus}}", "field": "paymentStatus", "hide": true}, {"headerName": "{{payment.entityId}}", "field": "entityId", "hide": true}, {"headerName": "{{payment.status1}}", "field": "status", "hide": true}, {"headerName": "{{payment.history.origin}}", "field": "origin", "hide": true}, {"headerName": "{{payment.id}}", "field": "vendorBillHeaderNumber", "cellLinkEmphasized": true, "onCellClicked": "@controller:viewVendorBill"}, {"headerName": "{{payment.vendor}}", "field": "vendorName"}, {"headerName": "{{payment.billNumber}}", "field": "bill<PERSON><PERSON><PERSON>"}, {"headerName": "{{payment.billDate}}", "field": "billDate"}, {"headerName": "{{payment.dueDate}}", "field": "dueDate"}, {"headerName": "{{payment.entity}}", "field": "entityName"}, {"headerName": "{{payment.currency}}", "field": "currency"}, {"headerName": "{{payment.amount.due}}", "field": "openAmount", "fieldProps": {"parseAs": "currency"}}], "data": "@binding:vendorBills"}}, {"component": "Table", "customId": "openExpenseReportsTable", "section": {"editing": false, "visible": true, "title": "{{payment.open.expense.reports.title}}", "grid": {"size": "12"}, "actions": [{"variant": "primary", "label": "{{payment.create.makePayment}}", "actions": [{"label": "{{payment.initiatePayment}}", "onClick": "@controller:createBankExpensePayment", "disabled": "@controller:isPaymentButtonDisabled()"}, {"label": "{{payment.recordPayment}}", "onClick": "@controller:createManualExpensePayment", "disabled": "@controller:isPaymentButtonDisabled()"}, {"label": "{{payment.markAsPaid}}", "onClick": "@controller:markEmployeeExpensesAsPaid", "disabled": "@controller:isPaymentButtonDisabled()", "visible": "@controller:isMarkAsPaidVisible()"}]}]}, "props": {"showRowCount": true, "rowSelection": true, "onRowSelectionChanged": "@controller:_onRowSelectionChangedExpenseReports", "columns": [{"headerName": "{{payment.id}}", "field": "expenseNumber", "cellLinkEmphasized": true, "onCellClicked": "@controller:viewExpenseReport"}, {"headerName": "{{payment.employee}}", "field": "ExpenseReportBase-Employee.name"}, {"headerName": "{{payment.submittedDate}}", "field": "submittedDate"}, {"headerName": "{{payment.entity}}", "field": "entityName"}, {"headerName": "{{payment.currency}}", "field": "reimbursementCurrency"}, {"headerName": "{{payment.amount.owed}}", "field": "openAmount"}], "data": "@binding:expenseReports"}}], "headerActions": {"content": [{"variant": "primary", "label": "{{payment.recordStandAlonePayment}}", "onClick": "@controller:createStandAlonePayment", "visible": "@controller:isStandAlonePaymentButtonVisible()"}]}}}}