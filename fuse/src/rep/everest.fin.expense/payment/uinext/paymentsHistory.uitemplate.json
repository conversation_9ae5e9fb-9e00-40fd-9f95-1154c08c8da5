{"version": 2, "uicontroller": "paymentsHistory.uicontroller.ts", "uimodel": {"nodes": {"outboundPaymentHeaders": {"type": "list", "modelId": "everest.fin.expense/OutboundPaymentHeaderBaseModel.OutboundPaymentHeaderBase", "query": "@controller:getPaymentsQuery()", "options": {"draft": "include"}, "pagination": true, "fieldList": ["id", "outboundPaymentHeaderNumber", "paymentDate", "accountName", "JournalEntryHeader-FinancialDocument.posted", "JournalEntryHeader-FinancialDocument.journalEntryNumber", "JournalEntryHeader-FinancialDocument.id", "OutboundPaymentItemBase.vendorOrPayee", "paymentType", "status", "accountId", "entityName", "paymentCurrency", "origin", "totalPaid", "migrationConfigurationMode"]}}}, "uiview": {"layout": "everest.base.ui/layouts/uilayout/report/report", "props": {"node": "fiscalCalendar", "title": "{{payment.history.payments}}", "tabTitle": "{{payment.history.payments}}", "autoRefreshData": true, "allowRefreshData": true, "stretch": true, "rowBackgroundGetter": "@controller:getRowBackground", "i18n": "payment", "table": {"variant": "white", "customId": "paymentsHistoryTable", "pagination": true, "data": "@binding:outboundPaymentHeaders", "onRowClicked": "@controller:navigateToPayment", "fixedFilters": ["paymentDate", "status"], "suppressFilterFields": ["id", "JournalEntryHeader-FinancialDocument.posted", "JournalEntryHeader-FinancialDocument.id", "accountId"], "columns": "@controller:getHistoryTableColumns()"}}}}