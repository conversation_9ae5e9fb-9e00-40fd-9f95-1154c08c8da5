import type { ISession } from '@everestsystems/content-core';
import { UpsertAmortizationMode } from '@pkg/everest.fin.accounting/public/amortization/types';
import { AmortizationScheduleHeader } from '@pkg/everest.fin.accounting/types/AmortizationScheduleHeader';
import { AmortizationScheduleLine } from '@pkg/everest.fin.accounting/types/AmortizationScheduleLine';
import { EvstJournalEntryStatus } from '@pkg/everest.fin.accounting/types/enums/JournalEntryStatus';
import { EvstVendorBillLineType } from '@pkg/everest.fin.base/types/enums/VendorBillLineType';
import {
  BusinessValidationError,
  BusinessValidationErrorCode,
} from '@pkg/everest.fin.expense/public/businessValidationError';
import { VendorBillHeaderBase } from '@pkg/everest.fin.expense/types/VendorBillHeaderBase';
import { VendorBillItemBase } from '@pkg/everest.fin.expense/types/VendorBillItemBase';

export default async function upsertVendorBillAmortizationSchedule(
  session: ISession,
  msg: {
    payload: { vendorBillHeaderId?: number };
    actionArgs?: {
      options: {
        draft: boolean;
        activateDraftBill: boolean;
        fieldList: string[];
      };
    };
  }
): Promise<void> {
  const { vendorBillHeaderId } = msg.payload;
  if (vendorBillHeaderId) {
    const isBillPosted = await isVendorBillPosted(session, vendorBillHeaderId);
    if (isBillPosted) {
      const vendorBillLines = await getVendorBillLines(
        session,
        vendorBillHeaderId
      );
      if (vendorBillLines.length > 0) {
        await upsertVBAmortizationBillLine(session, vendorBillLines);
      }
    }
  }
}

async function upsertVBAmortizationBillLine(
  session: ISession,
  vendorBillLines: Pick<
    VendorBillItemBase.VendorBillItemBase,
    | 'id'
    | 'amortizationScheduleId'
    | 'accountId'
    | 'departmentId'
    | 'businessUnitId'
    | 'amount'
  >[]
) {
  for (const line of vendorBillLines) {
    const { amortizationScheduleId } = line;
    if (!amortizationScheduleId || amortizationScheduleId === 0) {
      continue;
    }
    const { amortizationHeader, amortizationLines } =
      await getAmortizationScheduleHeaderAndLines(
        session,
        line.amortizationScheduleId
      );
    const { $metadata, ...amortizationHeaderData } = amortizationHeader;
    const { isDraft } = $metadata;
    await (isDraft
      ? createAmortizationFromDraft(session, amortizationHeader.id, line.id)
      : updateAmortization(
          session,
          amortizationHeaderData as AmortizationScheduleHeader.AmortizationScheduleHeader,
          amortizationLines[0] as AmortizationScheduleLine.AmortizationScheduleLine,
          line
        ));
  }
}

async function isVendorBillPosted(
  session: ISession,
  id: number
): Promise<boolean> {
  const vendorBill = await VendorBillHeaderBase.read(session, { id }, [
    'postingStatus',
  ]);
  return vendorBill.postingStatus === EvstJournalEntryStatus.Posted;
}

async function getVendorBillLines(
  session: ISession,
  vendorBillHeaderId: number
): Promise<
  Pick<
    VendorBillItemBase.VendorBillItemBase,
    | 'id'
    | 'amortizationScheduleId'
    | 'accountId'
    | 'departmentId'
    | 'businessUnitId'
    | 'amount'
  >[]
> {
  return VendorBillItemBase.query(
    session,
    {
      where: {
        vendorBillHeaderId,
        lineType: EvstVendorBillLineType.PrepaidExpense,
      },
      draft: 'exclude',
    },
    [
      'id',
      'amortizationScheduleId',
      'accountId',
      'departmentId',
      'businessUnitId',
      'amount',
    ]
  );
}

async function getAmortizationScheduleHeaderAndLines(
  session: ISession,
  id: number
) {
  const amortizationHeader = await AmortizationScheduleHeader.read(
    session,
    { id },
    [
      'id',
      'amortizationEndDate',
      'amortizationMethod',
      'amortizationStartDate',
      'deferredAccountId',
      'description',
    ],
    { draft: 'include' }
  );
  if (!amortizationHeader) {
    throw new BusinessValidationError(
      BusinessValidationErrorCode.VENDOR_BILL_AMORTIZATION_ERROR,
      'Could not find corresponding amortization schedule for the vendor bill',
      'Unable to save vendor bill amortization schedule'
    );
  }
  const amortizationLines = await AmortizationScheduleLine.query(
    session,
    {
      where: {
        amortizationScheduleHeaderId: id,
      },
      draft: 'include',
    },
    ['id', 'recognitionAccountId', 'departmentId', 'businessUnitId']
  );
  return {
    amortizationHeader,
    amortizationLines,
  };
}

async function createAmortizationFromDraft(
  session: ISession,
  asHeaderId: AmortizationScheduleHeader.AmortizationScheduleHeader['id'],
  vendorBillLineId: VendorBillItemBase.VendorBillItemBase['id']
) {
  const client = await AmortizationScheduleHeader.client(session);
  await client.createAmortizationFromDraft([asHeaderId], [vendorBillLineId]);
}

async function updateAmortization(
  session: ISession,
  header: AmortizationScheduleHeader.AmortizationScheduleHeader,
  line: AmortizationScheduleLine.AmortizationScheduleLine,
  vendorBillLine: Pick<
    VendorBillItemBase.VendorBillItemBase,
    | 'id'
    | 'amortizationScheduleId'
    | 'accountId'
    | 'departmentId'
    | 'businessUnitId'
    | 'amount'
  >
) {
  const amortizationLine = {
    ...line,
    departmentId: vendorBillLine?.departmentId ?? line?.departmentId,
    businessUnitId: vendorBillLine?.businessUnitId ?? line?.businessUnitId,
  };
  const client = await AmortizationScheduleHeader.client(session);
  await client.upsertAmortization(
    UpsertAmortizationMode.UpdateAmortizationWithHistoricalImpact,
    header,
    amortizationLine as unknown as AmortizationScheduleLine.AmortizationScheduleLine,
    ['id']
  );
}
