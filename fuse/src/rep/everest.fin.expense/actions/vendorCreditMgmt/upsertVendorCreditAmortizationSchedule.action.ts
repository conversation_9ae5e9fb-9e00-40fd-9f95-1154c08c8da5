import type { ISession } from '@everestsystems/content-core';
import { UpsertAmortizationMode } from '@pkg/everest.fin.accounting/public/amortization/types';
import { AmortizationScheduleHeader } from '@pkg/everest.fin.accounting/types/AmortizationScheduleHeader';
import { AmortizationScheduleLine } from '@pkg/everest.fin.accounting/types/AmortizationScheduleLine';
import { EvstJournalEntryStatus } from '@pkg/everest.fin.accounting/types/enums/JournalEntryStatus';
import {
  BusinessValidationError,
  BusinessValidationErrorCode,
} from '@pkg/everest.fin.expense/public/businessValidationError';
import { EvstVendorCreditLineType } from '@pkg/everest.fin.expense/types/enums/VendorCreditLineType';
import { VendorCreditHeader } from '@pkg/everest.fin.expense/types/VendorCreditHeader';
import { VendorCreditLine } from '@pkg/everest.fin.expense/types/VendorCreditLine';

export default async function upsertVendorCreditAmortizationSchedule(
  session: ISession,
  msg: {
    payload: { vendorCreditHeaderId?: number };
  }
): Promise<void> {
  const { vendorCreditHeaderId } = msg.payload;

  if (vendorCreditHeaderId) {
    const isCreditPosted = await isVendorCreditPosted(
      session,
      vendorCreditHeaderId
    );

    if (isCreditPosted) {
      const vendorCreditLines = await getVendorCreditLines(
        session,
        vendorCreditHeaderId
      );
      if (vendorCreditLines.length > 0) {
        await upsertVCAmortizationCreditLine(session, vendorCreditLines);
      }
    }
  }
}

async function upsertVCAmortizationCreditLine(
  session: ISession,
  vendorCreditLines: Pick<
    VendorCreditLine.VendorCreditLine,
    | 'id'
    | 'amortizationScheduleId'
    | 'accountId'
    | 'departmentId'
    | 'businessUnitId'
    | 'amount'
  >[]
) {
  for (const line of vendorCreditLines) {
    const { amortizationScheduleId } = line;
    if (!amortizationScheduleId) {
      continue;
    }

    const { amortizationHeader, amortizationLines } =
      await getAmortizationScheduleHeaderAndLines(
        session,
        line.amortizationScheduleId
      );

    const { $metadata, ...amortizationHeaderData } = amortizationHeader;
    const { isDraft } = $metadata;

    await (isDraft
      ? createAmortizationFromDraft(session, amortizationHeader.id, line.id)
      : updateAmortization(
          session,
          amortizationHeaderData as AmortizationScheduleHeader.AmortizationScheduleHeader,
          amortizationLines[0] as AmortizationScheduleLine.AmortizationScheduleLine,
          line
        ));
  }
}

async function isVendorCreditPosted(
  session: ISession,
  id: number
): Promise<boolean> {
  const vendorCredit = await VendorCreditHeader.read(session, { id }, [
    'postingStatus',
  ]);
  return vendorCredit.postingStatus === EvstJournalEntryStatus.Posted;
}

async function getVendorCreditLines(
  session: ISession,
  vendorCreditHeaderId: number
): Promise<
  Pick<
    VendorCreditLine.VendorCreditLine,
    | 'id'
    | 'amortizationScheduleId'
    | 'accountId'
    | 'departmentId'
    | 'businessUnitId'
    | 'amount'
  >[]
> {
  return VendorCreditLine.query(
    session,
    {
      where: {
        vendorCreditHeaderId,
        lineType: EvstVendorCreditLineType.Prepaid,
      },
      draft: 'exclude',
    },
    [
      'id',
      'amortizationScheduleId',
      'accountId',
      'departmentId',
      'businessUnitId',
      'amount',
    ]
  );
}

async function getAmortizationScheduleHeaderAndLines(
  session: ISession,
  id: number
) {
  const amortizationHeader = await AmortizationScheduleHeader.read(
    session,
    { id },
    [
      'id',
      'amortizationEndDate',
      'amortizationMethod',
      'amortizationStartDate',
      'deferredAccountId',
      'description',
    ],
    { draft: 'include' }
  );

  if (!amortizationHeader) {
    throw new BusinessValidationError(
      BusinessValidationErrorCode.VENDOR_CREDIT_AMORTIZATION_ERROR,
      'Could not find corresponding amortization schedule for the vendor credit',
      'Unable to save vendor credit amortization schedule'
    );
  }

  const amortizationLines = await AmortizationScheduleLine.query(
    session,
    {
      where: {
        amortizationScheduleHeaderId: id,
      },
      draft: 'include',
    },
    ['id', 'recognitionAccountId', 'departmentId', 'businessUnitId']
  );

  return {
    amortizationHeader,
    amortizationLines,
  };
}

async function createAmortizationFromDraft(
  session: ISession,
  asHeaderId: AmortizationScheduleHeader.AmortizationScheduleHeader['id'],
  vendorCreditLineId: VendorCreditLine.VendorCreditLine['id']
) {
  const client = await AmortizationScheduleHeader.client(session);
  await client.createAmortizationFromDraft(
    [asHeaderId],
    [vendorCreditLineId],
    'VendorCreditLine'
  );
}

async function updateAmortization(
  session: ISession,
  header: AmortizationScheduleHeader.AmortizationScheduleHeader,
  line: AmortizationScheduleLine.AmortizationScheduleLine,
  vendorCreditLine: Pick<
    VendorCreditLine.VendorCreditLine,
    | 'id'
    | 'amortizationScheduleId'
    | 'accountId'
    | 'departmentId'
    | 'businessUnitId'
    | 'amount'
  >
) {
  const amortizationLine = {
    ...line,
    departmentId: vendorCreditLine?.departmentId ?? line?.departmentId,
    businessUnitId: vendorCreditLine?.businessUnitId ?? line?.businessUnitId,
  };

  const client = await AmortizationScheduleHeader.client(session);
  await client.upsertAmortization(
    UpsertAmortizationMode.UpdateAmortizationWithHistoricalImpact,
    header,
    amortizationLine as unknown as AmortizationScheduleLine.AmortizationScheduleLine,
    ['id']
  );
}
