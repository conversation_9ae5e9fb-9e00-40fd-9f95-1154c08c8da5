/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// ******************************************************************************
// Generated Everest Types
// ******************************************************************************

import type { Decimal, RoundingMode } from '@everestsystems/decimal';
import type {
  ArgType,
  Association,
  AssociationManagementArgs,
  AsyncResult,
  Controller,
  ControllerClientProvider,
  CreateOptionsArg,
  DataWithMetadata,
  DeleteOptionsArg,
  FieldSelector,
  Filter,
  flattenFieldSelector,
  GraphInstanceEntry,
  KernelRequestOptions,
  NonAssociationFields,
  Optional,
  PurgeArgs,
  ReadOptionsArg,
  ReadReturnType as ReadReturnTypeGeneric,
  ReadReturnTypeWithAssociations,
  RequireAtLeastOne,
  SemanticSearchArg,
  SemanticSearchRequestOptions,
  TypeSafeQueryArgType,
  UpdateManyOptionsArg,
  UpdateOptionsArg,
  UpsertOptionsArg,
  WriteReturnType,
  WriteReturnTypeWithAssociations,
} from '@everestsystems/content-core';
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { EvstPlainDate as everest_appserver_primitive_PlainDate } from "@pkg/everest.appserver/types/primitives/PlainDate";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstID as everest_appserver_primitive_ID } from "@pkg/everest.appserver/types/primitives/ID";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { EvstDecimal as everest_appserver_primitive_Decimal } from "@pkg/everest.appserver/types/primitives/Decimal";

/** Generated types for View Expenses */
export namespace Expenses {
  export type Expenses = {
    id?: everest_appserver_primitive_Number | null;
    billDate?: everest_appserver_primitive_PlainDate | null;
    billableExpense?: everest_appserver_primitive_Text | null;
    categoryId?: everest_appserver_primitive_ID | null;
    categoryName?: everest_appserver_primitive_Text | null;
    customerId?: everest_appserver_primitive_ID | null;
    description?: everest_appserver_primitive_Text | null;
    employeeId?: everest_appserver_primitive_ID | null;
    employeeName?: everest_appserver_primitive_Text | null;
    expenseType?: everest_appserver_primitive_Text | null;
    expenseReportId?: everest_appserver_primitive_ID | null;
    expenseReportNumber?: everest_appserver_primitive_Text | null;
    merchant?: everest_appserver_primitive_Text | null;
    reimbursable?: everest_appserver_primitive_TrueFalse | null;
    status?: everest_appserver_primitive_Text | null;
    teamId?: everest_appserver_primitive_ID | null;
    amount?: everest_appserver_primitive_Decimal | null;
    amountCurrency?: everest_appserver_primitive_Text | null;
    reimbursableNetAmount?: everest_appserver_primitive_Decimal | null;
    reimbursableNetAmountCurrency?: everest_appserver_primitive_Text | null;
    billableExpenseAssignmentId?: everest_appserver_primitive_ID | null;
    expenseSalesOrderId?: everest_appserver_primitive_ID | null;
    };

  export type UniqueFields = Partial<Pick<Expenses, 'id' | 'billDate' | 'billableExpense' | 'categoryId' | 'categoryName' | 'customerId' | 'description' | 'employeeId' | 'employeeName' | 'expenseType' | 'expenseReportId' | 'expenseReportNumber' | 'merchant' | 'reimbursable' | 'status' | 'teamId' | 'amount' | 'amountCurrency' | 'reimbursableNetAmount' | 'reimbursableNetAmountCurrency' | 'billableExpenseAssignmentId' | 'expenseSalesOrderId'>>;
  export type UniqueWhereInput = Partial<Expenses>;
  export type ReadReturnType<U extends string | number | symbol = keyof Expenses> = ReadReturnTypeGeneric<Expenses, U>;

  export interface IControllerClient extends Omit<Controller<Expenses>, 'all' | 'create' | 'createMany' | 'delete' | 'deleteAll' | 'deleteMany' | 'explainRead' | 'exportMany' | 'getHistory' | 'importMany' | 'lock' | 'lookup' | 'purge' | 'semanticSearch' | 'unlock' | 'update' | 'updateMany' | 'upsert' | 'upsertMany'> {}

  /** @return a model controller instance for Expenses. */
  export function client(env: ControllerClientProvider) {
    return env.controllerClient<Expenses.IControllerClient>(MODEL_URN);
  }

  /** returns a list of all objects matching the requirements. */
  export async function query<U extends keyof Expenses, V extends string = 'ALL_FIELDS'>(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<Expenses>, 'draft'> | Omit<Omit<TypeSafeQueryArgType<Expenses>, 'draft'>, 'where'> & { where?: Partial<Expenses> }, fieldlist: ReadonlyArray<U | V> | 'ALL_FIELDS'): Promise<ReadReturnType<V>[]>;
  export async function query(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<Expenses>, 'draft'>, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown[]> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).query(args, fields as ReadonlyArray<string>);
  }

  export async function queryWithMetadata<U extends keyof Expenses = keyof Expenses>(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<Expenses>, 'draft'>, fieldlist: ReadonlyArray<U>): Promise<DataWithMetadata<Expenses, U>> {
    return (await client(env)).queryWithMetadata(args, fieldlist);
  }

  /** @deprecated Use the alternative overloaded method for reading associations. */
  export async function read<U extends keyof Expenses>(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnType<U>>;
  export async function read(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<unknown> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).read(where, fields as ReadonlyArray<keyof Expenses>, options);
  }

  export const MODEL_URN: string = 'urn:evst:everest:fin/expense:model/view:publicApi/view/Expenses/Expenses';
}
