/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable no-trailing-spaces */
/* eslint-disable no-multiple-empty-lines */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// ******************************************************************************
// Generated Everest Ui Template Types
// ******************************************************************************
import { ReadReturnTypeWithAssociations, UIExecutionContext } from '@everestsystems/content-core';

import employees_loadCurrentActiveEmployees from '@pkg/everest.hr.base/public/actions/Employee/loadCurrentActiveEmployees.action'
import {EmployeeUI as EmployeeNamespace } from '@pkg/everest.hr.base/types/Employee.ui'
import {EntityUI as EntityNamespace } from '@pkg/everest.base/types/Entity.ui'
import {JournalEntryHeaderUI as JournalEntryHeaderNamespace } from '@pkg/everest.fin.accounting/types/JournalEntryHeader.ui'
import {AccountingPeriodUI as AccountingPeriodNamespace } from '@pkg/everest.fin.accounting/types/AccountingPeriod.ui'
import {ExchangeRateUI as ExchangeRateNamespace } from '@pkg/everest.base/types/ExchangeRate.ui'
import {ExpenseReportBaseUI as ExpenseReportBaseNamespace } from '@pkg/everest.fin.expense/types/ExpenseReportBase.ui'
import {ExpenseReportBankDetailUI as ExpenseReportBankDetailNamespace } from '@pkg/everest.fin.expense/types/ExpenseReportBankDetail.ui'
import {ExpenseCategoryUI as ExpenseCategoryNamespace } from '@pkg/everest.fin.expense/types/ExpenseCategory.ui'
import {BusinessUnitUI as BusinessUnitNamespace } from '@pkg/everest.base/types/BusinessUnit.ui'
import {TeamUI as TeamNamespace } from '@pkg/everest.hr.base/types/Team.ui'
import {OutboundPaymentItemBaseUI as OutboundPaymentItemBaseNamespace } from '@pkg/everest.fin.expense/types/OutboundPaymentItemBase.ui'
import {ExpenseReportBusinessObjectUI as ExpenseReportBusinessObjectNamespace } from '@pkg/everest.fin.expense/types/BOComposition/ExpenseReportBusinessObject.ui'
import {BankDetailsUI as BankDetailsNamespace } from '@pkg/everest.fin.base/types/BankDetails.ui'
import {PolicyUI as PolicyNamespace } from '@pkg/everest.appserver/types/permission/Policy.ui'
import {ConfigurationUI as ConfigurationNamespace } from '@pkg/everest.appserver/types/Configuration.ui'
import isPostingPeriodOpen_isExpenseReportPostingPeriodOpen from '@pkg/everest.fin.expense/actions/validations/isExpenseReportPostingPeriodOpen.action'
import {ApprovalPolicyHeaderUI as ApprovalPolicyHeaderNamespace } from '@pkg/everest.base.approvals/types/ApprovalPolicyHeader.ui'

type BasicExecutionContext = UIExecutionContext.BasicExecutionContext;
type InnerPromiseType<T> = T extends Promise<infer R> ? R : T;
type UIModelNodeListType<T> =  UIExecutionContext.UIModelNodeListType<T>
type ListElementType<T> = T extends (infer R)[] ? R : unknown;

export namespace CreateExpenseReportUiTemplate {



export type EmployeeWithAssociation = EmployeeNamespace.EmployeeWithAssociation
export type Employee = EmployeeNamespace.Employee
export type EntityWithAssociation = EntityNamespace.EntityWithAssociation
export type Entity = EntityNamespace.Entity
export type JournalEntryHeaderWithAssociation = JournalEntryHeaderNamespace.JournalEntryHeaderWithAssociation
export type JournalEntryHeader = JournalEntryHeaderNamespace.JournalEntryHeader
export type AccountingPeriodWithAssociation = AccountingPeriodNamespace.AccountingPeriodWithAssociation
export type AccountingPeriod = AccountingPeriodNamespace.AccountingPeriod
export type ExchangeRateWithAssociation = ExchangeRateNamespace.ExchangeRateWithAssociation
export type ExchangeRate = ExchangeRateNamespace.ExchangeRate
export type ExpenseReportBaseWithAssociation = ExpenseReportBaseNamespace.ExpenseReportBaseWithAssociation
export type ExpenseReportBase = ExpenseReportBaseNamespace.ExpenseReportBase
export type ExpenseReportBankDetailWithAssociation = ExpenseReportBankDetailNamespace.ExpenseReportBankDetailWithAssociation
export type ExpenseReportBankDetail = ExpenseReportBankDetailNamespace.ExpenseReportBankDetail
export type ExpenseCategoryWithAssociation = ExpenseCategoryNamespace.ExpenseCategoryWithAssociation
export type ExpenseCategory = ExpenseCategoryNamespace.ExpenseCategory
export type BusinessUnitWithAssociation = BusinessUnitNamespace.BusinessUnitWithAssociation
export type BusinessUnit = BusinessUnitNamespace.BusinessUnit
export type TeamWithAssociation = TeamNamespace.TeamWithAssociation
export type Team = TeamNamespace.Team
export type OutboundPaymentItemBaseWithAssociation = OutboundPaymentItemBaseNamespace.OutboundPaymentItemBaseWithAssociation
export type OutboundPaymentItemBase = OutboundPaymentItemBaseNamespace.OutboundPaymentItemBase
export type ExpenseReportBusinessObject = ExpenseReportBusinessObjectNamespace.ExpenseReportBusinessObject
export type BankDetailsWithAssociation = BankDetailsNamespace.BankDetailsWithAssociation
export type BankDetails = BankDetailsNamespace.BankDetails
export type PolicyWithAssociation = PolicyNamespace.PolicyWithAssociation
export type Policy = PolicyNamespace.Policy
export type ConfigurationWithAssociation = ConfigurationNamespace.ConfigurationWithAssociation
export type Configuration = ConfigurationNamespace.Configuration
export type ApprovalPolicyHeaderWithAssociation = ApprovalPolicyHeaderNamespace.ApprovalPolicyHeaderWithAssociation
export type ApprovalPolicyHeader = ApprovalPolicyHeaderNamespace.ApprovalPolicyHeader



export type CommonDataType = { uuid?: string; _nodeReference: string; /** @deprecated use _nodeParentReference */ _parentReference?: string; _nodeParentReference?: string; $metadata?: { isDraft?: boolean } }
export type SingleNodeItemType = {
        employees: ListElementType<InnerPromiseType<ReturnType<typeof employees_loadCurrentActiveEmployees>>> & CommonDataType;
        entities: ListElementType<InnerPromiseType<ReturnType<EntityNamespace.IControllerClient['queryActiveEntities']>>> & CommonDataType;
        journalEntryHeader: (Pick<JournalEntryHeaderWithAssociation, ("id" | "journalEntryNumber") & keyof JournalEntryHeaderWithAssociation> & {"id"?: unknown; "journalEntryNumber"?: unknown}) & CommonDataType;
        postingPeriod: InnerPromiseType<ReturnType<AccountingPeriodNamespace.IControllerClient['findAccountingPeriod']>> & CommonDataType;
        exchangeRates: ExchangeRate & CommonDataType;
        expenseReport: (Pick<ExpenseReportBaseWithAssociation, ("id" | "expenseNumber" | "status" | "entityId" | "entityName" | "amountPaid" | "submittedDate" | "employeeId" | "employeeName" | "paymentStatus" | "reimbursableTotal" | "reimbursementCurrency" | "openAmount" | "pendingAmount" | "reviewMessage" | "externalUrl" | "origin" | "postingPeriodId") & keyof ExpenseReportBaseWithAssociation> & {"id"?: unknown; "expenseNumber"?: unknown; "status"?: unknown; "entityId"?: unknown; "entityName"?: unknown; "amountPaid"?: unknown; "submittedDate"?: unknown; "employeeId"?: unknown; "employeeName"?: unknown; "paymentStatus"?: unknown; "reimbursableTotal"?: unknown; "reimbursementCurrency"?: unknown; "openAmount"?: unknown; "pendingAmount"?: unknown; "reviewMessage"?: unknown; "externalUrl"?: unknown; "origin"?: unknown; "postingPeriodId"?: unknown} & ReadReturnTypeWithAssociations<ExpenseReportBaseWithAssociation, {"ExpenseReportBase-Employee":["name"]}>) & CommonDataType;
        currentEmployee: InnerPromiseType<ReturnType<EmployeeNamespace.IControllerClient['getCurrentEmployee']>> & CommonDataType;
        expenseReportBankDetails: (Pick<ExpenseReportBankDetailWithAssociation, ("id" | "expenseReportHeaderId" | "bankAccountNumberType") & keyof ExpenseReportBankDetailWithAssociation> & {"id"?: unknown; "expenseReportHeaderId"?: unknown; "bankAccountNumberType"?: unknown}) & CommonDataType;
        expenseItems: (CommonDataType & Record<string, any>);
        expenseCategories: ExpenseCategory & CommonDataType;
        businessUnits: BusinessUnit & CommonDataType;
        teams: Team & CommonDataType;
        outboundPaymentLines: OutboundPaymentItemBase & CommonDataType;
        expenseLineToReview: (CommonDataType & Record<string, any>);
        expenseBO: Pick<ExpenseReportBusinessObject, keyof ExpenseReportBusinessObject & ('id')> & CommonDataType;
        bankDetails: (Pick<BankDetailsWithAssociation, ("id" | "bankAccountNumberType" | "bankAccountHolderName" | "bankAccountNumber" | "financialInstitutionIdType" | "financialInstitutionId") & keyof BankDetailsWithAssociation> & {"id"?: unknown; "bankAccountNumberType"?: unknown; "bankAccountHolderName"?: unknown; "bankAccountNumber"?: unknown; "financialInstitutionIdType"?: unknown; "financialInstitutionId"?: unknown}) & CommonDataType;
        isExpenseAdmin: InnerPromiseType<ReturnType<PolicyNamespace.IControllerClient['checkPermission']>> & CommonDataType;
        businessUnitEnabled: (Pick<ConfigurationWithAssociation, ("value" | "id") & keyof ConfigurationWithAssociation> & {"value"?: unknown; "id"?: unknown}) & CommonDataType;
        isPostingPeriodOpen: InnerPromiseType<ReturnType<typeof isPostingPeriodOpen_isExpenseReportPostingPeriodOpen>> & CommonDataType;
        approvalPolicyHeader: InnerPromiseType<ReturnType<ApprovalPolicyHeaderNamespace.IControllerClient['getDocumentApprovalStatus']>> & CommonDataType;
      }
      
export type CreateExpenseReportData = {
        employees?: UIModelNodeListType<SingleNodeItemType['employees']>;
        entities?: UIModelNodeListType<SingleNodeItemType['entities']>;
        journalEntryHeader?: UIModelNodeListType<SingleNodeItemType['journalEntryHeader']>;
        postingPeriod?: SingleNodeItemType['postingPeriod'];
        exchangeRates?: SingleNodeItemType['exchangeRates'];
        expenseReport?: SingleNodeItemType['expenseReport'];
        currentEmployee?: SingleNodeItemType['currentEmployee'];
        expenseReportBankDetails?: SingleNodeItemType['expenseReportBankDetails'];
        expenseItems?: UIModelNodeListType<SingleNodeItemType['expenseItems']>; // potentially missing dependency or invalid modelId!
        expenseCategories?: UIModelNodeListType<SingleNodeItemType['expenseCategories']>;
        businessUnits?: UIModelNodeListType<SingleNodeItemType['businessUnits']>;
        teams?: UIModelNodeListType<SingleNodeItemType['teams']>;
        outboundPaymentLines?: UIModelNodeListType<SingleNodeItemType['outboundPaymentLines']>;
        expenseLineToReview?: UIModelNodeListType<SingleNodeItemType['expenseLineToReview']>; // potentially missing dependency or invalid modelId!
        expenseBO?: SingleNodeItemType['expenseBO'];
        bankDetails?: SingleNodeItemType['bankDetails'];
        isExpenseAdmin?: SingleNodeItemType['isExpenseAdmin'];
        businessUnitEnabled?: SingleNodeItemType['businessUnitEnabled'];
        isPostingPeriodOpen?: SingleNodeItemType['isPostingPeriodOpen'];
        approvalPolicyHeader?: SingleNodeItemType['approvalPolicyHeader'];
      }
      

export type CreateExpenseReportContext = BasicExecutionContext
  & {
    data: CreateExpenseReportData;
    state: {
        'mode'?: unknown;
        'isEmployeeSelectionTriggered'?: unknown;
        'employeeBankDetailsId'?: unknown;
        'featureToggles'?: unknown;
    } & Record<string, unknown>;
  }
  ;


type OverrideDataPayload = {
  [K in keyof CreateExpenseReportData]: CreateExpenseReportData[K] extends UIModelNodeListType<any>
    ? UIModelNodeListType<any>
    : any;
};

/** 
 * This Helper Type allows you to override the type of the data properties or extend them.
  * if the node you are overriding is of type list in the ui-template then
  * you need to wrap your type in {UIExecutionContext.UIModelNodeListType<T>} instead of normal array or list
  */
export type OverrideCreateExpenseReportData<
  Payload extends Record<string, unknown> & Partial<OverrideDataPayload>
> = Omit<CreateExpenseReportContext, 'data'> & {
  data: {
    [K in keyof CreateExpenseReportData | keyof Payload]: K extends keyof Payload
      ? Payload[K]
      : K extends keyof CreateExpenseReportData
      ? CreateExpenseReportData[K]
      : unknown;
  };
};


}
 /** @deprecated use `CreateExpenseReportUiTemplate.EmployeeWithAssociation` instead. */ export type EmployeeWithAssociation = CreateExpenseReportUiTemplate.EmployeeWithAssociation;
 /** @deprecated use `CreateExpenseReportUiTemplate.Employee` instead. */ export type Employee = CreateExpenseReportUiTemplate.Employee;
 /** @deprecated use `CreateExpenseReportUiTemplate.EntityWithAssociation` instead. */ export type EntityWithAssociation = CreateExpenseReportUiTemplate.EntityWithAssociation;
 /** @deprecated use `CreateExpenseReportUiTemplate.Entity` instead. */ export type Entity = CreateExpenseReportUiTemplate.Entity;
 /** @deprecated use `CreateExpenseReportUiTemplate.JournalEntryHeaderWithAssociation` instead. */ export type JournalEntryHeaderWithAssociation = CreateExpenseReportUiTemplate.JournalEntryHeaderWithAssociation;
 /** @deprecated use `CreateExpenseReportUiTemplate.JournalEntryHeader` instead. */ export type JournalEntryHeader = CreateExpenseReportUiTemplate.JournalEntryHeader;
 /** @deprecated use `CreateExpenseReportUiTemplate.AccountingPeriodWithAssociation` instead. */ export type AccountingPeriodWithAssociation = CreateExpenseReportUiTemplate.AccountingPeriodWithAssociation;
 /** @deprecated use `CreateExpenseReportUiTemplate.AccountingPeriod` instead. */ export type AccountingPeriod = CreateExpenseReportUiTemplate.AccountingPeriod;
 /** @deprecated use `CreateExpenseReportUiTemplate.ExchangeRateWithAssociation` instead. */ export type ExchangeRateWithAssociation = CreateExpenseReportUiTemplate.ExchangeRateWithAssociation;
 /** @deprecated use `CreateExpenseReportUiTemplate.ExchangeRate` instead. */ export type ExchangeRate = CreateExpenseReportUiTemplate.ExchangeRate;
 /** @deprecated use `CreateExpenseReportUiTemplate.ExpenseReportBaseWithAssociation` instead. */ export type ExpenseReportBaseWithAssociation = CreateExpenseReportUiTemplate.ExpenseReportBaseWithAssociation;
 /** @deprecated use `CreateExpenseReportUiTemplate.ExpenseReportBase` instead. */ export type ExpenseReportBase = CreateExpenseReportUiTemplate.ExpenseReportBase;
 /** @deprecated use `CreateExpenseReportUiTemplate.ExpenseReportBankDetailWithAssociation` instead. */ export type ExpenseReportBankDetailWithAssociation = CreateExpenseReportUiTemplate.ExpenseReportBankDetailWithAssociation;
 /** @deprecated use `CreateExpenseReportUiTemplate.ExpenseReportBankDetail` instead. */ export type ExpenseReportBankDetail = CreateExpenseReportUiTemplate.ExpenseReportBankDetail;
 /** @deprecated use `CreateExpenseReportUiTemplate.ExpenseCategoryWithAssociation` instead. */ export type ExpenseCategoryWithAssociation = CreateExpenseReportUiTemplate.ExpenseCategoryWithAssociation;
 /** @deprecated use `CreateExpenseReportUiTemplate.ExpenseCategory` instead. */ export type ExpenseCategory = CreateExpenseReportUiTemplate.ExpenseCategory;
 /** @deprecated use `CreateExpenseReportUiTemplate.BusinessUnitWithAssociation` instead. */ export type BusinessUnitWithAssociation = CreateExpenseReportUiTemplate.BusinessUnitWithAssociation;
 /** @deprecated use `CreateExpenseReportUiTemplate.BusinessUnit` instead. */ export type BusinessUnit = CreateExpenseReportUiTemplate.BusinessUnit;
 /** @deprecated use `CreateExpenseReportUiTemplate.TeamWithAssociation` instead. */ export type TeamWithAssociation = CreateExpenseReportUiTemplate.TeamWithAssociation;
 /** @deprecated use `CreateExpenseReportUiTemplate.Team` instead. */ export type Team = CreateExpenseReportUiTemplate.Team;
 /** @deprecated use `CreateExpenseReportUiTemplate.OutboundPaymentItemBaseWithAssociation` instead. */ export type OutboundPaymentItemBaseWithAssociation = CreateExpenseReportUiTemplate.OutboundPaymentItemBaseWithAssociation;
 /** @deprecated use `CreateExpenseReportUiTemplate.OutboundPaymentItemBase` instead. */ export type OutboundPaymentItemBase = CreateExpenseReportUiTemplate.OutboundPaymentItemBase;
 /** @deprecated use `CreateExpenseReportUiTemplate.ExpenseReportBusinessObject` instead. */ export type ExpenseReportBusinessObject = CreateExpenseReportUiTemplate.ExpenseReportBusinessObject;
 /** @deprecated use `CreateExpenseReportUiTemplate.BankDetailsWithAssociation` instead. */ export type BankDetailsWithAssociation = CreateExpenseReportUiTemplate.BankDetailsWithAssociation;
 /** @deprecated use `CreateExpenseReportUiTemplate.BankDetails` instead. */ export type BankDetails = CreateExpenseReportUiTemplate.BankDetails;
 /** @deprecated use `CreateExpenseReportUiTemplate.PolicyWithAssociation` instead. */ export type PolicyWithAssociation = CreateExpenseReportUiTemplate.PolicyWithAssociation;
 /** @deprecated use `CreateExpenseReportUiTemplate.Policy` instead. */ export type Policy = CreateExpenseReportUiTemplate.Policy;
 /** @deprecated use `CreateExpenseReportUiTemplate.ConfigurationWithAssociation` instead. */ export type ConfigurationWithAssociation = CreateExpenseReportUiTemplate.ConfigurationWithAssociation;
 /** @deprecated use `CreateExpenseReportUiTemplate.Configuration` instead. */ export type Configuration = CreateExpenseReportUiTemplate.Configuration;
 /** @deprecated use `CreateExpenseReportUiTemplate.ApprovalPolicyHeaderWithAssociation` instead. */ export type ApprovalPolicyHeaderWithAssociation = CreateExpenseReportUiTemplate.ApprovalPolicyHeaderWithAssociation;
 /** @deprecated use `CreateExpenseReportUiTemplate.ApprovalPolicyHeader` instead. */ export type ApprovalPolicyHeader = CreateExpenseReportUiTemplate.ApprovalPolicyHeader;
 /** @deprecated use `CreateExpenseReportUiTemplate.CommonDataType` instead. */ export type CommonDataType = CreateExpenseReportUiTemplate.CommonDataType;
 /** @deprecated use `CreateExpenseReportUiTemplate.SingleNodeItemType` instead. */ export type SingleNodeItemType = CreateExpenseReportUiTemplate.SingleNodeItemType;
 /** @deprecated use `CreateExpenseReportUiTemplate.CreateExpenseReportData` instead. */ export type CreateExpenseReportData = CreateExpenseReportUiTemplate.CreateExpenseReportData;
 /** @deprecated use `CreateExpenseReportUiTemplate.CreateExpenseReportContext` instead. */ export type CreateExpenseReportContext = CreateExpenseReportUiTemplate.CreateExpenseReportContext;

type OverrideDataPayload = {
  [K in keyof CreateExpenseReportUiTemplate.CreateExpenseReportData]: CreateExpenseReportUiTemplate.CreateExpenseReportData[K] extends UIModelNodeListType<any>
    ? UIModelNodeListType<any>
    : any;
};

/** @deprecated use `CreateExpenseReportUiTemplate.OverrideCreateExpenseReportData` instead.
 * This Helper Type allows you to override the type of the data properties or extend them.
  * if the node you are overriding is of type list in the ui-template then
  * you need to wrap your type in {UIExecutionContext.UIModelNodeListType<T>} instead of normal array or list
  */
export type OverrideCreateExpenseReportData<
  Payload extends Record<string, unknown> & Partial<OverrideDataPayload>
> = Omit<CreateExpenseReportUiTemplate.CreateExpenseReportContext, 'data'> & {
  data: {
    [K in keyof CreateExpenseReportUiTemplate.CreateExpenseReportData | keyof Payload]: K extends keyof Payload
      ? Payload[K]
      : K extends keyof CreateExpenseReportUiTemplate.CreateExpenseReportData
      ? CreateExpenseReportUiTemplate.CreateExpenseReportData[K]
      : unknown;
  };
};
