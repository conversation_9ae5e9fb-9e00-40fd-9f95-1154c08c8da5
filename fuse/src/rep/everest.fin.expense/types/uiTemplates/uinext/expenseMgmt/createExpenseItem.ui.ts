/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable no-trailing-spaces */
/* eslint-disable no-multiple-empty-lines */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// ******************************************************************************
// Generated Everest Ui Template Types
// ******************************************************************************
import { ReadReturnTypeWithAssociations, UIExecutionContext } from '@everestsystems/content-core';

import {ExpenseItemAttachmentBaseUI as ExpenseItemAttachmentBaseNamespace } from '@pkg/everest.fin.expense/types/ExpenseItemAttachmentBase.ui'
import {ExchangeRateUI as ExchangeRateNamespace } from '@pkg/everest.base/types/ExchangeRate.ui'
import {DraftV2UI as DraftV2Namespace } from '@pkg/everest.appserver/types/metadata/DraftV2.ui'
import {EntityUI as EntityNamespace } from '@pkg/everest.base/types/Entity.ui'
import {VATConfigurationLineUI as VATConfigurationLineNamespace } from '@pkg/everest.fin.expense/types/VATConfigurationLine.ui'
import {ExpenseCategoryUI as ExpenseCategoryNamespace } from '@pkg/everest.fin.expense/types/ExpenseCategory.ui'
import {ConfigurationUI as ConfigurationNamespace } from '@pkg/everest.appserver/types/Configuration.ui'

type BasicExecutionContext = UIExecutionContext.BasicExecutionContext;
type InnerPromiseType<T> = T extends Promise<infer R> ? R : T;
type UIModelNodeListType<T> =  UIExecutionContext.UIModelNodeListType<T>
type ListElementType<T> = T extends (infer R)[] ? R : unknown;

export namespace CreateExpenseItemUiTemplate {



export type ExpenseItemAttachmentBaseWithAssociation = ExpenseItemAttachmentBaseNamespace.ExpenseItemAttachmentBaseWithAssociation
export type ExpenseItemAttachmentBase = ExpenseItemAttachmentBaseNamespace.ExpenseItemAttachmentBase
export type ExchangeRateWithAssociation = ExchangeRateNamespace.ExchangeRateWithAssociation
export type ExchangeRate = ExchangeRateNamespace.ExchangeRate
export type DraftV2WithAssociation = DraftV2Namespace.DraftV2WithAssociation
export type DraftV2 = DraftV2Namespace.DraftV2
export type EntityWithAssociation = EntityNamespace.EntityWithAssociation
export type Entity = EntityNamespace.Entity
export type VATConfigurationLineWithAssociation = VATConfigurationLineNamespace.VATConfigurationLineWithAssociation
export type VATConfigurationLine = VATConfigurationLineNamespace.VATConfigurationLine
export type ExpenseCategoryWithAssociation = ExpenseCategoryNamespace.ExpenseCategoryWithAssociation
export type ExpenseCategory = ExpenseCategoryNamespace.ExpenseCategory
export type ConfigurationWithAssociation = ConfigurationNamespace.ConfigurationWithAssociation
export type Configuration = ConfigurationNamespace.Configuration



export type CommonDataType = { uuid?: string; _nodeReference: string; /** @deprecated use _nodeParentReference */ _parentReference?: string; _nodeParentReference?: string; $metadata?: { isDraft?: boolean } }
export type SingleNodeItemType = {
        ExpenseItemBase: (CommonDataType & Record<string, any>);
        expenseLine: (CommonDataType & Record<string, any>);
        expenseLineAttachment: ExpenseItemAttachmentBase & CommonDataType;
        exchangeRates: ExchangeRate & CommonDataType;
        draftV2: DraftV2 & CommonDataType;
        entities: ListElementType<InnerPromiseType<ReturnType<EntityNamespace.IControllerClient['queryActiveEntities']>>> & CommonDataType;
        vatConfigurationLines: (Pick<VATConfigurationLineWithAssociation, ("id" | "entityId" | "percentage" | "accountId") & keyof VATConfigurationLineWithAssociation> & {"id"?: unknown; "entityId"?: unknown; "percentage"?: unknown; "accountId"?: unknown}) & CommonDataType;
        expenseCategories: ExpenseCategory & CommonDataType;
        businessUnitEnabled: (Pick<ConfigurationWithAssociation, ("value" | "id") & keyof ConfigurationWithAssociation> & {"value"?: unknown; "id"?: unknown}) & CommonDataType;
      }
      
export type CreateExpenseItemData = {
        ExpenseItemBase?: SingleNodeItemType['ExpenseItemBase']; // potentially missing dependency or invalid modelId!
        expenseLine?: SingleNodeItemType['expenseLine']; // potentially missing dependency or invalid modelId!
        expenseLineAttachment?: UIModelNodeListType<SingleNodeItemType['expenseLineAttachment']>;
        exchangeRates?: SingleNodeItemType['exchangeRates'];
        draftV2?: SingleNodeItemType['draftV2'];
        entities?: UIModelNodeListType<SingleNodeItemType['entities']>;
        vatConfigurationLines?: UIModelNodeListType<SingleNodeItemType['vatConfigurationLines']>;
        expenseCategories?: UIModelNodeListType<SingleNodeItemType['expenseCategories']>;
        businessUnitEnabled?: SingleNodeItemType['businessUnitEnabled'];
      }
      

export type CreateExpenseItemContext = BasicExecutionContext
  & {
    data: CreateExpenseItemData;
    state: {
        'openSubmitExpenseReportModal'?: unknown;
    } & Record<string, unknown>;
  }
  ;


type OverrideDataPayload = {
  [K in keyof CreateExpenseItemData]: CreateExpenseItemData[K] extends UIModelNodeListType<any>
    ? UIModelNodeListType<any>
    : any;
};

/** 
 * This Helper Type allows you to override the type of the data properties or extend them.
  * if the node you are overriding is of type list in the ui-template then
  * you need to wrap your type in {UIExecutionContext.UIModelNodeListType<T>} instead of normal array or list
  */
export type OverrideCreateExpenseItemData<
  Payload extends Record<string, unknown> & Partial<OverrideDataPayload>
> = Omit<CreateExpenseItemContext, 'data'> & {
  data: {
    [K in keyof CreateExpenseItemData | keyof Payload]: K extends keyof Payload
      ? Payload[K]
      : K extends keyof CreateExpenseItemData
      ? CreateExpenseItemData[K]
      : unknown;
  };
};


}
 /** @deprecated use `CreateExpenseItemUiTemplate.ExpenseItemAttachmentBaseWithAssociation` instead. */ export type ExpenseItemAttachmentBaseWithAssociation = CreateExpenseItemUiTemplate.ExpenseItemAttachmentBaseWithAssociation;
 /** @deprecated use `CreateExpenseItemUiTemplate.ExpenseItemAttachmentBase` instead. */ export type ExpenseItemAttachmentBase = CreateExpenseItemUiTemplate.ExpenseItemAttachmentBase;
 /** @deprecated use `CreateExpenseItemUiTemplate.ExchangeRateWithAssociation` instead. */ export type ExchangeRateWithAssociation = CreateExpenseItemUiTemplate.ExchangeRateWithAssociation;
 /** @deprecated use `CreateExpenseItemUiTemplate.ExchangeRate` instead. */ export type ExchangeRate = CreateExpenseItemUiTemplate.ExchangeRate;
 /** @deprecated use `CreateExpenseItemUiTemplate.DraftV2WithAssociation` instead. */ export type DraftV2WithAssociation = CreateExpenseItemUiTemplate.DraftV2WithAssociation;
 /** @deprecated use `CreateExpenseItemUiTemplate.DraftV2` instead. */ export type DraftV2 = CreateExpenseItemUiTemplate.DraftV2;
 /** @deprecated use `CreateExpenseItemUiTemplate.EntityWithAssociation` instead. */ export type EntityWithAssociation = CreateExpenseItemUiTemplate.EntityWithAssociation;
 /** @deprecated use `CreateExpenseItemUiTemplate.Entity` instead. */ export type Entity = CreateExpenseItemUiTemplate.Entity;
 /** @deprecated use `CreateExpenseItemUiTemplate.VATConfigurationLineWithAssociation` instead. */ export type VATConfigurationLineWithAssociation = CreateExpenseItemUiTemplate.VATConfigurationLineWithAssociation;
 /** @deprecated use `CreateExpenseItemUiTemplate.VATConfigurationLine` instead. */ export type VATConfigurationLine = CreateExpenseItemUiTemplate.VATConfigurationLine;
 /** @deprecated use `CreateExpenseItemUiTemplate.ExpenseCategoryWithAssociation` instead. */ export type ExpenseCategoryWithAssociation = CreateExpenseItemUiTemplate.ExpenseCategoryWithAssociation;
 /** @deprecated use `CreateExpenseItemUiTemplate.ExpenseCategory` instead. */ export type ExpenseCategory = CreateExpenseItemUiTemplate.ExpenseCategory;
 /** @deprecated use `CreateExpenseItemUiTemplate.ConfigurationWithAssociation` instead. */ export type ConfigurationWithAssociation = CreateExpenseItemUiTemplate.ConfigurationWithAssociation;
 /** @deprecated use `CreateExpenseItemUiTemplate.Configuration` instead. */ export type Configuration = CreateExpenseItemUiTemplate.Configuration;
 /** @deprecated use `CreateExpenseItemUiTemplate.CommonDataType` instead. */ export type CommonDataType = CreateExpenseItemUiTemplate.CommonDataType;
 /** @deprecated use `CreateExpenseItemUiTemplate.SingleNodeItemType` instead. */ export type SingleNodeItemType = CreateExpenseItemUiTemplate.SingleNodeItemType;
 /** @deprecated use `CreateExpenseItemUiTemplate.CreateExpenseItemData` instead. */ export type CreateExpenseItemData = CreateExpenseItemUiTemplate.CreateExpenseItemData;
 /** @deprecated use `CreateExpenseItemUiTemplate.CreateExpenseItemContext` instead. */ export type CreateExpenseItemContext = CreateExpenseItemUiTemplate.CreateExpenseItemContext;

type OverrideDataPayload = {
  [K in keyof CreateExpenseItemUiTemplate.CreateExpenseItemData]: CreateExpenseItemUiTemplate.CreateExpenseItemData[K] extends UIModelNodeListType<any>
    ? UIModelNodeListType<any>
    : any;
};

/** @deprecated use `CreateExpenseItemUiTemplate.OverrideCreateExpenseItemData` instead.
 * This Helper Type allows you to override the type of the data properties or extend them.
  * if the node you are overriding is of type list in the ui-template then
  * you need to wrap your type in {UIExecutionContext.UIModelNodeListType<T>} instead of normal array or list
  */
export type OverrideCreateExpenseItemData<
  Payload extends Record<string, unknown> & Partial<OverrideDataPayload>
> = Omit<CreateExpenseItemUiTemplate.CreateExpenseItemContext, 'data'> & {
  data: {
    [K in keyof CreateExpenseItemUiTemplate.CreateExpenseItemData | keyof Payload]: K extends keyof Payload
      ? Payload[K]
      : K extends keyof CreateExpenseItemUiTemplate.CreateExpenseItemData
      ? CreateExpenseItemUiTemplate.CreateExpenseItemData[K]
      : unknown;
  };
};
