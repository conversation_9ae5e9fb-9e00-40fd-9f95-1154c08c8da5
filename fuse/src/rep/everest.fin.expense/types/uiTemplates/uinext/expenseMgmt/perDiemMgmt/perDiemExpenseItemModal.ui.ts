/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable no-trailing-spaces */
/* eslint-disable no-multiple-empty-lines */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// ******************************************************************************
// Generated Everest Ui Template Types
// ******************************************************************************
import { ReadReturnTypeWithAssociations, UIExecutionContext } from '@everestsystems/content-core';

import {PerDiemExpensePolicyUI as PerDiemExpensePolicyNamespace } from '@pkg/everest.fin.expense/types/PerDiemExpensePolicy.ui'

type BasicExecutionContext = UIExecutionContext.BasicExecutionContext;
type InnerPromiseType<T> = T extends Promise<infer R> ? R : T;
type UIModelNodeListType<T> =  UIExecutionContext.UIModelNodeListType<T>
type ListElementType<T> = T extends (infer R)[] ? R : unknown;

export namespace PerDiemExpenseItemModalUiTemplate {



export type PerDiemExpensePolicyWithAssociation = PerDiemExpensePolicyNamespace.PerDiemExpensePolicyWithAssociation
export type PerDiemExpensePolicy = PerDiemExpensePolicyNamespace.PerDiemExpensePolicy



export type CommonDataType = { uuid?: string; _nodeReference: string; /** @deprecated use _nodeParentReference */ _parentReference?: string; _nodeParentReference?: string; $metadata?: { isDraft?: boolean } }
export type SingleNodeItemType = {
        perDiemPolicy: (Pick<PerDiemExpensePolicyWithAssociation, ("id" | "entityId" | "travelType" | "arrivalOrDepartureDayRate" | "breakFastDeductionRate" | "dinnerDeductionRate" | "fullDayRate" | "lunchDeductionRate" | "minAbsenceDuration" | "partialDayRate" | "travelRules" | "country") & keyof PerDiemExpensePolicyWithAssociation> & {"id"?: unknown; "entityId"?: unknown; "travelType"?: unknown; "arrivalOrDepartureDayRate"?: unknown; "breakFastDeductionRate"?: unknown; "dinnerDeductionRate"?: unknown; "fullDayRate"?: unknown; "lunchDeductionRate"?: unknown; "minAbsenceDuration"?: unknown; "partialDayRate"?: unknown; "travelRules"?: unknown; "country"?: unknown}) & CommonDataType;
        expenseItem: (CommonDataType & Record<string, any>);
        expenseItems: (CommonDataType & Record<string, any>);
      }
      
export type PerDiemExpenseItemModalData = {
        perDiemPolicy?: SingleNodeItemType['perDiemPolicy'];
        expenseItem?: SingleNodeItemType['expenseItem']; // potentially missing dependency or invalid modelId!
        expenseItems?: UIModelNodeListType<SingleNodeItemType['expenseItems']>; // potentially missing dependency or invalid modelId!
      }
      

export type PerDiemExpenseItemModalContext = BasicExecutionContext
  & {
    data: PerDiemExpenseItemModalData;
    state: {
        'expenseItemId'?: unknown;
        'mode'?: unknown;
    } & Record<string, unknown>;
  }
  ;


type OverrideDataPayload = {
  [K in keyof PerDiemExpenseItemModalData]: PerDiemExpenseItemModalData[K] extends UIModelNodeListType<any>
    ? UIModelNodeListType<any>
    : any;
};

/** 
 * This Helper Type allows you to override the type of the data properties or extend them.
  * if the node you are overriding is of type list in the ui-template then
  * you need to wrap your type in {UIExecutionContext.UIModelNodeListType<T>} instead of normal array or list
  */
export type OverridePerDiemExpenseItemModalData<
  Payload extends Record<string, unknown> & Partial<OverrideDataPayload>
> = Omit<PerDiemExpenseItemModalContext, 'data'> & {
  data: {
    [K in keyof PerDiemExpenseItemModalData | keyof Payload]: K extends keyof Payload
      ? Payload[K]
      : K extends keyof PerDiemExpenseItemModalData
      ? PerDiemExpenseItemModalData[K]
      : unknown;
  };
};


}
 /** @deprecated use `PerDiemExpenseItemModalUiTemplate.PerDiemExpensePolicyWithAssociation` instead. */ export type PerDiemExpensePolicyWithAssociation = PerDiemExpenseItemModalUiTemplate.PerDiemExpensePolicyWithAssociation;
 /** @deprecated use `PerDiemExpenseItemModalUiTemplate.PerDiemExpensePolicy` instead. */ export type PerDiemExpensePolicy = PerDiemExpenseItemModalUiTemplate.PerDiemExpensePolicy;
 /** @deprecated use `PerDiemExpenseItemModalUiTemplate.CommonDataType` instead. */ export type CommonDataType = PerDiemExpenseItemModalUiTemplate.CommonDataType;
 /** @deprecated use `PerDiemExpenseItemModalUiTemplate.SingleNodeItemType` instead. */ export type SingleNodeItemType = PerDiemExpenseItemModalUiTemplate.SingleNodeItemType;
 /** @deprecated use `PerDiemExpenseItemModalUiTemplate.PerDiemExpenseItemModalData` instead. */ export type PerDiemExpenseItemModalData = PerDiemExpenseItemModalUiTemplate.PerDiemExpenseItemModalData;
 /** @deprecated use `PerDiemExpenseItemModalUiTemplate.PerDiemExpenseItemModalContext` instead. */ export type PerDiemExpenseItemModalContext = PerDiemExpenseItemModalUiTemplate.PerDiemExpenseItemModalContext;

type OverrideDataPayload = {
  [K in keyof PerDiemExpenseItemModalUiTemplate.PerDiemExpenseItemModalData]: PerDiemExpenseItemModalUiTemplate.PerDiemExpenseItemModalData[K] extends UIModelNodeListType<any>
    ? UIModelNodeListType<any>
    : any;
};

/** @deprecated use `PerDiemExpenseItemModalUiTemplate.OverridePerDiemExpenseItemModalData` instead.
 * This Helper Type allows you to override the type of the data properties or extend them.
  * if the node you are overriding is of type list in the ui-template then
  * you need to wrap your type in {UIExecutionContext.UIModelNodeListType<T>} instead of normal array or list
  */
export type OverridePerDiemExpenseItemModalData<
  Payload extends Record<string, unknown> & Partial<OverrideDataPayload>
> = Omit<PerDiemExpenseItemModalUiTemplate.PerDiemExpenseItemModalContext, 'data'> & {
  data: {
    [K in keyof PerDiemExpenseItemModalUiTemplate.PerDiemExpenseItemModalData | keyof Payload]: K extends keyof Payload
      ? Payload[K]
      : K extends keyof PerDiemExpenseItemModalUiTemplate.PerDiemExpenseItemModalData
      ? PerDiemExpenseItemModalUiTemplate.PerDiemExpenseItemModalData[K]
      : unknown;
  };
};
