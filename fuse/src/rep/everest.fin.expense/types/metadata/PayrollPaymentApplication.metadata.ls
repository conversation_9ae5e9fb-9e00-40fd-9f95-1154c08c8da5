package everest.fin.expense

node PayrollPaymentApplication {
	description: 'This is the intermediate node used to store related data for all payroll payments'
	draftable

	field payrollPaymentApplicationNumber number-range<PayrollPaymentApplication>{
		unique
		persisted
		label: 'Payroll Payment Application Number'
	}

	field accountId Id {
		required
		editable
		persisted
		label: 'Account ID'
		description: 'ID of the Ledger Account to be linked to this payment'
	}

	field accountName Relationship<Text> {
		editable
		label: 'Account Name'
		relation: account.accountName
	}

	field payrollDetailId Id {
		required
		editable
		persisted
		label: 'Payroll Detail ID'
		description: 'ID of the payroll detail record'
	}

	field vendorId Id {
		editable
		persisted
		label: 'Vendor ID'
		description: 'Vendor ID for the Payroll Payment Application'
	}

	field vendorName Relationship<Text> {
		editable
		label: 'Vendor Name'
		relation: vendor.vendorName
	}

	field amountPaid composite<everest.base::CurrencyAmount>{
		required
		editable
		persisted
		label: 'Amount Paid'
	}

	field payeeId Id {
		required
		editable
		persisted
		label: 'Payee ID'
		description: 'Payee ID for the Payroll Payment Application'
		migration: 0
	}

	field payeeCode Relationship<Text> {
		editable
		label: 'Payee Code'
		relation: payrollcategorypayee.code
	}

	field payeeLabel Relationship<Text> {
		editable
		label: 'Payee Label'
		relation: payrollcategorypayee.`label`
	}

	field entityId Relationship<Id> {
		label: 'Entity ID'
		relation: Payroll.entityId
	}

	field employeeId Id {
		editable
		persisted
		label: 'Employee ID'
	}

	field employeeName Relationship<Text> {
		editable
		label: 'Employee Name'
		relation: employee.name
	}

	field accountNumber Relationship<Text> {
		editable
		label: 'Account Number'
		relation: account.accountNumber
	}

	field paymentStatus enum<everest.fin.base::PaymentStatus>{
		formula
		label: 'Payment Status'
		description: 'Linked Outbound Payment status'
	}

	field outboundPaymentId Id {
		formula
		label: 'Outbound Payment ID'
		description: 'Linked Outbound Payment Header ID'
	}

	field `description` Text {
		editable
		persisted
		label: 'Description'
		description: 'Payment application description or reference.'
	}

	association alias Payroll for PayrollPaymentApplication-PayrollDetail

	generated association alias payrollcategorypayee for PayrollPaymentApplication-PayrollCategoryPayee

	generated association alias account for PayrollPaymentApplication-Account

	generated association alias employee for PayrollPaymentApplication-Employee

	generated association alias vendor for PayrollPaymentApplication-Vendor
	query action getPayrollPaymentApplications(outboundPaymentHeaderId: field<OutboundPaymentHeaderBase.id>): Array<node<PayrollPaymentApplication>>
}

association PayrollPaymentApplication-PayrollDetail {
	source: PayrollPaymentApplication
	sourceField: payrollDetailId
	target: PayrollDetail
	targetField: id
	kind: static
	type: reference
	multiplicity: many-to-one
	description: 'Association between  payroll payment application and payroll detail'
}

association PayrollPaymentApplication-PayrollCategoryPayee {
	source: PayrollPaymentApplication
	sourceField: payeeId
	target: PayrollCategoryPayee
	targetField: id
	kind: static
	type: reference
	multiplicity: many-to-one
	description: 'Association between Payroll Payment Application and Payroll Category Payee'
}

association PayrollPaymentApplication-Account {
	source: PayrollPaymentApplication
	sourceField: accountId
	target: everest.fin.accounting::Account
	targetField: id
	kind: static
	type: reference
	multiplicity: many-to-one
	description: ''
}

association PayrollPaymentApplication-Employee {
	source: PayrollPaymentApplication
	sourceField: employeeId
	target: everest.hr.base::Employee
	targetField: id
	kind: static
	type: reference
	multiplicity: many-to-one
	description: ''
}

association PayrollPaymentApplication-Vendor {
	source: PayrollPaymentApplication
	sourceField: vendorId
	target: Vendor
	targetField: id
	kind: static
	type: reference
	multiplicity: many-to-one
	description: ''
}
