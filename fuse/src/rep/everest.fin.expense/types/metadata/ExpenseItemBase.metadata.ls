package everest.fin.expense

node ExpenseItemBase {
	description: 'Expense incurred by an employee on behalf of the organization'
	draftable

	field amount composite<everest.base::CurrencyAmount>{
		required
		editable
		persisted
		label: 'Amount'
		description: 'Expense Item Amount'
	}

	field billDate PlainDate {
		required
		editable
		persisted
		label: 'Bill Date'
		description: 'This is the date when this expense item is incurred or created.'
	}

	field categoryId Id {
		editable
		persisted
		label: 'Category ID'
	}

	field currency primitive<everest.base::CurrencyCodeType>{
		required
		editable
		persisted
		label: 'Currency'
	}

	field `description` Text {
		editable
		persisted
		label: 'Description'
	}

	field distance Number<Decimal> {
		editable
		persisted
		label: 'Distance'
		default: 0
	}

	field expenseId Id {
		editable
		persisted
		label: 'Expense ID'
		description: 'This is the Expense ID that this Expense Item is related to'
		default: NaN
	}

	field expenseType enum<everest.fin.base::ExpenseType>{
		required
		editable
		persisted
		label: 'Expense Type'
		description: 'Types of expenses: 1-Purchase, 2-Travel'
	}

	field merchant Text {
		required
		editable
		persisted
		label: 'Merchant'
	}

	field rate Number<Decimal> {
		editable
		persisted
		label: 'Unit Rate'
		default: 0
	}

	field reimbursable TrueFalse {
		editable
		persisted
		label: 'Reimbursable'
	}

	field unit enum<everest.fin.base::DistanceUnit>{
		editable
		persisted
		label: 'Unit'
		description: 'Unit for distance traveled'
	}

	field reimbursableAmount composite<everest.base::CurrencyAmount>{
		editable
		persisted
		label: 'Reimbursable Amount'
		description: 'Amount * Fx Rate'
	}

	field expenseNumber Relationship<Text> {
		editable
		label: 'Expense Number'
		description: 'This is the Expense that this Expense Item is related to'
		relation: Hea.expenseNumber
	}

	field expenseItemNumber number-range<ExpenseItemBase>{
		unique
		persisted
		label: 'Expense Item Number'
		description: 'This is the system generated field identifying the Expense Item'
	}

	field status Relationship<enum<everest.fin.base::ExpenseStatus>>{
		editable
		label: 'Status'
		description: ''
		relation: Hea.status
	}

	field merchantAndDescription Text {
		editable
		formula
		label: 'Merchant and Description'
		description: 'Mix of merchant name and expense item description'
	}

	field receiptStatus Text {
		editable
		formula
		label: 'Receipt Status'
		description: 'Check if expense item has attachment'
	}

	field origin Text {
		editable
		persisted
		label: 'Origin'
		description: 'Introduce "origin" in nodes to differentiate native and imported records.'
	}

	field fxRate primitive<everest.base::ExchangeRate>{
		editable
		persisted
		label: 'Exchange Rate'
	}

	field accountId primitive<everest.fin.accounting::ExpenseAccountType>{
		editable
		persisted
		label: 'Account ID'
	}

	field accountName Relationship<Text> {
		editable
		label: 'Account Name'
		relation: account.accountName
	}

	field accountNumber Relationship<Text> {
		editable
		label: 'Account Number'
		relation: account.accountNumber
	}

	field vatAccountId primitive<VATBookingAccount>{
		editable
		persisted
		label: 'VAT Account ID'
	}

	field vatAmount composite<everest.base::CurrencyAmount>{
		editable
		persisted
		label: 'VAT Amount'
		description: 'Expense Item VAT Amount'
	}

	field vatPercentage primitive<VATPercentage>{
		editable
		persisted
		label: 'VAT Percentage'
	}

	field reimbursableNetAmount composite<everest.base::CurrencyAmount>{
		editable
		persisted
		label: 'Reimbursable Net Amount'
		description: 'Reimbursable Amount - Reimbursable  VAT Amount '
	}

	field reimbursableVATAmount composite<everest.base::CurrencyAmount>{
		editable
		persisted
		label: 'Reimbursable VAT Amount'
		description: 'VAT Amount * Fx Rate'
	}

	field employeeId Id {
		required
		editable
		persisted
		label: 'Employee ID'
	}

	field businessUnitId primitive<everest.base::BusinessUnit>{
		editable
		persisted
		label: 'Business Unit'
	}

	field externalExpenseItemId Text {
		editable
		persisted
		label: 'External Expense Item ID'
	}

	field billableExpense enum<everest.fin.base::BillableExpenseType>{
		editable
		persisted
		label: 'Billable Expense'
		description: 'Denotes if item is billable expense type or not.'
	}

	field customerId primitive<everest.fin.accounting::CustomerType>{
		editable
		persisted
		label: 'Customer'
		description: 'Customer ID for Billable Expenses'
	}

	field approvedAmount composite<everest.base::CurrencyAmount>{
		editable
		persisted
		label: 'Approved Amount'
	}

	field fullNetAmount composite<everest.base::CurrencyAmount>{
		editable
		persisted
		label: 'Net Amount'
		description: 'Amount - VAT Amount'
	}

	field perDiemItemData JSON {
		editable
		persisted
		label: 'Per Diem Item Data'
		description: 'Per diem item data in JSON which stores travel dates and meal selections.'
	}

	field perDiemPolicyID Id {
		editable
		persisted
		label: 'Per Diem Policy ID'
		description: 'Used per diem policy ID.'
	}

	field attendees Text {
		editable
		persisted
		label: 'Attendees'
		description: 'Attendees in case of expense item for group of people traveling.'
	}

	field teamId primitive<ExpenseTeamType>{
		editable
		persisted
		label: 'Team ID'
	}

	association alias Hea for ExpenseItemBase-ExpenseReportBase

	generated association alias expensecategory for ExpenseItemBase-ExpenseCategory

	generated association alias employee for ExpenseItemBase-Employee

	generated association alias account for ExpenseItem-Account

	generated association alias customer for ExpenseItemBase-Customer

	generated association alias perdiemexpensepolicy for ExpenseItemBase-PerDiemExpensePolicy

	generated association alias team for ExpenseItemBase-Team

	generated association alias expenseitemattachmentbase for ExpenseItemAttachmentBase-ExpenseItemBase
	action detachExpenseItems(msg: JSON)
	action createExpenseItemFromEmail(req: JSON, res: JSON): node<everest.appserver::Notification>
	action deleteOwnExpenseItems(ids: Array<field<ExpenseItemBase.id>>, isDraft: TrueFalse)
	action upsertExpenseItem(expenseItem: node<ExpenseItemBase>, optional attachments: Array<node<ExpenseItemAttachmentBase>>, optional fieldList: Array<Text>, optional options: JSON): node<ExpenseItemBase>
}

association ExpenseItemBase-ExpenseReportBase {
	source: ExpenseItemBase
	sourceField: expenseId
	target: ExpenseReportBase
	targetField: id
	kind: static
	type: containedBy
	multiplicity: many-to-one
	description: 'Association between the Expense Line and the Expense it is contained by'
}

association ExpenseItemBase-ExpenseCategory {
	source: ExpenseItemBase
	sourceField: categoryId
	target: ExpenseCategory
	targetField: id
	kind: static
	type: reference
	multiplicity: many-to-one
	description: 'Association between expense category and expense item'
}

association ExpenseItemBase-Employee {
	source: ExpenseItemBase
	sourceField: employeeId
	target: everest.hr.base::Employee
	targetField: id
	kind: static
	type: reference
	multiplicity: one-to-one
	description: ''
}

association ExpenseItem-Account {
	source: ExpenseItemBase
	sourceField: accountId
	target: everest.fin.accounting::Account
	targetField: id
	kind: static
	type: reference
	multiplicity: many-to-one
	description: ''
}

association ExpenseItemBase-Customer {
	source: ExpenseItemBase
	sourceField: customerId
	target: everest.fin.accounting::Customer
	targetField: id
	kind: static
	type: reference
	multiplicity: many-to-one
	description: 'Association between expense line and customer for billable expenses.'
}

association ExpenseItemBase-PerDiemExpensePolicy {
	source: ExpenseItemBase
	sourceField: perDiemPolicyID
	target: PerDiemExpensePolicy
	targetField: id
	kind: static
	type: reference
	multiplicity: many-to-one
	description: 'Denotes the policy that is used for creating per diem expense item.'
}

association ExpenseItemBase-Team {
	source: ExpenseItemBase
	sourceField: teamId
	target: everest.hr.base::Team
	targetField: id
	kind: static
	type: reference
	multiplicity: many-to-one
	description: ''
}
