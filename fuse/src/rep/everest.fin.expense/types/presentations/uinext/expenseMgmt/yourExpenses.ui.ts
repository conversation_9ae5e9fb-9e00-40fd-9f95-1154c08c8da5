/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

// ******************************************************************************
// Generated Everest Presentation UI Types
// ******************************************************************************

import type {
  GetElementType,
  UIExecutionContext
} from '@everestsystems/content-core';

import type { Employee as everest_hr_base_model_node_Employee } from "@pkg/everest.hr.base/types/Employee";
import type { ExpenseItemBase as everest_fin_expense_model_node_ExpenseItemBase } from "@pkg/everest.fin.expense/types/ExpenseItemBase";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { ExpenseReportBase as everest_fin_expense_model_node_ExpenseReportBase } from "@pkg/everest.fin.expense/types/ExpenseReportBase";
import type { EvstAmount as everest_appserver_primitive_Amount } from "@pkg/everest.appserver/types/primitives/Amount";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { EvstID as everest_appserver_primitive_ID } from "@pkg/everest.appserver/types/primitives/ID";
import type { EvstJSON as everest_appserver_primitive_JSON } from "@pkg/everest.appserver/types/primitives/JSON";

export namespace yourExpensesPresentationUI {
  export namespace dataSets {
    export namespace currentEmployee {
      export type instance = {
        bankDetailsId?: everest_hr_base_model_node_Employee.Employee["bankDetailsId"] | undefined | null;
        entityId?: everest_hr_base_model_node_Employee.Employee["entityId"] | undefined | null;
        id?: everest_hr_base_model_node_Employee.Employee["id"] | undefined | null;
        /** Unique UI identifier of the node instance */
        _nodeReference: string;
      };

      export type data = instance;
    }

    export namespace expenseItemDrafts {
      export type instance = {
        amount?: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["amount"] | undefined | null;
        billDate?: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["billDate"] | undefined | null;
        category?: everest_appserver_primitive_Text | undefined | null;
        currency?: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["currency"] | undefined | null;
        expenseItemNumber?: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["expenseItemNumber"] | undefined | null;
        id?: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["id"] | undefined | null;
        merchantAndDescription?: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["merchantAndDescription"] | undefined | null;
        receiptStatus?: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["receiptStatus"] | undefined | null;
        teamId?: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["teamId"] | undefined | null;
        /** Unique UI identifier of the node instance */
        _nodeReference: string;
      };

      export type data = instance[];
    }

    export namespace expenseItemsStatistics {
      export type instance = {
        unsubmittedExpenseItemsCount?: everest_appserver_primitive_Number | undefined | null;
        /** Unique UI identifier of the node instance */
        _nodeReference: string;
      };

      export type data = instance;
    }

    export namespace expenseReports {
      export type instance = {
        employeeId?: everest_fin_expense_model_node_ExpenseReportBase.ExpenseReportBase["employeeId"] | undefined | null;
        expenseNumber?: everest_fin_expense_model_node_ExpenseReportBase.ExpenseReportBase["expenseNumber"] | undefined | null;
        id?: everest_fin_expense_model_node_ExpenseReportBase.ExpenseReportBase["id"] | undefined | null;
        paymentStatus?: everest_fin_expense_model_node_ExpenseReportBase.ExpenseReportBase["paymentStatus"] | undefined | null;
        reimbursableTotal?: everest_fin_expense_model_node_ExpenseReportBase.ExpenseReportBase["reimbursableTotal"] | undefined | null;
        reimbursementCurrency?: everest_fin_expense_model_node_ExpenseReportBase.ExpenseReportBase["reimbursementCurrency"] | undefined | null;
        status?: everest_fin_expense_model_node_ExpenseReportBase.ExpenseReportBase["status"] | undefined | null;
        submittedDate?: everest_fin_expense_model_node_ExpenseReportBase.ExpenseReportBase["submittedDate"] | undefined | null;
        /** Unique UI identifier of the node instance */
        _nodeReference: string;
      };

      export type data = instance[];
    }

    export namespace expenseReportsStatistics {
      export type instance = {
        approvedReports?: everest_appserver_primitive_Number | undefined | null;
        moneyOwed?: everest_appserver_primitive_Amount | undefined | null;
        openExpenseReportsCount?: everest_appserver_primitive_Number | undefined | null;
        rejectedReports?: everest_appserver_primitive_Number | undefined | null;
        rejectedReportsIcon?: everest_appserver_primitive_Text | undefined | null;
        reportsPendingApproval?: everest_appserver_primitive_Number | undefined | null;
        /** Unique UI identifier of the node instance */
        _nodeReference: string;
      };

      export type data = instance;
    }

    export namespace globalAlerts {
      export type instance = {
        bankDetailsNotFound?: everest_appserver_primitive_TrueFalse | undefined | null;
        employeeRecordNotFound?: everest_appserver_primitive_TrueFalse | undefined | null;
        /** Unique UI identifier of the node instance */
        _nodeReference: string;
      };

      export type data = instance;
    }

    export namespace unsubmittedExpenseItems {
      export type instance = {
        amount?: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["amount"] | undefined | null;
        billDate?: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["billDate"] | undefined | null;
        category?: everest_appserver_primitive_Text | undefined | null;
        currency?: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["currency"] | undefined | null;
        expenseItemNumber?: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["expenseItemNumber"] | undefined | null;
        expenseType?: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["expenseType"] | undefined | null;
        id?: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["id"] | undefined | null;
        merchantAndDescription?: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["merchantAndDescription"] | undefined | null;
        receiptStatus?: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["receiptStatus"] | undefined | null;
        reimbursableAmount?: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["reimbursableAmount"] | undefined | null;
        teamId?: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["teamId"] | undefined | null;
        /** Unique UI identifier of the node instance */
        _nodeReference: string;
      };

      export type data = instance[];
    }
  }

  export namespace actions {
    export namespace deleteEI {
      export type input = never;
      export type output = void;
    }

    export namespace deleteExpenseItems {
      export type input = never;
      export type output = void;
    }

    export namespace deleteExpenseItemsDrafts {
      export type input = never;
      export type output = void;
    }

    export namespace getSelectedExpenseItems {
      export type input = never;
      export type output = {
        ids: everest_appserver_primitive_ID[];
      };
    }

    export namespace processScannedFiles {
      export type input = {
        files: everest_appserver_primitive_JSON;
      };
      export type output = void;
    }
  }

  export type context = UIExecutionContext.BasicExecutionContext & {
    data: {
      currentEmployee: dataSets.currentEmployee.data;
      expenseItemDrafts: dataSets.expenseItemDrafts.data;
      expenseItemsStatistics: dataSets.expenseItemsStatistics.data;
      expenseReports: dataSets.expenseReports.data;
      expenseReportsStatistics: dataSets.expenseReportsStatistics.data;
      globalAlerts: dataSets.globalAlerts.data;
      unsubmittedExpenseItems: dataSets.unsubmittedExpenseItems.data;
    };
    state: Record<string, unknown>;
    presentationClient: {
      deleteEI: () => Promise<actions.deleteEI.output>;
      deleteExpenseItems: () => Promise<actions.deleteExpenseItems.output>;
      deleteExpenseItemsDrafts: () => Promise<actions.deleteExpenseItemsDrafts.output>;
      getSelectedExpenseItems: () => Promise<actions.getSelectedExpenseItems.output>;
      processScannedFiles: (input: actions.processScannedFiles.input) => Promise<actions.processScannedFiles.output>;
    };
  }
}
