/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

// ******************************************************************************
// Generated Everest Presentation Types
// ******************************************************************************

import type {
  ActionValidationResult,
  COMPONENT,
  CONFIGURATIONS,
  DATA,
  DynamicFieldsMetadata,
  GetElementType,
  DynamicFieldDefinition,
  DATA_SETS,
  DYNAMIC_FIELDS,
  FieldInstanceMetadata,
  FieldLevelMetadata,
  IDENTIFIER,
  ISession,
  METADATA,
  QueryInput,
  QueryReason,
  RoutineDeterminationResult,
  RoutineValidationContext,
  RoutineValidationResult,
  TableComponent,
  ValidateReason
} from '@everestsystems/content-core';

import type { ExpenseItemBase as everest_fin_expense_model_node_ExpenseItemBase } from "@pkg/everest.fin.expense/types/ExpenseItemBase";
import type { EvstID as everest_appserver_primitive_ID } from "@pkg/everest.appserver/types/primitives/ID";
import type { EvstJSON as everest_appserver_primitive_JSON } from "@pkg/everest.appserver/types/primitives/JSON";
import type { Employee as everest_hr_base_model_node_Employee } from "@pkg/everest.hr.base/types/Employee";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { ExpenseReportBase as everest_fin_expense_model_node_ExpenseReportBase } from "@pkg/everest.fin.expense/types/ExpenseReportBase";
import type { EvstAmount as everest_appserver_primitive_Amount } from "@pkg/everest.appserver/types/primitives/Amount";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";

export namespace yourExpensesPresentation {
  export type mode = 'view';

  export namespace actions {
    export namespace deleteExpenseItems {
      export type input = {
        selectedItems: {
          id: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["id"];
        }[];
      };

      export type output = void;

      export type validateInput = {
        session: ISession;
        input: input;
      }

      export type executeInput = {
        session: ISession;
        input: input;
      }

      export type execute = (
        executeInput: executeInput
      ) => Promise<output>;

      export type validate = (
        validateInput: validateInput
      ) => Promise<ActionValidationResult>;

      export type implementation =
        | execute
        | {
            validate?: validate;
            execute: execute;
          };
    }

    export namespace deleteExpenseItemsDrafts {
      export type input = {
        selectedItems: {
          id: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["id"];
        }[];
      };

      export type output = void;

      export type validateInput = {
        session: ISession;
        input: input;
      }

      export type executeInput = {
        session: ISession;
        input: input;
      }

      export type execute = (
        executeInput: executeInput
      ) => Promise<output>;

      export type validate = (
        validateInput: validateInput
      ) => Promise<ActionValidationResult>;

      export type implementation =
        | execute
        | {
            validate?: validate;
            execute: execute;
          };
    }

    export namespace getSelectedExpenseItems {
      export type input = {
        selectedItems: {
          id: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["id"];
        }[];
      };

      export type output = {
        ids: everest_appserver_primitive_ID[];
      };

      export type validateInput = {
        session: ISession;
        input: input;
      }

      export type executeInput = {
        session: ISession;
        input: input;
      }

      export type execute = (
        executeInput: executeInput
      ) => Promise<output>;

      export type validate = (
        validateInput: validateInput
      ) => Promise<ActionValidationResult>;

      export type implementation =
        | execute
        | {
            validate?: validate;
            execute: execute;
          };
    }

    export namespace processScannedFiles {
      export type input = {
        files: everest_appserver_primitive_JSON;
      };

      export type output = void;

      export type validateInput = {
        session: ISession;
        input: input;
      }

      export type executeInput = {
        session: ISession;
        input: input;
      }

      export type execute = (
        executeInput: executeInput
      ) => Promise<output>;

      export type validate = (
        validateInput: validateInput
      ) => Promise<ActionValidationResult>;

      export type implementation =
        | execute
        | {
            validate?: validate;
            execute: execute;
          };
    }
  }

  export namespace dataSources {
    export namespace currentEmployee {
      export type levels = {
        '': {
          bankDetailsId?: everest_hr_base_model_node_Employee.Employee["bankDetailsId"] | undefined | null;
          entityId?: everest_hr_base_model_node_Employee.Employee["entityId"] | undefined | null;
          id?: everest_hr_base_model_node_Employee.Employee["id"] | undefined | null;
        };
      };

      export type instanceMetadata = {
        '': {
          bankDetailsId?: FieldInstanceMetadata | undefined;
          entityId?: FieldInstanceMetadata | undefined;
          id?: FieldInstanceMetadata | undefined;
        };
      };

      export type levelMetadata = {
        '': {
          bankDetailsId?: FieldLevelMetadata | undefined;
          entityId?: FieldLevelMetadata | undefined;
          id?: FieldLevelMetadata | undefined;
        };
      };

      export type levelConfigurations = {
        '': never;
      };

      export type parameters = never;

      export namespace callbacks {
        export namespace setUp {
          export type input = {
            session: ISession;
            mode: mode;
          };

          export type output = void;
        }

        export namespace query {
          export type queryParameters = never;

          export type queryMetadata = levelMetadata[''];

          export type queryConfigurations = levelConfigurations[''];

          export type queryData = levels[''] & {
            [METADATA]?: instanceMetadata[''] | undefined;
          };

          export type input = {
            session: ISession;
            mode: mode;
            queryReason: QueryReason;
            queryInput: QueryInput;
            queryInstruction: {
              fields: ReadonlySet<keyof levels['']> | undefined;
            };
          };

          export type output = queryData;

          export type combinedOutput = queryData | {
            [DATA]: queryData;
            [METADATA]?: queryMetadata | undefined;
            [CONFIGURATIONS]?: queryConfigurations | undefined;
            [key: string]: never;
          };
        }
      }

      export interface implementation {
        setUp?(input: callbacks.setUp.input): Promise<callbacks.setUp.output>;

        query(input: callbacks.query.input): Promise<callbacks.query.combinedOutput>;
      }
    }

    export namespace expenseItems {
      export type levels = {
        '': {
          amount?: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["amount"] | undefined | null;
          billDate?: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["billDate"] | undefined | null;
          category?: everest_appserver_primitive_Text | undefined | null;
          currency?: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["currency"] | undefined | null;
          expenseItemNumber?: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["expenseItemNumber"] | undefined | null;
          expenseType?: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["expenseType"] | undefined | null;
          id?: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["id"] | undefined | null;
          merchantAndDescription?: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["merchantAndDescription"] | undefined | null;
          receiptStatus?: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["receiptStatus"] | undefined | null;
          reimbursableAmount?: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["reimbursableAmount"] | undefined | null;
          teamId?: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["teamId"] | undefined | null;
        };
      };

      export type instanceMetadata = {
        '': {
          amount?: FieldInstanceMetadata | undefined;
          billDate?: FieldInstanceMetadata | undefined;
          category?: FieldInstanceMetadata | undefined;
          currency?: FieldInstanceMetadata | undefined;
          expenseItemNumber?: FieldInstanceMetadata | undefined;
          expenseType?: FieldInstanceMetadata | undefined;
          id?: FieldInstanceMetadata | undefined;
          merchantAndDescription?: FieldInstanceMetadata | undefined;
          receiptStatus?: FieldInstanceMetadata | undefined;
          reimbursableAmount?: FieldInstanceMetadata | undefined;
          teamId?: FieldInstanceMetadata | undefined;
        };
      };

      export type levelMetadata = {
        '': {
          amount?: FieldLevelMetadata | undefined;
          billDate?: FieldLevelMetadata | undefined;
          category?: FieldLevelMetadata | undefined;
          currency?: FieldLevelMetadata | undefined;
          expenseItemNumber?: FieldLevelMetadata | undefined;
          expenseType?: FieldLevelMetadata | undefined;
          id?: FieldLevelMetadata | undefined;
          merchantAndDescription?: FieldLevelMetadata | undefined;
          receiptStatus?: FieldLevelMetadata | undefined;
          reimbursableAmount?: FieldLevelMetadata | undefined;
          teamId?: FieldLevelMetadata | undefined;
        };
      };

      export type levelConfigurations = {
        '': never;
      };

      export type parameters = {
        currentEmployeeId: everest_hr_base_model_node_Employee.Employee["id"] | undefined;
      };

      export namespace callbacks {
        export namespace setUp {
          export type input = {
            session: ISession;
            mode: mode;
            parameters: parameters;
          };

          export type output = void;
        }

        export namespace query {
          export type queryParameters = never;

          export type queryMetadata = levelMetadata[''];

          export type queryConfigurations = levelConfigurations[''];

          export type queryData = (levels[''] & {
            [IDENTIFIER]?: number | string;
            [METADATA]?: instanceMetadata[''] | undefined;
          })[];

          export type input = {
            session: ISession;
            mode: mode;
            parameters: parameters;
            queryReason: QueryReason;
            queryInput: QueryInput;
            queryInstruction: {
              fields: ReadonlySet<keyof levels['']> | undefined;
              filters: Record<keyof levels[''], unknown> | undefined;
              orders: ReadonlyArray<{
                field: keyof levels[''];
                ordering: 'asc' | 'desc';
              }> | undefined;
              page: {
                skip: number;
                take: number;
              } | undefined;
            };
          };

          export type output = queryData;

          export type combinedOutput = queryData | {
            [DATA]: queryData;
            [METADATA]?: queryMetadata | undefined;
            [CONFIGURATIONS]?: queryConfigurations | undefined;
            [key: string]: never;
          };
        }
      }

      export namespace routines {
        export namespace deleteExpenseItemsRoutine {
          export type input = {
            selectedItems: {
              id: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["id"];
            }[];
          };

          export type output = void;

          export type determineInput = {
            session: ISession;
            mode: mode;
            parameters: parameters;
            input: Pick<input, 'selectedItems'>;
          };

          export type determineOutput = RoutineDeterminationResult;

          export type validateInput = {
            session: ISession;
            mode: mode;
            parameters: parameters;
            validateReason: ValidateReason;
            context: RoutineValidationContext;
            input: input;
          };

          export type validateOutput = RoutineValidationResult;

          export type executeInput = {
            session: ISession;
            mode: mode;
            parameters: parameters;
            input: input;
          };

          export type executeOutput = output;
        }
      }

      export interface implementation {
        setUp?(input: callbacks.setUp.input): Promise<callbacks.setUp.output>;

        query(input: callbacks.query.input): Promise<callbacks.query.combinedOutput>;

        determine_deleteExpenseItemsRoutine?(input: routines.deleteExpenseItemsRoutine.determineInput): Promise<routines.deleteExpenseItemsRoutine.determineOutput>;

        validate_deleteExpenseItemsRoutine?(input: routines.deleteExpenseItemsRoutine.validateInput): Promise<routines.deleteExpenseItemsRoutine.validateOutput>;

        execute_deleteExpenseItemsRoutine(input: routines.deleteExpenseItemsRoutine.executeInput): Promise<routines.deleteExpenseItemsRoutine.executeOutput>;
      }
    }

    export namespace expenseItemsDrafts {
      export type levels = {
        '': {
          amount?: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["amount"] | undefined | null;
          billDate?: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["billDate"] | undefined | null;
          category?: everest_appserver_primitive_Text | undefined | null;
          currency?: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["currency"] | undefined | null;
          expenseItemNumber?: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["expenseItemNumber"] | undefined | null;
          id?: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["id"] | undefined | null;
          merchantAndDescription?: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["merchantAndDescription"] | undefined | null;
          receiptStatus?: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["receiptStatus"] | undefined | null;
          teamId?: everest_fin_expense_model_node_ExpenseItemBase.ExpenseItemBase["teamId"] | undefined | null;
        };
      };

      export type instanceMetadata = {
        '': {
          amount?: FieldInstanceMetadata | undefined;
          billDate?: FieldInstanceMetadata | undefined;
          category?: FieldInstanceMetadata | undefined;
          currency?: FieldInstanceMetadata | undefined;
          expenseItemNumber?: FieldInstanceMetadata | undefined;
          id?: FieldInstanceMetadata | undefined;
          merchantAndDescription?: FieldInstanceMetadata | undefined;
          receiptStatus?: FieldInstanceMetadata | undefined;
          teamId?: FieldInstanceMetadata | undefined;
        };
      };

      export type levelMetadata = {
        '': {
          amount?: FieldLevelMetadata | undefined;
          billDate?: FieldLevelMetadata | undefined;
          category?: FieldLevelMetadata | undefined;
          currency?: FieldLevelMetadata | undefined;
          expenseItemNumber?: FieldLevelMetadata | undefined;
          id?: FieldLevelMetadata | undefined;
          merchantAndDescription?: FieldLevelMetadata | undefined;
          receiptStatus?: FieldLevelMetadata | undefined;
          teamId?: FieldLevelMetadata | undefined;
        };
      };

      export type levelConfigurations = {
        '': never;
      };

      export type parameters = {
        currentEmployeeId: everest_hr_base_model_node_Employee.Employee["id"] | undefined;
      };

      export namespace callbacks {
        export namespace setUp {
          export type input = {
            session: ISession;
            mode: mode;
            parameters: parameters;
          };

          export type output = void;
        }

        export namespace query {
          export type queryParameters = never;

          export type queryMetadata = levelMetadata[''];

          export type queryConfigurations = levelConfigurations[''];

          export type queryData = (levels[''] & {
            [IDENTIFIER]?: number | string;
            [METADATA]?: instanceMetadata[''] | undefined;
          })[];

          export type input = {
            session: ISession;
            mode: mode;
            parameters: parameters;
            queryReason: QueryReason;
            queryInput: QueryInput;
            queryInstruction: {
              fields: ReadonlySet<keyof levels['']> | undefined;
              filters: Record<keyof levels[''], unknown> | undefined;
              orders: ReadonlyArray<{
                field: keyof levels[''];
                ordering: 'asc' | 'desc';
              }> | undefined;
              page: {
                skip: number;
                take: number;
              } | undefined;
            };
          };

          export type output = queryData;

          export type combinedOutput = queryData | {
            [DATA]: queryData;
            [METADATA]?: queryMetadata | undefined;
            [CONFIGURATIONS]?: queryConfigurations | undefined;
            [key: string]: never;
          };
        }
      }

      export interface implementation {
        setUp?(input: callbacks.setUp.input): Promise<callbacks.setUp.output>;

        query(input: callbacks.query.input): Promise<callbacks.query.combinedOutput>;
      }
    }

    export namespace expenseItemsStatistics {
      export type levels = {
        '': {
          unsubmittedExpenseItemsCount?: everest_appserver_primitive_Number | undefined | null;
        };
      };

      export type instanceMetadata = {
        '': {
          unsubmittedExpenseItemsCount?: FieldInstanceMetadata | undefined;
        };
      };

      export type levelMetadata = {
        '': {
          unsubmittedExpenseItemsCount?: FieldLevelMetadata | undefined;
        };
      };

      export type levelConfigurations = {
        '': never;
      };

      export type parameters = {
        currentEmployeeId: everest_hr_base_model_node_Employee.Employee["id"] | undefined;
      };

      export namespace callbacks {
        export namespace setUp {
          export type input = {
            session: ISession;
            mode: mode;
            parameters: parameters;
          };

          export type output = void;
        }

        export namespace query {
          export type queryParameters = never;

          export type queryMetadata = levelMetadata[''];

          export type queryConfigurations = levelConfigurations[''];

          export type queryData = levels[''] & {
            [METADATA]?: instanceMetadata[''] | undefined;
          };

          export type input = {
            session: ISession;
            mode: mode;
            parameters: parameters;
            queryReason: QueryReason;
            queryInput: QueryInput;
            queryInstruction: {
              fields: ReadonlySet<keyof levels['']> | undefined;
            };
          };

          export type output = queryData;

          export type combinedOutput = queryData | {
            [DATA]: queryData;
            [METADATA]?: queryMetadata | undefined;
            [CONFIGURATIONS]?: queryConfigurations | undefined;
            [key: string]: never;
          };
        }
      }

      export interface implementation {
        setUp?(input: callbacks.setUp.input): Promise<callbacks.setUp.output>;

        query(input: callbacks.query.input): Promise<callbacks.query.combinedOutput>;
      }
    }

    export namespace expenseReports {
      export type levels = {
        '': {
          employeeId?: everest_fin_expense_model_node_ExpenseReportBase.ExpenseReportBase["employeeId"] | undefined | null;
          expenseNumber?: everest_fin_expense_model_node_ExpenseReportBase.ExpenseReportBase["expenseNumber"] | undefined | null;
          id?: everest_fin_expense_model_node_ExpenseReportBase.ExpenseReportBase["id"] | undefined | null;
          paymentStatus?: everest_fin_expense_model_node_ExpenseReportBase.ExpenseReportBase["paymentStatus"] | undefined | null;
          reimbursableTotal?: everest_fin_expense_model_node_ExpenseReportBase.ExpenseReportBase["reimbursableTotal"] | undefined | null;
          reimbursementCurrency?: everest_fin_expense_model_node_ExpenseReportBase.ExpenseReportBase["reimbursementCurrency"] | undefined | null;
          status?: everest_fin_expense_model_node_ExpenseReportBase.ExpenseReportBase["status"] | undefined | null;
          submittedDate?: everest_fin_expense_model_node_ExpenseReportBase.ExpenseReportBase["submittedDate"] | undefined | null;
        };
      };

      export type instanceMetadata = {
        '': {
          employeeId?: FieldInstanceMetadata | undefined;
          expenseNumber?: FieldInstanceMetadata | undefined;
          id?: FieldInstanceMetadata | undefined;
          paymentStatus?: FieldInstanceMetadata | undefined;
          reimbursableTotal?: FieldInstanceMetadata | undefined;
          reimbursementCurrency?: FieldInstanceMetadata | undefined;
          status?: FieldInstanceMetadata | undefined;
          submittedDate?: FieldInstanceMetadata | undefined;
        };
      };

      export type levelMetadata = {
        '': {
          employeeId?: FieldLevelMetadata | undefined;
          expenseNumber?: FieldLevelMetadata | undefined;
          id?: FieldLevelMetadata | undefined;
          paymentStatus?: FieldLevelMetadata | undefined;
          reimbursableTotal?: FieldLevelMetadata | undefined;
          reimbursementCurrency?: FieldLevelMetadata | undefined;
          status?: FieldLevelMetadata | undefined;
          submittedDate?: FieldLevelMetadata | undefined;
        };
      };

      export type levelConfigurations = {
        '': never;
      };

      export type parameters = {
        currentEmployeeId: everest_hr_base_model_node_Employee.Employee["id"] | undefined;
      };

      export namespace callbacks {
        export namespace setUp {
          export type input = {
            session: ISession;
            mode: mode;
            parameters: parameters;
          };

          export type output = void;
        }

        export namespace query {
          export type queryParameters = never;

          export type queryMetadata = levelMetadata[''];

          export type queryConfigurations = levelConfigurations[''];

          export type queryData = (levels[''] & {
            [IDENTIFIER]?: number | string;
            [METADATA]?: instanceMetadata[''] | undefined;
          })[];

          export type input = {
            session: ISession;
            mode: mode;
            parameters: parameters;
            queryReason: QueryReason;
            queryInput: QueryInput;
            queryInstruction: {
              fields: ReadonlySet<keyof levels['']> | undefined;
              filters: Record<keyof levels[''], unknown> | undefined;
              orders: ReadonlyArray<{
                field: keyof levels[''];
                ordering: 'asc' | 'desc';
              }> | undefined;
              page: {
                skip: number;
                take: number;
              } | undefined;
            };
          };

          export type output = queryData;

          export type combinedOutput = queryData | {
            [DATA]: queryData;
            [METADATA]?: queryMetadata | undefined;
            [CONFIGURATIONS]?: queryConfigurations | undefined;
            [key: string]: never;
          };
        }
      }

      export interface implementation {
        setUp?(input: callbacks.setUp.input): Promise<callbacks.setUp.output>;

        query(input: callbacks.query.input): Promise<callbacks.query.combinedOutput>;
      }
    }

    export namespace expenseReportsStatistics {
      export type levels = {
        '': {
          approvedReports?: everest_appserver_primitive_Number | undefined | null;
          moneyOwed?: everest_appserver_primitive_Amount | undefined | null;
          openExpenseReportsCount?: everest_appserver_primitive_Number | undefined | null;
          rejectedReports?: everest_appserver_primitive_Number | undefined | null;
          rejectedReportsIcon?: everest_appserver_primitive_Text | undefined | null;
          reportsPendingApproval?: everest_appserver_primitive_Number | undefined | null;
        };
      };

      export type instanceMetadata = {
        '': {
          approvedReports?: FieldInstanceMetadata | undefined;
          moneyOwed?: FieldInstanceMetadata | undefined;
          openExpenseReportsCount?: FieldInstanceMetadata | undefined;
          rejectedReports?: FieldInstanceMetadata | undefined;
          rejectedReportsIcon?: FieldInstanceMetadata | undefined;
          reportsPendingApproval?: FieldInstanceMetadata | undefined;
        };
      };

      export type levelMetadata = {
        '': {
          approvedReports?: FieldLevelMetadata | undefined;
          moneyOwed?: FieldLevelMetadata | undefined;
          openExpenseReportsCount?: FieldLevelMetadata | undefined;
          rejectedReports?: FieldLevelMetadata | undefined;
          rejectedReportsIcon?: FieldLevelMetadata | undefined;
          reportsPendingApproval?: FieldLevelMetadata | undefined;
        };
      };

      export type levelConfigurations = {
        '': never;
      };

      export type parameters = {
        currentEmployeeId: everest_hr_base_model_node_Employee.Employee["id"] | undefined;
      };

      export namespace callbacks {
        export namespace setUp {
          export type input = {
            session: ISession;
            mode: mode;
            parameters: parameters;
          };

          export type output = void;
        }

        export namespace query {
          export type queryParameters = never;

          export type queryMetadata = levelMetadata[''];

          export type queryConfigurations = levelConfigurations[''];

          export type queryData = levels[''] & {
            [METADATA]?: instanceMetadata[''] | undefined;
          };

          export type input = {
            session: ISession;
            mode: mode;
            parameters: parameters;
            queryReason: QueryReason;
            queryInput: QueryInput;
            queryInstruction: {
              fields: ReadonlySet<keyof levels['']> | undefined;
            };
          };

          export type output = queryData;

          export type combinedOutput = queryData | {
            [DATA]: queryData;
            [METADATA]?: queryMetadata | undefined;
            [CONFIGURATIONS]?: queryConfigurations | undefined;
            [key: string]: never;
          };
        }
      }

      export interface implementation {
        setUp?(input: callbacks.setUp.input): Promise<callbacks.setUp.output>;

        query(input: callbacks.query.input): Promise<callbacks.query.combinedOutput>;
      }
    }

    export namespace globalAlerts {
      export type levels = {
        '': {
          bankDetailsNotFound?: everest_appserver_primitive_TrueFalse | undefined | null;
          employeeRecordNotFound?: everest_appserver_primitive_TrueFalse | undefined | null;
        };
      };

      export type instanceMetadata = {
        '': {
          bankDetailsNotFound?: FieldInstanceMetadata | undefined;
          employeeRecordNotFound?: FieldInstanceMetadata | undefined;
        };
      };

      export type levelMetadata = {
        '': {
          bankDetailsNotFound?: FieldLevelMetadata | undefined;
          employeeRecordNotFound?: FieldLevelMetadata | undefined;
        };
      };

      export type levelConfigurations = {
        '': never;
      };

      export type parameters = {
        currentEmployee: {
          bankDetailsId: everest_hr_base_model_node_Employee.Employee["bankDetailsId"] | undefined;
          entityId: everest_hr_base_model_node_Employee.Employee["entityId"] | undefined;
          id: everest_hr_base_model_node_Employee.Employee["id"] | undefined;
        } | undefined;
      };

      export namespace callbacks {
        export namespace setUp {
          export type input = {
            session: ISession;
            mode: mode;
            parameters: parameters;
          };

          export type output = void;
        }

        export namespace query {
          export type queryParameters = never;

          export type queryMetadata = levelMetadata[''];

          export type queryConfigurations = levelConfigurations[''];

          export type queryData = levels[''] & {
            [METADATA]?: instanceMetadata[''] | undefined;
          };

          export type input = {
            session: ISession;
            mode: mode;
            parameters: parameters;
            queryReason: QueryReason;
            queryInput: QueryInput;
            queryInstruction: {
              fields: ReadonlySet<keyof levels['']> | undefined;
            };
          };

          export type output = queryData;

          export type combinedOutput = queryData | {
            [DATA]: queryData;
            [METADATA]?: queryMetadata | undefined;
            [CONFIGURATIONS]?: queryConfigurations | undefined;
            [key: string]: never;
          };
        }
      }

      export interface implementation {
        setUp?(input: callbacks.setUp.input): Promise<callbacks.setUp.output>;

        query(input: callbacks.query.input): Promise<callbacks.query.combinedOutput>;
      }
    }
  }

  export type implementation = {
    currentEmployee(): dataSources.currentEmployee.implementation;

    deleteExpenseItems: actions.deleteExpenseItems.implementation;

    deleteExpenseItemsDrafts: actions.deleteExpenseItemsDrafts.implementation;

    expenseItems(): dataSources.expenseItems.implementation;

    expenseItemsDrafts(): dataSources.expenseItemsDrafts.implementation;

    expenseItemsStatistics(): dataSources.expenseItemsStatistics.implementation;

    expenseReports(): dataSources.expenseReports.implementation;

    expenseReportsStatistics(): dataSources.expenseReportsStatistics.implementation;

    getSelectedExpenseItems: actions.getSelectedExpenseItems.implementation;

    globalAlerts(): dataSources.globalAlerts.implementation;

    processScannedFiles: actions.processScannedFiles.implementation;
  };
}
