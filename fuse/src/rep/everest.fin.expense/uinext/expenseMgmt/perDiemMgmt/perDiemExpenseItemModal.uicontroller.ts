import { PlainDateTime } from '@everestsystems/datetime';
import { Decimal } from '@everestsystems/decimal';
import {
  toPlainDate,
  toPlainDateTime,
} from '@pkg/everest.base/public/utils/Date/date';
import {
  getUIModelFromSharedState,
  setUIModelInSharedState,
} from '@pkg/everest.base/public/utils/ui/sharedStateUtils.ui';
import { EvstExpenseType } from '@pkg/everest.fin.base/types/enums/ExpenseType';
import { EvstPerDiemPolicyStatus } from '@pkg/everest.fin.expense/types/enums/PerDiemPolicyStatus';
import { ExpenseItemBaseUI } from '@pkg/everest.fin.expense/types/ExpenseItemBase.ui';
import { PerDiemExpensePolicyUI } from '@pkg/everest.fin.expense/types/PerDiemExpensePolicy.ui';
import type { PerDiemExpenseItemModalUiTemplate } from '@pkg/everest.fin.expense/types/uiTemplates/uinext/expenseMgmt/perDiemMgmt/perDiemExpenseItemModal.ui';

type PerDiemExpenseItemModalContext =
  PerDiemExpenseItemModalUiTemplate.PerDiemExpenseItemModalContext;

export type Context = PerDiemExpenseItemModalContext & {
  state: {
    id: number;
    mode: 'createMode' | 'editMode' | 'viewMode';
    entityId: number;
    selectedCountry: string;
    form: {
      startDate: PlainDateTime;
      endDate: PlainDateTime;
      mealsProvided: boolean;
    };
    perDiemPolicy: any;
  };
};

export function getPerDiemPolicyQuery(context) {
  if (context.state.mode !== 'createMode') {
    return {
      where: {
        id: context.data.expenseItem?.perDiemItemData?.perDiemPolicyId ?? 0,
        status: 'enabled',
      },
    };
  }

  return;
}

export function getValue(context: Context, fieldName: string) {
  const expenseItem = getUIModelFromSharedState(context, 'expenseItem');
  const { perDiemItemData = {} } = (expenseItem ?? {}) as any;

  if (
    context.state.mode === 'editMode' &&
    !context.state.perDiemPolicy &&
    context.data.perDiemPolicy?.id
  ) {
    context.state.perDiemPolicy = context.data.perDiemPolicy;
  }

  if (fieldName === 'mealsProvided') {
    return perDiemItemData[fieldName] ?? false;
  }

  if (fieldName === 'startDate' || fieldName === 'endDate') {
    return new Date(perDiemItemData[fieldName]);
  }

  if (perDiemItemData) {
    return perDiemItemData[fieldName];
  }
}
//

export async function addPerDiemExpenseItem(context: Context) {
  const { state, helpers, data } = context;
  const { startDate, endDate, mealsProvided } = state.form ?? {};
  let mealDetails = getUIModelFromSharedState(context, 'expenseItems');

  if (!startDate || !endDate || !data.expenseItem.currency) {
    helpers.showToast({
      title: 'Error',
      message: 'Please fill all the required fields',
      type: 'error',
      duration: 5,
    });
    return;
  }

  if (!state.perDiemPolicy.id) {
    helpers.showToast({
      title: 'Error',
      message:
        'A valid per diem policy is required to create a per diem expense item.',
      type: 'error',
      duration: 5,
    });
    return;
  }

  let response;

  if (
    !mealDetails ||
    !Array.isArray(mealDetails) ||
    mealDetails.length === 0 ||
    !mealsProvided
  ) {
    // Add default meal selection rows even if meals are not provided
    await addMealSelectionRows(context);
    mealDetails = getUIModelFromSharedState(context, 'expenseItems');
  }

  const perDiemItemData = {
    startDate: startDate.toString(),
    endDate: endDate.toString(),
    travelDays: mealDetails.length,
    mealsProvided,
    mealDetails,
    perDiemPolicyId: state.perDiemPolicy.id,
  };

  // Calculate total per diem amount based on German rules
  const totalPerDiemAmount = calculatePerDiemAmount(context);
  if (totalPerDiemAmount === 0) {
    helpers.showToast({
      title: 'Error',
      message: 'Total Per Diem Amount is 0. Please verify the meal details.',
      type: 'error',
    });
    return;
  }

  try {
    response = await ExpenseItemBaseUI.client.upsertExpenseItem({
      expenseItem: {
        id: data.expenseItem.id,
        amount: { amount: new Decimal(totalPerDiemAmount) },
        expenseType: EvstExpenseType.PerDiem,
        teamId: data.expenseItem.teamId,
        currency: data.expenseItem.currency,
        billDate: toPlainDate(startDate),
        description: 'Per Diem Expense',
        merchant: 'Per Diem Merchant',
        perDiemItemData,
      },
      attachments: [],
      fieldList: [
        'id',
        'expenseItemNumber',
        'merchantAndDescription',
        'receiptStatus',
        'categoryId',
        'currency',
        'amount',
        'expenseType',
        'billDate',
        'reimbursableAmount',
        'vatPercentage',
        'vatAmount',
        'vatAccountId',
        'reimbursableVATAmount',
        'fxRate',
        'businessUnitId',
        'teamId',
      ],
      options: { draft: false },
    });
  } catch (error) {
    helpers.showToast({
      title: 'Error',
      message: error.message,
      type: 'error',
      duration: 5,
    });
  }

  helpers.currentModalSubmitCallback({ perDiemExpenseItem: response });
  helpers.closeModal();
}

export function isMealsTableVisible(context: Context) {
  const { isFetchingUiModelData, state } = context;
  if (isFetchingUiModelData) {
    return false;
  }
  // TODO: Make this table always visible since we are storing meal details everytime anyways
  return (
    state.form.mealsProvided ?? getValue(context, 'mealsProvided') ?? false
  );
}

export function isAlertVisible(context: Context) {
  return context.state.perDiemPolicyNotAvailable ?? false;
}

export function openCategoriesPage({ helpers }: Context) {
  helpers.navigate({
    to: '/templates/everest.fin.expense/uinext/expenseMgmt/expenseCategories',
  });
}

export function onMealsTableLoaded(context: Context) {
  const { state, sharedState } = context;
  const expenseItem = getUIModelFromSharedState(context, 'expenseItem');
  const { perDiemItemData } = (expenseItem ?? {}) as any;
  const mealDetails =
    (
      perDiemItemData as {
        mealDetails: {
          date: string;
          day: string;
          breakFast: boolean;
          lunch: boolean;
          dinner: boolean;
          amountReimbursable: number;
        }[];
      }
    )?.mealDetails ?? [];

  if (mealDetails.length > 0 && !state.onceAdded) {
    for (const mealDetail of mealDetails) {
      const { date, day, breakFast, lunch, dinner, amountReimbursable } =
        mealDetail;

      // Add rows for each separate day
      sharedState.addNodeInstance({
        model: 'expenseItems',
        data: {
          date,
          amountReimbursable,
          day,
          breakFast,
          lunch,
          dinner,
        },
        action: 'update',
      });
    }
    state.onceAdded = true;
  }
}

export function isBlockVisible({ state }: Context, mode: string) {
  return state.mode === mode;
}

export function isPrimaryButtonVisible({ state }: Context) {
  return state.mode !== 'viewMode';
}

export function isPrimaryButtonDisabled({ state }: Context) {
  return !state.perDiemPolicy?.id;
}

export function cancelAction({ helpers }: Context) {
  helpers.closeModal();
}

export async function fetchPolicy(context: Context, country) {
  const response = await PerDiemExpensePolicyUI.client.query(
    {
      where: {
        entityId: context.state.entityId,
        status: EvstPerDiemPolicyStatus.Enabled,
        country,
      },
    },
    [
      'id',
      'entityId',
      'travelType',
      'arrivalOrDepartureDayRate',
      'breakFastDeductionRate',
      'dinnerDeductionRate',
      'fullDayRate',
      'lunchDeductionRate',
      'minAbsenceDuration',
      'partialDayRate',
      'travelRules',
      'country',
    ]
  );
  // eslint-disable-next-line unicorn/prefer-ternary
  if (response[0]?.id) {
    context.state.perDiemPolicyNotAvailable = false;
    context.state.perDiemPolicy = response[0];
  } else {
    context.state.perDiemPolicyNotAvailable = true;
    context.state.perDiemPolicy = undefined;
  }
}

export async function addMealSelectionRows(context: Context) {
  const { state, sharedState, data } = context;
  const { startDate, endDate, mealsProvided } = state.form ?? {};
  const perDiemPolicy = state.perDiemPolicy ?? data.perDiemPolicy;

  if (startDate && endDate && perDiemPolicy.id) {
    const travelDays = calculateTravelDays(
      toPlainDateTime(startDate),
      toPlainDateTime(endDate),
      perDiemPolicy.minAbsenceDuration
    );

    const expenseItems =
      sharedState.getModelData('expenseItems')?.instance || [];

    // Delete all existing items
    for (const item of expenseItems) {
      if (item.reference) {
        sharedState.removeNodeInstance({ reference: item.reference });
      }
    }

    if (travelDays > 0) {
      const startDateObj = toPlainDate(startDate);

      // Extract rates from policy
      const arrivalOrDepartureDayRate = Number(
        perDiemPolicy?.arrivalOrDepartureDayRate?.amount || 0
      );
      const fullDayRate = Number(perDiemPolicy?.fullDayRate?.amount || 0);

      // Generate a row for each day
      for (let i = 0; i < travelDays; i++) {
        const currentDate = startDateObj.plus({ days: i });
        const dayOfWeek = i + 1;

        // Format the date as ISO string (YYYY-MM-DD)
        const dateStr = currentDate.toString();

        // Determine rate based on day position
        let baseRate = fullDayRate;
        if (travelDays === 1 || i === 0 || i === travelDays - 1) {
          // Single day trip uses departure day rate
          // First day of multi-day trip uses departure day rate
          // Last day of multi-day trip uses departure day rate

          baseRate = arrivalOrDepartureDayRate;
        }
        const finalBaseRate = mealsProvided
          ? baseRate -
            baseRate *
              (Number(perDiemPolicy.breakFastDeductionRate) +
                Number(perDiemPolicy.lunchDeductionRate) +
                Number(perDiemPolicy.dinnerDeductionRate))
          : baseRate;

        const mealDetails = {
          date: dateStr,
          day: dayOfWeek,
          breakFast: mealsProvided,
          lunch: mealsProvided,
          dinner: mealsProvided,
          // Calculate initial reimbursable amount before meal deductions
          amountReimbursable: Number(finalBaseRate).toFixed(2),
        };

        // Create a new instance for this day
        sharedState.addNodeInstance({
          model: 'expenseItems',
          data: mealDetails,
          action: 'update',
        });
      }
    }
  }
}

function calculateTravelDays(
  startDate: PlainDateTime,
  endDate: PlainDateTime,
  minAbsenceDuration: number
): number {
  // Ensure startDate is before endDate using PlainDateTime.compare
  const start =
    PlainDateTime.compare(startDate, endDate) <= 0 ? startDate : endDate;
  const end =
    PlainDateTime.compare(startDate, endDate) <= 0 ? endDate : startDate;

  // Calculate total hours between dates using diff method
  // diff returns a Duration object with the difference
  const diffDuration = end.diff(start, ['hour']);
  const totalHours = diffDuration.hours;

  // If less than 8 hours, return 0 days
  if (totalHours < minAbsenceDuration) {
    return 0;
  }

  // Convert to PlainDate objects to count calendar days
  const startAsDate = start.toPlainDate();
  const endAsDate = end.toPlainDate();

  // Use diff to get days between dates
  const daysDuration = endAsDate.diff(startAsDate, ['day']);
  const dayDiff = daysDuration.days;

  // Add 1 to include both start and end dates
  return dayDiff + 1;
}

/**
 * Calculate the per diem amount based on the policy and meal details
 */
function calculatePerDiemAmount(context) {
  const perDiemPolicy = context.state.perDiemPolicy;
  const mealDetails = getUIModelFromSharedState(context, 'expenseItems');

  if (!perDiemPolicy || !mealDetails || !Array.isArray(mealDetails)) {
    return 0;
  }

  // Simply sum up the already calculated amountReimbursable values
  let totalAmount = 0;
  for (const day of mealDetails) {
    if (!Number.isNaN(day.amountReimbursable)) {
      totalAmount += Number(day.amountReimbursable);
    }
  }

  return totalAmount;
}

/**
 * Updates the reimbursable amount based on meal selections
 */
export async function updateReimbursableAmount(context: Context) {
  const { sharedState } = context;
  const expenseItems = sharedState.getModelData('expenseItems')?.instance || [];

  // Get per diem policy
  const perDiemPolicy = context.state.perDiemPolicy;

  // Extract deduction rates
  const breakfastDeduction = Number(perDiemPolicy?.breakFastDeductionRate || 0);
  const lunchDeduction = Number(perDiemPolicy?.lunchDeductionRate || 0);
  const dinnerDeduction = Number(perDiemPolicy?.dinnerDeductionRate || 0);

  const travelDays = expenseItems.length;

  // Update each row
  for (const [index, item] of expenseItems.entries()) {
    // Determine base rate based on day position
    let baseRate = Number(perDiemPolicy?.fullDayRate?.amount || 0);

    // If it's the first or last day, use arrival/departure rate
    if (travelDays === 1 || index === 0 || index === travelDays - 1) {
      baseRate = Number(perDiemPolicy?.arrivalOrDepartureDayRate?.amount || 0);
    }

    // Calculate meal deductions
    let mealDeductions = 0;
    const data = item.data || {};

    if (data.breakFast) {
      mealDeductions += baseRate * breakfastDeduction;
    }

    if (data.lunch) {
      mealDeductions += baseRate * lunchDeduction;
    }

    if (data.dinner) {
      mealDeductions += baseRate * dinnerDeduction;
    }

    // Ensure deductions don't exceed base rate
    mealDeductions = Math.min(mealDeductions, baseRate);

    // Calculate net amount (don't go negative)
    const netAmount = Number(Math.max(0, baseRate - mealDeductions)).toFixed(2);

    // Update the item's reimbursable amount
    setUIModelInSharedState(
      context,
      'expenseItems',
      'amountReimbursable',
      netAmount,
      item.reference
    );
  }
}

export function selectAllMeals(context: Context) {
  const expenseItems =
    context.sharedState.getModelData('expenseItems')?.instance || [];

  // Delete all existing items
  for (const item of expenseItems) {
    setUIModelInSharedState(
      context,
      'expenseItems',
      'breakFast',
      true,
      item.reference
    );
    setUIModelInSharedState(
      context,
      'expenseItems',
      'lunch',
      true,
      item.reference
    );
    setUIModelInSharedState(
      context,
      'expenseItems',
      'dinner',
      true,
      item.reference
    );
  }
}
export function clearAllMeals(context: Context) {
  const expenseItems =
    context.sharedState.getModelData('expenseItems')?.instance || [];

  // Delete all existing items
  for (const item of expenseItems) {
    setUIModelInSharedState(
      context,
      'expenseItems',
      'breakFast',
      false,
      item.reference
    );
    setUIModelInSharedState(
      context,
      'expenseItems',
      'lunch',
      false,
      item.reference
    );
    setUIModelInSharedState(
      context,
      'expenseItems',
      'dinner',
      false,
      item.reference
    );
  }
}
