{"version": 2, "uicontroller": ["perDiemExpenseItemModal.uicontroller.ts"], "uimodel": {"state": {"expenseItemId": 0, "mode": "createMode"}, "nodes": {"perDiemPolicy": {"type": "struct", "modelId": "everest.fin.expense/PerDiemExpensePolicyModel.PerDiemExpensePolicy", "query": "@controller:getPerDiemPolicyQuery()", "fieldList": ["id", "entityId", "travelType", "arrivalOrDepartureDayRate", "breakFastDeductionRate", "dinnerDeductionRate", "fullDayRate", "lunchDeductionRate", "minAbsenceDuration", "partialDayRate", "travelRules", "country"]}, "expenseItem": {"type": "struct", "query": {"where": {"id": "@state:expenseItemId"}}, "modelId": "everest.fin.expense/ExpenseItemBaseModel.ExpenseItemBase", "fieldList": ["id", "perDiemItemData", "currency", "teamId", "categoryId"]}, "expenseItems": {"type": "list", "modelId": "everest.fin.expense/ExpenseItemBaseModel.ExpenseItemBase", "fieldList": ["id"]}}}, "uiview": {"templateType": "details", "i18n": "employeeExpense", "tabTitle": "Create Policy", "title": "Create Policy", "config": {"stretch": true, "autoRefreshData": true}, "sections": {"content": [{"component": "<PERSON><PERSON>", "size": "12", "title": "Warning", "content": "Category for per diem expenses is not configured yet; Please contact your administrator to configre it.", "visible": "@controller:isAlertVisible()", "variant": "warning", "action": {"label": "{{expense.category.navigateToCategoriesPage}}", "onClick": "@controller:openCategoriesPage"}}, {"component": "FieldGroup", "visible": "@controller:isBlockVisible('createMode')", "section": {"editing": true, "title": "Travel Details", "grid": {"size": "12"}}, "props": {"type": "primary", "columns": "3", "elements": [{"component": "DatePicker", "label": "Start date *", "tooltip": {"text": "Start date of travel", "placement": "top"}, "hybridConfig": {"icon": "calendar", "iconPosition": "right", "variant": "secondary"}, "name": "startDate", "picker": "datetime", "onChange": "@controller:addMealSelectionRows"}, {"component": "DatePicker", "label": "End date *", "tooltip": {"text": "End date of travel", "placement": "top"}, "hybridConfig": {"icon": "calendar", "iconPosition": "right", "variant": "secondary"}, "name": "endDate", "picker": "datetime", "onChange": "@controller:addMealSelectionRows"}, {"value": "@binding:perDiemPolicy.country", "onChange": "@controller:fetchPolicy"}, {"component": "Select", "label": "<PERSON><PERSON><PERSON><PERSON>", "value": "@binding:expenseItem.currency", "initialValue": "@state:entityCurrency"}, {"value": "@binding:expenseItem.teamId"}, {"component": "Select", "label": "Meals provided *", "idProp": "id", "textProp": "text", "list": [{"id": true, "text": "Yes"}, {"id": false, "text": "No"}], "initialValue": false, "name": "mealsProvided", "onChange": "@controller:addMealSelectionRows"}]}}, {"component": "FieldGroup", "visible": "@controller:isBlockVisible('viewMode')", "section": {"editing": false, "title": "Travel Details", "grid": {"size": "12"}}, "props": {"type": "primary", "columns": "3", "elements": [{"component": "DatePicker", "label": "Start date", "tooltip": {"text": "Start date of travel", "placement": "top"}, "hybridConfig": {"icon": "calendar", "iconPosition": "right", "variant": "secondary"}, "name": "startDate", "picker": "datetime", "value": "@controller:getValue('startDate')"}, {"component": "DatePicker", "label": "End date", "tooltip": {"text": "End date of travel", "placement": "top"}, "hybridConfig": {"icon": "calendar", "iconPosition": "right", "variant": "secondary"}, "name": "endDate", "picker": "datetime", "value": "@controller:getValue('endDate')"}, {"value": "@binding:perDiemPolicy.country"}, {"component": "Select", "label": "<PERSON><PERSON><PERSON><PERSON>", "value": "@binding:expenseItem.currency", "initialValue": "@state:entityCurrency", "isDisabled": true}, {"value": "@binding:expenseItem.teamId"}, {"component": "Select", "label": "Meals provided", "idProp": "id", "textProp": "text", "list": [{"id": true, "text": "Yes"}, {"id": false, "text": "No"}], "value": "@controller:get<PERSON><PERSON><PERSON>('mealsProvided')", "name": "mealsProvided"}]}}, {"component": "FieldGroup", "visible": "@controller:isBlockVisible('editMode')", "section": {"editing": true, "title": "Travel Details", "grid": {"size": "12"}}, "props": {"type": "primary", "columns": "3", "elements": [{"component": "DatePicker", "label": "Start date", "tooltip": {"text": "Start date of travel", "placement": "top"}, "hybridConfig": {"icon": "calendar", "iconPosition": "right", "variant": "secondary"}, "name": "startDate", "picker": "datetime", "value": "@controller:getValue('startDate')", "onChange": "@controller:addMealSelectionRows"}, {"component": "DatePicker", "label": "End date", "tooltip": {"text": "End date of travel", "placement": "top"}, "hybridConfig": {"icon": "calendar", "iconPosition": "right", "variant": "secondary"}, "name": "endDate", "picker": "datetime", "value": "@controller:getValue('endDate')", "onChange": "@controller:addMealSelectionRows"}, {"value": "@binding:perDiemPolicy.country", "onChange": "@controller:fetchPolicy"}, {"component": "Select", "label": "<PERSON><PERSON><PERSON><PERSON>", "value": "@binding:expenseItem.currency", "initialValue": "@state:entityCurrency", "isDisabled": true}, {"value": "@binding:expenseItem.teamId"}, {"component": "Select", "label": "Meals provided", "idProp": "id", "textProp": "text", "list": [{"id": true, "text": "Yes"}, {"id": false, "text": "No"}], "value": "@controller:get<PERSON><PERSON><PERSON>('mealsProvided')", "allowClear": false, "name": "mealsProvided"}]}}, {"component": "Table", "customId": "mealsTable", "visible": "@controller:isMealsTableVisible()", "section": {"editing": true, "title": "Meals Selection", "grid": {"size": "12"}, "actions": [{"label": "Select All", "onClick": "@controller:selectAllMeals", "visible": "@controller:isPrimaryButtonVisible()"}, {"label": "Clear All", "onClick": "@controller:clearAllMeals", "visible": "@controller:isPrimaryButtonVisible()"}]}, "props": {"footerData": [{"amountReimbursable": 0}], "onTableLoaded": "@controller:onMealsTableLoaded", "addRowsOnEmpty": false, "addRows": false, "suppressDelete": true, "createAction": "update", "hideFilters": true, "data": "@binding:expenseItems", "columns": [{"headerName": "Date", "field": "date", "fieldProps": {"isEditing": false}}, {"headerName": "Amount reimbursable", "field": "amountReimbursable", "fieldProps": {"isEditing": false}}, {"headerName": "Breakfast", "field": "breakFast", "fieldProps": {"component": "Checkbox", "isEditing": "@controller:isPrimaryButtonVisible()", "onChange": "@controller:updateReimbursableAmount"}}, {"headerName": "Lunch", "field": "lunch", "fieldProps": {"component": "Checkbox", "isEditing": "@controller:isPrimaryButtonVisible()", "onChange": "@controller:updateReimbursableAmount"}}, {"headerName": "Dinner", "field": "dinner", "fieldProps": {"component": "Checkbox", "isEditing": "@controller:isPrimaryButtonVisible()", "onChange": "@controller:updateReimbursableAmount"}}]}}]}, "actions": {"content": [{"variant": "secondary", "label": "Cancel", "onClick": "@controller:cancelAction"}, {"variant": "primary", "label": "Save", "onClick": "@controller:addPerDiemExpenseItem", "visible": "@controller:isPrimaryButtonVisible()", "disabled": "@controller:isPrimaryButtonDisabled()"}]}}}