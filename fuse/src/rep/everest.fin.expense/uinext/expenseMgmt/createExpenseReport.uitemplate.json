{"version": 2, "uicontroller": ["createExpenseReport.uicontroller.ts"], "uimodel": {"state": {"mode": "view", "isEmployeeSelectionTriggered": false, "employeeBankDetailsId": null, "featureToggles": {"transaction": true}}, "nodes": {"employees": {"type": "list", "modelId": "everest.hr.base/EmployeeModel.Employee", "loadCurrentActiveEmployees": {"args": {"orderBy": ["name"]}, "fieldList": ["employeeNumber", "name", "email", "entityId", "expenseApprover", "id", "iban", "status", "bankAccountHolderName", "bankDetailsId"]}}, "entities": {"modelId": "everest.base/EntityModel.Entity", "type": "list", "queryActiveEntities": {"query": {}, "fieldList": ["id", "entityName", "currency"]}}, "journalEntryHeader": {"type": "list", "query": "@controller:getJournalEntryHeaderRequest()", "modelId": "everest.fin.accounting/JournalEntryHeaderModel.JournalEntryHeader", "fieldList": ["id", "journalEntryNumber"]}, "postingPeriod": {"type": "struct", "modelId": "everest.fin.accounting/AccountingPeriodModel.AccountingPeriod", "findAccountingPeriod": "@controller:getPeriodQuery()", "fieldList": ["id", "closed", "locked"]}, "exchangeRates": {"type": "struct", "modelId": "everest.base/ExchangeRateModel.ExchangeRate"}, "expenseReport": {"modelId": "everest.fin.expense/ExpenseReportBaseModel.ExpenseReportBase", "type": "struct", "query": {"where": "@controller:getCondition()"}, "options": {"draft": "include"}, "fieldList": ["id", "expenseNumber", "status", "entityId", "entityName", "amountPaid", "ExpenseReportBase-Employee.name", "submittedDate", "employeeId", "employeeName", "paymentStatus", "reimbursableTotal", "reimbursementCurrency", "openAmount", "pendingAmount", "reviewMessage", "paymentStatus", "externalUrl", "origin", "postingPeriodId"]}, "currentEmployee": {"type": "struct", "modelId": "everest.hr.base/EmployeeModel.Employee", "getCurrentEmployee": {"fieldList": ["id", "name", "bankDetailsId", "entityId"]}}, "expenseReportBankDetails": {"type": "struct", "modelId": "everest.fin.expense/ExpenseReportBankDetailModel.ExpenseReportBankDetail", "query": "@controller:getExpenseReportBankDetailsQuery()", "fieldList": ["id", "expenseReportHeaderId", "bankAccountNumberType"]}, "expenseItems": {"modelId": "everest.fin.expense/ExpenseItemBaseModel.ExpenseItemBase", "type": "list", "query": "@controller:getExpenseItemsTableQuery()", "options": {"draft": "include"}, "fieldList": ["id", "accountId", "billDate", "categoryId", "currency", "description", "expenseId", "expenseItemNumber", "status", "receiptStatus", "merchantAndDescription", "expenseType", "merchant", "reimbursable", "reimbursableAmount", "vatPercentage", "vatAmount", "vatAccountId", "billableExpense", "customerId", "reimbursableVATAmount", "amount", "fxRate", "employeeId", "ExpenseItemBase-ExpenseReportBase.paymentStatus", "businessUnitId", "teamId", "approvedAmount"]}, "expenseCategories": {"type": "list", "modelId": "everest.fin.expense/ExpenseCategoryModel.ExpenseCategory", "query": {"where": {"status": "active"}}}, "businessUnits": {"type": "list", "modelId": "everest.base/BusinessUnitModel.BusinessUnit", "query": {"where": {"active": true}}}, "teams": {"type": "list", "modelId": "everest.hr.base/TeamModel.Team", "query": {"where": {"status": "active"}}}, "outboundPaymentLines": {"type": "list", "modelId": "everest.fin.expense/OutboundPaymentItemBaseModel.OutboundPaymentItemBase", "query": "@controller:getQ<PERSON>y()", "pagination": true}, "expenseLineToReview": {"type": "list", "query": {"where": {"expenseId": "@controller:getExpenseId()"}}, "modelId": "everest.fin.expense/ExpenseItemBaseModel.ExpenseItemBase", "fieldList": ["id", "billDate", "categoryId", "accountId", "ExpenseItemBase-ExpenseCategory.category", "currency", "description", "expenseId", "expenseItemNumber", "status", "receiptStatus", "merchantAndDescription", "expenseType", "merchant", "reimbursable", "reimbursableAmount", "vatPercentage", "vatAmount", "vatAccountId", "reimbursableVATAmount", "amount", "fxRate", "employeeId", "ExpenseItemBase-ExpenseReportBase.paymentStatus", "businessUnitId", "teamId", "approvedAmount"]}, "expenseBO": {"type": "struct", "modelId": "everest.fin.expense/BOComposition/ExpenseReportBusinessObjectModel.ExpenseReportBusinessObject", "query": "@controller:getExpenseBOQuery()", "fieldList": ["id"]}, "bankDetails": {"type": "struct", "modelId": "everest.fin.base/BankDetailsModel.BankDetails", "query": "@controller:getBankDetailsQuery()", "fieldList": ["id", "bankAccountNumberType", "bankAccountHolderName", "bankAccountNumber", "financialInstitutionIdType", "financialInstitutionId"]}, "isExpenseAdmin": {"type": "struct", "modelId": "everest.appserver/permission/PolicyModel.Policy", "checkPermission": {"resource": "everest.fin.expense/ExpenseReportBaseModel.ExpenseReportBase", "action": "query", "checkConditions": true}}, "businessUnitEnabled": {"modelId": "everest.appserver/ConfigurationModel.Configuration", "type": "struct", "getValueOrDefault": {"name": "BUSINESS_UNIT_ENABLED", "packageName": "everest.base", "defaultValue": true}, "fieldList": ["value"]}, "isPostingPeriodOpen": {"type": "struct", "modelId": "everest.fin.expense/ExpenseReportBaseModel.ExpenseReportBase", "isExpenseReportPostingPeriodOpen": "@controller:getPostingPeriodQuery()", "fieldList": []}, "approvalPolicyHeader": {"type": "struct", "getDocumentApprovalStatus": "@controller:getExpenseReportApprovalParams()", "modelId": "everest.base.approvals/ApprovalPolicyHeaderModel.ApprovalPolicyHeader", "fieldList": []}}}, "uiview": {"templateType": "generic", "i18n": "employeeExpense", "title": "@controller:getTabTitle()", "config": {"stretch": true, "autoRefreshData": "@controller:isAutoRefreshDataEnabled()", "allowRefreshData": "@controller:isAutoRefreshDataEnabled()", "grid": {"limited": true}}, "help": {"visible": "@controller:isEReviewBlockVisible()", "text": "Help", "video": "https://drive.google.com/file/d/1j4ssrs-PHEhUXzsOm8SGl9LHEW2IIWeJ/preview"}, "header": {"content": {"title": "@controller:getTitle()", "status": "@controller:getStatus()", "subtitle": "@controller:getSubTitle()"}}, "sections": {"content": [{"component": "<PERSON><PERSON>", "visible": "@controller:isAlertVisible()", "section": {"title": "{{employeeExpense.reasonForRejection}}", "grid": {"size": "12"}}, "props": {"variant": "warning", "content": "@binding:expenseReport.reviewMessage"}}, {"component": "<PERSON><PERSON>", "visible": "@controller:isBankDetailsNotPresentAlertVisible()", "section": {"grid": {"size": "12"}}, "props": {"variant": "warning", "content": "@controller:getBankAccountDetailsNotPresentMessage()", "action": {"label": "{{employeeExpense.alert.clickHereToConfigure}}", "onClick": "@controller:navigateToEmployeeBankDetails"}}}, {"component": "FieldGroup", "customId": "expenseReportBaseFieldGroup", "visible": "@controller:isERBlockVisible()", "section": {"editing": "@controller:isERBlockEditable()", "grid": {"size": "9"}}, "props": {"type": "primary", "columns": "3", "elements": [{"component": "Select", "label": "{{employeeExpense.employee}}", "idProp": "id", "textProp": "name", "value": "@bindingController(expenseReport.employeeId, getEmployeeId())", "text": "@controller:getEmployeeName()", "list": "@binding:employees", "rules": "@controller:validateEmployee()", "action": "@controller:getAction()", "onChange": "@controller:onEmployeeChange", "link": {"to": "@uicode:employee", "qs": {"id": "@binding:expenseReport.employeeId"}}}, {"component": "Select", "label": "{{employeeExpense.entity}}", "idProp": "id", "textProp": "entityName", "value": "@bindingController(expenseReport.entityId, getEntityId())", "text": "@controller:getEntityName()", "list": "@binding:entities", "rules": "@controller:validateEntity()", "action": "@controller:getAction()", "onChange": "@controller:onEntityChange"}, {"label": "{{employeeExpense.currency}}", "value": "@bindingController(expenseReport.reimbursementCurrency, getReimbursementCurrency())", "action": "@controller:getAction()", "rules": "@controller:validateCurrency()", "isDisabled": true}, {"component": "DatePicker", "label": "{{employeeExpense.date}}", "parseAs": "date", "format": "P", "value": "@bindingController(expenseReport.submittedDate, getDate())", "action": "@controller:getAction()", "onChange": "@controller:onDateChange"}, {"label": "{{employeeExpense.journalEntry}}", "value": "@controller:getJEHeaderNumber()", "link": {"to": "@uicode:journalEntry", "qs": {"id": "@controller:getJELinkId()"}}, "isEditing": false, "isVisible": "@controller:isJELinkVisible()"}, {"label": "{{employeeExpense.source}}", "value": "@binding:expenseReport.origin", "externalLink": "@controller:getUrllink()", "isVisible": "@controller:isSourceVisible()"}]}}, {"component": "FieldGroup", "customId": "expenseReportBaseFieldGroupInReviewMode", "visible": "@controller:isEReviewBlockVisible()", "section": {"editing": false, "grid": {"size": "9"}}, "props": {"columns": "3", "elements": [{"component": "HiddenInput", "label": "id", "value": "@binding:expenseReport.id", "action": "@controller:getAction()"}, {"component": "Read<PERSON>nly", "label": "{{employeeExpense.employee}}", "value": "@controller:getEmployeeName()", "action": "@controller:getAction()", "link": {"to": "@uicode:employee", "qs": {"id": "@binding:expenseReport.employeeId"}}}, {"component": "Read<PERSON>nly", "label": "{{employeeExpense.entity}}", "value": "@binding:expenseReport.entityName", "action": "@controller:getAction()"}, {"component": "Read<PERSON>nly", "label": "{{employeeExpense.currency}}", "value": "@binding:expenseReport.reimbursementCurrency", "action": "@controller:getAction()"}, {"component": "Read<PERSON>nly", "label": "{{employeeExpense.date}}", "parseAs": "date", "value": "@binding:expenseReport.submittedDate", "action": "@controller:getAction()"}, {"label": "{{employeeExpense.journalEntry}}", "value": "@controller:getJEHeaderNumber()", "link": {"to": "@uicode:journalEntry", "qs": {"id": "@controller:getJELinkId()"}}, "isVisible": "@controller:isJELinkVisible()"}, {"label": "{{employeeExpense.source}}", "value": "@binding:expenseReport.origin", "externalLink": "@binding:expenseReport.externalUrl", "isVisible": "@controller:isSourceVisible()"}]}}, {"component": "SummaryText", "visible": "@controller:isSummaryBlockVisible('first')", "section": {"grid": {"size": "3"}}, "props": {"fields": [{"label": "{{employeeExpense.totalAmount}}", "value": "@bindingController:(expenseReport.reimbursableTotal, getTotal())", "action": "@controller:getAction()"}]}}, {"component": "SummaryText", "visible": "@controller:isSummaryBlockVisible('second')", "section": {"grid": {"size": "3"}}, "props": {"summaryBelow": true, "summary": {"label": "{{employeeExpense.openAmount}}", "value": "@binding:expenseReport.openAmount", "action": "@controller:getAction()"}, "fields": [{"label": "{{employeeExpense.totalAmount}}", "value": "@bindingController:(expenseReport.reimbursableTotal, getTotal())", "action": "@controller:getAction()"}, {"label": "{{employeeExpense.amount.paid}}", "value": "@bindingController:(expenseReport.amountPaid, getAmountPaid())", "action": "@controller:getAction()"}, {"label": "{{employeeExpense.paymentPending}}", "value": "@bindingController:(expenseReport.pendingAmount, getPendingPaymentAmount())", "isVisible": "@controller:isPendingAmountVisible()", "action": "@controller:getAction()"}]}}, {"component": "Table", "customId": "expenseItemsTable", "visible": "@controller:isERBlockVisible()", "section": {"editing": false, "grid": {"size": "12"}, "actions": [{"variant": "secondary", "label": "{{employeeExpense.add.expenseItem}}", "onClick": "@controller:addExpenseItemModal", "visible": "@controller:displayExtraAction()", "disabled": "@controller:isExtraActionDisabled()"}, {"variant": "secondary", "label": "Add Per Diem Expense Item", "onClick": "@controller:openAddPerDiemExpenseItemModal", "visible": "@controller:displayPerDiemActionButton()", "disabled": "@controller:isAddPerDiemActionDisabled()", "tooltip": {"text": "Make sure per diem category is configured", "placement": "top", "visible": "@controller:isAddPerDiemActionDisabled()"}}]}, "props": {"addRowsOnEmpty": false, "addRows": false, "showRowCount": "@controller:isStateModeView()", "suppressDelete": true, "createAction": "update", "hideFilters": true, "columns": [{"headerName": "{{employeeExpense.number}}", "field": "expenseItemNumber", "onCellClicked": "@controller:editExpenseItem", "cellLinkEmphasized": true, "fieldProps": {"isEditing": false, "sortable": false}}, {"headerName": "{{employeeExpense.date}}", "field": "billDate", "fieldProps": {"isEditing": false, "sortable": false}}, {"headerName": "{{employeeExpense.merchantOrdescription}}", "field": "merchantAndDescription", "fieldProps": {"isEditing": false, "sortable": false}}, {"headerName": "{{employeeExpense.category}}", "field": "categoryId", "valueGetter": "@controller:getCategoryName", "fieldProps": {"isEditing": false, "sortable": false}, "initialHide": true}, {"headerName": "{{employeeExpense.businessUnit}}", "field": "businessUnitId", "valueGetter": "@controller:getBussinessUnitId", "fieldProps": {"isEditing": false}, "visible": "@binding:businessUnitEnabled"}, {"headerName": "{{employeeExpense.team}}", "field": "team", "valueGetter": "@controller:getTeamId", "fieldProps": {"isEditing": false}}, {"headerName": "{{employeeExpense.account}}", "field": "accountId", "fieldProps": {"isEditing": false, "sortable": false}, "initialHide": true}, {"headerName": "{{employeeExpense.receipt}}", "field": "receiptStatus", "fieldProps": {"isEditing": false, "sortable": false}}, {"headerName": "{{employeeExpense.currency}}", "field": "currency", "initialHide": true, "fieldProps": {"isEditing": false, "sortable": false}}, {"headerName": "{{employeeExpense.amount}}", "field": "amount", "initialHide": true, "fieldProps": {"isEditing": false, "sortable": false}}, {"headerName": "{{employeeExpense.approvedAmount}}", "field": "approvedAmount", "initialHide": true, "fieldProps": {"isEditing": false, "sortable": false}}, {"field": "vatPercentage", "initialHide": true, "fieldProps": {"isEditing": false, "sortable": false}}, {"field": "vatAmount", "initialHide": true, "fieldProps": {"isEditing": false, "sortable": false}}, {"headerName": "{{employeeExpense.exchangeRate}}", "field": "fxRate", "initialHide": true, "fieldProps": {"isEditing": false, "precision": 6, "fixDecimalsBasedOnPrecision": true, "sortable": false}}, {"headerName": "{{employeeExpense.reimbursement.amount}}", "field": "reimbursableAmount", "fieldProps": {"isEditing": false, "sortable": false}}], "rowActions": "@controller:getRowActions()", "data": "@binding:expenseItems"}}, {"component": "Table", "customId": "expenseItemsTableInCreateView", "visible": "@controller:isEReviewBlockVisible()", "section": {"editing": false, "title": "@controller:getSecondaryTableTitle()", "grid": {"size": "12"}}, "props": {"addRowsOnEmpty": false, "addRows": false, "suppressDelete": true, "showRowCount": true, "columns": [{"headerName": "{{employeeExpense.number}}", "field": "expenseItemNumber", "onCellClicked": "@controller:openExpenseItemModal", "cellLinkEmphasized": true, "fieldProps": {"isEditing": false}}, {"headerName": "{{employeeExpense.date}}", "field": "billDate", "fieldProps": {"isEditing": false}}, {"headerName": "{{employeeExpense.merchantOrdescription}}", "field": "merchantAndDescription", "fieldProps": {"isEditing": false}}, {"headerName": "{{employeeExpense.category}}", "field": "ExpenseItemBase-ExpenseCategory.category", "fieldProps": {"isEditing": false}, "initialHide": true}, {"headerName": "{{employeeExpense.businessUnit}}", "field": "businessUnitId", "fieldProps": {"isEditing": false}, "visible": "@binding:businessUnitEnabled"}, {"headerName": "{{employeeExpense.team}}", "field": "teamId", "fieldProps": {"isEditing": false}}, {"headerName": "{{employeeExpense.account}}", "field": "accountId", "fieldProps": {"isEditing": false, "sortable": false}, "initialHide": true}, {"headerName": "{{employeeExpense.receipt}}", "field": "receiptStatus", "fieldProps": {"isEditing": false}}, {"headerName": "{{employeeExpense.currency}}", "field": "currency", "initialHide": true, "fieldProps": {"isEditing": false}}, {"headerName": "{{employeeExpense.amount}}", "field": "amount", "initialHide": true, "fieldProps": {"isEditing": false}}, {"headerName": "{{employeeExpense.approvedAmount}}", "field": "approvedAmount", "initialHide": true, "fieldProps": {"isEditing": false}}, {"field": "vatPercentage", "initialHide": true, "fieldProps": {"isEditing": false}}, {"field": "vatAmount", "initialHide": true, "fieldProps": {"isEditing": false}}, {"headerName": "{{employeeExpense.exchangeRate}}", "field": "fxRate", "initialHide": true, "fieldProps": {"isEditing": false, "precision": 6, "fixDecimalsBasedOnPrecision": true}}, {"headerName": "{{employeeExpense.reimbursement.amount}}", "valueGetter": "@controller:getReimbursableAmount", "field": "reimbursableAmount", "fieldProps": {"isEditing": false}}], "data": "@binding:expenseLineToReview"}}, {"component": "Table", "visible": "@controller:isPaymentsTableVisible()", "section": {"title": "{{employeeExpense.payments}}", "grid": {"size": "9"}}, "props": {"variant": "white-borderless", "pagination": true, "fullWidth": false, "columns": [{"headerName": "ID", "field": "outboundPaymentHeaderNumber", "sortable": true, "fieldProps": {"fontWeight": "bold"}, "onCellClicked": "@controller:navigateToPayment"}, {"headerName": "{{employeeExpense.paymentType}}", "field": "paymentType"}, {"headerName": "{{employeeExpense.paymentDate}}", "field": "paymentDate"}, {"headerName": "{{employeeExpense.status}}", "field": "status", "cellVariant": {"variant": "badge", "matchers": {"Manually Recorded": "success", "Partially successful": "mystic-grey", "Successful": "success", "Unsigned": "mystic-grey", "Processing": "mystic-grey", "Cancelled": "danger", "Unknown": "info", "Rejected": "danger", "default": "error"}}}, {"headerName": "{{employeeExpense.currency}}", "field": "paymentCurrency"}, {"headerName": "{{employeeExpense.amount}}", "field": "paymentAmount"}], "data": "@binding:outboundPaymentLines"}}]}, "actions": {"direction": "vertical", "content": [{"variant": "primary", "label": "@controller:getFirstButtonLabel()", "visible": "@controller:isFirstButtonVisible()", "onClick": "@controller:firstButtonAction"}, {"variant": "secondary", "label": "{{employeeExpense.more.actions}}", "visible": "@controller:isStateModeView()", "actions": [{"label": "{{employeeExpense.edit}}", "disabled": "@controller:isSecondaryActionsDisabled()", "visible": "@controller:isPostingPeriodOpen()", "onClick": "@controller:goToEditMode"}, {"label": "{{employeeExpense.cancelSubmission}}", "onClick": "@controller:cancelSubmission", "visible": "@controller:isPostingPeriodOpen()", "disabled": "@controller:isSecondaryActionsDisabled()", "confirmation": {"message": "{{employeeExpense.cancelSubmission.revertMessage}}", "description": "@controller:getCancelSubmissionDescription()", "cancelLabel": "{{employeeExpense.cancel}}", "confirmLabel": "{{employeeExpense.cancelSubmission}}"}}, {"label": "{{employeeExpense.viewApprovers}}", "disabled": false, "onClick": "@controller:viewApprovers"}, {"@uifragment": {"path": "everest.base.process/uinext/processWidget.uifragment.json", "props": {"boModelId": "everest.fin.expense/BOComposition/ExpenseReportBusinessObjectModel.ExpenseReportBusinessObject", "boInstanceIdPathInData": "expenseBO.id"}}}]}, {"variant": "secondary", "label": "{{employeeExpense.cancel}}", "visible": "@controller:isCancelButtonVisible()", "onClick": "@controller:cancelButtonAction"}, {"variant": "primary", "label": "@controller:getPrimaryButtonLabel()", "visible": "@controller:isEReviewBlockVisible()", "disabled": "@controller:isPrimaryButtonDisabled()", "onClick": "@controller:primaryButtonAction", "actions": "@controller:getPrimaryButtonMoreActions()"}, {"variant": "secondary", "label": "{{employeeExpense.more.actions}}", "visible": "@controller:isEReviewBlockVisible()", "actions": [{"label": "{{employeeExpense.edit}}", "disabled": "@controller:isEditDisabled()", "onClick": "@controller:goToEditMode"}, {"label": "{{employeeExpense.cancelSubmission}}", "onClick": "@controller:cancelSubmission", "visible": "@controller:isPostingPeriodOpen()", "disabled": "@controller:isCancelSubmissionButtonDisabled()", "confirmation": {"message": "{{employeeExpense.cancelSubmission.revertMessage}}", "description": "@controller:getCancelSubmissionDescription()", "cancelLabel": "{{employeeExpense.cancel}}", "confirmLabel": "{{employeeExpense.cancelSubmission}}"}}, {"label": "{{employeeExpense.reject}}", "disabled": "@controller:isRejectButtonDisabled()", "visible": "@controller:isRejectButtonVisible()", "onClick": "@controller:rejectAction"}, {"label": "{{employeeExpense.viewApprovers}}", "disabled": false, "onClick": "@controller:viewApprovers"}, {"@uifragment": {"path": "everest.base.process/uinext/processWidget.uifragment.json", "props": {"boModelId": "everest.fin.expense/BOComposition/ExpenseReportBusinessObjectModel.ExpenseReportBusinessObject", "boInstanceIdPathInData": "expenseBO.id"}}}]}]}}}