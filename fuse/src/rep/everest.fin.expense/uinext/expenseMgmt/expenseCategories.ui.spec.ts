import { getMockT } from '@pkg/everest.base/public/ui-types/mocks.ui';

import type { Context } from './expenseCategories.uicontroller';
import * as ExpenseCategoriesController from './expenseCategories.uicontroller';
import { EvstExpenseType } from '@pkg/everest.fin.base/types/enums/ExpenseType';

describe('expenseCategories uicontroller tests', () => {
  beforeEach(() => {
    jest.resetAllMocks();
  });

  describe('createRegularCategory', () => {
    it('should open the modal', () => {
      const context = getMockT<Context>({});

      const mockOpenModal = jest.fn();
      context.helpers.openModal = mockOpenModal;

      ExpenseCategoriesController.createRegularCategory(context);

      expect(mockOpenModal).toHaveBeenCalledTimes(1);
      expect(mockOpenModal).toHaveBeenCalledWith({
        size: 'small',
        title: '{{expense.createCategory}}',
        template:
          '/templates/everest.fin.expense/uinext/expenseMgmt/createExpenseCategory',
        initialState: {
          featureToggles: { delta: true },
          type: EvstExpenseType.Pur,
          mode: 'create',
        },
        onModalSubmit: context.actions.refetchUiModelData,
      });
    });
  });

  describe('createDistanceCategory', () => {
    it('should open the modal', () => {
      const context = getMockT<Context>({});

      const mockOpenModal = jest.fn();
      context.helpers.openModal = mockOpenModal;

      ExpenseCategoriesController.createDistanceCategory(context);

      expect(mockOpenModal).toHaveBeenCalledTimes(1);
      expect(mockOpenModal).toHaveBeenCalledWith({
        size: 'small',
        title: '{{expense.createDistanceCategory}}',
        template:
          '/templates/everest.fin.expense/uinext/expenseMgmt/createExpenseCategory',
        initialState: {
          featureToggles: { delta: true },
          type: EvstExpenseType.Dis,
          mode: 'create',
        },
        onModalSubmit: context.actions.refetchUiModelData,
      });
    });
  });

  describe('isDistanceButtonDisabled', () => {
    it('should return false when alert is visible', () => {
      const context = getMockT<Context>({});

      const mockIsAlertVisible = jest
        .spyOn(ExpenseCategoriesController, 'isAlertVisible')
        .mockReturnValue(true);

      const result =
        ExpenseCategoriesController.isDistanceButtonDisabled(context);

      expect(mockIsAlertVisible).toHaveBeenCalledTimes(1);
      expect(result).toBe(false);
      mockIsAlertVisible.mockRestore();
    });

    it('should return true when alert is not visible', () => {
      const context = getMockT<Context>({});

      const mockIsAlertVisible = jest
        .spyOn(ExpenseCategoriesController, 'isAlertVisible')
        .mockReturnValue(false);

      const result =
        ExpenseCategoriesController.isDistanceButtonDisabled(context);

      expect(mockIsAlertVisible).toHaveBeenCalledTimes(1);
      expect(result).toBe(true);
      mockIsAlertVisible.mockRestore();
    });
  });

  describe('isAlertVisible', () => {
    it('should return false we have one distance category', () => {
      const context = getMockT<Context>({
        data: {
          distanceCategory: [1],
        },
      });

      const result = ExpenseCategoriesController.isAlertVisible(context);

      expect(result).toBe(false);
    });

    it('should return true we have more than one distance category', () => {
      const context = getMockT<Context>({
        data: {
          distanceCategory: [1, 2],
        },
      });

      const result = ExpenseCategoriesController.isAlertVisible(context);

      expect(result).toBe(true);
    });
  });

  describe('isDataLoaded', () => {
    it('should return true when coa is not empty', () => {
      const context = getMockT<Context>({
        data: {
          chartOfAccounts: [1, 2],
        },
      });

      const result = ExpenseCategoriesController.isDataLoaded(context);

      expect(result).toBe(true);
    });

    it('should return false when coa is empty', () => {
      const context = getMockT<Context>({
        data: {
          chartOfAccounts: [],
        },
      });

      const result = ExpenseCategoriesController.isDataLoaded(context);

      expect(result).toBe(false);
    });
  });

  describe('getColumns', () => {
    it('should return columns based on coa', () => {
      const context = getMockT<Context>({
        data: {
          chartOfAccounts: [
            { id: 1, name: 'col1' },
            { id: 2, name: 'col2' },
          ],
        },
      });

      const result = ExpenseCategoriesController.getColumns(context);

      expect(result).toStrictEqual([
        {
          headerName: '{{expense.categoryName}}',
          field: 'categoryName',
          cellVariant: {},
        },
        {
          headerName: 'col1',
          field: `1Account`,
          cellVariant: {},
        },
        {
          headerName: 'col2',
          field: `2Account`,
          cellVariant: {},
        },
        {
          headerName: '{{expense.status}}',
          field: 'status',
          cellVariant: {
            variant: 'badge',
            matchers: { Active: 'success', Inactive: 'mystic-grey' },
          },
        },
      ]);
    });
  });

  describe('rowClickAction', () => {
    it('should open the modal', () => {
      const context = getMockT<Context>({});

      const mockOpenModal = jest.fn();
      context.helpers.openModal = mockOpenModal;

      ExpenseCategoriesController.rowClickAction(context, {
        status: 'rowStatus',
        categoryId: 'rowCategoryId',
        _nodeReference: '',
        _parentReference: '',
      });

      expect(mockOpenModal).toHaveBeenCalledTimes(1);
      expect(mockOpenModal).toHaveBeenCalledWith({
        size: 'small',
        template:
          '/templates/everest.fin.expense/uinext/expenseMgmt/createExpenseCategory',
        initialState: {
          featureToggles: { delta: true },
          mode: 'view',
          status: 'rowStatus',
          categoryId: 'rowCategoryId',
        },
        onModalSubmit: context.actions.refetchUiModelData,
        onClose: context.actions.refetchUiModelData,
      });
    });
  });

  describe('fillSampleData', () => {
    it('should run fill sample data action and refetch ui model data', async () => {
      const context = getMockT<Context>({});

      const mockActionRun = jest.fn().mockReturnValue({});
      context.actions.run = mockActionRun;

      const mockRefreshUiModelData = jest.fn();
      context.actions.refetchUiModelData = mockRefreshUiModelData;

      const mockShowToast = jest.fn();
      context.helpers.showToast = mockShowToast;

      await ExpenseCategoriesController.fillSampleData(context);

      expect(mockActionRun).toHaveBeenCalledTimes(1);
      expect(mockActionRun).toHaveBeenCalledWith({
        expenseCategories: {
          action: 'fillCategorySampleData',
          data: {},
        },
      });

      expect(mockRefreshUiModelData).toHaveBeenCalledTimes(1);
      expect(mockShowToast).toHaveBeenCalledTimes(1);
      expect(mockShowToast).toHaveBeenCalledWith({
        title: '{{expense.sampleData.success}}',
        type: 'success',
      });
    });

    it('should run fill sample data action and do not refetch ui model data on error', async () => {
      const context = getMockT<Context>({});

      const mockActionRun = jest.fn().mockReturnValue({ error: 1 });
      context.actions.run = mockActionRun;

      const mockRefreshUiModelData = jest.fn();
      context.actions.refetchUiModelData = mockRefreshUiModelData;

      const mockShowToast = jest.fn();
      context.helpers.showToast = mockShowToast;

      await ExpenseCategoriesController.fillSampleData(context);

      expect(mockActionRun).toHaveBeenCalledTimes(1);
      expect(mockActionRun).toHaveBeenCalledWith({
        expenseCategories: {
          action: 'fillCategorySampleData',
          data: {},
        },
      });

      expect(mockRefreshUiModelData).toHaveBeenCalledTimes(0);
      expect(mockShowToast).toHaveBeenCalledTimes(0);
    });
  });
  describe('_getIsDeleteDisabled', () => {
    it('should return inverse of isDeleteButtonEnabled', () => {
      expect(
        ExpenseCategoriesController._getIsDeleteDisabled(
          getMockT<Context>({
            state: { isDeleteButtonEnabled: true },
          })
        )
      ).toBe(false);
      expect(
        ExpenseCategoriesController._getIsDeleteDisabled(
          getMockT<Context>({
            state: { isDeleteButtonEnabled: false },
          })
        )
      ).toBe(true);
    });
  });

  describe('_deleteSelectedValues', () => {
    it('should run delete action on selectedRows', async () => {
      const selectedRows = [1, 2, 3].map((id) => ({ data: { id } }));
      const context = getMockT<Context>({
        state: {
          selectedRows,
        },
      });

      const mockActionsSubmit = jest.fn();
      context.actions.submit = mockActionsSubmit;

      await ExpenseCategoriesController._deleteSelectedValues(context);

      expect(mockActionsSubmit).toHaveBeenCalledWith({
        transform: expect.any(Function),
      });
    });
  });

  describe('onSelection', () => {
    it('should update the state properly when selected rows is not empty', () => {
      const context = getMockT<Context>({
        state: {
          selectedRows: [],
          isDeleteButtonEnabled: false,
          activeInactive: false,
        },
      });

      const selectedRows = [1, 2, 3];
      ExpenseCategoriesController.onSelection(context, selectedRows);

      expect(context.state.selectedRows).toStrictEqual(selectedRows);
      expect(context.state.isDeleteButtonEnabled).toBe(true);
      expect(context.state.activeInactive).toBe(true);
    });

    it('should update the state properly when selected rows is empty', () => {
      const context = getMockT<Context>({
        state: {
          selectedRows: [1, 2, 3],
          isDeleteButtonEnabled: true,
          activeInactive: true,
        },
      });

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const selectedRows: any[] = [];
      ExpenseCategoriesController.onSelection(context, selectedRows);

      expect(context.state.selectedRows).toStrictEqual(selectedRows);
      expect(context.state.isDeleteButtonEnabled).toBe(false);
      expect(context.state.activeInactive).toBe(false);
    });
  });

  describe('makeInactive', () => {
    it('should run update action on selectedRows', async () => {
      const selectedRows = [1, 2, 3].map((id) => ({ data: { id } }));
      const context = getMockT<Context>({
        state: {
          selectedRows,
        },
      });

      const mockActionsRun = jest.fn();
      context.actions.run = mockActionsRun;

      const mockShowToast = jest.fn();
      context.helpers.showToast = mockShowToast;

      const mockRefetchUiModelData = jest.fn();
      context.actions.refetchUiModelData = mockRefetchUiModelData;

      await ExpenseCategoriesController.makeInactive(context);

      for (const row of selectedRows) {
        expect(mockActionsRun).toHaveBeenCalledWith({
          expenseCategories: {
            action: 'update',
            data: { id: row.data.id, status: 'inactive' },
          },
        });
      }
      expect(mockShowToast).toHaveBeenCalledTimes(1);
      expect(mockShowToast).toHaveBeenCalledWith({
        title: '{{expense.action.success}}',
        type: 'success',
      });

      expect(mockRefetchUiModelData).toHaveBeenCalledTimes(1);
    });
  });

  describe('makeActive', () => {
    it('should run update action on selectedRows', async () => {
      const selectedRows = [1, 2, 3].map((id) => ({ data: { id } }));
      const context = getMockT<Context>({
        state: {
          selectedRows,
        },
      });

      const mockActionsRun = jest.fn();
      context.actions.run = mockActionsRun;

      const mockShowToast = jest.fn();
      context.helpers.showToast = mockShowToast;

      const mockRefetchUiModelData = jest.fn();
      context.actions.refetchUiModelData = mockRefetchUiModelData;

      await ExpenseCategoriesController.makeActive(context);

      for (const row of selectedRows) {
        expect(mockActionsRun).toHaveBeenCalledWith({
          expenseCategories: {
            action: 'update',
            data: { id: row.data.id, status: 'active' },
          },
        });
      }
      expect(mockShowToast).toHaveBeenCalledTimes(1);
      expect(mockShowToast).toHaveBeenCalledWith({
        title: '{{expense.action.success}}',
        type: 'success',
      });

      expect(mockRefetchUiModelData).toHaveBeenCalledTimes(1);
    });
  });

  describe('isMakeInactiveDisabled', () => {
    it('should return true when now row is selected', () => {
      const context = getMockT<Context>({ state: { selectedRows: [] } });
      const result =
        ExpenseCategoriesController.isMakeInactiveDisabled(context);
      expect(result).toBe(true);
    });

    it('should return true when all rows are inactive', () => {
      const context = getMockT<Context>({
        state: {
          selectedRows: [
            { data: { id: 1, status: 'Inactive' } },
            { data: { id: 2, status: 'Inactive' } },
          ],
        },
      });
      const result =
        ExpenseCategoriesController.isMakeInactiveDisabled(context);
      expect(result).toBe(true);
    });

    it('should return false when all rows are not inactive', () => {
      const context = getMockT<Context>({
        state: {
          selectedRows: [
            { data: { id: 1, status: 'Inactive' } },
            { data: { id: 2, status: 'Active' } },
          ],
        },
      });
      const result =
        ExpenseCategoriesController.isMakeInactiveDisabled(context);
      expect(result).toBe(false);
    });
  });

  describe('isMakeActiveDisabled', () => {
    it('should return true when now row is selected', () => {
      const context = getMockT<Context>({ state: { selectedRows: [] } });
      const result = ExpenseCategoriesController.isMakeActiveDisabled(context);
      expect(result).toBe(true);
    });

    it('should return true when all rows are active', () => {
      const context = getMockT<Context>({
        state: {
          selectedRows: [
            { data: { id: 1, status: 'Active' } },
            { data: { id: 2, status: 'Active' } },
          ],
        },
      });
      const result = ExpenseCategoriesController.isMakeActiveDisabled(context);
      expect(result).toBe(true);
    });

    it('should return false when all rows are not active', () => {
      const context = getMockT<Context>({
        state: {
          selectedRows: [
            { data: { id: 1, status: 'Inactive' } },
            { data: { id: 2, status: 'Active' } },
          ],
        },
      });
      const result = ExpenseCategoriesController.isMakeActiveDisabled(context);
      expect(result).toBe(false);
    });
  });
});
