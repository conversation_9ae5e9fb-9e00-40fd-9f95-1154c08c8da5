// @i18n:employeeExpense
import { UILifecycleHooks } from '@everestsystems/content-core';
import type { PlainDate } from '@everestsystems/datetime';
import { Decimal } from '@everestsystems/decimal';
import { toCurrencyValueString } from '@pkg/everest.base/public/currency/precision';
import {
  getUIModelFromSharedState,
  setUIModelInSharedState,
} from '@pkg/everest.base/public/utils/ui/sharedStateUtils.ui';
import type { EvstCurrencyAmount } from '@pkg/everest.base/types/composites/CurrencyAmount';
import type { EvstBaseCurrency } from '@pkg/everest.base/types/enums/BaseCurrency';
import { toPlainDate } from '@pkg/everest.fin.accounting/public/utils/Date/date';
import { EvstJournalEntryType } from '@pkg/everest.fin.accounting/types/enums/JournalEntryType';
import { EvstExpenseStatus } from '@pkg/everest.fin.base/types/enums/ExpenseStatus';
import { EvstExpenseType } from '@pkg/everest.fin.base/types/enums/ExpenseType';
import { EvstStatusOfPayment } from '@pkg/everest.fin.base/types/enums/StatusOfPayment';
import type { ExpenseReportBase } from '@pkg/everest.fin.expense/types/ExpenseReportBase';
import { ExpenseReportBaseUI } from '@pkg/everest.fin.expense/types/ExpenseReportBase.ui';
import type { CreateExpenseReportUiTemplate } from '@pkg/everest.fin.expense/types/uiTemplates/uinext/expenseMgmt/createExpenseReport.ui';
import {
  Action,
  Mode,
} from '@pkg/everest.fin.expense/uinext/generalFunctions.uicontroller';
import type { Employee } from '@pkg/everest.hr.base/types/Employee';

export type Context =
  CreateExpenseReportUiTemplate.OverrideCreateExpenseReportData<{
    postingPeriod: { locked: boolean; closed: boolean };
  }> & {
    state: {
      expenseItemIds: number[];
      exchangeRatesCache: Map<string, Decimal>;
      expenseStatusCodeList: Record<string, { text: string }>;
      expensePaymentStatusCodeList: Record<string, { text: string }>;
    };
    components: { receipt: { upload: () => void } };
  };

export const isEditable = ({ state }: Context) => {
  return state?.mode !== Mode.View;
};

export function isERBlockEditable(context: Context) {
  const { state } = context;
  const isPeriodOpen = isPostingPeriodOpen(context);
  return (
    state.mode === Mode.Create ||
    (state.mode === Mode.Edit && isPeriodOpen) ||
    false
  );
}

// uimodel request
export function getIds(context: Context) {
  return getUIModelFromSharedState(context, 'expenseItems').map((ei) => ei.id);
}

export function getQuery({ state }: Context) {
  if (state.mode === Mode.View || state.mode === 'review') {
    return {
      where: {
        sourcePaymentDocumentId: Number(state.id),
        sourcePaymentDocumentType: 'ExpenseReportBase',
      },
    };
  }
}

export function getExpenseReportBankDetailsQuery({ state }: Context) {
  if (state.mode === Mode.View || state.mode === 'review') {
    return {
      where: {
        expenseReportHeaderId: state.id,
      },
    };
  }
}

export function getBankDetailsQuery({ state }: Context) {
  if (state.employeeBankDetailsId) {
    return { where: { id: state.employeeBankDetailsId } };
  }
}

export function getPostingPeriodQuery({ state, data }: Context) {
  if (
    state.mode === Mode.View ||
    state.mode === 'review' ||
    state.mode === Mode.Create
  ) {
    return;
  }
  const { entityId, postingPeriodId } = data?.expenseReport ?? {};
  if (entityId && postingPeriodId) {
    return { entityId, postingPeriodId, mode: 'UI' };
  }
}

export function getExpenseItemsTableQuery({ state }: Context) {
  if (state.mode === Mode.Create) {
    const expenseItemIds = state?.expenseItemIds ?? [Number.MAX_VALUE];
    return {
      where: {
        id: { $in: expenseItemIds },
      },
    };
  } else {
    return {
      where: {
        expenseId: Number(state.id),
      },
    };
  }
}

export function getPeriodQuery({ state, data }: Context) {
  const { expenseReport } = data ?? {};
  if (
    expenseReport?.status === EvstExpenseStatus.Approved &&
    state.mode === Mode.View
  ) {
    const { entityId, submittedDate: postingDate } = expenseReport;
    return {
      entityId,
      postingDate,
      forPosting: false,
      lockEvaluation: {
        journalEntryTypes: [EvstJournalEntryType.Expense],
      },
    };
  }
}

export function getCondition({ state }: Context) {
  if (state?.mode === Mode.Create) {
    const condition = { id: Number.MAX_VALUE };
    return condition;
  } else {
    return { id: Number(state?.id) };
  }
}

export const toggleColumnsVisibility = (
  context: Context,
  entityCurrency: string,
  tableId?: string
) => {
  const { data, sections, state } = context;
  const expenseItems = data?.expenseItems ?? [];
  const origin = data?.expenseReport?.origin;

  setTimeout(() => {
    const functions = sections[tableId ?? 'expenseItemsTable'] ?? {};
    const hasDifferentCurrency =
      !!entityCurrency &&
      expenseItems.some((item) => entityCurrency !== item.currency);
    const hasVAT = expenseItems.some(
      (item) =>
        item.vatAmount && new Decimal(item.vatAmount.amount).greaterThan(0)
    );
    /* Added a new check to see if Approved Amount on any item is greater than 0 and only then show to column;
      This also hides the amount column which was being shown for same currency as entity currency
      Issue Link: https://github.com/everestsystems/content/issues/16818
    */
    const isApprovedAmountVisible =
      data?.expenseReport?.status === EvstExpenseStatus.Approved &&
      expenseItems.some((item) => {
        const approvedAmount = new Decimal(item?.approvedAmount?.amount || 0);
        const originalAmount = new Decimal(item?.amount?.amount || 0);
        return (
          approvedAmount.greaterThan(new Decimal(0)) &&
          !approvedAmount.equals(originalAmount)
        );
      });

    const columnsVisiblityStatus = {
      currency: hasDifferentCurrency,
      fxRate: hasDifferentCurrency,
      amount: hasDifferentCurrency || hasVAT || isApprovedAmountVisible,
      vatPercentage: hasVAT,
      approvedAmount: isApprovedAmountVisible,
      // Toggling of Category and Account based on origin - Issue Link : https://github.com/everestsystems/content/issues/15351
      categoryId: Boolean(!origin),
      'ExpenseItemBase-ExpenseCategory.category':
        Boolean(!origin) && state.mode === 'review',
      account: Boolean(origin),
      vatAmount: hasVAT,
    };

    functions?.setColumnsVisible(
      Object.keys(columnsVisiblityStatus).filter(
        (key) => columnsVisiblityStatus[key] === true
      ),
      true
    );
    functions?.setColumnsVisible(
      Object.keys(columnsVisiblityStatus).filter(
        (key) => columnsVisiblityStatus[key] === false
      ),
      false
    );
  }, 60); // Adding delay to prevent https://github.com/everestsystems/content/issues/12153
};

export function getTabTitle(context: Context) {
  const { state, data, sections } = context;
  const entityCurrency = data?.expenseReport?.reimbursementCurrency;
  const functions = sections.expenseItemsTable ?? {};
  const origin = data?.expenseReport?.origin;
  if (functions?.setColumnsVisible && !state.toggleOnce) {
    if (origin) {
      functions.setColumnsVisible(['accountId'], true);
    } else {
      functions.setColumnsVisible(['categoryId'], true);
    }
    toggleColumnsVisibility(context, entityCurrency);
    state.toggleOnce = true;
  }

  return state?.mode === Mode.Create
    ? `{{employeeExpense.expenseReport.create}}`
    : `${data?.expenseReport?.expenseNumber}: ${(
        data?.expenseReport?.['ExpenseReportBase-Employee'] as {
          name: string;
        }
      )?.name}`;
}

export function getExpenseId({ state }: Context) {
  return state?.mode === 'review' ? Number(state?.id) : Number.MAX_VALUE;
}

export function getMode({ state }: Context) {
  return state?.mode === 'review' ? 'reviewExpense' : 'expense';
}

export function isERBlockVisible({ state }: Context) {
  return state?.mode !== 'review';
}

export function isSummaryBlockVisible({ state, data }: Context, type) {
  const { status, paymentStatus } = data?.expenseReport ?? {};

  if (type === 'first') {
    return (
      state.mode === Mode.Create ||
      state.mode === Mode.Edit ||
      status === EvstExpenseStatus.PendingApproval ||
      ((state.mode === Mode.View || state.mode === 'review') &&
        paymentStatus === EvstStatusOfPayment.Unpaid)
    );
  } else if (type === 'second') {
    return (
      (state.mode === Mode.View || state.mode === 'review') &&
      paymentStatus &&
      paymentStatus !== EvstStatusOfPayment.Unpaid
    );
  }
}

export function isEReviewBlockVisible(context: Context) {
  const { state, sections, data } = context;
  if (state?.mode === 'review') {
    const entityCurrency = data?.expenseReport?.reimbursementCurrency;
    const origin = data?.expenseReport?.origin;

    const functions = sections.expenseItemsTableInCreateView ?? {};
    if (functions?.setColumnsVisible && !state.fetchOnce) {
      if (origin) {
        functions?.setColumnsVisible(['accountId'], true);
      } else {
        functions?.setColumnsVisible(
          ['ExpenseItemBase-ExpenseCategory.category'],
          true
        );
      }
      if (entityCurrency) {
        toggleColumnsVisibility(
          context,
          entityCurrency,
          'expenseItemsTableInCreateView'
        );
      }
      state.fetchOnce = true;
    }
    return true;
  }
  return false;
}

// Primary button
export function getPrimaryButtonLabel(context: Context) {
  const { data } = context;
  if (data?.expenseReport) {
    if (isPendingApproval(context)) {
      const { docState, userHasEmployeeRecord } =
        isUserAllowedToApprove(context);

      if (!userHasEmployeeRecord) {
        return '{{employeeExpense.user.notFound}}';
      }

      if (docState?.userApproved) {
        return '{{employeeExpense.approved}}';
      }
      if (docState?.userRejected) {
        return '{{employeeExpense.rejected}}';
      }
      if (!docState?.nextToApprove) {
        return '{{employeeExpense.pending.priorApproval}}';
      }
      return '{{employeeExpense.approve}}';
    }

    return '{{employeeExpense.payment.recordPayment}}';
  }
}

export const openReimburseModal = ({ data, helpers }: Context) => {
  const { id } = data?.expenseReport ?? {};
  helpers.navigate({
    to: `/templates/everest.fin.expense/payment/uinext/payment?mode=expenseReport&ids=${id}&batch=false`,
  });
};

export const initiateBankPaymentModal = ({ data, helpers }: Context) => {
  const { id } = data?.expenseReport ?? {};
  helpers.navigate({
    to: `/templates/everest.fin.integration.bank/uinext/payment/bankPayment?mode=expenseReport&ids=${id}&batch=false`,
  });
};

export const approveAction = async ({ helpers, actions, state }: Context) => {
  await actions.submit({
    onSuccess: async () => {
      helpers.showToast({
        title: '{{employeeExpense.approve}}',
        message: '{{employeeExpense.approve.message}}',
        type: 'success',
      });
      helpers.refetchUiModelDataSidebarTemplate({});
      await actions.refetchUiModelData();
    },
    successMessage: '{{employeeExpense.expenseReportApproved}}',
    loadingMessage: '{{employeeExpense.expenseReportApproving}}',
    onError: async () => {
      await actions.refetchUiModelData();
    },
    transform: (formValues) => {
      return {
        expenseReport: {
          reviewExpense: {
            id: state.id ?? 0,
            status: EvstExpenseStatus.Approved,
            reviewMessage: '',
            fieldlist: [
              'id',
              'expenseNumber',
              'employeeId',
              'entityId',
              'status',
              'reimbursementCurrency',
              'reimbursableTotal',
              'submittedDate',
            ],
          },
        },
      };
    },
  });
};

export function isPrimaryButtonDisabled(context: Context) {
  const { data } = context;

  if (data?.expenseReport) {
    if (isPendingApproval(context)) {
      const { isAllowed } = isUserAllowedToApprove(context);
      return !isAllowed;
    }
    return (
      data?.expenseReport?.status === EvstExpenseStatus.Rejected ||
      data?.expenseReport?.paymentStatus === EvstStatusOfPayment.FullyPaid
    );
  }
}

export async function primaryButtonAction(context: Context) {
  const { data } = context;
  if (data?.expenseReport) {
    if (data.expenseReport.status === EvstExpenseStatus.PendingApproval) {
      await approveAction(context);
    } else if (data.expenseReport.status === EvstExpenseStatus.Approved) {
      openReimburseModal(context);
    }
  }
}

export const getEmployee = (context: Context) => {
  const { data } = context;
  const employeeId = getUIModelFromSharedState(
    context,
    'expenseReport',
    'employeeId'
  );
  if (employeeId) {
    return data?.employees?.find((emp) => emp?.id === employeeId);
  } else if (isEditable(context)) {
    return data?.currentEmployee as unknown as Employee.Employee;
  }
};

export const getEmployeeName = (context: Context) => {
  const { data } = context;
  const name = getEmployee(context)?.name;
  if (name) {
    return name;
  }
  return data?.expenseReport?.employeeName;
};

export const getNoBankDetailsTooltipText = (context: Context) => {
  const name = getEmployeeName(context);

  return `{{expenseMgmt.payment.tooltip.noBankDetails}}`.replace(
    '$name$',
    name
  );
};

export async function getPrimaryButtonMoreActions(context: Context) {
  const { isFetchingUiModelData } = context;
  if (isFetchingUiModelData) {
    return [];
  }
  const { data, state } = context;
  if (
    state?.mode !== Mode.Create &&
    data?.expenseReport?.status === EvstExpenseStatus.Approved
  ) {
    const disabled = isInitiatePaymentsDisabled(context);
    return [
      {
        label: '{{expenseMgmt.payment.initiatePayment}}',
        visible: true,
        disabled,
        tooltip: {
          text: disabled ? getNoBankDetailsTooltipText(context) : '',
        },
        onClick: () => {
          initiateBankPaymentModal(context);
        },
      },
    ];
  }
  return [];
}

export function isInitiatePaymentsDisabled({ data }: Context) {
  return !(
    data?.expenseReportBankDetails?.id &&
    data?.expenseReportBankDetails?.bankAccountNumberType
  );
}

// More actions button
export function isRejectButtonDisabled(context: Context) {
  const { data } = context;
  if (data?.expenseReport && isPendingApproval(context)) {
    const { isAllowed } = isUserAllowedToApprove(context);
    return !isAllowed;
  }
  return true;
}

export function isRejectButtonVisible(context: Context) {
  return (
    !isUserAllowedToApprove(context).noMatchingPolicy &&
    isPendingApproval(context)
  );
}

export function isEditDisabled(context: Context) {
  const { data, state } = context;
  if (state.mode === Mode.Review) {
    state.originalMode = Mode.Review; // This is reqired
  }

  if (data?.expenseReport) {
    return (
      !isPostingPeriodOpen(context) || !data?.isExpenseAdmin // Only admins can edit others ER
    );
  }
  return true;
}

export function isCancelSubmissionButtonDisabled(context: Context) {
  const { data, state } = context;
  if (state.mode === Mode.Review) {
    state.originalMode = Mode.Review; // This is reqired
  }

  if (data?.expenseReport) {
    return (
      !isPostingPeriodOpen(context) ||
      data?.expenseReport?.paymentStatus !== EvstStatusOfPayment.Unpaid
    );
  }
  return true;
}

export function isSecondaryActionsDisabled({ data }: Context) {
  if (data?.expenseReport) {
    // User can edit/cancel until its unpaid and it should require re-approval
    return data.expenseReport.paymentStatus !== EvstStatusOfPayment.Unpaid;
  }
  return true;
}

export function getSecondaryTableTitle(_context: Context) {
  return '{{employeeExpense.expenseLine.title.plural}}';
}

export function navigateToPayment({ helpers }: Context, row) {
  helpers.navigate({
    to: `/templates/everest.fin.expense/payment/uinext/payment?mode=payment&id=${row.data.outboundPaymentHeaderId}`,
  });
}

export function isPaymentsTableVisible({
  state,
  data,
  isFetchingUiModelData,
}: Context) {
  const outboundPaymentLines = data?.outboundPaymentLines ?? [];
  return (
    !isFetchingUiModelData &&
    (state?.mode === Mode.View || state?.mode === 'review') &&
    outboundPaymentLines.length > 0
  );
}

export function getCancelSubmissionDescription({ data }: Context) {
  const expenseStatus = data?.expenseReport?.status;
  return expenseStatus === EvstExpenseStatus.Approved
    ? '{{employeeExpense.cancelSubmission.detailedRevertDescription}}'
    : '{{employeeExpense.cancelSubmission.revertDescription}}';
}

export function getTitle({ state, data }: Context) {
  if (state?.mode !== Mode.Create) {
    return data?.expenseReport?.expenseNumber;
  }
  return '{{employeeExpense.expenseReport.create}}';
}

export function getSubTitle({ state, data }: Context) {
  return state?.mode === Mode.Create
    ? undefined
    : (data?.expenseReport?.['ExpenseReportBase-Employee'] as { name: string })
        ?.name;
}

export function getJELinkId({ state, data }: Context) {
  const id = data?.journalEntryHeader?.[0]?.id;
  if (id) {
    state.JELinkId = id;
  }
  return state.JELinkId;
}

export function getJEHeaderNumber({ data }: Context) {
  const journalEntryNumber = data?.journalEntryHeader?.[0]?.journalEntryNumber;
  return journalEntryNumber;
}

export function isJELinkVisible({ data, state }: Context) {
  const status = data?.expenseReport?.status;
  const journalEntryNumber = data?.journalEntryHeader?.[0]?.journalEntryNumber;

  return (
    !!journalEntryNumber &&
    status === EvstExpenseStatus.Approved &&
    (state.mode === Mode.View || state.mode === 'review')
  );
}

export function isSourceVisible(context: Context) {
  return !!context.data?.expenseReport?.origin;
}

export function getUrllink(context: Context) {
  const { origin = '', externalUrl = '' } = context.data?.expenseReport ?? {};
  /**
   * This fix is needed since server redirects to index page if externalUrl is not prefixed with http or https.
   * https://github.com/everestsystems/content/issues/16405
   */
  return origin.trim().length > 0 &&
    externalUrl.trim().length > 0 &&
    externalUrl.slice(0, 4) !== 'http'
    ? '//' + externalUrl
    : externalUrl;
}

export function getJournalEntryHeaderRequest({ data }: Context) {
  const status = data?.expenseReport?.status;
  if (status === EvstExpenseStatus.Approved) {
    return {
      where: {
        sourceFinancialDocumentId: data.expenseReport.id,
        sourceFinancialDocumentType: 'ExpenseReportBase',
      },
    };
  }
  return;
}

export function getActualSourceDocumentId({ state, data }: Context) {
  return state.mode === Mode.Create || state.mode === Mode.Edit
    ? Number.MAX_VALUE
    : data?.expenseReport?.id;
}

// alert message
export function isAlertVisible({ data }: Context) {
  return data?.expenseReport?.status === EvstExpenseStatus.Rejected;
}
// block
export function getAction({ state }: Context) {
  return state?.mode === Mode.Create
    ? 'createExpenseReport'
    : 'updateOwnExpense';
}

export const getEmployeeId = (context: Context) => {
  const { state } = context;
  const employee = getEmployee(context);
  state.employeeBankDetailsId = employee?.bankDetailsId;
  return employee?.id;
};

export const formatExchangeRate = (
  { language }: Context,
  exchangeRate: number
) => {
  return new Intl.NumberFormat(language, {
    minimumFractionDigits: 6,
    maximumFractionDigits: 6,
  }).format(exchangeRate);
};

export const fetchExchangeRateCached = async (
  { state, actions }: Context,
  exchangeRateArguments: {
    baseCurrency?: string;
    targetCurrency?: string;
    date?: Date;
  }
): Promise<Decimal> => {
  state.exchangeRatesCache ??= new Map();

  const { baseCurrency, targetCurrency, date } = exchangeRateArguments;
  const argumentsHash = `${baseCurrency}-${targetCurrency}-${new Date(
    date
  ).toISOString()}`;
  if (state?.exchangeRatesCache?.has?.(argumentsHash)) {
    return state.exchangeRatesCache.get(argumentsHash);
  }

  const result = await actions.run({
    exchangeRates: {
      action: 'convertAmount',
      data: {
        ...exchangeRateArguments,
        amount: '0',
      },
    },
  });
  if (!('error' in result)) {
    const exchangeRate = result.exchangeRates?.exchangeRate;

    state?.exchangeRatesCache?.set?.(argumentsHash, exchangeRate);
    return exchangeRate;
  }
};

export const calculateExchangeRate = async (
  context: Context,
  exchangeRateArguments: {
    baseCurrency?: string;
    targetCurrency?: string;
    date?: Date;
  }
) => {
  const { targetCurrency, baseCurrency, date } = exchangeRateArguments;
  if (!targetCurrency || !baseCurrency || !date) {
    return null;
  }

  if (targetCurrency === baseCurrency) {
    return formatExchangeRate(context, 1);
  }

  return fetchExchangeRateCached(context, exchangeRateArguments);
};

export const setExchangeRates = async (context: Context) => {
  if (!isEditable(context)) {
    return;
  }

  const { data } = context;

  const selectedEntityId = getUIModelFromSharedState(
    context,
    'expenseReport',
    'entityId'
  );
  const entity = data?.entities?.find((ent) => ent.id === selectedEntityId);
  if (entity && 'currency' in entity) {
    const targetCurrency = entity.currency;

    const expenseItems = data?.expenseItems ?? [];

    const fxRates = await Promise.all(
      expenseItems.map((item) =>
        calculateExchangeRate(context, {
          baseCurrency: item.currency,
          targetCurrency,
          date: item.billDate,
        })
      )
    );

    for (const [index, item] of expenseItems.entries()) {
      setUIModelInSharedState(
        context,
        'expenseItems',
        'fxRate',
        fxRates[index],
        item._nodeReference
      );
    }
  }
};

export async function onEmployeeChange(context: Context) {
  const { state, data } = context;
  state.isEmployeeSelectionTriggered = true;

  // Change entity
  const { entityId, bankDetailsId } = getEmployee(context) ?? {};
  state.employeeBankDetailsId = bankDetailsId ?? null;
  // Checking if the entity is in active entity
  let activeEntityId;
  if (data?.entities?.find((entity) => entity?.id === entityId)?.id) {
    activeEntityId = entityId;
  }
  setUIModelInSharedState(
    context,
    'expenseReport',
    'entityId',
    activeEntityId,
    ''
  );
  await setExchangeRates(context);
}

export const getEntityId = (context: Context) => {
  const { data, state } = context;
  const expenseReportEntityId = getUIModelFromSharedState(
    context,
    'expenseReport',
    'entityId'
  );
  if (state?.mode !== 'review') {
    if (!isEditable(context)) {
      return expenseReportEntityId;
    }
    const entityId = expenseReportEntityId ?? getEmployee(context)?.entityId;
    // Checking if the entity is in active entity
    if (data?.entities?.find((entity) => entity?.id === entityId)?.id) {
      return entityId;
    }
  }
  const entities = data?.entities;
  if (expenseReportEntityId && entities) {
    return entities.find((entity) => entity.id === expenseReportEntityId)?.id;
  }
};

export function getEntityName(context: Context) {
  const { data } = context;
  const entityId = getEntityId(context);
  return data?.entities?.find((entity) => entity.id === entityId)?.entityName;
}

export async function onEntityChange(context: Context, id: number) {
  const { data } = context;
  const entity = data?.entities?.find((e) => e.id === id);
  if (entity?.id) {
    const entityCurrency = entity.currency;
    setUIModelInSharedState(
      context,
      'expenseReport',
      'reimbursementCurrency',
      entityCurrency,
      ''
    );
    toggleColumnsVisibility(context, entityCurrency);
    await setExchangeRates(context);
    setReimbursementAmount(context);
  }
}

export function setReimbursementAmount(context: Context) {
  const expenseItems = getUIModelFromSharedState(context, 'expenseItems') ?? [];
  const expenseReport = getUIModelFromSharedState(context, 'expenseReport');

  for (const ei of expenseItems) {
    if (expenseReport?.reimbursementCurrency) {
      const fxRate = new Decimal(ei.fxRate ?? 1);
      const reimbursableAmount = toCurrencyValueString(
        new Decimal(ei.amount.amount).times(fxRate),
        expenseReport.reimbursementCurrency as EvstBaseCurrency
      );

      setUIModelInSharedState(
        context,
        'expenseItems',
        'reimbursableAmount',
        {
          amount: reimbursableAmount,
          currency: expenseReport.reimbursementCurrency,
        },
        ei._nodeReference
      );
    }
  }
}

export function getReimbursementCurrency(context: Context) {
  const { data } = context;
  if (!isEditable(context)) {
    return data?.expenseReport?.reimbursementCurrency;
  }
  const entityId = getEntityId(context);
  return data?.entities?.find((entity) => entity.id === entityId)?.currency;
}

export function getDate(context: Context): PlainDate {
  const { state } = context;

  if (state?.mode === Mode.Create) {
    return toPlainDate(new Date());
  }
  return getUIModelFromSharedState(context, 'expenseReport', 'submittedDate');
}

export async function onDateChange(context: Context, _) {
  await setExchangeRates(context);
}

// total
export function getTotal(context: Context) {
  const { state } = context;
  const expenseReport = getUIModelFromSharedState(context, 'expenseReport');
  if (state.mode === 'review' || !isEditable(context)) {
    const reimbursableTotal = getUIModelFromSharedState(
      context,
      'expenseReport',
      'reimbursableTotal'
    );
    return reimbursableTotal;
  } else {
    const expenseItems = getUIModelFromSharedState(context, 'expenseItems');

    let total = new Decimal(0);
    if (expenseItems) {
      for (const item of expenseItems) {
        const { amount: itemAmount, reimbursableAmount } = item;
        const amount = reimbursableAmount?.amount
          ? new Decimal(reimbursableAmount?.amount)
          : new Decimal(itemAmount?.amount);
        if (!amount.isNaN() && expenseReport?.reimbursementCurrency) {
          total = total.plus(
            toCurrencyValueString(
              amount,
              expenseReport?.reimbursementCurrency as EvstBaseCurrency
            )
          );
        }
      }
    }
    return {
      amount: total.toFixed(),
      currency: expenseReport?.reimbursementCurrency,
    };
  }
}

export function removeExpenseItem(context: Context, { nodeInstance }) {
  const { state, sharedState, data } = context;
  const reference = nodeInstance.reference;
  const entityCurrency = data?.expenseReport?.reimbursementCurrency;
  sharedState.removeNodeInstance({ reference });
  state.expenseItemsModified = true;

  toggleColumnsVisibility(context, entityCurrency);
}

export function openExpenseItemModal(context: Context, row) {
  const { helpers, actions, state } = context;
  const { id, expenseItemNumber, expenseId, expenseType } = row.data;
  const entityId = getUIModelFromSharedState(
    context,
    'expenseReport',
    'entityId'
  );

  if (
    expenseType === EvstExpenseType.Pur ||
    expenseType === EvstExpenseType.Dis
  ) {
    const expenseTypeString =
      expenseType === EvstExpenseType.Pur ? 'Purchase' : 'Distance Travelled';
    helpers.openModal({
      title: `${expenseItemNumber}: ${expenseTypeString}`,
      size: 'small',
      template:
        '/templates/everest.fin.expense/uinext/expenseMgmt/createExpenseItem',
      initialState: {
        featureToggles: { delta: true },
        action: 'review',
        expenseType,
        expenseId: expenseId || state?.id,
        expenseLineId: id,
        entityId,
      },
      onModalSubmit: actions.refetchUiModelData,
    });
  } else {
    helpers.openModal({
      title: `${expenseItemNumber}: Per Diem Expense Item`,
      size: 'small',
      template:
        '/templates/everest.fin.expense/uinext/expenseMgmt/perDiemMgmt/perDiemExpenseItemModal',
      initialState: {
        mode: 'viewMode',
        expenseItemId: id,
        entityId,
      },
      onModalSubmit: actions.refetchUiModelData,
    });
  }
}

export function editExpenseItem(context: Context, row) {
  const { state, helpers, actions, data } = context;
  const expenseStatus = data?.expenseReport?.status;
  const paymentStatus = data?.expenseReport?.paymentStatus;
  const expenseItems = getUIModelFromSharedState(context, 'expenseItems');
  const entityId = getUIModelFromSharedState(
    context,
    'expenseReport',
    'entityId'
  );

  const periodData = data?.isPostingPeriodOpen as any;
  const isPostingPeriodLocked =
    periodData && typeof periodData === 'object' && 'locked' in periodData
      ? Boolean(periodData.locked)
      : false;

  const mode =
    expenseStatus === EvstExpenseStatus.Approved
      ? isPostingPeriodLocked
        ? 'modifyExpenseItem'
        : Mode.Edit
      : 'submitExpense';
  const paid =
    paymentStatus === EvstStatusOfPayment.FullyPaid ||
    paymentStatus === EvstStatusOfPayment.PartiallyPaid
      ? 'yes'
      : 'no';
  const action = state.mode === Mode.View ? Mode.View : Mode.Edit;
  const { id, expenseItemNumber, expenseType } = row.data;
  if (
    expenseType === EvstExpenseType.Pur ||
    expenseType === EvstExpenseType.Dis
  ) {
    const expenseTypeString =
      expenseType === 'Pur' ? 'Purchase' : 'Distance Travelled';
    helpers.openModal({
      size: 'small',
      title: `${expenseItemNumber}: ${expenseTypeString}`,
      template:
        '/templates/everest.fin.expense/uinext/expenseMgmt/createExpenseItem',
      initialState: {
        featureToggles: { delta: true },
        action,
        expenseType,
        mode,
        expenseId: state?.id,
        expenseLineId: id,
        entityId,
        paid,
        expenseStatus,
      },
      onModalSubmit: async (response) => {
        const keys = [
          'billDate',
          'merchantAndDescription',
          'receiptStatus',
          'categoryId',
          'currency',
          'reimbursableAmount',
          'amount',
          'vatPercentage',
          'vatAmount',
          'vatAccountId',
          'reimbursableVATAmount',
          'fxRate',
          'businessUnitId',
          'teamId',
        ];
        // Set updated value back to table's row
        if (expenseItems) {
          for (const item of expenseItems) {
            if (response?.expenseLine?.id === item?.id) {
              // Update all fields in specific row since it is not possible to identify which field was updated
              for (const field of keys) {
                setUIModelInSharedState(
                  context,
                  'expenseItems',
                  field,
                  response?.expenseLine[field],
                  item._nodeReference
                );
              }
            }
          }
        }
        // Fetch exchange rates again
        await setExchangeRates(context);

        const entityCurrency = data?.expenseReport?.reimbursementCurrency;
        toggleColumnsVisibility(context, entityCurrency);
        setReimbursementAmount(context);
        state.expenseItemsModified = true;
      },
    });
  } else {
    const modalMode =
      paid === 'yes' ||
      mode === 'modifyExpenseItem' ||
      state.mode === Mode.View ||
      state.mode === Mode.Review
        ? 'viewMode'
        : 'editMode';

    helpers.openModal({
      title: `${expenseItemNumber}: Per Diem Expense Item`,
      size: 'small',
      template:
        '/templates/everest.fin.expense/uinext/expenseMgmt/perDiemMgmt/perDiemExpenseItemModal',
      initialState: {
        mode: modalMode,
        expenseItemId: id,
        entityId,
      },
      onModalSubmit: async (response) => {
        const perDiemExpenseItem = expenseItems.find(
          (item) => item.id === response?.perDiemExpenseItem?.id
        );
        const keys = ['currency', 'amount', 'businessUnitId', 'teamId'];

        // Set updated value back to table's row
        for (const field of keys) {
          setUIModelInSharedState(
            context,
            'expenseItems',
            field,
            response?.perDiemExpenseItem[field],
            perDiemExpenseItem._nodeReference
          );
        }
        // Fetch exchange rates again
        await setExchangeRates(context);
        state.expenseItemsModified = true;

        const entityCurrency = data?.expenseReport?.reimbursementCurrency;
        toggleColumnsVisibility(context, entityCurrency);
        setReimbursementAmount(context);
      },
    });
  }
}

export function getExpenseBOQuery({ state }) {
  if (state.id) {
    return {
      where: {
        headerKey: state.id,
      },
    };
  }

  return;
}

export function addExpenseItemModal(context: Context) {
  const { helpers, state, sections, data } = context;
  helpers.openModal({
    title: '{{employeeExpense.create.expenseItem}}',
    size: 'small',
    template:
      '/templates/everest.fin.expense/uinext/expenseMgmt/createExpenseItem',
    initialState: {
      featureToggles: { delta: true },
      action: Action.Create,
      mode: 'submitExpense',
      expenseId: state?.id,
      entityId: getUIModelFromSharedState(context, 'expenseReport', 'entityId'),
    },
    onModalSubmit: async (response) => {
      const {
        id,
        expenseItemNumber,
        billDate,
        merchantAndDescription,
        receiptStatus,
        categoryId,
        currency,
        amount = { amount: 0 },
        reimbursableAmount = { amount: 0 },
        vatPercentage,
        vatAmount = { amount: 0 },
        vatAccountId,
        reimbursableVATAmount = {
          amount: 0,
        },
        fxRate,
        businessUnitId,
        teamId,
      } = response?.ExpenseItemBase ?? {};

      // Add new row with data in table
      await sections.expenseItemsTable.addRow({
        id,
        expenseItemNumber,
        billDate,
        merchantAndDescription,
        categoryId,
        receiptStatus,
        currency,
        amount,
        reimbursableAmount,
        vatPercentage,
        vatAmount,
        vatAccountId,
        reimbursableVATAmount,
        fxRate,
        businessUnitId,
        teamId,
      });
      // Fetch exchange rates again
      await setExchangeRates(context);
      state.expenseItemsModified = true;

      const entityCurrency = data?.expenseReport?.reimbursementCurrency;
      toggleColumnsVisibility(context, entityCurrency);
      setReimbursementAmount(context);
      helpers.closeModal();
    },
  });
}

export function openAddPerDiemExpenseItemModal(context: Context) {
  const { helpers, state, sections, data } = context;
  const entityId = getUIModelFromSharedState(
    context,
    'expenseReport',
    'entityId'
  );

  const entity = data?.entities?.find((e) => e.id === entityId);
  const entityCurrency = entity.currency;

  helpers.openModal({
    title: '{{employeeExpense.create.expenseItem}}',
    size: 'small',
    template:
      '/templates/everest.fin.expense/uinext/expenseMgmt/perDiemMgmt/perDiemExpenseItemModal',
    initialState: {
      mode: 'createMode',
      entityId,
      entityCurrency,
    },
    onModalSubmit: async (response) => {
      const {
        id,
        expenseItemNumber,
        billDate,
        merchantAndDescription,
        receiptStatus,
        categoryId,
        currency,
        amount = { amount: 0 },
        reimbursableAmount = { amount: 0 },
        vatPercentage,
        vatAmount = { amount: 0 },
        vatAccountId,
        reimbursableVATAmount = {
          amount: 0,
        },
        expenseType,
        fxRate,
        businessUnitId,
        teamId,
      } = response.perDiemExpenseItem ?? {};

      // Add new row with data in table
      await sections.expenseItemsTable.addRow({
        id,
        expenseItemNumber,
        billDate,
        merchantAndDescription,
        categoryId,
        receiptStatus,
        currency,
        amount,
        expenseType,
        reimbursableAmount,
        vatPercentage,
        vatAmount,
        vatAccountId,
        reimbursableVATAmount,
        fxRate,
        businessUnitId,
        teamId,
      });
      // Fetch exchange rates again
      await setExchangeRates(context);
      state.expenseItemsModified = true;

      const entityCurrency = data?.expenseReport?.reimbursementCurrency;
      toggleColumnsVisibility(context, entityCurrency);
      setReimbursementAmount(context);
      helpers.closeModal();
    },
  });
}

export function displayExtraAction({ state }: Context) {
  return state?.mode === Mode.Create || state?.mode === Mode.Edit;
}

export function displayPerDiemActionButton(context: Context) {
  return displayExtraAction(context) && !isAddPerDiemActionDisabled(context);
}

export function isExtraActionDisabled({ data }: Context) {
  // Adding or Removing Expense items is allowed untill it is not approved
  const expenseStatus = data?.expenseReport?.status;
  return !!expenseStatus && expenseStatus === EvstExpenseStatus.Approved;
}

export function isAddPerDiemActionDisabled(context: Context) {
  const { data } = context;
  const expenseItems = getUIModelFromSharedState(context, 'expenseItems');

  return !(
    data?.expenseCategories?.find(
      (category) => category.categoryType === EvstExpenseType.PerDiem
    ) &&
    !isExtraActionDisabled(context) &&
    expenseItems?.filter((item) => item.expenseType === EvstExpenseType.PerDiem)
      ?.length === 0
  );
}

export function isStateModeView({ state }: Context) {
  return state?.mode === Mode.View;
}

// Buttons-actions
export function getFirstButtonLabel({ state }: Context) {
  if (state?.mode === Mode.Create) {
    return '{{employeeExpense.submit}}';
  } else if (state?.mode === Mode.Edit) {
    return '{{employeeExpense.save}}';
  }
}

export function getReimbursableAmount(context: Context, params) {
  const reimbursableAmount = new Decimal(
    params?.data?.amount?.amount ?? 0
  ).times(params?.data?.fxRate ?? 1);
  const expenseReport = getUIModelFromSharedState(context, 'expenseReport');

  return {
    amount: reimbursableAmount?.toFixed(),
    currency: expenseReport.reimbursementCurrency,
  };
}

export function getCategoryName({ data }: Context, params) {
  const { categoryId } = params?.data ?? {};
  const categoryName = data?.expenseCategories?.find(
    (category) => category.id === categoryId
  )?.category;
  return categoryName;
}

export function getBussinessUnitId({ data }: Context, params) {
  const { businessUnitId } = params?.data ?? {};
  const unitName = data?.businessUnits?.find(
    (unit) => unit.id === businessUnitId
  )?.name;
  return unitName;
}

export function getTeamId({ data }: Context, params) {
  const { teamId } = params?.data ?? {};
  const teamName = data?.teams?.find((team) => team.id === teamId)?.teamName;
  return teamName;
}

export function isPostingPeriodOpen(context: Context) {
  const { data } = context;
  const { postingPeriod } = data ?? {};
  const { closed = false, locked = false } = postingPeriod ?? {};

  return !closed && !locked;
}

export function isFirstButtonVisible(context: Context) {
  const { state } = context;
  return state?.mode === Mode.Create || state?.mode === Mode.Edit;
}

export async function firstButtonAction(context: Context) {
  const { state } = context;
  const entityCurrency = getUIModelFromSharedState(
    context,
    'expenseReport',
    'reimbursementCurrency'
  );
  switch (state?.mode) {
    case Mode.Create:
    case Mode.Edit: {
      await submitExpenseReport(context);
      toggleColumnsVisibility(context, entityCurrency);
      break;
    }
  }
}

export function goToEditMode(context: Context) {
  const { state } = context;

  const entityCurrency = getUIModelFromSharedState(
    context,
    'expenseReport',
    'reimbursementCurrency'
  );

  state.mode = Mode.Edit;
  toggleColumnsVisibility(context, entityCurrency);
}

export function isCancelButtonVisible({ state }: Context) {
  return state?.mode === Mode.Edit;
}

export function cancelButtonAction(context: Context) {
  const { state, sharedState } = context;
  const entityCurrency = getUIModelFromSharedState(
    context,
    'expenseReport',
    'reimbursementCurrency'
  );

  sharedState.reset();
  toggleColumnsVisibility(context, entityCurrency);

  state.mode = state.originalMode === Mode.Review ? Mode.Review : Mode.View;
}

export async function submitExpenseReport(context: Context) {
  const { actions, data, form, helpers, state } = context;
  // Trigger custom form validations
  try {
    await form.handleSubmit({});
  } catch {
    return;
  }

  const expenseItems = getUIModelFromSharedState(context, 'expenseItems') ?? [];
  if (expenseItems.length === 0) {
    helpers.showToast({
      title: '{{employeeExpense.error}}',
      message: 'Cannot create expense without expense items',
      type: 'error',
    });
    return;
  }

  const action = getAction(context);

  const key = 'expenseReport';

  const fieldlist = ['id', 'status', 'employeeId', 'expenseNumber', 'entityId'];

  // set values
  const expense = {
    submittedDate: getUIModelFromSharedState(context, key, 'submittedDate'),
    status:
      action === 'createExpenseReport'
        ? EvstExpenseStatus.PendingApproval
        : data?.expenseReport?.status,
    employeeId: getUIModelFromSharedState(context, key, 'employeeId'),
    entityId: getUIModelFromSharedState(context, key, 'entityId'),
    reimbursementCurrency: getUIModelFromSharedState(
      context,
      key,
      'reimbursementCurrency'
    ),
  };

  helpers.showToast({
    title: '{{employeeExpense.expenseReportSubmitting}}',
    type: 'success',
  });

  const draft = !!data?.expenseReport?.$metadata?.isDraft;
  let response;
  const expenseItemIds = getIds(context);
  if (action === 'createExpenseReport') {
    response = await ExpenseReportBaseUI.createExpenseReport(context, {
      expense,
      expenseItemIds,
      fieldlist: fieldlist as (keyof ExpenseReportBase.ExpenseReportBase)[],
      sendNotification: true,
      options: { draft, activateDraft: true },
    }).run('expenseReport');
  } else if (action === 'updateOwnExpense') {
    response = await ExpenseReportBaseUI.updateOwnExpense(context, {
      expenseReportId: data.expenseReport.id,
      data: expense,
      expenseItemIds: state.expenseItemsModified ? expenseItemIds : null,
      fieldlist: fieldlist as (keyof ExpenseReportBase.ExpenseReportBase)[],
    }).run('expenseReport');
  }

  if (response?.error) {
    helpers.showToast({
      title: '{{employeeExpense.error}}',
      message: '{{employeeExpense.updateError.message}}',
      type: 'error',
    });
  } else {
    const { id, expenseNumber } = response?.expenseReport ?? {};
    if (state?.mode === Mode.Create) {
      helpers.showToast({
        title: `{{employeeExpense.toSubmitted}}`.replace(
          '$expenseNumber$',
          `${expenseNumber}`
        ),
        message: `{{employeeExpense.forApprovalNoReceiver}}`,
        type: 'success',
      });
      helpers.navigate({
        to: '/templates/everest.fin.expense/uinext/expenseMgmt/createExpenseReport',
        queryParams: { id, 'feat-delta': true },
        initialState: { mode: 'view' },
        closeCurrentTab: true,
      });
    } else {
      helpers.showToast({
        title: '{{employeeExpense.update.report}}',
        message: `{{employeeExpense.update.success.message}} (${expenseNumber}).`,
        type: 'success',
      });
      state.mode = state.originalMode === Mode.Review ? Mode.Review : Mode.View;
      await actions.refetchUiModelData();
    }
  }
}

export function isBankDetailsNotPresentAlertVisible(context: Context) {
  const { data, state, isFetchingUiModelData } = context;
  const bankDetails = data?.bankDetails;
  const employeeId = getEmployeeId(context);
  if (!employeeId || !bankDetails || isFetchingUiModelData) {
    return false;
  }
  return Boolean(
    employeeId &&
      (state?.mode === Mode.Create || state?.mode === Mode.Edit) &&
      !(
        state.employeeBankDetailsId &&
        bankDetails &&
        bankDetails.bankAccountHolderName &&
        bankDetails.bankAccountNumberType &&
        bankDetails.bankAccountNumber &&
        bankDetails.financialInstitutionId &&
        bankDetails.financialInstitutionIdType
      )
  );
}

export async function getBankAccountDetailsNotPresentMessage(context) {
  const name = getEmployeeName(context);
  return `{{employeeExpense.bankDetailsMissing}}`.replace('$name$', name);
}

export async function navigateToEmployeeBankDetails(context) {
  const { helpers } = context;

  const id = getEmployeeId(context);
  helpers.navigate({
    to: `@uicode:employee?id=${id}&mode=view&activeSegment=1`,
  });
}

export async function cancelSubmission(context: Context) {
  const { actions, form, helpers, data, state } = context;

  // User is allowed to cancel expense until any payment is done or posting period is available
  await actions.submit({
    onSuccess: (response) => {
      helpers.showToast({
        title: `{{employeeExpense.cancelled}}`.replace(
          '$expenseNumber$',
          `${response?.expenseReport?.expenseNumber}`
        ),
        type: 'success',
      });
      // delete form values
      form.deleteFormModel('expenseReport');
      helpers.navigate({
        to: '@uicode:yourExpenses',
        initialState: { activeSegment: 1 },
        closeCurrentTab: true,
      });
    },
    successMessage: '{{employeeExpense.expenseReportCancelled}}',
    loadingMessage: '{{employeeExpense.expenseReportCancelling}}',
    onError: () =>
      helpers.showToast({
        title: `{{employeeExpense.unableCancel}} ${data?.expenseReport?.expenseNumber}`,
        message: '{{employeeExpense.cancel.approve.reject.message}}',
        type: 'error',
      }),
    transform: (formValues) => {
      // WHAT are the next 3 lines??
      formValues['expenseReport'] ??= {};
      formValues['expenseReport'].updateOwnExpense ??= {};
      formValues['expenseReport'].updateOwnExpense.status =
        EvstExpenseStatus.PendingApproval;
      const id = state.id ?? 0;
      return {
        expenseReport: {
          cancelExpenseReportSubmission: {
            where: { id },
            fieldlist: ['id', 'expenseNumber', 'status'],
          },
        },
      };
    },
  });
}

//This Hook is used for initializing Status Header code values once on page initialization
UILifecycleHooks.onInit(({ state, getCodeList }: Context) => {
  // eslint-disable-next-line @typescript-eslint/no-floating-promises
  void (async () => {
    try {
      const [statusCodeList, paymentStatusCodeList] = await Promise.all([
        getCodeList('expenseReport', ['status']),
        getCodeList('expenseReport', ['paymentStatus']),
      ]);

      // Store in state for future use
      state.expenseStatusCodeList = statusCodeList;
      state.expensePaymentStatusCodeList = paymentStatusCodeList;
    } catch {
      // Initialize with empty objects on failure
      state.expenseStatusCodeList = {};
      state.expensePaymentStatusCodeList = {};
    }
  })();

  // This function doesn't return anything (void)
});
export function getStatus({ state, data }: Context) {
  if (state?.mode === Mode.Create) {
    return undefined;
  }

  const status = data?.expenseReport?.status;
  if (!status) {
    return undefined;
  }

  const statusText = state.expenseStatusCodeList?.[status]?.text || status;
  const paymentStatus = data.expenseReport.paymentStatus;
  if (
    (statusText === 'Approved' || statusText === 'approved') &&
    paymentStatus
  ) {
    const paymentStatusText =
      state.expensePaymentStatusCodeList?.[paymentStatus]?.text ||
      paymentStatus;
    return `${statusText}, ${paymentStatusText}`;
  }

  return statusText;
}
export function getRowActions(context: Context) {
  return {
    actions: [
      {
        content: '{{employeeExpense.remove}}',
        onClick: (row) => removeExpenseItem(context, row),
        disabled: isRowActionsDisabled(context),
      },
    ],
  };
}

// Allowed actions as per - https://github.com/everestsystems/content/issues/5695
export function isRowActionsDisabled(context: Context) {
  const { state, data } = context;
  if (
    state?.mode === Mode.Create &&
    !state.exchangeRateFetchedOnce &&
    state.expenseItemIds
  ) {
    state.exchangeRateFetchedOnce = true;
    // Fetch exchange rate once after table loads

    setTimeout(() => {
      void setExchangeRates(context);
    }, 1000);
  }

  const status = data?.expenseReport?.status;
  return (
    state?.mode === Mode.View ||
    state?.mode === 'review' ||
    status === EvstExpenseStatus.Approved
  );
}

export function rejectAction(context: Context) {
  const { actions, helpers, state } = context;
  const id = getUIModelFromSharedState(context, 'expenseReport', 'id');
  helpers.openModal({
    title: 'Reject Expense Report',
    size: 'small',
    template:
      '/templates/everest.fin.expense/uinext/expenseMgmt/rejectionMessage',
    initialState: {
      featureToggles: { delta: true },
      id,
    },
    onModalSubmit: async (response) => {
      if (response?.reject) {
        await actions.submit({
          onSuccess: async () => {
            helpers.showToast({
              title: '{{employeeExpense.reject}}',
              message: '{{employeeExpense.reject.message}}',
              type: 'success',
            });
            await actions.refetchUiModelData();
          },
          successMessage: '{{employeeExpense.expenseReportRejected}}',
          loadingMessage: '{{employeeExpense.expenseReportRejecting}}',
          onError: async () => {
            await actions.refetchUiModelData();
          },
          transform: (formValues) => {
            return {
              expenseReport: {
                reviewExpense: {
                  id: state.id ?? 0,
                  reviewMessage: response?.reason ?? '',
                  status: EvstExpenseStatus.Rejected,
                  fieldlist: [
                    'id',
                    'expenseNumber',
                    'entityId',
                    'status',
                    'reimbursementCurrency',
                    'reimbursableTotal',
                  ],
                },
              },
            };
          },
        });
      }
    },
  });
}

// Custom field validations
export function validateEmployee(_context: Context) {
  return {
    validate: (value) => !!value || '{{employeeExpense.validateEmployee}}',
  };
}

export function validateEntity(_context: Context) {
  return {
    validate: (value) => !!value || '{{employeeExpense.validateEntity}}',
  };
}

export function validateCurrency(_context: Context) {
  return {
    validate: (value) => !!value || '{{employeeExpense.validateCurrency}}',
  };
}

export function validateReviewer(_context: Context) {
  return {
    validate: (value) => !!value || '{{employeeExpense.validateReviewer}}',
  };
}

export function getAmountPaid({ data }: Context): EvstCurrencyAmount {
  const { amountPaid, reimbursementCurrency } = data.expenseReport ?? {};
  return {
    amount: new Decimal(amountPaid?.amount)
      ?.times(-1)
      .toFixed() as unknown as Decimal,
    currency: reimbursementCurrency,
  };
}

export function getPendingPaymentAmount(context: Context): EvstCurrencyAmount {
  const { data } = context;
  const { pendingAmount, reimbursementCurrency } = data.expenseReport ?? {};
  return {
    amount: new Decimal(pendingAmount?.amount)
      ?.times(-1)
      .toFixed() as unknown as Decimal,
    currency: reimbursementCurrency,
  };
}

export function isPendingAmountVisible({ data }: Context) {
  const paymentStatus = data?.expenseReport?.paymentStatus;
  return paymentStatus === EvstStatusOfPayment.Pending;
}

export function isAmountDueVisible({ data }: Context) {
  const paymentStatus = data?.expenseReport?.paymentStatus;
  return paymentStatus && paymentStatus !== EvstStatusOfPayment.Unpaid;
}

export function viewApprovers(context: Context) {
  const reportId = Number(context?.state?.id);

  if (reportId) {
    context.helpers.openSidebarTemplate?.({
      title: '{{employeeExpense.viewApprovals}}',
      template: `/templates/everest.base.approvals/uinext/viewApprovers?documentId=${reportId}&documentType=ExpenseReport`,
    });
  }
}

export function isUserAllowedToApprove(context: Context): {
  isAllowed: boolean;
  docState: any;
  userHasEmployeeRecord: boolean;
  noMatchingPolicy: boolean;
} {
  const res = getUIModelFromSharedState(context, 'approvalPolicyHeader') as any;

  const isAllowed =
    res?.userHasEmployeeRecord &&
    !res?.state?.userApproved &&
    !res?.state?.userRejected &&
    !res?.state?.noMatchingPolicy &&
    res?.state?.userCanApprove &&
    res?.state?.nextToApprove;

  const docState = res?.state;
  return {
    isAllowed,
    docState,
    userHasEmployeeRecord: res?.userHasEmployeeRecord,
    noMatchingPolicy: res?.state?.noMatchingPolicy,
  };
}

export function getExpenseReportApprovalParams(context: Context) {
  const { state } = context;
  const documentId = state?.id ? Number(state.id) : undefined;
  if (documentId) {
    return { documentId, documentType: 'ExpenseReport' };
  }
  return undefined;
}

export function isPendingApproval(context: Context) {
  const approvalStatus = getUIModelFromSharedState(
    context,
    'expenseReport',
    'status'
  );
  return approvalStatus === EvstExpenseStatus.PendingApproval;
}

// Function added with respect to Issue: https://github.com/everestsystems/content/issues/19923
// TODO : Disable options/alt + R for edit mode in future.
export function isAutoRefreshDataEnabled(context: Context) {
  return context?.state?.mode === 'view' || context?.state?.mode === 'review';
}
