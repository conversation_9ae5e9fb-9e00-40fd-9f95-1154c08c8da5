// @i18n:expense
import { EvstExpenseType } from '@pkg/everest.fin.base/types/enums/ExpenseType';
import type { ExpenseCategoriesUiTemplate } from '@pkg/everest.fin.expense/types/uiTemplates/uinext/expenseMgmt/expenseCategories.ui';

type ExpenseCategoriesContext =
  ExpenseCategoriesUiTemplate.ExpenseCategoriesContext;

export type Context = ExpenseCategoriesContext & {
  state: {
    selectedRows: {
      data: ExpenseCategoriesContext['data']['expenseCategoryAccountLinks'][0];
    }[];
  };
};

/**
 * Opens the modal to creates expense category of type purchase
 */
export function createRegularCategory({ helpers, actions }: Context) {
  helpers.openModal({
    size: 'small',
    title: '{{expense.createCategory}}',
    template:
      '/templates/everest.fin.expense/uinext/expenseMgmt/createExpenseCategory',
    initialState: {
      featureToggles: { delta: true },
      type: EvstExpenseType.Pur,
      mode: 'create',
    },
    onModalSubmit: actions.refetchUiModelData,
  });
}

/**
 * Opens the modal to creates expense category of type distance
 */
export function createDistanceCategory({ helpers, actions }: Context) {
  helpers.openModal({
    size: 'small',
    title: '{{expense.createDistanceCategory}}',
    template:
      '/templates/everest.fin.expense/uinext/expenseMgmt/createExpenseCategory',
    initialState: {
      featureToggles: { delta: true },
      type: EvstExpenseType.Dis,
      mode: 'create',
    },
    onModalSubmit: actions.refetchUiModelData,
  });
}

export function createPerDiemCategory({ helpers, actions }: Context) {
  helpers.openModal({
    size: 'small',
    title: '{{expense.createPerDiemCategory}}',
    template:
      '/templates/everest.fin.expense/uinext/expenseMgmt/createExpenseCategory',
    initialState: {
      featureToggles: { delta: true },
      type: EvstExpenseType.PerDiem,
      mode: 'create',
    },
    onModalSubmit: actions.refetchUiModelData,
  });
}

export const isAlertVisible = ({ data }: Context) => {
  return data?.distanceCategory?.length !== 1;
};

export function isDistanceButtonDisabled(context: Context) {
  return !isAlertVisible(context);
}

export function isDataLoaded({ data }: Context) {
  return data?.chartOfAccounts?.length > 0;
}

/**
 * The columns depends on the number of chart of accounts present in the system.
 * So, the columns is dynamic.
 * Return the columns based on the chart of accounts
 */
export function getColumns({ data }: Context) {
  const columns = [
    {
      headerName: '{{expense.categoryName}}',
      field: 'categoryName',
      cellVariant: {},
    },
  ];
  const chartOfAccounts = data?.chartOfAccounts ?? [];
  for (const coa of chartOfAccounts) {
    columns.push({
      headerName: coa.name,
      field: `${coa.id}Account`,
      cellVariant: {},
    });
  }
  columns.push({
    headerName: '{{expense.status}}',
    field: 'status',
    cellVariant: {
      variant: 'badge',
      matchers: { Active: 'success', Inactive: 'mystic-grey' },
    },
  });
  return columns ?? [];
}

export function rowClickAction(
  { actions, helpers }: Context,
  row: ExpenseCategoriesContext['data']['expenseCategoryAccountLinks'][0]
) {
  helpers.openModal({
    size: 'small',
    template:
      '/templates/everest.fin.expense/uinext/expenseMgmt/createExpenseCategory',
    initialState: {
      featureToggles: { delta: true },
      mode: 'view',
      status: row.status,
      categoryId: row.categoryId,
    },
    onModalSubmit: actions.refetchUiModelData,
    onClose: actions.refetchUiModelData,
  });
}

export async function fillSampleData(context: Context) {
  const { actions, helpers } = context;
  const response = await actions.run({
    expenseCategories: {
      action: 'fillCategorySampleData',
      data: {},
    },
  });
  if (!response?.error) {
    await actions.refetchUiModelData();
    helpers.showToast({
      title: '{{expense.sampleData.success}}',
      type: 'success',
    });
  }
}

export function _getIsDeleteDisabled({ state }: Context) {
  return !state?.isDeleteButtonEnabled;
}

export async function _deleteSelectedValues({ state, actions }: Context) {
  const returnObject: Record<string, unknown> = {};
  for (const [index, row] of state.selectedRows.entries()) {
    returnObject[`${index}:expenseCategories`] = {
      delete: row.data,
    };
  }
  await actions.submit({ transform: () => returnObject });
}

export function onSelection({ state }: Context, selectedRows: any[] = []) {
  state.selectedRows = selectedRows;
  state.isDeleteButtonEnabled = selectedRows.length > 0;
  state.activeInactive = selectedRows.length > 0;
}

export async function makeInactive({ actions, state, helpers }: Context) {
  await Promise.all(
    state.selectedRows.map((row) =>
      actions.run({
        expenseCategories: {
          action: 'update',
          data: { id: row.data.id, status: 'inactive' },
        },
      })
    )
  );

  helpers.showToast({
    title: '{{expense.action.success}}',
    type: 'success',
  });
  await actions.refetchUiModelData();
}

export async function makeActive({ actions, state, helpers }: Context) {
  for (const row of state.selectedRows) {
    await actions.run({
      expenseCategories: {
        action: 'update',
        data: { id: row.data.id, status: 'active' },
      },
    });
  }
  helpers.showToast({
    title: '{{expense.action.success}}',
    type: 'success',
  });
  await actions.refetchUiModelData();
}

export function isMakeInactiveDisabled({ state }: Context) {
  return state.selectedRows
    ? state.selectedRows.every((row) => row.data.status === 'Inactive')
    : true;
}

export function isMakeActiveDisabled({ state }: Context) {
  return state.selectedRows
    ? state.selectedRows.every((row) => row.data.status === 'Active')
    : true;
}

export function isFillSampleDataVisible({ data }: Context) {
  // https://github.com/everestsystems/content/issues/11467
  return data?.tenantType === 'development';
}
