{"version": 2, "uicontroller": ["createExpenseItem.uicontroller.ts"], "uimodel": {"state": {"openSubmitExpenseReportModal": false}, "nodes": {"ExpenseItemBase": {"type": "struct", "modelId": "everest.fin.expense/ExpenseItemBaseModel.ExpenseItemBase"}, "expenseLine": {"type": "struct", "query": "@controller:getExpenseItemQuery()", "modelId": "everest.fin.expense/ExpenseItemBaseModel.ExpenseItemBase", "options": {"draft": "include"}, "fieldList": ["id", "billDate", "categoryId", "currency", "description", "expenseId", "expenseItemNumber", "merchantAndDescription", "fxRate", "rate", "distance", "unit", "status", "expenseType", "merchant", "reimbursable", "amount", "vatPercentage", "billableExpense", "customerId", "vatAmount", "fullNetAmount", "vatAccountId", "employeeId", "businessUnitId", "teamId"]}, "expenseLineAttachment": {"type": "list", "query": {"where": {"expenseItemId": "@controller:getExpenseItemId()"}}, "options": {"draft": "include"}, "modelId": "everest.fin.expense/ExpenseItemAttachmentBaseModel.ExpenseItemAttachmentBase", "fieldList": []}, "exchangeRates": {"type": "struct", "modelId": "everest.base/ExchangeRateModel.ExchangeRate"}, "draftV2": {"modelId": "everest.appserver/metadata/DraftV2Model.DraftV2", "type": "struct"}, "entities": {"modelId": "everest.base/EntityModel.Entity", "type": "list", "queryActiveEntities": {"query": {}, "fieldList": ["id", "entityName", "currency"]}}, "vatConfigurationLines": {"type": "list", "model": "urn:evst:everest:fin/expense:model/node:VATConfigurationLine", "query": "@controller:getVATConfigurationQuery()", "fieldList": ["id", "entityId", "percentage", "accountId"]}, "expenseCategories": {"type": "list", "modelId": "everest.fin.expense/ExpenseCategoryModel.ExpenseCategory", "query": "@controller:getExpenseCategoriesQuery()"}, "businessUnitEnabled": {"modelId": "everest.appserver/ConfigurationModel.Configuration", "type": "struct", "getValueOrDefault": {"name": "BUSINESS_UNIT_ENABLED", "packageName": "everest.base", "defaultValue": true}, "fieldList": ["value"]}}}, "uiview": {"i18n": "employeeExpense", "autoRefreshData": true, "header": {"content": {"actions": [{"variant": "tertiary", "label": "{{employeeExpense.scan.receipt}}", "visible": "@controller:isEditable()", "onClick": "@controller:onScanClick"}]}}, "sections": {"content": [{"component": "Upload", "customId": "uplaodAttachment", "size": "3", "section": {"title": "{{employeeExpense.Receipt}}", "customId": "receipt"}, "props": {"name": "receipt", "version": 2, "everestPackage": "everest.fin.expense", "multiple": true, "disabled": "@controller:isDisabled()", "isEditing": "@controller:isEditable()", "formattedAcceptFileTypes": ".pdf,.tiff,.jpeg,.png,.jpg", "uploadHandler": "postgres", "variant": "ButtonView", "acceptableMimeTypes": ["application/pdf", "image/tiff", "image/jpeg", "image/png", "image/jpg"], "maxSizeInMB": 5, "attachmentFiles": "@controller:getAttachments()", "onSuccess": "@controller:scanAction"}}, {"component": "WidgetGroup", "customId": "expenseItemFieldGroup", "size": "12", "index": "2", "visible": "@controller:isWidgetBlockVisible()", "placement": "left", "selectedGroup": "@controller:getExpenseType()", "widgets": [{"label": "{{employeeExpense.purchase}}", "onClick": "@controller:setExpenseType", "component": "FieldGroup", "start": "1", "columns": "3", "type": "secondary", "editing": "@controller:isEditable()", "elements": [{"component": "Read<PERSON>nly", "value": "", "isEditing": false, "isPlainText": true, "start": "1", "end": "4"}, {"component": "DatePicker", "label": "{{employeeExpense.date}}", "parseAs": "date", "format": "P", "isDisabled": "@controller:isDisabledIfPaid()", "value": "@bindingController(expenseLine.billDate, getDate())"}, {"component": "Input", "label": "{{employeeExpense.merchant.name}}", "isDisabled": "@controller:isDisabled()", "value": "@bindingController(expenseLine.merchant, getMerchant())"}, {"component": "Select", "label": "{{employeeExpense.category}} *", "list": "@bindingController(expenseCategories, getPurchaseCategoriesList())", "idProp": "id", "textProp": "category", "value": "@bindingController(expenseLine.categoryId, getCategory())", "isDisabled": "@controller:isDisabled()", "customFieldMessages": {"fieldError": "@state:categoryValidationMessage"}}, {"component": "Select", "label": "{{employeeExpense.reimbursable}}", "value": "@bindingController(expenseLine.reimbursable, getReimbursable())", "text": "@controller:getReimbursableText()", "list": "@controller:getReimbursableList()", "visible": "@controller:isCheckboxVisible()", "isDisabled": true}, {"label": "{{employeeExpense.currency}}", "isDisabled": "@controller:isDisabledIfPaidOrApproved()", "value": "@bindingController(expenseLine.currency, getCurrency())"}, {"label": "{{employeeExpense.total}}", "value": "@binding:expenseLine.amount", "onChange": "@controller:validateTotal", "compositeConfig": {"amount": {"customFieldMessages": {"fieldError": "@state:totalErrorMsg"}}}, "isDisabled": "@controller:isTotalDisabled()"}, {"value": "@binding:expenseLine.businessUnitId", "label": "{{employeeExpense.businessUnit}}", "isDisabled": "@controller:isDisabled()", "visible": "@binding:businessUnitEnabled"}, {"value": "@binding:expenseLine.teamId", "label": "{{employeeExpense.team}}", "isDisabled": "@controller:isDisabled()"}, {"label": "{{employeeExpense.billableExpense}}", "value": "@binding:expenseLine.billableExpense", "onChange": "@controller:handleBillableExpenseChange"}, {"label": "{{employeeExpense.customer}}", "value": "@binding:expenseLine.customerId", "visible": "@controller:isCustomerVisible()"}, {"value": "@binding:expenseLine.vatPercentage", "label": "{{employeeExpense.vatPercentage}}", "visible": "@controller:isVATSelectionVisible()", "onChange": "@controller:handleVATPercentageChange", "isDisabled": "@controller:isDisabledIfPaid()", "semanticTypeUiParams": "@controller:getVATPercentageUiParams()"}, {"value": "@binding:expenseLine.vatAmount", "label": "{{employeeExpense.vatAmount}}", "isDisabled": "@controller:isDisabledIfPaid()", "visible": "@controller:isVATSelectionVisible()"}, {"value": "@binding:expenseLine.fullNetAmount", "label": "{{employeeExpense.netAmount}}", "isDisabled": "@controller:isDisabledIfPaid()", "visible": "@controller:isVATSelectionVisible()"}, {"component": "Input", "size": "3", "label": "{{employeeExpense.description}}", "value": "@bindingController(expenseLine.description, getDescription())"}]}, {"label": "{{employeeExpense.distanceTravelled}}", "onClick": "@controller:setExpenseType", "component": "FieldGroup", "columns": "3", "size": "12", "type": "secondary", "editing": "@controller:isEditable()", "elements": [{"component": "Read<PERSON>nly", "value": "{{employeeExpense.distanceTravelledMessage}}", "isEditing": false, "isPlainText": true, "start": "1", "end": "4"}, {"component": "DatePicker", "label": "{{employeeExpense.date}}", "parseAs": "date", "format": "P", "isDisabled": "@controller:isDisabledIfPaid()", "value": "@bindingController(expenseLine.billDate, getDate())"}, {"component": "Select", "label": "{{employeeExpense.reimbursable}}", "value": "@bindingController(expenseLine.reimbursable, getReimbursable())", "text": "@controller:getReimbursableText()", "list": "@controller:getReimbursableList()", "visible": "@controller:isCheckboxVisible()", "isDisabled": true}, {"component": "Select", "label": "{{employeeExpense.currency}}", "value": "@bindingController(expenseLine.currency, getCurrency())", "isDisabled": "@controller:isDisabledIfPaidOrApproved()"}, {"component": "Input", "type": "number", "label": "{{employeeExpense.rate}}", "value": "@bindingController(expenseLine.rate, getRate())", "rules": "@controller:validateOptionalFields('rateOrDistance')", "isDisabled": "@controller:isDisabledIfPaidOrApproved()", "onChange": "@controller:onRateChange"}, {"component": "Input", "type": "number", "label": "{{employeeExpense.distance}}", "value": "@bindingController(expenseLine.distance, getDistance())", "rules": "@controller:validateOptionalFields('rateOrDistance')", "isDisabled": "@controller:isDisabledIfPaidOrApproved()", "onChange": "@controller:onDistanceChange"}, {"component": "Select", "label": "{{employeeExpense.unit}} *", "value": "@binding:expenseLine.unit", "rules": "@controller:validateOptionalFields('unit')", "isDisabled": "@controller:isDisabled()"}, {"label": "{{employeeExpense.total}}", "value": "@binding:expenseLine.amount", "onChange": "@controller:validateTotal", "compositeConfig": {"amount": {"customFieldMessages": {"fieldError": "@state:totalErrorMsg"}}}, "isDisabled": true}, {"value": "@binding:expenseLine.businessUnitId", "label": "{{employeeExpense.businessUnit}}", "isDisabled": "@controller:isDisabled()", "visible": "@binding:businessUnitEnabled"}, {"value": "@binding:expenseLine.teamId", "label": "{{employeeExpense.team}}", "isDisabled": "@controller:isDisabled()"}, {"label": "{{employeeExpense.billableExpense}}", "value": "@binding:expenseLine.billableExpense", "onChange": "@controller:handleBillableExpenseChange"}, {"label": "{{employeeExpense.customer}}", "value": "@binding:expenseLine.customerId", "visible": "@controller:isCustomerVisible()"}, {"value": "@binding:expenseLine.vatPercentage", "label": "{{employeeExpense.vatPercentage}}", "visible": "@controller:isVATSelectionVisible()", "onChange": "@controller:handleVATPercentageChange", "isDisabled": "@controller:isDisabledIfPaid()", "semanticTypeUiParams": "@controller:getVATPercentageUiParams()"}, {"value": "@binding:expenseLine.vatAmount", "label": "{{employeeExpense.vatAmount}}", "isDisabled": "@controller:isDisabledIfPaid()", "visible": "@controller:isVATSelectionVisible()"}, {"value": "@binding:expenseLine.fullNetAmount", "isDisabled": "@controller:isTotalDisabled()", "visible": "@controller:isVATSelectionVisible()"}, {"component": "Input", "size": "3", "label": "{{employeeExpense.description}}", "value": "@bindingController(expenseLine.description, getDescription())"}]}]}, {"component": "FieldGroup", "visible": "@controller:isPlainBlockVisible()", "size": "12", "columns": "3", "type": "secondary", "editing": false, "elements": [{"component": "DatePicker", "label": "{{employeeExpense.date}}", "parseAs": "date", "format": "P", "value": "@bindingController(expenseLine.billDate, getDate())"}, {"component": "Input", "label": "{{employeeExpense.merchant.name}}", "value": "@bindingController(expenseLine.merchant, getMerchant())"}, {"component": "Input", "label": "{{employeeExpense.expenseType}}", "value": "@binding:expenseLine.expenseType"}, {"component": "Select", "label": "{{employeeExpense.category}}", "list": "@binding:expenseCategories", "idProp": "id", "textProp": "category", "value": "@bindingController(expenseLine.categoryId, getCategory())"}, {"component": "Select", "label": "{{employeeExpense.reimbursable}}", "value": "@bindingController(expenseLine.reimbursable, getReimbursable())", "text": "@controller:getReimbursableText()", "list": "@controller:getReimbursableList()", "visible": "@controller:isCheckboxVisible()"}, {"component": "Select", "label": "{{employeeExpense.currency}}", "value": "@bindingController(expenseLine.currency, getCurrency())"}, {"component": "Input", "label": "{{employeeExpense.total}}", "value": "@binding:expenseLine.amount"}, {"label": "{{employeeExpense.billableExpense}}", "value": "@binding:expenseLine.billableExpense"}, {"label": "{{employeeExpense.customer}}", "value": "@binding:expenseLine.customerId", "visible": "@controller:isCustomerVisible()"}, {"value": "@binding:expenseLine.teamId", "label": "{{employeeExpense.team}}"}, {"value": "@binding:expenseLine.vatPercentage", "label": "{{employeeExpense.vatPercentage}}", "visible": "@controller:isVATSelectionVisible()", "onChange": "@controller:handleVATPercentageChange", "semanticTypeUiParams": "@controller:getVATPercentageUiParams()"}, {"value": "@binding:expenseLine.vatAmount", "label": "{{employeeExpense.vatAmount}}", "visible": "@controller:isVATSelectionVisible()"}, {"value": "@binding:expenseLine.fullNetAmount", "visible": "@controller:isVATSelectionVisible()"}, {"component": "Input", "size": "2", "label": "{{employeeExpense.description}}", "value": "@bindingController(expenseLine.description, getDescription())"}]}, {"component": "<PERSON><PERSON>", "size": "12", "title": "Warning", "content": "{{expense.category.error.distanceCategoryNotAvailable}}", "visible": "@controller:isAlertVisible()", "action": {"label": "{{expense.category.navigateToCategoriesPage}}", "onClick": "@controller:openCategoriesPage"}}]}, "actions": {"content": [{"variant": "primary", "label": "@controller:getPrimaryButtonLabel()", "align": "right", "visible": "@controller:isPrimaryButtonVisible()", "disabled": "@controller:isPrimaryButtonDisabled()", "onClick": "@controller:saveAction", "actions": "@controller:getMoreActions()"}]}}}