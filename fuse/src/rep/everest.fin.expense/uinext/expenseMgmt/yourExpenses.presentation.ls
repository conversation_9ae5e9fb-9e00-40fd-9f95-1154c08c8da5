package everest.fin.expense

template presentation yourExpenses {

    object data-source currentEmployee {
        shape {
            id (visible: false): field<everest.hr.base::Employee.id>
            bankDetailsId (visible: false): field<everest.hr.base::Employee.bankDetailsId>
            entityId (visible: false): field<everest.hr.base::Employee.entityId>
        }
    }

    /**
     * Data sources can be used to create control flags that are used in the ui, without exposing unnecessary data to the uicontroller.
     */

    object data-source globalAlerts with (currentEmployee=currentEmployee){
        shape {
            employeeRecordNotFound (visible: false): TrueFalse
            bankDetailsNotFound (visible: false): TrueFalse
        }
    }

    object data-source expenseItemsStatistics with (currentEmployeeId=currentEmployee.id){
        shape {
            unsubmittedExpenseItemsCount: Number<Int>
        }
    }

    array data-source expenseItems with (currentEmployeeId=currentEmployee.id) {
        shape {
            id: field<ExpenseItemBase.id>
            expenseItemNumber: field<ExpenseItemBase.expenseItemNumber>
            expenseType: field<ExpenseItemBase.expenseType>
            billDate: field<ExpenseItemBase.billDate>
            merchantAndDescription: field<ExpenseItemBase.merchantAndDescription>
            reimbursableAmount: field<ExpenseItemBase.reimbursableAmount>
            category: Text
            teamId: field<ExpenseItemBase.teamId>
            receiptStatus: field<ExpenseItemBase.receiptStatus>
            currency: field<ExpenseItemBase.currency>
            amount: field<ExpenseItemBase.amount>
        }
        routine deleteExpenseItemsRoutine {
            inputs {
                selectedItems: array<object<{
                    id: field<ExpenseItemBase.id>
                }>>
            }
            properties {
                side-effects true
            }
        }
    }

    array data-source expenseItemsDrafts with (currentEmployeeId=currentEmployee.id) {
        shape {
            id: field<ExpenseItemBase.id>
            expenseItemNumber: field<ExpenseItemBase.expenseItemNumber>
            billDate: field<ExpenseItemBase.billDate>
            merchantAndDescription: field<ExpenseItemBase.merchantAndDescription>
            category: Text
            teamId: field<ExpenseItemBase.teamId>
            receiptStatus: field<ExpenseItemBase.receiptStatus>
            currency: field<ExpenseItemBase.currency>
            amount: field<ExpenseItemBase.amount>
        }
    }

    object data-source expenseReportsStatistics with (currentEmployeeId=currentEmployee.id) {
        shape {
            openExpenseReportsCount: Number<Int>
            reportsPendingApproval: Number<Int>
            approvedReports: Number<Int>
            rejectedReports: Number<Int>
            moneyOwed: Amount

            rejectedReportsIcon (visible: false): Text
        }
    }

    array data-source expenseReports with (currentEmployeeId=currentEmployee.id) {
        shape {
            id: field<ExpenseReportBase.id>
            expenseNumber: field<ExpenseReportBase.expenseNumber>
            submittedDate: field<ExpenseReportBase.submittedDate>
            employeeId: field<ExpenseReportBase.employeeId>
            status: field<ExpenseReportBase.status>
            paymentStatus: field<ExpenseReportBase.paymentStatus>
            reimbursementCurrency: field<ExpenseReportBase.reimbursementCurrency>
            reimbursableTotal: field<ExpenseReportBase.reimbursableTotal>
        }
    }

    struct currentEmployee {
        data currentEmployee

        fields id, bankDetailsId, entityId
    }

    struct globalAlerts {
        data globalAlerts
        fields *
    }

    struct expenseItemsStatistics {
        data expenseItemsStatistics
        fields *
    }

    list unsubmittedExpenseItems {
        data expenseItems
        fields *
    }

    list expenseItemDrafts {
        data expenseItemsDrafts
        fields *
    }

    struct expenseReportsStatistics {
        data expenseReportsStatistics
        fields *
    }

    list expenseReports {
        data expenseReports
        fields *
    }

    custom action processScannedFiles {
        inputs {
            files: JSON
        }
        properties {
            affected-data-sets expenseItemDrafts
            side-effects true
        }
    }

    custom action getSelectedExpenseItems {
        inputs {
            selectedItems = "$unsubmittedExpenseItems[selected].{id}$"
        }
        outputs {
            ids: Array<Id>
        }
        properties {
            side-effects false
        }
    }

    custom action deleteExpenseItems {
        inputs {
            selectedItems = "$unsubmittedExpenseItems[selected].{id}$"
        }
        properties {
            affected-data-sets unsubmittedExpenseItems, expenseItemsStatistics
            side-effects true
        }
    }

    custom action deleteExpenseItemsDrafts {
        inputs {
            selectedItems = "$expenseItemDrafts[selected].{id}$"
        }
        properties {
            affected-data-sets expenseItemDrafts
            side-effects true
        }
    }

    mode view {
        on expenseItemsStatistics allow view
        on unsubmittedExpenseItems allow view
        on expenseItemDrafts allow view
        on expenseReportsStatistics allow view
        on expenseReports allow view
        on currentEmployee allow view
        on globalAlerts allow view
        allow actions processScannedFiles, deleteExpenseItems, deleteExpenseItemsDrafts,

            deleteEI, getSelectedExpenseItems
    }

    delegate action deleteEI to data-source<expenseItems>.deleteExpenseItemsRoutine {
        inputs {
            selectedItems = "$unsubmittedExpenseItems[selected].{id}$"
        }
        properties {
            affected-data-sets unsubmittedExpenseItems, expenseItemsStatistics
        }
    }
}
