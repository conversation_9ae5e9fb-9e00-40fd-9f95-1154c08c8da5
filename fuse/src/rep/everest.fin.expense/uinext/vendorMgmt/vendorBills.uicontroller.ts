// @i18n:vendorMgmt
import type { RowType } from '@pkg/everest.base/public/ui-types/TransformedUIParam.uicontroller';
import { EvstStatusOfPayment } from '@pkg/everest.fin.base/types/enums/StatusOfPayment';
import { EvstVendorBillStatus } from '@pkg/everest.fin.base/types/enums/VendorBillStatus';
import type { BillExtractionResponse } from '@pkg/everest.fin.expense/actions/common/codeLists';
import { isError } from '@pkg/everest.fin.expense/actions/common/codeLists';
import type { VendorBillsUiTemplate } from '@pkg/everest.fin.expense/types/uiTemplates/uinext/vendorMgmt/vendorBills.ui';
import { VendorBillHeaderBaseUI } from '@pkg/everest.fin.expense/types/VendorBillHeaderBase.ui';

type SingleNodeItemType = VendorBillsUiTemplate.SingleNodeItemType;
type VendorBillsContext = VendorBillsUiTemplate.VendorBillsContext;

export type Context = VendorBillsContext & {
  state: {
    selectedRows: VendorBillRow['data'][];
  };
};

export type VendorBillRow = RowType<SingleNodeItemType['vendorBills']>;

export function navigateToCreateBill({ helpers }: Context) {
  helpers.navigate({
    to: `/templates/everest.fin.expense/uinext/vendorMgmt/createVendorBill`,
    queryParams: {
      'feat-delta': true,
    },
  });
}

export function isMigrationModeAvailable(context: Context) {
  const { data } = context;
  const availableModes =
    data?.vendorBills?.filter((vendor) => {
      return vendor?.migrationConfigurationMode;
    }) ?? [];
  return availableModes.length === 0;
}

export function getRowBackground(context: Context, row) {
  const { state } = context;
  const migrationStatus = row?.rowData?.migrationConfigurationMode;
  if (migrationStatus === 'archive' && state?.dataExtractionId) {
    return 'shamrock';
  } else if (migrationStatus === 'migration' && state?.dataExtractionId) {
    return 'ocean-blue';
  }
  return 'light';
}

export function navigateToBill(
  { helpers }: Context,
  row: VendorBillRow['data']
) {
  const { isDraft } = row?.$metadata ?? {};
  const { vendorId = 0 } = row ?? {};
  if (isDraft) {
    helpers.navigate({
      to: `/templates/everest.fin.expense/uinext/vendorMgmt/createVendorBill?id=${row.id}`,
      initialState: { vendorId },
    });
  } else {
    helpers.navigate({
      to: `/templates/everest.fin.expense/uinext/vendorMgmt/vendorBill?id=${row.id}`,
      queryParams: { 'feat-delta': true },
      initialState: { featureToggles: { delta: true } },
    });
  }
}

export function billNumberValueGetter(
  _context: Context,
  params: VendorBillRow
) {
  const { isDraft } = params?.data?.$metadata ?? {};
  const { vendorBillHeaderNumber } = params?.data ?? {};
  return isDraft ? null : vendorBillHeaderNumber;
}

export function openJournalEntry(
  { helpers }: Context,
  { data }: VendorBillRow
) {
  const id = data?.journalEntryId;
  if (id) {
    helpers.navigate({
      to: `@uicode:journalEntry?id=${id}`,
    });
  }
}

export function paySelectedBillsDisabled({ state }: Context) {
  const bills = state.selectedRows ?? [];
  let payDisabled = false;
  for (const bill of bills) {
    if (
      bill.paymentStatus === EvstStatusOfPayment.FullyPaid ||
      bill.status !== EvstVendorBillStatus.Approved ||
      bill.$metadata.isDraft
    ) {
      payDisabled = true;
    }
  }
  return bills.length === 0 || payDisabled;
}

export function uploadAndScanDisabled({ state }: Context) {
  return state.uploadAndScanDisabled ?? false;
}

export function _onRowSelectionChanged({ state }: Context, selectedRows = []) {
  state.selectedRows = selectedRows.map((row: VendorBillRow) => row.data);
}

export function paySelectedBills({ helpers, state }: Context) {
  // selected bills
  const vendorBills = state.selectedRows;

  const ids = vendorBills.map((vb) => vb.id);
  const entityCurrencyMap = Object.fromEntries(
    vendorBills.reduce((entityCurrencyMap, vb) => {
      if (!entityCurrencyMap?.has(vb.entityId)) {
        entityCurrencyMap?.set(vb.entityId, []);
      }
      entityCurrencyMap?.get(vb.entityId)?.push(vb.currency);
      return entityCurrencyMap;
    }, new Map())
  );

  const entities = new Set(Object.keys(entityCurrencyMap));
  const currencies = new Set(Object.values(entityCurrencyMap).flat());

  if (entities.size > 1 || currencies.size > 1) {
    const title =
      entities.size > 1 && currencies.size > 1
        ? '{{payment.multipleEntitiesAndMultipleCurrencies}}'
        : entities.size > 1
          ? '{{payment.multipleEntities}}'
          : '{{payment.multipleCurrencies}}';

    const message =
      entities.size > 1 && currencies.size > 1
        ? '{{payment.multipleEntitiesAndMultipleCurrencies.message}}'
        : entities.size > 1
          ? '{{payment.multipleEntities.message}}'
          : '{{payment.multipleCurrencies.message}}';

    helpers.showToast({
      type: 'warning',
      title,
      message,
    });
  } else {
    const batch = ids.length > 1;
    helpers.navigate({
      to: `/templates/everest.fin.expense/payment/uinext/payment?mode=bill&ids=${ids}&batch=${batch}`,
    });
  }
}

export function deleteSelectedBillsDisabled({ state }: Context) {
  const bills = state.selectedRows ?? [];
  let deleteDisabled = false;
  for (const bill of bills) {
    if (
      bill.paymentStatus === EvstStatusOfPayment.FullyPaid ||
      bill.paymentStatus === EvstStatusOfPayment.PartiallyPaid
    ) {
      deleteDisabled = true;
    }
  }
  return bills.length === 0 || deleteDisabled;
}

export function uploadAndScan(context: Context) {
  const { helpers } = context;
  helpers.openModal({
    title: '{{vendorMgmt.uploadAndScan}}',
    size: 'small',
    template:
      '/templates/everest.fin.expense/uinext/vendorMgmt/uploadVendorBillsModal',
    onModalSubmit: async (files) => {
      helpers.closeModal();
      await scanAndFill(context, files);
    },
  });
}

export function importFromCsv(context: Context) {
  const { helpers, actions } = context;
  helpers.openModal({
    size: 'xsmall',
    template:
      '/templates/everest.fin.expense/uinext/vendorMgmt/csvImport/importVendorBillsCsvModal',
    onModalSubmit: async () => {
      helpers.closeModal();
      await actions.refetchUiModelData();
    },
  });
}

export async function deleteSelectedBills(context: Context) {
  const { state, helpers, actions } = context;
  helpers.showNotificationMessage({
    key: 'deleteVB',
    type: 'loading',
    message: '{{vendorMgmt.deletingVendorBills}}',
    duration: 500,
  });

  const vendorBillIds = state.selectedRows.map((vb) => vb.id);
  const response = await VendorBillHeaderBaseUI.deleteVendorBills(context, {
    vendorBillIds,
  }).run('vendorBillExpense');

  if (response.error) {
    helpers.showNotificationMessage({
      key: 'deleteVB',
      type: 'error',
      message: '{{vendorMgmt.delete.error.message}}',
      duration: 3,
    });
  } else {
    helpers.showNotificationMessage({
      key: 'deleteVB',
      type: 'success',
      message: '{{vendorMgmt.successDeleteBills}}',
      duration: 5,
    });
    await actions.refetchUiModelData();
  }
}

export async function scanAndFill(
  context: Context,
  vendorBillAttachments: { fileId: string; fileName: string }[]
) {
  const { actions, helpers, state } = context;
  if (vendorBillAttachments.length > 0 && state) {
    let errorInScanning = false;
    state.uploadAndScanDisabled = true;
    helpers.showNotificationMessage({
      key: 'scanning',
      type: 'loading',
      message: '{{vendorMgmt.scanning.bill}}',
      duration: 500,
    });

    // TODO: https://github.com/everestsystems/content/issues/18687 - create custom action for bulk scanning
    // For each file scan and create draft
    for (const file of vendorBillAttachments) {
      const { fileId } = file ?? {};

      // Call action to extract text from receipt file
      const jobResponse: {
        vendorBillAttachments: BillExtractionResponse;
      } = await actions.run({
        vendorBillAttachments: {
          action: 'extractTextFromBill',
          data: { fileId },
        },
      });
      if (
        'error' in jobResponse ||
        isError(jobResponse.vendorBillAttachments)
      ) {
        helpers.showToast({
          title: 'Error',
          type: 'warning',
          message: '{{vendorMgmt.errorOCR.failed}}',
        });
        state.uploadAndScanDisabled = false;
        errorInScanning = true;
        break;
      }

      const extractedData = jobResponse.vendorBillAttachments;
      const vendorBillHeader = extractedData.vendorBillHeaderData;
      const vendorBillItems = [extractedData.vendorBillLineData];

      const response = await actions.run({
        vendorBillExpense: {
          action: 'upsertVendorBillV2',
          data: {
            vendorBillHeader,
            vendorBillItems,
            vendorBillAttachments: [file],
            options: {
              draft: true,
            },
          },
        },
      });
      if ('error' in response) {
        helpers.showToast({
          title: 'Error',
          message: '{{vendorMgmt.errorDraft.creation}}',
          type: 'error',
        });
        state.uploadAndScanDisabled = false;
        errorInScanning = true;
        break;
      }
    }
    if (errorInScanning) {
      helpers.showNotificationMessage({
        key: 'scanning',
        type: 'error',
        message: 'Failed',
        duration: 2,
      });
      return;
    } else {
      await actions.refetchUiModelData();
      helpers.showNotificationMessage({
        key: 'scanning',
        type: 'success',
        message: '{{vendorMgmt.scanComplete}}',
        duration: 2,
      });
      state.uploadAndScanDisabled = false;
      return;
    }
  }
}
