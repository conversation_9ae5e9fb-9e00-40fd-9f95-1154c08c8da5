import { getMockT } from '@pkg/everest.base/public/ui-types/mocks.ui';
import * as sharedStateUtils from '@pkg/everest.base/public/utils/ui/sharedStateUtils.ui';

import * as createVendorBill from './createVendorBill.uicontroller';
import * as utilities from './utilities.uicontroller';

type Context = createVendorBill.Context;

describe('everest.fin.expense/uinext/vendorMgmt/createVendorBill.uicontroller tests', () => {
  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('isScanButtonVisible', () => {
    it('should return true if state mode is "create" (visible)', () => {
      const context = getMockT<Context>({ state: { mode: 'create' } });
      expect(createVendorBill.isScanButtonVisible(context)).toBe(true);
    });
    it('should return false if state mode is not "create" (visible)', () => {
      const context = getMockT<Context>({ state: { mode: 'edit' } });
      expect(createVendorBill.isScanButtonVisible(context)).toBe(false);
    });
  });

  describe('getStatus', () => {
    it('should return null', () => {
      const context = getMockT<Context>({ state: { draft: 'no' } });
      expect(createVendorBill.getStatus(context)).toBeNull();
    });
  });

  describe('getDefaultVendor', () => {
    it('should return vendor ID if it exists in state', () => {
      const context = getMockT<Context>({
        data: { vendor: { id: 1 } },
        state: { vendorId: 1 },
      });
      expect(createVendorBill.getDefaultVendor(context)).toBe(1);
    });
    it('should return vendor ID from state if available', () => {
      const context = getMockT<Context>({
        data: {
          vendor: {
            id: 123,
            name: 'Vendor',
          },
        },
        state: { vendorId: 123 },
      });
      expect(createVendorBill.getDefaultVendor(context)).toBe(123);
    });
    it('should return vendor ID from data if not available in state', () => {
      const context = getMockT<Context>({
        data: { vendorBill: { vendorId: 456 } },
        state: {},
      });
      expect(createVendorBill.getDefaultVendor(context)).toBe(456);
    });
    it('should return undefined if vendor ID is not available in state or data', () => {
      const context = getMockT<Context>({
        data: {},
        state: {},
      });
      expect(createVendorBill.getDefaultVendor(context)).toBeUndefined();
    });
  });

  describe('onScanClick', () => {
    it('should handle scan click correctly when there is no file in upload component', async () => {
      const context = getMockT<Context>({
        state: {},
      });
      const functions = {
        getUploadFiles: jest.fn().mockReturnValue([]),
      };
      context.sections.bills = functions;

      const scanAndFillMock = jest
        .spyOn(createVendorBill, 'scanAndFill')
        .mockImplementation(() => Promise.resolve(undefined));

      await createVendorBill.onScanClick(context);

      expect(scanAndFillMock).toHaveBeenCalledTimes(0);
      expect(context.helpers.openModal).toHaveBeenCalledWith({
        title: '{{vendorBill.uploadAndScan}}',
        size: 'small',
        template:
          '/templates/everest.fin.expense/uinext/vendorMgmt/uploadVendorBillsModal',
        initialState: {
          singleFileOnly: true,
        },
        onModalSubmit: expect.any(Function),
      });

      // @ts-ignore
      const callParams = context.helpers.openModal.mock.calls[0][0];

      const onModalSubmitSpyOn = jest.spyOn(callParams, 'onModalSubmit');
      await onModalSubmitSpyOn([{ fileId: 12 }]);

      expect(context.helpers.closeModal).toHaveBeenCalledTimes(1);
      expect(scanAndFillMock).toHaveBeenCalledTimes(1);
      expect(scanAndFillMock).toHaveBeenCalledWith(context);
      expect(context.state.attachments).toStrictEqual([{ fileId: 12 }]);
    });

    it('should handle scan click correctly when there is one file in upload component', async () => {
      const context = getMockT<Context>({
        state: {},
      });
      const functions = {
        getUploadFiles: jest.fn().mockReturnValue([{ id: 4 }]),
      };
      context.sections.bills = functions;

      const scanAndFillMock = jest
        .spyOn(createVendorBill, 'scanAndFill')
        .mockImplementation(() => Promise.resolve(undefined));

      await createVendorBill.onScanClick(context);

      expect(context.helpers.openModal).toHaveBeenCalledWith({
        size: 'xsmall',
        template:
          '/templates/everest.fin.expense/uinext/vendorMgmt/vendorBillScanConfirmationModal',
        initialState: {
          hasMoreThanOneFile: false,
        },
        onModalSubmit: expect.any(Function),
      });

      // @ts-ignore
      const callParams = context.helpers.openModal.mock.calls[0][0];

      const openUploadFileModalMock = jest
        .spyOn(createVendorBill, 'openUploadFileModal')
        .mockImplementation(() => {});

      const onModalSubmitSpyOn = jest.spyOn(callParams, 'onModalSubmit');

      await onModalSubmitSpyOn('attachedFile');
      expect(context.helpers.closeModal).toHaveBeenCalledTimes(1);
      expect(openUploadFileModalMock).toHaveBeenCalledTimes(0);
      expect(scanAndFillMock).toHaveBeenCalledTimes(1);
      expect(scanAndFillMock).toHaveBeenCalledWith(context);

      await onModalSubmitSpyOn('newFile');
      expect(context.helpers.closeModal).toHaveBeenCalledTimes(2);
      expect(openUploadFileModalMock).toHaveBeenCalledTimes(1);
      expect(openUploadFileModalMock).toHaveBeenCalledWith(context);
      expect(scanAndFillMock).toHaveBeenCalledTimes(1);
    });

    it('should handle scan click correctly when there is more than one file in upload component', async () => {
      const context = getMockT<Context>({
        state: {},
      });
      const functions = {
        getUploadFiles: jest.fn().mockReturnValue([{ id: 4 }, { id: 5 }]),
      };
      context.sections.bills = functions;

      await createVendorBill.onScanClick(context);

      expect(context.helpers.openModal).toHaveBeenCalledWith({
        size: 'xsmall',
        template:
          '/templates/everest.fin.expense/uinext/vendorMgmt/vendorBillScanConfirmationModal',
        initialState: {
          hasMoreThanOneFile: true,
        },
        onModalSubmit: expect.any(Function),
      });
    });
  });

  describe('scanAndFill', () => {
    it('should open a dialog and show error if jobResponse is error', async () => {
      const context = getMockT<Context>({
        state: {
          attachments: [{ id: 4 }],
        },
        helpers: {
          openDialog: jest.fn(),
          closeModal: jest.fn(),
          showToast: jest.fn(),
        },
      });
      context.actions.run = jest.fn().mockResolvedValue({
        error: 'Error Occurred',
      });

      await createVendorBill.scanAndFill(context);

      expect(context.helpers.openDialog).toHaveBeenCalled();
      expect(context.actions.run).toHaveBeenCalled();
      expect(context.helpers.showToast).toHaveBeenCalledWith(
        expect.objectContaining({
          title: 'Error',
          message: 'Seems some problem in scanning.',
          type: 'error',
        })
      );
      expect(context.helpers.closeModal).toHaveBeenCalled();
    });
    it('should open a dialog and complete scan if jobResponse is valid', async () => {
      const context = getMockT<Context>({
        state: {
          attachments: [{ fileId: '123' }],
        },
        helpers: {
          openDialog: jest.fn(),
          closeModal: jest.fn(),
          showToast: jest.fn(),
        },
        data: {
          vendorBillLines: [{}],
        },
      });
      context.actions.run = jest.fn().mockResolvedValue({
        vendorBillAttachment: {
          vendorBillHeaderData: {
            billDate: '2022-04-12T00:00:00.000Z',
            billNumber: '***********',
            currency: 'EUR',
            vendorId: 101,
            businessPartnerId: 102,
            total: 500.9,
            entityId: 103,
            paymentTerms: 'net30',
            poNumber: '1234',
          },
          vendorBillLineData: {
            amount: 500.9,
            description: 'Gaming equipment',
          },
        },
      });

      context.sections.bills = { addRow: jest.fn() };

      const setUIModelInSharedStateMock = jest
        .spyOn(sharedStateUtils, 'setUIModelInSharedState')
        .mockImplementation(() => {});
      // Mock the setNewVendorDefaultValues function
      const setNewVendorDefaultValuesMock = jest
        .spyOn(utilities, 'setNewVendorDefaultValues')
        .mockImplementation(() => Promise.resolve());
      await createVendorBill.scanAndFill(context);

      expect(context.helpers.openDialog).toHaveBeenCalled();
      expect(context.actions.run).toHaveBeenCalled();
      expect(setUIModelInSharedStateMock).toHaveBeenCalledTimes(11);
      expect(setNewVendorDefaultValuesMock).toHaveBeenCalledWith(context, 101);
      expect(context.helpers.closeModal).toHaveBeenCalled();
    });
  });

  describe('getAttachments', () => {
    it('returns empty list if no attachments in data', () => {
      const context = getMockT<Context>({
        data: {},
        helpers: {},
      });
      context.form.getFormValues = jest.fn().mockReturnValue({ bill: [] });
      const ret = createVendorBill.getAttachments(context);
      expect(ret).toEqual([]);
    });

    it('returns state attachments if exists', () => {
      const context = getMockT<Context>({
        state: {
          attachments: [{ fileId: '321' }, { fileId: '654' }],
        },
        data: {
          vendorBillAttachments: [
            {
              fileId: '123',
            },
            {
              fileId: '456',
            },
          ],
        },
      });
      context.form.getFormValues = jest.fn().mockReturnValue({ bill: [] });

      const ret = createVendorBill.getAttachments(context);

      expect(ret).toEqual([
        {
          fileId: '321',
        },
        {
          fileId: '654',
        },
      ]);
    });

    it('returns attachment list', () => {
      const context = getMockT<Context>({
        data: {
          vendorBillAttachments: [
            {
              fileId: '123',
            },
            {
              fileId: '456',
            },
          ],
        },
      });
      context.form.getFormValues = jest.fn().mockReturnValue({ bill: [] });

      const ret = createVendorBill.getAttachments(context);

      expect(ret).toEqual([]);
    });
  });

  describe('isActionDisabled', () => {
    it('checks if the action is not disabled', () => {
      const context = getMockT<Context>({
        state: {
          clickedOnce: true,
        },
      });
      const ret = createVendorBill.isActionDisabled(context);
      expect(ret).toBe(true);
    });
    it('returns true if account data is loading', () => {
      const context = getMockT<Context>({
        state: {
          clickedOnce: false,
          isFetchingAccountData: true,
        },
      });
      const ret = createVendorBill.isActionDisabled(context);
      expect(ret).toBe(true);
    });
    it('checks if the action is disabled', () => {
      const context = getMockT<Context>({
        state: {
          clickedOnce: false,
          canBePosted: true,
        },
      });
      const ret = createVendorBill.isActionDisabled(context);
      expect(ret).toBeFalsy();
    });
    it('returns true if canBePosted(state attribute) is false', () => {
      const context = getMockT<Context>({
        state: {
          canBePosted: false,
        },
      });
      const ret = createVendorBill.isActionDisabled(context);
      expect(ret).toBe(true);
    });
  });

  describe('saveButtonAction', () => {
    it('checks if createVendorBill is called after save action', async () => {
      const context = getMockT<Context>({});
      const mockCreatVendorBill = jest
        .spyOn(utilities, 'createVendorBill')
        .mockImplementation(async () => {});
      await createVendorBill.saveButtonAction(context);
      expect(mockCreatVendorBill).toHaveBeenCalledWith(context, false);
    });
  });

  describe('deleteDraft', () => {
    it('checks for the complete deleteDraft functionality', async () => {
      const context = getMockT<Context>({
        state: {
          id: 123,
        },
        data: {
          vendorBill: {
            vendorId: '1001',
            businessPartnerId: '1002',
          },
        },
        actions: {
          run: jest.fn(),
        },
        helpers: {
          navigate: jest.fn(),
          showNotificationMessage: jest.fn(),
        },
      });
      await createVendorBill.deleteDraft(context);
      //TODO: Vendor draft is disabled
      /* expect(context.actions.run).toHaveBeenCalledWith(
        expect.objectContaining({
          businessPartner: {
            action: 'delete',
            data: { id: '1002' },
            options: { draft: true },
          },
          vendors: {
            action: 'delete',
            data: { id: '1001' },
            options: { draft: true },
          },
          vendorBill: {
            action: 'delete',
            data: { id: 123 },
            options: { draft: true },
          },
        }),
        true
      ); */
      expect(context.helpers.navigate).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '/templates/everest.fin.expense/uinext/vendorMgmt/vendorBills',
          closeCurrentTab: true,
        })
      );
      expect(context.helpers.showNotificationMessage).toHaveBeenCalledWith(
        expect.objectContaining({
          key: 'draftDeleted',
          type: 'success',
          message: 'Draft deleted successfully.',
        })
      );
    });
  });

  describe('saveDraft', () => {
    it('checks if correct function is called in saveDraft', async () => {
      const context = getMockT<Context>({});
      const mock1 = jest
        .spyOn(utilities, 'createVendorBill')
        .mockImplementation(async () => {});
      await createVendorBill.saveDraft(context);
      expect(mock1).toHaveBeenCalledWith(context, true);
    });
  });

  describe('openCreateVendorModal', () => {
    it('should open the create vendor modal and handle the submit correctly', async () => {
      const context = getMockT<Context>({
        data: {
          vendorBill: {},
        },
      });

      createVendorBill.openCreateVendorModal(context);

      expect(context.helpers.openModal).toHaveBeenCalledTimes(1);
      expect(context.helpers.openModal).toHaveBeenCalledWith({
        size: 'medium',
        template:
          '/templates/everest.fin.expense/uinext/vendorMgmt/createVendor?mode=create',
        onModalSubmit: expect.any(Function),
      });

      // @ts-ignore
      const callParams = context.helpers.openModal.mock.calls[0][0];

      const setUIModelInSharedStateMock = jest
        .spyOn(sharedStateUtils, 'setUIModelInSharedState')
        .mockImplementation(() => {});

      const onModalSubmitSpyOn = jest.spyOn(callParams, 'onModalSubmit');
      await onModalSubmitSpyOn({ id: 45 });

      expect(context.helpers.closeModal).toHaveBeenCalledTimes(1);
      expect(setUIModelInSharedStateMock).toHaveBeenCalledTimes(1);
      expect(setUIModelInSharedStateMock).toHaveBeenCalledWith(
        context,
        'vendorBill',
        'vendorId',
        45
      );
      expect(context.actions.refetchUiModelData).toHaveBeenCalledTimes(1);
    });
  });
});
