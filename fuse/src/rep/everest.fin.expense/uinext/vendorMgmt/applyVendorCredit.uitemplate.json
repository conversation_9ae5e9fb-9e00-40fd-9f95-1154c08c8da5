{"version": 2, "uicontroller": ["everest.fin.expense/uinext/vendorMgmt/utilities.uicontroller.ts", "applyVendorCredit.uicontroller.ts"], "uimodel": {"state": {"mode": "create"}, "nodes": {"entities": {"type": "list", "modelId": "everest.base/EntityModel.Entity", "queryActiveEntities": {"query": {"where": {}, "orderBy": ["entityName"]}, "fieldList": ["entityName", "id", "currency"]}}, "postingPeriod": {"type": "struct", "modelId": "everest.fin.accounting/AccountingPeriodModel.AccountingPeriod"}, "vendor": {"type": "struct", "modelId": "everest.fin.expense/VendorModel.Vendor", "fieldList": ["id"]}, "vendorCredits": {"type": "list", "modelId": "everest.fin.expense/VendorCreditHeaderModel.VendorCreditHeader", "query": "@controller:getVendorCreditsQuery()", "fieldList": ["id", "creditDate", "vendorName", "vendorId", "currency", "entityId", "entityName", "referenceNumber", "totalAmount", "status", "totalAmountApplied", "totalBalance", "vendorCreditNumber"]}, "vendorCreditBill": {"type": "struct", "modelId": "everest.fin.expense/VendorCreditBillModel.VendorCreditBill", "fieldList": ["appliedDate", "appliedAmount", "vendorBillHeaderId"]}, "vendorBills": {"type": "list", "queryVendorBills": "@controller:getBillsQuery()", "modelId": "everest.fin.expense/VendorBillHeaderBaseModel.VendorBillHeaderBase", "fieldList": ["currency", "openAmount"]}, "vendorCredit": {"type": "struct", "modelId": "everest.fin.expense/VendorCreditHeaderModel.VendorCreditHeader", "fieldList": ["vendorId", "currency"]}}}, "uiview": {"layout": "everest.base.ui/layouts/uilayout/landing", "props": {"i18n": ["vendorMgmt"], "title": "{{vendorMgmt.applyVendorCredit}}", "stretch": true, "config": {"stretch": true}, "mainBlock": {"columns": "4", "type": "primary", "customId": "applyVendorCreditFieldGroup", "isEditing": true, "elements": [{"component": "Select", "label": "{{vendorMgmt.vendor}}", "idProp": "id", "textProp": "vendorName", "value": "@bindingController(vendorCredit.vendorId, getDefaultVendor())", "allowClear": false, "action": "create", "onChange": "@controller:setNewVendorDefaultValues", "placeholder": "{{placeHolder.selectVendor}}"}, {"component": "Select", "label": "{{vendorMgmt.entity}}", "idProp": "id", "textProp": "entityName", "list": "@binding:entities", "onChange": "@controller:setEntityDefaults", "value": "@controller:getEntity()", "action": "create", "placeholder": "{{placeHolder.selectEntity}}"}, {"label": "{{vendorMgmt.applicationDate}}", "parseAs": "date", "format": "P", "value": "@bindingController(vendorCreditBill.appliedDate, getDefaultDate())", "onChange": "@controller:setPostingPeriod", "action": "create", "placeholder": "{{placeHolder.selectBillDate}}"}, {"component": "Select", "isDisabled": true, "label": "{{vendorMgmt.postingPeriod}}", "isVisible": true, "idProp": "value", "textProp": "text", "value": "@bindingController(postingPeriod.id, getPostingPeriodId())", "list": "@controller:getPostingPeriods()"}]}, "secondaryContent": {"component": "SectionGroup", "size": "3", "sections": [{"component": "SummaryText", "size": "12", "date": {}, "summary": {"label": "{{vendorMgmt.totalCreditsApplied}}", "parseAs": "currency", "value": "@controller:getTotalCreditApplied()"}, "fields": [{"label": "{{vendorMgmt.remainingCreditsBalance}}", "parseAs": "currency", "value": "@controller:getRemainingBalance()"}]}]}, "customSections": [{"component": "Table", "customId": "vendorCreditsTable", "section": {"grid": {"size": "9"}, "title": "{{vendorMgmt.availableVendorCredits}}", "filter": {"suppressFields": ["id", "vendorName", "vendorId", "entityName", "totalAmount", "totalAmountApplied"]}}, "props": {"variant": "white-borderless", "showRowCount": true, "rowSelection": true, "onRowSelectionChanged": "@controller:_onRowSelectionChanged", "editing": false, "addRows": false, "columns": [{"headerName": "{{vendorMgmt.status}}", "field": "status", "hide": true}, {"headerName": "{{vendorMgmt.entityId}}", "field": "entityId", "hide": true}, {"headerName": "{{vendorMgmt.number}}", "field": "vendorCreditNumber", "fieldProps": {"fontWeight": "bold"}}, {"headerName": "{{vendorMgmt.referenceNumber}}", "field": "referenceNumber"}, {"headerName": "{{vendorMgmt.creditDate}}", "field": "creditDate"}, {"headerName": "{{vendorMgmt.currency}}", "field": "currency", "fieldProps": {"parseAs": "currency"}}, {"headerName": "{{vendorMgmt.remainingBalance}}", "field": "totalBalance"}], "data": "@binding:vendorCredits"}}, {"component": "Table", "size": "12", "variant": "dark", "title": "{{vendorMgmt.openVendorBills}}", "customId": "vendorBillsTable", "onTableLoaded": "@controller:fillDefaultAppliedAmount", "showRowCount": true, "editing": true, "suppressDelete": true, "addRows": false, "data": "@binding:vendorBills", "columns": [{"headerName": "{{vendorMgmt.number}}", "field": "vendorBillHeaderNumber", "fieldProps": {"fontWeight": "bold", "isEditing": false}}, {"headerName": "{{vendorMgmt.billNumber}}", "field": "bill<PERSON><PERSON><PERSON>", "fieldProps": {"isEditing": false}}, {"headerName": "{{vendorMgmt.billDate}}", "field": "billDate", "fieldProps": {"isEditing": false, "parseAs": "date"}}, {"headerName": "{{vendorMgmt.dueDate}}", "field": "dueDate", "fieldProps": {"isEditing": false, "parseAs": "date"}}, {"headerName": "{{vendorMgmt.currency}}", "field": "currency", "fieldProps": {"isEditing": false}}, {"headerName": "{{vendorMgmt.amount.due}}", "field": "openAmount", "fieldProps": {"isEditing": false}}, {"headerName": "{{vendorMgmt.appliedAmount}} *", "field": "appliedAmount", "valueGetter": "@controller:appliedHeaderAmountValueGetter", "type": "rightAligned", "fieldProps": {"component": "InputNumber", "type": "amount", "parseAs": "currency", "customFieldMessages": "@controller:validateAppliedAmountField"}}]}], "primaryAction": {"label": "{{vendorMgmt.apply}}", "onClick": "@controller:applyVendorCredit", "disabled": "@controller:isButtonDisabled()"}, "showSecondaryAction": false}}}