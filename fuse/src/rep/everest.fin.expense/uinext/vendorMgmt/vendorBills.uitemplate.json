{"version": 2, "uicontroller": "vendorBills.uicontroller.ts", "uimodel": {"nodes": {"vendorBills": {"type": "list", "query": {"orderBy": [{"field": "vendorBillHeaderNumber", "ordering": "desc"}]}, "options": {"draft": "include"}, "modelId": "everest.fin.expense/VendorBillHeaderBaseModel.VendorBillHeaderBase", "pagination": true, "fieldList": ["id", "vendorId", "bill<PERSON><PERSON><PERSON>", "entityId", "amountPaid", "currency", "billDate", "paymentStatus", "postingStatus", "containsPrepaidItem", "dueDate", "journalEntryNumber", "journalEntryId", "total", "status", "origin", "vendorBillHeaderNumber", "migrationConfigurationMode"]}, "vendorBillExpense": {"type": "struct", "modelId": "everest.fin.expense/VendorBillHeaderBaseModel.VendorBillHeaderBase", "fieldList": ["id", "vendorBillHeaderNumber"]}, "vendorBillAttachments": {"type": "struct", "modelId": "everest.fin.expense/VendorBillAttachmentBaseModel.VendorBillAttachmentBase"}, "postingPeriod": {"type": "struct", "modelId": "everest.fin.accounting/AccountingPeriodModel.AccountingPeriod", "fieldList": ["id", "closed", "locked"]}}}, "uiview": {"layout": "everest.base.ui/layouts/uilayout/list", "props": {"i18n": "vendorMgmt", "title": "{{vendorMgmt.vendorBill.plural}}", "customId": "vendorBillsTable", "onRowClicked": "@controller:navigateToBill", "data": "@binding:vendorBills", "node": "vendorBills", "removeDeleteAction": true, "rowBackgroundGetter": "@controller:getRowBackground", "filterConfig": {"postingStatus": {"showNone": false}}, "fixedFilters": ["vendorId", "entityId"], "omitFieldsFromColumns": ["journalEntryId"], "suppressFilterFields": ["amountPaid", "containsPrepaidItem", "dueDate", "journalEntryId"], "columns": [{"headerName": "{{vendorMgmt.number}}", "valueGetter": "@controller:billNumberValueGetter", "field": "vendorBillHeaderNumber", "sortable": true, "fieldProps": {"nullContent": "custom", "customNullContent": "Draft"}}, {"headerName": "{{vendorMgmt.vendor}}", "field": "vendorId", "sortable": true, "fieldProps": {"fontWeight": "bold"}}, {"headerName": "{{vendorMgmt.referenceNumber}}", "field": "bill<PERSON><PERSON><PERSON>", "sortable": true}, {"headerName": "{{vendorMgmt.currency}}", "field": "currency"}, {"headerName": "{{vendorMgmt.totalAmount}}", "field": "total", "fieldProps": {"parseAs": "currency"}}, {"headerName": "{{vendorMgmt.entity}}", "field": "entityId", "sortable": true}, {"headerName": "{{vendorMgmt.billDate}}", "field": "billDate", "fieldProps": {"fontWeight": "bold"}}, {"headerName": "{{vendorMgmt.approvalStatus}}", "field": "status", "fieldProps": {"nullContent": "dash"}, "cellVariant": {"variant": "badge", "matchers": {"Approved": "shamrock", "Pending Approval": "jaffa", "Cancelled": "danger", "default": "info"}}}, {"headerName": "{{vendorMgmt.paymentStatus}}", "field": "paymentStatus", "fieldProps": {"nullContent": "dash"}, "cellVariant": {"variant": "badge", "matchers": {"Fully Paid": "success", "Unpaid": "jaffa", "Partially Paid": "jaffa", "Pending": "jaffa", "default": "error"}}}, {"headerName": "{{vendorMgmt.postingStatus}}", "field": "postingStatus", "fieldProps": {"nullContent": "dash"}, "cellVariant": {"variant": "badge", "matchers": {"Posted": "success", "Unposted": "jaffa", "default": "error"}}}, {"headerName": "{{vendorMgmt.mode}}", "field": "migrationConfigurationMode", "fieldProps": {"nullContent": "dash"}, "initialHide": "@controller:isMigrationModeAvailable()", "cellVariant": {"variant": "badge", "matchers": {"Archive": "havelock-blue", "Migration": "sharpay", "default": "default"}}}, {"headerName": "{{vendorMgmt.origin}}", "field": "origin", "sortable": true}, {"headerName": "{{vendorMgmt.journalEntry}}", "field": "journalEntryNumber", "onCellClicked": "@controller:openJournalEntry"}], "createActions": [{"label": "{{vendorMgmt.createManually}}", "onClick": "@controller:navigateToCreateBill"}, {"label": "{{vendorMgmt.uploadAndScan}}", "onClick": "@controller:uploadAndScan", "disabled": "@controller:uploadAndScanDisabled()"}, {"label": "Import from CSV", "onClick": "@controller:importFromCsv"}], "customActions": [{"label": "{{vendorMgmt.pay.selected.bills}}", "onClick": "@controller:paySelectedBills", "disabled": "@controller:paySelectedBillsDisabled()"}, {"label": "{{vendorMgmt.delete}}", "onClick": "@controller:deleteSelectedBills", "disabled": "@controller:deleteSelectedBillsDisabled()", "confirmation": {"message": "{{vendorMgmt.bill.delete}}", "description": "{{vendorMgmt.billUnPaid.delete}}", "confirmLabel": "{{vendorMgmt.delete}}"}}]}}}