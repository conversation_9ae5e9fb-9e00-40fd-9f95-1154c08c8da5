import { getMockT } from '@pkg/everest.base/public/ui-types/mocks.ui';
import { EvstStatusOfPayment } from '@pkg/everest.fin.base/types/enums/StatusOfPayment';
import { EvstVendorBillStatus } from '@pkg/everest.fin.base/types/enums/VendorBillStatus';
import { VendorBillHeaderBaseUI } from '@pkg/everest.fin.expense/types/VendorBillHeaderBase.ui';

import * as vendorBills from './vendorBills.uicontroller';

type Context = vendorBills.Context;

function vendorBillRow(context: Context) {
  return { data: context.state.selectedRows[0] };
}

describe('navigateToCreateBill', () => {
  it('should navigate to create vendor bill page', () => {
    const context = getMockT<Context>({ helpers: { navigate: jest.fn() } });
    vendorBills.navigateToCreateBill(context);
    expect(context.helpers.navigate).toHaveBeenCalledWith({
      to: `/templates/everest.fin.expense/uinext/vendorMgmt/createVendorBill`,
      queryParams: {
        'feat-delta': true,
      },
    });
  });
});

describe('navigateToBill', () => {
  it('should navigate to create vendor bill page if it is draft', () => {
    const context = getMockT<Context>({
      helpers: { navigate: jest.fn() },
      state: {
        selectedRows: [
          {
            id: 402,
            $metadata: {
              isDraft: true,
            },
            vendorId: 55,
          },
        ],
      },
    });
    const vbr = vendorBillRow(context);
    vendorBills.navigateToBill(context, vbr['data']);
    expect(context.helpers.navigate).toHaveBeenCalledWith({
      to: `/templates/everest.fin.expense/uinext/vendorMgmt/createVendorBill?id=402`,
      initialState: { vendorId: 55 },
    });
  });
  it('should navigate to specific vendor bill page if it is not a draft', () => {
    const context = getMockT<Context>({
      helpers: { navigate: jest.fn() },
      state: {
        selectedRows: [
          {
            id: 402,
            $metadata: {
              isDraft: false,
            },
          },
        ],
      },
    });
    const vbr = vendorBillRow(context);
    vendorBills.navigateToBill(context, vbr['data']);
    expect(context.helpers.navigate).toHaveBeenCalledWith({
      to: `/templates/everest.fin.expense/uinext/vendorMgmt/vendorBill?id=402`,
      queryParams: { 'feat-delta': true },
      initialState: { featureToggles: { delta: true } },
    });
  });
});
describe('billNumberValueGetter', () => {
  it('should get bill number value', () => {
    const context = getMockT<Context>({
      state: {
        selectedRows: [
          {
            $metadata: {
              isDraft: false,
            },
            vendorBillHeaderNumber: 'VB-5',
          },
        ],
      },
    });
    const vbr = vendorBillRow(context);
    const ret = vendorBills.billNumberValueGetter(context, vbr);
    expect(ret).toStrictEqual('VB-5');
  });
  it('should get null bill number value', () => {
    const context = getMockT<Context>({
      state: {
        selectedRows: [
          {
            $metadata: {
              isDraft: true,
            },
            vendorBillHeaderNumber: 'VB-5',
          },
        ],
      },
    });
    const vbr = vendorBillRow(context);
    const ret = vendorBills.billNumberValueGetter(context, vbr);
    expect(ret).toBeNull();
  });
});
describe('openJournalEntry', () => {
  it('should navigate to journal entry page', () => {
    const context = getMockT<Context>({
      helpers: { navigate: jest.fn() },
      state: {
        selectedRows: [
          {
            journalEntryId: 1001,
          },
        ],
      },
    });
    const vbr = vendorBillRow(context);
    vendorBills.openJournalEntry(context, vbr);

    expect(context.helpers.navigate).toHaveBeenCalledWith({
      to: `@uicode:journalEntry?id=1001`,
    });
  });
});
describe('paySelectedBillsDisabled', () => {
  it('should disable pay selected bills when conditions are met', () => {
    const context = getMockT<Context>({
      state: {
        selectedRows: [
          {
            paymentStatus: EvstStatusOfPayment.FullyPaid,
            status: EvstVendorBillStatus.Approved,
            $metadata: { isDraft: false },
          },
        ],
      },
    });

    const result = vendorBills.paySelectedBillsDisabled(context);

    expect(result).toBeTruthy();
  });
});
describe('uploadAndScanDisabled', () => {
  it('should return upload and scan disabled state', () => {
    const context = getMockT<Context>({
      state: {
        uploadAndScanDisabled: true,
      },
    });

    const ret = vendorBills.uploadAndScanDisabled(context);

    expect(ret).toBe(true);
  });
});

describe('paySelectedBills', () => {
  it('should handle payment for selected bills (multiple currencies)', () => {
    const context = getMockT<Context>({
      helpers: {
        showToast: jest.fn(),
      },
      state: {
        selectedRows: [
          { entityId: 1, currency: 'EUR', id: 2001 },
          { entityId: 1, currency: 'USD', id: 2002 },
        ],
      },
    });

    vendorBills.paySelectedBills(context);
    // Check for multiple currencies warning
    expect(context.helpers.showToast).toHaveBeenCalledWith({
      type: 'warning',
      title: '{{payment.multipleCurrencies}}',
      message: '{{payment.multipleCurrencies.message}}',
    });
  });
  it('should handle payment for selected bills (multiple ids)', () => {
    const context = getMockT<Context>({
      helpers: {
        openModal: jest.fn(),
      },
      state: {
        selectedRows: [
          { entityId: 1, currency: 'EUR', id: 2001 },
          { entityId: 2, currency: 'EUR', id: 2002 },
        ],
      },
    });

    vendorBills.paySelectedBills(context);
    expect(context.helpers.showToast).toHaveBeenCalledWith({
      type: 'warning',
      title: '{{payment.multipleEntities}}',
      message: '{{payment.multipleEntities.message}}',
    });
  });
  it('should handle payment for selected bills (successful payment)', () => {
    const context = getMockT<Context>({
      helpers: {
        navigate: jest.fn(),
      },
      state: {
        selectedRows: [
          { entityId: 1, currency: 'EUR', id: 2001 },
          { entityId: 1, currency: 'EUR', id: 2002 },
        ],
      },
    });

    vendorBills.paySelectedBills(context);
    expect(context.helpers.navigate).toHaveBeenCalledWith({
      to: '/templates/everest.fin.expense/payment/uinext/payment?mode=bill&ids=2001,2002&batch=true',
    });
  });
});
describe('deleteSelectedBillsDisabled', () => {
  it('should disable delete selected bills when conditions are met', () => {
    const context = getMockT<Context>({
      state: {
        selectedRows: [{ paymentStatus: EvstStatusOfPayment.FullyPaid }],
      },
    });

    const result = vendorBills.deleteSelectedBillsDisabled(context);

    expect(result).toBeTruthy();
  });
});
describe('uploadAndScan', () => {
  it('should open the upload and scan modal', () => {
    const context = getMockT<Context>({
      helpers: {
        openModal: jest.fn(),
        closeModal: jest.fn(),
      },
    });

    vendorBills.uploadAndScan(context);
    expect(context.helpers.openModal).toHaveBeenCalledWith(
      expect.objectContaining({
        title: '{{vendorMgmt.uploadAndScan}}',
        size: 'small',
        template:
          '/templates/everest.fin.expense/uinext/vendorMgmt/uploadVendorBillsModal',
        onModalSubmit: expect.any(Function),
      })
    );
  });
});
describe('deleteSelectedBills', () => {
  it('should allow deletion when conditions are met', async () => {
    const context = getMockT<Context>({
      state: {
        selectedRows: [
          {
            id: 37,
          },
          {
            id: 38,
          },
        ],
      },
      data: {},
    });

    jest.spyOn(VendorBillHeaderBaseUI, 'deleteVendorBills').mockImplementation(
      () =>
        ({
          run: jest.fn().mockResolvedValue({
            vendorBillExpense: {},
          }),
        }) as any
    );

    await vendorBills.deleteSelectedBills(context);
    expect(VendorBillHeaderBaseUI.deleteVendorBills).toHaveBeenCalledWith(
      context,
      {
        vendorBillIds: [37, 38],
      }
    );
    expect(context.helpers.showNotificationMessage).toHaveBeenNthCalledWith(2, {
      key: 'deleteVB',
      type: 'success',
      message: '{{vendorMgmt.successDeleteBills}}',
      duration: 5,
    });
  });
});
describe('scanAndFill', () => {
  it('should show scanning notification', async () => {
    const context = getMockT<Context>({
      helpers: {
        showNotificationMessage: jest.fn(),
      },
      actions: {
        run: jest.fn(),
        refetchUiModelData: jest.fn(),
      },
      state: {
        uploadAndScanDisabled: false,
      },
    });
    const mockVendorBillAttachments = [
      {
        fileId: 'dfc3b4e6-340c-466c-8810-cef1a86d9fd3',
        fileName: 'file1.pdf',
      },
      {
        fileId: 'b270b7ce-8345-42f1-af69-7bdc19e9e735',
        fileName: 'file2.pdf',
      },
    ];
    try {
      await vendorBills.scanAndFill(context, mockVendorBillAttachments);
    } catch {
      /* empty */
    }
    expect(context.helpers.showNotificationMessage).toHaveBeenCalledWith(
      expect.objectContaining({
        key: 'scanning',
        type: 'loading',
        message: '{{vendorMgmt.scanning.bill}}',
      })
    );
  });
  it('should show show error when OCR fails', async () => {
    const context = getMockT<Context>({
      helpers: {
        showNotificationMessage: jest.fn(),
        showToast: jest.fn(),
      },
      actions: {
        refetchUiModelData: jest.fn(),
      },
      state: {
        uploadAndScanDisabled: false,
      },
    });
    context.actions.run = jest.fn().mockResolvedValue({
      error: 'OCR Failed',
    });
    const mockVendorBillAttachments = [
      {
        fileId: 'dfc3b4e6-340c-466c-8810-cef1a86d9fd3',
        fileName: 'file1.pdf',
      },
      {
        fileId: 'b270b7ce-8345-42f1-af69-7bdc19e9e735',
        fileName: 'file2.pdf',
      },
    ];
    await vendorBills.scanAndFill(context, mockVendorBillAttachments);
    expect(context.helpers.showToast).toHaveBeenCalledWith(
      expect.objectContaining({
        title: 'Error',
        type: 'warning',
        message: '{{vendorMgmt.errorOCR.failed}}',
      })
    );
    expect(context.state.uploadAndScanDisabled).toBeFalsy();
  });
  it('should read vendorBillAttachments after OCR succeeds', async () => {
    const context = getMockT<Context>({
      helpers: {
        showNotificationMessage: jest.fn(),
        showToast: jest.fn(),
      },
      actions: {
        refetchUiModelData: jest.fn(),
      },
      state: {
        uploadAndScanDisabled: false,
      },
    });
    const extractedData = {
      vendorBillHeaderData: {
        billDate: '2022-04-12T00:00:00.000Z',
        billNumber: '19202828282',
        currency: 'EUR',
        vendorId: '451110',
        total: 500.9,
        entityId: '2443140',
        paymentTerms: 'net30',
      },
      vendorBillLineData: {
        amount: 500.9,
        description: 'Gaming equipment',
      },
    };
    context.actions.run = jest.fn().mockImplementation((args) => {
      if (args?.vendorBillAttachments?.action === 'extractTextFromBill') {
        return Promise.resolve({
          vendorBillAttachments: extractedData,
        });
      } else if (args?.vendorBillExpense?.action === 'upsertVendorBillV2') {
        return Promise.resolve({
          vendorBillExpense: {
            vendorBillHeaderNumber: 'VB-15',
            id: '371399',
            $metadata: {
              isDraft: true,
            },
          },
        });
      }
    });
    const mockVendorBillAttachments = [
      {
        fileId: 'dfc3b4e6-340c-466c-8810-cef1a86d9fd3',
        fileName: 'file1.pdf',
      },
    ];
    await vendorBills.scanAndFill(context, mockVendorBillAttachments);
    expect(context.actions.run).toHaveBeenNthCalledWith(1, {
      vendorBillAttachments: {
        action: 'extractTextFromBill',
        data: { fileId: mockVendorBillAttachments[0].fileId },
      },
    });
    expect(context.actions.run).toHaveBeenNthCalledWith(2, {
      vendorBillExpense: {
        action: 'upsertVendorBillV2',
        data: {
          vendorBillHeader: extractedData.vendorBillHeaderData,
          vendorBillItems: [extractedData.vendorBillLineData],
          vendorBillAttachments: mockVendorBillAttachments,
          options: {
            draft: true,
          },
        },
      },
    });
    expect(context.actions.refetchUiModelData).toHaveBeenCalled();
    expect(context.helpers.showNotificationMessage).toHaveBeenCalledWith(
      expect.objectContaining({
        key: 'scanning',
        type: 'success',
        message: '{{vendorMgmt.scanComplete}}',
      })
    );
    expect(context.state.uploadAndScanDisabled).toBeFalsy();
  });
  it('should show error after OCR succeeds but upsert fails', async () => {
    const context = getMockT<Context>({
      helpers: {
        showNotificationMessage: jest.fn(),
        showToast: jest.fn(),
      },
      actions: {
        refetchUiModelData: jest.fn(),
      },
      state: {
        uploadAndScanDisabled: false,
      },
    });
    context.actions.run = jest.fn().mockImplementation((args) => {
      if (args?.vendorBillAttachments?.action === 'extractTextFromBill') {
        return Promise.resolve({
          vendorBillAttachments: {
            vendorBillHeaderData: {
              billDate: '2022-04-12T00:00:00.000Z',
              billNumber: '19202828282',
              currency: 'EUR',
              vendorId: '451110',
              total: 500.9,
              entityId: '2443140',
              paymentTerms: 'net30',
            },
            vendorBillLineData: {
              amount: 500.9,
              description: 'Gaming equipment',
            },
          },
        });
      } else if (args?.vendorBillExpense?.action === 'upsertVendorBillV2') {
        return Promise.resolve({
          error: 'Upsert Failed',
        });
      }
    });
    const mockVendorBillAttachments = [
      {
        fileId: 'dfc3b4e6-340c-466c-8810-cef1a86d9fd3',
        fileName: 'file1.pdf',
      },
    ];
    await vendorBills.scanAndFill(context, mockVendorBillAttachments);
    expect(context.helpers.showToast).toHaveBeenCalledWith(
      expect.objectContaining({
        title: 'Error',
        message: '{{vendorMgmt.errorDraft.creation}}',
        type: 'error',
      })
    );
    expect(context.state.uploadAndScanDisabled).toBeFalsy();
  });
});
