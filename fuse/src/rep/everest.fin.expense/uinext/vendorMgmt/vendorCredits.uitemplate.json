{"version": 2, "uicontroller": "vendorCredits.uicontroller.ts", "uimodel": {"nodes": {"vendorCredits": {"type": "list", "query": {"orderBy": [{"field": "vendorCreditNumber", "ordering": "desc"}]}, "options": {"draft": "include"}, "modelId": "everest.fin.expense/VendorCreditHeaderModel.VendorCreditHeader", "pagination": true, "fieldList": ["id", "creditDate", "vendorName", "vendorId", "currency", "entityId", "referenceNumber", "totalAmount", "status", "totalAmountApplied", "totalBalance", "vendorCreditNumber", "journalEntryId", "journalEntryNumber", "postingStatus", "approvalStatus", "origin", "migrationConfigurationMode"]}}}, "uiview": {"layout": "everest.base.ui/layouts/uilayout/list", "props": {"i18n": "vendorMgmt", "title": "{{vendorMgmt.vendorCredit.plural}}", "rowBackgroundGetter": "@controller:getRowBackground", "customId": "vendorCreditsTable", "onRowClicked": "@controller:navigateToCredit", "data": "@binding:vendorCredits", "node": "vendorCredits", "removeDeleteAction": true, "suppressFilterFields": ["vendorName", "journalEntryId", "totalAmountApplied", "totalBalance"], "omitFieldsFromColumns": ["vendorName"], "filterConfig": {"postingStatus": {"showNone": false}}, "columns": [{"headerName": "{{vendorMgmt.number}}", "valueGetter": "@controller:creditNumberValueGetter", "field": "vendorCreditNumber", "sortable": true, "fieldProps": {"nullContent": "custom", "customNullContent": "Draft"}}, {"headerName": "{{vendorMgmt.vendor}}", "field": "vendorId", "sortable": true, "fieldProps": {"fontWeight": "bold"}}, {"headerName": "{{vendorMgmt.referenceNumber}}", "field": "referenceNumber", "sortable": true}, {"headerName": "{{vendorMgmt.origin}}", "field": "origin", "sortable": true}, {"headerName": "{{vendorMgmt.entity}}", "field": "entityId", "sortable": true}, {"headerName": "{{vendorMgmt.creditDate}}", "field": "creditDate", "fieldProps": {"fontWeight": "bold"}, "cellVariant": {"variant": "icon", "iconSize": "small", "iconPosition": "left", "iconColor": "jaffa", "matchers": null, "tooltip": "hii"}}, {"headerName": "{{vendorMgmt.approvalStatus}}", "field": "approvalStatus", "fieldProps": {"nullContent": "dash"}, "cellVariant": {"variant": "badge", "matchers": {"Approved": "shamrock", "Pending Approval": "jaffa", "Cancelled": "danger", "default": "info"}}}, {"headerName": "{{vendorMgmt.applicationStatus}}", "field": "status", "fieldProps": {"nullContent": "dash"}, "cellVariant": {"variant": "badge", "matchers": {"Fully Applied": "success", "Partially Applied": "jaffa", "Unapplied": "jaffa", "default": "info"}}}, {"headerName": "{{vendorMgmt.postingStatus}}", "field": "postingStatus", "fieldProps": {"nullContent": "dash"}, "cellVariant": {"variant": "badge", "matchers": {"Posted": "success", "Unposted": "jaffa", "default": "error"}}}, {"headerName": "{{vendorMgmt.journalEntry}}", "field": "journalEntryNumber", "onCellClicked": "@controller:openJournalEntry"}, {"headerName": "{{vendorMgmt.currency}}", "field": "currency"}, {"headerName": "{{vendorMgmt.totalAmount}}", "field": "totalAmount", "fieldProps": {"parseAs": "currency"}}, {"headerName": "{{vendorMgmt.mode}}", "field": "migrationConfigurationMode", "fieldProps": {"nullContent": "dash"}, "initialHide": "@controller:isMigrationModeAvailable()", "cellVariant": {"variant": "badge", "matchers": {"Archive": "havelock-blue", "Migration": "sharpay", "default": "default"}}}], "createActions": [{"label": "{{vendorMgmt.createManually}}", "onClick": "@controller:navigateToCreateVendorCredit"}], "customActions": [{"label": "{{vendorMgmt.applyCredit}}", "onClick": "@controller:navigateToApplyVendorCredit"}]}}}