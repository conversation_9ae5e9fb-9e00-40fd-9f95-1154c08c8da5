{"version": 2, "uicontroller": ["vendor.uicontroller.ts"], "uimodel": {"state": {"currentWidget": 0}, "nodes": {"vendor": {"type": "struct", "query": {"where": {"id": "@state:param.id"}}, "modelId": "everest.fin.expense/VendorModel.Vendor", "fieldList": []}, "vendorBills": {"type": "list", "modelId": "everest.fin.expense/VendorBillHeaderBaseModel.VendorBillHeaderBase", "query": {"where": {"vendorId": "@state:param.id"}}, "pagination": true, "fieldList": ["id", "vendorName", "bill<PERSON><PERSON><PERSON>", "entityName", "entityId", "amountPaid", "currency", "billDate", "paymentStatus", "dueDate", "JournalEntryHeader-FinancialDocument.posted", "JournalEntryHeader-FinancialDocument.journalEntryNumber", "JournalEntryHeader-FinancialDocument.id", "total", "status", "vendorBillHeaderNumber"]}, "vendorCredits": {"type": "list", "modelId": "everest.fin.expense/VendorCreditHeaderModel.VendorCreditHeader", "query": {"where": {"vendorId": "@state:param.id"}}, "pagination": true, "fieldList": ["id", "creditDate", "currency", "totalAmount", "referenceNumber", "status", "totalAmountApplied", "totalBalance", "vendorCreditNumber"]}, "remittanceAddress": {"type": "struct", "modelId": "everest.base/AddressModel.Address", "parent": "vendor", "joinKey": "id-remittanceAddressId", "fieldList": []}}}, "uiview": {"templateType": "details", "i18n": "vendorMgmt", "title": "@controller:getTabTitle()", "config": {"stretch": true, "autoRefreshData": true, "allowRefreshData": true, "grid": {"limited": true}}, "header": {"layout": "everest.base.ui/layouts/uilayout/segmented/avatar/avatar", "content": {"title": "@controller:getTitle()", "status": "@controller:getStatus()", "description": "@controller:getDescription()", "showAvatar": true, "avatar": {"isEditable": true, "size": "large", "src": "@controller:getAvatarSrc()", "active": false, "loading": "@state:photoIsLoading", "onSuccess": "@controller:onPhotoSuccess", "onRemove": "@controller:onPhotoRemove"}}}, "sidebar": {"variant": "transparent", "position": "left", "content": [{"component": "Input", "size": "12", "label": "{{vendorMgmt.mainContact}}", "value": "@controller:getMergedNameEmail()", "multiline": true}, {"component": "Input", "size": "12", "label": "{{vendorMgmt.billingEmail}}", "value": "@controller:getBillingEmail()"}, {"component": "Input", "size": "12", "label": "{{vendorMgmt.remittanceAddress}}", "value": "@controller:getRemittanceAddress()", "multiline": true}, {"component": "Input", "size": "12", "label": "{{vendorMgmt.paymentTypeType}}", "value": "@controller:getPaymentType()"}, {"component": "Input", "size": "12", "label": "{{vendorMgmt.vendor.account}}", "value": "@controller:getDefaultExpenseAccount()"}, {"component": "ButtonGroup", "direction": "vertical", "size": 8, "actions": [{"variant": "primary", "label": "{{vendorMgmt.createVendorBill}}", "disabled": "@controller:isCreateVendorBillButtonDisabled()", "onClick": "@controller:createVendorBill"}, {"variant": "secondary", "label": "{{vendorMgmt.sidebar.viewVendorDetials}}", "onClick": "@controller:openVendorDetailsModal"}]}]}, "sections": {"content": [{"component": "Statistic", "section": {"title": "{{vendorMgmt.summary}}", "grid": {"size": "12"}, "hideDivider": true}, "props": {"columns": 4, "fields": [{"label": "{{vendorMgmt.openBalance}}", "value": "@controller:getOpenBalance()", "parseAs": "currency"}, {"label": "{{vendorMgmt.remainingCreditBalance}}", "value": "@controller:getRemainCreditBalance()", "parseAs": "currency"}, {"label": "{{vendorMgmt.unpaidBills}}", "value": "@controller:getNumberOfUnpaidBills()"}, {"label": "{{vendorMgmt.billsPendingApproval}}", "value": "@controller:getNumberOfPendingApprovalBills()"}]}}, {"component": "WidgetGroup", "customId": "vendorBillsTable", "section": {"grid": {"size": "12"}, "title": "@controller:getTableTitle()", "actions": "@controller:getActions()"}, "props": {"size": "12", "index": "2", "placement": "right", "selectedGroup": "@state:currentWidget", "widgets": [{"component": "Table", "label": "{{vendorMgmt.bills}}", "onClick": "@controller:setWidgetGroupType", "section": {"grid": {"size": "12"}, "pagination": true, "filter": {"suppressFields": ["id", "vendorName", "entityName", "JournalEntryHeader-FinancialDocument.posted", "JournalEntryHeader-FinancialDocument.journalEntryNumber", "JournalEntryHeader-FinancialDocument.id", "isDrafts"]}}, "props": {"variant": "white-borderless", "columns": [{"headerName": "{{vendorMgmt.entityId}}", "field": "entityId", "hide": true}, {"headerName": "{{vendorMgmt.amountPaid}}", "field": "amountPaid", "hide": true}, {"headerName": "{{vendorMgmt.number}}", "field": "vendorBillHeaderNumber"}, {"headerName": "{{vendorMgmt.referenceNumber}}", "field": "bill<PERSON><PERSON><PERSON>"}, {"headerName": "{{vendorMgmt.billDate}}", "field": "billDate"}, {"headerName": "{{vendorMgmt.dueDate}}", "field": "dueDate"}, {"headerName": "{{vendorMgmt.status}}", "field": "status", "cellVariant": {"variant": "badge", "matchers": {"Approved": "shamrock", "Pending Approval": "jaffa", "Cancelled": "danger", "default": "info"}}}, {"headerName": "{{vendorMgmt.paymentStatus}}", "field": "paymentStatus", "cellVariant": {"variant": "badge", "matchers": {"Fully Paid": "success", "Unpaid": "jaffa", "Partially Paid": "jaffa", "Pending": "info", "default": "error"}}}, {"headerName": "{{vendorMgmt.currency}}", "field": "currency"}, {"headerName": "{{vendorMgmt.amount}}", "field": "total", "fieldProps": {"parseAs": "currency"}}], "data": "@binding:vendorBills", "onRowClicked": "@controller:navigateToVendorBill"}}, {"component": "Table", "label": "{{vendorMgmt.credits}}", "onClick": "@controller:setWidgetGroupType", "disabled": false, "section": {"grid": {"size": "12"}, "pagination": true}, "props": {"variant": "white-borderless", "columns": [{"headerName": "{{vendorMgmt.number}}", "field": "vendorCreditNumber", "fieldProps": {"fontWeight": "bold"}}, {"headerName": "{{vendorMgmt.referenceNumber}}", "field": "referenceNumber"}, {"headerName": "{{vendorMgmt.creditDate}}", "field": "creditDate"}, {"headerName": "{{vendorMgmt.currency}}", "field": "currency"}, {"headerName": "{{vendorMgmt.remainingBalance}}", "field": "totalBalance", "fieldProps": {"parseAs": "currency"}}], "data": "@binding:vendorCredits", "onRowClicked": "@controller:navigateToVendorCredit"}}]}}]}}}