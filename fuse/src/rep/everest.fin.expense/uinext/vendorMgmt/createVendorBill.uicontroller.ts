// @i18n:vendorMgmt

import type { RowType } from '@pkg/everest.base/public/ui-types/TransformedUIParam.uicontroller';
import {
  getUIModelFromSharedState,
  setUIModelInSharedState,
} from '@pkg/everest.base/public/utils/ui/sharedStateUtils.ui';
import type { BillExtractionResponse } from '@pkg/everest.fin.expense/actions/common/codeLists';
import { isError } from '@pkg/everest.fin.expense/actions/common/codeLists';
import type { CreateVendorBillUiTemplate } from '@pkg/everest.fin.expense/types/uiTemplates/uinext/vendorMgmt/createVendorBill.ui';
import { VendorBillHeaderBaseUI } from '@pkg/everest.fin.expense/types/VendorBillHeaderBase.ui';
import {
  createVendorBill,
  handleBillDateChange,
  handleInputChange,
  handlePaymentTermsChange,
  setDefaultVendorValues,
  setEntityDefaults,
  setNewVendorDefaultValues,
  validateBillNumber,
} from '@pkg/everest.fin.expense/uinext/vendorMgmt/utilities.uicontroller';

type CreateVendorBillContext =
  CreateVendorBillUiTemplate.CreateVendorBillContext;
type SingleNodeItemType = CreateVendorBillUiTemplate.SingleNodeItemType;

export type Context = CreateVendorBillContext & {
  state: {
    id: number;
    defaultCurrency: string;
    isVendorDefaultsSet: boolean;
    isVendorDraft: boolean;
    isDueDateAndPostingPeriodSet: boolean;
    attachments: { fileId: string; fileName: string }[];
    clickedOnce: boolean;
    isFormDataChanged: boolean;
    toBeDeletedVendorBillItemIds: Set<number>;
    isFetchingAccountData: boolean;
    canBePosted: boolean;
    amortizationDrafts: Map<
      string,
      { header: Record<string, unknown>; line: Record<string, unknown> }
    >;
  };
};

export type VendorBillItemRow = RowType<SingleNodeItemType['vendorBillLines']>;

export function isScanButtonVisible({ state }: Context) {
  return state?.mode === 'create';
}

export function getTabTitle(context: Context) {
  return getTitle(context);
}

export function isDraft({ data }: Context) {
  return !!data?.vendorBill?.$metadata?.isDraft;
}

export function getTitle(context: Context) {
  return isDraft(context)
    ? '{{vendorMgmt.vendorBill}}'
    : '{{vendorMgmt.create.vendorBill}}';
}

export function getStatus(context: Context) {
  return isDraft(context) ? 'Draft' : null;
}

export function getDefaultVendor(context: Context) {
  const { data, state } = context;
  const vendor = data?.vendor;
  if (vendor) {
    if (state.id) {
      if (data.vendorBill.billDate && !state.isDueDateAndPostingPeriodSet) {
        state.isDueDateAndPostingPeriodSet = true;
        handleBillDateChange(context, data.vendorBill.billDate).catch(
          console.log
        );
      }
    } else {
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      setDefaultVendorValues(context, vendor);
    }
    return vendor?.id;
  } else {
    state.isVendorDraft = false;
  }
  return data?.vendorBill?.vendorId;
}

export const scanAndFill = async (context: Context) => {
  const { actions, helpers, state, sections } = context;
  state.isScanCanceled = false;

  const files = state.attachments;
  if (files?.length) {
    helpers.openDialog({
      variant: 'loading',
      title: '{{vendorMgmt.scanning.bill}}',
      actions: {
        secondary: {
          label: '{{vendorMgmt.cancel}}',
          onClick: () => {
            state.isScanCanceled = true;
            helpers.closeModal();
          },
        },
      },
    });

    // Call action to extract text from receipt file
    const jobResponse: {
      vendorBillAttachment: BillExtractionResponse;
    } = await actions.run({
      // TODO: Do we need a different key for this action
      vendorBillAttachment: {
        action: 'extractTextFromBill',
        data: { fileId: files[0].fileId },
      },
    });

    if ('error' in jobResponse || isError(jobResponse.vendorBillAttachment)) {
      helpers.showToast({
        title: 'Error',
        message: 'Seems some problem in scanning.',
        type: 'error',
      });
      helpers.closeModal();
      return;
    }

    if (state.isScanCanceled) {
      helpers.closeModal();
      return;
    }

    const dataReceived = jobResponse.vendorBillAttachment;
    // Fill form fields
    const { vendorBillHeaderData, vendorBillLineData } = dataReceived;
    for (const key of Object.keys(vendorBillHeaderData)) {
      setUIModelInSharedState(
        context,
        'vendorBill',
        key as unknown as keyof Context['data']['vendorBill'],
        vendorBillHeaderData[key]
      );
    }

    if (getUIModelFromSharedState(context, 'vendorBillLines').length === 0) {
      sections.vendorBillLinesTable.addRow({}, {});
    }

    const vendorBillLines = getUIModelFromSharedState(
      context,
      'vendorBillLines'
    );
    for (const key of Object.keys(vendorBillLineData)) {
      setUIModelInSharedState(
        context,
        'vendorBillLines',
        key,
        vendorBillLineData[key],
        vendorBillLines[0]._nodeReference
      );
    }

    await setNewVendorDefaultValues(context, vendorBillHeaderData.vendorId);

    helpers.closeModal();
  }
};

export const openUploadFileModal = (context: Context) => {
  const { helpers, state } = context;

  helpers.openModal({
    title: '{{vendorBill.uploadAndScan}}',
    size: 'small',
    template:
      '/templates/everest.fin.expense/uinext/vendorMgmt/uploadVendorBillsModal',
    initialState: {
      singleFileOnly: true,
    },
    onModalSubmit: async (files) => {
      helpers.closeModal();
      if (files) {
        state.attachments = [...files, ...(state.attachments ?? [])];
      }
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve(scanAndFill(context));
        }, 500);
      });
    },
  });
};

export async function onScanClick(context: Context) {
  const { sections, helpers, state } = context;

  const files = sections.bills?.getUploadFiles();
  if (files?.length >= 1) {
    helpers.openModal({
      size: 'xsmall',
      template:
        '/templates/everest.fin.expense/uinext/vendorMgmt/vendorBillScanConfirmationModal',
      initialState: {
        hasMoreThanOneFile: files.length > 1,
      },
      onModalSubmit: async (chosenOption) => {
        helpers.closeModal();
        if (chosenOption === 'attachedFile') {
          await scanAndFill(context);
        } else if (chosenOption === 'newFile') {
          openUploadFileModal(context);
        }
      },
    });
  } else if (state.isFormDataChanged) {
    helpers.openDialog({
      variant: 'warning',
      title: '{{vendorMgmt.alreadyScannedTitle}}',
      message: '{{vendorMgmt.alreadyScannedMsg}}',
      actions: {
        secondary: {
          label: '{{vendorMgmt.cancel}}',
          onClick: helpers.closeModal,
        },
        primary: {
          label: '{{vendorMgmt.proceed}}',
          onClick: () => {
            helpers.closeModal();
            openUploadFileModal(context);
          },
        },
      },
    });
  } else {
    openUploadFileModal(context);
  }
}

export function getAttachments({ data, form, state }: Context) {
  const { bill } = form.getFormValues();
  if (!state.attachments || bill.length > state.attachments?.length) {
    state.attachments = bill;
  }
  return state.attachments ?? data?.vendorBillAttachments ?? [];
}

export function isActionDisabled({ state }: Context) {
  return state.isFetchingAccountData || state.clickedOnce || !state.canBePosted;
}

export async function saveButtonAction(context: Context) {
  await createVendorBill(context, false);
}

export async function deleteDraft(context: Context) {
  const { state, helpers } = context;
  state.clickedOnce = true;
  await VendorBillHeaderBaseUI.deleteVendorBills(context, {
    vendorBillIds: [state.id],
  }).run('vendorBill');
  helpers.navigate({
    to: '/templates/everest.fin.expense/uinext/vendorMgmt/vendorBills',
    closeCurrentTab: true,
  });
  helpers.showNotificationMessage({
    key: 'draftDeleted',
    type: 'success',
    message: 'Draft deleted successfully.',
    duration: 10,
  });
}

export async function saveDraft(context: Context) {
  await createVendorBill(context, true);
}

export function openCreateVendorModal(context: Context) {
  const { actions, helpers } = context;

  helpers.openModal({
    size: 'medium',
    template:
      '/templates/everest.fin.expense/uinext/vendorMgmt/createVendor?mode=create',
    onModalSubmit: async ({ id: vendorId }) => {
      helpers.closeModal();
      setUIModelInSharedState(context, 'vendorBill', 'vendorId', vendorId);
      await actions.refetchUiModelData();
    },
  });
}

export function getMoreActions(context: Context) {
  return [
    {
      variant: 'secondary',
      label: isDraft(context)
        ? '{{vendorMgmt.saveDraft}}'
        : '{{vendorMgmt.saveAsDraft}}',
      visible: true,
      disabled: isActionDisabled(context),
      onClick: () => saveDraft(context),
    },
    {
      variant: 'secondary',
      label: '{{vendorMgmt.delete}}',
      visible: isDraft(context),
      disabled: isActionDisabled(context),
      onClick: () => deleteDraft(context),
    },
  ];
}

export function getCustomActions(context: Context) {
  return isDraft(context)
    ? []
    : [
        {
          label: '{{vendorMgmt.saveAsDraft}}',
          visible: true,
          disabled: isActionDisabled(context),
          onClick: () => saveDraft(context),
        },
      ];
}

export function isCustomActionsVisible(context: Context) {
  return !isDraft(context);
}

export async function handleVendorChange(context: Context, vendorId: number) {
  handleInputChange(context);
  await validateBillNumber(context);
  await setNewVendorDefaultValues(context, vendorId);
}

export async function handleEntityChange(context: Context) {
  handleInputChange(context);
  await setEntityDefaults(context);
}

export function handlePaymentTermChange(context: Context, value: string) {
  handleInputChange(context);
  handlePaymentTermsChange(context, value);
}

export function isSelectedEntityInactive({ data }: Context) {
  const vendorBill = data.vendorBill;

  return Boolean(
    vendorBill?.entityId &&
      !data.entities.some((entity) => entity.id === vendorBill.entityId)
  );
}

export function getEntitiesList(context: Context) {
  if (isSelectedEntityInactive(context)) {
    setUIModelInSharedState(context, 'vendorBill', 'entityId', null);
  }

  const { data } = context;
  return data.entities;
}

export function handleVendorBillItemDelete(context: Context, { nodeInstance }) {
  const { state } = context;
  if (nodeInstance) {
    if (!state.toBeDeletedVendorBillItemIds) {
      state.toBeDeletedVendorBillItemIds = new Set();
    }
    state.toBeDeletedVendorBillItemIds.add(nodeInstance.data.id);
  }
}

export function handleVendorBillItemUndoDelete(
  context: Context,
  { nodeInstance }
) {
  const { state } = context;
  if (nodeInstance) {
    if (!state.toBeDeletedVendorBillItemIds) {
      state.toBeDeletedVendorBillItemIds = new Set();
    }
    state.toBeDeletedVendorBillItemIds.delete(nodeInstance.data.id);
  }
}

export async function handleAttachmentRemove(context: Context, fileId: string) {
  const { state } = context;
  state.attachments = (state.attachments ?? []).filter(
    (file) => file.fileId !== fileId
  );
}
