{"version": 2, "uicontroller": "vendors.uicontroller.ts", "uimodel": {"nodes": {"draftV2": {"modelId": "everest.appserver/metadata/DraftV2Model.DraftV2", "type": "struct"}, "vendors": {"type": "list", "query": {"orderBy": [{"field": "vendorNumber", "ordering": "desc"}]}, "options": {"draft": "include"}, "modelId": "everest.fin.expense/VendorModel.Vendor", "pagination": true, "fieldList": ["id", "vendorName", "vendorNumber", "businessPartnerId", "departmentId", "vendorEntityId", "preferredCurrency", "vendorType", "bankDetailsId", "remittanceAddressId", "bankAccountNumberTypeText", "status", "photo", "migrationConfigurationProvider", "Vendor-BusinessPartner.taxId"]}, "vendorBills": {"type": "struct", "modelId": "everest.fin.expense/VendorBillHeaderBaseModel.VendorBillHeaderBase"}, "payrollPaymentApplications": {"type": "struct", "modelId": "everest.fin.expense/PayrollPaymentApplicationModel.PayrollPaymentApplication", "fieldList": ["id", "vendorId"]}}}, "uiview": {"layout": "everest.base.ui/layouts/uilayout/list", "props": {"i18n": "vendorMgmt", "title": "@controller:getTitle()", "customId": "vendorsTable", "onCreateClick": "@controller:openCreateVendorModal", "data": "@binding:vendors", "node": "vendors", "removeDeleteAction": true, "onRowClicked": "@controller:openOrNavigateToVendor", "fixedFilters": ["vendorName", "vendorEntityId", "vendorType"], "suppressFilterFields": ["id", "businessPartnerId", "departmentId", "bankDetailsId", "remittanceAddressId", "photo", "migrationConfigurationProvider", "Vendor-BusinessPartner.taxId"], "omitFieldsFromColumns": ["photo", "businessPartnerId", "departmentId"], "rowBackgroundGetter": "@controller:getRowBackground", "columns": [{"headerName": "{{vendorMgmt.number}}", "valueGetter": "@controller:vendorNumberValueGetter", "field": "vendorNumber", "sortable": true, "fieldProps": {"nullContent": "custom", "customNullContent": "Draft"}}, {"headerName": "{{vendorMgmt.name}}", "field": "vendorName", "fieldProps": {"fontWeight": "bold"}, "cellVariant": {"variant": "avatar", "matchers": "@controller:getAvatar"}}, {"headerName": "{{vendorMgmt.vendorType}}", "field": "vendorType"}, {"headerName": "{{vendorMgmt.entity}}", "field": "vendorEntityId"}, {"headerName": "{{vendorMgmt.preferredCurrency}}", "field": "preferredCurrency"}, {"headerName": "{{vendorMgmt.vendor.paymentMethod}}", "field": "bankAccountNumberTypeText"}, {"headerName": "{{vendorMgmt.taxId}}", "field": "Vendor-BusinessPartner.taxId", "initialHide": true}, {"headerName": "{{vendorMgmt.status}}", "valueGetter": "@controller:vendorStatusValueGetter", "field": "status", "cellVariant": {"variant": "badge", "matchers": {"Active": "success", "Inactive": "mystic-grey", "default": "info"}}}, {"headerName": "{{vendorMgmt.migrationSource}}", "field": "migrationConfigurationProvider", "initialHide": "@controller:isMigrationSourceAvailable()", "fieldProps": {"nullContent": "dash"}, "cellVariant": {"variant": "badge", "matchers": {"Intacct": "sharpay"}}}], "customActions": [{"variant": "secondary", "label": "{{vendorMgmt.vendor.makeInactive}}", "onClick": "@controller:deactivateVendors", "disabled": "@controller:isDeactivateVendorsDisabled()", "confirmation": {"message": "{{vendorMgmt.vendor.makeInactiveMessage}}", "confirmLabel": "{{vendorMgmt.vendor.makeInactive}}"}}, {"variant": "secondary", "label": "{{vendorMgmt.vendor.makeActive}}", "onClick": "@controller:activateVendors", "disabled": "@controller:isActivateVendorsDisabled()", "confirmation": {"message": "{{vendorMgmt.vendor.makeActiveMessage}}", "confirmLabel": "{{vendorMgmt.vendor.makeActive}}"}}, {"variant": "secondary", "label": "{{vendorMgmt.vendor.markAsDuplicate}}", "onClick": "@controller:openMarkAsDuplicateModal", "disabled": "@controller:isDuplicateButtonDisabled()"}, {"variant": "secondary", "label": "{{vendorMgmt.vendor.activateDrafts}}", "onClick": "@controller:activateDraftVendors", "disabled": "@controller:isActionButtonDisabled()", "confirmation": {"message": "{{vendorMgmt.vendor.makeDraftsActiveMessage}}", "confirmLabel": "{{vendorMgmt.vendor.activateDrafts}}"}}, {"variant": "secondary", "label": "{{vendorMgmt.vendor.delete}}", "onClick": "@controller:deleteSelectedVendors", "disabled": "@controller:isDeleteVendorsDisabled()", "confirmation": {"description": "{{vendorMgmt.vendor.deleteRowsConfirmationDetails}}", "confirmLabel": "{{vendorMgmt.delete}}"}}]}}}