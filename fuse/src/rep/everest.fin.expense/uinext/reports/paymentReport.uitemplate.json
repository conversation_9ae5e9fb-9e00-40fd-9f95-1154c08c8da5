{"version": 2, "uicontroller": "paymentReport.uicontroller.ts", "uimodel": {"nodes": {"vendorStats": {"type": "struct", "detailedType": "table", "generateVendorPaymetReportData": "@controller:getApAgingChartDataQuery('table')", "modelId": "everest.fin.expense/VendorModel.Vendor"}}}, "uiview": {"layout": "everest.base.ui/layouts/uilayout/startPage/startPage", "props": {"i18n": ["p2pDashboard"], "title": "{{paymentReport.title1099}}", "keyDataConfig": {"@uifragment": {"path": "everest.base/entity/uinext/entityPicker.uifragment.json", "props": {"listSingleEntities": true, "listConsolidatedEntities": false, "displayCurrency": true, "displaySectionLabels": true, "getConfigDefaultEntity": true}}}, "firstRowContent": {"first": {"component": "Table", "size": "12", "contained": true, "props": {"customId": "paymentReportTable", "data": "@binding:vendorStats", "fixedFilters": ["independentContractor", "totalAmount"], "columns": [{"field": "vendorTaxId", "headerName": "{{paymentReport.vendorTaxId}}"}, {"field": "vendorName", "headerName": "{{paymentReport.vendorName}}"}, {"field": "independentContractor", "headerName": "{{paymentReport.independentContractor}}"}, {"field": "totalAmount", "headerName": "{{paymentReport.totalAmount}}", "@command:onCellClicked": [{"action": "controllers.navigateToPayments", "params": {"context": "@context:args", "rowContext": "@context:data"}}]}], "variant": "white-borderless"}}}, "headerActions": {"@uifragment": {"path": "everest.fin.accounting/uinext/common/periodPickerFragment", "props": {"singleDateSelection": false}}}}}}