/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable jest/expect-expect */
/* eslint-disable import/no-unresolved */
/* eslint-disable @typescript-eslint/no-explicit-any */
import type { ISession } from '@everestsystems/content-core';
import type { TestTeardown } from '@everestsystems/content-core/lib/tests';
import { setupTest } from '@everestsystems/content-core/lib/tests';
import { toPlainDate } from '@pkg/everest.base/public/utils/Date/date';
import type { Entity } from '@pkg/everest.base/types/Entity';
import { Account } from '@pkg/everest.fin.accounting/types/Account';
import { EvstOutboundPaymentType } from '@pkg/everest.fin.base/types/enums/OutboundPaymentType';
import type { OutboundPaymentHeaderBase } from '@pkg/everest.fin.expense/types/OutboundPaymentHeaderBase';
import { EvstExtractionProviders } from '@pkg/everest.fin.integration.base/types/enums/ExtractionProviders';
import { EvstStagingDataStatus } from '@pkg/everest.fin.integration.base/types/enums/StagingDataStatus';
import { EvstStagingStatus } from '@pkg/everest.fin.integration.base/types/enums/StagingStatus';
import syncData from '@pkg/everest.fin.integration.brex/actions/template/syncData/syncData.action';
import { employeeExpenseDataPayload } from '@pkg/everest.fin.integration.brex/test/integration/fixtures/employeeExpenseData';
import { employeeSyncDataPayload } from '@pkg/everest.fin.integration.brex/test/integration/fixtures/employeeSyncData';
import { entitySyncDataPayload } from '@pkg/everest.fin.integration.brex/test/integration/fixtures/entitySyncData';
import { fetchingDataPayload } from '@pkg/everest.fin.integration.brex/test/integration/fixtures/fetchingData';
import {
  TEST_EMAIL,
  TEST_ENTITY_NAME,
  TEST_EXPENSE_ID,
  TEST_FIRST_NAME,
  TEST_LAST_NAME,
} from '@pkg/everest.fin.integration.brex/test/integration/utils/constant';
import {
  createStagingData,
  createTestEmployee,
  createTestEntityNodes,
  getMapping,
  getOutboundPaymentById,
  getOutboundPaymentItemsByPaymentHeaderId,
  simulateMatch,
  verifyStagingData,
} from '@pkg/everest.fin.integration.brex/test/integration/utils/createMocks';
import getDataAPI from '@pkg/everest.fin.integration.brex/utils/api/getDataAPI';
import { BREX_MODELS } from '@pkg/everest.fin.integration.brex/utils/api/types';
import type { Employee } from '@pkg/everest.hr.base/types/Employee';

jest.mock('@pkg/everest.fin.integration.brex/utils/api/getDataAPI');

describe('Integration Test for Employee Expense Payment Sync Data from Brex', () => {
  let session: ISession;
  let teardown: TestTeardown;
  let entityId: Entity.Entity['id'];
  let accountId: Account.Account['id'];
  let employeeId: Employee.Employee['id'];
  let outboundPaymentId: number;
  let outboundPaymentHeader: OutboundPaymentHeaderBase.OutboundPaymentHeaderBase;
  let etlResults: any;

  beforeAll(async () => {
    ({ teardown, session } = await setupTest({
      withUser: 'test-user',
    }));

    // Mock getDataAPI
    (getDataAPI as jest.Mock).mockImplementation((_env, providerModel) => {
      switch (providerModel) {
        case BREX_MODELS.Entity: {
          return Promise.resolve(entitySyncDataPayload);
        }
        case BREX_MODELS.User: {
          return Promise.resolve(employeeSyncDataPayload);
        }
        case BREX_MODELS.EmployeeExpense: {
          employeeExpenseDataPayload.items[0].id = TEST_EXPENSE_ID;
          return Promise.resolve(employeeExpenseDataPayload);
        }
        default: {
          employeeExpenseDataPayload.items[0].payment_status = 'CLEAR';
          // employeeExpenseDataPayload.items[0].id = TEST_EXPENSE_PAYMENT_ID;
          return Promise.resolve(employeeExpenseDataPayload);
        }
      }
    });

    // Create test entity nodes and match to Brex entities
    const entityNode = await createTestEntityNodes(session);
    entityId = entityNode.entityId;
    accountId = entityNode.accountId;
    await syncData(
      session,
      BREX_MODELS.Entity,
      await getMapping(session, 'metadata', BREX_MODELS.Entity),
      fetchingDataPayload
    );
    // Simulate a new record for matching ReimbursementLiabilityAccount from Brex to Account Everest
    await createStagingData(session, [
      {
        originalId: entitySyncDataPayload.items[1].id,
        providerName: EvstExtractionProviders.Brex,
        providerModel: 'ReimbursementLiabilityAccount',
        everestModel: Account.MODEL_URN,
        dataStatus: EvstStagingDataStatus.Latest,
        status: EvstStagingStatus.Matched,
        everestId: accountId,
        dataExtractionId: 1,
        dataVersion: 2,
      },
      {
        originalId: entitySyncDataPayload.items[1].id,
        providerName: EvstExtractionProviders.Brex,
        providerModel: 'ReimbursementBankAccount',
        everestModel: Account.MODEL_URN,
        dataStatus: EvstStagingDataStatus.Latest,
        status: EvstStagingStatus.Matched,
        everestId: accountId,
        dataExtractionId: 1,
        dataVersion: 3,
      },
    ]);

    // Create an employee for expense report and match to Brex user
    const employee = await createTestEmployee(
      session,
      entityId,
      TEST_FIRST_NAME + ' ' + TEST_LAST_NAME,
      TEST_EMAIL
    );
    employeeId = employee.id;
    await syncData(
      session,
      BREX_MODELS.User,
      await getMapping(session, 'metadata', BREX_MODELS.User),
      fetchingDataPayload
    );
    // Simulate match user from Brex to Everest
    await simulateMatch(
      session,
      employeeSyncDataPayload.items[0].id,
      employeeId
    );

    // Sync categories and
    await syncData(
      session,
      BREX_MODELS.Category,
      await getMapping(session, 'metadata', BREX_MODELS.Category),
      fetchingDataPayload
    );
    // Simulate match categories from Brex to Everest
    await simulateMatch(
      session,
      TEST_ENTITY_NAME + ' - ' + employeeExpenseDataPayload.items[0].category,
      accountId
    );

    // Call employee expense payment sync data
    etlResults = await syncData(
      session,
      BREX_MODELS.EmployeeExpensePayment,
      await getMapping(
        session,
        'businessData',
        BREX_MODELS.EmployeeExpensePayment
      ),
      fetchingDataPayload
    );
  });

  afterAll(async () => {
    jest.clearAllMocks();
    await teardown?.();
  });

  const validateOutboundPaymentHeaders = async () => {
    const outboundPaymentHeaders = await getOutboundPaymentById(
      session,
      outboundPaymentId
    );
    expect(outboundPaymentHeaders.length).toBe(1);
    outboundPaymentHeader = outboundPaymentHeaders[0];
    expect(outboundPaymentHeader.id).toBeDefined();
    expect(outboundPaymentHeader.paymentCurrency).toEqual(
      employeeExpenseDataPayload.items[0].billing_amount.currency
    );
    expect(outboundPaymentHeader.status).toEqual('manuallyRecorded');
    expect(outboundPaymentHeader.senderAccountId).toEqual('N/A');
    expect(outboundPaymentHeader.paymentType).toEqual(
      EvstOutboundPaymentType.Manual
    );
    expect(outboundPaymentHeader.paymentDate).toEqual(
      toPlainDate(employeeExpenseDataPayload.items[0].payment_posted_at)
    );
    expect(outboundPaymentHeader.accountId).toEqual(accountId);
    expect(outboundPaymentHeader.origin).toEqual(EvstExtractionProviders.Brex);
  };

  const validateOutboundPaymentItems = async () => {
    const outboundPaymentItems = await getOutboundPaymentItemsByPaymentHeaderId(
      session,
      outboundPaymentHeader.id
    );
    expect(outboundPaymentItems.length).toBe(1);
    const outboundPaymentItem = outboundPaymentItems[0];
    expect(outboundPaymentItem.paymentCurrency).toEqual(
      employeeExpenseDataPayload.items[0].billing_amount.currency
    );
    expect(
      outboundPaymentItem.paymentAmount.amount.equals(
        employeeExpenseDataPayload.items[0].billing_amount.amount / 100
      )
    ).toBeTruthy();
    expect(outboundPaymentItem.entityId).toEqual(entityId);
    expect(outboundPaymentItem.paymentDate).toEqual(
      toPlainDate(employeeExpenseDataPayload.items[0].payment_posted_at)
    );
    expect(outboundPaymentItem.receiverName).toEqual(
      `${employeeExpenseDataPayload.items[0].user.first_name} ${employeeExpenseDataPayload.items[0].user.last_name}`
    );
    expect(outboundPaymentItem.description).toEqual(
      `Reimbursement for ${
        employeeExpenseDataPayload.items[0].user.first_name
      } ${employeeExpenseDataPayload.items[0].user.last_name} on ${toPlainDate(
        employeeExpenseDataPayload.items[0].payment_posted_at
      )}`
    );
    expect(outboundPaymentItem.origin).toEqual(EvstExtractionProviders.Brex);
  };

  it('should fail to sync Employee Expense Report Payment from Brex to Everest if there is no expense report synced under Everest', async () => {
    expect(etlResults.success).toEqual(0);
    await verifyStagingData(
      session,
      BREX_MODELS.EmployeeExpensePayment,
      employeeExpenseDataPayload.items[0].id,
      EvstStagingStatus.FAILED,
      undefined,
      `Transformation Failed: integrationIdMapper could not find a staged entry for reference id: "${TEST_EXPENSE_ID}"`
    );
  });

  it('should sync an Expense Report Payment successfully from Brex to Everest', async () => {
    // Sync Employee Expense
    await syncData(
      session,
      BREX_MODELS.EmployeeExpense,
      await getMapping(session, 'businessData', BREX_MODELS.EmployeeExpense),
      fetchingDataPayload
    );
    // Call Employee Expense Payment syncData again
    etlResults = await syncData(
      session,
      BREX_MODELS.EmployeeExpensePayment,
      await getMapping(
        session,
        'businessData',
        BREX_MODELS.EmployeeExpensePayment
      ),
      fetchingDataPayload
    );
    expect(etlResults.retryResult.success).toEqual(1);
    await verifyStagingData(
      session,
      BREX_MODELS.EmployeeExpense,
      employeeExpenseDataPayload.items[0].id,
      EvstStagingStatus.Integrated
    );
    outboundPaymentId = await verifyStagingData(
      session,
      BREX_MODELS.EmployeeExpensePayment,
      employeeExpenseDataPayload.items[0].id,
      EvstStagingStatus.Integrated
    );

    await validateOutboundPaymentHeaders();
    await validateOutboundPaymentItems();
  });
});
