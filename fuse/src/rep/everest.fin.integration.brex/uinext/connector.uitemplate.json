{"version": 2, "uicontroller": "connector.uicontroller.ts", "uimodel": {"nodes": {"brex": {"type": "struct", "modelId": "everest.fin.integration.brex/BrexModel.Brex"}}}, "uiview": {"templateType": "details", "i18n": ["everest.connectorengine/connectorEngine", "brex"], "title": "{{connectorEngine.create}}", "actions": {"content": [{"variant": "secondary", "label": "{{connectorEngine.cancel}}", "onClick": "@controller:cancel", "align": "right", "visible": "@controller:isEditing()"}, {"variant": "primary", "label": "@controller:getPrimaryLabel()", "onClick": "@controller:save", "align": "right"}]}, "sections": {"content": [{"component": "Block", "section": {"grid": {"size": "12"}}, "props": {"type": "secondary", "elements": [{"component": "Input", "label": "{{brex.name}}", "name": "name", "value": "@controller:getName()", "isEditing": true}, {"component": "Input", "label": "{{brex.token}}", "name": "token", "value": "@controller:getToken()", "isEditing": true}]}}]}}}