// @i18n:everest.connectorengine/connectorEngine
import { BrexUI } from '@pkg/everest.fin.integration.brex/types/Brex.ui';
import type { GetConnector } from '@pkg/everest.fin.integration.expense/public/types';

import type { ConnectorUiTemplate } from '../types/uiTemplates/uinext/connector.ui';

type ConnectorContext = ConnectorUiTemplate.ConnectorContext;

export type Context = ConnectorContext & {
  state: {
    connector: GetConnector & { configValues: Record<string, unknown> };
  };
};

export function isEditing({ state }: Context) {
  return state.connector !== undefined;
}

export function getPrimaryLabel(ctx: Context) {
  return isEditing(ctx)
    ? '{{connectorEngine.save}}'
    : '{{connectorEngine.connect}}';
}

export function getName({ state }: Context) {
  return state.connector?.name || '';
}

export function getToken({ state }: Context) {
  const authHeader: string =
    state?.connector?.configValues?.headers?.['Authorization'] || '';

  if (authHeader && authHeader.includes(' ')) {
    const [tokenType, tokenValue] = authHeader.split(' ', 2);
    if (tokenType === 'Bearer' && tokenValue) {
      return tokenValue;
    }
  }

  return '';
}

export function cancel({ helpers }: Context) {
  helpers.closeModal();
}

export async function save(ctx: Context) {
  const { helpers, form, state } = ctx;
  const { name, token } = form.getFormValues();
  const { connector } = state;

  await BrexUI.saveConnector(ctx, {
    args: {
      name,
      token,
      connectorId: connector?.id,
    },
  }).run('brex');
  if (connector) {
    helpers.closeModal();
  } else {
    helpers.navigate({
      to: '/templates/everest.fin.integration.expense/uinext/overview?providerName=Brex',
      closeCurrentTab: true,
    });
  }
}
