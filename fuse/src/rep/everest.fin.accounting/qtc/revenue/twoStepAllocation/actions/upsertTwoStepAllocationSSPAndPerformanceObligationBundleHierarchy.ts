import type { ISession } from '@everestsystems/content-core';
import { getTranslation, ValidationError } from '@everestsystems/content-core';
import { Decimal } from '@everestsystems/decimal';
import type { CustomTwoStepAllocationSSPHierarchy } from '@pkg/everest.fin.accounting/qtc/revenue/twoStepAllocation/actions/getTwoStepAllocationForTreeView.action';
import getTwoStepAllocationForTreeView from '@pkg/everest.fin.accounting/qtc/revenue/twoStepAllocation/actions/getTwoStepAllocationForTreeView.action';
import { PerformanceObligationBundleHierarchy } from '@pkg/everest.fin.accounting/types/PerformanceObligationBundleHierarchy';
import { TwoStepAllocationSSP } from '@pkg/everest.fin.accounting/types/TwoStepAllocationSSP';
import type { TwoStepAllocationSSPHierarchy } from '@pkg/everest.fin.accounting/types/TwoStepAllocationSSPHierarchy';

export default async function upsertTwoStepAllocationSSPAndPerformanceObligationBundleHierarchy(
  session: ISession,
  parentPerformanceObligationId: number,
  newTwoStepAllocationSSPHierarchies: Partial<TwoStepAllocationSSPHierarchy.TwoStepAllocationSSPHierarchy>[] = []
): Promise<{
  twoStepAllocationSSPs: Partial<TwoStepAllocationSSP.TwoStepAllocationSSP>[];
  performanceObligationBundleHierarchies: Partial<PerformanceObligationBundleHierarchy.PerformanceObligationBundleHierarchy>[];
}> {
  const twoStepAllocationSSPs: Partial<TwoStepAllocationSSP.TwoStepAllocationSSP>[] =
    [];
  const performanceObligationBundleHierarchies: Partial<PerformanceObligationBundleHierarchy.PerformanceObligationBundleHierarchy>[] =
    [];

  await validate(session, newTwoStepAllocationSSPHierarchies);

  const existTwoStepAllocationSSPHierarchies =
    await getTwoStepAllocationForTreeView(
      session,
      parentPerformanceObligationId
    );

  if (
    !newTwoStepAllocationSSPHierarchies ||
    newTwoStepAllocationSSPHierarchies.length <= 0
  ) {
    await deleteTwoStepAllocationSSPAndPerformanceObligationBundleHierarchyNodes(
      session,
      parentPerformanceObligationId,
      existTwoStepAllocationSSPHierarchies
    );
    return {
      twoStepAllocationSSPs,
      performanceObligationBundleHierarchies,
    };
  }

  const parentNewTwoStepAllocationSSPHierarchy =
    newTwoStepAllocationSSPHierarchies.find(
      ({ performanceObligationId }) =>
        performanceObligationId === parentPerformanceObligationId ||
        performanceObligationId === -1
    );
  const parentExistTwoStepAllocationSSPHierarchy =
    existTwoStepAllocationSSPHierarchies.find(
      ({ twoStepAllocation: { performanceObligationId } }) =>
        performanceObligationId === parentPerformanceObligationId
    );

  const parentNodes =
    await upsertTwoStepAllocationSSPAndPerformanceObligationBundleHierarchyNodes(
      session,
      {
        id: parentExistTwoStepAllocationSSPHierarchy?.id,
        twoStepAllocationPercent:
          parentNewTwoStepAllocationSSPHierarchy.twoStepAllocationPercent,
      },
      parentPerformanceObligationId,
      parentPerformanceObligationId,
      parentPerformanceObligationId
    );
  twoStepAllocationSSPs.push(parentNodes.twoStepAllocationSSP);
  performanceObligationBundleHierarchies.push(
    parentNodes.performanceObligationBundleHierarchy
  );

  const childNewTwoStepAllocationSSPHierarchy =
    newTwoStepAllocationSSPHierarchies.find(
      ({ performanceObligationId }) =>
        performanceObligationId !== parentPerformanceObligationId &&
        performanceObligationId !== -1
    );
  const childExistTwoStepAllocationSSPHierarchy =
    existTwoStepAllocationSSPHierarchies.find(
      ({ twoStepAllocation: { performanceObligationId } }) =>
        performanceObligationId !== parentPerformanceObligationId
    );

  const childNodes =
    await upsertTwoStepAllocationSSPAndPerformanceObligationBundleHierarchyNodes(
      session,
      {
        id: childExistTwoStepAllocationSSPHierarchy?.id,
        twoStepAllocationPercent:
          childNewTwoStepAllocationSSPHierarchy.twoStepAllocationPercent,
      },
      parentPerformanceObligationId,
      childNewTwoStepAllocationSSPHierarchy.performanceObligationId,
      childExistTwoStepAllocationSSPHierarchy?.twoStepAllocation
        ?.performanceObligationId,
      true
    );

  twoStepAllocationSSPs.push(childNodes.twoStepAllocationSSP);
  performanceObligationBundleHierarchies.push(
    childNodes.performanceObligationBundleHierarchy
  );

  return {
    twoStepAllocationSSPs,
    performanceObligationBundleHierarchies,
  };
}

async function upsertTwoStepAllocationSSPAndPerformanceObligationBundleHierarchyNodes(
  session: ISession,
  newTwoStepAllocationSSP:
    | Partial<TwoStepAllocationSSP.TwoStepAllocationSSP>
    | TwoStepAllocationSSP.CreationFields,
  parentPerformanceObligationId: number,
  newPerformanceObligationId: number,
  oldPerformanceObligationId?: number,
  isChild: boolean = false
): Promise<{
  twoStepAllocationSSP: Partial<TwoStepAllocationSSP.TwoStepAllocationSSP>;
  performanceObligationBundleHierarchy: Partial<PerformanceObligationBundleHierarchy.PerformanceObligationBundleHierarchy>;
}> {
  let oldPerformanceObligationBundleHierarchy: Partial<PerformanceObligationBundleHierarchy.PerformanceObligationBundleHierarchy>;
  if (oldPerformanceObligationId) {
    // We just have this scenario in customer tenants.
    // Today when the POB have no child we do not create the POBBundle anymore.
    // But have some older tenants that have, because the old code produced.
    const oldPerformanceObligationBundleHierarchies =
      await PerformanceObligationBundleHierarchy.query(
        session,
        {
          where: {
            bundleId: parentPerformanceObligationId,
            performanceObligationId: oldPerformanceObligationId,
          },
        },
        ['id']
      );
    oldPerformanceObligationBundleHierarchy =
      oldPerformanceObligationBundleHierarchies[0];
  }

  const performanceObligationBundleHierarchy =
    await (oldPerformanceObligationBundleHierarchy?.id
      ? PerformanceObligationBundleHierarchy.update(
          session,
          {
            bundleId: parentPerformanceObligationId,
            performanceObligationId: oldPerformanceObligationId,
          },
          {
            bundleId: parentPerformanceObligationId,
            performanceObligationId: newPerformanceObligationId,
            nodeId: newPerformanceObligationId,
            contractAmountAllocationPercent: isChild
              ? new Decimal(0)
              : new Decimal(1),
            parentId: isChild ? parentPerformanceObligationId : undefined,
          },
          'ALL_FIELDS'
        )
      : PerformanceObligationBundleHierarchy.create(
          session,
          {
            bundleId: parentPerformanceObligationId,
            performanceObligationId: newPerformanceObligationId,
            nodeId: newPerformanceObligationId,
            contractAmountAllocationPercent: isChild
              ? new Decimal(0)
              : new Decimal(1),
            parentId: isChild ? parentPerformanceObligationId : undefined,
          },
          'ALL_FIELDS'
        ));

  const id = newTwoStepAllocationSSP['id'];
  delete newTwoStepAllocationSSP['id'];
  const twoStepAllocationSSP = await (id && oldPerformanceObligationId
    ? TwoStepAllocationSSP.update(
        session,
        { id },
        { ...newTwoStepAllocationSSP },
        'ALL_FIELDS'
      )
    : TwoStepAllocationSSP.create(
        session,
        {
          ...(newTwoStepAllocationSSP as TwoStepAllocationSSP.CreationFields),
          performanceObligationBundleHierarchyId:
            performanceObligationBundleHierarchy.id,
        },
        'ALL_FIELDS'
      ));

  return {
    twoStepAllocationSSP,
    performanceObligationBundleHierarchy,
  };
}

async function deleteTwoStepAllocationSSPAndPerformanceObligationBundleHierarchyNodes(
  session: ISession,
  parentPerformanceObligationId: number,
  existTwoStepAllocationSSPHierarchies?: CustomTwoStepAllocationSSPHierarchy[]
) {
  if (
    existTwoStepAllocationSSPHierarchies &&
    existTwoStepAllocationSSPHierarchies.length > 0
  ) {
    for (const {
      twoStepAllocation: { id, performanceObligationId },
    } of existTwoStepAllocationSSPHierarchies) {
      await Promise.all([
        TwoStepAllocationSSP.Delete(session, {
          id,
        }),
        PerformanceObligationBundleHierarchy.Delete(session, {
          bundleId: parentPerformanceObligationId,
          performanceObligationId,
        }),
      ]);
    }
  } else {
    // We just have this scenario in customer tenants.
    // Today when the POB have no child we do not create the POBBundle anymore.
    // But have some older tenants that have, because the old code produced.
    await PerformanceObligationBundleHierarchy.Delete(session, {
      bundleId: parentPerformanceObligationId,
      performanceObligationId: parentPerformanceObligationId,
    });
  }
}

async function validate(
  session: ISession,
  newTwoStepAllocationSSPHierarchies: Partial<TwoStepAllocationSSPHierarchy.TwoStepAllocationSSPHierarchy>[]
) {
  if (newTwoStepAllocationSSPHierarchies.length > 0) {
    let sum = new Decimal(0);
    for (const {
      twoStepAllocationPercent,
    } of newTwoStepAllocationSSPHierarchies) {
      sum = sum.plus(twoStepAllocationPercent);
    }
    if (!sum.equals(new Decimal(1))) {
      const percentageNot100ErrorMessage = await getTranslation(
        session,
        'performanceObligation.error.validation.pob.percentage',
        'everest.fin.accounting/performanceObligation'
      );
      throw new ValidationError( 
        percentageNot100ErrorMessage,
        'TWOSTEPALLOCATION_NOT_100%' //TODO: need to do something here? what is this code?
      );
    }
  }
}
