import type { ISession } from '@everestsystems/content-core';
import type { PlainDate } from '@everestsystems/datetime';
import type {
  JEHeaderForCreation,
  JEHeaderForUpdate,
  JELineForUpdate,
} from '@pkg/everest.fin.accounting/public/journalEntry/journalEntryTypes';
import { Account } from '@pkg/everest.fin.accounting/types/Account';
import { AmortizationScheduleHeader } from '@pkg/everest.fin.accounting/types/AmortizationScheduleHeader';
import type { AmortizationScheduleLine } from '@pkg/everest.fin.accounting/types/AmortizationScheduleLine';
import { EvstAccountSubType } from '@pkg/everest.fin.accounting/types/enums/AccountSubType';
import { EvstJournalEntryStatus } from '@pkg/everest.fin.accounting/types/enums/JournalEntryStatus';
import { EvstJournalEntryType } from '@pkg/everest.fin.accounting/types/enums/JournalEntryType';
import { EvstWorkflowApproval } from '@pkg/everest.fin.accounting/types/enums/WorkflowApproval';
import type { JournalEntryHeader } from '@pkg/everest.fin.accounting/types/JournalEntryHeader';
import type { JournalEntryLine } from '@pkg/everest.fin.accounting/types/JournalEntryLine';

export function getJournalEntryHeaderValues(
  amortizationScheduleHeader: Partial<AmortizationScheduleHeader.AmortizationScheduleHeaderWithAssociation>,
  postingDate: PlainDate,
  journalEntryHeaderId?: JournalEntryHeader.JournalEntryHeader['id']
): JEHeaderForUpdate | JEHeaderForCreation {
  if (journalEntryHeaderId) {
    const journalEntryHeader: JEHeaderForUpdate = {
      id: journalEntryHeaderId,
      postingDate,
      sourceFinancialDocumentId: amortizationScheduleHeader.id,
      sourceFinancialDocument: 'amortizationScheduleHeaderNumber',
      sourceFinancialDocumentPath: 'AmortizationScheduleHeader.id',
      sourceFinancialDocumentType: 'amortizationScheduleHeader',
      journalEntryName: `From Amortization Schedule ${amortizationScheduleHeader.amortizationScheduleHeaderNumber}`,
    };
    return journalEntryHeader;
  }
  const journalEntryHeader: JEHeaderForCreation = {
    postingDate,
    sourceFinancialDocumentId: amortizationScheduleHeader.id,
    sourceFinancialDocument: 'amortizationScheduleHeaderNumber',
    sourceFinancialDocumentPath: 'AmortizationScheduleHeader.id',
    sourceFinancialDocumentType: 'amortizationScheduleHeader',
    type: EvstJournalEntryType.AmortizationSchedule,
    approvalStatus: EvstWorkflowApproval.Approved,
    journalEntryStatus: EvstJournalEntryStatus.Posted,
    journalEntryName: `From Amortization Schedule ${amortizationScheduleHeader.amortizationScheduleHeaderNumber}`,
  };
  return journalEntryHeader;
}

export async function getJournalEntryLineValues(
  env: ISession,
  amortizationScheduleHeader: Partial<AmortizationScheduleHeader.AmortizationScheduleHeaderWithAssociation>,
  amortizationScheduleLines: Partial<AmortizationScheduleLine.AmortizationScheduleLine>[],
  journalEntryLines: Partial<JournalEntryLine.JournalEntryLine>[]
): Promise<JELineForUpdate[]> {
  const asJournalEntryLine = amortizationScheduleHeader.SourceJournalEntryLine;
  const jeLines: JELineForUpdate[] = [];
  for (const line of journalEntryLines) {
    const {
      id,
      accountId,
      transactionDate,
      transactionalCurrency,
      entityId,
      employeeId,
      exchangeRate,
      debit,
      credit,
      description,
      sourceTransLineId,
      sourceTransLinePath,
      sourceTransLineType,
      jeLocalLineNumber,
    } = line;
    const [account] = await Account.query(env, { where: { id: accountId } }, [
      'accountSubType',
    ]);
    const amortizationScheduleLine = amortizationScheduleLines.find(
      (asLine) => asLine.id === sourceTransLineId
    );
    const {
      recognitionAccountId,
      departmentId,
      businessUnitId,
      deferredAccountId,
    } = amortizationScheduleLine;

    const isPrepaidExpense =
      account.accountSubType === EvstAccountSubType.PrepaidExpenses;

    const jeLine: JELineForUpdate = {
      id,
      accountId: isPrepaidExpense ? deferredAccountId : recognitionAccountId,
      transactionDate,
      transactionalCurrency,
      entityId,
      businessPartnerId: asJournalEntryLine.businessPartnerId, //businessPartnerId should be same for both lines
      employeeId: isPrepaidExpense ? asJournalEntryLine.employeeId : employeeId,
      exchangeRate,
      debit,
      credit,
      description,
      sourceTransLineId,
      sourceTransLinePath,
      sourceTransLineType,
      departmentId: isPrepaidExpense
        ? asJournalEntryLine.departmentId
        : departmentId,
      businessUnitId: isPrepaidExpense
        ? asJournalEntryLine.businessUnitId
        : businessUnitId,
      jeLocalLineNumber,
      sourceTransLine: 'amortizationScheduleLineNumber',
    };
    jeLines.push(jeLine);
  }
  return jeLines;
}

export async function getHeader(session: ISession, id: number) {
  return await AmortizationScheduleHeader.read(session, { id }, [
    'id',
    'amortizationScheduleHeaderNumber',
    'entityId',
    'sourceFinancialDocumentDescription',
    {
      SourceJournalEntryLine: [
        'businessPartnerId',
        'businessUnitId',
        'departmentId',
        'transactionalCurrency',
        'employeeId',
      ],
    },
  ]);
}
