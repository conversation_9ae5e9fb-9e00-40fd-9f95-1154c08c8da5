import type { ISession } from '@everestsystems/content-core';
import { ValidationError } from '@everestsystems/content-core';
import { UpsertAmortizationMode } from '@pkg/everest.fin.accounting/public/amortization/types';
import { AmortizationScheduleHeader } from '@pkg/everest.fin.accounting/types/AmortizationScheduleHeader';
import { AmortizationScheduleLine } from '@pkg/everest.fin.accounting/types/AmortizationScheduleLine';
import { CreditCardEntryBusinessObject } from '@pkg/everest.fin.accounting/types/BOComposition/CreditCardEntryBusinessObject';
import { CreditCardEntryHeader } from '@pkg/everest.fin.accounting/types/CreditCardEntryHeader';
import { CreditCardEntryLine } from '@pkg/everest.fin.accounting/types/CreditCardEntryLine';
import { EvstCreditCardEntryLineType } from '@pkg/everest.fin.accounting/types/enums/CreditCardEntryLineType';
import { EvstJournalEntryType } from '@pkg/everest.fin.accounting/types/enums/JournalEntryType';
import { JournalEntryHeader } from '@pkg/everest.fin.accounting/types/JournalEntryHeader';
import { JournalEntryLine } from '@pkg/everest.fin.accounting/types/JournalEntryLine';
import { EvstFinancialProcess } from '@pkg/everest.fin.processor/types/enums/FinancialProcess';
import { FinancialProcessor } from '@pkg/everest.fin.processor/types/FinancialProcessor';
import { omit } from 'lodash';

import { skipCCEJournalEntry } from './CreditCardEntryUtils';

export type CreditCardEntryBusinessObjectUpsertMessage = {
  action: 'create' | 'update';
  payload: Partial<CreditCardEntryBusinessObject.CreditCardEntryBusinessObject>;
};

type AmortizationScheduleHeaderWithMetadata = Pick<
  AmortizationScheduleHeader.AmortizationScheduleHeader,
  | 'id'
  | 'amortizationStartDate'
  | 'term'
  | 'amortizationEndDate'
  | 'amortizationMethod'
  | 'description'
  | 'entityId'
  | 'deferredAccountId'
> & {
  $metadata?: {
    isDraft?: boolean;
  };
};

type CreditCardEntryLineForAmortization = Pick<
  CreditCardEntryLine.CreditCardEntryLine,
  | 'id'
  | 'amortizationScheduleHeaderId'
  | 'amount'
  | 'accountId'
  | 'departmentId'
  | 'businessUnitId'
>;

export default async function upsertCreditCardEntryJournalEntry(
  env: ISession,
  message: CreditCardEntryBusinessObjectUpsertMessage
) {
  const creditCardEntryBusinessObjectId = message?.payload?.id;
  if (!creditCardEntryBusinessObjectId) {
    throw new ValidationError(
      'Invalid credit card entry business object id',
      'CREDIT_CARD_ENTRY_JE'
    );
  }

  const creditCardEntry = await CreditCardEntryBusinessObject.read(
    env,
    { id: creditCardEntryBusinessObjectId },
    {
      CreditCardEntryHeader: ['id', 'entityId', 'skipJournalEntry'],
    }
  );
  const skipJECreation = await skipCCEJournalEntry(env, creditCardEntry);
  if (skipJECreation) {
    return;
  }

  const financialProcessorClient = await FinancialProcessor.client(env);
  await financialProcessorClient.runProcess(
    CreditCardEntryHeader.MODEL_URN,
    EvstFinancialProcess.UpsertJournalEntry,
    {
      entityIds: [creditCardEntry.entityId],
    },
    {
      modelUrn: CreditCardEntryHeader.MODEL_URN,
      id: creditCardEntry.id,
    }
  );
  const journalEntryHeaderId = await getJournalEntryId(env, creditCardEntry.id);
  if (journalEntryHeaderId) {
    await upsertAmortizationData(env, journalEntryHeaderId);
  }
}

export async function getJELineMap(
  session: ISession,
  journalEntryHeaderId: number
): Promise<Record<string, number>> {
  const existingJELines = await JournalEntryLine.query(
    session,
    {
      where: {
        journalEntryHeaderId,
      },
    },
    ['id', 'sourceTransLineId']
  );
  const jeLineMap: Record<string, number> = {};
  for (const line of existingJELines) {
    jeLineMap[String(line.sourceTransLineId)] = line.id;
  }
  return jeLineMap;
}

async function upsertAmortizationData(
  session: ISession,
  journalEntryHeaderId: number
) {
  const journalEntryLines = await JournalEntryLine.query(
    session,
    { where: { journalEntryHeaderId } },
    ['id', 'sourceTransLineId']
  );
  const ccPrepaidLines = await CreditCardEntryLine.query(
    session,
    {
      where: {
        id: { $in: journalEntryLines.map((line) => line.sourceTransLineId) },
        lineType: EvstCreditCardEntryLineType.Prepaid,
      },
    },
    [
      'id',
      'amortizationScheduleHeaderId',
      'amount',
      'accountId',
      'departmentId',
      'businessUnitId',
    ]
  );
  if (ccPrepaidLines.length === 0) {
    return;
  }

  const asHeaderToJeLineMap = new Map<number, number>();
  const asHeaderToCcLineMap = new Map<number, number>();
  for (const cc of ccPrepaidLines) {
    const je = journalEntryLines.find((je) => je.sourceTransLineId === cc.id);
    if (je) {
      asHeaderToJeLineMap.set(cc.amortizationScheduleHeaderId, je.id);
      asHeaderToCcLineMap.set(cc.amortizationScheduleHeaderId, cc.id);
    }
  }

  const client = await AmortizationScheduleHeader.client(session);

  // Get all amortization headers (both draft and non-draft)
  const allAmortizationHeaders = await client.query(
    {
      where: {
        id: {
          $in: ccPrepaidLines.map((line) => line.amortizationScheduleHeaderId),
        },
      },
      draft: 'include',
    },
    [
      'id',
      'amortizationStartDate',
      'term',
      'amortizationEndDate',
      'amortizationMethod',
      'description',
      'entityId',
      'deferredAccountId',
    ]
  );

  if (allAmortizationHeaders.length === 0) {
    return;
  }

  // Separate draft and non-draft headers
  const draftHeaders = allAmortizationHeaders.filter(
    (header) => header.$metadata?.isDraft
  );
  const nonDraftHeaders = allAmortizationHeaders.filter(
    (header) => !header.$metadata?.isDraft
  );

  // Handle draft amortizations (create new)
  await handleDraftAmortizations(
    session,
    draftHeaders,
    asHeaderToJeLineMap,
    asHeaderToCcLineMap,
    ccPrepaidLines,
    client
  );

  // Handle existing amortizations (update)
  await handleExistingAmortizations(
    session,
    nonDraftHeaders,
    asHeaderToCcLineMap,
    ccPrepaidLines,
    client
  );
}

async function handleDraftAmortizations(
  session: ISession,
  draftHeaders: AmortizationScheduleHeaderWithMetadata[],
  asHeaderToJeLineMap: Map<number, number>,
  asHeaderToCcLineMap: Map<number, number>,
  ccPrepaidLines: CreditCardEntryLineForAmortization[],
  client: Awaited<ReturnType<typeof AmortizationScheduleHeader.client>>
) {
  for (const header of draftHeaders) {
    const sourceFinancialDocumentId = asHeaderToJeLineMap.get(header.id);
    const ccLineId = asHeaderToCcLineMap.get(header.id);

    if (!sourceFinancialDocumentId || !ccLineId) {
      continue;
    }

    const ccLine = ccPrepaidLines.find((line) => line.id === ccLineId);
    if (!ccLine) {
      continue;
    }

    const amount = ccLine.amount.amount;

    const {
      id,
      amortizationStartDate,
      term,
      amortizationEndDate,
      amortizationMethod,
      description,
      entityId,
      deferredAccountId,
    } = header;

    const asHeaderData = {
      amortizationStartDate,
      term,
      amortizationEndDate,
      amortizationMethod,
      description,
      entityId,
      deferredAccountId,
      amount,
    };

    const [asLine] = await AmortizationScheduleLine.query(
      session,
      {
        where: {
          amortizationScheduleHeaderId: id,
        },
        take: 1,
        draft: 'only',
      },
      ['id', 'departmentId', 'recognitionAccountId', 'businessUnitId']
    );

    if (!asLine) {
      continue;
    }

    await client.delete({ id }, { draft: true });
    await AmortizationScheduleLine.deleteMany(
      session,
      { amortizationScheduleHeaderId: id },
      { draft: true }
    );

    const asHeader = await client.upsertAmortization(
      UpsertAmortizationMode.CreateAmortizationFromJournalEntry,
      {
        ...asHeaderData,
        sourceFinancialDocumentId,
      } as unknown as AmortizationScheduleHeader.AmortizationScheduleHeader,
      {
        departmentId: asLine.departmentId,
        recognitionAccountId: asLine.recognitionAccountId,
        businessUnitId: asLine?.businessUnitId,
      } as unknown as AmortizationScheduleLine.AmortizationScheduleLine,
      ['id']
    );

    await CreditCardEntryLine.update(
      session,
      { id: ccLineId },
      { amortizationScheduleHeaderId: asHeader.id },
      ['id']
    );
  }
}

async function handleExistingAmortizations(
  session: ISession,
  nonDraftHeaders: AmortizationScheduleHeaderWithMetadata[],
  asHeaderToCcLineMap: Map<number, number>,
  ccPrepaidLines: CreditCardEntryLineForAmortization[],
  client: Awaited<ReturnType<typeof AmortizationScheduleHeader.client>>
) {
  for (const header of nonDraftHeaders) {
    const ccLineId = asHeaderToCcLineMap.get(header.id);
    if (!ccLineId) {
      continue;
    }

    const ccLine = ccPrepaidLines.find((line) => line.id === ccLineId);
    if (!ccLine) {
      continue;
    }

    // Get the corresponding amortization schedule line
    const [amortizationLine] = await AmortizationScheduleLine.query(
      session,
      {
        where: {
          amortizationScheduleHeaderId: header.id,
        },
        take: 1,
      },
      ['id', 'recognitionAccountId', 'departmentId', 'businessUnitId']
    );

    if (!amortizationLine) {
      continue;
    }

    // Update the amortization with new data from credit card line
    const updatedAmortizationLine = {
      ...amortizationLine,
      departmentId: ccLine?.departmentId ?? amortizationLine?.departmentId,
      businessUnitId:
        ccLine?.businessUnitId ?? amortizationLine?.businessUnitId,
    };

    await client.upsertAmortization(
      UpsertAmortizationMode.UpdateAmortizationWithHistoricalImpact,
      omit(
        header,
        '$metadata'
      ) as AmortizationScheduleHeader.AmortizationScheduleHeader,
      updatedAmortizationLine as unknown as AmortizationScheduleLine.AmortizationScheduleLine,
      ['id']
    );
  }
}

async function getJournalEntryId(
  env: ISession,
  creditCardEntryHeaderId: number
): Promise<number | null> {
  const journalEntryHeader = await JournalEntryHeader.query(
    env,
    {
      where: {
        type: EvstJournalEntryType.CreditCardEntry,
        sourceFinancialDocumentId: creditCardEntryHeaderId,
      },
    },
    ['id']
  );
  if (journalEntryHeader.length === 0) {
    return null;
  }
  return journalEntryHeader[0].id;
}
