import type { ISession } from '@everestsystems/content-core';
import { ValidationError } from '@everestsystems/content-core';
import { UpsertAmortizationMode } from '@pkg/everest.fin.accounting/public/amortization/types';
import { AmortizationScheduleHeader } from '@pkg/everest.fin.accounting/types/AmortizationScheduleHeader';
import { AmortizationScheduleLine } from '@pkg/everest.fin.accounting/types/AmortizationScheduleLine';
import { CreditCardEntryBusinessObject } from '@pkg/everest.fin.accounting/types/BOComposition/CreditCardEntryBusinessObject';
import { CreditCardEntryHeader } from '@pkg/everest.fin.accounting/types/CreditCardEntryHeader';
import { CreditCardEntryLine } from '@pkg/everest.fin.accounting/types/CreditCardEntryLine';
import { EvstCreditCardEntryLineType } from '@pkg/everest.fin.accounting/types/enums/CreditCardEntryLineType';
import { JournalEntryHeader } from '@pkg/everest.fin.accounting/types/JournalEntryHeader';
import { JournalEntryLine } from '@pkg/everest.fin.accounting/types/JournalEntryLine';
import { EvstFinancialProcess } from '@pkg/everest.fin.processor/types/enums/FinancialProcess';
import { FinancialProcessor } from '@pkg/everest.fin.processor/types/FinancialProcessor';

import type { CreditCardEntryBusinessObjectUpsertMessage } from './upsertCreditCardEntryJournalEntry.action';
import upsertCreditCardEntryJournalEntry, {
  getJELineMap,
} from './upsertCreditCardEntryJournalEntry.action';

jest.mock('./CreditCardEntryUtils', () => ({
  skipCCEJournalEntry: jest.fn().mockResolvedValue(false),
}));
jest.mock(
  '@pkg/everest.fin.accounting/types/BOComposition/CreditCardEntryBusinessObject'
);
jest.mock('@pkg/everest.fin.processor/types/FinancialProcessor');
jest.mock('@pkg/everest.fin.accounting/types/JournalEntryHeader');
jest.mock('@pkg/everest.fin.accounting/types/JournalEntryLine');
jest.mock('@pkg/everest.fin.accounting/types/CreditCardEntryLine');
jest.mock('@pkg/everest.fin.accounting/types/AmortizationScheduleHeader');
jest.mock('@pkg/everest.fin.accounting/types/AmortizationScheduleLine');

describe('upsertCreditCardEntryJournalEntry', () => {
  const mockEnv: ISession = {} as ISession;
  const mockMessage: CreditCardEntryBusinessObjectUpsertMessage = {
    action: 'create',
    payload: { id: 123 },
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should throw ValidationError if creditCardEntryBusinessObjectId is missing', async () => {
    await expect(
      upsertCreditCardEntryJournalEntry(mockEnv, {
        action: 'create',
        payload: {},
      })
    ).rejects.toThrow(ValidationError);
  });

  it('should process credit card entry and upsert journal entry', async () => {
    const mockCreditCardEntry = { id: 123, entityId: 456 };
    (CreditCardEntryBusinessObject.read as jest.Mock).mockResolvedValue(
      mockCreditCardEntry
    );

    const mockFinancialProcessorClient = {
      runProcess: jest.fn(),
    };
    (FinancialProcessor.client as jest.Mock).mockResolvedValue(
      mockFinancialProcessorClient
    );

    (JournalEntryHeader.query as jest.Mock).mockResolvedValue([{ id: 789 }]);
    (JournalEntryLine.query as jest.Mock).mockResolvedValue([]);
    (CreditCardEntryLine.query as jest.Mock).mockResolvedValue([]);

    await upsertCreditCardEntryJournalEntry(mockEnv, mockMessage);

    expect(CreditCardEntryBusinessObject.read).toHaveBeenCalledWith(
      mockEnv,
      { id: 123 },
      { CreditCardEntryHeader: ['id', 'entityId', 'skipJournalEntry'] }
    );

    expect(mockFinancialProcessorClient.runProcess).toHaveBeenCalledWith(
      CreditCardEntryHeader.MODEL_URN,
      EvstFinancialProcess.UpsertJournalEntry,
      { entityIds: [456] },
      { modelUrn: CreditCardEntryHeader.MODEL_URN, id: 123 }
    );

    expect(JournalEntryHeader.query).toHaveBeenCalled();
  });

  it('should not upsert amortization data if journal entry is not found', async () => {
    (CreditCardEntryBusinessObject.read as jest.Mock).mockResolvedValue({
      id: 123,
      entityId: 456,
    });
    (FinancialProcessor.client as jest.Mock).mockResolvedValue({
      runProcess: jest.fn(),
    });
    (JournalEntryHeader.query as jest.Mock).mockResolvedValue([]);

    await upsertCreditCardEntryJournalEntry(mockEnv, mockMessage);

    expect(JournalEntryLine.query).not.toHaveBeenCalled();
    expect(CreditCardEntryLine.query).not.toHaveBeenCalled();
  });

  it('should handle draft amortizations correctly', async () => {
    (CreditCardEntryBusinessObject.read as jest.Mock).mockResolvedValue({
      id: 123,
      entityId: 456,
    });
    (FinancialProcessor.client as jest.Mock).mockResolvedValue({
      runProcess: jest.fn(),
    });
    (JournalEntryHeader.query as jest.Mock).mockResolvedValue([{ id: 789 }]);

    (JournalEntryLine.query as jest.Mock).mockResolvedValue([
      { id: 1, sourceTransLineId: 10 },
    ]);

    (CreditCardEntryLine.query as jest.Mock).mockResolvedValue([
      {
        id: 10,
        amortizationScheduleHeaderId: 100,
        amount: { amount: 1000 },
        accountId: 500,
        departmentId: 600,
        businessUnitId: 700,
        lineType: EvstCreditCardEntryLineType.Prepaid,
      },
    ]);

    const mockAmortizationClient = {
      query: jest.fn().mockResolvedValue([
        {
          id: 100,
          amortizationStartDate: '2023-01-01',
          term: 12,
          amortizationEndDate: '2023-12-31',
          amortizationMethod: 'straight-line',
          description: 'Test Draft',
          entityId: 456,
          deferredAccountId: 789,
          $metadata: { isDraft: true },
        },
      ]),
      delete: jest.fn(),
      upsertAmortization: jest.fn().mockResolvedValue({ id: 200 }),
    };
    (AmortizationScheduleHeader.client as jest.Mock).mockResolvedValue(
      mockAmortizationClient
    );

    (AmortizationScheduleLine.query as jest.Mock).mockResolvedValue([
      {
        id: 1000,
        departmentId: 1,
        recognitionAccountId: 2,
        businessUnitId: 3,
      },
    ]);

    (AmortizationScheduleLine.deleteMany as jest.Mock).mockResolvedValue(
      undefined
    );
    (CreditCardEntryLine.update as jest.Mock).mockResolvedValue([{ id: 10 }]);

    await upsertCreditCardEntryJournalEntry(mockEnv, mockMessage);

    expect(mockAmortizationClient.delete).toHaveBeenCalledWith(
      { id: 100 },
      { draft: true }
    );
    expect(AmortizationScheduleLine.deleteMany).toHaveBeenCalledWith(
      mockEnv,
      { amortizationScheduleHeaderId: 100 },
      { draft: true }
    );
    expect(mockAmortizationClient.upsertAmortization).toHaveBeenCalledWith(
      UpsertAmortizationMode.CreateAmortizationFromJournalEntry,
      expect.objectContaining({
        amortizationStartDate: '2023-01-01',
        term: 12,
        amortizationEndDate: '2023-12-31',
        amortizationMethod: 'straight-line',
        description: 'Test Draft',
        entityId: 456,
        deferredAccountId: 789,
        amount: 1000,
        sourceFinancialDocumentId: 1,
      }),
      expect.objectContaining({
        departmentId: 1,
        recognitionAccountId: 2,
        businessUnitId: 3,
      }),
      ['id']
    );
    expect(CreditCardEntryLine.update).toHaveBeenCalledWith(
      mockEnv,
      { id: 10 },
      { amortizationScheduleHeaderId: 200 },
      ['id']
    );
  });

  it('should handle existing (non-draft) amortizations correctly', async () => {
    (CreditCardEntryBusinessObject.read as jest.Mock).mockResolvedValue({
      id: 123,
      entityId: 456,
    });
    (FinancialProcessor.client as jest.Mock).mockResolvedValue({
      runProcess: jest.fn(),
    });
    (JournalEntryHeader.query as jest.Mock).mockResolvedValue([{ id: 789 }]);

    (JournalEntryLine.query as jest.Mock).mockResolvedValue([
      { id: 1, sourceTransLineId: 10 },
    ]);

    (CreditCardEntryLine.query as jest.Mock).mockResolvedValue([
      {
        id: 10,
        amortizationScheduleHeaderId: 100,
        amount: { amount: 1500 },
        accountId: 500,
        departmentId: 800,
        businessUnitId: 900,
        lineType: EvstCreditCardEntryLineType.Prepaid,
      },
    ]);

    const mockAmortizationClient = {
      query: jest.fn().mockResolvedValue([
        {
          id: 100,
          amortizationStartDate: '2023-01-01',
          term: 12,
          amortizationEndDate: '2023-12-31',
          amortizationMethod: 'straight-line',
          description: 'Test Existing',
          entityId: 456,
          deferredAccountId: 789,
          $metadata: { isDraft: false },
        },
      ]),
      upsertAmortization: jest.fn().mockResolvedValue({ id: 100 }),
    };
    (AmortizationScheduleHeader.client as jest.Mock).mockResolvedValue(
      mockAmortizationClient
    );

    (AmortizationScheduleLine.query as jest.Mock).mockResolvedValue([
      {
        id: 2000,
        recognitionAccountId: 3000,
        departmentId: 4000,
        businessUnitId: 5000,
      },
    ]);

    await upsertCreditCardEntryJournalEntry(mockEnv, mockMessage);

    expect(mockAmortizationClient.upsertAmortization).toHaveBeenCalledWith(
      UpsertAmortizationMode.UpdateAmortizationWithHistoricalImpact,
      expect.objectContaining({
        id: 100,
        amortizationStartDate: '2023-01-01',
        term: 12,
        amortizationEndDate: '2023-12-31',
        amortizationMethod: 'straight-line',
        description: 'Test Existing',
        entityId: 456,
        deferredAccountId: 789,
      }),
      expect.objectContaining({
        id: 2000,
        recognitionAccountId: 3000,
        departmentId: 800, // Updated from credit card line
        businessUnitId: 900, // Updated from credit card line
      }),
      ['id']
    );
  });

  it('should return early if no prepaid credit card lines exist', async () => {
    (CreditCardEntryBusinessObject.read as jest.Mock).mockResolvedValue({
      id: 123,
      entityId: 456,
    });
    (FinancialProcessor.client as jest.Mock).mockResolvedValue({
      runProcess: jest.fn(),
    });
    (JournalEntryHeader.query as jest.Mock).mockResolvedValue([{ id: 789 }]);

    (JournalEntryLine.query as jest.Mock).mockResolvedValue([
      { id: 1, sourceTransLineId: 10 },
    ]);

    // Mock no prepaid lines
    (CreditCardEntryLine.query as jest.Mock).mockResolvedValue([]);

    const mockAmortizationClient = {
      query: jest.fn(),
      upsertAmortization: jest.fn(),
    };
    (AmortizationScheduleHeader.client as jest.Mock).mockResolvedValue(
      mockAmortizationClient
    );

    await upsertCreditCardEntryJournalEntry(mockEnv, mockMessage);

    expect(mockAmortizationClient.query).not.toHaveBeenCalled();
    expect(mockAmortizationClient.upsertAmortization).not.toHaveBeenCalled();
  });
});

describe('getJELineMap', () => {
  const mockSession: ISession = {} as ISession;

  it('should return a map of sourceTransLineId to JournalEntryLine id', async () => {
    (JournalEntryLine.query as jest.Mock).mockResolvedValue([
      { id: 1, sourceTransLineId: 100 },
      { id: 2, sourceTransLineId: 200 },
    ]);

    const result = await getJELineMap(mockSession, 789);

    expect(result).toEqual({
      '100': 1,
      '200': 2,
    });

    expect(JournalEntryLine.query).toHaveBeenCalledWith(
      mockSession,
      { where: { journalEntryHeaderId: 789 } },
      ['id', 'sourceTransLineId']
    );
  });

  it('should return an empty object if no journal entry lines are found', async () => {
    (JournalEntryLine.query as jest.Mock).mockResolvedValue([]);

    const result = await getJELineMap(mockSession, 789);

    expect(result).toEqual({});
  });
});
