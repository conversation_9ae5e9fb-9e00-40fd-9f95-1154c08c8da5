import type { ISession } from '@everestsystems/content-core';
import type { TestTeardown } from '@everestsystems/content-core/lib/tests';
import { setupTest } from '@everestsystems/content-core/lib/tests';
import { PlainDate } from '@everestsystems/datetime';
import { Decimal } from '@everestsystems/decimal';
import { UpsertAmortizationMode } from '@pkg/everest.fin.accounting/public/amortization/types';
import { AccountingPeriod } from '@pkg/everest.fin.accounting/types/AccountingPeriod';
import { AmortizationScheduleHeader } from '@pkg/everest.fin.accounting/types/AmortizationScheduleHeader';
import { AmortizationScheduleLine } from '@pkg/everest.fin.accounting/types/AmortizationScheduleLine';
import { BankEntryLineNew } from '@pkg/everest.fin.accounting/types/BankEntryLineNew';
import { EvstBankEntryLineType } from '@pkg/everest.fin.accounting/types/enums/BankEntryLineType';
import { JournalEntryLine } from '@pkg/everest.fin.accounting/types/JournalEntryLine';

import {
  accountingPeriodValidation,
  upsertAmortizationData,
} from './bankEntryUtils';

/**
 * @group p2p/banking
 */
describe('bankEntryValidations', () => {
  let session: ISession;
  let teardown: TestTeardown;

  const findOpenAccountingPeriodMock = jest.fn();
  const journalEntryLineQueryMock = jest.fn();
  const bankEntryLineQueryMock = jest.fn();
  const amortizationHeaderClientMock = {
    query: jest.fn(),
    delete: jest.fn(),
    upsertAmortization: jest.fn(),
  };
  const amortizationLineQueryMock = jest.fn();
  const amortizationLineDeleteManyMock = jest.fn();
  const bankEntryLineUpdateMock = jest.fn();

  beforeAll(async () => {
    ({ session, teardown } = await setupTest({
      withAppserver: false,
    }));
    jest.spyOn(AccountingPeriod, 'client').mockResolvedValue({
      findOpenAccountingPeriod: findOpenAccountingPeriodMock,
    } as unknown as AccountingPeriod.IControllerClient);

    jest
      .spyOn(JournalEntryLine, 'query')
      .mockImplementation(journalEntryLineQueryMock);
    jest
      .spyOn(BankEntryLineNew, 'query')
      .mockImplementation(bankEntryLineQueryMock);
    jest
      .spyOn(BankEntryLineNew, 'update')
      .mockImplementation(bankEntryLineUpdateMock);
    jest
      .spyOn(AmortizationScheduleHeader, 'client')
      .mockResolvedValue(
        amortizationHeaderClientMock as unknown as AmortizationScheduleHeader.IControllerClient
      );
    jest
      .spyOn(AmortizationScheduleLine, 'query')
      .mockImplementation(amortizationLineQueryMock);
    jest
      .spyOn(AmortizationScheduleLine, 'deleteMany')
      .mockImplementation(amortizationLineDeleteManyMock);
  });

  afterEach(() => jest.clearAllMocks());
  afterAll(() => teardown());

  describe('accounting period validations', () => {
    it('should throw error for closed accounting period', async () => {
      findOpenAccountingPeriodMock.mockResolvedValueOnce({ closed: true });
      await expect(
        accountingPeriodValidation(
          session,
          1,
          PlainDate.from(new Date().toISOString())
        )
      ).rejects.toThrow(
        'Please select another date as posting period is closed or locked.'
      );
    });

    it('should throw error for locked accounting period', async () => {
      findOpenAccountingPeriodMock.mockResolvedValueOnce({ locked: true });
      await expect(
        accountingPeriodValidation(
          session,
          1,
          PlainDate.from(new Date().toISOString())
        )
      ).rejects.toThrow(
        'Please select another date as posting period is closed or locked.'
      );
    });

    it('should not fail for open accouting period', async () => {
      findOpenAccountingPeriodMock.mockResolvedValueOnce({
        locked: false,
        closed: false,
      });
      await expect(
        accountingPeriodValidation(
          session,
          1,
          PlainDate.from(new Date().toISOString())
        )
      ).resolves.not.toThrow();
    });

    it('should not fail for undefined output', async () => {
      findOpenAccountingPeriodMock.mockResolvedValueOnce({});
      await expect(
        accountingPeriodValidation(
          session,
          1,
          PlainDate.from(new Date().toISOString())
        )
      ).resolves.not.toThrow();
    });
  });

  describe('upsertAmortizationData', () => {
    it('should return early when no prepaid bank entry lines exist', async () => {
      const journalEntryHeaderId = 123;

      journalEntryLineQueryMock.mockResolvedValueOnce([
        { id: 1, sourceTransLineId: 10 },
        { id: 2, sourceTransLineId: 20 },
      ]);

      bankEntryLineQueryMock.mockResolvedValueOnce([]); // No prepaid lines

      await upsertAmortizationData(session, journalEntryHeaderId);

      expect(journalEntryLineQueryMock).toHaveBeenCalledWith(
        session,
        { where: { journalEntryHeaderId } },
        ['id', 'sourceTransLineId']
      );
      expect(bankEntryLineQueryMock).toHaveBeenCalledWith(
        session,
        {
          where: {
            id: { $in: [10, 20] },
            lineType: EvstBankEntryLineType.Prepaid,
          },
        },
        [
          'id',
          'amortizationScheduleHeaderId',
          'amount',
          'accountId',
          'departmentId',
          'businessUnitId',
        ]
      );
      expect(amortizationHeaderClientMock.query).not.toHaveBeenCalled();
    });

    it('should handle draft amortization headers by creating new amortizations', async () => {
      const journalEntryHeaderId = 123;

      journalEntryLineQueryMock.mockResolvedValueOnce([
        { id: 1, sourceTransLineId: 10 },
      ]);

      bankEntryLineQueryMock.mockResolvedValueOnce([
        {
          id: 10,
          amortizationScheduleHeaderId: 100,
          amount: { amount: new Decimal(1000) },
          accountId: 5001,
          departmentId: 101,
          businessUnitId: 201,
        },
      ]);

      amortizationHeaderClientMock.query.mockResolvedValueOnce([
        {
          id: 100,
          amortizationStartDate: PlainDate.from('2024-01-01'),
          term: 12,
          amortizationEndDate: PlainDate.from('2024-12-31'),
          amortizationMethod: 'straight-line',
          description: 'Test Amortization',
          entityId: 1,
          deferredAccountId: 5002,
          $metadata: { isDraft: true },
        },
      ]);

      amortizationLineQueryMock.mockResolvedValueOnce([
        {
          id: 1001,
          departmentId: 101,
          recognitionAccountId: 5003,
          businessUnitId: 201,
        },
      ]);

      amortizationHeaderClientMock.upsertAmortization.mockResolvedValueOnce({
        id: 200,
      });

      await upsertAmortizationData(session, journalEntryHeaderId);

      expect(amortizationHeaderClientMock.delete).toHaveBeenCalledWith(
        { id: 100 },
        { draft: true }
      );
      expect(amortizationLineDeleteManyMock).toHaveBeenCalledWith(
        session,
        { amortizationScheduleHeaderId: 100 },
        { draft: true }
      );
      expect(
        amortizationHeaderClientMock.upsertAmortization
      ).toHaveBeenCalledWith(
        UpsertAmortizationMode.CreateAmortizationFromJournalEntry,
        expect.objectContaining({
          amortizationStartDate: PlainDate.from('2024-01-01'),
          term: 12,
          amount: new Decimal(1000),
          sourceFinancialDocumentId: 1,
        }),
        expect.objectContaining({
          departmentId: 101,
          recognitionAccountId: 5003,
          businessUnitId: 201,
        }),
        ['id']
      );
      expect(bankEntryLineUpdateMock).toHaveBeenCalledWith(
        session,
        { id: 10 },
        { amortizationScheduleHeaderId: 200 },
        ['id']
      );
    });

    it('should handle existing amortization headers by updating with historical impact', async () => {
      const journalEntryHeaderId = 456;

      journalEntryLineQueryMock.mockResolvedValueOnce([
        { id: 2, sourceTransLineId: 20 },
      ]);

      bankEntryLineQueryMock.mockResolvedValueOnce([
        {
          id: 20,
          amortizationScheduleHeaderId: 300,
          amount: { amount: new Decimal(2000) },
          accountId: 5004,
          departmentId: 102,
          businessUnitId: 202,
        },
      ]);

      amortizationHeaderClientMock.query.mockResolvedValueOnce([
        {
          id: 300,
          amortizationStartDate: PlainDate.from('2024-02-01'),
          term: 24,
          amortizationEndDate: PlainDate.from('2026-01-31'),
          amortizationMethod: 'declining-balance',
          description: 'Existing Amortization',
          entityId: 1,
          deferredAccountId: 5005,
          $metadata: { isDraft: false },
        },
      ]);

      amortizationLineQueryMock.mockResolvedValueOnce([
        {
          id: 2001,
          recognitionAccountId: 5006,
          departmentId: 999,
          businessUnitId: 888,
        },
      ]);

      await upsertAmortizationData(session, journalEntryHeaderId);

      expect(amortizationHeaderClientMock.delete).not.toHaveBeenCalled();
      expect(amortizationLineDeleteManyMock).not.toHaveBeenCalled();
      expect(bankEntryLineUpdateMock).not.toHaveBeenCalled();

      expect(
        amortizationHeaderClientMock.upsertAmortization
      ).toHaveBeenCalledWith(
        UpsertAmortizationMode.UpdateAmortizationWithHistoricalImpact,
        expect.objectContaining({
          id: 300,
          amortizationStartDate: PlainDate.from('2024-02-01'),
          term: 24,
          amortizationEndDate: PlainDate.from('2026-01-31'),
          amortizationMethod: 'declining-balance',
          description: 'Existing Amortization',
          entityId: 1,
          deferredAccountId: 5005,
        }),
        expect.objectContaining({
          id: 2001,
          recognitionAccountId: 5006,
          departmentId: 102,
          businessUnitId: 202,
        }),
        ['id']
      );
    });
  });
});
