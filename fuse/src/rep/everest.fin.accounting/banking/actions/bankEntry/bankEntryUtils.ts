import type { ISession } from '@everestsystems/content-core';
import { ValidationError } from '@everestsystems/content-core';
import type { PlainDate } from '@everestsystems/datetime';
import { Decimal } from '@everestsystems/decimal';
import { UpsertAmortizationMode } from '@pkg/everest.fin.accounting/public/amortization/types';
import type {
  JEHeaderForCreation,
  JELineForCreation,
} from '@pkg/everest.fin.accounting/public/journalEntry/journalEntryTypes';
import { AccountingPeriod } from '@pkg/everest.fin.accounting/types/AccountingPeriod';
import { AmortizationScheduleHeader } from '@pkg/everest.fin.accounting/types/AmortizationScheduleHeader';
import { AmortizationScheduleLine } from '@pkg/everest.fin.accounting/types/AmortizationScheduleLine';
import type { BankEntryHeaderNew } from '@pkg/everest.fin.accounting/types/BankEntryHeaderNew';
import { BankEntryLineNew } from '@pkg/everest.fin.accounting/types/BankEntryLineNew';
import { EvstBankEntryLineType } from '@pkg/everest.fin.accounting/types/enums/BankEntryLineType';
import { EvstJournalEntryStatus } from '@pkg/everest.fin.accounting/types/enums/JournalEntryStatus';
import { EvstJournalEntryType } from '@pkg/everest.fin.accounting/types/enums/JournalEntryType';
import { EvstWorkflowApproval } from '@pkg/everest.fin.accounting/types/enums/WorkflowApproval';
import { JournalEntryLine } from '@pkg/everest.fin.accounting/types/JournalEntryLine';
import { omit } from 'lodash';

type AmortizationScheduleHeaderWithMetadata = Pick<
  AmortizationScheduleHeader.AmortizationScheduleHeader,
  | 'id'
  | 'amortizationStartDate'
  | 'term'
  | 'amortizationEndDate'
  | 'amortizationMethod'
  | 'description'
  | 'entityId'
  | 'deferredAccountId'
> & {
  $metadata?: {
    isDraft?: boolean;
  };
};

type BankEntryLineForAmortization = Pick<
  BankEntryLineNew.BankEntryLineNew,
  | 'id'
  | 'amortizationScheduleHeaderId'
  | 'amount'
  | 'accountId'
  | 'departmentId'
  | 'businessUnitId'
>;

export function getJournalEntryHeaderForBankEntry(
  bankEntryHeader: Pick<
    BankEntryHeaderNew.BankEntryHeaderNew,
    'id' | 'transactionDate' | 'bankEntryHeaderNewNumber'
  >
): JEHeaderForCreation {
  return {
    postingDate: bankEntryHeader.transactionDate,
    sourceFinancialDocumentId: bankEntryHeader.id,
    sourceFinancialDocument: 'bankEntryHeaderNewNumber',
    sourceFinancialDocumentPath: 'BankEntryHeaderNew.id',
    sourceFinancialDocumentType: 'BankEntryHeaderNew',
    type: EvstJournalEntryType.BankEntryNew,
    journalEntryStatus: EvstJournalEntryStatus.Posted,
    approvalStatus: EvstWorkflowApproval.Approved,
    journalEntryName: `From Bank Entry ${bankEntryHeader.bankEntryHeaderNewNumber}`,
  };
}

export function getJournalEntryLinesForBankEntry(
  bankEntryLine: Pick<
    BankEntryLineNew.BankEntryLineNew,
    | 'id'
    | 'description'
    | 'amount'
    | 'departmentId'
    | 'accountId'
    | 'businessPartnerId'
    | 'businessUnitId'
  >,
  bankEntryHeader: Pick<
    BankEntryHeaderNew.BankEntryHeaderNew,
    'id' | 'currency' | 'entityId' | 'transactionDate'
  >
): JELineForCreation {
  const baseJE: Partial<JELineForCreation> = {
    sourceTransLineId: bankEntryLine.id,
    sourceTransLine: 'bankEntryLineNewNumber',
    sourceTransLinePath:
      'urn:evst:everest:fin/accounting:model/node:BankEntryLineNew.id',
    sourceTransLineType: 'BankEntryLineNew',
    transactionDate: bankEntryHeader.transactionDate,
    transactionalCurrency: bankEntryHeader.currency,
    entityId: bankEntryHeader.entityId,
    description: bankEntryLine.description,
    businessPartnerId: bankEntryLine.businessPartnerId,
    departmentId: bankEntryLine.departmentId,
    accountId: bankEntryLine.accountId,
    businessUnitId: bankEntryLine?.businessUnitId ?? null,
  };
  const absoluteAmount = bankEntryLine?.amount?.amount?.absoluteValue();
  const jel: Partial<JELineForCreation> =
    bankEntryLine?.amount?.amount?.greaterThan(0)
      ? {
          ...baseJE,
          credit: { amount: absoluteAmount },
          debit: null,
        }
      : {
          ...baseJE,
          debit: { amount: absoluteAmount },
          credit: null,
        };
  return jel as JELineForCreation;
}

export function getJournalEntryLineToKeepBankEntryBalance(
  bankEntryLines: Array<
    Pick<
      BankEntryLineNew.BankEntryLineNew,
      'id' | 'description' | 'amount' | 'departmentId' | 'accountId'
    >
  >,
  bankEntryHeader: Pick<
    Partial<BankEntryHeaderNew.BankEntryHeaderNew>,
    keyof BankEntryHeaderNew.BankEntryHeaderNew
  >
): JELineForCreation {
  const baseJE: Partial<JELineForCreation> = {
    sourceTransLineId: bankEntryHeader.id,
    sourceTransLine: 'bankEntryHeaderNewNumber',
    sourceTransLinePath:
      'urn:evst:everest:fin/accounting:model/node:BankEntryHeaderNew.id',
    sourceTransLineType: 'BankEntryHeaderNew',
    transactionDate: bankEntryHeader.transactionDate,
    transactionalCurrency: bankEntryHeader.currency,
    entityId: bankEntryHeader.entityId,
    description: bankEntryHeader?.bankEntryDescription ?? null,
    accountId: bankEntryHeader.accountId,
  };
  const totalAmount = Decimal.getSum(
    bankEntryLines.map((bel) => bel?.amount?.amount ?? 0)
  );
  const totalAmountAbsolute = totalAmount.absoluteValue();
  const jel: Partial<JELineForCreation> = totalAmount.greaterThan(0)
    ? {
        ...baseJE,
        debit: { amount: totalAmountAbsolute },
        credit: null,
      }
    : {
        ...baseJE,
        credit: { amount: totalAmountAbsolute },
        debit: null,
      };
  return jel as JELineForCreation;
}

export function bankEntryValidations(
  bankEntryLines: Partial<BankEntryLineNew.BankEntryLineNew>[]
) {
  if (bankEntryLines.length === 0) {
    throw new ValidationError(
      'Cannot create or update bank entry without lines',
      'BANK_ENTRY_ERROR_01'
    );
  }
  let totalAmount = new Decimal(0);
  const ZERO = new Decimal(0);
  for (const line of bankEntryLines) {
    const { amount } = line.amount;
    if (new Decimal(amount).equals(ZERO)) {
      throw new ValidationError(
        'Individual lines should not have zero amount',
        'BANK_ENTRY_ERROR_02'
      );
    }
    totalAmount = totalAmount.plus(amount);
  }
  if (totalAmount.equals(ZERO)) {
    throw new ValidationError(
      'Total amount should not be zero',
      'BANK_ENTRY_ERROR_03'
    );
  }
}

export async function accountingPeriodValidation(
  session: ISession,
  entityId: number,
  postingDate: PlainDate
) {
  const accountingPeriodClient = await AccountingPeriod.client(session);
  const accountingPeriod =
    (await accountingPeriodClient.findOpenAccountingPeriod(
      entityId,
      postingDate,
      { journalEntryTypes: [EvstJournalEntryType.BankEntryNew] }
    )) as Partial<
      AccountingPeriod.AccountingPeriod & { closed: boolean; locked: boolean }
    >;

  const { closed = false, locked = false } = accountingPeriod ?? {};

  if (closed || locked) {
    throw new ValidationError(
      'Please select another date as posting period is closed or locked.',
      'POSTING_ERROR_01'
    );
  }
  return accountingPeriod.id;
}

export async function upsertAmortizationData(
  session: ISession,
  journalEntryHeaderId: number
) {
  const journalEntryLines = await JournalEntryLine.query(
    session,
    { where: { journalEntryHeaderId } },
    ['id', 'sourceTransLineId']
  );
  const bePrepaidLines = await BankEntryLineNew.query(
    session,
    {
      where: {
        id: { $in: journalEntryLines.map((line) => line.sourceTransLineId) },
        lineType: EvstBankEntryLineType.Prepaid,
      },
    },
    [
      'id',
      'amortizationScheduleHeaderId',
      'amount',
      'accountId',
      'departmentId',
      'businessUnitId',
    ]
  );
  if (bePrepaidLines.length === 0) {
    return;
  }

  const asHeaderToJeLineMap = new Map<number, number>();
  const asHeaderToBankLineMap = new Map<number, number>();

  for (const bankLine of bePrepaidLines) {
    const je = journalEntryLines.find(
      (je) => je.sourceTransLineId === bankLine.id
    );
    if (je) {
      asHeaderToJeLineMap.set(bankLine.amortizationScheduleHeaderId, je.id);
      asHeaderToBankLineMap.set(
        bankLine.amortizationScheduleHeaderId,
        bankLine.id
      );
    }
  }

  const client = await AmortizationScheduleHeader.client(session);

  // Get all amortization headers (both draft and non-draft)
  const allAmortizationHeaders = await client.query(
    {
      where: {
        id: {
          $in: bePrepaidLines.map((line) => line.amortizationScheduleHeaderId),
        },
      },
      draft: 'include',
    },
    [
      'id',
      'amortizationStartDate',
      'term',
      'amortizationEndDate',
      'amortizationMethod',
      'description',
      'entityId',
      'deferredAccountId',
    ]
  );

  if (allAmortizationHeaders.length === 0) {
    return;
  }

  // Separate draft and non-draft headers
  const draftHeaders = allAmortizationHeaders.filter(
    (header) => header.$metadata?.isDraft
  );
  const nonDraftHeaders = allAmortizationHeaders.filter(
    (header) => !header.$metadata?.isDraft
  );

  // Handle draft amortizations (create new)
  await handleDraftAmortizations(
    session,
    draftHeaders,
    asHeaderToJeLineMap,
    asHeaderToBankLineMap,
    bePrepaidLines,
    client
  );

  // Handle existing amortizations (update)
  await handleExistingAmortizations(
    session,
    nonDraftHeaders,
    asHeaderToBankLineMap,
    bePrepaidLines,
    client
  );
}

async function handleDraftAmortizations(
  session: ISession,
  draftHeaders: AmortizationScheduleHeaderWithMetadata[],
  asHeaderToJeLineMap: Map<number, number>,
  asHeaderToBankLineMap: Map<number, number>,
  bankPrepaidLines: BankEntryLineForAmortization[],
  client: Awaited<ReturnType<typeof AmortizationScheduleHeader.client>>
) {
  for (const header of draftHeaders) {
    const sourceFinancialDocumentId = asHeaderToJeLineMap.get(header.id);
    const bankLineId = asHeaderToBankLineMap.get(header.id);

    if (!sourceFinancialDocumentId || !bankLineId) {
      continue;
    }

    const bankLine = bankPrepaidLines.find((line) => line.id === bankLineId);
    if (!bankLine) {
      continue;
    }

    const amount = bankLine.amount.amount;

    const {
      id,
      amortizationStartDate,
      term,
      amortizationEndDate,
      amortizationMethod,
      description,
      entityId,
      deferredAccountId,
    } = header;

    const asHeaderData = {
      amortizationStartDate,
      term,
      amortizationEndDate,
      amortizationMethod,
      description,
      entityId,
      deferredAccountId,
      amount,
    };

    const [asLine] = await AmortizationScheduleLine.query(
      session,
      {
        where: {
          amortizationScheduleHeaderId: id,
        },
        take: 1,
        draft: 'only',
      },
      ['id', 'departmentId', 'recognitionAccountId', 'businessUnitId']
    );

    if (!asLine) {
      continue;
    }

    await client.delete({ id }, { draft: true });
    await AmortizationScheduleLine.deleteMany(
      session,
      { amortizationScheduleHeaderId: id },
      { draft: true }
    );

    const asHeader = await client.upsertAmortization(
      UpsertAmortizationMode.CreateAmortizationFromJournalEntry,
      {
        ...asHeaderData,
        sourceFinancialDocumentId,
      } as unknown as AmortizationScheduleHeader.AmortizationScheduleHeader,
      {
        departmentId: asLine.departmentId,
        recognitionAccountId: asLine.recognitionAccountId,
        businessUnitId: asLine?.businessUnitId,
      } as unknown as AmortizationScheduleLine.AmortizationScheduleLine,
      ['id']
    );

    await BankEntryLineNew.update(
      session,
      { id: bankLineId },
      { amortizationScheduleHeaderId: asHeader.id },
      ['id']
    );
  }
}

async function handleExistingAmortizations(
  session: ISession,
  nonDraftHeaders: AmortizationScheduleHeaderWithMetadata[],
  asHeaderToBankLineMap: Map<number, number>,
  bankPrepaidLines: BankEntryLineForAmortization[],
  client: Awaited<ReturnType<typeof AmortizationScheduleHeader.client>>
) {
  for (const header of nonDraftHeaders) {
    const bankLineId = asHeaderToBankLineMap.get(header.id);
    if (!bankLineId) {
      continue;
    }

    const bankLine = bankPrepaidLines.find((line) => line.id === bankLineId);
    if (!bankLine) {
      continue;
    }

    // Get the corresponding amortization schedule line
    const [amortizationLine] = await AmortizationScheduleLine.query(
      session,
      {
        where: {
          amortizationScheduleHeaderId: header.id,
        },
        take: 1,
      },
      ['id', 'recognitionAccountId', 'departmentId', 'businessUnitId']
    );

    if (!amortizationLine) {
      continue;
    }

    // Update the amortization with new data from bank entry line
    const updatedAmortizationLine = {
      ...amortizationLine,
      departmentId: bankLine?.departmentId ?? amortizationLine?.departmentId,
      businessUnitId:
        bankLine?.businessUnitId ?? amortizationLine?.businessUnitId,
    };

    await client.upsertAmortization(
      UpsertAmortizationMode.UpdateAmortizationWithHistoricalImpact,
      omit(
        header,
        '$metadata'
      ) as AmortizationScheduleHeader.AmortizationScheduleHeader,
      updatedAmortizationLine as unknown as AmortizationScheduleLine.AmortizationScheduleLine,
      ['id']
    );
  }
}
