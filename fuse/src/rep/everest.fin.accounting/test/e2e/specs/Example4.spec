# Logging in when there is no warm instance

When we have a cold instance, it might take longer - much longer then we would
like to wait in every scenario. Hence this scenario is very short.

## Test Scenario 

* Store fixture table below to key Reference key 

| Reference |                     journalEntryName                     |                 description                 | entityName | postingDate | functionalCurrency |
| --------- | -------------------------------------------------------- | ------------------------------------------- | ---------- | ----------- | ------------------ |
| je1       | {{random-string-with-prefix: Automation Journal Entry-}} | {{random-string-with-prefix: Description-}} | Flow Inc.  | 05/01/2021  | US Dollar          |

* Store fixture table below to key Reference key 

| Reference |       accountName       | debit | credit | businessPartner |     department     |   employee    |
| --------- | ----------------------- | ----- | ------ | --------------- | ------------------ | ------------- |
| jel1      | 111101 — Chase Checking | 1000  | 0      | Acme Inc.       | DPT-8 - Accounting | Expense User  |
| jel2      | 111101 — Chase Checking | 0     | 1000   | Acme Inc.       | DPT-8 - Accounting | Kolja Dummann |

* Create a new Journal Entry via API from 

|       Model        | Key  |
| ------------------ | ---- |
| JournalEntryHeader | je1  |
| JournalEntryLine   | jel1 |
| JournalEntryLine   | jel2 |

## Create SO using API

//////////////////////////////////
// Create new Sales Order from API
//////////////////////////////////

* Store fixture table below to key Reference key

| Reference | Customer  |  Entity   |      Booking Date      |     Service Start      | Evergreen | Term |     Price Book      |
| --------- | --------- | --------- | ---------------------- | ---------------------- | --------- | ---- | ------------------- |
| so1       | Softmatic | Flow Inc. | 01/01/{{current-year}} | 01/01/{{current-year}} | No        | 12   | Standard Price Book |

* Store fixture table below to key Reference key

| Reference |   Product   |        Product Name        |
| --------- | ----------- | -------------------------- |
| sop1      | FLOW-S      | Flow Software              |
| sop2      | FLOW-TIERED | Flow Video Tiered          |
| sop3      | FLOW-PS     | Flow Professional Services |

* Store fixture table below to key Reference key

| Reference |         Product Line         | Line Type | Pricing Term | Invoice Frequency | Unit of Measure | List Price | Price Model | Quantity | Discount Percentage | Discount Amount |      Service End       | Total Price |
| --------- | ---------------------------- | --------- | ------------ | ----------------- | --------------- | ---------- | ----------- | -------- | ------------------- | --------------- | ---------------------- | ----------- |
| sopl1     | Annual Recurring (Instance)  | Recurring | Annually     | Annually          | Instance        | 1,000.00   | Per Unit    | 1        |                     |                 |                        |             |
| sopl2     | Annual Recurring Tiered (GB) | Recurring | Annually     | Annually          | GB              |            | Tiered      | 1        |                     |                 |                        | 100.00      |
| sopl3     | One-Time (Hour)              | One-Time  | Once         | Once              | Hour            | 150.00     | Per Unit    | 1        | 50                  | 75              | 01/31/{{current-year}} |             |

* Store fixture table below to key Reference key

| Reference | Price Model | List Price | Unit Price |
| --------- | ----------- | ---------- | ---------- |
| soplt2    | Per Unit    | 100.00     | 100.00     |

* Store fixture table below to key Reference key

| Reference |           Identifier            | Auto Renew |
| --------- | ------------------------------- | ---------- |
| s1        | {{random-number-with-length:5}} | false      |

* Create a new sales order via API

| Sales Order Details |  SO Products   | SO Product Lines  | Subscription | SOPL Tiers |
| ------------------- | -------------- | ----------------- | ------------ | ---------- |
| so1                 | sop1,sop2,sop3 | sopl1,sopl2,sopl3 | s1           | -,soplt2,- |

* Approve "{{so1.salesOrderNumber}}" sales order via API
* From "{{so1.salesOrderNumber}}" sales order number, create invoice with actual date via API "01/01/{{current-year}}"

| Planned Invoice Dates  |
| ---------------------- |
| 01/01/{{current-year}} |
