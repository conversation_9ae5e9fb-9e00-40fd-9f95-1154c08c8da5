{"version": 2, "uicontroller": "connector.uicontroller.ts", "uimodel": {"state": {"form": {"processFutureEventsOnly": true, "registerWebhook": false}}, "nodes": {"stripe": {"type": "struct", "model": "urn:evst:everest:fin/integration/stripe:model/node:Stripe", "fieldList": []}, "connectorEngine": {"type": "struct", "model": "urn:evst:everest:connectorengine:model/node:ConnectorEngine", "fieldList": []}, "webhookRegistrationEnabled": {"modelId": "everest.appserver/ConfigurationModel.Configuration", "type": "struct", "getValue": {"name": "STRIPE_WEBHOOK_REGISTRATION_ENABLED_KEY", "packageName": "everest.fin.integration.stripe"}, "fieldList": ["value", "name"]}}}, "uiview": {"config": {"allowRefreshData": true, "autoRefreshData": true}, "i18n": ["everest.connectorengine/connectorEngine", "stripe"], "title": "{{stripe.connector.create}}", "sections": [{"component": "Block", "size": 12, "type": "secondary", "elements": [{"component": "Input", "label": "{{connectorEngine.name}}", "name": "name", "isEditing": true, "size": 3}, {"component": "Input", "type": "password", "label": "{{key}}", "name": "key", "isEditing": true, "size": 3}, {"component": "Checkbox", "label": "{{stripe.connector.processFutureEventsOnly}}", "name": "processFutureEventsOnly", "isEditing": true, "size": 1}, {"component": "Checkbox", "label": "{{stripe.connector.registerWebhook}}", "name": "registerWebhook", "isEditing": true, "visible": "@binding:webhookRegistrationEnabled", "size": 1}, {"component": "Input", "label": "{{stripe.connector.webhookUrl}}", "name": "webhookUrl", "isEditing": "@controller:isRegisterWebhook()", "value": "@controller:getWebhookUrl()", "visible": "@binding:webhookRegistrationEnabled", "size": 3}]}, {"component": "ButtonGroup", "direction": "horizontal", "actions": [{"label": "{{connectorEngine.test}}", "variant": "secondary", "onClick": "@controller:test", "disabled": "@controller:isTestDisabled()"}, {"label": "{{connectorEngine.create}}", "variant": "primary", "onClick": "@controller:save", "disabled": "@controller:isSaveDisabled()"}, {"label": "New Stripe Connector", "variant": "primary", "onClick": "@controller:navigateToNewStripeConnectorCreate"}]}]}}