{"version": 2, "uicontroller": "editConnector.uicontroller.ts", "uimodel": {"nodes": {"stripe": {"type": "struct", "model": "urn:evst:everest:fin/integration/stripe:model/node:Stripe", "fieldList": []}, "connectorEngine": {"type": "struct", "model": "urn:evst:everest:connectorengine:model/node:ConnectorEngine", "fieldList": ["id", "name"], "query": {"where": {"id": "@state:param.id"}}}}}, "uiview": {"config": {"allowRefreshData": true, "autoRefreshData": true}, "i18n": ["everest.connectorengine/connectorEngine", "stripe"], "title": "{{stripe.connector.edit}}", "sections": [{"component": "Block", "size": 12, "type": "secondary", "elements": [{"component": "Input", "label": "{{connectorEngine.name}}", "value": "@binding:connectorEngine.name", "isEditing": false}, {"component": "Input", "type": "password", "label": "{{key}}", "name": "key", "isEditing": true}]}, {"component": "ButtonGroup", "direction": "horizontal", "actions": [{"label": "{{connectorEngine.test}}", "variant": "secondary", "onClick": "@controller:test", "disabled": "@controller:isTestDisabled()"}, {"label": "{{connectorEngine.save}}", "variant": "primary", "onClick": "@controller:save", "disabled": "@controller:isSaveDisabled()"}]}]}}