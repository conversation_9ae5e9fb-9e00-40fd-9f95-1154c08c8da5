// @i18n:everest.connectorengine/connectorEngine
import { DivvyUI } from '@pkg/everest.fin.integration.divvy/types/Divvy.ui';

import type { ConnectorUiTemplate } from '../types/uiTemplates/uinext/connector.ui';

type ConnectorContext = ConnectorUiTemplate.ConnectorContext;

export type Context = ConnectorContext & {
  state: {
    connector: Record<string, unknown> & {
      id: number;
      configValues: Record<string, unknown>;
      extraData: Record<string, unknown>;
    };
  };
};
export function isEditing({ state }: Context) {
  return state.connector !== undefined;
}

export function getPrimaryLabel(ctx: Context) {
  return isEditing(ctx)
    ? '{{connectorEngine.save}}'
    : '{{connectorEngine.connect}}';
}

export function getName({ state }: Context) {
  return state.connector?.name || '';
}

export function isSandbox({ state }: Context) {
  return state.connector?.extraData?.isSandbox;
}

export function getToken({ state }: Context) {
  return state.connector?.configValues?.token || '';
}

export function cancel({ helpers }: Context) {
  helpers.closeModal();
}

export async function save(ctx: Context) {
  const { helpers, form, state } = ctx;
  const { name, token, isSandbox } = form.getFormValues();
  const { connector } = state;

  await DivvyUI.saveConnector(ctx, {
    args: {
      name,
      token,
      connectorId: connector?.id,
      isSandbox,
    },
  }).run('divvy');
  if (connector) {
    helpers.closeModal();
  } else {
    helpers.navigate({
      to: '/templates/everest.fin.integration.base.ui/uinext/overview/overview?providerName=Divvy',
      closeCurrentTab: true,
    });
  }
}
