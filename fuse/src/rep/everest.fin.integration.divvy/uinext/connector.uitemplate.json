{"version": 2, "uicontroller": "connector.uicontroller.ts", "uimodel": {"nodes": {"divvy": {"type": "struct", "model": "urn:evst:everest:fin/integration/divvy:model/node:Divvy"}}}, "uiview": {"templateType": "details", "i18n": ["everest.connectorengine/connectorEngine", "divvy"], "title": "{{connectorEngine.create}}", "actions": {"content": [{"variant": "secondary", "label": "{{connectorEngine.cancel}}", "onClick": "@controller:cancel", "align": "right", "visible": "@controller:isEditing()"}, {"variant": "primary", "label": "@controller:getPrimaryLabel()", "onClick": "@controller:save", "align": "right"}]}, "sections": {"content": [{"component": "Block", "section": {"grid": {"size": "12"}}, "props": {"type": "secondary", "elements": [{"component": "Input", "label": "{{divvy.name}}", "name": "name", "value": "@controller:getName()", "isEditing": true}, {"component": "Input", "label": "{{divvy.token}}", "name": "token", "type": "password", "isEditing": true}, {"component": "Checkbox", "name": "isSandbox", "label": "{{divvy.scope}}", "text": "{{divvy.divvySandbox}}", "value": "@controller:isSandbox()", "isEditing": true}]}}]}}}