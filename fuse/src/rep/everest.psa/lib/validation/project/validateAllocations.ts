import { PlainDate } from '@everestsystems/datetime';

/**
 * Checks if any allocation dates are outside the activity date range
 * @param allocationStartDates Array of allocation start dates
 * @param allocationEndDates Array of allocation end dates
 * @param activityStartDate Activity start date
 * @param activityEndDate Activity end date
 * @returns True if any allocations are outside the activity date range
 */
export function checkAllocationsOutsideDateRange(
  allocationStartDates: PlainDate[],
  allocationEndDates: PlainDate[],
  activityStartDate: PlainDate,
  activityEndDate: PlainDate
): boolean {
  // Check if any allocation starts before the activity start date
  const hasEarlyAllocations = allocationStartDates.some(
    (date) => PlainDate.compare(date, activityStartDate) < 0
  );

  // Check if any allocation ends after the activity end date
  const hasLateAllocations = allocationEndDates.some(
    (date) => PlainDate.compare(date, activityEndDate) > 0
  );

  // Return true if any allocations are outside the task date range
  return hasEarlyAllocations || hasLateAllocations;
}
