import type { ControllerClientProvider } from '@everestsystems/content-core';
import { EmployeeApi } from '@pkg/everest.hr.base/types/presentations/publicApi/api/EmployeeApi/EmployeeApi';
import { EmployeeView } from '@pkg/everest.hr.base/types/views/publicApi/view/EmployeeView/EmployeeView';

import PsaPermissionApi from './PsaPermissionApi';

export interface AccessibleEmployee {
  id: number;
  displayName: string;
  firstName: string;
  lastName: string;
  email: string;
}

export class PersonalDashboardPermissions {
  public static async getAccessibleEmployees(
    session: ControllerClientProvider
  ): Promise<AccessibleEmployee[]> {
    const { employeeId } = await EmployeeApi.getEmployeeId.execute(session, {});

    const isPsaAdmin =
      await PsaPermissionApi.isSuperAdminOrSandboxMerge(session);

    if (isPsaAdmin) {
      const employeeClient = await EmployeeView.client(session);
      const employees = await employeeClient.query(
        {
          where: {
            $or: [{ status: 'Active' }, { status: 'Planned' }],
          },
          orderBy: [{ field: 'displayName', ordering: 'asc' }],
        },
        ['id', 'displayName', 'firstName', 'lastName', 'email']
      );

      return employees.map((employee) => ({
        id: employee.id,
        displayName: employee.displayName,
        firstName: employee.firstName,
        lastName: employee.lastName,
        email: employee.email,
      }));
    } else {
      const employeeClient = await EmployeeView.client(session);
      const currentEmployee = await employeeClient.query(
        {
          where: { id: employeeId },
        },
        ['id', 'displayName', 'firstName', 'lastName', 'email']
      );

      if (currentEmployee.length === 0) {
        return [];
      }

      return [
        {
          id: currentEmployee[0].id,
          displayName: currentEmployee[0].displayName,
          firstName: currentEmployee[0].firstName,
          lastName: currentEmployee[0].lastName,
          email: currentEmployee[0].email,
        },
      ];
    }
  }

  public static async canViewEmployeeDashboard(
    session: ControllerClientProvider,
    targetEmployeeId: number
  ): Promise<boolean> {
    const { employeeId } = await EmployeeApi.getEmployeeId.execute(session, {});

    // Always allow viewing own dashboard
    if (employeeId === targetEmployeeId) {
      return true;
    }

    // Check if target employee is in the list of accessible employees
    const accessibleEmployees = await this.getAccessibleEmployees(session);
    const targetIsAccessible = accessibleEmployees.some(
      (employee) => employee.id === targetEmployeeId
    );

    return targetIsAccessible;
  }
}

export default PersonalDashboardPermissions;
