{"title": "View projects", "description": "View-only access to project-related functionality", "pages": {"urn:evst:everest:psa:page:modules/projects/pages/projectList/page/ProjectList": "view", "urn:evst:everest:psa:page:modules/projects/pages/projectDetail/page/ProjectDetail": "view", "urn:evst:everest:psa:page:modules/projects/pages/projectDetail/components/ExpensesTab/page/ExpensesTab": "view", "urn:evst:everest:psa:page:modules/projects/pages/projectDetail/components/FixedFeeTabs/page/FixedFee": "view", "urn:evst:everest:psa:page:modules/projects/pages/projectDetail/components/InvoicesTab/page/InvoicesTab": "view", "urn:evst:everest:psa:page:modules/projects/pages/projectDetail/components/MilestonesTab/page/MilestonesTab": "view", "urn:evst:everest:psa:page:modules/projects/pages/projectDetail/components/OverviewTab/page/OverviewTab": "view", "urn:evst:everest:psa:page:modules/projects/pages/projectDetail/components/OverviewTab/components/page/ProgressSection": "view", "urn:evst:everest:psa:page:modules/projects/pages/projectDetail/components/RolesTab/page/RolesTab": "view", "urn:evst:everest:psa:page:modules/projects/pages/projectDetail/components/TasksTab/page/TasksTab": "view", "urn:evst:everest:psa:page:modules/projects/pages/projectDetail/components/TeamTab/page/TeamTab": "view", "urn:evst:everest:psa:page:modules/projects/pages/projectDetail/components/TimeTab/page/TimeTab": "view", "urn:evst:everest:psa:page:modules/projects/pages/personal/page/Personal": "view", "urn:evst:everest:psa:page:modules/projects/pages/personal/components/ChartsTab/page/ChartsTab": "view", "urn:evst:everest:psa:page:modules/projects/pages/personal/components/ProjectsTab/page/ProjectsTab": "view", "urn:evst:everest:psa:page:modules/projects/pages/personal/components/TasksTab/page/TasksTab": "view"}}