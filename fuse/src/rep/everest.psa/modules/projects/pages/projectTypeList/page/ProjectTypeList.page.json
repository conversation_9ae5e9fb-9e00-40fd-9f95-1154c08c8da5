{"type": "react", "componentUrn": "urn:evst:everest:psa:artifact/ui/tsx:modules/projects/pages/projectTypeList/ProjectTypeList", "backendAccess": {"urn:evst:everest:psa:presentation:modules/projects/pages/projectTypeList/ProjectTypeList": ["ProjectTypes/view", "ProjectTypes/add", "ProjectTypes/change", "ProjectTypes/remove"]}, "variants": {"management": {"title": "Project type management", "description": "Full management access to project types"}, "view": {"title": "Project type view", "description": "View-only access to project types", "excludedBackendAccess": {"urn:evst:everest:psa:presentation:modules/projects/pages/projectTypeList/ProjectTypeList": ["ProjectTypes/add", "ProjectTypes/change", "ProjectTypes/remove"]}}}}