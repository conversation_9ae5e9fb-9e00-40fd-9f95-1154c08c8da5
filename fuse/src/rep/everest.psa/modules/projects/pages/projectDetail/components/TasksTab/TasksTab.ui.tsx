/**
 * # Tasks Tab in Project Detail page
 */
// @i18n:psa

import React, {
  useState,
  useEffect,
  useMemo,
  useRef,
  useCallback,
} from 'react';
import { useTranslation } from 'react-i18next';
import {
  Input,
  Spin,
  Alert,
  Tooltip,
  Button,
  message,
  Modal,
  Form,
  Switch,
  Select,
  Space,
  InputNumber,
} from 'antd';
import DatePicker from '@pkg/everest.base.ui/public/reactComponents/DatePicker.ui';
const { RangePicker } = DatePicker;
import {
  Typography,
  Icon,
  DataGrid,
  DataGridRef,
} from '@everestsystems/design-system';
import { Decimal } from '@everestsystems/decimal';
import {
  GridReadyEvent,
  ColDef,
  GridApi,
  ICellRendererParams,
  ValueFormatterParams,
  ValueGetterParams,
} from 'ag-grid-community';
import { PlainDate } from '@everestsystems/datetime';
import { DateTime } from 'luxon';
import { TasksTabUI } from '@pkg/everest.psa/types/presentations/modules/projects/pages/projectDetail/components/TasksTab/TasksTab.ui';
import { EvstActivityBillingType } from '@pkg/everest.timetracking/types/enums/ActivityBillingType';
import { useProjectDetail } from '../../ProjectDetailContext.ui';
import { PsaPermissionPreflightUI } from '@pkg/everest.psa/types/presentations/lib/permissions/PsaPermissionPreflight.ui';

const { Span } = Typography;
const { Search } = Input;
const { TextArea } = Input;
const { Option } = Select;

/**
 * Interface for task data based on the TasksTab OData service response
 */
type TaskData = TasksTabUI.EntitySets.Tasks.Get.Entity;
type TeamMemberData = TasksTabUI.EntitySets.TeamMembers.Get.Entity;

/**
 * Interface for task form values
 */
interface TaskFormValues {
  name: string;
  description: string;
  assignedMembers?: number[]; // Array of Team Member IDs
  billable: boolean;
  hoursBudget?: number;
  billingMethod?: 'hours' | 'milestone' | 'fixedFee';
  milestoneId?: number;
  fixedFeeId?: number;
  dateRange?: [PlainDate, PlainDate];
}

/**
 * TasksTab component displays the project tasks in a table with filtering capabilities.
 * Uses AG Grid and fetches data from OData service with pagination.
 */
const TasksTab: React.FC = () => {
  const { t } = useTranslation();

  // Get psaProjectId from context
  const { psaProjectId } = useProjectDetail();

  const gridRef = useRef<DataGridRef>(null);
  const [gridApi, setGridApi] = useState<GridApi<TaskData> | null>(null);

  // State for data, loading, and error
  const [tasks, setTasks] = useState<TaskData[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Filter states
  const [searchText, setSearchText] = useState<string>('');
  const [hasEditPermission, setHasEditPermission] = useState<boolean>(false);

  // Selection states
  const [selectedTasks, setSelectedTasks] = useState<TaskData[]>([]);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);

  // Team members data
  const [teamMembers, setTeamMembers] = useState<TeamMemberData[]>([]);
  const [loadingTeamMembers, setLoadingTeamMembers] = useState<boolean>(false);

  // Billing data
  const [milestones, setMilestones] = useState<
    TasksTabUI.EntitySets.BillableMilestones.Get.Entity[]
  >([]);
  const [fixedFees, setFixedFees] = useState<
    TasksTabUI.EntitySets.BillableFixedFees.Get.Entity[]
  >([]);
  const [loadingBillingData, setLoadingBillingData] = useState<boolean>(false);

  // Combined form modal states
  const [formModalVisible, setFormModalVisible] = useState<boolean>(false);
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [taskForm] = Form.useForm();
  const [formKey, setFormKey] = useState<number>(0);
  const [editingTaskId, setEditingTaskId] = useState<number | null>(null);

  // Default form values
  const defaultFormValues: TaskFormValues = {
    name: '',
    description: '',
    assignedMembers: [],
    billable: true,
    billingMethod: 'hours',
  };

  // Fetch tasks data with OData parameters
  const fetchTasks = useCallback(async () => {
    if (!psaProjectId) {
      console.error(t('{{projects.missingProjectId}}'));
      setError(t('{{projects.projectIdRequired}}'));
      setLoading(false);
      return;
    }

    try {
      setLoading(true);

      const client = TasksTabUI.client;
      const request = client.createEntitySetGetRequest('Tasks');

      // Use the proper filter structure from the types
      request.setFilter({
        property: 'psaProjectId',
        comparator: 'eq',
        value: psaProjectId,
      });

      const response = await request.execute();
      const taskData = response.entities;

      setTasks(taskData);
      setError(null);
    } catch (error) {
      console.error(t('{{projects.errorFetchingTasks}}'), error);
      setError(t('{{projects.failedToLoadTasks}}'));
    } finally {
      setLoading(false);
    }
  }, [psaProjectId]);

  // Fetch team members from TeamMembers OData endpoint
  const fetchTeamMembers = useCallback(async () => {
    console.log('[TasksTab] fetchTeamMembers() triggered.');
    if (!psaProjectId) {
      console.error(t('{{projects.missingProjectIdForTeamMembers}}'));
      return;
    }

    try {
      setLoadingTeamMembers(true);

      const client = TasksTabUI.client;
      const request = client.createEntitySetGetRequest('TeamMembers');

      // Use the proper filter structure from the types
      request.setFilter({
        property: 'psaProjectId',
        comparator: 'eq',
        value: psaProjectId,
      });

      const response = await request.execute();
      const teamMembersData = response.entities;

      console.log('[TasksTab] Fetched team members:', teamMembersData);

      setTeamMembers(teamMembersData);
    } catch (error) {
      console.error(t('{{projects.errorFetchingTeamMembers}}'), error);
    } finally {
      setLoadingTeamMembers(false);
    }
  }, [psaProjectId]);

  // Fetch billing data (milestones and fixed fees)
  const fetchBillingData = useCallback(async () => {
    console.log('[TasksTab] fetchBillingData() triggered.');
    if (!psaProjectId) {
      console.error(t('{{projects.missingProjectIdForBillingData}}'));
      return;
    }

    try {
      setLoadingBillingData(true);

      const client = TasksTabUI.client;

      // Fetch billable milestones
      const milestonesRequest =
        client.createEntitySetGetRequest('BillableMilestones');
      milestonesRequest.setFilter({
        property: 'psaProjectId',
        comparator: 'eq',
        value: psaProjectId,
      });

      // Fetch billable fixed fees
      const fixedFeesRequest =
        client.createEntitySetGetRequest('BillableFixedFees');
      fixedFeesRequest.setFilter({
        property: 'psaProjectId',
        comparator: 'eq',
        value: psaProjectId,
      });

      const [milestonesResponse, fixedFeesResponse] = await Promise.all([
        milestonesRequest.execute(),
        fixedFeesRequest.execute(),
      ]);

      console.log(
        '[TasksTab] Fetched milestones:',
        milestonesResponse.entities
      );
      console.log('[TasksTab] Fetched fixed fees:', fixedFeesResponse.entities);

      setMilestones(milestonesResponse.entities);
      setFixedFees(fixedFeesResponse.entities);
    } catch (error) {
      console.error(t('{{projects.errorFetchingBillingData}}'), error);
    } finally {
      setLoadingBillingData(false);
    }
  }, [psaProjectId]);

  // Check if user has edit permission
  const checkEditPermission = useCallback(async () => {
    if (!psaProjectId) {
      setHasEditPermission(false);
      return;
    }

    try {
      const response = await PsaPermissionPreflightUI.client
        .createFunctionExecuteRequest('CheckEditPermission')
        .setInput({ psaProjectId: psaProjectId })
        .execute();

      setHasEditPermission(response.output.hasEditPermission);
    } catch (error_) {
      console.error('Error checking edit permission:', error_);
      setHasEditPermission(false);
    }
  }, [psaProjectId]);

  // Initial data fetch
  useEffect(() => {
    if (psaProjectId) {
      fetchTasks();
      // Also fetch team members and billing data when component loads
      fetchTeamMembers();
      fetchBillingData();
      checkEditPermission();
    }
  }, [
    psaProjectId,
    fetchTasks,
    fetchTeamMembers,
    fetchBillingData,
    checkEditPermission,
  ]);

  // Initialize grid when ready
  const onGridReady = useCallback((params: GridReadyEvent<TaskData>) => {
    setGridApi(params.api);
  }, []);

  // Filter tasks based on search text
  const filteredTasks = useMemo(() => {
    if (!searchText) {
      return tasks;
    }

    return tasks.filter((task) =>
      task.taskName.toLowerCase().includes(searchText.toLowerCase())
    );
  }, [tasks, searchText]);

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<{ value: string }>) => {
    setSearchText(e.target.value);

    // Clear current selection when filtering
    if (gridApi) {
      gridApi.deselectAll();
      setSelectedTasks([]);
    }
  };

  // --- Renderers & Formatters ---
  const teamMemberRenderer = useCallback((params: any) => {
    const teamMember = params.value as TaskData['teamMember'];
    if (!teamMember || teamMember.length === 0) {
      return <Span color="cool-grey-500">{t('{{projects.unassigned}}')}</Span>;
    }

    // Show comma-separated member names directly (same as tooltip)
    const memberNames = teamMember
      .map((member) => member.memberDisplayName)
      .join(', ');

    return <Span>{memberNames}</Span>;
  }, []);

  // Formatter for hour values - used in the valueFormatter of relevant columns
  const hoursFormatter = useCallback(
    (params: ValueFormatterParams<TaskData, Decimal | undefined>) => {
      // Return integer values instead of decimals
      return params.value ? Math.round(Number(params.value)).toString() : '-';
    },
    []
  );

  const plainDateFormatter = useCallback(
    (params: ValueFormatterParams<TaskData, PlainDate | undefined>) => {
      return params.value ? params.value.toString() : '-';
    },
    []
  );

  /**
   * Progress bar renderer for the combined hours column
   */
  const hoursProgressRenderer = useCallback((params: ICellRendererParams) => {
    const data = params.data as TaskData;
    if (!data || !data.allocatedHours || !data.actualHours) {
      return <Span color="cool-grey-500">{t('{{projects.noData}}')}</Span>;
    }

    // Calculate progress percentage using Decimal operations
    const allocated = data.allocatedHours;
    const actual = data.actualHours;

    // Calculate percentage with Decimal operations
    let percentage = 0;
    if (!allocated.equals(Decimal.Zero)) {
      // Multiply by 100 to get percentage
      const ratio = actual.dividedBy(allocated).times(new Decimal(100));
      // Convert to number for Progress component
      percentage = Math.min(Math.round(Number(ratio.toFixed(0))), 100);
    }

    // Determine if over budget
    const isOverBudget = actual.greaterThan(allocated);
    const progressColor = isOverBudget ? '#f5222d' : '#52c41a'; // red for over budget, green otherwise

    // Format number as integers
    const actualFormatted = Math.round(Number(data.actualHours)).toString();
    const allocatedFormatted = Math.round(
      Number(data.allocatedHours)
    ).toString();

    return (
      <div style={{ width: '100%', paddingRight: '8px', paddingTop: '4px' }}>
        {/* Hours display row */}
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            marginBottom: '4px',
            fontSize: '12px',
          }}
        >
          <Span>{actualFormatted}</Span>
          <Span>{allocatedFormatted}</Span>
        </div>

        {/* Progress bar */}
        <div style={{ height: '10px', marginBottom: '5px' }}>
          {isOverBudget ? (
            // For over budget, show full red bar
            <div style={{ position: 'relative' }}>
              <div
                style={{
                  width: '100%',
                  height: '6px',
                  backgroundColor: progressColor,
                  borderRadius: '3px',
                }}
              />
              <div
                style={{
                  textAlign: 'center',
                  fontSize: '11px',
                  marginTop: '2px',
                  color: '#f5222d',
                }}
              ></div>
            </div>
          ) : (
            // For under budget, show partial green bar
            <div style={{ position: 'relative' }}>
              <div
                style={{
                  width: '100%',
                  height: '6px',
                  backgroundColor: '#f5f5f5',
                  borderRadius: '3px',
                  position: 'relative',
                }}
              >
                <div
                  style={{
                    width: `${percentage}%`,
                    height: '6px',
                    backgroundColor: progressColor,
                    borderRadius: '3px',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                  }}
                />
              </div>
              <div
                style={{
                  textAlign: 'center',
                  fontSize: '11px',
                  marginTop: '2px',
                }}
              ></div>
            </div>
          )}
        </div>
      </div>
    );
  }, []);

  // Effect to reset form state when modal closes
  useEffect(() => {
    if (!formModalVisible) {
      // Clear form state when modal is hidden
      setEditingTaskId(null);
      // Reset the form fields using any type
      (taskForm as any).resetFields?.();
    }
  }, [formModalVisible, taskForm]);

  // Open form in create mode
  const openCreateForm = () => {
    // Set mode to create
    setFormMode('create');

    // Reset form with default values
    (taskForm as any).resetFields?.();
    setFormKey((prev) => prev + 1);

    // Show the modal
    setFormModalVisible(true);
  };

  // Open form in edit mode
  const openEditForm = useCallback(
    (task: TaskData) => {
      // Check if the task is invoiced
      if (task.invoiced) {
        message.error(t('{{projects.cannotEditInvoicedTask}}'));
        return;
      }

      // Set mode to edit
      setFormMode('edit');

      // Store the task ID being edited
      setEditingTaskId(task.taskId);

      // Show loading state
      setLoadingTeamMembers(true);

      // Fetch team members data first to ensure it's available for the select component
      fetchTeamMembers()
        .then(() => {
          // Prepare initial values
          const assignedMemberIds =
            task.teamMember
              ?.map((member) => member.memberId)
              .filter((id) => id && id !== 0) || [];

          // Determine billing method based on task billingType from backend
          let billingMethod: 'hours' | 'milestone' | 'fixedFee' = 'hours';
          let milestoneId: number | undefined;
          let fixedFeeId: number | undefined;

          // Use the billingType from the backend to determine the form state
          switch (task.billingType) {
            case EvstActivityBillingType.Milestone: {
              billingMethod = 'milestone';
              milestoneId = task.milestoneId;
              break;
            }
            case EvstActivityBillingType.FixedFee: {
              billingMethod = 'fixedFee';
              fixedFeeId = task.fixedFeeId;
              break;
            }
            default: {
              billingMethod = 'hours';
              // Explicitly clear these values when switching to hours
              milestoneId = undefined;
              fixedFeeId = undefined;
              break;
            }
          }

          const initialValues = {
            name: task.taskName,
            assignedMembers: assignedMemberIds,
            description: task.activityDescription || '',
            billable: task.isBillableActivity,
            hoursBudget: task.hoursBudget,
            billingMethod,
            milestoneId,
            fixedFeeId,
            dateRange:
              task.startDate && task.endDate
                ? [
                    // Convert PlainDate to Luxon DateTime
                    DateTime.fromObject({
                      year: task.startDate.year,
                      month: task.startDate.month,
                      day: task.startDate.day,
                    }),
                    DateTime.fromObject({
                      year: task.endDate.year,
                      month: task.endDate.month,
                      day: task.endDate.day,
                    }),
                  ]
                : undefined,
            active: true,
          };

          // Reset form first
          (taskForm as any).resetFields?.();
          // Increment form key to force re-render
          setFormKey((prev) => prev + 1);
          // Set form values directly with a slight delay to ensure form is reset
          setTimeout(() => {
            (taskForm as any).setFieldsValue?.(initialValues);
          }, 0);
        })
        .catch((error) => {
          console.error(
            t('{{projects.errorLoadingTeamMembersForEdit}}'),
            error
          );
          message.error(t('{{projects.errorLoadingTeamMembersForEdit}}'));
        })
        .finally(() => {
          // Always show the modal, even if roles failed to load
          setFormModalVisible(true);
        });
    },
    [taskForm, fetchTeamMembers, setLoadingTeamMembers]
  );

  // Handle delete single task from action column
  const handleDeleteTask = useCallback(
    (task: TaskData) => {
      // Check if the task is invoiced
      if (task.invoiced) {
        message.error(t('{{projects.cannotDeleteInvoicedTask}}'));
        return;
      }

      Modal.confirm({
        title: t('{{projects.deleteTask}}'),
        content: t('{{projects.confirmDeleteTask}}', {
          taskName: task.taskName,
        }),
        okText: t('{{projects.yes}}'),
        okType: 'danger',
        cancelText: t('{{projects.no}}'),
        onOk: async () => {
          try {
            setIsDeleting(true);
            const client = TasksTabUI.client;
            const request = client
              .createEntitySetDeleteRequest('Tasks')
              .setId(task.taskId);

            await request.execute();

            // Refresh data
            fetchTasks();

            message.success(t('{{projects.taskDeletedSuccessfully}}'));
          } catch (error) {
            message.error(
              error instanceof Error
                ? error.message
                : t('{{projects.failedToDeleteTask}}')
            );
          } finally {
            setIsDeleting(false);
          }
        },
      });
    },
    [fetchTasks]
  );

  /**
   * Custom header component for AG Grid that includes tooltip and info icon
   */
  const HeaderWithTooltip = (props: any) => {
    const { displayName, tooltip } = props;

    return (
      <div
        className="ag-header-cell-label"
        style={{ display: 'flex', alignItems: 'center' }}
      >
        <span style={{ display: 'flex', alignItems: 'center' }}>
          {displayName}
        </span>
        {tooltip && (
          <Tooltip title={tooltip}>
            <span
              style={{
                marginLeft: '4px',
                cursor: 'help',
                display: 'flex',
                alignItems: 'center',
              }}
            >
              <Icon
                type="info"
                style={{ fontSize: '14px', color: '#8c8c8c' }}
              />
            </span>
          </Tooltip>
        )}
      </div>
    );
  };

  const colDefs = useMemo<ColDef[]>(
    () => [
      {
        // No ID column, but keep checkbox selection
        headerName: '',
        field: 'taskId',
        sortable: false,
        filter: false,
        checkboxSelection: true,
        headerCheckboxSelection: true,
        flex: 0.4,
        hide: false,
        cellRenderer: () => '', // Return empty string to hide the ID
      },
      {
        headerName: t('{{projects.taskName}}'),
        field: 'taskName',
        sortable: true,
        filter: 'agTextColumnFilter',
        flex: 2,
        cellRenderer: (params: ICellRendererParams) => {
          const task = params.data as TaskData;
          return (
            <div>
              <Span>
                {params.value}{' '}
                {task.dateWarning && (
                  <Tooltip title={t('{{projects.allocationsDateWarning}}')}>
                    <span
                      style={{
                        marginLeft: '4px',
                        color: '#faad14',
                        display: 'inline-flex',
                        alignItems: 'center',
                        verticalAlign: 'middle',
                      }}
                    >
                      <Icon type="warning" style={{ fontSize: '16px' }} />
                    </span>
                  </Tooltip>
                )}
              </Span>
            </div>
          );
        },
        headerComponent: HeaderWithTooltip,
        headerComponentParams: {
          displayName: t('{{projects.taskName}}'),
          tooltip: t('{{projects.taskNameTooltip}}'),
        },
      },
      {
        headerName: t('{{projects.billable}}'),
        field: 'isBillableActivity',
        sortable: true,
        filter: 'agSetColumnFilter',
        flex: 0.8,
        cellRenderer: (params: ICellRendererParams) => {
          const isBillable = params.value;
          return (
            <Span>
              {isBillable ? t('{{projects.yes}}') : t('{{projects.no}}')}
            </Span>
          );
        },
        headerComponent: HeaderWithTooltip,
        headerComponentParams: {
          displayName: t('{{projects.billable}}'),
          tooltip: t('{{projects.billableTooltip}}'),
        },
      },
      {
        headerName: t('{{projects.billingType}}'),
        field: 'billingType',
        sortable: true,
        filter: 'agSetColumnFilter',
        flex: 1,
        cellRenderer: (params: ICellRendererParams) => {
          const billingType = params.value;
          const task = params.data as TaskData;

          if (!task.isBillableActivity) {
            return (
              <Span color="cool-grey-500">{t('{{projects.notBillable}}')}</Span>
            );
          }

          switch (billingType) {
            case EvstActivityBillingType.Milestone: {
              return <Span>{t('{{projects.milestone}}')}</Span>;
            }
            case EvstActivityBillingType.FixedFee: {
              return <Span>{t('{{projects.fixedFee}}')}</Span>;
            }
            case EvstActivityBillingType.Hours: {
              return <Span>{t('{{projects.hours}}')}</Span>;
            }
            default: {
              return (
                <Span color="cool-grey-500">
                  {billingType || t('{{projects.unknown}}')}
                </Span>
              );
            }
          }
        },
        headerComponent: HeaderWithTooltip,
        headerComponentParams: {
          displayName: t('{{projects.billingType}}'),
          tooltip: t('{{projects.billingTypeTooltip}}'),
        },
      },
      {
        headerName: t('{{projects.assignedTo}}'),
        field: 'teamMember',
        sortable: true,
        filter: 'agTextColumnFilter',
        valueGetter: (params: ValueGetterParams<TaskData>) =>
          params.data?.teamMember,
        cellRenderer: teamMemberRenderer,
        flex: 1.5,
        headerComponent: HeaderWithTooltip,
        headerComponentParams: {
          displayName: t('{{projects.assignedTo}}'),
          tooltip: t('{{projects.assignedToTooltip}}'),
        },
      },
      {
        headerName: t('{{projects.startDate}}'),
        field: 'startDate',
        sortable: true,
        filter: 'agDateColumnFilter',
        valueFormatter: plainDateFormatter,
        flex: 1,
        headerComponent: HeaderWithTooltip,
        headerComponentParams: {
          displayName: t('{{projects.startDate}}'),
          tooltip: t('{{projects.startDateTooltip}}'),
        },
      },
      {
        headerName: t('{{projects.endDate}}'),
        field: 'endDate',
        sortable: true,
        filter: 'agDateColumnFilter',
        valueFormatter: plainDateFormatter,
        flex: 1,
        headerComponent: HeaderWithTooltip,
        headerComponentParams: {
          displayName: t('{{projects.endDate}}'),
          tooltip: t('{{projects.endDateTooltip}}'),
        },
      },
      {
        headerName: t('{{projects.hoursBudget}}'),
        field: 'hoursBudget',
        sortable: true,
        filter: 'agNumberColumnFilter',
        valueFormatter: hoursFormatter,
        flex: 1,
        headerComponent: HeaderWithTooltip,
        headerComponentParams: {
          displayName: t('{{projects.hoursBudget}}'),
          tooltip: t('{{projects.hoursBudgetTooltip}}'),
        },
      },
      {
        headerName: t('{{projects.hoursProgress}}'),
        field: 'actualHours',
        sortable: true,
        filter: 'agNumberColumnFilter',
        cellRenderer: hoursProgressRenderer,
        // For filtering and sorting purposes, use the hoursFormatter
        valueFormatter: hoursFormatter,
        flex: 1.5,
        headerComponent: HeaderWithTooltip,
        headerComponentParams: {
          displayName: t('{{projects.hoursProgress}}'),
          tooltip: t('{{projects.hoursProgressTooltip}}'),
        },
      },
      {
        headerName: t('{{projects.actions}}'),
        field: 'actions',
        sortable: false,
        filter: false,
        flex: 0.8,
        cellRenderer: (params: ICellRendererParams) => {
          const task = params.data as TaskData;
          if (!task) {
            return null;
          }

          return (
            <Space
              size="middle"
              style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: '100%',
              }}
            >
              {task.invoiced || !hasEditPermission ? (
                <Tooltip
                  title={
                    task.invoiced
                      ? t('{{projects.cannotEditInvoicedTask}}')
                      : t('{{projects.noEditPermission}}')
                  }
                >
                  <Button
                    type="text"
                    disabled
                    title={t('{{projects.editTask}}')}
                    icon={<Icon type="edit" />}
                  />
                </Tooltip>
              ) : (
                <Button
                  type="text"
                  onClick={() => openEditForm(task)}
                  title={t('{{projects.editTask}}')}
                  icon={<Icon type="edit" />}
                />
              )}
              {task.invoiced || !hasEditPermission ? (
                <Tooltip
                  title={
                    task.invoiced
                      ? t('{{projects.cannotDeleteInvoicedTask}}')
                      : t('{{projects.noEditPermission}}')
                  }
                >
                  <Button
                    type="text"
                    danger
                    disabled
                    title={t('{{projects.deleteTask}}')}
                    icon={<Icon type="delete" />}
                  />
                </Tooltip>
              ) : (
                <Button
                  type="text"
                  danger
                  onClick={() => handleDeleteTask(task)}
                  title={t('{{projects.deleteTask}}')}
                  icon={<Icon type="delete" />}
                />
              )}
            </Space>
          );
        },
      },
    ],
    [
      teamMemberRenderer,
      hoursProgressRenderer,
      plainDateFormatter,
      hoursFormatter,
      openEditForm,
      handleDeleteTask,
      hasEditPermission,
    ]
  );

  const handleDeleteTasks = async () => {
    if (selectedTasks.length === 0) {
      message.warning(t('{{projects.pleaseSelectTasksToDelete}}'));
      return;
    }

    // Check if any of the selected tasks are invoiced
    const invoicedTasks = selectedTasks.filter((task) => task.invoiced);
    if (invoicedTasks.length > 0) {
      message.error(t('{{projects.cannotDeleteInvoicedTasks}}'));
      return;
    }

    try {
      setIsDeleting(true);
      const client = TasksTabUI.client;

      // Delete each task individually using the EntitySet API
      const deletePromises = selectedTasks.map((task) => {
        const request = client
          .createEntitySetDeleteRequest('Tasks')
          .setId(task.taskId);
        return request.execute();
      });

      await Promise.all(deletePromises);

      // Clear selection first
      if (gridRef.current?.api) {
        gridRef.current.api.deselectAll();
        setSelectedTasks([]);
      }

      // Refresh data
      fetchTasks();

      message.success(
        t('{{projects.tasksDeletedSuccessfully}}', {
          count: selectedTasks.length,
        })
      );
    } catch (error) {
      message.error(
        error instanceof Error
          ? error.message
          : t('{{projects.failedToDeleteTasks}}')
      );
    } finally {
      setIsDeleting(false);
    }
  };

  const onSelectionChanged = useCallback(() => {
    if (!gridApi) {
      return;
    }
    const selectedRows = gridApi.getSelectedRows();
    setSelectedTasks(selectedRows);
  }, [gridApi]);

  // Handle task creation
  const handleCreateTask = async (values: TaskFormValues) => {
    if (!psaProjectId) {
      message.error(t('{{projects.projectIdMissing}}'));
      return;
    }

    try {
      setIsProcessing(true);

      // Debug logging
      console.log('[TasksTab] Form values:', values);
      console.log('[TasksTab] Billing method:', values.billingMethod);
      console.log('[TasksTab] Milestone ID:', values.milestoneId);
      console.log('[TasksTab] Fixed Fee ID:', values.fixedFeeId);

      // Ensure assignedMembers is always an array
      const memberIds = Array.isArray(values.assignedMembers)
        ? values.assignedMembers
        : values.assignedMembers
          ? [values.assignedMembers]
          : [];

      // Find the selected team members by IDs
      const selectedMembers = memberIds
        .map((memberId: number) => {
          return teamMembers.find((member) => member.memberId === memberId);
        })
        .filter((member) => member !== undefined);

      const client = TasksTabUI.client;
      const taskData: any = {
        psaProjectId: psaProjectId,
        taskName: values.name,
        activityDescription: values.description || '',
        teamMember: selectedMembers.map((member) => ({
          memberId: member.memberId || 0,
          memberDisplayName:
            member.memberDisplayName || member.memberName || '',
          projectPositionId: member.positionId || 0,
          projectPositionName: member.positionName || '',
          positionBillable: member.positionBillable || false,
        })),
        isBillableActivity: values.billable ?? true,
        hoursBudget: values.hoursBudget
          ? new Decimal(values.hoursBudget)
          : undefined,
        startDate: values.dateRange
          ? PlainDate.from({
              year: values.dateRange[0].year,
              month: values.dateRange[0].month,
              day: values.dateRange[0].day,
            })
          : undefined,
        endDate: values.dateRange
          ? PlainDate.from({
              year: values.dateRange[1].year,
              month: values.dateRange[1].month,
              day: values.dateRange[1].day,
            })
          : undefined,
      };

      // Add billing method specific data and ensure proper nulling
      if (
        values.billable &&
        values.billingMethod === 'milestone' &&
        values.milestoneId
      ) {
        taskData.milestoneId = values.milestoneId;
        taskData.fixedFeeId = null; // Explicitly null the other field
        console.log('[TasksTab] Setting milestone ID:', values.milestoneId);
      } else if (
        values.billable &&
        values.billingMethod === 'fixedFee' &&
        values.fixedFeeId
      ) {
        taskData.fixedFeeId = values.fixedFeeId;
        taskData.milestoneId = null; // Explicitly null the other field
        console.log('[TasksTab] Setting fixed fee ID:', values.fixedFeeId);
      } else {
        // For hours billing or non-billable tasks, clear both IDs
        taskData.milestoneId = null;
        taskData.fixedFeeId = null;
        console.log(
          '[TasksTab] Clearing milestone and fixed fee IDs (hourly billing)'
        );
      }

      console.log('[TasksTab] Final task data being sent:', taskData);

      const request = client
        .createEntitySetPostRequest('Tasks')
        .setData(taskData);

      await request.execute();

      // Close the modal
      setFormModalVisible(false);

      // Refresh data
      fetchTasks();

      message.success(t('{{projects.taskCreatedSuccessfully}}'));
    } catch (error) {
      console.error(t('{{projects.errorCreatingTask}}'), error);
      message.error(
        error instanceof Error
          ? error.message
          : t('{{projects.failedToCreateTask}}')
      );
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle task update
  const handleUpdateTask = async (values: TaskFormValues) => {
    if (!editingTaskId) {
      message.warning(t('{{projects.noTaskSelectedForUpdate}}'));
      return;
    }

    // Find the task being edited to check if it's invoiced
    const taskBeingEdited = tasks.find((task) => task.taskId === editingTaskId);
    if (taskBeingEdited?.invoiced) {
      message.error(t('{{projects.cannotUpdateInvoicedTask}}'));
      return;
    }

    try {
      setIsProcessing(true);

      // Ensure assignedMembers is always an array
      const memberIds = Array.isArray(values.assignedMembers)
        ? values.assignedMembers
        : values.assignedMembers
          ? [values.assignedMembers]
          : [];

      // Find the selected team members by IDs
      const selectedMembers = memberIds
        .map((memberId: number) => {
          return teamMembers.find((member) => member.memberId === memberId);
        })
        .filter((member) => member !== undefined);

      const client = TasksTabUI.client;
      const taskData: any = {
        taskName: values.name,
        activityDescription: values.description || '',
        teamMember: selectedMembers.map((member) => ({
          memberId: member.memberId || 0,
          memberDisplayName:
            member.memberDisplayName || member.memberName || '',
          projectPositionId: member.positionId || 0,
          projectPositionName: member.positionName || '',
          positionBillable: member.positionBillable || false,
        })),
        isBillableActivity: values.billable,
        hoursBudget: values.hoursBudget
          ? new Decimal(values.hoursBudget)
          : null,
        startDate: values.dateRange
          ? PlainDate.from({
              year: values.dateRange[0].year,
              month: values.dateRange[0].month,
              day: values.dateRange[0].day,
            })
          : undefined,
        endDate: values.dateRange
          ? PlainDate.from({
              year: values.dateRange[1].year,
              month: values.dateRange[1].month,
              day: values.dateRange[1].day,
            })
          : undefined,
      };

      // Add billing method specific data
      if (
        values.billable &&
        values.billingMethod === 'milestone' &&
        values.milestoneId
      ) {
        taskData.milestoneId = values.milestoneId;
        taskData.fixedFeeId = null; // Clear the other field
      } else if (
        values.billable &&
        values.billingMethod === 'fixedFee' &&
        values.fixedFeeId
      ) {
        taskData.fixedFeeId = values.fixedFeeId;
        taskData.milestoneId = null; // Clear the other field
      } else {
        // Clear both if billing by hours
        taskData.milestoneId = null;
        taskData.fixedFeeId = null;
      }

      const request = client
        .createEntitySetPatchRequest('Tasks')
        .setId(editingTaskId)
        .setData(taskData);

      await request.execute();

      // Close modal (useEffect will handle state cleanup)
      setFormModalVisible(false);

      // Refresh data
      fetchTasks();

      message.success(t('{{projects.taskUpdatedSuccessfully}}'));
    } catch (error) {
      console.error(t('{{projects.errorUpdatingTask}}'), error);
      message.error(
        error instanceof Error
          ? error.message
          : t('{{projects.failedToUpdateTask}}')
      );
    } finally {
      setIsProcessing(false);
    }
  };

  if (!psaProjectId) {
    return <div>{t('{{projects.pleaseSelectProjectForTasks}}')}</div>;
  }

  return (
    <div className="tasks-tab">
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          marginBottom: '16px',
        }}
      >
        <div>
          <Search
            placeholder={t('{{projects.searchTasks}}')}
            onChange={handleSearchChange}
            style={{ width: 300 }}
            allowClear
          />
        </div>
        <div>
          {selectedTasks.length > 0 ? (
            <Space>
              <Button
                onClick={handleDeleteTasks}
                loading={isDeleting}
                type="primary"
                danger
                disabled={!hasEditPermission}
              >
                {t('{{projects.deleteSelected}}', {
                  count: selectedTasks.length,
                })}
              </Button>
            </Space>
          ) : (
            <Tooltip
              title={
                teamMembers.length === 0
                  ? t('{{projects.addTeamMembersBeforeTask}}')
                  : ''
              }
            >
              <Button
                type="primary"
                onClick={openCreateForm}
                disabled={teamMembers.length === 0 || !hasEditPermission}
              >
                {t('{{projects.createTask}}')}
              </Button>
            </Tooltip>
          )}
        </div>
      </div>

      <div
        className="ag-theme-alpine"
        style={{ height: 600, width: '100%', position: 'relative' }}
      >
        {loading && (
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: 'rgba(255, 255, 255, 0.7)',
              zIndex: 999,
            }}
          >
            <Spin tip={t('{{projects.loadingTasks}}')} size="large" />
          </div>
        )}
        {error && <Alert message={error} type="error" />}

        <DataGrid
          gridId="everest.psa_projectDetail_tasksTab"
          ref={gridRef}
          rowData={filteredTasks}
          columnDefs={colDefs}
          onGridReady={onGridReady}
          rowSelection="multiple"
          onSelectionChanged={onSelectionChanged}
          variant="white"
          defaultColDef={{
            sortable: true,
            filter: true,
            resizable: true,
          }}
          pagination={true}
          paginationPageSize={25}
        />
      </div>

      {/* Combined Form Modal */}
      <Modal
        title={
          formMode === 'create'
            ? t('{{projects.createNewTask}}')
            : t('{{projects.editTask}}')
        }
        open={formModalVisible}
        onCancel={() => setFormModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setFormModalVisible(false)}>
            {t('{{projects.cancel}}')}
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={isProcessing}
            htmlType="submit"
            form="taskForm"
          >
            {formMode === 'create'
              ? t('{{projects.create}}')
              : t('{{projects.update}}')}
          </Button>,
        ]}
        width={600}
      >
        <Form
          key={formKey}
          form={taskForm}
          layout="vertical"
          onFinish={formMode === 'create' ? handleCreateTask : handleUpdateTask}
          initialValues={defaultFormValues}
          id="taskForm"
        >
          <Form.Item
            label={t('{{projects.taskName}}')}
            name="name"
            rules={[
              {
                required: true,
                message: t('{{projects.pleaseEnterTaskName}}'),
              },
            ]}
          >
            <Input placeholder={t('{{projects.enterTaskName}}')} />
          </Form.Item>

          <Form.Item label={t('{{projects.description}}')} name="description">
            <TextArea
              placeholder={t('{{projects.enterTaskDescription}}')}
              rows={4}
            />
          </Form.Item>

          <Form.Item
            label={t('{{projects.assignedTeamMembers}}')}
            name="assignedMembers"
            rules={[
              {
                required: true,
                message: t('{{projects.pleaseSelectTeamMembers}}'),
              },
            ]}
            tooltip={
              teamMembers.filter(
                (member) => member.memberId && member.memberId !== 0
              ).length === 0
                ? t('{{projects.assignRolesToMembersFirst}}')
                : undefined
            }
          >
            <Select
              mode="multiple"
              placeholder={
                teamMembers.filter(
                  (member) => member.memberId && member.memberId !== 0
                ).length === 0
                  ? t('{{projects.noMembersWithRoles}}')
                  : t('{{projects.selectTeamMembers}}')
              }
              loading={loadingTeamMembers}
              allowClear
              onDropdownVisibleChange={(open) => {
                if (open) {
                  // Only fetch team members when dropdown is opened
                  fetchTeamMembers();
                }
              }}
              optionLabelProp="label"
              filterOption={(input, option) =>
                (option?.label?.toString() || '')
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
              notFoundContent={
                loadingTeamMembers ? null : t('{{projects.noMembersWithRoles}}')
              }
            >
              {teamMembers
                .filter((member) => member.memberId && member.memberId !== 0)
                .map((member, index) => {
                  const uniqueValue = member.memberId;

                  return (
                    <Option
                      key={`${member.memberId}-${index}`}
                      value={uniqueValue}
                      label={`${member.memberDisplayName || member.memberName}`}
                    >
                      <div
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '8px',
                        }}
                      >
                        <div
                          style={{
                            width: '18px',
                            height: '18px',
                            borderRadius: '50%',
                            backgroundColor: member.positionBillable
                              ? '#52c41a'
                              : '#d9d9d9',
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            fontSize: '12px',
                            color: 'white',
                            fontWeight: 'bold',
                          }}
                        >
                          {(member.memberDisplayName || member.memberName || '')
                            .charAt(0)
                            .toUpperCase()}
                        </div>
                        <div>
                          <div>{`${
                            member.memberDisplayName || member.memberName
                          }`}</div>
                          <div style={{ fontSize: '12px', color: '#666' }}>
                            {member.positionName || 'No role'}{' '}
                            {member.memberEmail && `(${member.memberEmail})`}
                          </div>
                        </div>
                      </div>
                    </Option>
                  );
                })}
            </Select>
          </Form.Item>

          <Form.Item label={t('{{projects.dateRange}}')} name="dateRange">
            <RangePicker
              style={{ width: '100%' }}
              placeholder={[
                t('{{projects.startDate}}'),
                t('{{projects.endDate}}'),
              ]}
              allowClear
            />
          </Form.Item>

          <Form.Item
            label={t('{{projects.billable}}')}
            name="billable"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item dependencies={['billable']} noStyle>
            {({ getFieldValue }) => {
              const isBillable = getFieldValue('billable');
              return isBillable ? (
                <Form.Item
                  label={t('{{projects.billingMethod}}')}
                  name="billingMethod"
                  rules={[
                    {
                      required: true,
                      message: t('{{projects.pleaseSelectBillingMethod}}'),
                    },
                  ]}
                >
                  <Select placeholder={t('{{projects.selectBillingMethod}}')}>
                    <Option value="hours">
                      {t('{{projects.billByHours}}')}
                    </Option>
                    <Option value="milestone">
                      {t('{{projects.billByMilestone}}')}
                    </Option>
                    <Option value="fixedFee">
                      {t('{{projects.billByFixedFee}}')}
                    </Option>
                  </Select>
                </Form.Item>
              ) : null;
            }}
          </Form.Item>

          <Form.Item dependencies={['billable', 'billingMethod']} noStyle>
            {({ getFieldValue }) => {
              const isBillable = getFieldValue('billable');
              const billingMethod = getFieldValue('billingMethod');

              if (!isBillable || billingMethod !== 'milestone') {
                return null;
              }

              return (
                <Form.Item
                  label={t('{{projects.milestone}}')}
                  name="milestoneId"
                  rules={[
                    {
                      required: true,
                      message: t('{{projects.pleaseSelectMilestone}}'),
                    },
                  ]}
                >
                  <Select
                    placeholder={t('{{projects.selectMilestone}}')}
                    loading={loadingBillingData}
                    notFoundContent={
                      loadingBillingData
                        ? null
                        : t('{{projects.noMilestonesAvailable}}')
                    }
                    optionLabelProp="label"
                  >
                    {milestones.map((milestone) => (
                      <Option
                        key={milestone.id}
                        value={milestone.id}
                        label={milestone.name}
                      >
                        <div>
                          <div>{milestone.name}</div>
                          <div style={{ fontSize: '12px', color: '#666' }}>
                            {milestone.amount?.amount
                              ? `${milestone.amount.amount} ${
                                  milestone.amount.currency || ''
                                }`
                              : ''}
                            {milestone.dueDate
                              ? ` - Due: ${milestone.dueDate.toString()}`
                              : ''}
                          </div>
                        </div>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              );
            }}
          </Form.Item>

          <Form.Item dependencies={['billable', 'billingMethod']} noStyle>
            {({ getFieldValue }) => {
              const isBillable = getFieldValue('billable');
              const billingMethod = getFieldValue('billingMethod');

              if (!isBillable || billingMethod !== 'fixedFee') {
                return null;
              }

              return (
                <Form.Item
                  label={t('{{projects.fixedFee}}')}
                  name="fixedFeeId"
                  rules={[
                    {
                      required: true,
                      message: t('{{projects.pleaseSelectFixedFee}}'),
                    },
                  ]}
                >
                  <Select
                    placeholder={t('{{projects.selectFixedFee}}')}
                    loading={loadingBillingData}
                    notFoundContent={
                      loadingBillingData
                        ? null
                        : t('{{projects.noFixedFeesAvailable}}')
                    }
                    optionLabelProp="label"
                  >
                    {fixedFees.map((fixedFee) => (
                      <Option
                        key={fixedFee.id}
                        value={fixedFee.id}
                        label={
                          fixedFee.amount?.amount
                            ? `${fixedFee.amount.amount} ${
                                fixedFee.amount.currency || ''
                              }`
                            : `Fixed Fee ${fixedFee.id}`
                        }
                      >
                        <div>
                          <div>
                            {fixedFee.amount?.amount
                              ? `${fixedFee.amount.amount} ${
                                  fixedFee.amount.currency || ''
                                }`
                              : ''}
                          </div>
                          <div style={{ fontSize: '12px', color: '#666' }}>
                            {fixedFee.frequency || ''}
                            {fixedFee.startDate && fixedFee.endDate
                              ? ` - ${fixedFee.startDate.toString()} to ${fixedFee.endDate.toString()}`
                              : ''}
                          </div>
                        </div>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              );
            }}
          </Form.Item>

          <Form.Item label={t('{{projects.hoursBudget}}')} name="hoursBudget">
            <InputNumber
              style={{ width: '100%' }}
              placeholder={t('{{projects.enterHoursBudget}}')}
              formatter={(value) =>
                `${value}`.replaceAll(/\B(?=(\d{3})+(?!\d))/g, ',')
              }
              parser={(value) => (value ? value.replaceAll(/[^\d.]/g, '') : '')}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default TasksTab;
