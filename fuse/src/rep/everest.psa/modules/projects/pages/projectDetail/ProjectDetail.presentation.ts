import { log } from '@everestsystems/content-core';
import type { EvstBaseCurrency } from '@pkg/everest.base/types/enums/BaseCurrency';
import type { EvstPsaProjectStatus } from '@pkg/everest.psa/types/enums/PsaProjectStatus';
import type { ProjectDetail } from '@pkg/everest.psa/types/presentations/modules/projects/pages/projectDetail/ProjectDetail';
import { JoinedProjectView } from '@pkg/everest.psa/types/views/publicApi/view/Projects/JoinedProjectView';

export default {
  ProjectHeader: {
    query: {
      async execute({
        session,
        where,
        orderBy,
        skip,
        take,
        fieldList,
        count,
      }: ProjectDetail.EntitySets.ProjectHeader.Query.Execute.Request): Promise<ProjectDetail.EntitySets.ProjectHeader.Query.Execute.Response> {
        try {
          // Use JoinedProjectView to fetch project data
          const joinedProjects = await JoinedProjectView.client(session).then(
            (client) =>
              client.query(
                {
                  where,
                  orderBy,
                  skip,
                  take,
                },
                [
                  'psaProjectId',
                  'projectName',
                  'status',
                  'salesArrangementCustomerName',
                  'currency',
                  'budgetAmount',
                  'salesArrangementId',
                  'salesRepresentativeId',
                  'salesRepresentativeName',
                ]
              )
          );

          // Map the projects to the expected format
          const instances: ProjectDetail.EntitySets.ProjectHeader.Query.Instance[] =
            joinedProjects.map(
              (
                project
              ): ProjectDetail.EntitySets.ProjectHeader.Query.Instance => {
                return {
                  psaProjectId: project.psaProjectId,
                  projectName: project.projectName,
                  clientName: project.salesArrangementCustomerName,
                  status: project.status as EvstPsaProjectStatus,
                  currency: project.currency as EvstBaseCurrency,
                  budgetAmount: project.budgetAmount,
                  salesArrangementId: project.salesArrangementId,
                  salesRepresentativeId: project.salesRepresentativeId,
                  salesRepresentativeName: project.salesRepresentativeName,
                };
              }
            );

          return {
            instances,
            count: count ? instances.length : undefined,
          };
        } catch (error) {
          log.error('Error in ProjectDetails query execute', error);
          throw error;
        }
      },
    },
  },
} satisfies ProjectDetail.Implementation;
