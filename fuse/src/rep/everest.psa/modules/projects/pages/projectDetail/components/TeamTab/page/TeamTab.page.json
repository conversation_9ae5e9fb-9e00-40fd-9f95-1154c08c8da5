{"type": "react", "componentUrn": "urn:evst:everest:psa:artifact/ui/tsx:modules/projects/pages/projectDetail/components/TeamTab/TeamTab", "backendAccess": {"urn:evst:everest:psa:presentation:modules/projects/pages/projectDetail/components/TeamTab/TeamTab": ["ProjectPositions/view", "Members/view", "Members/add", "Members/remove", "ActiveAndPlannedEmployees/view", "unassignTeamMember", "getMemberAndRoles"], "urn:evst:everest:psa:presentation:lib/permissions/PsaPermissionPreflight": ["CheckEditPermission"]}, "variants": {"management": {"title": "Project team management", "description": "Manage project team members"}, "view": {"title": "Project team view", "description": "View project team members", "excludedBackendAccess": {"urn:evst:everest:psa:presentation:modules/projects/pages/projectDetail/components/TeamTab/TeamTab": ["Members/add", "Members/remove", "unassignTeamMember", "ActiveAndPlannedEmployees/view"]}}}}