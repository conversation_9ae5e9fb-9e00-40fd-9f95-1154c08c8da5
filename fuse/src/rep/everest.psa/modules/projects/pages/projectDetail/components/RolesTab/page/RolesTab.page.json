{"type": "react", "componentUrn": "urn:evst:everest:psa:artifact/ui/tsx:modules/projects/pages/projectDetail/components/RolesTab/RolesTab", "backendAccess": {"urn:evst:everest:psa:presentation:modules/projects/pages/projectDetail/components/RolesTab/RolesTab": ["ProjectRoles/view", "ProjectRoles/add", "ProjectRoles/change", "ProjectRoles/remove", "Projects/view", "validateSOLinks"], "urn:evst:everest:psa:presentation:lib/permissions/PsaPermissionPreflight": ["CheckEditPermission"]}, "variants": {"management": {"title": "Project roles management", "description": "Manage project roles"}, "view": {"title": "Project roles view", "description": "View project roles", "excludedBackendAccess": {"urn:evst:everest:psa:presentation:modules/projects/pages/projectDetail/components/RolesTab/RolesTab": ["ProjectRoles/add", "ProjectRoles/change", "ProjectRoles/remove"]}}}}