package everest.psa

odata presentation TasksTab {

    data-set Tasks {
        supported-operations view, add, change,
            remove
        field psaProjectId (required: false): field<PsaProject.id>
        field taskId (required: false): field<everest.timetracking::ProjectActivity.id>
        field taskName: Text
        field startDate (required: false): PlainDate
        field endDate (required: false): PlainDate
        field teamMember (required: false): array<object<{
            memberId: Number<Int>
            memberDisplayName: Text
            projectPositionId: Number<Int>
            projectPositionName: Text
            positionBillable: TrueFalse
        }>>
        field activityDescription: Text
        field isBillableActivity: TrueFalse
        field hoursBudget (required: false): Number<Decimal>
        field allocatedHours (required: false): Number<Decimal>
        field actualHours (required: false): Number<Decimal>
        field invoiced (required: false): TrueFalse
        field milestoneId (required: false): Number<Int>
        field fixedFeeId (required: false): Number<Int>
        field billingType (required: false): Text
        field dateWarning (required: false): TrueFalse
    }

    data-set Roles {
        supported-operations view
        field psaProjectId (required: false): field<PsaProject.id>
        field positionId (required: false): field<everest.timetracking::ProjectPosition.id>
        field positionName: Text
        field positionBillable: TrueFalse
    }

    data-set TeamMembers {
        supported-operations view
        field psaProjectId (required: false): field<PsaProject.id>
        field memberId: Number<Int>
        field memberName: Text
        field memberDisplayName: Text
        field memberEmail: Text
        field positionId: Number<Int>
        field positionName: Text
        field positionBillable: TrueFalse
    }

    data-set BillableMilestones {
        supported-operations view
        field psaProjectId (required: false): field<PsaProject.id>
        field id: field<Milestone.id>
        field name: Text
        field amount: composite<everest.base::CurrencyAmount>
        field dueDate: PlainDate
        field status: enum<MilestoneStatus>
    }

    data-set BillableFixedFees {
        supported-operations view
        field psaProjectId (required: false): field<PsaProject.id>
        field id: field<FixedFee.id>
        field amount: composite<everest.base::CurrencyAmount>
        field frequency: enum<FixedFeeFrequency>
        field startDate: PlainDate
        field endDate: PlainDate
    }
}
