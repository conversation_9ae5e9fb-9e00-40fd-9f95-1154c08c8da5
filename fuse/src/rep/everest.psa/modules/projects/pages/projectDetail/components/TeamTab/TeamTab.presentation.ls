package everest.psa

odata presentation TeamTab {

    data-set ProjectPositions {
        supported-operations view
        field id: field<everest.timetracking::ProjectPosition.id>
        field uuid: field<everest.timetracking::ProjectPosition.uuid>
        field version: Number<Int>
        field externalId: Text
        field active: TrueFalse
        field createdBy: Text
        field createdDate: DateTime
        field lastModifiedBy: Text
        field lastModifiedDate: DateTime
        field name (required: true): Text
        field `description`: Text
        field billable: TrueFalse
        field productId: field<everest.timetracking::ProjectPosition.salesOrderProductLineId>
        field projectId (required: true): field<everest.timetracking::TimetrackingProject.id>
    }

    data-set Members {
        supported-operations view, add, remove
        field id (editable: false, required: false): field<everest.timetracking::Member.id>
        field uuid (editable: false, required: false): field<everest.timetracking::Member.uuid>
        field version (editable: false, required: false): Number<Int>
        field externalId (editable: false, required: false): Text
        field active: TrueFalse
        field createdBy (editable: false, required: false): Text
        field createdDate (editable: false, required: false): DateTime
        field lastModifiedBy (editable: false): Text
        field lastModifiedDate (editable: false): DateTime
        field employeeId (required: true): field<everest.hr.base::Employee.id>
        field projectPositionId (required: false): field<everest.timetracking::Member.projectPositionId>
        field projectId (required: false): field<everest.timetracking::TimetrackingProject.id>
    }

    data-set ActiveAndPlannedEmployees {
        supported-operations view
        field id: Number<Int>
        field name: Text
        field displayName: Text
        field email: Text
    }

    action unassignTeamMember {
        inputs {
            teamMemberId: Number<Int>
            psaProjectId: Number<Int>
        }
        properties {
            side-effects true
        }
    }

    action getMemberAndRoles {
        inputs {
            psaProjectId: Number<Int>
        }
        outputs {
            teamMembers: array<object<{
                id: Number<Int>
                name: Text
                email: Text
                photo: Text
                status: Text
                teamMemberId: Number<Int>
                memberIsTeamLead: TrueFalse
                roles: array<object<{
                    memberId: field<everest.timetracking::Member.id>
                    id: Number<Int>
                    title: Text
                    isBillable: TrueFalse
                    invoiced: TrueFalse
                    hourlyBillRate: Number<Decimal>
                    currency: enum<everest.base::BaseCurrency>
                }>>
            }>>
        }
        properties {
            side-effects false
        }
    }
}
