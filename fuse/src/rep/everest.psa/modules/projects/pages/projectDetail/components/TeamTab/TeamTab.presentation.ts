import { log } from '@everestsystems/content-core';
import { Decimal } from '@everestsystems/decimal';
import type { EvstBaseCurrency } from '@pkg/everest.base/types/enums/BaseCurrency';
import { EmployeeView } from '@pkg/everest.hr.base/types/views/publicApi/view/EmployeeView/EmployeeView';
import PsaPermissionApi, {
  PsaPermission,
} from '@pkg/everest.psa/lib/permissions/PsaPermissionApi';
import { getTimetrackingProjectIdFromPsa } from '@pkg/everest.psa/lib/projectMapping';
import { mapQuery } from '@pkg/everest.psa/modules/util/queryViews';
import type { TeamTab } from '@pkg/everest.psa/types/presentations/modules/projects/pages/projectDetail/components/TeamTab/TeamTab';
import { PsaProject } from '@pkg/everest.psa/types/PsaProject';
import { JoinedProjectView } from '@pkg/everest.psa/types/views/publicApi/view/Projects/JoinedProjectView';
import { ProjectApi } from '@pkg/everest.timetracking/types/presentations/publicApi/api/ProjectApi/ProjectApi';
import { Projects } from '@pkg/everest.timetracking/types/views/publicApi/view/Projects/Projects';
import { omit, pick, uniq } from 'lodash';

/**
 * Implementation of the TeamTabService OData service
 */
export default {
  // Implementation for the getMemberAndRoles action
  getMemberAndRoles: {
    async execute({
      session,
      input,
    }: TeamTab.Actions.getMemberAndRoles.Execute.Request): Promise<TeamTab.Actions.getMemberAndRoles.Execute.Response> {
      try {
        log.debug(
          'TeamTabService.getMemberAndRoles.execute called with psaProjectId:',
          input.psaProjectId
        );
        // Check permissions for the project
        const permission = await PsaPermissionApi.getProjectPermissions(
          session,
          input.psaProjectId
        );
        if (!(permission >= PsaPermission.View)) {
          throw new Error('You do not have permission to view this project');
        }

        // First, query the PsaProject to get the HR projectId
        const psaProject = await PsaProject.read(
          session,
          { id: input.psaProjectId },
          ['projectId']
        );

        if (!psaProject.projectId) {
          throw new Error(
            `No HR projectId found for PsaProject with id ${input.psaProjectId}`
          );
        }

        log.debug(
          `Found HR projectId: ${psaProject.projectId} for PsaProject: ${input.psaProjectId}`
        );

        // Get Projects view client
        const projectsClient = await Projects.client(session);

        // Query the Projects view with filter on HR projectId and members data
        const projectsMemberAssignments = await projectsClient.query(
          {
            where: {
              projectId: psaProject.projectId,
              select: 'membersAndPositions',
            },
          },
          [
            'memberIsTeamLead',
            'teamMemberId',
            'memberId',
            'memberEmployeeId',
            'memberName',
            'memberEmail',
            'memberPhoto',
            'positionId',
            'positionName',
            'positionBillable',
            'memberInvoiced',
            'positionHourlyBillRate',
            'positionHourlyBillRateCurrency',
          ]
        );

        // Group positions by employee ID
        const employeeMap: Record<
          number, // employeeId
          TeamTab.Actions.getMemberAndRoles.Output['teamMembers'][number]
        > = {};

        // Process each valid position with member
        for (const record of projectsMemberAssignments) {
          if (!record.memberEmployeeId) {
            continue;
          }
          const employeeId = Number(record.memberEmployeeId);
          const memberName = record.memberName?.toString().trim() || '';

          // If this is the first time we see this employee, create an entry
          if (!employeeMap[employeeId]) {
            employeeMap[employeeId] = {
              id: employeeId,
              name: memberName,
              email: record.memberEmail,
              photo: record.memberPhoto ? String(record.memberPhoto) : null,
              teamMemberId: record.teamMemberId,
              memberIsTeamLead: record.memberIsTeamLead,
              status: 'Active', // Default to active
              roles: [],
            };
          }

          // Add the role if it exists
          if (record.positionName) {
            employeeMap[employeeId].roles.push({
              memberId: record.memberId,
              id: record.positionId,
              title: record.positionName.toString().trim(),
              isBillable: record.positionBillable === true, // Use actual billable status
              invoiced: record.memberInvoiced === true, // Use actual invoiced status
              hourlyBillRate: record.positionHourlyBillRate
                ? new Decimal(record.positionHourlyBillRate)
                : new Decimal(0), // Use actual hourly rate
              currency:
                record.positionHourlyBillRateCurrency as EvstBaseCurrency,
            });
          }
        }

        // Convert the map to an array
        const teamMembers = Object.values(employeeMap);

        log.debug(
          `TeamTabService.getMemberAndRoles.execute returning ${teamMembers.length} team members`
        );

        return {
          output: {
            teamMembers,
          },
        };
      } catch (error) {
        log.error('Error in TeamTabService.getMemberAndRoles.execute:', error);
        throw error;
      }
    },
  },

  // ProjectPositions EntitySet implementation
  ProjectPositions: {
    query: {
      async execute({
        session,
        where,
        orderBy,
        skip,
        take,
        fieldList,
        count,
      }: TeamTab.EntitySets.ProjectPositions.Query.Execute.Request): Promise<TeamTab.EntitySets.ProjectPositions.Query.Execute.Response> {
        log.info('ProjectPositions.query.execute called');

        try {
          // Extract projectId from where clause
          // Handle both simple number values and objects with $eq property
          let psaProjectId: number;
          if (where && where.projectId) {
            psaProjectId =
              typeof where.projectId === 'object' &&
              where.projectId.$eq !== undefined
                ? Number(where.projectId.$eq)
                : Number(where.projectId);
            log.debug(`Extracted PSA project ID: ${psaProjectId}`);
          } else {
            throw new Error('No project ID provided in the where clause');
          }

          // Check permissions for the project
          const permission = await PsaPermissionApi.getProjectPermissions(
            session,
            psaProjectId
          );
          if (!(permission >= PsaPermission.View)) {
            throw new Error('You do not have permission to view this project');
          }

          const hrProjectId = await getTimetrackingProjectIdFromPsa(
            session,
            psaProjectId
          );

          // Get Projects view client
          const projectsClient = await Projects.client(session);

          // Define field mapping between OData fields and Projects view fields
          const fieldMapping: Record<string, string> = {
            id: 'positionId',
            name: 'positionName',
            description: 'positionDescription',
            productId: 'positionSalesOrderProductLineId',
            projectId: 'projectId',
          };

          // Map query parameters - ensure all parameters are valid
          const {
            fieldList: viewFields,
            orderBy: viewOrderBy,
            where: viewWhere,
          } = mapQuery(
            fieldList || [],
            orderBy || [],
            where || {},
            fieldMapping
          );

          // Only add projectId if we have a valid HR project ID
          const newViewWhere = {
            select: 'positions',
            projectId: hrProjectId,
            ...omit(viewWhere, ['projectId']),
          };
          log.debug(`Using HR project ID ${hrProjectId} in query`);

          // Query the Projects view with positions select
          const positions = await projectsClient.query(
            {
              where: newViewWhere,
              orderBy: viewOrderBy,
              skip,
              take,
            },
            viewFields
          );

          // Get total count if requested
          let totalCount: number | undefined;
          if (count) {
            const countResult = await projectsClient.query(
              {
                where: newViewWhere,
              },
              ['positionId']
            );
            totalCount = countResult.length;
          }

          // Map view results to OData format
          const instances = positions.map((position) => ({
            id: Number(position.positionId),
            name: position.positionName ? String(position.positionName) : '',
            description: position.positionDescription
              ? String(position.positionDescription)
              : '',
            productId: position.positionSalesOrderProductLineId
              ? Number(position.positionSalesOrderProductLineId)
              : undefined,
            projectId: position.projectId
              ? Number(position.projectId)
              : undefined,
          }));

          return {
            instances,
            count: totalCount,
          };
        } catch (error) {
          log.error('Error querying project positions:', error);
          throw error;
        }
      },
    },
  },

  // Members EntitySet implementation
  Members: {
    query: {
      async execute({
        session,
        where,
        orderBy,
        skip,
        take,
        fieldList,
        count,
      }: TeamTab.EntitySets.Members.Query.Execute.Request): Promise<TeamTab.EntitySets.Members.Query.Execute.Response> {
        log.info('Members.query.execute called');

        try {
          // Extract projectId from where clause
          let psaProjectId: number | undefined;
          let hrProjectId: number | undefined;

          if (where && typeof where === 'object' && 'projectId' in where) {
            psaProjectId = Number(where.projectId);
            log.debug(
              `Members.query: Found PSA project ID in filter: ${psaProjectId}`
            );

            // Get HR project ID from PSA project ID
            try {
              hrProjectId = await getTimetrackingProjectIdFromPsa(
                session,
                psaProjectId
              );
            } catch (error) {
              log.warn(`Could not get HR project ID: ${error.message}`);
              // Continue without HR project ID - will return empty results
            }
          } else {
            log.warn('No project ID provided in the where clause for members');
          }

          // Get Projects view client
          const projectsClient = await JoinedProjectView.client(session);

          // Define field mapping between OData fields and Projects view fields
          const fieldMapping: Record<string, string> = {
            id: 'memberId',
            employeeId: 'memberEmployeeId',
            projectPositionId: 'positionId',
          };

          // Map query parameters - ensure all parameters are valid
          const {
            fieldList: viewFields,
            orderBy: viewOrderBy,
            where: viewWhere,
          } = mapQuery(
            fieldList || [],
            orderBy || [],
            where || {},
            fieldMapping
          );

          // Create a query object with the select parameter and HR project ID if available
          const queryWhere: Record<string, unknown> = {
            select: 'membersAndPositions',
            psaProjectId: psaProjectId,
          };

          // Only add projectId if we have a valid HR project ID
          if (hrProjectId) {
            queryWhere.projectId = hrProjectId;
            log.debug(`Using HR project ID ${hrProjectId} in query`);
          } else if (psaProjectId) {
            // If we couldn't get the HR project ID but have a PSA project ID,
            // log a warning but continue with the query
            log.warn(
              `Could not find HR project ID for PSA project ID ${psaProjectId}, query may return no results`
            );
          }

          // Add any other conditions from viewWhere if it exists
          if (viewWhere && typeof viewWhere === 'object') {
            // Remove the projectId from viewWhere as we've already set it
            // Safely destructure by checking if viewWhere is an object first
            const { projectId: _projectId, ...otherConditions } = viewWhere;
            Object.assign(queryWhere, otherConditions);
          }

          // Query the Projects view with members select
          const members = await projectsClient.query(
            {
              where: queryWhere,
              orderBy: viewOrderBy,
              skip,
              take,
            },
            [...viewFields, 'memberName', 'memberEmail']
          );

          // Get total count if requested
          let totalCount: number | undefined;
          if (count) {
            const countResult = await projectsClient.query(
              {
                where: queryWhere,
              },
              ['memberId']
            );
            totalCount = countResult.length;
          }

          // Map view results to OData format
          const instances = members.map((member) => ({
            id: Number(member.memberId),
            employeeId: Number(member.memberEmployeeId),
            projectPositionId: Number(member.positionId),
            active: true, // Assuming all members from the view are active
          }));

          return {
            instances,
            count: totalCount,
          };
        } catch (error) {
          log.error('Error querying members:', error);
          throw error;
        }
      },
    },

    create: {
      async execute({
        session,
        inputs,
      }: TeamTab.EntitySets.Members.Create.Execute.Request): Promise<TeamTab.EntitySets.Members.Create.Execute.Response> {
        log.info(
          'Members.create.execute called with inputs:',
          JSON.stringify(inputs)
        );

        const uniqueProjectIds = uniq(
          inputs.map((project) => project.projectId)
        );
        if (uniqueProjectIds.length !== 1) {
          throw new Error('All inputs must belong to the same project');
        }
        const psaProjectId = uniqueProjectIds[0];
        const projectId = await getTimetrackingProjectIdFromPsa(
          session,
          psaProjectId
        );

        // Ensure that the user has at least edit permissions on the relevant PSA project
        const permission = await PsaPermissionApi.getProjectPermissions(
          session,
          psaProjectId
        );
        if (!(permission >= PsaPermission.Edit)) {
          throw new Error(
            `You do not have permission to assign members to project ${psaProjectId}`
          );
        }

        // Prepare assignments for the public API
        const { memberIds } = await ProjectApi.addMembers.execute(session, {
          projectId: projectId,
          assignments: inputs.map((input) =>
            pick(input, ['employeeId', 'projectPositionId'])
          ),
        });

        log.info('Members created successfully with IDs:', memberIds);

        return {
          instances: inputs.map((input, index) => ({
            id: memberIds[index],
            employeeId: Number(input.employeeId),
            projectPositionId: Number(input.projectPositionId),
            active: input.active === undefined ? true : Boolean(input.active),
          })),
        };
      },
    },

    delete: {
      async execute({
        session,
        ids,
      }: TeamTab.EntitySets.Members.Delete.Execute.Request): Promise<TeamTab.EntitySets.Members.Delete.Execute.Response> {
        log.info(`Members.delete.execute called for IDs: ${ids.join(', ')}`);

        // Ensure that we have at least edit permissions on all relevant PSA projects
        const joinedProjectsViewClient =
          await JoinedProjectView.client(session);
        const psaProjects = await joinedProjectsViewClient.query(
          {
            where: {
              select: 'membersAndPositions',
              memberId: {
                $in: ids,
              },
            },
          },
          ['psaProjectId']
        );
        const psaProjectIds = uniq(
          psaProjects.map((project) => project.psaProjectId)
        );

        for (const psaProjectId of psaProjectIds) {
          const permission = await PsaPermissionApi.getProjectPermissions(
            session,
            psaProjectId
          );
          if (!(permission >= PsaPermission.Edit)) {
            throw new Error(
              `You do not have permission to unassign members from project ${psaProjectId}`
            );
          }
        }

        await ProjectApi.deleteMembers.execute(session, { memberIds: ids });

        log.info(`Successfully unassigned members with IDs: ${ids.join(', ')}`);
        return;
      },
    },
  },

  unassignTeamMember: {
    async execute({
      session,
      input,
    }: TeamTab.Actions.unassignTeamMember.Execute.Request): Promise<TeamTab.Actions.unassignTeamMember.Execute.Response> {
      try {
        const { teamMemberId, psaProjectId } = input;

        // Check if we have edit permissions on the project
        const permission = await PsaPermissionApi.getProjectPermissions(
          session,
          psaProjectId
        );
        if (!(permission >= PsaPermission.Edit)) {
          throw new Error(
            `You do not have permission to unassign members from project ${psaProjectId}`
          );
        }

        await ProjectApi.unassignMembers.execute(session, {
          projectId: await getTimetrackingProjectIdFromPsa(
            session,
            psaProjectId
          ),
          teamMemberId,
        });

        return {
          output: {},
        };
      } catch (error) {
        log.error('Error unassigning members:', error);
        throw error;
      }
    },
  },

  // ActiveAndPlannedEmployees EntitySet implementation
  ActiveAndPlannedEmployees: {
    query: {
      async execute({
        session,
        where: _where,
        orderBy: _orderBy,
        skip: _skip,
        take: _take,
        fieldList: _fieldList,
        count,
      }: TeamTab.EntitySets.ActiveAndPlannedEmployees.Query.Execute.Request): Promise<TeamTab.EntitySets.ActiveAndPlannedEmployees.Query.Execute.Response> {
        log.info('ActiveAndPlannedEmployees.query.execute called');

        try {
          // Use EmployeeView to get employees
          const employeeClient = await EmployeeView.client(session);

          // Query for employees without filtering by active status
          const employees = await employeeClient.query(
            {
              skip: _skip,
              take: _take,
            },
            ['id', 'name', 'displayName', 'email']
          );

          // Get total count if requested
          let totalCount: number | undefined;
          if (count) {
            const countResult = await employeeClient.query({}, ['id']);
            totalCount = countResult.length;
          }

          // Map results to OData format
          const instances = employees.map((employee) => ({
            id: employee.id,
            name: employee.name,
            displayName: employee.displayName,
            email: employee.email,
          }));

          return {
            instances,
            count: totalCount,
          };
        } catch (error) {
          log.error('Error querying active and planned employees:', error);
          throw error;
        }
      },
    },
  },
} satisfies TeamTab.Implementation;
