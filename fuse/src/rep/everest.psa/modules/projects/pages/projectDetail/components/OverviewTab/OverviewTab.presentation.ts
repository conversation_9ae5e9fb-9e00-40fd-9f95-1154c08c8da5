/**
 * Implementation of the OverviewTab presentation service
 * This file provides real data access for the OverviewTab component
 */

import { log } from '@everestsystems/content-core';
import { PlainDate } from '@everestsystems/datetime';
import { SalesArrangement } from '@pkg/everest.fin.accounting/types/SalesArrangement';
import { Department } from '@pkg/everest.hr.base/types/Department';
import { EmployeeView } from '@pkg/everest.hr.base/types/views/publicApi/view/EmployeeView/EmployeeView';
import { calcProjectMonthlyMargin } from '@pkg/everest.psa/lib/kpi/monthlyMargin';
import PsaPermissionApi, {
  PsaPermission,
} from '@pkg/everest.psa/lib/permissions/PsaPermissionApi';
import PsaInternalApi from '@pkg/everest.psa/lib/projectApi/PsaInternalApi';
import { validatePsaProjectBudgetByPsaId } from '@pkg/everest.psa/lib/validation/project/validateBudget';
import type { EvstPsaProjectStatus } from '@pkg/everest.psa/types/enums/PsaProjectStatus';
import type { OverviewTab } from '@pkg/everest.psa/types/presentations/modules/projects/pages/projectDetail/components/OverviewTab/OverviewTab';
import { PsaProject } from '@pkg/everest.psa/types/PsaProject';
import { PsaProjectType } from '@pkg/everest.psa/types/PsaProjectType';
import { JoinedProjectView } from '@pkg/everest.psa/types/views/publicApi/view/Projects/JoinedProjectView';
import { ProjectApi } from '@pkg/everest.timetracking/types/presentations/publicApi/api/ProjectApi/ProjectApi';

/**
 * Implementation of the OverviewTabService presentation
 * This provides real data access for all entity sets
 */
export default {
  ProjectTypes: {
    query: {
      async execute({
        session,
        where,
        orderBy,
        skip,
        take,
        fieldList,
        count,
      }: OverviewTab.EntitySets.ProjectTypes.Query.Execute.Request): Promise<OverviewTab.EntitySets.ProjectTypes.Query.Execute.Response> {
        try {
          // Query project types from the database
          const projectTypes = await PsaProjectType.query(
            session,
            {
              where,
              orderBy,
              skip,
              take,
            },
            ['id', 'name']
          );

          // Map to the expected response format
          const instances: OverviewTab.EntitySets.ProjectTypes.Query.Instance[] =
            projectTypes.map((projectType) => ({
              id: projectType.id,
              name: projectType.name,
            }));

          const response: OverviewTab.EntitySets.ProjectTypes.Query.Execute.Response =
            {
              instances,
            };

          // Add count if requested
          if (count) {
            response.count = instances.length;
          }

          return response;
        } catch (error) {
          log.error('Error in ProjectTypes query execute', error);
          throw error;
        }
      },
    },
  },

  ProjectDetails: {
    query: {
      async execute({
        session,
        where,
        orderBy,
        skip,
        take,
        fieldList,
        count,
      }): Promise<OverviewTab.EntitySets.ProjectDetails.Query.Execute.Response> {
        try {
          let psaProjectId: number;
          if (where && where.psaProjectId) {
            psaProjectId =
              typeof where.psaProjectId === 'object' &&
              where.psaProjectId.$eq !== undefined
                ? Number(where.psaProjectId.$eq)
                : Number(where.psaProjectId);
            log.debug(`Extracted PSA project ID: ${psaProjectId}`);
          } else {
            throw new Error('No PSA project ID provided in the where clause');
          }
          // Check permissions for the current user
          const permission = await PsaPermissionApi.getProjectPermissions(
            session,
            psaProjectId
          );
          if (!(permission >= PsaPermission.View)) {
            throw new Error('You do not have permission to view this project');
          }

          // Use JoinedProjectView to get project data
          const joinedProjectClient = await JoinedProjectView.client(session);
          const joinedProjects = await joinedProjectClient.query(
            {
              where: {
                psaProjectId: psaProjectId,
              },
            },
            [
              'psaProjectId',
              'projectId',
              'projectName',
              'startDate',
              'endDate',
              'status',
              'currency',
              'salesArrangementId',
              'notes',
              'budgetAmount',
              'salesRepresentativeId',
              'salesRepresentativeName',
              'psaProjectTypeId',
              'psaProjectTypeName',
              'departmentId',
            ]
          );

          if (!joinedProjects || joinedProjects.length === 0) {
            throw new Error(`Project with ID ${psaProjectId} not found`);
          }

          const projectData = joinedProjects[0];

          const projectMonthlyMargin = await calcProjectMonthlyMargin(
            session,
            psaProjectId
          );
          const projectMonthlyMarginArray = Array.from(
            projectMonthlyMargin.entries()
          ).map(([yearMonth, margin]) => ({
            yearMonth: PlainDate.from(yearMonth),
            ...margin,
          })) as OverviewTab.EntitySets.ProjectDetails.Query.Execute.Response['instances'][0]['projectMonthlyMargin'];

          // Get sales arrangement details if salesArrangementId is available
          let salesArrangementData = null;
          if (projectData.salesArrangementId) {
            try {
              const salesArrangementClient =
                await SalesArrangement.client(session);
              const salesArrangementId = projectData.salesArrangementId;
              const salesArrangement = await salesArrangementClient.read(
                { id: salesArrangementId },
                [
                  'id',
                  'salesArrangementNumber',
                  'businessPartnerId',
                  'businessPartnerName',
                  'functionalCurrency',
                ],
                {}
              );

              salesArrangementData = {
                id: salesArrangement.id,
                number: salesArrangement.salesArrangementNumber,
                businessPartnerId: salesArrangement.businessPartnerId,
                functionalCurrency: salesArrangement.functionalCurrency,
                businessPartnerName: salesArrangement.businessPartnerName,
              };
            } catch (error) {
              log.warn('Failed to fetch sales arrangement details', error);
              // Continue without sales arrangement data if it fails
            }
          }

          // Get currency from project or sales order if available
          let currency;
          if (projectData.currency) {
            currency = projectData.currency;
          } else if (
            salesArrangementData &&
            salesArrangementData.functionalCurrency
          ) {
            currency = salesArrangementData.functionalCurrency;
          } else {
            log.warn(`No currency found for project ${psaProjectId}`);
          }

          // Get the project leads for the selected project
          const projectLeads = (
            await joinedProjectClient.query(
              {
                where: {
                  psaProjectId: psaProjectId,
                  select: 'members',
                  memberIsTeamLead: true,
                },
              },
              ['memberName', 'memberEmployeeId']
            )
          ).map((lead) => ({
            projectLead: lead.memberName,
            projectLeadId: lead.memberEmployeeId,
          }));

          // Fetch department information if available
          let departmentName = '';
          if (projectData.departmentId) {
            try {
              const departmentClient = await Department.client(session);
              const department = await departmentClient.read(
                { id: projectData.departmentId },
                ['departmentName'],
                {}
              );
              departmentName = department.departmentName || '';
            } catch (error) {
              log.warn('Failed to fetch department details', error);
              // Continue without department data if it fails
            }
          }

          // Map the project data to the expected format
          const projectDetails = {
            psaProjectId: projectData.psaProjectId,
            projectStatus: projectData.status as EvstPsaProjectStatus,
            projectLeads: projectLeads.map((lead) => ({
              projectLead: lead.projectLead,
              projectLeadId: lead.projectLeadId,
            })),
            projectName: projectData.projectName,
            projectTimeFrame: {
              start: projectData.startDate
                ? projectData.startDate.toPlainDate()
                : null,
              end: projectData.endDate
                ? projectData.endDate.toPlainDate()
                : null,
            },
            salesArrangement: salesArrangementData,
            notes: projectData.notes,
            budgetAmount: projectData.budgetAmount,
            currency: currency, // Use the determined currency
            projectMonthlyMargin: projectMonthlyMarginArray,
            salesRepresentativeId: projectData.salesRepresentativeId || null,
            salesRepresentativeName: projectData.salesRepresentativeName || '',
            projectTypeId: projectData.psaProjectTypeId || null,
            projectTypeName: projectData.psaProjectTypeName || '',
            departmentId: projectData.departmentId || null,
            departmentName: departmentName,
          };

          // Return response with instances and count if requested
          const response: OverviewTab.EntitySets.ProjectDetails.Query.Execute.Response =
            {
              instances: [projectDetails],
            };
          if (count) {
            response.count = 1;
          }

          return response;
        } catch (error) {
          log.error('Error fetching project details:', error);
          throw error;
        }
      },
    },

    // Implement update operation for edit mode
    update: {
      async execute({ session, id, data }): Promise<void> {
        try {
          if (!id) {
            throw new Error('Project ID is required for update');
          }

          // Check permissions for the current user
          const permission = await PsaPermissionApi.getProjectPermissions(
            session,
            id
          );
          if (permission !== PsaPermission.Edit) {
            throw new Error('You do not have permission to edit this project');
          }

          // Get the current project data
          const psaProjectClient = await PsaProject.client(session);
          const existingProject = await psaProjectClient.read(
            { id },
            [
              'id',
              'projectId',

              'status',
              'currency',
              'budget',
              'notes',
              'typeId',
              'departmentId',
            ],
            {}
          );

          if (!existingProject) {
            throw new Error(`Project with ID ${id} not found`);
          }

          // Prepare update data for PsaProject
          const updateData: Partial<PsaProject.PsaProject> = {};

          // Update the project status if provided
          if (data.projectStatus !== undefined) {
            updateData.status = data.projectStatus;
          }

          // Update the notes if provided
          if (data.notes !== undefined) {
            updateData.notes = data.notes;
          }

          // Update the sales representative ID if provided and changed
          if (data.salesRepresentativeId !== undefined) {
            updateData.salesRepresentativeId = data.salesRepresentativeId;
          }

          // Update the sales arrangement ID if provided
          if (
            data.salesArrangement &&
            data.salesArrangement.id !==
              (existingProject as any).salesArrangementId
          ) {
            (updateData as any).salesArrangementId = data.salesArrangement.id;
          }

          // Update the budget amount if provided
          if (data.budgetAmount !== undefined) {
            updateData.budget = data.budgetAmount
              ? {
                  amount: data.budgetAmount,
                  currency: existingProject.currency,
                }
              : {
                  amount: null,
                  currency: null,
                };
          }

          // Update the project type if provided
          if (data.projectTypeId !== undefined) {
            updateData.typeId = data.projectTypeId;
          }

          // Update the department if provided
          if (data.departmentId !== undefined) {
            updateData.departmentId = data.departmentId;
          }

          // Update the PsaProject if there are changes
          if (Object.keys(updateData).length > 0) {
            await PsaInternalApi.updatePsaProject(session, {
              where: { id },
              data: updateData,
            });
          }

          // Update project details in TimeTracking using the TimeTracking public API
          // This handles the project lead, start date, and end date
          if (
            data.projectLeads?.length !== 0 ||
            data.projectTimeFrame?.start !== undefined ||
            data.projectTimeFrame?.end !== undefined ||
            data.projectName !== undefined
          ) {
            // Prepare TimeTracking project update data
            const timetrackingProjectUpdateData: Record<string, any> = {};

            // Add project name if provided
            if (data.projectName !== undefined) {
              timetrackingProjectUpdateData.projectName = data.projectName;
            }

            // Add project leads if provided
            timetrackingProjectUpdateData.projectLeadIds =
              data.projectLeads?.map((lead) => lead.projectLeadId) || [];

            // Add start date if provided - convert PlainDate to PlainDateTime
            if (data.projectTimeFrame?.start !== undefined) {
              // Create a PlainDateTime from the PlainDate by adding time component (00:00:00)
              const startDate = data.projectTimeFrame.start;
              timetrackingProjectUpdateData.startDate = {
                year: startDate.year,
                month: startDate.month,
                day: startDate.day,
                hour: 0,
                minute: 0,
                second: 0,
                millisecond: 0,
              };
            }

            // Add end date if provided - convert PlainDate to PlainDateTime
            if (data.projectTimeFrame?.end !== undefined) {
              // Create a PlainDateTime from the PlainDate by adding time component (23:59:59)
              const endDate = data.projectTimeFrame.end;
              timetrackingProjectUpdateData.endDate = {
                year: endDate.year,
                month: endDate.month,
                day: endDate.day,
                hour: 23,
                minute: 59,
                second: 59,
                millisecond: 999,
              };
            }

            await ProjectApi.updateProject.execute(session, {
              id: existingProject.projectId,
              ...timetrackingProjectUpdateData,
            });

            log.debug('Successfully updated TimeTracking project details', {
              projectId: existingProject.projectId,
              updatedFields: Object.keys(timetrackingProjectUpdateData),
            });
          }

          // For update operations in this system, we don't need to return anything
          // The system expects a void return type
          return;
        } catch (error) {
          log.error('Error updating project details:', error);
          throw error;
        }
      },
    },
  },

  Employees: {
    query: {
      async execute({
        session,
        where,
        orderBy,
        skip,
        take,
        fieldList,
        count,
      }): Promise<OverviewTab.EntitySets.Employees.Query.Execute.Response> {
        try {
          // Use EmployeeView to get employee data
          const employeeClient = await EmployeeView.client(session);
          const employees = await employeeClient.query(
            {
              where: where || {},
              orderBy: orderBy || [{ field: 'displayName', ordering: 'asc' }],
              skip,
              take: take || 100, // Default limit to 100 employees
            },
            ['id', 'name', 'displayName', 'email', 'status']
          );

          // Map the employees to the expected format
          const mappedEmployees = employees.map((employee) => ({
            id: employee.id,
            name: employee.name,
            displayName: employee.displayName || employee.name,
            email: employee.email,
            active: employee.status === 'Active',
          }));

          // Return response with instances and count if requested
          const response: OverviewTab.EntitySets.Employees.Query.Execute.Response =
            { instances: mappedEmployees };
          if (count) {
            response.count = mappedEmployees.length;
          }

          return response;
        } catch (error) {
          log.error('Error fetching employees:', error);
          throw error;
        }
      },
    },
  },

  // Sales Arrangements entity set implementation
  SalesArrangements: {
    query: {
      async execute({
        session,
        where,
        orderBy,
        skip,
        take,
        fieldList,
        count,
      }): Promise<OverviewTab.EntitySets.SalesArrangements.Query.Execute.Response> {
        try {
          // Use SalesArrangement controller to get sales arrangement data
          const salesArrangementClient = await SalesArrangement.client(session);
          const salesArrangements = await salesArrangementClient.query(
            {
              where: where || {},
              orderBy: orderBy || [
                { field: 'salesArrangementNumber', ordering: 'asc' },
              ],
              skip,
              take: take || 100, // Default limit to 100 sales arrangements
            },
            ['id', 'salesArrangementNumber', 'businessPartnerName']
          );

          // Map the sales arrangements to the expected format
          const mappedSalesArrangements = salesArrangements.map(
            (arrangement) => ({
              id: arrangement.id,
              number: arrangement.salesArrangementNumber,
              businessPartnerName: arrangement.businessPartnerName,
            })
          );

          // Return response with instances and count if requested
          const response: OverviewTab.EntitySets.SalesArrangements.Query.Execute.Response =
            { instances: mappedSalesArrangements };
          if (count) {
            response.count = mappedSalesArrangements.length;
          }

          return response;
        } catch (error) {
          log.error('Error fetching sales arrangements:', error);
          throw error;
        }
      },
    },
  },

  // Departments entity set implementation
  Departments: {
    query: {
      async execute({
        session,
        where,
        orderBy,
        skip,
        take,
        fieldList,
        count,
      }: OverviewTab.EntitySets.Departments.Query.Execute.Request): Promise<OverviewTab.EntitySets.Departments.Query.Execute.Response> {
        try {
          // Query departments from the database
          const departments = await Department.query(
            session,
            {
              where: {
                active: true,
                ...where,
              },
              orderBy: orderBy || [
                { field: 'departmentName', ordering: 'asc' },
              ],
              skip,
              take,
            },
            ['id', 'departmentName', 'departmentCode']
          );

          // Map to the expected response format
          const instances: OverviewTab.EntitySets.Departments.Query.Instance[] =
            departments.map((department) => ({
              id: department.id,
              name: department.departmentName,
              code: department.departmentCode || '',
            }));

          const response: OverviewTab.EntitySets.Departments.Query.Execute.Response =
            {
              instances,
            };

          // Add count if requested
          if (count) {
            response.count = instances.length;
          }

          return response;
        } catch (error) {
          log.error('Error in Departments query execute', error);
          throw error;
        }
      },
    },
  },

  validateBudget: {
    async execute(
      request: OverviewTab.Actions.validateBudget.Execute.Request
    ): Promise<OverviewTab.Actions.validateBudget.Execute.Response> {
      const { session, input } = request;
      log.info('validateBudget.execute called');

      try {
        // Run budget validation with the PSA project ID
        const warnings = await validatePsaProjectBudgetByPsaId(
          session,
          input.psaProjectId
        );

        return {
          output: {
            warnings: warnings.map((warning) => ({
              message: warning.message,
              projectName: warning.projectName || '',
            })),
          },
        };
      } catch (error) {
        log.error('Error in validateBudget:', error);
        throw error;
      }
    },
  },
} satisfies OverviewTab.Implementation;
