package everest.psa

odata presentation RolesTab {

    data-set ProjectRoles {
        supported-operations view, add, change,
            remove
        field id (required: false): field<everest.timetracking::ProjectPosition.id>
        field uuid (required: false): field<everest.timetracking::ProjectPosition.uuid>
        field name (required: true): Text
        field `description`: Text
        field billable: TrueFalse
        field hourlyBillRate: Number<Decimal>
        field hourlyBillRateCurrency: enum<everest.base::BaseCurrency>
        field hourlyCostRate: Number<Decimal>
        field hourlyCostRateCurrency: enum<everest.base::BaseCurrency>
        field hoursBudget: Number<Decimal>
        field salesOrderProductLineId: field<everest.timetracking::ProjectPosition.salesOrderProductLineId>
        field projectId (required: true): field<everest.timetracking::TimetrackingProject.id>
        field invoiced: TrueFalse
    }

    data-set Projects {
        supported-operations view
        field id (required: false): field<PsaProject.id>
        field currency: enum<everest.base::BaseCurrency>
        field salesArrangement: object<{
            id: field<everest.fin.accounting::SalesArrangement.id>
            salesArrangementNumber: Text
            salesOrders: array<object<{
                id: field<everest.fin.accounting::SalesOrder.id>
                salesOrderNumber: Text
                products: array<object<{
                    id: field<everest.fin.accounting::SalesOrderProduct.id>
                    productName: Text
                    totalPrice: composite<everest.base::CurrencyAmount>
                    active: TrueFalse
                    productLines: array<object<{
                        id: field<everest.fin.accounting::SalesOrderProductLine.id>
                        productLineName: Text
                        salesOrderProductLineNumber: Text
                        serviceStartDate: PlainDate
                        serviceEndDate: PlainDate
                        quantity: JSON
                        unitPrice: composite<everest.base::CurrencyAmount>
                        totalPrice: composite<everest.base::CurrencyAmount>
                        priceModel: Text
                        invoiceFrequency: Text
                        lineType: enum<everest.fin.accounting::ProductLineType>
                        active: TrueFalse
                        productLineId: field<everest.fin.accounting::ProductLine.id>
                        unitCost: composite<everest.base::CurrencyAmount>
                    }>>
                }>>
            }>>
        }>
    }

    action validateSOLinks {
        inputs {
            psaProjectId: field<PsaProject.id>
        }
        outputs {
            warnings: array<object<{
                message: Text
                code: Text
                severity: Text
                id: Number<Int>
                artifactType: Text
            }>>
        }
        properties {
            side-effects false
        }
    }
}

