{"type": "react", "componentUrn": "urn:evst:everest:psa:artifact/ui/tsx:modules/projects/pages/projectDetail/components/FixedFeeTabs/FixedFee", "backendAccess": {"urn:evst:everest:psa:presentation:modules/projects/pages/projectDetail/components/FixedFeeTabs/FixedFee": ["FixedFees/view", "FixedFees/add", "FixedFees/change", "FixedFees/remove", "Projects/view", "validateFixedFees"], "urn:evst:everest:psa:presentation:lib/permissions/PsaPermissionPreflight": ["CheckEditPermission"]}, "variants": {"management": {"title": "Fixed fee management", "description": "Manage project fixed fees"}, "view": {"title": "Fixed fee view", "description": "View project fixed fees", "excludedBackendAccess": {"urn:evst:everest:psa:presentation:modules/projects/pages/projectDetail/components/FixedFeeTabs/FixedFee": ["FixedFees/add", "FixedFees/change", "FixedFees/remove"]}}}}