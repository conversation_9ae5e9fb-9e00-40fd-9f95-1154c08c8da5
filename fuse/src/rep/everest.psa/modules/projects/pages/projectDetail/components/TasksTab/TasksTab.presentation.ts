import { log } from '@everestsystems/content-core';
import type { PlainDate } from '@everestsystems/datetime';
import { Decimal } from '@everestsystems/decimal';
import PsaPermissionApi, {
  PsaPermission,
} from '@pkg/everest.psa/lib/permissions/PsaPermissionApi';
import PsaInternalApi from '@pkg/everest.psa/lib/projectApi/PsaInternalApi';
import { checkAllocationsOutsideDateRange } from '@pkg/everest.psa/lib/validation/project/validateAllocations';
import { FixedFee } from '@pkg/everest.psa/types/FixedFee';
import { Milestone } from '@pkg/everest.psa/types/Milestone';
import type { TasksTab } from '@pkg/everest.psa/types/presentations/modules/projects/pages/projectDetail/components/TasksTab/TasksTab';
import { PsaProject } from '@pkg/everest.psa/types/PsaProject';
import { JoinedAllocations as Allocations } from '@pkg/everest.psa/types/views/publicApi/view/Allocations/JoinedAllocations';
import { JoinedProjectView } from '@pkg/everest.psa/types/views/publicApi/view/Projects/JoinedProjectView';
import { EvstActivityBillingType } from '@pkg/everest.timetracking/types/enums/ActivityBillingType';
import { ProjectApi } from '@pkg/everest.timetracking/types/presentations/publicApi/api/ProjectApi/ProjectApi';
import { Projects } from '@pkg/everest.timetracking/types/views/publicApi/view/Projects/Projects';
import { TimeEntries } from '@pkg/everest.timetracking/types/views/publicApi/view/TimeEntries/TimeEntries';

/**
 * Implementation of the TasksTabService OData service
 */
export default {
  Tasks: {
    query: {
      async execute({
        session,
        where,
        orderBy: _orderBy,
        skip: _skip,
        take: _take,
        fieldList: _fieldList,
      }: TasksTab.EntitySets.Tasks.Query.Execute.Request): Promise<TasksTab.EntitySets.Tasks.Query.Execute.Response> {
        try {
          log.debug('TasksTabService.Tasks.query.execute called');

          // Extract psaProjectId from where filter
          let psaProjectId: number | undefined;

          if (
            where.psaProjectId &&
            typeof where.psaProjectId === 'object' &&
            '$eq' in where.psaProjectId
          ) {
            psaProjectId = where.psaProjectId.$eq as number;
          }

          if (!psaProjectId) {
            throw new Error('One single `psaProjectId` is required');
          }

          // Check permissions for the project
          const permission = await PsaPermissionApi.getProjectPermissions(
            session,
            psaProjectId
          );
          if (!(permission >= PsaPermission.View)) {
            throw new Error('You do not have permission to view this project');
          }

          // 1. Fetch HR Project ID from PsaProject
          const psaProjectClient = await PsaProject.client(session);
          const psaProject = await psaProjectClient.read({ id: psaProjectId }, [
            'projectId',
          ]);

          if (!psaProject.projectId) {
            throw new Error(
              `No Project found for PsaProject with id ${psaProjectId}`
            );
          }

          log.debug(
            `Found HR projectId: ${psaProject.projectId} for PsaProject: ${psaProjectId}`
          );

          // 2. Fetch project activities (tasks) using the Projects view
          // First, get the projects view client
          const projectsViewClient = await Projects.client(session);

          // Query the projects view with 'activities' selection to include activities
          const projectsData = await projectsViewClient.query(
            {
              where: {
                projectId: psaProject.projectId,
                select: 'activities',
              },
            },
            [
              'projectId',
              'positionId',
              'positionName',
              'positionBillable',
              'activityId',
              'activityName',
              'activityDescription',
              'isBillableActivity',
              'activityHoursBudget',
              'activityInvoiced',
              'activityMilestoneId',
              'activityFixedFeeId',
              'activityBillingType',
              'activityStartDate',
              'activityEndDate',
              'memberId',
              'memberDisplayName',
            ]
          );

          if (projectsData.length === 0) {
            log.debug(
              `No project activities found for HR projectId ${psaProject.projectId}. Returning empty task list.`
            );
            return { instances: [] };
          }

          // 3. Get allocations for start and end dates and allocated hours
          const allocationsViewClient = await Allocations.client(session);
          const allocationsData = await allocationsViewClient.query(
            {
              where: {
                projectId: psaProject.projectId,
              },
            },
            [
              'allocationId',
              'projectActivityId',
              'startDate',
              'endDate',
              'totalHours',
            ]
          );

          // Create a map to store allocation data by activity ID
          const allocationsByActivityId = new Map<
            number,
            {
              startDates: PlainDate[];
              endDates: PlainDate[];
              totalHours: Decimal;
            }
          >();

          // Process allocations data
          for (const allocation of allocationsData) {
            if (allocation.projectActivityId) {
              const activityId = allocation.projectActivityId;

              // Initialize if this is the first allocation for this activity
              if (!allocationsByActivityId.has(activityId)) {
                allocationsByActivityId.set(activityId, {
                  startDates: [],
                  endDates: [],
                  totalHours: new Decimal(0),
                });
              }

              const activityAllocations =
                allocationsByActivityId.get(activityId);

              // Add start and end dates to the arrays
              if (allocation.startDate) {
                activityAllocations.startDates.push(allocation.startDate);
              }

              if (allocation.endDate) {
                activityAllocations.endDates.push(allocation.endDate);
              }

              // Add to the total hours if available
              if (allocation.totalHours) {
                // Use plus method for Decimal instead of add
                activityAllocations.totalHours =
                  activityAllocations.totalHours.plus(allocation.totalHours);
              }
            }
          }

          // 4. Get time entries to calculate actual hours
          const timeEntriesViewClient = await TimeEntries.client(session);
          const timeEntriesData = await timeEntriesViewClient.query(
            {
              where: {},
            },
            ['projectActivityId', 'hours']
          );

          // Create a map to store time entry hours by activity ID
          const hoursByActivityId = new Map<number, Decimal>();

          // Process time entries data
          for (const entry of timeEntriesData) {
            if (entry.projectActivityId && entry.hours) {
              const activityId = entry.projectActivityId;

              // Initialize or update hours for this activity
              const currentHours =
                hoursByActivityId.get(activityId) || new Decimal(0);
              // Use plus method for Decimal instead of add
              hoursByActivityId.set(activityId, currentHours.plus(entry.hours));
            }
          }

          // 5. Group project data by activityId and aggregate team mapping
          const taskMap = new Map();

          for (const item of projectsData) {
            const activityId = item.activityId;

            if (!taskMap.has(activityId)) {
              // Get allocation data for this activity
              const allocationData = allocationsByActivityId.get(activityId);

              const startDate = item.activityStartDate;
              const endDate = item.activityEndDate;

              // Check if there are allocations outside the activity date range
              let dateWarning = false;
              if (startDate && endDate && allocationData) {
                // Use the validation function to check for allocations outside date range
                dateWarning = checkAllocationsOutsideDateRange(
                  allocationData.startDates,
                  allocationData.endDates,
                  startDate,
                  endDate
                );
              }

              // Get allocated hours
              const allocatedHours =
                allocationData?.totalHours || new Decimal(0);

              // Get actual hours from time entries
              const actualHours =
                hoursByActivityId.get(activityId) || new Decimal(0);

              // Initialize the task in the map
              taskMap.set(activityId, {
                psaProjectId: psaProjectId,
                taskId: activityId,
                taskName: item.activityName || '',
                startDate: startDate,
                endDate: endDate,
                activityDescription: item.activityDescription || '',
                isBillableActivity: item.isBillableActivity,
                hoursBudget: item.activityHoursBudget,
                allocatedHours,
                actualHours,
                invoiced: item.activityInvoiced,
                milestoneId: item.activityMilestoneId || undefined,
                fixedFeeId: item.activityFixedFeeId || undefined,
                billingType: item.activityBillingType,
                dateWarning: dateWarning,
                teamMembers: [],
              });
            }

            // Add team member to the task if position data exists and not already added
            const task = taskMap.get(activityId);
            task.teamMembers.push({
              memberId: item.memberId,
              memberDisplayName: item.memberDisplayName,
              projectPositionId: item.positionId,
              projectPositionName: item.positionName || '',
              positionBillable: item.positionBillable || false,
            });
          }

          // Convert grouped tasks to the final format
          const instances = Array.from(taskMap.values()).map((task) => {
            return {
              psaProjectId: task.psaProjectId,
              taskId: task.taskId,
              taskName: task.taskName,
              startDate: task.startDate,
              endDate: task.endDate,
              teamMember: task.teamMembers,
              activityDescription: task.activityDescription,
              isBillableActivity: task.isBillableActivity,
              hoursBudget: task.hoursBudget,
              allocatedHours: task.allocatedHours,
              actualHours: task.actualHours,
              invoiced: task.invoiced,
              milestoneId: task.milestoneId,
              fixedFeeId: task.fixedFeeId,
              billingType: task.billingType,
              dateWarning: task.dateWarning,
            };
          });

          return { instances: instances };
        } catch (error) {
          log.error('Error in TasksTabService.Tasks.query.execute:', error);
          // Re-throw the error to ensure it's propagated correctly
          throw error;
        }
      },
    },

    create: {
      async execute({
        session,
        inputs,
      }: TasksTab.EntitySets.Tasks.Create.Execute.Request): Promise<TasksTab.EntitySets.Tasks.Create.Execute.Response> {
        log.debug(
          'TasksTabService.Tasks.create.execute called with inputs:',
          inputs
        );

        if (!inputs || inputs.length !== 1) {
          throw new Error('Only one task data is allowed for creation');
        }

        const input = inputs[0];
        const psaProjectId = input.psaProjectId;

        if (!psaProjectId) {
          throw new Error('psaProjectId is required');
        }

        // Check permissions for the project
        const permission = await PsaPermissionApi.getProjectPermissions(
          session,
          psaProjectId
        );
        if (!(permission >= PsaPermission.Edit)) {
          throw new Error('You do not have permission to edit this project');
        }

        // 1. Fetch HR Project ID from PsaProject
        const psaProjectClient = await PsaProject.client(session);
        const psaProject = await psaProjectClient.read({ id: psaProjectId }, [
          'projectId',
        ]);

        if (!psaProject.projectId) {
          throw new Error(
            `No Project found for PsaProject with id ${psaProjectId}`
          );
        }

        const activityData: ProjectApi.createActivities.Input['activities'] = [
          {
            name: input.taskName,
            description: input.activityDescription,
            billable: input.isBillableActivity,
            hoursBudget: input.hoursBudget,
            teamMemberIds: input.teamMember.map((member) => member.memberId),
            projectId: input.psaProjectId,
            billingType: EvstActivityBillingType.Hours,
            startDate: input.startDate,
            endDate: input.endDate,
          },
        ];

        // Set billing type and related fields based on provided data
        if (!input.isBillableActivity) {
          // For non-billable activities, we only set it as hours billing by default
          // since the enum doesn't have a not_billable option
          activityData[0].billingType = EvstActivityBillingType.Hours;
          activityData[0].milestoneId = null;
          activityData[0].fixedFeeId = null;
        } else if (input.milestoneId) {
          activityData[0].billingType = EvstActivityBillingType.Milestone;
          activityData[0].milestoneId = input.milestoneId;
          activityData[0].fixedFeeId = null;
        } else if (input.fixedFeeId) {
          activityData[0].billingType = EvstActivityBillingType.FixedFee;
          activityData[0].fixedFeeId = input.fixedFeeId;
          activityData[0].milestoneId = null;
        } else {
          activityData[0].billingType = EvstActivityBillingType.Hours;
          activityData[0].milestoneId = null;
          activityData[0].fixedFeeId = null;
        }

        log.debug('[TasksTab] Creating activity with data:', activityData);

        const result = await PsaInternalApi.createActivities(session, {
          projectId: psaProject.projectId,
          activities: activityData,
        });

        // 3. Return the created task instance
        if (!result.activityIds || result.activityIds.length === 0) {
          throw new Error('Failed to create task');
        }

        const createdInstances = inputs.map((input, index) => {
          return {
            psaProjectId: input.psaProjectId,
            taskId: result.activityIds[index],
            taskName: input.taskName,
            activityDescription: input.activityDescription,
            teamMember: input.teamMember,
            isBillableActivity: input.isBillableActivity,
            hoursBudget: input.hoursBudget,
            startDate: input.startDate,
            endDate: input.endDate,
            allocatedHours: input.allocatedHours,
            actualHours: input.actualHours || new Decimal(0),
          };
        });

        return {
          instances: createdInstances,
        };
      },
    },

    update: {
      async execute({
        session,
        id,
        data,
      }: TasksTab.EntitySets.Tasks.Update.Execute.Request): Promise<TasksTab.EntitySets.Tasks.Update.Execute.Response> {
        log.debug(
          'TasksTabService.Tasks.update.execute called with id:',
          id,
          'data:',
          data
        );

        if (!id) {
          throw new Error('Task ID is required for update');
        }

        // Get the timetrackingProjectId from the task ID
        const projectsViewClient = await Projects.client(session);
        const projectData = await projectsViewClient.query(
          {
            where: {
              select: 'activities',
              activityId: id,
            },
          },
          ['projectId']
        );
        if (projectData.length === 0) {
          throw new Error(`No project found for activity ID: ${id}`);
        }
        const timetrackingProjectId = projectData[0].projectId;
        // Get the psaProjectId from the timetrackingProjectId
        const psaProjectClient = await PsaProject.client(session);
        const psaProject = await psaProjectClient.read(
          { projectId: timetrackingProjectId },
          ['id']
        );
        if (!psaProject || !psaProject.id) {
          throw new Error(
            `No PsaProject found for timetrackingProjectId: ${timetrackingProjectId}`
          );
        }
        const psaProjectId = psaProject.id;

        // Check permissions for the project
        const permission = await PsaPermissionApi.getProjectPermissions(
          session,
          psaProjectId
        );
        if (!(permission >= PsaPermission.Edit)) {
          throw new Error('You do not have permission to edit this project');
        }

        // Call the timetracking public API to update the activity
        const activityData: ProjectApi.updateActivities.Input['activities'] = [
          {
            id: id,
            name: data.taskName,
            description: data.activityDescription,
            billable: data.isBillableActivity,
            hoursBudget: data.hoursBudget,
            teamMemberIds: data.teamMember.map((member) => member.memberId),
            billingType: EvstActivityBillingType.Hours,
            startDate: data.startDate,
            endDate: data.endDate,
          },
        ];

        // Set billing type and related fields based on provided data
        if (!data.isBillableActivity) {
          // For non-billable activities, we only set it as hours billing by default
          // since the enum doesn't have a not_billable option
          activityData[0].billingType = EvstActivityBillingType.Hours;
          activityData[0].milestoneId = null;
          activityData[0].fixedFeeId = null;
        } else if (data.milestoneId) {
          activityData[0].billingType = EvstActivityBillingType.Milestone;
          activityData[0].milestoneId = data.milestoneId;
          activityData[0].fixedFeeId = null;
        } else if (data.fixedFeeId) {
          activityData[0].billingType = EvstActivityBillingType.FixedFee;
          activityData[0].fixedFeeId = data.fixedFeeId;
          activityData[0].milestoneId = null;
        } else {
          activityData[0].billingType = EvstActivityBillingType.Hours;
          activityData[0].milestoneId = null;
          activityData[0].fixedFeeId = null;
        }

        log.debug('[TasksTab] Updating activity with data:', activityData);

        const result = await PsaInternalApi.updateActivities(session, {
          activities: activityData,
        });

        log.debug('Task updated successfully:', result.activityIds);
      },
    },

    delete: {
      async execute({
        session,
        ids,
      }: TasksTab.EntitySets.Tasks.Delete.Execute.Request): Promise<TasksTab.EntitySets.Tasks.Delete.Execute.Response> {
        log.debug('TasksTabService.Tasks.delete.execute called with ids:', ids);

        if (!ids || ids.length === 0) {
          throw new Error('No task IDs provided for deletion');
        }

        // Get all distinct psaProjectIds from the tasks
        const psaProjectIds = new Set<number>();
        const projectsViewClient = await Projects.client(session);
        for (const id of ids) {
          const projectData = await projectsViewClient.query(
            {
              where: {
                select: 'activities',
                activityId: id,
              },
            },
            ['projectId']
          );
          if (projectData.length === 0) {
            throw new Error(`No project found for activity ID: ${id}`);
          }
          const timetrackingProjectId = projectData[0].projectId;

          // Get the psaProjectId from the timetrackingProjectId
          const psaProjectClient = await PsaProject.client(session);
          const psaProject = await psaProjectClient.read(
            { projectId: timetrackingProjectId },
            ['id']
          );
          if (!psaProject || !psaProject.id) {
            throw new Error(
              `No PsaProject found for timetrackingProjectId: ${timetrackingProjectId}`
            );
          }
          psaProjectIds.add(psaProject.id);
        }

        // Check that we have at least edit permissions for all projects
        for (const psaProjectId of psaProjectIds) {
          const permission = await PsaPermissionApi.getProjectPermissions(
            session,
            psaProjectId
          );
          if (!(permission >= PsaPermission.Edit)) {
            throw new Error(
              `You do not have permission to delete tasks for project ID: ${psaProjectId}`
            );
          }
        }

        await ProjectApi.deleteActivities.execute(session, {
          activityIds: ids,
        });

        return;
      },
    },
  },

  Roles: {
    query: {
      async execute({
        session,
        where,
        orderBy,
        skip,
        take,
        fieldList: _fieldList,
      }: TasksTab.EntitySets.Roles.Query.Execute.Request): Promise<TasksTab.EntitySets.Roles.Query.Execute.Response> {
        log.debug(
          'TasksTabService.Roles.query.execute called with where:',
          where
        );

        // Ensure psaProjectId is in where as:
        // psaProjectId: {$eq: number}
        if (
          !where?.psaProjectId ||
          typeof where.psaProjectId !== 'object' ||
          !('$eq' in where.psaProjectId) ||
          typeof where.psaProjectId.$eq !== 'number'
        ) {
          throw new Error('Invalid psaProjectId provided');
        }

        const psaProjectId = where.psaProjectId.$eq;

        // Check permissions for the project
        const permission = await PsaPermissionApi.getProjectPermissions(
          session,
          psaProjectId
        );
        if (!(permission >= PsaPermission.View)) {
          throw new Error('You do not have permission to view this project');
        }

        // Get project data to obtain the HR project ID
        const psaProject = await PsaProject.read(
          session,
          { id: psaProjectId },
          ['id', 'projectId']
        );

        if (!psaProject) {
          throw new Error(`No PSA project found for ID: ${psaProjectId}`);
        }

        // Query the projects view to get positions for this project
        const projectsViewClient = await Projects.client(session);
        const positions = await projectsViewClient.query(
          {
            where: {
              projectId: psaProject.projectId,
              select: 'positions',
            },
            orderBy,
            skip,
            take,
          },
          ['positionId', 'positionName', 'positionBillable']
        );

        // Transform the positions data to match the expected response format
        const instances = positions.map((position) => ({
          psaProjectId,
          ...position,
        }));

        // Return roles data following the structure defined in presentation.ls
        return { instances };
      },
    },
  },

  TeamMembers: {
    query: {
      async execute({
        session,
        where,
        orderBy,
        skip,
        take,
        fieldList: _fieldList,
      }: TasksTab.EntitySets.TeamMembers.Query.Execute.Request): Promise<TasksTab.EntitySets.TeamMembers.Query.Execute.Response> {
        log.debug(
          'TasksTabService.TeamMembers.query.execute called with where:',
          where
        );

        // Ensure psaProjectId is in where as:
        // psaProjectId: {$eq: number}
        if (
          !where?.psaProjectId ||
          typeof where.psaProjectId !== 'object' ||
          !('$eq' in where.psaProjectId) ||
          typeof where.psaProjectId.$eq !== 'number'
        ) {
          throw new Error('Invalid psaProjectId provided');
        }

        const psaProjectId = where.psaProjectId.$eq;

        // Query the projects view to get team members for this project
        const projectsViewClient = await JoinedProjectView.client(session);
        const members = await projectsViewClient.query(
          {
            where: {
              psaProjectId: psaProjectId,
              select: 'membersAndPositions',
            },
            orderBy,
            skip,
            take,
          },
          [
            'memberId',
            'memberName',
            'memberDisplayName',
            'memberEmail',
            'positionId',
            'positionName',
            'positionBillable',
          ]
        );

        // Return team members data following the structure defined in presentation.ls
        return { instances: members };
      },
    },
  },

  BillableMilestones: {
    query: {
      async execute({
        session,
        where,
        orderBy,
        skip,
        take,
        fieldList: _fieldList,
      }: TasksTab.EntitySets.BillableMilestones.Query.Execute.Request): Promise<TasksTab.EntitySets.BillableMilestones.Query.Execute.Response> {
        log.debug(
          'TasksTabService.BillableMilestones.query.execute called with where:',
          where
        );

        // Ensure psaProjectId is in where as:
        // psaProjectId: {$eq: number}
        if (
          !where?.psaProjectId ||
          typeof where.psaProjectId !== 'object' ||
          !('$eq' in where.psaProjectId) ||
          typeof where.psaProjectId.$eq !== 'number'
        ) {
          throw new Error('Invalid psaProjectId provided');
        }

        const psaProjectId = where.psaProjectId.$eq;

        // Query billable milestones for this project
        const milestones = await Milestone.query(
          session,
          {
            where: {
              psaProjectId: psaProjectId,
              billable: true, // Only fetch billable milestones
            },
            orderBy,
            skip,
            take,
          },
          ['id', 'name', 'amount', 'dueDate', 'status']
        );

        // Transform to match the expected structure
        const instances = milestones.map((milestone) => ({
          psaProjectId: psaProjectId,
          id: milestone.id,
          name: milestone.name,
          amount: milestone.amount,
          dueDate: milestone.dueDate,
          status: milestone.status,
        }));

        return { instances };
      },
    },
  },

  BillableFixedFees: {
    query: {
      async execute({
        session,
        where,
        orderBy,
        skip,
        take,
        fieldList: _fieldList,
      }: TasksTab.EntitySets.BillableFixedFees.Query.Execute.Request): Promise<TasksTab.EntitySets.BillableFixedFees.Query.Execute.Response> {
        log.debug(
          'TasksTabService.BillableFixedFees.query.execute called with where:',
          where
        );

        // Ensure psaProjectId is in where as:
        // psaProjectId: {$eq: number}
        if (
          !where?.psaProjectId ||
          typeof where.psaProjectId !== 'object' ||
          !('$eq' in where.psaProjectId) ||
          typeof where.psaProjectId.$eq !== 'number'
        ) {
          throw new Error('Invalid psaProjectId provided');
        }

        const psaProjectId = where.psaProjectId.$eq;

        // Query fixed fees for this project
        const fixedFees = await FixedFee.query(
          session,
          {
            where: {
              psaProjectId: psaProjectId,
            },
            orderBy,
            skip,
            take,
          },
          ['id', 'amount', 'frequency', 'startDate', 'endDate']
        );

        // Transform to match the expected structure
        const instances = fixedFees.map((fixedFee) => ({
          psaProjectId: psaProjectId,
          id: fixedFee.id,
          amount: fixedFee.amount,
          frequency: fixedFee.frequency,
          startDate: fixedFee.startDate,
          endDate: fixedFee.endDate,
        }));

        return { instances };
      },
    },
  },
} satisfies TasksTab.Implementation;
