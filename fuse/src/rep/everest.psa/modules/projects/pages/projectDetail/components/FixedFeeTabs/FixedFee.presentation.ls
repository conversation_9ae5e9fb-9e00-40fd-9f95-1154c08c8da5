package everest.psa

odata presentation FixedFee {

    data-set FixedFees {
        supported-operations view, add, change,
            remove
        field id (editable: false): field<FixedFee.id>
        field psaProjectId (required: true): field<PsaProject.id>
        field startDate (required: true): PlainDate
        field endDate (required: true): PlainDate
        field amount (required: true): composite<everest.base::CurrencyAmount>
        field frequency (required: true): enum<FixedFeeFrequency>
        field salesOrderProductLineId: field<FixedFee.salesOrderProductLineId>
    }

    data-set Projects {
        supported-operations view
        field id (required: false): field<PsaProject.id>
        field currency: enum<everest.base::BaseCurrency>
        field salesArrangement: object<{
            id: field<everest.fin.accounting::SalesArrangement.id>
            salesArrangementNumber: Text
            salesOrders: array<object<{
                id: field<everest.fin.accounting::SalesOrder.id>
                salesOrderNumber: Text
                products: array<object<{
                    id: field<everest.fin.accounting::SalesOrderProduct.id>
                    productName: Text
                    totalPrice: composite<everest.base::CurrencyAmount>
                    active: TrueFalse
                    productLines: array<object<{
                        id: field<everest.fin.accounting::SalesOrderProductLine.id>
                        productLineName: Text
                        salesOrderProductLineNumber: Text
                        serviceStartDate: PlainDate
                        serviceEndDate: PlainDate
                        quantity: JSON
                        unitPrice: JSON
                        totalPrice: composite<everest.base::CurrencyAmount>
                        priceModel: Text
                        invoiceFrequency: Text
                        lineType: enum<everest.fin.accounting::ProductLineType>
                        active: TrueFalse
                        productLineId: field<everest.fin.accounting::SalesOrderProductLine.id>
                        unitCost: composite<everest.base::CurrencyAmount>
                        invoiceMilestones: array<object<{
                            id: field<everest.fin.accounting::InvoiceMilestone.id>
                            `description`: Text
                            totalPrice: composite<everest.base::CurrencyAmount>
                            projectedDate: PlainDate
                            invoiceDate: PlainDate
                            invoiceNumber: Text
                        }>>
                    }>>
                }>>
            }>>
        }>
    }

    action validateFixedFees {
        inputs {
            psaProjectId: field<PsaProject.id>
        }
        outputs {
            warnings: array<object<{
                message: Text
                code: Text
                severity: Text
                id: Number<Int>
                artifactType: Text
            }>>
        }
        properties {
            side-effects false
        }
    }
}
