/**
 * Real implementation of the ExpensesTab presentation service
 * This file fetches expense data from the Expenses view
 */

import { log } from '@everestsystems/content-core';
import type { EvstBillableExpenseType } from '@pkg/everest.fin.base/types/enums/BillableExpenseType';
import type { EvstExpenseStatus } from '@pkg/everest.fin.base/types/enums/ExpenseStatus';
import { Expenses } from '@pkg/everest.fin.expense/types/views/publicApi/view/Expenses/Expenses';
import PsaPermissionApi, {
  PsaPermission,
} from '@pkg/everest.psa/lib/permissions/PsaPermissionApi';
import type { ExpensesTab } from '@pkg/everest.psa/types/presentations/modules/projects/pages/projectDetail/components/ExpensesTab/ExpensesTab';
import { PsaProject } from '@pkg/everest.psa/types/PsaProject';
import { TimetrackingProject } from '@pkg/everest.timetracking/types/TimetrackingProject';

/**
 * Implementation of the ExpensesTabService presentation
 * This provides real data for the ProjectExpenses entity set by querying the Expenses view
 */
export default {
  ProjectExpenses: {
    query: {
      async execute({
        session,
        where,
        orderBy,
        skip,
        take,
        fieldList: _fieldList,
        count: _count,
      }: ExpensesTab.EntitySets.ProjectExpenses.Query.Execute.Request): Promise<ExpensesTab.EntitySets.ProjectExpenses.Query.Execute.Response> {
        try {
          log.debug('ExpensesTab.ProjectExpenses.query.execute called with:', {
            where,
          });

          // Extract project ID from where clause if available
          let psaProjectId: number | undefined;
          if (where && where.psaProjectId) {
            psaProjectId =
              typeof where.psaProjectId === 'number'
                ? where.psaProjectId
                : (where.psaProjectId as { $eq: number }).$eq;
          }

          if (!psaProjectId) {
            throw new Error('psaProjectId is required in the where clause');
          }

          // Check permissions for the project
          const permission = await PsaPermissionApi.getProjectPermissions(
            session,
            psaProjectId
          );
          if (!(permission >= PsaPermission.View)) {
            throw new Error('You do not have permission to view this project');
          }

          // Get timetracking project ID from PSA project ID
          const psaProject = await PsaProject.read(
            session,
            { id: psaProjectId },
            ['projectId']
          );

          if (!psaProject || !psaProject.projectId) {
            throw new Error(
              'No timetracking project found for PSA project ID: ' +
                psaProjectId
            );
          }

          const timetrackingProjectId = psaProject.projectId;

          // Get team ID from timetracking project
          const timetrackingProject = await TimetrackingProject.read(
            session,
            { id: timetrackingProjectId },
            ['teamId']
          );

          if (!timetrackingProject || !timetrackingProject.teamId) {
            throw new Error(
              'No team found for timetracking project ID: ' +
                timetrackingProjectId
            );
          }

          const teamId = timetrackingProject.teamId;

          // Query expenses related to the project - filter by team ID
          const expenses = await Expenses.query(
            session,
            {
              where: {
                teamId: { $eq: teamId },
              },
              orderBy,
              skip,
              take,
            },
            [
              'id',
              'expenseReportId',
              'employeeName',
              'categoryName',
              'amount',
              'amountCurrency',
              'status',
              'description',
              'billDate',
              'billableExpense',
              'expenseReportNumber',
            ]
          );

          // Return response with instances
          const response: ExpensesTab.EntitySets.ProjectExpenses.Query.Execute.Response =
            {
              instances: expenses.map((expense) => ({
                psaProjectId: psaProjectId,
                categoryName: expense.categoryName,
                expenseId: expense.id,
                associatedExpenseReportId: expense.expenseReportId,
                employeeName: expense.employeeName,
                amount: expense.amount,
                amountCurrency: expense.amountCurrency,
                status: expense.status as EvstExpenseStatus,
                description: expense.description,
                billDate: expense.billDate,
                billableExpenseType:
                  expense.billableExpense as EvstBillableExpenseType,
                expenseReportNumber: expense.expenseReportNumber,
              })),
            };

          return response;
        } catch (error) {
          log.error(
            'Error in ExpensesTab.ProjectExpenses.query.execute:',
            error
          );
          throw error;
        }
      },
    },
  },
} satisfies ExpensesTab.Implementation;
