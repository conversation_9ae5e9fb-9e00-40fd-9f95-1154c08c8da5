{"type": "react", "componentUrn": "urn:evst:everest:psa:artifact/ui/tsx:modules/projects/pages/projectDetail/components/TasksTab/TasksTab", "backendAccess": {"urn:evst:everest:psa:presentation:modules/projects/pages/projectDetail/components/TasksTab/TasksTab": ["Tasks/view", "Tasks/add", "Tasks/change", "Tasks/remove", "Roles/view", "TeamMembers/view", "BillableMilestones/view", "BillableFixedFees/view"], "urn:evst:everest:psa:presentation:lib/permissions/PsaPermissionPreflight": ["CheckEditPermission"]}, "variants": {"management": {"title": "Project tasks management", "description": "Manage project tasks"}, "view": {"title": "Project tasks view", "description": "View project tasks", "excludedBackendAccess": {"urn:evst:everest:psa:presentation:modules/projects/pages/projectDetail/components/TasksTab/TasksTab": ["Tasks/add", "Tasks/change", "Tasks/remove"]}}}}