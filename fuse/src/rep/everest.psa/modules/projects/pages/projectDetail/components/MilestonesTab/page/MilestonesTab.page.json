{"type": "react", "componentUrn": "urn:evst:everest:psa:artifact/ui/tsx:modules/projects/pages/projectDetail/components/MilestonesTab/MilestonesTab", "backendAccess": {"urn:evst:everest:psa:presentation:modules/projects/pages/projectDetail/components/MilestonesTab/MilestonesTab": ["Milestones/view", "Milestones/add", "Milestones/change", "Milestones/remove", "Projects/view", "validateMilestones"], "urn:evst:everest:psa:presentation:lib/permissions/PsaPermissionPreflight": ["CheckEditPermission"]}, "variants": {"management": {"title": "Milestones management", "description": "Manage project milestones"}, "view": {"title": "Milestones view", "description": "View project milestones", "excludedBackendAccess": {"urn:evst:everest:psa:presentation:modules/projects/pages/projectDetail/components/MilestonesTab/MilestonesTab": ["Milestones/add", "Milestones/change", "Milestones/remove"]}}}}