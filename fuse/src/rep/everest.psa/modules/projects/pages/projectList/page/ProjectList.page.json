{"type": "react", "componentUrn": "urn:evst:everest:psa:artifact/ui/tsx:modules/projects/pages/projectList/ProjectList", "backendAccess": {"urn:evst:everest:psa:presentation:lib/permissions/IsSuperAdmin": ["IsSuper<PERSON>dmin"], "urn:evst:everest:psa:presentation:modules/projects/pages/projectList/ProjectList": ["PsaProjects/view", "PsaProjects/add", "PsaProjects/remove", "ActiveAndPlannedEmployees/view", "hasEditPermissions"]}, "variants": {"administration": {"title": "Project administration", "description": "Full administrative access to all projects"}, "view": {"title": "Project view", "description": "View-only access to projects", "excludedBackendAccess": {"urn:evst:everest:psa:presentation:lib/permissions/IsSuperAdmin": ["IsSuper<PERSON>dmin"], "urn:evst:everest:psa:presentation:modules/projects/pages/projectList/ProjectList": ["PsaProjects/add", "PsaProjects/remove", "ActiveAndPlannedEmployees/view"]}}}}