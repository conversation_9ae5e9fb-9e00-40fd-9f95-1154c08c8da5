// @i18n:psa
import React, {
  useEffect,
  useState,
  useRef,
  useMemo,
  useCallback,
} from 'react';
import { useTranslation } from 'react-i18next';
import {
  Input,
  Button,
  Select,
  Tag,
  Modal,
  Form,
  message,
  DatePicker,
  InputNumber,
  Tooltip,
} from 'antd';
import { useHistory } from 'react-router-dom';
import { ProjectListUI } from '@pkg/everest.psa/types/presentations/modules/projects/pages/projectList/ProjectList.ui';
import type { EvstPlainDate } from '@pkg/everest.appserver/types/primitives/PlainDate';
import { EvstPsaProjectStatus } from '@pkg/everest.psa/types/enums/PsaProjectStatus';
import { PlainDate } from '@everestsystems/datetime';
import { Typography, Icon, DataGridRef } from '@everestsystems/design-system';
import { getCurrencySymbol } from '@pkg/everest.base/public/currency/precision';
import {
  ColDef,
  GridReadyEvent,
  <PERSON>rid<PERSON><PERSON>,
  ICellRendererParams,
  FirstDataRenderedEvent,
  SortChangedEvent,
} from 'ag-grid-community';
import { EvstBaseCurrency } from '@pkg/everest.base/types/enums/BaseCurrency';
import ListLayout from '@pkg/everest.base.ui/public/reactLayouts/List.ui.tsx';

const { Span } = Typography;
const { Option } = Select;

// Create status labels mapping programmatically from all enum values
const STATUS_LABELS: Record<
  string,
  { text: string; color: string; background: string }
> = {};

// Define status colors based on status type
const STATUS_COLORS: Record<string, { color: string; background: string }> = {
  [EvstPsaProjectStatus.NotStarted]: {
    color: '#EF6C00',
    background: '#FFF8DE',
  },
  [EvstPsaProjectStatus.Paused]: { color: '#757575', background: '#F0F0F0' },
  [EvstPsaProjectStatus.Tentative]: { color: '#EF6C00', background: '#FFF8DE' },
  [EvstPsaProjectStatus.Cancelled]: { color: '#C62828', background: '#FFEBEE' },
  [EvstPsaProjectStatus.Completed]: { color: '#0277BD', background: '#DEF3FF' },
  [EvstPsaProjectStatus.InProgress]: {
    color: '#2E7D32',
    background: '#E8F5E9',
  },
};

// Populate STATUS_LABELS from the enum values
for (const value of Object.values(EvstPsaProjectStatus) as string[]) {
  // Format the enum value for display (e.g., "NotStarted" -> "Not Started")
  const displayName = value.replaceAll(/([A-Z])/g, ' $1').trim();
  STATUS_LABELS[value] = {
    text: displayName,
    color: STATUS_COLORS[value]?.color || '#757575',
    background: STATUS_COLORS[value]?.background || '#F0F0F0',
  };
}

// Define available currencies from EvstBaseCurrency enum - using all available options
const CURRENCIES = Object.entries(EvstBaseCurrency).map(([key, value]) => ({
  code: key,
  name: value,
}));

// Using types directly from ProjectList.ui.ts with additional tracking properties
type ProjectEntity = ProjectListUI.EntitySets.PsaProjects.Get.Entity;

interface ProjectsPageProps {
  pageState: {
    tabTitle: string;
  };
}

const ProjectsPage: React.FC<ProjectsPageProps> = ({ pageState }) => {
  const { t } = useTranslation();
  // Set the page title in the browser tab
  pageState.tabTitle = t('{{projects.pageTitle}}');

  const history = useHistory();
  const gridRef = useRef<DataGridRef>(null);
  const [gridApi, setGridApi] = useState<GridApi | null>(null);
  const [selectedProjects, setSelectedProjects] = useState<ProjectEntity[]>([]);

  const [projects, setProjects] = useState<ProjectEntity[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [sortField, setSortField] = useState('projectName');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [filters, setFilters] = useState<
    ProjectListUI.EntitySets.PsaProjects.Get.Filter | undefined
  >();
  const [searchText, setSearchText] = useState('');
  const [employees, setEmployees] = useState<
    ProjectListUI.EntitySets.ActiveAndPlannedEmployees.Get.Entity[]
  >([]);
  const [loadingEmployees, setLoadingEmployees] = useState(false);
  const [selectedCurrency, setSelectedCurrency] = useState<string>('USD');
  const [hasEditPermissions, setHasEditPermissions] = useState(false);
  const [checkingPermissions, setCheckingPermissions] = useState(true);
  // Form state is no longer needed with inline editing

  useEffect(() => {
    fetchProjects();
  }, [currentPage, pageSize, sortField, sortOrder, filters, searchText]);

  // Check permissions first, then fetch employees only if needed
  useEffect(() => {
    checkEditPermissions();
  }, []);

  // Separate useEffect for fetching employees only when we have edit permissions
  useEffect(() => {
    if (hasEditPermissions) {
      fetchEmployees();
    }
  }, [hasEditPermissions]);

  // Check edit permissions
  const checkEditPermissions = async () => {
    setCheckingPermissions(true);
    try {
      const response = await ProjectListUI.client
        .createFunctionExecuteRequest('hasEditPermissions')
        .setInput({})
        .execute();

      setHasEditPermissions(response.output.hasEditPermission);
    } catch (error) {
      console.error('Error checking edit permissions:', error);
      setHasEditPermissions(false);
    } finally {
      setCheckingPermissions(false);
    }
  };

  // Fetch projects with proper error handling and state management
  const fetchProjects = async () => {
    setLoading(true);
    // Clear current projects state to avoid any stale data
    setProjects([]);

    try {
      // Create a request with explicit query parameters
      const request = ProjectListUI.client
        .createEntitySetGetRequest('PsaProjects')
        .setSkip((currentPage - 1) * pageSize)
        .setTop(pageSize)
        .setOrderby([{ property: sortField, ordering: sortOrder }])
        .setCount(true);

      // Apply search if provided
      if (searchText && searchText.length >= 3) {
        request.setFilter({
          property: 'projectName',
          comparator: 'contains',
          value: searchText,
        });
      } else if (filters) {
        request.setFilter(filters);
      }

      // Execute the request with a fresh connection
      const response = await request.execute();

      if (!response || !response.entities) {
        console.error('Invalid response format from server');
        message.error(t('{{projects.loadError}}'));
        return;
      }

      // Add a unique key to each project for React rendering
      const projectsWithId = response.entities.map((entity) => {
        // Ensure we have a clean project entity with all needed fields from backend
        return {
          ...entity,
          // Add a unique key for React if needed
          _key: `psaProject_${entity.id}_${Date.now()}`,
        } as ProjectEntity;
      });

      // Set state with the completely fresh data
      setProjects(projectsWithId);
      setTotalCount(response.count || 0);
    } catch (error) {
      console.error('Error fetching projects:', error);
      message.error(
        t('{{projects.fetchError}}', {
          error:
            error instanceof Error
              ? error.message
              : t('{{projects.networkError}}'),
        })
      );
    } finally {
      setLoading(false);
    }
  };

  const fetchEmployees = async () => {
    setLoadingEmployees(true);
    try {
      const response = await ProjectListUI.client
        .createEntitySetGetRequest('ActiveAndPlannedEmployees')
        .setOrderby([{ property: 'displayName', ordering: 'asc' }])
        .execute();

      if (response.entities && response.entities.length > 0) {
        // Filter out empty objects and objects without proper ID and displayName
        const filteredEmployees = response.entities.filter((emp) => {
          const isEmpty = Object.keys(emp).length === 0;
          const hasRequiredFields =
            typeof emp.displayName === 'string' && typeof emp.id === 'number';
          return !isEmpty && hasRequiredFields;
        });

        if (filteredEmployees.length === 0) {
          message.error(t('{{projects.loadEmployeeError}}'));
          setEmployees([]);
        } else {
          setEmployees(filteredEmployees);
        }
      } else {
        setEmployees([]);
      }
    } catch (error) {
      console.error('Error fetching employees:', error);
      message.error(
        t('{{projects.employeeDataError}}', {
          error:
            error instanceof Error
              ? error.message
              : t('{{projects.networkError}}'),
        })
      );
      setEmployees([]);
    } finally {
      setLoadingEmployees(false);
    }
  };

  const onFirstDataRendered = (params: FirstDataRenderedEvent) => {
    params.api.sizeColumnsToFit();
  };

  const onFilterChanged = useCallback(() => {
    if (!gridApi) {
      return;
    }

    const newFilters = gridApi.getFilterModel();
    if (Object.keys(newFilters).length > 0) {
      const conditions: ProjectListUI.EntitySets.PsaProjects.Get.PropertyFilter[] =
        [];

      for (const [key, filterObj] of Object.entries(newFilters)) {
        // Safely cast the filter object to a type with the properties we need
        const filter = filterObj as {
          type?: string;
          filter?: string;
          values?: any[];
        };

        // Handle the projectLeads filter specially
        if (key === 'projectLeads') {
          // For text filters on projectLeads
          if (filter.type === 'text' && filter.filter) {
            // Use contains filter for project lead names
            conditions.push({
              property: 'projectName', // Fallback to filter by project name since backend doesn't support projectLeads filtering
              comparator: 'contains',
              value: filter.filter,
            });
          }
        } else if (filter.type === 'set' && Array.isArray(filter.values)) {
          // Handle set filters for other columns
          for (const value of filter.values) {
            conditions.push({
              property: key,
              comparator: 'eq',
              value,
            });
          }
        } else if (filter.type === 'text' && filter.filter) {
          // Handle text filters for other columns
          conditions.push({
            property: key,
            comparator: 'contains',
            value: filter.filter,
          });
        }
      }

      if (conditions.length > 0) {
        if (conditions.length === 1) {
          setFilters(conditions[0]);
        } else {
          // Create a compound filter with AND conditions
          let compoundFilter: ProjectListUI.EntitySets.PsaProjects.Get.Filter =
            conditions[0];
          for (let i = 1; i < conditions.length; i++) {
            compoundFilter = [compoundFilter, 'and', conditions[i]];
          }
          setFilters(compoundFilter);
        }
      } else {
        // eslint-disable-next-line unicorn/no-useless-undefined
        setFilters(undefined);
      }
    } else {
      // eslint-disable-next-line unicorn/no-useless-undefined
      setFilters(undefined);
    }
  }, [gridApi]);

  const handleSearch = (value: string) => {
    setSearchText(value);
    setCurrentPage(1);
  };

  const handleBatchDeleteProjects = async (selectedProjects) => {
    // Filter out invoiced projects
    const invoicedProjects = selectedProjects.filter(
      (project) => project.invoiced
    );
    const nonInvoicedProjects = selectedProjects.filter(
      (project) => !project.invoiced
    );

    if (invoicedProjects.length > 0) {
      if (nonInvoicedProjects.length === 0) {
        throw new Error(t('{{projects.cannotDeleteInvoicedProjects}}'));
      }

      message.warning(
        t('{{projects.invoicedProjectsSkipped}}', {
          count: invoicedProjects.length,
        })
      );
    }

    // Process deletions sequentially to ensure all are handled
    let successCount = 0;
    let failureCount = 0;

    for (const project of selectedProjects) {
      // Skip invoiced projects
      if (project.invoiced) {
        console.log(`Skipping invoiced project: ${project.projectName}`);
        continue;
      }

      try {
        if (project.id) {
          await ProjectListUI.client
            .createEntitySetDeleteRequest('PsaProjects')
            .setId(project.id as number)
            .execute();
          successCount++;
        } else {
          console.error('Cannot delete project: Missing psaProjectId', project);
          failureCount++;
        }
      } catch (error) {
        console.error(`Error deleting project ${project.projectName}:`, error);
        failureCount++;
      }
    }

    if (successCount > 0 && failureCount > 0) {
      message.warning(
        t('{{projects.partialDeleteSuccess}}', {
          successCount,
          failureCount,
        })
      );
    }

    if (successCount === 0) {
      throw new Error(t('{{projects.deleteFailure}}'));
    }

    fetchProjects();
  };

  const handleDeleteProject = async (project: ProjectEntity) => {
    // Check if the project is invoiced
    if (project.invoiced) {
      message.error(t('{{projects.cannotDeleteInvoicedProject}}'));
      return;
    }

    // Check if we have the necessary ID to delete this project
    if (!project.id) {
      console.error('Cannot delete project: Missing id', project);
      message.error(t('{{projects.missingIdError}}'));
      return;
    }

    try {
      // Confirm with the user before deletion
      Modal.confirm({
        title: t('{{projects.deleteSingleTitle}}'),
        content: t('{{projects.deleteSingleConfirmation}}', {
          projectName: project.projectName,
        }),
        okText: t('{{projects.yes}}'),
        okType: 'danger',
        cancelText: t('{{projects.no}}'),
        onOk: async () => {
          try {
            // Use the psaProjectId (PsaProject.id) for deletion
            await ProjectListUI.client
              .createEntitySetDeleteRequest('PsaProjects')
              .setId(project.id as number)
              .execute();

            message.success(
              t('{{projects.projectDeletedSuccess}}', {
                projectName: project.projectName,
              })
            );
            fetchProjects(); // Refresh the list
          } catch (error) {
            console.error('Error in deletion confirmation:', error);
            message.error(
              t('{{projects.deleteProjectError}}', {
                error:
                  error instanceof Error
                    ? error.message
                    : t('{{projects.unknownError}}'),
              })
            );
          }
        },
      });
    } catch (error) {
      console.error('Error preparing project deletion:', error);
      message.error(
        t('{{projects.deleteProjectError}}', {
          error:
            error instanceof Error
              ? error.message
              : t('{{projects.unknownError}}'),
        })
      );
    }
  };

  // Helper function to format date for display
  const formatDateForDisplay = (plainDate: PlainDate) => {
    if (!plainDate) {
      return '-';
    }
    const jsDate = new Date(plainDate.year, plainDate.month - 1, plainDate.day);
    return jsDate.toLocaleDateString();
  };

  // Helper function to format date for API
  const formatDateString = (date: Date | string | any): EvstPlainDate => {
    let year: number, month: number, day: number;

    if (date instanceof Date) {
      // Handle Date object
      year = date.getFullYear();
      month = date.getMonth() + 1;
      day = date.getDate();
    } else if (typeof date === 'string') {
      // If it's already a string, check if it's already formatted
      if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
        const parts = date.split('-');
        year = Number.parseInt(parts[0], 10);
        month = Number.parseInt(parts[1], 10);
        day = Number.parseInt(parts[2], 10);
      } else {
        // Handle string date by converting to Date first
        const dateObj = new Date(date);
        if (Number.isNaN(dateObj.getTime())) {
          console.error('Invalid date string:', date);
          throw new Error('Invalid date format');
        } else {
          year = dateObj.getFullYear();
          month = dateObj.getMonth() + 1;
          day = dateObj.getDate();
        }
      }
    } else if (date && typeof date === 'object' && date.$isDayjsObject) {
      // Handle dayjs object
      const dayjsDate = date.$d as Date;
      if (dayjsDate && !Number.isNaN(dayjsDate.getTime())) {
        year = dayjsDate.getFullYear();
        month = dayjsDate.getMonth() + 1;
        day = dayjsDate.getDate();
      } else {
        console.error('Invalid dayjs date:', date);
        throw new Error('Invalid dayjs date');
      }
    } else {
      console.error('Invalid date type:', typeof date, date);
      throw new Error('Invalid date type');
    }

    // Return a PlainDateLike object
    return {
      year,
      month,
      day,
    } as unknown as EvstPlainDate;
  };

  const handleProjectCreation = async (form) => {
    const values = await form.validateFields();

    // Convert date objects to proper format
    const formattedValues = {
      ...values,
      startDate: values.startDate
        ? formatDateString(values.startDate)
        : undefined,
      endDate: values.endDate ? formatDateString(values.endDate) : undefined,
      currency: values.currency || 'USD',
    };

    // Handle projectLeads explicitly when they exist in the form values
    if (
      values.projectLeadIds &&
      Array.isArray(values.projectLeadIds) &&
      values.projectLeadIds.length > 0
    ) {
      // Convert to array of project lead objects
      formattedValues.projectLeads = values.projectLeadIds.map((id: number) => {
        // Find the employee with the matching ID
        const selectedEmployee = employees.find((emp) => emp.id === id);

        return {
          projectLeadId: id,
          projectLeadDisplayName: selectedEmployee?.displayName || '',
          projectLead: selectedEmployee?.name || '',
        };
      });

      // Remove the projectLeadIds field as it's not part of the API
      delete formattedValues.projectLeadIds;
    }

    // Create new project
    const response = await ProjectListUI.client
      .createEntitySetPostRequest('PsaProjects')
      .setData(formattedValues)
      .execute();

    // Store the created project ID for navigation after modal closes
    const createdProjectId = response?.entity?.id ?? null;

    // First clear all UI state completely
    try {
      form.resetFields();
    } catch (error) {
      console.error('Error clearing form:', error);
    }
    setProjects([]); // Clear projects to avoid duplicates
    setLoading(true); // Show loading state

    // Navigate to the project detail page if we have a pending project ID
    if (createdProjectId) {
      // Refresh the project list first to include the newly created project
      await fetchProjects();
      history.push(
        `/content/everest.psa/modules/projects/pages/projectDetail/ProjectDetail?psaProjectId=${createdProjectId}`
      );
    } else {
      // Fallback: refresh the project list if navigation is not possible
      setTimeout(() => {
        fetchProjects();
      }, 300);
    }
  };

  // Helper function to format date for display
  const getStatusDisplay = (status?: string) => {
    if (!status) {
      return null;
    }

    const statusInfo = STATUS_LABELS[status as keyof typeof STATUS_LABELS] || {
      text: status,
      color: '#757575',
      background: '#F0F0F0',
    };

    return (
      <Tag
        style={{
          backgroundColor: statusInfo.background,
          color: statusInfo.color,
          border: `1px solid ${statusInfo.color}`,
          borderRadius: '4px',
          padding: '0 8px',
        }}
      >
        {statusInfo.text}
      </Tag>
    );
  };

  // Cell renderer for the project name with tooltip and navigation
  const projectNameRenderer = (params: ICellRendererParams) => {
    return (
      <Span
        onClick={() => handleProjectNameClick(params.data)}
        style={{
          cursor: 'pointer',
          color: '#1890ff',
          textDecoration: 'underline',
        }}
      >
        {params.value}
      </Span>
    );
  };

  // Function to handle project name click and navigate to detail page
  const handleProjectNameClick = (project: ProjectEntity) => {
    if (project && project.id) {
      history.push(
        `/content/everest.psa/modules/projects/pages/projectDetail/ProjectDetail?psaProjectId=${project.id}`
      );
    } else {
      message.error(t('{{projects.navigationError}}'));
    }
  };

  // Cell renderer for the project leads display names
  const projectLeadRenderer = (params: ICellRendererParams) => {
    // Check if we have projectLeads array
    if (
      params.data?.projectLeads &&
      Array.isArray(params.data.projectLeads) &&
      params.data.projectLeads.length > 0
    ) {
      // Extract display names and join them with commas
      const displayNames = params.data.projectLeads
        .map((lead: any) => lead.projectLeadDisplayName)
        .filter(Boolean)
        .join(', ');
      return displayNames || '-';
    }
    return '-';
  };

  // Cell renderer for dates
  const dateRenderer = (params: ICellRendererParams) => {
    return formatDateForDisplay(params.value);
  };

  // Cell renderer for the status column
  const statusRenderer = (params: ICellRendererParams) => {
    return getStatusDisplay(params.value);
  };

  // Cell renderer for the project type column
  const projectTypeRenderer = (params: ICellRendererParams) => {
    return params.value || '-';
  };

  // Cell renderer for the budget amount column
  const budgetAmountRenderer = (params: ICellRendererParams) => {
    if (!params.value && params.value !== 0) {
      return '-';
    }

    // Access the currency from the data row
    // We added a default currency in the fetchProjects function
    const currencyCode = params.data?.currency;

    // Get the currency symbol - this should now always have a valid currency
    const symbol = getCurrencySymbol(currencyCode as EvstBaseCurrency);

    // Format the number with commas for thousands
    const formattedAmount = params.value
      .toString()
      .replaceAll(/\B(?=(\d{3})+(?!\d))/g, ',');

    return `${symbol}${formattedAmount}`;
  };

  // Cell renderer for the actions column
  const actionsRenderer = (params: ICellRendererParams) => {
    const record = params.data;
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          gap: '8px',
          height: '100%', // Fill the entire cell height
        }}
      >
        {hasEditPermissions ? (
          record.invoiced ? (
            <Tooltip
              title={t('{{projects.cannotDeleteInvoicedProjectTooltip}}')}
            >
              <Button
                type="text"
                danger
                icon={<Icon type="delete" />}
                disabled
                title={t('{{projects.deleteProjectTitle}}')}
              />
            </Tooltip>
          ) : (
            <Button
              type="text"
              danger
              icon={<Icon type="delete" />}
              onClick={() => handleDeleteProject(record)}
              title={t('{{projects.deleteProjectTitle}}')}
            />
          )
        ) : (
          <Tooltip title={t('{{projects.noEditPermissionTooltip}}')}>
            <Button
              type="text"
              danger
              icon={<Icon type="delete" />}
              disabled
              title={t('{{projects.deleteProjectTitle}}')}
            />
          </Tooltip>
        )}
      </div>
    );
  };
  // AG Grid column definitions
  const columnDefs = useMemo<ColDef[]>(
    () => [
      {
        headerName: '',
        field: 'selection',
        width: 50,
        checkboxSelection: true,
        headerCheckboxSelection: true,
        headerCheckboxSelectionFilteredOnly: true,
        pinned: 'left',
        lockPosition: true,
      },
      {
        headerName: t('{{projects.idHeader}}'),
        field: 'psaProjectId',
        sortable: true,
        filter: 'agNumberColumnFilter',
        width: 100,
        hide: true,
      },
      {
        headerName: t('{{projects.projectCodeHeader}}'),
        field: 'projectCode',
        sortable: true,
        filter: 'agTextColumnFilter',
        flex: 1.3,
      },
      {
        headerName: t('{{projects.projectNameHeader}}'),
        field: 'projectName',
        sortable: true,
        filter: 'agTextColumnFilter',
        cellRenderer: projectNameRenderer,
        flex: 2,
      },
      {
        headerName: t('{{projects.projectTypeHeader}}'),
        field: 'psaProjectTypeName',
        sortable: true,
        filter: 'agTextColumnFilter',
        cellRenderer: projectTypeRenderer,
        flex: 1,
      },
      {
        headerName: t('{{projects.projectLeadsHeader}}'),
        field: 'projectLeads',
        sortable: true,
        filter: 'agTextColumnFilter',
        cellRenderer: projectLeadRenderer,
        // Add a valueGetter to extract a filterable string from the projectLeads array
        valueGetter: (params) => {
          if (
            params.data?.projectLeads &&
            Array.isArray(params.data.projectLeads) &&
            params.data.projectLeads.length > 0
          ) {
            return params.data.projectLeads
              .map((lead: any) => lead.projectLeadDisplayName)
              .filter(Boolean)
              .join(', ');
          }
          return '';
        },
        flex: 1,
      },
      {
        headerName: t('{{projects.startDateHeader}}'),
        field: 'startDate',
        sortable: true,
        filter: 'agDateColumnFilter',
        cellRenderer: dateRenderer,
        flex: 1,
      },
      {
        headerName: t('{{projects.endDateHeader}}'),
        field: 'endDate',
        sortable: true,
        filter: 'agDateColumnFilter',
        cellRenderer: dateRenderer,
        flex: 1,
      },
      {
        headerName: t('{{projects.statusHeader}}'),
        field: 'status',
        sortable: true,
        filter: 'agSetColumnFilter',
        cellRenderer: statusRenderer,
        flex: 1,
      },
      {
        headerName: t('{{projects.budgetHeader}}'),
        field: 'budgetAmount',
        sortable: true,
        filter: 'agNumberColumnFilter',
        cellRenderer: budgetAmountRenderer,
        flex: 1,
      },
      {
        headerName: t('{{projects.actionsHeader}}'),
        field: 'actions',
        sortable: false,
        filter: false,
        cellRenderer: actionsRenderer,
        width: 120,
        cellStyle: {
          display: 'flex',
          alignItems: 'center',
          padding: '0', // Remove default padding to allow full height for content
        },
      },
    ],
    [
      projectNameRenderer,
      projectLeadRenderer,
      dateRenderer,
      statusRenderer,
      projectTypeRenderer,
      budgetAmountRenderer,
      actionsRenderer,
      hasEditPermissions,
    ]
  );

  const onPaginationChanged = useCallback(() => {
    if (gridApi) {
      const newPage = gridApi.paginationGetCurrentPage() + 1;
      const newPageSize = gridApi.paginationGetPageSize();

      setCurrentPage(newPage);
      if (pageSize !== newPageSize) {
        setPageSize(newPageSize);
      }
    }
  }, [gridApi, pageSize]);

  const onGridReady = (params: GridReadyEvent) => {
    setGridApi(params.api);
    // Set total row count for display in the status bar
    if (params.api && totalCount > 0) {
      params.api.setRowCount(totalCount);
    }
  };

  useEffect(() => {
    if (gridApi && totalCount > 0) {
      gridApi.setRowCount(totalCount);
    }
  }, [gridApi, totalCount]);

  const projectNames = selectedProjects.map((p) => p.projectName).join('\n• ');

  return (
    <>
      <ListLayout
        headerTitle="{{projects.pageTitle}}"
        loading={loading || checkingPermissions}
        contentAboveTable={
          <Input
            placeholder="{{projects.searchPlaceholder}}"
            allowClear
            onPressEnter={(e) =>
              /** Antd seems to not have the event properly typed */
              handleSearch((e.target as unknown as { value: string }).value)
            }
            onBlur={(e) => handleSearch(e.target.value)}
            style={{ width: 250 }}
            prefix={<Icon type="search" />}
          />
        }
        gridProps={{
          gridId: 'everest.psa_projectList',
          ref: gridRef,
          columnDefs: columnDefs,
          rowData: projects,
          onGridReady: onGridReady,
          onFirstDataRendered: onFirstDataRendered,
          onSortChanged: (params: SortChangedEvent) => {
            if (params.api.getColumnState) {
              const columnState = params.api.getColumnState();
              const sortedColumns = columnState.filter((col) => col.sort);

              if (sortedColumns.length > 0) {
                const sortCol = sortedColumns[0];
                setSortField(sortCol.colId);
                setSortOrder(sortCol.sort as 'asc' | 'desc');
              } else {
                setSortField('projectName');
                setSortOrder('asc');
              }
            }
          },
          onSelectionChanged: (event) => {
            setSelectedProjects(event.api.getSelectedRows());
          },
          onFilterChanged: onFilterChanged,
          onPaginationChanged: onPaginationChanged,
          pagination: true,
          paginationPageSize: pageSize,
          paginationPageSizeSelector: [10, 20, 50, 100],
          suppressPaginationPanel: false,
          rowModelType: 'clientSide',
          rowSelection: 'multiple',
          animateRows: true,
          enableCellTextSelection: true,
          statusBar: {
            statusPanels: [
              {
                statusPanel: 'agFilteredRowCountComponent',
                align: 'left',
              },
            ],
          },
        }}
        {...(hasEditPermissions && {
          createConfig: {
            createModalTitle: t('{{projects.createProjectTitle}}'),
            createFields: (
              <>
                <Form.Item
                  name="projectName"
                  label={t('{{projects.projectNameLabel}}')}
                  rules={[
                    {
                      required: true,
                      message: t('{{projects.projectNameRequired}}'),
                    },
                  ]}
                >
                  <Input
                    placeholder={t('{{projects.projectNamePlaceholder}}')}
                  />
                </Form.Item>

                <Form.Item
                  name="projectLeadIds"
                  label={t('{{projects.projectLeadsLabel}}')}
                  rules={[
                    {
                      required: true,
                      message: t('{{projects.projectLeadRequired}}'),
                    },
                  ]}
                >
                  <Select
                    placeholder={
                      employees.length > 0
                        ? t('{{projects.selectProjectLeads}}')
                        : t('{{projects.noEmployeesFound}}')
                    }
                    loading={loadingEmployees}
                    showSearch
                    mode="multiple"
                    disabled={employees.length === 0}
                    optionFilterProp="children"
                    filterOption={(input, option) =>
                      (option?.children as unknown as string)
                        ?.toLowerCase()
                        .includes(input.toLowerCase())
                    }
                  >
                    {employees.map((employee) => {
                      return (
                        <Option key={employee.id} value={employee.id}>
                          {employee.displayName}
                        </Option>
                      );
                    })}
                  </Select>
                </Form.Item>

                <Form.Item
                  name="startDate"
                  label={t('{{projects.startDateLabel}}')}
                >
                  <DatePicker style={{ width: '100%' }} />
                </Form.Item>

                <Form.Item
                  name="endDate"
                  label={t('{{projects.endDateLabel}}')}
                >
                  <DatePicker style={{ width: '100%' }} />
                </Form.Item>

                <Form.Item
                  name="status"
                  label={t('{{projects.statusLabel}}')}
                  initialValue={EvstPsaProjectStatus.NotStarted}
                  rules={[
                    {
                      required: true,
                      message: t('{{projects.statusRequired}}'),
                    },
                  ]}
                >
                  <Select placeholder={t('{{projects.selectStatus}}')}>
                    {Object.keys(STATUS_LABELS).map((key) => (
                      <Option key={key} value={key}>
                        {STATUS_LABELS[key as keyof typeof STATUS_LABELS].text}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>

                <Form.Item
                  name="currency"
                  label={t('{{projects.currencyLabel}}')}
                  initialValue={EvstBaseCurrency.USD}
                  rules={[
                    {
                      required: true,
                      message: t('{{projects.currencyRequired}}'),
                    },
                  ]}
                >
                  <Select
                    placeholder={t('{{projects.selectCurrency}}')}
                    onChange={(value) => setSelectedCurrency(value)}
                    showSearch
                    filterOption={(input, option) =>
                      (option?.children as unknown as string)
                        .toLowerCase()
                        .includes(input.toLowerCase())
                    }
                  >
                    {CURRENCIES.map((currency) => (
                      <Option key={currency.code} value={currency.code}>
                        {currency.name}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
                <Form.Item
                  name="budgetAmount"
                  label={t('{{projects.budgetLabel}}')}
                >
                  <InputNumber
                    key={`budget-amount-${selectedCurrency}`}
                    style={{ width: '100%' }}
                    placeholder={t('{{projects.budgetPlaceholder}}')}
                    formatter={(value) =>
                      `${getCurrencySymbol(
                        selectedCurrency as EvstBaseCurrency
                      )}${value}`.replaceAll(/\B(?=(\d{3})+(?!\d))/g, ',')
                    }
                    parser={(value) =>
                      value ? value.replaceAll(/[^\d.]/g, '') : ''
                    }
                  />
                </Form.Item>
              </>
            ),
            createSuccessTitle: t('{{projects.projectCreatedSuccess}}'),
            createErrorTitle: t('{{projects.saveProjectError}}'),
            onCreate: handleProjectCreation,
          },
        })}
        {...(hasEditPermissions && {
          deleteConfig: {
            deleteModalTitle: t('{{projects.deleteMultipleTitle}}', {
              count: selectedProjects.length,
            }),
            deleteModalContent: (
              <div>
                <p>{t('{{projects.deleteMultipleConfirmation}}')}</p>
                <p>• {projectNames}</p>
              </div>
            ),
            onDelete: handleBatchDeleteProjects,
            deleteErrorTitle: t('{{projects.deleteError}}'),
            deleteSuccessTitle: t('{{projects.projectDeletedSuccess}}'),
          },
        })}
      />
    </>
  );
};

export default ProjectsPage;
