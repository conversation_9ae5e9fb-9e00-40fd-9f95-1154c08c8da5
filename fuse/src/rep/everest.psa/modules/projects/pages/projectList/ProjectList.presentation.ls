package everest.psa

odata presentation ProjectList {

    data-set PsaProjects {
        supported-operations view, add, remove
        field id (required: false): field<PsaProject.id>
        field projectId (required: false): field<PsaProject.projectId>
        field projectName (required: true): Text
        field projectCode: number-range<PsaProject>
        field projectLeads: array<object<{
            projectLead: Text
            projectLeadId: Number<Int>
            projectLeadDisplayName: Text
        }>>
        field startDate: PlainDate
        field endDate: PlainDate
        field status (required: true): field<PsaProject.status>
        field createdDate (editable: false): DateTime
        field lastModifiedDate (editable: false): DateTime
        field active (editable: false): TrueFalse
        field currency (required: true): enum<everest.base::BaseCurrency>
        field budgetAmount: Number<Decimal>
        field salesRepresentativeId: Number<Int>
        field salesRepresentativeName: Text
        field invoiced: TrueFalse
        field psaProjectTypeName: Text
    }

    data-set ActiveAndPlannedEmployees {
        supported-operations view
        field id: Number<Int>
        field name: Text
        field displayName: Text
        field email: Text
    }

    action hasEditPermissions {
        inputs {
        }
        outputs {
            hasEditPermission: TrueFalse
        }
        properties {
            side-effects false
        }
    }
}
