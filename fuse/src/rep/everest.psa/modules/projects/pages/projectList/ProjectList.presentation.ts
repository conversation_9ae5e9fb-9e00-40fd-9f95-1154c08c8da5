import { log } from '@everestsystems/content-core';
import { PlainDateTime } from '@everestsystems/datetime';
import type { EvstPlainDate } from '@pkg/everest.appserver/types/primitives/PlainDate';
import type { EvstBaseCurrency } from '@pkg/everest.base/types/enums/BaseCurrency';
import { EmployeeView } from '@pkg/everest.hr.base/types/views/publicApi/view/EmployeeView/EmployeeView';
import { PsaPermissionApi } from '@pkg/everest.psa/lib/permissions/PsaPermissionApi';
import { PsaInternalApi } from '@pkg/everest.psa/lib/projectApi/PsaInternalApi';
import type { EvstPsaProjectStatus } from '@pkg/everest.psa/types/enums/PsaProjectStatus';
import type { ProjectList } from '@pkg/everest.psa/types/presentations/modules/projects/pages/projectList/ProjectList';
import { PsaProject } from '@pkg/everest.psa/types/PsaProject';
import { JoinedProjectView } from '@pkg/everest.psa/types/views/publicApi/view/Projects/JoinedProjectView';
import { ProjectApi } from '@pkg/everest.timetracking/types/presentations/publicApi/api/ProjectApi/ProjectApi';

export default {
  PsaProjects: {
    query: {
      async execute({
        session,
        where,
        orderBy,
        skip,
        take,
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        fieldList,
        count,
      }: ProjectList.EntitySets.PsaProjects.Query.Execute.Request): Promise<ProjectList.EntitySets.PsaProjects.Query.Execute.Response> {
        try {
          // Use JoinedProjectView to fetch project data
          const joinedProjectClient = await JoinedProjectView.client(session);
          const joinedProjects = await joinedProjectClient.query(
            {
              where,
              orderBy,
              skip,
              take,
            },
            [
              'psaProjectId',
              'projectId',
              'id',
              'projectName',
              'startDate',
              'endDate',
              'status',
              'active',
              'createdDate',
              'lastModifiedDate',
              'currency',
              'budgetAmount',
              'projectCode',
              'invoiced',
              'psaProjectTypeName',
            ]
          );
          // Merge this with the ProjectLeadView data
          const allProjectLeads = await joinedProjectClient.query(
            {
              where: {
                ...where,
                select: 'members',
                memberIsTeamLead: true,
              },
              orderBy,
              skip,
              take,
            },
            ['projectId', 'memberEmployeeId', 'memberName', 'memberDisplayName']
          );

          // Map the projects to the expected format
          const instances: ProjectList.EntitySets.PsaProjects.Query.Instance[] =
            joinedProjects.map((project) => {
              // Since PlainDate and PlainDateTime are incompatible, we need to cast
              // Using explicit typing instead of 'any'
              const startDate = project.startDate?.toPlainDate();
              const endDate = project.endDate?.toPlainDate();
              const projectLeads = allProjectLeads.filter(
                (lead) => lead.projectId === project.projectId
              );

              return {
                id: project.psaProjectId,
                projectId: project.projectId, // Use projectId for delete operations
                projectName: project.projectName,
                projectLeads: projectLeads.map((lead) => ({
                  projectLead: lead.memberName,
                  projectLeadId: lead.memberEmployeeId,
                  projectLeadDisplayName: lead.memberDisplayName,
                })),
                startDate,
                endDate,
                status: project.status as EvstPsaProjectStatus, // Using enum value directly for value-help
                currency: project.currency as EvstBaseCurrency,
                budgetAmount: project.budgetAmount,
                // Using proper type casting but needs to be addressed in a future update
                createdDate: project.createdDate,
                lastModifiedDate: project.lastModifiedDate,
                active: project.active as boolean,
                projectCode: project.projectCode,
                invoiced: project.invoiced,
                psaProjectTypeName: project.psaProjectTypeName,
              };
            });

          const response: ProjectList.EntitySets.PsaProjects.Query.Execute.Response =
            {
              instances,
            };

          // Add count if requested
          if (count) {
            response.count = instances.length;
          }

          return response;
        } catch (error) {
          log.error('Error in PsaProjects query execute', error);
          throw error;
        }
      },
    },

    create: {
      async execute({
        session,
        inputs,
      }: ProjectList.EntitySets.PsaProjects.Create.Execute.Request): Promise<ProjectList.EntitySets.PsaProjects.Create.Execute.Response> {
        try {
          const instances: ProjectList.EntitySets.PsaProjects.Create.Instance[] =
            [];

          for (const input of inputs) {
            // Validate required fields
            if (!input.projectName) {
              throw new Error('Project name is required');
            }

            if (!input.status) {
              throw new Error('Status is required');
            }

            // Convert PlainDate to PlainDateTime for Project creation
            // Using 12:00:00 (noon) to avoid timezone boundary issues
            const startDate = input.startDate
              ? new PlainDateTime(
                  input.startDate.year,
                  input.startDate.month,
                  input.startDate.day,
                  12,
                  0,
                  0,
                  0
                )
              : undefined;

            const endDate = input.endDate
              ? new PlainDateTime(
                  input.endDate.year,
                  input.endDate.month,
                  input.endDate.day,
                  12,
                  0,
                  0,
                  0
                )
              : undefined;

            const { projectId } = await ProjectApi.createProject.execute(
              session,
              {
                projectName: input.projectName,
                startDate,
                endDate,
                ...(input.projectLeads?.length > 0
                  ? {
                      projectLeadIds: input.projectLeads.map(
                        (lead) => lead.projectLeadId
                      ),
                    }
                  : {}),
              }
            );

            log.info(`Created timetracking project with ID ${projectId}`);

            if (!projectId) {
              throw new Error('Failed to create project in everest.hr.base');
            }

            // Create the PsaProject with the Project ID
            await PsaProject.create(
              session,
              {
                projectId,
                active: true,

                status: input.status as EvstPsaProjectStatus, // Use status from user input,
                currency: input.currency,
                budget: {
                  amount: input.budgetAmount,
                  currency: input.currency,
                },
                projectCode: input.projectCode,
              },
              ['id', 'projectId', 'active', 'projectCode']
            );

            log.info(`Created PsaProject`);

            // Query the created project to get its details
            const client = await JoinedProjectView.client(session);
            const project = await client.query(
              {
                where: {
                  projectId: { $eq: projectId },
                },
              },
              [
                'psaProjectId',
                'projectId',
                'projectName',
                'startDate',
                'endDate',
                'status',
                'active',
                'createdDate',
                'lastModifiedDate',
                'currency',
                'budgetAmount',
                'projectCode',
              ]
            );

            // Log the full project data for debugging
            log.info(
              `Created project details: ${JSON.stringify(project[0], null, 2)}`
            );

            if (project.length === 0) {
              throw new Error(
                `Failed to retrieve created project with ID ${projectId}`
              );
            }

            // Convert PlainDateTime back to PlainDate for the response
            const responseStartDate = project[0]
              .startDate as unknown as EvstPlainDate;
            const responseEndDate = project[0]
              .endDate as unknown as EvstPlainDate;

            // Map the created project to the expected instance format
            instances.push({
              id: project[0].psaProjectId,
              projectName: project[0].projectName,
              projectLeads: input.projectLeads,
              startDate: responseStartDate,
              endDate: responseEndDate,

              status: input.status as EvstPsaProjectStatus, // Use status from user input
              createdDate: project[0].createdDate,
              lastModifiedDate: project[0].lastModifiedDate,
              active: project[0].active as boolean,
              currency: input.currency, // Add currency from input
              budgetAmount: input.budgetAmount, // Add budgetAmount from input
              projectCode: project[0].projectCode, // Include projectCode from the project
            });
          }

          return {
            instances,
          };
        } catch (error) {
          log.error('Error in PsaProjects create execute', error);
          log.error(error instanceof Error ? error.message : String(error));
          throw error;
        }
      },
    },

    delete: {
      async execute({
        session,
        ids,
      }: ProjectList.EntitySets.PsaProjects.Delete.Execute.Request): Promise<ProjectList.EntitySets.PsaProjects.Delete.Execute.Response> {
        try {
          log.debug(`Deleting projects: ${ids.join(', ')}`);

          if (!ids || ids.length === 0) {
            return;
          }

          // Use the new deep delete function from PsaInternalApi
          await PsaInternalApi.deleteProjects(session, {
            projectIds: ids,
          });

          log.debug('Projects deleted successfully');
          return;
        } catch (error) {
          log.error('Error deleting project data', error);
          throw error;
        }
      },
    },
  },
  ActiveAndPlannedEmployees: {
    query: {
      async execute({
        session,
        where,
        orderBy,
        skip,
        take,
        fieldList,
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        count,
      }: ProjectList.EntitySets.ActiveAndPlannedEmployees.Query.Execute.Request): Promise<ProjectList.EntitySets.ActiveAndPlannedEmployees.Query.Execute.Response> {
        // Using the HR package EmployeeView with explicit filters for Active/Planned status
        const queryResults = await EmployeeView.client(session).then((client) =>
          client.query(
            {
              where: {
                $or: [{ status: 'Active' }, { status: 'Planned' }],
                ...where, // Keep any other filters from the original request
              },
              orderBy,
              skip,
              take,
            },
            fieldList
          )
        );

        // Map the query results to match the expected Instance type
        const instances: ProjectList.EntitySets.ActiveAndPlannedEmployees.Query.Instance[] =
          queryResults.map(({ id, name, displayName, email }) => ({
            id,
            name,
            displayName,
            email,
          }));

        return {
          instances,
          count: instances.length,
        };
      },
    },
  },
  hasEditPermissions: {
    async execute({
      session,
    }: ProjectList.Actions.hasEditPermissions.Execute.Request): Promise<ProjectList.Actions.hasEditPermissions.Execute.Response> {
      try {
        // Check if we have permission to execute the IsSuperAdmin dummy action or sandbox merge.
        const hasEditPermission =
          await PsaPermissionApi.isSuperAdminOrSandboxMerge(session);

        return {
          output: {
            hasEditPermission,
          },
        };
      } catch (error) {
        log.error('Error checking edit permissions', error);
        return {
          output: {
            hasEditPermission: false,
          },
        };
      }
    },
  },
} satisfies ProjectList.Implementation;
