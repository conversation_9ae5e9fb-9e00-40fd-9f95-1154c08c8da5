// @i18n:psa
import React, { useState, useEffect, useMemo, useRef } from 'react';
import { message } from 'antd';
import {
  Typography,
  DataGrid,
  DataGridRef,
} from '@everestsystems/design-system';
import { useTranslation } from 'react-i18next';
import { useHistory } from 'react-router-dom';
import { ColDef, ICellRendererParams, GridReadyEvent } from 'ag-grid-community';
import { TasksTabUI } from '@pkg/everest.psa/types/presentations/modules/projects/pages/personal/components/TasksTab/TasksTab.ui';

const { Span } = Typography;

type MyTasks = TasksTabUI.EntitySets.myTasks.Get.Entity[];

const TasksTab: React.FC<{ selectedEmployeeId?: number | null }> = ({
  selectedEmployeeId,
}) => {
  const { t } = useTranslation();
  const history = useHistory();

  const [loading, setLoading] = useState<boolean>(true);
  const [tasks, setTasks] = useState<MyTasks>([]);
  const gridRef = useRef<DataGridRef>(null);

  useEffect(() => {
    const fetchTasks = async () => {
      try {
        setLoading(true);

        // Only fetch data if we have a selected employee
        if (!selectedEmployeeId) {
          setTasks([]);
          setLoading(false);
          return;
        }

        const response = await TasksTabUI.client
          .createEntitySetGetRequest('myTasks')
          .setFilter({
            property: 'employeeId',
            comparator: 'eq',
            value: selectedEmployeeId,
          })
          .execute();

        const taskData = response.entities;
        setTasks(taskData);

        setLoading(false);
      } catch {
        setLoading(false);
      }
    };

    fetchTasks();
  }, [selectedEmployeeId]);

  const handleProjectNameClick = (task: any) => {
    if (task && task.psaProjectId) {
      history.push(
        `/content/everest.psa/modules/projects/pages/projectDetail/ProjectDetail?psaProjectId=${task.psaProjectId}`
      );
    } else {
      message.error(t('{{projects.navigationError}}'));
    }
  };

  const taskColumns: ColDef[] = useMemo(
    () => [
      {
        headerName: t('{{projects.taskName}}'),
        field: 'taskName',
        flex: 1,
        filter: true,
        sortable: true,
      },
      {
        headerName: t('{{projects.projectName}}'),
        field: 'projectName',
        flex: 1,
        filter: true,
        sortable: true,
        cellRenderer: (params: ICellRendererParams) => {
          return (
            <Span
              fontWeight="medium"
              onClick={() => handleProjectNameClick(params.data)}
              style={{
                cursor: 'pointer',
                color: '#1890ff',
                textDecoration: 'underline',
              }}
            >
              {params.value}
            </Span>
          );
        },
      },
      {
        headerName: t('{{projects.description}}'),
        field: 'activityDescription',
        flex: 2,
        filter: true,
        sortable: true,
      },
      {
        headerName: t('{{projects.billable}}'),
        field: 'isBillableActivity',
        flex: 1,
        filter: true,
        sortable: true,
        cellRenderer: (params: ICellRendererParams) => {
          return params.value ? t('{{common.yes}}') : t('{{common.no}}');
        },
      },
    ],
    [t]
  );

  const handleGridReady = (event: GridReadyEvent) => {
    gridRef.current = event.api as any;
  };

  return (
    <div className="tasks-tab">
      <div
        className="ag-theme-alpine"
        style={{ height: 600, width: '100%', position: 'relative' }}
      >
        <DataGrid
          gridId="everest.psa_personal_tasksTab"
          ref={gridRef}
          rowData={tasks}
          columnDefs={taskColumns}
          loading={loading}
          onGridReady={handleGridReady}
          variant="white"
          defaultColDef={{
            sortable: true,
            filter: true,
            resizable: true,
          }}
          pagination={true}
          paginationPageSize={25}
          animateRows={true}
          getRowId={(params) => params.data.taskId}
        />
      </div>
    </div>
  );
};

export default TasksTab;
