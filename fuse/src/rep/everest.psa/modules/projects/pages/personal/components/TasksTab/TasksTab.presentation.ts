import { log } from '@everestsystems/content-core';
import PersonalDashboardPermissions from '@pkg/everest.psa/lib/permissions/PersonalDashboardPermissions';
import type { TasksTab } from '@pkg/everest.psa/types/presentations/modules/projects/pages/personal/components/TasksTab/TasksTab';
import { JoinedProjectView } from '@pkg/everest.psa/types/views/publicApi/view/Projects/JoinedProjectView';

export default {
  myTasks: {
    query: {
      async execute({
        session,
        where,
        orderBy,
        skip,
        take,
        fieldList: _fieldList,
      }: TasksTab.EntitySets.myTasks.Query.Execute.Request): Promise<TasksTab.EntitySets.myTasks.Query.Execute.Response> {
        try {
          log.debug('TasksTabService.myTasks.query.execute called');

          // Get the employee ID from the filter
          const targetEmployeeId =
            typeof where?.employeeId === 'object'
              ? (where.employeeId?.$eq as number)
              : where?.employeeId;

          if (!targetEmployeeId) {
            log.warn('No employee ID provided in filter');
            return { instances: [] };
          }

          // Check if the current user can view this employee's dashboard
          const canView =
            await PersonalDashboardPermissions.canViewEmployeeDashboard(
              session,
              targetEmployeeId
            );

          if (!canView) {
            throw new Error(
              `You do not have permission to view employee ${targetEmployeeId}'s tasks`
            );
          }

          // Query JoinedProjectView with activities select to get task data
          const joinedProjectViewClient =
            await JoinedProjectView.client(session);
          const activities = await joinedProjectViewClient.query(
            {
              where: {
                select: 'activities',
                memberEmployeeId: targetEmployeeId,
              },
              orderBy,
              skip,
              take,
            },
            [
              'psaProjectId',
              'projectName',
              'activityId',
              'activityName',
              'activityDescription',
              'isBillableActivity',
            ]
          );

          // Deduplicate activities by taskId (same activity can appear multiple times for different roles)
          const activityMap = new Map();

          for (const activity of activities) {
            const taskId = activity.activityId;
            if (taskId && !activityMap.has(taskId)) {
              activityMap.set(taskId, {
                psaProjectId: activity.psaProjectId || 0,
                projectName: activity.projectName || '',
                taskId: taskId,
                taskName: activity.activityName || '',
                activityDescription: activity.activityDescription || '',
                isBillableActivity: activity.isBillableActivity || false,
              });
            }
          }

          const instances = Array.from(activityMap.values());

          log.debug(
            `Found ${instances.length} tasks for employee ${targetEmployeeId}`
          );

          return { instances };
        } catch (error) {
          log.error('Error in TasksTabService.myTasks.query.execute:', error);
          throw error;
        }
      },
    },
  },
} satisfies TasksTab.Implementation;
