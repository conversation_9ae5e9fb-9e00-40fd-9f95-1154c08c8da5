import { log } from '@everestsystems/content-core';
import { Employee<PERSON><PERSON> } from '@pkg/everest.hr.base/types/presentations/publicApi/api/EmployeeApi/EmployeeApi';
import { EmployeeView } from '@pkg/everest.hr.base/types/views/publicApi/view/EmployeeView/EmployeeView';
import PersonalDashboardPermissions from '@pkg/everest.psa/lib/permissions/PersonalDashboardPermissions';
import type { Personal } from '@pkg/everest.psa/types/presentations/modules/projects/pages/personal/Personal';

/**
 * OData implementation for the Personal page
 */
export default {
  getEmployeeInfo: {
    async execute({
      session,
      input: _input,
    }: Personal.Actions.getEmployeeInfo.Execute.Request): Promise<Personal.Actions.getEmployeeInfo.Execute.Response> {
      try {
        log.debug('Personal.getEmployeeInfo.execute called');

        // Get the current employee ID
        const { employeeId } = await Employee<PERSON><PERSON>.getEmployeeId.execute(
          session,
          {}
        );

        if (!employeeId) {
          throw new Error('No employee ID found for current user');
        }

        // Get employee display name
        const employeeClient = await EmployeeView.client(session);
        const employees = await employeeClient.query(
          {
            where: {
              id: employeeId,
            },
          },
          ['id', 'displayName']
        );

        const displayName =
          employees.length > 0 && employees[0].displayName
            ? employees[0].displayName
            : 'Unknown User';

        return {
          output: {
            displayName,
            employeeId,
          },
        };
      } catch (error) {
        log.error('Error in Personal.getEmployeeInfo.execute:', error);
        throw error;
      }
    },
  },

  getAccessibleEmployees: {
    async execute({
      session,
      input: _input,
    }: Personal.Actions.getAccessibleEmployees.Execute.Request): Promise<Personal.Actions.getAccessibleEmployees.Execute.Response> {
      try {
        log.debug('Personal.getAccessibleEmployees.execute called');

        const employees =
          await PersonalDashboardPermissions.getAccessibleEmployees(session);

        return {
          output: {
            employees,
          },
        };
      } catch (error) {
        log.error('Error in Personal.getAccessibleEmployees.execute:', error);
        throw error;
      }
    },
  },
} satisfies Personal.Implementation;
