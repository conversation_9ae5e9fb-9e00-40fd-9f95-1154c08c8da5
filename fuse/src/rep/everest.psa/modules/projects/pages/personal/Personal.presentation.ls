package everest.psa

odata presentation Personal {
    action getEmployeeInfo {
        inputs {
        }
        outputs {
            displayName: Text
            employeeId: Number<Int>
        }

        properties {
            side-effects false
        }
    }

    action getAccessibleEmployees {
        inputs {
        }
        outputs {
            employees: array<object<{
                id: Number<Int>
                displayName: Text
                firstName: Text
                lastName: Text
                email: Text
            }>>
        }

        properties {
            side-effects false
        }
    }

}
