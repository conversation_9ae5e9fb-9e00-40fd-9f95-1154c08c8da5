import { log } from '@everestsystems/content-core';
import computeDailyBreakdown from '@pkg/everest.psa/lib/kpi/time/dailyBreakdown';
import computeTimeKpis from '@pkg/everest.psa/lib/kpi/time/kpi';
import PersonalDashboardPermissions from '@pkg/everest.psa/lib/permissions/PersonalDashboardPermissions';
import type { ChartsTab } from '@pkg/everest.psa/types/presentations/modules/projects/pages/personal/components/ChartsTab/ChartsTab';

export default {
  getUtilizationMetrics: {
    async execute({
      session,
      input,
    }: ChartsTab.Actions.getUtilizationMetrics.Execute.Request): Promise<ChartsTab.Actions.getUtilizationMetrics.Execute.Response> {
      try {
        log.debug('ChartsTabService.getUtilizationMetrics.execute called', {
          employeeId: input.employeeId,
        });

        // Check if the current user can view this employee's dashboard
        const canView =
          await PersonalDashboardPermissions.canViewEmployeeDashboard(
            session,
            input.employeeId
          );

        if (!canView) {
          throw new Error(
            "You do not have permission to view this employee's dashboard"
          );
        }

        // Use the computeTimeKpis function for accurate calculations
        const [timeKpis, dailyBreakdown] = await Promise.all([
          computeTimeKpis(
            session,
            input.employeeId,
            input.startDate,
            input.endDate
          ),
          computeDailyBreakdown(
            session,
            input.employeeId,
            input.startDate,
            input.endDate
          ),
        ]);

        return {
          output: {
            // Core 4 KPIs
            totalHours: timeKpis.hours.total,
            totalBillableHours: timeKpis.hours.billable,
            totalHoursIncludingAbsences:
              timeKpis.hours.totalHoursIncludingAbsences,
            utilization: timeKpis.utilization.total,
            billableUtilization: timeKpis.utilization.billable,

            // User-friendly KPIs without absences
            utilizationWithoutAbsences:
              timeKpis.utilization.totalWithoutAbsences,
            billableUtilizationWithoutAbsences:
              timeKpis.utilization.billableWithoutAbsences,

            // Daily breakdown for chart
            dailyBreakdown: dailyBreakdown.map((day) => ({
              date: day.date,
              billableHours: day.billableHours,
              nonBillableHours: day.nonBillableHours,
              timeOffHours: day.timeOffHours,
            })),
          },
        };
      } catch (error) {
        log.error(
          'Error in ChartsTabService.getUtilizationMetrics.execute:',
          error
        );
        throw error;
      }
    },
  },
} satisfies ChartsTab.Implementation;
