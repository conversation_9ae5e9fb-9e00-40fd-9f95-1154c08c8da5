import { log } from '@everestsystems/content-core';
import PersonalDashboardPermissions from '@pkg/everest.psa/lib/permissions/PersonalDashboardPermissions';
import type { ProjectsTab } from '@pkg/everest.psa/types/presentations/modules/projects/pages/personal/components/ProjectsTab/ProjectsTab';
import { JoinedProjectView } from '@pkg/everest.psa/types/views/publicApi/view/Projects/JoinedProjectView';

export default {
  myProjects: {
    query: {
      async execute({
        session,
        where,
        orderBy,
        skip,
        take,
        fieldList: _fieldList,
      }: ProjectsTab.EntitySets.myProjects.Query.Execute.Request): Promise<ProjectsTab.EntitySets.myProjects.Query.Execute.Response> {
        try {
          log.debug('ProjectsTabService.myProjects.query.execute called');

          // Get the employee ID from the filter
          const targetEmployeeId =
            typeof where?.employeeId === 'object'
              ? (where.employeeId?.$eq as number)
              : where?.employeeId;

          if (!targetEmployeeId) {
            log.warn('No employee ID provided in filter');
            return { instances: [] };
          }

          // Check if the current user can view this employee's dashboard
          const canView =
            await PersonalDashboardPermissions.canViewEmployeeDashboard(
              session,
              targetEmployeeId
            );

          if (!canView) {
            throw new Error(
              `You do not have permission to view employee ${targetEmployeeId}'s projects`
            );
          }

          log.info(`targetEmployeeId: ${targetEmployeeId}`);

          // Get all projects where the target employee is a member
          const joinedProjectViewClient =
            await JoinedProjectView.client(session);
          const projectData = await joinedProjectViewClient.query(
            {
              where: {
                select: 'membersAndPositions',
                memberEmployeeId: targetEmployeeId,
              },
              orderBy,
              skip,
              take,
            },
            [
              'psaProjectId',
              'projectName',
              'startDate',
              'endDate',
              'status',
              'positionName',
            ]
          );

          // Group by project and collect roles
          const projectMap = new Map();

          for (const item of projectData) {
            const projectId = item.psaProjectId;

            if (!projectMap.has(projectId)) {
              projectMap.set(projectId, {
                psaProjectId: projectId,
                name: item.projectName,
                startDate: item.startDate,
                endDate: item.endDate,
                status: item.status,
                roles: [],
              });
            }

            // Add role if it's not already in the list
            const project = projectMap.get(projectId);
            if (
              item.positionName &&
              !project.roles.includes(item.positionName)
            ) {
              project.roles.push(item.positionName);
            }
          }

          const instances = Array.from(projectMap.values());

          log.debug(
            `Found ${instances.length} projects for employee ${targetEmployeeId}`
          );

          return { instances };
        } catch (error) {
          log.error(
            'Error in ProjectsTabService.myProjects.query.execute:',
            error
          );
          throw error;
        }
      },
    },
  },
} satisfies ProjectsTab.Implementation;
