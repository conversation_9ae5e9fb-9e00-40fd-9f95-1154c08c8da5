// @i18n:psa
import React, { useState, useEffect } from 'react';
import { Tabs, Select } from 'antd';
import { Typography } from '@everestsystems/design-system';
import { useTranslation } from 'react-i18next';
import { PersonalUI } from '@pkg/everest.psa/types/presentations/modules/projects/pages/personal/Personal.ui';
import ProjectsTab from './components/ProjectsTab/ProjectsTab.ui';
import TasksTab from './components/TasksTab/TasksTab.ui';
import ChartsTab from './components/ChartsTab/ChartsTab.ui';

const { H1 } = Typography;

interface AccessibleEmployee {
  id: number;
  displayName: string;
  firstName: string;
  lastName: string;
  email: string;
}

const PersonalTabs: {
  key: string;
  label: string;
  component: React.FC<{ selectedEmployeeId?: number | null }>;
}[] = [
  {
    key: 'time',
    label: '{{projects.time}}',
    component: ChartsTab,
  },
  {
    key: 'projects',
    label: '{{projects.projects}}',
    component: ProjectsTab,
  },
  {
    key: 'tasks',
    label: '{{projects.tasks}}',
    component: TasksTab,
  },
];

const PersonalPage = ({ pageState }: { pageState: { tabTitle: string } }) => {
  const { t } = useTranslation();

  pageState.tabTitle = t('{{projects.personalDashboard}}');

  const [activeTab, setActiveTab] = useState<string>('time');
  const [employeeDisplayName, setEmployeeDisplayName] = useState<string>('');
  const [accessibleEmployees, setAccessibleEmployees] = useState<
    AccessibleEmployee[]
  >([]);
  const [selectedEmployeeId, setSelectedEmployeeId] = useState<number | null>(
    null
  );

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Get current employee info
        const employeeInfoResponse = await PersonalUI.client
          .createFunctionExecuteRequest('getEmployeeInfo')
          .setInput({})
          .execute();

        const currentEmpId = employeeInfoResponse.output.employeeId;
        setSelectedEmployeeId(currentEmpId);
        setEmployeeDisplayName(employeeInfoResponse.output.displayName);

        // Get accessible employees
        const accessibleEmployeesResponse = await PersonalUI.client
          .createFunctionExecuteRequest('getAccessibleEmployees')
          .setInput({})
          .execute();

        setAccessibleEmployees(accessibleEmployeesResponse.output.employees);
      } catch {
        // Silently handle errors - will use fallback values
      }
    };

    fetchData();
  }, []);

  const handleEmployeeChange = async (employeeId: number) => {
    setSelectedEmployeeId(employeeId);

    // Update display name when employee changes
    const selectedEmployee = accessibleEmployees.find(
      (emp) => emp.id === employeeId
    );
    if (selectedEmployee) {
      setEmployeeDisplayName(selectedEmployee.displayName);
    }
  };

  return (
    <div
      className="personal-page"
      style={{ width: '90%', margin: 'auto auto', padding: '24px' }}
    >
      <div style={{ marginBottom: '24px' }}>
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '16px',
            marginBottom: '16px',
          }}
        >
          <H1>{t('{{projects.personalDashboard}}')}</H1>

          {/* Employee selector */}
          {accessibleEmployees.length > 1 ? (
            <Select
              value={selectedEmployeeId}
              onChange={handleEmployeeChange}
              style={{ width: 300 }}
              placeholder={t('{{projects.selectEmployee}}')}
              showSearch
              filterOption={(input, option) =>
                option?.children
                  ?.toString()
                  ?.toLowerCase()
                  ?.includes(input.toLowerCase()) ?? false
              }
            >
              {accessibleEmployees.map((employee) => (
                <Select.Option key={employee.id} value={employee.id}>
                  {employee.displayName}
                </Select.Option>
              ))}
            </Select>
          ) : (
            <span style={{ color: '#999', fontSize: '14px' }}>
              {employeeDisplayName}
            </span>
          )}
        </div>
      </div>

      <Tabs
        activeKey={activeTab}
        onChange={(key: string) => setActiveTab(key)}
        items={PersonalTabs.map((tab) => ({
          key: tab.key,
          label: t(tab.label),
          children: null,
        }))}
        style={{ marginBottom: '24px' }}
      />

      <div style={{ marginTop: '24px' }}>
        {activeTab === 'time' && (
          <ChartsTab selectedEmployeeId={selectedEmployeeId} />
        )}
        {activeTab === 'projects' && (
          <ProjectsTab selectedEmployeeId={selectedEmployeeId} />
        )}
        {activeTab === 'tasks' && (
          <TasksTab selectedEmployeeId={selectedEmployeeId} />
        )}
      </div>
    </div>
  );
};

export default PersonalPage;
