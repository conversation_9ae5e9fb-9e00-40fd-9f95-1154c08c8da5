// @i18n:psa
import React, { useState, useEffect, useMemo, useRef } from 'react';
import { Tag, message } from 'antd';
import {
  Typography,
  DataGrid,
  DataGridRef,
} from '@everestsystems/design-system';
import { useTranslation } from 'react-i18next';
import { useHistory } from 'react-router-dom';
import { EvstPsaProjectStatus } from '@pkg/everest.psa/types/enums/PsaProjectStatus';
import { ColDef, ICellRendererParams, GridReadyEvent } from 'ag-grid-community';
import { ProjectsTabUI } from '@pkg/everest.psa/types/presentations/modules/projects/pages/personal/components/ProjectsTab/ProjectsTab.ui';

const { Span } = Typography;

const STATUS_COLORS: Record<string, { color: string; background: string }> = {
  [EvstPsaProjectStatus.NotStarted]: {
    color: '#EF6C00',
    background: '#FFF8DE',
  },
  [EvstPsaProjectStatus.Paused]: { color: '#757575', background: '#F0F0F0' },
  [EvstPsaProjectStatus.Tentative]: { color: '#EF6C00', background: '#FFF8DE' },
  [EvstPsaProjectStatus.Cancelled]: { color: '#C62828', background: '#FFEBEE' },
  [EvstPsaProjectStatus.Completed]: { color: '#0277BD', background: '#DEF3FF' },
  [EvstPsaProjectStatus.InProgress]: {
    color: '#2E7D32',
    background: '#E8F5E9',
  },
};

type MyProjects = ProjectsTabUI.EntitySets.myProjects.Get.Entity[];

const ProjectsTab: React.FC<{ selectedEmployeeId?: number | null }> = ({
  selectedEmployeeId,
}) => {
  const { t } = useTranslation();
  const history = useHistory();

  const [loading, setLoading] = useState<boolean>(true);
  const [projects, setProjects] = useState<MyProjects>([]);
  const gridRef = useRef<DataGridRef>(null);

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setLoading(true);

        // Only fetch data if we have a selected employee
        if (!selectedEmployeeId) {
          setProjects([]);
          setLoading(false);
          return;
        }

        const response = await ProjectsTabUI.client
          .createEntitySetGetRequest('myProjects')
          .setFilter({
            property: 'employeeId',
            comparator: 'eq',
            value: selectedEmployeeId,
          })
          .execute();

        const projectData = response.entities;
        setProjects(projectData);

        setLoading(false);
      } catch {
        setLoading(false);
      }
    };

    fetchProjects();
  }, [selectedEmployeeId]);

  const formatDate = (date: any) => {
    if (!date) {
      return '-';
    }
    try {
      return date.toString();
    } catch {
      return '-';
    }
  };

  const handleProjectNameClick = (project: any) => {
    if (project && project.psaProjectId) {
      history.push(
        `/content/everest.psa/modules/projects/pages/projectDetail/ProjectDetail?psaProjectId=${project.psaProjectId}`
      );
    } else {
      message.error(t('{{projects.navigationError}}'));
    }
  };

  const projectColumns: ColDef[] = useMemo(
    () => [
      {
        headerName: t('{{projects.projectName}}'),
        field: 'name',
        flex: 1,
        filter: true,
        sortable: true,
        cellRenderer: (params: ICellRendererParams) => {
          return (
            <Span
              fontWeight="medium"
              onClick={() => handleProjectNameClick(params.data)}
              style={{
                cursor: 'pointer',
                color: '#1890ff',
                textDecoration: 'underline',
              }}
            >
              {params.value}
            </Span>
          );
        },
      },
      {
        headerName: t('{{projects.startDate}}'),
        field: 'startDate',
        flex: 1,
        filter: true,
        sortable: true,
        cellRenderer: (params: ICellRendererParams) => {
          return formatDate(params.value);
        },
      },
      {
        headerName: t('{{projects.endDate}}'),
        field: 'endDate',
        flex: 1,
        filter: true,
        sortable: true,
        cellRenderer: (params: ICellRendererParams) => {
          return formatDate(params.value);
        },
      },
      {
        headerName: t('{{projects.status}}'),
        field: 'status',
        flex: 1,
        filter: true,
        sortable: true,
        cellRenderer: (params: ICellRendererParams) => {
          const status = params.value;
          const statusColor = STATUS_COLORS[status] || {
            color: '#757575',
            background: '#F0F0F0',
          };
          const displayText = status.replaceAll(/([A-Z])/g, ' $1').trim();

          return (
            <Tag
              color={statusColor.background}
              style={{
                color: statusColor.color,
                border: `1px solid ${statusColor.color}`,
              }}
            >
              {displayText}
            </Tag>
          );
        },
      },
      {
        headerName: t('{{projects.roles}}'),
        field: 'roles',
        flex: 1,
        filter: true,
        sortable: true,
        cellRenderer: (params: ICellRendererParams) => {
          const roles = params.value as string[];
          return roles ? roles.join(', ') : '';
        },
      },
    ],
    [t]
  );

  const handleGridReady = (event: GridReadyEvent) => {
    gridRef.current = event.api as any;
  };

  return (
    <div className="projects-tab">
      <div
        className="ag-theme-alpine"
        style={{ height: 600, width: '100%', position: 'relative' }}
      >
        <DataGrid
          gridId="everest.psa_personal_projectsTab"
          ref={gridRef}
          rowData={projects}
          columnDefs={projectColumns}
          loading={loading}
          onGridReady={handleGridReady}
          variant="white"
          defaultColDef={{
            sortable: true,
            filter: true,
            resizable: true,
          }}
          pagination={true}
          paginationPageSize={25}
          animateRows={true}
          getRowId={(params) => params.data.psaProjectId}
        />
      </div>
    </div>
  );
};

export default ProjectsTab;
