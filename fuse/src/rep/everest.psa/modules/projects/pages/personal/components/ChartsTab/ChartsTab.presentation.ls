package everest.psa

odata presentation ChartsTab {
    action getUtilizationMetrics {
        inputs {
            startDate: PlainDate
            endDate: PlainDate
            employeeId: Number<Int>
        }
        outputs {
            totalHours: Number<Decimal>
            totalBillableHours: Number<Decimal>
            totalHoursIncludingAbsences: Number<Decimal>
            utilization: Number<Decimal>
            billableUtilization: Number<Decimal>
            utilizationWithoutAbsences: Number<Decimal>
            billableUtilizationWithoutAbsences: Number<Decimal>
            dailyBreakdown: array<object<{
                date: PlainDate
                billableHours: Number<Decimal>
                nonBillableHours: Number<Decimal>
                timeOffHours: Number<Decimal>
            }>>
        }

        properties {
            side-effects false
        }
    }
}
