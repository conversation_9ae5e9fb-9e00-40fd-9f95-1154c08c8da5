// @i18n:psa
import React, { useState, useEffect, useMemo } from 'react';
import { Row, Col, Card, DatePicker, Statistic, Tooltip, Button } from 'antd';
import { Typography } from '@everestsystems/design-system';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
} from 'chart.js';
import { Bar } from 'react-chartjs-2';
import { useTranslation } from 'react-i18next';
import { useHistory } from 'react-router-dom';
import { PlainDate } from '@everestsystems/datetime';
import { ChartsTabUI } from '@pkg/everest.psa/types/presentations/modules/projects/pages/personal/components/ChartsTab/ChartsTab.ui';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  ChartTooltip,
  Legend
);

const { RangePicker } = DatePicker;
const { Span } = Typography;

type UtilizationMetrics = {
  totalHours: number;
  totalBillableHours: number;
  totalHoursIncludingAbsences: number;
  utilization: number;
  billableUtilization: number;
  utilizationWithoutAbsences: number;
  billableUtilizationWithoutAbsences: number;
  dailyBreakdown: Array<{
    date: string;
    billableHours: number;
    nonBillableHours: number;
    timeOffHours: number;
  }>;
};

// Helper function to get the start of the week (Sunday)
const getWeekStart = (date: Date): Date => {
  const result = new Date(date);
  const day = result.getDay();
  const diff = result.getDate() - day;
  result.setDate(diff);
  result.setHours(0, 0, 0, 0);
  return result;
};

// Helper function to get the end of the week (Saturday)
const getWeekEnd = (date: Date): Date => {
  const result = new Date(date);
  const day = result.getDay();
  const diff = result.getDate() + (6 - day);
  result.setDate(diff);
  result.setHours(23, 59, 59, 999);
  return result;
};

const ChartsTab: React.FC<{ selectedEmployeeId?: number | null }> = ({
  selectedEmployeeId,
}) => {
  const { t } = useTranslation();
  const history = useHistory();

  const [utilizationMetrics, setUtilizationMetrics] =
    useState<UtilizationMetrics | null>(null);
  const [dateRange, setDateRange] = useState<[Date, Date]>(() => {
    // Default to current week + 3 weeks before (4 weeks total)
    const today = new Date();
    const currentWeekEnd = getWeekEnd(today);
    const threeWeeksAgoStart = getWeekStart(
      new Date(today.getTime() - 3 * 7 * 24 * 60 * 60 * 1000)
    );
    return [threeWeeksAgoStart, currentWeekEnd];
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Convert date range to PlainDate (timezone-safe)
        const [startDate, endDate] = dateRange;
        const startPlainDate = PlainDate.from(
          `${startDate.getFullYear()}-${String(
            startDate.getMonth() + 1
          ).padStart(2, '0')}-${String(startDate.getDate()).padStart(2, '0')}`
        );
        const endPlainDate = PlainDate.from(
          `${endDate.getFullYear()}-${String(endDate.getMonth() + 1).padStart(
            2,
            '0'
          )}-${String(endDate.getDate()).padStart(2, '0')}`
        );

        // Only fetch data if we have a selected employee
        if (!selectedEmployeeId) {
          setUtilizationMetrics(null);
          return;
        }

        // Fetch utilization metrics from backend
        const utilizationResponse = await ChartsTabUI.client
          .createFunctionExecuteRequest('getUtilizationMetrics')
          .setInput({
            startDate: startPlainDate,
            endDate: endPlainDate,
            employeeId: selectedEmployeeId,
          })
          .execute();

        // Convert Decimal values to numbers
        const convertedMetrics: UtilizationMetrics = {
          totalHours: Number(utilizationResponse.output.totalHours),
          totalBillableHours: Number(
            utilizationResponse.output.totalBillableHours
          ),
          totalHoursIncludingAbsences: Number(
            utilizationResponse.output.totalHoursIncludingAbsences
          ),
          utilization: Number(utilizationResponse.output.utilization),
          billableUtilization: Number(
            utilizationResponse.output.billableUtilization
          ),
          utilizationWithoutAbsences: Number(
            utilizationResponse.output.utilizationWithoutAbsences
          ),
          billableUtilizationWithoutAbsences: Number(
            utilizationResponse.output.billableUtilizationWithoutAbsences
          ),
          dailyBreakdown: utilizationResponse.output.dailyBreakdown.map(
            (day) => ({
              date: day.date.toString(),
              billableHours: Number(day.billableHours),
              nonBillableHours: Number(day.nonBillableHours),
              timeOffHours: Number(day.timeOffHours),
            })
          ),
        };

        setUtilizationMetrics(convertedMetrics);
      } catch {
        // Silently handle errors - metrics will remain null and UI will show default values
      }
    };

    fetchData();
  }, [dateRange, selectedEmployeeId]);

  // Calculate KPIs from utilization metrics
  const kpis = useMemo(() => {
    if (!utilizationMetrics) {
      return {
        totalHours: 0,
        totalBillableHours: 0,
        totalHoursIncludingAbsences: 0,
        utilization: 0,
        billableUtilization: 0,
        utilizationWithoutAbsences: 0,
        billableUtilizationWithoutAbsences: 0,
      };
    }

    return {
      totalHours: Number(utilizationMetrics.totalHours),
      totalBillableHours: Number(utilizationMetrics.totalBillableHours),
      totalHoursIncludingAbsences: Number(
        utilizationMetrics.totalHoursIncludingAbsences
      ),
      utilization: Number(utilizationMetrics.utilization),
      billableUtilization: Number(utilizationMetrics.billableUtilization),
      utilizationWithoutAbsences: Number(
        utilizationMetrics.utilizationWithoutAbsences
      ),
      billableUtilizationWithoutAbsences: Number(
        utilizationMetrics.billableUtilizationWithoutAbsences
      ),
    };
  }, [utilizationMetrics]);

  // Prepare chart data
  const chartData = useMemo(() => {
    if (!utilizationMetrics || !utilizationMetrics.dailyBreakdown) {
      return {
        labels: [],
        datasets: [],
      };
    }

    // Sort daily breakdown by date
    const sortedDailyData = [...utilizationMetrics.dailyBreakdown].sort(
      (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
    );

    const labels = sortedDailyData.map((day) => {
      const date = new Date(day.date);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const dayOfMonth = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${dayOfMonth}`;
    });

    const billableData = sortedDailyData.map((day) => day.billableHours);
    const nonBillableData = sortedDailyData.map((day) => day.nonBillableHours);
    const timeOffData = sortedDailyData.map((day) => day.timeOffHours);

    return {
      labels,
      datasets: [
        {
          label: t('{{projects.billableHours}}'),
          data: billableData,
          backgroundColor: 'rgba(22, 163, 74, 0.2)',
          borderColor: '#16a34a',
          borderWidth: 1,
        },
        {
          label: t('{{projects.nonBillableHours}}'),
          data: nonBillableData,
          backgroundColor: 'rgba(156, 163, 175, 0.2)',
          borderColor: '#9ca3af',
          borderWidth: 1,
        },
        {
          label: t('{{projects.timeOff}}'),
          data: timeOffData,
          backgroundColor: 'rgba(14, 165, 233, 0.2)',
          borderColor: '#0ea5e9',
          borderWidth: 1,
        },
      ],
    };
  }, [utilizationMetrics, t]);

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      tooltip: {
        mode: 'index' as const,
        intersect: false,
        callbacks: {
          label: function (context: any) {
            return `${context.dataset.label}: ${context.parsed.y}h`;
          },
        },
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
      },
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.05)',
        },
        ticks: {
          callback: function (value: any) {
            return `${value}h`;
          },
        },
      },
    },
  };

  const handleDateRangeChange = (dates: any) => {
    if (dates && dates.length === 2) {
      setDateRange([dates[0].toDate(), dates[1].toDate()]);
    }
  };

  const handleTimeRecordingClick = () => {
    history.push('/content/everest.psa/modules/time/pages/timesheet/Timesheet');
  };

  return (
    <div>
      {/* Header with Date Range Picker and Time Recording Button */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col span={24}>
          <Card>
            <Row align="middle" justify="space-between" gutter={16}>
              <Col>
                <Row align="middle" gutter={16}>
                  <Col>
                    <Span fontWeight="medium">
                      {t('{{projects.timePeriod}}')}:
                    </Span>
                  </Col>
                  <Col>
                    <RangePicker
                      onChange={handleDateRangeChange}
                      format="YYYY-MM-DD"
                    />
                  </Col>
                </Row>
              </Col>
              <Col>
                <Button type="primary" onClick={handleTimeRecordingClick}>
                  {t('{{projects.timeRecording}}')}
                </Button>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* Core KPIs */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={6}>
          <Tooltip
            title={`${t(
              '{{projects.includingAbsences}}'
            )}: ${kpis.totalHoursIncludingAbsences.toFixed(1)}h`}
            placement="top"
          >
            <Card style={{ cursor: 'help' }}>
              <Statistic
                title={t('{{projects.totalHours}}')}
                value={kpis.totalHours}
                suffix="h"
                precision={1}
              />
            </Card>
          </Tooltip>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title={t('{{projects.totalBillableHours}}')}
              value={kpis.totalBillableHours}
              suffix="h"
              precision={1}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Tooltip
            title={`${t(
              '{{projects.adjustedForAbsences}}'
            )}: ${kpis.utilizationWithoutAbsences.toFixed(1)}%`}
            placement="top"
          >
            <Card style={{ cursor: 'help' }}>
              <Statistic
                title={t('{{projects.utilization}}')}
                value={kpis.utilization}
                suffix="%"
                precision={1}
              />
            </Card>
          </Tooltip>
        </Col>
        <Col xs={24} sm={6}>
          <Tooltip
            title={`${t(
              '{{projects.adjustedForAbsences}}'
            )}: ${kpis.billableUtilizationWithoutAbsences.toFixed(1)}%`}
            placement="top"
          >
            <Card style={{ cursor: 'help' }}>
              <Statistic
                title={t('{{projects.billableUtilization}}')}
                value={kpis.billableUtilization}
                suffix="%"
                precision={1}
              />
            </Card>
          </Tooltip>
        </Col>
      </Row>

      {/* Daily Hours Chart */}
      <Row gutter={[16, 16]} style={{ marginTop: '24px' }}>
        <Col span={24}>
          <Card
            title={
              <Span fontWeight="medium">
                {t('{{projects.dailyHoursBreakdown}}')}
              </Span>
            }
          >
            <div style={{ height: 400 }}>
              <Bar data={chartData} options={chartOptions} />
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default ChartsTab;
