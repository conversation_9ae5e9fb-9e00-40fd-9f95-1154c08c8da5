{"type": "react", "componentUrn": "urn:evst:everest:psa:artifact/ui/tsx:modules/reports/pages/reports/UtilizationByMember", "backendAccess": {"urn:evst:everest:psa:presentation:modules/reports/pages/reports/UtilizationByMember": ["getUtilizationByMember", "exportUtilizationByMemberCsv"], "urn:evst:everest:psa:presentation:modules/reports/pages/reports/components/ReportFilters": ["Projects/view", "PsaProjectTypes/view", "Employees/view", "Departments/view", "Customers/view", "Entities/view"]}, "variants": {"view": {"title": "Utilization by member report view", "description": "View-only access to utilization by member report"}}}