{"type": "react", "componentUrn": "urn:evst:everest:psa:artifact/ui/tsx:modules/reports/pages/reports/UtilizationByEntity", "backendAccess": {"urn:evst:everest:psa:presentation:modules/reports/pages/reports/UtilizationByEntity": ["getUtilizationByEntity", "exportUtilizationByEntityCsv"], "urn:evst:everest:psa:presentation:modules/reports/pages/reports/components/ReportFilters": ["Projects/view", "PsaProjectTypes/view", "Employees/view", "Departments/view", "Customers/view", "Entities/view"]}, "variants": {"view": {"title": "Utilization by entity report view", "description": "View-only access to utilization by entity report"}}}