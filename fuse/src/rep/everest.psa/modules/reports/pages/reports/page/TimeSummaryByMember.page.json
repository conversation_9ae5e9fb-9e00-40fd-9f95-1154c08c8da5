{"type": "react", "componentUrn": "urn:evst:everest:psa:artifact/ui/tsx:modules/reports/pages/reports/TimeSummaryByMember", "backendAccess": {"urn:evst:everest:psa:presentation:modules/reports/pages/reports/TimeSummaryByMember": ["getTimeSummaryByMember", "exportTimeSummaryByMemberCsv"], "urn:evst:everest:psa:presentation:modules/reports/pages/reports/components/ReportFilters": ["Projects/view", "PsaProjectTypes/view", "Employees/view", "Departments/view", "Customers/view", "Entities/view"]}, "variants": {"view": {"title": "Time summary report view", "description": "View-only access to time summary by member report"}}}