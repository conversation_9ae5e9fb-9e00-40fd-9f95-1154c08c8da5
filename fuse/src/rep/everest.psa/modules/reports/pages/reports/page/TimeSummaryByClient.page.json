{"type": "react", "componentUrn": "urn:evst:everest:psa:artifact/ui/tsx:modules/reports/pages/reports/TimeSummaryByClient", "backendAccess": {"urn:evst:everest:psa:presentation:modules/reports/pages/reports/TimeSummaryByClient": ["getTimeSummaryByClient", "exportTimeSummaryByClientCsv"], "urn:evst:everest:psa:presentation:modules/reports/pages/reports/components/ReportFilters": ["Projects/view", "PsaProjectTypes/view", "Employees/view", "Departments/view", "Customers/view", "Entities/view"]}, "variants": {"view": {"title": "Time summary by client report view", "description": "View-only access to time summary by client report"}}}