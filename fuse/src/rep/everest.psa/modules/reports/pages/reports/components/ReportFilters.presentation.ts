import { EntityView } from '@pkg/everest.base/types/views/publicApi/view/EntityView/EntityView';
import { CustomerView } from '@pkg/everest.fin.accounting/types/views/publicApi/view/CustomerView/CustomerView';
import { DepartmentView } from '@pkg/everest.hr.base/types/views/publicApi/view/DepartmentView/DepartmentView';
import { EmployeeView } from '@pkg/everest.hr.base/types/views/publicApi/view/EmployeeView/EmployeeView';
import type { ReportFilters } from '@pkg/everest.psa/types/presentations/modules/reports/pages/reports/components/ReportFilters';
import { PsaProjectType } from '@pkg/everest.psa/types/PsaProjectType';
import { JoinedProjectView } from '@pkg/everest.psa/types/views/publicApi/view/Projects/JoinedProjectView';

export default {
  Projects: {
    query: {
      async execute({
        session,
        where,
        orderBy,
        skip,
        take,
      }: ReportFilters.EntitySets.Projects.Query.Execute.Request): Promise<ReportFilters.EntitySets.Projects.Query.Execute.Response> {
        const joinedProjectClient = await JoinedProjectView.client(session);
        const result = await joinedProjectClient.query(
          {
            where,
            orderBy,
            skip,
            take,
          },
          ['psaProjectId', 'projectName'] as const
        );
        const instances = result.map((item) => ({
          psaProjectId: item.psaProjectId,
          projectName: item.projectName,
        }));
        return { instances };
      },
    },
  },
  PsaProjectTypes: {
    query: {
      async execute({
        session,
        where,
        orderBy,
        skip,
        take,
      }: ReportFilters.EntitySets.PsaProjectTypes.Query.Execute.Request): Promise<ReportFilters.EntitySets.PsaProjectTypes.Query.Execute.Response> {
        const client = await PsaProjectType.client(session);
        const result = await client.query(
          {
            where: where || { active: true },
            orderBy: orderBy || [{ field: 'name', ordering: 'asc' }],
            skip,
            take,
          },
          ['id', 'name'] as const
        );
        const instances = result.map((item) => ({
          id: item.id,
          name: item.name,
        }));
        return { instances };
      },
    },
  },
  Employees: {
    query: {
      async execute({
        session,
        where,
        orderBy,
        skip,
        take,
      }: ReportFilters.EntitySets.Employees.Query.Execute.Request): Promise<ReportFilters.EntitySets.Employees.Query.Execute.Response> {
        const employeeClient = await EmployeeView.client(session);
        const result = await employeeClient.query(
          {
            where,
            orderBy: orderBy || [{ field: 'displayName', ordering: 'asc' }],
            skip,
            take,
          },
          ['id', 'displayName', 'email'] as const
        );
        const instances = result.map((item) => ({
          id: item.id,
          displayName: item.displayName,
          email: item.email,
        }));
        return { instances };
      },
    },
  },
  Departments: {
    query: {
      async execute({
        session,
        where,
        orderBy,
        skip,
        take,
      }: ReportFilters.EntitySets.Departments.Query.Execute.Request): Promise<ReportFilters.EntitySets.Departments.Query.Execute.Response> {
        const departmentClient = await DepartmentView.client(session);
        const result = await departmentClient.query(
          {
            where,
            orderBy: orderBy || [{ field: 'departmentName', ordering: 'asc' }],
            skip,
            take,
          },
          ['id', 'departmentName'] as const
        );
        const instances = result.map((item) => ({
          id: item.id,
          name: item.departmentName || '',
        }));
        return { instances };
      },
    },
  },
  Customers: {
    query: {
      async execute({
        session,
        where,
        orderBy,
        skip,
        take,
      }: ReportFilters.EntitySets.Customers.Query.Execute.Request): Promise<ReportFilters.EntitySets.Customers.Query.Execute.Response> {
        const customerClient = await CustomerView.client(session);
        const result = await customerClient.query(
          {
            where,
            orderBy: orderBy || [{ field: 'customerName', ordering: 'asc' }],
            skip,
            take,
          },
          ['id', 'customerName'] as const
        );
        const instances = result.map((item) => ({
          id: item.id,
          name: item.customerName || '',
        }));
        return { instances };
      },
    },
  },
  Entities: {
    query: {
      async execute({
        session,
        where,
        orderBy,
        skip,
        take,
      }: ReportFilters.EntitySets.Entities.Query.Execute.Request): Promise<ReportFilters.EntitySets.Entities.Query.Execute.Response> {
        const entityClient = await EntityView.client(session);
        const result = await entityClient.query(
          {
            where,
            orderBy: orderBy || [{ field: 'entityName', ordering: 'asc' }],
            skip,
            take,
          },
          ['id', 'entityName'] as const
        );
        const instances = result.map((item) => ({
          id: item.id,
          name: item.entityName || '',
        }));
        return { instances };
      },
    },
  },
} satisfies ReportFilters.Implementation;
