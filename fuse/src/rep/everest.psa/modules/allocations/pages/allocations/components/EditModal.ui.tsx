// @i18n:psa
import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Button,
  Spin,
  Radio,
  Tooltip,
  Alert,
} from 'antd';
import DatePicker from '@pkg/everest.base.ui/public/reactComponents/DatePicker.ui';
import { useTranslation } from 'react-i18next';
import type { FormInstance } from 'antd/es/form';
import { Allocation, Member, Project, ProjectActivity } from './types.ui';
import { EvstTimeAllocationMethod } from '@pkg/everest.timetracking/types/enums/TimeAllocationMethod';
import { EvstDayOfWeek } from '@pkg/everest.appserver/types/enums/DayOfWeek';

import { Icon } from '@everestsystems/design-system';
import { PlainDate } from '@everestsystems/datetime';
const { Option } = Select;
const { RangePicker } = DatePicker;

interface EditModalProps {
  isEditAllocationModalVisible: boolean;
  editingAllocation: Allocation | null;
  editFormRef: React.RefObject<FormInstance>;
  selectedProject: number | null;
  projects: Project[];
  loading: boolean;
  handleEditAllocationCancel: () => void;
  handleEditAllocationSubmit: () => void;
  handleDeleteAllocation: () => void;
  handleProjectChange: (projectId: number) => void;
  fetchProjectActivities: (
    projectId?: number,
    memberId?: number,
    projectPositionId?: number
  ) => Promise<ProjectActivity[]>;
  fetchMembers: (projectId?: number) => Promise<Member[]>;
  canModifyAllocation?: (allocation: Allocation) => boolean;
  currentEmployeeId?: number | null;
  projectPermissions?: Map<number, { hasEditPermission: boolean }>;
  validateAllocationDate?: (
    allocationStartDate: PlainDate,
    allocationEndDate: PlainDate,
    activityStartDate: PlainDate,
    activityEndDate: PlainDate
  ) => Promise<boolean>;
}

const EditModal: React.FC<EditModalProps> = ({
  isEditAllocationModalVisible,
  editingAllocation,
  editFormRef,
  selectedProject,
  projects,
  loading,
  handleEditAllocationCancel,
  handleEditAllocationSubmit,
  handleDeleteAllocation,
  handleProjectChange,
  fetchProjectActivities,
  fetchMembers,
  canModifyAllocation,
  currentEmployeeId,
  projectPermissions,
  validateAllocationDate,
}) => {
  const { t } = useTranslation();
  const [projectActivities, setProjectActivities] = useState<ProjectActivity[]>(
    []
  );
  const [projectMembers, setProjectMembers] = useState<Member[]>([]);
  const [loadingProjectData, setLoadingProjectData] = useState(false);
  const [selectedMemberId, setSelectedMemberId] = useState<number | null>(null);
  const [selectedMemberPositionId, setSelectedMemberPositionId] = useState<
    number | null
  >(null);
  const [selectedActivity, setSelectedActivity] =
    useState<ProjectActivity | null>(null);
  const [showDateWarning, setShowDateWarning] = useState<boolean>(
    editingAllocation?.dateWarning || false
  );

  // Helper function to check if user can edit the current allocation
  const canEditAllocation = () => {
    if (!editingAllocation || !canModifyAllocation) {
      return false;
    }
    return canModifyAllocation(editingAllocation);
  };

  // Filter members based on permissions
  const getFilteredMembers = () => {
    if (!currentEmployeeId || !projectPermissions || !selectedProject) {
      return projectMembers;
    }

    const permission = projectPermissions.get(selectedProject);
    if (!permission) {
      return [];
    }

    // If user has edit permission, they can see all members
    if (permission.hasEditPermission) {
      return projectMembers;
    }

    // If user has view permission, they can only see themselves
    return projectMembers.filter(
      (member) => member.employeeId === currentEmployeeId
    );
  };

  // Fetch activities and members when project changes
  useEffect(() => {
    if (selectedProject) {
      const loadProjectData = async () => {
        setLoadingProjectData(true);
        try {
          // Fetch members for this project
          const projectMembers = await fetchMembers(selectedProject);
          setProjectMembers(projectMembers);

          // If we have an editing allocation with a memberId, check if it's in the current project
          if (editingAllocation?.memberId) {
            const existingMember = projectMembers.find(
              (member) => member.id === editingAllocation.memberId
            );

            if (existingMember) {
              setSelectedMemberId(existingMember.id);
              setSelectedMemberPositionId(
                existingMember.projectPositionId || null
              );
            }
          }
        } catch (error) {
          console.error('Error loading project data:', error);
        } finally {
          setLoadingProjectData(false);
        }
      };

      loadProjectData();
    } else {
      setProjectMembers([]);
      setSelectedMemberId(null);
      setSelectedMemberPositionId(null);
    }
  }, [selectedProject, editingAllocation?.memberId, fetchMembers]);

  // Initialize date warning state when modal opens with an existing allocation
  useEffect(() => {
    if (isEditAllocationModalVisible && editingAllocation) {
      setShowDateWarning(editingAllocation.dateWarning || false);
    }
  }, [isEditAllocationModalVisible, editingAllocation]);

  // Refresh activities when member changes
  useEffect(() => {
    const loadActivities = async () => {
      if (selectedProject && selectedMemberId) {
        setLoadingProjectData(true);
        try {
          // Find the selected member to get their projectPositionId
          const selectedMember = projectMembers.find(
            (member) => member.id === selectedMemberId
          );

          if (selectedMember && selectedMember.projectPositionId) {
            // Fetch activities based on project, member, and position
            const activities = await fetchProjectActivities(
              selectedProject,
              selectedMember.id,
              selectedMember.projectPositionId
            );
            setProjectActivities(activities);

            // If we have an editing allocation with an activityId, find and set the selected activity
            if (editingAllocation?.projectActivityId) {
              const activity = activities.find(
                (a) => a.id === editingAllocation.projectActivityId
              );
              setSelectedActivity(activity || null);
            }
          } else {
            // If member has no position ID, fetch all activities for the project
            const activities = await fetchProjectActivities(selectedProject);
            setProjectActivities(activities);

            // If we have an editing allocation with an activityId, find and set the selected activity
            if (editingAllocation?.projectActivityId) {
              const activity = activities.find(
                (a) => a.id === editingAllocation.projectActivityId
              );
              setSelectedActivity(activity || null);
            }
          }
        } catch (error) {
          console.error('Error loading activities:', error);
        } finally {
          setLoadingProjectData(false);
        }
      } else if (selectedProject) {
        // If only project is selected but no member, clear activities
        setProjectActivities([]);
      }
    };

    loadActivities();
  }, [
    selectedProject,
    selectedMemberId,
    projectMembers,
    fetchProjectActivities,
  ]);

  // Handle activity selection change
  const handleProjectActivityChange = async (activityId: number) => {
    const form = editFormRef.current;
    if (!form) {
      return;
    }

    // Find the selected activity and update state
    const activity = projectActivities.find((a) => a.id === activityId);
    setSelectedActivity(activity || null);

    // Check if current date range is valid for the new activity
    if (form && activity) {
      const dateRange = form.getFieldValue('dateRange');
      if (dateRange && dateRange.length === 2) {
        // Handle Luxon DateTime format
        const startDate = PlainDate.from({
          year: dateRange[0].year,
          month: dateRange[0].month,
          day: dateRange[0].day,
        });
        const endDate = PlainDate.from({
          year: dateRange[1].year,
          month: dateRange[1].month,
          day: dateRange[1].day,
        });

        // Update the date warning state
        setShowDateWarning(
          await validateAllocationDate(
            startDate,
            endDate,
            activity.startDate,
            activity.endDate
          )
        );
      } else {
        setShowDateWarning(false);
      }
    } else {
      setShowDateWarning(false);
    }
  };

  // Handle member selection change
  const handleMemberChange = (memberId: number) => {
    setSelectedMemberId(memberId);

    // Find the selected member to get their projectPositionId and employeeId
    const selectedMember = projectMembers.find(
      (member) => member.id === memberId
    );

    if (selectedMember) {
      // Set memberEmployeeId and reset projectActivityId when member changes
      if (
        editFormRef.current &&
        typeof editFormRef.current.setFieldsValue === 'function'
      ) {
        editFormRef.current.setFieldsValue({
          projectActivityId: undefined,
          memberEmployeeId: selectedMember.employeeId,
        });
      }

      // Update selected member position ID
      if (selectedMember.projectPositionId) {
        setSelectedMemberPositionId(selectedMember.projectPositionId);
      } else {
        setSelectedMemberPositionId(null);
      }
    }
  };

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Icon type="edit" useMaterialIcon style={{ marginRight: 8 }} />{' '}
          {t('{{allocations.editTitle}}')}
        </div>
      }
      open={isEditAllocationModalVisible}
      onCancel={handleEditAllocationCancel}
      footer={[
        canEditAllocation() ? (
          <Button
            key="delete"
            danger
            onClick={handleDeleteAllocation}
            style={{
              float: 'left',
              borderRadius: '10px',
              padding: '0 16px',
              height: '34px',
              boxShadow: '0 2px 6px rgba(255, 0, 0, 0.1)',
            }}
          >
            <Icon type="delete" useMaterialIcon style={{ marginRight: 4 }} />{' '}
            {t('{{allocations.delete}}')}
          </Button>
        ) : (
          <Tooltip title={t('{{allocations.noPermissionToModify}}')}>
            <Button
              key="delete"
              danger
              disabled
              style={{
                float: 'left',
                borderRadius: '10px',
                padding: '0 16px',
                height: '34px',
              }}
            >
              <Icon type="delete" useMaterialIcon style={{ marginRight: 4 }} />{' '}
              {t('{{allocations.delete}}')}
            </Button>
          </Tooltip>
        ),
        <Button
          key="cancel"
          onClick={handleEditAllocationCancel}
          style={{
            borderRadius: '10px',
            padding: '0 16px',
            height: '34px',
          }}
        >
          {t('{{allocations.cancel}}')}
        </Button>,
        canEditAllocation() ? (
          <Button
            key="submit"
            type="primary"
            onClick={handleEditAllocationSubmit}
            loading={loading}
            style={{
              borderRadius: '10px',
              boxShadow: '0 2px 6px rgba(0, 0, 0, 0.1)',
              padding: '0 16px',
              height: '34px',
            }}
          >
            {t('{{allocations.save}}')}
          </Button>
        ) : (
          <Tooltip title={t('{{allocations.noPermissionToModify}}')}>
            <Button
              key="submit"
              type="primary"
              disabled
              style={{
                borderRadius: '10px',
                padding: '0 16px',
                height: '34px',
              }}
            >
              {t('{{allocations.save}}')}
            </Button>
          </Tooltip>
        ),
      ]}
      width={600}
    >
      <Spin
        spinning={loadingProjectData || loading}
        tip={t('{{allocations.loading}}')}
        style={{ width: '100%' }}
      >
        {showDateWarning && (
          <Alert
            message={t('{{projects.validationWarnings}}')}
            description={t('{{projects.allocationDateWarning}}')}
            type="warning"
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}
        <Form
          ref={editFormRef}
          layout="vertical"
          requiredMark={false}
          initialValues={{
            method: EvstTimeAllocationMethod.TotalHours,
            timeUnit: EvstTimeAllocationMethod.HoursPerDay,
          }}
          onValuesChange={(changedValues) => {
            const form = editFormRef.current;
            if (!form) {
              return;
            }

            // When method changes to HoursPerDay, ensure timeUnit is set to HoursPerDay
            if (changedValues.method === EvstTimeAllocationMethod.HoursPerDay) {
              form.setFieldsValue({
                timeUnit: EvstTimeAllocationMethod.HoursPerDay,
                dateRange: undefined,
              });
            } else if (
              changedValues.method === EvstTimeAllocationMethod.TotalHours
            ) {
              // When switching to TotalHours, reset dateRange
              form.setFieldsValue({ dateRange: undefined });
            }

            // When timeUnit changes, reset dateRange
            if (changedValues.timeUnit) {
              form.setFieldsValue({ dateRange: undefined });
            }
          }}
        >
          {/* Hidden form field for memberEmployeeId */}
          <Form.Item name="memberEmployeeId" hidden>
            <Input type="hidden" />
          </Form.Item>

          <Form.Item
            name="projectId"
            label={t('{{allocations.fieldProject}}')}
            rules={[
              {
                required: true,
                message: t('{{allocations.errorSelectProject}}'),
              },
            ]}
          >
            <Select
              placeholder={t('{{allocations.placeholderSelectProject}}')}
              onChange={handleProjectChange}
              showSearch
              optionFilterProp="children"
              filterOption={(input, option) =>
                option?.children
                  ?.toString()
                  .toLowerCase()
                  .indexOf(input.toLowerCase()) >= 0
              }
            >
              {projects.map((project) => (
                <Option key={project.id} value={project.id}>
                  {project.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="memberId"
            label={t('{{allocations.fieldTeamMemberAndPosition}}')}
            rules={[
              {
                required: true,
                message: t('{{allocations.errorSelectTeamMemberAndPosition}}'),
              },
            ]}
          >
            <Select
              placeholder={
                selectedProject
                  ? t('{{allocations.placeholderSelectTeamMemberAndPosition}}')
                  : t('{{allocations.placeholderSelectProjectFirst}}')
              }
              disabled={!selectedProject}
              loading={loadingProjectData}
              showSearch
              optionFilterProp="children"
              filterOption={(input, option) =>
                option?.children
                  ?.toString()
                  .toLowerCase()
                  .indexOf(input.toLowerCase()) >= 0
              }
              onChange={handleMemberChange}
              optionLabelProp="label"
            >
              {getFilteredMembers().map((member) => (
                <Option
                  key={member.id}
                  value={member.id}
                  label={`${member.name}${
                    member.position ? ` - ${member.position}` : ''
                  }`}
                >
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                    }}
                  >
                    <div
                      style={{
                        width: '18px',
                        height: '18px',
                        borderRadius: '50%',
                        backgroundColor: '#52c41a',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        fontSize: '12px',
                        color: 'white',
                        fontWeight: 'bold',
                      }}
                    >
                      {member.name.charAt(0).toUpperCase()}
                    </div>
                    <div>
                      <div>{member.name}</div>
                      <div style={{ fontSize: '12px', color: '#666' }}>
                        {member.position || 'No position assigned'}
                      </div>
                    </div>
                  </div>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="projectActivityId"
            label={t('{{allocations.fieldActivity}}')}
            rules={[
              {
                required: true,
                message: t('{{allocations.errorSelectActivity}}'),
              },
            ]}
          >
            <Select
              placeholder={t('{{allocations.placeholderSelectActivity}}')}
              loading={loadingProjectData}
              showSearch
              optionFilterProp="children"
              onChange={handleProjectActivityChange}
              filterOption={(input, option) =>
                option?.children
                  ?.toString()
                  .toLowerCase()
                  .indexOf(input.toLowerCase()) >= 0
              }
            >
              {projectActivities.map((activity) => (
                <Option key={activity.id} value={activity.id}>
                  {activity.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="method"
            label={t('{{allocations.fieldAllocationMethod}}')}
            rules={[
              {
                required: true,
                message: t('{{allocations.errorSelectMethod}}'),
              },
            ]}
          >
            <Select style={{ width: '100%' }}>
              <Option value={EvstTimeAllocationMethod.TotalHours}>
                {t('{{allocations.methodTotalHours}}')}
              </Option>
              <Option value={EvstTimeAllocationMethod.HoursPerDay}>
                {t('{{allocations.methodHoursPerPeriod}}')}
              </Option>
            </Select>
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.method !== currentValues.method ||
              prevValues.timeUnit !== currentValues.timeUnit
            }
          >
            {({ getFieldValue }) => {
              const method = getFieldValue('method');

              if (method === EvstTimeAllocationMethod.HoursPerDay) {
                return (
                  <>
                    <Form.Item
                      name="timeUnit"
                      className="radio-group-container"
                    >
                      <Radio.Group>
                        <Radio value={EvstTimeAllocationMethod.HoursPerDay}>
                          {t('{{allocations.periodDay}}')}
                        </Radio>
                        <Radio value={EvstTimeAllocationMethod.HoursPerWeek}>
                          {t('{{allocations.periodWeek}}')}
                        </Radio>
                        <Radio value={EvstTimeAllocationMethod.HoursPerMonth}>
                          {t('{{allocations.periodMonth}}')}
                        </Radio>
                      </Radio.Group>
                    </Form.Item>

                    <Form.Item
                      noStyle
                      shouldUpdate={(prevValues, currentValues) =>
                        prevValues.timeUnit !== currentValues.timeUnit
                      }
                    >
                      {({ getFieldValue }) => {
                        const timeUnit = getFieldValue('timeUnit');
                        let label = t('{{allocations.fieldHoursPerDay}}');
                        let message = t(
                          '{{allocations.errorEnterHoursPerDay}}'
                        );
                        let maxValue = 24;

                        if (
                          timeUnit === EvstTimeAllocationMethod.HoursPerWeek
                        ) {
                          label = t('{{allocations.fieldHoursPerWeek}}');
                          message = t('{{allocations.errorEnterHoursPerWeek}}');
                          maxValue = 168; // 24 * 7
                        } else if (
                          timeUnit === EvstTimeAllocationMethod.HoursPerMonth
                        ) {
                          label = t('{{allocations.fieldHoursPerMonth}}');
                          message = t(
                            '{{allocations.errorEnterHoursPerMonth}}'
                          );
                          maxValue = 744; // 24 * 31
                        }

                        return (
                          <Form.Item
                            name="convertedHours"
                            label={label}
                            rules={[{ required: true, message: message }]}
                          >
                            <InputNumber
                              min={1}
                              max={maxValue}
                              style={{ width: '100%' }}
                            />
                          </Form.Item>
                        );
                      }}
                    </Form.Item>

                    <Form.Item
                      name="workingDays"
                      label={t('{{allocations.fieldWorkingDays}}')}
                    >
                      <Select
                        mode="multiple"
                        placeholder={t(
                          '{{allocations.placeholderSelectWorkingDays}}'
                        )}
                        style={{ width: '100%' }}
                      >
                        <Option value={EvstDayOfWeek.Monday}>
                          {t('{{allocations.dayMonday}}')}
                        </Option>
                        <Option value={EvstDayOfWeek.Tuesday}>
                          {t('{{allocations.dayTuesday}}')}
                        </Option>
                        <Option value={EvstDayOfWeek.Wednesday}>
                          {t('{{allocations.dayWednesday}}')}
                        </Option>
                        <Option value={EvstDayOfWeek.Thursday}>
                          {t('{{allocations.dayThursday}}')}
                        </Option>
                        <Option value={EvstDayOfWeek.Friday}>
                          {t('{{allocations.dayFriday}}')}
                        </Option>
                      </Select>
                    </Form.Item>

                    {/* Date Range field with behavior based on time unit */}
                    <Form.Item
                      noStyle
                      shouldUpdate={(prevValues, currentValues) =>
                        prevValues.timeUnit !== currentValues.timeUnit
                      }
                    >
                      {({ getFieldValue }) => {
                        const timeUnit = getFieldValue('timeUnit');
                        return (
                          <Form.Item
                            name="dateRange"
                            label={t('{{allocations.fieldDateRange}}')}
                            rules={[
                              {
                                required: true,
                                message: t(
                                  '{{allocations.errorSelectDateRange}}'
                                ),
                              },
                            ]}
                          >
                            {timeUnit ===
                            EvstTimeAllocationMethod.HoursPerWeek ? (
                              <RangePicker
                                style={{ width: '100%' }}
                                format="YYYY-MM-DD"
                                picker="week"
                                showWeekNumber={false}
                                onChange={async (dates) => {
                                  if (dates && dates.length === 2) {
                                    // Get the first day of the week (Monday) and last day (Sunday)
                                    const startOfWeek =
                                      dates[0].startOf('week');
                                    const endOfWeek = dates[1].endOf('week');

                                    // Update the form with the correct start and end dates
                                    const form = editFormRef.current;
                                    if (form) {
                                      form.setFieldsValue({
                                        dateRange: [startOfWeek, endOfWeek],
                                      });
                                    }

                                    // Update date warning if we have a selected activity
                                    if (selectedActivity) {
                                      // Convert Luxon DateTime to PlainDate for checking date warnings
                                      const startDate = PlainDate.from({
                                        year: startOfWeek.year,
                                        month: startOfWeek.month,
                                        day: startOfWeek.day,
                                      });
                                      const endDate = PlainDate.from({
                                        year: endOfWeek.year,
                                        month: endOfWeek.month,
                                        day: endOfWeek.day,
                                      });

                                      // Update the date warning state
                                      setShowDateWarning(
                                        await validateAllocationDate(
                                          startDate,
                                          endDate,
                                          selectedActivity?.startDate,
                                          selectedActivity?.endDate
                                        )
                                      );
                                    }
                                  }
                                }}
                              />
                            ) : timeUnit ===
                              EvstTimeAllocationMethod.HoursPerMonth ? (
                              <RangePicker
                                style={{ width: '100%' }}
                                format="YYYY-MM-DD"
                                picker="month"
                                onChange={async (dates) => {
                                  if (dates && dates.length === 2) {
                                    // Get the first day of the month and last day of the month
                                    const startOfMonth =
                                      dates[0].startOf('month');
                                    const endOfMonth = dates[1].endOf('month');

                                    // Update the form with the correct start and end dates
                                    const form = editFormRef.current;
                                    if (form) {
                                      form.setFieldsValue({
                                        dateRange: [startOfMonth, endOfMonth],
                                      });
                                    }

                                    // Update date warning if we have a selected activity
                                    if (selectedActivity) {
                                      // Convert Luxon DateTime to PlainDate for checking date warnings
                                      const startDate = PlainDate.from({
                                        year: startOfMonth.year,
                                        month: startOfMonth.month,
                                        day: startOfMonth.day,
                                      });
                                      const endDate = PlainDate.from({
                                        year: endOfMonth.year,
                                        month: endOfMonth.month,
                                        day: endOfMonth.day,
                                      });

                                      // Update the date warning state
                                      setShowDateWarning(
                                        await validateAllocationDate(
                                          startDate,
                                          endDate,
                                          selectedActivity?.startDate,
                                          selectedActivity?.endDate
                                        )
                                      );
                                    }
                                  }
                                }}
                              />
                            ) : (
                              <RangePicker
                                style={{ width: '100%' }}
                                format="YYYY-MM-DD"
                                onChange={async (dates) => {
                                  if (
                                    selectedActivity &&
                                    dates &&
                                    dates[0] &&
                                    dates[1]
                                  ) {
                                    // Handle Luxon DateTime format
                                    const startDate = PlainDate.from({
                                      year: dates[0].year,
                                      month: dates[0].month,
                                      day: dates[0].day,
                                    });
                                    const endDate = PlainDate.from({
                                      year: dates[1].year,
                                      month: dates[1].month,
                                      day: dates[1].day,
                                    });

                                    // Update the date warning state
                                    setShowDateWarning(
                                      await validateAllocationDate(
                                        startDate,
                                        endDate,
                                        selectedActivity?.startDate,
                                        selectedActivity?.endDate
                                      )
                                    );
                                  }
                                }}
                              />
                            )}
                          </Form.Item>
                        );
                      }}
                    </Form.Item>
                  </>
                );
              }

              return (
                <>
                  <Form.Item
                    name="totalHours"
                    label={t('{{allocations.methodTotalHours}}')}
                    rules={[
                      {
                        required: true,
                        message: t('{{allocations.errorEnterTotalHours}}'),
                      },
                    ]}
                  >
                    <InputNumber min={1} max={1000} style={{ width: '100%' }} />
                  </Form.Item>

                  <Form.Item
                    name="workingDays"
                    label={t('{{allocations.fieldWorkingDays}}')}
                  >
                    <Select
                      mode="multiple"
                      placeholder={t(
                        '{{allocations.placeholderSelectWorkingDays}}'
                      )}
                      style={{ width: '100%' }}
                    >
                      <Option value={EvstDayOfWeek.Monday}>
                        {t('{{allocations.dayMonday}}')}
                      </Option>
                      <Option value={EvstDayOfWeek.Tuesday}>
                        {t('{{allocations.dayTuesday}}')}
                      </Option>
                      <Option value={EvstDayOfWeek.Wednesday}>
                        {t('{{allocations.dayWednesday}}')}
                      </Option>
                      <Option value={EvstDayOfWeek.Thursday}>
                        {t('{{allocations.dayThursday}}')}
                      </Option>
                      <Option value={EvstDayOfWeek.Friday}>
                        {t('{{allocations.dayFriday}}')}
                      </Option>
                    </Select>
                  </Form.Item>

                  <Form.Item
                    name="dateRange"
                    label={t('{{allocations.fieldDateRange}}')}
                    rules={[
                      {
                        required: true,
                        message: t('{{allocations.errorSelectDateRange}}'),
                      },
                    ]}
                  >
                    <RangePicker
                      style={{ width: '100%' }}
                      format="YYYY-MM-DD"
                      onChange={async (dates) => {
                        if (selectedActivity && dates && dates[0] && dates[1]) {
                          // Handle Luxon DateTime format
                          const startDate = PlainDate.from({
                            year: dates[0].year,
                            month: dates[0].month,
                            day: dates[0].day,
                          });
                          const endDate = PlainDate.from({
                            year: dates[1].year,
                            month: dates[1].month,
                            day: dates[1].day,
                          });

                          // Update the date warning state
                          setShowDateWarning(
                            await validateAllocationDate(
                              startDate,
                              endDate,
                              selectedActivity?.startDate,
                              selectedActivity?.endDate
                            )
                          );
                        }
                      }}
                    />
                  </Form.Item>
                </>
              );
            }}
          </Form.Item>

          <Form.Item name="notes" label={t('{{allocations.fieldNotes}}')}>
            <Input.TextArea
              rows={4}
              placeholder={t('{{allocations.placeholderNotes}}')}
            />
          </Form.Item>
        </Form>
      </Spin>
    </Modal>
  );
};

export default EditModal;
