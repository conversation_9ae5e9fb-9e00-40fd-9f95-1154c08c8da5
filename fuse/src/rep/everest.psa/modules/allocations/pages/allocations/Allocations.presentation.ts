import type { ISession } from '@everestsystems/content-core';
import { getTranslation, log } from '@everestsystems/content-core';
import { PlainDate } from '@everestsystems/datetime';
import { Decimal } from '@everestsystems/decimal';
import type { EvstDayOfWeek } from '@pkg/everest.appserver/types/enums/DayOfWeek';
import { EmployeeApi } from '@pkg/everest.hr.base/types/presentations/publicApi/api/EmployeeApi/EmployeeApi';
import { getEmployeeAbsences } from '@pkg/everest.psa/lib/kpi/time/kpi';
import PsaPermissionApi, {
  PsaPermission,
} from '@pkg/everest.psa/lib/permissions/PsaPermissionApi';
import AllocationApi from '@pkg/everest.psa/lib/projectApi/AllocationApi';
import { getPsaProjectIdFromTimetracking } from '@pkg/everest.psa/lib/projectMapping';
import { checkAllocationsOutsideDateRange } from '@pkg/everest.psa/lib/validation/project/validateAllocations';
import { validatePsaProjectBudget } from '@pkg/everest.psa/lib/validation/project/validateBudget';
import { mapQuery } from '@pkg/everest.psa/modules/util/queryViews';
import type { EvstPsaProjectStatus } from '@pkg/everest.psa/types/enums/PsaProjectStatus';
import type { Allocations } from '@pkg/everest.psa/types/presentations/modules/allocations/pages/allocations/Allocations';
import { JoinedAllocations } from '@pkg/everest.psa/types/views/publicApi/view/Allocations/JoinedAllocations';
import { JoinedProjectView } from '@pkg/everest.psa/types/views/publicApi/view/Projects/JoinedProjectView';
import type { EvstTimeAllocationMethod } from '@pkg/everest.timetracking/types/enums/TimeAllocationMethod';
import { uniq } from 'lodash';

/**
 * Helper function to get PSA project IDs from timetracking project IDs in batch
 */
async function getPsaProjectIds(
  session: ISession,
  projectIds: number[]
): Promise<{ projectId: number; psaProjectId: number }[]> {
  // Use getPsaProjectIdFromTimetracking to get PSA project IDs in parallel
  const psaProjectPromises = projectIds.map((projectId) =>
    getPsaProjectIdFromTimetracking(session, projectId).then(
      (psaProjectId) => ({ projectId, psaProjectId })
    )
  );

  return Promise.all(psaProjectPromises);
}

/**
 * Helper function to get allocation details (PSA project ID and member ID) from allocation IDs in batch
 */
async function getAllocationDetails(
  session: ISession,
  allocationIds: number[]
): Promise<{ psaProjectId: number; memberId: number; employeeId: number }[]> {
  const allocationsClient = await JoinedAllocations.client(session);
  const allocations = await allocationsClient.query(
    { where: { allocationId: { $in: allocationIds } } },
    ['allocationId', 'psaProjectId', 'memberId', 'employeeId']
  );

  return allocations.map((allocation) => ({
    psaProjectId: Number(allocation.psaProjectId),
    memberId: Number(allocation.memberId),
    employeeId: Number(allocation.employeeId),
  }));
}

/**
 * Helper function to check if user can modify allocations for members in batch
 * Throws an error if any allocation cannot be modified
 */
async function canModifyAllocation(
  session: ISession,
  members: {
    psaProjectId: number;
    memberId: number;
    employeeId: number;
  }[]
): Promise<void> {
  // Get current employee ID
  const { employeeId } = await EmployeeApi.getEmployeeId.execute(session, {});

  const psaProjectIds = uniq(members.map((a) => a.psaProjectId));

  // Check permissions for all projects in parallel
  const projectPermissions = await Promise.all(
    psaProjectIds.map((projectId) =>
      PsaPermissionApi.getProjectPermissions(session, projectId)
    )
  );

  const projectPermissionsMap = new Map<number, PsaPermission>(
    psaProjectIds.map((projectId, index) => [
      projectId,
      projectPermissions[index],
    ])
  );

  // Check permissions for each allocation
  for (const member of members) {
    const permission = projectPermissionsMap.get(member.psaProjectId);

    // If user has edit permission, they can modify allocations for anyone
    if (permission >= PsaPermission.Edit) {
      continue;
    }

    // If user has view permission, they can only modify their own allocations
    if (permission >= PsaPermission.View && member.employeeId === employeeId) {
      continue;
    }

    // No permission - throw error
    throw new Error(
      await getTranslation(
        session,
        'allocations.noPermissionToModify',
        'everest.psa/uinext/i18n/psa'
      )
    );
  }
}

export default {
  Allocations: {
    query: {
      async execute({
        session,
        where,
        orderBy,
        skip,
        take,
        fieldList,
        count,
      }: Allocations.EntitySets.Allocations.Query.Execute.Request): Promise<Allocations.EntitySets.Allocations.Query.Execute.Response> {
        try {
          // Create a mapping between the OData field names and the view field names
          const fieldMapping: Record<string, string> = {
            id: 'allocationId',
            memberId: 'memberId',
            projectActivityId: 'projectActivityId',
            startDate: 'startDate',
            endDate: 'endDate',
            totalHours: 'totalHours',
            notes: 'notes',
            method: 'method',
            workingDays: 'workingDays',
            projectId: 'projectId',
            projectName: 'projectName',
            memberName: 'memberName',
            memberEmployeeId: 'employeeId',
            memberPosition: 'projectPositionName',
            memberActivity: 'projectActivityName',
            memberActivityDescription: 'projectActivityDescription',
            memberActivityStartDate: 'projectActivityStartDate',
            memberActivityEndDate: 'projectActivityEndDate',
            memberTotalCapacity: 'memberCapacity',
          };

          // Map the query parameters using the mapQuery function
          const {
            fieldList: viewFields,
            orderBy: viewOrderBy,
            where: viewWhere,
          } = mapQuery(fieldList, orderBy, where, fieldMapping);

          // Use the Allocations view to get allocation data
          const allocationsClient = await JoinedAllocations.client(session);
          const allocationsData: JoinedAllocations.JoinedAllocations[] =
            (await allocationsClient.query(
              {
                where: viewWhere,
                orderBy: viewOrderBy,
                skip,
                take,
              },
              viewFields
            )) as JoinedAllocations.JoinedAllocations[];

          // Transform view data to match the expected OData format
          const instances = allocationsData.map((allocation) => ({
            id: Number(allocation.allocationId),
            memberId: Number(allocation.memberId),
            projectActivityId: Number(allocation.projectActivityId),
            startDate: allocation.startDate,
            endDate: allocation.endDate,
            totalHours: allocation.totalHours ?? new Decimal(0),
            notes: allocation.notes?.toString() || '',
            method: allocation.method as EvstTimeAllocationMethod,
            workingDays: allocation?.workingDays as EvstDayOfWeek[],
            projectId: Number(allocation.projectId),
            projectName: allocation.projectName?.toString() || '',
            memberName: allocation.memberName?.toString() || '',
            memberEmployeeId: Number(allocation.employeeId),
            memberPosition: allocation.projectPositionName?.toString() || '',
            memberActivity: allocation.projectActivityName?.toString() || '',
            memberActivityDescription:
              allocation.projectActivityDescription?.toString() || '',
            memberActivityStartDate: allocation.projectActivityStartDate,
            memberActivityEndDate: allocation.projectActivityEndDate,
            memberTotalCapacity: allocation.memberCapacity
              ? Number(allocation.memberCapacity)
              : 0,
            // Check if allocation dates are outside the task date range
            dateWarning:
              allocation.startDate &&
              allocation.endDate &&
              allocation.projectActivityStartDate &&
              allocation.projectActivityEndDate
                ? checkAllocationsOutsideDateRange(
                    [allocation.startDate],
                    [allocation.endDate],
                    allocation.projectActivityStartDate,
                    allocation.projectActivityEndDate
                  )
                : false,
          }));

          const response: Allocations.EntitySets.Allocations.Query.Execute.Response =
            {
              instances,
            };

          if (count) {
            response.count = instances.length;
          }

          return response;
        } catch (error) {
          log.error('Failed to query allocations', error);
          throw error;
        }
      },
    },

    create: {
      async execute({
        session,
        inputs,
      }: Allocations.EntitySets.Allocations.Create.Execute.Request): Promise<Allocations.EntitySets.Allocations.Create.Execute.Response> {
        try {
          log.info('Allocations.create.execute called');

          // Get PSA project IDs for all inputs in batch
          const projectIds = inputs.map((input) => input.projectId);
          const psaProjectMappings = await getPsaProjectIds(
            session,
            projectIds
          );

          // Prepare allocation data for permission check
          const allocationData = inputs.map((input, index) => {
            return {
              psaProjectId: psaProjectMappings[index].psaProjectId,
              memberId: input.memberId,
              employeeId: input.memberEmployeeId,
            };
          });

          // Check permissions for all allocations in batch
          await canModifyAllocation(session, allocationData);

          // Prepare allocations for batch creation
          const allocationsToCreate = inputs.map((input) => ({
            memberId: input.memberId,
            projectActivityId: input.projectActivityId,
            startDate: input.startDate,
            endDate: input.endDate,
            totalHours: input.totalHours,
            notes: input.notes,
            method: input.method,
            projectId: input.projectId,
            workingDays: input.workingDays,
          }));

          // Call the real API to create allocations
          const result = await AllocationApi.createAllocations(session, {
            allocations: allocationsToCreate,
          });

          // Merge the allocation IDs with the input data to construct the response
          const createdInstances: Allocations.EntitySets.Allocations.Create.Instance[] =
            result.allocationIds.map((id, index) => {
              const input = inputs[index];
              return {
                id: id,
                memberId: input.memberId,
                projectActivityId: input.projectActivityId,
                startDate: input.startDate,
                endDate: input.endDate,
                totalHours: input.totalHours,
                notes: input.notes || '',
                method: input.method,
                projectId: input.projectId,
                workingDays: input.workingDays || [],
              };
            });

          return {
            instances: createdInstances,
          };
        } catch (error) {
          log.error('Failed to create allocations', error);
          throw error;
        }
      },
    },

    update: {
      async execute({
        session,
        id,
        data,
      }: Allocations.EntitySets.Allocations.Update.Execute.Request): Promise<Allocations.EntitySets.Allocations.Update.Execute.Response> {
        try {
          log.info(`Allocations.update.execute called for id ${id}`);

          // Check if the user has permissions to modify current allocation
          const allocationDetails = await getAllocationDetails(session, [id]);
          await canModifyAllocation(session, allocationDetails);

          // Check if the user has permissions to modify updated allocation (need to check permissions if the project/member changed)
          if (data.projectId && data.memberId && data.memberEmployeeId) {
            const psaProjectIds = await getPsaProjectIds(session, [
              data.projectId,
            ]);
            const updatedAllocationDetails = {
              psaProjectId: psaProjectIds[0].psaProjectId,
              memberId: data.memberId,
              employeeId: data.memberEmployeeId,
            };
            await canModifyAllocation(session, [updatedAllocationDetails]);
          }

          // Prepare allocation for update - exclude projectId and memberEmployeeId in case they are present
          const {
            projectId: _projectId,
            memberEmployeeId: _memberEmployeeId,
            ...updateData
          } = data;

          const allocationUpdate = {
            id,
            ...updateData,
          };

          await AllocationApi.updateAllocations(session, {
            allocations: [allocationUpdate],
          });

          return;
        } catch (error) {
          log.error(`Failed to update allocation with ID ${id}`, error);
          throw error;
        }
      },
    },

    delete: {
      async execute({
        session,
        ids,
      }: Allocations.EntitySets.Allocations.Delete.Execute.Request): Promise<Allocations.EntitySets.Allocations.Delete.Execute.Response> {
        try {
          log.info(
            `Allocations.delete.execute called for ids ${ids.join(', ')}`
          );

          // Check permissions for all allocations in batch
          const allocationDetails = await getAllocationDetails(session, ids);
          await canModifyAllocation(session, allocationDetails);

          await AllocationApi.deleteAllocations(session, {
            allocationIds: ids,
          });

          return;
        } catch (error) {
          log.error(
            `Failed to delete allocations with IDs ${ids.join(', ')}`,
            error
          );
          throw error;
        }
      },
    },
  },

  Members: {
    query: {
      async execute({
        session,
        where,
        orderBy,
        skip,
        take,
        fieldList,
        count,
      }: Allocations.EntitySets.Members.Query.Execute.Request): Promise<Allocations.EntitySets.Members.Query.Execute.Response> {
        try {
          // Create a mapping between the OData field names and the view field names
          const fieldMapping: Record<string, string> = {
            id: 'memberId',
            employeeId: 'memberEmployeeId',
            projectPositionId: 'positionId',
            name: 'memberName',
            totalCapacity: 'memberCapacity',
            position: 'positionName',
            projectId: 'projectId',
            projectName: 'projectName',
          };

          // Map the query parameters using the mapQuery function
          const {
            fieldList: viewFields,
            orderBy: viewOrderBy,
            where: viewWhere,
          } = mapQuery(fieldList, orderBy, where, fieldMapping);

          const filteredViewWhere = {
            $and: [
              viewWhere,
              {
                positionId: {
                  $ne: null,
                },
              },
            ],
          };

          // Use the Projects view with 'members' select option
          const projectsClient = await JoinedProjectView.client(session);
          const projectsData = await projectsClient.query(
            {
              where: {
                ...filteredViewWhere,
                select: 'membersAndPositions',
              },
              orderBy: viewOrderBy,
              skip,
              take,
            },
            viewFields
          );

          // Transform view data to match the expected OData format
          const instances = projectsData.map((item) => ({
            id: Number(item.memberId),
            employeeId: Number(item.memberEmployeeId),
            projectPositionId: Number(item.positionId),
            name: item.memberName?.toString() || '',
            totalCapacity: Number(item.memberCapacity) || 0,
            position: item.positionName?.toString() || '',
            projectId: Number(item.projectId),
            projectName: item.projectName?.toString() || '',
          }));

          const response: Allocations.EntitySets.Members.Query.Execute.Response =
            {
              instances,
            };

          if (count) {
            response.count = instances.length;
          }

          return response;
        } catch (error) {
          log.error('Failed to query members', error);
          throw error;
        }
      },
    },
  },

  ProjectPositions: {
    query: {
      async execute({
        session,
        where,
        orderBy,
        skip,
        take,
        fieldList,
        count,
      }: Allocations.EntitySets.ProjectPositions.Query.Execute.Request): Promise<Allocations.EntitySets.ProjectPositions.Query.Execute.Response> {
        try {
          // Create a mapping between the OData field names and the view field names
          const fieldMapping: Record<string, string> = {
            id: 'positionId',
            name: 'positionName',
            description: 'positionDescription',
            projectId: 'projectId',
          };

          // Map the query parameters using the mapQuery function
          const {
            fieldList: viewFields,
            orderBy: viewOrderBy,
            where: viewWhere,
          } = mapQuery(fieldList, orderBy, where, fieldMapping);

          // Use the Projects view with 'positions' select option
          const projectsClient = await JoinedProjectView.client(session);
          const projectsData = await projectsClient.query(
            {
              where: {
                ...viewWhere,
                select: 'positions',
              },
              orderBy: viewOrderBy,
              skip,
              take,
            },
            viewFields
          );

          // Transform view data to match the expected OData format
          const instances = projectsData.map((position) => ({
            id: Number(position.positionId),
            name: position.positionName?.toString() || '',
            description: position.positionDescription?.toString() || '',
            projectId: Number(position.projectId),
          }));

          const response: Allocations.EntitySets.ProjectPositions.Query.Execute.Response =
            {
              instances,
            };

          if (count) {
            response.count = instances.length;
          }

          return response;
        } catch (error) {
          log.error('Failed to query project positions', error);
          throw error;
        }
      },
    },
  },

  Projects: {
    query: {
      async execute({
        session,
        where,
        orderBy,
        skip,
        take,
        fieldList,
        count,
      }: Allocations.EntitySets.Projects.Query.Execute.Request): Promise<Allocations.EntitySets.Projects.Query.Execute.Response> {
        try {
          // Use the Projects view without a select option to get basic project details
          const projectsClient = await JoinedProjectView.client(session);
          const projectsData = await projectsClient.query(
            {
              where,
              orderBy,
              skip,
              take,
            },
            [
              'psaProjectId',
              'projectId',
              'projectName',
              'startDate',
              'endDate',
              'status',
            ]
          );

          // Transform view data to match the expected OData format
          const instances = projectsData.map((project) => ({
            id: Number(project.projectId),
            name: project.projectName?.toString() || '',
            psaProjectId: Number(project.psaProjectId),
            status: project.status as EvstPsaProjectStatus,
          }));

          const response: Allocations.EntitySets.Projects.Query.Execute.Response =
            {
              instances,
            };

          if (count) {
            response.count = instances.length;
          }

          return response;
        } catch (error) {
          log.error('Failed to query projects', error);
          throw error;
        }
      },
    },
  },

  ProjectActivities: {
    query: {
      async execute({
        session,
        where,
        orderBy,
        skip,
        take,
        fieldList,
        count,
      }: Allocations.EntitySets.ProjectActivities.Query.Execute.Request): Promise<Allocations.EntitySets.ProjectActivities.Query.Execute.Response> {
        try {
          // Create a mapping between the OData field names and the view field names
          const fieldMapping: Record<string, string> = {
            id: 'activityId',
            name: 'activityName',
            description: 'activityDescription',
            projectId: 'projectId',
            projectPositionId: 'positionId',
            memberId: 'memberId',
            startDate: 'activityStartDate',
            endDate: 'activityEndDate',
          };

          // Map the query parameters using the mapQuery function
          const {
            fieldList: viewFields,
            orderBy: viewOrderBy,
            where: viewWhere,
          } = mapQuery(fieldList, orderBy, where, fieldMapping);

          // Use the Projects view with 'activities' select option
          const projectsClient = await JoinedProjectView.client(session);
          const projectsData = await projectsClient.query(
            {
              where: {
                ...viewWhere,
                select: 'activities',
              },
              orderBy: viewOrderBy,
              skip,
              take,
            },
            viewFields
          );

          // Transform view data to match the expected OData format
          const instances = projectsData.map((activity) => ({
            id: Number(activity.activityId),
            name: activity.activityName?.toString() || '',
            description: activity.activityDescription?.toString() || '',
            projectId: Number(activity.projectId),
            projectPositionId: Number(activity.positionId),
            memberId: Number(activity.memberId),
            startDate: activity.activityStartDate as PlainDate,
            endDate: activity.activityEndDate as PlainDate,
          }));

          const response: Allocations.EntitySets.ProjectActivities.Query.Execute.Response =
            {
              instances,
            };

          if (count) {
            response.count = instances.length;
          }

          return response;
        } catch (error) {
          log.error('Failed to query project activities', error);
          throw error;
        }
      },
    },
  },

  validateBudget: {
    async execute(
      request: Allocations.Actions.validateBudget.Execute.Request
    ): Promise<Allocations.Actions.validateBudget.Execute.Response> {
      const { session, input } = request;
      log.info('validateBudget.execute called');

      try {
        // Run budget validation with the simplified signature
        const warnings = await validatePsaProjectBudget(
          session,
          input.projectId
        );

        return {
          output: {
            warnings: warnings.map((warning) => ({
              message: warning.message,
              projectName: warning.projectName || '',
            })),
          },
        };
      } catch (error) {
        log.error('Error in validateBudget:', error);
        throw error;
      }
    },
  },

  GetCurrentEmployeeId: {
    async execute({
      session,
    }: Allocations.Actions.GetCurrentEmployeeId.Execute.Request): Promise<Allocations.Actions.GetCurrentEmployeeId.Execute.Response> {
      try {
        // Get the current employee ID
        const { employeeId } = await EmployeeApi.getEmployeeId.execute(
          session,
          {}
        );

        return {
          output: {
            employeeId,
          },
        };
      } catch (error) {
        log.error('Error in GetCurrentEmployeeId:', error);
        throw error;
      }
    },
  },

  GetTimeOff: {
    async execute({
      session,
      input,
    }: Allocations.Actions.GetTimeOff.Execute.Request): Promise<Allocations.Actions.GetTimeOff.Execute.Response> {
      const { startDate, endDate, employeeIds } = input;
      log.debug('GetTimeOff.execute called', {
        startDate,
        endDate,
        employeeIds,
      });

      try {
        const timeOff = await Promise.all(
          employeeIds.map((employeeId) =>
            getEmployeeAbsences(session, employeeId, startDate, endDate)
          )
        );

        return {
          output: {
            result: timeOff.map((timeOff, index) => ({
              employeeId: employeeIds[index],
              holidays: timeOff.holidays,
              absences: timeOff.absences,
            })),
          },
        };
      } catch (error) {
        log.error('Error in GetTimeOff:', error);
        return {
          output: {
            result: [],
          },
        };
      }
    },
  },

  validateAllocationDate: {
    async execute({
      input,
    }: Allocations.Actions.validateAllocationDate.Execute.Request): Promise<Allocations.Actions.validateAllocationDate.Execute.Response> {
      try {
        const {
          allocationStartDate,
          allocationEndDate,
          activityStartDate,
          activityEndDate,
        } = input;

        // Validate that we have all required dates
        if (!activityStartDate || !activityEndDate) {
          return { output: { isOutsideDateRange: false } };
        }

        // If there are no allocation dates, return false
        if (!allocationStartDate || !allocationEndDate) {
          return { output: { isOutsideDateRange: false } };
        }

        // Use the shared validation function to check if allocations are outside the activity date range
        const isOutsideDateRange = checkAllocationsOutsideDateRange(
          [allocationStartDate],
          [allocationEndDate],
          activityStartDate,
          activityEndDate
        );

        return { output: { isOutsideDateRange } };
      } catch (error) {
        log.error('Failed to validate allocation dates', error);
        throw error;
      }
    },
  },
} satisfies Allocations.Implementation;
