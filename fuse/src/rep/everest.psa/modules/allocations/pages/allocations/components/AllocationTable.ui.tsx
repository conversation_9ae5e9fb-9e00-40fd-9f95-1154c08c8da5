// @i18n:psa
import React from 'react';
import { Table, Button, Tooltip, Radio } from 'antd';
import { useTranslation } from 'react-i18next';
import {
  Allocation,
  Member,
  WeekInfo,
  MonthInfo,
  TimeUnit,
  ViewType,
  Metric,
  Project,
} from './types.ui';
import { EvstTimeAllocationMethod } from '@pkg/everest.timetracking/types/enums/TimeAllocationMethod';
import { EvstDayOfWeek } from '@pkg/everest.appserver/types/enums/DayOfWeek';
import { convertTotalHoursToConvertedHours } from '@pkg/everest.timetracking/public/utils/allocationUtils';
import { getMemberAllocations } from './utils.ui';
import {
  formatDateToYYYYMMDD,
  plainDateToDate,
} from '@pkg/everest.psa/modules/util/date.ui';

import { Icon } from '@everestsystems/design-system';
import { useColumnGroups } from '../hooks/useColumnGroups.ui';
import { calculateContinuousAllocationBarDimensions } from '../helpers/continuousTimelineCalculator.ui';
import { useContinuousAllocationBarResize } from '../hooks/useContinuousAllocationBarResize.ui';

interface AllocationTableProps {
  members: Member[];
  allocations: Allocation[];
  projects: Project[];
  expandedRowKeys: number[];
  weeks: WeekInfo[];
  months: MonthInfo[];
  timeUnit: TimeUnit;
  viewType: ViewType;
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  loading: boolean;
  metric: Metric;
  showTimeOff: boolean;
  toggleExpandRow: (memberId: number) => void;
  handleEditAllocation: (allocation: Allocation) => void;
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  handleAllocationUpdateResize: (params: {
    barId: string;
    newStartDate: Date;
    newEndDate: Date;
  }) => Promise<void>;
  handleViewTypeChange: (viewType: ViewType) => void;
  canModifyAllocation?: (allocation: Allocation) => boolean;
}

const AllocationTable: React.FC<AllocationTableProps> = ({
  members,
  allocations,
  projects,
  expandedRowKeys,
  weeks,
  months,
  timeUnit,
  viewType,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  loading,
  metric,
  showTimeOff = false,
  toggleExpandRow,
  handleEditAllocation,
  handleAllocationUpdateResize,
  handleViewTypeChange,
  canModifyAllocation,
}) => {
  const { t } = useTranslation();

  // Constants for layout and padding
  const CELL_HORIZONTAL_PADDING = '4px';
  const CELL_VERTICAL_PADDING = '8px';

  // Define column widths as percentages for responsiveness
  const EXPAND_ICON_WIDTH = '3%';
  const NAME_COLUMN_WIDTH = '15%';

  // Calculate timeline column width based on number of columns to ensure they all fit
  const timelineItems = timeUnit === 'week' ? weeks : months;

  // Initialize the continuous resize hook
  const {
    getAllocationBarProps,
    getResizeHandleProps,
    tooltipState,
    isResizing,
  } = useContinuousAllocationBarResize({
    timelineItems,
    timeUnit,
    onAllocationUpdate: handleAllocationUpdateResize,
  });
  const TIMELINE_COLUMN_WIDTH =
    timeUnit === 'week'
      ? `${82 / Math.max(timelineItems.length, 1)}%`
      : `${82 / Math.max(timelineItems.length, 1)}%`;

  // Get column groups using the hook
  const columnGroups = useColumnGroups({
    timeUnit,
    metric,
    weeks,
    months,
    members,
    allocations,
    timelineColumnWidth: TIMELINE_COLUMN_WIDTH,
    viewType,
  });

  const columns = [
    // Expand icon column
    {
      title: '',
      dataIndex: 'expandIcon',
      key: 'expandIcon',
      width: EXPAND_ICON_WIDTH,
      render: (_: any, record: any) => {
        if (record.isTotal) {
          return null;
        }

        return (
          <Button
            type="text"
            icon={
              <Icon
                type={
                  expandedRowKeys.includes(record.id)
                    ? 'expand_less'
                    : 'expand_more'
                }
                useMaterialIcon
              />
            }
            onClick={(e) => {
              e.stopPropagation();
              toggleExpandRow(record.id);
            }}
            style={{ padding: 0 }}
          />
        );
      },
    },
    // Members column with view selector
    {
      title: (
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <Radio.Group
            value={viewType}
            onChange={(e) => handleViewTypeChange(e.target.value)}
            buttonStyle="solid"
            size="small"
            style={{
              borderRadius: '8px',
              overflow: 'hidden',
            }}
          >
            <Radio.Button
              value="members"
              style={{
                borderRadius: '8px 0 0 8px',
                padding: '0 8px',
                fontSize: '10px',
              }}
            >
              {t('{{allocations.columnMembers}}')}
            </Radio.Button>
            <Radio.Button
              value="projects"
              style={{
                borderRadius: '0 8px 8px 0',
                padding: '0 8px',
                fontSize: '10px',
              }}
            >
              {t('{{allocations.projects}}')}
            </Radio.Button>
          </Radio.Group>
        </div>
      ),
      dataIndex: 'name',
      key: 'name',
      width: NAME_COLUMN_WIDTH,
      render: (text: string, record: any) => {
        if (record.isTotal) {
          return (
            <div style={{ fontWeight: 'bold', textTransform: 'uppercase' }}>
              {t('{{allocations.total}}')}
            </div>
          );
        }
        return <div style={{ fontWeight: 500 }}>{text}</div>;
      },
    },
    ...columnGroups,
  ];

  // Function to render timeoff (holidays and absences) as allocation bars
  const renderTimeOffBar = (
    timeOffItem: any,
    index: number,
    isHoliday: boolean
  ) => {
    try {
      // Create a pseudo-allocation object from timeoff data
      let startDate, endDate;

      if (isHoliday) {
        // For holidays, both start and end date are the holidayDate
        startDate = timeOffItem.holidayDate;
        endDate = timeOffItem.holidayDate;
      } else {
        // For absences, use the startDate and endDate properties
        startDate = timeOffItem.startDate;
        endDate = timeOffItem.endDate;
      }

      const timeOffAllocation = {
        startDate,
        endDate,
        id: `timeoff-${index}`,
      } as unknown as Allocation;

      // Calculate allocation bar dimensions using the continuous helper
      const barDimensions = calculateContinuousAllocationBarDimensions(
        timeOffAllocation,
        timelineItems,
        timeUnit
      );

      // If the timeoff is not visible, don't render it
      if (!barDimensions.isVisible) {
        return null;
      }

      const { leftOffsetPercentage, widthPercentage } = barDimensions;

      // Create unique bar ID
      const barId = `timeoff-${isHoliday ? 'holiday' : 'absence'}-${index}`;

      // Determine the label to display
      const label = isHoliday
        ? timeOffItem.holidayName || t('{{allocations.holiday}}')
        : timeOffItem.absenceType || t('{{allocations.absence}}');

      // Determine the background and border color based on type
      const backgroundColor = isHoliday ? '#fff1f0' : '#f6ffed';
      const borderColor = isHoliday ? '#ff4d4f' : '#52c41a';
      const textColor = isHoliday ? '#cf1322' : '#389e0d';

      return (
        <div
          key={barId}
          className="allocation-row continuous-timeline-container"
          style={{
            display: 'grid',
            gridTemplateColumns: `${EXPAND_ICON_WIDTH} ${NAME_COLUMN_WIDTH} 82%`,
            width: '100%',
            minHeight: '40px',
          }}
        >
          {/* Empty cell for expand icon column */}
          <div
            style={{
              padding: `${CELL_VERTICAL_PADDING} ${CELL_HORIZONTAL_PADDING}`,
            }}
          ></div>

          {/* Empty cell for name column */}
          <div
            style={{
              padding: `${CELL_VERTICAL_PADDING} ${CELL_HORIZONTAL_PADDING}`,
            }}
          ></div>

          {/* Continuous timeline container wrapper with padding */}
          <div
            style={{
              padding: `${CELL_VERTICAL_PADDING} ${CELL_HORIZONTAL_PADDING}`,
              paddingLeft: '3%',
            }}
          >
            <div
              className="timeline-container"
              style={{
                position: 'relative',
                width: '100%',
                height: '32px',
                borderRadius: '4px',
                backgroundColor: '#fafafa',
              }}
            >
              <Tooltip
                title={
                  <div style={{ whiteSpace: 'pre-line' }}>
                    <div>
                      <strong>
                        {isHoliday
                          ? t('{{allocations.holiday}}')
                          : t('{{allocations.absence}}')}
                      </strong>{' '}
                      {label}
                    </div>
                    {isHoliday && timeOffItem.halfDay && (
                      <div>
                        <strong>{t('{{allocations.halfDay}}')}</strong>
                      </div>
                    )}
                    {!isHoliday && timeOffItem.days && (
                      <div>
                        <strong>{t('{{allocations.days}}')}</strong>{' '}
                        {timeOffItem.days.toString()}
                      </div>
                    )}
                    <div>
                      <strong>{t('{{allocations.tooltipStartDate}}')}</strong>{' '}
                      {formatDateToYYYYMMDD(startDate)}
                    </div>
                    <div>
                      <strong>{t('{{allocations.tooltipEndDate}}')}</strong>{' '}
                      {formatDateToYYYYMMDD(endDate)}
                    </div>
                  </div>
                }
                mouseEnterDelay={0.1}
                overlayStyle={{ maxWidth: '300px' }}
              >
                <div
                  style={{
                    backgroundColor: backgroundColor,
                    borderRadius: '4px',
                    width: `${widthPercentage}%`,
                    height: '24px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '11px',
                    color: textColor,
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    position: 'absolute',
                    left: `${leftOffsetPercentage}%`,
                    top: '4px',
                    minWidth: '20px',
                    border: `1px solid ${borderColor}`,
                    boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
                  }}
                >
                  <span
                    style={{
                      pointerEvents: 'none',
                      zIndex: 1,
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      maxWidth: '100%',
                      display: 'block',
                      textAlign: widthPercentage < 10 ? 'left' : 'center',
                      paddingLeft: widthPercentage < 10 ? '4px' : '0',
                    }}
                  >
                    {label}
                  </span>
                </div>
              </Tooltip>
            </div>
          </div>
        </div>
      );
    } catch (error) {
      console.error('Error rendering timeoff bar:', error);
      return null;
    }
  };

  const renderAllocationBar = (allocation: Allocation, index: number) => {
    // Calculate allocation bar dimensions using the continuous helper
    const barDimensions = calculateContinuousAllocationBarDimensions(
      allocation,
      timelineItems,
      timeUnit
    );

    // If the allocation is not visible, don't render it
    if (!barDimensions.isVisible) {
      return null;
    }

    const { leftOffsetPercentage, widthPercentage } = barDimensions;

    // Create unique bar ID for resize functionality
    const barId = `allocation-${allocation.id}-${index}`;

    return (
      <div
        key={`allocation-${allocation.id}-${index}`}
        className="allocation-row continuous-timeline-container"
        style={{
          display: 'grid',
          gridTemplateColumns: `${EXPAND_ICON_WIDTH} ${NAME_COLUMN_WIDTH} 82%`,
          width: '100%',
          minHeight: '40px',
        }}
      >
        {/* Empty cell for expand icon column */}
        <div
          style={{
            padding: `${CELL_VERTICAL_PADDING} ${CELL_HORIZONTAL_PADDING}`,
          }}
        ></div>

        {/* Empty cell for name column */}
        <div
          style={{
            padding: `${CELL_VERTICAL_PADDING} ${CELL_HORIZONTAL_PADDING}`,
          }}
        ></div>

        {/* Continuous timeline container wrapper with padding */}
        <div
          style={{
            padding: `${CELL_VERTICAL_PADDING} ${CELL_HORIZONTAL_PADDING}`,
            paddingLeft: '3%',
          }}
        >
          <div
            className="timeline-container"
            style={{
              position: 'relative',
              width: '100%',
              height: '32px',
              borderRadius: '4px',
              backgroundColor: '#fafafa',
            }}
          >
            <Tooltip
              title={
                <div style={{ whiteSpace: 'pre-line' }}>
                  <div>
                    <strong>{t('{{allocations.tooltipPosition}}')}</strong>{' '}
                    {allocation.memberPosition ||
                      t('{{allocations.notAvailable}}')}
                  </div>
                  <div>
                    <strong>{t('{{allocations.tooltipActivity}}')}</strong>{' '}
                    {allocation.memberActivity ||
                      t('{{allocations.notAvailable}}')}
                  </div>
                  <div>
                    <strong>{t('{{allocations.tooltipDescription}}')}</strong>{' '}
                    {allocation.memberActivityDescription ||
                      t('{{allocations.noDescription}}')}
                  </div>
                  <div>
                    <strong>{t('{{allocations.tooltipTotalHours}}')}</strong>{' '}
                    {allocation.totalHours
                      ? Math.round(Number(allocation.totalHours))
                      : '0'}
                    h
                  </div>
                  {allocation.method && (
                    <div>
                      {allocation.method ===
                        EvstTimeAllocationMethod.HoursPerDay && (
                        <div>
                          <strong>
                            {t('{{allocations.tooltipHoursPerDay}}')}
                          </strong>{' '}
                          {convertTotalHoursToConvertedHours(
                            allocation.startDate,
                            allocation.endDate,
                            Number(allocation.totalHours),
                            EvstTimeAllocationMethod.HoursPerDay,
                            allocation.workingDays as EvstDayOfWeek[]
                          )}
                          h
                        </div>
                      )}
                      {allocation.method ===
                        EvstTimeAllocationMethod.HoursPerWeek && (
                        <div>
                          <strong>
                            {t('{{allocations.tooltipHoursPerWeek}}')}
                          </strong>{' '}
                          {convertTotalHoursToConvertedHours(
                            allocation.startDate,
                            allocation.endDate,
                            Number(allocation.totalHours),
                            EvstTimeAllocationMethod.HoursPerWeek,
                            allocation.workingDays as EvstDayOfWeek[]
                          )}
                          h
                        </div>
                      )}
                      {allocation.method ===
                        EvstTimeAllocationMethod.HoursPerMonth && (
                        <div>
                          <strong>
                            {t('{{allocations.tooltipHoursPerMonth}}')}
                          </strong>{' '}
                          {convertTotalHoursToConvertedHours(
                            allocation.startDate,
                            allocation.endDate,
                            Number(allocation.totalHours),
                            EvstTimeAllocationMethod.HoursPerMonth,
                            allocation.workingDays as EvstDayOfWeek[]
                          )}
                          h
                        </div>
                      )}
                    </div>
                  )}
                  <div>
                    <strong>{t('{{allocations.tooltipStartDate}}')}</strong>{' '}
                    {formatDateToYYYYMMDD(
                      plainDateToDate(allocation.startDate)
                    )}
                  </div>
                  <div>
                    <strong>{t('{{allocations.tooltipEndDate}}')}</strong>{' '}
                    {formatDateToYYYYMMDD(plainDateToDate(allocation.endDate))}
                  </div>
                  {allocation.dateWarning && (
                    <div>
                      <strong>
                        <Icon
                          type="warning"
                          style={{
                            fontSize: '16px',
                            color: '#faad14',
                            display: 'inline-flex',
                            alignItems: 'center',
                            verticalAlign: 'middle',
                          }}
                        />
                      </strong>{' '}
                      {t('{{projects.allocationDateWarning}}')}
                    </div>
                  )}
                </div>
              }
              mouseEnterDelay={0.1}
              overlayStyle={{ maxWidth: '300px' }}
            >
              <div
                {...getAllocationBarProps(barId, allocation, {
                  style: {
                    backgroundColor: '#e6f7ff',
                    borderRadius: '4px',
                    width: `${widthPercentage}%`,
                    height: '24px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '11px',
                    color: '#1890ff',
                    cursor: 'pointer',
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    position: 'absolute',
                    left: `${leftOffsetPercentage}%`,
                    top: '4px',
                    minWidth: '20px',
                    border: '1px solid #1890ff',
                    boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
                  },
                  onClick: () => {
                    // Only open edit modal if not currently resizing and user has permission
                    if (
                      !isResizing &&
                      canModifyAllocation &&
                      canModifyAllocation(allocation)
                    ) {
                      handleEditAllocation(allocation);
                    }
                  },
                })}
              >
                {/* Start resize handle */}
                <div
                  {...(canModifyAllocation && canModifyAllocation(allocation)
                    ? getResizeHandleProps('start', barId, allocation)
                    : {})}
                />

                {/* Bar content */}
                <span
                  style={{
                    pointerEvents: 'none',
                    zIndex: 1,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    maxWidth: '100%',
                    display: 'block',
                    textAlign: widthPercentage < 10 ? 'left' : 'center',
                    paddingLeft: widthPercentage < 10 ? '4px' : '0',
                  }}
                >
                  {`${
                    allocation.memberActivity || t('{{allocations.unknown}}')
                  } - ${
                    allocation.totalHours
                      ? Math.round(Number(allocation.totalHours))
                      : '0'
                  }h`}
                  {allocation.dateWarning && (
                    <Icon
                      type="warning"
                      style={{
                        fontSize: '12px',
                        marginLeft: '4px',
                        color: '#faad14',
                        display: 'inline-flex',
                        alignItems: 'center',
                        verticalAlign: 'middle',
                      }}
                    />
                  )}
                </span>

                {/* End resize handle */}
                <div
                  {...(canModifyAllocation && canModifyAllocation(allocation)
                    ? getResizeHandleProps('end', barId, allocation)
                    : {})}
                />
              </div>
            </Tooltip>
          </div>
        </div>
      </div>
    );
  };

  const expandedRowRender = (record: any) => {
    if (viewType === 'members') {
      // Members view - show projects under each member
      if (!record.id || !record.employeeId) {
        return null;
      }

      // Get all members with the same employeeId
      const employeeMembers = members.filter(
        (m) => m.employeeId === record.employeeId
      );

      // Get all allocations for all members with this employeeId
      let allEmployeeAllocations: Allocation[] = [];
      for (const member of employeeMembers) {
        if (member.id) {
          const memberAllocations = getMemberAllocations(
            member.id,
            allocations
          );
          allEmployeeAllocations = [
            ...allEmployeeAllocations,
            ...memberAllocations,
          ];
        }
      }

      // Get timeoff data for this employee - only for members view
      const timeOffBars = [];
      if (viewType === 'members') {
        const memberWithTimeOff = employeeMembers.find((m) => m.timeOff);
        const timeOffData = memberWithTimeOff?.timeOff;

        // Render timeoff bars if there's timeoff data and showTimeOff is true
        if (timeOffData && showTimeOff) {
          // First render holidays
          if (timeOffData.holidays && timeOffData.holidays.length > 0) {
            for (const [index, holiday] of timeOffData.holidays.entries()) {
              const timeOffBar = renderTimeOffBar(holiday, index, true);
              if (timeOffBar) {
                timeOffBars.push(timeOffBar);
              }
            }
          }

          // Then render absences
          if (timeOffData.absences && timeOffData.absences.length > 0) {
            for (const [index, absence] of timeOffData.absences.entries()) {
              const timeOffBar = renderTimeOffBar(absence, index, false);
              if (timeOffBar) {
                timeOffBars.push(timeOffBar);
              }
            }
          }
        }
      }

      if (allEmployeeAllocations.length === 0 && timeOffBars.length === 0) {
        return (
          <div
            style={{
              paddingLeft: '2%',
              paddingTop: '16px',
              paddingBottom: '16px',
              color: '#666',
            }}
          >
            {t('{{allocations.noAllocations}}')}
          </div>
        );
      }

      // Group allocations by project name
      const projectGroups: { [projectName: string]: Allocation[] } = {};

      for (const allocation of allEmployeeAllocations) {
        if (!allocation.id) {
          continue;
        }

        const projectName =
          allocation.projectName || t('{{allocations.unknown}}');
        if (!projectGroups[projectName]) {
          projectGroups[projectName] = [];
        }
        projectGroups[projectName].push(allocation);
      }

      return (
        <div
          className="expanded-content"
          style={{
            width: '100%',
            margin: 0,
            padding: 0,
          }}
        >
          {/* Render timeoff bars first if they exist */}
          {timeOffBars.length > 0 && (
            <div className="project-container">
              {/* TimeOff header row */}
              <div
                className="allocation-header"
                style={{
                  display: 'grid',
                  gridTemplateColumns: `${EXPAND_ICON_WIDTH} ${NAME_COLUMN_WIDTH} repeat(${timelineItems.length}, ${TIMELINE_COLUMN_WIDTH})`,
                  width: '100%',
                  paddingTop: '8px',
                  paddingBottom: '8px',
                }}
              >
                <div
                  style={{
                    padding: `${CELL_VERTICAL_PADDING} ${CELL_HORIZONTAL_PADDING}`,
                  }}
                ></div>
                <div
                  style={{
                    fontWeight: 500,
                    paddingLeft: '15px',
                    fontSize: '12px',
                    padding: `${CELL_VERTICAL_PADDING} ${CELL_HORIZONTAL_PADDING}`,
                  }}
                >
                  {t('{{allocations.timeOff}}')}
                </div>
                {timelineItems.map((_, index) => (
                  <div
                    key={`header-cell-${index}`}
                    style={{
                      padding: `${CELL_VERTICAL_PADDING} ${CELL_HORIZONTAL_PADDING}`,
                    }}
                  ></div>
                ))}
              </div>

              {/* TimeOff bars */}
              {timeOffBars}
            </div>
          )}

          {/* Render project allocations */}
          {Object.entries(projectGroups).map(
            ([projectName, projectAllocations]) => (
              <div key={projectName} className="project-container">
                {/* Project header row */}
                <div
                  className="allocation-header"
                  style={{
                    display: 'grid',
                    gridTemplateColumns: `${EXPAND_ICON_WIDTH} ${NAME_COLUMN_WIDTH} repeat(${timelineItems.length}, ${TIMELINE_COLUMN_WIDTH})`,
                    width: '100%',
                    paddingTop: '8px',
                    paddingBottom: '8px',
                  }}
                >
                  <div
                    style={{
                      padding: `${CELL_VERTICAL_PADDING} ${CELL_HORIZONTAL_PADDING}`,
                    }}
                  ></div>
                  <div
                    style={{
                      fontWeight: 500,
                      paddingLeft: '15px',
                      fontSize: '12px',
                      padding: `${CELL_VERTICAL_PADDING} ${CELL_HORIZONTAL_PADDING}`,
                    }}
                  >
                    {projectName}
                  </div>
                  {timelineItems.map((_, index) => (
                    <div
                      key={`header-cell-${index}`}
                      style={{
                        padding: `${CELL_VERTICAL_PADDING} ${CELL_HORIZONTAL_PADDING}`,
                      }}
                    ></div>
                  ))}
                </div>

                {/* Allocations rows */}
                {projectAllocations
                  .filter(
                    (allocation) =>
                      allocation.id &&
                      allocation.startDate &&
                      allocation.endDate
                  )
                  .sort((a, b) => {
                    // Sort allocations by start date
                    if (!a.startDate || !b.startDate) {
                      return 0;
                    }
                    return (
                      plainDateToDate(a.startDate).getTime() -
                      plainDateToDate(b.startDate).getTime()
                    );
                  })
                  .map((allocation, index) => {
                    if (
                      !allocation.id ||
                      !allocation.startDate ||
                      !allocation.endDate
                    ) {
                      return null;
                    }

                    return renderAllocationBar(allocation, index);
                  })}
              </div>
            )
          )}
        </div>
      );
    } else {
      // Projects view - show members under each project
      if (!record.projectAllocations) {
        return null;
      }

      const projectAllocations = record.projectAllocations as Allocation[];

      if (projectAllocations.length === 0) {
        return (
          <div
            style={{
              paddingLeft: '2%',
              paddingTop: '16px',
              paddingBottom: '16px',
              color: '#666',
            }}
          >
            {t('{{allocations.noAllocations}}')}
          </div>
        );
      }

      // Group allocations by member name
      const memberGroups: { [memberName: string]: Allocation[] } = {};

      for (const allocation of projectAllocations) {
        if (!allocation.id) {
          continue;
        }

        const memberName =
          allocation.memberName || t('{{allocations.unknown}}');
        if (!memberGroups[memberName]) {
          memberGroups[memberName] = [];
        }
        memberGroups[memberName].push(allocation);
      }

      return (
        <div
          className="expanded-content"
          style={{
            width: '100%',
            margin: 0,
            padding: 0,
          }}
        >
          {Object.entries(memberGroups).map(
            ([memberName, memberAllocations]) => (
              <div key={memberName} className="member-container">
                {/* Member header row */}
                <div
                  className="allocation-header"
                  style={{
                    display: 'grid',
                    gridTemplateColumns: `${EXPAND_ICON_WIDTH} ${NAME_COLUMN_WIDTH} repeat(${timelineItems.length}, ${TIMELINE_COLUMN_WIDTH})`,
                    width: '100%',
                    paddingTop: '8px',
                    paddingBottom: '8px',
                  }}
                >
                  <div
                    style={{
                      padding: `${CELL_VERTICAL_PADDING} ${CELL_HORIZONTAL_PADDING}`,
                    }}
                  ></div>
                  <div
                    style={{
                      fontWeight: 500,
                      paddingLeft: '15px',
                      fontSize: '12px',
                      padding: `${CELL_VERTICAL_PADDING} ${CELL_HORIZONTAL_PADDING}`,
                    }}
                  >
                    {memberName}
                  </div>
                  {timelineItems.map((_, index) => (
                    <div
                      key={`header-cell-${index}`}
                      style={{
                        padding: `${CELL_VERTICAL_PADDING} ${CELL_HORIZONTAL_PADDING}`,
                      }}
                    ></div>
                  ))}
                </div>

                {/* Allocations rows */}
                {memberAllocations
                  .filter(
                    (allocation) =>
                      allocation.id &&
                      allocation.startDate &&
                      allocation.endDate
                  )
                  .sort((a, b) => {
                    // Sort allocations by start date
                    if (!a.startDate || !b.startDate) {
                      return 0;
                    }
                    return (
                      plainDateToDate(a.startDate).getTime() -
                      plainDateToDate(b.startDate).getTime()
                    );
                  })
                  .map((allocation, index) => {
                    if (
                      !allocation.id ||
                      !allocation.startDate ||
                      !allocation.endDate
                    ) {
                      return null;
                    }

                    return renderAllocationBar(allocation, index);
                  })}
              </div>
            )
          )}
        </div>
      );
    }
  };

  // Generate table data based on view type
  let tableData: any[] = [];

  if (viewType === 'members') {
    // Group members by employeeId
    const membersByEmployeeId: { [employeeId: string]: Member[] } = {};

    for (const member of members) {
      if (member.employeeId) {
        const employeeIdStr = String(member.employeeId);
        if (!membersByEmployeeId[employeeIdStr]) {
          membersByEmployeeId[employeeIdStr] = [];
        }
        membersByEmployeeId[employeeIdStr].push(member);
      }
    }

    // Create a list of unique members (one per employeeId)
    const uniqueMembers: Member[] = [];

    for (const employeeIdStr of Object.keys(membersByEmployeeId)) {
      // Use the first member entry for each employeeId
      if (membersByEmployeeId[employeeIdStr].length > 0) {
        uniqueMembers.push(membersByEmployeeId[employeeIdStr][0]);
      }
    }

    tableData = [
      // Total row
      {
        key: 'total',
        isTotal: true,
        name: 'TOTAL',
      },
      // Unique member rows (one per employeeId)
      ...uniqueMembers.map((member) => ({
        ...member,
        key: member.id,
      })),
    ];
  } else {
    // Projects view - group allocations by project
    const projectGroups: {
      [projectId: number]: {
        projectName: string;
        allocations: Allocation[];
      };
    } = {};

    for (const project of projects) {
      if (!project.id) {
        continue;
      }

      const projectId = project.id;
      if (!projectGroups[projectId]) {
        projectGroups[projectId] = {
          projectName: project.name || t('{{allocations.unknown}}'),
          allocations: [],
        };
      }
    }

    for (const allocation of allocations) {
      if (!allocation.id) {
        continue;
      }

      const projectId = allocation.projectId;
      if (!projectGroups[projectId]) {
        projectGroups[projectId] = {
          projectName: allocation.projectName || t('{{allocations.unknown}}'),
          allocations: [],
        };
      }
      projectGroups[projectId].allocations.push(allocation);
    }

    tableData = [
      // Total row
      {
        key: 'total',
        isTotal: true,
        name: 'TOTAL',
      },
      // Project rows
      ...Object.keys(projectGroups).map((projectId, index) => ({
        id: `project-${index}`,
        key: `project-${index}`,
        name: projectGroups[projectId].projectName,
        isProject: true,
        projectAllocations: projectGroups[projectId].allocations,
      })),
    ];
  }

  return (
    <div className="allocation-table-container">
      {/* Resize tooltip */}
      {tooltipState.isVisible && (
        <div
          style={{
            position: 'fixed',
            left: tooltipState.x,
            top: tooltipState.y,
            backgroundColor: 'rgba(0, 0, 0, 0.85)',
            color: 'white',
            padding: '6px 12px',
            borderRadius: '4px',
            fontSize: '12px',
            fontWeight: 500,
            zIndex: 9999,
            pointerEvents: 'none',
            whiteSpace: 'nowrap',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',
          }}
        >
          {tooltipState.date}
        </div>
      )}
      <style>
        {`
          /* Set consistent cell padding for main table */
          .allocation-table-container .ant-table-thead > tr > th,
          .allocation-table-container .ant-table-tbody > tr > td {
            padding: ${CELL_VERTICAL_PADDING} ${CELL_HORIZONTAL_PADDING};
          }

          .current-week-column .ant-table-column-title,
          .current-month-column .ant-table-column-title {
            background-color: #e6f7ff;
            border-radius: 4px;
            padding: 2px;
            border: 2px solid #1890ff;
          }

          .current-week-cell,
          .current-month-cell {
            background-color: #e6f7ff !important;
          }

          .allocation-table-container .ant-table-thead > tr > th {
            background-color: #fafafa;
          }

          .allocation-table-container .ant-table-tbody > tr.ant-table-row:hover > td {
            background-color: #f5f5f5;
          }

          .allocation-table-container .current-week-cell:hover,
          .allocation-table-container .current-month-cell:hover {
            background-color: #d6eeff !important;
          }

          .expanded-content .project-container,
          .expanded-content .member-container {
            margin-bottom: 0;
            border-bottom: 1px solid #f0f0f0;
            padding-bottom: 0;
            width: 100%;
          }

          /* Ensure expanded content fills the entire row width */
          .ant-table-expanded-row > td {
            padding: 0 !important;
          }

          .ant-table-expanded-row-fixed {
            margin: 0 !important;
            padding: 0 !important;
          }

          /* Force the width of expanded row to match parent */
          .ant-table-expanded-row td,
          .ant-table-expanded-row-fixed {
            width: 100% !important;
            box-sizing: border-box !important;
          }

          /* Custom grid layouts classes */
          .expanded-content {
            box-sizing: border-box;
            width: 100%;
            padding: 0;
            margin: 0;
          }

          /* Zero margin/padding for expanded rows */
          .zero-margin-expanded-row > td {
            padding: 0 !important;
            margin: 0 !important;
          }

          /* Ensure alignment between expanded content and parent table */
          .allocation-row, .allocation-header {
            box-sizing: border-box;
            width: 100%;
          }

          /* Make all text and columns more condensed */
          .ant-table {
            font-size: 12px;
          }

          /* Fix any overflow issues in column titles */
          .ant-table-column-title {
            overflow: hidden;
            white-space: normal;
            word-break: break-word;
          }

          /* Remove any potential margin from the allocation bars */
          .allocation-row > div {
            margin: 0;
          }

          /* Resize handle styles */
          .allocation-table-container [data-bar-id] {
            transition: box-shadow 0.2s ease;
          }

          .allocation-table-container [data-bar-id]:hover {
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
          }

          /* Ensure resize handles are visible on hover */
          .allocation-table-container [data-bar-id] > div[style*="cursor: col-resize"] {
            transition: background-color 0.2s ease;
          }

          .allocation-table-container [data-bar-id]:hover > div[style*="cursor: col-resize"] {
            background-color: rgba(24, 144, 255, 0.2) !important;
          }
        `}
      </style>
      <Table
        columns={columns}
        dataSource={tableData}
        expandable={{
          expandedRowRender,
          expandedRowKeys,
          expandIcon: () => null, // Hide default expand icon since we're using our own
          rowExpandable: (record) => !record.isTotal, // Don't allow expanding the total row
          expandedRowClassName: () => 'zero-margin-expanded-row', // Add a class
        }}
        onRow={(record) => ({
          onClick: (event) => {
            // Only toggle expansion if the click is on the row itself, not on buttons or other interactive elements
            if (
              event.target &&
              'closest' in event.target &&
              !(event.target as any).closest('button') &&
              !(event.target as any).closest('[role="button"]') &&
              !(event.target as any).closest('.ant-dropdown-trigger') &&
              !record.isTotal // Don't expand the total row
            ) {
              toggleExpandRow(record.id);
            }
          },
          style: { cursor: record.isTotal ? 'default' : 'pointer' }, // Change cursor to pointer for expandable rows
        })}
        pagination={false}
        scroll={{ x: 'max-content' }}
        style={{ marginBottom: 0 }}
        size="small" // Use small size for more compact table
      />
    </div>
  );
};

export default AllocationTable;
