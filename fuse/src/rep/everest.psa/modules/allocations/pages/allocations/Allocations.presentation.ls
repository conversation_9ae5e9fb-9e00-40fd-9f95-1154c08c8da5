package everest.psa

odata presentation Allocations {

    data-set Allocations {
        supported-operations view, add, change,
            remove
        field id (required: false): field<everest.timetracking::Allocation.id>
        field memberId: Id
        field projectActivityId: Id
        field startDate: PlainDate
        field endDate: PlainDate
        field totalHours: Number<Decimal>
        field notes: Text
        field method: enum<everest.timetracking::TimeAllocationMethod>
        field workingDays (required: false): array<enum<everest.appserver::DayOfWeek>>
        field projectId: Id
        field projectName: Text
        field memberName: Text
        field memberEmployeeId: Id
        field memberPosition: Text
        field memberActivity: Text
        field memberActivityDescription: Text
        field memberActivityStartDate: PlainDate
        field memberActivityEndDate: PlainDate
        field memberTotalCapacity: Number<Int>
        field dateWarning (required: false): TrueFalse
    }

    data-set Members {
        supported-operations view
        field id: field<everest.timetracking::Member.id>
        field employeeId: Id
        field projectPositionId: Id
        field name: Text
        field totalCapacity: Number<Int>
        field position: Text
        field projectId: Id
        field projectName: Text
    }

    data-set ProjectPositions {
        supported-operations view
        field id: field<everest.timetracking::ProjectPosition.id>
        field name: Text
        field `description`: Text
        field projectId: Id
    }

    data-set Projects {
        supported-operations view
        field id: field<everest.timetracking::TimetrackingProject.id>
        field name: Text
        field psaProjectId: Id
        field status: enum<PsaProjectStatus>
    }

    data-set ProjectActivities {
        supported-operations view
        field id: field<everest.timetracking::ProjectActivity.id>
        field name: Text
        field `description`: Text
        field projectId: Id
        field memberId: Id
        field projectPositionId: Id
        field startDate: PlainDate
        field endDate: PlainDate
    }

    action validateBudget {
        inputs {
            projectId: field<everest.timetracking::TimetrackingProject.id>
        }
        outputs {
            warnings: array<object<{
                message: Text
                projectName: Text
            }>>
        }
        properties {
            side-effects false
        }
    }

    action GetCurrentEmployeeId {
        outputs {
            employeeId: Id
        }
        properties {
            side-effects false
        }
    }

    action GetTimeOff {
        inputs {
            employeeIds: array<field<everest.hr.base::Employee.id>>
            startDate: Date
            endDate: Date
        }
        outputs {
            result: array<object<{
                employeeId: Id
                holidays: array<object<{
                    holidayDate: PlainDate
                    holidayName: Text
                    halfDay: TrueFalse
                }>>
                absences: array<object<{
                    startDate: PlainDate
                    endDate: PlainDate
                    mandatoryLeaveId: Number<Int>
                    absenceType: Text
                    days: Number<Decimal>
                }>>
            }>>
        }
        properties {
            side-effects false
        }
    }

    action validateAllocationDate {
        inputs {
            allocationStartDate: PlainDate
            allocationEndDate: PlainDate
            activityStartDate: PlainDate
            activityEndDate: PlainDate
        }
        outputs {
            isOutsideDateRange: TrueFalse
        }
        properties {
            side-effects false
        }
    }
}
