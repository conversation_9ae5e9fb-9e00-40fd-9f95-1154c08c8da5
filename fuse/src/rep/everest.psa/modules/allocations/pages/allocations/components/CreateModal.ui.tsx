// @i18n:psa
import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Button,
  Spin,
  Radio,
  Tooltip,
  Alert,
} from 'antd';
import { useTranslation } from 'react-i18next';
import DatePicker from '@pkg/everest.base.ui/public/reactComponents/DatePicker.ui';
import type { FormInstance } from 'antd/es/form';
import { Member, Project, ProjectActivity } from './types.ui';
import { EvstTimeAllocationMethod } from '@pkg/everest.timetracking/types/enums/TimeAllocationMethod';
import { EvstDayOfWeek } from '@pkg/everest.appserver/types/enums/DayOfWeek';

import { Icon } from '@everestsystems/design-system';
import { PlainDate } from '@everestsystems/datetime';
const { Option } = Select;
const { RangePicker } = DatePicker;

interface CreateModalProps {
  isNewAllocationModalVisible: boolean;
  newFormRef: React.RefObject<FormInstance>;
  selectedProject: number | null;
  projects: Project[];
  loading: boolean;
  handleNewAllocationCancel: () => void;
  handleNewAllocationSubmit: () => void;
  handleProjectChange: (projectId: number) => void;
  fetchProjectActivities: (
    projectId?: number,
    memberId?: number,
    projectPositionId?: number
  ) => Promise<ProjectActivity[]>;
  fetchMembers: (projectId?: number) => Promise<Member[]>;
  canModifyAllocation?: (allocation: {
    projectId?: number;
    memberEmployeeId?: number;
  }) => boolean;
  currentEmployeeId?: number | null;
  projectPermissions?: Map<number, { hasEditPermission: boolean }>;
  validateAllocationDate?: (
    allocationStartDate: PlainDate,
    allocationEndDate: PlainDate,
    activityStartDate: PlainDate,
    activityEndDate: PlainDate
  ) => Promise<boolean>;
}

const CreateModal: React.FC<CreateModalProps> = ({
  isNewAllocationModalVisible,
  newFormRef,
  selectedProject,
  projects,
  loading,
  handleNewAllocationCancel,
  handleNewAllocationSubmit,
  handleProjectChange,
  fetchProjectActivities,
  fetchMembers,
  canModifyAllocation,
  currentEmployeeId,
  projectPermissions,
  validateAllocationDate,
}) => {
  const { t } = useTranslation();
  const [projectActivities, setProjectActivities] = useState<ProjectActivity[]>(
    []
  );
  const [projectMembers, setProjectMembers] = useState<Member[]>([]);
  const [loadingProjectData, setLoadingProjectData] = useState(false);
  const [selectedMemberId, setSelectedMemberId] = useState<number | null>(null);
  const [selectedMemberPositionId, setSelectedMemberPositionId] = useState<
    number | null
  >(null);
  const [selectedActivity, setSelectedActivity] =
    useState<ProjectActivity | null>(null);
  const [showDateWarning, setShowDateWarning] = useState<boolean>(false);

  // Helper function to check if user can create allocation for current form values
  const canCreateAllocation = () => {
    // If no project selected, allow form validation to handle it
    if (!selectedProject) {
      return true;
    }

    // If we have a project but no permission function or employee ID, deny
    if (!currentEmployeeId || !canModifyAllocation) {
      return false;
    }

    const formValues = newFormRef.current?.getFieldsValue();
    const memberEmployeeId = formValues?.memberEmployeeId;

    return canModifyAllocation({
      projectId: selectedProject,
      memberEmployeeId: memberEmployeeId || currentEmployeeId,
    });
  };

  // Filter members based on permissions
  const getFilteredMembers = () => {
    if (!currentEmployeeId || !projectPermissions || !selectedProject) {
      return projectMembers;
    }

    const permission = projectPermissions.get(selectedProject);
    if (!permission) {
      return [];
    }

    // If user has edit permission, they can see all members
    if (permission.hasEditPermission) {
      return projectMembers;
    }

    // If user has view permission, they can only see themselves
    return projectMembers.filter(
      (member) => member.employeeId === currentEmployeeId
    );
  };

  // Fetch activities and members when project changes
  useEffect(() => {
    if (selectedProject) {
      const loadProjectData = async () => {
        setLoadingProjectData(true);
        try {
          // Reset member and activity selections when project changes
          setSelectedMemberId(null);
          setSelectedMemberPositionId(null);
          setProjectActivities([]);

          // Fetch members for this project
          const projectMembers = await fetchMembers(selectedProject);
          setProjectMembers(projectMembers);
        } catch (error) {
          console.error('Error loading project data:', error);
        } finally {
          setLoadingProjectData(false);
        }
      };

      loadProjectData();
    } else {
      setProjectActivities([]);
      setProjectMembers([]);
      setSelectedMemberId(null);
      setSelectedMemberPositionId(null);
    }
  }, [selectedProject, fetchMembers]);

  // Handle member selection change
  const handleMemberChange = async (memberId: number) => {
    setSelectedMemberId(memberId);

    // Clear the activity selection in the form when member changes
    const form = newFormRef.current;
    if (form) {
      form.setFieldsValue({ projectActivityId: undefined });
    }

    // Find the selected member to get their projectPositionId and employeeId
    const selectedMember = projectMembers.find(
      (member) => member.id === memberId
    );

    if (selectedMember) {
      // Set the projectActivityId to undefined and memberEmployeeId to the selected member's employeeId
      form.setFieldsValue({
        memberEmployeeId: selectedMember.employeeId,
      });

      if (selectedMember.projectPositionId) {
        setSelectedMemberPositionId(selectedMember.projectPositionId);

        // Fetch activities based on project, member, and position
        setLoadingProjectData(true);
        try {
          const activities = await fetchProjectActivities(
            selectedProject,
            selectedMember.id,
            selectedMember.projectPositionId
          );
          setProjectActivities(activities);
        } catch (error) {
          console.error('Error loading activities for member position:', error);
        } finally {
          setLoadingProjectData(false);
        }
      } else {
        // If member has no position ID, fetch all activities for the project
        setSelectedMemberPositionId(null);
        setLoadingProjectData(true);
        try {
          const activities = await fetchProjectActivities(selectedProject);
          setProjectActivities(activities);
        } catch (error) {
          console.error('Error loading activities for project:', error);
        } finally {
          setLoadingProjectData(false);
        }
      }
    }
  };

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Icon type="add" useMaterialIcon style={{ marginRight: 8 }} />{' '}
          {t('{{allocations.modalTitle}}')}
        </div>
      }
      open={isNewAllocationModalVisible}
      onCancel={handleNewAllocationCancel}
      footer={[
        <Button
          key="cancel"
          onClick={handleNewAllocationCancel}
          style={{
            borderRadius: '10px',
            padding: '0 16px',
            height: '34px',
          }}
        >
          {t('{{allocations.cancel}}')}
        </Button>,
        canCreateAllocation() ? (
          <Button
            key="submit"
            type="primary"
            onClick={handleNewAllocationSubmit}
            loading={loading}
            style={{
              borderRadius: '10px',
              boxShadow: '0 2px 6px rgba(0, 0, 0, 0.1)',
              padding: '0 16px',
              height: '34px',
            }}
          >
            {t('{{allocations.save}}')}
          </Button>
        ) : (
          <Tooltip title={t('{{allocations.noPermissionToModify}}')}>
            <Button
              key="submit"
              type="primary"
              disabled
              style={{
                borderRadius: '10px',
                padding: '0 16px',
                height: '34px',
              }}
            >
              {t('{{allocations.save}}')}
            </Button>
          </Tooltip>
        ),
      ]}
      width={600}
    >
      <Spin
        spinning={loadingProjectData || loading}
        tip={t('{{allocations.loading}}')}
        style={{ width: '100%' }}
      >
        {showDateWarning && (
          <Alert
            message={t('{{projects.validationWarnings}}')}
            description={t('{{projects.allocationDateWarning}}')}
            type="warning"
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}
        <Form
          ref={newFormRef}
          layout="vertical"
          requiredMark={false}
          initialValues={{
            method: EvstTimeAllocationMethod.TotalHours,
            timeUnit: EvstTimeAllocationMethod.HoursPerDay,
          }}
          onValuesChange={(changedValues) => {
            const form = newFormRef.current;
            if (!form) {
              return;
            }

            // When method changes to HoursPerDay, ensure timeUnit is set to HoursPerDay
            if (changedValues.method === EvstTimeAllocationMethod.HoursPerDay) {
              form.setFieldsValue({
                timeUnit: EvstTimeAllocationMethod.HoursPerDay,
                dateRange: undefined,
              });
            } else if (
              changedValues.method === EvstTimeAllocationMethod.TotalHours
            ) {
              // When switching to TotalHours, reset dateRange
              form.setFieldsValue({ dateRange: undefined });
            }

            // When timeUnit changes, reset dateRange
            if (changedValues.timeUnit) {
              form.setFieldsValue({ dateRange: undefined });
            }
          }}
        >
          <Form.Item
            name="projectId"
            label={t('{{allocations.fieldProject}}')}
            rules={[
              {
                required: true,
                message: t('{{allocations.errorSelectProject}}'),
              },
            ]}
          >
            <Select
              placeholder={t('{{allocations.placeholderSelectProject}}')}
              onChange={handleProjectChange}
              showSearch
              optionFilterProp="children"
              filterOption={(input, option) =>
                option?.children
                  ?.toString()
                  .toLowerCase()
                  .indexOf(input.toLowerCase()) >= 0
              }
            >
              {projects.map((project) => (
                <Option key={project.id} value={project.id}>
                  {project.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          {/* Hidden form field for memberEmployeeId */}
          <Form.Item name="memberEmployeeId" hidden>
            <Input type="hidden" />
          </Form.Item>

          <Form.Item
            name="memberId"
            label={t('{{allocations.fieldTeamMemberAndPosition}}')}
            rules={[
              {
                required: true,
                message: t('{{allocations.errorSelectTeamMemberAndPosition}}'),
              },
            ]}
          >
            <Select
              placeholder={
                selectedProject
                  ? t('{{allocations.placeholderSelectTeamMemberAndPosition}}')
                  : t('{{allocations.placeholderSelectProjectFirst}}')
              }
              disabled={!selectedProject}
              loading={loadingProjectData}
              showSearch
              optionFilterProp="children"
              filterOption={(input, option) =>
                option?.children
                  ?.toString()
                  .toLowerCase()
                  .indexOf(input.toLowerCase()) >= 0
              }
              onChange={handleMemberChange}
              optionLabelProp="label"
            >
              {getFilteredMembers().map((member) => (
                <Option
                  key={member.id}
                  value={member.id}
                  label={`${member.name}${
                    member.position ? ` - ${member.position}` : ''
                  }`}
                >
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                    }}
                  >
                    <div
                      style={{
                        width: '18px',
                        height: '18px',
                        borderRadius: '50%',
                        backgroundColor: '#52c41a',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        fontSize: '12px',
                        color: 'white',
                        fontWeight: 'bold',
                      }}
                    >
                      {member.name.charAt(0).toUpperCase()}
                    </div>
                    <div>
                      <div>{member.name}</div>
                      <div style={{ fontSize: '12px', color: '#666' }}>
                        {member.position || 'No position assigned'}
                      </div>
                    </div>
                  </div>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="projectActivityId"
            label={t('{{allocations.fieldActivity}}')}
            rules={[
              {
                required: true,
                message: t('{{allocations.errorSelectActivity}}'),
              },
            ]}
          >
            <Select
              placeholder={
                selectedMemberId
                  ? t('{{allocations.placeholderSelectActivity}}')
                  : t(
                      '{{allocations.placeholderSelectTeamMemberAndPositionFirst}}'
                    )
              }
              disabled={!selectedMemberId}
              loading={loadingProjectData}
              showSearch
              optionFilterProp="children"
              onChange={async (activityId) => {
                const activity = projectActivities.find(
                  (a) => a.id === activityId
                );
                setSelectedActivity(activity || null);

                // Check if current date range is valid for the new activity
                const form = newFormRef.current;
                if (form && activity) {
                  const dateRange = form.getFieldValue('dateRange');
                  if (dateRange && dateRange.length === 2) {
                    // Handle Luxon DateTime format
                    const startDate = PlainDate.from({
                      year: dateRange[0].year,
                      month: dateRange[0].month,
                      day: dateRange[0].day,
                    });
                    const endDate = PlainDate.from({
                      year: dateRange[1].year,
                      month: dateRange[1].month,
                      day: dateRange[1].day,
                    });

                    // Update the date warning state
                    setShowDateWarning(
                      await validateAllocationDate(
                        startDate,
                        endDate,
                        activity.startDate,
                        activity.endDate
                      )
                    );
                  } else {
                    setShowDateWarning(false);
                  }
                } else {
                  setShowDateWarning(false);
                }
              }}
              filterOption={(input, option) =>
                option?.children
                  ?.toString()
                  .toLowerCase()
                  .indexOf(input.toLowerCase()) >= 0
              }
            >
              {projectActivities.map((activity) => (
                <Option key={activity.id} value={activity.id}>
                  {activity.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="method"
            label={t('{{allocations.fieldAllocationMethod}}')}
            rules={[
              {
                required: true,
                message: t('{{allocations.errorSelectMethod}}'),
              },
            ]}
          >
            <Select style={{ width: '100%' }}>
              <Option value={EvstTimeAllocationMethod.TotalHours}>
                {t('{{allocations.methodTotalHours}}')}
              </Option>
              <Option value={EvstTimeAllocationMethod.HoursPerDay}>
                {t('{{allocations.methodHoursPerPeriod}}')}
              </Option>
            </Select>
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => {
              // Reset date range when method changes
              if (prevValues.method !== currentValues.method) {
                const form = newFormRef.current;
                if (form) {
                  form.setFieldsValue({ dateRange: undefined });
                }
              }
              return (
                prevValues.method !== currentValues.method ||
                prevValues.timeUnit !== currentValues.timeUnit
              );
            }}
          >
            {({ getFieldValue }) => {
              const method = getFieldValue('method');

              if (method === EvstTimeAllocationMethod.HoursPerDay) {
                return (
                  <>
                    <Form.Item
                      name="timeUnit"
                      className="radio-group-container"
                    >
                      <Radio.Group>
                        <Radio value={EvstTimeAllocationMethod.HoursPerDay}>
                          {t('{{allocations.periodDay}}')}
                        </Radio>
                        <Radio value={EvstTimeAllocationMethod.HoursPerWeek}>
                          {t('{{allocations.periodWeek}}')}
                        </Radio>
                        <Radio value={EvstTimeAllocationMethod.HoursPerMonth}>
                          {t('{{allocations.periodMonth}}')}
                        </Radio>
                      </Radio.Group>
                    </Form.Item>

                    <Form.Item
                      noStyle
                      shouldUpdate={(prevValues, currentValues) =>
                        prevValues.timeUnit !== currentValues.timeUnit
                      }
                    >
                      {({ getFieldValue }) => {
                        const timeUnit = getFieldValue('timeUnit');
                        let label = t('{{allocations.fieldHoursPerDay}}');
                        let message = t(
                          '{{allocations.errorEnterHoursPerDay}}'
                        );
                        let maxValue = 24;

                        if (
                          timeUnit === EvstTimeAllocationMethod.HoursPerWeek
                        ) {
                          label = t('{{allocations.fieldHoursPerWeek}}');
                          message = t('{{allocations.errorEnterHoursPerWeek}}');
                          maxValue = 168; // 24 * 7
                        } else if (
                          timeUnit === EvstTimeAllocationMethod.HoursPerMonth
                        ) {
                          label = t('{{allocations.fieldHoursPerMonth}}');
                          message = t(
                            '{{allocations.errorEnterHoursPerMonth}}'
                          );
                          maxValue = 744; // 24 * 31
                        }

                        return (
                          <Form.Item
                            name="convertedHours"
                            label={label}
                            rules={[{ required: true, message: message }]}
                          >
                            <InputNumber
                              min={1}
                              max={maxValue}
                              style={{ width: '100%' }}
                            />
                          </Form.Item>
                        );
                      }}
                    </Form.Item>

                    <Form.Item
                      name="workingDays"
                      label={t('{{allocations.fieldWorkingDays}}')}
                    >
                      <Select
                        mode="multiple"
                        placeholder={t(
                          '{{allocations.placeholderSelectWorkingDays}}'
                        )}
                        style={{ width: '100%' }}
                      >
                        <Option value={EvstDayOfWeek.Monday}>
                          {t('{{allocations.dayMonday}}')}
                        </Option>
                        <Option value={EvstDayOfWeek.Tuesday}>
                          {t('{{allocations.dayTuesday}}')}
                        </Option>
                        <Option value={EvstDayOfWeek.Wednesday}>
                          {t('{{allocations.dayWednesday}}')}
                        </Option>
                        <Option value={EvstDayOfWeek.Thursday}>
                          {t('{{allocations.dayThursday}}')}
                        </Option>
                        <Option value={EvstDayOfWeek.Friday}>
                          {t('{{allocations.dayFriday}}')}
                        </Option>
                      </Select>
                    </Form.Item>

                    {/* Date Range field with behavior based on time unit */}
                    <Form.Item
                      noStyle
                      shouldUpdate={(prevValues, currentValues) =>
                        prevValues.timeUnit !== currentValues.timeUnit
                      }
                    >
                      {({ getFieldValue }) => {
                        const timeUnit = getFieldValue('timeUnit');
                        return (
                          <Form.Item
                            name="dateRange"
                            label={t('{{allocations.fieldDateRange}}')}
                            rules={[
                              {
                                required: true,
                                message: t(
                                  '{{allocations.errorSelectDateRange}}'
                                ),
                              },
                            ]}
                          >
                            {timeUnit ===
                            EvstTimeAllocationMethod.HoursPerWeek ? (
                              <RangePicker
                                style={{ width: '100%' }}
                                format="YYYY-MM-DD"
                                picker="week"
                                showWeekNumber={false}
                                onChange={async (dates) => {
                                  if (dates && dates.length === 2) {
                                    // Get the first day of the week (Monday) and last day (Sunday)
                                    const startOfWeek =
                                      dates[0].startOf('week');
                                    const endOfWeek = dates[1].endOf('week');

                                    // Update the form with the correct start and end dates
                                    const form = newFormRef.current;
                                    if (form) {
                                      form.setFieldsValue({
                                        dateRange: [startOfWeek, endOfWeek],
                                      });
                                    }

                                    // Check for date warnings if activity is selected
                                    if (selectedActivity) {
                                      const startDate = PlainDate.from({
                                        year: startOfWeek.year,
                                        month: startOfWeek.month,
                                        day: startOfWeek.day,
                                      });
                                      const endDate = PlainDate.from({
                                        year: endOfWeek.year,
                                        month: endOfWeek.month,
                                        day: endOfWeek.day,
                                      });

                                      setShowDateWarning(
                                        await validateAllocationDate(
                                          startDate,
                                          endDate,
                                          selectedActivity?.startDate,
                                          selectedActivity?.endDate
                                        )
                                      );
                                    }
                                  }
                                }}
                              />
                            ) : timeUnit ===
                              EvstTimeAllocationMethod.HoursPerMonth ? (
                              <RangePicker
                                style={{ width: '100%' }}
                                format="YYYY-MM-DD"
                                picker="month"
                                onChange={async (dates) => {
                                  if (dates && dates.length === 2) {
                                    // Get the first day of the month and last day of the month
                                    const startOfMonth =
                                      dates[0].startOf('month');
                                    const endOfMonth = dates[1].endOf('month');

                                    // Update the form with the correct start and end dates
                                    const form = newFormRef.current;
                                    if (form) {
                                      form.setFieldsValue({
                                        dateRange: [startOfMonth, endOfMonth],
                                      });
                                    }

                                    // Check for date warnings if activity is selected
                                    if (selectedActivity) {
                                      const startDate = PlainDate.from({
                                        year: startOfMonth.year,
                                        month: startOfMonth.month,
                                        day: startOfMonth.day,
                                      });
                                      const endDate = PlainDate.from({
                                        year: endOfMonth.year,
                                        month: endOfMonth.month,
                                        day: endOfMonth.day,
                                      });

                                      setShowDateWarning(
                                        await validateAllocationDate(
                                          startDate,
                                          endDate,
                                          selectedActivity?.startDate,
                                          selectedActivity?.endDate
                                        )
                                      );
                                    }
                                  }
                                }}
                              />
                            ) : (
                              <RangePicker
                                style={{ width: '100%' }}
                                format="YYYY-MM-DD"
                                onChange={async (dates) => {
                                  if (
                                    selectedActivity &&
                                    dates &&
                                    dates[0] &&
                                    dates[1]
                                  ) {
                                    // Handle Luxon DateTime format
                                    const startDate = PlainDate.from({
                                      year: dates[0].year,
                                      month: dates[0].month,
                                      day: dates[0].day,
                                    });
                                    const endDate = PlainDate.from({
                                      year: dates[1].year,
                                      month: dates[1].month,
                                      day: dates[1].day,
                                    });

                                    // Update the date warning state
                                    setShowDateWarning(
                                      await validateAllocationDate(
                                        startDate,
                                        endDate,
                                        selectedActivity?.startDate,
                                        selectedActivity?.endDate
                                      )
                                    );
                                  }
                                }}
                              />
                            )}
                          </Form.Item>
                        );
                      }}
                    </Form.Item>
                  </>
                );
              }

              return (
                <>
                  <Form.Item
                    name="totalHours"
                    label={t('{{allocations.methodTotalHours}}')}
                    rules={[
                      {
                        required: true,
                        message: t('{{allocations.errorEnterTotalHours}}'),
                      },
                    ]}
                  >
                    <InputNumber min={1} max={1000} style={{ width: '100%' }} />
                  </Form.Item>

                  <Form.Item
                    name="workingDays"
                    label={t('{{allocations.fieldWorkingDays}}')}
                  >
                    <Select
                      mode="multiple"
                      placeholder={t(
                        '{{allocations.placeholderSelectWorkingDays}}'
                      )}
                      style={{ width: '100%' }}
                    >
                      <Option value={EvstDayOfWeek.Monday}>
                        {t('{{allocations.dayMonday}}')}
                      </Option>
                      <Option value={EvstDayOfWeek.Tuesday}>
                        {t('{{allocations.dayTuesday}}')}
                      </Option>
                      <Option value={EvstDayOfWeek.Wednesday}>
                        {t('{{allocations.dayWednesday}}')}
                      </Option>
                      <Option value={EvstDayOfWeek.Thursday}>
                        {t('{{allocations.dayThursday}}')}
                      </Option>
                      <Option value={EvstDayOfWeek.Friday}>
                        {t('{{allocations.dayFriday}}')}
                      </Option>
                    </Select>
                  </Form.Item>

                  <Form.Item
                    name="dateRange"
                    label={t('{{allocations.fieldDateRange}}')}
                    rules={[
                      {
                        required: true,
                        message: t('{{allocations.errorSelectDateRange}}'),
                      },
                    ]}
                  >
                    <RangePicker
                      style={{ width: '100%' }}
                      format="YYYY-MM-DD"
                      onChange={async (dates) => {
                        if (selectedActivity && dates && dates[0] && dates[1]) {
                          // Handle Luxon DateTime format
                          const startDate = PlainDate.from({
                            year: dates[0].year,
                            month: dates[0].month,
                            day: dates[0].day,
                          });
                          const endDate = PlainDate.from({
                            year: dates[1].year,
                            month: dates[1].month,
                            day: dates[1].day,
                          });

                          // Update the date warning state
                          setShowDateWarning(
                            await validateAllocationDate(
                              startDate,
                              endDate,
                              selectedActivity?.startDate,
                              selectedActivity?.endDate
                            )
                          );
                        }
                      }}
                    />
                  </Form.Item>
                </>
              );
            }}
          </Form.Item>

          <Form.Item name="notes" label={t('{{allocations.fieldNotes}}')}>
            <Input.TextArea
              rows={4}
              placeholder={t('{{allocations.placeholderNotes}}')}
            />
          </Form.Item>
        </Form>
      </Spin>
    </Modal>
  );
};

export default CreateModal;
