{"type": "react", "componentUrn": "urn:evst:everest:psa:artifact/ui/tsx:modules/allocations/pages/allocations/Allocations", "backendAccess": {"urn:evst:everest:psa:presentation:modules/allocations/pages/allocations/Allocations": ["Allocations/view", "Allocations/add", "Allocations/change", "Allocations/remove", "Members/view", "ProjectPositions/view", "Projects/view", "ProjectActivities/view", "validate<PERSON><PERSON><PERSON>", "GetCurrentEmployeeId", "GetTimeOff", "validateAllocationDate"], "urn:evst:everest:psa:presentation:lib/permissions/PsaPermissionPreflight": ["CheckEditPermission"]}, "variants": {"management": {"title": "Allocation management", "description": "Full management access to allocations"}}}