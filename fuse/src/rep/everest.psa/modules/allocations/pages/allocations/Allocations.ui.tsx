// @i18n:psa
import React, { useEffect, useState, useRef, useMemo } from 'react';
import { uniq } from 'lodash';
import {
  Button,
  Spin,
  message,
  Modal,
  Select,
  Space,
  Tooltip,
  Alert,
  Switch,
} from 'antd';
import type { FormInstance } from 'antd/es/form';
import { useTranslation } from 'react-i18next';
import { DateTime } from 'luxon';
import { AllocationsUI } from '@pkg/everest.psa/types/presentations/modules/allocations/pages/allocations/Allocations.ui';
import { PsaPermissionPreflightUI } from '@pkg/everest.psa/types/presentations/lib/permissions/PsaPermissionPreflight.ui';
import {
  Allocation,
  Member,
  Project,
  ProjectActivity,
  ProjectPosition,
  WeekInfo,
  MonthInfo,
  TimeUnit,
  ViewType,
  Metric,
} from './components/types.ui';
import { EvstTimeAllocationMethod } from '@pkg/everest.timetracking/types/enums/TimeAllocationMethod';
import { EvstDayOfWeek } from '@pkg/everest.appserver/types/enums/DayOfWeek';
import { EvstPsaProjectStatus } from '@pkg/everest.psa/types/enums/PsaProjectStatus';
import {
  convertTotalHoursToConvertedHours,
  convertConvertedHoursToTotalHours,
} from '@pkg/everest.timetracking/public/utils/allocationUtils';
import { Decimal } from '@everestsystems/decimal';
import { generateWeeks, generateMonths } from './components/utils.ui';
import {
  formatDateToYYYYMMDD,
  stringToPlainDate,
} from '@pkg/everest.psa/modules/util/date.ui';
import TimeUnitSelector from './components/TimeUnitSelector.ui';
import AllocationTable from './components/AllocationTable.ui';
import EditModal from './components/EditModal.ui';
import CreateModal from './components/CreateModal.ui';

import {
  Icon,
  Typography as EverestTypography,
} from '@everestsystems/design-system';
import { PlainDate } from '@everestsystems/datetime';
const { H2 } = EverestTypography;

/**
 * Interface for budget validation warning
 */
interface BudgetValidationWarning {
  message: string;
  projectName?: string;
}

const AllocationsPage = ({ pageState }) => {
  const { t } = useTranslation();
  pageState.tabTitle = t('{{allocations.pageTitle}}');
  const [loading, setLoading] = useState(true);
  const [members, setMembers] = useState<Member[]>([]);
  const [allocations, setAllocations] = useState<Allocation[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [expandedRowKeys, setExpandedRowKeys] = useState<number[]>([]);
  const [weeks, setWeeks] = useState<WeekInfo[]>([]);
  const [isNewAllocationModalVisible, setIsNewAllocationModalVisible] =
    useState(false);
  const [isEditAllocationModalVisible, setIsEditAllocationModalVisible] =
    useState(false);
  const [selectedProject, setSelectedProject] = useState<number | null>(null);
  const [editingAllocation, setEditingAllocation] = useState<Allocation | null>(
    null
  );
  const [currentDate, setCurrentDate] = useState<Date>(new Date());
  const [timeUnit, setTimeUnit] = useState<TimeUnit>('week');
  const [viewType, setViewType] = useState<ViewType>('members');
  const [months, setMonths] = useState<MonthInfo[]>([]);

  // Filter states
  const [selectedMemberFilter, setSelectedMemberFilter] = useState<number[]>(
    []
  );
  const [selectedProjectFilter, setSelectedProjectFilter] = useState<number[]>(
    []
  );
  const [selectedProjectStatus, setSelectedProjectStatus] = useState<
    EvstPsaProjectStatus[]
  >([]);
  const [selectedMetric, setSelectedMetric] = useState<Metric>('percentage');
  const [previousMembersMetric, setPreviousMembersMetric] =
    useState<Metric>('percentage');
  const [showTimeOff, setShowTimeOff] = useState(false);

  const newFormRef = useRef<FormInstance | null>(null);
  const editFormRef = useRef<FormInstance | null>(null);

  // State for budget validation warnings
  const [validationWarnings, setValidationWarnings] = useState<
    BudgetValidationWarning[]
  >([]);
  const [validationLoading, setValidationLoading] = useState(false);

  // State for permission checks
  const [currentEmployeeId, setCurrentEmployeeId] = useState<number | null>(
    null
  );
  const [projectPermissions, setProjectPermissions] = useState<
    Map<number, { hasEditPermission: boolean }>
  >(new Map());

  // Update weeks or months when currentDate or timeUnit changes
  useEffect(() => {
    // Update the weeks or months based on the current date and time unit
    if (timeUnit === 'week') {
      setWeeks(generateWeeks(currentDate));
    } else {
      setMonths(generateMonths(currentDate));
    }
  }, [currentDate, timeUnit]);

  // Initial data load for projects and members - runs only once on component mount
  useEffect(() => {
    const loadInitialData = async () => {
      setLoading(true);
      try {
        await fetchCurrentEmployeeId();
        await fetchProjects();
        await fetchMembers();
      } catch (error) {
        console.error('Error fetching initial data:', error);
        message.error(t('{{allocations.errorLoadData}}'));
      } finally {
        setLoading(false);
      }
    };

    loadInitialData();
  }, []);

  // Separate useEffect to fetch allocations after weeks/months are updated
  useEffect(() => {
    if (
      (timeUnit === 'week' && weeks.length > 0) ||
      (timeUnit === 'month' && months.length > 0)
    ) {
      fetchAllocations();
    }
  }, [weeks, months]);

  // Refetch members when showTimeOff changes to update time off data
  useEffect(() => {
    if (members.length > 0) {
      fetchMembers();
    }
  }, [weeks, months, showTimeOff]);

  // Navigate to previous period (week or month)
  const goToPrevious = () => {
    const newDate = new Date(currentDate);
    if (timeUnit === 'week') {
      newDate.setDate(newDate.getDate() - 7);
    } else {
      newDate.setMonth(newDate.getMonth() - 1);
    }
    setCurrentDate(newDate);
  };

  // Navigate to next period (week or month)
  const goToNext = () => {
    const newDate = new Date(currentDate);
    if (timeUnit === 'week') {
      newDate.setDate(newDate.getDate() + 7);
    } else {
      newDate.setMonth(newDate.getMonth() + 1);
    }
    setCurrentDate(newDate);
  };

  // Navigate to current period (today)
  const goToToday = () => {
    setCurrentDate(new Date());
  };

  // Handle time unit change
  const handleTimeUnitChange = (e: any) => {
    setTimeUnit(e.target.value);
  };

  // Handle view type change
  const handleViewTypeChange = (newViewType: ViewType) => {
    // Store current metric if switching away from members view
    if (viewType === 'members' && newViewType !== 'members') {
      setPreviousMembersMetric(selectedMetric);
    }

    setViewType(newViewType);
    // Clear expanded rows when switching views
    setExpandedRowKeys([]);

    // Set metric based on view type
    if (newViewType === 'members') {
      // Restore previous members metric
      setSelectedMetric(previousMembersMetric);
    } else {
      // Projects view always uses hours
      setSelectedMetric('hours');
    }
  };

  const getCurrentDateRange = () => {
    let periodStartDate: Date;
    let periodEndDate: Date;

    if (timeUnit === 'week' && weeks.length > 0) {
      periodStartDate = weeks[0].startDate;
      periodEndDate = weeks[weeks.length - 1].endDate;
    } else if (timeUnit === 'month' && months.length > 0) {
      periodStartDate = months[0].startDate;
      periodEndDate = months[months.length - 1].endDate;
    } else {
      periodStartDate = new Date(currentDate);
      periodStartDate.setMonth(periodStartDate.getMonth() - 1);
      periodEndDate = new Date(currentDate);
      periodEndDate.setMonth(periodEndDate.getMonth() + 1);
    }

    return { periodStartDate, periodEndDate };
  };

  const fetchMembers = async (projectId?: number) => {
    try {
      const request = AllocationsUI.client.createEntitySetGetRequest('Members');

      // Filter members by projectId if provided (for form dropdowns)
      if (projectId) {
        // Use the correct filter structure for OData
        request.setFilter({
          property: 'projectId',
          comparator: 'eq',
          value: projectId,
        });
      }

      const response = await request.execute();

      if (response && response.entities) {
        const fetchedMembers = response.entities as Member[];

        // If we're fetching all members (no projectId filter), update the main members state
        if (!projectId) {
          const { periodStartDate, periodEndDate } = getCurrentDateRange();
          console.log(
            'Fetching members for period:',
            periodStartDate,
            periodEndDate
          );

          // Only fetch time off data if showTimeOff is true
          if (showTimeOff) {
            // Get employee IDs for TimeOff lookup
            const employeeIds = uniq(
              fetchedMembers.map((member) => member.employeeId)
            );

            try {
              // Call the GetTimeOff action to get timeoff information
              const timeOffRequest =
                AllocationsUI.client.createFunctionExecuteRequest('GetTimeOff');
              timeOffRequest.setInput({
                employeeIds,
                startDate: periodStartDate,
                endDate: periodEndDate,
              });

              const timeOffResponse = await timeOffRequest.execute();

              // Create a map of employee ID to timeoff information
              const timeOffMap = new Map();
              for (const item of timeOffResponse.output.result) {
                timeOffMap.set(item.employeeId, {
                  holidays: item.holidays || [],
                  absences: item.absences || [],
                });
              }

              // Add timeoff information to each member
              for (const member of fetchedMembers) {
                if (timeOffMap.has(member.employeeId)) {
                  const timeOff = timeOffMap.get(member.employeeId);
                  member.timeOff = timeOff;
                }
              }
            } catch (error) {
              console.error('Failed to get timeoff information', error);
              // Continue without timeoff information
            }
          }
          console.log('Fetched members:', fetchedMembers);
          setMembers(fetchedMembers);
        }

        return fetchedMembers;
      } else {
        return [];
      }
    } catch (error) {
      console.error('Error fetching members:', error);
      throw error;
    }
  };

  const fetchAllocations = async () => {
    try {
      const request =
        AllocationsUI.client.createEntitySetGetRequest('Allocations');

      const { periodStartDate, periodEndDate } = getCurrentDateRange();

      // Format dates to YYYY-MM-DD for OData filtering
      const formattedStartDate = formatDateToYYYYMMDD(periodStartDate);
      const formattedEndDate = formatDateToYYYYMMDD(periodEndDate);

      // Set filter to get allocations that overlap with the displayed period
      // An allocation overlaps if:
      // - Its start date is before or equal to the end of the displayed period AND
      // - Its end date is after or equal to the start of the displayed period
      request.setFilter([
        {
          property: 'startDate',
          comparator: 'le',
          value: formattedEndDate,
        },
        'and',
        {
          property: 'endDate',
          comparator: 'ge',
          value: formattedStartDate,
        },
      ]);

      const response = await request.execute();
      console.log('Allocations response:', response);

      if (response && response.entities) {
        setAllocations(response.entities as Allocation[]);
        console.log('Allocations data:', response.entities);
      }
    } catch (error) {
      console.error('Error fetching allocations:', error);
    }
  };

  // Fetch current employee ID
  const fetchCurrentEmployeeId = async () => {
    try {
      const client = AllocationsUI.client;
      const request = client.createFunctionExecuteRequest(
        'GetCurrentEmployeeId'
      );
      const response = await request.execute();
      setCurrentEmployeeId(response.output.employeeId);
    } catch (error) {
      console.error('Error fetching current employee ID:', error);
    }
  };

  // Check edit permission for a specific project
  const checkEditPermission = async (
    projectId: number,
    psaProjectId: number
  ) => {
    try {
      const response = await PsaPermissionPreflightUI.client
        .createFunctionExecuteRequest('CheckEditPermission')
        .setInput({ psaProjectId })
        .execute();

      setProjectPermissions(
        (prev) =>
          new Map(
            prev.set(projectId, {
              hasEditPermission: response.output.hasEditPermission,
            })
          )
      );

      return response.output.hasEditPermission;
    } catch (error) {
      console.error('Error checking edit permission:', error);
      return false;
    }
  };

  // Helper function to check if user can modify an allocation
  const canModifyAllocation = (allocation: Allocation) => {
    if (!allocation.projectId || currentEmployeeId === null) {
      return false;
    }

    const permission = projectPermissions.get(allocation.projectId);
    if (!permission) {
      return false;
    }

    // If user has edit permission, they can modify all allocations
    if (permission.hasEditPermission) {
      return true;
    }

    // If user has view permission, they can only modify their own allocations
    return allocation.memberEmployeeId === currentEmployeeId;
  };

  const fetchProjects = async () => {
    try {
      const request =
        AllocationsUI.client.createEntitySetGetRequest('Projects');

      const response = await request.execute();

      if (response && response.entities) {
        const projectsData = response.entities as Project[];
        setProjects(projectsData);

        // Check permissions for all projects
        await Promise.all(
          projectsData.map((project) =>
            checkEditPermission(project.id, project.psaProjectId)
          )
        );
      }
    } catch (error) {
      console.error('Error fetching projects:', error);
      throw error;
    }
  };

  const fetchProjectActivities = async (
    projectId?: number,
    memberId?: number,
    projectPositionId?: number
  ) => {
    try {
      const request =
        AllocationsUI.client.createEntitySetGetRequest('ProjectActivities');

      // Create filters based on provided parameters
      let filter: any = null;

      // Apply filters based on what parameters were provided
      if (projectId != null && memberId != null && projectPositionId != null) {
        // Filter by project, member, and position (most specific)
        filter = [
          {
            property: 'projectId',
            comparator: 'eq',
            value: projectId,
          },
          'and',
          {
            property: 'memberId',
            comparator: 'eq',
            value: memberId,
          },
          'and',
          {
            property: 'projectPositionId',
            comparator: 'eq',
            value: projectPositionId,
          },
        ];
        request.setFilter(filter);
      } else if (projectId != null) {
        // Only project filter
        filter = {
          property: 'projectId',
          comparator: 'eq',
          value: projectId,
        };
        request.setFilter(filter);
      }

      const response = await request.execute();

      return response && response.entities
        ? (response.entities as ProjectActivity[])
        : [];
    } catch (error) {
      console.error('Error fetching project activities:', error);
      throw error;
    }
  };

  const fetchProjectPositions = async (projectId?: number) => {
    try {
      const request =
        AllocationsUI.client.createEntitySetGetRequest('ProjectPositions');

      // Filter positions by projectId if provided
      if (projectId) {
        request.setFilter({
          property: 'projectId',
          comparator: 'eq',
          value: projectId,
        });
      }

      const response = await request.execute();

      return response && response.entities
        ? (response.entities as ProjectPosition[])
        : [];
    } catch (error) {
      console.error('Error fetching project positions:', error);
      throw error;
    }
  };

  // Fetch budget validation warnings for a project
  const fetchBudgetValidationWarnings = async (projectId?: number) => {
    if (!projectId) {
      setValidationWarnings([]);
      return;
    }

    try {
      setValidationLoading(true);

      // Create a function request for validateBudget
      const client = AllocationsUI.client;
      const request = client.createFunctionExecuteRequest('validateBudget');
      request.setInput({
        projectId: projectId,
      });
      const response = await request.execute();

      setValidationWarnings(
        response.output.warnings as BudgetValidationWarning[]
      );
    } catch (error) {
      console.error('Error fetching budget validation warnings:', error);
      message.error(t('{{allocations.error.validationWarnings}}'));
    } finally {
      setValidationLoading(false);
    }
  };

  // Function to validate if allocation dates are outside activity date range
  const validateAllocationDate = async (
    allocationStartDate: PlainDate,
    allocationEndDate: PlainDate,
    activityStartDate: PlainDate,
    activityEndDate: PlainDate
  ): Promise<boolean> => {
    try {
      // Create an action request for validateAllocationDate
      const client = AllocationsUI.client;
      const request = client.createFunctionExecuteRequest(
        'validateAllocationDate'
      );
      request.setInput({
        allocationStartDate,
        allocationEndDate,
        activityStartDate,
        activityEndDate,
      });
      const response = await request.execute();

      return response.output?.isOutsideDateRange || false;
    } catch (error) {
      console.error('Error validating allocation dates:', error);
      return false;
    }
  };

  const toggleExpandRow = (memberId: number) => {
    setExpandedRowKeys((prevKeys) => {
      return prevKeys.includes(memberId)
        ? prevKeys.filter((key) => key !== memberId)
        : [...prevKeys, memberId];
    });
  };

  // New Allocation Modal functions
  const showNewAllocationModal = () => {
    setIsNewAllocationModalVisible(true);
    // Reset form fields
    if (
      newFormRef.current &&
      typeof newFormRef.current.resetFields === 'function'
    ) {
      newFormRef.current.resetFields();
    }
  };

  const handleNewAllocationCancel = () => {
    setIsNewAllocationModalVisible(false);
    setSelectedProject(null);
    // Reset form fields
    if (
      newFormRef.current &&
      typeof newFormRef.current.resetFields === 'function'
    ) {
      newFormRef.current.resetFields();
    }
  };

  // Edit Allocation Modal functions
  // Effect to set form values when the edit modal becomes visible
  useEffect(() => {
    if (
      isEditAllocationModalVisible &&
      editingAllocation &&
      editFormRef.current
    ) {
      const loadProjectData = async () => {
        setLoading(true);
        try {
          // Get the project ID from the allocation
          const projectId = editingAllocation.projectId;

          if (projectId) {
            setSelectedProject(projectId);

            // Fetch data for this project
            await fetchMembers(projectId);
            await fetchProjectPositions(projectId);

            // Pre-populate form fields with existing allocation data including date range
            if (typeof editFormRef.current?.setFieldsValue === 'function') {
              // Use setTimeout to ensure the form is fully rendered
              setTimeout(() => {
                if (
                  editFormRef.current &&
                  editingAllocation.startDate &&
                  editingAllocation.endDate
                ) {
                  // Convert dates to Luxon DateTime objects
                  const startDate = DateTime.fromISO(
                    formatDateToYYYYMMDD(editingAllocation.startDate)
                  );
                  const endDate = DateTime.fromISO(
                    formatDateToYYYYMMDD(editingAllocation.endDate)
                  );

                  // Determine which method to show and how to populate fields
                  const method = (editingAllocation.method ||
                    EvstTimeAllocationMethod.TotalHours) as EvstTimeAllocationMethod;
                  const timeUnit =
                    method === EvstTimeAllocationMethod.TotalHours
                      ? undefined
                      : method;

                  // For hours per day/week/month, calculate the converted hours
                  const convertedHours = convertTotalHoursToConvertedHours(
                    editingAllocation.startDate,
                    editingAllocation.endDate,
                    Number(editingAllocation.totalHours),
                    method,
                    editingAllocation.workingDays as EvstDayOfWeek[]
                  );

                  editFormRef.current.setFieldsValue({
                    memberId: editingAllocation.memberId,
                    memberEmployeeId: editingAllocation.memberEmployeeId,
                    projectId: projectId,
                    projectActivityId: editingAllocation.projectActivityId,
                    dateRange: [startDate, endDate],
                    method:
                      method === EvstTimeAllocationMethod.TotalHours
                        ? EvstTimeAllocationMethod.TotalHours
                        : EvstTimeAllocationMethod.HoursPerDay,
                    timeUnit: timeUnit,
                    totalHours:
                      method === EvstTimeAllocationMethod.TotalHours
                        ? editingAllocation.totalHours
                        : undefined,
                    convertedHours: convertedHours,
                    workingDays: editingAllocation.workingDays || undefined,
                    notes: editingAllocation.notes,
                  });
                }
              }, 100);
            }
          }
        } catch (error) {
          console.error('Error loading project data for edit:', error);
          message.error(t('Failed to load project data. Please try again.'));
        } finally {
          setLoading(false);
        }
      };

      loadProjectData();
    }
  }, [isEditAllocationModalVisible, editingAllocation]);

  const handleEditAllocation = async (allocation: Allocation) => {
    console.log('Edit allocation:', allocation);

    // Find the project ID for the activity if not already set
    if (!allocation.projectId && allocation.projectActivityId) {
      try {
        const allActivities = await fetchProjectActivities();
        const activity = allActivities.find(
          (a) => a.id === allocation.projectActivityId
        );
        if (activity && activity.projectId) {
          allocation.projectId = activity.projectId;
        }
      } catch (error) {
        console.error('Error finding project for activity:', error);
      }
    }

    // Store the allocation for update/delete operations
    setEditingAllocation(allocation);
    setSelectedProject(allocation.projectId || null);

    // Show the modal - form values will be set by the useEffect
    setIsEditAllocationModalVisible(true);
  };

  const handleEditAllocationCancel = () => {
    setIsEditAllocationModalVisible(false);
    setEditingAllocation(null);
    setSelectedProject(null);

    // Reset form fields
    if (
      editFormRef.current &&
      typeof editFormRef.current.resetFields === 'function'
    ) {
      editFormRef.current.resetFields();
    }
  };

  const handleAllocationUpdateDragAndDrop = async ({
    barId,
    newStartDate,
    newEndDate,
  }) => {
    try {
      setLoading(true);

      // Extract allocation ID from barId (format: "allocation-{id}-{index}")
      const barIdParts = barId.split('-');
      if (barIdParts.length < 2) {
        throw new Error('Invalid bar ID format');
      }

      const allocationId = Number.parseInt(barIdParts[1], 10);
      if (Number.isNaN(allocationId)) {
        throw new TypeError('Invalid allocation ID');
      }

      // Find the allocation data from the current allocations state
      const allocation = allocations.find((a) => a.id === allocationId);
      if (!allocation) {
        throw new Error('Allocation not found');
      }

      // Format the new dates
      const formattedStartDate = formatDateToYYYYMMDD(newStartDate);
      const formattedEndDate = formatDateToYYYYMMDD(newEndDate);

      let totalHours = Number(allocation.totalHours);
      if (allocation.method !== EvstTimeAllocationMethod.TotalHours) {
        const convertedHours = convertTotalHoursToConvertedHours(
          allocation.startDate,
          allocation.endDate,
          Number(allocation.totalHours),
          allocation.method,
          allocation.workingDays
        );
        totalHours = convertConvertedHoursToTotalHours(
          stringToPlainDate(formattedStartDate),
          stringToPlainDate(formattedEndDate),
          convertedHours,
          allocation.method,
          allocation.workingDays
        );
      }

      const updatedAllocation: AllocationsUI.EntitySets.Allocations.Patch.Data =
        {
          startDate: stringToPlainDate(formattedStartDate),
          endDate: stringToPlainDate(formattedEndDate),
          totalHours: new Decimal(totalHours),
        };

      // Use the presentation layer to update the allocation
      const request =
        AllocationsUI.client.createEntitySetPatchRequest('Allocations');
      request.setId(allocation.id);
      request.setData(updatedAllocation);
      await request.execute();

      message.success(t('{{allocations.successUpdate}}'));

      // Refresh allocations data
      await fetchAllocations();

      // Fetch budget validation warnings
      if (allocation.projectId) {
        await fetchBudgetValidationWarnings(allocation.projectId);
      }
    } catch (error) {
      console.error('Error updating allocation:', error);
      message.error(error.message || t('{{allocations.errorUpdate}}'));
    } finally {
      setLoading(false);
    }
  };

  const handleEditAllocationSubmit = async () => {
    try {
      // Validate form fields
      if (!editFormRef.current || !editingAllocation || !editingAllocation.id) {
        return;
      }

      if (typeof editFormRef.current.validateFields !== 'function') {
        message.error(t('{{allocations.errorFormValidation}}'));
        return;
      }

      const values = await editFormRef.current.validateFields();

      setLoading(true);

      // Safely get the date objects from the range picker and format them
      let formattedStartDate, formattedEndDate;

      try {
        if (
          !values.dateRange ||
          !Array.isArray(values.dateRange) ||
          values.dateRange.length < 2
        ) {
          throw new Error('Invalid date range');
        }

        formattedStartDate = formatDateToYYYYMMDD(values.dateRange[0]);
        formattedEndDate = formatDateToYYYYMMDD(values.dateRange[1]);

        console.log('Formatted dates:', {
          formattedStartDate,
          formattedEndDate,
        });
      } catch (error) {
        console.error('Error formatting dates:', error);
        message.error(t('{{allocations.errorInvalidDateFormat}}'));
        setLoading(false);
        return;
      }

      // Determine the method and calculate totalHours
      let totalHours = values.totalHours;
      let method = values.method;

      // If using hours per day/week/month, convert to totalHours
      if (method !== EvstTimeAllocationMethod.TotalHours && values.timeUnit) {
        const startDate = stringToPlainDate(formattedStartDate);
        const endDate = stringToPlainDate(formattedEndDate);

        // Use the selected time unit (day, week, month) and the utility to convert
        method = values.timeUnit; // This will be HoursPerDay, HoursPerWeek, or HoursPerMonth
        const totalHoursNumber = convertConvertedHoursToTotalHours(
          startDate,
          endDate,
          values.convertedHours,
          method,
          values.workingDays as EvstDayOfWeek[]
        );
        // Convert the number to Decimal
        totalHours = totalHoursNumber;
      }

      const updatedAllocation: AllocationsUI.EntitySets.Allocations.Patch.Data =
        {
          memberId: values.memberId,
          projectActivityId: values.projectActivityId,
          startDate: stringToPlainDate(formattedStartDate),
          endDate: stringToPlainDate(formattedEndDate),
          totalHours: new Decimal(totalHours),
          notes: values.notes,
          method: method,
          workingDays: values.workingDays,
          // fields for permission check
          memberEmployeeId: values.memberEmployeeId,
          projectId: values.projectId || selectedProject,
        };

      // Use the presentation layer to update the allocation
      const request =
        AllocationsUI.client.createEntitySetPatchRequest('Allocations');
      request.setId(editingAllocation.id);
      request.setData(updatedAllocation);
      await request.execute();

      message.success(t('{{allocations.successUpdate}}'));
      setIsEditAllocationModalVisible(false);
      setEditingAllocation(null);
      setSelectedProject(null);

      // Reset form fields
      if (
        editFormRef.current &&
        typeof editFormRef.current.resetFields === 'function'
      ) {
        editFormRef.current.resetFields();
      }

      // Refresh allocations data
      await fetchAllocations();

      // Fetch budget validation warnings for the project
      const projectId = values.projectId || selectedProject;
      if (projectId) {
        await fetchBudgetValidationWarnings(projectId);
      }
    } catch (error) {
      console.error('Error updating allocation:', error);
      // Display the error message directly
      message.error(error.message || t('{{allocations.errorUpdate}}'));
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteAllocation = async () => {
    if (!editingAllocation || !editingAllocation.id) {
      return;
    }

    // Show confirmation dialog
    Modal.confirm({
      title: t('{{allocations.deleteTitle}}'),
      content: t('{{allocations.deleteConfirmation}}'),
      okText: t('{{allocations.deleteConfirm}}'),
      okType: 'danger',
      cancelText: t('{{allocations.cancel}}'),
      okButtonProps: {
        style: {
          borderRadius: '10px',
          boxShadow: '0 2px 6px rgba(255, 0, 0, 0.1)',
          padding: '0 16px',
          height: '34px',
        },
      },
      cancelButtonProps: {
        style: {
          borderRadius: '10px',
          padding: '0 16px',
          height: '34px',
        },
      },
      onOk: async () => {
        try {
          setLoading(true);

          // Use the presentation layer to delete the allocation
          const request =
            AllocationsUI.client.createEntitySetDeleteRequest('Allocations');
          request.setId(editingAllocation.id);
          await request.execute();

          message.success(t('{{allocations.successDelete}}'));
          setIsEditAllocationModalVisible(false);
          setEditingAllocation(null);
          setSelectedProject(null);

          // Reset form fields
          if (
            editFormRef.current &&
            typeof editFormRef.current.resetFields === 'function'
          ) {
            editFormRef.current.resetFields();
          }

          // Refresh allocations data
          await fetchAllocations();

          // Fetch budget validation warnings
          if (editingAllocation.projectId) {
            await fetchBudgetValidationWarnings(editingAllocation.projectId);
          }
        } catch (error) {
          console.error('Error deleting allocation:', error);
          // Display the error message directly
          message.error(error.message || t('{{allocations.errorDelete}}'));
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const handleProjectChange = async (projectId: number) => {
    setSelectedProject(projectId);

    // Show loading state
    setLoading(true);

    try {
      // Update form field - determine which form to update based on which modal is open
      if (isNewAllocationModalVisible && newFormRef.current) {
        if (typeof newFormRef.current.setFieldsValue === 'function') {
          newFormRef.current.setFieldsValue({
            projectId: projectId, // Ensure projectId is set in the form values
            projectActivityId: undefined,
            memberId: undefined,
          });
        }
      } else if (
        isEditAllocationModalVisible &&
        editFormRef.current &&
        typeof editFormRef.current.setFieldsValue === 'function'
      ) {
        editFormRef.current.setFieldsValue({
          projectId: projectId, // Ensure projectId is set in the form values
          projectActivityId: undefined,
          memberId: undefined,
        });
      }
    } catch (error) {
      console.error('Error fetching project data:', error);
      message.error(t('{{allocations.errorLoadProjectData}}'));
    } finally {
      setLoading(false);
    }
  };

  const handleNewAllocationSubmit = async () => {
    try {
      // Validate form fields
      if (!newFormRef.current) {
        return;
      }

      if (typeof newFormRef.current.validateFields !== 'function') {
        message.error(t('Form validation not available'));
        return;
      }

      const values = await newFormRef.current.validateFields();

      setLoading(true);

      // Get the date objects from the range picker and format them
      const formattedStartDate = formatDateToYYYYMMDD(values.dateRange[0]);
      const formattedEndDate = formatDateToYYYYMMDD(values.dateRange[1]);

      // Determine the method and calculate totalHours
      let totalHours = values.totalHours;
      let method = values.method;

      // If using hours per day/week/month, convert to totalHours
      if (method !== EvstTimeAllocationMethod.TotalHours && values.timeUnit) {
        const startDate = stringToPlainDate(formattedStartDate);
        const endDate = stringToPlainDate(formattedEndDate);

        // Use the selected time unit (day, week, month) and the utility to convert
        method = values.timeUnit; // This will be HoursPerDay, HoursPerWeek, or HoursPerMonth
        const totalHoursNumber = convertConvertedHoursToTotalHours(
          startDate,
          endDate,
          values.convertedHours,
          method,
          values.workingDays as EvstDayOfWeek[]
        );
        totalHours = totalHoursNumber;
      }

      const newAllocation: AllocationsUI.EntitySets.Allocations.Post.Data = {
        memberId: values.memberId,
        projectActivityId: values.projectActivityId,
        startDate: stringToPlainDate(formattedStartDate),
        endDate: stringToPlainDate(formattedEndDate),
        totalHours: new Decimal(totalHours),
        notes: values.notes,
        method: method,
        workingDays: values.workingDays,
        // fields for permission check
        memberEmployeeId: values.memberEmployeeId,
        projectId: values.projectId,
      };

      const request =
        AllocationsUI.client.createEntitySetPostRequest('Allocations');
      request.setData(newAllocation);
      await request.execute();

      message.success(t('{{allocations.successCreate}}'));
      setIsNewAllocationModalVisible(false);
      setSelectedProject(null);

      // Reset form fields
      if (
        newFormRef.current &&
        typeof newFormRef.current.resetFields === 'function'
      ) {
        newFormRef.current.resetFields();
      }

      // Refresh allocations data
      await fetchAllocations();

      // Fetch budget validation warnings for the project
      const projectId = values.projectId || selectedProject;
      if (projectId) {
        await fetchBudgetValidationWarnings(projectId);
      }
    } catch (error) {
      console.error('Error creating allocation:', error);
      // Display the error message directly
      message.error(error.message || t('{{allocations.errorCreate}}'));
    } finally {
      setLoading(false);
    }
  };

  // Memoized filtered allocations based on selected filters
  const filteredAllocations = useMemo(() => {
    let filtered = allocations;

    // Filter by selected members (only in members view)
    if (viewType === 'members' && selectedMemberFilter.length > 0) {
      // Group members by employeeId to match the table logic
      const membersByEmployeeId: { [employeeId: string]: Member[] } = {};

      for (const member of members) {
        if (member.employeeId) {
          const employeeIdStr = String(member.employeeId);
          if (!membersByEmployeeId[employeeIdStr]) {
            membersByEmployeeId[employeeIdStr] = [];
          }
          membersByEmployeeId[employeeIdStr].push(member);
        }
      }

      // Get all member IDs that belong to selected employeeId groups
      const selectedMemberIds = new Set<number>();
      for (const selectedMemberId of selectedMemberFilter) {
        const selectedMember = members.find((m) => m.id === selectedMemberId);
        if (selectedMember && selectedMember.employeeId) {
          const employeeIdStr = String(selectedMember.employeeId);
          const membersInGroup = membersByEmployeeId[employeeIdStr] || [];
          for (const member of membersInGroup) {
            selectedMemberIds.add(member.id);
          }
        }
      }

      filtered = filtered.filter((allocation) =>
        selectedMemberIds.has(allocation.memberId)
      );
    }

    // Filter by selected projects
    if (selectedProjectFilter.length > 0) {
      filtered = filtered.filter(
        (allocation) =>
          allocation.projectId &&
          selectedProjectFilter.includes(allocation.projectId)
      );
    }

    // Filter by project status
    if (selectedProjectStatus.length > 0) {
      // Get projects that match the selected statuses
      const projectsWithSelectedStatus = projects.filter(
        (project) =>
          project.status && selectedProjectStatus.includes(project.status)
      );
      const projectIdsWithSelectedStatus = new Set(
        projectsWithSelectedStatus.map((p) => p.id)
      );

      filtered = filtered.filter(
        (allocation) =>
          allocation.projectId &&
          projectIdsWithSelectedStatus.has(allocation.projectId)
      );
    }

    return filtered;
  }, [
    allocations,
    viewType,
    selectedMemberFilter,
    selectedProjectFilter,
    selectedProjectStatus,
    members,
    projects,
  ]);

  // Memoized filtered projects based on selected filters
  const filteredProjects = useMemo(() => {
    let filtered = projects;

    // Filter by selected projects
    if (selectedProjectFilter.length > 0) {
      filtered = filtered.filter(
        (project) => project.id && selectedProjectFilter.includes(project.id)
      );
    }

    // Filter by project status
    if (selectedProjectStatus.length > 0) {
      filtered = filtered.filter(
        (project) =>
          project.status && selectedProjectStatus.includes(project.status)
      );
    }

    return filtered;
  }, [projects, selectedProjectFilter, selectedProjectStatus]);

  // Memoized filtered members based on selected filters
  const filteredMembers = useMemo(() => {
    let filtered = members;

    // Filter by selected members (only in members view)
    if (viewType === 'members' && selectedMemberFilter.length > 0) {
      // Group members by employeeId to match the table logic
      const membersByEmployeeId: { [employeeId: string]: Member[] } = {};

      for (const member of members) {
        if (member.employeeId) {
          const employeeIdStr = String(member.employeeId);
          if (!membersByEmployeeId[employeeIdStr]) {
            membersByEmployeeId[employeeIdStr] = [];
          }
          membersByEmployeeId[employeeIdStr].push(member);
        }
      }

      // Get all member IDs that belong to selected employeeId groups
      const selectedMemberIds = new Set<number>();
      for (const selectedMemberId of selectedMemberFilter) {
        const selectedMember = members.find((m) => m.id === selectedMemberId);
        if (selectedMember && selectedMember.employeeId) {
          const employeeIdStr = String(selectedMember.employeeId);
          const membersInGroup = membersByEmployeeId[employeeIdStr] || [];
          for (const member of membersInGroup) {
            selectedMemberIds.add(member.id);
          }
        }
      }

      filtered = filtered.filter((member) => selectedMemberIds.has(member.id));
    }

    // Filter by selected projects - keep only members that belong to selected projects
    if (selectedProjectFilter.length > 0) {
      filtered = filtered.filter(
        (member) =>
          member.projectId && selectedProjectFilter.includes(member.projectId)
      );
    }

    // Filter by project status - keep only members that belong to projects with selected statuses
    if (selectedProjectStatus.length > 0) {
      // Get projects that match the selected statuses
      const projectsWithSelectedStatus = projects.filter(
        (project) =>
          project.status && selectedProjectStatus.includes(project.status)
      );
      const projectIdsWithSelectedStatus = new Set(
        projectsWithSelectedStatus.map((p) => p.id)
      );

      filtered = filtered.filter(
        (member) =>
          member.projectId && projectIdsWithSelectedStatus.has(member.projectId)
      );
    }

    return filtered;
  }, [
    members,
    viewType,
    selectedMemberFilter,
    selectedProjectFilter,
    selectedProjectStatus,
    projects,
  ]);

  return (
    <div style={{ padding: 60 }}>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: 24,
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <H2 style={{ margin: 0 }}>{t('{{allocations.pageTitle}}')}</H2>

          {/* Time Unit Selector and Navigation */}
          <TimeUnitSelector
            timeUnit={timeUnit}
            weeks={weeks}
            months={months}
            handleTimeUnitChange={handleTimeUnitChange}
            goToPrevious={goToPrevious}
            goToNext={goToNext}
            goToToday={goToToday}
          />
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          {validationLoading && (
            <span style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <Icon name="loading" /> {t('{{allocations.validating}}')}
            </span>
          )}
          <Button
            type="primary"
            icon={<Icon type="add" useMaterialIcon />}
            onClick={showNewAllocationModal}
            style={{
              borderRadius: '12px',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
              padding: '0 16px',
              height: '36px',
              display: 'flex',
              alignItems: 'center',
            }}
          >
            {t('{{allocations.newButton}}')}
          </Button>
        </div>
      </div>

      {/* Filters Section */}
      <div
        style={{
          marginBottom: 16,
          padding: '12px 16px',
          backgroundColor: '#fafafa',
          borderRadius: '8px',
          border: '1px solid #f0f0f0',
        }}
      >
        <Space size="middle" wrap>
          {/* Members view filters */}
          {viewType === 'members' && (
            <>
              <div>
                <div
                  style={{ marginBottom: 4, fontSize: '12px', color: '#666' }}
                >
                  {t('{{allocations.filterMember}}')}
                </div>
                <Select
                  mode="multiple"
                  placeholder={t('{{allocations.filterAllMembers}}')}
                  style={{
                    width: 200,
                    height: 24,
                    overflow: 'hidden',
                  }}
                  size="small"
                  allowClear
                  showSearch
                  maxTagCount="responsive"
                  maxTagTextLength={8}
                  dropdownStyle={{ maxHeight: 200, overflow: 'auto' }}
                  value={selectedMemberFilter}
                  onChange={setSelectedMemberFilter}
                  filterOption={(input, option) =>
                    option?.children
                      ?.toString()
                      .toLowerCase()
                      .indexOf(input.toLowerCase()) >= 0
                  }
                >
                  {(() => {
                    // Group members by employeeId (same logic as in the table)
                    const membersByEmployeeId: {
                      [employeeId: string]: Member[];
                    } = {};

                    for (const member of members) {
                      if (member.employeeId) {
                        const employeeIdStr = String(member.employeeId);
                        if (!membersByEmployeeId[employeeIdStr]) {
                          membersByEmployeeId[employeeIdStr] = [];
                        }
                        membersByEmployeeId[employeeIdStr].push(member);
                      }
                    }

                    // Create a list of unique members (one per employeeId)
                    const uniqueMembers: Member[] = [];
                    for (const employeeIdStr of Object.keys(
                      membersByEmployeeId
                    )) {
                      if (membersByEmployeeId[employeeIdStr].length > 0) {
                        uniqueMembers.push(
                          membersByEmployeeId[employeeIdStr][0]
                        );
                      }
                    }

                    return uniqueMembers.map((member) => (
                      <Select.Option key={member.id} value={member.id}>
                        {member.name}
                      </Select.Option>
                    ));
                  })()}
                </Select>
              </div>
            </>
          )}

          {/* Project filter - shown in both views */}
          <div>
            <div style={{ marginBottom: 4, fontSize: '12px', color: '#666' }}>
              {t('{{allocations.filterProject}}')}
            </div>
            <Select
              mode="multiple"
              placeholder={t('{{allocations.filterAllProjects}}')}
              style={{
                width: 200,
                height: 24,
                overflow: 'hidden',
              }}
              size="small"
              allowClear
              showSearch
              maxTagCount="responsive"
              maxTagTextLength={8}
              dropdownStyle={{ maxHeight: 200, overflow: 'auto' }}
              filterOption={(input, option) =>
                String(option?.children || '')
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
              value={selectedProjectFilter}
              onChange={setSelectedProjectFilter}
            >
              {projects.map((project) => (
                <Select.Option key={project.id} value={project.id}>
                  {project.name}
                </Select.Option>
              ))}
            </Select>
          </div>

          {/* Project Status filter - shown in both views */}
          <div>
            <div style={{ marginBottom: 4, fontSize: '12px', color: '#666' }}>
              {t('{{allocations.filterStatus}}')}
            </div>
            <Select
              mode="multiple"
              placeholder={t('{{allocations.filterAllStatuses}}')}
              style={{
                width: 180,
                height: 24,
                overflow: 'hidden',
              }}
              size="small"
              allowClear
              maxTagCount="responsive"
              maxTagTextLength={6}
              dropdownStyle={{ maxHeight: 200, overflow: 'auto' }}
              value={selectedProjectStatus}
              onChange={setSelectedProjectStatus}
            >
              {Object.values(EvstPsaProjectStatus).map((status) => (
                <Select.Option key={status} value={status}>
                  {status}
                </Select.Option>
              ))}
            </Select>
          </div>

          {/* Metric filter - only shown in members view */}
          {viewType === 'members' && (
            <div>
              <div
                style={{
                  marginBottom: 4,
                  fontSize: '12px',
                  color: '#666',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px',
                }}
              >
                {t('{{allocations.filterMetric}}')}
                <Tooltip
                  title={
                    <div style={{ whiteSpace: 'pre-line' }}>
                      <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>
                        {t('{{allocations.metricExplanation}}')}
                      </div>
                      <div style={{ marginBottom: '6px' }}>
                        <strong>{t('{{allocations.hours}}')}</strong>:{' '}
                        {t('{{allocations.hoursExplanation}}')}
                      </div>
                      <div>
                        <strong>{t('{{allocations.percentage}}')}</strong>:{' '}
                        {t('{{allocations.percentageExplanation}}')}
                      </div>
                    </div>
                  }
                  placement="top"
                  overlayStyle={{ maxWidth: '300px' }}
                >
                  <Icon
                    type="help_outline"
                    useMaterialIcon
                    style={{
                      fontSize: '12px',
                      color: '#999',
                      cursor: 'help',
                    }}
                  />
                </Tooltip>
              </div>
              <Select
                placeholder={t('{{allocations.filterSelectMetric}}')}
                style={{ width: 120 }}
                size="small"
                value={selectedMetric}
                onChange={setSelectedMetric}
              >
                <Select.Option value="hours">
                  {t('{{allocations.hours}}')}
                </Select.Option>
                <Select.Option value="percentage">
                  {t('{{allocations.percentage}}')}
                </Select.Option>
              </Select>
            </div>
          )}
          {/* Time Off Switch */}
          {viewType === 'members' && (
            <div>
              <div
                style={{
                  marginBottom: 4,
                  fontSize: '12px',
                  color: '#666',
                  display: 'flex',
                  alignItems: 'center',
                }}
              >
                {t('{{allocations.timeOff}}')}
                <Tooltip
                  title={
                    <div>
                      <div>
                        <strong>{t('{{allocations.timeOff}}')}</strong>:{' '}
                        {t('{{allocations.timeOffExplanation}}')}
                      </div>
                    </div>
                  }
                  placement="top"
                  overlayStyle={{ maxWidth: '300px' }}
                >
                  <Icon
                    type="help_outline"
                    useMaterialIcon
                    style={{
                      fontSize: '12px',
                      color: '#999',
                      cursor: 'help',
                      marginLeft: '4px',
                    }}
                  />
                </Tooltip>
              </div>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <Switch
                  size="small"
                  checked={showTimeOff}
                  onChange={setShowTimeOff}
                />
              </div>
            </div>
          )}
        </Space>
      </div>

      {/* Budget Validation Warnings */}
      {validationWarnings.length > 0 && (
        <div style={{ marginBottom: '16px' }}>
          <Alert
            message={t('{{allocations.budgetValidationTitle}}')}
            description={
              <div>
                {validationWarnings.map((warning, index) => (
                  <div key={index} style={{ marginBottom: '4px' }}>
                    <Icon
                      type="warning"
                      style={{ color: '#faad14', marginRight: '8px' }}
                    />
                    <strong>
                      {warning.projectName && `[${warning.projectName}] `}
                    </strong>
                    {warning.message}
                  </div>
                ))}
              </div>
            }
            type="warning"
            showIcon
            closable
            style={{ marginBottom: '16px' }}
          />
        </div>
      )}

      <Spin spinning={loading}>
        <AllocationTable
          members={filteredMembers}
          allocations={filteredAllocations}
          projects={filteredProjects}
          expandedRowKeys={expandedRowKeys}
          weeks={weeks}
          months={months}
          timeUnit={timeUnit}
          viewType={viewType}
          loading={loading}
          metric={selectedMetric}
          showTimeOff={showTimeOff}
          toggleExpandRow={toggleExpandRow}
          handleEditAllocation={handleEditAllocation}
          handleAllocationUpdateResize={handleAllocationUpdateDragAndDrop}
          handleViewTypeChange={handleViewTypeChange}
          canModifyAllocation={canModifyAllocation}
        />

        <div
          style={{ marginTop: 16, display: 'flex', justifyContent: 'flex-end' }}
        >
          <div
            style={{ display: 'flex', alignItems: 'center', marginRight: 16 }}
          >
            <div
              style={{
                width: 16,
                height: 16,
                backgroundColor: '#52c41a',
                borderRadius: 8,
                marginRight: 8,
                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
              }}
            ></div>
            <span>{t('{{allocations.legendHealthy}}')}</span>
          </div>
          <div
            style={{ display: 'flex', alignItems: 'center', marginRight: 16 }}
          >
            <div
              style={{
                width: 16,
                height: 16,
                backgroundColor: '#fa8c16',
                borderRadius: 8,
                marginRight: 8,
                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
              }}
            ></div>
            <span>{t('{{allocations.legendNearCapacity}}')}</span>
          </div>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div
              style={{
                width: 16,
                height: 16,
                backgroundColor: '#f5222d',
                borderRadius: 8,
                marginRight: 8,
                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
              }}
            ></div>
            <span>{t('{{allocations.legendOverallocated}}')}</span>
          </div>
        </div>
      </Spin>

      <EditModal
        isEditAllocationModalVisible={isEditAllocationModalVisible}
        editingAllocation={editingAllocation}
        editFormRef={editFormRef}
        selectedProject={selectedProject}
        projects={projects}
        loading={loading}
        handleEditAllocationCancel={handleEditAllocationCancel}
        handleEditAllocationSubmit={handleEditAllocationSubmit}
        handleDeleteAllocation={handleDeleteAllocation}
        handleProjectChange={handleProjectChange}
        fetchProjectActivities={fetchProjectActivities}
        fetchMembers={fetchMembers}
        canModifyAllocation={canModifyAllocation}
        currentEmployeeId={currentEmployeeId}
        projectPermissions={projectPermissions}
        validateAllocationDate={validateAllocationDate}
      />

      <CreateModal
        isNewAllocationModalVisible={isNewAllocationModalVisible}
        newFormRef={newFormRef}
        selectedProject={selectedProject}
        projects={projects}
        loading={loading}
        handleNewAllocationCancel={handleNewAllocationCancel}
        handleNewAllocationSubmit={handleNewAllocationSubmit}
        handleProjectChange={handleProjectChange}
        fetchProjectActivities={fetchProjectActivities}
        fetchMembers={fetchMembers}
        canModifyAllocation={canModifyAllocation}
        currentEmployeeId={currentEmployeeId}
        projectPermissions={projectPermissions}
        validateAllocationDate={validateAllocationDate}
      />
    </div>
  );
};

export default AllocationsPage;
