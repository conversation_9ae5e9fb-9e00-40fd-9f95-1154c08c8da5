import { getTranslation, log } from '@everestsystems/content-core';
import { PlainDate } from '@everestsystems/datetime';
import { Decimal } from '@everestsystems/decimal';
import { EvstBaseCurrency } from '@pkg/everest.base/types/enums/BaseCurrency';
import { BillableTime } from '@pkg/everest.fin.accounting/types/BillableTime';
import { InvoiceMilestone } from '@pkg/everest.fin.accounting/types/InvoiceMilestone';
import { EvstBillableExpenseType } from '@pkg/everest.fin.base/types/enums/BillableExpenseType';
import { EvstExpenseStatus } from '@pkg/everest.fin.base/types/enums/ExpenseStatus';
import { BillableExpenseAssignment } from '@pkg/everest.fin.expense/types/BillableExpenseAssignment';
import { EvstBillableExpenseAssignmentSourceDocType } from '@pkg/everest.fin.expense/types/enums/BillableExpenseAssignmentSourceDocType';
import { Expenses } from '@pkg/everest.fin.expense/types/views/publicApi/view/Expenses/Expenses';
import PsaPermissionApi, {
  PsaPermission,
} from '@pkg/everest.psa/lib/permissions/PsaPermissionApi';
import MilestoneApi from '@pkg/everest.psa/lib/projectApi/MilestoneApi';
import PsaInternalApi from '@pkg/everest.psa/lib/projectApi/PsaInternalApi';
import { mapQuery } from '@pkg/everest.psa/modules/util/queryViews';
import { EvstMilestoneStatus } from '@pkg/everest.psa/types/enums/MilestoneStatus';
import type { EvstPsaProjectStatus } from '@pkg/everest.psa/types/enums/PsaProjectStatus';
import { FixedFee } from '@pkg/everest.psa/types/FixedFee';
import type { Billing } from '@pkg/everest.psa/types/presentations/modules/billing/pages/billing/Billing';
import { InvoicedPeriodApi } from '@pkg/everest.psa/types/presentations/publicApi/api/InvoicedPeriodApi/InvoicedPeriodApi';
import { JoinedProjectView } from '@pkg/everest.psa/types/views/publicApi/view/Projects/JoinedProjectView';
import { EvstActivityBillingType } from '@pkg/everest.timetracking/types/enums/ActivityBillingType';
import { EvstTimesheetSubmissionStatus } from '@pkg/everest.timetracking/types/enums/TimesheetSubmissionStatus';
import { TimeEntryApi } from '@pkg/everest.timetracking/types/presentations/publicApi/api/TimeEntryApi/TimeEntryApi';
import { TimeEntries } from '@pkg/everest.timetracking/types/views/publicApi/view/TimeEntries/TimeEntries';

/**
 * OData implementation for the Billing service
 */
export default {
  projectSalesOrders: {
    query: {
      async execute({
        session,
        where,
      }: Billing.EntitySets.projectSalesOrders.Query.Execute.Request): Promise<Billing.EntitySets.projectSalesOrders.Query.Execute.Response> {
        try {
          if (!where?.psaProjectId?.['$eq']) {
            throw new Error('psaProjectId.$eq must be provided');
          }

          const psaProjectId = where.psaProjectId['$eq'];

          // Get project sales orders from JoinedProjectView
          const projectClient = await JoinedProjectView.client(session);
          const salesOrders = (await projectClient.query(
            {
              where: {
                psaProjectId,
                select: 'salesOrders',
              },
            },
            ['psaProjectId', 'salesOrderId', 'salesOrderNumber']
          )) as JoinedProjectView.JoinedProjectView[];

          // Transform the data to match the expected format
          const instances = salesOrders.map((salesOrder) => ({
            psaProjectId: salesOrder.psaProjectId,
            salesOrderId: salesOrder.salesOrderId,
            salesOrderNumber: salesOrder.salesOrderNumber,
          }));

          return {
            instances,
          };
        } catch (error) {
          log.error('Error fetching project sales orders', error);
          throw error;
        }
      },
    },
  },
  billableProjects: {
    query: {
      async execute({
        session,
        where,
        orderBy,
        fieldList,
        count,
      }: Billing.EntitySets.billableProjects.Query.Execute.Request): Promise<Billing.EntitySets.billableProjects.Query.Execute.Response> {
        try {
          // We always expect the frontend to send the correct structure
          if (
            !where?.['$and']?.[0]?.dateFilter?.['$gte'] ||
            !where?.['$and']?.[1]?.dateFilter?.['$lte']
          ) {
            throw new Error(
              'Invalid where clause structure: $and with 2 elements must be the only key in the where clause, with dateFilter.$gte in first position and dateFilter.$lte in second position'
            );
          }
          // Destructure the where clause
          const startDateFilter = where.$and[0].dateFilter['$gte'];
          const endDateFilter = where.$and[1].dateFilter['$lte'];

          // Create a mapping between the OData field names and the view field names
          const fieldMapping: Record<string, string> = {
            id: 'psaProjectId',
            name: 'projectName',
            customerId: 'salesArrangementCustomerId',
            customerName: 'salesArrangementCustomerName',
          };

          // Map the query parameters using the mapQuery function
          const {
            fieldList: viewFields,
            orderBy: viewOrderBy,
            where: viewWhere,
          } = mapQuery(fieldList, orderBy, where, fieldMapping);

          // Get billable projects from JoinedProjectView
          const projectClient = await JoinedProjectView.client(session);
          const projects: JoinedProjectView.JoinedProjectView[] =
            (await projectClient.query(
              {
                where: {
                  ...viewWhere,
                  billable: { $eq: true },
                },
                orderBy: viewOrderBy,
              },
              [
                ...viewFields,
                'projectId',
                'teamId',
                'status',
                'salesArrangementId',
                'currency',
                'salesArrangementCurrency',
              ]
            )) as JoinedProjectView.JoinedProjectView[];

          // Get milestones from JoinedProjectView
          const milestones: JoinedProjectView.JoinedProjectView[] =
            (await projectClient.query(
              {
                where: {
                  select: 'milestones',
                  milestoneStatus: { $ne: EvstMilestoneStatus.Invoiced },
                  milestoneBillable: { $eq: true },
                },
                orderBy: viewOrderBy,
              },
              [
                'milestoneId',
                'milestoneAmount',
                'milestoneStatus',
                'milestoneSalesOrderProductLineId',
                'psaProjectId',
              ]
            )) as JoinedProjectView.JoinedProjectView[];

          // Get time entries for the date range
          const timeEntriesClient = await TimeEntries.client(session);
          const timeEntries: TimeEntries.TimeEntries[] =
            (await timeEntriesClient.query(
              {
                where: {
                  date: { $between: [startDateFilter, endDateFilter] },
                  projectActivityBillable: { $eq: true },
                  projectPositionBillable: { $eq: true },
                  submissionStatus: {
                    $eq: EvstTimesheetSubmissionStatus.Approved,
                  },
                  projectActivityBillingType: {
                    $eq: EvstActivityBillingType.Hours,
                  },
                },
              },
              [
                'timeEntryId',
                'projectId',
                'hours',
                'projectPositionHourlyBillRate',
                'projectPositionSalesOrderProductLineId',
              ]
            )) as TimeEntries.TimeEntries[];

          // Get all teamIds for expense filtering from projects
          const teamIds = projects
            .map((project) => project.teamId)
            .filter(
              (teamId): teamId is number =>
                teamId !== null && teamId !== undefined
            );

          // Get expenses for the date range
          const expenses: Expenses.Expenses[] =
            teamIds.length > 0
              ? ((await Expenses.query(
                  session,
                  {
                    where: {
                      billDate: { $between: [startDateFilter, endDateFilter] },
                      teamId: { $in: teamIds },
                      billableExpenseAssignmentId: { $eq: null },
                      billableExpense: { $eq: EvstBillableExpenseType.Yes },
                      status: { $eq: EvstExpenseStatus.Approved },
                    },
                  },
                  [
                    'id',
                    'amount',
                    'reimbursableNetAmount',
                    'customerId',
                    'teamId',
                  ]
                )) as Expenses.Expenses[])
              : [];

          const unassignedCustomerText = await getTranslation(
            session,
            'billing.unassignedCustomer',
            'everest.psa/uinext/i18n/psa'
          );

          // Calculate totals for each project
          const instances = projects.map((project) => {
            // Calculate milestone total
            let milestoneTotalAmount = Decimal.Zero;
            const projectMilestones = milestones.filter(
              (milestone) => milestone.psaProjectId === project.psaProjectId
            );
            for (const milestone of projectMilestones) {
              milestoneTotalAmount = milestoneTotalAmount.plus(
                milestone.milestoneAmount
              );
            }

            // TimeEntries view cannot join PsaProject table (reverse dependency)
            let timeEntriesTotalAmount = Decimal.Zero;

            const projectTimeEntries = timeEntries.filter(
              (entry) => entry.projectId === project.projectId
            );
            for (const entry of projectTimeEntries) {
              const hours = entry.hours;
              const rate = entry.projectPositionHourlyBillRate;
              timeEntriesTotalAmount = timeEntriesTotalAmount.plus(
                hours.times(rate)
              );
            }

            // Total services is time entries + milestones
            const totalServices =
              timeEntriesTotalAmount.plus(milestoneTotalAmount);

            // Calculate expenses total
            const projectExpenses = expenses.filter(
              (expense) => expense.teamId === project.teamId
            );
            let totalExpenses = Decimal.Zero;
            for (const expense of projectExpenses) {
              totalExpenses = totalExpenses.plus(expense.reimbursableNetAmount);
            }

            // Calculate total price
            const totalPrice = totalServices.plus(totalExpenses);

            // Get the project currency (prefer currency from project, fallback to salesOrderCurrency)
            const currency =
              project.currency ||
              project.salesArrangementCurrency ||
              EvstBaseCurrency.USD;

            return {
              id: project.psaProjectId,
              name: project.projectName,
              customerId: project.salesArrangementCustomerId,
              customerName:
                project.salesArrangementCustomerName ?? unassignedCustomerText,
              address: '', // TODO: Remove
              currency: currency as EvstBaseCurrency,
              totalServices,
              totalExpenses,
              totalPrice,
              status: project.status as EvstPsaProjectStatus,
              inconsistentBillingState: project?.salesArrangementId
                ? false
                : true,
            };
          });

          return {
            instances,
            count: count ? instances.length : undefined,
          };
        } catch (error) {
          log.error('Error fetching billable projects', error);
          throw error;
        }
      },
    },
  },

  projectBillingDetails: {
    query: {
      async execute({
        session,
        where,
        count,
      }: Billing.EntitySets.projectBillingDetails.Query.Execute.Request): Promise<Billing.EntitySets.projectBillingDetails.Query.Execute.Response> {
        try {
          // We always expect the frontend to send the correct structure
          if (
            !where?.['$and']?.[0]?.psaProjectId?.['$eq'] ||
            !where?.['$and']?.[1]?.['$and']?.[0]?.dateFilter?.['$gte'] ||
            !where?.['$and']?.[1]?.['$and']?.[1]?.dateFilter?.['$lte']
          ) {
            throw new Error(
              'Invalid where clause structure: $and with 2 elements must be the only key in the where clause, with psaProjectId.$eq in first position and nested $and must have exactly 2 elements with dateFilter.$gte in first position and dateFilter.$lte in second position'
            );
          }
          // Destructure the where clause
          const psaProjectId = where.$and[0].psaProjectId['$eq'];
          const startDateFilter = where.$and[1].$and[0].dateFilter['$gte'];
          const endDateFilter = where.$and[1].$and[1].dateFilter['$lte'];

          // Get project details from JoinedProjectView
          const projectClient = await JoinedProjectView.client(session);
          const projects = (await projectClient.query(
            {
              where: {
                psaProjectId,
                billable: { $eq: true },
              },
            },
            [
              'psaProjectId',
              'projectId',
              'teamId',
              'salesArrangementCustomerId',
              'status',
              'salesArrangementId',
              'currency',
              'salesArrangementCurrency',
            ]
          )) as JoinedProjectView.JoinedProjectView[];

          if (projects.length === 0) {
            return { instances: [] };
          }
          const projectsIds = projects.map((project) => project.projectId);

          // Get fixed fees for the project
          const fixedFees = await FixedFee.query(
            session,
            {
              where: {
                psaProjectId,
                // Only get fixed fees that are active during the selected period
                $and: [
                  { startDate: { $lte: endDateFilter } },
                  { endDate: { $gte: startDateFilter } },
                ],
              },
            },
            [
              'id',
              'psaProjectId',
              'startDate',
              'endDate',
              'amount',
              'frequency',
              'salesOrderProductLineId',
            ]
          );

          // Get milestones for the project
          const milestones = (await projectClient.query(
            {
              where: {
                projectId: { $in: projectsIds },
                select: 'milestones',
                milestoneStatus: { $ne: EvstMilestoneStatus.Invoiced },
                milestoneBillable: { $eq: true },
              },
            },
            [
              'psaProjectId',
              'milestoneId',
              'milestoneName',
              'milestoneDescription',
              'milestoneDueDate',
              'milestoneAmount',
              'milestoneStatus',
              'milestoneSalesOrderProductLineId',
            ]
          )) as JoinedProjectView.JoinedProjectView[];

          // Get time entries for the project
          const timeEntriesClient = await TimeEntries.client(session);
          const timeEntries = (await timeEntriesClient.query(
            {
              where: {
                date: { $between: [startDateFilter, endDateFilter] },
                projectId: { $in: projectsIds },
                projectActivityBillable: { $eq: true },
                projectPositionBillable: { $eq: true },
                projectActivityBillingType: {
                  $eq: EvstActivityBillingType.Hours,
                },
                submissionStatus: {
                  $eq: EvstTimesheetSubmissionStatus.Approved,
                },
              },
            },
            [
              'timeEntryId',
              'projectId',
              'date',
              'hours',
              'memberId',
              'projectActivityId',
              'memberEmployeeName',
              'memberEmployeeId',
              'projectPositionId',
              'projectPositionName',
              'projectActivityName',
              'projectPositionHourlyBillRate',
              'projectPositionSalesOrderProductLineId',
              'submissionStatus',
            ]
          )) as TimeEntries.TimeEntries[];

          // Get all teamIds for expense filtering from projects
          const teamIds = projects
            .map((project) => project.teamId)
            .filter(
              (teamId): teamId is number =>
                teamId !== null && teamId !== undefined
            );

          // Get expenses for the project's teams
          const expenses: Expenses.Expenses[] =
            teamIds.length > 0
              ? ((await Expenses.query(
                  session,
                  {
                    where: {
                      billDate: { $between: [startDateFilter, endDateFilter] },
                      teamId: { $in: teamIds },
                      billableExpenseAssignmentId: { $eq: null },
                      billableExpense: { $eq: EvstBillableExpenseType.Yes },
                      status: { $eq: EvstExpenseStatus.Approved },
                    },
                  },
                  [
                    'id',
                    'billDate',
                    'description',
                    'merchant',
                    'amount',
                    'reimbursableNetAmount',
                    'employeeId',
                    'employeeName',
                    'billableExpense',
                    'status',
                    'customerId',
                    'teamId',
                  ]
                )) as Expenses.Expenses[])
              : [];

          // Process fixed fees for display
          const processedFixedFees = fixedFees.map((fixedFee) => {
            return {
              ...fixedFee,
              isAutomaticallyInvoiced: true, // Fixed fees are always automatically invoiced
            };
          });

          // Map the data to the expected format for each project
          const instances = projects.map((project) => {
            // Filter time entries for this project
            const projectTimeEntries = timeEntries.filter(
              (entry) => entry.projectId === project.projectId
            );

            // Filter milestones for this project
            const projectMilestones = milestones.filter(
              (milestone) => milestone.psaProjectId === project.psaProjectId
            );

            // Filter expenses for this project
            const projectExpenses = expenses.filter(
              (expense) => expense.teamId === project.teamId
            );

            // Get the project currency (prefer currency from project, fallback to salesOrderCurrency)
            const currency =
              project.currency ||
              project.salesArrangementCurrency ||
              EvstBaseCurrency.USD;

            return {
              psaProjectId: project.psaProjectId,
              currency: currency as EvstBaseCurrency,
              status: project.status as EvstPsaProjectStatus,
              inconsistentProjectBillingState: project?.salesArrangementId
                ? false
                : true,
              fixedFees: processedFixedFees.map((fixedFee) => ({
                id: fixedFee.id,
                projectId: project.projectId,
                name: `Fixed Fee #${fixedFee.id}`,
                description: `Fixed fee from ${fixedFee.startDate.toString()} to ${fixedFee.endDate.toString()}`,
                amount: fixedFee.amount?.amount,
                frequency: fixedFee.frequency,
                startDate: fixedFee.startDate,
                endDate: fixedFee.endDate,

                isAutomaticallyInvoiced: fixedFee.isAutomaticallyInvoiced,
              })),
              timeEntries: projectTimeEntries.map((entry) => ({
                id: entry.timeEntryId,
                projectId: project.projectId,
                memberId: entry.memberId,
                memberName: entry.memberEmployeeName,
                memberEmployeeId: entry.memberEmployeeId,
                positionId: entry.projectPositionId,
                positionName: entry.projectPositionName,
                activityId: entry.projectActivityId,
                activityName: entry.projectActivityName,
                date: entry.date,
                hours: entry.hours,
                hourlyBillRate: entry.projectPositionHourlyBillRate,
                totalPrice: entry.hours.times(
                  entry.projectPositionHourlyBillRate
                ),
                status: entry.submissionStatus as EvstTimesheetSubmissionStatus,
                inconsistentBillingState:
                  entry?.projectPositionSalesOrderProductLineId ? false : true,
              })),
              milestones: projectMilestones.map((milestone) => ({
                id: milestone.milestoneId,
                projectId: project.projectId,
                name: milestone.milestoneName,
                description: milestone.milestoneDescription || '',
                accomplishedDate: milestone.milestoneDueDate,
                price: milestone.milestoneAmount,
                status: milestone.milestoneStatus as EvstMilestoneStatus,
                inconsistentBillingState:
                  milestone?.milestoneSalesOrderProductLineId ? false : true,
              })),
              expenses: projectExpenses.map((expense) => ({
                id: expense.id,
                projectId: project.projectId,
                memberId: expense.employeeId,
                memberName: expense.employeeName,
                description: expense.description,
                merchant: expense.merchant,
                date: expense.billDate,
                amount: expense.reimbursableNetAmount,
                billable:
                  expense.billableExpense === EvstBillableExpenseType.Yes,
                status: expense.status as EvstExpenseStatus,
                inconsistentBillingState:
                  expense?.customerId &&
                  expense.customerId === project?.salesArrangementCustomerId
                    ? false
                    : true,
              })),
            };
          });

          return {
            instances,
            count: count ? instances.length : undefined,
          };
        } catch (error) {
          log.error('Error fetching project billing details', error);
          throw error;
        }
      },
    },
  },

  submitForInvoicing: {
    async execute({
      session,
      input,
    }: Billing.Actions.submitForInvoicing.Execute.Request): Promise<Billing.Actions.submitForInvoicing.Execute.Response> {
      // Check if we have edit permissions for the project
      const permission = await PsaPermissionApi.getProjectPermissions(
        session,
        input.psaProjectId
      );
      if (!(permission >= PsaPermission.Edit)) {
        throw new Error(
          await getTranslation(
            session,
            'projects.noEditPermission',
            'everest.psa/uinext/i18n/psa'
          )
        );
      }

      // Get project details from JoinedProjectView
      const projectClient = await JoinedProjectView.client(session);
      const projects = (await projectClient.query(
        {
          where: {
            psaProjectId: { $eq: input.psaProjectId },
          },
        },
        [
          'psaProjectId',
          'projectId',
          'salesArrangementId',
          'salesArrangementCustomerId',
          'salesArrangementEntityId',
        ]
      )) as JoinedProjectView.JoinedProjectView[];

      if (projects.length === 0) {
        throw new Error(`Project with ID ${input.psaProjectId} not found.`);
      }
      const project = projects[0];

      // Get milestones for the selected IDs
      let selectedMilestones: JoinedProjectView.JoinedProjectView[] = [];
      if (input.selectedMilestoneIds && input.selectedMilestoneIds.length > 0) {
        selectedMilestones = (await projectClient.query(
          {
            where: {
              select: 'milestones',
              milestoneId: { $in: input.selectedMilestoneIds },
            },
          },
          [
            'milestoneId',
            'milestoneName',
            'milestoneAmount',
            'milestoneCurrency',
            'milestoneSalesOrderProductLineId',
            'milestoneSalesOrderId',
            'milestoneDescription',
            'milestoneDueDate',
          ]
        )) as JoinedProjectView.JoinedProjectView[];
      }

      // Get time entries for the selected IDs
      let selectedTimeEntries: TimeEntries.TimeEntries[] = [];
      if (input.selectedTimeEntryIds && input.selectedTimeEntryIds.length > 0) {
        // Update time entries with invoiced hours
        await TimeEntryApi.updateTimeEntries.execute(session, {
          timeEntries: input.selectedTimeEntryIds,
        });

        const timeEntriesClient = await TimeEntries.client(session);
        // Extract just the IDs from the selectedTimeEntryIds objects
        const timeEntryIds = input.selectedTimeEntryIds.map(
          (entry) => entry.id
        );
        selectedTimeEntries = (await timeEntriesClient.query(
          {
            where: {
              timeEntryId: { $in: timeEntryIds },
            },
          },
          [
            'timeEntryId',
            'hours',
            'invoicedHours',
            'projectPositionHourlyBillRate',
            'projectPositionHourlyBillRateCurrency',
            'projectPositionSalesOrderProductLineId',
            'projectPositionSalesOrderId',
            'date',
            'memberEmployeeName',
          ]
        )) as TimeEntries.TimeEntries[];
      }

      // Get expenses for the selected IDs
      let selectedExpenses: Expenses.Expenses[] = [];
      if (input.selectedExpenseIds && input.selectedExpenseIds.length > 0) {
        if (!input.expensesSalesOrderId) {
          throw new Error('Missing expenses sales order id');
        }

        const expensesClient = await Expenses.client(session);
        selectedExpenses = (await expensesClient.query(
          {
            where: {
              id: { $in: input.selectedExpenseIds },
            },
          },
          [
            'id',
            'amount',
            'amountCurrency',
            'reimbursableNetAmount',
            'reimbursableNetAmountCurrency',
            'billableExpense',
            'billDate',
            'description',
          ]
        )) as Expenses.Expenses[];
      }

      // Process milestones if we have any
      if (selectedMilestones.length > 0) {
        // Get the current date for invoice date
        const now = new Date();
        const invoiceDate = new PlainDate(
          now.getFullYear(),
          now.getMonth() + 1,
          now.getDate()
        );

        // Format milestones for invoice creation
        const invoiceMilestonesToProcess = selectedMilestones.map(
          (milestone) => ({
            salesOrderProductLineId: milestone.milestoneSalesOrderProductLineId,
            description: milestone.milestoneDescription,
            projectedDate: milestone.milestoneDueDate,
            totalPrice: {
              amount: milestone.milestoneAmount,
              currency: milestone.milestoneCurrency,
            },
          })
        );

        const invoiceMilestoneClient = await InvoiceMilestone.client(session);

        // We can only create new invoices for milestones that already exist
        await invoiceMilestoneClient.createMilestoneInvoice(
          project.salesArrangementEntityId,
          invoiceDate,
          invoiceMilestonesToProcess as any
        );

        // Update milestone status to "Invoiced"
        await MilestoneApi.invoice(session, input.selectedMilestoneIds);
      }

      // Process time entries if we have any
      if (selectedTimeEntries.length > 0) {
        // Calculate the date range for the billable time
        const dates = selectedTimeEntries.map((entry) => entry.date);
        // Find minimum date
        let startDate = dates[0];
        for (const date of dates) {
          if (PlainDate.compare(date, startDate) < 0) {
            startDate = date;
          }
        }
        // Find maximum date
        let endDate = dates[0];
        for (const date of dates) {
          if (PlainDate.compare(date, endDate) > 0) {
            endDate = date;
          }
        }

        // Group time entries by salesOrderId
        const entriesBySalesOrderId: Record<number, TimeEntries.TimeEntries[]> =
          {};

        for (const entry of selectedTimeEntries) {
          const salesOrderId = entry.projectPositionSalesOrderId;
          if (!salesOrderId) {
            continue; // Skip entries without a sales order ID
          }

          if (!entriesBySalesOrderId[salesOrderId]) {
            entriesBySalesOrderId[salesOrderId] = [];
          }

          entriesBySalesOrderId[salesOrderId].push(entry);
        }

        // Process each group of time entries separately
        const billableTimeClient = await BillableTime.client(session);

        for (const [salesOrderId, entries] of Object.entries(
          entriesBySalesOrderId
        )) {
          // Format billable time entries for this sales order
          const billableTimeLines = entries.map((entry) => {
            // Use invoicedHours if available, otherwise use hours
            const hoursToInvoice = entry.invoicedHours || entry.hours;

            return {
              salesOrderProductLineId:
                entry.projectPositionSalesOrderProductLineId,
              employee: entry.memberEmployeeName,
              startDate: entry.date,
              endDate: entry.date,
              quantity: hoursToInvoice,
              unitPrice: {
                amount: entry.projectPositionHourlyBillRate,
                currency: entry.projectPositionHourlyBillRateCurrency,
              },
              totalPrice: {
                amount: hoursToInvoice.times(
                  entry.projectPositionHourlyBillRate
                ),
                currency: entry.projectPositionHourlyBillRateCurrency,
              },
            };
          });

          // Create billable time entries for this sales order
          const billableTimeResult =
            (await billableTimeClient.handleCreateOrUpdateOfBillableTimes(
              Number(salesOrderId), // Convert string key back to number
              startDate,
              endDate,
              billableTimeLines as any,
              false // No attachment modification
            )) as {
              billableTime: { id: number };
              billableTimeLines: Array<{ id: number; quantity: unknown }>;
            };

          // Update time entry status to "Invoiced" and set billableTimeId for this group
          await PsaInternalApi.invoiceTimeEntries(
            session,
            entries.map((entry, index) => ({
              id: entry.timeEntryId,
              billableTimeLineId:
                billableTimeResult.billableTimeLines[index].id,
            }))
          );
        }
      }

      // Assign billable expenses to the sales order if we have any
      if (selectedExpenses.length > 0) {
        const now = new Date();
        const today = new PlainDate(
          now.getFullYear(),
          now.getMonth() + 1,
          now.getDate()
        );
        const expenseClient = await BillableExpenseAssignment.client(session);
        await expenseClient.assignBillableExpenses(
          project.salesArrangementCustomerId,
          input.expensesSalesOrderId,
          selectedExpenses.map((expense) => ({
            documentId: expense.id,
            documentType:
              EvstBillableExpenseAssignmentSourceDocType.ExpenseReportLine,
            assignmentDate: today,
            description: expense.description,
            amount: {
              amount: expense.reimbursableNetAmount,
              currency: expense.reimbursableNetAmountCurrency,
            },
            documentDate: expense.billDate,
          }))
        );
      }

      try {
        // Create InvoicedPeriod entry
        if (input.startDate && input.endDate) {
          await InvoicedPeriodApi.createInvoicedPeriods.execute(session, {
            invoicedPeriods: [
              {
                psaProjectId: input.psaProjectId,
                startDate: input.startDate,
                endDate: input.endDate,
              },
            ],
          });
        }
      } catch {
        throw new Error(
          `This period is already invoiced: ${input.startDate} - ${input.endDate}`
        );
      }

      return {
        output: {
          salesOrderId:
            input.expensesSalesOrderId ||
            selectedMilestones?.[0]?.milestoneSalesOrderId ||
            selectedTimeEntries?.[0]?.projectPositionSalesOrderId,
        },
      };
    },
  },
  isPeriodEnabledForInvoicing: {
    async execute({
      session,
      input,
    }: Billing.Actions.isPeriodEnabledForInvoicing.Execute.Request): Promise<Billing.Actions.isPeriodEnabledForInvoicing.Execute.Response> {
      // Check if we have view permissions for the project
      const permission = await PsaPermissionApi.getProjectPermissions(
        session,
        input.psaProjectId
      );
      if (!(permission >= PsaPermission.View)) {
        throw new Error(
          await getTranslation(
            session,
            'projects.noViewPermission',
            'everest.psa/uinext/i18n/psa'
          )
        );
      }

      const { enabled } =
        await InvoicedPeriodApi.isPeriodEnabledForInvoicing.execute(session, {
          psaProjectId: input.psaProjectId,
          startDate: input.startDate,
          endDate: input.endDate,
        });

      return {
        output: {
          enabled,
        },
      };
    },
  },
} satisfies Billing.Implementation;
