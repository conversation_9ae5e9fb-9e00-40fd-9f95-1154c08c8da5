package everest.psa

odata presentation TimesheetService {

    // Action to get the employee's weekly capacity
    action GetEmployeeWeeklyCapacity {
        outputs {
            weeklyCapacity: Number<Int>
        }
        properties {
            side-effects false
        }
    }


    // Action to get time KPIs for a date range
    action GetTimeKpis {
        inputs {
            startDate: PlainDate
            endDate: PlainDate
        }
        outputs {
            totalHours: Number<Decimal>
            billableHours: Number<Decimal>
            utilization: Number<Decimal>
            billableUtilization: Number<Decimal>
            totalUtilization: Number<Decimal>
            billableUtilizationWithoutAbsences: Number<Decimal>
            totalUtilizationWithoutAbsences: Number<Decimal>
            totalHoursIncludingAbsences: Number<Decimal>
        }
        properties {
            side-effects false
        }
    }

    // List for InvoicedPeriods - Query all invoiced periods for a project

    data-set InvoicedPeriods {
        supported-operations view
        field id: field<everest.psa::InvoicedPeriod.id>
        field startDate (required: true): field<everest.psa::InvoicedPeriod.startDate>
        field endDate (required: true): field<everest.psa::InvoicedPeriod.endDate>
        field psaProjectId (required: true): field<everest.psa::InvoicedPeriod.psaProjectId>
    }

    // List for Projects - Query all projects

    data-set Projects {
        supported-operations view
        field id: field<everest.timetracking::TimetrackingProject.id>
        field projectName (required: true): field<everest.hr.base::Project.projectName>
        field startDate: PlainDate
        field endDate: PlainDate
        field status: Text
    }

    // List for Project Positions - Query all positions by project

    data-set ProjectPositions {
        supported-operations view
        field id: field<everest.timetracking::ProjectPosition.id>
        field name (required: true): field<everest.timetracking::ProjectPosition.name>
        field `description`: field<everest.timetracking::ProjectPosition.`description`>
        field projectId (required: true): field<everest.timetracking::ProjectPosition.projectId>
    }

    // List for Project Activities - Query all activities by position

    data-set ProjectActivities {
        supported-operations view
        field id: field<everest.timetracking::ProjectActivity.id>
        field name (required: true): field<everest.timetracking::ProjectActivity.name>
        field `description`: field<everest.timetracking::ProjectActivity.`description`>
        field projectPositionId (required: true): Id
        field memberId: Id
        field billable: field<everest.timetracking::ProjectActivity.billable>
        field projectId: field<everest.timetracking::TimetrackingProject.id>
    }

    // List for Time Entries - Query all time entries for a given member and date range

    data-set TimeEntries {
        supported-operations view
        field id: field<everest.timetracking::TimeEntry.id>
        field date (required: true): field<everest.timetracking::TimeEntry.date>
        field hours (required: true): field<everest.timetracking::TimeEntry.hours>
        field memberId (required: true): field<everest.timetracking::TimeEntry.memberId>
        field notes: field<everest.timetracking::TimeEntry.notes>
        field projectActivityId (required: true): field<everest.timetracking::TimeEntry.projectActivityId>
        field submissionStatus (required: true): field<everest.timetracking::TimeEntry.submissionStatus>
    }



    // Custom action submitting time entries

    action SubmitTimeEntries {
        inputs {
            timeEntryIds: array<Id>
        }
        properties {
            side-effects true
        }
    }

    // Custom action for upserting time entries (batched)

    action UpsertTimeEntries {
        inputs {
            startDate: PlainDate
            endDate: PlainDate
            timeEntries: array<object<{
                id: field<everest.timetracking::TimeEntry.id>
                date: PlainDate
                hours: Number<Decimal>
                notes: Text
                memberId: field<everest.timetracking::TimeEntry.memberId>
                projectActivityId: field<everest.timetracking::TimeEntry.projectActivityId>
                projectId: field<everest.timetracking::TimetrackingProject.id>
            }>>
        }
        outputs {
            upsertedTimeEntryIds: array<field<everest.timetracking::TimeEntry.id>>
        }
        properties {
            side-effects true
        }
    }

    // Custom function to check if a timesheet is submittable based on the week date
    action IsTimesheetSubmittable {
        inputs {
            weekStartDate: PlainDate
        }
        outputs {
            isSubmittable: TrueFalse
        }
        properties {
            side-effects false
        }
    }

    // Custom action to get time off (holidays and absences) for the current employee
    action GetTimeOff {
        inputs {
            startDate: Date
            endDate: Date
        }
        outputs {
            holidays: array<object<{
                holidayDate: PlainDate
                holidayName: Text
                halfDay: TrueFalse
            }>>
            absences: array<object<{
                startDate: PlainDate
                endDate: PlainDate
                mandatoryLeaveId: Number<Int>
                absenceType: Text
                days: Number<Decimal>
            }>>
        }
        properties {
            side-effects false
        }
    }

    // Custom action to get daily allocations with time-off awareness using AllocationApi
    action GetDailyAllocations {
        inputs {
            startDate: PlainDate
            endDate: PlainDate
        }
        outputs {
            dailyAllocations: array<object<{
                date: PlainDate
                allocations: array<object<{
                    allocationId: field<everest.timetracking::Allocation.id>
                    memberId: field<everest.timetracking::Member.id>
                    projectActivityId: field<everest.timetracking::ProjectActivity.id>
                    projectActivity: Text
                    totalHours: Number<Decimal>
                    allocatedHours: Number<Decimal>
                    method: enum<everest.timetracking::TimeAllocationMethod>
                    workingDays: array<enum<everest.appserver::DayOfWeek>>
                    positionName: Text
                    projectName: Text
                    activityName: Text
                    billable: TrueFalse
                }>>
                timeOff: object<{
                    hasTimeOff: TrueFalse
                    isFullDay: TrueFalse
                    holidayName: Text
                    absenceType: Text
                }>
            }>>
        }
        properties {
            side-effects false
        }
    }

    // Action to delete time entries for a specific member and activity combination
    action DeleteTimeEntriesForMemberActivity {
        inputs {
            memberId: Number<Int>
            projectActivityId: Number<Int>
            startDate: PlainDate
            endDate: PlainDate
        }

        outputs {
            deletedCount: Number<Int>
        }

        properties {
            side-effects true
        }
    }
}
