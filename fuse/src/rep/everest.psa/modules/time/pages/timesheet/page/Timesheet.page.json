{"type": "react", "componentUrn": "urn:evst:everest:psa:artifact/ui/tsx:modules/time/pages/timesheet/Timesheet", "backendAccess": {"urn:evst:everest:psa:presentation:modules/time/pages/timesheet/TimesheetService": ["TimeEntries/view", "Projects/view", "ProjectPositions/view", "ProjectActivities/view", "InvoicedPeriods/view", "UpsertTimeEntries", "SubmitTimeEntries", "DeleteTimeEntriesForMemberActivity", "GetEmployeeWeeklyCapacity", "GetTimeKpis", "IsTimesheetSubmittable", "GetTimeOff", "GetDailyAllocations"]}, "variants": {"management": {"title": "Time entry management", "description": "Full management access to time entries"}}}