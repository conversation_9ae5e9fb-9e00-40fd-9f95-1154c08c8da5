package everest.psa

odata presentation TimeApprovalService {
    // List for Time Entries - Query all time entries for approval

    data-set TimeEntries {
        supported-operations view, change
        field id: field<everest.timetracking::TimeEntry.id>
        field date (required: true): field<everest.timetracking::TimeEntry.date>
        field hours (required: true): field<everest.timetracking::TimeEntry.hours>
        field memberId (required: true): field<everest.timetracking::TimeEntry.memberId>
        field notes: field<everest.timetracking::TimeEntry.notes>
        field projectActivityId (required: true): field<everest.timetracking::TimeEntry.projectActivityId>
        field submissionDate: field<everest.timetracking::TimeEntry.submissionDate>
        field submissionStatus (required: true): field<everest.timetracking::TimeEntry.submissionStatus>
        field projectPositionId: field<everest.timetracking::ProjectPosition.id>
        field positionName: field<everest.timetracking::ProjectPosition.name>
        field positionProjectId: field<everest.timetracking::TimetrackingProject.id>
        field projectName: field<everest.hr.base::Project.projectName>
        field projectId: field<everest.timetracking::TimetrackingProject.id>
        field employeeId: field<everest.hr.base::Employee.id>
        field activityId: field<everest.timetracking::ProjectActivity.id>
        field activityName: field<everest.timetracking::ProjectActivity.name>
        field employeeName: field<everest.hr.base::Employee.name>
    }


    // Custom action for approving time entries

    action ApproveTimeEntries {
        inputs {
            timeEntryIds: array<field<everest.timetracking::TimeEntry.id>>
            projectIds: array<field<everest.timetracking::TimetrackingProject.id>>
        }
        properties {
            side-effects true
        }
    }

    // Custom action for rejecting time entries

    action RejectTimeEntries {
        inputs {
            timeEntryIds: array<field<everest.timetracking::TimeEntry.id>>
            projectIds: array<field<everest.timetracking::TimetrackingProject.id>>
            rejectionReason: Text
        }
        properties {
            side-effects true
        }
    }

    // Custom action for checking approval policies
    
    action CheckApprovalPolicies {
        outputs {
            hasApprovalPolicies: TrueFalse
        }

        properties {
            side-effects false
        }
    }
}
