import { log } from '@everestsystems/content-core';
import { GenericApprovalPolicy } from '@pkg/everest.base.approvals/public/types';
import { DocumentApprovalApi } from '@pkg/everest.base.approvals/types/presentations/publicApi/api/DocumentApprovalApi/DocumentApprovalApi';
import { EmployeeApi } from '@pkg/everest.hr.base/types/presentations/publicApi/api/EmployeeApi/EmployeeApi';
import { mapQuery } from '@pkg/everest.psa/modules/util/queryViews';
import type { TimeApprovalService } from '@pkg/everest.psa/types/presentations/modules/time/pages/timeApproval/TimeApprovalService';
import type { EvstTimesheetSubmissionStatus } from '@pkg/everest.timetracking/types/enums/TimesheetSubmissionStatus';
import { TimeEntry } from '@pkg/everest.timetracking/types/TimeEntry';
import { TimeEntries } from '@pkg/everest.timetracking/types/views/publicApi/view/TimeEntries/TimeEntries';

const TIMEENTRY_PATH = `${TimeEntry.MODEL_URN}.id`;
const TIMEENTRY_DOCUMENT =
  GenericApprovalPolicy.ApprovalPolicyBusinessObject.TimeEntry;

/**
 * Implementation of the TimeApprovalService OData service.
 * This service provides functionality for approving and rejecting time entries.
 */
const implementation: TimeApprovalService.Implementation = {
  // TimeEntries EntitySet implementation
  TimeEntries: {
    query: {
      async execute({
        session,
        where,
        orderBy,
        skip,
        take,
        fieldList,
        count,
      }: TimeApprovalService.EntitySets.TimeEntries.Query.Execute.Request): Promise<TimeApprovalService.EntitySets.TimeEntries.Query.Execute.Response> {
        log.debug('TimeEntries.query.execute called', { where });

        // Get the current employee ID
        const { employeeId } = await EmployeeApi.getEmployeeId.execute(
          session,
          {}
        );
        log.debug('Current employee ID', { employeeId });

        const approvalDocs =
          await DocumentApprovalApi.getBulkDocumentApprovalStatus.execute(
            session,
            {
              documentType: TIMEENTRY_DOCUMENT,
              options: {
                employeeId,
              },
            }
          );

        const relevantTimeEntryIds = approvalDocs.results
          .filter((doc) => doc.state.userCanApprove)
          .map((doc) => doc.id);

        // Define field mapping for OData to view query
        const fieldMapping: Record<string, string> = {
          id: 'timeEntryId',
          date: 'date',
          hours: 'hours',
          notes: 'notes',
          submissionStatus: 'submissionStatus',
          submissionDate: 'submissionDate',
          employeeId: 'memberEmployeeId',
          employeeName: 'memberEmployeeName',
          projectId: 'projectId',
          projectName: 'projectName',
          positionName: 'projectPositionName',
          activityId: 'projectActivityId',
          activityName: 'projectActivityName',
        };

        const { orderBy: viewOrderBy } = mapQuery(
          fieldList,
          orderBy,
          where,
          fieldMapping
        );

        // Use the TimeEntries view which now includes project and position information
        const client = await TimeEntries.client(session);

        // Query the TimeEntries view with explicitly selected fields to avoid ambiguous column references
        const timeEntries = await client.query(
          {
            where: {
              timeEntryId: {
                $in: relevantTimeEntryIds,
              },
            },
            orderBy: viewOrderBy,
            skip,
            take,
          },
          [
            'timeEntryId',
            'date',
            'hours',
            'notes',
            'submissionStatus',
            'submissionDate',
            'memberEmployeeId',
            'memberEmployeeName',
            'projectId',
            'projectName',
            'projectPositionName',
            'projectActivityId',
            'projectActivityName',
          ]
        );

        log.debug('TimeEntries query result', { count: timeEntries.length });

        // Transform the view data to the OData format
        const instances = timeEntries.map((entry) => ({
          id: entry.timeEntryId,
          date: entry.date,
          hours: entry.hours,
          notes: entry.notes,
          submissionStatus:
            entry.submissionStatus as EvstTimesheetSubmissionStatus,
          submissionDate: entry.submissionDate,
          employeeId: entry.memberEmployeeId,
          employeeName: entry.memberEmployeeName,
          projectId: entry.projectId,
          projectName: entry.projectName,
          positionName: entry.projectPositionName,
          activityId: entry.projectActivityId,
          activityName: entry.projectActivityName,
        }));

        return {
          instances,
          count: count ? instances.length : undefined,
        };
      },
    },
    update: {
      async execute({
        session: _session,
        id: _id,
        data: _data,
      }: TimeApprovalService.EntitySets.TimeEntries.Update.Execute.Request): Promise<void> {
        log.debug('TimeEntries.update.execute called', {
          id: _id,
          data: _data,
        });

        // For now, this is a placeholder for future implementation
        // This could be used to update individual time entry fields if needed
      },
    },
  },

  // ApproveTimeEntries Action implementation
  ApproveTimeEntries: {
    async execute({
      session,
      input,
    }: TimeApprovalService.Actions.ApproveTimeEntries.Execute.Request): Promise<TimeApprovalService.Actions.ApproveTimeEntries.Execute.Response> {
      log.debug('ApproveTimeEntries.execute called', { input });

      const { timeEntryIds } = input;

      const { employeeId } = await EmployeeApi.getEmployeeId.execute(
        session,
        {}
      );

      // Convert timeEntryIds to numbers
      const numericTimeEntryIds = timeEntryIds.map(Number);

      const result = await DocumentApprovalApi.approveDocuments.execute(
        session,
        {
          businessObjectType: TIMEENTRY_DOCUMENT,
          businessObjectIds: numericTimeEntryIds,
          path: TIMEENTRY_PATH,
          options: {
            employeeId,
          },
        }
      );

      log.debug('Time entries approval result', result);

      const unsuccessfulApprovals = result.results.filter(
        (res) => !res.success
      );
      if (unsuccessfulApprovals.length > 0) {
        throw new Error(
          `Time entries with ids ${unsuccessfulApprovals.map(
            (u) => u.businessObjectId
          )} could not be approved.`
        );
      }

      return { output: {} };
    },
  },

  // RejectTimeEntries Action implementation
  RejectTimeEntries: {
    async execute({
      session,
      input,
    }: TimeApprovalService.Actions.RejectTimeEntries.Execute.Request): Promise<TimeApprovalService.Actions.RejectTimeEntries.Execute.Response> {
      log.debug('RejectTimeEntries.execute called', { input });

      const { timeEntryIds } = input;
      // Since projectIds is not yet in the type definition, we'll need to handle it differently
      // until the type definition is updated to match the .ls file
      const { employeeId } = await EmployeeApi.getEmployeeId.execute(
        session,
        {}
      );

      // Convert timeEntryIds to numbers
      const numericTimeEntryIds = timeEntryIds.map(Number);

      const result = await DocumentApprovalApi.rejectDocuments.execute(
        session,
        {
          businessObjectType: TIMEENTRY_DOCUMENT,
          businessObjectIds: numericTimeEntryIds,
          path: TIMEENTRY_PATH,
          options: {
            employeeId,
          },
        }
      );

      log.debug('Time entries rejection result', result);

      const unsuccessfulRejections = result.results.filter(
        (res) => !res.success
      );
      if (unsuccessfulRejections.length > 0) {
        throw new Error(
          `Time entries with ids ${unsuccessfulRejections.map(
            (u) => u.businessObjectId
          )} could not be rejected.`
        );
      }

      return { output: {} };
    },
  },

  // CheckApprovalPolicies Action implementation
  CheckApprovalPolicies: {
    async execute({
      session,
      input,
    }: TimeApprovalService.Actions.CheckApprovalPolicies.Execute.Request): Promise<TimeApprovalService.Actions.CheckApprovalPolicies.Execute.Response> {
      log.debug('CheckApprovalPolicies.execute called', { input });

      const response =
        await DocumentApprovalApi.getApprovalPolicyHeadersByDocumentType.execute(
          session,
          {
            documentType:
              GenericApprovalPolicy.ApprovalPolicyBusinessObject.TimeEntry,
          }
        );

      const hasApprovalPolicies =
        response.results && response.results.length > 0;

      return {
        output: {
          hasApprovalPolicies,
        },
      };
    },
  },
};

export default implementation;
