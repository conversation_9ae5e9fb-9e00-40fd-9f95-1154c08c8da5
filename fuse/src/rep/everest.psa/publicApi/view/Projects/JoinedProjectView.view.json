{"type": "tdp-ts", "visibility": "public", "metadata": [{"name": "select", "type": "String", "parameter": true}, {"name": "disablePermissionCheck", "type": "Boolean", "parameter": true}, {"name": "active", "type": "Boolean"}, {"name": "psaProjectId", "type": "Int"}, {"name": "invoiced", "type": "Boolean"}, {"name": "created<PERSON>y", "type": "String"}, {"name": "createdDate", "type": "DateTime"}, {"name": "lastModifiedBy", "type": "String"}, {"name": "lastModifiedDate", "type": "DateTime"}, {"name": "projectId", "type": "ID"}, {"name": "billable", "type": "Boolean"}, {"name": "status", "type": "String"}, {"name": "notes", "type": "String"}, {"name": "currency", "type": "String"}, {"name": "budgetAmount", "type": "Decimal"}, {"name": "revenueRecordedTimeToDate", "type": "Decimal"}, {"name": "revenueAccomplishedMilestones", "type": "Decimal"}, {"name": "revenueFixedFeeToDate", "type": "Decimal"}, {"name": "projectRevenueToDate", "type": "Decimal"}, {"name": "projectBudgetRemaining", "type": "Decimal"}, {"name": "budgetCurrency", "type": "String"}, {"name": "id", "type": "Int"}, {"name": "budgetId", "type": "Int"}, {"name": "startDate", "type": "PlainDateTime"}, {"name": "endDate", "type": "PlainDateTime"}, {"name": "projectName", "type": "String"}, {"name": "projectCode", "type": "String"}, {"name": "milestoneId", "type": "Int"}, {"name": "milestoneName", "type": "String"}, {"name": "milestoneDescription", "type": "String"}, {"name": "milestoneAmount", "type": "Decimal"}, {"name": "milestoneCurrency", "type": "String"}, {"name": "milestoneDueDate", "type": "PlainDate"}, {"name": "milestoneStatus", "type": "String"}, {"name": "milestonePsaProjectId", "type": "Int"}, {"name": "milestoneInvoiceId", "type": "Int"}, {"name": "milestoneInvoiceMilestoneId", "type": "Int"}, {"name": "milestoneBillable", "type": "Boolean"}, {"name": "milestoneSalesOrderProductLineId", "type": "Int"}, {"name": "salesArrangementId", "type": "Int"}, {"name": "salesArrangementCurrency", "type": "String"}, {"name": "salesArrangementCustomerId", "type": "Int"}, {"name": "milestoneSalesOrderId", "type": "Int"}, {"name": "salesArrangementCustomerName", "type": "String"}, {"name": "salesArrangementEntityName", "type": "String"}, {"name": "salesArrangementEntityId", "type": "Int"}, {"name": "salesRepresentativeId", "type": "Int"}, {"name": "salesRepresentativeName", "type": "String"}, {"name": "psaProjectTypeId", "type": "Int"}, {"name": "psaProjectTypeName", "type": "String"}, {"name": "salesOrderId", "type": "Int"}, {"name": "salesOrderNumber", "type": "String"}, {"name": "departmentId", "type": "ID"}, {"name": "memberId", "type": "Int"}, {"name": "memberInvoiced", "type": "Boolean"}, {"name": "memberEmployeeId", "type": "Int"}, {"name": "memberName", "type": "String"}, {"name": "memberEmail", "type": "String"}, {"name": "memberCapacity", "type": "Int"}, {"name": "memberIsTeamLead", "type": "Boolean"}, {"name": "teamMemberId", "type": "Int"}, {"name": "memberDisplayName", "type": "String"}, {"name": "memberProjectPositionId", "type": "Int"}, {"name": "positionId", "type": "Int"}, {"name": "positionName", "type": "String"}, {"name": "positionDescription", "type": "String"}, {"name": "positionSalesOrderProductLineId", "type": "Int"}, {"name": "positionBillable", "type": "Boolean"}, {"name": "positionHourlyBillRate", "type": "Decimal"}, {"name": "positionHourlyBillRateCurrency", "type": "String"}, {"name": "positionHourlyCostRate", "type": "Decimal"}, {"name": "positionHourlyCostRateCurrency", "type": "String"}, {"name": "positionHoursBudget", "type": "Decimal"}, {"name": "positionInvoiced", "type": "Boolean"}, {"name": "activityId", "type": "Int"}, {"name": "activityName", "type": "String"}, {"name": "activityDescription", "type": "String"}, {"name": "activityHoursBudget", "type": "Decimal"}, {"name": "activityInvoiced", "type": "Boolean"}, {"name": "activityStartDate", "type": "PlainDate"}, {"name": "activityEndDate", "type": "PlainDate"}, {"name": "isBillableActivity", "type": "Boolean"}, {"name": "fixedFeeId", "type": "Int"}, {"name": "fixedFeeAmount", "type": "Decimal"}, {"name": "fixedFeeCurrency", "type": "String"}, {"name": "fixedFeeEndDate", "type": "PlainDate"}, {"name": "fixedFeeFrequency", "type": "String"}, {"name": "fixedFeeSalesOrderProductLineId", "type": "Int"}, {"name": "fixedFeeStartDate", "type": "PlainDate"}, {"name": "fixedFeePsaProjectId", "type": "Int"}]}