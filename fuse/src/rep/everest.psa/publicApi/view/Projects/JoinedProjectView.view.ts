import type { Co<PERSON><PERSON><PERSON><PERSON> } from '@everestsystems/content-core/lib/analytics/tables';
import type { TdpBuilderNode } from '@pkg/everest.analytics/public/tableProcessing/pipelineBuilder';
import {
  fromModel,
  PipelineBuilder,
} from '@pkg/everest.analytics/public/tableProcessing/pipelineBuilder';
import type { TdpTsViewQueryArgs } from '@pkg/everest.analytics/public/views/types';
import { SalesArrangement } from '@pkg/everest.fin.accounting/types/SalesArrangement';
import { SalesOrder } from '@pkg/everest.fin.accounting/types/SalesOrder';
import { SalesOrderProductLine } from '@pkg/everest.fin.accounting/types/SalesOrderProductLine';
import { EmployeeView } from '@pkg/everest.hr.base/types/views/publicApi/view/EmployeeView/EmployeeView';
import PsaPermissionApi from '@pkg/everest.psa/lib/permissions/PsaPermissionApi';
import { EvstFixedFeeFrequency } from '@pkg/everest.psa/types/enums/FixedFeeFrequency';
import { EvstMilestoneStatus } from '@pkg/everest.psa/types/enums/MilestoneStatus';
import { FixedFee } from '@pkg/everest.psa/types/FixedFee';
import { Milestone } from '@pkg/everest.psa/types/Milestone';
import { PsaProject } from '@pkg/everest.psa/types/PsaProject';
import { PsaProjectType } from '@pkg/everest.psa/types/PsaProjectType';
import { TimeEntry } from '@pkg/everest.timetracking/types/TimeEntry';
import { Projects } from '@pkg/everest.timetracking/types/views/publicApi/view/Projects/Projects';

// We'll use the existing type structure to maintain compatibility

/**
 * Joined view combining PsaProject and Project data
 * This view joins PsaProject from the PSA package with ProjectView from the HR base package
 * using the projectId field from PsaProject that links to the id field in ProjectView
 *
 * @param args - Query arguments
 * @param args.params.select - Optional parameter to control joins:
 *                            'milestones' to include milestone data
 *                            'salesOrders' to include sales order data
 *                            'members' to include member data
 *                            'membersAndPositions' to include both members and positions
 *                            'positions' to include position data
 *                            'activities' to include activity data
 *
 */
export default async function query(
  args: TdpTsViewQueryArgs
): Promise<ComputeJson> {
  const pipeline = new PipelineBuilder('JoinedProjectView');
  const select = args.params?.select as string | undefined;

  // Create a PsaProject view
  let psaProjectView = fromModel(PsaProject, pipeline).select([
    { name: 'id', as: 'psaProjectId' },
    'invoiced',
    'createdBy',
    'createdDate',
    'lastModifiedBy',
    'lastModifiedDate',
    'projectId',
    'billable',
    { name: 'salesArrangementId', as: 'internalSalesArrangementId' },
    'status',
    'currency',
    'notes',
    { name: 'budget.amount', as: 'budgetAmount' },
    { name: 'budget.currency', as: 'budgetCurrency' },
    'projectCode',
    'salesRepresentativeId',
    'typeId',
    'departmentId',
  ] as const);

  // Add permission check to only include PsaProjects where the user is a member of the team
  psaProjectView = await PsaPermissionApi.extendViewWithProjectPermissionCheck(
    args,
    pipeline,
    psaProjectView
  );

  const nonProjectSelectParams = ['milestones', 'salesOrders', 'fixedFee'];
  let selectParam;
  // This is ugly, but we need the select to be dynamic based on the `select` parameter. 'ALL_FIELDS' will fail, due to the existence of the `select` parameter.
  switch (select) {
    case 'members': {
      selectParam = [
        { name: 'projectId', as: 'id' },
        'teamId',
        'startDate',
        'endDate',
        'projectName',
        'budgetId',
        'active',
        'memberEmployeeId',
        'memberIsTeamLead',
        'memberName',
        'memberDisplayName',
        'memberEmail',
        'memberCapacity',
      ] as const;
      break;
    }
    case 'membersAndPositions': {
      selectParam = [
        { name: 'projectId', as: 'id' },
        'teamId',
        'startDate',
        'endDate',
        'projectName',
        'budgetId',
        'active',
        'memberEmployeeId',
        'memberIsTeamLead',
        'memberName',
        'memberDisplayName',
        'memberEmail',
        'memberCapacity',
        'memberId',
        'teamMemberId',
        'memberProjectPositionId',
        'memberInvoiced',
        'positionId',
        'positionName',
        'positionDescription',
        'positionSalesOrderProductLineId',
        'positionBillable',
        'positionHourlyBillRate',
        'positionHourlyBillRateCurrency',
        'positionHourlyCostRate',
        'positionHourlyCostRateCurrency',
        'positionHoursBudget',
        'positionInvoiced',
      ] as const;
      break;
    }
    case 'positions': {
      selectParam = [
        { name: 'projectId', as: 'id' },
        'teamId',
        'startDate',
        'endDate',
        'projectName',
        'budgetId',
        'active',
        'positionId',
        'positionName',
        'positionDescription',
        'positionSalesOrderProductLineId',
        'positionBillable',
        'positionHourlyBillRate',
        'positionHourlyBillRateCurrency',
        'positionHourlyCostRate',
        'positionHourlyCostRateCurrency',
        'positionHoursBudget',
        'positionInvoiced',
      ] as const;
      break;
    }
    case 'activities': {
      selectParam = [
        { name: 'projectId', as: 'id' },
        'teamId',
        'startDate',
        'endDate',
        'projectName',
        'budgetId',
        'active',
        'activityId',
        'activityName',
        'activityDescription',
        'activityHoursBudget',
        'isBillableActivity',
        'activityInvoiced',
        'activityStartDate',
        'activityEndDate',
        'activityMilestoneId',
        'activityFixedFeeId',
        'activityBillingType',
        'memberId',
        'memberInvoiced',
        'teamMemberId',
        'memberEmployeeId',
        'memberIsTeamLead',
        'memberName',
        'memberDisplayName',
        'memberEmail',
        'memberCapacity',
        'positionId',
        'positionName',
        'positionDescription',
        'positionSalesOrderProductLineId',
        'positionBillable',
        'positionHourlyBillRate',
        'positionHourlyBillRateCurrency',
        'positionHourlyCostRate',
        'positionHourlyCostRateCurrency',
        'positionHoursBudget',
        'positionInvoiced',
      ] as const;
      break;
    }
    default: {
      if (select && !nonProjectSelectParams.includes(select)) {
        throw new Error(
          `Invalid select parameter: ${select}. Valid options are: ${[
            ...nonProjectSelectParams,
            'members',
            'membersAndPositions',
            'positions',
            'activities',
          ].join(', ')}.`
        );
      }

      selectParam = [
        { name: 'projectId', as: 'id' },
        'teamId',
        'startDate',
        'endDate',
        'projectName',
        'budgetId',
        'active',
      ] as const;
      break;
    }
  }
  let projectViewRaw = fromModel(Projects, pipeline);
  if (select && !nonProjectSelectParams.includes(select)) {
    projectViewRaw = projectViewRaw.where({ select: select });
  }
  const projectView = projectViewRaw.select(selectParam);

  // Perform the join between PsaProject and ProjectView
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let joinedView: TdpBuilderNode<any> = psaProjectView.join(
    'inner',
    projectView,
    { projectId: { $take: 'id' } } // Join on PsaProject.projectId = Project.projectId
  );

  joinedView = joinedView
    // Join with EmployeeView for sales representative
    .join(
      'left',
      fromModel(EmployeeView, pipeline).select([
        { name: 'id', as: 'employeeId' },
        { name: 'name', as: 'salesRepresentativeName' },
      ] as const),
      {
        salesRepresentativeId: { $take: 'employeeId' },
      }
    )
    // Join with PsaProjectType for project type
    .join(
      'left',
      fromModel(PsaProjectType, pipeline).select([
        { name: 'id', as: 'psaProjectTypeId' },
        { name: 'name', as: 'psaProjectTypeName' },
      ] as const),
      {
        typeId: { $take: 'psaProjectTypeId' },
      }
    );
  // Always join SalesArrangement
  joinedView = joinSalesArrangement(pipeline, joinedView);

  // Always join calculated revenue
  joinedView = joinCalculatedRevenue(pipeline, joinedView);

  if (select) {
    switch (select) {
      case 'milestones': {
        joinedView = joinMilestones(pipeline, joinedView);
        break;
      }
      case 'salesOrders': {
        joinedView = joinSalesOrders(pipeline, joinedView);
        break;
      }
      case 'fixedFee': {
        joinedView = joinFixedFee(pipeline, joinedView);
        break;
      }
    }
  }

  // This is necessary due to a bug in the TDP engine: https://github.com/everestsystems/kernel/issues/4269
  const fieldsToAdd = ['projectRevenueToDate', 'projectBudgetRemaining'];
  joinedView = joinedView.select([
    ...new Set([...args.fields, ...fieldsToAdd]),
  ]);
  pipeline.setFlag('everest.analytics', 'executionBackend', 'kernel');

  return pipeline.build([joinedView.unwrap()]);
}

function joinCalculatedRevenue(
  pipeline: PipelineBuilder,
  view: TdpBuilderNode<any>
): TdpBuilderNode<any> {
  const todayISO = new Date().toISOString();

  const fixedFeeRevenueView = fromModel(FixedFee, pipeline)
    .where({
      frequency: EvstFixedFeeFrequency.Monthly,
      startDate: {
        $lte: todayISO, // FixedFee start date should be in the past
      },
    })
    .select(['psaProjectId', 'totalAmountToDate'] as const)
    .aggregate(['psaProjectId'] as const, {
      totalAmountToDate: 'sum',
    })
    .select([
      { name: 'psaProjectId', as: 'fixedFeeAggregatedPsaProjectId' },
      { name: 'totalAmountToDate', as: 'fixedFeeRevenueToDate' },
    ] as const);

  view = view.join('left', fixedFeeRevenueView, {
    psaProjectId: { $take: 'fixedFeeAggregatedPsaProjectId' },
  });

  const recordedTimeRevenueView = fromModel(TimeEntry, pipeline)
    .where({
      date: {
        $lte: todayISO, // TimeEntry date should be in the past
      },
    })
    .select(['projectId', 'revenue'] as const)
    .aggregate(['projectId'] as const, {
      revenue: 'sum',
    })
    .select([
      { name: 'projectId', as: 'timeEntryProjectId' },
      { name: 'revenue', as: 'revenueRecordedTimeToDate' },
    ] as const);

  view = view.join('left', recordedTimeRevenueView, {
    projectId: { $take: 'timeEntryProjectId' },
  });

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const milestoneRevenueView: TdpBuilderNode<any> = fromModel(
    Milestone,
    pipeline
  )
    .where({
      status: {
        $in: [EvstMilestoneStatus.Invoiced, EvstMilestoneStatus.Accomplished],
      },
    })
    .select([
      { name: 'amount.amount', as: 'revenueAccomplishedMilestones' },
      'psaProjectId',
    ] as const);

  const milestoneRevenueViewAggregated = milestoneRevenueView
    .aggregate(['psaProjectId'] as const, {
      revenueAccomplishedMilestones: 'sum',
    })
    .select([
      { name: 'psaProjectId', as: 'milestoneRevenuePsaProjectId' },
      {
        name: 'revenueAccomplishedMilestones',
        as: 'revenueAccomplishedMilestones',
      },
    ] as const);

  view = view.join('left', milestoneRevenueViewAggregated, {
    psaProjectId: { $take: 'milestoneRevenuePsaProjectId' },
  });

  view = view
    .formula(
      'decimal',
      'coalesce($.fixedFeeRevenueToDate, decimal.new(0)) + coalesce($.revenueRecordedTimeToDate, decimal.new(0)) + coalesce($.revenueAccomplishedMilestones, decimal.new(0))',
      'projectRevenueToDate'
    )
    .formula(
      'decimal',
      '$.budgetAmount - $.projectRevenueToDate',
      'projectBudgetRemaining'
    );

  return view;
}

/**
 * Join SalesArrangement to the project view
 * @param pipeline - The pipeline builder
 * @param view - The view to join sales arrangement to
 * @returns The view with sales arrangement joined
 */
function joinSalesArrangement(
  pipeline: PipelineBuilder,
  view: TdpBuilderNode<any>
): TdpBuilderNode<any> {
  const salesArrangementView = fromModel(SalesArrangement, pipeline).select([
    { name: 'id', as: 'salesArrangementId' },
    { name: 'functionalCurrency', as: 'salesArrangementCurrency' },
    { name: 'businessPartnerId', as: 'salesArrangementCustomerId' },
    { name: 'businessPartnerName', as: 'salesArrangementCustomerName' },
    { name: 'entityId', as: 'salesArrangementEntityId' },
    { name: 'entityName', as: 'salesArrangementEntityName' },
  ] as const);

  return view.join('left', salesArrangementView, {
    internalSalesArrangementId: { $take: 'salesArrangementId' },
  });
}

function joinFixedFee(
  pipeline: PipelineBuilder,
  view: TdpBuilderNode<any>
): TdpBuilderNode<any> {
  const salesArrangementView = fromModel(FixedFee, pipeline).select([
    { name: 'id', as: 'fixedFeeId' },
    { name: 'amount.amount', as: 'fixedFeeAmount' },
    { name: 'amount.currency', as: 'fixedFeeCurrency' },
    { name: 'endDate', as: 'fixedFeeEndDate' },
    { name: 'frequency', as: 'fixedFeeFrequency' },
    { name: 'salesOrderProductLineId', as: 'fixedFeeSalesOrderProductLineId' },
    { name: 'startDate', as: 'fixedFeeStartDate' },
    { name: 'psaProjectId', as: 'fixedFeePsaProjectId' },
  ] as const);

  return view.join('left', salesArrangementView, {
    psaProjectId: { $take: 'fixedFeePsaProjectId' },
  });
}

/**
 * Join milestones to the project view
 * @param pipeline - The pipeline builder
 * @param view - The view to join milestones to
 * @returns The view with milestones joined
 */
function joinMilestones(
  pipeline: PipelineBuilder,
  view: TdpBuilderNode<any>
): TdpBuilderNode<any> {
  const milestoneView = fromModel(Milestone, pipeline)
    .select([
      { name: 'id', as: 'milestoneId' },
      { name: 'name', as: 'milestoneName' },
      { name: 'description', as: 'milestoneDescription' },
      { name: 'amount.amount', as: 'milestoneAmount' },
      { name: 'amount.currency', as: 'milestoneCurrency' },
      { name: 'dueDate', as: 'milestoneDueDate' },
      { name: 'status', as: 'milestoneStatus' },
      { name: 'psaProjectId', as: 'milestonePsaProjectId' },
      { name: 'invoiceId', as: 'milestoneInvoiceId' },
      { name: 'invoiceMilestoneId', as: 'milestoneInvoiceMilestoneId' },
      { name: 'billable', as: 'milestoneBillable' },
      {
        name: 'salesOrderProductLineId',
        as: 'milestoneSalesOrderProductLineId',
      },
    ] as const)
    .join(
      'left',
      fromModel(SalesOrderProductLine, pipeline).select([
        { name: 'id', as: 'soplId' },
        { name: 'salesOrderId', as: 'milestoneSalesOrderId' },
      ] as const),
      { soplId: { $take: 'milestoneSalesOrderProductLineId' } }
    );

  return view.join('left', milestoneView, {
    psaProjectId: { $take: 'milestonePsaProjectId' },
  });
}

/**
 * Join SalesOrder to the project view
 * @param pipeline - The pipeline builder
 * @param view - The view to join sales orders to
 * @returns The view with sales orders joined
 */
function joinSalesOrders(
  pipeline: PipelineBuilder,
  view: TdpBuilderNode<any>
): TdpBuilderNode<any> {
  const salesOrderView = fromModel(SalesOrder, pipeline).select([
    { name: 'id', as: 'salesOrderId' },
    { name: 'salesOrderNumber', as: 'salesOrderNumber' },
    { name: 'salesArrangementId', as: 'salesOrderSalesArrangementId' },
  ] as const);

  return view.join('left', salesOrderView, {
    internalSalesArrangementId: { $take: 'salesOrderSalesArrangementId' },
  });
}
