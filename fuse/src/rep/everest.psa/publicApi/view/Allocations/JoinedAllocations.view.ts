import type { Compute<PERSON><PERSON> } from '@everestsystems/content-core/lib/analytics/tables';
import type { TdpBuilderNode } from '@pkg/everest.analytics/public/tableProcessing/pipelineBuilder';
import {
  fromModel,
  PipelineBuilder,
} from '@pkg/everest.analytics/public/tableProcessing/pipelineBuilder';
import type { TdpTsViewQueryArgs as ViewArgs } from '@pkg/everest.analytics/public/views/types';
import PsaPermissionApi from '@pkg/everest.psa/lib/permissions/PsaPermissionApi';
import { PsaProject } from '@pkg/everest.psa/types/PsaProject';
import { Allocations } from '@pkg/everest.timetracking/types/views/publicApi/view/Allocations/Allocations';

/**
 * View that lists allocations joined with PSA projects.
 * This view provides a comprehensive list of allocations along with their associated PSA project information.
 *
 * @param args - Query arguments
 * @param args.params.disablePermissionCheck - Optional parameter to disable permission checking.
 *                                             When false or undefined, only allocations for projects
 *                                             where the user is a team member are returned.
 */
export default async function query(args: ViewArgs): Promise<ComputeJson> {
  const pipeline = new PipelineBuilder('JoinedAllocations');
  let joinedAllocationsView: TdpBuilderNode<any> = fromModel(
    Allocations,
    pipeline
  );

  const allocationEntriesFields = [
    'allocationEntryId',
    'allocationEntryDate',
    'allocationEntryHours',
    'allocationEntryWeek',
    'allocationEntryMonth',
  ];

  const includeAllocationEntries = args.params?.includeAllocationEntries as
    | boolean
    | undefined;

  if (includeAllocationEntries) {
    joinedAllocationsView = joinedAllocationsView.where({
      includeAllocationEntries: includeAllocationEntries,
    });
  }

  joinedAllocationsView = joinedAllocationsView.select([
    'allocationId',
    'active',
    'startDate',
    'endDate',
    'memberId',
    'projectActivityId',
    'totalHours',
    'notes',
    'method',
    'workingDays',
    'memberActive',
    'employeeId',
    'memberIsTeamLead',
    'memberName',
    'memberProjectPositionId',
    'memberCapacity',
    'projectActivityActive',
    'projectActivityName',
    'projectActivityDescription',
    'projectActivityHoursBudget',
    'projectActivityIsBillable',
    'projectActivityStartDate',
    'projectActivityEndDate',
    'projectPositionId',
    'projectPositionActive',
    'projectPositionName',
    'projectPositionDescription',
    'projectPositionHourlyBillRateAmount',
    'projectPositionHourlyBillRateCurrency',
    'projectPositionHoursBudget',
    'projectId',
    'projectActive',
    'projectName',
    'projectStartDate',
    'projectEndDate',
    'projectStatus',
    ...(includeAllocationEntries ? allocationEntriesFields : []),
  ]);

  // Add permission check to only include allocations where the user is a member of the team
  joinedAllocationsView =
    await PsaPermissionApi.extendViewWithProjectPermissionCheck(
      args,
      pipeline,
      joinedAllocationsView
    );

  // Create a view for PsaProject with selected fields
  const psaProjectView = fromModel(PsaProject, pipeline).select([
    { name: 'id', as: 'psaProjectId' },
    { name: 'billable', as: 'psaProjectBillable' },
    { name: 'budget.amount', as: 'psaProjectBudgetAmount' },
    { name: 'budget.currency', as: 'psaProjectBudgetCurrency' },
    { name: 'currency', as: 'psaProjectCurrency' },
    { name: 'departmentId', as: 'psaProjectDepartmentId' },
    { name: 'invoiced', as: 'psaProjectInvoiced' },
    { name: 'notes', as: 'psaProjectNotes' },
    { name: 'projectCode', as: 'psaProjectProjectCode' },
    { name: 'projectId', as: 'psaProjectProjectId' },
    { name: 'salesArrangementId', as: 'psaProjectSalesArrangementId' },
    { name: 'salesRepresentativeId', as: 'psaProjectSalesRepresentativeId' },
    { name: 'status', as: 'psaProjectStatus' },
    { name: 'typeId', as: 'psaProjectTypeId' },
  ] as const);

  // Join allocations with PsaProject using projectId
  joinedAllocationsView = joinedAllocationsView.join('left', psaProjectView, {
    projectId: { $take: 'psaProjectProjectId' },
  });

  joinedAllocationsView = joinedAllocationsView.select(args.fields);
  pipeline.setFlag('everest.analytics', 'executionBackend', 'kernel');
  const result = pipeline.build([joinedAllocationsView.unwrap()]);
  return result;
}
