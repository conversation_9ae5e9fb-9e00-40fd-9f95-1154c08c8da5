{"type": "tdp-ts", "visibility": "public", "metadata": [{"name": "includeAllocationEntries", "type": "Boolean", "parameter": true}, {"name": "disablePermissionCheck", "type": "Boolean", "parameter": true}, {"name": "allocationId", "type": "Int"}, {"name": "active", "type": "Boolean"}, {"name": "startDate", "type": "PlainDate"}, {"name": "endDate", "type": "PlainDate"}, {"name": "memberId", "type": "ID"}, {"name": "projectActivityId", "type": "ID"}, {"name": "totalHours", "type": "Decimal"}, {"name": "notes", "type": "String"}, {"name": "method", "type": "String"}, {"name": "workingDays", "type": "JSON"}, {"name": "memberActive", "type": "Boolean"}, {"name": "employeeId", "type": "ID"}, {"name": "memberIsTeamLead", "type": "Boolean"}, {"name": "memberName", "type": "String"}, {"name": "memberProjectPositionId", "type": "ID"}, {"name": "memberCapacity", "type": "Int"}, {"name": "projectActivityActive", "type": "Boolean"}, {"name": "projectActivityName", "type": "String"}, {"name": "projectActivityDescription", "type": "String"}, {"name": "projectActivityStartDate", "type": "PlainDate"}, {"name": "projectActivityEndDate", "type": "PlainDate"}, {"name": "projectActivityHoursBudget", "type": "Decimal"}, {"name": "projectActivityIsBillable", "type": "Boolean"}, {"name": "projectPositionId", "type": "ID"}, {"name": "projectPositionActive", "type": "Boolean"}, {"name": "projectPositionName", "type": "String"}, {"name": "projectPositionDescription", "type": "String"}, {"name": "projectPositionHourlyBillRateAmount", "type": "Decimal"}, {"name": "projectPositionHourlyBillRateCurrency", "type": "String"}, {"name": "projectPositionHoursBudget", "type": "Decimal"}, {"name": "projectId", "type": "ID"}, {"name": "projectActive", "type": "Boolean"}, {"name": "projectName", "type": "String"}, {"name": "projectStartDate", "type": "PlainDateTime"}, {"name": "projectEndDate", "type": "PlainDateTime"}, {"name": "projectStatus", "type": "String"}, {"name": "psaProjectId", "type": "ID"}, {"name": "psaProjectBillable", "type": "Boolean"}, {"name": "psaProjectBudgetAmount", "type": "Decimal"}, {"name": "psaProjectBudgetCurrency", "type": "String"}, {"name": "psaProjectCurrency", "type": "String"}, {"name": "psaProjectDepartmentId", "type": "ID"}, {"name": "psaProjectInvoiced", "type": "Boolean"}, {"name": "psaProjectNotes", "type": "String"}, {"name": "psaProjectProjectCode", "type": "String"}, {"name": "psaProjectSalesArrangementId", "type": "ID"}, {"name": "psaProjectSalesRepresentativeId", "type": "ID"}, {"name": "psaProjectStatus", "type": "String"}, {"name": "psaProjectTypeId", "type": "ID"}, {"name": "allocationEntryId", "type": "Int"}, {"name": "allocationEntryDate", "type": "PlainDate"}, {"name": "allocationEntryHours", "type": "Decimal"}, {"name": "allocationEntryWeek", "type": "PlainDate"}, {"name": "allocationEntryMonth", "type": "PlainDate"}]}