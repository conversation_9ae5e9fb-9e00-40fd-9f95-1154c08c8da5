/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// ******************************************************************************
// Generated Everest Types
// ******************************************************************************

import type { Decimal, RoundingMode } from '@everestsystems/decimal';
import type {
  ArgType,
  Association,
  AssociationManagementArgs,
  AsyncResult,
  Controller,
  ControllerClientProvider,
  CreateOptionsArg,
  DataWithMetadata,
  DeleteOptionsArg,
  FieldSelector,
  Filter,
  flattenFieldSelector,
  GraphInstanceEntry,
  KernelRequestOptions,
  NonAssociationFields,
  Optional,
  PurgeArgs,
  ReadOptionsArg,
  ReadReturnType as ReadReturnTypeGeneric,
  ReadReturnTypeWithAssociations,
  RequireAtLeastOne,
  SemanticSearchArg,
  SemanticSearchRequestOptions,
  TypeSafeQueryArgType,
  UpdateManyOptionsArg,
  UpdateOptionsArg,
  UpsertOptionsArg,
  WriteReturnType,
  WriteReturnTypeWithAssociations,
} from '@everestsystems/content-core';
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { EvstDateTime as everest_appserver_primitive_DateTime } from "@pkg/everest.appserver/types/primitives/DateTime";
import type { EvstID as everest_appserver_primitive_ID } from "@pkg/everest.appserver/types/primitives/ID";
import type { EvstDecimal as everest_appserver_primitive_Decimal } from "@pkg/everest.appserver/types/primitives/Decimal";
import type { EvstPlainDateTime as everest_appserver_primitive_PlainDateTime } from "@pkg/everest.appserver/types/primitives/PlainDateTime";
import type { EvstPlainDate as everest_appserver_primitive_PlainDate } from "@pkg/everest.appserver/types/primitives/PlainDate";

/** Generated types for View JoinedProjectView */
export namespace JoinedProjectView {
  export type JoinedProjectView = {
    select?: everest_appserver_primitive_Text | null;
    disablePermissionCheck?: everest_appserver_primitive_TrueFalse | null;
    active?: everest_appserver_primitive_TrueFalse | null;
    psaProjectId?: everest_appserver_primitive_Number | null;
    invoiced?: everest_appserver_primitive_TrueFalse | null;
    createdBy?: everest_appserver_primitive_Text | null;
    createdDate?: everest_appserver_primitive_DateTime | null;
    lastModifiedBy?: everest_appserver_primitive_Text | null;
    lastModifiedDate?: everest_appserver_primitive_DateTime | null;
    projectId?: everest_appserver_primitive_ID | null;
    billable?: everest_appserver_primitive_TrueFalse | null;
    status?: everest_appserver_primitive_Text | null;
    notes?: everest_appserver_primitive_Text | null;
    currency?: everest_appserver_primitive_Text | null;
    budgetAmount?: everest_appserver_primitive_Decimal | null;
    revenueRecordedTimeToDate?: everest_appserver_primitive_Decimal | null;
    revenueAccomplishedMilestones?: everest_appserver_primitive_Decimal | null;
    revenueFixedFeeToDate?: everest_appserver_primitive_Decimal | null;
    projectRevenueToDate?: everest_appserver_primitive_Decimal | null;
    projectBudgetRemaining?: everest_appserver_primitive_Decimal | null;
    budgetCurrency?: everest_appserver_primitive_Text | null;
    id?: everest_appserver_primitive_Number | null;
    budgetId?: everest_appserver_primitive_Number | null;
    startDate?: everest_appserver_primitive_PlainDateTime | null;
    endDate?: everest_appserver_primitive_PlainDateTime | null;
    projectName?: everest_appserver_primitive_Text | null;
    projectCode?: everest_appserver_primitive_Text | null;
    milestoneId?: everest_appserver_primitive_Number | null;
    milestoneName?: everest_appserver_primitive_Text | null;
    milestoneDescription?: everest_appserver_primitive_Text | null;
    milestoneAmount?: everest_appserver_primitive_Decimal | null;
    milestoneCurrency?: everest_appserver_primitive_Text | null;
    milestoneDueDate?: everest_appserver_primitive_PlainDate | null;
    milestoneStatus?: everest_appserver_primitive_Text | null;
    milestonePsaProjectId?: everest_appserver_primitive_Number | null;
    milestoneInvoiceId?: everest_appserver_primitive_Number | null;
    milestoneInvoiceMilestoneId?: everest_appserver_primitive_Number | null;
    milestoneBillable?: everest_appserver_primitive_TrueFalse | null;
    milestoneSalesOrderProductLineId?: everest_appserver_primitive_Number | null;
    salesArrangementId?: everest_appserver_primitive_Number | null;
    salesArrangementCurrency?: everest_appserver_primitive_Text | null;
    salesArrangementCustomerId?: everest_appserver_primitive_Number | null;
    milestoneSalesOrderId?: everest_appserver_primitive_Number | null;
    salesArrangementCustomerName?: everest_appserver_primitive_Text | null;
    salesArrangementEntityName?: everest_appserver_primitive_Text | null;
    salesArrangementEntityId?: everest_appserver_primitive_Number | null;
    salesRepresentativeId?: everest_appserver_primitive_Number | null;
    salesRepresentativeName?: everest_appserver_primitive_Text | null;
    psaProjectTypeId?: everest_appserver_primitive_Number | null;
    psaProjectTypeName?: everest_appserver_primitive_Text | null;
    salesOrderId?: everest_appserver_primitive_Number | null;
    salesOrderNumber?: everest_appserver_primitive_Text | null;
    departmentId?: everest_appserver_primitive_ID | null;
    memberId?: everest_appserver_primitive_Number | null;
    memberInvoiced?: everest_appserver_primitive_TrueFalse | null;
    memberEmployeeId?: everest_appserver_primitive_Number | null;
    memberName?: everest_appserver_primitive_Text | null;
    memberEmail?: everest_appserver_primitive_Text | null;
    memberCapacity?: everest_appserver_primitive_Number | null;
    memberIsTeamLead?: everest_appserver_primitive_TrueFalse | null;
    teamMemberId?: everest_appserver_primitive_Number | null;
    memberDisplayName?: everest_appserver_primitive_Text | null;
    memberProjectPositionId?: everest_appserver_primitive_Number | null;
    positionId?: everest_appserver_primitive_Number | null;
    positionName?: everest_appserver_primitive_Text | null;
    positionDescription?: everest_appserver_primitive_Text | null;
    positionSalesOrderProductLineId?: everest_appserver_primitive_Number | null;
    positionBillable?: everest_appserver_primitive_TrueFalse | null;
    positionHourlyBillRate?: everest_appserver_primitive_Decimal | null;
    positionHourlyBillRateCurrency?: everest_appserver_primitive_Text | null;
    positionHourlyCostRate?: everest_appserver_primitive_Decimal | null;
    positionHourlyCostRateCurrency?: everest_appserver_primitive_Text | null;
    positionHoursBudget?: everest_appserver_primitive_Decimal | null;
    positionInvoiced?: everest_appserver_primitive_TrueFalse | null;
    activityId?: everest_appserver_primitive_Number | null;
    activityName?: everest_appserver_primitive_Text | null;
    activityDescription?: everest_appserver_primitive_Text | null;
    activityHoursBudget?: everest_appserver_primitive_Decimal | null;
    activityInvoiced?: everest_appserver_primitive_TrueFalse | null;
    activityStartDate?: everest_appserver_primitive_PlainDate | null;
    activityEndDate?: everest_appserver_primitive_PlainDate | null;
    isBillableActivity?: everest_appserver_primitive_TrueFalse | null;
    fixedFeeId?: everest_appserver_primitive_Number | null;
    fixedFeeAmount?: everest_appserver_primitive_Decimal | null;
    fixedFeeCurrency?: everest_appserver_primitive_Text | null;
    fixedFeeEndDate?: everest_appserver_primitive_PlainDate | null;
    fixedFeeFrequency?: everest_appserver_primitive_Text | null;
    fixedFeeSalesOrderProductLineId?: everest_appserver_primitive_Number | null;
    fixedFeeStartDate?: everest_appserver_primitive_PlainDate | null;
    fixedFeePsaProjectId?: everest_appserver_primitive_Number | null;
    };

  export type JoinedProjectViewParameters = {
    select: JoinedProjectView['select'];
    disablePermissionCheck: JoinedProjectView['disablePermissionCheck'];
    };

  export type UniqueFields = Partial<Pick<JoinedProjectView, 'select' | 'disablePermissionCheck' | 'active' | 'psaProjectId' | 'invoiced' | 'createdBy' | 'createdDate' | 'lastModifiedBy' | 'lastModifiedDate' | 'projectId' | 'billable' | 'status' | 'notes' | 'currency' | 'budgetAmount' | 'revenueRecordedTimeToDate' | 'revenueAccomplishedMilestones' | 'revenueFixedFeeToDate' | 'projectRevenueToDate' | 'projectBudgetRemaining' | 'budgetCurrency' | 'id' | 'budgetId' | 'startDate' | 'endDate' | 'projectName' | 'projectCode' | 'milestoneId' | 'milestoneName' | 'milestoneDescription' | 'milestoneAmount' | 'milestoneCurrency' | 'milestoneDueDate' | 'milestoneStatus' | 'milestonePsaProjectId' | 'milestoneInvoiceId' | 'milestoneInvoiceMilestoneId' | 'milestoneBillable' | 'milestoneSalesOrderProductLineId' | 'salesArrangementId' | 'salesArrangementCurrency' | 'salesArrangementCustomerId' | 'milestoneSalesOrderId' | 'salesArrangementCustomerName' | 'salesArrangementEntityName' | 'salesArrangementEntityId' | 'salesRepresentativeId' | 'salesRepresentativeName' | 'psaProjectTypeId' | 'psaProjectTypeName' | 'salesOrderId' | 'salesOrderNumber' | 'departmentId' | 'memberId' | 'memberInvoiced' | 'memberEmployeeId' | 'memberName' | 'memberEmail' | 'memberCapacity' | 'memberIsTeamLead' | 'teamMemberId' | 'memberDisplayName' | 'memberProjectPositionId' | 'positionId' | 'positionName' | 'positionDescription' | 'positionSalesOrderProductLineId' | 'positionBillable' | 'positionHourlyBillRate' | 'positionHourlyBillRateCurrency' | 'positionHourlyCostRate' | 'positionHourlyCostRateCurrency' | 'positionHoursBudget' | 'positionInvoiced' | 'activityId' | 'activityName' | 'activityDescription' | 'activityHoursBudget' | 'activityInvoiced' | 'activityStartDate' | 'activityEndDate' | 'isBillableActivity' | 'fixedFeeId' | 'fixedFeeAmount' | 'fixedFeeCurrency' | 'fixedFeeEndDate' | 'fixedFeeFrequency' | 'fixedFeeSalesOrderProductLineId' | 'fixedFeeStartDate' | 'fixedFeePsaProjectId'>>;
  export type UniqueWhereInput = Partial<JoinedProjectView>;
  export type ReadReturnType<U extends string | number | symbol = keyof JoinedProjectView> = ReadReturnTypeGeneric<JoinedProjectView, U>;

  export interface IControllerClient extends Omit<Controller<JoinedProjectView>, 'all' | 'create' | 'createMany' | 'delete' | 'deleteAll' | 'deleteMany' | 'explainRead' | 'exportMany' | 'getHistory' | 'importMany' | 'lock' | 'lookup' | 'purge' | 'semanticSearch' | 'unlock' | 'update' | 'updateMany' | 'upsert' | 'upsertMany'> {}

  /** @return a model controller instance for JoinedProjectView. */
  export function client(env: ControllerClientProvider) {
    return env.controllerClient<JoinedProjectView.IControllerClient>(MODEL_URN);
  }

  /** returns a list of all objects matching the requirements. */
  export async function query<U extends keyof JoinedProjectView, V extends string = 'ALL_FIELDS'>(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<JoinedProjectView>, 'draft'> | Omit<Omit<TypeSafeQueryArgType<JoinedProjectView>, 'draft'>, 'where'> & { where?: Partial<JoinedProjectView> }, fieldlist: ReadonlyArray<U | V> | 'ALL_FIELDS'): Promise<ReadReturnType<V>[]>;
  export async function query(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<JoinedProjectView>, 'draft'>, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown[]> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).query(args, fields as ReadonlyArray<string>);
  }

  export async function queryWithMetadata<U extends keyof JoinedProjectView = keyof JoinedProjectView>(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<JoinedProjectView>, 'draft'>, fieldlist: ReadonlyArray<U>): Promise<DataWithMetadata<JoinedProjectView, U>> {
    return (await client(env)).queryWithMetadata(args, fieldlist);
  }

  /** @deprecated Use the alternative overloaded method for reading associations. */
  export async function read<U extends keyof JoinedProjectView>(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnType<U>>;
  export async function read(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<unknown> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).read(where, fields as ReadonlyArray<keyof JoinedProjectView>, options);
  }

  export const MODEL_URN: string = 'urn:evst:everest:psa:model/view:publicApi/view/Projects/JoinedProjectView';
}
