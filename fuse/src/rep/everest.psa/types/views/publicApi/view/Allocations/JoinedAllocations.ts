/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// ******************************************************************************
// Generated Everest Types
// ******************************************************************************

import type { Decimal, RoundingMode } from '@everestsystems/decimal';
import type {
  ArgType,
  Association,
  AssociationManagementArgs,
  AsyncResult,
  Controller,
  ControllerClientProvider,
  CreateOptionsArg,
  DataWithMetadata,
  DeleteOptionsArg,
  FieldSelector,
  Filter,
  flattenFieldSelector,
  GraphInstanceEntry,
  KernelRequestOptions,
  NonAssociationFields,
  Optional,
  PurgeArgs,
  ReadOptionsArg,
  ReadReturnType as ReadReturnTypeGeneric,
  ReadReturnTypeWithAssociations,
  RequireAtLeastOne,
  SemanticSearchArg,
  SemanticSearchRequestOptions,
  TypeSafeQueryArgType,
  UpdateManyOptionsArg,
  UpdateOptionsArg,
  UpsertOptionsArg,
  WriteReturnType,
  WriteReturnTypeWithAssociations,
} from '@everestsystems/content-core';
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { EvstPlainDate as everest_appserver_primitive_PlainDate } from "@pkg/everest.appserver/types/primitives/PlainDate";
import type { EvstID as everest_appserver_primitive_ID } from "@pkg/everest.appserver/types/primitives/ID";
import type { EvstDecimal as everest_appserver_primitive_Decimal } from "@pkg/everest.appserver/types/primitives/Decimal";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstJSON as everest_appserver_primitive_JSON } from "@pkg/everest.appserver/types/primitives/JSON";
import type { EvstPlainDateTime as everest_appserver_primitive_PlainDateTime } from "@pkg/everest.appserver/types/primitives/PlainDateTime";

/** Generated types for View JoinedAllocations */
export namespace JoinedAllocations {
  export type JoinedAllocations = {
    includeAllocationEntries?: everest_appserver_primitive_TrueFalse | null;
    disablePermissionCheck?: everest_appserver_primitive_TrueFalse | null;
    allocationId?: everest_appserver_primitive_Number | null;
    active?: everest_appserver_primitive_TrueFalse | null;
    startDate?: everest_appserver_primitive_PlainDate | null;
    endDate?: everest_appserver_primitive_PlainDate | null;
    memberId?: everest_appserver_primitive_ID | null;
    projectActivityId?: everest_appserver_primitive_ID | null;
    totalHours?: everest_appserver_primitive_Decimal | null;
    notes?: everest_appserver_primitive_Text | null;
    method?: everest_appserver_primitive_Text | null;
    workingDays?: everest_appserver_primitive_JSON | null;
    memberActive?: everest_appserver_primitive_TrueFalse | null;
    employeeId?: everest_appserver_primitive_ID | null;
    memberIsTeamLead?: everest_appserver_primitive_TrueFalse | null;
    memberName?: everest_appserver_primitive_Text | null;
    memberProjectPositionId?: everest_appserver_primitive_ID | null;
    memberCapacity?: everest_appserver_primitive_Number | null;
    projectActivityActive?: everest_appserver_primitive_TrueFalse | null;
    projectActivityName?: everest_appserver_primitive_Text | null;
    projectActivityDescription?: everest_appserver_primitive_Text | null;
    projectActivityStartDate?: everest_appserver_primitive_PlainDate | null;
    projectActivityEndDate?: everest_appserver_primitive_PlainDate | null;
    projectActivityHoursBudget?: everest_appserver_primitive_Decimal | null;
    projectActivityIsBillable?: everest_appserver_primitive_TrueFalse | null;
    projectPositionId?: everest_appserver_primitive_ID | null;
    projectPositionActive?: everest_appserver_primitive_TrueFalse | null;
    projectPositionName?: everest_appserver_primitive_Text | null;
    projectPositionDescription?: everest_appserver_primitive_Text | null;
    projectPositionHourlyBillRateAmount?: everest_appserver_primitive_Decimal | null;
    projectPositionHourlyBillRateCurrency?: everest_appserver_primitive_Text | null;
    projectPositionHoursBudget?: everest_appserver_primitive_Decimal | null;
    projectId?: everest_appserver_primitive_ID | null;
    projectActive?: everest_appserver_primitive_TrueFalse | null;
    projectName?: everest_appserver_primitive_Text | null;
    projectStartDate?: everest_appserver_primitive_PlainDateTime | null;
    projectEndDate?: everest_appserver_primitive_PlainDateTime | null;
    projectStatus?: everest_appserver_primitive_Text | null;
    psaProjectId?: everest_appserver_primitive_ID | null;
    psaProjectBillable?: everest_appserver_primitive_TrueFalse | null;
    psaProjectBudgetAmount?: everest_appserver_primitive_Decimal | null;
    psaProjectBudgetCurrency?: everest_appserver_primitive_Text | null;
    psaProjectCurrency?: everest_appserver_primitive_Text | null;
    psaProjectDepartmentId?: everest_appserver_primitive_ID | null;
    psaProjectInvoiced?: everest_appserver_primitive_TrueFalse | null;
    psaProjectNotes?: everest_appserver_primitive_Text | null;
    psaProjectProjectCode?: everest_appserver_primitive_Text | null;
    psaProjectSalesArrangementId?: everest_appserver_primitive_ID | null;
    psaProjectSalesRepresentativeId?: everest_appserver_primitive_ID | null;
    psaProjectStatus?: everest_appserver_primitive_Text | null;
    psaProjectTypeId?: everest_appserver_primitive_ID | null;
    allocationEntryId?: everest_appserver_primitive_Number | null;
    allocationEntryDate?: everest_appserver_primitive_PlainDate | null;
    allocationEntryHours?: everest_appserver_primitive_Decimal | null;
    allocationEntryWeek?: everest_appserver_primitive_PlainDate | null;
    allocationEntryMonth?: everest_appserver_primitive_PlainDate | null;
    };

  export type JoinedAllocationsParameters = {
    includeAllocationEntries: JoinedAllocations['includeAllocationEntries'];
    disablePermissionCheck: JoinedAllocations['disablePermissionCheck'];
    };

  export type UniqueFields = Partial<Pick<JoinedAllocations, 'includeAllocationEntries' | 'disablePermissionCheck' | 'allocationId' | 'active' | 'startDate' | 'endDate' | 'memberId' | 'projectActivityId' | 'totalHours' | 'notes' | 'method' | 'workingDays' | 'memberActive' | 'employeeId' | 'memberIsTeamLead' | 'memberName' | 'memberProjectPositionId' | 'memberCapacity' | 'projectActivityActive' | 'projectActivityName' | 'projectActivityDescription' | 'projectActivityStartDate' | 'projectActivityEndDate' | 'projectActivityHoursBudget' | 'projectActivityIsBillable' | 'projectPositionId' | 'projectPositionActive' | 'projectPositionName' | 'projectPositionDescription' | 'projectPositionHourlyBillRateAmount' | 'projectPositionHourlyBillRateCurrency' | 'projectPositionHoursBudget' | 'projectId' | 'projectActive' | 'projectName' | 'projectStartDate' | 'projectEndDate' | 'projectStatus' | 'psaProjectId' | 'psaProjectBillable' | 'psaProjectBudgetAmount' | 'psaProjectBudgetCurrency' | 'psaProjectCurrency' | 'psaProjectDepartmentId' | 'psaProjectInvoiced' | 'psaProjectNotes' | 'psaProjectProjectCode' | 'psaProjectSalesArrangementId' | 'psaProjectSalesRepresentativeId' | 'psaProjectStatus' | 'psaProjectTypeId' | 'allocationEntryId' | 'allocationEntryDate' | 'allocationEntryHours' | 'allocationEntryWeek' | 'allocationEntryMonth'>>;
  export type UniqueWhereInput = Partial<JoinedAllocations>;
  export type ReadReturnType<U extends string | number | symbol = keyof JoinedAllocations> = ReadReturnTypeGeneric<JoinedAllocations, U>;

  export interface IControllerClient extends Omit<Controller<JoinedAllocations>, 'all' | 'create' | 'createMany' | 'delete' | 'deleteAll' | 'deleteMany' | 'explainRead' | 'exportMany' | 'getHistory' | 'importMany' | 'lock' | 'lookup' | 'purge' | 'semanticSearch' | 'unlock' | 'update' | 'updateMany' | 'upsert' | 'upsertMany'> {}

  /** @return a model controller instance for JoinedAllocations. */
  export function client(env: ControllerClientProvider) {
    return env.controllerClient<JoinedAllocations.IControllerClient>(MODEL_URN);
  }

  /** returns a list of all objects matching the requirements. */
  export async function query<U extends keyof JoinedAllocations, V extends string = 'ALL_FIELDS'>(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<JoinedAllocations>, 'draft'> | Omit<Omit<TypeSafeQueryArgType<JoinedAllocations>, 'draft'>, 'where'> & { where?: Partial<JoinedAllocations> }, fieldlist: ReadonlyArray<U | V> | 'ALL_FIELDS'): Promise<ReadReturnType<V>[]>;
  export async function query(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<JoinedAllocations>, 'draft'>, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown[]> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).query(args, fields as ReadonlyArray<string>);
  }

  export async function queryWithMetadata<U extends keyof JoinedAllocations = keyof JoinedAllocations>(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<JoinedAllocations>, 'draft'>, fieldlist: ReadonlyArray<U>): Promise<DataWithMetadata<JoinedAllocations, U>> {
    return (await client(env)).queryWithMetadata(args, fieldlist);
  }

  /** @deprecated Use the alternative overloaded method for reading associations. */
  export async function read<U extends keyof JoinedAllocations>(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnType<U>>;
  export async function read(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<unknown> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).read(where, fields as ReadonlyArray<keyof JoinedAllocations>, options);
  }

  export const MODEL_URN: string = 'urn:evst:everest:psa:model/view:publicApi/view/Allocations/JoinedAllocations';
}
