/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

import type { Milestone as everest_psa_model_node_Milestone } from "@pkg/everest.psa/types/Milestone";
import type { PsaProject as everest_psa_model_node_PsaProject } from "@pkg/everest.psa/types/PsaProject";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstPlainDate as everest_appserver_primitive_PlainDate } from "@pkg/everest.appserver/types/primitives/PlainDate";
import type { EvstCurrencyAmount as everest_base_composite_CurrencyAmount } from "@pkg/everest.base/types/composites/CurrencyAmount";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { EvstMilestoneStatus as everest_psa_enum_MilestoneStatus } from "@pkg/everest.psa/types/enums/MilestoneStatus";
import type { EvstDateTime as everest_appserver_primitive_DateTime } from "@pkg/everest.appserver/types/primitives/DateTime";
import type { EvstBaseCurrency as everest_base_enum_BaseCurrency } from "@pkg/everest.base/types/enums/BaseCurrency";
import type { SalesArrangement as everest_fin_accounting_model_node_SalesArrangement } from "@pkg/everest.fin.accounting/types/SalesArrangement";
import type { SalesOrder as everest_fin_accounting_model_node_SalesOrder } from "@pkg/everest.fin.accounting/types/SalesOrder";
import type { SalesOrderProduct as everest_fin_accounting_model_node_SalesOrderProduct } from "@pkg/everest.fin.accounting/types/SalesOrderProduct";
import type { SalesOrderProductLine as everest_fin_accounting_model_node_SalesOrderProductLine } from "@pkg/everest.fin.accounting/types/SalesOrderProductLine";
import type { EvstJSON as everest_appserver_primitive_JSON } from "@pkg/everest.appserver/types/primitives/JSON";
import type { EvstProductLineType as everest_fin_accounting_enum_ProductLineType } from "@pkg/everest.fin.accounting/types/enums/ProductLineType";
import type { InvoiceMilestone as everest_fin_accounting_model_node_InvoiceMilestone } from "@pkg/everest.fin.accounting/types/InvoiceMilestone";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";

import { createOdataClientRuntime } from '@everestsystems/content-core/lib/runtime/loader';

/**
 * Generated frontend types for OData presentation MilestonesTab.
 */
export namespace MilestonesTabUI {
  export namespace EntitySets {
    export namespace Milestones {
      export namespace Get {
        export type Entity = {
          id?: everest_psa_model_node_Milestone.Milestone["id"] | undefined;
          psaProjectId?: everest_psa_model_node_PsaProject.PsaProject["id"] | undefined;
          name?: everest_appserver_primitive_Text | undefined;
          description?: everest_appserver_primitive_Text | undefined;
          dueDate?: everest_appserver_primitive_PlainDate | undefined;
          amount?: everest_base_composite_CurrencyAmount | undefined;
          billable?: everest_appserver_primitive_TrueFalse | undefined;
          status?: everest_psa_enum_MilestoneStatus | undefined;
          salesOrderProductLineId?: everest_psa_model_node_Milestone.Milestone["salesOrderProductLineId"] | undefined;
          invoiceId?: everest_psa_model_node_Milestone.Milestone["invoiceId"] | undefined;
          invoiceMilestoneId?: everest_psa_model_node_Milestone.Milestone["invoiceMilestoneId"] | undefined;
          createdBy?: everest_appserver_primitive_Text | undefined;
          createdDate?: everest_appserver_primitive_DateTime | undefined;
          lastModifiedBy?: everest_appserver_primitive_Text | undefined;
          lastModifiedDate?: everest_appserver_primitive_DateTime | undefined;
          active?: everest_appserver_primitive_TrueFalse | undefined;
        };

        export type Filter =
          | PropertyFilter
          | [Filter, 'and', Filter]
          | [Filter, 'or', Filter];

        /**
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), filters must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to filter by its amount component.
         */
        export type PropertyFilter = {
          property: string;
          comparator: 'eq' | 'ne' | 'lt' | 'le' | 'gt' | 'ge' | 'contains';
          value: unknown;
        };

        /**
         * Specifies sorting for a property in ascending or descending order.
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), sorting must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to sort by its amount component.
         */
        export type OrderbyItem = {
          property: string;
          ordering: 'asc' | 'desc';
        };

        export interface Request {
          setFilter(filter: Filter): this;

          setOrderby(orderby: ReadonlyArray<OrderbyItem>): this;

          setSkip(skip: number): this;

          setTop(top: number): this;

          setSelect(select: ReadonlyArray<keyof Entity>): this;

          setCount(count: boolean): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entities: Entity[];
          count?: number | undefined;
        };
      }

      export namespace Post {
        export type Data = {
          psaProjectId?: everest_psa_model_node_PsaProject.PsaProject["id"] | undefined;
          name?: everest_appserver_primitive_Text | undefined;
          description?: everest_appserver_primitive_Text | undefined;
          dueDate?: everest_appserver_primitive_PlainDate | undefined;
          amount?: everest_base_composite_CurrencyAmount | undefined;
          billable?: everest_appserver_primitive_TrueFalse | undefined;
          status?: everest_psa_enum_MilestoneStatus | undefined;
          salesOrderProductLineId?: everest_psa_model_node_Milestone.Milestone["salesOrderProductLineId"] | undefined;
          invoiceMilestoneId?: everest_psa_model_node_Milestone.Milestone["invoiceMilestoneId"] | undefined;
          active?: everest_appserver_primitive_TrueFalse | undefined;
        };

        export type Entity = {
          id: everest_psa_model_node_Milestone.Milestone["id"];
          psaProjectId: everest_psa_model_node_PsaProject.PsaProject["id"];
          name: everest_appserver_primitive_Text;
          description?: everest_appserver_primitive_Text | undefined;
          dueDate: everest_appserver_primitive_PlainDate;
          amount?: everest_base_composite_CurrencyAmount | undefined;
          billable?: everest_appserver_primitive_TrueFalse | undefined;
          status: everest_psa_enum_MilestoneStatus;
          salesOrderProductLineId?: everest_psa_model_node_Milestone.Milestone["salesOrderProductLineId"] | undefined;
          invoiceId?: everest_psa_model_node_Milestone.Milestone["invoiceId"] | undefined;
          invoiceMilestoneId?: everest_psa_model_node_Milestone.Milestone["invoiceMilestoneId"] | undefined;
          createdBy?: everest_appserver_primitive_Text | undefined;
          createdDate?: everest_appserver_primitive_DateTime | undefined;
          lastModifiedBy?: everest_appserver_primitive_Text | undefined;
          lastModifiedDate?: everest_appserver_primitive_DateTime | undefined;
          active?: everest_appserver_primitive_TrueFalse | undefined;
        };

        export interface Request {
          setData(data: Data): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entity: Entity;
        };
      }

      export namespace Patch {
        export type Data = {
          psaProjectId?: everest_psa_model_node_PsaProject.PsaProject["id"] | null | undefined;
          name?: everest_appserver_primitive_Text | null | undefined;
          description?: everest_appserver_primitive_Text | null | undefined;
          dueDate?: everest_appserver_primitive_PlainDate | null | undefined;
          amount?: everest_base_composite_CurrencyAmount | null | undefined;
          billable?: everest_appserver_primitive_TrueFalse | null | undefined;
          status?: everest_psa_enum_MilestoneStatus | null | undefined;
          salesOrderProductLineId?: everest_psa_model_node_Milestone.Milestone["salesOrderProductLineId"] | null | undefined;
          invoiceMilestoneId?: everest_psa_model_node_Milestone.Milestone["invoiceMilestoneId"] | null | undefined;
          active?: everest_appserver_primitive_TrueFalse | null | undefined;
        };

        export interface Request {
          setId(id: number): this;

          setData(data: Data): this;

          execute(): Promise<Response>;
        }

        export type Response = Record<string, never>;
      }

      export namespace Delete {
        export interface Request {
          setId(id: number): this;

          execute(): Promise<Response>;
        }

        export type Response = Record<string, never>;
      }
    }
    export namespace Projects {
      export namespace Get {
        export type Entity = {
          id?: everest_psa_model_node_PsaProject.PsaProject["id"] | undefined;
          currency?: everest_base_enum_BaseCurrency | undefined;
          salesArrangement?: {
            id: everest_fin_accounting_model_node_SalesArrangement.SalesArrangement["id"];
            salesArrangementNumber: everest_appserver_primitive_Text;
            salesOrders: {
              id: everest_fin_accounting_model_node_SalesOrder.SalesOrder["id"];
              salesOrderNumber: everest_appserver_primitive_Text;
              products: {
                id: everest_fin_accounting_model_node_SalesOrderProduct.SalesOrderProduct["id"];
                productName: everest_appserver_primitive_Text;
                totalPrice: everest_base_composite_CurrencyAmount;
                active: everest_appserver_primitive_TrueFalse;
                productLines: {
                  id: everest_fin_accounting_model_node_SalesOrderProductLine.SalesOrderProductLine["id"];
                  productLineName: everest_appserver_primitive_Text;
                  salesOrderProductLineNumber: everest_appserver_primitive_Text;
                  serviceStartDate: everest_appserver_primitive_PlainDate;
                  serviceEndDate: everest_appserver_primitive_PlainDate;
                  quantity: everest_appserver_primitive_JSON;
                  unitPrice: everest_appserver_primitive_JSON;
                  totalPrice: everest_base_composite_CurrencyAmount;
                  priceModel: everest_appserver_primitive_Text;
                  invoiceFrequency: everest_appserver_primitive_Text;
                  lineType: everest_fin_accounting_enum_ProductLineType;
                  active: everest_appserver_primitive_TrueFalse;
                  productLineId: everest_fin_accounting_model_node_SalesOrderProductLine.SalesOrderProductLine["id"];
                  unitCost: everest_base_composite_CurrencyAmount;
                  invoiceMilestones: {
                    id: everest_fin_accounting_model_node_InvoiceMilestone.InvoiceMilestone["id"];
                    description: everest_appserver_primitive_Text;
                    totalPrice: everest_base_composite_CurrencyAmount;
                    projectedDate: everest_appserver_primitive_PlainDate;
                    invoiceDate: everest_appserver_primitive_PlainDate;
                    invoiceNumber: everest_appserver_primitive_Text;
                  }[];
                }[];
              }[];
            }[];
          } | undefined;
        };

        export type Filter =
          | PropertyFilter
          | [Filter, 'and', Filter]
          | [Filter, 'or', Filter];

        /**
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), filters must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to filter by its amount component.
         */
        export type PropertyFilter = {
          property: string;
          comparator: 'eq' | 'ne' | 'lt' | 'le' | 'gt' | 'ge' | 'contains';
          value: unknown;
        };

        /**
         * Specifies sorting for a property in ascending or descending order.
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), sorting must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to sort by its amount component.
         */
        export type OrderbyItem = {
          property: string;
          ordering: 'asc' | 'desc';
        };

        export interface Request {
          setFilter(filter: Filter): this;

          setOrderby(orderby: ReadonlyArray<OrderbyItem>): this;

          setSkip(skip: number): this;

          setTop(top: number): this;

          setSelect(select: ReadonlyArray<keyof Entity>): this;

          setCount(count: boolean): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entities: Entity[];
          count?: number | undefined;
        };
      }
    }
  }

  export namespace Actions { }

  export namespace Functions {
    export namespace validateMilestones {
      export namespace Execute {
        export type Input = {
          psaProjectId: everest_psa_model_node_PsaProject.PsaProject["id"];
        };

        export type Output = {
          warnings: {
            message: everest_appserver_primitive_Text;
            code: everest_appserver_primitive_Text;
            severity: everest_appserver_primitive_Text;
            id: everest_appserver_primitive_Number;
            artifactType: everest_appserver_primitive_Text;
          }[];
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }
  }

  export namespace Permission {
    export namespace Check {
      export type Result = {
        dataSets: {
          Milestones: {
            isGetPermitted: boolean;
            isPostPermitted: boolean;
            isPatchPermitted: boolean;
            isDeletePermitted: boolean;
          };
          Projects: {
            isGetPermitted: boolean;
          };
        };
        actions: {
          validateMilestones: {
            isExecutePermitted: boolean;
          };
        };
      };

      export interface Request {
        execute(): Promise<Response>;
      }

      export type Response = {
        result: Result;
      };
    }
  }

  export namespace Api {
    export type EntitySets = {
      Milestones: {
        get: EntitySets.Milestones.Get.Request;
        post: EntitySets.Milestones.Post.Request;
        patch: EntitySets.Milestones.Patch.Request;
        delete: EntitySets.Milestones.Delete.Request;
      };
      Projects: {
        get: EntitySets.Projects.Get.Request;
      };
    };

    export type Actions = Record<string, never>;

    export type Functions = {
      validateMilestones: {
        execute: Functions.validateMilestones.Execute.Request;
      };
    };

    export interface Client {
      createEntitySetGetRequest<T extends 'Milestones' | 'Projects'>(entitySetName: T): Api.EntitySets[T]['get'];

      createEntitySetPostRequest<T extends 'Milestones'>(entitySetName: T): Api.EntitySets[T]['post'];

      createEntitySetPatchRequest<T extends 'Milestones'>(entitySetName: T): Api.EntitySets[T]['patch'];

      createEntitySetDeleteRequest<T extends 'Milestones'>(entitySetName: T): Api.EntitySets[T]['delete'];

      createFunctionExecuteRequest<T extends 'validateMilestones'>(functionName: T): Api.Functions[T]['execute'];

      createPermissionCheckRequest(): Permission.Check.Request;
    }
  }

  export const client = createOdataClientRuntime<Api.Client>('everest.psa/modules/projects/pages/projectDetail/components/MilestonesTab/MilestonesTab');
}

