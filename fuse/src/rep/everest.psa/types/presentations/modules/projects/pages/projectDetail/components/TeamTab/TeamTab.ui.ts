/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

import type { ProjectPosition as everest_timetracking_model_node_ProjectPosition } from "@pkg/everest.timetracking/types/ProjectPosition";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { EvstDateTime as everest_appserver_primitive_DateTime } from "@pkg/everest.appserver/types/primitives/DateTime";
import type { TimetrackingProject as everest_timetracking_model_node_TimetrackingProject } from "@pkg/everest.timetracking/types/TimetrackingProject";
import type { Member as everest_timetracking_model_node_Member } from "@pkg/everest.timetracking/types/Member";
import type { Employee as everest_hr_base_model_node_Employee } from "@pkg/everest.hr.base/types/Employee";
import type { EvstDecimal as everest_appserver_primitive_Decimal } from "@pkg/everest.appserver/types/primitives/Decimal";
import type { EvstBaseCurrency as everest_base_enum_BaseCurrency } from "@pkg/everest.base/types/enums/BaseCurrency";

import { createOdataClientRuntime } from '@everestsystems/content-core/lib/runtime/loader';

/**
 * Generated frontend types for OData presentation TeamTab.
 */
export namespace TeamTabUI {
  export namespace EntitySets {
    export namespace ProjectPositions {
      export namespace Get {
        export type Entity = {
          id?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["id"] | undefined;
          uuid?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["uuid"] | undefined;
          version?: everest_appserver_primitive_Number | undefined;
          externalId?: everest_appserver_primitive_Text | undefined;
          active?: everest_appserver_primitive_TrueFalse | undefined;
          createdBy?: everest_appserver_primitive_Text | undefined;
          createdDate?: everest_appserver_primitive_DateTime | undefined;
          lastModifiedBy?: everest_appserver_primitive_Text | undefined;
          lastModifiedDate?: everest_appserver_primitive_DateTime | undefined;
          name?: everest_appserver_primitive_Text | undefined;
          description?: everest_appserver_primitive_Text | undefined;
          productId?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["salesOrderProductLineId"] | undefined;
          projectId?: everest_timetracking_model_node_TimetrackingProject.TimetrackingProject["id"] | undefined;
        };

        export type Filter =
          | PropertyFilter
          | [Filter, 'and', Filter]
          | [Filter, 'or', Filter];

        /**
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), filters must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to filter by its amount component.
         */
        export type PropertyFilter = {
          property: string;
          comparator: 'eq' | 'ne' | 'lt' | 'le' | 'gt' | 'ge' | 'contains';
          value: unknown;
        };

        /**
         * Specifies sorting for a property in ascending or descending order.
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), sorting must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to sort by its amount component.
         */
        export type OrderbyItem = {
          property: string;
          ordering: 'asc' | 'desc';
        };

        export interface Request {
          setFilter(filter: Filter): this;

          setOrderby(orderby: ReadonlyArray<OrderbyItem>): this;

          setSkip(skip: number): this;

          setTop(top: number): this;

          setSelect(select: ReadonlyArray<keyof Entity>): this;

          setCount(count: boolean): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entities: Entity[];
          count?: number | undefined;
        };
      }
    }
    export namespace Members {
      export namespace Get {
        export type Entity = {
          id?: everest_timetracking_model_node_Member.Member["id"] | undefined;
          uuid?: everest_timetracking_model_node_Member.Member["uuid"] | undefined;
          version?: everest_appserver_primitive_Number | undefined;
          externalId?: everest_appserver_primitive_Text | undefined;
          active?: everest_appserver_primitive_TrueFalse | undefined;
          createdBy?: everest_appserver_primitive_Text | undefined;
          createdDate?: everest_appserver_primitive_DateTime | undefined;
          lastModifiedBy?: everest_appserver_primitive_Text | undefined;
          lastModifiedDate?: everest_appserver_primitive_DateTime | undefined;
          employeeId?: everest_hr_base_model_node_Employee.Employee["id"] | undefined;
          projectPositionId?: everest_timetracking_model_node_Member.Member["projectPositionId"] | undefined;
          projectId?: everest_timetracking_model_node_TimetrackingProject.TimetrackingProject["id"] | undefined;
        };

        export type Filter =
          | PropertyFilter
          | [Filter, 'and', Filter]
          | [Filter, 'or', Filter];

        /**
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), filters must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to filter by its amount component.
         */
        export type PropertyFilter = {
          property: string;
          comparator: 'eq' | 'ne' | 'lt' | 'le' | 'gt' | 'ge' | 'contains';
          value: unknown;
        };

        /**
         * Specifies sorting for a property in ascending or descending order.
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), sorting must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to sort by its amount component.
         */
        export type OrderbyItem = {
          property: string;
          ordering: 'asc' | 'desc';
        };

        export interface Request {
          setFilter(filter: Filter): this;

          setOrderby(orderby: ReadonlyArray<OrderbyItem>): this;

          setSkip(skip: number): this;

          setTop(top: number): this;

          setSelect(select: ReadonlyArray<keyof Entity>): this;

          setCount(count: boolean): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entities: Entity[];
          count?: number | undefined;
        };
      }

      export namespace Post {
        export type Data = {
          active?: everest_appserver_primitive_TrueFalse | undefined;
          employeeId?: everest_hr_base_model_node_Employee.Employee["id"] | undefined;
          projectPositionId?: everest_timetracking_model_node_Member.Member["projectPositionId"] | undefined;
          projectId?: everest_timetracking_model_node_TimetrackingProject.TimetrackingProject["id"] | undefined;
        };

        export type Entity = {
          id: everest_timetracking_model_node_Member.Member["id"];
          uuid?: everest_timetracking_model_node_Member.Member["uuid"] | undefined;
          version?: everest_appserver_primitive_Number | undefined;
          externalId?: everest_appserver_primitive_Text | undefined;
          active?: everest_appserver_primitive_TrueFalse | undefined;
          createdBy?: everest_appserver_primitive_Text | undefined;
          createdDate?: everest_appserver_primitive_DateTime | undefined;
          lastModifiedBy?: everest_appserver_primitive_Text | undefined;
          lastModifiedDate?: everest_appserver_primitive_DateTime | undefined;
          employeeId: everest_hr_base_model_node_Employee.Employee["id"];
          projectPositionId?: everest_timetracking_model_node_Member.Member["projectPositionId"] | undefined;
          projectId?: everest_timetracking_model_node_TimetrackingProject.TimetrackingProject["id"] | undefined;
        };

        export interface Request {
          setData(data: Data): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entity: Entity;
        };
      }

      export namespace Delete {
        export interface Request {
          setId(id: number): this;

          execute(): Promise<Response>;
        }

        export type Response = Record<string, never>;
      }
    }
    export namespace ActiveAndPlannedEmployees {
      export namespace Get {
        export type Entity = {
          id?: everest_appserver_primitive_Number | undefined;
          name?: everest_appserver_primitive_Text | undefined;
          displayName?: everest_appserver_primitive_Text | undefined;
          email?: everest_appserver_primitive_Text | undefined;
        };

        export type Filter =
          | PropertyFilter
          | [Filter, 'and', Filter]
          | [Filter, 'or', Filter];

        /**
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), filters must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to filter by its amount component.
         */
        export type PropertyFilter = {
          property: string;
          comparator: 'eq' | 'ne' | 'lt' | 'le' | 'gt' | 'ge' | 'contains';
          value: unknown;
        };

        /**
         * Specifies sorting for a property in ascending or descending order.
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), sorting must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to sort by its amount component.
         */
        export type OrderbyItem = {
          property: string;
          ordering: 'asc' | 'desc';
        };

        export interface Request {
          setFilter(filter: Filter): this;

          setOrderby(orderby: ReadonlyArray<OrderbyItem>): this;

          setSkip(skip: number): this;

          setTop(top: number): this;

          setSelect(select: ReadonlyArray<keyof Entity>): this;

          setCount(count: boolean): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entities: Entity[];
          count?: number | undefined;
        };
      }
    }
  }

  export namespace Actions {
    export namespace unassignTeamMember {
      export namespace Execute {
        export type Input = {
          teamMemberId: everest_appserver_primitive_Number;
          psaProjectId: everest_appserver_primitive_Number;
        };

        export type Output = Record<string, never>;

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }
  }

  export namespace Functions {
    export namespace getMemberAndRoles {
      export namespace Execute {
        export type Input = {
          psaProjectId: everest_appserver_primitive_Number;
        };

        export type Output = {
          teamMembers: {
            id: everest_appserver_primitive_Number;
            name: everest_appserver_primitive_Text;
            email: everest_appserver_primitive_Text;
            photo: everest_appserver_primitive_Text;
            status: everest_appserver_primitive_Text;
            teamMemberId: everest_appserver_primitive_Number;
            memberIsTeamLead: everest_appserver_primitive_TrueFalse;
            roles: {
              memberId: everest_timetracking_model_node_Member.Member["id"];
              id: everest_appserver_primitive_Number;
              title: everest_appserver_primitive_Text;
              isBillable: everest_appserver_primitive_TrueFalse;
              invoiced: everest_appserver_primitive_TrueFalse;
              hourlyBillRate: everest_appserver_primitive_Decimal;
              currency: everest_base_enum_BaseCurrency;
            }[];
          }[];
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }
  }

  export namespace Permission {
    export namespace Check {
      export type Result = {
        dataSets: {
          ActiveAndPlannedEmployees: {
            isGetPermitted: boolean;
          };
          Members: {
            isGetPermitted: boolean;
            isPostPermitted: boolean;
            isDeletePermitted: boolean;
          };
          ProjectPositions: {
            isGetPermitted: boolean;
          };
        };
        actions: {
          getMemberAndRoles: {
            isExecutePermitted: boolean;
          };
          unassignTeamMember: {
            isExecutePermitted: boolean;
          };
        };
      };

      export interface Request {
        execute(): Promise<Response>;
      }

      export type Response = {
        result: Result;
      };
    }
  }

  export namespace Api {
    export type EntitySets = {
      ProjectPositions: {
        get: EntitySets.ProjectPositions.Get.Request;
      };
      Members: {
        get: EntitySets.Members.Get.Request;
        post: EntitySets.Members.Post.Request;
        delete: EntitySets.Members.Delete.Request;
      };
      ActiveAndPlannedEmployees: {
        get: EntitySets.ActiveAndPlannedEmployees.Get.Request;
      };
    };

    export type Actions = {
      unassignTeamMember: {
        execute: Actions.unassignTeamMember.Execute.Request;
      };
    };

    export type Functions = {
      getMemberAndRoles: {
        execute: Functions.getMemberAndRoles.Execute.Request;
      };
    };

    export interface Client {
      createEntitySetGetRequest<T extends 'ProjectPositions' | 'Members' | 'ActiveAndPlannedEmployees'>(entitySetName: T): Api.EntitySets[T]['get'];

      createEntitySetPostRequest<T extends 'Members'>(entitySetName: T): Api.EntitySets[T]['post'];

      createEntitySetDeleteRequest<T extends 'Members'>(entitySetName: T): Api.EntitySets[T]['delete'];

      createActionExecuteRequest<T extends 'unassignTeamMember'>(actionName: T): Api.Actions[T]['execute'];

      createFunctionExecuteRequest<T extends 'getMemberAndRoles'>(functionName: T): Api.Functions[T]['execute'];

      createPermissionCheckRequest(): Permission.Check.Request;
    }
  }

  export const client = createOdataClientRuntime<Api.Client>('everest.psa/modules/projects/pages/projectDetail/components/TeamTab/TeamTab');
}

