/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

import type { PsaProject as everest_psa_model_node_PsaProject } from "@pkg/everest.psa/types/PsaProject";
import type { ProjectActivity as everest_timetracking_model_node_ProjectActivity } from "@pkg/everest.timetracking/types/ProjectActivity";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstPlainDate as everest_appserver_primitive_PlainDate } from "@pkg/everest.appserver/types/primitives/PlainDate";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { EvstDecimal as everest_appserver_primitive_Decimal } from "@pkg/everest.appserver/types/primitives/Decimal";
import type { ProjectPosition as everest_timetracking_model_node_ProjectPosition } from "@pkg/everest.timetracking/types/ProjectPosition";
import type { Milestone as everest_psa_model_node_Milestone } from "@pkg/everest.psa/types/Milestone";
import type { EvstCurrencyAmount as everest_base_composite_CurrencyAmount } from "@pkg/everest.base/types/composites/CurrencyAmount";
import type { EvstMilestoneStatus as everest_psa_enum_MilestoneStatus } from "@pkg/everest.psa/types/enums/MilestoneStatus";
import type { FixedFee as everest_psa_model_node_FixedFee } from "@pkg/everest.psa/types/FixedFee";
import type { EvstFixedFeeFrequency as everest_psa_enum_FixedFeeFrequency } from "@pkg/everest.psa/types/enums/FixedFeeFrequency";

import { createOdataClientRuntime } from '@everestsystems/content-core/lib/runtime/loader';

/**
 * Generated frontend types for OData presentation TasksTab.
 */
export namespace TasksTabUI {
  export namespace EntitySets {
    export namespace Tasks {
      export namespace Get {
        export type Entity = {
          psaProjectId?: everest_psa_model_node_PsaProject.PsaProject["id"] | undefined;
          taskId?: everest_timetracking_model_node_ProjectActivity.ProjectActivity["id"] | undefined;
          taskName?: everest_appserver_primitive_Text | undefined;
          startDate?: everest_appserver_primitive_PlainDate | undefined;
          endDate?: everest_appserver_primitive_PlainDate | undefined;
          teamMember?: {
            memberId: everest_appserver_primitive_Number;
            memberDisplayName: everest_appserver_primitive_Text;
            projectPositionId: everest_appserver_primitive_Number;
            projectPositionName: everest_appserver_primitive_Text;
            positionBillable: everest_appserver_primitive_TrueFalse;
          }[] | undefined;
          activityDescription?: everest_appserver_primitive_Text | undefined;
          isBillableActivity?: everest_appserver_primitive_TrueFalse | undefined;
          hoursBudget?: everest_appserver_primitive_Decimal | undefined;
          allocatedHours?: everest_appserver_primitive_Decimal | undefined;
          actualHours?: everest_appserver_primitive_Decimal | undefined;
          invoiced?: everest_appserver_primitive_TrueFalse | undefined;
          milestoneId?: everest_appserver_primitive_Number | undefined;
          fixedFeeId?: everest_appserver_primitive_Number | undefined;
          billingType?: everest_appserver_primitive_Text | undefined;
          dateWarning?: everest_appserver_primitive_TrueFalse | undefined;
        };

        export type Filter =
          | PropertyFilter
          | [Filter, 'and', Filter]
          | [Filter, 'or', Filter];

        /**
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), filters must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to filter by its amount component.
         */
        export type PropertyFilter = {
          property: string;
          comparator: 'eq' | 'ne' | 'lt' | 'le' | 'gt' | 'ge' | 'contains';
          value: unknown;
        };

        /**
         * Specifies sorting for a property in ascending or descending order.
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), sorting must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to sort by its amount component.
         */
        export type OrderbyItem = {
          property: string;
          ordering: 'asc' | 'desc';
        };

        export interface Request {
          setFilter(filter: Filter): this;

          setOrderby(orderby: ReadonlyArray<OrderbyItem>): this;

          setSkip(skip: number): this;

          setTop(top: number): this;

          setSelect(select: ReadonlyArray<keyof Entity>): this;

          setCount(count: boolean): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entities: Entity[];
          count?: number | undefined;
        };
      }

      export namespace Post {
        export type Data = {
          psaProjectId?: everest_psa_model_node_PsaProject.PsaProject["id"] | undefined;
          taskId?: everest_timetracking_model_node_ProjectActivity.ProjectActivity["id"] | undefined;
          taskName?: everest_appserver_primitive_Text | undefined;
          startDate?: everest_appserver_primitive_PlainDate | undefined;
          endDate?: everest_appserver_primitive_PlainDate | undefined;
          teamMember?: {
            memberId: everest_appserver_primitive_Number;
            memberDisplayName: everest_appserver_primitive_Text;
            projectPositionId: everest_appserver_primitive_Number;
            projectPositionName: everest_appserver_primitive_Text;
            positionBillable: everest_appserver_primitive_TrueFalse;
          }[] | undefined;
          activityDescription?: everest_appserver_primitive_Text | undefined;
          isBillableActivity?: everest_appserver_primitive_TrueFalse | undefined;
          hoursBudget?: everest_appserver_primitive_Decimal | undefined;
          allocatedHours?: everest_appserver_primitive_Decimal | undefined;
          actualHours?: everest_appserver_primitive_Decimal | undefined;
          invoiced?: everest_appserver_primitive_TrueFalse | undefined;
          milestoneId?: everest_appserver_primitive_Number | undefined;
          fixedFeeId?: everest_appserver_primitive_Number | undefined;
          billingType?: everest_appserver_primitive_Text | undefined;
          dateWarning?: everest_appserver_primitive_TrueFalse | undefined;
        };

        export type Entity = {
          psaProjectId?: everest_psa_model_node_PsaProject.PsaProject["id"] | undefined;
          taskId?: everest_timetracking_model_node_ProjectActivity.ProjectActivity["id"] | undefined;
          taskName?: everest_appserver_primitive_Text | undefined;
          startDate?: everest_appserver_primitive_PlainDate | undefined;
          endDate?: everest_appserver_primitive_PlainDate | undefined;
          teamMember?: {
            memberId: everest_appserver_primitive_Number;
            memberDisplayName: everest_appserver_primitive_Text;
            projectPositionId: everest_appserver_primitive_Number;
            projectPositionName: everest_appserver_primitive_Text;
            positionBillable: everest_appserver_primitive_TrueFalse;
          }[] | undefined;
          activityDescription?: everest_appserver_primitive_Text | undefined;
          isBillableActivity?: everest_appserver_primitive_TrueFalse | undefined;
          hoursBudget?: everest_appserver_primitive_Decimal | undefined;
          allocatedHours?: everest_appserver_primitive_Decimal | undefined;
          actualHours?: everest_appserver_primitive_Decimal | undefined;
          invoiced?: everest_appserver_primitive_TrueFalse | undefined;
          milestoneId?: everest_appserver_primitive_Number | undefined;
          fixedFeeId?: everest_appserver_primitive_Number | undefined;
          billingType?: everest_appserver_primitive_Text | undefined;
          dateWarning?: everest_appserver_primitive_TrueFalse | undefined;
        };

        export interface Request {
          setData(data: Data): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entity: Entity;
        };
      }

      export namespace Patch {
        export type Data = {
          psaProjectId?: everest_psa_model_node_PsaProject.PsaProject["id"] | null | undefined;
          taskId?: everest_timetracking_model_node_ProjectActivity.ProjectActivity["id"] | null | undefined;
          taskName?: everest_appserver_primitive_Text | null | undefined;
          startDate?: everest_appserver_primitive_PlainDate | null | undefined;
          endDate?: everest_appserver_primitive_PlainDate | null | undefined;
          teamMember?: {
            memberId: everest_appserver_primitive_Number;
            memberDisplayName: everest_appserver_primitive_Text;
            projectPositionId: everest_appserver_primitive_Number;
            projectPositionName: everest_appserver_primitive_Text;
            positionBillable: everest_appserver_primitive_TrueFalse;
          }[] | null | undefined;
          activityDescription?: everest_appserver_primitive_Text | null | undefined;
          isBillableActivity?: everest_appserver_primitive_TrueFalse | null | undefined;
          hoursBudget?: everest_appserver_primitive_Decimal | null | undefined;
          allocatedHours?: everest_appserver_primitive_Decimal | null | undefined;
          actualHours?: everest_appserver_primitive_Decimal | null | undefined;
          invoiced?: everest_appserver_primitive_TrueFalse | null | undefined;
          milestoneId?: everest_appserver_primitive_Number | null | undefined;
          fixedFeeId?: everest_appserver_primitive_Number | null | undefined;
          billingType?: everest_appserver_primitive_Text | null | undefined;
          dateWarning?: everest_appserver_primitive_TrueFalse | null | undefined;
        };

        export interface Request {
          setId(id: number): this;

          setData(data: Data): this;

          execute(): Promise<Response>;
        }

        export type Response = Record<string, never>;
      }

      export namespace Delete {
        export interface Request {
          setId(id: number): this;

          execute(): Promise<Response>;
        }

        export type Response = Record<string, never>;
      }
    }
    export namespace Roles {
      export namespace Get {
        export type Entity = {
          psaProjectId?: everest_psa_model_node_PsaProject.PsaProject["id"] | undefined;
          positionId?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["id"] | undefined;
          positionName?: everest_appserver_primitive_Text | undefined;
          positionBillable?: everest_appserver_primitive_TrueFalse | undefined;
        };

        export type Filter =
          | PropertyFilter
          | [Filter, 'and', Filter]
          | [Filter, 'or', Filter];

        /**
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), filters must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to filter by its amount component.
         */
        export type PropertyFilter = {
          property: string;
          comparator: 'eq' | 'ne' | 'lt' | 'le' | 'gt' | 'ge' | 'contains';
          value: unknown;
        };

        /**
         * Specifies sorting for a property in ascending or descending order.
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), sorting must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to sort by its amount component.
         */
        export type OrderbyItem = {
          property: string;
          ordering: 'asc' | 'desc';
        };

        export interface Request {
          setFilter(filter: Filter): this;

          setOrderby(orderby: ReadonlyArray<OrderbyItem>): this;

          setSkip(skip: number): this;

          setTop(top: number): this;

          setSelect(select: ReadonlyArray<keyof Entity>): this;

          setCount(count: boolean): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entities: Entity[];
          count?: number | undefined;
        };
      }
    }
    export namespace TeamMembers {
      export namespace Get {
        export type Entity = {
          psaProjectId?: everest_psa_model_node_PsaProject.PsaProject["id"] | undefined;
          memberId?: everest_appserver_primitive_Number | undefined;
          memberName?: everest_appserver_primitive_Text | undefined;
          memberDisplayName?: everest_appserver_primitive_Text | undefined;
          memberEmail?: everest_appserver_primitive_Text | undefined;
          positionId?: everest_appserver_primitive_Number | undefined;
          positionName?: everest_appserver_primitive_Text | undefined;
          positionBillable?: everest_appserver_primitive_TrueFalse | undefined;
        };

        export type Filter =
          | PropertyFilter
          | [Filter, 'and', Filter]
          | [Filter, 'or', Filter];

        /**
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), filters must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to filter by its amount component.
         */
        export type PropertyFilter = {
          property: string;
          comparator: 'eq' | 'ne' | 'lt' | 'le' | 'gt' | 'ge' | 'contains';
          value: unknown;
        };

        /**
         * Specifies sorting for a property in ascending or descending order.
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), sorting must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to sort by its amount component.
         */
        export type OrderbyItem = {
          property: string;
          ordering: 'asc' | 'desc';
        };

        export interface Request {
          setFilter(filter: Filter): this;

          setOrderby(orderby: ReadonlyArray<OrderbyItem>): this;

          setSkip(skip: number): this;

          setTop(top: number): this;

          setSelect(select: ReadonlyArray<keyof Entity>): this;

          setCount(count: boolean): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entities: Entity[];
          count?: number | undefined;
        };
      }
    }
    export namespace BillableMilestones {
      export namespace Get {
        export type Entity = {
          psaProjectId?: everest_psa_model_node_PsaProject.PsaProject["id"] | undefined;
          id?: everest_psa_model_node_Milestone.Milestone["id"] | undefined;
          name?: everest_appserver_primitive_Text | undefined;
          amount?: everest_base_composite_CurrencyAmount | undefined;
          dueDate?: everest_appserver_primitive_PlainDate | undefined;
          status?: everest_psa_enum_MilestoneStatus | undefined;
        };

        export type Filter =
          | PropertyFilter
          | [Filter, 'and', Filter]
          | [Filter, 'or', Filter];

        /**
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), filters must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to filter by its amount component.
         */
        export type PropertyFilter = {
          property: string;
          comparator: 'eq' | 'ne' | 'lt' | 'le' | 'gt' | 'ge' | 'contains';
          value: unknown;
        };

        /**
         * Specifies sorting for a property in ascending or descending order.
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), sorting must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to sort by its amount component.
         */
        export type OrderbyItem = {
          property: string;
          ordering: 'asc' | 'desc';
        };

        export interface Request {
          setFilter(filter: Filter): this;

          setOrderby(orderby: ReadonlyArray<OrderbyItem>): this;

          setSkip(skip: number): this;

          setTop(top: number): this;

          setSelect(select: ReadonlyArray<keyof Entity>): this;

          setCount(count: boolean): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entities: Entity[];
          count?: number | undefined;
        };
      }
    }
    export namespace BillableFixedFees {
      export namespace Get {
        export type Entity = {
          psaProjectId?: everest_psa_model_node_PsaProject.PsaProject["id"] | undefined;
          id?: everest_psa_model_node_FixedFee.FixedFee["id"] | undefined;
          amount?: everest_base_composite_CurrencyAmount | undefined;
          frequency?: everest_psa_enum_FixedFeeFrequency | undefined;
          startDate?: everest_appserver_primitive_PlainDate | undefined;
          endDate?: everest_appserver_primitive_PlainDate | undefined;
        };

        export type Filter =
          | PropertyFilter
          | [Filter, 'and', Filter]
          | [Filter, 'or', Filter];

        /**
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), filters must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to filter by its amount component.
         */
        export type PropertyFilter = {
          property: string;
          comparator: 'eq' | 'ne' | 'lt' | 'le' | 'gt' | 'ge' | 'contains';
          value: unknown;
        };

        /**
         * Specifies sorting for a property in ascending or descending order.
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), sorting must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to sort by its amount component.
         */
        export type OrderbyItem = {
          property: string;
          ordering: 'asc' | 'desc';
        };

        export interface Request {
          setFilter(filter: Filter): this;

          setOrderby(orderby: ReadonlyArray<OrderbyItem>): this;

          setSkip(skip: number): this;

          setTop(top: number): this;

          setSelect(select: ReadonlyArray<keyof Entity>): this;

          setCount(count: boolean): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entities: Entity[];
          count?: number | undefined;
        };
      }
    }
  }

  export namespace Actions { }

  export namespace Functions { }

  export namespace Permission {
    export namespace Check {
      export type Result = {
        dataSets: {
          BillableFixedFees: {
            isGetPermitted: boolean;
          };
          BillableMilestones: {
            isGetPermitted: boolean;
          };
          Roles: {
            isGetPermitted: boolean;
          };
          Tasks: {
            isGetPermitted: boolean;
            isPostPermitted: boolean;
            isPatchPermitted: boolean;
            isDeletePermitted: boolean;
          };
          TeamMembers: {
            isGetPermitted: boolean;
          };
        };
      };

      export interface Request {
        execute(): Promise<Response>;
      }

      export type Response = {
        result: Result;
      };
    }
  }

  export namespace Api {
    export type EntitySets = {
      Tasks: {
        get: EntitySets.Tasks.Get.Request;
        post: EntitySets.Tasks.Post.Request;
        patch: EntitySets.Tasks.Patch.Request;
        delete: EntitySets.Tasks.Delete.Request;
      };
      Roles: {
        get: EntitySets.Roles.Get.Request;
      };
      TeamMembers: {
        get: EntitySets.TeamMembers.Get.Request;
      };
      BillableMilestones: {
        get: EntitySets.BillableMilestones.Get.Request;
      };
      BillableFixedFees: {
        get: EntitySets.BillableFixedFees.Get.Request;
      };
    };

    export type Actions = Record<string, never>;

    export type Functions = Record<string, never>;

    export interface Client {
      createEntitySetGetRequest<T extends 'Tasks' | 'Roles' | 'TeamMembers' | 'BillableMilestones' | 'BillableFixedFees'>(entitySetName: T): Api.EntitySets[T]['get'];

      createEntitySetPostRequest<T extends 'Tasks'>(entitySetName: T): Api.EntitySets[T]['post'];

      createEntitySetPatchRequest<T extends 'Tasks'>(entitySetName: T): Api.EntitySets[T]['patch'];

      createEntitySetDeleteRequest<T extends 'Tasks'>(entitySetName: T): Api.EntitySets[T]['delete'];

      createPermissionCheckRequest(): Permission.Check.Request;
    }
  }

  export const client = createOdataClientRuntime<Api.Client>('everest.psa/modules/projects/pages/projectDetail/components/TasksTab/TasksTab');
}

