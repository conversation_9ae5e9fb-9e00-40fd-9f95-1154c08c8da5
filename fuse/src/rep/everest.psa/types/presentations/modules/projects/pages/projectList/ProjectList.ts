/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

import type { PsaProject as everest_psa_model_node_PsaProject } from "@pkg/everest.psa/types/PsaProject";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { EvstPlainDate as everest_appserver_primitive_PlainDate } from "@pkg/everest.appserver/types/primitives/PlainDate";
import type { EvstDateTime as everest_appserver_primitive_DateTime } from "@pkg/everest.appserver/types/primitives/DateTime";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { EvstBaseCurrency as everest_base_enum_BaseCurrency } from "@pkg/everest.base/types/enums/BaseCurrency";
import type { EvstDecimal as everest_appserver_primitive_Decimal } from "@pkg/everest.appserver/types/primitives/Decimal";

import type {
  Filter,
  PresentationSession,
} from '@everestsystems/content-core';

/**
 * Generated backend types for OData presentation ProjectList.
 */
export namespace ProjectList {
  export namespace EntitySets {
    export namespace PsaProjects {
      export namespace Query {
        export type Instance = {
          id?: everest_psa_model_node_PsaProject.PsaProject["id"] | undefined;
          projectId?: everest_psa_model_node_PsaProject.PsaProject["projectId"] | undefined;
          projectName?: everest_appserver_primitive_Text | undefined;
          projectCode?: everest_appserver_primitive_Text | undefined;
          projectLeads?: {
            projectLead: everest_appserver_primitive_Text;
            projectLeadId: everest_appserver_primitive_Number;
            projectLeadDisplayName: everest_appserver_primitive_Text;
          }[] | undefined;
          startDate?: everest_appserver_primitive_PlainDate | undefined;
          endDate?: everest_appserver_primitive_PlainDate | undefined;
          status?: everest_psa_model_node_PsaProject.PsaProject["status"] | undefined;
          createdDate?: everest_appserver_primitive_DateTime | undefined;
          lastModifiedDate?: everest_appserver_primitive_DateTime | undefined;
          active?: everest_appserver_primitive_TrueFalse | undefined;
          currency?: everest_base_enum_BaseCurrency | undefined;
          budgetAmount?: everest_appserver_primitive_Decimal | undefined;
          salesRepresentativeId?: everest_appserver_primitive_Number | undefined;
          salesRepresentativeName?: everest_appserver_primitive_Text | undefined;
          invoiced?: everest_appserver_primitive_TrueFalse | undefined;
          psaProjectTypeName?: everest_appserver_primitive_Text | undefined;
        };

        export type Order = {
          field: keyof Instance;
          ordering: 'asc' | 'desc';
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            where: Filter<Instance>;
            orderBy: ReadonlyArray<Order>;
            skip: number | undefined;
            take: number | undefined;
            fieldList: ReadonlyArray<keyof Instance>;
            count: boolean;
          };

          export type Response = {
            instances: Instance[];

            /**
             * If count was set to true on the request object, this property
             * must be set, otherwise it must evaluate to undefined.
             */
            count?: number | undefined;
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export namespace Create {
        export type Input = {
          projectId?: everest_psa_model_node_PsaProject.PsaProject["projectId"] | undefined;
          projectName: everest_appserver_primitive_Text;
          projectCode?: everest_appserver_primitive_Text | undefined;
          projectLeads?: {
            projectLead: everest_appserver_primitive_Text;
            projectLeadId: everest_appserver_primitive_Number;
            projectLeadDisplayName: everest_appserver_primitive_Text;
          }[] | undefined;
          startDate?: everest_appserver_primitive_PlainDate | undefined;
          endDate?: everest_appserver_primitive_PlainDate | undefined;
          status: everest_psa_model_node_PsaProject.PsaProject["status"];
          currency: everest_base_enum_BaseCurrency;
          budgetAmount?: everest_appserver_primitive_Decimal | undefined;
          salesRepresentativeId?: everest_appserver_primitive_Number | undefined;
          salesRepresentativeName?: everest_appserver_primitive_Text | undefined;
          invoiced?: everest_appserver_primitive_TrueFalse | undefined;
          psaProjectTypeName?: everest_appserver_primitive_Text | undefined;
        };

        export type Instance = {
          id: everest_psa_model_node_PsaProject.PsaProject["id"];
          projectId?: everest_psa_model_node_PsaProject.PsaProject["projectId"] | undefined;
          projectName: everest_appserver_primitive_Text;
          projectCode?: everest_appserver_primitive_Text | undefined;
          projectLeads?: {
            projectLead: everest_appserver_primitive_Text;
            projectLeadId: everest_appserver_primitive_Number;
            projectLeadDisplayName: everest_appserver_primitive_Text;
          }[] | undefined;
          startDate?: everest_appserver_primitive_PlainDate | undefined;
          endDate?: everest_appserver_primitive_PlainDate | undefined;
          status: everest_psa_model_node_PsaProject.PsaProject["status"];
          createdDate?: everest_appserver_primitive_DateTime | undefined;
          lastModifiedDate?: everest_appserver_primitive_DateTime | undefined;
          active?: everest_appserver_primitive_TrueFalse | undefined;
          currency: everest_base_enum_BaseCurrency;
          budgetAmount?: everest_appserver_primitive_Decimal | undefined;
          salesRepresentativeId?: everest_appserver_primitive_Number | undefined;
          salesRepresentativeName?: everest_appserver_primitive_Text | undefined;
          invoiced?: everest_appserver_primitive_TrueFalse | undefined;
          psaProjectTypeName?: everest_appserver_primitive_Text | undefined;
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            inputs: Input[];
          };

          export type Response = {
            instances: Instance[];
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export namespace Delete {
        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            ids: number[];
          };

          export type Response = void;
        }

        export interface Implementation {
          execute(input: Execute.Request): Promise<Execute.Response>;
        }
      }

      export type Implementation = {
        query: Query.Implementation;
        create: Create.Implementation;
        delete: Delete.Implementation;
      };
    }
    export namespace ActiveAndPlannedEmployees {
      export namespace Query {
        export type Instance = {
          id?: everest_appserver_primitive_Number | undefined;
          name?: everest_appserver_primitive_Text | undefined;
          displayName?: everest_appserver_primitive_Text | undefined;
          email?: everest_appserver_primitive_Text | undefined;
        };

        export type Order = {
          field: keyof Instance;
          ordering: 'asc' | 'desc';
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            where: Filter<Instance>;
            orderBy: ReadonlyArray<Order>;
            skip: number | undefined;
            take: number | undefined;
            fieldList: ReadonlyArray<keyof Instance>;
            count: boolean;
          };

          export type Response = {
            instances: Instance[];

            /**
             * If count was set to true on the request object, this property
             * must be set, otherwise it must evaluate to undefined.
             */
            count?: number | undefined;
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export type Implementation = {
        query: Query.Implementation;
      };
    }
  }

  export namespace Actions {
    export namespace hasEditPermissions {
      export type Input = Record<string, never>;

      export type Output = {
        hasEditPermission: everest_appserver_primitive_TrueFalse;
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }
  }

  export type Implementation = {
    PsaProjects: EntitySets.PsaProjects.Implementation;
    ActiveAndPlannedEmployees: EntitySets.ActiveAndPlannedEmployees.Implementation;
    hasEditPermissions: Actions.hasEditPermissions.Implementation;
  };
}

