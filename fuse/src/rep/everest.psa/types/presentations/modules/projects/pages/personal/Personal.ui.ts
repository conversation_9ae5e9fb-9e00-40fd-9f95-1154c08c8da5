/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";

import { createOdataClientRuntime } from '@everestsystems/content-core/lib/runtime/loader';

/**
 * Generated frontend types for OData presentation Personal.
 */
export namespace PersonalUI {
  export namespace EntitySets { }

  export namespace Actions { }

  export namespace Functions {
    export namespace getEmployeeInfo {
      export namespace Execute {
        export type Input = Record<string, never>;

        export type Output = {
          displayName: everest_appserver_primitive_Text;
          employeeId: everest_appserver_primitive_Number;
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace getAccessibleEmployees {
      export namespace Execute {
        export type Input = Record<string, never>;

        export type Output = {
          employees: {
            id: everest_appserver_primitive_Number;
            displayName: everest_appserver_primitive_Text;
            firstName: everest_appserver_primitive_Text;
            lastName: everest_appserver_primitive_Text;
            email: everest_appserver_primitive_Text;
          }[];
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }
  }

  export namespace Permission {
    export namespace Check {
      export type Result = {
        actions: {
          getAccessibleEmployees: {
            isExecutePermitted: boolean;
          };
          getEmployeeInfo: {
            isExecutePermitted: boolean;
          };
        };
      };

      export interface Request {
        execute(): Promise<Response>;
      }

      export type Response = {
        result: Result;
      };
    }
  }

  export namespace Api {
    export type EntitySets = Record<string, never>;

    export type Actions = Record<string, never>;

    export type Functions = {
      getEmployeeInfo: {
        execute: Functions.getEmployeeInfo.Execute.Request;
      };
      getAccessibleEmployees: {
        execute: Functions.getAccessibleEmployees.Execute.Request;
      };
    };

    export interface Client {
      createFunctionExecuteRequest<T extends 'getEmployeeInfo' | 'getAccessibleEmployees'>(functionName: T): Api.Functions[T]['execute'];

      createPermissionCheckRequest(): Permission.Check.Request;
    }
  }

  export const client = createOdataClientRuntime<Api.Client>('everest.psa/modules/projects/pages/personal/Personal');
}

