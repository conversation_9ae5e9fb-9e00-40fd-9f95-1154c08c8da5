/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

import type { PsaProject as everest_psa_model_node_PsaProject } from "@pkg/everest.psa/types/PsaProject";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { EvstPlainDate as everest_appserver_primitive_PlainDate } from "@pkg/everest.appserver/types/primitives/PlainDate";
import type { EvstDateTime as everest_appserver_primitive_DateTime } from "@pkg/everest.appserver/types/primitives/DateTime";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { EvstBaseCurrency as everest_base_enum_BaseCurrency } from "@pkg/everest.base/types/enums/BaseCurrency";
import type { EvstDecimal as everest_appserver_primitive_Decimal } from "@pkg/everest.appserver/types/primitives/Decimal";

import { createOdataClientRuntime } from '@everestsystems/content-core/lib/runtime/loader';

/**
 * Generated frontend types for OData presentation ProjectList.
 */
export namespace ProjectListUI {
  export namespace EntitySets {
    export namespace PsaProjects {
      export namespace Get {
        export type Entity = {
          id?: everest_psa_model_node_PsaProject.PsaProject["id"] | undefined;
          projectId?: everest_psa_model_node_PsaProject.PsaProject["projectId"] | undefined;
          projectName?: everest_appserver_primitive_Text | undefined;
          projectCode?: everest_appserver_primitive_Text | undefined;
          projectLeads?: {
            projectLead: everest_appserver_primitive_Text;
            projectLeadId: everest_appserver_primitive_Number;
            projectLeadDisplayName: everest_appserver_primitive_Text;
          }[] | undefined;
          startDate?: everest_appserver_primitive_PlainDate | undefined;
          endDate?: everest_appserver_primitive_PlainDate | undefined;
          status?: everest_psa_model_node_PsaProject.PsaProject["status"] | undefined;
          createdDate?: everest_appserver_primitive_DateTime | undefined;
          lastModifiedDate?: everest_appserver_primitive_DateTime | undefined;
          active?: everest_appserver_primitive_TrueFalse | undefined;
          currency?: everest_base_enum_BaseCurrency | undefined;
          budgetAmount?: everest_appserver_primitive_Decimal | undefined;
          salesRepresentativeId?: everest_appserver_primitive_Number | undefined;
          salesRepresentativeName?: everest_appserver_primitive_Text | undefined;
          invoiced?: everest_appserver_primitive_TrueFalse | undefined;
          psaProjectTypeName?: everest_appserver_primitive_Text | undefined;
        };

        export type Filter =
          | PropertyFilter
          | [Filter, 'and', Filter]
          | [Filter, 'or', Filter];

        /**
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), filters must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to filter by its amount component.
         */
        export type PropertyFilter = {
          property: string;
          comparator: 'eq' | 'ne' | 'lt' | 'le' | 'gt' | 'ge' | 'contains';
          value: unknown;
        };

        /**
         * Specifies sorting for a property in ascending or descending order.
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), sorting must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to sort by its amount component.
         */
        export type OrderbyItem = {
          property: string;
          ordering: 'asc' | 'desc';
        };

        export interface Request {
          setFilter(filter: Filter): this;

          setOrderby(orderby: ReadonlyArray<OrderbyItem>): this;

          setSkip(skip: number): this;

          setTop(top: number): this;

          setSelect(select: ReadonlyArray<keyof Entity>): this;

          setCount(count: boolean): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entities: Entity[];
          count?: number | undefined;
        };
      }

      export namespace Post {
        export type Data = {
          projectId?: everest_psa_model_node_PsaProject.PsaProject["projectId"] | undefined;
          projectName?: everest_appserver_primitive_Text | undefined;
          projectCode?: everest_appserver_primitive_Text | undefined;
          projectLeads?: {
            projectLead: everest_appserver_primitive_Text;
            projectLeadId: everest_appserver_primitive_Number;
            projectLeadDisplayName: everest_appserver_primitive_Text;
          }[] | undefined;
          startDate?: everest_appserver_primitive_PlainDate | undefined;
          endDate?: everest_appserver_primitive_PlainDate | undefined;
          status?: everest_psa_model_node_PsaProject.PsaProject["status"] | undefined;
          currency?: everest_base_enum_BaseCurrency | undefined;
          budgetAmount?: everest_appserver_primitive_Decimal | undefined;
          salesRepresentativeId?: everest_appserver_primitive_Number | undefined;
          salesRepresentativeName?: everest_appserver_primitive_Text | undefined;
          invoiced?: everest_appserver_primitive_TrueFalse | undefined;
          psaProjectTypeName?: everest_appserver_primitive_Text | undefined;
        };

        export type Entity = {
          id: everest_psa_model_node_PsaProject.PsaProject["id"];
          projectId?: everest_psa_model_node_PsaProject.PsaProject["projectId"] | undefined;
          projectName: everest_appserver_primitive_Text;
          projectCode?: everest_appserver_primitive_Text | undefined;
          projectLeads?: {
            projectLead: everest_appserver_primitive_Text;
            projectLeadId: everest_appserver_primitive_Number;
            projectLeadDisplayName: everest_appserver_primitive_Text;
          }[] | undefined;
          startDate?: everest_appserver_primitive_PlainDate | undefined;
          endDate?: everest_appserver_primitive_PlainDate | undefined;
          status: everest_psa_model_node_PsaProject.PsaProject["status"];
          createdDate?: everest_appserver_primitive_DateTime | undefined;
          lastModifiedDate?: everest_appserver_primitive_DateTime | undefined;
          active?: everest_appserver_primitive_TrueFalse | undefined;
          currency: everest_base_enum_BaseCurrency;
          budgetAmount?: everest_appserver_primitive_Decimal | undefined;
          salesRepresentativeId?: everest_appserver_primitive_Number | undefined;
          salesRepresentativeName?: everest_appserver_primitive_Text | undefined;
          invoiced?: everest_appserver_primitive_TrueFalse | undefined;
          psaProjectTypeName?: everest_appserver_primitive_Text | undefined;
        };

        export interface Request {
          setData(data: Data): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entity: Entity;
        };
      }

      export namespace Delete {
        export interface Request {
          setId(id: number): this;

          execute(): Promise<Response>;
        }

        export type Response = Record<string, never>;
      }
    }
    export namespace ActiveAndPlannedEmployees {
      export namespace Get {
        export type Entity = {
          id?: everest_appserver_primitive_Number | undefined;
          name?: everest_appserver_primitive_Text | undefined;
          displayName?: everest_appserver_primitive_Text | undefined;
          email?: everest_appserver_primitive_Text | undefined;
        };

        export type Filter =
          | PropertyFilter
          | [Filter, 'and', Filter]
          | [Filter, 'or', Filter];

        /**
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), filters must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to filter by its amount component.
         */
        export type PropertyFilter = {
          property: string;
          comparator: 'eq' | 'ne' | 'lt' | 'le' | 'gt' | 'ge' | 'contains';
          value: unknown;
        };

        /**
         * Specifies sorting for a property in ascending or descending order.
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), sorting must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to sort by its amount component.
         */
        export type OrderbyItem = {
          property: string;
          ordering: 'asc' | 'desc';
        };

        export interface Request {
          setFilter(filter: Filter): this;

          setOrderby(orderby: ReadonlyArray<OrderbyItem>): this;

          setSkip(skip: number): this;

          setTop(top: number): this;

          setSelect(select: ReadonlyArray<keyof Entity>): this;

          setCount(count: boolean): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entities: Entity[];
          count?: number | undefined;
        };
      }
    }
  }

  export namespace Actions { }

  export namespace Functions {
    export namespace hasEditPermissions {
      export namespace Execute {
        export type Input = Record<string, never>;

        export type Output = {
          hasEditPermission: everest_appserver_primitive_TrueFalse;
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }
  }

  export namespace Permission {
    export namespace Check {
      export type Result = {
        dataSets: {
          ActiveAndPlannedEmployees: {
            isGetPermitted: boolean;
          };
          PsaProjects: {
            isGetPermitted: boolean;
            isPostPermitted: boolean;
            isDeletePermitted: boolean;
          };
        };
        actions: {
          hasEditPermissions: {
            isExecutePermitted: boolean;
          };
        };
      };

      export interface Request {
        execute(): Promise<Response>;
      }

      export type Response = {
        result: Result;
      };
    }
  }

  export namespace Api {
    export type EntitySets = {
      PsaProjects: {
        get: EntitySets.PsaProjects.Get.Request;
        post: EntitySets.PsaProjects.Post.Request;
        delete: EntitySets.PsaProjects.Delete.Request;
      };
      ActiveAndPlannedEmployees: {
        get: EntitySets.ActiveAndPlannedEmployees.Get.Request;
      };
    };

    export type Actions = Record<string, never>;

    export type Functions = {
      hasEditPermissions: {
        execute: Functions.hasEditPermissions.Execute.Request;
      };
    };

    export interface Client {
      createEntitySetGetRequest<T extends 'PsaProjects' | 'ActiveAndPlannedEmployees'>(entitySetName: T): Api.EntitySets[T]['get'];

      createEntitySetPostRequest<T extends 'PsaProjects'>(entitySetName: T): Api.EntitySets[T]['post'];

      createEntitySetDeleteRequest<T extends 'PsaProjects'>(entitySetName: T): Api.EntitySets[T]['delete'];

      createFunctionExecuteRequest<T extends 'hasEditPermissions'>(functionName: T): Api.Functions[T]['execute'];

      createPermissionCheckRequest(): Permission.Check.Request;
    }
  }

  export const client = createOdataClientRuntime<Api.Client>('everest.psa/modules/projects/pages/projectList/ProjectList');
}

