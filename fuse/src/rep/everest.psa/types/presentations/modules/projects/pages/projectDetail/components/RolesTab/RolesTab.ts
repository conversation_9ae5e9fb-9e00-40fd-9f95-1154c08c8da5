/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

import type { ProjectPosition as everest_timetracking_model_node_ProjectPosition } from "@pkg/everest.timetracking/types/ProjectPosition";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { EvstDecimal as everest_appserver_primitive_Decimal } from "@pkg/everest.appserver/types/primitives/Decimal";
import type { EvstBaseCurrency as everest_base_enum_BaseCurrency } from "@pkg/everest.base/types/enums/BaseCurrency";
import type { TimetrackingProject as everest_timetracking_model_node_TimetrackingProject } from "@pkg/everest.timetracking/types/TimetrackingProject";
import type { PsaProject as everest_psa_model_node_PsaProject } from "@pkg/everest.psa/types/PsaProject";
import type { SalesArrangement as everest_fin_accounting_model_node_SalesArrangement } from "@pkg/everest.fin.accounting/types/SalesArrangement";
import type { SalesOrder as everest_fin_accounting_model_node_SalesOrder } from "@pkg/everest.fin.accounting/types/SalesOrder";
import type { SalesOrderProduct as everest_fin_accounting_model_node_SalesOrderProduct } from "@pkg/everest.fin.accounting/types/SalesOrderProduct";
import type { EvstCurrencyAmount as everest_base_composite_CurrencyAmount } from "@pkg/everest.base/types/composites/CurrencyAmount";
import type { SalesOrderProductLine as everest_fin_accounting_model_node_SalesOrderProductLine } from "@pkg/everest.fin.accounting/types/SalesOrderProductLine";
import type { EvstPlainDate as everest_appserver_primitive_PlainDate } from "@pkg/everest.appserver/types/primitives/PlainDate";
import type { EvstJSON as everest_appserver_primitive_JSON } from "@pkg/everest.appserver/types/primitives/JSON";
import type { EvstProductLineType as everest_fin_accounting_enum_ProductLineType } from "@pkg/everest.fin.accounting/types/enums/ProductLineType";
import type { ProductLine as everest_fin_accounting_model_node_ProductLine } from "@pkg/everest.fin.accounting/types/ProductLine";
import type { InvoiceMilestone as everest_fin_accounting_model_node_InvoiceMilestone } from "@pkg/everest.fin.accounting/types/InvoiceMilestone";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";

import type {
  Filter,
  PresentationSession,
} from '@everestsystems/content-core';

/**
 * Generated backend types for OData presentation RolesTab.
 */
export namespace RolesTab {
  export namespace EntitySets {
    export namespace ProjectRoles {
      export namespace Query {
        export type Instance = {
          id?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["id"] | undefined;
          uuid?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["uuid"] | undefined;
          name?: everest_appserver_primitive_Text | undefined;
          description?: everest_appserver_primitive_Text | undefined;
          billable?: everest_appserver_primitive_TrueFalse | undefined;
          hourlyBillRate?: everest_appserver_primitive_Decimal | undefined;
          hourlyBillRateCurrency?: everest_base_enum_BaseCurrency | undefined;
          hourlyCostRate?: everest_appserver_primitive_Decimal | undefined;
          hourlyCostRateCurrency?: everest_base_enum_BaseCurrency | undefined;
          hoursBudget?: everest_appserver_primitive_Decimal | undefined;
          salesOrderProductLineId?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["salesOrderProductLineId"] | undefined;
          projectId?: everest_timetracking_model_node_TimetrackingProject.TimetrackingProject["id"] | undefined;
          invoiced?: everest_appserver_primitive_TrueFalse | undefined;
        };

        export type Order = {
          field: keyof Instance;
          ordering: 'asc' | 'desc';
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            where: Filter<Instance>;
            orderBy: ReadonlyArray<Order>;
            skip: number | undefined;
            take: number | undefined;
            fieldList: ReadonlyArray<keyof Instance>;
            count: boolean;
          };

          export type Response = {
            instances: Instance[];

            /**
             * If count was set to true on the request object, this property
             * must be set, otherwise it must evaluate to undefined.
             */
            count?: number | undefined;
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export namespace Create {
        export type Input = {
          uuid?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["uuid"] | undefined;
          name: everest_appserver_primitive_Text;
          description?: everest_appserver_primitive_Text | undefined;
          billable?: everest_appserver_primitive_TrueFalse | undefined;
          hourlyBillRate?: everest_appserver_primitive_Decimal | undefined;
          hourlyBillRateCurrency?: everest_base_enum_BaseCurrency | undefined;
          hourlyCostRate?: everest_appserver_primitive_Decimal | undefined;
          hourlyCostRateCurrency?: everest_base_enum_BaseCurrency | undefined;
          hoursBudget?: everest_appserver_primitive_Decimal | undefined;
          salesOrderProductLineId?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["salesOrderProductLineId"] | undefined;
          projectId: everest_timetracking_model_node_TimetrackingProject.TimetrackingProject["id"];
          invoiced?: everest_appserver_primitive_TrueFalse | undefined;
        };

        export type Instance = {
          id: everest_timetracking_model_node_ProjectPosition.ProjectPosition["id"];
          uuid?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["uuid"] | undefined;
          name: everest_appserver_primitive_Text;
          description?: everest_appserver_primitive_Text | undefined;
          billable?: everest_appserver_primitive_TrueFalse | undefined;
          hourlyBillRate?: everest_appserver_primitive_Decimal | undefined;
          hourlyBillRateCurrency?: everest_base_enum_BaseCurrency | undefined;
          hourlyCostRate?: everest_appserver_primitive_Decimal | undefined;
          hourlyCostRateCurrency?: everest_base_enum_BaseCurrency | undefined;
          hoursBudget?: everest_appserver_primitive_Decimal | undefined;
          salesOrderProductLineId?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["salesOrderProductLineId"] | undefined;
          projectId: everest_timetracking_model_node_TimetrackingProject.TimetrackingProject["id"];
          invoiced?: everest_appserver_primitive_TrueFalse | undefined;
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            inputs: Input[];
          };

          export type Response = {
            instances: Instance[];
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export namespace Update {
        export type Data = {
          uuid?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["uuid"] | null;
          name?: everest_appserver_primitive_Text | null;
          description?: everest_appserver_primitive_Text | null;
          billable?: everest_appserver_primitive_TrueFalse | null;
          hourlyBillRate?: everest_appserver_primitive_Decimal | null;
          hourlyBillRateCurrency?: everest_base_enum_BaseCurrency | null;
          hourlyCostRate?: everest_appserver_primitive_Decimal | null;
          hourlyCostRateCurrency?: everest_base_enum_BaseCurrency | null;
          hoursBudget?: everest_appserver_primitive_Decimal | null;
          salesOrderProductLineId?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["salesOrderProductLineId"] | null;
          projectId?: everest_timetracking_model_node_TimetrackingProject.TimetrackingProject["id"] | null;
          invoiced?: everest_appserver_primitive_TrueFalse | null;
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            id: number;
            data: Data;
          };

          export type Response = void;
        }

        export interface Implementation {
          execute(input: Execute.Request): Promise<Execute.Response>;
        }
      }

      export namespace Delete {
        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            ids: number[];
          };

          export type Response = void;
        }

        export interface Implementation {
          execute(input: Execute.Request): Promise<Execute.Response>;
        }
      }

      export type Implementation = {
        query: Query.Implementation;
        create: Create.Implementation;
        update: Update.Implementation;
        delete: Delete.Implementation;
      };
    }
    export namespace Projects {
      export namespace Query {
        export type Instance = {
          id?: everest_psa_model_node_PsaProject.PsaProject["id"] | undefined;
          currency?: everest_base_enum_BaseCurrency | undefined;
          salesArrangement?: {
            id: everest_fin_accounting_model_node_SalesArrangement.SalesArrangement["id"];
            salesArrangementNumber: everest_appserver_primitive_Text;
            salesOrders: {
              id: everest_fin_accounting_model_node_SalesOrder.SalesOrder["id"];
              salesOrderNumber: everest_appserver_primitive_Text;
              products: {
                id: everest_fin_accounting_model_node_SalesOrderProduct.SalesOrderProduct["id"];
                productName: everest_appserver_primitive_Text;
                totalPrice: everest_base_composite_CurrencyAmount;
                active: everest_appserver_primitive_TrueFalse;
                productLines: {
                  id: everest_fin_accounting_model_node_SalesOrderProductLine.SalesOrderProductLine["id"];
                  productLineName: everest_appserver_primitive_Text;
                  salesOrderProductLineNumber: everest_appserver_primitive_Text;
                  serviceStartDate: everest_appserver_primitive_PlainDate;
                  serviceEndDate: everest_appserver_primitive_PlainDate;
                  quantity: everest_appserver_primitive_JSON;
                  unitPrice: everest_base_composite_CurrencyAmount;
                  totalPrice: everest_base_composite_CurrencyAmount;
                  priceModel: everest_appserver_primitive_Text;
                  invoiceFrequency: everest_appserver_primitive_Text;
                  lineType: everest_fin_accounting_enum_ProductLineType;
                  active: everest_appserver_primitive_TrueFalse;
                  productLineId: everest_fin_accounting_model_node_ProductLine.ProductLine["id"];
                  unitCost: everest_base_composite_CurrencyAmount;
                  invoiceMilestones: {
                    id: everest_fin_accounting_model_node_InvoiceMilestone.InvoiceMilestone["id"];
                    description: everest_appserver_primitive_Text;
                    totalPrice: everest_base_composite_CurrencyAmount;
                    projectedDate: everest_appserver_primitive_PlainDate;
                    invoiceDate: everest_appserver_primitive_PlainDate;
                    invoiceNumber: everest_appserver_primitive_Text;
                  }[];
                }[];
              }[];
            }[];
          } | undefined;
        };

        export type Order = {
          field: keyof Instance;
          ordering: 'asc' | 'desc';
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            where: Filter<Instance>;
            orderBy: ReadonlyArray<Order>;
            skip: number | undefined;
            take: number | undefined;
            fieldList: ReadonlyArray<keyof Instance>;
            count: boolean;
          };

          export type Response = {
            instances: Instance[];

            /**
             * If count was set to true on the request object, this property
             * must be set, otherwise it must evaluate to undefined.
             */
            count?: number | undefined;
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export type Implementation = {
        query: Query.Implementation;
      };
    }
  }

  export namespace Actions {
    export namespace validateSOLinks {
      export type Input = {
        psaProjectId: everest_psa_model_node_PsaProject.PsaProject["id"];
      };

      export type Output = {
        warnings: {
          message: everest_appserver_primitive_Text;
          code: everest_appserver_primitive_Text;
          severity: everest_appserver_primitive_Text;
          id: everest_appserver_primitive_Number;
          artifactType: everest_appserver_primitive_Text;
        }[];
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }
  }

  export type Implementation = {
    ProjectRoles: EntitySets.ProjectRoles.Implementation;
    Projects: EntitySets.Projects.Implementation;
    validateSOLinks: Actions.validateSOLinks.Implementation;
  };
}

