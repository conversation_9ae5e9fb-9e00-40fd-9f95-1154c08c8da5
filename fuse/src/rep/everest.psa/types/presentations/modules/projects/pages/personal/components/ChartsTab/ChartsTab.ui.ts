/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

import type { EvstPlainDate as everest_appserver_primitive_PlainDate } from "@pkg/everest.appserver/types/primitives/PlainDate";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { EvstDecimal as everest_appserver_primitive_Decimal } from "@pkg/everest.appserver/types/primitives/Decimal";

import { createOdataClientRuntime } from '@everestsystems/content-core/lib/runtime/loader';

/**
 * Generated frontend types for OData presentation ChartsTab.
 */
export namespace ChartsTabUI {
  export namespace EntitySets { }

  export namespace Actions { }

  export namespace Functions {
    export namespace getUtilizationMetrics {
      export namespace Execute {
        export type Input = {
          startDate: everest_appserver_primitive_PlainDate;
          endDate: everest_appserver_primitive_PlainDate;
          employeeId: everest_appserver_primitive_Number;
        };

        export type Output = {
          totalHours: everest_appserver_primitive_Decimal;
          totalBillableHours: everest_appserver_primitive_Decimal;
          totalHoursIncludingAbsences: everest_appserver_primitive_Decimal;
          utilization: everest_appserver_primitive_Decimal;
          billableUtilization: everest_appserver_primitive_Decimal;
          utilizationWithoutAbsences: everest_appserver_primitive_Decimal;
          billableUtilizationWithoutAbsences: everest_appserver_primitive_Decimal;
          dailyBreakdown: {
            date: everest_appserver_primitive_PlainDate;
            billableHours: everest_appserver_primitive_Decimal;
            nonBillableHours: everest_appserver_primitive_Decimal;
            timeOffHours: everest_appserver_primitive_Decimal;
          }[];
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }
  }

  export namespace Permission {
    export namespace Check {
      export type Result = {
        actions: {
          getUtilizationMetrics: {
            isExecutePermitted: boolean;
          };
        };
      };

      export interface Request {
        execute(): Promise<Response>;
      }

      export type Response = {
        result: Result;
      };
    }
  }

  export namespace Api {
    export type EntitySets = Record<string, never>;

    export type Actions = Record<string, never>;

    export type Functions = {
      getUtilizationMetrics: {
        execute: Functions.getUtilizationMetrics.Execute.Request;
      };
    };

    export interface Client {
      createFunctionExecuteRequest<T extends 'getUtilizationMetrics'>(functionName: T): Api.Functions[T]['execute'];

      createPermissionCheckRequest(): Permission.Check.Request;
    }
  }

  export const client = createOdataClientRuntime<Api.Client>('everest.psa/modules/projects/pages/personal/components/ChartsTab/ChartsTab');
}

