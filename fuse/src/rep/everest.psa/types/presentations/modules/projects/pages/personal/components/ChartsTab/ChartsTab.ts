/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

import type { EvstPlainDate as everest_appserver_primitive_PlainDate } from "@pkg/everest.appserver/types/primitives/PlainDate";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { EvstDecimal as everest_appserver_primitive_Decimal } from "@pkg/everest.appserver/types/primitives/Decimal";

import type { PresentationSession } from '@everestsystems/content-core';

/**
 * Generated backend types for OData presentation ChartsTab.
 */
export namespace ChartsTab {
  export namespace EntitySets { }

  export namespace Actions {
    export namespace getUtilizationMetrics {
      export type Input = {
        startDate: everest_appserver_primitive_PlainDate;
        endDate: everest_appserver_primitive_PlainDate;
        employeeId: everest_appserver_primitive_Number;
      };

      export type Output = {
        totalHours: everest_appserver_primitive_Decimal;
        totalBillableHours: everest_appserver_primitive_Decimal;
        totalHoursIncludingAbsences: everest_appserver_primitive_Decimal;
        utilization: everest_appserver_primitive_Decimal;
        billableUtilization: everest_appserver_primitive_Decimal;
        utilizationWithoutAbsences: everest_appserver_primitive_Decimal;
        billableUtilizationWithoutAbsences: everest_appserver_primitive_Decimal;
        dailyBreakdown: {
          date: everest_appserver_primitive_PlainDate;
          billableHours: everest_appserver_primitive_Decimal;
          nonBillableHours: everest_appserver_primitive_Decimal;
          timeOffHours: everest_appserver_primitive_Decimal;
        }[];
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }
  }

  export type Implementation = {
    getUtilizationMetrics: Actions.getUtilizationMetrics.Implementation;
  };
}

