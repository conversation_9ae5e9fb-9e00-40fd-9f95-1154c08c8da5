/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

import type { FixedFee as everest_psa_model_node_FixedFee } from "@pkg/everest.psa/types/FixedFee";
import type { PsaProject as everest_psa_model_node_PsaProject } from "@pkg/everest.psa/types/PsaProject";
import type { EvstPlainDate as everest_appserver_primitive_PlainDate } from "@pkg/everest.appserver/types/primitives/PlainDate";
import type { EvstCurrencyAmount as everest_base_composite_CurrencyAmount } from "@pkg/everest.base/types/composites/CurrencyAmount";
import type { EvstFixedFeeFrequency as everest_psa_enum_FixedFeeFrequency } from "@pkg/everest.psa/types/enums/FixedFeeFrequency";
import type { EvstBaseCurrency as everest_base_enum_BaseCurrency } from "@pkg/everest.base/types/enums/BaseCurrency";
import type { SalesArrangement as everest_fin_accounting_model_node_SalesArrangement } from "@pkg/everest.fin.accounting/types/SalesArrangement";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { SalesOrder as everest_fin_accounting_model_node_SalesOrder } from "@pkg/everest.fin.accounting/types/SalesOrder";
import type { SalesOrderProduct as everest_fin_accounting_model_node_SalesOrderProduct } from "@pkg/everest.fin.accounting/types/SalesOrderProduct";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { SalesOrderProductLine as everest_fin_accounting_model_node_SalesOrderProductLine } from "@pkg/everest.fin.accounting/types/SalesOrderProductLine";
import type { EvstJSON as everest_appserver_primitive_JSON } from "@pkg/everest.appserver/types/primitives/JSON";
import type { EvstProductLineType as everest_fin_accounting_enum_ProductLineType } from "@pkg/everest.fin.accounting/types/enums/ProductLineType";
import type { InvoiceMilestone as everest_fin_accounting_model_node_InvoiceMilestone } from "@pkg/everest.fin.accounting/types/InvoiceMilestone";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";

import type {
  Filter,
  PresentationSession,
} from '@everestsystems/content-core';

/**
 * Generated backend types for OData presentation FixedFee.
 */
export namespace FixedFee {
  export namespace EntitySets {
    export namespace FixedFees {
      export namespace Query {
        export type Instance = {
          id?: everest_psa_model_node_FixedFee.FixedFee["id"] | undefined;
          psaProjectId?: everest_psa_model_node_PsaProject.PsaProject["id"] | undefined;
          startDate?: everest_appserver_primitive_PlainDate | undefined;
          endDate?: everest_appserver_primitive_PlainDate | undefined;
          amount?: everest_base_composite_CurrencyAmount | undefined;
          frequency?: everest_psa_enum_FixedFeeFrequency | undefined;
          salesOrderProductLineId?: everest_psa_model_node_FixedFee.FixedFee["salesOrderProductLineId"] | undefined;
        };

        export type Order = {
          field: keyof Instance;
          ordering: 'asc' | 'desc';
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            where: Filter<Instance>;
            orderBy: ReadonlyArray<Order>;
            skip: number | undefined;
            take: number | undefined;
            fieldList: ReadonlyArray<keyof Instance>;
            count: boolean;
          };

          export type Response = {
            instances: Instance[];

            /**
             * If count was set to true on the request object, this property
             * must be set, otherwise it must evaluate to undefined.
             */
            count?: number | undefined;
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export namespace Create {
        export type Input = {
          psaProjectId: everest_psa_model_node_PsaProject.PsaProject["id"];
          startDate: everest_appserver_primitive_PlainDate;
          endDate: everest_appserver_primitive_PlainDate;
          amount: everest_base_composite_CurrencyAmount;
          frequency: everest_psa_enum_FixedFeeFrequency;
          salesOrderProductLineId?: everest_psa_model_node_FixedFee.FixedFee["salesOrderProductLineId"] | undefined;
        };

        export type Instance = {
          id: everest_psa_model_node_FixedFee.FixedFee["id"];
          psaProjectId: everest_psa_model_node_PsaProject.PsaProject["id"];
          startDate: everest_appserver_primitive_PlainDate;
          endDate: everest_appserver_primitive_PlainDate;
          amount: everest_base_composite_CurrencyAmount;
          frequency: everest_psa_enum_FixedFeeFrequency;
          salesOrderProductLineId?: everest_psa_model_node_FixedFee.FixedFee["salesOrderProductLineId"] | undefined;
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            inputs: Input[];
          };

          export type Response = {
            instances: Instance[];
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export namespace Update {
        export type Data = {
          psaProjectId?: everest_psa_model_node_PsaProject.PsaProject["id"] | null;
          startDate?: everest_appserver_primitive_PlainDate | null;
          endDate?: everest_appserver_primitive_PlainDate | null;
          amount?: everest_base_composite_CurrencyAmount | null;
          frequency?: everest_psa_enum_FixedFeeFrequency | null;
          salesOrderProductLineId?: everest_psa_model_node_FixedFee.FixedFee["salesOrderProductLineId"] | null;
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            id: number;
            data: Data;
          };

          export type Response = void;
        }

        export interface Implementation {
          execute(input: Execute.Request): Promise<Execute.Response>;
        }
      }

      export namespace Delete {
        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            ids: number[];
          };

          export type Response = void;
        }

        export interface Implementation {
          execute(input: Execute.Request): Promise<Execute.Response>;
        }
      }

      export type Implementation = {
        query: Query.Implementation;
        create: Create.Implementation;
        update: Update.Implementation;
        delete: Delete.Implementation;
      };
    }
    export namespace Projects {
      export namespace Query {
        export type Instance = {
          id?: everest_psa_model_node_PsaProject.PsaProject["id"] | undefined;
          currency?: everest_base_enum_BaseCurrency | undefined;
          salesArrangement?: {
            id: everest_fin_accounting_model_node_SalesArrangement.SalesArrangement["id"];
            salesArrangementNumber: everest_appserver_primitive_Text;
            salesOrders: {
              id: everest_fin_accounting_model_node_SalesOrder.SalesOrder["id"];
              salesOrderNumber: everest_appserver_primitive_Text;
              products: {
                id: everest_fin_accounting_model_node_SalesOrderProduct.SalesOrderProduct["id"];
                productName: everest_appserver_primitive_Text;
                totalPrice: everest_base_composite_CurrencyAmount;
                active: everest_appserver_primitive_TrueFalse;
                productLines: {
                  id: everest_fin_accounting_model_node_SalesOrderProductLine.SalesOrderProductLine["id"];
                  productLineName: everest_appserver_primitive_Text;
                  salesOrderProductLineNumber: everest_appserver_primitive_Text;
                  serviceStartDate: everest_appserver_primitive_PlainDate;
                  serviceEndDate: everest_appserver_primitive_PlainDate;
                  quantity: everest_appserver_primitive_JSON;
                  unitPrice: everest_appserver_primitive_JSON;
                  totalPrice: everest_base_composite_CurrencyAmount;
                  priceModel: everest_appserver_primitive_Text;
                  invoiceFrequency: everest_appserver_primitive_Text;
                  lineType: everest_fin_accounting_enum_ProductLineType;
                  active: everest_appserver_primitive_TrueFalse;
                  productLineId: everest_fin_accounting_model_node_SalesOrderProductLine.SalesOrderProductLine["id"];
                  unitCost: everest_base_composite_CurrencyAmount;
                  invoiceMilestones: {
                    id: everest_fin_accounting_model_node_InvoiceMilestone.InvoiceMilestone["id"];
                    description: everest_appserver_primitive_Text;
                    totalPrice: everest_base_composite_CurrencyAmount;
                    projectedDate: everest_appserver_primitive_PlainDate;
                    invoiceDate: everest_appserver_primitive_PlainDate;
                    invoiceNumber: everest_appserver_primitive_Text;
                  }[];
                }[];
              }[];
            }[];
          } | undefined;
        };

        export type Order = {
          field: keyof Instance;
          ordering: 'asc' | 'desc';
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            where: Filter<Instance>;
            orderBy: ReadonlyArray<Order>;
            skip: number | undefined;
            take: number | undefined;
            fieldList: ReadonlyArray<keyof Instance>;
            count: boolean;
          };

          export type Response = {
            instances: Instance[];

            /**
             * If count was set to true on the request object, this property
             * must be set, otherwise it must evaluate to undefined.
             */
            count?: number | undefined;
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export type Implementation = {
        query: Query.Implementation;
      };
    }
  }

  export namespace Actions {
    export namespace validateFixedFees {
      export type Input = {
        psaProjectId: everest_psa_model_node_PsaProject.PsaProject["id"];
      };

      export type Output = {
        warnings: {
          message: everest_appserver_primitive_Text;
          code: everest_appserver_primitive_Text;
          severity: everest_appserver_primitive_Text;
          id: everest_appserver_primitive_Number;
          artifactType: everest_appserver_primitive_Text;
        }[];
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }
  }

  export type Implementation = {
    FixedFees: EntitySets.FixedFees.Implementation;
    Projects: EntitySets.Projects.Implementation;
    validateFixedFees: Actions.validateFixedFees.Implementation;
  };
}

