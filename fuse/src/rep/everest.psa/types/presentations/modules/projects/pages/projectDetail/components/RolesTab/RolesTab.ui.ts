/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

import type { ProjectPosition as everest_timetracking_model_node_ProjectPosition } from "@pkg/everest.timetracking/types/ProjectPosition";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { EvstDecimal as everest_appserver_primitive_Decimal } from "@pkg/everest.appserver/types/primitives/Decimal";
import type { EvstBaseCurrency as everest_base_enum_BaseCurrency } from "@pkg/everest.base/types/enums/BaseCurrency";
import type { TimetrackingProject as everest_timetracking_model_node_TimetrackingProject } from "@pkg/everest.timetracking/types/TimetrackingProject";
import type { PsaProject as everest_psa_model_node_PsaProject } from "@pkg/everest.psa/types/PsaProject";
import type { SalesArrangement as everest_fin_accounting_model_node_SalesArrangement } from "@pkg/everest.fin.accounting/types/SalesArrangement";
import type { SalesOrder as everest_fin_accounting_model_node_SalesOrder } from "@pkg/everest.fin.accounting/types/SalesOrder";
import type { SalesOrderProduct as everest_fin_accounting_model_node_SalesOrderProduct } from "@pkg/everest.fin.accounting/types/SalesOrderProduct";
import type { EvstCurrencyAmount as everest_base_composite_CurrencyAmount } from "@pkg/everest.base/types/composites/CurrencyAmount";
import type { SalesOrderProductLine as everest_fin_accounting_model_node_SalesOrderProductLine } from "@pkg/everest.fin.accounting/types/SalesOrderProductLine";
import type { EvstPlainDate as everest_appserver_primitive_PlainDate } from "@pkg/everest.appserver/types/primitives/PlainDate";
import type { EvstJSON as everest_appserver_primitive_JSON } from "@pkg/everest.appserver/types/primitives/JSON";
import type { EvstProductLineType as everest_fin_accounting_enum_ProductLineType } from "@pkg/everest.fin.accounting/types/enums/ProductLineType";
import type { ProductLine as everest_fin_accounting_model_node_ProductLine } from "@pkg/everest.fin.accounting/types/ProductLine";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";

import { createOdataClientRuntime } from '@everestsystems/content-core/lib/runtime/loader';

/**
 * Generated frontend types for OData presentation RolesTab.
 */
export namespace RolesTabUI {
  export namespace EntitySets {
    export namespace ProjectRoles {
      export namespace Get {
        export type Entity = {
          id?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["id"] | undefined;
          uuid?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["uuid"] | undefined;
          name?: everest_appserver_primitive_Text | undefined;
          description?: everest_appserver_primitive_Text | undefined;
          billable?: everest_appserver_primitive_TrueFalse | undefined;
          hourlyBillRate?: everest_appserver_primitive_Decimal | undefined;
          hourlyBillRateCurrency?: everest_base_enum_BaseCurrency | undefined;
          hourlyCostRate?: everest_appserver_primitive_Decimal | undefined;
          hourlyCostRateCurrency?: everest_base_enum_BaseCurrency | undefined;
          hoursBudget?: everest_appserver_primitive_Decimal | undefined;
          salesOrderProductLineId?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["salesOrderProductLineId"] | undefined;
          projectId?: everest_timetracking_model_node_TimetrackingProject.TimetrackingProject["id"] | undefined;
          invoiced?: everest_appserver_primitive_TrueFalse | undefined;
        };

        export type Filter =
          | PropertyFilter
          | [Filter, 'and', Filter]
          | [Filter, 'or', Filter];

        /**
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), filters must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to filter by its amount component.
         */
        export type PropertyFilter = {
          property: string;
          comparator: 'eq' | 'ne' | 'lt' | 'le' | 'gt' | 'ge' | 'contains';
          value: unknown;
        };

        /**
         * Specifies sorting for a property in ascending or descending order.
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), sorting must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to sort by its amount component.
         */
        export type OrderbyItem = {
          property: string;
          ordering: 'asc' | 'desc';
        };

        export interface Request {
          setFilter(filter: Filter): this;

          setOrderby(orderby: ReadonlyArray<OrderbyItem>): this;

          setSkip(skip: number): this;

          setTop(top: number): this;

          setSelect(select: ReadonlyArray<keyof Entity>): this;

          setCount(count: boolean): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entities: Entity[];
          count?: number | undefined;
        };
      }

      export namespace Post {
        export type Data = {
          uuid?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["uuid"] | undefined;
          name?: everest_appserver_primitive_Text | undefined;
          description?: everest_appserver_primitive_Text | undefined;
          billable?: everest_appserver_primitive_TrueFalse | undefined;
          hourlyBillRate?: everest_appserver_primitive_Decimal | undefined;
          hourlyBillRateCurrency?: everest_base_enum_BaseCurrency | undefined;
          hourlyCostRate?: everest_appserver_primitive_Decimal | undefined;
          hourlyCostRateCurrency?: everest_base_enum_BaseCurrency | undefined;
          hoursBudget?: everest_appserver_primitive_Decimal | undefined;
          salesOrderProductLineId?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["salesOrderProductLineId"] | undefined;
          projectId?: everest_timetracking_model_node_TimetrackingProject.TimetrackingProject["id"] | undefined;
          invoiced?: everest_appserver_primitive_TrueFalse | undefined;
        };

        export type Entity = {
          id: everest_timetracking_model_node_ProjectPosition.ProjectPosition["id"];
          uuid?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["uuid"] | undefined;
          name: everest_appserver_primitive_Text;
          description?: everest_appserver_primitive_Text | undefined;
          billable?: everest_appserver_primitive_TrueFalse | undefined;
          hourlyBillRate?: everest_appserver_primitive_Decimal | undefined;
          hourlyBillRateCurrency?: everest_base_enum_BaseCurrency | undefined;
          hourlyCostRate?: everest_appserver_primitive_Decimal | undefined;
          hourlyCostRateCurrency?: everest_base_enum_BaseCurrency | undefined;
          hoursBudget?: everest_appserver_primitive_Decimal | undefined;
          salesOrderProductLineId?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["salesOrderProductLineId"] | undefined;
          projectId: everest_timetracking_model_node_TimetrackingProject.TimetrackingProject["id"];
          invoiced?: everest_appserver_primitive_TrueFalse | undefined;
        };

        export interface Request {
          setData(data: Data): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entity: Entity;
        };
      }

      export namespace Patch {
        export type Data = {
          uuid?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["uuid"] | null | undefined;
          name?: everest_appserver_primitive_Text | null | undefined;
          description?: everest_appserver_primitive_Text | null | undefined;
          billable?: everest_appserver_primitive_TrueFalse | null | undefined;
          hourlyBillRate?: everest_appserver_primitive_Decimal | null | undefined;
          hourlyBillRateCurrency?: everest_base_enum_BaseCurrency | null | undefined;
          hourlyCostRate?: everest_appserver_primitive_Decimal | null | undefined;
          hourlyCostRateCurrency?: everest_base_enum_BaseCurrency | null | undefined;
          hoursBudget?: everest_appserver_primitive_Decimal | null | undefined;
          salesOrderProductLineId?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["salesOrderProductLineId"] | null | undefined;
          projectId?: everest_timetracking_model_node_TimetrackingProject.TimetrackingProject["id"] | null | undefined;
          invoiced?: everest_appserver_primitive_TrueFalse | null | undefined;
        };

        export interface Request {
          setId(id: number): this;

          setData(data: Data): this;

          execute(): Promise<Response>;
        }

        export type Response = Record<string, never>;
      }

      export namespace Delete {
        export interface Request {
          setId(id: number): this;

          execute(): Promise<Response>;
        }

        export type Response = Record<string, never>;
      }
    }
    export namespace Projects {
      export namespace Get {
        export type Entity = {
          id?: everest_psa_model_node_PsaProject.PsaProject["id"] | undefined;
          currency?: everest_base_enum_BaseCurrency | undefined;
          salesArrangement?: {
            id: everest_fin_accounting_model_node_SalesArrangement.SalesArrangement["id"];
            salesArrangementNumber: everest_appserver_primitive_Text;
            salesOrders: {
              id: everest_fin_accounting_model_node_SalesOrder.SalesOrder["id"];
              salesOrderNumber: everest_appserver_primitive_Text;
              products: {
                id: everest_fin_accounting_model_node_SalesOrderProduct.SalesOrderProduct["id"];
                productName: everest_appserver_primitive_Text;
                totalPrice: everest_base_composite_CurrencyAmount;
                active: everest_appserver_primitive_TrueFalse;
                productLines: {
                  id: everest_fin_accounting_model_node_SalesOrderProductLine.SalesOrderProductLine["id"];
                  productLineName: everest_appserver_primitive_Text;
                  salesOrderProductLineNumber: everest_appserver_primitive_Text;
                  serviceStartDate: everest_appserver_primitive_PlainDate;
                  serviceEndDate: everest_appserver_primitive_PlainDate;
                  quantity: everest_appserver_primitive_JSON;
                  unitPrice: everest_base_composite_CurrencyAmount;
                  totalPrice: everest_base_composite_CurrencyAmount;
                  priceModel: everest_appserver_primitive_Text;
                  invoiceFrequency: everest_appserver_primitive_Text;
                  lineType: everest_fin_accounting_enum_ProductLineType;
                  active: everest_appserver_primitive_TrueFalse;
                  productLineId: everest_fin_accounting_model_node_ProductLine.ProductLine["id"];
                  unitCost: everest_base_composite_CurrencyAmount;
                }[];
              }[];
            }[];
          } | undefined;
        };

        export type Filter =
          | PropertyFilter
          | [Filter, 'and', Filter]
          | [Filter, 'or', Filter];

        /**
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), filters must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to filter by its amount component.
         */
        export type PropertyFilter = {
          property: string;
          comparator: 'eq' | 'ne' | 'lt' | 'le' | 'gt' | 'ge' | 'contains';
          value: unknown;
        };

        /**
         * Specifies sorting for a property in ascending or descending order.
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), sorting must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to sort by its amount component.
         */
        export type OrderbyItem = {
          property: string;
          ordering: 'asc' | 'desc';
        };

        export interface Request {
          setFilter(filter: Filter): this;

          setOrderby(orderby: ReadonlyArray<OrderbyItem>): this;

          setSkip(skip: number): this;

          setTop(top: number): this;

          setSelect(select: ReadonlyArray<keyof Entity>): this;

          setCount(count: boolean): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entities: Entity[];
          count?: number | undefined;
        };
      }
    }
  }

  export namespace Actions { }

  export namespace Functions {
    export namespace validateSOLinks {
      export namespace Execute {
        export type Input = {
          psaProjectId: everest_psa_model_node_PsaProject.PsaProject["id"];
        };

        export type Output = {
          warnings: {
            message: everest_appserver_primitive_Text;
            code: everest_appserver_primitive_Text;
            severity: everest_appserver_primitive_Text;
            id: everest_appserver_primitive_Number;
            artifactType: everest_appserver_primitive_Text;
          }[];
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }
  }

  export namespace Permission {
    export namespace Check {
      export type Result = {
        dataSets: {
          ProjectRoles: {
            isGetPermitted: boolean;
            isPostPermitted: boolean;
            isPatchPermitted: boolean;
            isDeletePermitted: boolean;
          };
          Projects: {
            isGetPermitted: boolean;
          };
        };
        actions: {
          validateSOLinks: {
            isExecutePermitted: boolean;
          };
        };
      };

      export interface Request {
        execute(): Promise<Response>;
      }

      export type Response = {
        result: Result;
      };
    }
  }

  export namespace Api {
    export type EntitySets = {
      ProjectRoles: {
        get: EntitySets.ProjectRoles.Get.Request;
        post: EntitySets.ProjectRoles.Post.Request;
        patch: EntitySets.ProjectRoles.Patch.Request;
        delete: EntitySets.ProjectRoles.Delete.Request;
      };
      Projects: {
        get: EntitySets.Projects.Get.Request;
      };
    };

    export type Actions = Record<string, never>;

    export type Functions = {
      validateSOLinks: {
        execute: Functions.validateSOLinks.Execute.Request;
      };
    };

    export interface Client {
      createEntitySetGetRequest<T extends 'ProjectRoles' | 'Projects'>(entitySetName: T): Api.EntitySets[T]['get'];

      createEntitySetPostRequest<T extends 'ProjectRoles'>(entitySetName: T): Api.EntitySets[T]['post'];

      createEntitySetPatchRequest<T extends 'ProjectRoles'>(entitySetName: T): Api.EntitySets[T]['patch'];

      createEntitySetDeleteRequest<T extends 'ProjectRoles'>(entitySetName: T): Api.EntitySets[T]['delete'];

      createFunctionExecuteRequest<T extends 'validateSOLinks'>(functionName: T): Api.Functions[T]['execute'];

      createPermissionCheckRequest(): Permission.Check.Request;
    }
  }

  export const client = createOdataClientRuntime<Api.Client>('everest.psa/modules/projects/pages/projectDetail/components/RolesTab/RolesTab');
}

