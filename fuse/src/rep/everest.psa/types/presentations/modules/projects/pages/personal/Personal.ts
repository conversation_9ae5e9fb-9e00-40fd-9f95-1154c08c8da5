/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";

import type { PresentationSession } from '@everestsystems/content-core';

/**
 * Generated backend types for OData presentation Personal.
 */
export namespace Personal {
  export namespace EntitySets { }

  export namespace Actions {
    export namespace getEmployeeInfo {
      export type Input = Record<string, never>;

      export type Output = {
        displayName: everest_appserver_primitive_Text;
        employeeId: everest_appserver_primitive_Number;
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace getAccessibleEmployees {
      export type Input = Record<string, never>;

      export type Output = {
        employees: {
          id: everest_appserver_primitive_Number;
          displayName: everest_appserver_primitive_Text;
          firstName: everest_appserver_primitive_Text;
          lastName: everest_appserver_primitive_Text;
          email: everest_appserver_primitive_Text;
        }[];
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }
  }

  export type Implementation = {
    getEmployeeInfo: Actions.getEmployeeInfo.Implementation;
    getAccessibleEmployees: Actions.getAccessibleEmployees.Implementation;
  };
}

