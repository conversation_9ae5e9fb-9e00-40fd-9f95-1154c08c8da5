/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

import type { ProjectPosition as everest_timetracking_model_node_ProjectPosition } from "@pkg/everest.timetracking/types/ProjectPosition";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { EvstDateTime as everest_appserver_primitive_DateTime } from "@pkg/everest.appserver/types/primitives/DateTime";
import type { TimetrackingProject as everest_timetracking_model_node_TimetrackingProject } from "@pkg/everest.timetracking/types/TimetrackingProject";
import type { Member as everest_timetracking_model_node_Member } from "@pkg/everest.timetracking/types/Member";
import type { Employee as everest_hr_base_model_node_Employee } from "@pkg/everest.hr.base/types/Employee";
import type { EvstDecimal as everest_appserver_primitive_Decimal } from "@pkg/everest.appserver/types/primitives/Decimal";
import type { EvstBaseCurrency as everest_base_enum_BaseCurrency } from "@pkg/everest.base/types/enums/BaseCurrency";

import type {
  Filter,
  PresentationSession,
} from '@everestsystems/content-core';

/**
 * Generated backend types for OData presentation TeamTab.
 */
export namespace TeamTab {
  export namespace EntitySets {
    export namespace ProjectPositions {
      export namespace Query {
        export type Instance = {
          id?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["id"] | undefined;
          uuid?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["uuid"] | undefined;
          version?: everest_appserver_primitive_Number | undefined;
          externalId?: everest_appserver_primitive_Text | undefined;
          active?: everest_appserver_primitive_TrueFalse | undefined;
          createdBy?: everest_appserver_primitive_Text | undefined;
          createdDate?: everest_appserver_primitive_DateTime | undefined;
          lastModifiedBy?: everest_appserver_primitive_Text | undefined;
          lastModifiedDate?: everest_appserver_primitive_DateTime | undefined;
          name?: everest_appserver_primitive_Text | undefined;
          description?: everest_appserver_primitive_Text | undefined;
          productId?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["salesOrderProductLineId"] | undefined;
          projectId?: everest_timetracking_model_node_TimetrackingProject.TimetrackingProject["id"] | undefined;
        };

        export type Order = {
          field: keyof Instance;
          ordering: 'asc' | 'desc';
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            where: Filter<Instance>;
            orderBy: ReadonlyArray<Order>;
            skip: number | undefined;
            take: number | undefined;
            fieldList: ReadonlyArray<keyof Instance>;
            count: boolean;
          };

          export type Response = {
            instances: Instance[];

            /**
             * If count was set to true on the request object, this property
             * must be set, otherwise it must evaluate to undefined.
             */
            count?: number | undefined;
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export type Implementation = {
        query: Query.Implementation;
      };
    }
    export namespace Members {
      export namespace Query {
        export type Instance = {
          id?: everest_timetracking_model_node_Member.Member["id"] | undefined;
          uuid?: everest_timetracking_model_node_Member.Member["uuid"] | undefined;
          version?: everest_appserver_primitive_Number | undefined;
          externalId?: everest_appserver_primitive_Text | undefined;
          active?: everest_appserver_primitive_TrueFalse | undefined;
          createdBy?: everest_appserver_primitive_Text | undefined;
          createdDate?: everest_appserver_primitive_DateTime | undefined;
          lastModifiedBy?: everest_appserver_primitive_Text | undefined;
          lastModifiedDate?: everest_appserver_primitive_DateTime | undefined;
          employeeId?: everest_hr_base_model_node_Employee.Employee["id"] | undefined;
          projectPositionId?: everest_timetracking_model_node_Member.Member["projectPositionId"] | undefined;
          projectId?: everest_timetracking_model_node_TimetrackingProject.TimetrackingProject["id"] | undefined;
        };

        export type Order = {
          field: keyof Instance;
          ordering: 'asc' | 'desc';
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            where: Filter<Instance>;
            orderBy: ReadonlyArray<Order>;
            skip: number | undefined;
            take: number | undefined;
            fieldList: ReadonlyArray<keyof Instance>;
            count: boolean;
          };

          export type Response = {
            instances: Instance[];

            /**
             * If count was set to true on the request object, this property
             * must be set, otherwise it must evaluate to undefined.
             */
            count?: number | undefined;
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export namespace Create {
        export type Input = {
          active?: everest_appserver_primitive_TrueFalse | undefined;
          employeeId: everest_hr_base_model_node_Employee.Employee["id"];
          projectPositionId?: everest_timetracking_model_node_Member.Member["projectPositionId"] | undefined;
          projectId?: everest_timetracking_model_node_TimetrackingProject.TimetrackingProject["id"] | undefined;
        };

        export type Instance = {
          id: everest_timetracking_model_node_Member.Member["id"];
          uuid?: everest_timetracking_model_node_Member.Member["uuid"] | undefined;
          version?: everest_appserver_primitive_Number | undefined;
          externalId?: everest_appserver_primitive_Text | undefined;
          active?: everest_appserver_primitive_TrueFalse | undefined;
          createdBy?: everest_appserver_primitive_Text | undefined;
          createdDate?: everest_appserver_primitive_DateTime | undefined;
          lastModifiedBy?: everest_appserver_primitive_Text | undefined;
          lastModifiedDate?: everest_appserver_primitive_DateTime | undefined;
          employeeId: everest_hr_base_model_node_Employee.Employee["id"];
          projectPositionId?: everest_timetracking_model_node_Member.Member["projectPositionId"] | undefined;
          projectId?: everest_timetracking_model_node_TimetrackingProject.TimetrackingProject["id"] | undefined;
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            inputs: Input[];
          };

          export type Response = {
            instances: Instance[];
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export namespace Delete {
        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            ids: number[];
          };

          export type Response = void;
        }

        export interface Implementation {
          execute(input: Execute.Request): Promise<Execute.Response>;
        }
      }

      export type Implementation = {
        query: Query.Implementation;
        create: Create.Implementation;
        delete: Delete.Implementation;
      };
    }
    export namespace ActiveAndPlannedEmployees {
      export namespace Query {
        export type Instance = {
          id?: everest_appserver_primitive_Number | undefined;
          name?: everest_appserver_primitive_Text | undefined;
          displayName?: everest_appserver_primitive_Text | undefined;
          email?: everest_appserver_primitive_Text | undefined;
        };

        export type Order = {
          field: keyof Instance;
          ordering: 'asc' | 'desc';
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            where: Filter<Instance>;
            orderBy: ReadonlyArray<Order>;
            skip: number | undefined;
            take: number | undefined;
            fieldList: ReadonlyArray<keyof Instance>;
            count: boolean;
          };

          export type Response = {
            instances: Instance[];

            /**
             * If count was set to true on the request object, this property
             * must be set, otherwise it must evaluate to undefined.
             */
            count?: number | undefined;
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export type Implementation = {
        query: Query.Implementation;
      };
    }
  }

  export namespace Actions {
    export namespace unassignTeamMember {
      export type Input = {
        teamMemberId: everest_appserver_primitive_Number;
        psaProjectId: everest_appserver_primitive_Number;
      };

      export type Output = Record<string, never>;

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace getMemberAndRoles {
      export type Input = {
        psaProjectId: everest_appserver_primitive_Number;
      };

      export type Output = {
        teamMembers: {
          id: everest_appserver_primitive_Number;
          name: everest_appserver_primitive_Text;
          email: everest_appserver_primitive_Text;
          photo: everest_appserver_primitive_Text;
          status: everest_appserver_primitive_Text;
          teamMemberId: everest_appserver_primitive_Number;
          memberIsTeamLead: everest_appserver_primitive_TrueFalse;
          roles: {
            memberId: everest_timetracking_model_node_Member.Member["id"];
            id: everest_appserver_primitive_Number;
            title: everest_appserver_primitive_Text;
            isBillable: everest_appserver_primitive_TrueFalse;
            invoiced: everest_appserver_primitive_TrueFalse;
            hourlyBillRate: everest_appserver_primitive_Decimal;
            currency: everest_base_enum_BaseCurrency;
          }[];
        }[];
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }
  }

  export type Implementation = {
    ProjectPositions: EntitySets.ProjectPositions.Implementation;
    Members: EntitySets.Members.Implementation;
    ActiveAndPlannedEmployees: EntitySets.ActiveAndPlannedEmployees.Implementation;
    unassignTeamMember: Actions.unassignTeamMember.Implementation;
    getMemberAndRoles: Actions.getMemberAndRoles.Implementation;
  };
}

