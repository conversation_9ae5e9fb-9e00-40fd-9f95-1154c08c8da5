/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

import type { PsaProject as everest_psa_model_node_PsaProject } from "@pkg/everest.psa/types/PsaProject";
import type { ProjectActivity as everest_timetracking_model_node_ProjectActivity } from "@pkg/everest.timetracking/types/ProjectActivity";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstPlainDate as everest_appserver_primitive_PlainDate } from "@pkg/everest.appserver/types/primitives/PlainDate";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { EvstDecimal as everest_appserver_primitive_Decimal } from "@pkg/everest.appserver/types/primitives/Decimal";
import type { ProjectPosition as everest_timetracking_model_node_ProjectPosition } from "@pkg/everest.timetracking/types/ProjectPosition";
import type { Milestone as everest_psa_model_node_Milestone } from "@pkg/everest.psa/types/Milestone";
import type { EvstCurrencyAmount as everest_base_composite_CurrencyAmount } from "@pkg/everest.base/types/composites/CurrencyAmount";
import type { EvstMilestoneStatus as everest_psa_enum_MilestoneStatus } from "@pkg/everest.psa/types/enums/MilestoneStatus";
import type { FixedFee as everest_psa_model_node_FixedFee } from "@pkg/everest.psa/types/FixedFee";
import type { EvstFixedFeeFrequency as everest_psa_enum_FixedFeeFrequency } from "@pkg/everest.psa/types/enums/FixedFeeFrequency";

import type {
  Filter,
  PresentationSession,
} from '@everestsystems/content-core';

/**
 * Generated backend types for OData presentation TasksTab.
 */
export namespace TasksTab {
  export namespace EntitySets {
    export namespace Tasks {
      export namespace Query {
        export type Instance = {
          psaProjectId?: everest_psa_model_node_PsaProject.PsaProject["id"] | undefined;
          taskId?: everest_timetracking_model_node_ProjectActivity.ProjectActivity["id"] | undefined;
          taskName?: everest_appserver_primitive_Text | undefined;
          startDate?: everest_appserver_primitive_PlainDate | undefined;
          endDate?: everest_appserver_primitive_PlainDate | undefined;
          teamMember?: {
            memberId: everest_appserver_primitive_Number;
            memberDisplayName: everest_appserver_primitive_Text;
            projectPositionId: everest_appserver_primitive_Number;
            projectPositionName: everest_appserver_primitive_Text;
            positionBillable: everest_appserver_primitive_TrueFalse;
          }[] | undefined;
          activityDescription?: everest_appserver_primitive_Text | undefined;
          isBillableActivity?: everest_appserver_primitive_TrueFalse | undefined;
          hoursBudget?: everest_appserver_primitive_Decimal | undefined;
          allocatedHours?: everest_appserver_primitive_Decimal | undefined;
          actualHours?: everest_appserver_primitive_Decimal | undefined;
          invoiced?: everest_appserver_primitive_TrueFalse | undefined;
          milestoneId?: everest_appserver_primitive_Number | undefined;
          fixedFeeId?: everest_appserver_primitive_Number | undefined;
          billingType?: everest_appserver_primitive_Text | undefined;
          dateWarning?: everest_appserver_primitive_TrueFalse | undefined;
        };

        export type Order = {
          field: keyof Instance;
          ordering: 'asc' | 'desc';
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            where: Filter<Instance>;
            orderBy: ReadonlyArray<Order>;
            skip: number | undefined;
            take: number | undefined;
            fieldList: ReadonlyArray<keyof Instance>;
            count: boolean;
          };

          export type Response = {
            instances: Instance[];

            /**
             * If count was set to true on the request object, this property
             * must be set, otherwise it must evaluate to undefined.
             */
            count?: number | undefined;
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export namespace Create {
        export type Input = {
          psaProjectId?: everest_psa_model_node_PsaProject.PsaProject["id"] | undefined;
          taskId?: everest_timetracking_model_node_ProjectActivity.ProjectActivity["id"] | undefined;
          taskName?: everest_appserver_primitive_Text | undefined;
          startDate?: everest_appserver_primitive_PlainDate | undefined;
          endDate?: everest_appserver_primitive_PlainDate | undefined;
          teamMember?: {
            memberId: everest_appserver_primitive_Number;
            memberDisplayName: everest_appserver_primitive_Text;
            projectPositionId: everest_appserver_primitive_Number;
            projectPositionName: everest_appserver_primitive_Text;
            positionBillable: everest_appserver_primitive_TrueFalse;
          }[] | undefined;
          activityDescription?: everest_appserver_primitive_Text | undefined;
          isBillableActivity?: everest_appserver_primitive_TrueFalse | undefined;
          hoursBudget?: everest_appserver_primitive_Decimal | undefined;
          allocatedHours?: everest_appserver_primitive_Decimal | undefined;
          actualHours?: everest_appserver_primitive_Decimal | undefined;
          invoiced?: everest_appserver_primitive_TrueFalse | undefined;
          milestoneId?: everest_appserver_primitive_Number | undefined;
          fixedFeeId?: everest_appserver_primitive_Number | undefined;
          billingType?: everest_appserver_primitive_Text | undefined;
          dateWarning?: everest_appserver_primitive_TrueFalse | undefined;
        };

        export type Instance = {
          psaProjectId?: everest_psa_model_node_PsaProject.PsaProject["id"] | undefined;
          taskId?: everest_timetracking_model_node_ProjectActivity.ProjectActivity["id"] | undefined;
          taskName?: everest_appserver_primitive_Text | undefined;
          startDate?: everest_appserver_primitive_PlainDate | undefined;
          endDate?: everest_appserver_primitive_PlainDate | undefined;
          teamMember?: {
            memberId: everest_appserver_primitive_Number;
            memberDisplayName: everest_appserver_primitive_Text;
            projectPositionId: everest_appserver_primitive_Number;
            projectPositionName: everest_appserver_primitive_Text;
            positionBillable: everest_appserver_primitive_TrueFalse;
          }[] | undefined;
          activityDescription?: everest_appserver_primitive_Text | undefined;
          isBillableActivity?: everest_appserver_primitive_TrueFalse | undefined;
          hoursBudget?: everest_appserver_primitive_Decimal | undefined;
          allocatedHours?: everest_appserver_primitive_Decimal | undefined;
          actualHours?: everest_appserver_primitive_Decimal | undefined;
          invoiced?: everest_appserver_primitive_TrueFalse | undefined;
          milestoneId?: everest_appserver_primitive_Number | undefined;
          fixedFeeId?: everest_appserver_primitive_Number | undefined;
          billingType?: everest_appserver_primitive_Text | undefined;
          dateWarning?: everest_appserver_primitive_TrueFalse | undefined;
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            inputs: Input[];
          };

          export type Response = {
            instances: Instance[];
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export namespace Update {
        export type Data = {
          psaProjectId?: everest_psa_model_node_PsaProject.PsaProject["id"] | null;
          taskId?: everest_timetracking_model_node_ProjectActivity.ProjectActivity["id"] | null;
          taskName?: everest_appserver_primitive_Text | null;
          startDate?: everest_appserver_primitive_PlainDate | null;
          endDate?: everest_appserver_primitive_PlainDate | null;
          teamMember?: {
            memberId: everest_appserver_primitive_Number;
            memberDisplayName: everest_appserver_primitive_Text;
            projectPositionId: everest_appserver_primitive_Number;
            projectPositionName: everest_appserver_primitive_Text;
            positionBillable: everest_appserver_primitive_TrueFalse;
          }[] | null;
          activityDescription?: everest_appserver_primitive_Text | null;
          isBillableActivity?: everest_appserver_primitive_TrueFalse | null;
          hoursBudget?: everest_appserver_primitive_Decimal | null;
          allocatedHours?: everest_appserver_primitive_Decimal | null;
          actualHours?: everest_appserver_primitive_Decimal | null;
          invoiced?: everest_appserver_primitive_TrueFalse | null;
          milestoneId?: everest_appserver_primitive_Number | null;
          fixedFeeId?: everest_appserver_primitive_Number | null;
          billingType?: everest_appserver_primitive_Text | null;
          dateWarning?: everest_appserver_primitive_TrueFalse | null;
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            id: number;
            data: Data;
          };

          export type Response = void;
        }

        export interface Implementation {
          execute(input: Execute.Request): Promise<Execute.Response>;
        }
      }

      export namespace Delete {
        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            ids: number[];
          };

          export type Response = void;
        }

        export interface Implementation {
          execute(input: Execute.Request): Promise<Execute.Response>;
        }
      }

      export type Implementation = {
        query: Query.Implementation;
        create: Create.Implementation;
        update: Update.Implementation;
        delete: Delete.Implementation;
      };
    }
    export namespace Roles {
      export namespace Query {
        export type Instance = {
          psaProjectId?: everest_psa_model_node_PsaProject.PsaProject["id"] | undefined;
          positionId?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["id"] | undefined;
          positionName?: everest_appserver_primitive_Text | undefined;
          positionBillable?: everest_appserver_primitive_TrueFalse | undefined;
        };

        export type Order = {
          field: keyof Instance;
          ordering: 'asc' | 'desc';
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            where: Filter<Instance>;
            orderBy: ReadonlyArray<Order>;
            skip: number | undefined;
            take: number | undefined;
            fieldList: ReadonlyArray<keyof Instance>;
            count: boolean;
          };

          export type Response = {
            instances: Instance[];

            /**
             * If count was set to true on the request object, this property
             * must be set, otherwise it must evaluate to undefined.
             */
            count?: number | undefined;
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export type Implementation = {
        query: Query.Implementation;
      };
    }
    export namespace TeamMembers {
      export namespace Query {
        export type Instance = {
          psaProjectId?: everest_psa_model_node_PsaProject.PsaProject["id"] | undefined;
          memberId?: everest_appserver_primitive_Number | undefined;
          memberName?: everest_appserver_primitive_Text | undefined;
          memberDisplayName?: everest_appserver_primitive_Text | undefined;
          memberEmail?: everest_appserver_primitive_Text | undefined;
          positionId?: everest_appserver_primitive_Number | undefined;
          positionName?: everest_appserver_primitive_Text | undefined;
          positionBillable?: everest_appserver_primitive_TrueFalse | undefined;
        };

        export type Order = {
          field: keyof Instance;
          ordering: 'asc' | 'desc';
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            where: Filter<Instance>;
            orderBy: ReadonlyArray<Order>;
            skip: number | undefined;
            take: number | undefined;
            fieldList: ReadonlyArray<keyof Instance>;
            count: boolean;
          };

          export type Response = {
            instances: Instance[];

            /**
             * If count was set to true on the request object, this property
             * must be set, otherwise it must evaluate to undefined.
             */
            count?: number | undefined;
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export type Implementation = {
        query: Query.Implementation;
      };
    }
    export namespace BillableMilestones {
      export namespace Query {
        export type Instance = {
          psaProjectId?: everest_psa_model_node_PsaProject.PsaProject["id"] | undefined;
          id?: everest_psa_model_node_Milestone.Milestone["id"] | undefined;
          name?: everest_appserver_primitive_Text | undefined;
          amount?: everest_base_composite_CurrencyAmount | undefined;
          dueDate?: everest_appserver_primitive_PlainDate | undefined;
          status?: everest_psa_enum_MilestoneStatus | undefined;
        };

        export type Order = {
          field: keyof Instance;
          ordering: 'asc' | 'desc';
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            where: Filter<Instance>;
            orderBy: ReadonlyArray<Order>;
            skip: number | undefined;
            take: number | undefined;
            fieldList: ReadonlyArray<keyof Instance>;
            count: boolean;
          };

          export type Response = {
            instances: Instance[];

            /**
             * If count was set to true on the request object, this property
             * must be set, otherwise it must evaluate to undefined.
             */
            count?: number | undefined;
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export type Implementation = {
        query: Query.Implementation;
      };
    }
    export namespace BillableFixedFees {
      export namespace Query {
        export type Instance = {
          psaProjectId?: everest_psa_model_node_PsaProject.PsaProject["id"] | undefined;
          id?: everest_psa_model_node_FixedFee.FixedFee["id"] | undefined;
          amount?: everest_base_composite_CurrencyAmount | undefined;
          frequency?: everest_psa_enum_FixedFeeFrequency | undefined;
          startDate?: everest_appserver_primitive_PlainDate | undefined;
          endDate?: everest_appserver_primitive_PlainDate | undefined;
        };

        export type Order = {
          field: keyof Instance;
          ordering: 'asc' | 'desc';
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            where: Filter<Instance>;
            orderBy: ReadonlyArray<Order>;
            skip: number | undefined;
            take: number | undefined;
            fieldList: ReadonlyArray<keyof Instance>;
            count: boolean;
          };

          export type Response = {
            instances: Instance[];

            /**
             * If count was set to true on the request object, this property
             * must be set, otherwise it must evaluate to undefined.
             */
            count?: number | undefined;
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export type Implementation = {
        query: Query.Implementation;
      };
    }
  }

  export namespace Actions { }

  export type Implementation = {
    Tasks: EntitySets.Tasks.Implementation;
    Roles: EntitySets.Roles.Implementation;
    TeamMembers: EntitySets.TeamMembers.Implementation;
    BillableMilestones: EntitySets.BillableMilestones.Implementation;
    BillableFixedFees: EntitySets.BillableFixedFees.Implementation;
  };
}

