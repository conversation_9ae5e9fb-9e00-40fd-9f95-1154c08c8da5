/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

import type { Allocation as everest_timetracking_model_node_Allocation } from "@pkg/everest.timetracking/types/Allocation";
import type { EvstID as everest_appserver_primitive_ID } from "@pkg/everest.appserver/types/primitives/ID";
import type { EvstPlainDate as everest_appserver_primitive_PlainDate } from "@pkg/everest.appserver/types/primitives/PlainDate";
import type { EvstDecimal as everest_appserver_primitive_Decimal } from "@pkg/everest.appserver/types/primitives/Decimal";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstTimeAllocationMethod as everest_timetracking_enum_TimeAllocationMethod } from "@pkg/everest.timetracking/types/enums/TimeAllocationMethod";
import type { EvstDayOfWeek as everest_appserver_enum_DayOfWeek } from "@pkg/everest.appserver/types/enums/DayOfWeek";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { Member as everest_timetracking_model_node_Member } from "@pkg/everest.timetracking/types/Member";
import type { ProjectPosition as everest_timetracking_model_node_ProjectPosition } from "@pkg/everest.timetracking/types/ProjectPosition";
import type { TimetrackingProject as everest_timetracking_model_node_TimetrackingProject } from "@pkg/everest.timetracking/types/TimetrackingProject";
import type { EvstPsaProjectStatus as everest_psa_enum_PsaProjectStatus } from "@pkg/everest.psa/types/enums/PsaProjectStatus";
import type { ProjectActivity as everest_timetracking_model_node_ProjectActivity } from "@pkg/everest.timetracking/types/ProjectActivity";
import type { Employee as everest_hr_base_model_node_Employee } from "@pkg/everest.hr.base/types/Employee";
import type { EvstDate as everest_appserver_primitive_Date } from "@pkg/everest.appserver/types/primitives/Date";

import { createOdataClientRuntime } from '@everestsystems/content-core/lib/runtime/loader';

/**
 * Generated frontend types for OData presentation Allocations.
 */
export namespace AllocationsUI {
  export namespace EntitySets {
    export namespace Allocations {
      export namespace Get {
        export type Entity = {
          id?: everest_timetracking_model_node_Allocation.Allocation["id"] | undefined;
          memberId?: everest_appserver_primitive_ID | undefined;
          projectActivityId?: everest_appserver_primitive_ID | undefined;
          startDate?: everest_appserver_primitive_PlainDate | undefined;
          endDate?: everest_appserver_primitive_PlainDate | undefined;
          totalHours?: everest_appserver_primitive_Decimal | undefined;
          notes?: everest_appserver_primitive_Text | undefined;
          method?: everest_timetracking_enum_TimeAllocationMethod | undefined;
          workingDays?: everest_appserver_enum_DayOfWeek[] | undefined;
          projectId?: everest_appserver_primitive_ID | undefined;
          projectName?: everest_appserver_primitive_Text | undefined;
          memberName?: everest_appserver_primitive_Text | undefined;
          memberEmployeeId?: everest_appserver_primitive_ID | undefined;
          memberPosition?: everest_appserver_primitive_Text | undefined;
          memberActivity?: everest_appserver_primitive_Text | undefined;
          memberActivityDescription?: everest_appserver_primitive_Text | undefined;
          memberActivityStartDate?: everest_appserver_primitive_PlainDate | undefined;
          memberActivityEndDate?: everest_appserver_primitive_PlainDate | undefined;
          memberTotalCapacity?: everest_appserver_primitive_Number | undefined;
          dateWarning?: everest_appserver_primitive_TrueFalse | undefined;
        };

        export type Filter =
          | PropertyFilter
          | [Filter, 'and', Filter]
          | [Filter, 'or', Filter];

        /**
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), filters must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to filter by its amount component.
         */
        export type PropertyFilter = {
          property: string;
          comparator: 'eq' | 'ne' | 'lt' | 'le' | 'gt' | 'ge' | 'contains';
          value: unknown;
        };

        /**
         * Specifies sorting for a property in ascending or descending order.
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), sorting must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to sort by its amount component.
         */
        export type OrderbyItem = {
          property: string;
          ordering: 'asc' | 'desc';
        };

        export interface Request {
          setFilter(filter: Filter): this;

          setOrderby(orderby: ReadonlyArray<OrderbyItem>): this;

          setSkip(skip: number): this;

          setTop(top: number): this;

          setSelect(select: ReadonlyArray<keyof Entity>): this;

          setCount(count: boolean): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entities: Entity[];
          count?: number | undefined;
        };
      }

      export namespace Post {
        export type Data = {
          memberId?: everest_appserver_primitive_ID | undefined;
          projectActivityId?: everest_appserver_primitive_ID | undefined;
          startDate?: everest_appserver_primitive_PlainDate | undefined;
          endDate?: everest_appserver_primitive_PlainDate | undefined;
          totalHours?: everest_appserver_primitive_Decimal | undefined;
          notes?: everest_appserver_primitive_Text | undefined;
          method?: everest_timetracking_enum_TimeAllocationMethod | undefined;
          workingDays?: everest_appserver_enum_DayOfWeek[] | undefined;
          projectId?: everest_appserver_primitive_ID | undefined;
          projectName?: everest_appserver_primitive_Text | undefined;
          memberName?: everest_appserver_primitive_Text | undefined;
          memberEmployeeId?: everest_appserver_primitive_ID | undefined;
          memberPosition?: everest_appserver_primitive_Text | undefined;
          memberActivity?: everest_appserver_primitive_Text | undefined;
          memberActivityDescription?: everest_appserver_primitive_Text | undefined;
          memberActivityStartDate?: everest_appserver_primitive_PlainDate | undefined;
          memberActivityEndDate?: everest_appserver_primitive_PlainDate | undefined;
          memberTotalCapacity?: everest_appserver_primitive_Number | undefined;
          dateWarning?: everest_appserver_primitive_TrueFalse | undefined;
        };

        export type Entity = {
          id: everest_timetracking_model_node_Allocation.Allocation["id"];
          memberId?: everest_appserver_primitive_ID | undefined;
          projectActivityId?: everest_appserver_primitive_ID | undefined;
          startDate?: everest_appserver_primitive_PlainDate | undefined;
          endDate?: everest_appserver_primitive_PlainDate | undefined;
          totalHours?: everest_appserver_primitive_Decimal | undefined;
          notes?: everest_appserver_primitive_Text | undefined;
          method?: everest_timetracking_enum_TimeAllocationMethod | undefined;
          workingDays?: everest_appserver_enum_DayOfWeek[] | undefined;
          projectId?: everest_appserver_primitive_ID | undefined;
          projectName?: everest_appserver_primitive_Text | undefined;
          memberName?: everest_appserver_primitive_Text | undefined;
          memberEmployeeId?: everest_appserver_primitive_ID | undefined;
          memberPosition?: everest_appserver_primitive_Text | undefined;
          memberActivity?: everest_appserver_primitive_Text | undefined;
          memberActivityDescription?: everest_appserver_primitive_Text | undefined;
          memberActivityStartDate?: everest_appserver_primitive_PlainDate | undefined;
          memberActivityEndDate?: everest_appserver_primitive_PlainDate | undefined;
          memberTotalCapacity?: everest_appserver_primitive_Number | undefined;
          dateWarning?: everest_appserver_primitive_TrueFalse | undefined;
        };

        export interface Request {
          setData(data: Data): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entity: Entity;
        };
      }

      export namespace Patch {
        export type Data = {
          memberId?: everest_appserver_primitive_ID | null | undefined;
          projectActivityId?: everest_appserver_primitive_ID | null | undefined;
          startDate?: everest_appserver_primitive_PlainDate | null | undefined;
          endDate?: everest_appserver_primitive_PlainDate | null | undefined;
          totalHours?: everest_appserver_primitive_Decimal | null | undefined;
          notes?: everest_appserver_primitive_Text | null | undefined;
          method?: everest_timetracking_enum_TimeAllocationMethod | null | undefined;
          workingDays?: everest_appserver_enum_DayOfWeek[] | null | undefined;
          projectId?: everest_appserver_primitive_ID | null | undefined;
          projectName?: everest_appserver_primitive_Text | null | undefined;
          memberName?: everest_appserver_primitive_Text | null | undefined;
          memberEmployeeId?: everest_appserver_primitive_ID | null | undefined;
          memberPosition?: everest_appserver_primitive_Text | null | undefined;
          memberActivity?: everest_appserver_primitive_Text | null | undefined;
          memberActivityDescription?: everest_appserver_primitive_Text | null | undefined;
          memberActivityStartDate?: everest_appserver_primitive_PlainDate | null | undefined;
          memberActivityEndDate?: everest_appserver_primitive_PlainDate | null | undefined;
          memberTotalCapacity?: everest_appserver_primitive_Number | null | undefined;
          dateWarning?: everest_appserver_primitive_TrueFalse | null | undefined;
        };

        export interface Request {
          setId(id: number): this;

          setData(data: Data): this;

          execute(): Promise<Response>;
        }

        export type Response = Record<string, never>;
      }

      export namespace Delete {
        export interface Request {
          setId(id: number): this;

          execute(): Promise<Response>;
        }

        export type Response = Record<string, never>;
      }
    }
    export namespace Members {
      export namespace Get {
        export type Entity = {
          id?: everest_timetracking_model_node_Member.Member["id"] | undefined;
          employeeId?: everest_appserver_primitive_ID | undefined;
          projectPositionId?: everest_appserver_primitive_ID | undefined;
          name?: everest_appserver_primitive_Text | undefined;
          totalCapacity?: everest_appserver_primitive_Number | undefined;
          position?: everest_appserver_primitive_Text | undefined;
          projectId?: everest_appserver_primitive_ID | undefined;
          projectName?: everest_appserver_primitive_Text | undefined;
        };

        export type Filter =
          | PropertyFilter
          | [Filter, 'and', Filter]
          | [Filter, 'or', Filter];

        /**
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), filters must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to filter by its amount component.
         */
        export type PropertyFilter = {
          property: string;
          comparator: 'eq' | 'ne' | 'lt' | 'le' | 'gt' | 'ge' | 'contains';
          value: unknown;
        };

        /**
         * Specifies sorting for a property in ascending or descending order.
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), sorting must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to sort by its amount component.
         */
        export type OrderbyItem = {
          property: string;
          ordering: 'asc' | 'desc';
        };

        export interface Request {
          setFilter(filter: Filter): this;

          setOrderby(orderby: ReadonlyArray<OrderbyItem>): this;

          setSkip(skip: number): this;

          setTop(top: number): this;

          setSelect(select: ReadonlyArray<keyof Entity>): this;

          setCount(count: boolean): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entities: Entity[];
          count?: number | undefined;
        };
      }
    }
    export namespace ProjectPositions {
      export namespace Get {
        export type Entity = {
          id?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["id"] | undefined;
          name?: everest_appserver_primitive_Text | undefined;
          description?: everest_appserver_primitive_Text | undefined;
          projectId?: everest_appserver_primitive_ID | undefined;
        };

        export type Filter =
          | PropertyFilter
          | [Filter, 'and', Filter]
          | [Filter, 'or', Filter];

        /**
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), filters must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to filter by its amount component.
         */
        export type PropertyFilter = {
          property: string;
          comparator: 'eq' | 'ne' | 'lt' | 'le' | 'gt' | 'ge' | 'contains';
          value: unknown;
        };

        /**
         * Specifies sorting for a property in ascending or descending order.
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), sorting must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to sort by its amount component.
         */
        export type OrderbyItem = {
          property: string;
          ordering: 'asc' | 'desc';
        };

        export interface Request {
          setFilter(filter: Filter): this;

          setOrderby(orderby: ReadonlyArray<OrderbyItem>): this;

          setSkip(skip: number): this;

          setTop(top: number): this;

          setSelect(select: ReadonlyArray<keyof Entity>): this;

          setCount(count: boolean): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entities: Entity[];
          count?: number | undefined;
        };
      }
    }
    export namespace Projects {
      export namespace Get {
        export type Entity = {
          id?: everest_timetracking_model_node_TimetrackingProject.TimetrackingProject["id"] | undefined;
          name?: everest_appserver_primitive_Text | undefined;
          psaProjectId?: everest_appserver_primitive_ID | undefined;
          status?: everest_psa_enum_PsaProjectStatus | undefined;
        };

        export type Filter =
          | PropertyFilter
          | [Filter, 'and', Filter]
          | [Filter, 'or', Filter];

        /**
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), filters must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to filter by its amount component.
         */
        export type PropertyFilter = {
          property: string;
          comparator: 'eq' | 'ne' | 'lt' | 'le' | 'gt' | 'ge' | 'contains';
          value: unknown;
        };

        /**
         * Specifies sorting for a property in ascending or descending order.
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), sorting must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to sort by its amount component.
         */
        export type OrderbyItem = {
          property: string;
          ordering: 'asc' | 'desc';
        };

        export interface Request {
          setFilter(filter: Filter): this;

          setOrderby(orderby: ReadonlyArray<OrderbyItem>): this;

          setSkip(skip: number): this;

          setTop(top: number): this;

          setSelect(select: ReadonlyArray<keyof Entity>): this;

          setCount(count: boolean): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entities: Entity[];
          count?: number | undefined;
        };
      }
    }
    export namespace ProjectActivities {
      export namespace Get {
        export type Entity = {
          id?: everest_timetracking_model_node_ProjectActivity.ProjectActivity["id"] | undefined;
          name?: everest_appserver_primitive_Text | undefined;
          description?: everest_appserver_primitive_Text | undefined;
          projectId?: everest_appserver_primitive_ID | undefined;
          memberId?: everest_appserver_primitive_ID | undefined;
          projectPositionId?: everest_appserver_primitive_ID | undefined;
          startDate?: everest_appserver_primitive_PlainDate | undefined;
          endDate?: everest_appserver_primitive_PlainDate | undefined;
        };

        export type Filter =
          | PropertyFilter
          | [Filter, 'and', Filter]
          | [Filter, 'or', Filter];

        /**
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), filters must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to filter by its amount component.
         */
        export type PropertyFilter = {
          property: string;
          comparator: 'eq' | 'ne' | 'lt' | 'le' | 'gt' | 'ge' | 'contains';
          value: unknown;
        };

        /**
         * Specifies sorting for a property in ascending or descending order.
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), sorting must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to sort by its amount component.
         */
        export type OrderbyItem = {
          property: string;
          ordering: 'asc' | 'desc';
        };

        export interface Request {
          setFilter(filter: Filter): this;

          setOrderby(orderby: ReadonlyArray<OrderbyItem>): this;

          setSkip(skip: number): this;

          setTop(top: number): this;

          setSelect(select: ReadonlyArray<keyof Entity>): this;

          setCount(count: boolean): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entities: Entity[];
          count?: number | undefined;
        };
      }
    }
  }

  export namespace Actions { }

  export namespace Functions {
    export namespace validateBudget {
      export namespace Execute {
        export type Input = {
          projectId: everest_timetracking_model_node_TimetrackingProject.TimetrackingProject["id"];
        };

        export type Output = {
          warnings: {
            message: everest_appserver_primitive_Text;
            projectName: everest_appserver_primitive_Text;
          }[];
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace GetCurrentEmployeeId {
      export namespace Execute {
        export type Input = Record<string, never>;

        export type Output = {
          employeeId: everest_appserver_primitive_ID;
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace GetTimeOff {
      export namespace Execute {
        export type Input = {
          employeeIds: everest_hr_base_model_node_Employee.Employee["id"][];
          startDate: everest_appserver_primitive_Date;
          endDate: everest_appserver_primitive_Date;
        };

        export type Output = {
          result: {
            employeeId: everest_appserver_primitive_ID;
            holidays: {
              holidayDate: everest_appserver_primitive_PlainDate;
              holidayName: everest_appserver_primitive_Text;
              halfDay: everest_appserver_primitive_TrueFalse;
            }[];
            absences: {
              startDate: everest_appserver_primitive_PlainDate;
              endDate: everest_appserver_primitive_PlainDate;
              mandatoryLeaveId: everest_appserver_primitive_Number;
              absenceType: everest_appserver_primitive_Text;
              days: everest_appserver_primitive_Decimal;
            }[];
          }[];
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace validateAllocationDate {
      export namespace Execute {
        export type Input = {
          allocationStartDate: everest_appserver_primitive_PlainDate;
          allocationEndDate: everest_appserver_primitive_PlainDate;
          activityStartDate: everest_appserver_primitive_PlainDate;
          activityEndDate: everest_appserver_primitive_PlainDate;
        };

        export type Output = {
          isOutsideDateRange: everest_appserver_primitive_TrueFalse;
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }
  }

  export namespace Permission {
    export namespace Check {
      export type Result = {
        dataSets: {
          Allocations: {
            isGetPermitted: boolean;
            isPostPermitted: boolean;
            isPatchPermitted: boolean;
            isDeletePermitted: boolean;
          };
          Members: {
            isGetPermitted: boolean;
          };
          ProjectActivities: {
            isGetPermitted: boolean;
          };
          ProjectPositions: {
            isGetPermitted: boolean;
          };
          Projects: {
            isGetPermitted: boolean;
          };
        };
        actions: {
          GetCurrentEmployeeId: {
            isExecutePermitted: boolean;
          };
          GetTimeOff: {
            isExecutePermitted: boolean;
          };
          validateAllocationDate: {
            isExecutePermitted: boolean;
          };
          validateBudget: {
            isExecutePermitted: boolean;
          };
        };
      };

      export interface Request {
        execute(): Promise<Response>;
      }

      export type Response = {
        result: Result;
      };
    }
  }

  export namespace Api {
    export type EntitySets = {
      Allocations: {
        get: EntitySets.Allocations.Get.Request;
        post: EntitySets.Allocations.Post.Request;
        patch: EntitySets.Allocations.Patch.Request;
        delete: EntitySets.Allocations.Delete.Request;
      };
      Members: {
        get: EntitySets.Members.Get.Request;
      };
      ProjectPositions: {
        get: EntitySets.ProjectPositions.Get.Request;
      };
      Projects: {
        get: EntitySets.Projects.Get.Request;
      };
      ProjectActivities: {
        get: EntitySets.ProjectActivities.Get.Request;
      };
    };

    export type Actions = Record<string, never>;

    export type Functions = {
      validateBudget: {
        execute: Functions.validateBudget.Execute.Request;
      };
      GetCurrentEmployeeId: {
        execute: Functions.GetCurrentEmployeeId.Execute.Request;
      };
      GetTimeOff: {
        execute: Functions.GetTimeOff.Execute.Request;
      };
      validateAllocationDate: {
        execute: Functions.validateAllocationDate.Execute.Request;
      };
    };

    export interface Client {
      createEntitySetGetRequest<T extends 'Allocations' | 'Members' | 'ProjectPositions' | 'Projects' | 'ProjectActivities'>(entitySetName: T): Api.EntitySets[T]['get'];

      createEntitySetPostRequest<T extends 'Allocations'>(entitySetName: T): Api.EntitySets[T]['post'];

      createEntitySetPatchRequest<T extends 'Allocations'>(entitySetName: T): Api.EntitySets[T]['patch'];

      createEntitySetDeleteRequest<T extends 'Allocations'>(entitySetName: T): Api.EntitySets[T]['delete'];

      createFunctionExecuteRequest<T extends 'validateBudget' | 'GetCurrentEmployeeId' | 'GetTimeOff' | 'validateAllocationDate'>(functionName: T): Api.Functions[T]['execute'];

      createPermissionCheckRequest(): Permission.Check.Request;
    }
  }

  export const client = createOdataClientRuntime<Api.Client>('everest.psa/modules/allocations/pages/allocations/Allocations');
}

