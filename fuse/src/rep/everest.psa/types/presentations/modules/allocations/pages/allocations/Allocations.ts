/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

import type { Allocation as everest_timetracking_model_node_Allocation } from "@pkg/everest.timetracking/types/Allocation";
import type { EvstID as everest_appserver_primitive_ID } from "@pkg/everest.appserver/types/primitives/ID";
import type { EvstPlainDate as everest_appserver_primitive_PlainDate } from "@pkg/everest.appserver/types/primitives/PlainDate";
import type { EvstDecimal as everest_appserver_primitive_Decimal } from "@pkg/everest.appserver/types/primitives/Decimal";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstTimeAllocationMethod as everest_timetracking_enum_TimeAllocationMethod } from "@pkg/everest.timetracking/types/enums/TimeAllocationMethod";
import type { EvstDayOfWeek as everest_appserver_enum_DayOfWeek } from "@pkg/everest.appserver/types/enums/DayOfWeek";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { Member as everest_timetracking_model_node_Member } from "@pkg/everest.timetracking/types/Member";
import type { ProjectPosition as everest_timetracking_model_node_ProjectPosition } from "@pkg/everest.timetracking/types/ProjectPosition";
import type { TimetrackingProject as everest_timetracking_model_node_TimetrackingProject } from "@pkg/everest.timetracking/types/TimetrackingProject";
import type { EvstPsaProjectStatus as everest_psa_enum_PsaProjectStatus } from "@pkg/everest.psa/types/enums/PsaProjectStatus";
import type { ProjectActivity as everest_timetracking_model_node_ProjectActivity } from "@pkg/everest.timetracking/types/ProjectActivity";
import type { Employee as everest_hr_base_model_node_Employee } from "@pkg/everest.hr.base/types/Employee";
import type { EvstDate as everest_appserver_primitive_Date } from "@pkg/everest.appserver/types/primitives/Date";

import type {
  Filter,
  PresentationSession,
} from '@everestsystems/content-core';

/**
 * Generated backend types for OData presentation Allocations.
 */
export namespace Allocations {
  export namespace EntitySets {
    export namespace Allocations {
      export namespace Query {
        export type Instance = {
          id?: everest_timetracking_model_node_Allocation.Allocation["id"] | undefined;
          memberId?: everest_appserver_primitive_ID | undefined;
          projectActivityId?: everest_appserver_primitive_ID | undefined;
          startDate?: everest_appserver_primitive_PlainDate | undefined;
          endDate?: everest_appserver_primitive_PlainDate | undefined;
          totalHours?: everest_appserver_primitive_Decimal | undefined;
          notes?: everest_appserver_primitive_Text | undefined;
          method?: everest_timetracking_enum_TimeAllocationMethod | undefined;
          workingDays?: everest_appserver_enum_DayOfWeek[] | undefined;
          projectId?: everest_appserver_primitive_ID | undefined;
          projectName?: everest_appserver_primitive_Text | undefined;
          memberName?: everest_appserver_primitive_Text | undefined;
          memberEmployeeId?: everest_appserver_primitive_ID | undefined;
          memberPosition?: everest_appserver_primitive_Text | undefined;
          memberActivity?: everest_appserver_primitive_Text | undefined;
          memberActivityDescription?: everest_appserver_primitive_Text | undefined;
          memberActivityStartDate?: everest_appserver_primitive_PlainDate | undefined;
          memberActivityEndDate?: everest_appserver_primitive_PlainDate | undefined;
          memberTotalCapacity?: everest_appserver_primitive_Number | undefined;
          dateWarning?: everest_appserver_primitive_TrueFalse | undefined;
        };

        export type Order = {
          field: keyof Instance;
          ordering: 'asc' | 'desc';
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            where: Filter<Instance>;
            orderBy: ReadonlyArray<Order>;
            skip: number | undefined;
            take: number | undefined;
            fieldList: ReadonlyArray<keyof Instance>;
            count: boolean;
          };

          export type Response = {
            instances: Instance[];

            /**
             * If count was set to true on the request object, this property
             * must be set, otherwise it must evaluate to undefined.
             */
            count?: number | undefined;
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export namespace Create {
        export type Input = {
          memberId?: everest_appserver_primitive_ID | undefined;
          projectActivityId?: everest_appserver_primitive_ID | undefined;
          startDate?: everest_appserver_primitive_PlainDate | undefined;
          endDate?: everest_appserver_primitive_PlainDate | undefined;
          totalHours?: everest_appserver_primitive_Decimal | undefined;
          notes?: everest_appserver_primitive_Text | undefined;
          method?: everest_timetracking_enum_TimeAllocationMethod | undefined;
          workingDays?: everest_appserver_enum_DayOfWeek[] | undefined;
          projectId?: everest_appserver_primitive_ID | undefined;
          projectName?: everest_appserver_primitive_Text | undefined;
          memberName?: everest_appserver_primitive_Text | undefined;
          memberEmployeeId?: everest_appserver_primitive_ID | undefined;
          memberPosition?: everest_appserver_primitive_Text | undefined;
          memberActivity?: everest_appserver_primitive_Text | undefined;
          memberActivityDescription?: everest_appserver_primitive_Text | undefined;
          memberActivityStartDate?: everest_appserver_primitive_PlainDate | undefined;
          memberActivityEndDate?: everest_appserver_primitive_PlainDate | undefined;
          memberTotalCapacity?: everest_appserver_primitive_Number | undefined;
          dateWarning?: everest_appserver_primitive_TrueFalse | undefined;
        };

        export type Instance = {
          id: everest_timetracking_model_node_Allocation.Allocation["id"];
          memberId?: everest_appserver_primitive_ID | undefined;
          projectActivityId?: everest_appserver_primitive_ID | undefined;
          startDate?: everest_appserver_primitive_PlainDate | undefined;
          endDate?: everest_appserver_primitive_PlainDate | undefined;
          totalHours?: everest_appserver_primitive_Decimal | undefined;
          notes?: everest_appserver_primitive_Text | undefined;
          method?: everest_timetracking_enum_TimeAllocationMethod | undefined;
          workingDays?: everest_appserver_enum_DayOfWeek[] | undefined;
          projectId?: everest_appserver_primitive_ID | undefined;
          projectName?: everest_appserver_primitive_Text | undefined;
          memberName?: everest_appserver_primitive_Text | undefined;
          memberEmployeeId?: everest_appserver_primitive_ID | undefined;
          memberPosition?: everest_appserver_primitive_Text | undefined;
          memberActivity?: everest_appserver_primitive_Text | undefined;
          memberActivityDescription?: everest_appserver_primitive_Text | undefined;
          memberActivityStartDate?: everest_appserver_primitive_PlainDate | undefined;
          memberActivityEndDate?: everest_appserver_primitive_PlainDate | undefined;
          memberTotalCapacity?: everest_appserver_primitive_Number | undefined;
          dateWarning?: everest_appserver_primitive_TrueFalse | undefined;
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            inputs: Input[];
          };

          export type Response = {
            instances: Instance[];
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export namespace Update {
        export type Data = {
          memberId?: everest_appserver_primitive_ID | null;
          projectActivityId?: everest_appserver_primitive_ID | null;
          startDate?: everest_appserver_primitive_PlainDate | null;
          endDate?: everest_appserver_primitive_PlainDate | null;
          totalHours?: everest_appserver_primitive_Decimal | null;
          notes?: everest_appserver_primitive_Text | null;
          method?: everest_timetracking_enum_TimeAllocationMethod | null;
          workingDays?: everest_appserver_enum_DayOfWeek[] | null;
          projectId?: everest_appserver_primitive_ID | null;
          projectName?: everest_appserver_primitive_Text | null;
          memberName?: everest_appserver_primitive_Text | null;
          memberEmployeeId?: everest_appserver_primitive_ID | null;
          memberPosition?: everest_appserver_primitive_Text | null;
          memberActivity?: everest_appserver_primitive_Text | null;
          memberActivityDescription?: everest_appserver_primitive_Text | null;
          memberActivityStartDate?: everest_appserver_primitive_PlainDate | null;
          memberActivityEndDate?: everest_appserver_primitive_PlainDate | null;
          memberTotalCapacity?: everest_appserver_primitive_Number | null;
          dateWarning?: everest_appserver_primitive_TrueFalse | null;
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            id: number;
            data: Data;
          };

          export type Response = void;
        }

        export interface Implementation {
          execute(input: Execute.Request): Promise<Execute.Response>;
        }
      }

      export namespace Delete {
        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            ids: number[];
          };

          export type Response = void;
        }

        export interface Implementation {
          execute(input: Execute.Request): Promise<Execute.Response>;
        }
      }

      export type Implementation = {
        query: Query.Implementation;
        create: Create.Implementation;
        update: Update.Implementation;
        delete: Delete.Implementation;
      };
    }
    export namespace Members {
      export namespace Query {
        export type Instance = {
          id?: everest_timetracking_model_node_Member.Member["id"] | undefined;
          employeeId?: everest_appserver_primitive_ID | undefined;
          projectPositionId?: everest_appserver_primitive_ID | undefined;
          name?: everest_appserver_primitive_Text | undefined;
          totalCapacity?: everest_appserver_primitive_Number | undefined;
          position?: everest_appserver_primitive_Text | undefined;
          projectId?: everest_appserver_primitive_ID | undefined;
          projectName?: everest_appserver_primitive_Text | undefined;
        };

        export type Order = {
          field: keyof Instance;
          ordering: 'asc' | 'desc';
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            where: Filter<Instance>;
            orderBy: ReadonlyArray<Order>;
            skip: number | undefined;
            take: number | undefined;
            fieldList: ReadonlyArray<keyof Instance>;
            count: boolean;
          };

          export type Response = {
            instances: Instance[];

            /**
             * If count was set to true on the request object, this property
             * must be set, otherwise it must evaluate to undefined.
             */
            count?: number | undefined;
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export type Implementation = {
        query: Query.Implementation;
      };
    }
    export namespace ProjectPositions {
      export namespace Query {
        export type Instance = {
          id?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["id"] | undefined;
          name?: everest_appserver_primitive_Text | undefined;
          description?: everest_appserver_primitive_Text | undefined;
          projectId?: everest_appserver_primitive_ID | undefined;
        };

        export type Order = {
          field: keyof Instance;
          ordering: 'asc' | 'desc';
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            where: Filter<Instance>;
            orderBy: ReadonlyArray<Order>;
            skip: number | undefined;
            take: number | undefined;
            fieldList: ReadonlyArray<keyof Instance>;
            count: boolean;
          };

          export type Response = {
            instances: Instance[];

            /**
             * If count was set to true on the request object, this property
             * must be set, otherwise it must evaluate to undefined.
             */
            count?: number | undefined;
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export type Implementation = {
        query: Query.Implementation;
      };
    }
    export namespace Projects {
      export namespace Query {
        export type Instance = {
          id?: everest_timetracking_model_node_TimetrackingProject.TimetrackingProject["id"] | undefined;
          name?: everest_appserver_primitive_Text | undefined;
          psaProjectId?: everest_appserver_primitive_ID | undefined;
          status?: everest_psa_enum_PsaProjectStatus | undefined;
        };

        export type Order = {
          field: keyof Instance;
          ordering: 'asc' | 'desc';
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            where: Filter<Instance>;
            orderBy: ReadonlyArray<Order>;
            skip: number | undefined;
            take: number | undefined;
            fieldList: ReadonlyArray<keyof Instance>;
            count: boolean;
          };

          export type Response = {
            instances: Instance[];

            /**
             * If count was set to true on the request object, this property
             * must be set, otherwise it must evaluate to undefined.
             */
            count?: number | undefined;
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export type Implementation = {
        query: Query.Implementation;
      };
    }
    export namespace ProjectActivities {
      export namespace Query {
        export type Instance = {
          id?: everest_timetracking_model_node_ProjectActivity.ProjectActivity["id"] | undefined;
          name?: everest_appserver_primitive_Text | undefined;
          description?: everest_appserver_primitive_Text | undefined;
          projectId?: everest_appserver_primitive_ID | undefined;
          memberId?: everest_appserver_primitive_ID | undefined;
          projectPositionId?: everest_appserver_primitive_ID | undefined;
          startDate?: everest_appserver_primitive_PlainDate | undefined;
          endDate?: everest_appserver_primitive_PlainDate | undefined;
        };

        export type Order = {
          field: keyof Instance;
          ordering: 'asc' | 'desc';
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            where: Filter<Instance>;
            orderBy: ReadonlyArray<Order>;
            skip: number | undefined;
            take: number | undefined;
            fieldList: ReadonlyArray<keyof Instance>;
            count: boolean;
          };

          export type Response = {
            instances: Instance[];

            /**
             * If count was set to true on the request object, this property
             * must be set, otherwise it must evaluate to undefined.
             */
            count?: number | undefined;
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export type Implementation = {
        query: Query.Implementation;
      };
    }
  }

  export namespace Actions {
    export namespace validateBudget {
      export type Input = {
        projectId: everest_timetracking_model_node_TimetrackingProject.TimetrackingProject["id"];
      };

      export type Output = {
        warnings: {
          message: everest_appserver_primitive_Text;
          projectName: everest_appserver_primitive_Text;
        }[];
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace GetCurrentEmployeeId {
      export type Input = Record<string, never>;

      export type Output = {
        employeeId: everest_appserver_primitive_ID;
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace GetTimeOff {
      export type Input = {
        employeeIds: everest_hr_base_model_node_Employee.Employee["id"][];
        startDate: everest_appserver_primitive_Date;
        endDate: everest_appserver_primitive_Date;
      };

      export type Output = {
        result: {
          employeeId: everest_appserver_primitive_ID;
          holidays: {
            holidayDate: everest_appserver_primitive_PlainDate;
            holidayName: everest_appserver_primitive_Text;
            halfDay: everest_appserver_primitive_TrueFalse;
          }[];
          absences: {
            startDate: everest_appserver_primitive_PlainDate;
            endDate: everest_appserver_primitive_PlainDate;
            mandatoryLeaveId: everest_appserver_primitive_Number;
            absenceType: everest_appserver_primitive_Text;
            days: everest_appserver_primitive_Decimal;
          }[];
        }[];
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace validateAllocationDate {
      export type Input = {
        allocationStartDate: everest_appserver_primitive_PlainDate;
        allocationEndDate: everest_appserver_primitive_PlainDate;
        activityStartDate: everest_appserver_primitive_PlainDate;
        activityEndDate: everest_appserver_primitive_PlainDate;
      };

      export type Output = {
        isOutsideDateRange: everest_appserver_primitive_TrueFalse;
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }
  }

  export type Implementation = {
    Allocations: EntitySets.Allocations.Implementation;
    Members: EntitySets.Members.Implementation;
    ProjectPositions: EntitySets.ProjectPositions.Implementation;
    Projects: EntitySets.Projects.Implementation;
    ProjectActivities: EntitySets.ProjectActivities.Implementation;
    validateBudget: Actions.validateBudget.Implementation;
    GetCurrentEmployeeId: Actions.GetCurrentEmployeeId.Implementation;
    GetTimeOff: Actions.GetTimeOff.Implementation;
    validateAllocationDate: Actions.validateAllocationDate.Implementation;
  };
}

