/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

import type { InvoicedPeriod as everest_psa_model_node_InvoicedPeriod } from "@pkg/everest.psa/types/InvoicedPeriod";
import type { TimetrackingProject as everest_timetracking_model_node_TimetrackingProject } from "@pkg/everest.timetracking/types/TimetrackingProject";
import type { Project as everest_hr_base_model_node_Project } from "@pkg/everest.hr.base/types/Project";
import type { EvstPlainDate as everest_appserver_primitive_PlainDate } from "@pkg/everest.appserver/types/primitives/PlainDate";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { ProjectPosition as everest_timetracking_model_node_ProjectPosition } from "@pkg/everest.timetracking/types/ProjectPosition";
import type { ProjectActivity as everest_timetracking_model_node_ProjectActivity } from "@pkg/everest.timetracking/types/ProjectActivity";
import type { EvstID as everest_appserver_primitive_ID } from "@pkg/everest.appserver/types/primitives/ID";
import type { TimeEntry as everest_timetracking_model_node_TimeEntry } from "@pkg/everest.timetracking/types/TimeEntry";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { EvstDecimal as everest_appserver_primitive_Decimal } from "@pkg/everest.appserver/types/primitives/Decimal";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { EvstDate as everest_appserver_primitive_Date } from "@pkg/everest.appserver/types/primitives/Date";
import type { Allocation as everest_timetracking_model_node_Allocation } from "@pkg/everest.timetracking/types/Allocation";
import type { Member as everest_timetracking_model_node_Member } from "@pkg/everest.timetracking/types/Member";
import type { EvstTimeAllocationMethod as everest_timetracking_enum_TimeAllocationMethod } from "@pkg/everest.timetracking/types/enums/TimeAllocationMethod";
import type { EvstDayOfWeek as everest_appserver_enum_DayOfWeek } from "@pkg/everest.appserver/types/enums/DayOfWeek";

import type {
  Filter,
  PresentationSession,
} from '@everestsystems/content-core';

/**
 * Generated backend types for OData presentation TimesheetService.
 */
export namespace TimesheetService {
  export namespace EntitySets {
    export namespace InvoicedPeriods {
      export namespace Query {
        export type Instance = {
          id?: everest_psa_model_node_InvoicedPeriod.InvoicedPeriod["id"] | undefined;
          startDate?: everest_psa_model_node_InvoicedPeriod.InvoicedPeriod["startDate"] | undefined;
          endDate?: everest_psa_model_node_InvoicedPeriod.InvoicedPeriod["endDate"] | undefined;
          psaProjectId?: everest_psa_model_node_InvoicedPeriod.InvoicedPeriod["psaProjectId"] | undefined;
        };

        export type Order = {
          field: keyof Instance;
          ordering: 'asc' | 'desc';
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            where: Filter<Instance>;
            orderBy: ReadonlyArray<Order>;
            skip: number | undefined;
            take: number | undefined;
            fieldList: ReadonlyArray<keyof Instance>;
            count: boolean;
          };

          export type Response = {
            instances: Instance[];

            /**
             * If count was set to true on the request object, this property
             * must be set, otherwise it must evaluate to undefined.
             */
            count?: number | undefined;
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export type Implementation = {
        query: Query.Implementation;
      };
    }
    export namespace Projects {
      export namespace Query {
        export type Instance = {
          id?: everest_timetracking_model_node_TimetrackingProject.TimetrackingProject["id"] | undefined;
          projectName?: everest_hr_base_model_node_Project.Project["projectName"] | undefined;
          startDate?: everest_appserver_primitive_PlainDate | undefined;
          endDate?: everest_appserver_primitive_PlainDate | undefined;
          status?: everest_appserver_primitive_Text | undefined;
        };

        export type Order = {
          field: keyof Instance;
          ordering: 'asc' | 'desc';
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            where: Filter<Instance>;
            orderBy: ReadonlyArray<Order>;
            skip: number | undefined;
            take: number | undefined;
            fieldList: ReadonlyArray<keyof Instance>;
            count: boolean;
          };

          export type Response = {
            instances: Instance[];

            /**
             * If count was set to true on the request object, this property
             * must be set, otherwise it must evaluate to undefined.
             */
            count?: number | undefined;
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export type Implementation = {
        query: Query.Implementation;
      };
    }
    export namespace ProjectPositions {
      export namespace Query {
        export type Instance = {
          id?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["id"] | undefined;
          name?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["name"] | undefined;
          description?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["description"] | undefined;
          projectId?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["projectId"] | undefined;
        };

        export type Order = {
          field: keyof Instance;
          ordering: 'asc' | 'desc';
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            where: Filter<Instance>;
            orderBy: ReadonlyArray<Order>;
            skip: number | undefined;
            take: number | undefined;
            fieldList: ReadonlyArray<keyof Instance>;
            count: boolean;
          };

          export type Response = {
            instances: Instance[];

            /**
             * If count was set to true on the request object, this property
             * must be set, otherwise it must evaluate to undefined.
             */
            count?: number | undefined;
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export type Implementation = {
        query: Query.Implementation;
      };
    }
    export namespace ProjectActivities {
      export namespace Query {
        export type Instance = {
          id?: everest_timetracking_model_node_ProjectActivity.ProjectActivity["id"] | undefined;
          name?: everest_timetracking_model_node_ProjectActivity.ProjectActivity["name"] | undefined;
          description?: everest_timetracking_model_node_ProjectActivity.ProjectActivity["description"] | undefined;
          projectPositionId?: everest_appserver_primitive_ID | undefined;
          memberId?: everest_appserver_primitive_ID | undefined;
          billable?: everest_timetracking_model_node_ProjectActivity.ProjectActivity["billable"] | undefined;
          projectId?: everest_timetracking_model_node_TimetrackingProject.TimetrackingProject["id"] | undefined;
        };

        export type Order = {
          field: keyof Instance;
          ordering: 'asc' | 'desc';
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            where: Filter<Instance>;
            orderBy: ReadonlyArray<Order>;
            skip: number | undefined;
            take: number | undefined;
            fieldList: ReadonlyArray<keyof Instance>;
            count: boolean;
          };

          export type Response = {
            instances: Instance[];

            /**
             * If count was set to true on the request object, this property
             * must be set, otherwise it must evaluate to undefined.
             */
            count?: number | undefined;
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export type Implementation = {
        query: Query.Implementation;
      };
    }
    export namespace TimeEntries {
      export namespace Query {
        export type Instance = {
          id?: everest_timetracking_model_node_TimeEntry.TimeEntry["id"] | undefined;
          date?: everest_timetracking_model_node_TimeEntry.TimeEntry["date"] | undefined;
          hours?: everest_timetracking_model_node_TimeEntry.TimeEntry["hours"] | undefined;
          memberId?: everest_timetracking_model_node_TimeEntry.TimeEntry["memberId"] | undefined;
          notes?: everest_timetracking_model_node_TimeEntry.TimeEntry["notes"] | undefined;
          projectActivityId?: everest_timetracking_model_node_TimeEntry.TimeEntry["projectActivityId"] | undefined;
          submissionStatus?: everest_timetracking_model_node_TimeEntry.TimeEntry["submissionStatus"] | undefined;
        };

        export type Order = {
          field: keyof Instance;
          ordering: 'asc' | 'desc';
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            where: Filter<Instance>;
            orderBy: ReadonlyArray<Order>;
            skip: number | undefined;
            take: number | undefined;
            fieldList: ReadonlyArray<keyof Instance>;
            count: boolean;
          };

          export type Response = {
            instances: Instance[];

            /**
             * If count was set to true on the request object, this property
             * must be set, otherwise it must evaluate to undefined.
             */
            count?: number | undefined;
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export type Implementation = {
        query: Query.Implementation;
      };
    }
  }

  export namespace Actions {
    export namespace GetEmployeeWeeklyCapacity {
      export type Input = Record<string, never>;

      export type Output = {
        weeklyCapacity: everest_appserver_primitive_Number;
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace GetTimeKpis {
      export type Input = {
        startDate: everest_appserver_primitive_PlainDate;
        endDate: everest_appserver_primitive_PlainDate;
      };

      export type Output = {
        totalHours: everest_appserver_primitive_Decimal;
        billableHours: everest_appserver_primitive_Decimal;
        utilization: everest_appserver_primitive_Decimal;
        billableUtilization: everest_appserver_primitive_Decimal;
        totalUtilization: everest_appserver_primitive_Decimal;
        billableUtilizationWithoutAbsences: everest_appserver_primitive_Decimal;
        totalUtilizationWithoutAbsences: everest_appserver_primitive_Decimal;
        totalHoursIncludingAbsences: everest_appserver_primitive_Decimal;
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace SubmitTimeEntries {
      export type Input = {
        timeEntryIds: everest_appserver_primitive_ID[];
      };

      export type Output = Record<string, never>;

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace UpsertTimeEntries {
      export type Input = {
        startDate: everest_appserver_primitive_PlainDate;
        endDate: everest_appserver_primitive_PlainDate;
        timeEntries: {
          id: everest_timetracking_model_node_TimeEntry.TimeEntry["id"];
          date: everest_appserver_primitive_PlainDate;
          hours: everest_appserver_primitive_Decimal;
          notes: everest_appserver_primitive_Text;
          memberId: everest_timetracking_model_node_TimeEntry.TimeEntry["memberId"];
          projectActivityId: everest_timetracking_model_node_TimeEntry.TimeEntry["projectActivityId"];
          projectId: everest_timetracking_model_node_TimetrackingProject.TimetrackingProject["id"];
        }[];
      };

      export type Output = {
        upsertedTimeEntryIds: everest_timetracking_model_node_TimeEntry.TimeEntry["id"][];
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace IsTimesheetSubmittable {
      export type Input = {
        weekStartDate: everest_appserver_primitive_PlainDate;
      };

      export type Output = {
        isSubmittable: everest_appserver_primitive_TrueFalse;
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace GetTimeOff {
      export type Input = {
        startDate: everest_appserver_primitive_Date;
        endDate: everest_appserver_primitive_Date;
      };

      export type Output = {
        holidays: {
          holidayDate: everest_appserver_primitive_PlainDate;
          holidayName: everest_appserver_primitive_Text;
          halfDay: everest_appserver_primitive_TrueFalse;
        }[];
        absences: {
          startDate: everest_appserver_primitive_PlainDate;
          endDate: everest_appserver_primitive_PlainDate;
          mandatoryLeaveId: everest_appserver_primitive_Number;
          absenceType: everest_appserver_primitive_Text;
          days: everest_appserver_primitive_Decimal;
        }[];
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace GetDailyAllocations {
      export type Input = {
        startDate: everest_appserver_primitive_PlainDate;
        endDate: everest_appserver_primitive_PlainDate;
      };

      export type Output = {
        dailyAllocations: {
          date: everest_appserver_primitive_PlainDate;
          allocations: {
            allocationId: everest_timetracking_model_node_Allocation.Allocation["id"];
            memberId: everest_timetracking_model_node_Member.Member["id"];
            projectActivityId: everest_timetracking_model_node_ProjectActivity.ProjectActivity["id"];
            projectActivity: everest_appserver_primitive_Text;
            totalHours: everest_appserver_primitive_Decimal;
            allocatedHours: everest_appserver_primitive_Decimal;
            method: everest_timetracking_enum_TimeAllocationMethod;
            workingDays: everest_appserver_enum_DayOfWeek[];
            positionName: everest_appserver_primitive_Text;
            projectName: everest_appserver_primitive_Text;
            activityName: everest_appserver_primitive_Text;
            billable: everest_appserver_primitive_TrueFalse;
          }[];
          timeOff: {
            hasTimeOff: everest_appserver_primitive_TrueFalse;
            isFullDay: everest_appserver_primitive_TrueFalse;
            holidayName: everest_appserver_primitive_Text;
            absenceType: everest_appserver_primitive_Text;
          };
        }[];
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace DeleteTimeEntriesForMemberActivity {
      export type Input = {
        memberId: everest_appserver_primitive_Number;
        projectActivityId: everest_appserver_primitive_Number;
        startDate: everest_appserver_primitive_PlainDate;
        endDate: everest_appserver_primitive_PlainDate;
      };

      export type Output = {
        deletedCount: everest_appserver_primitive_Number;
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }
  }

  export type Implementation = {
    InvoicedPeriods: EntitySets.InvoicedPeriods.Implementation;
    Projects: EntitySets.Projects.Implementation;
    ProjectPositions: EntitySets.ProjectPositions.Implementation;
    ProjectActivities: EntitySets.ProjectActivities.Implementation;
    TimeEntries: EntitySets.TimeEntries.Implementation;
    GetEmployeeWeeklyCapacity: Actions.GetEmployeeWeeklyCapacity.Implementation;
    GetTimeKpis: Actions.GetTimeKpis.Implementation;
    SubmitTimeEntries: Actions.SubmitTimeEntries.Implementation;
    UpsertTimeEntries: Actions.UpsertTimeEntries.Implementation;
    IsTimesheetSubmittable: Actions.IsTimesheetSubmittable.Implementation;
    GetTimeOff: Actions.GetTimeOff.Implementation;
    GetDailyAllocations: Actions.GetDailyAllocations.Implementation;
    DeleteTimeEntriesForMemberActivity: Actions.DeleteTimeEntriesForMemberActivity.Implementation;
  };
}

