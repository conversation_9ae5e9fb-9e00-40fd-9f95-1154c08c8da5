/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

import type { TimeEntry as everest_timetracking_model_node_TimeEntry } from "@pkg/everest.timetracking/types/TimeEntry";
import type { ProjectPosition as everest_timetracking_model_node_ProjectPosition } from "@pkg/everest.timetracking/types/ProjectPosition";
import type { TimetrackingProject as everest_timetracking_model_node_TimetrackingProject } from "@pkg/everest.timetracking/types/TimetrackingProject";
import type { Project as everest_hr_base_model_node_Project } from "@pkg/everest.hr.base/types/Project";
import type { Employee as everest_hr_base_model_node_Employee } from "@pkg/everest.hr.base/types/Employee";
import type { ProjectActivity as everest_timetracking_model_node_ProjectActivity } from "@pkg/everest.timetracking/types/ProjectActivity";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";

import { createOdataClientRuntime } from '@everestsystems/content-core/lib/runtime/loader';

/**
 * Generated frontend types for OData presentation TimeApprovalService.
 */
export namespace TimeApprovalServiceUI {
  export namespace EntitySets {
    export namespace TimeEntries {
      export namespace Get {
        export type Entity = {
          id?: everest_timetracking_model_node_TimeEntry.TimeEntry["id"] | undefined;
          date?: everest_timetracking_model_node_TimeEntry.TimeEntry["date"] | undefined;
          hours?: everest_timetracking_model_node_TimeEntry.TimeEntry["hours"] | undefined;
          memberId?: everest_timetracking_model_node_TimeEntry.TimeEntry["memberId"] | undefined;
          notes?: everest_timetracking_model_node_TimeEntry.TimeEntry["notes"] | undefined;
          projectActivityId?: everest_timetracking_model_node_TimeEntry.TimeEntry["projectActivityId"] | undefined;
          submissionDate?: everest_timetracking_model_node_TimeEntry.TimeEntry["submissionDate"] | undefined;
          submissionStatus?: everest_timetracking_model_node_TimeEntry.TimeEntry["submissionStatus"] | undefined;
          projectPositionId?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["id"] | undefined;
          positionName?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["name"] | undefined;
          positionProjectId?: everest_timetracking_model_node_TimetrackingProject.TimetrackingProject["id"] | undefined;
          projectName?: everest_hr_base_model_node_Project.Project["projectName"] | undefined;
          projectId?: everest_timetracking_model_node_TimetrackingProject.TimetrackingProject["id"] | undefined;
          employeeId?: everest_hr_base_model_node_Employee.Employee["id"] | undefined;
          activityId?: everest_timetracking_model_node_ProjectActivity.ProjectActivity["id"] | undefined;
          activityName?: everest_timetracking_model_node_ProjectActivity.ProjectActivity["name"] | undefined;
          employeeName?: everest_hr_base_model_node_Employee.Employee["name"] | undefined;
        };

        export type Filter =
          | PropertyFilter
          | [Filter, 'and', Filter]
          | [Filter, 'or', Filter];

        /**
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), filters must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to filter by its amount component.
         */
        export type PropertyFilter = {
          property: string;
          comparator: 'eq' | 'ne' | 'lt' | 'le' | 'gt' | 'ge' | 'contains';
          value: unknown;
        };

        /**
         * Specifies sorting for a property in ascending or descending order.
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), sorting must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to sort by its amount component.
         */
        export type OrderbyItem = {
          property: string;
          ordering: 'asc' | 'desc';
        };

        export interface Request {
          setFilter(filter: Filter): this;

          setOrderby(orderby: ReadonlyArray<OrderbyItem>): this;

          setSkip(skip: number): this;

          setTop(top: number): this;

          setSelect(select: ReadonlyArray<keyof Entity>): this;

          setCount(count: boolean): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entities: Entity[];
          count?: number | undefined;
        };
      }

      export namespace Patch {
        export type Data = {
          date?: everest_timetracking_model_node_TimeEntry.TimeEntry["date"] | null | undefined;
          hours?: everest_timetracking_model_node_TimeEntry.TimeEntry["hours"] | null | undefined;
          memberId?: everest_timetracking_model_node_TimeEntry.TimeEntry["memberId"] | null | undefined;
          notes?: everest_timetracking_model_node_TimeEntry.TimeEntry["notes"] | null | undefined;
          projectActivityId?: everest_timetracking_model_node_TimeEntry.TimeEntry["projectActivityId"] | null | undefined;
          submissionDate?: everest_timetracking_model_node_TimeEntry.TimeEntry["submissionDate"] | null | undefined;
          submissionStatus?: everest_timetracking_model_node_TimeEntry.TimeEntry["submissionStatus"] | null | undefined;
          projectPositionId?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["id"] | null | undefined;
          positionName?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["name"] | null | undefined;
          positionProjectId?: everest_timetracking_model_node_TimetrackingProject.TimetrackingProject["id"] | null | undefined;
          projectName?: everest_hr_base_model_node_Project.Project["projectName"] | null | undefined;
          projectId?: everest_timetracking_model_node_TimetrackingProject.TimetrackingProject["id"] | null | undefined;
          employeeId?: everest_hr_base_model_node_Employee.Employee["id"] | null | undefined;
          activityId?: everest_timetracking_model_node_ProjectActivity.ProjectActivity["id"] | null | undefined;
          activityName?: everest_timetracking_model_node_ProjectActivity.ProjectActivity["name"] | null | undefined;
          employeeName?: everest_hr_base_model_node_Employee.Employee["name"] | null | undefined;
        };

        export interface Request {
          setId(id: number): this;

          setData(data: Data): this;

          execute(): Promise<Response>;
        }

        export type Response = Record<string, never>;
      }
    }
  }

  export namespace Actions {
    export namespace ApproveTimeEntries {
      export namespace Execute {
        export type Input = {
          timeEntryIds: everest_timetracking_model_node_TimeEntry.TimeEntry["id"][];
          projectIds: everest_timetracking_model_node_TimetrackingProject.TimetrackingProject["id"][];
        };

        export type Output = Record<string, never>;

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace RejectTimeEntries {
      export namespace Execute {
        export type Input = {
          timeEntryIds: everest_timetracking_model_node_TimeEntry.TimeEntry["id"][];
          projectIds: everest_timetracking_model_node_TimetrackingProject.TimetrackingProject["id"][];
          rejectionReason: everest_appserver_primitive_Text;
        };

        export type Output = Record<string, never>;

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }
  }

  export namespace Functions {
    export namespace CheckApprovalPolicies {
      export namespace Execute {
        export type Input = Record<string, never>;

        export type Output = {
          hasApprovalPolicies: everest_appserver_primitive_TrueFalse;
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }
  }

  export namespace Permission {
    export namespace Check {
      export type Result = {
        dataSets: {
          TimeEntries: {
            isGetPermitted: boolean;
            isPatchPermitted: boolean;
          };
        };
        actions: {
          ApproveTimeEntries: {
            isExecutePermitted: boolean;
          };
          CheckApprovalPolicies: {
            isExecutePermitted: boolean;
          };
          RejectTimeEntries: {
            isExecutePermitted: boolean;
          };
        };
      };

      export interface Request {
        execute(): Promise<Response>;
      }

      export type Response = {
        result: Result;
      };
    }
  }

  export namespace Api {
    export type EntitySets = {
      TimeEntries: {
        get: EntitySets.TimeEntries.Get.Request;
        patch: EntitySets.TimeEntries.Patch.Request;
      };
    };

    export type Actions = {
      ApproveTimeEntries: {
        execute: Actions.ApproveTimeEntries.Execute.Request;
      };
      RejectTimeEntries: {
        execute: Actions.RejectTimeEntries.Execute.Request;
      };
    };

    export type Functions = {
      CheckApprovalPolicies: {
        execute: Functions.CheckApprovalPolicies.Execute.Request;
      };
    };

    export interface Client {
      createEntitySetGetRequest<T extends 'TimeEntries'>(entitySetName: T): Api.EntitySets[T]['get'];

      createEntitySetPatchRequest<T extends 'TimeEntries'>(entitySetName: T): Api.EntitySets[T]['patch'];

      createActionExecuteRequest<T extends 'ApproveTimeEntries' | 'RejectTimeEntries'>(actionName: T): Api.Actions[T]['execute'];

      createFunctionExecuteRequest<T extends 'CheckApprovalPolicies'>(functionName: T): Api.Functions[T]['execute'];

      createPermissionCheckRequest(): Permission.Check.Request;
    }
  }

  export const client = createOdataClientRuntime<Api.Client>('everest.psa/modules/time/pages/timeApproval/TimeApprovalService');
}

