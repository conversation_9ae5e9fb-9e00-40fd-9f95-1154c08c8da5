/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

import type { TimeEntry as everest_timetracking_model_node_TimeEntry } from "@pkg/everest.timetracking/types/TimeEntry";
import type { ProjectPosition as everest_timetracking_model_node_ProjectPosition } from "@pkg/everest.timetracking/types/ProjectPosition";
import type { TimetrackingProject as everest_timetracking_model_node_TimetrackingProject } from "@pkg/everest.timetracking/types/TimetrackingProject";
import type { Project as everest_hr_base_model_node_Project } from "@pkg/everest.hr.base/types/Project";
import type { Employee as everest_hr_base_model_node_Employee } from "@pkg/everest.hr.base/types/Employee";
import type { ProjectActivity as everest_timetracking_model_node_ProjectActivity } from "@pkg/everest.timetracking/types/ProjectActivity";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";

import type {
  Filter,
  PresentationSession,
} from '@everestsystems/content-core';

/**
 * Generated backend types for OData presentation TimeApprovalService.
 */
export namespace TimeApprovalService {
  export namespace EntitySets {
    export namespace TimeEntries {
      export namespace Query {
        export type Instance = {
          id?: everest_timetracking_model_node_TimeEntry.TimeEntry["id"] | undefined;
          date?: everest_timetracking_model_node_TimeEntry.TimeEntry["date"] | undefined;
          hours?: everest_timetracking_model_node_TimeEntry.TimeEntry["hours"] | undefined;
          memberId?: everest_timetracking_model_node_TimeEntry.TimeEntry["memberId"] | undefined;
          notes?: everest_timetracking_model_node_TimeEntry.TimeEntry["notes"] | undefined;
          projectActivityId?: everest_timetracking_model_node_TimeEntry.TimeEntry["projectActivityId"] | undefined;
          submissionDate?: everest_timetracking_model_node_TimeEntry.TimeEntry["submissionDate"] | undefined;
          submissionStatus?: everest_timetracking_model_node_TimeEntry.TimeEntry["submissionStatus"] | undefined;
          projectPositionId?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["id"] | undefined;
          positionName?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["name"] | undefined;
          positionProjectId?: everest_timetracking_model_node_TimetrackingProject.TimetrackingProject["id"] | undefined;
          projectName?: everest_hr_base_model_node_Project.Project["projectName"] | undefined;
          projectId?: everest_timetracking_model_node_TimetrackingProject.TimetrackingProject["id"] | undefined;
          employeeId?: everest_hr_base_model_node_Employee.Employee["id"] | undefined;
          activityId?: everest_timetracking_model_node_ProjectActivity.ProjectActivity["id"] | undefined;
          activityName?: everest_timetracking_model_node_ProjectActivity.ProjectActivity["name"] | undefined;
          employeeName?: everest_hr_base_model_node_Employee.Employee["name"] | undefined;
        };

        export type Order = {
          field: keyof Instance;
          ordering: 'asc' | 'desc';
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            where: Filter<Instance>;
            orderBy: ReadonlyArray<Order>;
            skip: number | undefined;
            take: number | undefined;
            fieldList: ReadonlyArray<keyof Instance>;
            count: boolean;
          };

          export type Response = {
            instances: Instance[];

            /**
             * If count was set to true on the request object, this property
             * must be set, otherwise it must evaluate to undefined.
             */
            count?: number | undefined;
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export namespace Update {
        export type Data = {
          date?: everest_timetracking_model_node_TimeEntry.TimeEntry["date"] | null;
          hours?: everest_timetracking_model_node_TimeEntry.TimeEntry["hours"] | null;
          memberId?: everest_timetracking_model_node_TimeEntry.TimeEntry["memberId"] | null;
          notes?: everest_timetracking_model_node_TimeEntry.TimeEntry["notes"] | null;
          projectActivityId?: everest_timetracking_model_node_TimeEntry.TimeEntry["projectActivityId"] | null;
          submissionDate?: everest_timetracking_model_node_TimeEntry.TimeEntry["submissionDate"] | null;
          submissionStatus?: everest_timetracking_model_node_TimeEntry.TimeEntry["submissionStatus"] | null;
          projectPositionId?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["id"] | null;
          positionName?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["name"] | null;
          positionProjectId?: everest_timetracking_model_node_TimetrackingProject.TimetrackingProject["id"] | null;
          projectName?: everest_hr_base_model_node_Project.Project["projectName"] | null;
          projectId?: everest_timetracking_model_node_TimetrackingProject.TimetrackingProject["id"] | null;
          employeeId?: everest_hr_base_model_node_Employee.Employee["id"] | null;
          activityId?: everest_timetracking_model_node_ProjectActivity.ProjectActivity["id"] | null;
          activityName?: everest_timetracking_model_node_ProjectActivity.ProjectActivity["name"] | null;
          employeeName?: everest_hr_base_model_node_Employee.Employee["name"] | null;
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            id: number;
            data: Data;
          };

          export type Response = void;
        }

        export interface Implementation {
          execute(input: Execute.Request): Promise<Execute.Response>;
        }
      }

      export type Implementation = {
        query: Query.Implementation;
        update: Update.Implementation;
      };
    }
  }

  export namespace Actions {
    export namespace ApproveTimeEntries {
      export type Input = {
        timeEntryIds: everest_timetracking_model_node_TimeEntry.TimeEntry["id"][];
        projectIds: everest_timetracking_model_node_TimetrackingProject.TimetrackingProject["id"][];
      };

      export type Output = Record<string, never>;

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace RejectTimeEntries {
      export type Input = {
        timeEntryIds: everest_timetracking_model_node_TimeEntry.TimeEntry["id"][];
        projectIds: everest_timetracking_model_node_TimetrackingProject.TimetrackingProject["id"][];
        rejectionReason: everest_appserver_primitive_Text;
      };

      export type Output = Record<string, never>;

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace CheckApprovalPolicies {
      export type Input = Record<string, never>;

      export type Output = {
        hasApprovalPolicies: everest_appserver_primitive_TrueFalse;
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }
  }

  export type Implementation = {
    TimeEntries: EntitySets.TimeEntries.Implementation;
    ApproveTimeEntries: Actions.ApproveTimeEntries.Implementation;
    RejectTimeEntries: Actions.RejectTimeEntries.Implementation;
    CheckApprovalPolicies: Actions.CheckApprovalPolicies.Implementation;
  };
}

