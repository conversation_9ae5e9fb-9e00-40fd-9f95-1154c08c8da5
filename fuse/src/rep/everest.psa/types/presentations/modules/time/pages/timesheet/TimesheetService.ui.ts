/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

import type { InvoicedPeriod as everest_psa_model_node_InvoicedPeriod } from "@pkg/everest.psa/types/InvoicedPeriod";
import type { TimetrackingProject as everest_timetracking_model_node_TimetrackingProject } from "@pkg/everest.timetracking/types/TimetrackingProject";
import type { Project as everest_hr_base_model_node_Project } from "@pkg/everest.hr.base/types/Project";
import type { EvstPlainDate as everest_appserver_primitive_PlainDate } from "@pkg/everest.appserver/types/primitives/PlainDate";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { ProjectPosition as everest_timetracking_model_node_ProjectPosition } from "@pkg/everest.timetracking/types/ProjectPosition";
import type { ProjectActivity as everest_timetracking_model_node_ProjectActivity } from "@pkg/everest.timetracking/types/ProjectActivity";
import type { EvstID as everest_appserver_primitive_ID } from "@pkg/everest.appserver/types/primitives/ID";
import type { TimeEntry as everest_timetracking_model_node_TimeEntry } from "@pkg/everest.timetracking/types/TimeEntry";
import type { EvstDecimal as everest_appserver_primitive_Decimal } from "@pkg/everest.appserver/types/primitives/Decimal";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { EvstDate as everest_appserver_primitive_Date } from "@pkg/everest.appserver/types/primitives/Date";
import type { Allocation as everest_timetracking_model_node_Allocation } from "@pkg/everest.timetracking/types/Allocation";
import type { Member as everest_timetracking_model_node_Member } from "@pkg/everest.timetracking/types/Member";
import type { EvstTimeAllocationMethod as everest_timetracking_enum_TimeAllocationMethod } from "@pkg/everest.timetracking/types/enums/TimeAllocationMethod";
import type { EvstDayOfWeek as everest_appserver_enum_DayOfWeek } from "@pkg/everest.appserver/types/enums/DayOfWeek";

import { createOdataClientRuntime } from '@everestsystems/content-core/lib/runtime/loader';

/**
 * Generated frontend types for OData presentation TimesheetService.
 */
export namespace TimesheetServiceUI {
  export namespace EntitySets {
    export namespace InvoicedPeriods {
      export namespace Get {
        export type Entity = {
          id?: everest_psa_model_node_InvoicedPeriod.InvoicedPeriod["id"] | undefined;
          startDate?: everest_psa_model_node_InvoicedPeriod.InvoicedPeriod["startDate"] | undefined;
          endDate?: everest_psa_model_node_InvoicedPeriod.InvoicedPeriod["endDate"] | undefined;
          psaProjectId?: everest_psa_model_node_InvoicedPeriod.InvoicedPeriod["psaProjectId"] | undefined;
        };

        export type Filter =
          | PropertyFilter
          | [Filter, 'and', Filter]
          | [Filter, 'or', Filter];

        /**
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), filters must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to filter by its amount component.
         */
        export type PropertyFilter = {
          property: string;
          comparator: 'eq' | 'ne' | 'lt' | 'le' | 'gt' | 'ge' | 'contains';
          value: unknown;
        };

        /**
         * Specifies sorting for a property in ascending or descending order.
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), sorting must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to sort by its amount component.
         */
        export type OrderbyItem = {
          property: string;
          ordering: 'asc' | 'desc';
        };

        export interface Request {
          setFilter(filter: Filter): this;

          setOrderby(orderby: ReadonlyArray<OrderbyItem>): this;

          setSkip(skip: number): this;

          setTop(top: number): this;

          setSelect(select: ReadonlyArray<keyof Entity>): this;

          setCount(count: boolean): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entities: Entity[];
          count?: number | undefined;
        };
      }
    }
    export namespace Projects {
      export namespace Get {
        export type Entity = {
          id?: everest_timetracking_model_node_TimetrackingProject.TimetrackingProject["id"] | undefined;
          projectName?: everest_hr_base_model_node_Project.Project["projectName"] | undefined;
          startDate?: everest_appserver_primitive_PlainDate | undefined;
          endDate?: everest_appserver_primitive_PlainDate | undefined;
          status?: everest_appserver_primitive_Text | undefined;
        };

        export type Filter =
          | PropertyFilter
          | [Filter, 'and', Filter]
          | [Filter, 'or', Filter];

        /**
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), filters must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to filter by its amount component.
         */
        export type PropertyFilter = {
          property: string;
          comparator: 'eq' | 'ne' | 'lt' | 'le' | 'gt' | 'ge' | 'contains';
          value: unknown;
        };

        /**
         * Specifies sorting for a property in ascending or descending order.
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), sorting must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to sort by its amount component.
         */
        export type OrderbyItem = {
          property: string;
          ordering: 'asc' | 'desc';
        };

        export interface Request {
          setFilter(filter: Filter): this;

          setOrderby(orderby: ReadonlyArray<OrderbyItem>): this;

          setSkip(skip: number): this;

          setTop(top: number): this;

          setSelect(select: ReadonlyArray<keyof Entity>): this;

          setCount(count: boolean): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entities: Entity[];
          count?: number | undefined;
        };
      }
    }
    export namespace ProjectPositions {
      export namespace Get {
        export type Entity = {
          id?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["id"] | undefined;
          name?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["name"] | undefined;
          description?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["description"] | undefined;
          projectId?: everest_timetracking_model_node_ProjectPosition.ProjectPosition["projectId"] | undefined;
        };

        export type Filter =
          | PropertyFilter
          | [Filter, 'and', Filter]
          | [Filter, 'or', Filter];

        /**
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), filters must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to filter by its amount component.
         */
        export type PropertyFilter = {
          property: string;
          comparator: 'eq' | 'ne' | 'lt' | 'le' | 'gt' | 'ge' | 'contains';
          value: unknown;
        };

        /**
         * Specifies sorting for a property in ascending or descending order.
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), sorting must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to sort by its amount component.
         */
        export type OrderbyItem = {
          property: string;
          ordering: 'asc' | 'desc';
        };

        export interface Request {
          setFilter(filter: Filter): this;

          setOrderby(orderby: ReadonlyArray<OrderbyItem>): this;

          setSkip(skip: number): this;

          setTop(top: number): this;

          setSelect(select: ReadonlyArray<keyof Entity>): this;

          setCount(count: boolean): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entities: Entity[];
          count?: number | undefined;
        };
      }
    }
    export namespace ProjectActivities {
      export namespace Get {
        export type Entity = {
          id?: everest_timetracking_model_node_ProjectActivity.ProjectActivity["id"] | undefined;
          name?: everest_timetracking_model_node_ProjectActivity.ProjectActivity["name"] | undefined;
          description?: everest_timetracking_model_node_ProjectActivity.ProjectActivity["description"] | undefined;
          projectPositionId?: everest_appserver_primitive_ID | undefined;
          memberId?: everest_appserver_primitive_ID | undefined;
          billable?: everest_timetracking_model_node_ProjectActivity.ProjectActivity["billable"] | undefined;
          projectId?: everest_timetracking_model_node_TimetrackingProject.TimetrackingProject["id"] | undefined;
        };

        export type Filter =
          | PropertyFilter
          | [Filter, 'and', Filter]
          | [Filter, 'or', Filter];

        /**
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), filters must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to filter by its amount component.
         */
        export type PropertyFilter = {
          property: string;
          comparator: 'eq' | 'ne' | 'lt' | 'le' | 'gt' | 'ge' | 'contains';
          value: unknown;
        };

        /**
         * Specifies sorting for a property in ascending or descending order.
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), sorting must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to sort by its amount component.
         */
        export type OrderbyItem = {
          property: string;
          ordering: 'asc' | 'desc';
        };

        export interface Request {
          setFilter(filter: Filter): this;

          setOrderby(orderby: ReadonlyArray<OrderbyItem>): this;

          setSkip(skip: number): this;

          setTop(top: number): this;

          setSelect(select: ReadonlyArray<keyof Entity>): this;

          setCount(count: boolean): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entities: Entity[];
          count?: number | undefined;
        };
      }
    }
    export namespace TimeEntries {
      export namespace Get {
        export type Entity = {
          id?: everest_timetracking_model_node_TimeEntry.TimeEntry["id"] | undefined;
          date?: everest_timetracking_model_node_TimeEntry.TimeEntry["date"] | undefined;
          hours?: everest_timetracking_model_node_TimeEntry.TimeEntry["hours"] | undefined;
          memberId?: everest_timetracking_model_node_TimeEntry.TimeEntry["memberId"] | undefined;
          notes?: everest_timetracking_model_node_TimeEntry.TimeEntry["notes"] | undefined;
          projectActivityId?: everest_timetracking_model_node_TimeEntry.TimeEntry["projectActivityId"] | undefined;
          submissionStatus?: everest_timetracking_model_node_TimeEntry.TimeEntry["submissionStatus"] | undefined;
        };

        export type Filter =
          | PropertyFilter
          | [Filter, 'and', Filter]
          | [Filter, 'or', Filter];

        /**
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), filters must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to filter by its amount component.
         */
        export type PropertyFilter = {
          property: string;
          comparator: 'eq' | 'ne' | 'lt' | 'le' | 'gt' | 'ge' | 'contains';
          value: unknown;
        };

        /**
         * Specifies sorting for a property in ascending or descending order.
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), sorting must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to sort by its amount component.
         */
        export type OrderbyItem = {
          property: string;
          ordering: 'asc' | 'desc';
        };

        export interface Request {
          setFilter(filter: Filter): this;

          setOrderby(orderby: ReadonlyArray<OrderbyItem>): this;

          setSkip(skip: number): this;

          setTop(top: number): this;

          setSelect(select: ReadonlyArray<keyof Entity>): this;

          setCount(count: boolean): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entities: Entity[];
          count?: number | undefined;
        };
      }
    }
  }

  export namespace Actions {
    export namespace SubmitTimeEntries {
      export namespace Execute {
        export type Input = {
          timeEntryIds: everest_appserver_primitive_ID[];
        };

        export type Output = Record<string, never>;

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace UpsertTimeEntries {
      export namespace Execute {
        export type Input = {
          startDate: everest_appserver_primitive_PlainDate;
          endDate: everest_appserver_primitive_PlainDate;
          timeEntries: {
            id: everest_timetracking_model_node_TimeEntry.TimeEntry["id"];
            date: everest_appserver_primitive_PlainDate;
            hours: everest_appserver_primitive_Decimal;
            notes: everest_appserver_primitive_Text;
            memberId: everest_timetracking_model_node_TimeEntry.TimeEntry["memberId"];
            projectActivityId: everest_timetracking_model_node_TimeEntry.TimeEntry["projectActivityId"];
            projectId: everest_timetracking_model_node_TimetrackingProject.TimetrackingProject["id"];
          }[];
        };

        export type Output = {
          upsertedTimeEntryIds: everest_timetracking_model_node_TimeEntry.TimeEntry["id"][];
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace DeleteTimeEntriesForMemberActivity {
      export namespace Execute {
        export type Input = {
          memberId: everest_appserver_primitive_Number;
          projectActivityId: everest_appserver_primitive_Number;
          startDate: everest_appserver_primitive_PlainDate;
          endDate: everest_appserver_primitive_PlainDate;
        };

        export type Output = {
          deletedCount: everest_appserver_primitive_Number;
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }
  }

  export namespace Functions {
    export namespace GetEmployeeWeeklyCapacity {
      export namespace Execute {
        export type Input = Record<string, never>;

        export type Output = {
          weeklyCapacity: everest_appserver_primitive_Number;
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace GetTimeKpis {
      export namespace Execute {
        export type Input = {
          startDate: everest_appserver_primitive_PlainDate;
          endDate: everest_appserver_primitive_PlainDate;
        };

        export type Output = {
          totalHours: everest_appserver_primitive_Decimal;
          billableHours: everest_appserver_primitive_Decimal;
          utilization: everest_appserver_primitive_Decimal;
          billableUtilization: everest_appserver_primitive_Decimal;
          totalUtilization: everest_appserver_primitive_Decimal;
          billableUtilizationWithoutAbsences: everest_appserver_primitive_Decimal;
          totalUtilizationWithoutAbsences: everest_appserver_primitive_Decimal;
          totalHoursIncludingAbsences: everest_appserver_primitive_Decimal;
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace IsTimesheetSubmittable {
      export namespace Execute {
        export type Input = {
          weekStartDate: everest_appserver_primitive_PlainDate;
        };

        export type Output = {
          isSubmittable: everest_appserver_primitive_TrueFalse;
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace GetTimeOff {
      export namespace Execute {
        export type Input = {
          startDate: everest_appserver_primitive_Date;
          endDate: everest_appserver_primitive_Date;
        };

        export type Output = {
          holidays: {
            holidayDate: everest_appserver_primitive_PlainDate;
            holidayName: everest_appserver_primitive_Text;
            halfDay: everest_appserver_primitive_TrueFalse;
          }[];
          absences: {
            startDate: everest_appserver_primitive_PlainDate;
            endDate: everest_appserver_primitive_PlainDate;
            mandatoryLeaveId: everest_appserver_primitive_Number;
            absenceType: everest_appserver_primitive_Text;
            days: everest_appserver_primitive_Decimal;
          }[];
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace GetDailyAllocations {
      export namespace Execute {
        export type Input = {
          startDate: everest_appserver_primitive_PlainDate;
          endDate: everest_appserver_primitive_PlainDate;
        };

        export type Output = {
          dailyAllocations: {
            date: everest_appserver_primitive_PlainDate;
            allocations: {
              allocationId: everest_timetracking_model_node_Allocation.Allocation["id"];
              memberId: everest_timetracking_model_node_Member.Member["id"];
              projectActivityId: everest_timetracking_model_node_ProjectActivity.ProjectActivity["id"];
              projectActivity: everest_appserver_primitive_Text;
              totalHours: everest_appserver_primitive_Decimal;
              allocatedHours: everest_appserver_primitive_Decimal;
              method: everest_timetracking_enum_TimeAllocationMethod;
              workingDays: everest_appserver_enum_DayOfWeek[];
              positionName: everest_appserver_primitive_Text;
              projectName: everest_appserver_primitive_Text;
              activityName: everest_appserver_primitive_Text;
              billable: everest_appserver_primitive_TrueFalse;
            }[];
            timeOff: {
              hasTimeOff: everest_appserver_primitive_TrueFalse;
              isFullDay: everest_appserver_primitive_TrueFalse;
              holidayName: everest_appserver_primitive_Text;
              absenceType: everest_appserver_primitive_Text;
            };
          }[];
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }
  }

  export namespace Permission {
    export namespace Check {
      export type Result = {
        dataSets: {
          InvoicedPeriods: {
            isGetPermitted: boolean;
          };
          ProjectActivities: {
            isGetPermitted: boolean;
          };
          ProjectPositions: {
            isGetPermitted: boolean;
          };
          Projects: {
            isGetPermitted: boolean;
          };
          TimeEntries: {
            isGetPermitted: boolean;
          };
        };
        actions: {
          DeleteTimeEntriesForMemberActivity: {
            isExecutePermitted: boolean;
          };
          GetDailyAllocations: {
            isExecutePermitted: boolean;
          };
          GetEmployeeWeeklyCapacity: {
            isExecutePermitted: boolean;
          };
          GetTimeKpis: {
            isExecutePermitted: boolean;
          };
          GetTimeOff: {
            isExecutePermitted: boolean;
          };
          IsTimesheetSubmittable: {
            isExecutePermitted: boolean;
          };
          SubmitTimeEntries: {
            isExecutePermitted: boolean;
          };
          UpsertTimeEntries: {
            isExecutePermitted: boolean;
          };
        };
      };

      export interface Request {
        execute(): Promise<Response>;
      }

      export type Response = {
        result: Result;
      };
    }
  }

  export namespace Api {
    export type EntitySets = {
      InvoicedPeriods: {
        get: EntitySets.InvoicedPeriods.Get.Request;
      };
      Projects: {
        get: EntitySets.Projects.Get.Request;
      };
      ProjectPositions: {
        get: EntitySets.ProjectPositions.Get.Request;
      };
      ProjectActivities: {
        get: EntitySets.ProjectActivities.Get.Request;
      };
      TimeEntries: {
        get: EntitySets.TimeEntries.Get.Request;
      };
    };

    export type Actions = {
      SubmitTimeEntries: {
        execute: Actions.SubmitTimeEntries.Execute.Request;
      };
      UpsertTimeEntries: {
        execute: Actions.UpsertTimeEntries.Execute.Request;
      };
      DeleteTimeEntriesForMemberActivity: {
        execute: Actions.DeleteTimeEntriesForMemberActivity.Execute.Request;
      };
    };

    export type Functions = {
      GetEmployeeWeeklyCapacity: {
        execute: Functions.GetEmployeeWeeklyCapacity.Execute.Request;
      };
      GetTimeKpis: {
        execute: Functions.GetTimeKpis.Execute.Request;
      };
      IsTimesheetSubmittable: {
        execute: Functions.IsTimesheetSubmittable.Execute.Request;
      };
      GetTimeOff: {
        execute: Functions.GetTimeOff.Execute.Request;
      };
      GetDailyAllocations: {
        execute: Functions.GetDailyAllocations.Execute.Request;
      };
    };

    export interface Client {
      createEntitySetGetRequest<T extends 'InvoicedPeriods' | 'Projects' | 'ProjectPositions' | 'ProjectActivities' | 'TimeEntries'>(entitySetName: T): Api.EntitySets[T]['get'];

      createActionExecuteRequest<T extends 'SubmitTimeEntries' | 'UpsertTimeEntries' | 'DeleteTimeEntriesForMemberActivity'>(actionName: T): Api.Actions[T]['execute'];

      createFunctionExecuteRequest<T extends 'GetEmployeeWeeklyCapacity' | 'GetTimeKpis' | 'IsTimesheetSubmittable' | 'GetTimeOff' | 'GetDailyAllocations'>(functionName: T): Api.Functions[T]['execute'];

      createPermissionCheckRequest(): Permission.Check.Request;
    }
  }

  export const client = createOdataClientRuntime<Api.Client>('everest.psa/modules/time/pages/timesheet/TimesheetService');
}

