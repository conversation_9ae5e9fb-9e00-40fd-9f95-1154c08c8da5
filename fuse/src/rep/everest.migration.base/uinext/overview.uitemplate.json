{"version": 2, "uicontroller": ["overview.uicontroller.ts", "everest.migration.base/uinext/dataSyncTracking/dataSyncTracker.uicontroller.ts"], "uimodel": {"nodes": {"migration": {"type": "struct", "modelId": "everest.migration.base/MigrationModel.Migration"}, "mappings": {"type": "struct", "modelId": "everest.fin.integration.base/MappingsModel.Mappings"}, "config": {"type": "struct", "modelId": "everest.migration.base/MigrationModel.Migration", "getConfiguration": {"providerName": "@state:providerName"}}, "connector": {"type": "struct", "modelId": "everest.migration.base/MigrationModel.Migration", "getConnector": {"providerName": "@state:providerName"}}, "settings": {"type": "struct", "modelId": "everest.migration.base/MigrationModel.Migration", "getSettings": {"providerName": "@state:providerName"}}, "linkings": {"type": "list", "modelId": "everest.migration.base/MigrationModel.Migration", "getLinkings": {"providerName": "@state:providerName"}}, "semanticMetadataExtractions": {"type": "list", "modelId": "everest.migration.base/MigrationModel.Migration", "getExtractions": {"providerName": "@state:providerName", "migrationType": "Semantic", "dataType": "metadata"}}, "semanticBusinessDataExtractions": {"type": "list", "modelId": "everest.migration.base/MigrationModel.Migration", "getExtractions": {"providerName": "@state:providerName", "migrationType": "Semantic", "dataType": "businessData"}}, "technicalMetadataExtractions": {"type": "list", "modelId": "everest.migration.base/MigrationModel.Migration", "getExtractions": {"providerName": "@state:providerName", "migrationType": "Technical", "dataType": "metadata"}}, "technicalBusinessDataExtractions": {"type": "list", "modelId": "everest.migration.base/MigrationModel.Migration", "getExtractions": {"providerName": "@state:providerName", "migrationType": "Technical", "dataType": "businessData"}}, "tasks": {"type": "struct", "modelId": "everest.migration.base/MigrationModel.Migration", "getTaskInformation": "@controller:getTaskInformation()"}, "dataSyncRun": {"type": "struct", "model": "urn:evst:everest:base:model/node:RunningAction"}}}, "uiview": {"templateType": "details", "i18n": ["everest.fin.integration.expense/expenseIntegration", "migrationBase"], "title": "@controller:getTabTitle()", "config": {"autoRefreshData": true, "allowRefreshData": true, "stretch": true}, "grid": {"limited": false, "sidebarSize": "360px"}, "header": {"layout": "everest.base.ui/layouts/uilayout/segmented/avatar/avatar", "content": {"status": "@controller:getConnectionStatusText()", "title": "@controller:getIntegrationTitle()", "showAvatar": true}}, "sidebar": {"variant": "transparent", "position": "left", "content": [{"component": "Input", "size": "12", "label": "{{expenseIntegration.entity}}", "value": "@controller:getEntityName()", "visible   ": "@controller:showEntity()"}, {"component": "Input", "size": "12", "label": "{{expenseIntegration.connectedBy}}", "value": "@controller:getUserName()"}, {"component": "Input", "size": "12", "label": "{{expenseIntegration.connectedOn}}", "value": "@controller:getCreatedDate()"}, {"component": "Input", "size": "12", "label": "{{expenseIntegration.lastSynced}}", "value": "@controller:getLastSyncDate()"}, {"component": "ButtonGroup", "direction": "vertical", "size": "12", "actions": [{"variant": "primary", "label": "{{migrationBase.syncAll}}", "visible": "@controller:showSyncAll()", "disabled": "@controller:syncStatus()", "onClick": "@controller:syncAll"}, {"variant": "secondary", "label": "{{expenseIntegration.editConnectionDetails}}", "visible": "@controller:showEditConnectionDetails()", "disabled": "@controller:connectionStatus()", "onClick": "@controller:editConnectionDetails"}]}]}, "sections": {"content": [{"component": "Segments", "props": {"segments": [{"title": "{{migrationBase.technicalMappings}}", "sections": [{"component": "Table", "visible": "@controller:showTable('technicalMetadataExtractions')", "customId": "@controller:getTableCustomId('technicalMappingsMasterDataTable')", "section": {"title": "{{expenseIntegration.masterData}}", "grid": {"size": "12"}, "displayControl": {"hide": true}}, "props": {"data": "@binding:technicalMetadataExtractions", "variant": "white-borderless", "rowBackgroundGetter": "@controller:getRowBackgroundColor", "hideFilters": true, "columns": [{"headerName": "{{expenseIntegration.type}}", "field": "displayNamePlural", "pinned": "left", "autoSizeMinWidth": "md", "fieldProps": {"fontWeight": "bold"}, "onCellClicked": "@controller:goToApplication", "disableCellClick": "@controller:disableApplicationLink"}, {"headerName": "@controller:getTotalImportedHeader()", "field": "totalImported"}, {"headerName": "{{expenseIntegration.skipped}}", "field": "totalSkipped", "onCellClicked": "@controller:goToSkipped", "disableCellClick": "@controller:disableStagingLink"}, {"headerName": "{{expenseIntegration.failed}}", "field": "totalFailed", "onCellClicked": "@controller:goToFailed", "disableCellClick": "@controller:disableStagingLink"}, {"headerName": "{{expenseIntegration.drafted}}", "field": "totalDrafts", "onCellClicked": "@controller:goToDrafted", "disableCellClick": "@controller:disableStagingLink"}, {"headerName": "{{expenseIntegration.integrated}}", "field": "totalIntegrated", "onCellClicked": "@controller:goToIntegrated", "disableCellClick": "@controller:disableStagingLink"}, {"headerName": "{{expenseIntegration.mappedToEverest}}", "field": "totalMatched", "initialHide": "@controller:hideExtraColumn()", "onCellClicked": "@controller:openMatchingModal", "disableCellClick": "@controller:disableStagingLink"}, {"headerName": "Migration Type", "field": "migrationType", "fieldProps": {"fontWeight": "bold"}, "initialHide": true}, {"headerName": "{{expenseIntegration.lastSynced}}", "field": "lastSyncDate", "valueGetter": "@controller:getLastSyncValue", "initialHide": "@controller:hideLastSyncColumn()"}, {"sortable": false, "suppressColumnsToolPanel": true, "suppressFilter": true, "suppressFiltersToolPanel": true, "suppressMenu": true, "suppressMovable": true, "suppressSort": true, "valueGetter": "@controller:getDataSyncStatus", "headerName": "", "field": "status", "cellVariant": {"variant": "badge", "matchers": "@controller:getMatchers"}, "initialHide": "@controller:shouldHideDataSyncStatus('master')"}], "rowActions": {"variant": "button", "shape": "pill", "buttonLabel": "Actions", "columnPosition": -1, "tooltip": {"text": "{{migrationBase.availableRowActions}}"}, "actions": [{"content": "{{expenseIntegration.sync}}", "hidden": "@controller:hideApiActions", "disabled": "@controller:syncStatus()", "onClick": "@controller:sync"}, {"content": "{{migrationBase.upload}}", "hidden": "@controller:hideFileActions", "onClick": "@controller:upload"}, {"content": "{{migrationBase.viewSyncMapping}}", "hidden": "@controller:hideApiActions", "onClick": "@controller:viewApiMapping"}, {"content": "{{migrationBase.viewUploadMapping}}", "hidden": "@controller:hideFileActions", "onClick": "@controller:viewFileMapping"}, {"content": "Open Run Report", "hidden": "@controller:hideRunReportAction", "onClick": "@controller:openRunReport"}]}}}, {"component": "Table", "visible": "@controller:showTable('technicalBusinessDataExtractions')", "customId": "@controller:getTableCustomId('technicalMappingsBusinessDataTable')", "section": {"title": "{{expenseIntegration.transactionData}}", "grid": {"size": "12"}, "displayControl": {"hide": true}}, "props": {"data": "@binding:technicalBusinessDataExtractions", "variant": "white-borderless", "rowBackgroundGetter": "@controller:getRowBackgroundColor", "hideFilters": true, "columns": [{"headerName": "{{expenseIntegration.type}}", "field": "displayNamePlural", "pinned": "left", "autoSizeMinWidth": "md", "fieldProps": {"fontWeight": "bold"}, "onCellClicked": "@controller:goToApplication", "disableCellClick": "@controller:disableApplicationLink"}, {"headerName": "@controller:getTotalImportedHeader()", "field": "totalImported"}, {"headerName": "{{expenseIntegration.skipped}}", "field": "totalSkipped", "onCellClicked": "@controller:goToSkipped", "disableCellClick": "@controller:disableStagingLink"}, {"headerName": "{{expenseIntegration.failed}}", "field": "totalFailed", "onCellClicked": "@controller:goToFailed", "disableCellClick": "@controller:disableStagingLink"}, {"headerName": "{{expenseIntegration.drafted}}", "field": "totalDrafts", "onCellClicked": "@controller:goToDrafted", "disableCellClick": "@controller:disableStagingLink"}, {"headerName": "{{expenseIntegration.needsInteraction}}", "field": "totalNeedsInteraction", "onCellClicked": "@controller:goToNeedsInteraction", "disableCellClick": "@controller:disableStagingLink", "initialHide": "@controller:hideNeedsInteractionColumn()"}, {"headerName": "{{expenseIntegration.integrated}}", "field": "totalIntegrated", "onCellClicked": "@controller:goToIntegrated", "disableCellClick": "@controller:disableStagingLink"}, {"headerName": "Migration Type", "field": "migrationType", "fieldProps": {"fontWeight": "bold"}, "initialHide": true}, {"headerName": "{{expenseIntegration.lastSynced}}", "field": "lastSyncDate", "valueGetter": "@controller:getLastSyncValue", "initialHide": "@controller:hideLastSyncColumn()"}, {"sortable": false, "suppressColumnsToolPanel": true, "suppressFilter": true, "suppressFiltersToolPanel": true, "suppressMenu": true, "suppressMovable": true, "suppressSort": true, "valueGetter": "@controller:getDataSyncStatus", "headerName": "", "field": "status", "cellVariant": {"variant": "badge", "matchers": "@controller:getMatchers"}, "initialHide": "@controller:shouldHideDataSyncStatus('transaction')"}], "rowActions": {"variant": "button", "shape": "pill", "buttonLabel": "Actions", "columnPosition": -1, "tooltip": {"text": "{{migrationBase.availableRowActions}}"}, "actions": [{"content": "{{expenseIntegration.sync}}", "hidden": "@controller:hideApiActions", "disabled": "@controller:syncStatus()", "onClick": "@controller:sync"}, {"content": "{{migrationBase.upload}}", "hidden": "@controller:hideFileActions", "onClick": "@controller:upload"}, {"content": "{{migrationBase.viewSyncMapping}}", "hidden": "@controller:hideApiActions", "onClick": "@controller:viewApiMapping"}, {"content": "{{migrationBase.viewUploadMapping}}", "hidden": "@controller:hideFileActions", "onClick": "@controller:viewFileMapping"}, {"content": "Open Run Report", "hidden": "@controller:hideRunReportAction", "onClick": "@controller:openRunReport"}]}}}]}, {"title": "{{migrationBase.semanticMappings}}", "sections": [{"component": "Table", "visible": "@controller:showTable('semanticMetadataExtractions')", "customId": "@controller:getTableCustomId('semanticMappingsMasterDataTable')", "section": {"title": "{{expenseIntegration.masterData}}", "grid": {"size": "12"}, "displayControl": {"hide": true}}, "props": {"data": "@binding:semanticMetadataExtractions", "variant": "white-borderless", "hideFilters": true, "rowBackgroundGetter": "@controller:getRowBackgroundColor", "columns": [{"headerName": "Provider Name", "field": "providerName", "fieldProps": {"fontWeight": "bold"}, "initialHide": true}, {"headerName": "{{expenseIntegration.dataType}}", "field": "displayNamePlural", "pinned": "left", "autoSizeMinWidth": "md", "fieldProps": {"fontWeight": "bold"}, "onCellClicked": "@controller:goToApplication", "disableCellClick": "@controller:disableApplicationLink"}, {"headerName": "@controller:getTotalImportedHeader()", "field": "totalImported"}, {"headerName": "{{expenseIntegration.skipped}}", "field": "totalSkipped", "onCellClicked": "@controller:goToSkipped", "disableCellClick": "@controller:disableStagingLink"}, {"headerName": "{{expenseIntegration.mappedToEverest}}", "field": "totalMatched", "onCellClicked": "@controller:openMatchingModal", "disableCellClick": "@controller:disableStagingLink"}, {"headerName": "{{expenseIntegration.integrated}}", "field": "totalIntegrated", "initialHide": "@controller:hideExtraColumn()", "onCellClicked": "@controller:goToIntegrated", "disableCellClick": "@controller:disableStagingLink"}, {"valueGetter": "@controller:getDataSyncStatus", "headerName": "{{expenseIntegration.status}}", "field": "status", "cellVariant": {"variant": "badge", "matchers": "@controller:getMatchers"}}, {"headerName": "Migration Type", "field": "migrationType", "fieldProps": {"fontWeight": "bold"}, "initialHide": true}, {"headerName": "{{expenseIntegration.lastSynced}}", "field": "lastSyncDate", "valueGetter": "@controller:getLastSyncValue", "initialHide": "@controller:hideLastSyncColumn()"}], "rowActions": {"variant": "button", "columnPosition": -1, "shape": "pill", "buttonLabel": "Actions", "tooltip": {"text": "{{migrationBase.availableRowActions}}"}, "actions": [{"content": "{{expenseIntegration.sync}}", "hidden": "@controller:hideApiActions", "disabled": "@controller:syncStatus()", "onClick": "@controller:sync"}, {"content": "{{migrationBase.upload}}", "hidden": "@controller:hideFileActions", "onClick": "@controller:upload"}, {"content": "Edit Matchings", "onClick": "@controller:openMatchingModal", "disabled": "@controller:disableMatching"}, {"content": "Edit Matchings (Presentation Modal)", "onClick": "@controller:openNewMatchingModal", "disabled": "@controller:disableMatching"}, {"content": "{{migrationBase.viewSyncMapping}}", "hidden": "@controller:hideApiActions", "onClick": "@controller:viewApiMapping"}, {"content": "{{migrationBase.viewUploadMapping}}", "hidden": "@controller:hideFileActions", "onClick": "@controller:viewFileMapping"}, {"content": "Open Run Report", "hidden": "@controller:hideRunReportAction", "onClick": "@controller:openRunReport"}]}}}, {"component": "Table", "visible": "@controller:showTable('semanticBusinessDataExtractions')", "customId": "@controller:getTableCustomId('semanticMappingsBusinessDataTable')", "section": {"title": "{{expenseIntegration.transactionData}}", "grid": {"size": "12"}, "displayControl": {"hide": true}}, "props": {"data": "@binding:semanticBusinessDataExtractions", "variant": "white-borderless", "rowBackgroundGetter": "@controller:getRowBackgroundColor", "hideFilters": true, "columns": [{"headerName": "Provider Name", "field": "providerName", "fieldProps": {"fontWeight": "bold"}, "initialHide": true}, {"headerName": "{{expenseIntegration.dataType}}", "field": "displayNamePlural", "pinned": "left", "autoSizeMinWidth": "md", "fieldProps": {"fontWeight": "bold"}, "onCellClicked": "@controller:goToApplication", "disableCellClick": "@controller:disableApplicationLink"}, {"headerName": "@controller:getTotalImportedHeader()", "field": "totalImported"}, {"headerName": "{{expenseIntegration.skipped}}", "field": "totalSkipped"}, {"headerName": "{{expenseIntegration.mappedToEverest}}", "field": "totalIntegrated"}, {"valueGetter": "@controller:getDataSyncStatus", "headerName": "{{expenseIntegration.status}}", "field": "status", "cellVariant": {"variant": "badge", "matchers": "@controller:getMatchers"}}, {"headerName": "Migration Type", "field": "migrationType", "fieldProps": {"fontWeight": "bold"}, "initialHide": true}, {"headerName": "{{expenseIntegration.lastSynced}}", "field": "lastSyncDate", "valueGetter": "@controller:getLastSyncValue", "initialHide": "@controller:hideLastSyncColumn()"}], "rowActions": {"variant": "button", "columnPosition": -1, "shape": "pill", "buttonLabel": "Actions", "tooltip": {"text": "{{migrationBase.availableRowActions}}"}, "actions": [{"content": "{{expenseIntegration.sync}}", "hidden": "@controller:hideApiActions", "disabled": "@controller:syncStatus()", "onClick": "@controller:sync"}, {"content": "{{migrationBase.upload}}", "hidden": "@controller:hideFileActions", "onClick": "@controller:upload"}, {"content": "Edit Matchings", "onClick": "@controller:openMatchingModal", "disabled": "@controller:disableMatching"}, {"content": "{{migrationBase.viewSyncMapping}}", "hidden": "@controller:hideApiActions", "onClick": "@controller:viewApiMapping"}, {"content": "{{migrationBase.viewUploadMapping}}", "hidden": "@controller:hideFileActions", "onClick": "@controller:viewFileMapping"}, {"content": "Open Run Report", "hidden": "@controller:hideRunReportAction", "onClick": "@controller:openRunReport"}]}}}]}, {"title": "{{migrationBase.linking}}", "sections": [{"component": "Table", "customId": "@controller:getTableCustomId('table-4')", "section": {"editing": true, "grid": {"size": "12"}}, "props": {"data": "@binding:linkings", "variant": "white-borderless", "rowBackgroundGetter": "@controller:getRowBackgroundColor", "hideFilters": true, "addRows": true, "suppressDelete": true, "columns": [{"headerName": "{{migrationBase.sourceModel}}", "field": "sourceModel", "fieldProps": {"component": "Select", "label": "{{migrationBase.sourceModel}}", "idProp": "providerModel", "textProp": "displayName", "list": "@controller:getProviderModels()", "editing": "@controller:isLinkingDropdownDisabled"}}, {"headerName": "{{migrationBase.targetModel}}", "field": "targetModel", "fieldProps": {"component": "Select", "label": "{{migrationBase.targetModel}}", "idProp": "providerModel", "textProp": "displayName", "list": "@controller:getProviderModels()", "editing": "@controller:isLinkingDropdownDisabled"}}, {"headerName": "Linked Objects", "field": "totalLinkedObjects", "fieldProps": {"editing": false}}, {"headerName": "Unlinked Objects", "field": "totalUnlinkedObjects", "fieldProps": {"editing": false}}, {"headerName": "Last Executed", "field": "lastExecutionDate", "valueGetter": "@controller:getLastExecutionDate", "fieldProps": {"editing": false}}], "rowActions": {"variant": "button", "columnPosition": -1, "shape": "pill", "actions": [{"content": "Execute", "onClick": "@controller:performLinking"}, {"content": "Open Advance Linking", "onClick": "@controller:openAdvanceLinking", "visible": "@controller:isAdvanceLinkingVisible()"}, {"content": "Open Business Object Linking", "onClick": "@controller:openBusinessObjectLinking", "visible": "@controller:isBusinessObjectLinkingVisible()"}, {"content": "Open Manual Linking", "onClick": "@controller:openManualLinking", "visible": "@controller:isManualLinkingVisible()"}]}}}]}, {"title": "{{expenseIntegration.errors}}", "subtitle": "@controller:getErrorsStatus()", "visible": "@controller:hasErrors()", "sections": [{"component": "ActionGroup", "section": {"grid": {"size": "12"}}, "props": {"buttonActionsPosition": "right", "lines": "@controller:getErrors()"}}]}, {"title": "{{migrationBase.configuration}}", "visible": "@controller:showConfigurations()", "sections": [{"component": "ActionGroup", "customId": "configurationActionGroup", "section": {"grid": {"size": "12"}}, "props": {"buttonActionsPosition": "right", "lines": "@controller:getConfigurations()"}}]}]}}]}}}