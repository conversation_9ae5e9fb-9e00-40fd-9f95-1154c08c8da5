// @i18n:migrationBase
// @i18n:everest.fin.integration.expense/expenseIntegration
import { UILifecycleHooks } from '@everestsystems/content-core';
import { setUIModelInSharedState } from '@pkg/everest.base/public/utils/ui/sharedStateUtils.ui';
import { EvstExtractionSource } from '@pkg/everest.fin.integration.base/types/enums/ExtractionSource';
import { EvstStagingDataStatus } from '@pkg/everest.fin.integration.base/types/enums/StagingDataStatus';
import { EvstStagingStatus } from '@pkg/everest.fin.integration.base/types/enums/StagingStatus';
import type { EvstExternalProviderType } from '@pkg/everest.masterdatamgmt/types/primitives/ExternalProviderType';
import type {
  CustomConfig,
  ETLInfo,
  MappingOptions,
  SyncOrder,
} from '@pkg/everest.migration.base/public/config';
import { MigrationUI } from '@pkg/everest.migration.base/types/Migration.ui';
import { DataSyncTracking } from '@pkg/everest.migration.base/uinext/dataSyncTracking/dataSyncTracker.uicontroller';
import { isRunStatus } from '@pkg/everest.migration.base/uinext/dataSyncTracking/utils.uicontroller';

import type {
  ExpenseETLExecutionResponse,
  FetchingArgs,
  MappingArgs,
} from '../public/types';
import { MigrationType, ModelDataload } from '../public/types';
import type { OverviewUiTemplate } from '../types/uiTemplates/uinext/overview.ui';
import type { GetExtraction } from '../utils/overview/types';
import { ConnectorStatus, MappingStatus } from '../utils/overview/types';

type OverviewContext = OverviewUiTemplate.OverviewContext;

type TableProps = {
  rowData: GetExtraction;
  value: string;
};

type Context = OverviewContext & {
  state: {
    providerName: EvstExternalProviderType;
    taskPollingInterval?: ReturnType<typeof setTimeout>;
    isTestingConnection: boolean;
    connectionStatus: string;
  };
};

export function getTabTitle({ data }: Context) {
  return `Migration: ${data.config?.templates?.overview?.title}`;
}

UILifecycleHooks.onInit((context: Context) => {
  // Only executes when testing connection on overview is true
  if (shouldTestConnectionOnOverview(context)) {
    testConnection(context).catch(console.error);
  }
});

export async function testConnection({ data, actions, state }: Context) {
  state.connectionStatus = 'testing';
  state.isTestingConnection = true;

  try {
    const result = await actions.run({
      connector: {
        action: 'testConnection',
        data: {
          integrationName: data.connector?.configValues?.integrationName,
        },
      },
    });

    // Update the status based on the API response
    state.connectionStatus = result?.connector?.isConnected
      ? 'connected'
      : 'disconnected';
  } catch (error) {
    // Handle error case
    state.connectionStatus = 'error';

    console.error('Connection test failed:', error);
  } finally {
    // Always reset the testing state
    state.isTestingConnection = false;
  }
}

export function getConnectionStatusText(context: Context) {
  const { data } = context;

  if (shouldTestConnectionOnOverview(context)) {
    return getConnectionStatusMessage(context);
  }

  // Use the original function's logic for initial state
  if (
    !data.config?.executions?.metadata &&
    !data.config?.executions?.businessData
  ) {
    return;
  }

  const mappings = [
    ...data.config.executions.metadata,
    ...data.config.executions.businessData,
  ];
  const hasApiMappings = mappings.find((e) => e.mappings?.api);
  const hasCsvMappings = mappings.find((e) => e.mappings?.file);

  return hasApiMappings && data.connector?.id !== undefined
    ? '{{expenseIntegration.connected}}'
    : hasApiMappings
      ? '{{expenseIntegration.disconnected}}'
      : hasCsvMappings
        ? '{{migrationBase.fileUploadOnly}}'
        : '';
}

/**
 * Returns the appropriate connection status message based on current state
 * @returns {string} The translated status message
 */
function getConnectionStatusMessage({ state }: Context) {
  const message = {
    connected: '{{expenseIntegration.connected}}',
    disconnected: '{{expenseIntegration.disconnected}}',
    error: '{{expenseIntegration.connectionError}}',
    testingConnection: '{{expenseIntegration.testingConnection}}',
  };

  if (state.isTestingConnection) {
    return message.testingConnection;
  }

  return message[state.connectionStatus] || message.testingConnection;
}

export function shouldTestConnectionOnOverview({ data }: Context) {
  return Boolean(data?.config?.templates?.overview?.testConnectionOnOverview);
}

export function getIntegrationTitle({ data }: Context) {
  return data.config?.templates?.overview?.title ?? '';
}

// =========================================================
// Sidebar
// =========================================================

export function showEntity({ data }: Context) {
  return data.config?.templates?.overview?.hasOneEntityPerConnector ?? false;
}

export async function getEntityName({ data }: Context) {
  return data.connector?.entityName ?? '{{expenseIntegration.noEntityFound}}';
}

export function getUserName({ data }: Context) {
  return data.connector?.userName ?? ConnectorStatus.NoConnector;
}

export function getCreatedDate({ data }: Context) {
  return data.connector?.createdDate
    ? new Date(data.connector?.createdDate).toLocaleString('en-US')
    : ConnectorStatus.NoConnector;
}

export function getLastSyncDate({ data }: Context) {
  const syncDates = [];
  for (const extraction of data?.semanticMetadataExtractions ?? []) {
    if (extraction.lastSyncDate) {
      syncDates.push(new Date(extraction.lastSyncDate));
    }
  }
  for (const extraction of data?.technicalMetadataExtractions ?? []) {
    if (extraction.lastSyncDate) {
      syncDates.push(new Date(extraction.lastSyncDate));
    }
  }
  for (const extraction of data?.semanticBusinessDataExtractions ?? []) {
    if (extraction.lastSyncDate) {
      syncDates.push(new Date(extraction.lastSyncDate));
    }
  }
  for (const extraction of data?.technicalBusinessDataExtractions ?? []) {
    if (extraction.lastSyncDate) {
      syncDates.push(new Date(extraction.lastSyncDate));
    }
  }

  // Sort the array of sync dates in descending order
  syncDates.sort((date1, date2) =>
    date1 < date2 ? 1 : date1 > date2 ? -1 : 0
  );
  const lastSyncDate = syncDates?.[0];
  return lastSyncDate
    ? new Date(lastSyncDate).toLocaleString('en-US')
    : '{{expenseIntegration.never}}';
}

export function showSyncAll({ data }: Context) {
  return data.config?.templates?.overview?.syncOrder ? true : false;
}

function isInSync({ data }: Context) {
  return data.semanticMetadataExtractions?.every(
    (e) => e.status === MappingStatus.SyncInProgress
  );
}

export function syncStatus(ctx: Context) {
  return isInSync(ctx) || ctx.data.connector?.id === undefined;
}

export function editConnectionDetails({ helpers, actions, data }: Context) {
  helpers.openModal({
    title: '{{expenseIntegration.connectionDetails}}',
    template: data.config.templates?.overview?.editConnectorTemplateLink,
    initialState: {
      connector: data.connector,
    },
    onClose: async () => {
      await actions.refetchUiModelData();
    },
  });
}

export function showEditConnectionDetails(ctx: Context) {
  return getConnectionStatusText(ctx) !== '{{migrationBase.fileUploadOnly}}';
}

export function connectionStatus(ctx: Context) {
  return isInSync(ctx);
}

// =========================================================
// Configuration
// =========================================================

export function showConfigurations({ data }: Context) {
  return (
    data.config?.templates?.overview?.configurations?.base?.useDateRange ||
    data.config?.templates?.overview?.configurations?.custom?.length > 0
  );
}

export function getConfigurations(ctx: Context) {
  const { data, state } = ctx;

  const configs: {
    label: string;
    result: string;
    buttonActions: {
      shape: string;
      label: string;
      onClick: () => void;
      disabled?: boolean;
    }[];
  }[] = [];
  if (data.config?.templates?.overview?.configurations?.base?.useDateRange) {
    configs.push({
      label: '{{migrationBase.dateRange}}',
      result: dateRangeStatus(ctx),
      buttonActions: [
        {
          shape: 'pill',
          label: '{{expenseIntegration.manage}}',
          onClick: () =>
            openConfigModal(
              ctx,
              '{{expenseIntegration.titleStartDate}}'.replace(
                '$title$',
                data.config?.templates?.overview?.title
              ),
              '/templates/everest.migration.base/uinext/configurations/dateRange',
              {
                settingsId: data.settings?.settingsId,
                providerName: state.providerName,
                startDate: data.settings?.startDate,
                endDate: data.settings?.endDate,
              }
            ),
        },
      ],
    });
  }

  const customConfigs =
    data.config?.templates?.overview?.configurations?.custom?.map((c) => ({
      label: c.name,
      result: c.status,
      description: c.description,
      buttonActions: [
        {
          shape: 'pill',
          label: '{{expenseIntegration.manage}}',
          onClick: () =>
            openConfigModal(
              ctx,
              `${data.config?.templates?.overview?.title}: ${c.name}`,
              c.templateUrl,
              {
                connectorId: data.connector?.id,
                ...c.initialState,
              },
              c.size
            ),
        },
      ],
    }));
  if (customConfigs) {
    configs.push(...customConfigs);
  }

  return configs;
}

function openConfigModal(
  ctx: Context,
  title: string,
  templateUrl: string,
  initialState: Record<string, unknown>,
  size: CustomConfig['size'] = 'xsmall'
) {
  const { helpers, actions } = ctx;
  helpers.openModal({
    title,
    template: templateUrl,
    size,
    initialState,
    onClose: async () => {
      await actions.refetchUiModelData();
    },
  });
}

export function dateRangeStatus({ data }: Context): string {
  const { startDate, endDate } = data.settings ?? {};

  if (!startDate && !endDate) {
    return '{{expenseIntegration.notSet}}';
  }

  if (startDate && endDate) {
    return `${startDate} - ${endDate}`;
  } else if (startDate) {
    return `${startDate} - /`;
  } else if (endDate) {
    return `/ - ${endDate}`;
  }

  return '{{expenseIntegration.notSet}}';
}

export function hideExtraColumn({ data }: Context) {
  const currentMappings = [
    ...(data?.config?.executions?.metadata || []),
    ...(data?.config?.executions?.businessData || []),
  ];

  return !currentMappings.some((mapping) =>
    checkTypeOfMapping(mapping.mappings)
  );
}

export function hideLastSyncColumn({ data }: Context) {
  /** by default we hide the last sync column */
  return data?.config?.templates?.overview?.showLastSyncColumn
    ? !data?.config?.templates?.overview?.showLastSyncColumn
    : true;
}

export function hideNeedsInteractionColumn({ data }: Context) {
  /** by default we hide the Needs Interaction column */
  return data?.config?.templates?.overview?.showNeedsInteractionColumn
    ? !data?.config?.templates?.overview?.showNeedsInteractionColumn
    : true;
}

export function getLastSyncValue(
  _ctx: Context,
  rowData: { data: { lastSyncDate: Date } }
) {
  return rowData.data.lastSyncDate
    ? new Date(rowData.data.lastSyncDate.toString()).toLocaleString('en-US')
    : '{{expenseIntegration.never}}';
}

function checkTypeOfMapping(mappings: {
  api?: MappingOptions;
  file?: MappingOptions;
}): boolean {
  if (mappings?.api && mappings?.file) {
    return (
      (mappings.api.type?.semantic &&
        (mappings.api.type?.technical || mappings.file.type?.technical)) ||
      (mappings.file.type?.semantic &&
        (mappings.api.type?.technical || mappings.file.type?.technical))
    );
  } else if (mappings?.api) {
    return mappings.api.type?.semantic && mappings.api.type?.technical;
  } else if (mappings?.file) {
    return mappings.file.type?.semantic && mappings.file.type?.technical;
  }
}

// =========================================================
// Extractions
// =========================================================

export function showTable({ data }: Context, binding: string) {
  return data[binding]?.length > 0;
}

export function getTotalImportedHeader({ data }: Context) {
  return '{{expenseIntegration.objectsIn}}'.replace(
    '$title$',
    data.config?.templates?.overview?.title
  );
}

export function getMatchers(_: Context, { value, rowData }: TableProps) {
  const { status } = rowData;

  if (isRunStatus(value)) {
    return 'havelock-blue';
  }

  switch (status) {
    case MappingStatus.Mapped: {
      return 'shamrock';
    }
    case MappingStatus.MappingIncomplete: {
      return 'orchid';
    }
    case MappingStatus.NoMapping: {
      return 'amaranth';
    }
    case MappingStatus.NotSynced: {
      return 'grey';
    }
    case MappingStatus.SyncInProgress: {
      return 'jaffa';
    }
    case MappingStatus.ConfirmMapping: {
      return 'orchid';
    }
  }
}

function updateMetadataExtractionStatus(
  ctx: Context,
  status?: MappingStatus,
  rowData?: Record<'data', GetExtraction>
) {
  const { data } = ctx;

  if (rowData) {
    const {
      _nodeReference,
      lastSyncDate,
      status: calculatedStatus,
    } = rowData.data;

    let rowStatus = MappingStatus.NotSynced;
    if (status) {
      rowStatus = status;
    } else if (lastSyncDate) {
      rowStatus = calculatedStatus;
    }
    setUIModelInSharedState(
      ctx,
      'semanticMetadataExtractions',
      'status',
      rowStatus,
      _nodeReference
    );
    return;
  }

  for (const e of data.semanticMetadataExtractions) {
    const { _nodeReference, lastSyncDate, status: calculatedStatus } = e;
    let rowStatus = MappingStatus.NotSynced;
    if (status) {
      rowStatus = status;
    } else if (lastSyncDate) {
      rowStatus = calculatedStatus;
    }
    setUIModelInSharedState(
      ctx,
      'semanticMetadataExtractions',
      'status',
      rowStatus,
      _nodeReference
    );
  }
}

export function findMapping(
  ctx: Context,
  rowData: GetExtraction,
  extractionSource?: EvstExtractionSource
) {
  const { data } = ctx;
  const mapping = [
    ...data.config.executions.metadata,
    ...data.config.executions.businessData,
  ].find(
    (m) =>
      m.providerModel === rowData?.providerModel &&
      (rowData.migrationType === MigrationType.Semantic
        ? extractionSource === EvstExtractionSource.API
          ? m.mappings.api?.type?.semantic
          : extractionSource === EvstExtractionSource.File
            ? m.mappings.file?.type?.semantic
            : m.mappings.api?.type?.semantic || m.mappings.file?.type?.semantic
        : extractionSource === EvstExtractionSource.API
          ? m.mappings.api?.type?.technical
          : extractionSource === EvstExtractionSource.File
            ? m.mappings.file?.type?.technical
            : m.mappings.api?.type?.technical ||
              m.mappings.file?.type?.technical)
  );
  return mapping;
}

export function hideApiActions(
  ctx: Context,
  { rowData }: Record<'rowData', GetExtraction>
) {
  return findMapping(ctx, rowData, EvstExtractionSource.API) ? false : true;
}

export function hideFileActions(
  ctx: Context,
  { rowData }: Record<'rowData', GetExtraction>
) {
  return findMapping(ctx, rowData, EvstExtractionSource.File) ? false : true;
}

async function getMappingId(ctx: Context, name: string, subject: string) {
  const res = await ctx.actions.run({
    mappings: {
      action: 'query',
      data: { where: { name, subject } },
    },
  });
  return res?.mappings?.[0]?.id;
}

export async function viewApiMapping(
  ctx: Context,
  rowData: Record<'data', GetExtraction>
) {
  await viewMapping(ctx, rowData, EvstExtractionSource.API);
}

export async function viewFileMapping(
  ctx: Context,
  rowData: Record<'data', GetExtraction>
) {
  await viewMapping(ctx, rowData, EvstExtractionSource.File);
}

export async function viewMapping(
  ctx: Context,
  rowData: Record<'data', GetExtraction>,
  extractionSource: EvstExtractionSource
) {
  const { helpers, data } = ctx;

  const m = findMapping(ctx, rowData?.data, extractionSource);
  const providerName = data?.config?.general?.providerName;
  const mappingName =
    extractionSource === EvstExtractionSource.API
      ? m?.mappings?.api && m?.mappings?.api?.name
      : m?.mappings?.file && m?.mappings?.file?.name;

  const mappingId = await getMappingId(ctx, mappingName, providerName);
  helpers.navigate({ to: `/mapper?id=${mappingId}` });
}

export async function upload(
  ctx: Context,
  rowData: Record<'data', GetExtraction>
) {
  const { data, helpers } = ctx;
  const mapping = findMapping(ctx, rowData.data, EvstExtractionSource.File);
  const { defaultValues } = mapping;
  const providerName = data?.config?.general?.providerName;

  if (defaultValues) {
    const mappingId = await getMappingId(
      ctx,
      mapping.mappings?.file?.name,
      providerName
    );

    if (!mappingId) {
      showErrorNotification(helpers, '{{migrationBase.errorMapping}}');
      return;
    }

    helpers.openModal({
      title: 'Maintain default values',
      size: 'small',
      template: `/templates/everest.migration.base/defaultFields/maintainDefaultValues?feat-delta=true&feat-inspect=true`,
      initialState: {
        defaultFieldsModal:
          rowData.data.migrationType === MigrationType.Semantic
            ? defaultValues.semanticDefaultFieldsModal
            : defaultValues.technicalDefaultFieldsModal,
      },
      onModalSubmit: async (defaultValues) => {
        await MigrationUI.updateDefaultValueInMapping(ctx, {
          mappingId: mappingId,
          defaultValues,
        }).run('migration');

        await openUploadModal(ctx, rowData);
      },
    });
  } else {
    await openUploadModal(ctx, rowData);
  }
}

export async function openUploadModal(
  ctx: Context,
  rowData: Record<'data', GetExtraction>
) {
  const { data, helpers, actions } = ctx;

  const { displayName } = [
    ...data.config.executions.metadata,
    ...data.config.executions.businessData,
  ].find((m) => m.providerModel === rowData?.data?.providerModel);

  const dataSyncTracking = await ctx.uiLoader.getSingletonInstanceOf({
    type: DataSyncTracking,
  });

  const runTrackingKey = dataSyncTracking.getRunTrackingKey(rowData.data);

  helpers.openModal({
    title: `Import ${displayName} from CSV`,
    template: `/templates/everest.migration.base/uinext/upload`,
    size: 'large',
    initialState: {
      mapping: findMapping(ctx, rowData.data, EvstExtractionSource.File),
      migrationType: rowData.data.migrationType,
      startDate: data.settings?.startDate,
      endDate: data.settings?.endDate,
      providerName: data.config?.general?.providerName,
      runTrackingKey,
      sessionId: data?.connector?.sessionId,
    },
    onModalSubmit: async () => {
      await actions.refetchUiModelData();
    },
  });
}

export async function syncData(
  ctx: Context,
  mapping: MappingArgs,
  fetching: FetchingArgs
) {
  return MigrationUI.syncData(ctx, {
    mapping,
    fetching,
  }).run('migration');
}

export async function sync(
  ctx: Context,
  rowData: Record<'data', GetExtraction>
) {
  const { state, data, helpers, actions } = ctx;

  const mapping = findMapping(ctx, rowData.data, EvstExtractionSource.API);

  const { defaultValues, enableExpertDataMode } = mapping;
  const providerName = data?.config?.general?.providerName;
  const providerModel = rowData?.data?.providerModel;
  const defaultFieldsModal =
    rowData.data.migrationType === MigrationType.Semantic
      ? defaultValues?.semanticDefaultFieldsModal
      : defaultValues?.technicalDefaultFieldsModal;
  let syncRes;

  if (
    !data.config?.templates?.overview?.testConnector &&
    !validateSyncRequirements(ctx, mapping.mappings?.api?.name, providerName)
  ) {
    return;
  }

  if (defaultValues && defaultFieldsModal) {
    const mappingId = await getMappingId(
      ctx,
      mapping.mappings?.api?.name,
      providerName
    );

    if (!mappingId) {
      showErrorNotification(helpers, '{{migrationBase.errorMapping}}');
      return;
    }

    helpers.openModal({
      title: 'Maintain default values',
      size: 'medium',
      template: `/templates/everest.migration.base/defaultFields/maintainDefaultValues?feat-delta=true&feat-inspect=true`,
      initialState: {
        defaultFieldsModal,
      },
      onModalSubmit: async (defaultValues) => {
        await MigrationUI.updateDefaultValueInMapping(ctx, {
          mappingId: mappingId,
          defaultValues,
        }).run('migration');
        syncRes = await performSync(ctx, mapping, rowData, state, data);
        await validateSyncResult(
          ctx,
          syncRes,
          mapping,
          rowData?.data?.migrationType,
          rowData
        );
      },
    });
  } else if (
    enableExpertDataMode?.[rowData?.data?.migrationType?.toLowerCase()] &&
    !enableExpertDataMode?.[rowData?.data?.migrationType?.toLowerCase()].linking
  ) {
    helpers.navigate({
      to: `/templates/everest.migration.base/uinext/dataBrowser/dataBrowser?providerModel=${providerModel}&providerName=${providerName}&feat-delta=true&feat-inspect=true`,
      // This is hardcoded but will be updated in the next MR
      initialState: {
        model:
          enableExpertDataMode?.[rowData?.data?.migrationType?.toLowerCase()]
            .modelUrn,
        entryPointFunctionName:
          enableExpertDataMode?.[rowData?.data?.migrationType?.toLowerCase()]
            .entryPointFunctionName,
        migrationMode: rowData?.data?.migrationType?.toLowerCase(),
      },
    });
  } else {
    syncRes = await performSync(ctx, mapping, rowData, state, data);
    await validateSyncResult(
      ctx,
      syncRes,
      mapping,
      rowData?.data?.migrationType,
      rowData
    );
  }

  updateMetadataExtractionStatus(ctx, undefined, rowData);
  await actions.refetchUiModelData();
}

export async function openAdvanceLinking(ctx: Context) {
  const { data, helpers, actions } = ctx;
  const sourceModel = data?.linkings?.[0]?.sourceModel;
  const targetModel = data?.linkings?.[0]?.targetModel;

  const requiredData = data?.technicalBusinessDataExtractions?.find((row) => {
    return row.providerModel === sourceModel;
  });
  const mappingData = data?.config?.executions?.businessData?.find((row) => {
    return row.providerModel === sourceModel;
  });

  const targetMappingData = data?.config?.executions?.businessData?.find(
    (row) => {
      return row.providerModel === targetModel;
    }
  );

  const { defaultValues } = targetMappingData;

  const { enableExpertDataMode } = mappingData;

  if (
    enableExpertDataMode?.[requiredData?.migrationType?.toLowerCase()] &&
    enableExpertDataMode?.[requiredData?.migrationType?.toLowerCase()].linking
  ) {
    const defaultFilters =
      defaultValues['semanticDefaultFieldsModal'] ??
      defaultValues['technicalDefaultFieldsModal'];
    if (defaultFilters) {
      helpers.openModal({
        title: 'Provide filter values',
        size: 'medium',
        template: `/templates/everest.migration.base/defaultFields/maintainDefaultValues?feat-delta=true&feat-inspect=true`,
        initialState: {
          defaultFieldsModal: defaultFilters,
        },
        onModalSubmit: async (defaultValues) => {
          await actions.refetchUiModelData();
          helpers.navigate({
            to: `/templates/everest.migration.base/uinext/dataBrowser/dataBrowser?providerModel=${sourceModel}&providerName=${requiredData?.providerName}&feat-delta=true&feat-inspect=true`,
            queryParams: {
              linkingModel: targetModel,
            },
            initialState: {
              model:
                enableExpertDataMode?.[
                  requiredData?.migrationType?.toLowerCase()
                ].modelUrn,
              entryPointFunctionName:
                enableExpertDataMode?.[
                  requiredData?.migrationType?.toLowerCase()
                ].entryPointFunctionName,
              migrationMode: requiredData?.migrationType?.toLowerCase(),
              filterConfig: defaultValues,
            },
          });
        },
      });
    } else {
      helpers.navigate({
        to: `/templates/everest.migration.base/uinext/dataBrowser/dataBrowser?providerModel=${sourceModel}&providerName=${requiredData?.providerName}&feat-delta=true&feat-inspect=true`,
        queryParams: {
          linkingModel: targetModel,
        },
        initialState: {
          model:
            enableExpertDataMode?.[requiredData?.migrationType?.toLowerCase()]
              .modelUrn,
          entryPointFunctionName:
            enableExpertDataMode?.[requiredData?.migrationType?.toLowerCase()]
              .entryPointFunctionName,
          migrationMode: requiredData?.migrationType?.toLowerCase(),
        },
      });
    }
  }
}

function validateSyncRequirements(
  ctx: Context,
  mappingName: string | undefined,
  providerName: string | undefined
): boolean {
  const { data, helpers } = ctx;

  if (!mappingName || !providerName) {
    showErrorNotification(helpers, '{{migrationBase.errorMappingAndProvider}}');
    return false;
  }

  if (!data.connector || !data.connector.id) {
    showErrorNotification(helpers, '{{migrationBase.errorNoConnector}}');
    return false;
  }

  return true;
}

function showErrorNotification(helpers: Context['helpers'], message: string) {
  helpers.showNotificationMessage({
    key: 'loading',
    type: 'error',
    message,
    duration: 4,
  });
}

async function performSync(
  ctx: Context,
  mapping: ReturnType<typeof findMapping>,
  rowData: Record<'data', GetExtraction>,
  state: Context['state'],
  data: Context['data']
) {
  const dataSyncTracking = await ctx.uiLoader.getSingletonInstanceOf({
    type: DataSyncTracking,
  });

  const runTrackingKey = dataSyncTracking.getRunTrackingKey(rowData.data);
  return syncData(
    ctx,
    {
      ...mapping,
      providerName: state.providerName,
      entityId: data.connector?.entityId,
      runTrackingKey,
    },
    {
      dataload: ModelDataload.Single,
      migrationType: rowData.data.migrationType,
      connectorId: data.connector?.id,
      configValues: data.connector?.configValues,
      sessionId: data.connector?.sessionId,
      startDate: data.settings?.startDate,
      endDate: data.settings?.endDate,
      extractionUUID: ctx.helpers.uuid(),
    }
  );
}

export async function validateSyncResult(
  ctx: Context,
  syncRes: {
    migration?: Awaited<ExpenseETLExecutionResponse>;
    error?: { message: string };
  },
  mapping: ETLInfo,
  migrationType: MigrationType,
  /** optional rowData as in case of syncAll we don't have a specific rowData */
  rowData?: Record<'data', GetExtraction>
) {
  const { helpers, data, state, actions } = ctx;
  if (syncRes?.error) {
    helpers.showNotificationMessage({
      key: 'loading',
      type: 'error',
      message: '{{migrationBase.failedToSyncModel}}'
        .replace('$model$', mapping.displayNamePlural)
        .replace('$errorMessage', syncRes?.error?.message),
      duration: 4,
    });
  } else if (syncRes?.migration?.errors?.length) {
    helpers.showNotificationMessage({
      key: 'loading',
      type: 'error',
      message: syncRes.migration.errors.map((e) => e.message).join('\n'),
      duration: 4,
    });
  }
  // Dataload detected. Move to background job.
  else if (syncRes?.migration?.continueInTask) {
    let runTrackingKey: string;
    if (rowData) {
      /** optional rowData as in case of syncAll we don't have a specific rowData */
      const dataSyncTracking = await ctx.uiLoader.getSingletonInstanceOf({
        type: DataSyncTracking,
      });
      runTrackingKey = dataSyncTracking.getRunTrackingKey(rowData.data);
    }

    const jobRes = await MigrationUI.createDataloadTask(ctx, {
      mapping: {
        ...mapping,
        providerName: state.providerName,
        currentProviderModel: mapping.providerModel,
        entityId: data.connector?.entityId,
        runTrackingKey,
      },
      fetching: {
        dataload: ModelDataload.All,
        migrationType,
        connectorId: data.connector?.id,
        configValues: data.connector?.configValues,
        sessionId: data.connector?.sessionId,
        startDate: data.settings?.startDate,
        endDate: data.settings?.endDate,
        extractionUUID: ctx.helpers.uuid(),
      },
    }).run('migration');
    if (jobRes?.error) {
      helpers.showNotificationMessage({
        key: 'loading',
        type: 'error',
        message: '{{migrationBase.failedToCreateTask}}'.replace(
          '$errorMessage$',
          jobRes?.error?.message
        ),
        duration: 4,
      });
    } else if (jobRes?.migration?.jobId) {
      helpers.showNotificationMessage({
        key: 'loading',
        type: 'success',
        message: 'Integration continues in background due to high data volume',
        duration: 2,
      });
    }
  } else if (
    syncRes.migration?.backgroundJobId ||
    syncRes.migration?.jobId ||
    syncRes.migration?.hasRunningSubTasks
  ) {
    helpers.showNotificationMessage({
      key: 'loading',
      type: 'info',
      message: 'Integration continues in background due to high data volume',
      duration: 2,
    });
  } else {
    helpers.showNotificationMessage({
      key: 'loading',
      type: 'success',
      message: 'Data successfully loaded into Everest',
      duration: 2,
    });
  }
  updateMetadataExtractionStatus(ctx);
  await actions.refetchUiModelData();
}

export async function syncAll(ctx: Context) {
  const { state, data, actions } = ctx;

  const mappings = [
    ...data.config.executions.metadata,
    ...data.config.executions.businessData,
  ];
  const migrationOrder = data?.config?.templates?.overview?.syncOrder;
  const sortedMappings = sortMappings(mappings, migrationOrder);

  let migrationIndex = 0;
  for (const mapping of sortedMappings) {
    const migrationType = migrationOrder[migrationIndex].migrationType;
    const mappingArgs: MappingArgs = {
      ...mapping,
      providerName: state.providerName,
      entityId: data.connector?.entityId,
      // TODO: add runTrackingKey
    };
    const fetchingArgs: FetchingArgs = {
      dataload: ModelDataload.Single,
      migrationType,
      connectorId: data.connector?.id,
      configValues: data.connector?.configValues,
      sessionId: data.connector?.sessionId,
      startDate: data.settings?.startDate,
      endDate: data.settings?.endDate,
      extractionUUID: ctx.helpers.uuid(),
    };
    const syncRes = await syncData(ctx, mappingArgs, fetchingArgs);
    await validateSyncResult(ctx, syncRes, mapping, migrationType);
    migrationIndex++;
  }

  updateMetadataExtractionStatus(ctx);
  await actions.refetchUiModelData();
}

// Sort mappings based on provided order
function sortMappings(
  mappings: ETLInfo[],
  migrationOrder: SyncOrder[]
): ETLInfo[] {
  const sortedMappings = [];
  for (const migrationInfo of migrationOrder) {
    const selectedMapping = mappings.find(
      (m) =>
        m.providerModel === migrationInfo.providerModel &&
        (migrationInfo.migrationType === MigrationType.Semantic
          ? m.mappings.api?.type?.semantic
          : m.mappings.api?.type?.technical)
    );
    sortedMappings.push(selectedMapping);
  }
  return sortedMappings;
}

export function disableMatching({ data }: Context) {
  return data.semanticMetadataExtractions
    .map((e) => e.status)
    .every((status) => status === MappingStatus.SyncInProgress);
}

export function openNewMatchingModal(
  ctx: Context,
  rowData: Record<'data', GetExtraction>
) {
  const { helpers, actions, data } = ctx;
  const execution = data.config.executions.metadata.find(
    (m) => m.providerModel === rowData.data.providerModel
  );
  const extractions =
    rowData.data.migrationType === MigrationType.Semantic
      ? data.semanticMetadataExtractions
      : data.technicalMetadataExtractions;
  const extraction = extractions.find(
    (e) => e.providerModel === rowData.data.providerModel
  );

  const mapping = findMapping(ctx, rowData.data);
  const { defaultValues } = mapping;

  if (defaultValues) {
    helpers.openModal({
      title: 'Maintain default values',
      size: 'medium',
      template: `/templates/everest.migration.base/defaultFields/maintainDefaultValues?feat-delta=true&feat-inspect=true`,
      initialState: {
        defaultFieldsModal: defaultValues?.semanticDefaultFieldsModal,
      },
      onModalSubmit: async (defaultValues) => {
        await actions.refetchUiModelData();
        helpers.openModal({
          title: `${execution?.displayName} Mapping`,
          template: '/templates/everest.migration.base/uinext/match/match',
          size: 'large',

          initialState: {
            mapping: mapping,
            providerName: extraction.providerName,
            providerModel: extraction.providerModel,
            entityId: data.connector?.entityId,
            filterConfig: defaultValues,
            migrationType: rowData.data.migrationType,
            startDate: data.settings?.startDate,
            endDate: data.settings?.endDate,
          },
          onModalSubmit: actions.refetchUiModelData,
        });
      },
    });
  } else {
    helpers.openModal({
      title: `${execution?.displayName} Mapping`,
      template: '/templates/everest.migration.base/uinext/match/match',
      size: 'large',
      initialState: {
        mapping: mapping,
        providerName: extraction.providerName,
        providerModel: extraction.providerModel,
        entityId: data.connector?.entityId,
      },
      onModalSubmit: async (count) => {
        // const { _nodeReference } = data.semanticMetadataExtractions[0];
        const { _nodeReference } = extraction;
        if (count === 0) {
          setUIModelInSharedState(
            ctx,
            'semanticMetadataExtractions',
            'status',
            MappingStatus.Mapped,
            _nodeReference
          );
        }
        if (count !== 0) {
          setUIModelInSharedState(
            ctx,
            'semanticMetadataExtractions',
            'status',
            MappingStatus.ConfirmMapping,
            _nodeReference
          );
        }
        await actions.refetchUiModelData();
      },
    });
  }
}

export function openMatchingModal(
  ctx: Context,
  rowData: Record<'data', GetExtraction>
) {
  const { helpers, actions, data } = ctx;
  const execution = data.config.executions.metadata.find(
    (m) => m.providerModel === rowData.data.providerModel
  );
  const extractions =
    rowData.data.migrationType === MigrationType.Semantic
      ? data.semanticMetadataExtractions
      : data.technicalMetadataExtractions;
  const extraction = extractions.find(
    (e) => e.providerModel === rowData.data.providerModel
  );

  const mapping = findMapping(ctx, rowData.data);
  const { defaultValues } = mapping;

  if (defaultValues) {
    helpers.openModal({
      title: 'Maintain default values',
      size: 'medium',
      template: `/templates/everest.migration.base/defaultFields/maintainDefaultValues?feat-delta=true&feat-inspect=true`,
      initialState: {
        defaultFieldsModal: defaultValues?.semanticDefaultFieldsModal,
      },
      onModalSubmit: async (defaultValues) => {
        await actions.refetchUiModelData();
        helpers.openModal({
          title: `${execution?.displayName} Mapping`,
          template: '/templates/everest.migration.base/uinext/match?mode=view',
          size: 'large',
          initialState: {
            mapping: mapping,
            title: data.config?.templates?.overview?.title,
            extraction,
            connectorId: data.connector.id,
            entityId: data.connector?.entityId,
            filterConfig: defaultValues,
            migrationType: rowData.data.migrationType,
            startDate: data.settings?.startDate,
            endDate: data.settings?.endDate,
          },
          onModalSubmit: actions.refetchUiModelData,
        });
      },
    });
  } else {
    helpers.openModal({
      title: `${execution?.displayName} Mapping`,
      template: '/templates/everest.migration.base/uinext/match?mode=view',
      size: 'large',
      initialState: {
        mapping: mapping,
        title: data.config?.templates?.overview?.title,
        extraction,
        connectorId: data.connector.id,
        entityId: data.connector?.entityId,
        filterConfig: defaultValues,
      },
      onModalSubmit: async (count) => {
        // const { _nodeReference } = data.semanticMetadataExtractions[0];
        const { _nodeReference } = extraction;
        if (count === 0) {
          setUIModelInSharedState(
            ctx,
            'semanticMetadataExtractions',
            'status',
            MappingStatus.Mapped,
            _nodeReference
          );
        }
        if (count !== 0) {
          setUIModelInSharedState(
            ctx,
            'semanticMetadataExtractions',
            'status',
            MappingStatus.ConfirmMapping,
            _nodeReference
          );
        }
        await actions.refetchUiModelData();
      },
    });
  }
}

export function goToApplication(
  { data, helpers }: Context,
  rowData: Record<'data', Record<'providerModel', string>>
) {
  const { applicationUrl } = [
    ...data.config.executions.metadata,
    ...data.config.executions.businessData,
  ].find((m) => m.providerModel === rowData?.data?.providerModel);
  helpers.navigate({
    to: applicationUrl,
  });
}

export function disableApplicationLink(
  { data }: Context,
  row: {
    column: string;
    rowData: Record<'providerModel', string>;
  }
) {
  const { applicationUrl } = [
    ...data.config.executions.metadata,
    ...data.config.executions.businessData,
  ].find((m) => m.providerModel === row?.rowData?.providerModel);
  return applicationUrl ? false : true;
}

export function goToSkipped(
  ctx: Context,
  rowData: Record<'data', GetExtraction>
) {
  goToStaging(ctx, rowData.data, EvstStagingStatus.Skipped);
}

export function goToFailed(
  ctx: Context,
  rowData: Record<'data', GetExtraction>
) {
  goToStaging(ctx, rowData.data, EvstStagingStatus.FAILED);
}

export function goToDrafted(
  ctx: Context,
  rowData: Record<'data', GetExtraction>
) {
  goToStaging(ctx, rowData.data, EvstStagingStatus.Drafted);
}

export function goToNeedsInteraction(
  ctx: Context,
  rowData: Record<'data', GetExtraction>
) {
  goToStaging(ctx, rowData.data, EvstStagingStatus.NeedsInteraction);
}

export function goToIntegrated(
  ctx: Context,
  rowData: Record<'data', GetExtraction>
) {
  goToStaging(ctx, rowData.data, EvstStagingStatus.Integrated);
}

function goToStaging(
  ctx: Context,
  extractionData: GetExtraction,
  status: EvstStagingStatus
) {
  const { helpers, state, data } = ctx;

  const providerModel = extractionData.providerModel;

  const nodeParams = {
    staging: {
      filters: {
        providerName: {
          $in: [state.providerName],
        },
        providerModel: {
          $eq: providerModel,
        },
        status: {
          $in: [status],
        },
        dataStatus: {
          $in: [EvstStagingDataStatus.Latest],
        },
      },
    },
  };

  if (isBusinessUserStagingViewEnabled(ctx)) {
    const mapping = findMapping(ctx, extractionData);
    const mappingName =
      mapping?.mappings?.api?.name ?? mapping?.mappings?.file?.name;
    const sessionId = data?.connector?.sessionId;

    helpers.navigate({
      to: '/templates/everest.migration.base/uinext/simpleStaging/listStaging',
      nodeParams,
      initialState: {
        providerName: state.providerName,
        providerModel,
        mapping: mappingName,
        session: sessionId,
      },
    });
  } else {
    helpers.navigate({
      to: '/templates/everest.fin.integration.base/staging/uinext/list',
      nodeParams,
    });
  }
}

export function disableStagingLink(
  _ctx: Context,
  row: {
    column: Record<'field', string>;
    rowData: Record<
      'totalSkipped' | 'totalFailed' | 'totalIntegrated' | 'totalMatched',
      number
    >;
  }
) {
  return row?.rowData?.[row?.column?.field] ? false : true;
}

// =========================================================
// Linking
// =========================================================

export async function getProviderModels({ data }: Context) {
  if (data.config === undefined) {
    return;
  }

  const executions = [
    ...data.config.executions.metadata,
    ...data.config.executions.businessData,
  ];
  if (executions.length === 0) {
    return;
  }

  const models = [];
  for (const m of executions) {
    const { providerModel, displayName, everestModel } = m;
    models.push({
      providerModel,
      displayName,
      everestModel,
    });
  }
  return models;
}

export async function performLinking(
  ctx: Context,
  {
    nodeInstance,
  }: Record<
    'nodeInstance',
    Record<'data', Record<'sourceModel' | 'targetModel', string>>
  >
) {
  const {
    state: { providerName },
    data,
    helpers,
  } = ctx;
  const {
    data: { sourceModel, targetModel },
  } = nodeInstance;
  if (!sourceModel || !targetModel) {
    helpers.showNotificationMessage({
      key: 'loading',
      type: 'error',
      message: 'Provide source and target model for linking',
      duration: 4,
    });
    return;
  }

  const settingsId = data.settings?.settingsId;
  const linkingRes = await MigrationUI.performLinking(ctx, {
    providerName,
    sourceModel,
    targetModel,
    settingsId,
  }).run('migration');
  if (
    linkingRes?.migration &&
    typeof linkingRes.migration === 'object' &&
    'continueInTask' in linkingRes.migration
  ) {
    helpers.showNotificationMessage({
      key: 'loading',
      type: 'success',
      message: 'Linking will be executed in a background task',
      duration: 3,
    });
    return;
  }

  if (!linkingRes?.error) {
    helpers.showNotificationMessage({
      key: 'loading',
      type: 'success',
      message: 'Data successfully loaded into Everest',
      duration: 2,
    });
    return;
  }
}

export async function openBusinessObjectLinking(
  ctx: Context,
  {
    nodeInstance,
  }: Record<
    'nodeInstance',
    Record<'data', Record<'sourceModel' | 'targetModel', string>>
  >
) {
  const { helpers, state } = ctx;

  const {
    data: { sourceModel, targetModel },
  } = nodeInstance;

  if (!sourceModel || !targetModel) {
    helpers.showNotificationMessage({
      key: 'loading',
      type: 'error',
      message: 'Provide source and target model for linking',
      duration: 4,
    });

    return;
  }

  const sourceUrn = getModelUrnByProviderModel(ctx, sourceModel);
  const targetUrn = getModelUrnByProviderModel(ctx, targetModel);

  helpers.openModal({
    title: `Link ${sourceModel} to ${targetModel}`,
    size: 'medium',
    template: `/templates/everest.migration.base/uinext/linking/businessObjectLinking/businessObjectLinking?feat-delta=true&feat-inspect=true`,
    initialState: {
      sourceUrn,
      targetUrn,
      sourceModelName: sourceModel,
      targetModelName: targetModel,
      providerName: state.providerName,
    },
  });
}

export async function openManualLinking(
  ctx: Context,
  {
    nodeInstance,
  }: Record<
    'nodeInstance',
    Record<'data', Record<'sourceModel' | 'targetModel', string>>
  >
) {
  const { helpers, state } = ctx;

  const {
    data: { sourceModel, targetModel },
  } = nodeInstance;

  if (!sourceModel || !targetModel) {
    helpers.showNotificationMessage({
      key: 'loading',
      type: 'error',
      message: 'Provide source and target model for linking',
      duration: 4,
    });

    return;
  }
  helpers.openModal({
    title: `Link ${sourceModel} to ${targetModel}`,
    size: 'medium',
    template: `/templates/everest.migration.base/uinext/linking/manualLinking/manualLinking?feat-delta=true&feat-inspect=true`,
    initialState: {
      sourceModel,
      targetModel,
      providerName: state.providerName,
    },
  });
}

export function getModelUrnByProviderModel(
  ctx: Context,
  providerModel: string
) {
  const { data } = ctx;

  const mappings = [
    ...data.config.executions.metadata,
    ...data.config.executions.businessData,
  ];

  return mappings.find((mapping) => mapping.providerModel === providerModel)
    ?.everestModel;
}

export function isBusinessObjectLinkingVisible(ctx: Context) {
  const { data } = ctx;

  return Boolean(data.config?.general?.enableSimpleLink);
}

export function isAdvanceLinkingVisible(ctx: Context) {
  const { data } = ctx;

  return Boolean(data.config?.general?.enableAdvanceLink);
}

export function isManualLinkingVisible(ctx: Context) {
  const { data } = ctx;

  return Boolean(data.config?.general?.enableManualLink);
}

export function isBusinessUserStagingViewEnabled(ctx: Context) {
  const { data } = ctx;

  return Boolean(data.config?.general?.enableBusinessUserStagingView);
}

// =========================================================
// Errors
// =========================================================

export function hasErrors({ data }: Context) {
  if (
    !data.semanticMetadataExtractions &&
    !data.semanticBusinessDataExtractions &&
    !data.technicalMetadataExtractions &&
    !data.technicalBusinessDataExtractions
  ) {
    return;
  }
  return [
    ...data.semanticMetadataExtractions,
    ...data.semanticBusinessDataExtractions,
    ...data.technicalMetadataExtractions,
    ...data.technicalBusinessDataExtractions,
  ].some((e) => e.totalFailed > 0);
}

export function getErrorsStatus({ data }: Context) {
  if (
    !data.semanticMetadataExtractions &&
    !data.semanticBusinessDataExtractions &&
    !data.technicalMetadataExtractions &&
    !data.technicalBusinessDataExtractions
  ) {
    return;
  }
  const failedExtractions = [
    ...data.semanticMetadataExtractions,
    ...data.semanticBusinessDataExtractions,
    ...data.technicalMetadataExtractions,
    ...data.technicalBusinessDataExtractions,
  ].filter((e) => e.totalFailed > 0)?.length;
  return '{{expenseIntegration.toResolve}}'.replace(
    '$num$',
    failedExtractions.toString()
  );
}

export function getErrors(ctx: Context): Error[] {
  const errorConfigs = [
    {
      key: 'semanticMetadataExtractions',
      errorMessage: '{{expenseIntegration.unmapped}}',
      action: openMatchingModal,
    },
    {
      key: 'semanticBusinessDataExtractions',
      errorMessage: '{{expenseIntegration.unmapped}}',
      action: openMatchingModal,
    },
    {
      key: 'technicalMetadataExtractions',
      errorMessage: '{{expenseIntegration.failedToIntegrate}}',
      action: goToFailed,
    },
    {
      key: 'technicalBusinessDataExtractions',
      errorMessage: '{{expenseIntegration.failedToIntegrate}}',
      action: goToFailed,
    },
  ];

  return errorConfigs.flatMap(({ key, errorMessage, action }) => {
    const extractions = ctx.data[key] as GetExtraction[] | undefined;
    return extractions
      ? getErrorsInner(
          ctx,
          extractions.filter((e) => e.totalFailed > 0),
          errorMessage,
          action
        )
      : [];
  });
}

function getErrorsInner(
  ctx: Context,
  extractions: GetExtraction[],
  description: string,
  review: (
    ctx: Context,
    rowData: Record<'data', Record<'providerModel', string>>
  ) => void
) {
  const errors = [];
  for (const e of extractions) {
    errors.push({
      status: 'invalid',
      label: e.displayNamePlural,
      description: description.replace('$num$', e.totalFailed.toString()),
      buttonActions: [
        {
          shape: 'pill',
          label: '{{expenseIntegration.review}}',
          onClick: () =>
            review(ctx, { data: { providerModel: e.providerModel } }),
          disabled: disableMatching(ctx),
        },
      ],
    });
  }
  return errors;
}

// =========================================================
// Polling
// =========================================================

export async function getTaskInformation(ctx: Context) {
  const { state, data } = ctx;
  if (state.taskPollingInterval) {
    return;
  }

  if (data?.tasks?.hasRunningTask) {
    updateMetadataExtractionStatus(ctx, MappingStatus.SyncInProgress);
    poll(ctx);
  }
  return;
}

function poll(ctx: Context) {
  const { state } = ctx;

  state.taskPollingInterval = setInterval(() => {
    MigrationUI.getTaskInformation(ctx, {
      providerName: state.providerName,
    })
      .run('migration')
      .then((response) => {
        if (response?.migration?.hasRunningTask) {
          triggerRefetch(ctx);
        }
      })
      .catch(() => {
        clearInterval(state.taskPollingInterval);
        state.taskPollingInterval = undefined;
      })
      .finally(() => {
        clearInterval(state.taskPollingInterval);
        state.taskPollingInterval = undefined;
        updateMetadataExtractionStatus(ctx);
        triggerRefetch(ctx);
      });
  }, 5000);
}

function triggerRefetch({ actions }: Context) {
  actions
    .refetchUiModelData()
    .then(() => ({}))
    .catch(() => ({}));
}

export function getLastExecutionDate(
  _ctx: Context,
  rowData: { data: { lastExecutionDate: Date } }
) {
  return rowData?.data?.lastExecutionDate
    ? new Date(rowData.data.lastExecutionDate.toString()).toLocaleString(
        'en-US'
      )
    : '{{expenseIntegration.never}}';
}

export function isLinkingDropdownDisabled(
  _ctx: Context,
  rowData: { rowData: { executed: boolean } }
) {
  return !rowData?.rowData?.executed;
}
