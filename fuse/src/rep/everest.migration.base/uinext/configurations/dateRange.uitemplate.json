{"version": 2, "uicontroller": "dateRange.uicontroller.ts", "uimodel": {"nodes": {"settings": {"type": "struct", "modelId": "everest.migration.base/MigrationSettingsModel.MigrationSettings"}}}, "uiview": {"templateType": "list", "i18n": ["everest.fin.integration.expense/expenseIntegration", "migrationBase"], "grid": {"size": "12"}, "actions": {"content": [{"variant": "secondary", "label": "{{expenseIntegration.cancel}}", "onClick": "@controller:cancel"}, {"variant": "primary", "label": "{{expenseIntegration.save}}", "onClick": "@controller:save"}]}, "sections": {"content": [{"component": "Block", "size": "11", "variant": "primary", "customId": "<PERSON><PERSON><PERSON><PERSON>", "elements": [{"component": "DatePicker", "name": "startDate", "label": "{{expenseIntegration.startDate}}", "parseAs": "date", "format": "MM/dd/yyyy", "isEditing": true, "value": "@state:startDate"}]}, {"component": "Block", "size": "11", "variant": "primary", "customId": "<PERSON><PERSON><PERSON><PERSON>", "elements": [{"component": "DatePicker", "name": "endDate", "label": "{{migrationBase.endDate}}", "parseAs": "date", "format": "MM/dd/yyyy", "isEditing": true, "value": "@state:endDate"}]}]}}}