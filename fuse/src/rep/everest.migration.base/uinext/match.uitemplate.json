{"version": 2, "uicontroller": "match.uicontroller.ts", "uimodel": {"nodes": {"expense": {"type": "struct", "modelId": "everest.migration.base/MigrationModel.Migration"}, "staging": {"type": "struct", "modelId": "everest.fin.integration.base/StagingModel.Staging"}, "getMatches": {"type": "list", "modelId": "everest.migration.base/MigrationModel.Migration", "pagination": 35, "getMatches": {"everestModel": "@state:extraction.everestModel", "providerName": "@state:extraction.providerName", "providerModel": "@controller:getProviderModel()", "filterData": "@state:showUnmappedData", "filterConfig": "@state:filterConfig"}}, "getMatchingCandidates": {"type": "list", "modelId": "everest.migration.base/MigrationModel.Migration", "getMatchingCandidates": {"providerName": "@state:extraction.providerName", "providerModel": "@controller:getProviderModel()", "dependencies": {"entityId": "@state:entityId", "connectorId": "@state:connectorId"}, "filterConfig": "@state:filterConfig"}}}}, "uiview": {"templateType": "list", "i18n": "everest.fin.integration.expense/expenseIntegration", "config": {"allowRefreshData": true, "autoRefreshData": true}, "grid": {"size": "12"}, "actions": {"content": [{"variant": "secondary", "label": "{{expenseIntegration.cancel}}", "onClick": "@controller:cancel"}, {"variant": "primary", "label": "{{expenseIntegration.save}}", "onClick": "@controller:save"}]}, "sections": {"content": [{"component": "Table", "customId": "matchingTable", "props": {"data": "@binding:getMatches", "variant": "dark", "suppressDelete": true, "addRows": false, "hideFilters": true, "columns": "@controller:getColumns()"}, "section": {"editing": true, "grid": {"size": "12"}, "displayControl": {"hide": true}, "isEditing": true, "actions": [{"label": "Actions", "variant": "secondary", "actions": [{"label": "Import Mapping", "onClick": "@controller:importMapping", "disabled": "@controller:disableImport()"}, {"label": "Export Mapping", "onClick": "@controller:exportMapping"}, {"label": "Auto Match AI", "onClick": "@controller:autoMatchAI"}]}], "header": {"secondaryContent": [{"component": "Switch", "props": {"direction": "horizontal-reverse", "label": "{{expenseIntegration.showUnmappedOnly}}", "onChange": "@controller:updateData", "isEditing": true}}]}}}]}}}