{"version": 2, "uicontroller": "upload.uicontroller.ts", "uimodel": {"nodes": {"expense": {"type": "struct", "modelId": "everest.migration.base/MigrationModel.Migration"}, "upload": {"type": "struct", "modelId": "everest.appserver/UploadModel.Upload"}}}, "uiview": {"templateType": "list", "i18n": "everest.fin.integration.expense/expenseIntegration", "config": {"allowRefreshData": true, "autoRefreshData": true}, "grid": {"size": "12"}, "actions": {"content": [{"variant": "secondary", "label": "{{expenseIntegration.cancel}}", "onClick": "@controller:cancel"}, {"variant": "primary", "label": "{{expenseIntegration.import}}", "onClick": "@controller:importFile"}]}, "sections": {"content": [{"component": "FieldGroup", "section": {"title": "{{expenseIntegration.selectFile}}", "grid": {"start": "3", "end": "11"}}, "props": {"variant": "secondary", "elements": [{"component": "Upload", "version": 2, "isEditing": true, "multiple": true, "name": "file", "uploadHandler": "postgres", "everestPackage": "everest.integration.expense", "acceptableMimeTypes": ["application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "text/csv"], "maxSizeInMB": 20, "attachmentFiles": "@controller:getAttachments()"}]}}]}}}