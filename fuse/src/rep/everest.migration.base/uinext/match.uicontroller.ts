// @i18n:everest.fin.integration.expense/expenseIntegration
// import { UILifecycleHooks } from '@everestsystems/content-core';
import type { PlainDate } from '@everestsystems/datetime';
import { setUIModelInSharedState } from '@pkg/everest.base/public/utils/ui/sharedStateUtils.ui';
import type { EvstMatchingResult } from '@pkg/everest.fin.integration.base/types/composites/MatchingResult';
import { StagingUI } from '@pkg/everest.fin.integration.base/types/Staging.ui';
import type { MatchingInfo } from '@pkg/everest.migration.base/public/config';
import { MatchingCandidatesFilter } from '@pkg/everest.migration.base/public/matchingCandidatesFilter';
import type {
  FormattedMatch,
  MatchingCandidate,
  MigrationType,
} from '@pkg/everest.migration.base/public/types';
import { MigrationUI } from '@pkg/everest.migration.base/types/Migration.ui';
import type { GetExtraction } from '@pkg/everest.migration.base/utils/overview/types';

import type { MatchUiTemplate } from '../types/uiTemplates/uinext/match.ui';

type MatchContext = MatchUiTemplate.MatchContext;

type Context = MatchContext & {
  state: {
    matches: Map<string, number>;
    mapping: MatchingInfo;
    title: string;
    extraction: GetExtraction;
    showUnmappedData: boolean;
    migrationType: MigrationType;
    startDate?: PlainDate;
    endDate?: PlainDate;
    entityId?: number;
    filterConfig?: Record<string, unknown>;
  };
};

type ProcessCellForExportParams = {
  value: string | number;
  column: {
    colId: string;
  };
};

// To be revised later for further investigation regarding the status
// UILifecycleHooks.onInit((context: Context) => {
//   const { state, data } = context;
//   state.matches = new Map();
//   for (const match of data.getMatches) {
//     state.matches.set(match.originalId, match?.everestId);
//   }
// });

export function getProviderModel({ state }: Context) {
  return state.mapping?.providerModel;
}

export function disableImport({ state }: Context) {
  return state.mapping?.mappings?.file ? true : false;
}

export async function importMapping({ state, helpers, sections }: Context) {
  const table = sections.matchingTable;
  const columns = (table.getColumns() as Record<'headerName', string>[]).map(
    (c) => c.headerName
  );

  const {
    mapping,
    extraction: { providerName },
    entityId,
    migrationType,
    startDate,
    endDate,
  } = state;
  helpers.closeModal();
  helpers.openModal({
    title: 'Import Mapping from CSV',
    template: `/templates/everest.migration.base/uinext/upload`,
    size: 'large',
    initialState: {
      mapping,
      providerName,
      entityId,
      columns,
      migrationType,
      startDate,
      endDate,
    },
    onModalSubmit: () => {
      // Run callback immediately and move the closing process to the next tick
      helpers.currentModalSubmitCallback();
      setTimeout(() => helpers.closeModal(), 10);
    },
  });
}

export function exportMapping(ctx: Context) {
  const { state, sections, data } = ctx;
  const table = sections.matchingTable;

  const matchingCandidates = data.getMatchingCandidates;

  const columnKeys = ['originalId', 'name', 'everestId'];
  const additionalColumnKeys = state.mapping?.exportOptions?.additionalFields;
  if (additionalColumnKeys) {
    columnKeys.splice(1, 0, ...additionalColumnKeys.map((f) => f.field));
  }

  table.exportDataAsCsv({
    fileName: `${state.providerName} ${state.mapping.providerModel} Export`,
    columnKeys,
    processCellCallback: (params: ProcessCellForExportParams) => {
      if (params.column.colId !== 'everestId') {
        return params.value;
      }
      return matchingCandidates.find((c) => c.id === params.value)?.name;
    },
  });
}

export function updateData({ state }: Context, currentValue: boolean) {
  state.showUnmappedData = currentValue;
}

export function cancel({ helpers }: Context) {
  helpers.closeModal();
}

export async function save(ctx: Context) {
  const { state, helpers, data } = ctx;
  if (state.matches) {
    const matches: EvstMatchingResult[] = [];
    for (const [originalId, everestId] of state.matches.entries()) {
      matches.push({
        originalId,
        everestNodeReference: {
          id: everestId,
          modelUrn: state.extraction.everestModel,
        },
      });
    }

    await StagingUI.rematchStagingItems(ctx, {
      args: {
        providerName: state.extraction.providerName,
        providerModel: state.mapping?.providerModel,
        matches,
      },
    }).run('staging');
  }
  const unmatchedCount = data?.getMatches?.filter((item) => !item?.everestId)
    ?.length;

  helpers.currentModalSubmitCallback(unmatchedCount);
  helpers.closeModal();
}

export function getColumns(ctx: Context) {
  const columns = [
    {
      headerName: `${ctx.state.title} Original Id`,
      field: 'originalId',
      fieldProps: {
        editing: false,
      },
      initialHide: true,
    },
    {
      headerName: getHeaderName(ctx, ctx.state.title),
      field: 'name',
      fieldProps: {
        editing: false,
      },
    },
    {
      headerName: getHeaderName(ctx, 'Everest', true),
      field: 'everestId',
      fieldProps: {
        component: 'Select',
        idProp: 'id',
        textProp: 'name',
        isEditing: true,
        list: (data: Record<'rowData', Record<string, unknown>>) =>
          getMatchingCandidates(ctx, data),
        onChange: (
          value: number,
          data: Record<'rowData', Record<string, unknown>>
        ) => onMatching(ctx, value, data),
      },
    },
  ];
  const additionalFields = ctx.state.mapping?.exportOptions?.additionalFields;
  if (additionalFields) {
    const additionalColumns = additionalFields.map((c) => {
      return {
        headerName: `${ctx.state?.title} ${c.headerName}`,
        field: c.field,
        fieldProps: { editing: false },
        initialHide: true,
      };
    });
    columns.splice(1, 0, ...additionalColumns);
  }
  return columns;
}

function getHeaderName({ state }: Context, prefix: string, required?: boolean) {
  const suffix = required ? ' *' : '';
  return `${prefix} ${state.mapping?.displayName}${suffix}`;
}

async function getMatchingCandidates(
  ctx: Context,
  { rowData }: Record<'rowData', Record<string, unknown>>
) {
  const { data, state, uiLoader } = ctx;
  const candidates = data.getMatchingCandidates;

  if (!state.mapping?.filterCandidates) {
    return candidates;
  }

  const matchingFilter = await uiLoader.getSingletonInstanceOf({
    type: MatchingCandidatesFilter,
    extensionFiles: [state.mapping.filterCandidates.filePath],
  });
  return matchingFilter.filter(
    { ...rowData },
    candidates as unknown as MatchingCandidate[]
  );
}

export function onMatching({ state }: Context, value: number, { rowData }) {
  if (value === undefined) {
    return;
  }
  if (state.matches === undefined) {
    state.matches = new Map();
  }
  state.matches.set(rowData.originalId as string, value);
}

export async function autoMatchAI(ctx: Context) {
  const { data, state, helpers } = ctx;

  if (data.getMatches.length === 0) {
    helpers.showNotificationMessage({
      key: 'loading',
      type: 'error',
      message: '{{expenseIntegration.noStagingItemsFound}}'.replace(
        '$model$',
        state.mapping.displayNamePlural
      ),
      duration: 4,
    });
    return;
  } else if (data.getMatchingCandidates.length === 0) {
    helpers.showNotificationMessage({
      key: 'loading',
      type: 'error',
      message: '{{expenseIntegration.noMatchingCandidatesFound}}',
      duration: 4,
    });
    return;
  }

  helpers.showNotificationMessage({
    key: 'loading',
    type: 'loading',
    message: '{{expenseIntegration.autoMatchProgress}}',
    duration: 0,
  });

  const res = await MigrationUI.autoMatch(ctx, {
    providerName: state.extraction.providerName,
    mapping: state.mapping,
    source: data.getMatches as unknown as FormattedMatch[],
    target: data.getMatchingCandidates as unknown as MatchingCandidate[],
  }).run('expense');
  if (res.error) {
    helpers.showNotificationMessage({
      key: 'loading',
      type: 'error',
      message: `${res.error.message}`,
      duration: 4,
    });
    return;
  }
  const autoMatches = res.expense;
  if (!state.matches) {
    state.matches = new Map();
  }
  for (const m of autoMatches) {
    state.matches.set(m.originalId, m.everestId);
  }

  // Update table
  for (const m of data.getMatches) {
    const { _nodeReference, originalId } = m;
    const everestId = state.matches.get(originalId);
    if (everestId) {
      setUIModelInSharedState(
        ctx,
        'getMatches',
        'everestId',
        everestId,
        _nodeReference
      );
    }
  }

  helpers.showNotificationMessage({
    key: 'loading',
    type: 'success',
    message: '{{expenseIntegration.autoMatchSuccessful}}',
    duration: 2,
  });
}
