{"version": 2, "uicontroller": "paymentDetailsConfigurationModal.uicontroller.ts", "uimodel": {"nodes": {"configurationSettings": {"type": "struct", "modelId": "everest.fin.integration.gusto/GustoSettingsModel.GustoSettings", "query": {"where": {"connectorId": "@state:connectorId"}}, "fieldList": ["id", "configurationSettings"]}, "accounts": {"type": "list", "getEnabledAccounts": "@controller:getParametersForGetAccounts()", "modelId": "everest.fin.accounting/AccountModel.Account"}, "entities": {"type": "list", "modelId": "everest.base/EntityModel.Entity", "queryActiveEntities": {"query": {}, "fieldList": ["entityName", "id", "currency"]}}, "vendors": {"type": "list", "query": {"where": {"status": "active"}, "orderBy": [{"field": "vendorNumber", "ordering": "desc"}]}, "modelId": "everest.fin.expense/VendorModel.Vendor", "fieldList": ["id", "vendorName"]}, "payees": {"type": "list", "query": {}, "modelId": "everest.fin.expense/PayrollCategoryPayeeModel.PayrollCategoryPayee", "fieldList": ["id", "code", "label"]}, "categoryAccountsNetPay": {"modelId": "everest.fin.expense/PayrollCategoryModel.PayrollCategory", "type": "list", "getPayrollCategoryMatchedAccountsByEntity": "@controller:getCategoryMatchedAccountsQuery()"}, "categoryAccountsTaxPay": {"modelId": "everest.fin.expense/PayrollCategoryModel.PayrollCategory", "type": "list", "getPayrollCategoryMatchedAccountsByEntity": "@controller:getCategoryMatchedAccountsQuery2()"}, "integrationSession": {"type": "struct", "modelId": "everest.fin.integration.base/IntegrationSessionModel.IntegrationSession", "query": {"where": {"providerName": "@controller:getSessionProviderName()"}}, "fieldList": ["id"]}, "sessionVariables": {"type": "list", "modelId": "everest.fin.integration.base/SessionVariablesModel.SessionVariables", "parent": "integrationSession", "joinKey": "id-sessionId", "fieldList": ["config<PERSON><PERSON>", "config<PERSON><PERSON><PERSON>"]}}}, "uiview": {"i18n": ["everest.connectorengine/connectorEngine", "gusto"], "title": "{{connectorEngine.create}}", "sections": [{"component": "FieldGroup", "size": "12", "type": "primary", "title": "{{gusto.netPayConfig.title}}", "visible": "@controller:isNetPaySettingsVisible()", "columns": 4, "elements": [{"component": "Select", "label": "{{gusto.vendor}} *", "idProp": "id", "textProp": "vendorName", "value": "@controller:getSelectedValueForNetPay('vendorId')", "isEditing": true, "list": "@binding:vendors", "onChange": "@controller:selectNetPayVendor"}, {"component": "Select", "label": "{{gusto.payeeType}} *", "idProp": "id", "textProp": "label", "value": "@controller:getSelectedValueForNetPay('payeeId')", "isEditing": true, "list": "@binding:payees", "onChange": "@controller:selectNetPayPayee"}, {"component": "Select", "label": "{{gusto.account}} *", "mode": "multiple", "idProp": "id", "prefixProp": "accountNumber", "textProp": "accountName", "name": "categoryAccountsNetPay", "value": "@controller:getSelectedValueForNetPay('accountId')", "list": "@binding:categoryAccountsNetPay", "isEditing": true, "onChange": "@controller:selectNetPayAccount"}, {"component": "Select", "label": "{{gusto.paymentAccount}} *", "idProp": "id", "prefixProp": "accountNumber", "textProp": "accountName", "isEditing": true, "list": "@controller:getAccountList()", "value": "@controller:getSelectedValueForNetPay('paymentAccountId')", "onChange": "@controller:selectNetPayPaymentAccount"}]}, {"component": "FieldGroup", "size": "12", "title": "{{gusto.taxPayConfig.title}}", "visible": "@controller:isTaxPaySettingsVisible()", "elements": [{"component": "Select", "label": "{{gusto.vendor}} *", "idProp": "id", "textProp": "vendorName", "value": "@controller:getSelectedValueForTaxPay('vendorId')", "isEditing": true, "list": "@binding:vendors", "onChange": "@controller:selectTaxPayVendor"}, {"component": "Select", "label": "{{gusto.payeeType}} *", "idProp": "id", "textProp": "label", "value": "@controller:getSelectedValueForTaxPay('payeeId')", "isEditing": true, "list": "@binding:payees", "onChange": "@controller:selectTaxPayPayee"}, {"component": "Select", "label": "{{gusto.account}} *", "name": "categoryAccountsTaxPay", "mode": "multiple", "idProp": "id", "prefixProp": "accountNumber", "textProp": "accountName", "value": "@controller:getSelectedValueForTaxPay('accountId')", "list": "@binding:categoryAccountsTaxPay", "isEditing": true, "onChange": "@controller:selectTaxPayAccount"}, {"component": "Select", "label": "{{gusto.paymentAccount}} *", "idProp": "id", "prefixProp": "accountNumber", "textProp": "accountName", "isEditing": true, "list": "@controller:getAccountList()", "value": "@controller:getSelectedValueForTaxPay('paymentAccountId')", "onChange": "@controller:selectTaxPayPaymentAccount"}]}, {"component": "ButtonGroup", "direction": "horizontal", "actions": [{"variant": "primary", "label": "{{connectorEngine.save}}", "onClick": "@controller:save", "disabled": "@controller:isSaveButtonDisabled()"}, {"variant": "secondary", "label": "{{gusto.cancel}}", "onClick": "@controller:cancel"}]}]}}