import { type ISession, METADATA } from '@everestsystems/content-core';
import { getTranslations } from '@everestsystems/content-core';
import { EvstExtractionProviders } from '@pkg/everest.fin.integration.base/types/enums/ExtractionProviders';
import {
  ENTITY_ID,
  IS_AUTHENTICATED,
  IS_DISCONNECTED,
  IS_SANDBOX,
  PAYROLL_TEMPLATE_ID,
} from '@pkg/everest.fin.integration.gusto/public/config';
import type { connectorPresentation } from '@pkg/everest.fin.integration.gusto/types/presentations/uinext/connector';
import {
  BaseCache,
  EntityIdNamesCache,
  PayrollTemplateIdNamesCache,
  SessionNameConfigValuesCache,
} from '@pkg/everest.fin.integration.gusto/utils/common/cacheClasses';
import { executeAsyncOperation } from '@pkg/everest.fin.integration.gusto/utils/common/utils';
import { authenticate } from '@pkg/everest.fin.integration.gusto/utils/connector/authenticate';
import { disconnect } from '@pkg/everest.fin.integration.gusto/utils/connector/disconnect';
import { upsertConnector } from '@pkg/everest.fin.integration.gusto/utils/connector/upsertConnector';
import type { GustoSessionVariables } from '@pkg/everest.fin.integration.gusto/utils/types';
import { isEmpty, set, unset } from 'lodash';

import { getTokenSet } from '../utils/connector/getTokenSet';
import { testConnector } from '../utils/connector/testConnector';

class TranslationsCache extends BaseCache<string, string> {
  public async init(args: { session: ISession; bypassCaching?: boolean }) {
    const { session, bypassCaching = false } = args;

    if (this.centralProperty === undefined || bypassCaching) {
      const connectorEngineTranslations = await getTranslations(
        session,
        [
          'connectorEngine.reconnect',
          'connectorEngine.connect',
          'connectorEngine.create',
          'connectorEngine.test',
          'connectorEngine.disconnect',
          'connectorEngine.save',
        ],
        'everest.connectorengine/connectorEngine'
      );
      const gustoTranslations = await getTranslations(
        session,
        ['gusto.testConnection.title'],
        'everest.fin.integration.gusto/gusto'
      );

      this.centralProperty = new Map<string, string>([
        ...[
          'connectorEngine.reconnect',
          'connectorEngine.connect',
          'connectorEngine.create',
          'connectorEngine.test',
          'connectorEngine.disconnect',
          'connectorEngine.save',
        ].map((key, index): [string, string] => [
          key,
          connectorEngineTranslations[index],
        ]),
        ...['gusto.testConnection.title'].map(
          (key, index): [string, string] => [key, gustoTranslations[index]]
        ),
      ]);
    }
  }

  public get translations() {
    return this.centralProperty;
  }

  public getTranslationByName(value: string) {
    return this.centralProperty?.get(value);
  }
}

class ConnectorDataSource
  implements connectorPresentation.dataSources.connectorData.implementation
{
  private connectorData:
    | connectorPresentation.dataSources.connectorData.levels['']
    | undefined = undefined;
  private sessionNameConfigValuesCache = new SessionNameConfigValuesCache();
  private entityIdNamesCache = new EntityIdNamesCache();
  private payrollTemplateIdNamesCache = new PayrollTemplateIdNamesCache();
  private translationsCache = new TranslationsCache();

  public async query({
    session,
    parameters,
  }: connectorPresentation.dataSources.connectorData.callbacks.query.input): Promise<connectorPresentation.dataSources.connectorData.callbacks.query.output> {
    if (this.connectorData === undefined) {
      // Call order is important in here. Be careful!
      await this.initCaches({ session, parameters });
      const formValues = await this.getInitialFormValues({ parameters });
      const formFlags = await this.getInitialFormFlags({ parameters });
      const formLabels = this.getInitialFormLabels();

      this.connectorData = {
        ...formValues,
        ...formFlags,
        ...formLabels,
      };
    }

    return {
      ...this.connectorData,
      [METADATA]: {
        mappedPayrollTemplate: {
          typeParameters: {
            entityId: this.connectorData.mappedEntity,
          },
        },
      },
    };
  }

  async update({ fieldName, newFieldValue }) {
    if (fieldName === 'mappedEntity') {
      unset(this.connectorData, 'mappedPayrollTemplate');
    }

    set(this.connectorData, [fieldName], newFieldValue);
  }

  public async execute_test({
    session,
    parameters,
  }: connectorPresentation.dataSources.connectorData.routines.test.executeInput): Promise<connectorPresentation.dataSources.connectorData.routines.test.executeOutput> {
    const operationResponse = await executeAsyncOperation(
      async () => await testConnector(session, parameters.connector.id),
      'Connection OK'
    );

    return {
      ...operationResponse,
      response: (
        operationResponse.response as { testConnectionResponse: string }
      ).testConnectionResponse,
    };
  }

  public async validate_connect({
    context,
  }: connectorPresentation.dataSources.connectorData.routines.connect.validateInput): Promise<connectorPresentation.dataSources.connectorData.routines.connect.validateOutput> {
    if (!this.connectorData.name) {
      context.addError([], 'Missing name', 'name');
    }
    if (!this.connectorData.mappedEntity) {
      context.addError([], 'Missing mappedEntity', 'mappedEntity');
    }
    if (!this.connectorData.mappedPayrollTemplate) {
      context.addError(
        [],
        'Missing mappedPayrollTemplate',
        'mappedPayrollTemplate'
      );
    }
  }

  public async execute_connect({
    session,
    parameters,
  }: connectorPresentation.dataSources.connectorData.routines.connect.executeInput): Promise<connectorPresentation.dataSources.connectorData.routines.connect.executeOutput> {
    // We call the upsert first in order to apply the form values in case something has changed.
    await this.upsertConnector({ session, parameters });

    const result = {
      isTokenRefreshed: false,
      authenticationExecuted: false,
    };
    const isConnectorAuthenticated = this.isConnectorAuthenticated();

    if (isConnectorAuthenticated) {
      const authResponse = await authenticate(session, parameters.connector.id);
      await this.sessionNameConfigValuesCache.resetCache();

      if (authResponse.isRefreshed) {
        result.isTokenRefreshed = true;
      }
      result.authenticationExecuted = true;
    }

    this.connectorData = undefined;
    return result;
  }

  public async validate_disconnect(
    _input: connectorPresentation.dataSources.connectorData.routines.disconnect.validateInput
  ): Promise<connectorPresentation.dataSources.connectorData.routines.disconnect.validateOutput> {
    return this.isConnectorAuthenticated();
  }

  public async execute_disconnect({
    session,
    parameters,
  }: connectorPresentation.dataSources.connectorData.routines.disconnect.executeInput): Promise<connectorPresentation.dataSources.connectorData.routines.disconnect.executeOutput> {
    return await executeAsyncOperation(
      async () => await disconnect(session, parameters.connector.id),
      'Successfully disconnected from Gusto',
      'Failed to disconnect from Gusto'
    );
  }

  public async validate_save({
    context,
  }: connectorPresentation.dataSources.connectorData.routines.save.validateInput): Promise<connectorPresentation.dataSources.connectorData.routines.save.validateOutput> {
    if (!this.connectorData.name) {
      context.addError([], 'Missing name', 'name');
    }
    if (!this.connectorData.mappedEntity) {
      context.addError([], 'Missing mappedEntity', 'mappedEntity');
    }
    if (!this.connectorData.mappedPayrollTemplate) {
      context.addError(
        [],
        'Missing mappedPayrollTemplate',
        'mappedPayrollTemplate'
      );
    }
  }

  public async execute_save({
    session,
    parameters,
  }: connectorPresentation.dataSources.connectorData.routines.save.executeInput): Promise<connectorPresentation.dataSources.connectorData.routines.save.executeOutput> {
    await this.upsertConnector({ session, parameters });
  }

  private async upsertConnector({
    session,
    parameters,
  }: Pick<
    connectorPresentation.dataSources.connectorData.routines.connect.executeInput,
    'session' | 'parameters'
  >) {
    const isAuthenticated = this.isConnectorAuthenticated();
    const isDisconnected = this.isConnectorDisconnected();

    await upsertConnector(
      session,
      {
        payrollTemplateId: this.connectorData.mappedPayrollTemplate,
        entityId: this.connectorData.mappedEntity,
        isSandbox: this.connectorData.isSandbox,
        name: this.connectorData.name,
        isAuthenticated,
        isDisconnected,
      },
      {
        connectorId: parameters.connector?.id,
        integrationSessionId: parameters.connector?.sessionId,
      }
    );
    await this.sessionNameConfigValuesCache.resetCache();
  }

  private isScopeDisabled({
    parameters,
  }: Pick<
    connectorPresentation.dataSources.connectorData.callbacks.query.input,
    'parameters'
  >) {
    return parameters.connector ? true : false;
  }

  private getEntityIdFromSessionVariables():
    | GustoSessionVariables['entityId']
    | undefined {
    const sessionVariableValue =
      this.sessionNameConfigValuesCache.findSessionVariableByName(ENTITY_ID);
    if (!sessionVariableValue) {
      return undefined;
    }

    const entityIdNames = this.entityIdNamesCache.entityIdNames;
    const entityName = entityIdNames.get(Number(sessionVariableValue));
    if (!entityName) {
      return undefined;
    }

    return Number(sessionVariableValue);
  }

  private getSelectedPayrollTemplateId():
    | GustoSessionVariables['payrollTemplateId']
    | undefined {
    const sessionVariableValue =
      this.sessionNameConfigValuesCache.findSessionVariableByName(
        PAYROLL_TEMPLATE_ID
      );
    if (!sessionVariableValue) {
      return undefined;
    }

    const payrollTemplateIdNames =
      this.payrollTemplateIdNamesCache.payrollTemplateIdNames;
    const name = payrollTemplateIdNames.get(Number(sessionVariableValue));
    if (!name) {
      return undefined;
    }

    return Number(sessionVariableValue);
  }

  private getSelectedIsSandbox():
    | GustoSessionVariables['isSandbox']
    | undefined {
    const sessionVariableValue =
      this.sessionNameConfigValuesCache.findSessionVariableByName(IS_SANDBOX);
    if (!sessionVariableValue) {
      return undefined;
    }

    return sessionVariableValue === 'true';
  }

  private getConnectorName({
    parameters,
  }: Pick<
    connectorPresentation.dataSources.connectorData.callbacks.query.input,
    'parameters'
  >): string | EvstExtractionProviders {
    return parameters.connector?.name || EvstExtractionProviders.Gusto;
  }

  private getInitialFormLabels() {
    const isConnectorAuthenticated = this.isConnectorAuthenticated();
    const translationKeys = [
      'connectorEngine.reconnect',
      'connectorEngine.connect',
      'connectorEngine.create',
      'connectorEngine.test',
      'connectorEngine.disconnect',
      'connectorEngine.save',
      'gusto.testConnection.title',
    ];

    const translations = translationKeys.map((key) =>
      this.translationsCache.getTranslationByName(key)
    );

    const [
      reconnectLabel,
      connectLabel,
      createLabel,
      testLabel,
      disconnectLabel,
      saveLabel,
      testConnectionTitle,
    ] = translations;

    return {
      editorTitle: testConnectionTitle,
      mainTitle: createLabel,
      connectButtonLabel: isConnectorAuthenticated
        ? reconnectLabel
        : connectLabel,
      testButtonLabel: testLabel,
      disconnectButtonLabel: disconnectLabel,
      saveButtonLabel: saveLabel,
    };
  }

  private async getInitialFormFlags({
    parameters,
  }: Pick<
    connectorPresentation.dataSources.connectorData.callbacks.query.input,
    'parameters'
  >) {
    const isScopeDisabled = this.isScopeDisabled({ parameters });
    const isConnectorAuthenticated = this.isConnectorAuthenticated();
    const isConnectorDisconnected = this.isConnectorDisconnected();

    return {
      isScopeDisabled,
      isConnectorAuthenticated,
      isConnectorDisconnected,
    };
  }

  private async getInitialFormValues({
    parameters,
  }: Pick<
    connectorPresentation.dataSources.connectorData.callbacks.query.input,
    'parameters'
  >) {
    const name = this.getConnectorName({ parameters });
    const mappedEntity = this.getEntityIdFromSessionVariables();
    const mappedPayrollTemplate = this.getSelectedPayrollTemplateId();
    const isSandbox = this.getSelectedIsSandbox();

    return {
      name,
      mappedEntity,
      mappedPayrollTemplate,
      isSandbox,
    };
  }

  private isConnectorAuthenticated(): GustoSessionVariables['isAuthenticated'] {
    const sessionVariableValue =
      this.sessionNameConfigValuesCache.findSessionVariableByName(
        IS_AUTHENTICATED
      );

    return sessionVariableValue === 'true';
  }

  private isConnectorDisconnected(): GustoSessionVariables['isDisconnected'] {
    const sessionVariableValue =
      this.sessionNameConfigValuesCache.findSessionVariableByName(
        IS_DISCONNECTED
      );

    return sessionVariableValue === 'true';
  }

  private async initSessionVariables({
    session,
    parameters,
  }: Pick<
    connectorPresentation.dataSources.connectorData.callbacks.query.input,
    'session' | 'parameters'
  >) {
    await this.sessionNameConfigValuesCache.init({ session });
    await this.refreshSessionVariablesByTokenSet({ session, parameters });
  }

  private async refreshSessionVariablesByTokenSet({
    session,
    parameters,
  }: Pick<
    connectorPresentation.dataSources.connectorData.callbacks.query.input,
    'session' | 'parameters'
  >) {
    const isSandbox = this.getSelectedIsSandbox();
    const tokenSet = await getTokenSet(session, isSandbox);

    const configPairs: Array<{
      configKey: keyof GustoSessionVariables;
      configValue: unknown;
    }> = isEmpty(tokenSet)
      ? [
          { configKey: IS_DISCONNECTED, configValue: true },
          { configKey: IS_AUTHENTICATED, configValue: false },
        ]
      : [
          { configKey: IS_DISCONNECTED, configValue: false },
          { configKey: IS_AUTHENTICATED, configValue: true },
        ];

    await this.sessionNameConfigValuesCache.setSessionVariables(
      session,
      parameters.connector.id,
      configPairs
    );
    await this.sessionNameConfigValuesCache.resetCache();
  }

  private async initCaches({
    session,
    parameters,
  }: Pick<
    connectorPresentation.dataSources.connectorData.callbacks.query.input,
    'session' | 'parameters'
  >) {
    // Call order is important in here. Be careful!
    await this.translationsCache.init({ session });
    await this.initSessionVariables({ session, parameters });
    await this.entityIdNamesCache.init({ session });

    const entityId = this.getEntityIdFromSessionVariables();
    await this.payrollTemplateIdNamesCache.init({ session, entityId });
  }
}

const presentationImplementation: connectorPresentation.implementation = {
  connectorData: () => new ConnectorDataSource(),
};
export default presentationImplementation;
