import { getTranslations, type ISession } from '@everestsystems/content-core';
import type { signInSignUpModalPresentation } from '@pkg/everest.fin.integration.gusto/types/presentations/uinext/signInSignUpModal';
import { BaseCache } from '@pkg/everest.fin.integration.gusto/utils/common/cacheClasses';
import { executeAsyncOperation } from '@pkg/everest.fin.integration.gusto/utils/common/utils';
import { authenticate } from '@pkg/everest.fin.integration.gusto/utils/connector/authenticate';
import { register } from '@pkg/everest.fin.integration.gusto/utils/connector/register';
import { set } from 'lodash';

class TranslationsCache extends BaseCache<string, string> {
  public async init(args: { session: ISession; bypassCaching?: boolean }) {
    const { session, bypassCaching = false } = args;

    if (this.centralProperty === undefined || bypassCaching) {
      const translations = await getTranslations(
        session,
        [
          'gusto.connector.suggestion.signIn',
          'gusto.connector.suggestion.signUp',
        ],
        'everest.fin.integration.gusto/gusto'
      );

      this.centralProperty = new Map<string, string>(
        [
          'gusto.connector.suggestion.signIn',
          'gusto.connector.suggestion.signUp',
        ].map((key, index): [string, string] => [key, translations[index]])
      );
    }
  }

  public get translations() {
    return this.centralProperty;
  }

  public getTranslationByName(value: string) {
    return this.centralProperty?.get(value);
  }
}

class SignInSignUpModalDataSource
  implements
    signInSignUpModalPresentation.dataSources.signInSignUpData.implementation
{
  private signInSignUpData:
    | signInSignUpModalPresentation.dataSources.signInSignUpData.levels['']
    | undefined = undefined;
  private translationsCache = new TranslationsCache();

  public async query({ session }) {
    if (this.signInSignUpData === undefined) {
      await this.initCaches(session);
      const formLabels = this.getInitialFormLabels();

      this.signInSignUpData = {
        ...formLabels,
      };
    }

    return this.signInSignUpData;
  }

  async update({ fieldName, newFieldValue }) {
    set(this.signInSignUpData, [fieldName], newFieldValue);
  }

  public async execute_signIn({ session, parameters }) {
    return await executeAsyncOperation(
      async () => await authenticate(session, parameters.connectorId),
      'Gusto authentication is successful',
      'Gusto authentication is unsuccessful.'
    );
  }

  public async execute_signUp({ session, parameters }) {
    return await executeAsyncOperation(
      async () => await register(session, parameters.connectorId),
      'Gusto registration is successful',
      'Gusto registration is unsuccessful.'
    );
  }

  private getInitialFormLabels() {
    const signInSuggestion = this.translationsCache.getTranslationByName(
      'gusto.connector.suggestion.signIn'
    );
    const signUpSuggestion = this.translationsCache.getTranslationByName(
      'gusto.connector.suggestion.signUp'
    );

    return {
      signInSuggestion,
      signUpSuggestion,
    };
  }

  private async initCaches(session: ISession) {
    await this.translationsCache.init({ session });
  }
}

const presentationImplementation: signInSignUpModalPresentation.implementation =
  {
    signInSignUpData: () => new SignInSignUpModalDataSource(),
  };
export default presentationImplementation;
