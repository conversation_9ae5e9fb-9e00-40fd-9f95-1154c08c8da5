{"version": 3, "uicontroller": "signInSignUpModal.uicontroller.ts", "uimodel": {"presentation": {"urn": "urn:evst:everest:fin/integration/gusto:presentation:uinext/signInSignUpModal", "parameters": {"mode": "view", "connectorId": "@state:connectorId"}}}, "uiview": {"i18n": ["everest.connectorengine/connectorEngine", "gusto"], "title": "{{gusto.connect}}", "sections": {"content": [{"component": "ActionGroup", "props": {"buttonActionsPosition": "right", "lines": [{"description": "@binding:signInSignUpData.signInSuggestion", "buttonActions": [{"label": "{{gusto.signIn}}", "variant": "primary", "presentationAction": "@action:signIn", "onClick": "@controller:signIn"}]}, {"description": "@binding:signInSignUpData.signUpSuggestion", "buttonActions": [{"label": "{{gusto.signUp}}", "variant": "secondary", "presentationAction": "@action:signUp", "onClick": "@controller:signUp"}]}]}}]}}}