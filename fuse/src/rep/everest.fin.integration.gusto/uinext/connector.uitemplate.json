{"version": 3, "uicontroller": "connector.uicontroller.ts", "uimodel": {"presentation": {"urn": "urn:evst:everest:fin/integration/gusto:presentation:uinext/connector", "parameters": {"mode": "main", "providerName": "@state:param.providerName", "connector": "@state:connector"}}}, "uiview": {"i18n": ["everest.connectorengine/connectorEngine", "gusto"], "title": "@binding:connectorData.mainTitle", "autoRefreshData": true, "sections": [{"component": "FieldGroup", "section": {"title": "Personal Information", "grid": {"size": "12"}}, "props": {"columns": "4", "presentationDataSet": "@dataSet:connectorData", "fields": ["name", {"field": "mappedEntity", "fieldProps": {"allowClear": true}}, {"field": "mappedPayrollTemplate", "fieldProps": {"allowClear": true}}, {"component": "Checkbox", "field": "isSandbox", "fieldProps": {"component": "Checkbox", "text": "GUSTO Sandbox", "isDisabled": "@binding:connectorData.isScopeDisabled"}}]}}, {"component": "Block", "size": "12", "elements": [{"component": "Editor", "label": "@binding:connectorData.editorTitle", "value": "@binding:testConnection.response", "isEditing": false, "editorConfig": {"language": "json", "height": 200}}]}, {"component": "ButtonGroup", "direction": "horizontal", "actions": [{"variant": "secondary", "label": "@binding:connectorData.testButtonLabel", "presentationAction": "@action:test", "onClick": "@controller:test"}, {"variant": "secondary", "label": "@binding:connectorData.disconnectButtonLabel", "presentationAction": "@action:disconnect", "onClick": "@controller:disconnect"}, {"variant": "primary", "label": "@binding:connectorData.saveButtonLabel", "presentationAction": "@action:save", "onClick": "@controller:save"}, {"variant": "primary", "label": "@binding:connectorData.connectButtonLabel", "presentationAction": "@action:connect", "onClick": "@controller:connect"}]}]}}