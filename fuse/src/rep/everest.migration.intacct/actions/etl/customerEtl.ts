import type { ISession } from '@everestsystems/content-core';
import type { EvstCustomerUpsertPayload } from '@pkg/everest.fin.accounting/types/composites/CustomerUpsertPayload';
import type { Customer } from '@pkg/everest.fin.accounting/types/Customer';
import type { SyncDataMapping } from '@pkg/everest.fin.integration.base.ui/public/types/SyncDataMapping';
import executeETL from '@pkg/everest.fin.integration.base/public/executeETL2';
import type { StagingExecutionResponseType } from '@pkg/everest.fin.integration.base/public/integrationTypes';
import { EvstExtractionSource } from '@pkg/everest.fin.integration.base/types/enums/ExtractionSource';
import { EvstStagingStatus } from '@pkg/everest.fin.integration.base/types/enums/StagingStatus';
import type { IntegrationSession } from '@pkg/everest.fin.integration.base/types/IntegrationSession';
import { generateExtractionName } from '@pkg/everest.migration.base/public/utils/extractionName';
import { Migration } from '@pkg/everest.migration.base/types/Migration';
import { loadIntacctBO } from '@pkg/everest.migration.intacct/utils/intacctWrapperConfig';
import { getFileData } from '@pkg/everest.migration.intacct/utils/syncData/getData';
import { ProviderModel } from '@pkg/everest.migration.intacct/utils/types';

export default async function customerEtl(
  env: ISession,
  fileId: string,
  mapping: SyncDataMapping,
  sessionId?: IntegrationSession.IntegrationSession['id'],
  fileData?: Record<string, string>[]
): Promise<StagingExecutionResponseType> {
  const {
    providerModel,
    providerName,
    originalIdKey,
    mappingName,
    everestModel,
    concurrency,
  } = mapping;
  const extractionSource = EvstExtractionSource.File;
  const parentIdentifierPath = 'Parent ID';

  return executeETL(
    env,
    {
      extractFn: async () => {
        const data = fileData ?? getFileData(env, fileId, originalIdKey);
        return data;
      },
      providerModel,
      providerName,
      originalIdKey,
      everestModel,
      extractionSource,
      extractionName: generateExtractionName(
        providerName,
        providerModel,
        extractionSource
      ),
      descriptionFn: (data) => String(data['Customer name']),
      isHierarchy: true,
      parentIdentifierPath,
      retryItemsStatuses: [EvstStagingStatus.FAILED, EvstStagingStatus.PENDING],
    },
    [{ name: mappingName }],
    {
      loadFunction: async (
        transformedData: EvstCustomerUpsertPayload,
        everestId?: Customer.Customer['id']
      ) => {
        const migrationClient = await Migration.client(env);

        transformedData.migrationConfigurationId =
          await migrationClient.getMigrationConfigurationId(
            transformedData.dataSource
          );

        return loadIntacctBO<EvstCustomerUpsertPayload>(
          env,
          ProviderModel.Customer,
          transformedData,
          everestId
        );
      },
      concurrency,
    },
    sessionId
  );
}
