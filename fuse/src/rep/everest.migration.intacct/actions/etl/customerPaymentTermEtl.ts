import type { ISession } from '@everestsystems/content-core';
import type { EvstCustomerPaymentTermTransformedData } from '@pkg/everest.fin.accounting/types/composites/CustomerPaymentTermTransformedData';
import type { PaymentTerm } from '@pkg/everest.fin.accounting/types/PaymentTerm';
import type { SyncDataMapping } from '@pkg/everest.fin.integration.base.ui/public/types/SyncDataMapping';
import executeETL from '@pkg/everest.fin.integration.base/public/executeETL2';
import type { StagingExecutionResponseType } from '@pkg/everest.fin.integration.base/public/integrationTypes';
import { EvstExtractionSource } from '@pkg/everest.fin.integration.base/types/enums/ExtractionSource';
import { EvstStagingStatus } from '@pkg/everest.fin.integration.base/types/enums/StagingStatus';
import type { IntegrationSession } from '@pkg/everest.fin.integration.base/types/IntegrationSession';
import { generateExtractionName } from '@pkg/everest.migration.base/public/utils/extractionName';
import { loadIntacctBO } from '@pkg/everest.migration.intacct/utils/intacctWrapperConfig';
import { getFileData } from '@pkg/everest.migration.intacct/utils/syncData/getData';
import { ProviderModel } from '@pkg/everest.migration.intacct/utils/types';

export default async function customerPaymentTermEtl(
  env: ISession,
  fileId: string,
  mapping: SyncDataMapping,
  sessionId?: IntegrationSession.IntegrationSession['id'],
  fileData?: Record<string, string>[]
): Promise<StagingExecutionResponseType> {
  const {
    providerModel,
    providerName,
    originalIdKey,
    mappingName,
    everestModel,
  } = mapping;

  const extractionSource = EvstExtractionSource.File;
  return executeETL(
    env,
    {
      extractFn: async () => {
        const data =
          fileData ?? (await getFileData(env, fileId, originalIdKey));
        return deduplicateCustomerPaymentTerms(data);
      },
      providerModel,
      providerName,
      originalIdKey,
      everestModel,
      extractionSource,
      extractionName: generateExtractionName(
        providerName,
        providerModel,
        extractionSource
      ),
      descriptionFn: (data) => String(data['Description']),
      isHierarchy: false,
      retryItemsStatuses: [EvstStagingStatus.FAILED, EvstStagingStatus.PENDING],
    },
    [{ name: mappingName }],
    {
      loadFunction: async (
        transformedData: EvstCustomerPaymentTermTransformedData,
        everestId?: PaymentTerm.PaymentTerm['id']
      ) => {
        return loadIntacctBO<EvstCustomerPaymentTermTransformedData>(
          env,
          ProviderModel.CustomerPaymentTerm,
          transformedData,
          everestId,
          undefined,
          {
            update: false,
          }
        );
      },
      concurrency: 1,
    },
    sessionId
  );
}
function deduplicateCustomerPaymentTerms(
  data: Record<string, string>[]
): Record<string, string>[] {
  const uniqueTerms = new Map<string, Record<string, string>>();

  // Add each row to the map using Term as the key, overwriting duplicates
  for (const row of data) {
    if (row.Term) {
      uniqueTerms.set(row.Term, row);
    }
  }

  // Convert the map values back to an array
  return Array.from(uniqueTerms.values());
}
