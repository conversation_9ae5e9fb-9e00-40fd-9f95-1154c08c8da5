{"version": 2, "uicontroller": "customerPaymentTermDeduplication.uicontroller.ts", "uimodel": {"nodes": {"extractedData": {"type": "list", "modelId": "everest.migration.intacct/IntacctMigrationModel.IntacctMigration", "getCustomerDataFromExtraction": {"dataExtractionId": "@state:dataExtractionId"}, "fieldList": ["id", "customerName", "crmCustomerId", "paymentTerms", "paymentTerm"]}}}, "uiview": {"i18n": "everest.fin.integration.intacct/intacct", "title": "Solve Conflicts", "tabTitle": "Solve Conflicts", "header": {"margin": 0, "content": {"title": "Solve Conflicts", "description": "Customers with multiple payment terms, select one to continue"}}, "actions": {"content": [{"variant": "primary", "align": "right", "label": "Save", "onClick": "@controller:save"}]}, "sections": {"content": [{"component": "Table", "section": {"title": "Select Payment Term", "editing": true, "grid": {"size": "12"}}, "props": {"data": "@binding:extractedData", "variant": "light", "suppressDelete": true, "addRows": false, "columns": [{"headerName": "Id", "field": "id", "hide": true, "fieldProps": {"isEditing": false}}, {"headerName": "Customer", "field": "customerName", "fieldProps": {"isEditing": false}}, {"headerName": "CRM", "field": "crmCustomerId", "fieldProps": {"isEditing": false}}, {"headerName": "Payment Term", "field": "paymentTerm", "fieldProps": {"required": true, "component": "Select", "idProp": "value", "textProp": "text", "list": "@controller:getPaymentTermsList", "isEditing": true, "customFieldMessages": "@controller:isValidPaymentTerm"}}]}}]}}}