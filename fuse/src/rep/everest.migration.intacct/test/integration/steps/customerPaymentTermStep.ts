import type { ControllerClientProvider } from '@everestsystems/content-core';
import { customerPaymentTermContext } from '@pkg/everest.migration.intacct/test/integration/contexts/customerPaymentTermContext';

export const customerStep = {
  uuid: customerPaymentTermContext.createBaseNodesUUID,
  fn: async (session: ControllerClientProvider) =>
    customerPaymentTermContext.createBaseNodes(session),
};
