import type { ISession } from '@everestsystems/content-core';
import type {
  TestDataContext,
  TestTeardown,
} from '@everestsystems/content-core/lib/tests';
import { setupTest } from '@everestsystems/content-core/lib/tests';
import { Decimal } from '@everestsystems/decimal';
import { Customer } from '@pkg/everest.fin.accounting/types/Customer';
import { ENTITY_SESSION_VARIABLE_CONFIG_KEY } from '@pkg/everest.fin.integration.base.ui/public/utils/constants/constants';
import type { StagingExecutionResponse } from '@pkg/everest.fin.integration.base/public/integrationTypes';
import { EvstExtractionProviders } from '@pkg/everest.fin.integration.base/types/enums/ExtractionProviders';
import { Staging } from '@pkg/everest.fin.integration.base/types/Staging';
import type { CustomerContext } from '@pkg/everest.migration.intacct/test/integration/contexts/customerContext';
import type { MigrationSettingContext } from '@pkg/everest.migration.intacct/test/integration/contexts/migrationSettingContext';
import { customersCSVExtracted } from '@pkg/everest.migration.intacct/test/integration/fixtures/customersCSVExtracted';
import { customerStep } from '@pkg/everest.migration.intacct/test/integration/steps/customerStep';
import { migrationSettingStep } from '@pkg/everest.migration.intacct/test/integration/steps/migrationSettingStep';
import { mockFileStorage } from '@pkg/everest.migration.intacct/test/integration/utils/mockFileStorage';
import { setupMigrationMode } from '@pkg/everest.migration.intacct/test/integration/utils/setupMigrationMode';
import { IntacctMigration } from '@pkg/everest.migration.intacct/types/IntacctMigration';
import { CURRENCY_SESSION_VARIABLE_CONFIG_KEY } from '@pkg/everest.migration.intacct/utils/sessionVariables/currencySessionVariable';
import { TAX_EXEMPT_SESSION_VARIABLE_CONFIG_KEY } from '@pkg/everest.migration.intacct/utils/sessionVariables/taxExemptSessionVariable';
import { ProviderModel } from '@pkg/everest.migration.intacct/utils/types';
import { omit } from 'lodash';

import type { SessionVariableContext } from '../contexts/sessionVariableContext';
import { sessionVariableStep } from '../steps/sessionVariableStep';
import { upsertSessionVariable } from '../utils/upsertSessionVariables';

describe('Customer Upload - Integration Test', () => {
  describe('customer csv with one complete address and one missing address', () => {
    let session: ISession;
    let teardown: TestTeardown;
    let context: TestDataContext;
    let contextStepResults: [
      SessionVariableContext,
      CustomerContext,
      MigrationSettingContext,
    ];

    const mockFileId = 'mock-file-id';

    beforeEach(async () => {
      ({ session, teardown, context } = await setupTest({}));

      contextStepResults = await setupContext(context);

      await Promise.all([
        upsertSessionVariable(session, {
          sessionId: contextStepResults[0].id,
          configKey: ENTITY_SESSION_VARIABLE_CONFIG_KEY,
          configValue: contextStepResults[1].entityFlowInc.id.toString(),
        }),
        upsertSessionVariable(session, {
          sessionId: contextStepResults[0].id,
          configKey: TAX_EXEMPT_SESSION_VARIABLE_CONFIG_KEY,
          configValue: 'True',
        }),
        upsertSessionVariable(session, {
          sessionId: contextStepResults[0].id,
          configKey: CURRENCY_SESSION_VARIABLE_CONFIG_KEY,
          configValue: 'BRL',
        }),
      ]);

      mockFileStorage(customersCSVExtracted);

      await setupMigrationMode(session);
    });

    afterEach(() => jest.clearAllMocks());

    afterAll(() => teardown?.());

    it('should execute ETL, and create related nodes correctly', async () => {
      const result = await executeETL(
        session,
        mockFileId,
        contextStepResults[0].id
      );

      expect(result.success).toEqual(2);
      expect(result).toHaveProperty('dataExtractionId');

      const stagingDatas = await fetchStagingData(
        session,
        result.dataExtractionId
      );

      expect(stagingDatas.length).toBe(2);
      expect(stagingDatas[0].description).toBe('Tech Innovators Inc.');
      expect(stagingDatas[1].description).toBe('Dark Horizons Education');
      const expectedCustomers = getExpectedCustomers();

      for (const [i, stagingData] of stagingDatas.entries()) {
        await assertCustomer(
          session,
          stagingData.everestId,
          expectedCustomers[i]
        );
      }
    });
  });
});

async function setupContext(
  context: TestDataContext
): Promise<[SessionVariableContext, CustomerContext, MigrationSettingContext]> {
  const stepContext = context
    .with(sessionVariableStep)
    .with(customerStep)
    .with(migrationSettingStep);
  const { stepsResults } = await stepContext.createCheckpoint();
  return stepsResults;
}

async function executeETL(
  session: ISession,
  fileId: string,
  sessionId: number
): Promise<StagingExecutionResponse> {
  const intacctMigrationClient = await IntacctMigration.client(session);

  return intacctMigrationClient.syncData(
    ProviderModel.Customer,
    {
      displayName: 'Customer',
      providerName: EvstExtractionProviders.Intacct,
      displayNamePlural: 'Customers',
      everestModel: Customer.MODEL_URN,
      providerModel: ProviderModel.Customer,
      originalIdKey: 'Customer ID',
      mappings: {
        file: {
          name: 'Customer Intacct Mapping',
          type: {
            semantic: false,
            technical: true,
          },
        },
      },
      concurrency: 1,
      mappingName: 'Customer Intacct Mapping',
    },
    {
      fileId,
      sessionId,
    }
  );
}

async function fetchStagingData(session: ISession, dataExtractionId: number) {
  return Staging.query(session, { where: { dataExtractionId } }, [
    'everestId',
    'description',
  ]);
}

function getExpectedCustomers() {
  return [
    {
      annualRecurringRevenue: new Decimal('0'),
      country: 'US',
      creditBalance: new Decimal('0'),
      crmCustomerId: '0011G999913Aq7Pldoe',
      customerEntityName: 'Flow Inc',
      customerName: 'Tech Innovators Inc.',
      dataSource: 'Intacct',
      defaultCurrency: 'BRL',
      invoiceConsolidation: '',
      monthlyRecurringRevenue: new Decimal('0'),
      remainingPerformanceObligation: new Decimal('0'),
      sameAsBillToAddress: false,
      stateProvince: 'US-CA',
      status: 'active',
      taxExempt: true,
      totalAmountOverdue: new Decimal('0'),
      totalBookings: new Decimal('0'),
      totalInvoicedAmount: new Decimal('0'),
      totalRevenueRecognized: new Decimal('0'),
      unappliedPaymentsAmount: new Decimal('0'),
      migrationConfigurationProvider: EvstExtractionProviders.Intacct,
      $metadata: {
        isDraft: false,
      },
    },
    {
      annualRecurringRevenue: new Decimal('0'),
      creditBalance: new Decimal('0'),
      crmCustomerId: '0011U03210Qcq939Lp',
      customerEntityName: 'Flow Inc',
      customerName: 'Dark Horizons Education',
      dataSource: 'Intacct',
      defaultCurrency: 'BRL',
      invoiceConsolidation: '',
      monthlyRecurringRevenue: new Decimal('0'),
      remainingPerformanceObligation: new Decimal('0'),
      sameAsBillToAddress: false,
      status: 'inactive',
      taxExempt: true,
      totalAmountOverdue: new Decimal('0'),
      totalBookings: new Decimal('0'),
      totalInvoicedAmount: new Decimal('0'),
      totalRevenueRecognized: new Decimal('0'),
      unappliedPaymentsAmount: new Decimal('0'),
      migrationConfigurationProvider: EvstExtractionProviders.Intacct,
      $metadata: {
        isDraft: true,
      },
    },
  ];
}

async function assertCustomer(
  session: ISession,
  customerId: number,
  expectObject: Record<string, unknown>
) {
  const customer = await Customer.read(
    session,
    { id: customerId },
    [
      'customerName',
      'crmCustomerId',
      'additionalContactEmail',
      'annualRecurringRevenue',
      'billingEmail',
      'mainContactEmail',
      'mainContactName',
      'preferredLanguage',
      'remainingPerformanceObligation',
      'totalAmountOverdue',
      'totalInvoicedAmount',
      'totalBookings',
      'totalRevenueRecognized',
      'monthlyRecurringRevenue',
      'creditBalance',
      'defaultCurrency',
      'taxExempt',
      'customerEntityName',
      'paymentTerm',
      'sameAsBillToAddress',
      'unappliedPaymentsAmount',
      'taxExemptCode',
      'invoiceConsolidation',
      'file',
      'exemptionNumber',
      'businessIdentificationNumber',
      'status',
      'globalParentId',
      'globalParentName',
      'extraData',
      'avatar',
      'dataSource',
      'country',
      'stateProvince',
      'paymentTermLabel',
      'customerSegment',
      'migrationConfigurationProvider',
    ],
    { draft: 'include' }
  );

  expect(omit(customer, 'id')).toStrictEqual(expectObject);
}
