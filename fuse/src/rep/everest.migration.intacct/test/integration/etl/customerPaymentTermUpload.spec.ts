import type { ISession } from '@everestsystems/content-core';
import type {
  TestDataContext,
  TestTeardown,
} from '@everestsystems/content-core/lib/tests';
import { setupTest } from '@everestsystems/content-core/lib/tests';
import { PaymentTerm } from '@pkg/everest.fin.accounting/types/PaymentTerm';
import { ENTITY_SESSION_VARIABLE_CONFIG_KEY } from '@pkg/everest.fin.integration.base.ui/public/utils/constants/constants';
import type { StagingExecutionResponse } from '@pkg/everest.fin.integration.base/public/integrationTypes';
import { EvstExtractionProviders } from '@pkg/everest.fin.integration.base/types/enums/ExtractionProviders';
import { EvstStagingStatus } from '@pkg/everest.fin.integration.base/types/enums/StagingStatus';
import { Staging } from '@pkg/everest.fin.integration.base/types/Staging';
import type { CustomerPaymentTermContext } from '@pkg/everest.migration.intacct/test/integration/contexts/customerPaymentTermContext';
import type { MigrationSettingContext } from '@pkg/everest.migration.intacct/test/integration/contexts/migrationSettingContext';
import { customerPaymentTermCSVExtracted } from '@pkg/everest.migration.intacct/test/integration/fixtures/customerPaymentTermCSVExtracted';
import { customerStep as customerPaymentTermStep } from '@pkg/everest.migration.intacct/test/integration/steps/customerPaymentTermStep';
import { migrationSettingStep } from '@pkg/everest.migration.intacct/test/integration/steps/migrationSettingStep';
import { mockFileStorage } from '@pkg/everest.migration.intacct/test/integration/utils/mockFileStorage';
import { setupMigrationMode } from '@pkg/everest.migration.intacct/test/integration/utils/setupMigrationMode';
import { IntacctMigration } from '@pkg/everest.migration.intacct/types/IntacctMigration';
import { ProviderModel } from '@pkg/everest.migration.intacct/utils/types';
import { omit } from 'lodash';

import type { SessionVariableContext } from '../contexts/sessionVariableContext';
import { sessionVariableStep } from '../steps/sessionVariableStep';
import { upsertSessionVariable } from '../utils/upsertSessionVariables';

describe('Customer Payment Term Upload - Integration Test', () => {
  describe('customer payment term csv with different term options', () => {
    let session: ISession;
    let teardown: TestTeardown;
    let context: TestDataContext;
    let contextStepResults: [
      SessionVariableContext,
      CustomerPaymentTermContext,
      MigrationSettingContext,
    ];

    const mockFileId = 'mock-file-id';

    beforeEach(async () => {
      ({ session, teardown, context } = await setupTest({}));

      contextStepResults = await setupContext(context);

      await upsertSessionVariable(session, {
        sessionId: contextStepResults[0].id,
        configKey: ENTITY_SESSION_VARIABLE_CONFIG_KEY,
        configValue: contextStepResults[1].entityFlowInc.id.toString(),
      });

      mockFileStorage(customerPaymentTermCSVExtracted);

      await setupMigrationMode(session);
    });

    afterEach(() => jest.clearAllMocks());

    afterAll(() => teardown?.());

    it('should execute ETL, and create payment terms correctly', async () => {
      const result = await executeETL(
        session,
        mockFileId,
        contextStepResults[0].id
      );

      expect(result.success).toEqual(2);
      expect(result).toHaveProperty('dataExtractionId');

      const stagingDatas = await fetchStagingData(
        session,
        result.dataExtractionId
      );

      expect(stagingDatas.length).toBe(2);

      expect(stagingDatas[0].status).toBe(EvstStagingStatus.Matched);
      expect(stagingDatas[1].status).toBe(EvstStagingStatus.Integrated);
      const expectedPaymentTerms = getExpectedPaymentTerms();

      for (const [i, stagingData] of stagingDatas.entries()) {
        await assertPaymentTerm(
          session,
          stagingData.everestId,
          expectedPaymentTerms[i]
        );
      }
    });
  });
});

async function setupContext(
  context: TestDataContext
): Promise<
  [SessionVariableContext, CustomerPaymentTermContext, MigrationSettingContext]
> {
  const stepContext = context
    .with(sessionVariableStep)
    .with(customerPaymentTermStep)
    .with(migrationSettingStep);
  const { stepsResults } = await stepContext.createCheckpoint();
  return stepsResults;
}

async function executeETL(
  session: ISession,
  fileId: string,
  sessionId: number
): Promise<StagingExecutionResponse> {
  const intacctMigrationClient = await IntacctMigration.client(session);

  return intacctMigrationClient.syncData(
    ProviderModel.CustomerPaymentTerm,
    {
      displayName: 'Customer Payment Term',
      providerName: EvstExtractionProviders.Intacct,
      displayNamePlural: 'Customer Payment Terms',
      everestModel: PaymentTerm.MODEL_URN,
      providerModel: ProviderModel.CustomerPaymentTerm,
      originalIdKey: 'Customer ID',
      mappings: {
        file: {
          name: 'Customer Payment Term Configuration Intacct Mapping',
          type: {
            semantic: false,
            technical: true,
          },
        },
      },
      concurrency: 1,
      mappingName: 'Customer Payment Term Configuration Intacct Mapping',
    },
    {
      fileId,
      sessionId,
    }
  );
}

async function fetchStagingData(session: ISession, dataExtractionId: number) {
  return Staging.query(session, { where: { dataExtractionId } }, [
    'everestId',
    'status',
  ]);
}

function getExpectedPaymentTerms() {
  return [
    {
      code: 'net10',
      label: 'Net 10',
      description: 'Invoice will be due after 10 days',
      numberOfDays: 10,
    },
    {
      code: 'net50',
      label: 'Net 50',
      description: 'Invoice will be due after 50 days',
      numberOfDays: 50,
    },
  ];
}

async function assertPaymentTerm(
  session: ISession,
  paymentTermId: number,
  expectObject: Record<string, unknown>
) {
  const paymentTerm = await PaymentTerm.read(session, { id: paymentTermId }, [
    'code',
    'label',
    'description',
    'numberOfDays',
  ]);

  expect(omit(paymentTerm, 'id')).toStrictEqual(expectObject);
}
