import type { ControllerClientProvider } from '@everestsystems/content-core';
import { Address } from '@pkg/everest.base/types/Address';
import { Entity } from '@pkg/everest.base/types/Entity';
import { EvstEntityStatus } from '@pkg/everest.base/types/enums/EntityStatus';

const nodesFieldList = {
  address: ['id'] as const satisfies ReadonlyArray<keyof Address.Address>,
  entity: ['id'] as const satisfies ReadonlyArray<keyof Entity.Entity>,
};

export type CustomerContext = {
  entityFlowInc: Pick<Entity.Entity, (typeof nodesFieldList)['entity'][number]>;
};

export const customerContext = {
  createBaseNodesUUID: 'b7f9e1d2-3c4a-5b6d-7e8f-9a0b1c2d3e4f',
  async createBaseNodes(
    session: ControllerClientProvider
  ): Promise<CustomerContext> {
    const defaultRegisteredAddress = await Address.create(
      session,
      {
        active: true,
        attention: '<PERSON> Kyle<PERSON>',
        city: 'Palo Alto',
        country: 'US',
        externalId: '25',
        line1: '3885 El Camino Real',
        package: 'everest.fin.accounting',
        stateProvince: 'US-CA',
        zipCode: '94306',
      },
      nodesFieldList.address
    );

    const entityFlowInc = await Entity.create(
      session,
      {
        active: true,
        country: 'US',
        currency: 'USD',
        defaultRegisteredAddressId: defaultRegisteredAddress.id,
        entityName: 'Flow Inc',
        externalId: '1',
        status: EvstEntityStatus.Active,
        taxId: '1000001',
        timeZone: 'Etc/UTC',
      },
      nodesFieldList.entity
    );
    return {
      entityFlowInc,
    };
  },
};
