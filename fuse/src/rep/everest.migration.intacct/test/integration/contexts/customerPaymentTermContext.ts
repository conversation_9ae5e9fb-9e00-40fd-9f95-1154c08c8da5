import type { ControllerClientProvider } from '@everestsystems/content-core';
import { Address } from '@pkg/everest.base/types/Address';
import { Entity } from '@pkg/everest.base/types/Entity';
import { EvstEntityStatus } from '@pkg/everest.base/types/enums/EntityStatus';
import { PaymentTerm } from '@pkg/everest.fin.accounting/types/PaymentTerm';

const nodesFieldList = {
  address: ['id'] as const satisfies ReadonlyArray<keyof Address.Address>,
  entity: ['id'] as const satisfies ReadonlyArray<keyof Entity.Entity>,
  paymentTerm: ['id'] as const satisfies ReadonlyArray<
    keyof PaymentTerm.PaymentTerm
  >,
};

export type CustomerPaymentTermContext = {
  entityFlowInc: Pick<Entity.Entity, (typeof nodesFieldList)['entity'][number]>;
  paymentTermNet10: Pick<
    PaymentTerm.PaymentTerm,
    (typeof nodesFieldList)['paymentTerm'][number]
  >;
};

export const customerPaymentTermContext = {
  createBaseNodesUUID: 'ff4c4fb1-b570-43c9-a9f1-ac8d5c04a02b',
  async createBaseNodes(
    session: ControllerClientProvider
  ): Promise<CustomerPaymentTermContext> {
    const defaultRegisteredAddress = await Address.create(
      session,
      {
        active: true,
        attention: 'Kendall Kylee',
        city: 'Palo Alto',
        country: 'US',
        externalId: '25',
        line1: '3885 El Camino Real',
        package: 'everest.fin.accounting',
        stateProvince: 'US-CA',
        zipCode: '94306',
      },
      nodesFieldList.address
    );

    const entityFlowInc = await Entity.create(
      session,
      {
        active: true,
        country: 'US',
        currency: 'USD',
        defaultRegisteredAddressId: defaultRegisteredAddress.id,
        entityName: 'Flow Inc',
        externalId: '1',
        status: EvstEntityStatus.Active,
        taxId: '1000001',
        timeZone: 'Etc/UTC',
      },
      nodesFieldList.entity
    );

    const paymentTermNet10 = await PaymentTerm.create(
      session,
      {
        code: 'net10',
        description: 'Invoice will be due after 10 days',
        label: 'Net 10',
        numberOfDays: 10,
      },
      nodesFieldList.paymentTerm
    );

    return {
      entityFlowInc,
      paymentTermNet10,
    };
  },
};
