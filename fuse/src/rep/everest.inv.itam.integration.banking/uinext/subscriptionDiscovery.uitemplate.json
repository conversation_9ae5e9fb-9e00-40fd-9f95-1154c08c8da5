{"version": 3, "uicontroller": "subscriptionDiscovery.uicontroller.ts", "uimodel": {"presentation": {"urn": "urn:evst:everest:inv/itam/integration/banking:presentation:uinext/subscriptionDiscovery", "parameters": {"groupUUID": "@state:groupUUID"}}}, "uiview": {"title": "{{discoverSubscriptions}}", "i18n": "everest.inv.itam/software", "sections": {"content": [{"component": "Segments", "size": "12", "controlSize": "12", "variant": "secondary", "segments": [{"title": "{{discoveredSubscriptions}}", "sections": [{"component": "Table", "customId": "detectedSubscriptions", "section": {"grid": {"size": 12}, "editing": true, "actions": [{"label": "{{submit}}", "onClick": "@controller:submit"}]}, "props": {"presentationDataSet": "@dataSet:detectedSubscriptions", "addRows": false, "suppressDelete": true, "columns": ["patternSuggestedName", "patternSuggestedVendorId", {"field": "matchingEntitlementId", "fieldProps": {"isDisabled": "@controller:isMatchingEntitlementIdDisabled"}}, "patternAverageAmount", "startDate", "subscriptionType", "patternEntityId", "patternFrequency", "patternConfidence", {"field": "finalAction", "fieldProps": {"list": "@controller:getFinalActionList()", "component": "Select", "idProp": "actionId", "textProp": "actionName"}}]}}]}, {"title": "{{softwareEntitlementBankTransactions}}", "isDisabled": "@controller:isSoftwareEntitlementBankTransactionsDisabled()", "sections": [{"component": "Table", "section": {"grid": {"size": "12"}}, "props": {"presentationDataSet": "@dataSet:bankTransactions", "addRows": false, "suppressDelete": true, "addRowsOnEmpty": false, "showRowCount": true, "pagination": false}}]}]}]}}}