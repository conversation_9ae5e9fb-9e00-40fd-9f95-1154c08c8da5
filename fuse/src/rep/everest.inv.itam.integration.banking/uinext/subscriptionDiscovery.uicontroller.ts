// @i18n:everest.inv.itam/software
import { UILifecycleHooks } from '@everestsystems/content-core';
import type { subscriptionDiscoveryPresentationUI } from '@pkg/everest.inv.itam.integration.banking/types/presentations/uinext/subscriptionDiscovery.ui';
import { EvstSoftwareEntitlementType } from '@pkg/everest.inv.itam/types/enums/SoftwareEntitlementType';

type Context = subscriptionDiscoveryPresentationUI.context;

UILifecycleHooks.onInit((context: Context) =>
  context.helpers.closeNotificationMessage('discoverSubscriptionsInProgress')
);

export async function submit(context: Context): Promise<void> {
  const { helpers, data } = context;
  const detectedSubscriptions = data.detectedSubscriptions;
  const subscriptionsToCreate = detectedSubscriptions.filter(
    (subscription) => subscription.finalAction === 0
  );
  const subscriptionsToMatch = detectedSubscriptions.filter(
    (subscription) => subscription.finalAction === 1
  );
  helpers.showNotificationMessage({
    key: 'save',
    message: '{{saving}}',
    type: 'loading',
    duration: 0,
  });
  try {
    if (subscriptionsToCreate) {
      await context.presentationClient.createEntitlement({
        data: subscriptionsToCreate.map((sub) => ({
          entitlementDetails: {
            billingPeriod: sub.patternFrequency,
            currency: sub.patternAverageAmount.currency,
            description: sub.patternSuggestedName,
            entitlementType: EvstSoftwareEntitlementType.Subscription,
            entityId: sub.patternEntityId,
            subscriptionType: sub.subscriptionType,
            startDate: sub.startDate,
            totalCost: { amount: sub.patternAverageAmount.amount },
            vendorId: sub.patternSuggestedVendorId,
          } satisfies subscriptionDiscoveryPresentationUI.actions.createEntitlement.input['data'][number]['entitlementDetails'],
          transactionIds: data.subscriptionTransactions
            .filter((tr) => tr._nodeParentReference === sub._nodeReference)
            .map((tr) => tr.id),
        })),
      });
    }
    if (subscriptionsToMatch) {
      const entitlementTransactionPairs = subscriptionsToMatch.map(
        (subscription) => ({
          entitlementId: subscription.matchingEntitlementId,
          transactionIds: data.subscriptionTransactions
            .filter(
              (tr) => tr._nodeParentReference === subscription._nodeReference
            )
            .map((tr) => tr.id),
        })
      );
      await context.presentationClient.linkToEntitlement({
        data: entitlementTransactionPairs,
      });
    }
  } catch (error) {
    helpers.showNotificationMessage({
      key: 'save',
      message: `{{errorMessage}} ${error.message}`,
      type: 'error',
    });
    throw error;
  }
  helpers.showNotificationMessage({
    key: 'save',
    message: '{{successMessage}}',
    type: 'success',
  });
  helpers.currentModalSubmitCallback();
  helpers.closeModal();
}

export function getFinalActionList(
  _context: Context
): { entitlementId: number; entitlementName: string }[] {
  const mappedEntitlements = [];
  mappedEntitlements.push(
    {
      actionId: 0,
      actionName: '{{createNewEntitlemet}}',
    },
    {
      actionId: 1,
      actionName: '{{link}}',
    },
    {
      actionId: 2,
      actionName: '{{noAction}}',
    }
  );

  return mappedEntitlements;
}

export function isMatchingEntitlementIdDisabled(
  _context: Context,
  row: { rowData: Context['data']['detectedSubscriptions'][number] }
) {
  return row.rowData.finalAction === 0;
}

export function isSoftwareEntitlementBankTransactionsDisabled(
  context: Context
) {
  const { data } = context;
  return (data.bankTransactions ?? []).length === 0;
}
