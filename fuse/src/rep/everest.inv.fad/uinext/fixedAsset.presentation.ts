// @i18n:fixedAssets

import type {
  ISession,
  RoutineValidationContext,
} from '@everestsystems/content-core';
import {
  DATA,
  getTranslationsMap,
  IDENTIFIER,
  METADATA,
} from '@everestsystems/content-core';
import { PlainDate } from '@everestsystems/datetime';
import { Entity } from '@pkg/everest.base/types/Entity';
import { JournalEntryLine } from '@pkg/everest.fin.accounting/types/JournalEntryLine';
import { DepreciationPolicy } from '@pkg/everest.inv.fad/types/DepreciationPolicy';
import { EvstAssetStatus } from '@pkg/everest.inv.fad/types/enums/AssetStatus';
import { EvstDepreciationMethod } from '@pkg/everest.inv.fad/types/enums/DepreciationMethod';
import { EvstDepreciationScheduleStatus } from '@pkg/everest.inv.fad/types/enums/DepreciationScheduleStatus';
import { FixedAsset } from '@pkg/everest.inv.fad/types/FixedAsset';
import { FixedAssetType } from '@pkg/everest.inv.fad/types/FixedAssetType';
import type { FixedAssetUsageLine } from '@pkg/everest.inv.fad/types/FixedAssetUsageLine';
import type { fixedAssetPresentation } from '@pkg/everest.inv.fad/types/presentations/uinext/fixedAsset';
import type { FixedAssetTypeRelatedFields } from '@pkg/everest.inv.fad/utils/utils';
import {
  depreciationPolicyFinder,
  formatPlainDate,
  getFixedAssetFromSourceJournalEntryLines,
  getSourceLink,
  getUsefulLifeText,
} from '@pkg/everest.inv.fad/utils/utils';
import { pick, set } from 'lodash';

const DELETED: unique symbol = Symbol();

type Validation = (
  context: RoutineValidationContext,
  data: fixedAssetPresentation.dataSources.FixedAssetDataSource.callbacks.query.queryData
) => Promise<boolean>;

class FixedAssetDataSource
  implements
    fixedAssetPresentation.dataSources.FixedAssetDataSource.implementation
{
  private data: fixedAssetPresentation.dataSources.FixedAssetDataSource.callbacks.query.queryData | null =
    null;
  private metadata: fixedAssetPresentation.dataSources.FixedAssetDataSource.callbacks.query.queryMetadata;

  private validationQueue: Validation[] = [];

  private translationsMap: Awaited<
    ReturnType<typeof getTranslationsMap>
  > | null = null;
  private entityToCurrencyMap: Map<
    Entity.Entity['id'],
    Entity.Entity['currency']
  > = new Map();
  private depreciationPolicyHelper: Awaited<
    ReturnType<typeof depreciationPolicyFinder>
  > | null = null;
  private isDepreciationMethodChangedByUser = false;
  private chosenDepreciationMethod:
    | FixedAsset.FixedAsset['depreciationMethod']
    | null = null;

  public async query(
    input: fixedAssetPresentation.dataSources.FixedAssetDataSource.callbacks.query.input
  ): Promise<fixedAssetPresentation.dataSources.FixedAssetDataSource.callbacks.query.combinedOutput> {
    const { mode, parameters, session, queryReason } = input;

    if (this.data === null || queryReason === 'externalRequest') {
      await this.fillTranslationsMap(input);
      await this.fillEntityCurrencyMap(input);
      this.isDepreciationMethodChangedByUser = false;

      const isCreateMode = mode === 'create';

      this.data = {};
      this.metadata = {};

      if (isCreateMode) {
        this.data = {
          status: EvstAssetStatus.Available,
          AssignmentLines: [],
        };

        if (parameters.sourceJournalEntryLineId) {
          const sourceJournalEntryLine = await JournalEntryLine.read(
            session,
            {
              id: parameters.sourceJournalEntryLineId,
            },
            [
              'id',
              'accountId',
              'entityId',
              'description',
              'departmentId',

              'transactionDate',

              'glAmount',
              'functionalCurrency',

              'sourceTransLine',
              'jeLineReference',
            ]
          );

          if (sourceJournalEntryLine) {
            this.data = {
              ...this.data,
              ...getFixedAssetFromSourceJournalEntryLines(
                sourceJournalEntryLine
              ),
              assetAccountId: sourceJournalEntryLine.accountId,
            };

            await this.updateDepreciationPolicyHelper(input.session, true);
            const possibleFixedAssetTypes =
              this.depreciationPolicyHelper.getPossibleFixedAssetTypesFromAssetAccountId(
                sourceJournalEntryLine.accountId
              );
            if (possibleFixedAssetTypes.length === 1) {
              const typeRelatedFields = possibleFixedAssetTypes.at(0);
              this.setFixedAssetTypeRelatedFields(typeRelatedFields);
              this.determineDepreciationMethod();
            }
          }
        }
      } else {
        const fixedAsset = await FixedAsset.read(
          session,
          {
            id: parameters.id,
          },
          [
            'id',
            'source',
            'fixedAssetNumber',
            'name',
            'entityId',
            'currency',
            'typeId',
            'depreciationMethod',
            'depreciationInterval',
            'departmentId',
            'serialNumber',
            'internalSerialNumber',
            'salvageValue',
            'status',
            'acquisitionValue',
            'acquisitionDate',
            'depreciationStartDate',
            'depreciationStatus',
            'depreciatedAmount',
            'remainingValue',
            'sourceJournalEntryLineId',
            'depreciationStatus',
            'disposalDate',
            {
              DepreciationScheduleHeader: [
                'startDate',
                'endDate',
                {
                  DepreciationRule: [
                    'usefulLife',
                    'usefulLifeDurationType',
                    'minimumAmount',
                    {
                      DepreciationPolicy: ['name'],
                    },
                  ],
                  DepreciationScheduleLines: [
                    'postingDate',
                    'amount',
                    'journalEntryHeaderId',
                    'journalEntryHeaderNumber',
                  ],
                },
              ],
              AssignmentLines: [
                'id',
                'startDate',
                'endDate',
                'employeeId',
                'remarks',
              ],
            },
          ]
        );

        this.data = {
          ...fixedAsset,
          usefulLife: getUsefulLifeText(
            fixedAsset.DepreciationScheduleHeader?.DepreciationRule.usefulLife,
            fixedAsset.DepreciationScheduleHeader?.DepreciationRule
              .usefulLifeDurationType
          ),
          AssignmentLines: fixedAsset.AssignmentLines.map((line) => ({
            ...line,
            [IDENTIFIER]: line.id,
            [DELETED]: false,
          })),
          DepreciationScheduleHeader: {
            ...fixedAsset.DepreciationScheduleHeader,
            DepreciationScheduleLines:
              fixedAsset.DepreciationScheduleHeader?.DepreciationScheduleLines.map(
                (depreciationLine) => ({
                  ...depreciationLine,
                  // TODO: We should add department id to depreciation lines in future so we can handle updating them
                  departmentId: fixedAsset.departmentId,
                  postingPeriod: formatPlainDate(depreciationLine.postingDate),
                })
              ).toSorted((a, b) =>
                PlainDate.compare(a.postingDate, b.postingDate)
              ),
          },
        };
      }

      await this.fillLinkToSource(input.session);

      await this.updateDisbledFixedAssetTypeIds(input.session);
    }

    this.updateMetadataAndFlags(input);

    return {
      [DATA]: this.data,
      [METADATA]: this.metadata,
    };
  }

  private updateMetadataAndFlags(
    input: fixedAssetPresentation.dataSources.FixedAssetDataSource.callbacks.query.input
  ) {
    const { mode } = input;
    const isViewMode = mode === 'view';
    const isCreateMode = mode === 'create';

    const isDepreciationScheduleNotStartedOrCreateMode =
      this.data.depreciationStatus ===
        EvstDepreciationScheduleStatus.NotStarted || isCreateMode;

    const isFixedAssetStatusRetired =
      this.data.status === EvstAssetStatus.Retired;

    this.metadata = {
      ...this.metadata,
      entityId: {
        editable:
          !this.data.sourceJournalEntryLineId &&
          isDepreciationScheduleNotStartedOrCreateMode,
      },
      currency: {
        editable:
          !this.data.sourceJournalEntryLineId &&
          isDepreciationScheduleNotStartedOrCreateMode,
      },
      typeId: {
        editable: isDepreciationScheduleNotStartedOrCreateMode,
      },
      depreciationMethod: {
        editable: isDepreciationScheduleNotStartedOrCreateMode,
      },
      depreciationInterval: {
        editable: isDepreciationScheduleNotStartedOrCreateMode,
      },
      departmentId: {
        editable: isDepreciationScheduleNotStartedOrCreateMode,
      },
      salvageValue: {
        editable: isDepreciationScheduleNotStartedOrCreateMode,
      },
      disposalDate: {
        required: isFixedAssetStatusRetired,
      },
      acquisitionValue: {
        editable:
          !this.data.sourceJournalEntryLineId &&
          isDepreciationScheduleNotStartedOrCreateMode,
      },
      acquisitionDate: {
        visible: !isViewMode,
        editable:
          !this.data.sourceJournalEntryLineId &&
          isDepreciationScheduleNotStartedOrCreateMode,
      },
      depreciationStartDate: {
        visible: !isViewMode,
        editable:
          !this.data.sourceJournalEntryLineId &&
          isDepreciationScheduleNotStartedOrCreateMode,
      },
      source: {
        visible: !!this.data.sourceJournalEntryLineId,
      },
      DepreciationScheduleHeader: {
        startDate: {
          visible: isViewMode,
        },
        endDate: {
          visible: isViewMode,
        },
      },
    };

    this.data.Page = {
      status: this.data.depreciationStatus,
      tabTitle: isCreateMode
        ? this.translationsMap.get('fixedAssets.create')
        : `${this.data.fixedAssetNumber}: ${this.data.name}`,
      title: isViewMode
        ? `${this.data.fixedAssetNumber}: ${this.data.name}`
        : this.data.name,
    };
    const isDepreciationScheduleDisposed =
      this.data.depreciationStatus === EvstDepreciationScheduleStatus.Disposed;

    const isDepreciationScheduleStarted =
      this.data.depreciationStatus !==
      EvstDepreciationScheduleStatus.NotStarted;

    this.data.Flags = {
      isTitleEditable: !isViewMode,
      isDepreciationLinesVisible: isViewMode,
      isMoreActionsVisible: isViewMode,
      isReviseDisabled: isDepreciationScheduleDisposed,
      isDeleteDisabled: isDepreciationScheduleStarted,
      isDisposalDateVisible: isFixedAssetStatusRetired,
      isRecalculateDepreciationVisible: !isDepreciationScheduleDisposed,
    };

    this.data.Summary = {
      acquisitionValue: this.data.acquisitionValue,
      depreciatedAmount: this.data.depreciatedAmount,
      remainingValue: this.data.remainingValue,
    };
  }

  public async update(
    input: fixedAssetPresentation.dataSources.FixedAssetDataSource.callbacks.update.input
  ): Promise<fixedAssetPresentation.dataSources.FixedAssetDataSource.callbacks.update.output> {
    const { fieldName, newFieldValue, oldFieldValue, session } = input;

    set(this.data, fieldName, newFieldValue);

    switch (fieldName) {
      case 'entityId': {
        const newCurrency = this.entityToCurrencyMap.get(
          newFieldValue as number
        );
        if (newCurrency) {
          set(this.data, 'currency', newCurrency);
        }
        if (oldFieldValue !== newFieldValue && newFieldValue) {
          await this.updateDepreciationPolicyHelper(session, true);

          this.handleFixedAssetTypeChange(this.data.typeId);

          await this.updateDisbledFixedAssetTypeIds(session);
        }

        break;
      }
      case 'acquisitionDate': {
        set(this.data, 'depreciationStartDate', newFieldValue);

        break;
      }
      case 'typeId': {
        await this.updateDepreciationPolicyHelper(session);

        this.handleFixedAssetTypeChange(newFieldValue as number);

        break;
      }
      case 'depreciationMethod': {
        this.isDepreciationMethodChangedByUser = true;
        this.chosenDepreciationMethod = this.data.depreciationMethod;

        break;
      }
    }

    this.determineDepreciationMethod();
  }

  public async update_Page(
    input: fixedAssetPresentation.dataSources.FixedAssetDataSource.callbacks.update_Page.input
  ): Promise<fixedAssetPresentation.dataSources.FixedAssetDataSource.callbacks.update_Page.output> {
    const { fieldName, newFieldValue } = input;

    set(this.data.Page, fieldName, newFieldValue);

    if (fieldName === 'title') {
      set(this.data, 'name', newFieldValue);
    }
  }

  public async create_AssignmentLines(
    input: fixedAssetPresentation.dataSources.FixedAssetDataSource.callbacks.create_AssignmentLines.input
  ): Promise<fixedAssetPresentation.dataSources.FixedAssetDataSource.callbacks.create_AssignmentLines.output> {
    if (input.isRestore) {
      this.data.AssignmentLines.find(
        (line) => line[IDENTIFIER] === input.data[IDENTIFIER]
      )[DELETED] = false;

      return;
    }

    const identifier = input.session.util.uuid.v4();
    const newLine = {
      [IDENTIFIER]: identifier,
      ...input.data,
    };
    this.data.AssignmentLines.push(newLine);

    return identifier;
  }

  public async update_AssignmentLines(
    input: fixedAssetPresentation.dataSources.FixedAssetDataSource.callbacks.update_AssignmentLines.input
  ): Promise<fixedAssetPresentation.dataSources.FixedAssetDataSource.callbacks.update_AssignmentLines.output> {
    const targetLine = this.data.AssignmentLines.find(
      (line) => line[IDENTIFIER] === input.path.at(0)
    );
    set(targetLine, input.fieldName, input.newFieldValue);
  }

  public async delete_AssignmentLines(
    input: fixedAssetPresentation.dataSources.FixedAssetDataSource.callbacks.delete_AssignmentLines.input
  ): Promise<fixedAssetPresentation.dataSources.FixedAssetDataSource.callbacks.delete_AssignmentLines.output> {
    const identifier = input.path.at(0);

    if (input.isRestorable) {
      this.data.AssignmentLines.find((line) => line[IDENTIFIER] === identifier)[
        DELETED
      ] = true;
    } else {
      this.data.AssignmentLines = this.data.AssignmentLines.filter(
        (line) => line[IDENTIFIER] !== identifier
      );
    }
  }

  public async determine_save(
    input: fixedAssetPresentation.dataSources.FixedAssetDataSource.routines.save.determineInput
  ): Promise<fixedAssetPresentation.dataSources.FixedAssetDataSource.routines.save.determineOutput> {
    const { mode } = input;
    const isCreateMode = mode === 'create';

    const label = this.translationsMap.get(
      isCreateMode ? 'fixedAsset.create' : 'fixedAsset.save'
    );

    return {
      label,
    };
  }

  public async validate_save(
    input: fixedAssetPresentation.dataSources.FixedAssetDataSource.routines.save.validateInput
  ): Promise<fixedAssetPresentation.dataSources.FixedAssetDataSource.routines.save.validateOutput> {
    const { validateReason, context } = input;
    const validations = [...this.validationQueue];

    if (validateReason === 'executionRequest') {
      validations.push(...this.getRequiredValidations());
    }

    const failedValidations: Validation[] = [];

    for (const validation of validations) {
      const validationResult = await validation(context, this.data);
      if (validationResult === false) {
        failedValidations.push(validation);
      }
    }

    this.validationQueue = failedValidations;

    return validateReason === 'earlyEvaluation'
      ? true
      : failedValidations.length === 0;
  }

  public async execute_save(
    input: fixedAssetPresentation.dataSources.FixedAssetDataSource.routines.save.executeInput
  ): Promise<fixedAssetPresentation.dataSources.FixedAssetDataSource.routines.save.executeOutput> {
    const fixedAssetClient = await FixedAsset.client(input.session);

    const fixedAsset = pick(this.data, [
      'id',
      'name',
      'entityId',
      'currency',
      'acquisitionValue',
      'salvageValue',
      'acquisitionDate',
      'depreciationStartDate',
      'typeId',
      'depreciationMethod',
      'depreciationInterval',
      'departmentId',
      'serialNumber',
      'internalSerialNumber',
      'sourceJournalEntryLineId',
      'status',
      'disposalDate',
    ]);
    const assignmentLines = this.data.AssignmentLines.filter(
      (assignmentLine) => !assignmentLine[DELETED]
    );

    const result = await fixedAssetClient.upsertFixedAsset(
      fixedAsset as FixedAsset.FixedAsset,
      assignmentLines as unknown as FixedAssetUsageLine.FixedAssetUsageLine[]
    );

    this.resetData();

    return {
      id: result.id,
    };
  }

  public async validate_deleteFixedAsset(
    _input: fixedAssetPresentation.dataSources.FixedAssetDataSource.routines.deleteFixedAsset.validateInput
  ): Promise<fixedAssetPresentation.dataSources.FixedAssetDataSource.routines.deleteFixedAsset.validateOutput> {
    return {
      passed: !this.data.Flags.isDeleteDisabled,
    };
  }

  public async execute_deleteFixedAsset(
    input: fixedAssetPresentation.dataSources.FixedAssetDataSource.routines.deleteFixedAsset.executeInput
  ): Promise<fixedAssetPresentation.dataSources.FixedAssetDataSource.routines.deleteFixedAsset.executeOutput> {
    const fixedAssetClient = await FixedAsset.client(input.session);
    await fixedAssetClient.deleteFixedAsset(this.data.id);
  }

  public async execute_recalculateDepreciationSchedule(
    input: fixedAssetPresentation.dataSources.FixedAssetDataSource.routines.recalculateDepreciationSchedule.executeInput
  ): Promise<fixedAssetPresentation.dataSources.FixedAssetDataSource.routines.recalculateDepreciationSchedule.executeOutput> {
    const fixedAssetClient = await FixedAsset.client(input.session);
    await fixedAssetClient.recalculateDepreciation(
      this.data.id,
      input.input.overrideUsefulMonths
    );

    this.resetData();
  }

  public async execute_reset(
    _input: fixedAssetPresentation.dataSources.FixedAssetDataSource.routines.reset.executeInput
  ): Promise<fixedAssetPresentation.dataSources.FixedAssetDataSource.routines.reset.executeOutput> {
    this.resetData();
  }

  private resetData() {
    this.data = null;
  }

  private async fillTranslationsMap(
    input: fixedAssetPresentation.dataSources.FixedAssetDataSource.callbacks.query.input
  ) {
    this.translationsMap = await getTranslationsMap(
      input.session,
      [
        'fixedAssets.required',
        'fixedAsset.create',
        'fixedAsset.save',
        'fixedAsset.createFixedAsset',
        'fixedAsset.msg.saving',
        'fixedAsset.msg.saved',
        'fixedAsset.msg.failed',
        'fixedAssets.salvageValue.greaterThanAcquisition',
      ],
      'everest.inv.fad/fixedAssets'
    );
  }

  private async fillEntityCurrencyMap(
    input: fixedAssetPresentation.dataSources.FixedAssetDataSource.callbacks.query.input
  ) {
    const entityClient = await Entity.client(input.session);
    const entites = await entityClient.queryActiveEntities({}, [
      'id',
      'currency',
    ]);

    this.entityToCurrencyMap = new Map(
      entites.map((entity) => [entity.id, entity.currency])
    );
  }

  private async fillLinkToSource(session: ISession) {
    const sourceJournalEntryLineId = this.data.sourceJournalEntryLineId;
    if (!sourceJournalEntryLineId) {
      return;
    }

    const journalEntryLine = await JournalEntryLine.read(
      session,
      {
        id: sourceJournalEntryLineId,
      },
      [
        {
          'JournalEntryLine-JournalEntryHeader': [
            'id',
            'type',
            'sourceFinancialDocumentId',
          ],
        },
      ]
    );
    if (journalEntryLine) {
      this.data.linkToSource = getSourceLink(
        journalEntryLine['JournalEntryLine-JournalEntryHeader']
      );
    }
  }

  private async updateDepreciationPolicyHelper(
    session: ISession,
    forceUpdate: boolean = false
  ) {
    if (forceUpdate || !this.depreciationPolicyHelper) {
      this.depreciationPolicyHelper = await depreciationPolicyFinder(session, [
        this.data.entityId,
      ]);
    }
  }

  private setFixedAssetTypeRelatedFields(
    typeRelatedFields: FixedAssetTypeRelatedFields
  ) {
    set(this.data, 'typeId', typeRelatedFields.typeId);
    set(
      this.data,
      'usefulLife',
      getUsefulLifeText(
        typeRelatedFields.usefulLife,
        typeRelatedFields.usefulLifeDurationType
      )
    );
    set(
      this.data,
      'depreciationInterval',
      typeRelatedFields.depreciationInterval
    );
    set(
      this.data,
      'DepreciationScheduleHeader.DepreciationRule.minimumAmount',
      typeRelatedFields.minimumAmount
    );
    set(
      this.data,
      'DepreciationScheduleHeader.DepreciationRule.DepreciationPolicy.name',
      typeRelatedFields.policy
    );
    set(
      this.data,
      'DepreciationScheduleHeader.DepreciationRule.DepreciationPolicy.depreciationMethod',
      typeRelatedFields.depreciationMethod
    );
  }

  private handleFixedAssetTypeChange(
    newFixedAssetTypeId: FixedAsset.FixedAsset['typeId']
  ) {
    const typeRelatedFields =
      this.depreciationPolicyHelper?.getFixedAssetTypeFromFixedAssetTypeId(
        newFixedAssetTypeId
      );

    if (typeRelatedFields) {
      this.setFixedAssetTypeRelatedFields(typeRelatedFields);
    }
  }

  private determineDepreciationMethod() {
    const isLessThanThreshold =
      this.data.acquisitionValue?.amount &&
      this.data.DepreciationScheduleHeader?.DepreciationRule?.minimumAmount
        ?.amount &&
      this.data.acquisitionValue.amount.lessThan(
        this.data.DepreciationScheduleHeader?.DepreciationRule.minimumAmount
          .amount
      );

    set(
      this.data,
      'depreciationMethod',
      isLessThanThreshold
        ? EvstDepreciationMethod.Immediate
        : this.isDepreciationMethodChangedByUser
          ? this.chosenDepreciationMethod
          : this.data.DepreciationScheduleHeader?.DepreciationRule
              ?.DepreciationPolicy?.depreciationMethod ??
            this.data.depreciationMethod
    );
  }

  private async updateDisbledFixedAssetTypeIds(session: ISession) {
    const validDepreciationPolicies = await DepreciationPolicy.query(
      session,
      {
        where: {
          'DepreciationPolicyEntityMapping-DepreciationPolicy': {
            entityId: this.data.entityId,
          },
          ...(this.data.assetAccountId
            ? {
                assetAccountId: this.data.assetAccountId,
              }
            : {}),
        },
      },
      ['id']
    );
    const validDepreciationPolicyIds = new Set(
      validDepreciationPolicies.map(
        (depreciationPolicy) => depreciationPolicy.id
      )
    );

    const fixedAssetTypes = await FixedAssetType.query(
      session,
      {
        where: {},
      },
      [
        'id',
        {
          DepreciationRules: [
            'id',
            {
              DepreciationPolicy: ['id'],
            },
          ],
        },
      ]
    );

    this.data.disabledFixedAssetTypeIds = fixedAssetTypes
      .filter((fixedAssetType) => {
        const depreciationRules = fixedAssetType.DepreciationRules;
        if (depreciationRules.length === 0) {
          return true;
        }
        return !depreciationRules.some((depreciationRule) =>
          validDepreciationPolicyIds.has(depreciationRule.DepreciationPolicy.id)
        );
      })
      .map((fixedAssetType) => fixedAssetType.id);
  }

  private getRequiredValidations(): Validation[] {
    const requiredMessage = this.translationsMap.get('fixedAssets.required');
    const greaterThanAcquisition = this.translationsMap.get(
      'fixedAssets.salvageValue.greaterThanAcquisition'
    );
    const requiredFieldsVAlidation: Validation = async (context, data) => {
      let isValid = true;
      if (!data.name) {
        context.addError(['Page'], requiredMessage, 'title');
        isValid = false;
      }
      if (!data.entityId) {
        context.addError([], requiredMessage, 'entityId');
        isValid = false;
      }
      if (!data.currency) {
        context.addError([], requiredMessage, 'currency');
        isValid = false;
      }
      if (!data.acquisitionValue?.amount) {
        context.addError([], requiredMessage, 'acquisitionValue', 'amount');
        isValid = false;
      }
      if (!data.acquisitionDate) {
        context.addError([], requiredMessage, 'acquisitionDate');
        isValid = false;
      }
      if (!data.depreciationStartDate) {
        context.addError([], requiredMessage, 'depreciationStartDate');
        isValid = false;
      }
      if (!data.typeId) {
        context.addError([], requiredMessage, 'typeId');
        isValid = false;
      }
      if (!data.depreciationMethod) {
        context.addError([], requiredMessage, 'depreciationMethod');
        isValid = false;
      }
      if (!data.depreciationInterval) {
        context.addError([], requiredMessage, 'depreciationInterval');
        isValid = false;
      }
      if (!data.departmentId) {
        context.addError([], requiredMessage, 'departmentId');
        isValid = false;
      }
      if (!data.status) {
        context.addError([], requiredMessage, 'status');
        isValid = false;
      }
      if (
        data.salvageValue?.amount &&
        data.acquisitionValue?.amount &&
        data.salvageValue.amount.greaterThan(data.acquisitionValue.amount)
      ) {
        context.addError([], greaterThanAcquisition, 'salvageValue', 'amount');
        isValid = false;
      }
      return isValid;
    };

    return [requiredFieldsVAlidation];
  }
}

const presentationImplementation: fixedAssetPresentation.implementation = {
  FixedAssetDataSource: () => new FixedAssetDataSource(),
};

export default presentationImplementation;
