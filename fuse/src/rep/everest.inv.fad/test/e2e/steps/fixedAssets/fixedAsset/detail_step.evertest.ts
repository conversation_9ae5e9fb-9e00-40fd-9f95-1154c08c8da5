// FixedAssetSteps.ts

import { ContinueOnFailure, Step, Table } from 'gauge-ts';
import { FixedAssetModal } from './detail_modal.evertest';
import { baseSteps } from '@pkg/everest.base.test/test/e2e/steps/BaseSteps.evertest';
import { FixedAssetsPage } from '../fixedAssets/page.evertest';
import { toastMessageSteps } from '@pkg/everest.base.test/test/e2e/steps/ToastMessageSteps.evertest';
import { convertAllVariableInTableToValueV2 } from '@pkg/everest.base.test/test/e2e/utils/data/convertDataUtils.evertest';
import { API_DATE_FORMAT } from '@pkg/everest.base.test/test/e2e/utils/constants/constant.evertest';
import { getMomentByE2E } from '@pkg/everest.base.test/test/e2e/utils/dateUtils.evertest';

export default class FixedAssetSteps {
  static fixedAssetName: string = 'Name';
  @Step('On the Fixed Asset modal, fill header information <table>')
  public async fillCoAHeader(referenceKey: string, table: Table) {
    const detailModal = new FixedAssetModal();
    const [convertedDataTable] = await baseSteps.convertTableAndSaveReference(
      referenceKey,
      table
    );
    const [name, desc] = convertedDataTable.getColumnNames();
    const row = convertedDataTable.getTableRows()[0];
    await detailModal.header.setTitle(row.getCell(name));
    if (desc) {
      await detailModal.header.setDescription(row.getCell(desc));
    }
  }

  @Step('On the Fixed Asset modal, fill the form then save as reference key <key> <table>')
  public async fillFAForm(faKey: string, table: Table) {
    const detailModal = new FixedAssetModal();
    let [convertedInputTable] = await baseSteps.convertTableAndSaveReference(
      faKey,
      table
    );
    
    const row = convertedInputTable.getTableRows()[0]; // Alway just 1 row
    const name = row.getCell(FixedAssetSteps.fixedAssetName);
    await detailModal.header.setTitle(name);

    const headers = convertedInputTable.getColumnNames();
    const fixedAssetNameIndex = headers.indexOf(FixedAssetSteps.fixedAssetName);
    headers.splice(fixedAssetNameIndex, 1);
    const values = row.getCellValues();
    values.splice(fixedAssetNameIndex, 1);

    const tableWithoutName = new Table(headers);
    tableWithoutName.addRow(values);

    await detailModal.fixedAssetDetailsBlock.fillForm(tableWithoutName);
  }

  @Step(
    'On the Fixed Asset modal, click on the create button to create the fixed asset'
  )
  public async clickSaveButton() {
    // On the Modal Click on Create
    const detailModal = new FixedAssetModal();
    await detailModal.dynamicButtonOnModal('Create').click();
    await baseSteps.waitForPageLoadedCompletely();
    await toastMessageSteps.waitForCenterMessageAppearsThenDisappears();
    // Ensure that we navigated back to the List
    const fixedAssetCategoriesPage = new FixedAssetsPage();
    await fixedAssetCategoriesPage.fixedAssetsTable.isDisplayed();
  }

  @ContinueOnFailure()
  @Step([
    'On the Fixed Asset modal, Check that the form displays with data below <dataTable>',
  ])
  public async checkFormDisplay(dataTable: Table) {
    const detailModal = new FixedAssetModal();
    dataTable = await convertAllVariableInTableToValueV2(dataTable);
    await detailModal.fixedAssetDetailsBlock.verifyFieldGroupDisplaysWithExpectedData(
      dataTable
    );
  }

  @Step('On the Fixed Asset page, retire the fixed asset')
  public async fillStatus() {
    const detailModal = new FixedAssetModal();
    await detailModal.fixedAssetDetailsBlock.setValue('Status', 'Retired');
    await detailModal.fixedAssetDetailsBlock.setValue(
      'Disposal Date',
      getMomentByE2E().format(API_DATE_FORMAT)
    );
  }
}
