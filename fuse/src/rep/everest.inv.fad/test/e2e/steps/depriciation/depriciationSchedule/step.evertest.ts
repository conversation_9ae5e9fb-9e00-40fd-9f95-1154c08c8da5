// DepreciationScheduleSteps.ts

import { Step, Table } from 'gauge-ts';
import { DepreciationSchedulePage } from './page.evertest';
import { baseSteps } from '@pkg/everest.base.test/test/e2e/steps/BaseSteps.evertest';
import { convertAllVariableInTableToValueV2 } from '@pkg/everest.base.test/test/e2e/utils/data/convertDataUtils.evertest';

export default class DepreciationScheduleSteps {
  @Step('Navigate to the Depreciation Schedule page')
  public async navigateToDepreciationSchedule() {
    const depreciationSchedulePage = new DepreciationSchedulePage();
    await depreciationSchedulePage.goToSelf();
    await baseSteps.waitForPageLoadedCompletely();
  }

  @Step(
    'On the Depreciation Schedule page, check Depreciation Method is <method>'
  )
  public async checkDepreciationMethodIsImmediate(method: Table) {
    const depreciationSchedulePage = new DepreciationSchedulePage();
    const actualDepreciationMethod =
      await depreciationSchedulePage.headerSection.verifyFieldGroupDisplaysWithExpectedData(
        method
      );
  }
  @Step(
    'On the Depreciation Schedule page, check Depreciable amount is <amount>'
  )
  public async checkDepreciableAmount(amount: Table) {
    const depreciationSchedulePage = new DepreciationSchedulePage();
    const actualDepreciationMethod =
      await depreciationSchedulePage.summarySection.verifyBlockDisplaysWithExpectedData(
        amount
      );
  }
  @Step('On the Depreciation Schedule page, check Remaining Amount is <amount>')
  public async RemainingAmount(amount: Table) {
    const depreciationSchedulePage = new DepreciationSchedulePage();
    const actualDepreciationMethod =
      await depreciationSchedulePage.summarySection.verifyBlockDisplaysWithExpectedData(
        amount
      );
  }
  @Step(
    'On the Depreciation Schedule page, click on Posted Journal Entry value <data>'
  )
  public async clickJournalEntry(data: Table) {
    const depreciationSchedulePage = new DepreciationSchedulePage();
    data = await convertAllVariableInTableToValueV2(data);
    const rows = data.getTableRows();
    await depreciationSchedulePage.depreciationPlanTable.clickCellByHeaderAndRelevantData(
      'Journal Entry',
      rows[0].getCellValues()
    );
    await baseSteps.waitForPageLoadedCompletely();
  }
}
