# Fixed Assets

> **This is an overview table with the most common used data**

   |Asset Name                            |Asset Category                        |Asset Type                            |Entity Name|Policy Name                           |Depreciation Asset                    |
   |--------------------------------------|--------------------------------------|--------------------------------------|-----------|--------------------------------------|--------------------------------------|
   |FAAuto{{random-string-with-length: 2}}|AutoAC{{random-string-with-length: 2}}|AutoAT{{random-string-with-length: 2}}|Flow Inc.  |AutoPN{{random-string-with-length: 2}}|AutoDA{{random-string-with-length: 2}}|


## A line from a source does not flow to Fixed Assets page as unscheduled when the account on the line does not have a created policy

> [UAT] - A line from a source does not flow to Fixed Assets page as unscheduled when the account on the line does not have a created policy
@UA-203-2

>**Fixed Assets Unscheduled Check**

* Navigate to the Fixed Assets page
* On the header, select Entity "Flow Inc."
* On the Fixed Assets page, select tab "Unscheduled"
* On the Fixed Assets page, check that the unscheduled table is empty

## A line coded to a fixed asset account with a policy appears on the Fixed Assets page as Unscheduled for review
Tags: failing
> [UAT] - A line coded to a fixed asset account with a policy appears on the Fixed Assets page as Unscheduled for review
@UA-203-1

> [UAT] - Lines in the unscheduled tab should autopopulate some fields from the Policy and/or source
@UA-203-3

> **Create Vendor Bill**

* Go to the Vendor Bills page
* On the Vendor Bills page, select Create Manually option from Create button
* On the Create Vendor Bill page, fill Vendor Bill Details then store to key "vbh1" with data below

   | Vendor |           Bill Number            |    Entity     | Currency  | Bill Date  |
   | ------ | -------------------------------- | ------------- | --------- | ---------- |
   | Audi   | {{random-string-with-length:10}} | <Entity Name> | US Dollar | 09/30/2023 |

* On the Create Vendor Bill page, fill Vendor Bill Line table and store to key Reference key with below

   | Reference | Description |               Account               |           Department           | Amount |
   | --------- | ----------- | ----------------------------------- | ------------------------------ | ------ |
   | vbl1      | test        | 121101 Computer & Network Equipment | DPT-1 Research and Development | 21000  |

* Click Create button to create new Vendor Bill
* Go to the Payments page
* Create Record Payment for Open Vendor Bill item from

   |     Vendor      |     Bill Number     |
   | --------------- | ------------------- |
   | {{vbh1.Vendor}} | {{vbh1.BillNumber}} |

* On the Create Payment page, fill Payment Details with data below and store to key "p1"

   |      Payment Account       |
   | -------------------------- |
   | 111104 — Wells Fargo Sweep |

* On create page header, click on the primary button and wait for success toast to appear

> **Create Asset Categories**

* Navigate to the Fixed Asset Categories page
* On the header, click on button "Create" and wait for modal to appear
* On the Fixed Asset Category modal, fill the category information then save as reference name "fac"

   |       Name       |
   | ---------------- |
   | <Asset Category> |

* On the modal, click on the confirmation button and wait for page to reload successfully
* On the Fixed Asset Categories page, check that the table displays information correctly

   |     Name     |
   | ------------ |
   | {{fac.Name}} |

> **Create Asset Types within the categories**

* Navigate to the Fixed Asset Types page
* On the header, click on button "Create" and wait for modal to appear
* On the Fixed Asset Type modal, fill the form with data below and save as reference key "fat"

   |Name        |Category    |
   |------------|------------|
   |<Asset Type>|{{fac.Name}}|

* On the modal, click on the confirmation button and wait for page to reload successfully
* On Fixed Asset Types page, check if the table contains

   |     Name     |     Category     |
   | ------------ | ---------------- |
   | {{fat.Name}} | {{fat.Category}} |

>**Create Depreciation policy for the Asset Type**

* Navigate to the Depreciation Policies page
* On the page header, click on the button label "Create" and wait for the page is loaded completely
* On the Depreciation Policy page, enter target entity <Entity Name> then save as reference key "dp"
* On the Depreciation Policy page, name policy to <Policy Name> then save as reference key "dp"
* On the Depreciation Policy page, set the policy details then save as reference key "dp"

   |            Asset Account            |         Accumulated Depreciation Account         |       Depreciation Expense Account        |   Gain/Loss Account   | Depreciation Interval | Depreciation Method |
   | ----------------------------------- | ------------------------------------------------ | ----------------------------------------- | --------------------- | --------------------- | ------------------- |
   | 121101 Computer & Network Equipment | 121201 Accum Depr - Computer & Network Equipment | 613801 Depreciation Computers & Equipment | 841101 Asset Disposal | Monthly               | Straight Line       |

* On the Depreciation Policy page, set the policy rules then save as reference key "dp"

   |Fixed Asset Type|Code|Threshold|Currency |Useful Life|Duration Unit|
   |----------------|----|---------|---------|-----------|-------------|
   |{{fat.Name}}    |1.1 |1200     |US Dollar|3          |Years        |

* On the page header, click on the button label "Create" and wait for tab to rebuild

>**Fixed Assets Unscheduled Check**

* Navigate to the Fixed Assets page
* On the header, select Entity <Entity Name>
* On the Fixed Assets page, select tab "Unscheduled"
* On the Fixed Assets page, check if the unscheduled table contains

   |Name        |
   |------------|
   |{{fat.Name}}|

>**Lines in the unscheduled tab should autopopulate some fields from the Policy and/or source**

* On the Fixed Assets page, on unscheduled table, click on row

   |Name                |
   |--------------------|
   |{{vbl1.Description}}|

>* On the Fixed Asset modal, fill header information
>
>   |Name|
>   |----|
>   |test|

* On the Fixed Asset modal, Check that the form displays with data below

   |Entity       |Currency |Fixed Asset Type    |Depreciation Policy|Useful Life                          |Depreciation Method       |Depreciation Interval      |
   |-------------|---------|--------------------|-------------------|-------------------------------------|--------------------------|---------------------------|
   |<Entity Name>|US Dollar|{{vbl1.Description}}|{{dp.Name}}        |{{dp.UsefulLife}} {{dp.DurationUnit}}|{{dp.Depreciation Method}}|{{dp.DepreciationInterval}}|

## A user is able to successfully create an asset from a source

> [UAT] - A user is able to successfully create an asset from a source
@UA-203-4

> **Create Vendor Bill**

* Go to the Vendor Bills page
* On the Vendor Bills page, select Create Manually option from Create button
* On the Create Vendor Bill page, fill Vendor Bill Details then store to key "vbh1" with data below

   | Vendor |           Bill Number            |    Entity     | Currency  | Bill Date  |
   | ------ | -------------------------------- | ------------- | --------- | ---------- |
   | Audi   | {{random-string-with-length:10}} | <Entity Name> | US Dollar | 09/30/2023 |

* On the Create Vendor Bill page, fill Vendor Bill Line table and store to key Reference key with below

   | Reference |                 Description              |               Account               |           Department           | Amount |
   | --------- | ---------------------------------------- | ----------------------------------- | ------------------------------ | ------ |
   | vbl1      | AutoDesc{{random-string-with-length: 2}} | 121101 Computer & Network Equipment | DPT-1 Research and Development | 21000  |

* Click Create button to create new Vendor Bill
* Go to the Payments page
* Create Record Payment for Open Vendor Bill item from

   |     Vendor      |     Bill Number     |
   | --------------- | ------------------- |
   | {{vbh1.Vendor}} | {{vbh1.BillNumber}} |

* On the Create Payment page, fill Payment Details with data below and store to key "p1"

   |      Payment Account       |     Description     |
   | -------------------------- | ------------------- |
   | 111104 — Wells Fargo Sweep | Payment Description |

* On create page header, click on the primary button and wait for success toast to appear

> **Create Asset Categories**

* Navigate to the Fixed Asset Categories page
* On the header, click on button "Create" and wait for modal to appear
* On the Fixed Asset Category modal, fill the category information then save as reference name "fac1"

   |       Name       |
   | ---------------- |
   | <Asset Category> |

* On the modal, click on the confirmation button and wait for page to reload successfully
* On the Fixed Asset Categories page, check that the table displays information correctly

   |     Name      |
   | ------------- |
   | {{fac1.Name}} |

> **Create Asset Types within the categories**

* Navigate to the Fixed Asset Types page
* On the header, click on button "Create" and wait for modal to appear
* On the Fixed Asset Type modal, fill the form with data below and save as reference key "fat1"

   |Name        |Category     |
   |------------|-------------|
   |<Asset Type>|{{fac1.Name}}|

* On the modal, click on the confirmation button and wait for page to reload successfully
* On Fixed Asset Types page, check if the table contains

   |     Name      |     Category      |
   | ------------- | ----------------- |
   | {{fat1.Name}} | {{fat1.Category}} |

>**Create Depreciation policy for the Asset Type**

* Navigate to the Depreciation Policies page
* On the page header, click on the button label "Create" and wait for the page is loaded completely
* On the Depreciation Policy page, enter target entity <Entity Name> then save as reference key "dp"
* On the Depreciation Policy page, name policy to <Policy Name> then save as reference key "dp"
* On the Depreciation Policy page, set the policy details then save as reference key "dp"

   |            Asset Account            |         Accumulated Depreciation Account         |       Depreciation Expense Account        |   Gain/Loss Account   | Depreciation Interval | Depreciation Method |
   | ----------------------------------- | ------------------------------------------------ | ----------------------------------------- | --------------------- | --------------------- | ------------------- |
   | 121101 Computer & Network Equipment | 121201 Accum Depr - Computer & Network Equipment | 613801 Depreciation Computers & Equipment | 841101 Asset Disposal | Monthly               | Straight Line       |

* On the Depreciation Policy page, set the policy rules then save as reference key "dp"

   |Fixed Asset Type|Code|Threshold|Currency |Useful Life|Duration Unit|
   |----------------|----|---------|---------|-----------|-------------|
   |{{fat1.Name}}   |1.1 |1200     |US Dollar|3          |Years        |

* On the page header, click on the button label "Create" and wait for tab to rebuild

>**fill fixed asset**

* Navigate to the Fixed Assets page
* On the header, select Entity <Entity Name>
* On the Fixed Assets page, select tab "Unscheduled"
* On the Fixed Assets page, on unscheduled table, click on row

   |Name                |
   |--------------------|
   |{{vbl1.Description}}|

* On the Fixed Asset modal, fill the form then save as reference key "faKey"

   |     Name     | Fixed Asset Type |       
   | ------------ | ---------------- |
   | <Asset Name> | {{fat1.Name}}    | 

* On the modal, click on the "Create" button and wait for success message

* On the Fixed Assets page, select tab "Scheduled"
* On the Fixed Assets page, check if the assets table contains

   |Type         |Description   |Source             |Depreciation Status|
   |-------------|--------------|-------------------|-------------------|
   |{{fat1.Name}}|{{faKey.Name}}|{{start-with:VBL-}}|Not Started        |

>**A user is able to successfully create an asset from a source**

> [UAT] - Run depreciation on active fixed assets
@UA-203-5

* Navigate to the Record Depreciation page
* Select Entity <Entity Name>
* Set the date to one month after today
* Select the first row on the table and post journal entries
* Navigate to the Fixed Assets page
* On the header, select Entity <Entity Name>
* On the Fixed Assets page, check if the assets table contains

   |Type         |Description   |Depreciation Status|
   |-------------|--------------|-------------------|
   |{{fat1.Name}}|{{faKey.Name}}|Ongoing            |
   
## Retire an asset and stop depreciation

> [UAT] - Retire an asset and stop depreciation
@UA-203-6

> **Create Asset Categories**

* Navigate to the Fixed Asset Categories page
* On the header, click on button "Create" and wait for modal to appear
* On the Fixed Asset Category modal, fill the category information then save as reference name "fac2"

   |       Name       |
   | ---------------- |
   | <Asset Category> |

* On the modal, click on the confirmation button and wait for page to reload successfully
* On the Fixed Asset Categories page, check that the table displays information correctly

   |     Name      |
   | ------------- |
   | {{fac2.Name}} |

> **Create Asset Types within the categories**

* Navigate to the Fixed Asset Types page
* On the header, click on button "Create" and wait for modal to appear
* On the Fixed Asset Type modal, fill the form with data below and save as reference key "fat2"

   |  Name      |  Category   |
   |------------|-------------|
   |<Asset Type>|{{fac2.Name}}|

* On the modal, click on the confirmation button and wait for page to reload successfully
* On Fixed Asset Types page, check if the table contains

   |     Name      |     Category      |
   | ------------- | ----------------- |
   | {{fat2.Name}} | {{fat2.Category}} |

>**Create Depreciation policy for the Asset Type**
* Navigate to the Depreciation Policies page
* On the page header, click on the button label "Create" and wait for the page is loaded completely
* On the Depreciation Policy page, enter target entity <Entity Name> then save as reference key "dp"
* On the Depreciation Policy page, name policy to <Policy Name> then save as reference key "dp"
* On the Depreciation Policy page, set the policy details then save as reference key "dp"

   |            Asset Account            |         Accumulated Depreciation Account         |       Depreciation Expense Account        |   Gain/Loss Account   | Depreciation Interval | Depreciation Method |
   | ----------------------------------- | ------------------------------------------------ | ----------------------------------------- | --------------------- | --------------------- | ------------------- |
   | 121101 Computer & Network Equipment | 121201 Accum Depr - Computer & Network Equipment | 613801 Depreciation Computers & Equipment | 841101 Asset Disposal | Monthly               | Straight Line       |

* On the Depreciation Policy page, set the policy rules then save as reference key "dp"

   | Fixed Asset Type | Code | Threshold | Currency  | Useful Life | Duration Unit |
   | ---------------- | ---- | --------- | --------- | ----------- | ------------- |
   |{{fat2.Name}}     | 1.1  | 1200      | US Dollar | 3           | Years         |

* On the page header, click on the button label "Create" and wait for tab to rebuild

>**Create Fixed Asset**

* Navigate to the Fixed Assets page
* On Fixed Assets page, click on the create button
* On the Fixed Asset modal, fill the form then save as reference key "faKey"

   |     Name     | Fixed Asset Type |           Department           |    Entity     | Acquisition Date | Acquisition Value | Currency | Serial Number | Internal Serial Number |
   | ------------ | ---------------- | ------------------------------ | ------------- | ---------------- | ----------------- | -------- | ------------- | ---------------------- |
   | <Asset Name> | {{fat2.Name}}    | DPT-1 Research and Development | <Entity Name> | 07/28/2024       | 1,200.00          | Euro     | 1234513gh     | 123hh1123fg            |

* On the Fixed Asset modal, click on the create button to create the fixed asset

>**Retire Fixed Asset**
* On the header, select Entity <Entity Name>
* On the Fixed Assets page, on scheduled table, click on row

   |  Description   |
   | -------------- |
   | {{faKey.Name}} |
   
* On the page header, click on the button label "Revise"
* On the Fixed Asset page, retire the fixed asset
* On the page header, click on the button label "Save"
* On the modal, click on the confirmation button and wait for page to reload successfully
* Navigate to the Fixed Assets page
* On the header, select Entity <Entity Name>
* On the Fixed Assets page, check if the assets table contains

   |  Description  | Status  |            Type          |
   | ------------- | ------- | ------------------------ |
   | {{faKey.Name}}| Retired | {{faKey.FixedAssetType}} |
