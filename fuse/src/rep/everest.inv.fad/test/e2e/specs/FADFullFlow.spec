# FAD Full Flow
> **This is an overview table with the most common used data**

   |Asset Name                                     |Asset Category                           |Asset Type                                |Entity Name|Policy Name                                  |
   |-----------------------------------------------|-----------------------------------------|------------------------------------------|-----------|---------------------------------------------|
   |Macbook ProFull{{random-string-with-length: 2}}|ACFADFull{{random-string-with-length: 2}}|LaptopFull{{random-string-with-length: 2}}|Flow Inc.  |Office Assets{{random-string-with-length: 2}}|

## Create Category, Type & Asset

> **Create Asset Categories**

* Navigate to the Fixed Asset Categories page
* On the header, click on button "Create" and wait for modal to appear
* On the Fixed Asset Category modal, fill the category information then save as reference name "fac"

   |       Name       |
   | ---------------- |
   | <Asset Category> |

* On the modal, click on the confirmation button and wait for page to reload successfully
* On the Fixed Asset Categories page, check that the table displays information correctly

   |     Name     |
   | ------------ |
   | {{fac.Name}} |

> **Create Asset Types within the categories**

* Navigate to the Fixed Asset Types page
* On the header, click on button "Create" and wait for modal to appear
* On the Fixed Asset Type modal, fill the form with data below and save as reference key "fat"

   |Name        |Category    |
   |------------|------------|
   |<Asset Type>|{{fac.Name}}|

* On the modal, click on the confirmation button and wait for page to reload successfully
* On Fixed Asset Types page, check if the table contains

   |     Name     |     Category     |
   | ------------ | ---------------- |
   | {{fat.Name}} | {{fat.Category}} |

**Create Depreciation policy for the Asset Type**
* Navigate to the Depreciation Policies page
* On the page header, click on the button label "Create" and wait for the page is loaded completely
* On the Depreciation Policy page, enter target entity <Entity Name> then save as reference key "dp"
* On the Depreciation Policy page, name policy to <Policy Name> then save as reference key "dp"
* On the Depreciation Policy page, set the policy details then save as reference key "dp"

   |            Asset Account            |         Accumulated Depreciation Account         |       Depreciation Expense Account        |   Gain/Loss Account   | Depreciation Interval | Depreciation Method |
   | ----------------------------------- | ------------------------------------------------ | ----------------------------------------- | --------------------- | --------------------- | ------------------- |
   | 121101 Computer & Network Equipment | 121201 Accum Depr - Computer & Network Equipment | 613801 Depreciation Computers & Equipment | 841101 Asset Disposal | Monthly               | Straight Line       |

* On the Depreciation Policy page, set the policy rules then save as reference key "dp"

   |Fixed Asset Type|Code|Threshold|Currency|Useful Life|Duration Unit|
   |----------------|----|---------|--------|-----------|-------------|
   |{{fat.Name}}    |1.1 |1200     |Euro    |3          |Years        |

* On the page header, click on the button label "Create" and wait for tab to rebuild

> **Create an actual Asset**
>
> **Hint:** We create the asset without assingment as this has no effect on the actual
>       Asset Management/Depreciation calculation features.

* Navigate to the Fixed Assets page
* On Fixed Assets page, click on the create button
* On the Fixed Asset modal, fill the form then save as reference key "faKey"

   |Name        |Fixed Asset Type|Department                    |Entity       |Acquisition Date|Acquisition Value|Currency|Serial Number|Internal Serial Number|
   |------------|----------------|------------------------------|-------------|----------------|-----------------|--------|-------------|----------------------|
   |<Asset Name>|{{fat.Name}}    |DPT-1 Research and Development|<Entity Name>|07/28/2024      |1,200.00         |Euro    |1234513gh    |123hh1123fg           |

* On the Fixed Asset modal, click on the create button to create the fixed asset
* On the header, select Entity <Entity Name>
* On the Fixed Assets page, check if the assets table contains

   |Description   |Depreciation Status|Type        |
   |--------------|-------------------|------------|
   |{{faKey.Name}}|Not Started        |{{fat.Name}}|

> **Hint** The Depreciation Status is dependend on the date, hence
> we should improve the test by using a dynamic date - otherwise the test will fail upon august

**Depreciation Process with Acquisition Value Less Than Minimum**

* On Fixed Assets page, click on the create button
* On the Fixed Asset modal, fill the form then save as reference key "fa2Key"

   |Name        |Fixed Asset Type|Department                    |Entity       |Acquisition Date|Acquisition Value|Currency|Serial Number|Internal Serial Number|Salvage Value|Depreciation Start Date|Depreciation Interval|Depreciation Method|
   |------------|----------------|------------------------------|-------------|----------------|-----------------|--------|-------------|----------------------|-------------|-----------------------|---------------------|-------------------|
   |<Asset Name>|{{fat.Name}}    |DPT-1 Research and Development|<Entity Name>|10/21/2024      |1,000.00         |Euro    |323          |123m1123fg            |110.00       |10/21/2024             |Monthly              |Straight Line      |

* On the Fixed Asset modal, click on the create button to create the fixed asset

* On the header, select Entity <Entity Name>
* On the Fixed Assets page, check if the assets table contains

   |  Description  | Depreciation Status |
   | ------------- | ------------------- |
   |{{fa2Key.Name}}| Completed           |

* Navigate to the Depreciation Schedules page
* Click on the row with Fixed Asset "{{fa2Key.Name}}"
* On the Depreciation Schedule page, check Depreciation Method is

   | Depreciation Method |
   | ------------------- |
   | Immediate           |

* On the Depreciation Schedule page, check Depreciable amount is

   | Depreciable Amount |
   | ------------------ |
   | 890.00             |

* On the Depreciation Schedule page, click on Posted Journal Entry value

   |Depreciable Amount|Posted|
   |------------------|------|
   |890.00            |True  |

* On the Journal Entry page, check if table contains

   |                      Account                       |
   | -------------------------------------------------- |
   | 613801 — Depreciation Computers & Equipment        |
   | 121201 — Accum Depr - Computer & Network Equipment |

**Depreciation Process with Acquisition Value Greater Than or Equal to Minimum**

* Navigate to the Record Depreciation page
* Select Entity <Entity Name>
* Set the date to one month after today
* Select the first row on the table and post journal entries

* Navigate to the Fixed Assets page
* On the header, select Entity <Entity Name>
* On the Fixed Assets page, check if the assets table contains

   |Description   |Depreciation Status|Type        |
   |--------------|-------------------|------------|
   |{{faKey.Name}}|Ongoing            |{{fat.Name}}|

* Navigate to the Depreciation Schedules page
* Click on the row with Fixed Asset "{{faKey.Name}}"
* On the Depreciation Schedule page, check Depreciable amount is

   | Depreciable Amount |
   | ------------------ |
   | 1,200.00           |

* On the Depreciation Schedule page, check Remaining Amount is

   | Remaining Asset Value |
   | --------------------- |
   | 1,166.67              |

* On the Depreciation Schedule page, click on Posted Journal Entry value

   | Posted |
   | ------ |
   | True   |

* On the Journal Entry page, check if table contains

   |                      Account                       |
   | -------------------------------------------------- |
   | 613801 — Depreciation Computers & Equipment        |
   | 121201 — Accum Depr - Computer & Network Equipment |
