import type {
  ControllerClientProvider,
  ISession,
} from '@everestsystems/content-core';
import { EvstTeamType } from '@pkg/everest.hr.base/types/enums/TeamType';
import { TeamA<PERSON> } from '@pkg/everest.hr.base/types/presentations/publicApi/api/TeamApi/TeamApi';
import { ActivityMapping } from '@pkg/everest.timetracking/types/ActivityMapping';
import { Allocation } from '@pkg/everest.timetracking/types/Allocation';
import { Member } from '@pkg/everest.timetracking/types/Member';
import { AllocationApi } from '@pkg/everest.timetracking/types/presentations/publicApi/api/AllocationApi/AllocationApi';
import type { ProjectApiImplementation } from '@pkg/everest.timetracking/types/presentations/publicApi/api/ProjectApi/ProjectApiImplementation';
import { TimeEntryApi } from '@pkg/everest.timetracking/types/presentations/publicApi/api/TimeEntryApi/TimeEntryApi';
import { ProjectActivity } from '@pkg/everest.timetracking/types/ProjectActivity';
import { ProjectPosition } from '@pkg/everest.timetracking/types/ProjectPosition';
import { TimeEntry } from '@pkg/everest.timetracking/types/TimeEntry';
import { TimetrackingProject } from '@pkg/everest.timetracking/types/TimetrackingProject';
import { uniq } from 'lodash';

import { verifyPosition } from './positionUtil';

async function deleteProjects(
  session: ControllerClientProvider,
  input: ProjectApiImplementation.Methods.deleteProjects.Input
) {
  const { projectIds } = input;

  if (!projectIds || projectIds.length === 0) {
    return {
      output: {},
    };
  }

  // Get the Team ids
  const projects = await TimetrackingProject.query(
    session,
    { where: { id: { $in: projectIds } } },
    ['teamId']
  );

  const teamIdsToDelete = projects
    .filter(
      (project) => project.teamId !== null && project.teamId !== undefined
    )
    .map((project) => project.teamId);

  await TimetrackingProject.deleteMany(session, { id: { $in: projectIds } });

  // Delete Project Team
  await TeamApi.deleteTeams.execute(session, {
    teamIds: teamIdsToDelete,
  });

  // Delete Positions
  const projectPositions = await ProjectPosition.query(
    session,
    { where: { projectId: { $in: projectIds } } },
    ['id']
  );
  await deletePositions(session, {
    positionIds: projectPositions.map((position) => position.id),
  });

  return {
    output: {},
  };
}

async function deleteActivities(
  session: ControllerClientProvider,
  input: ProjectApiImplementation.Methods.deleteActivities.Input
) {
  const { activityIds } = input;

  // validate that no project activities are invoiced already
  const projectActivities = await ProjectActivity.query(
    session,
    { where: { id: { $in: activityIds }, invoiced: true } },
    ['id', 'name', 'milestoneId', 'fixedFeeId']
  );

  if (projectActivities.length > 0) {
    throw new Error(
      'Cannot delete activities that are invoiced: ' +
        projectActivities.map((activity) => ` ${activity.name}`).join(', ')
    );
  }

  await ProjectActivity.deleteMany(session, { id: { $in: activityIds } });

  // delete associated allocations
  const projectAllocations = await Allocation.query(
    session,
    { where: { projectActivityId: { $in: activityIds } } },
    ['id']
  );
  await AllocationApi.deleteAllocations.execute(session, {
    allocationIds: projectAllocations.map((allocation) => allocation.id),
  });

  // delete time entries associated with these activities
  const timeEntries = await TimeEntry.query(
    session,
    { where: { projectActivityId: { $in: activityIds } } },
    ['id']
  );
  await TimeEntryApi.deleteTimeEntries.execute(session, {
    timeEntryIds: timeEntries.map((timeEntry) => timeEntry.id),
  });

  // Delete activity mappings
  await ActivityMapping.deleteMany(session, {
    projectActivityId: { $in: activityIds },
  });

  return {
    output: {},
  };
}

async function deletePositions(
  session: ControllerClientProvider,
  input: ProjectApiImplementation.Methods.deletePositions.Input
) {
  const { positionIds } = input;

  // validate that no project positions are invoiced already
  const projectPositions = await ProjectPosition.query(
    session,
    { where: { id: { $in: positionIds }, invoiced: true } },
    ['id', 'name']
  );

  if (projectPositions.length > 0) {
    throw new Error(
      'Cannot delete positions that are invoiced: ' +
        projectPositions.map((position) => ` ${position.name}`).join(', ')
    );
  }

  await ProjectPosition.deleteMany(session, { id: { $in: positionIds } });

  // Delete members associated with these positions
  const projectMembers = await Member.query(
    session,
    { where: { projectPositionId: { $in: positionIds } } },
    ['id']
  );
  await deleteMembers(session, {
    memberIds: projectMembers.map((member) => member.id),
  });

  return {
    output: {},
  };
}

async function deleteMembers(
  session: ControllerClientProvider,
  input: ProjectApiImplementation.Methods.deleteMembers.Input
) {
  const { memberIds } = input;

  // validate that no project members are invoiced already
  const projectMembers = await Member.query(
    session,
    { where: { id: { $in: memberIds }, invoiced: true } },
    ['id']
  );

  if (projectMembers.length > 0) {
    throw new Error(
      'Cannot delete members that are invoiced: ' +
        projectMembers.map((member) => ` ${member.id}`).join(', ')
    );
  }

  await Member.deleteMany(session, { id: { $in: memberIds } });

  // delete allocations associated with these members
  const projectAllocations = await Allocation.query(
    session,
    { where: { memberId: { $in: memberIds } } },
    ['id']
  );
  await AllocationApi.deleteAllocations.execute(session, {
    allocationIds: projectAllocations.map((allocation) => allocation.id),
  });

  // delete time entries associated with these members
  const timeEntries = await TimeEntry.query(
    session,
    { where: { memberId: { $in: memberIds } } },
    ['id']
  );
  await TimeEntryApi.deleteTimeEntries.execute(session, {
    timeEntryIds: timeEntries.map((timeEntry) => timeEntry.id),
  });

  // Delete activity mappings
  await ActivityMapping.deleteMany(session, {
    memberId: { $in: memberIds },
  });

  return {
    output: {},
  };
}

/**
 * Implementation of the Project API
 */
export default {
  /*
   * Projects
   */
  deleteProjects: {
    execute: ({ session, input }) => deleteProjects(session, input),
  },

  createProject: {
    execute: async ({
      session,
      input,
    }: ProjectApiImplementation.Methods.createProject.Execute.Request): Promise<ProjectApiImplementation.Methods.createProject.Execute.Response> => {
      // Create a Team in HR Teams called "ProjectNameTeam" and assign the projectLeadIds to it as project Leads

      const team = await TeamApi.createTeam.execute(session, {
        teamName: `${input.projectName}Team`,
        startDate: input.startDate ? input.startDate.toPlainDate() : undefined,
        endDate: input.endDate ? input.endDate.toPlainDate() : undefined,
        teamType: EvstTeamType.PsaTeam,
        projectLeads: input.projectLeadIds ?? [],
      });

      // Create a project in TimetrackingProjects that points to this Team
      const { projectLeadIds, ...projectInput } = input;
      const project = await TimetrackingProject.create(
        session,
        {
          teamId: team.teamId,
          ...projectInput,
        },
        ['id', 'uuid']
      );

      return {
        output: {
          projectId: project.id,
          projectUuid: project.uuid || '',
        },
      };
    },
  },

  updateProject: {
    execute: async ({
      session,
      input,
    }: ProjectApiImplementation.Methods.updateProject.Execute.Request): Promise<ProjectApiImplementation.Methods.updateProject.Execute.Response> => {
      // Verify project exists
      const existingProject = await TimetrackingProject.read(
        session,
        { id: input.id },
        ['id', 'version', 'teamId']
      );

      if (!existingProject) {
        throw new Error(`Project with ID ${input.id} not found`);
      }

      // Update the projectLeads in the Team
      await TeamApi.updateTeam.execute(session, {
        id: existingProject.teamId,
        projectLeads: input.projectLeadIds,
      });

      // Update the TimetrackingProject
      const { projectLeadIds, ...projectUpdateData } = input;
      await TimetrackingProject.update(
        session,
        { id: input.id },
        projectUpdateData,
        ['id']
      );

      return {
        output: {},
      };
    },
  },

  /*
   * Activities
   */
  createActivities: {
    async execute({
      session,
      input,
    }: ProjectApiImplementation.Methods.createActivities.Execute.Request): Promise<ProjectApiImplementation.Methods.createActivities.Execute.Response> {
      const { projectId, activities } = input;

      // Create activities
      const createdActivities = await ProjectActivity.createMany(
        session,
        activities.map((activity) => ({
          name: activity.name,
          description: activity.description,
          billable: activity.billable,
          billingType: activity.billingType,
          hoursBudget: activity.hoursBudget,
          milestoneId: activity.milestoneId,
          fixedFeeId: activity.fixedFeeId,
          startDate: activity.startDate,
          endDate: activity.endDate,
          projectId,
        })),
        ['id']
      );

      const activityIds = createdActivities.map((activity) => activity.id);

      // Create activity mappings
      const activityMappings = activityIds.flatMap((activityId, index) =>
        (activities[index].teamMemberIds || []).map((memberId) => ({
          memberId,
          projectActivityId: activityId,
          projectId,
        }))
      );

      if (activityMappings.length > 0) {
        await ActivityMapping.createMany(session, activityMappings);
      }

      return {
        output: {
          activityIds,
          hrProjectIds: [projectId],
        },
      };
    },
  },

  deleteActivities: {
    execute: ({ session, input }) => deleteActivities(session, input),
  },

  updateActivities: {
    async execute({
      session,
      input,
    }: ProjectApiImplementation.Methods.updateActivities.Execute.Request): Promise<ProjectApiImplementation.Methods.updateActivities.Execute.Response> {
      const { activities } = input;

      // Validate input
      if (!activities || activities.length === 0) {
        throw new Error('No activity data provided');
      }

      // Update all activities and their mappings
      await Promise.all(
        activities.map(async (activity) => {
          const { id, teamMemberIds, ...updateFields } = activity;

          // Verify activity exists and is not invoiced
          const existingActivity = await ProjectActivity.read(session, { id }, [
            'id',
            'invoiced',
          ]);

          if (!existingActivity) {
            throw new Error(`Activity with ID ${id} not found`);
          }

          if (existingActivity.invoiced) {
            throw new Error(
              `Cannot update activity with ID ${id} that is already invoiced`
            );
          }

          // Update the activity
          await ProjectActivity.update(session, { id }, updateFields, ['id']);

          // Update team member mappings if provided
          if (teamMemberIds !== undefined) {
            // Delete existing mappings
            await ActivityMapping.deleteMany(session, {
              projectActivityId: id,
            });

            // Create new mappings
            if (teamMemberIds.length > 0) {
              // Get the projectId from the activity
              const activity = await ProjectActivity.read(session, { id }, [
                'projectId',
              ]);
              const newMappings = teamMemberIds.map((memberId) => ({
                memberId,
                projectActivityId: id,
                projectId: activity.projectId,
              }));
              await ActivityMapping.createMany(session, newMappings);
            }
          }
        })
      );

      // Get unique project IDs from activities
      const activityIds = input.activities.map((activity) => activity.id);
      const projectActivities = await ProjectActivity.query(
        session,
        { where: { id: { $in: activityIds } } },
        ['projectId']
      );
      const hrProjectIds = uniq(
        projectActivities.map((activity) => activity.projectId)
      );

      return {
        output: {
          activityIds,
          hrProjectIds,
        },
      };
    },
  },

  /*
   * Positions
   */
  createPositions: {
    async execute({
      session,
      input,
    }: ProjectApiImplementation.Methods.createPositions.Execute.Request): Promise<ProjectApiImplementation.Methods.createPositions.Execute.Response> {
      const { positions } = input;

      // Create all project positions
      for (const position of positions) {
        await verifyPosition(session as unknown as ISession, position);
      }
      const createdPositions = await ProjectPosition.createMany(
        session,
        positions.map((position) => ({
          ...position,
          projectId: input.projectId,
        })),
        ['id', 'name', 'description', 'billable']
      );

      const positionIds = createdPositions.map((position) => position.id);

      return {
        output: {
          positionIds,
          hrProjectIds: [input.projectId],
        },
      };
    },
  },

  deletePositions: {
    execute: ({ session, input }) => deletePositions(session, input),
  },

  updatePosition: {
    async execute({
      session,
      input,
    }: ProjectApiImplementation.Methods.updatePosition.Execute.Request): Promise<ProjectApiImplementation.Methods.updatePosition.Execute.Response> {
      await verifyPosition(session as unknown as ISession, input);

      const { positionId, ...data } = input;

      const updatedProjectPosition = await ProjectPosition.update(
        session,
        { id: positionId },
        data,
        ['projectId']
      );

      return {
        output: {
          hrProjectIds: [updatedProjectPosition.projectId],
        },
      };
    },
  },

  /*
   * Members
   */
  deleteMembers: {
    execute: ({ session, input }) => deleteMembers(session, input),
  },

  /*
   * Assign Employees to Positions.
   * Returned memberIds retain the order of the input assignments.
   */
  addMembers: {
    async execute({
      session,
      input,
    }: ProjectApiImplementation.Methods.addMembers.Execute.Request): Promise<ProjectApiImplementation.Methods.addMembers.Execute.Response> {
      const { assignments: employeeAssignments, projectId } = input;

      const team = await TimetrackingProject.read(session, { id: projectId }, [
        'teamId',
      ]);

      const uniqueEmployees = uniq(
        employeeAssignments.map((assignment) => assignment.employeeId)
      );
      const { teamMemberIds } = await TeamApi.upsertTeamMembers.execute(
        session,
        {
          teamId: team.teamId,
          teamMembers: uniqueEmployees.map((employeeId) => ({
            employeeId,
          })),
        }
      );

      const employeeToteamMemberIdMap = new Map();
      for (const [index, teamMemberId] of teamMemberIds.entries()) {
        const employeeId = uniqueEmployees[index];
        employeeToteamMemberIdMap.set(employeeId, teamMemberId);
      }

      const createdMembers = await Member.createMany(
        session,
        employeeAssignments
          .filter((assignment) => assignment.projectPositionId)
          .map((assignment) => ({
            projectPositionId: assignment.projectPositionId,
            teamMemberId: employeeToteamMemberIdMap.get(assignment.employeeId),
            projectId,
          })),
        ['id']
      );

      const memberIds = createdMembers.map((member) => member.id);

      return {
        output: {
          memberIds,
        },
      };
    },
  },

  /*
   * unassignMembers
   */
  unassignMembers: {
    async execute({
      session,
      input,
    }: ProjectApiImplementation.Methods.unassignMembers.Execute.Request): Promise<ProjectApiImplementation.Methods.unassignMembers.Execute.Response> {
      const { teamMemberId } = input;

      const members = await Member.query(
        session,
        {
          where: {
            teamMemberId,
          },
        },
        ['id']
      );
      if (members.length > 0) {
        throw new Error('Cannot delete team member that is assigned to a role');
      }
      await TeamApi.deleteTeamMembers.execute(session, {
        teamMemberIds: [teamMemberId],
      });

      return {
        output: {},
      };
    },
  },
} satisfies ProjectApiImplementation.Implementation;
