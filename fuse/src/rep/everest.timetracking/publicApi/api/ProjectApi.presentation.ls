package everest.timetracking

api presentation ProjectApi {

    /*
     * Projects
     */

    action createProject {
        inputs {
            projectName: Text
            startDate (optional: true): PlainDateTime
            endDate (optional: true): PlainDateTime
            projectLeadIds (optional: true): array<Number<Int>>
            budgetId (optional: true): Number<Int>
            active (optional: true): TrueFalse
        }

        outputs {
            projectId: Number<Int>
            projectUuid: Text
        }

        properties {
            side-effects true
        }
    }

    action updateProject {
        inputs {
            id: Number<Int>
            projectName (optional: true): Text
            startDate (optional: true): PlainDateTime
            endDate (optional: true): PlainDateTime
            projectLeadIds (optional: true): array<Number<Int>>
            budgetId (optional: true): Number<Int>
            active (optional: true): TrueFalse
        }

        properties {
            side-effects true
        }
    }

    action deleteProjects {
        inputs {
            projectIds: array<Number<Int>>
        }

        properties {
            side-effects true
        }
    }
    

    /*
     * Activities
     */

    action deleteActivities {
        inputs {
            activityIds: array<Number<Int>>
        }

        properties {
            side-effects true
        }
    }

    action createActivities {
        inputs {
            projectId: Number<Int>
            activities: array<object<{
                name: Text
                `description` (optional: true): Text
                billable (optional: true): TrueFalse
                billingType: enum<ActivityBillingType>
                hoursBudget (optional: true): Number<Decimal>
                milestoneId (nullable: true, optional: true): Number<Int>
                fixedFeeId (nullable: true, optional: true): Number<Int>
                teamMemberIds (optional: true): array<Number<Int>>
                startDate (optional: true): PlainDate
                endDate (optional: true): PlainDate
                projectId: Id
            }>>
        }

        outputs {
            activityIds: array<Number<Int>>
            hrProjectIds: array<Number<Int>>
        }

        properties {
            side-effects true
        }
    }

    action updateActivities {
        inputs {
            activities: array<object<{
                id: Number<Int>
                name (optional: true): Text
                `description` (optional: true): Text
                billable (optional: true): TrueFalse
                billingType (optional: true): enum<ActivityBillingType>
                hoursBudget (nullable: true, optional: true): Number<Decimal>
                milestoneId (nullable: true, optional: true): Number<Int>
                fixedFeeId (nullable: true, optional: true): Number<Int>
                teamMemberIds (optional: true): array<Number<Int>>
                startDate (optional: true): PlainDate
                endDate (optional: true): PlainDate
            }>>
        }

        outputs {
            activityIds: array<Number<Int>>
            hrProjectIds: array<Number<Int>>
        }

        properties {
            side-effects true
        }
    }

    /*
     * Positions
     */

    action deletePositions {
        inputs {
            positionIds: array<Number<Int>>
        }

        properties {
            side-effects true
        }
    }

    action createPositions {
        inputs {
            projectId: Number<Int>
            positions: array<object<{
                name: Text
                `description` (optional: true): Text
                salesOrderProductLineId (nullable: true,optional: true): Number<Int>
                hourlyBillRate (optional: true): object<{
                    amount (nullable: true): Amount
                    currency (nullable: true): primitive<everest.base::CurrencyCodeType>
                }>
                hourlyCostRate (optional: false): object<{
                    amount (nullable: false): Amount
                    currency (nullable: false): primitive<everest.base::CurrencyCodeType>
                }>
                billable (optional: true): TrueFalse
                hoursBudget (optional: true): Number<Decimal>
            }>>
        }

        outputs {
            positionIds: array<Number<Int>>
            hrProjectIds: array<Number<Int>>
        }

        properties {
            side-effects true
        }
    }

    action updatePosition {
        inputs {
            positionId: Number<Int>
            name (optional: true): Text
            `description` (optional: true): Text
            salesOrderProductLineId (nullable: true, optional: true): Number<Int>
            hourlyBillRate (optional: true): object<{
                amount (nullable: true): Amount
                currency (nullable: true): primitive<everest.base::CurrencyCodeType>
            }>
            hourlyCostRate (optional: true): object<{
                amount (nullable: true): Amount
                currency (nullable: true): primitive<everest.base::CurrencyCodeType>
            }>
            billable (optional: true): TrueFalse
            hoursBudget (optional: true): Number<Decimal>
        }

        outputs {
            hrProjectIds: array<Number<Int>>
        }

        properties {
            side-effects true
        }
    }

    /*
     * Members
     */

    action deleteMembers {
        inputs {
            memberIds: array<Number<Int>>
        }

        properties {
            side-effects true
        }
    }

    action addMembers {
        inputs {
            projectId: Number<Int>
            assignments: array<object<{
                employeeId: Number<Int>
                projectPositionId (optional: true, nullable: true): Number<Int>
            }>>
        }
        outputs {
            memberIds: array<Number<Int>>
        }

        properties {
            side-effects true
        }
    }

    action unassignMembers {
        inputs {
            projectId: Number<Int>
            teamMemberId: Number<Int>
        }

        properties {
            side-effects true
        }
    }
}
