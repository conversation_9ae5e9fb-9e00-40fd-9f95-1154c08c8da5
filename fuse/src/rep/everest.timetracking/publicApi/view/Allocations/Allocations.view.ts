import { ModelUrn } from '@everestsystems/content-core';
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@everestsystems/content-core/lib/analytics/tables';
import type { TdpBuilderNode } from '@pkg/everest.analytics/public/tableProcessing/pipelineBuilder';
import {
  fromModel,
  PipelineBuilder,
} from '@pkg/everest.analytics/public/tableProcessing/pipelineBuilder';
import type { TdpTsViewQueryArgs as ViewArgs } from '@pkg/everest.analytics/public/views/types';
import { Employee } from '@pkg/everest.hr.base/types/Employee';
import { TeamMemberView } from '@pkg/everest.hr.base/types/views/publicApi/view/TeamMemberView/TeamMemberView';
import { Allocation } from '@pkg/everest.timetracking/types/Allocation';
import { AllocationEntry } from '@pkg/everest.timetracking/types/AllocationEntry';
import { Member } from '@pkg/everest.timetracking/types/Member';
import { ProjectActivity } from '@pkg/everest.timetracking/types/ProjectActivity';
import { ProjectPosition } from '@pkg/everest.timetracking/types/ProjectPosition';
import { TimetrackingProject } from '@pkg/everest.timetracking/types/TimetrackingProject';

/**
 * View that lists allocations from the timetracking package.
 * This view provides a comprehensive list of time allocations with their details,
 * including member, project position, and project information.
 *
 * Features:
 * - Access to allocation master data (dates, hours, method, working days)
 * - Member information (name, capacity, team lead status)
 * - Project position details (name, bill rate, hours budget)
 * - Project information (name, dates, status)
 * - Optional daily allocation entries with optimized week/month fields (when includeAllocationEntries=true)
 *
 * Parameters:
 * - includeAllocationEntries (boolean): When true, joins with AllocationEntry to provide
 *   daily breakdown data and optimized week/month fields for efficient aggregation.
 *   When false or omitted, only returns allocation-level summary data.
 */
export default async function query(args: ViewArgs): Promise<ComputeJson> {
  // Initialize the pipeline with the Allocation model
  const modelUrn = ModelUrn.parse(Allocation.MODEL_URN);
  const pipeline = new PipelineBuilder(modelUrn.name);

  // Start with the Allocation model and select relevant fields
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let allocationsView: TdpBuilderNode<any> = fromModel(
    Allocation,
    pipeline
  ).select([
    { name: 'id', as: 'allocationId' },
    'active',
    'startDate',
    'endDate',
    'memberId',
    'projectActivityId',
    'totalHours',
    'notes',
    'method',
    'workingDays',
  ] as const);

  // Join with Member model
  const memberView = fromModel(Member, pipeline).select([
    { name: 'id', as: 'memberInternalId' },
    { name: 'active', as: 'memberActive' },
    { name: 'teamMemberId', as: 'memberTeamMemberId' },
    { name: 'projectPositionId', as: 'memberProjectPositionId' },
  ] as const);

  // Join allocations with members
  allocationsView = allocationsView.join('left', memberView, {
    memberId: { $take: 'memberInternalId' },
  });

  // Join with TeamMember model
  const teamMemberView = fromModel(TeamMemberView, pipeline).select([
    { name: 'id', as: 'teamMemberId' },
    { name: 'employeeId', as: 'employeeId' },
    { name: 'isTeamLead', as: 'memberIsTeamLead' },
  ] as const);

  allocationsView = allocationsView.join('inner', teamMemberView, {
    memberTeamMemberId: { $take: 'teamMemberId' },
  });

  const employeesView = fromModel(Employee, pipeline).select([
    { name: 'id', as: 'employeeInternalId' },
    { name: 'name', as: 'memberName' },
    { name: 'weeklyWorkingHours', as: 'memberCapacity' },
  ] as const);

  // Join allocations with employees
  allocationsView = allocationsView.join('left', employeesView, {
    employeeId: { $take: 'employeeInternalId' },
  });

  // Join with ProjectActivity model
  const projectActivityView = fromModel(ProjectActivity, pipeline).select([
    { name: 'id', as: 'projectActivityInternalId' },
    { name: 'active', as: 'projectActivityActive' },
    { name: 'name', as: 'projectActivityName' },
    { name: 'description', as: 'projectActivityDescription' },
    { name: 'hoursBudget', as: 'projectActivityHoursBudget' },
    { name: 'billable', as: 'projectActivityIsBillable' },
    { name: 'startDate', as: 'projectActivityStartDate' },
    { name: 'endDate', as: 'projectActivityEndDate' },
  ] as const);

  // Join allocations with project activities
  allocationsView = allocationsView.join('left', projectActivityView, {
    projectActivityId: { $take: 'projectActivityInternalId' },
  });

  // Join with ProjectPosition model
  const projectPositionView = fromModel(ProjectPosition, pipeline).select([
    { name: 'id', as: 'projectPositionId' },
    { name: 'active', as: 'projectPositionActive' },
    { name: 'name', as: 'projectPositionName' },
    { name: 'description', as: 'projectPositionDescription' },
    {
      name: 'hourlyBillRate.amount',
      as: 'projectPositionHourlyBillRateAmount',
    },
    {
      name: 'hourlyBillRate.currency',
      as: 'projectPositionHourlyBillRateCurrency',
    },
    { name: 'hoursBudget', as: 'projectPositionHoursBudget' },
    { name: 'projectId', as: 'projectPositionProjectId' },
  ] as const);

  // Join with project positions via project activity
  allocationsView = allocationsView.join('left', projectPositionView, {
    memberProjectPositionId: { $take: 'projectPositionId' },
  });

  // Join with Project model from HR package
  const projectView = fromModel(TimetrackingProject, pipeline).select([
    { name: 'id', as: 'projectId' },
    { name: 'active', as: 'projectActive' }, // TODO: remove (this is Everest and not business logic)
    { name: 'projectName', as: 'projectName' },
    { name: 'startDate', as: 'projectStartDate' },
    { name: 'endDate', as: 'projectEndDate' },
    { name: 'status', as: 'projectStatus' },
  ] as const);

  // Join with projects
  allocationsView = allocationsView.join('left', projectView, {
    projectPositionProjectId: { $take: 'projectId' },
  });

  // Conditional AllocationEntry join - only join when specifically requested
  const includeAllocationEntries = args.params?.includeAllocationEntries as
    | boolean
    | undefined;

  if (includeAllocationEntries) {
    // Join with AllocationEntry model to access daily entries with week/month fields
    const allocationEntryView = fromModel(AllocationEntry, pipeline).select([
      { name: 'id', as: 'allocationEntryId' },
      { name: 'allocationId', as: 'allocationEntryAllocationId' },
      { name: 'date', as: 'allocationEntryDate' },
      { name: 'hours', as: 'allocationEntryHours' },
      { name: 'week', as: 'allocationEntryWeek' },
      { name: 'month', as: 'allocationEntryMonth' },
    ] as const);

    // Join with allocation entries to access daily breakdown and week/month fields
    allocationsView = allocationsView.join('left', allocationEntryView, {
      allocationId: { $take: 'allocationEntryAllocationId' },
    });
  }

  allocationsView = allocationsView.select(args.fields);
  pipeline.setFlag('everest.analytics', 'executionBackend', 'kernel');
  const result = pipeline.build([allocationsView.unwrap()]);
  return result;
}
