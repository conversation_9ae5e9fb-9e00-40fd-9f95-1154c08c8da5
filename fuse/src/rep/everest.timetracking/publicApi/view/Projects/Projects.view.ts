import { ModelUrn } from '@everestsystems/content-core';
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@everestsystems/content-core/lib/analytics/tables';
import type { TdpBuilderNode } from '@pkg/everest.analytics/public/tableProcessing/pipelineBuilder';
import {
  fromModel,
  PipelineBuilder,
} from '@pkg/everest.analytics/public/tableProcessing/pipelineBuilder';
import type { TdpTsViewQueryArgs as ViewArgs } from '@pkg/everest.analytics/public/views/types';
import { Employee } from '@pkg/everest.hr.base/types/Employee';
import { Person } from '@pkg/everest.hr.base/types/Person';
import { TeamMemberView } from '@pkg/everest.hr.base/types/views/publicApi/view/TeamMemberView/TeamMemberView';
import { ActivityMapping } from '@pkg/everest.timetracking/types/ActivityMapping';
import { Member } from '@pkg/everest.timetracking/types/Member';
import { ProjectActivity } from '@pkg/everest.timetracking/types/ProjectActivity';
import { ProjectPosition } from '@pkg/everest.timetracking/types/ProjectPosition';
import { TimetrackingProject } from '@pkg/everest.timetracking/types/TimetrackingProject';

/**
 * View that lists projects from HR joined with project positions, members and activities.
 * This view provides a comprehensive list of projects along with their associated positions and activities.
 *
 * 'select' options:
 * - positions: includes project positions in the result
 * - activities: includes project activities in the result
 * - members: includes project members in the result
 */
export default async function query(args: ViewArgs): Promise<ComputeJson> {
  // Access parameters
  const { select } = args.params ?? {};

  // Initialize the pipeline with the Project model
  const modelUrn = ModelUrn.parse(TimetrackingProject.MODEL_URN);
  const pipeline = new PipelineBuilder(modelUrn.name);

  // Start with the Project model
  let projectsView: TdpBuilderNode<any> = fromModel(
    TimetrackingProject,
    pipeline
  ).select([
    { name: 'id', as: 'projectId' },
    'teamId',
    'projectName',
    'startDate',
    'endDate',
    'status',
    'budgetId',
    'active',
  ] as const);

  if (select) {
    switch (select) {
      case 'positions': {
        projectsView = joinPositions(pipeline, projectsView);
        break;
      }

      case 'activities': {
        projectsView = joinActivities(pipeline, projectsView);
        break;
      }

      case 'members': {
        projectsView = joinMembers(pipeline, projectsView);
        break;
      }

      case 'membersAndPositions': {
        projectsView = joinMembersAndPositions(pipeline, projectsView);
        break;
      }

      default: {
        throw new Error(
          'Invalid select parameter, possible options are: positions, members, activities, membersAndPositions'
        );
      }
    }
  }

  projectsView = projectsView.select(args.fields);
  pipeline.setFlag('everest.analytics', 'executionBackend', 'kernel');
  const result = pipeline.build([projectsView.unwrap()]);
  return result;
}

function joinPositions(
  pipeline: PipelineBuilder,
  view: TdpBuilderNode<any>
): TdpBuilderNode<any> {
  const positionsView = fromModel(ProjectPosition, pipeline).select([
    { name: 'id', as: 'positionId' },
    { name: 'name', as: 'positionName' },
    { name: 'description', as: 'positionDescription' },
    { name: 'salesOrderProductLineId', as: 'positionSalesOrderProductLineId' },
    { name: 'projectId', as: 'positionProjectId' },
    { name: 'billable', as: 'positionBillable' },
    { name: 'hourlyBillRate.amount', as: 'positionHourlyBillRate' },
    { name: 'hourlyBillRate.currency', as: 'positionHourlyBillRateCurrency' },
    { name: 'hourlyCostRate.amount', as: 'positionHourlyCostRate' },
    { name: 'hourlyCostRate.currency', as: 'positionHourlyCostRateCurrency' },
    { name: 'hoursBudget', as: 'positionHoursBudget' },
    { name: 'invoiced', as: 'positionInvoiced' },
  ] as const);

  view = view.join('inner', positionsView, {
    projectId: { $take: 'positionProjectId' },
  });

  return view;
}

function joinActivities(
  pipeline: PipelineBuilder,
  view: TdpBuilderNode<any>
): TdpBuilderNode<any> {
  const activitiesView = fromModel(ProjectActivity, pipeline).select([
    { name: 'id', as: 'activityId' },
    { name: 'name', as: 'activityName' },
    { name: 'description', as: 'activityDescription' },
    { name: 'hoursBudget', as: 'activityHoursBudget' },
    { name: 'billable', as: 'isBillableActivity' },
    { name: 'invoiced', as: 'activityInvoiced' },
    { name: 'projectId', as: 'activityProjectId' },
    { name: 'milestoneId', as: 'activityMilestoneId' },
    { name: 'fixedFeeId', as: 'activityFixedFeeId' },
    { name: 'billingType', as: 'activityBillingType' },
    { name: 'startDate', as: 'activityStartDate' },
    { name: 'endDate', as: 'activityEndDate' },
  ] as const);

  view = view.join('inner', activitiesView, {
    projectId: { $take: 'activityProjectId' },
  });

  const activityMappingView = fromModel(ActivityMapping, pipeline).select([
    { name: 'memberId', as: 'activityMappingMemberId' },
    { name: 'projectActivityId', as: 'activityMappingProjectActivityId' },
  ] as const);

  view = view.join('left', activityMappingView, {
    activityId: { $take: 'activityMappingProjectActivityId' },
  });

  const membersView = fromModel(Member, pipeline).select([
    { name: 'id', as: 'memberId' },
    { name: 'teamMemberId', as: 'memberTeamMemberId' },
    { name: 'projectPositionId', as: 'memberProjectPositionId' },
    { name: 'invoiced', as: 'memberInvoiced' },
  ] as const);

  view = view.join('left', membersView, {
    activityMappingMemberId: { $take: 'memberId' },
  });

  const teamMemberView = fromModel(TeamMemberView, pipeline).select([
    { name: 'id', as: 'teamMemberId' },
    { name: 'teamId', as: 'teamMemberTeamId' },
    { name: 'employeeId', as: 'memberEmployeeId' },
    { name: 'isTeamLead', as: 'memberIsTeamLead' },
  ] as const);

  view = view.join('left', teamMemberView, {
    memberTeamMemberId: { $take: 'teamMemberId' },
  });

  const employeesView = fromModel(Employee, pipeline).select([
    { name: 'id', as: 'employeeEmployeeId' },
    { name: 'name', as: 'memberName' },
    { name: 'displayName', as: 'memberDisplayName' },
    { name: 'email', as: 'memberEmail' },
    { name: 'weeklyWorkingHours', as: 'memberCapacity' },
  ] as const);

  view = view.join('left', employeesView, {
    memberEmployeeId: { $take: 'employeeEmployeeId' },
  });

  const positionsView = fromModel(ProjectPosition, pipeline).select([
    { name: 'id', as: 'positionId' },
    { name: 'name', as: 'positionName' },
    { name: 'description', as: 'positionDescription' },
    { name: 'salesOrderProductLineId', as: 'positionSalesOrderProductLineId' },
    { name: 'projectId', as: 'positionProjectId' },
    { name: 'billable', as: 'positionBillable' },
    { name: 'hourlyBillRate.amount', as: 'positionHourlyBillRate' },
    { name: 'hourlyBillRate.currency', as: 'positionHourlyBillRateCurrency' },
    { name: 'hourlyCostRate.amount', as: 'positionHourlyCostRate' },
    { name: 'hourlyCostRate.currency', as: 'positionHourlyCostRateCurrency' },
    { name: 'hoursBudget', as: 'positionHoursBudget' },
    { name: 'invoiced', as: 'positionInvoiced' },
  ] as const);

  view = view.join('left', positionsView, {
    memberProjectPositionId: { $take: 'positionId' },
  });

  return view;
}

function joinMembers(
  pipeline: PipelineBuilder,
  view: TdpBuilderNode<any>
): TdpBuilderNode<any> {
  const teamMemberView = fromModel(TeamMemberView, pipeline).select([
    { name: 'id', as: 'teamMemberId' },
    { name: 'teamId', as: 'teamMemberTeamId' },
    { name: 'employeeId', as: 'memberEmployeeId' },
    { name: 'isTeamLead', as: 'memberIsTeamLead' },
  ] as const);

  view = view.join('left', teamMemberView, {
    teamId: { $take: 'teamMemberTeamId' },
  });

  const employeesView = fromModel(Employee, pipeline)
    .select([
      { name: 'id', as: 'employeeEmployeeId' },
      { name: 'name', as: 'memberName' },
      { name: 'displayName', as: 'memberDisplayName' },
      { name: 'email', as: 'memberEmail' },
      { name: 'weeklyWorkingHours', as: 'memberCapacity' },
      { name: 'personId', as: 'employeePersonId' },
    ] as const)
    .join(
      'left',
      fromModel(Person, pipeline).select([
        { name: 'id', as: 'memberPersonId' },
        { name: 'photo', as: 'memberPhoto' },
      ] as const),
      { employeePersonId: { $take: 'memberPersonId' } }
    );

  view = view.join('left', employeesView, {
    memberEmployeeId: { $take: 'employeeEmployeeId' },
  });

  return view;
}

function joinMembersAndPositions(
  pipeline: PipelineBuilder,
  view: TdpBuilderNode<any>
): TdpBuilderNode<any> {
  view = joinMembers(pipeline, view);

  const membersView = fromModel(Member, pipeline).select([
    { name: 'id', as: 'memberId' },
    { name: 'teamMemberId', as: 'memberTeamMemberId' },
    { name: 'projectPositionId', as: 'memberProjectPositionId' },
    { name: 'invoiced', as: 'memberInvoiced' },
  ] as const);

  view = view.join('left', membersView, {
    teamMemberId: { $take: 'memberTeamMemberId' },
  });

  const positionsView = fromModel(ProjectPosition, pipeline).select([
    { name: 'id', as: 'positionId' },
    { name: 'name', as: 'positionName' },
    { name: 'description', as: 'positionDescription' },
    { name: 'salesOrderProductLineId', as: 'positionSalesOrderProductLineId' },
    { name: 'projectId', as: 'positionProjectId' },
    { name: 'billable', as: 'positionBillable' },
    { name: 'hourlyBillRate.amount', as: 'positionHourlyBillRate' },
    { name: 'hourlyBillRate.currency', as: 'positionHourlyBillRateCurrency' },
    { name: 'hourlyCostRate.amount', as: 'positionHourlyCostRate' },
    { name: 'hourlyCostRate.currency', as: 'positionHourlyCostRateCurrency' },
    { name: 'hoursBudget', as: 'positionHoursBudget' },
    { name: 'invoiced', as: 'positionInvoiced' },
  ] as const);

  view = view.join('left', positionsView, {
    memberProjectPositionId: { $take: 'positionId' },
  });

  return view;
}
