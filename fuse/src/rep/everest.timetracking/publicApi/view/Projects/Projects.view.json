{"type": "tdp-ts", "visibility": "public", "metadata": [{"name": "select", "type": "String", "parameter": true}, {"name": "projectId", "type": "Int"}, {"name": "teamId", "type": "Int"}, {"name": "budgetId", "type": "Int"}, {"name": "active", "type": "Boolean"}, {"name": "projectName", "type": "String"}, {"name": "startDate", "type": "PlainDateTime"}, {"name": "endDate", "type": "PlainDateTime"}, {"name": "status", "type": "String"}, {"name": "positionId", "type": "Int"}, {"name": "positionName", "type": "String"}, {"name": "positionDescription", "type": "String"}, {"name": "positionSalesOrderProductLineId", "type": "Int"}, {"name": "positionBillable", "type": "Boolean"}, {"name": "positionHourlyBillRate", "type": "Decimal"}, {"name": "positionHourlyBillRateCurrency", "type": "String"}, {"name": "positionHourlyCostRate", "type": "Decimal"}, {"name": "positionHourlyCostRateCurrency", "type": "String"}, {"name": "positionHoursBudget", "type": "Decimal"}, {"name": "positionInvoiced", "type": "Boolean"}, {"name": "activityId", "type": "Int"}, {"name": "activityName", "type": "String"}, {"name": "activityDescription", "type": "String"}, {"name": "activityHoursBudget", "type": "Decimal"}, {"name": "activityInvoiced", "type": "Boolean"}, {"name": "isBillableActivity", "type": "Boolean"}, {"name": "memberId", "type": "Int"}, {"name": "memberInvoiced", "type": "Boolean"}, {"name": "memberEmployeeId", "type": "Int"}, {"name": "memberName", "type": "String"}, {"name": "memberEmail", "type": "String"}, {"name": "memberPhoto", "type": "String"}, {"name": "memberCapacity", "type": "Int"}, {"name": "memberIsTeamLead", "type": "Boolean"}, {"name": "teamMemberId", "type": "Int"}, {"name": "memberDisplayName", "type": "String"}, {"name": "memberProjectPositionId", "type": "Int"}, {"name": "activityMilestoneId", "type": "Int"}, {"name": "activityFixedFeeId", "type": "Int"}, {"name": "activityBillingType", "type": "String"}, {"name": "activityStartDate", "type": "PlainDate"}, {"name": "activityEndDate", "type": "PlainDate"}]}