/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

import { Presentation as everest_appserver_model_node_runtime_Presentation } from "@pkg/everest.appserver/types/runtime/Presentation";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstPlainDateTime as everest_appserver_primitive_PlainDateTime } from "@pkg/everest.appserver/types/primitives/PlainDateTime";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { EvstActivityBillingType as everest_timetracking_enum_ActivityBillingType } from "@pkg/everest.timetracking/types/enums/ActivityBillingType";
import type { EvstDecimal as everest_appserver_primitive_Decimal } from "@pkg/everest.appserver/types/primitives/Decimal";
import type { EvstPlainDate as everest_appserver_primitive_PlainDate } from "@pkg/everest.appserver/types/primitives/PlainDate";
import type { EvstID as everest_appserver_primitive_ID } from "@pkg/everest.appserver/types/primitives/ID";
import type { EvstAmount as everest_appserver_primitive_Amount } from "@pkg/everest.appserver/types/primitives/Amount";
import type { EvstCurrencyCodeType as everest_base_primitive_CurrencyCodeType } from "@pkg/everest.base/types/primitives/CurrencyCodeType";

import type { ControllerClientProvider } from '@everestsystems/content-core';

/**
 * Generated consumption types for API presentation ProjectApi.
 */
export namespace ProjectApi {
  export const PRESENTATION_URN = 'urn:evst:everest:timetracking:presentation:publicApi/api/ProjectApi';

  export namespace createProject {
    export type Input = {
      projectName: everest_appserver_primitive_Text;
      startDate?: everest_appserver_primitive_PlainDateTime | undefined;
      endDate?: everest_appserver_primitive_PlainDateTime | undefined;
      projectLeadIds?: everest_appserver_primitive_Number[] | undefined;
      budgetId?: everest_appserver_primitive_Number | undefined;
      active?: everest_appserver_primitive_TrueFalse | undefined;
    };

    export type Output = {
      projectId: everest_appserver_primitive_Number;
      projectUuid: everest_appserver_primitive_Text;
    };

    export async function execute(session: ControllerClientProvider, input: Input): Promise<Output> {
      const presentationClient = await everest_appserver_model_node_runtime_Presentation.client(session);

      const output = await presentationClient.executeMethod(PRESENTATION_URN, 'createProject', input);
      return output as unknown as Output;
    }
  }

  export namespace updateProject {
    export type Input = {
      id: everest_appserver_primitive_Number;
      projectName?: everest_appserver_primitive_Text | undefined;
      startDate?: everest_appserver_primitive_PlainDateTime | undefined;
      endDate?: everest_appserver_primitive_PlainDateTime | undefined;
      projectLeadIds?: everest_appserver_primitive_Number[] | undefined;
      budgetId?: everest_appserver_primitive_Number | undefined;
      active?: everest_appserver_primitive_TrueFalse | undefined;
    };

    export type Output = Record<string, never>;

    export async function execute(session: ControllerClientProvider, input: Input): Promise<Output> {
      const presentationClient = await everest_appserver_model_node_runtime_Presentation.client(session);

      const output = await presentationClient.executeMethod(PRESENTATION_URN, 'updateProject', input);
      return output as unknown as Output;
    }
  }

  export namespace deleteProjects {
    export type Input = {
      projectIds: everest_appserver_primitive_Number[];
    };

    export type Output = Record<string, never>;

    export async function execute(session: ControllerClientProvider, input: Input): Promise<Output> {
      const presentationClient = await everest_appserver_model_node_runtime_Presentation.client(session);

      const output = await presentationClient.executeMethod(PRESENTATION_URN, 'deleteProjects', input);
      return output as unknown as Output;
    }
  }

  export namespace deleteActivities {
    export type Input = {
      activityIds: everest_appserver_primitive_Number[];
    };

    export type Output = Record<string, never>;

    export async function execute(session: ControllerClientProvider, input: Input): Promise<Output> {
      const presentationClient = await everest_appserver_model_node_runtime_Presentation.client(session);

      const output = await presentationClient.executeMethod(PRESENTATION_URN, 'deleteActivities', input);
      return output as unknown as Output;
    }
  }

  export namespace createActivities {
    export type Input = {
      projectId: everest_appserver_primitive_Number;
      activities: {
        name: everest_appserver_primitive_Text;
        description?: everest_appserver_primitive_Text | undefined;
        billable?: everest_appserver_primitive_TrueFalse | undefined;
        billingType: everest_timetracking_enum_ActivityBillingType;
        hoursBudget?: everest_appserver_primitive_Decimal | undefined;
        milestoneId?: everest_appserver_primitive_Number | null | undefined;
        fixedFeeId?: everest_appserver_primitive_Number | null | undefined;
        teamMemberIds?: everest_appserver_primitive_Number[] | undefined;
        startDate?: everest_appserver_primitive_PlainDate | undefined;
        endDate?: everest_appserver_primitive_PlainDate | undefined;
        projectId: everest_appserver_primitive_ID;
      }[];
    };

    export type Output = {
      activityIds: everest_appserver_primitive_Number[];
      hrProjectIds: everest_appserver_primitive_Number[];
    };

    export async function execute(session: ControllerClientProvider, input: Input): Promise<Output> {
      const presentationClient = await everest_appserver_model_node_runtime_Presentation.client(session);

      const output = await presentationClient.executeMethod(PRESENTATION_URN, 'createActivities', input);
      return output as unknown as Output;
    }
  }

  export namespace updateActivities {
    export type Input = {
      activities: {
        id: everest_appserver_primitive_Number;
        name?: everest_appserver_primitive_Text | undefined;
        description?: everest_appserver_primitive_Text | undefined;
        billable?: everest_appserver_primitive_TrueFalse | undefined;
        billingType?: everest_timetracking_enum_ActivityBillingType | undefined;
        hoursBudget?: everest_appserver_primitive_Decimal | null | undefined;
        milestoneId?: everest_appserver_primitive_Number | null | undefined;
        fixedFeeId?: everest_appserver_primitive_Number | null | undefined;
        teamMemberIds?: everest_appserver_primitive_Number[] | undefined;
        startDate?: everest_appserver_primitive_PlainDate | undefined;
        endDate?: everest_appserver_primitive_PlainDate | undefined;
      }[];
    };

    export type Output = {
      activityIds: everest_appserver_primitive_Number[];
      hrProjectIds: everest_appserver_primitive_Number[];
    };

    export async function execute(session: ControllerClientProvider, input: Input): Promise<Output> {
      const presentationClient = await everest_appserver_model_node_runtime_Presentation.client(session);

      const output = await presentationClient.executeMethod(PRESENTATION_URN, 'updateActivities', input);
      return output as unknown as Output;
    }
  }

  export namespace deletePositions {
    export type Input = {
      positionIds: everest_appserver_primitive_Number[];
    };

    export type Output = Record<string, never>;

    export async function execute(session: ControllerClientProvider, input: Input): Promise<Output> {
      const presentationClient = await everest_appserver_model_node_runtime_Presentation.client(session);

      const output = await presentationClient.executeMethod(PRESENTATION_URN, 'deletePositions', input);
      return output as unknown as Output;
    }
  }

  export namespace createPositions {
    export type Input = {
      projectId: everest_appserver_primitive_Number;
      positions: {
        name: everest_appserver_primitive_Text;
        description?: everest_appserver_primitive_Text | undefined;
        salesOrderProductLineId?: everest_appserver_primitive_Number | null | undefined;
        hourlyBillRate?: {
          amount: everest_appserver_primitive_Amount | null;
          currency: everest_base_primitive_CurrencyCodeType | null;
        } | undefined;
        hourlyCostRate: {
          amount: everest_appserver_primitive_Amount;
          currency: everest_base_primitive_CurrencyCodeType;
        };
        billable?: everest_appserver_primitive_TrueFalse | undefined;
        hoursBudget?: everest_appserver_primitive_Decimal | undefined;
      }[];
    };

    export type Output = {
      positionIds: everest_appserver_primitive_Number[];
      hrProjectIds: everest_appserver_primitive_Number[];
    };

    export async function execute(session: ControllerClientProvider, input: Input): Promise<Output> {
      const presentationClient = await everest_appserver_model_node_runtime_Presentation.client(session);

      const output = await presentationClient.executeMethod(PRESENTATION_URN, 'createPositions', input);
      return output as unknown as Output;
    }
  }

  export namespace updatePosition {
    export type Input = {
      positionId: everest_appserver_primitive_Number;
      name?: everest_appserver_primitive_Text | undefined;
      description?: everest_appserver_primitive_Text | undefined;
      salesOrderProductLineId?: everest_appserver_primitive_Number | null | undefined;
      hourlyBillRate?: {
        amount: everest_appserver_primitive_Amount | null;
        currency: everest_base_primitive_CurrencyCodeType | null;
      } | undefined;
      hourlyCostRate?: {
        amount: everest_appserver_primitive_Amount | null;
        currency: everest_base_primitive_CurrencyCodeType | null;
      } | undefined;
      billable?: everest_appserver_primitive_TrueFalse | undefined;
      hoursBudget?: everest_appserver_primitive_Decimal | undefined;
    };

    export type Output = {
      hrProjectIds: everest_appserver_primitive_Number[];
    };

    export async function execute(session: ControllerClientProvider, input: Input): Promise<Output> {
      const presentationClient = await everest_appserver_model_node_runtime_Presentation.client(session);

      const output = await presentationClient.executeMethod(PRESENTATION_URN, 'updatePosition', input);
      return output as unknown as Output;
    }
  }

  export namespace deleteMembers {
    export type Input = {
      memberIds: everest_appserver_primitive_Number[];
    };

    export type Output = Record<string, never>;

    export async function execute(session: ControllerClientProvider, input: Input): Promise<Output> {
      const presentationClient = await everest_appserver_model_node_runtime_Presentation.client(session);

      const output = await presentationClient.executeMethod(PRESENTATION_URN, 'deleteMembers', input);
      return output as unknown as Output;
    }
  }

  export namespace addMembers {
    export type Input = {
      projectId: everest_appserver_primitive_Number;
      assignments: {
        employeeId: everest_appserver_primitive_Number;
        projectPositionId?: everest_appserver_primitive_Number | null | undefined;
      }[];
    };

    export type Output = {
      memberIds: everest_appserver_primitive_Number[];
    };

    export async function execute(session: ControllerClientProvider, input: Input): Promise<Output> {
      const presentationClient = await everest_appserver_model_node_runtime_Presentation.client(session);

      const output = await presentationClient.executeMethod(PRESENTATION_URN, 'addMembers', input);
      return output as unknown as Output;
    }
  }

  export namespace unassignMembers {
    export type Input = {
      projectId: everest_appserver_primitive_Number;
      teamMemberId: everest_appserver_primitive_Number;
    };

    export type Output = Record<string, never>;

    export async function execute(session: ControllerClientProvider, input: Input): Promise<Output> {
      const presentationClient = await everest_appserver_model_node_runtime_Presentation.client(session);

      const output = await presentationClient.executeMethod(PRESENTATION_URN, 'unassignMembers', input);
      return output as unknown as Output;
    }
  }
}

