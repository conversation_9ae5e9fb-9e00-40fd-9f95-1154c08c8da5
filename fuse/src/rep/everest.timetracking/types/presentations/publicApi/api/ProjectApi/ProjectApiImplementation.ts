/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstPlainDateTime as everest_appserver_primitive_PlainDateTime } from "@pkg/everest.appserver/types/primitives/PlainDateTime";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { EvstActivityBillingType as everest_timetracking_enum_ActivityBillingType } from "@pkg/everest.timetracking/types/enums/ActivityBillingType";
import type { EvstDecimal as everest_appserver_primitive_Decimal } from "@pkg/everest.appserver/types/primitives/Decimal";
import type { EvstPlainDate as everest_appserver_primitive_PlainDate } from "@pkg/everest.appserver/types/primitives/PlainDate";
import type { EvstID as everest_appserver_primitive_ID } from "@pkg/everest.appserver/types/primitives/ID";
import type { EvstAmount as everest_appserver_primitive_Amount } from "@pkg/everest.appserver/types/primitives/Amount";
import type { EvstCurrencyCodeType as everest_base_primitive_CurrencyCodeType } from "@pkg/everest.base/types/primitives/CurrencyCodeType";

import type { ControllerClientProvider } from '@everestsystems/content-core';

/**
 * Generated implementation types for API presentation ProjectApi.
 */
export namespace ProjectApiImplementation {
  export namespace Methods {
    export namespace createProject {
      export type Input = {
        projectName: everest_appserver_primitive_Text;
        startDate?: everest_appserver_primitive_PlainDateTime;
        endDate?: everest_appserver_primitive_PlainDateTime;
        projectLeadIds?: everest_appserver_primitive_Number[];
        budgetId?: everest_appserver_primitive_Number;
        active?: everest_appserver_primitive_TrueFalse;
      };

      export type Output = {
        projectId: everest_appserver_primitive_Number;
        projectUuid: everest_appserver_primitive_Text;
      };

      export namespace Execute {
        export type Request = {
          session: ControllerClientProvider;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }
    export namespace updateProject {
      export type Input = {
        id: everest_appserver_primitive_Number;
        projectName?: everest_appserver_primitive_Text;
        startDate?: everest_appserver_primitive_PlainDateTime;
        endDate?: everest_appserver_primitive_PlainDateTime;
        projectLeadIds?: everest_appserver_primitive_Number[];
        budgetId?: everest_appserver_primitive_Number;
        active?: everest_appserver_primitive_TrueFalse;
      };

      export type Output = Record<string, never>;

      export namespace Execute {
        export type Request = {
          session: ControllerClientProvider;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }
    export namespace deleteProjects {
      export type Input = {
        projectIds: everest_appserver_primitive_Number[];
      };

      export type Output = Record<string, never>;

      export namespace Execute {
        export type Request = {
          session: ControllerClientProvider;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }
    export namespace deleteActivities {
      export type Input = {
        activityIds: everest_appserver_primitive_Number[];
      };

      export type Output = Record<string, never>;

      export namespace Execute {
        export type Request = {
          session: ControllerClientProvider;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }
    export namespace createActivities {
      export type Input = {
        projectId: everest_appserver_primitive_Number;
        activities: {
          name: everest_appserver_primitive_Text;
          description?: everest_appserver_primitive_Text;
          billable?: everest_appserver_primitive_TrueFalse;
          billingType: everest_timetracking_enum_ActivityBillingType;
          hoursBudget?: everest_appserver_primitive_Decimal;
          milestoneId?: everest_appserver_primitive_Number | null;
          fixedFeeId?: everest_appserver_primitive_Number | null;
          teamMemberIds?: everest_appserver_primitive_Number[];
          startDate?: everest_appserver_primitive_PlainDate;
          endDate?: everest_appserver_primitive_PlainDate;
          projectId: everest_appserver_primitive_ID;
        }[];
      };

      export type Output = {
        activityIds: everest_appserver_primitive_Number[];
        hrProjectIds: everest_appserver_primitive_Number[];
      };

      export namespace Execute {
        export type Request = {
          session: ControllerClientProvider;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }
    export namespace updateActivities {
      export type Input = {
        activities: {
          id: everest_appserver_primitive_Number;
          name?: everest_appserver_primitive_Text;
          description?: everest_appserver_primitive_Text;
          billable?: everest_appserver_primitive_TrueFalse;
          billingType?: everest_timetracking_enum_ActivityBillingType;
          hoursBudget?: everest_appserver_primitive_Decimal | null;
          milestoneId?: everest_appserver_primitive_Number | null;
          fixedFeeId?: everest_appserver_primitive_Number | null;
          teamMemberIds?: everest_appserver_primitive_Number[];
          startDate?: everest_appserver_primitive_PlainDate;
          endDate?: everest_appserver_primitive_PlainDate;
        }[];
      };

      export type Output = {
        activityIds: everest_appserver_primitive_Number[];
        hrProjectIds: everest_appserver_primitive_Number[];
      };

      export namespace Execute {
        export type Request = {
          session: ControllerClientProvider;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }
    export namespace deletePositions {
      export type Input = {
        positionIds: everest_appserver_primitive_Number[];
      };

      export type Output = Record<string, never>;

      export namespace Execute {
        export type Request = {
          session: ControllerClientProvider;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }
    export namespace createPositions {
      export type Input = {
        projectId: everest_appserver_primitive_Number;
        positions: {
          name: everest_appserver_primitive_Text;
          description?: everest_appserver_primitive_Text;
          salesOrderProductLineId?: everest_appserver_primitive_Number | null;
          hourlyBillRate?: {
            amount: everest_appserver_primitive_Amount | null;
            currency: everest_base_primitive_CurrencyCodeType | null;
          };
          hourlyCostRate: {
            amount: everest_appserver_primitive_Amount;
            currency: everest_base_primitive_CurrencyCodeType;
          };
          billable?: everest_appserver_primitive_TrueFalse;
          hoursBudget?: everest_appserver_primitive_Decimal;
        }[];
      };

      export type Output = {
        positionIds: everest_appserver_primitive_Number[];
        hrProjectIds: everest_appserver_primitive_Number[];
      };

      export namespace Execute {
        export type Request = {
          session: ControllerClientProvider;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }
    export namespace updatePosition {
      export type Input = {
        positionId: everest_appserver_primitive_Number;
        name?: everest_appserver_primitive_Text;
        description?: everest_appserver_primitive_Text;
        salesOrderProductLineId?: everest_appserver_primitive_Number | null;
        hourlyBillRate?: {
          amount: everest_appserver_primitive_Amount | null;
          currency: everest_base_primitive_CurrencyCodeType | null;
        };
        hourlyCostRate?: {
          amount: everest_appserver_primitive_Amount | null;
          currency: everest_base_primitive_CurrencyCodeType | null;
        };
        billable?: everest_appserver_primitive_TrueFalse;
        hoursBudget?: everest_appserver_primitive_Decimal;
      };

      export type Output = {
        hrProjectIds: everest_appserver_primitive_Number[];
      };

      export namespace Execute {
        export type Request = {
          session: ControllerClientProvider;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }
    export namespace deleteMembers {
      export type Input = {
        memberIds: everest_appserver_primitive_Number[];
      };

      export type Output = Record<string, never>;

      export namespace Execute {
        export type Request = {
          session: ControllerClientProvider;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }
    export namespace addMembers {
      export type Input = {
        projectId: everest_appserver_primitive_Number;
        assignments: {
          employeeId: everest_appserver_primitive_Number;
          projectPositionId?: everest_appserver_primitive_Number | null;
        }[];
      };

      export type Output = {
        memberIds: everest_appserver_primitive_Number[];
      };

      export namespace Execute {
        export type Request = {
          session: ControllerClientProvider;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }
    export namespace unassignMembers {
      export type Input = {
        projectId: everest_appserver_primitive_Number;
        teamMemberId: everest_appserver_primitive_Number;
      };

      export type Output = Record<string, never>;

      export namespace Execute {
        export type Request = {
          session: ControllerClientProvider;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }
  }

  export type Implementation = {
    createProject: Methods.createProject.Implementation;
    updateProject: Methods.updateProject.Implementation;
    deleteProjects: Methods.deleteProjects.Implementation;
    deleteActivities: Methods.deleteActivities.Implementation;
    createActivities: Methods.createActivities.Implementation;
    updateActivities: Methods.updateActivities.Implementation;
    deletePositions: Methods.deletePositions.Implementation;
    createPositions: Methods.createPositions.Implementation;
    updatePosition: Methods.updatePosition.Implementation;
    deleteMembers: Methods.deleteMembers.Implementation;
    addMembers: Methods.addMembers.Implementation;
    unassignMembers: Methods.unassignMembers.Implementation;
  };
}

