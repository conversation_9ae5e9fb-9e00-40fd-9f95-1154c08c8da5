package everest.timetracking

node ProjectActivity {
	description: 'Activity for a certain project'
	label: 'Project Activity'
	under-development

	field name Text {
		required
		editable
		persisted
		label: 'Name'
	}

	field `description` Text {
		editable
		persisted
		label: 'Description'
	}

	field billable TrueFalse {
		persisted
		label: 'Billable'
		default: true
	}

	field invoiced TrueFalse {
		editable
		persisted
		label: 'Invoiced'
	}

	field hoursBudget Number<Decimal> {
		editable
		persisted
		label: 'Hours Budget'
	}

	field milestoneId Id {
		editable
		persisted
		label: 'Milestone ID'
	}

	field fixedFeeId Id {
		editable
		persisted
		label: 'Fixed Fee ID'
	}

	field projectId Id {
		required
		editable
		persisted
		label: 'Project ID'
	}

	field billingType enum<ActivityBillingType>{
		required
		editable
		persisted
		label: 'Billing Type'
	}

	field startDate PlainDate {
		persisted
		label: 'Start Date'
	}

	field endDate PlainDate {
		persisted
		label: 'End Date'
	}

	generated association alias project for ProjectActivity-Project

	generated association alias timeentry for TimeEntry-ProjectActivity

	generated association alias allocation for Allocation-ProjectActivity

	generated association alias activitymapping for ActivityMapping-ProjectActivity
}

association ProjectActivity-Project {
	source: ProjectActivity
	sourceField: projectId
	target: TimetrackingProject
	targetField: id
	kind: static
	multiplicity: many-to-one
	description: ''
}
