/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// ******************************************************************************
// Generated Everest Types
// ******************************************************************************

import type { Decimal, RoundingMode } from '@everestsystems/decimal';
import type {
  ArgType,
  Association,
  AssociationManagementArgs,
  AsyncResult,
  Controller,
  ControllerClientProvider,
  CreateOptionsArg,
  DataWithMetadata,
  DeleteOptionsArg,
  FieldSelector,
  Filter,
  flattenFieldSelector,
  GraphInstanceEntry,
  KernelRequestOptions,
  NonAssociationFields,
  Optional,
  PurgeArgs,
  ReadOptionsArg,
  ReadReturnType as ReadReturnTypeGeneric,
  ReadReturnTypeWithAssociations,
  RequireAtLeastOne,
  SemanticSearchArg,
  SemanticSearchRequestOptions,
  TypeSafeQueryArgType,
  UpdateManyOptionsArg,
  UpdateOptionsArg,
  UpsertOptionsArg,
  WriteReturnType,
  WriteReturnTypeWithAssociations,
} from '@everestsystems/content-core';

import { UIExecutionContext } from '@everestsystems/content-core';
import type { TimeEntry as everest_timetracking_model_node_TimeEntry } from "@pkg/everest.timetracking/types/TimeEntry";
import type { Allocation as everest_timetracking_model_node_Allocation } from "@pkg/everest.timetracking/types/Allocation";
import type { ActivityMapping as everest_timetracking_model_node_ActivityMapping } from "@pkg/everest.timetracking/types/ActivityMapping";
import type { TimetrackingProject as everest_timetracking_model_node_TimetrackingProject } from "@pkg/everest.timetracking/types/TimetrackingProject";
import type { ProjectActivity as everest_timetracking_model_node_ProjectActivity } from "@pkg/everest.timetracking/types/ProjectActivity";

/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */

type RequiredContext = {
  data: Record<string, any>;
  actions: {
    run: (...args: any[]) => any;
  };
};

type ApiError = {
  message: string;
  name: string;
  errorCode: string;
  stack?: string;
};

type InnerPromiseType<T> = T extends Promise<infer R> ? R : T;

type ActionResultOrError<T1 extends string | number | symbol, T2> = {[P in T1]?: T2} & {error?: ApiError};

type ArrayIfNeeded<C extends RequiredContext, K extends keyof C['data'], T> = C['data'][K] extends UIExecutionContext.UIModelNodeListType<any> ? T[] : T;

export namespace ProjectActivityUI {
  /**
   * Activity for a certain project
   */
  export type ProjectActivityWithAssociation = ProjectActivity & {
    ["TimeEntry-ProjectActivity"]?: Association<everest_timetracking_model_node_TimeEntry.TimeEntryWithAssociation>[];
    ["Allocation-ProjectActivity"]?: Association<everest_timetracking_model_node_Allocation.AllocationWithAssociation>[];
    ["ActivityMapping-ProjectActivity"]?: Association<everest_timetracking_model_node_ActivityMapping.ActivityMappingWithAssociation>[];
    ["ProjectActivity-Project"]?: Association<everest_timetracking_model_node_TimetrackingProject.TimetrackingProjectWithAssociation>;
    };
  export interface IControllerClient extends everest_timetracking_model_node_ProjectActivity.IControllerClient {}

  export type ProjectActivity = everest_timetracking_model_node_ProjectActivity.ProjectActivity;
  export type CreationFields = Pick<ProjectActivity, 'uuid' | 'externalId' | 'active' | 'billable' | 'billingType' | 'description' | 'endDate' | 'fixedFeeId' | 'hoursBudget' | 'invoiced' | 'milestoneId' | 'name' | 'projectId' | 'startDate'>;
  export type UniqueFields = Pick<ProjectActivity, 'id' | 'uuid'>;
  export type UniqueWhereInput = (RequireAtLeastOne<UniqueFields>) & Partial<ProjectActivity>;
  export type ReadReturnType<U extends string | number | symbol = keyof ProjectActivity> = ReadReturnTypeGeneric<ProjectActivity, U>;

  class ActionRequest<C extends RequiredContext, FuncReturnType> {
    constructor(private readonly context: C, private readonly partialPayload: Record<string, any>) {
    }

    private async __run__(payload: any) {
      return this.context.actions.run(payload);
    }

    payload<K extends keyof C['data']>(nodeAlias: K): Record<string, any> {
      return {[nodeAlias]: this.partialPayload};
    }

    run<K extends keyof C['data']>(nodeAlias: K): Promise<ActionResultOrError<K, ArrayIfNeeded<C, K, InnerPromiseType<FuncReturnType>>>> {
      const payload = this.payload(nodeAlias);
      return this.__run__(payload);
    }
  }


  /** @deprecated - use {@link MODEL_URN} instead */
  export const name = 'urn:evst:everest:timetracking:model/node:ProjectActivity';
  export const MODEL_URN = 'urn:evst:everest:timetracking:model/node:ProjectActivity';
  /** @deprecated - use {@link MODEL_URN} instead */
  export const MODEL_ID = 'everest.timetracking/ProjectActivityModel.ProjectActivity';
  export const MODEL_UUID = '81c5f68e-03d9-41b0-9b9b-46f24b4e62e8';

  /** @deprecated use ```client``` instead */
  export function create<C extends RequiredContext, U extends keyof ProjectActivity>(context: C, input: CreationFields, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<ProjectActivity, U>>> {
    const convertedArgs = {data: input, fieldList: fieldlist, options: options} as Record<string, any>
    const partialPayload = {action: 'create', ...convertedArgs}

    return new ActionRequest<C, Promise<WriteReturnType<ProjectActivity, U>>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function createMany<C extends RequiredContext, U extends keyof ProjectActivity>(context: C, input: CreationFields[], fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<ProjectActivity, U>[]>> {
    const convertedArgs = {data: input, fieldList: fieldlist, options: options} as Record<string, any>
    const partialPayload = {action: 'createMany', ...convertedArgs}

    return new ActionRequest<C, Promise<WriteReturnType<ProjectActivity, U>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function Delete<C extends RequiredContext>(context: C, where: UniqueWhereInput, options?: undefined): ActionRequest<C, Promise<Partial<ProjectActivity>[]>> {
    const convertedArgs = {where: where, options} as Record<string, any>
    const partialPayload = {action: 'delete', ...convertedArgs}

    return new ActionRequest<C, Promise<Partial<ProjectActivity>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function deleteAll<C extends RequiredContext>(context: C): ActionRequest<C, Promise<Partial<ProjectActivity>[]>> {
    const convertedArgs = {data: {}} as Record<string, any>
    const partialPayload = {action: 'deleteAll', ...convertedArgs}

    return new ActionRequest<C, Promise<Partial<ProjectActivity>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function deleteMany<C extends RequiredContext>(context: C, where: Filter<ProjectActivityWithAssociation>): ActionRequest<C, Promise<Partial<ProjectActivity>[]>>;
  /** @deprecated use ```client``` instead */
  export function deleteMany<C extends RequiredContext>(context: C, where: Partial<ProjectActivity>): ActionRequest<C, Promise<Partial<ProjectActivity>[]>>;
  /** @deprecated use ```client``` instead */
  export function deleteMany<C extends RequiredContext>(context: C, where: Filter<ProjectActivity> | Partial<ProjectActivity>, options?: undefined): ActionRequest<C, Promise<Partial<ProjectActivity>[]>> {
    const convertedArgs = {data: {where: where}, options} as Record<string, any>
    const partialPayload = {action: 'deleteMany', ...convertedArgs}

    return new ActionRequest<C, Promise<Partial<ProjectActivity>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function query<C extends RequiredContext, U extends FieldSelector<ProjectActivityWithAssociation>>(context: C, args: Omit<TypeSafeQueryArgType<ProjectActivityWithAssociation>, 'draft'>, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<ReadReturnTypeWithAssociations<ProjectActivityWithAssociation, U>[]>>;
  /** @deprecated use ```client``` instead */
  export function query<C extends RequiredContext, U extends keyof ProjectActivity, V extends string = 'ALL_FIELDS'>(context: C, args: Omit<TypeSafeQueryArgType<ProjectActivity>, 'draft'> | Omit<Omit<TypeSafeQueryArgType<ProjectActivity>, 'draft'>, 'where'> & { where?: Partial<ProjectActivity> }, fieldlist: ReadonlyArray<U | V> | 'ALL_FIELDS'): ActionRequest<C, Promise<ReadReturnType<V>[]>>;
  /** @deprecated use ```client``` instead */
  export function query<C extends RequiredContext>(context: C, args: Omit<TypeSafeQueryArgType<ProjectActivity>, 'draft'>, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS'): ActionRequest<C, Promise<unknown[]>> {
    const convertedArgs = {data: {...args, fieldList: fieldlist}} as Record<string, any>
    const partialPayload = {action: 'query', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function read<C extends RequiredContext, U extends FieldSelector<ProjectActivityWithAssociation>>(context: C, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): ActionRequest<C, Promise<ReadReturnTypeWithAssociations<ProjectActivityWithAssociation, U>>>;
  /** @deprecated use ```client``` instead */
  export function read<C extends RequiredContext, U extends keyof ProjectActivity>(context: C, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): ActionRequest<C, Promise<ReadReturnType<U>>>;
  /** @deprecated use ```client``` instead */
  export function read<C extends RequiredContext>(context: C, where: UniqueWhereInput, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): ActionRequest<C, Promise<unknown>> {
    const convertedArgs = {data: {where: where, fieldList: fieldlist}, options: options} as Record<string, any>
    const partialPayload = {action: 'read', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function update<C extends RequiredContext, U extends FieldSelector<ProjectActivityWithAssociation>>(context: C, where: UniqueWhereInput, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<WriteReturnTypeWithAssociations<ProjectActivityWithAssociation, U>>>;
  /** @deprecated use ```client``` instead */
  export function update<C extends RequiredContext, U extends keyof ProjectActivity>(context: C, where: Partial<ProjectActivity>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<WriteReturnType<ProjectActivity, U>>>;
  /** @deprecated use ```client``` instead */
  export function update<C extends RequiredContext>(context: C, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: undefined): ActionRequest<C, Promise<unknown>> {
    const convertedArgs = {where: where, data: data, fieldList: fieldlist, options} as Record<string, any>
    const partialPayload = {action: 'update', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function updateMany<C extends RequiredContext, U extends FieldSelector<ProjectActivityWithAssociation>>(context: C, where: Filter<ProjectActivityWithAssociation>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<WriteReturnTypeWithAssociations<ProjectActivityWithAssociation, U>[]>>;
  /** @deprecated use ```client``` instead */
  export function updateMany<C extends RequiredContext, U extends keyof ProjectActivity>(context: C, where: Partial<ProjectActivity>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<WriteReturnType<ProjectActivity, U>[]>>;
  /** @deprecated use ```client``` instead */
  export function updateMany<C extends RequiredContext>(context: C, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: undefined): ActionRequest<C, Promise<unknown[]>> {
    const convertedArgs = {data: {where: where, data: data, fieldList: fieldlist}, options} as Record<string, any>
    const partialPayload = {action: 'updateMany', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function upsert<C extends RequiredContext, U extends keyof ProjectActivity & string>(context: C, data: Partial<ProjectActivity>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<ProjectActivity, U>>>;
  /** @deprecated use ```client``` instead */
  export function upsert<C extends RequiredContext, U extends keyof ProjectActivity & string>(context: C, where: Partial<ProjectActivity>, data: Partial<ProjectActivity>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<ProjectActivity, U>>>;
  /** @deprecated use ```client``` instead */
  export function upsert<C extends RequiredContext, U extends keyof ProjectActivity & string>(context: C, whereOrData: Partial<ProjectActivity>, dataOrFieldList?: Partial<ProjectActivity> | ReadonlyArray<U> | 'ALL_FIELDS', fieldlistOrOptions?: ReadonlyArray<U> | 'ALL_FIELDS' | Omit<UpsertOptionsArg, 'draft'>, maybeOptions?: Omit<UpsertOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<ProjectActivity, U>>> {
    const convertedArgs = {data: {where: whereOrData, data: dataOrFieldList, fieldList: fieldlistOrOptions}, options: maybeOptions} as Record<string, any>
    const partialPayload = {action: 'upsert', ...convertedArgs}

    return new ActionRequest<C, Promise<WriteReturnType<ProjectActivity, U>>>(context, partialPayload);
  }
  export namespace client {

    /** write a new object to the database. */
    export declare function create<U extends keyof ProjectActivity>(input: CreationFields, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): Promise<WriteReturnType<ProjectActivity, U>>

    /** write new objects to the database. */
    export declare function createMany<U extends keyof ProjectActivity>(input: CreationFields[], fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): Promise<WriteReturnType<ProjectActivity, U>[]>

    /** delete the object that is uniquely identified by the where condition. */
    export declare function Delete(where: UniqueWhereInput, options?: undefined): Promise<Partial<ProjectActivity>[]>

    /** delete all objects. */
    export declare function deleteAll(): Promise<Partial<ProjectActivity>[]>

    /** delete the objects that are identified by the where condition. */
    export declare function deleteMany(where: Filter<ProjectActivityWithAssociation>): Promise<Partial<ProjectActivity>[]>;
    /** @deprecated use the alternative overloaded function */
    export declare function deleteMany(where: Partial<ProjectActivity>): Promise<Partial<ProjectActivity>[]>;
    export declare function deleteMany(where: Filter<ProjectActivity> | Partial<ProjectActivity>, options?: undefined): Promise<Partial<ProjectActivity>[]>

    /** returns a list of all objects matching the requirements. */
    export declare function query<U extends FieldSelector<ProjectActivityWithAssociation>>(args: Omit<TypeSafeQueryArgType<ProjectActivityWithAssociation>, 'draft'>, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<ReadReturnTypeWithAssociations<ProjectActivityWithAssociation, U>[]>;
    /** @deprecated Use the alternative overloaded method for querying associations. */
    export declare function query<U extends keyof ProjectActivity, V extends string = 'ALL_FIELDS'>(args: Omit<TypeSafeQueryArgType<ProjectActivity>, 'draft'> | Omit<Omit<TypeSafeQueryArgType<ProjectActivity>, 'draft'>, 'where'> & { where?: Partial<ProjectActivity> }, fieldlist: ReadonlyArray<U | V> | 'ALL_FIELDS'): Promise<ReadReturnType<V>[]>;
    export declare function query(args: Omit<TypeSafeQueryArgType<ProjectActivity>, 'draft'>, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown[]>

    /** reads the object that is uniquely identified by the where condition. */
    export declare function read<U extends FieldSelector<ProjectActivityWithAssociation>>(where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnTypeWithAssociations<ProjectActivityWithAssociation, U>>;
    /** @deprecated Use the alternative overloaded method for reading associations. */
    export declare function read<U extends keyof ProjectActivity>(where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnType<U>>;
    export declare function read(where: UniqueWhereInput, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<unknown>

    /** writes modified values back to the database for a given node instance. */
    export declare function update<U extends FieldSelector<ProjectActivityWithAssociation>>(where: UniqueWhereInput, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnTypeWithAssociations<ProjectActivityWithAssociation, U>>;
    /** @deprecated Use the alternative overloaded method for writing with associations. */
    export declare function update<U extends keyof ProjectActivity>(where: Partial<ProjectActivity>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnType<ProjectActivity, U>>;
    export declare function update(where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: undefined): Promise<unknown>

    /** writes modified values back to the database for selected node instances. */
    export declare function updateMany<U extends FieldSelector<ProjectActivityWithAssociation>>(where: Filter<ProjectActivityWithAssociation>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnTypeWithAssociations<ProjectActivityWithAssociation, U>[]>;
    /** @deprecated Use the alternative overloaded method for writing with associations. */
    export declare function updateMany<U extends keyof ProjectActivity>(where: Partial<ProjectActivity>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnType<ProjectActivity, U>[]>;
    export declare function updateMany(where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: undefined): Promise<unknown[]>

    /** creates or updates object identified by where. */
    export declare function upsert<U extends keyof ProjectActivity & string>(data: Partial<ProjectActivity>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<ProjectActivity, U>>;
    /** @deprecated use the overload without an explicit where clause */
    export declare function upsert<U extends keyof ProjectActivity & string>(where: Partial<ProjectActivity>, data: Partial<ProjectActivity>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<ProjectActivity, U>>;
    export declare function upsert<U extends keyof ProjectActivity & string>(whereOrData: Partial<ProjectActivity>, dataOrFieldList?: Partial<ProjectActivity> | ReadonlyArray<U> | 'ALL_FIELDS', fieldlistOrOptions?: ReadonlyArray<U> | 'ALL_FIELDS' | Omit<UpsertOptionsArg, 'draft'>, maybeOptions?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<ProjectActivity, U>>
  }
}
