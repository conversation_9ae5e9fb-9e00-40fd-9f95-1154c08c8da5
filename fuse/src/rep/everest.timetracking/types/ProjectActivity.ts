/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// ******************************************************************************
// Generated Everest Types
// ******************************************************************************

import type { Decimal, RoundingMode } from '@everestsystems/decimal';
import type {
  ArgType,
  Association,
  AssociationManagementArgs,
  AsyncResult,
  Controller,
  ControllerClientProvider,
  CreateOptionsArg,
  DataWithMetadata,
  DeleteOptionsArg,
  FieldSelector,
  Filter,
  flattenFieldSelector,
  GraphInstanceEntry,
  KernelRequestOptions,
  NonAssociationFields,
  Optional,
  PurgeArgs,
  ReadOptionsArg,
  ReadReturnType as ReadReturnTypeGeneric,
  ReadReturnTypeWithAssociations,
  RequireAtLeastOne,
  SemanticSearchArg,
  SemanticSearchRequestOptions,
  TypeSafeQueryArgType,
  UpdateManyOptionsArg,
  UpdateOptionsArg,
  UpsertOptionsArg,
  WriteReturnType,
  WriteReturnTypeWithAssociations,
} from '@everestsystems/content-core';
import type { EvstUUID as everest_appserver_primitive_UUID } from "@pkg/everest.appserver/types/primitives/UUID";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { EvstUserEmail as everest_appserver_usermgmt_primitive_user_UserEmail } from "@pkg/everest.appserver.usermgmt/types/primitives/user/UserEmail";
import type { EvstDateTime as everest_appserver_primitive_DateTime } from "@pkg/everest.appserver/types/primitives/DateTime";
import type { EvstActivityBillingType as everest_timetracking_enum_ActivityBillingType } from "@pkg/everest.timetracking/types/enums/ActivityBillingType";
import type { EvstPlainDate as everest_appserver_primitive_PlainDate } from "@pkg/everest.appserver/types/primitives/PlainDate";
import type { EvstID as everest_appserver_primitive_ID } from "@pkg/everest.appserver/types/primitives/ID";
import type { EvstDecimal as everest_appserver_primitive_Decimal } from "@pkg/everest.appserver/types/primitives/Decimal";
import type { TimeEntry as everest_timetracking_model_node_TimeEntry } from "@pkg/everest.timetracking/types/TimeEntry";
import type { Allocation as everest_timetracking_model_node_Allocation } from "@pkg/everest.timetracking/types/Allocation";
import type { ActivityMapping as everest_timetracking_model_node_ActivityMapping } from "@pkg/everest.timetracking/types/ActivityMapping";
import type { TimetrackingProject as everest_timetracking_model_node_TimetrackingProject } from "@pkg/everest.timetracking/types/TimetrackingProject";

/**
 * Types for ProjectActivity
 *
 * @everest-node-metadata='{"visibility":"public"}'
 */
export namespace ProjectActivity {
  export type CreationFields = Pick<ProjectActivity, 'uuid' | 'externalId' | 'active' | 'billable' | 'billingType' | 'description' | 'endDate' | 'fixedFeeId' | 'hoursBudget' | 'invoiced' | 'milestoneId' | 'name' | 'projectId' | 'startDate'>;
  export type UniqueFields = Pick<ProjectActivity, 'id' | 'uuid'>;
  export type UniqueWhereInput = (RequireAtLeastOne<UniqueFields>) & Partial<ProjectActivity>;
  export type ReadReturnType<U extends string | number | symbol = keyof ProjectActivity> = ReadReturnTypeGeneric<ProjectActivity, U>;

  export interface IControllerClient extends Omit<Controller<ProjectActivity>, 'all' | 'getHistory' | 'lookup' | 'purge' | 'queryWithMetadata' | 'semanticSearch' | 'upsertMany'> {}

  /**
   * Activity for a certain project
   */
  export type ProjectActivity = {
    /** Database generated identifier of this record. */
    id: number;
    /** Database generated version of this record. */
    version?: number;
    uuid?: everest_appserver_primitive_UUID | null;
    /**
     * ID of the system from which this record was sourced
     */
    externalId?: everest_appserver_primitive_Text | null;
    /**
     * Indicates whether this record is usable for business users
     */
    active?: everest_appserver_primitive_TrueFalse | null;
    /**
     * User that created this record
     */
    createdBy: everest_appserver_usermgmt_primitive_user_UserEmail;
    /**
     * Date/time when the record was created
     */
    createdDate: everest_appserver_primitive_DateTime;
    /**
     * User that last modified the record
     */
    lastModifiedBy: everest_appserver_usermgmt_primitive_user_UserEmail;
    /**
     * Date/time of the last modification to the record
     */
    lastModifiedDate: everest_appserver_primitive_DateTime;
    billable?: everest_appserver_primitive_TrueFalse | null;
    billingType: everest_timetracking_enum_ActivityBillingType;
    description?: everest_appserver_primitive_Text | null;
    endDate?: everest_appserver_primitive_PlainDate | null;
    fixedFeeId?: everest_appserver_primitive_ID | null;
    hoursBudget?: everest_appserver_primitive_Decimal | null;
    invoiced?: everest_appserver_primitive_TrueFalse | null;
    milestoneId?: everest_appserver_primitive_ID | null;
    name: everest_appserver_primitive_Text;
    projectId: everest_appserver_primitive_ID;
    startDate?: everest_appserver_primitive_PlainDate | null;
    };
  /**
   * Activity for a certain project
   */
  export type ProjectActivityWithAssociation = ProjectActivity & {
    ["TimeEntry-ProjectActivity"]?: Association<everest_timetracking_model_node_TimeEntry.TimeEntryWithAssociation>[];
    ["Allocation-ProjectActivity"]?: Association<everest_timetracking_model_node_Allocation.AllocationWithAssociation>[];
    ["ActivityMapping-ProjectActivity"]?: Association<everest_timetracking_model_node_ActivityMapping.ActivityMappingWithAssociation>[];
    ["ProjectActivity-Project"]?: Association<everest_timetracking_model_node_TimetrackingProject.TimetrackingProjectWithAssociation>;
    };
  /** @deprecated - use {@link MODEL_URN} instead */
  export const name = 'urn:evst:everest:timetracking:model/node:ProjectActivity';
  export const MODEL_URN = 'urn:evst:everest:timetracking:model/node:ProjectActivity';
  /** @deprecated - use {@link MODEL_URN} instead */
  export const MODEL_ID = 'everest.timetracking/ProjectActivityModel.ProjectActivity';
  export const MODEL_UUID = '81c5f68e-03d9-41b0-9b9b-46f24b4e62e8';

  /** @return a model controller instance for ProjectActivity. */
  export function client(env: ControllerClientProvider) {
    return env.controllerClient<ProjectActivity.IControllerClient>(MODEL_URN);
  }

  /** write a new object to the database. */
  export async function create<U extends keyof ProjectActivity>(env: ControllerClientProvider, input: CreationFields, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): Promise<WriteReturnType<ProjectActivity, U>> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).create(input, fields, options);
  }

  /** write new objects to the database. */
  export async function createMany<U extends keyof ProjectActivity>(env: ControllerClientProvider, input: CreationFields[], fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): Promise<WriteReturnType<ProjectActivity, U>[]> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).createMany(input, fields, options);
  }

  /** delete the object that is uniquely identified by the where condition. */
  export async function Delete(env: ControllerClientProvider, where: UniqueWhereInput): Promise<Partial<ProjectActivity>[]> {
    return (await client(env)).delete(where);
  }

  /** delete all objects. */
  export async function deleteAll(env: ControllerClientProvider): Promise<Partial<ProjectActivity>[]> {
    return (await client(env)).deleteAll();
  }

  /** delete the objects that are identified by the where condition. */
  export async function deleteMany(env: ControllerClientProvider, where: Filter<ProjectActivityWithAssociation>): Promise<Partial<ProjectActivity>[]>;
  /** @deprecated use the alternative overloaded function */
  export async function deleteMany(env: ControllerClientProvider, where: Partial<ProjectActivity>): Promise<Partial<ProjectActivity>[]>;
  export async function deleteMany(env: ControllerClientProvider, where: Filter<ProjectActivity> | Partial<ProjectActivity>): Promise<Partial<ProjectActivity>[]> {
    return (await client(env)).deleteMany(where as Filter<ProjectActivity>);
  }

  /** returns a list of all objects matching the requirements. */
  export async function query<U extends FieldSelector<ProjectActivityWithAssociation>>(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<ProjectActivityWithAssociation>, 'draft'>, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<ReadReturnTypeWithAssociations<ProjectActivityWithAssociation, U>[]>;
  /** @deprecated Use the alternative overloaded method for querying associations. */
  export async function query<U extends keyof ProjectActivity, V extends string = 'ALL_FIELDS'>(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<ProjectActivity>, 'draft'> | Omit<Omit<TypeSafeQueryArgType<ProjectActivity>, 'draft'>, 'where'> & { where?: Partial<ProjectActivity> }, fieldlist: ReadonlyArray<U | V> | 'ALL_FIELDS'): Promise<ReadReturnType<V>[]>;
  export async function query(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<ProjectActivity>, 'draft'>, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown[]> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).query(args, fields as ReadonlyArray<string>);
  }

  /** reads the object that is uniquely identified by the where condition. */
  export async function read<U extends FieldSelector<ProjectActivityWithAssociation>>(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnTypeWithAssociations<ProjectActivityWithAssociation, U>>;
  /** @deprecated Use the alternative overloaded method for reading associations. */
  export async function read<U extends keyof ProjectActivity>(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnType<U>>;
  export async function read(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<unknown> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).read(where, fields as ReadonlyArray<keyof ProjectActivity>, options);
  }

  /** writes modified values back to the database for a given node instance. */
  export async function update<U extends FieldSelector<ProjectActivityWithAssociation>>(env: ControllerClientProvider, where: UniqueWhereInput, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnTypeWithAssociations<ProjectActivityWithAssociation, U>>;
  /** @deprecated Use the alternative overloaded method for writing with associations. */
  export async function update<U extends keyof ProjectActivity>(env: ControllerClientProvider, where: Partial<ProjectActivity>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnType<ProjectActivity, U>>;
  export async function update(env: ControllerClientProvider, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).update(where, data, fields as ReadonlyArray<keyof ProjectActivity>);
  }

  /** writes modified values back to the database for selected node instances. */
  export async function updateMany<U extends FieldSelector<ProjectActivityWithAssociation>>(env: ControllerClientProvider, where: Filter<ProjectActivityWithAssociation>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnTypeWithAssociations<ProjectActivityWithAssociation, U>[]>;
  /** @deprecated Use the alternative overloaded method for writing with associations. */
  export async function updateMany<U extends keyof ProjectActivity>(env: ControllerClientProvider, where: Partial<ProjectActivity>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnType<ProjectActivity, U>[]>;
  export async function updateMany(env: ControllerClientProvider, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown[]> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).updateMany(where, data, fields as ReadonlyArray<keyof ProjectActivity>);
  }

  /** creates or updates object identified by where. */
  export async function upsert<U extends keyof ProjectActivity & string>(env: ControllerClientProvider, data: Partial<ProjectActivity>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<ProjectActivity, U>>;
  /** @deprecated use the overload without an explicit where clause */
  export async function upsert<U extends keyof ProjectActivity & string>(env: ControllerClientProvider, where: Partial<ProjectActivity>, data: Partial<ProjectActivity>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<ProjectActivity, U>>;
  export async function upsert<U extends keyof ProjectActivity & string>(env: ControllerClientProvider, whereOrData: Partial<ProjectActivity>, dataOrFieldList?: Partial<ProjectActivity> | ReadonlyArray<U> | 'ALL_FIELDS', fieldlistOrOptions?: ReadonlyArray<U> | 'ALL_FIELDS' | Omit<UpsertOptionsArg, 'draft'>, maybeOptions?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<ProjectActivity, U>> {
    return (await client(env)).upsert(whereOrData, dataOrFieldList, fieldlistOrOptions, maybeOptions);
  }
}
