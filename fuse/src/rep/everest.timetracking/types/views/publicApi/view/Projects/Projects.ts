/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// ******************************************************************************
// Generated Everest Types
// ******************************************************************************

import type { Decimal, RoundingMode } from '@everestsystems/decimal';
import type {
  ArgType,
  Association,
  AssociationManagementArgs,
  AsyncResult,
  Controller,
  ControllerClientProvider,
  CreateOptionsArg,
  DataWithMetadata,
  DeleteOptionsArg,
  FieldSelector,
  Filter,
  flattenFieldSelector,
  GraphInstanceEntry,
  KernelRequestOptions,
  NonAssociationFields,
  Optional,
  PurgeArgs,
  ReadOptionsArg,
  ReadReturnType as ReadReturnTypeGeneric,
  ReadReturnTypeWithAssociations,
  RequireAtLeastOne,
  SemanticSearchArg,
  SemanticSearchRequestOptions,
  TypeSafeQueryArgType,
  UpdateManyOptionsArg,
  UpdateOptionsArg,
  UpsertOptionsArg,
  WriteReturnType,
  WriteReturnTypeWithAssociations,
} from '@everestsystems/content-core';
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { EvstPlainDateTime as everest_appserver_primitive_PlainDateTime } from "@pkg/everest.appserver/types/primitives/PlainDateTime";
import type { EvstDecimal as everest_appserver_primitive_Decimal } from "@pkg/everest.appserver/types/primitives/Decimal";
import type { EvstPlainDate as everest_appserver_primitive_PlainDate } from "@pkg/everest.appserver/types/primitives/PlainDate";

/** Generated types for View Projects */
export namespace Projects {
  export type Projects = {
    select?: everest_appserver_primitive_Text | null;
    projectId?: everest_appserver_primitive_Number | null;
    teamId?: everest_appserver_primitive_Number | null;
    budgetId?: everest_appserver_primitive_Number | null;
    active?: everest_appserver_primitive_TrueFalse | null;
    projectName?: everest_appserver_primitive_Text | null;
    startDate?: everest_appserver_primitive_PlainDateTime | null;
    endDate?: everest_appserver_primitive_PlainDateTime | null;
    status?: everest_appserver_primitive_Text | null;
    positionId?: everest_appserver_primitive_Number | null;
    positionName?: everest_appserver_primitive_Text | null;
    positionDescription?: everest_appserver_primitive_Text | null;
    positionSalesOrderProductLineId?: everest_appserver_primitive_Number | null;
    positionBillable?: everest_appserver_primitive_TrueFalse | null;
    positionHourlyBillRate?: everest_appserver_primitive_Decimal | null;
    positionHourlyBillRateCurrency?: everest_appserver_primitive_Text | null;
    positionHourlyCostRate?: everest_appserver_primitive_Decimal | null;
    positionHourlyCostRateCurrency?: everest_appserver_primitive_Text | null;
    positionHoursBudget?: everest_appserver_primitive_Decimal | null;
    positionInvoiced?: everest_appserver_primitive_TrueFalse | null;
    activityId?: everest_appserver_primitive_Number | null;
    activityName?: everest_appserver_primitive_Text | null;
    activityDescription?: everest_appserver_primitive_Text | null;
    activityHoursBudget?: everest_appserver_primitive_Decimal | null;
    activityInvoiced?: everest_appserver_primitive_TrueFalse | null;
    isBillableActivity?: everest_appserver_primitive_TrueFalse | null;
    memberId?: everest_appserver_primitive_Number | null;
    memberInvoiced?: everest_appserver_primitive_TrueFalse | null;
    memberEmployeeId?: everest_appserver_primitive_Number | null;
    memberName?: everest_appserver_primitive_Text | null;
    memberEmail?: everest_appserver_primitive_Text | null;
    memberPhoto?: everest_appserver_primitive_Text | null;
    memberCapacity?: everest_appserver_primitive_Number | null;
    memberIsTeamLead?: everest_appserver_primitive_TrueFalse | null;
    teamMemberId?: everest_appserver_primitive_Number | null;
    memberDisplayName?: everest_appserver_primitive_Text | null;
    memberProjectPositionId?: everest_appserver_primitive_Number | null;
    activityMilestoneId?: everest_appserver_primitive_Number | null;
    activityFixedFeeId?: everest_appserver_primitive_Number | null;
    activityBillingType?: everest_appserver_primitive_Text | null;
    activityStartDate?: everest_appserver_primitive_PlainDate | null;
    activityEndDate?: everest_appserver_primitive_PlainDate | null;
    };

  export type ProjectsParameters = {
    select: Projects['select'];
    };

  export type UniqueFields = Partial<Pick<Projects, 'select' | 'projectId' | 'teamId' | 'budgetId' | 'active' | 'projectName' | 'startDate' | 'endDate' | 'status' | 'positionId' | 'positionName' | 'positionDescription' | 'positionSalesOrderProductLineId' | 'positionBillable' | 'positionHourlyBillRate' | 'positionHourlyBillRateCurrency' | 'positionHourlyCostRate' | 'positionHourlyCostRateCurrency' | 'positionHoursBudget' | 'positionInvoiced' | 'activityId' | 'activityName' | 'activityDescription' | 'activityHoursBudget' | 'activityInvoiced' | 'isBillableActivity' | 'memberId' | 'memberInvoiced' | 'memberEmployeeId' | 'memberName' | 'memberEmail' | 'memberPhoto' | 'memberCapacity' | 'memberIsTeamLead' | 'teamMemberId' | 'memberDisplayName' | 'memberProjectPositionId' | 'activityMilestoneId' | 'activityFixedFeeId' | 'activityBillingType' | 'activityStartDate' | 'activityEndDate'>>;
  export type UniqueWhereInput = Partial<Projects>;
  export type ReadReturnType<U extends string | number | symbol = keyof Projects> = ReadReturnTypeGeneric<Projects, U>;

  export interface IControllerClient extends Omit<Controller<Projects>, 'all' | 'create' | 'createMany' | 'delete' | 'deleteAll' | 'deleteMany' | 'explainRead' | 'exportMany' | 'getHistory' | 'importMany' | 'lock' | 'lookup' | 'purge' | 'semanticSearch' | 'unlock' | 'update' | 'updateMany' | 'upsert' | 'upsertMany'> {}

  /** @return a model controller instance for Projects. */
  export function client(env: ControllerClientProvider) {
    return env.controllerClient<Projects.IControllerClient>(MODEL_URN);
  }

  /** returns a list of all objects matching the requirements. */
  export async function query<U extends keyof Projects, V extends string = 'ALL_FIELDS'>(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<Projects>, 'draft'> | Omit<Omit<TypeSafeQueryArgType<Projects>, 'draft'>, 'where'> & { where?: Partial<Projects> }, fieldlist: ReadonlyArray<U | V> | 'ALL_FIELDS'): Promise<ReadReturnType<V>[]>;
  export async function query(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<Projects>, 'draft'>, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown[]> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).query(args, fields as ReadonlyArray<string>);
  }

  export async function queryWithMetadata<U extends keyof Projects = keyof Projects>(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<Projects>, 'draft'>, fieldlist: ReadonlyArray<U>): Promise<DataWithMetadata<Projects, U>> {
    return (await client(env)).queryWithMetadata(args, fieldlist);
  }

  /** @deprecated Use the alternative overloaded method for reading associations. */
  export async function read<U extends keyof Projects>(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnType<U>>;
  export async function read(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<unknown> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).read(where, fields as ReadonlyArray<keyof Projects>, options);
  }

  export const MODEL_URN: string = 'urn:evst:everest:timetracking:model/view:publicApi/view/Projects/Projects';
}
