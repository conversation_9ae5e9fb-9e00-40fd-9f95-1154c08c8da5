{"version": 2, "uicontroller": "executiveDashboard.uicontroller.ts", "uimodel": {"state": {"duration": "6"}, "nodes": {"postingPeriod": {"type": "list", "modelId": "everest.fin.accounting/AccountingPeriodModel.AccountingPeriod", "query": "@controller:postingPeriodQuery()", "fieldList": ["fiscalYear", "fiscalQuarter", "periodName", "startDate", "endDate"]}, "cashBalance": {"modelId": "everest.fin.accounting/JournalEntryLineModel.JournalEntryLine", "type": "struct", "detailedType": "treeDataTable", "balanceSheet": "@controller:getCashBalanceBSQuery()"}, "cashBalanceQuarter": {"modelId": "everest.fin.accounting/JournalEntryLineModel.JournalEntryLine", "type": "struct", "detailedType": "treeDataTable", "balanceSheet": "@controller:getCashBalanceQuarterBSQuery()"}, "incomeStatement": {"modelId": "everest.fin.accounting/JournalEntryLineModel.JournalEntryLine", "type": "struct", "detailedType": "treeDataTable", "incomeStatement": "@controller:getIsQuery()"}, "breakdownChart": {"model": "urn:evst:everest:fin/accounting:model/node:analytics/ARR", "type": "struct", "entityBreakdown": "@controller:getBreakdownChartQuery()"}, "headcountByDepData": {"modelId": "everest.analytics/AnalyticEngineModel.AnalyticEngine", "type": "struct", "fieldList": [], "sliceCubeArtifact": "@controller:headcountByDepDataQuery()"}, "totalHeadcount": {"modelId": "everest.analytics/AnalyticEngineModel.AnalyticEngine", "type": "struct", "fieldList": [], "sliceCubeArtifact": "@controller:totalHeadcountDataQuery()"}, "spendByDepartment": {"modelId": "everest.fin.accounting/JournalEntryLineModel.JournalEntryLine", "type": "list", "spendByDepartment": "@controller:getSpendByDepartmentQuery()"}, "cloudCostReport": {"type": "struct", "getAllCloudCosts": "@controller:getCloudCostReport()", "modelId": "everest.cloudcosts/CloudCostLineModel.CloudCostLine"}, "headcountIncludeStudents": {"modelId": "everest.appserver/ConfigurationModel.Configuration", "type": "struct", "getValueOrDefault": {"name": "EXECUTIVE_DASHBOARD_INCLUDE_STUDENTS", "packageName": "everest.simplifieddashboard", "defaultValue": true}, "fieldList": ["value"]}}}, "uiview": {"layout": "everest.base.ui/layouts/uilayout/startPage/startPage", "props": {"i18n": "everest.fin.accounting/executiveDashboard", "title": "{{title}}", "description": "@controller:executiveDashboardDescription()", "autoRefreshData": ["entities"], "keyDataConfig": {"@uifragment": {"path": "everest.base/entity/uinext/entityPicker.uifragment.json", "props": {"listSingleEntities": true, "listConsolidatedEntities": true, "listNonLegalEntities": true, "displayCurrency": true, "displaySectionLabels": true, "getConfigDefaultEntity": true}}}, "headerActions": {"direction": "horizontal", "content": [{"label": "{{selectTrailing}}", "actions": [{"label": "{{3<PERSON><PERSON><PERSON>}}", "onClick": "@controller:setDurationTo3Months", "disabled": "@controller:isDurationDisabled('3')"}, {"label": "{{6Months}}", "onClick": "@controller:setDurationTo6Months", "disabled": "@controller:isDurationDisabled('6')"}, {"label": "{{12Months}}", "onClick": "@controller:setDurationTo12Months", "disabled": "@controller:isDurationDisabled('12')"}]}], "hybridInput": [{"@uifragment": {"path": "everest.fin.accounting/accountingBook/bookPicker/uinext/bookPicker"}}, {"field": {"component": "Select", "value": "@controller:getDropdownListValue()", "idProp": "value", "textProp": "label", "list": "@controller:getPeriodDropDownData()", "onChange": "@controller:onDropdownListChange", "isSelect": true}}]}, "summaryColumns": 3, "summaryFields": [{"label": "{{cashBalance}}", "parseAs": "currency", "value": "@controller:getCashBalanceValue()"}, {"label": "{{avgNetBurn}}", "parseAs": "currency", "value": "@controller:getAvgNetBurnValue()"}, {"label": "{{runwayRunRate}}", "value": "@controller:getRunwayRunRateValue()"}], "firstRowContent": {"first": {"component": "WidgetGroup", "title": "{{netBurnAndCashBalance}}", "size": "4", "section": {"variant": "card", "contained": true}, "props": {"widgets": [{"label": "{{Month}}", "component": "Chart", "props": {"height": "300px", "type": "bar", "data": {"labels": "@controller:graphLabels('month')", "datasets": [{"label": "{{cashBalance}}", "type": "line", "data": "@controller:cashBalanceData('month')", "backgroundColor": "#EE8D46", "borderColor": "#EE8D46", "yAxisID": "y", "tension": 0.1}, {"label": "{{netBurn}}", "data": "@controller:netBurnData('month')", "backgroundColor": "#4D6ED6", "yAxisID": "y1"}]}, "options": {"plugins": {"legend": {"position": "top", "labels": {"fontColor": "#000080", "usePointStyle": true, "boxWidth": 6, "boxHeight": 6}}}, "scales": {"y": {"type": "linear", "display": true, "position": "left", "beginAtZero": false, "grid": {"drawOnChartArea": false}}, "y1": {"type": "linear", "display": true, "position": "right", "beginAtZero": false, "grid": {"drawOnChartArea": false}}}}}}, {"label": "{{Quarter}}", "component": "Chart", "props": {"height": "300px", "type": "bar", "data": {"labels": "@controller:graphLabels('quarter')", "datasets": [{"label": "{{cashBalance}}", "type": "line", "data": "@controller:cashBalanceData('quarter')", "backgroundColor": "#EE8D46", "borderColor": "#EE8D46", "yAxisID": "y", "tension": 0.1}, {"label": "{{netBurn}}", "data": "@controller:netBurnData('quarter')", "backgroundColor": "#4D6ED6", "yAxisID": "y1"}]}, "options": {"plugins": {"legend": {"position": "top", "labels": {"fontColor": "#000080", "usePointStyle": true, "boxWidth": 6, "boxHeight": 6}}}, "scales": {"y": {"type": "linear", "display": true, "position": "left", "beginAtZero": false, "grid": {"drawOnChartArea": false}}, "y1": {"type": "linear", "display": true, "position": "right", "beginAtZero": false, "grid": {"drawOnChartArea": false}}}}}}]}}, "second": {"component": "WidgetGroup", "section": {"variant": "card", "title": "{{revenueSpendAndProfit}}", "contained": true, "grid": {"size": "4"}}, "props": {"onWidgetChange": "@controller:onRevenueSpendAndProfitWidgetChange", "widgets": [{"label": "{{Month}}", "component": "Chart", "props": {"height": "300px", "type": "line", "data": {"labels": "@controller:graphLabels('month')", "datasets": [{"label": "{{revenue}}", "data": "@controller:revenueData()", "borderColor": "#EE8D46", "backgroundColor": "#EE8D46", "tension": 0.1}, {"label": "{{spend}}", "data": "@controller:spendData()", "borderColor": "#CB2C90", "backgroundColor": "#CB2C90", "tension": 0.1}, {"label": "{{profit}}", "data": "@controller:profitData()", "borderColor": "#00A854", "backgroundColor": "#00A854", "tension": 0.1}]}, "options": {"plugins": {"legend": {"position": "top", "labels": {"fontColor": "#000080", "usePointStyle": true, "boxWidth": 6, "boxHeight": 6}}}}}}, {"label": "{{Quarter}}", "component": "Chart", "props": {"height": "300px", "type": "line", "data": {"labels": "@controller:graphLabels('quarter')", "datasets": [{"label": "{{revenue}}", "data": "@controller:revenueData()", "borderColor": "#EE8D46", "backgroundColor": "#EE8D46", "tension": 0.1}, {"label": "{{spend}}", "data": "@controller:spendData()", "borderColor": "#CB2C90", "backgroundColor": "#CB2C90", "tension": 0.1}, {"label": "{{profit}}", "data": "@controller:profitData()", "borderColor": "#00A854", "backgroundColor": "#00A854", "tension": 0.1}]}, "options": {"plugins": {"legend": {"position": "top", "labels": {"fontColor": "#000080", "usePointStyle": true, "boxWidth": 6, "boxHeight": 6}}}}}}]}}, "third": {"component": "WidgetGroup", "visible": false, "section": {"variant": "card", "title": "{{arrBreakdown}}", "contained": true, "grid": {"size": "4"}}, "props": {"widgets": [{"label": "{{Month}}", "component": "Chart", "section": {"grid": {"size": "6"}}, "props": {"type": "bar", "data": "@controller:getBreakdownChartData('month')", "height": "300px", "options": "@controller:getBreakdownChartOptions()"}}, {"label": "{{Quarter}}", "component": "Chart", "section": {"grid": {"size": "6"}}, "props": {"type": "bar", "data": "@controller:getBreakdownChartData('quarter')", "height": "300px", "options": "@controller:getBreakdownChartOptions()"}}]}}}, "secondRowContent": [{"component": "WidgetGroup", "section": {"variant": "card", "title": "{{headcount}}", "contained": true, "grid": {"size": "4"}}, "props": {"onWidgetChange": "@controller:onHeadcountByMonthWidgetChange", "widgets": [{"label": "{{Total}}", "component": "Chart", "props": {"height": "300px", "type": "bar", "data": "@controller:totalActualHeadcountData()", "options": {"plugins": {"legend": {"display": false}}, "scales": {"x": {"grid": {"display": false}}, "y": {"ticks": {"stepSize": 1, "precision": 0}}}}}}, {"label": "{{Department}}", "component": "Chart", "props": {"type": "bar", "height": "300px", "data": "@controller:getHeadcountByDepData()", "options": {"scales": {"x": {"stacked": true, "grid": {"display": false}}, "y": {"stacked": true, "ticks": {"stepSize": 1, "precision": 0}}}, "plugins": {"legend": {"position": "top", "labels": {"fontColor": "#000080", "usePointStyle": true, "boxWidth": 6, "boxHeight": 6}}}}}}]}}, {"component": "WidgetGroup", "section": {"variant": "card", "title": "{{spendByDepartment}}", "contained": true, "grid": {"size": "4"}}, "props": {"onWidgetChange": "@controller:onSpendByDepartmentWidgetChange", "widgets": [{"label": "{{Month}}", "component": "Chart", "section": {"grid": {"size": "6"}}, "props": {"height": "300px", "type": "bar", "data": {"labels": "@controller:graphLabels('month')", "datasets": "@controller:spendByDepartmentDataset()"}, "options": {"scales": {"x": {"stacked": true, "grid": {"display": false}}, "y": {"stacked": true}}, "plugins": {"legend": {"position": "top", "labels": {"fontColor": "#000080", "usePointStyle": true, "boxWidth": 6, "boxHeight": 6}}}}}}, {"label": "{{Quarter}}", "component": "Chart", "section": {"grid": {"size": "6"}}, "props": {"height": "300px", "type": "bar", "data": {"labels": "@controller:graphLabels('quarter')", "datasets": "@controller:spendByDepartmentDataset()"}, "options": {"scales": {"x": {"stacked": true, "grid": {"display": false}}, "y": {"stacked": true}}, "plugins": {"legend": {"position": "top", "labels": {"fontColor": "#000080", "usePointStyle": true, "boxWidth": 6, "boxHeight": 6}}}}}}]}}, {"component": "Chart", "visible": false, "section": {"grid": {"size": "4"}, "title": "{{cloudCosts}}", "contained": true}, "props": {"height": "300px", "type": "bar", "data": "@binding:cloudCostReport", "options": "@controller:getCCBarChartOptions()"}}]}}}