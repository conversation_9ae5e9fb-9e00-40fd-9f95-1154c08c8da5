// @i18n:everest.fin.accounting/executiveDashboard

import type {
  Ui<PERSON>hart,
  UiTable2Json,
} from '@pkg/everest.analytics/public/formats';
import { formatDateWithoutTimezone } from '@pkg/everest.fin.accounting/public/utils/Date/date';
import { EvstAccountSubType } from '@pkg/everest.fin.accounting/types/enums/AccountSubType';
import { isNil } from 'lodash';
import { BookPickerPublic } from '@pkg/everest.fin.accounting/public/accountingBook/bookPicker';
import type { ExecutiveDashboardUiTemplate } from '../types/uiTemplates/uinext/executiveDashboard.ui';

type ExecutiveDashboardContext =
  ExecutiveDashboardUiTemplate.ExecutiveDashboardContext;

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type Any = any;

type queryReturnType = Record<string, unknown>;

type Entities = { id: string; entityName: string; currency: string };

const EXPENSE_TYPES = new Set([
  EvstAccountSubType.CostOfGoodsSold,
  EvstAccountSubType.Expense,
  EvstAccountSubType.OtherExpense,
]);

export type Granularity = 'month' | 'quarter';

export type Context = BookPickerPublic.Context & ExecutiveDashboardContext & {
  state: ExecutiveDashboardContext['state'] & {
    dropdownListPeriod: Any;
    entities: Entities[];
    entityPicker: {
      onValueChange?: (context: Context, id: string | number) => void;
      [k: string]: unknown;
    };
    granularity: Granularity;
    headcountByMonthWidget: 'department' | 'total';
    revenueSpendAndProfitWidget: 'month' | 'quarter';
    spendByDepartmentWidget: 'month' | 'quarter';
  };
  data: ExecutiveDashboardContext['data'] & {
    headcountData?: {
      data: UiChart.ChartData;
    };
    totalHeadcount: Any;
    headcountByDepData: Any;
    cashBalance: Any;
    cashBalanceQuarter: Any;
    incomeStatement: UiTable2Json;
  };
};

export function getEntity(context: Context) {
  const { state } = context;
  const { entityPicker } = state;
  return entityPicker.entityId;
}

export function postingPeriodQuery(context: Context) {
  const bookId = BookPickerPublic.getSelectedBookId(context);
  const entityId = getEntity(context);
  if (!entityId || !bookId) {
    return {};
  }

  return {
    where: {
      'EntityAccountingPeriodStatus.EntityBook.entityId': entityId,
    },
    orderBy: [{ field: 'startDate', ordering: 'asc' }],
  };
}

export function getCashBalanceBSQuery(context: Context) {
  const { state } = context;
  const { entityPicker, dropdownListPeriod, duration } = state;
  const { entityId, consolidated: isConsolidated } = entityPicker;
  const bookId = BookPickerPublic.getSelectedBookId(context);
  const period = dropdownListPeriod;
  if (!period || !entityId || !bookId) {
    return;
  }

  return {
    args: {
      entityId,
      bookId,
      period,
      isConsolidated,
      splitBy: 'month',
      trailing: `MTH-${Number(duration) + 1}`,
      compare: undefined,
      hierarchy: {
        nodeId: null,
        query: 'children',
        maxDepth: 10,
      },
      dateFilterType: 'period',
    },
    getExplanation: false,
  };
}

export function getCashBalanceQuarterBSQuery(context: Context) {
  const { state } = context;
  const { entityPicker, dropdownListPeriod, duration } = state;
  const { entityId, consolidated: isConsolidated } = entityPicker;
  const bookId = BookPickerPublic.getSelectedBookId(context);

  const period = dropdownListPeriod;
  if (!period || !entityId || !bookId) {
    return;
  }

  const quarterCount = Math.ceil((Number(duration) + 1) / 3);

  return {
    args: {
      entityId,
      bookId,
      period,
      isConsolidated,
      splitBy: 'quarter',
      trailing: `MTH-${quarterCount}`,
      compare: undefined,
      hierarchy: {
        nodeId: null,
        query: 'children',
        maxDepth: 10,
      },
      dateFilterType: 'period',
    },
    getExplanation: false,
  };
}

export function getIsQuery(context: Context): queryReturnType {
  const { state, data } = context;
  const {
    entityPicker,
    dropdownListPeriod,
    duration,
    revenueSpendAndProfitWidget,
  } = state;

  const bookId = BookPickerPublic.getSelectedBookId(context);
  const { entityId, consolidated: isConsolidated } = entityPicker ?? {};
  
  if (!entityId ||!bookId) {
    return undefined;
  }

  const endDatePeriodIndex = data.postingPeriod.findIndex((period) => {
    return period.periodName === dropdownListPeriod.periodName;
  });

  const startDatePeriodIndex = endDatePeriodIndex - (Number(duration) - 1);

  const startDatePeriod =
    data.postingPeriod?.[startDatePeriodIndex < 0 ? 0 : startDatePeriodIndex];

  const period = {
    startDate: startDatePeriod.startDate,
    endDate: dropdownListPeriod.endDate,
  };

  if (!period) {
    return;
  }

  const compare = {};

  return {
    args: {
      hierarchy: {
        nodeId: null,
        query: 'children',
        maxDepth: 10,
      },
      entityId,
      bookId,
      isConsolidated,
      period,
      splitBy: revenueSpendAndProfitWidget,
      dateFilterType: 'period',
      ...compare,
    },
    getExplanation: false,
  };
}
export function getBreakdownChartQuery(_context: Context) {
  // Disabling report because of issues with the ARR calculation
  // May be activated again once MR below is merged
  // https://appdev.internal.everest-erp.com/sandboxes/mergeRequest?id=86874
  return undefined;

  // return { entityId: getEntity(context) };
}

export function headcountByDepDataQuery(context: Context): queryReturnType {
  const { state } = context;
  const { headcountByMonthWidget } = state;

  if (headcountByMonthWidget !== 'department') {
    return undefined;
  }

  return buildHeadcountCubeQuery(context, true);
}

export function buildHeadcountCubeQuery(
  context: Context,
  splitByDepartment: boolean
): Record<string, unknown> {
  const { state, data } = context;
  const { entityPicker, dropdownListPeriod, duration } = state;
  const { entityId, consolidated } = entityPicker;
  const { headcountIncludeStudents } = data;

  const endDatePeriodIndex = data.postingPeriod?.findIndex(
    (period) => period.periodName === dropdownListPeriod?.periodName
  );
  const startDatePeriodIndex = Math.max(
    0,
    endDatePeriodIndex - (Number(duration) - 1)
  );

  const startDatePeriod = data.postingPeriod[startDatePeriodIndex];
  const startDate = startDatePeriod?.startDate;
  const endDate = dropdownListPeriod?.endDate;

  if ([entityId, startDate, endDate].some(isNil)) {
    return undefined;
  }

  const dimensions: unknown[] = [
    {
      name: 'Date',
      attributes: [
        {
          name: 'date',
          transforms: [
            {
              type: 'format-date',
              format: 'MMM yyyy',
            },
          ],
        },
      ],
    },
  ];

  const output = {
    format: 'ui-chart',
    formatOptions: {
      title: 'Headcount',
      'ui-chart': {},
    },
    axes: [
      {
        entries: [
          {
            dimension: 'Date',
            attribute: 'date',
          },
          { type: 'measures' },
        ],
      },
    ],
  };

  let sort: undefined | unknown[] = undefined;
  const filter: unknown[] = [];

  if (splitByDepartment) {
    dimensions.push({
      name: 'Department',
      attributes: ['name', 'code'],
      hierarchy: {
        name: 'DepartmentHierarchy',
        levels: { limit: 1 },
      },
      loadedMembers: 'with-records',
    });

    sort = [
      {
        dimension: 'Department',
        attribute: 'code',
        order: 'asc',
      },
    ];

    output.axes.push({
      entries: [
        {
          dimension: 'Department',
          attribute: 'name',
        },
      ],
    });

    output.formatOptions['ui-chart'] = {
      colorEachDataElement: true,
      stacked: true,
    };
  }

  if (!headcountIncludeStudents) {
    /**
     * Temporarily introduced for ERE, so that students are not accounted for.
     * This should be moved to a dashboard configuration once there's support for it.
     */
    filter.push({
      dimension: 'Department',
      where: {
        name: { $ne: 'Students' },
      },
    });
  }

  return {
    cube: 'everest.hr.base/analytics/Headcount.cube.json',
    query: {
      measures: [
        {
          name: 'headcount',
          hierarchyAggregation: 'SUM',
        },
      ],
      dimensions,
      filter,
      output,
      sort,
      parameters: {
        startDate,
        endDate,
        entityId,
        consolidated,
      },
    },
  };
}

export function totalHeadcountDataQuery(context: Context): queryReturnType {
  const { state } = context;
  const { headcountByMonthWidget } = state;

  if (headcountByMonthWidget !== 'total') {
    return undefined;
  }

  return buildHeadcountCubeQuery(context, false);
}

export function getSpendByDepartmentQuery(context: Context) {
  const { state, data } = context;
  const {
    entityPicker,
    dropdownListPeriod,
    duration,
    spendByDepartmentWidget,
  } = state;

  const bookId = BookPickerPublic.getSelectedBookId(context);
  const { entityId, consolidated: isConsolidated } = entityPicker ?? {};
  
  if (!entityId || !bookId) {
    return undefined;
  }

  const currentPeriodIndex = data.postingPeriod.indexOf(
    data.postingPeriod?.find((postingPeriod) => {
      return postingPeriod.periodName === dropdownListPeriod.periodName;
    })
  );

  const trailingPeriods = [];
  for (const [index, period] of Object.entries(data.postingPeriod)) {
    if (
      Number(index) <= currentPeriodIndex &&
      Number(index) > currentPeriodIndex - Number(duration)
    ) {
      if (spendByDepartmentWidget === 'quarter') {
        const remove = trailingPeriods.findIndex((dt) => {
          return (
            dt.fiscalQuarter === period.fiscalQuarter &&
            period.endDate > dt.endDate
          );
        });
        if (remove > -1) {
          trailingPeriods.splice(remove, 1, {
            ...trailingPeriods[remove],
            endDate: period.endDate,
          });
        } else {
          trailingPeriods.push({ ...period, periodName: period.fiscalQuarter });
        }
      } else {
        trailingPeriods.push(period);
      }
    }
  }

  if (trailingPeriods.length === 0) {
    return;
  }

  //TODO-MB: add book picker in executive dashboard
  return {
    entityId,
    bookId,
    trailingPeriods,
    isConsolidated,
  };
}

export function getCloudCostReport(_context: Context) {
  // const { state } = context;

  // return { duration: Number(state.duration), colours: colors };
  return undefined;
}

export function executiveDashboardDescription(context: Context) {
  const { state } = context;

  const month = state.dropdownListPeriod?.periodName;
  const trailing = state.duration;

  return `As of ${month} - with ${trailing} month view`;
}

export async function setDurationTo3Months(context: Context) {
  const { state } = context;
  state.duration = '3';
}
export async function setDurationTo6Months(context: Context) {
  const { state } = context;
  state.duration = '6';
}
export async function setDurationTo12Months(context: Context) {
  const { state } = context;
  state.duration = '12';
}

export function isDurationDisabled(context: Context, duration: string) {
  const { state } = context;

  return state.duration === duration;
}

export function getPeriodIdx(context: Context, date: Any) {
  const { data } = context;

  return data?.postingPeriod?.findIndex(
    (el: Any) =>
      formatDateWithoutTimezone(el.startDate) <=
        formatDateWithoutTimezone(date) &&
      formatDateWithoutTimezone(el.endDate) >= formatDateWithoutTimezone(date)
  );
}

export function getCurrentPeriod(context: Context) {
  const { data } = context;

  const idx = getPeriodIdx(context, new Date());
  if ((idx ?? -1) !== -1) {
    return data.postingPeriod[idx];
  }
}

export function getDropdownListValue(context: Context) {
  const { state } = context;

  if (
    !state.dropdownListPeriod ||
    !state.dropdownListPeriod.periodName ||
    !state.dropdownListPeriod.endDate ||
    !state.dropdownListPeriod.startDate
  ) {
    const { periodName, endDate, startDate } = getCurrentPeriod(context) ?? {};
    state.dropdownListPeriod = { periodName, endDate, startDate };
  }
  return state.dropdownListPeriod.periodName;
}

export function getPeriodDropDownData(context: Context) {
  const { data } = context;

  const years: Record<string, Any> = {};

  for (const postingPeriod of data?.postingPeriod ?? []) {
    years[postingPeriod.fiscalYear] = years[postingPeriod.fiscalYear]
      ? [
          ...years[postingPeriod.fiscalYear],
          {
            value: postingPeriod.periodName,
            label: postingPeriod.periodName,
            period: postingPeriod.periodName,
          },
        ]
      : [
          {
            value: postingPeriod.periodName,
            label: postingPeriod.periodName,
            period: postingPeriod.periodName,
          },
        ];
  }

  const result = [];

  for (const key in years) {
    const data = years[key];
    result.push({
      value: key,
      label: key,
      list: data,
    });
  }
  return result;
}

export function onDropdownListChange(context: Context, selected: Any) {
  const { data, state } = context;

  const period = data.postingPeriod.find((period) => {
    return period.periodName === selected;
  });
  state.dropdownListPeriod = {
    periodName: selected,
    endDate: period.endDate,
    startDate: period.startDate,
  };
}

export function getCashBalanceValue(context: Context) {
  const cb = cashBalanceData(context, 'month');
  return cb[cb.length - 1];
}

export function getAvgNetBurnValue(context: Context) {
  const { state } = context;

  const cashBalanceValue = getCashBalanceValue(context);

  if (cashBalanceValue < 0) {
    return '-';
  }

  const netBurn = netBurnData(context, 'month');

  let totalNetBurn = 0;

  for (const value of netBurn) {
    totalNetBurn += value;
  }
  const average = totalNetBurn / Number(state.duration);

  return average;
}

export function getRunwayRunRateValue(context: Context) {
  const totalCashBalance = getCashBalanceValue(context) as number;
  const averageNetBurn = getAvgNetBurnValue(context) as number;

  if (totalCashBalance < 0 || averageNetBurn < 0) {
    return `- {{Months}}`;
  }

  const months = Math.round(totalCashBalance / averageNetBurn) ?? 0;

  return months > 0 ? `${months} {{Months}}` : `- {{Months}}`;
}

export function onRevenueSpendAndProfitWidgetChange(
  context: Context,
  index: number
) {
  const { state } = context;
  state.revenueSpendAndProfitWidget = index === 1 ? 'quarter' : 'month';
}

export function graphLabels(context: Context, granularity: Granularity) {
  const { data, state } = context;

  const endDatePeriodIndex = data.postingPeriod?.findIndex((period) => {
    return period.periodName === state.dropdownListPeriod.periodName;
  });

  const startDatePeriodIndex =
    endDatePeriodIndex - (Number(state.duration) - 1);

  const startDatePeriod =
    data.postingPeriod?.[startDatePeriodIndex < 0 ? 0 : startDatePeriodIndex];

  const postingPeriodFiltered = data.postingPeriod?.filter((period) => {
    return (
      period.startDate >= startDatePeriod?.startDate &&
      period.endDate <= state.dropdownListPeriod?.endDate
    );
  });

  const labels =
    granularity === 'month'
      ? postingPeriodFiltered?.map((period) => period.periodName)
      : postingPeriodFiltered?.map((period) => period.fiscalQuarter);

  return [...new Set(labels)];
}

export function cashBalance(context: Context, granularity: Granularity) {
  const { data } = context;

  const BS =
    granularity === 'month' ? data.cashBalance : data.cashBalanceQuarter;

  const bankAccounts = BS?.rowData?.filter((account) => {
    return account.data['Account:accountSubType'] === EvstAccountSubType.Bank;
  });

  const columns = BS?.columnDefs?.map((columnDef) => {
    return columnDef.field;
  });

  const cashBalance = [];
  if (columns?.length > 0) {
    for (const [index, column] of columns.entries()) {
      cashBalance[index] = 0;
      if (bankAccounts?.length > 0) {
        for (const account of bankAccounts) {
          const columnValue = account.data[column] ?? 0;
          cashBalance[index] += Number(columnValue);
        }
      }
    }
  }

  return cashBalance;
}

export function cashBalanceData(context: Context, granularity: Granularity) {
  const cb = cashBalance(context, granularity);
  const labels = graphLabels(context, granularity);

  while (cb.length > labels.length) {
    cb.shift();
  }

  return cb;
}

export function netBurnData(context: Context, granularity: Granularity) {
  const cashBalanceForNetBurn = cashBalance(context, granularity);

  const netBurnData = [];

  for (const [index, value] of cashBalanceForNetBurn.entries()) {
    if (index > 0) {
      netBurnData.push(-(value - cashBalanceForNetBurn[index - 1]));
    }
  }

  return netBurnData;
}

export function revenueData(context: Context) {
  const { data } = context;

  const revenueAccounts = (data.incomeStatement?.rowData as Any[])?.filter(
    (account) => {
      return (
        account.data['Account:accountSubType'] === EvstAccountSubType.Income
      );
    }
  );

  const columns = (data.incomeStatement?.columnDefs as Any[])?.map(
    (columnDef) => {
      return columnDef.field;
    }
  );

  const revenueData = [];
  if (columns?.length > 0) {
    for (const [index, column] of columns.entries()) {
      revenueData[index] = 0;
      if (revenueAccounts?.length > 0) {
        for (const account of revenueAccounts) {
          const columnValue = account.data[column] ?? 0;
          revenueData[index] += Number(columnValue);
        }
      }
    }
  }

  return revenueData;
}

export function spendData(context: Context) {
  const { data } = context;

  const spendAccounts = data.incomeStatement?.rowData?.filter((account) =>
    EXPENSE_TYPES.has(account.data['Account:accountSubType'])
  );

  const columns = data.incomeStatement?.columnDefs?.map(
    (columnDef) => columnDef.field
  );

  const spendData = [];
  if (columns?.length > 0) {
    for (const [index, column] of columns.entries()) {
      spendData[index] = 0;
      if (spendAccounts?.length > 0) {
        for (const account of spendAccounts) {
          const columnValue = account.data[column] ?? 0;
          spendData[index] += Number(columnValue);
        }
      }
    }
  }

  return spendData;
}

export function profitData(context: Context) {
  const revenue = revenueData(context);
  const spend = spendData(context);

  const profitData = [];

  for (const [index, rev] of revenue.entries()) {
    profitData[index] = rev - spend[index];
  }

  return profitData;
}

export function getBreakdownChartData(
  { data }: Context,
  granularity: Granularity
) {
  return data.breakdownChart?.[granularity]?.data;
}

export function getBreakdownChartOptions() {
  return {
    plugins: {
      legend: {
        display: true,
        position: 'top',
        align: 'start',
        labels: {
          fontColor: '#000080',
          usePointStyle: true,
          boxWidth: 6,
          boxHeight: 6,
        },
      },
      datalabels: {
        display: false,
        labels: {
          value: true,
        },
        formatter: currencyFormatter,
        color: 'white',
        font: {
          weight: '500',
        },
      },
    },
    scales: {
      x: {
        stacked: true,
      },
      y: {
        stacked: true,
        ticks: {
          callback: function (value) {
            const asNumber = Number(value);
            if (Number.isNaN(asNumber)) {
              return '';
            }
            const abs = Math.abs(asNumber);
            const absText = abs < 1000 ? `${abs}` : `${abs / 1000}K`;
            if (asNumber < 0) {
              return `(${absText})`;
            }
            return absText;
          },
        },
      },
    },
  };
}

function currencyFormatter(context: Context, value: unknown) {
  return `$${formatCurrency(context.language, value)}`;
}

function formatCurrency(language: string, value: unknown) {
  return new Intl.NumberFormat(language, {
    maximumFractionDigits: 0,
  }).format(Number(value));
}

export function onHeadcountByMonthWidgetChange(
  context: Context,
  index: number
) {
  const { state } = context;
  state.headcountByMonthWidget = index === 1 ? 'department' : 'total';
}

/**
 * Aligns a dataset with a new set of labels (periods).
 * Required for datasets which don't retrieve null members for all periods in range.
 *
 * Returning all period members should ideally be handled by OLAP directly.
 */
function alignDatasetWithPeriods(
  chartData: {
    datasets: {
      data: unknown[];
      [key: string]: unknown;
    }[];
    labels: string[];
  },
  targetLabels: string[]
) {
  const { datasets, labels: originalLabels } = chartData;
  return datasets.map((dataset) => {
    const dataMap = new Map(
      dataset.data.map((value, index) => [originalLabels[index], value])
    );

    return {
      ...dataset,
      data: targetLabels.map((label) => dataMap.get(label) ?? null),
    };
  });
}

export function totalActualHeadcountData(context: Context) {
  const { data } = context;
  const totalHeadcount = data.totalHeadcount?.data;

  if (!totalHeadcount) {
    return { datasets: [], labels: [] };
  }

  const periodsInRange = graphLabels(context, 'month');

  return {
    datasets: totalHeadcount.datasets,
    labels: periodsInRange,
  };
}

export function getHeadcountByDepData(context: Context) {
  const { data } = context;
  const headcountByDepData = data.headcountByDepData?.data;

  if (!headcountByDepData) {
    return { datasets: [], labels: [] };
  }
  const periodsInRange = graphLabels(context, 'month');

  const { datasets } = headcountByDepData;
  const dataSetWithColor = datasets.map((item) => ({
    ...item,
    borderColor: item.backgroundColor,
  }));

  return {
    datasets: alignDatasetWithPeriods(
      { datasets: dataSetWithColor, labels: headcountByDepData.labels },
      periodsInRange
    ),
    labels: periodsInRange,
  };
}

export function onSpendByDepartmentWidgetChange(
  context: Context,
  index: number
) {
  const { state } = context;
  state.spendByDepartmentWidget = index === 1 ? 'quarter' : 'month';
}

export function spendByDepartmentDataset(context: Context) {
  const { data } = context;

  return data.spendByDepartment;
}

export function getCCBarChartOptions() {
  return {
    scales: {
      x: {
        stacked: true,
      },
      y: {
        stacked: true,
      },
    },
    plugins: {
      legend: {
        position: 'top',
        labels: {
          fontColor: '#000080',
          usePointStyle: true,
          boxWidth: 6,
          boxHeight: 6,
        },
      },
      datalabels: {
        labels: {
          value: false,
        },
        color: 'white',
        font: {
          weight: '500',
        },
      },
    },
  };
}
