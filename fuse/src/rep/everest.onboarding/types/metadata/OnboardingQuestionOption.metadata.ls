package everest.onboarding

node OnboardingQuestionOption {
	description: 'Onboarding Question Option'
	label: 'Onboarding Question Option'
	extendable
	transport-class: source-preserving-upsert
	deny-clean-business-data
	semantic-key: onboardingQuestionUUID, `description`, `value`

	field onboardingQuestionUUID UUID {
		required
		editable
		persisted
		label: 'Onboarding Question UUID'
	}

	field `description` Text {
		required
		editable
		persisted
		label: 'Description'
	}

	field `value` Text {
		required
		editable
		persisted
		label: 'Value'
	}

	field orderIndex Number<Int> {
		required
		editable
		persisted
		label: 'Order Index'
	}

	field isSelected TrueFalse {
		editable
		persisted
		label: 'Is Selected'
		description: 'Records if the option was selected'
		default: false
	}

	field routeToNextQuestion UUID {
		editable
		persisted
		label: 'Route To Next Question'
		description: 'When this answer is beeing choosen we reroute to question from this field'
	}

	generated association alias onboardingquestion for OnboardingQuestionOption-OnboardingQuestion

	generated association alias onboardingoptiontostepmapping for OnboardingOptionToStepMapping-OnboardingQuestionOption
}

association OnboardingQuestionOption-OnboardingQuestion {
	source: OnboardingQuestionOption
	sourceField: onboardingQuestionUUID
	target: OnboardingQuestion
	targetField: uuid
	kind: static
	type: reference
	multiplicity: many-to-one
	description: 'Association between OnboardingQuestionOption artifact and OnboardingQuestion'
}
