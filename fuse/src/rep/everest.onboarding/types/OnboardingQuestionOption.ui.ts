/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// ******************************************************************************
// Generated Everest Types
// ******************************************************************************

import type { Decimal, RoundingMode } from '@everestsystems/decimal';
import type {
  ArgType,
  Association,
  AssociationManagementArgs,
  AsyncResult,
  Controller,
  ControllerClientProvider,
  CreateOptionsArg,
  DataWithMetadata,
  DeleteOptionsArg,
  FieldSelector,
  Filter,
  flattenFieldSelector,
  GraphInstanceEntry,
  KernelRequestOptions,
  NonAssociationFields,
  Optional,
  PurgeArgs,
  ReadOptionsArg,
  ReadReturnType as ReadReturnTypeGeneric,
  ReadReturnTypeWithAssociations,
  RequireAtLeastOne,
  SemanticSearchArg,
  SemanticSearchRequestOptions,
  TypeSafeQueryArgType,
  UpdateManyOptionsArg,
  UpdateOptionsArg,
  UpsertOptionsArg,
  WriteReturnType,
  WriteReturnTypeWithAssociations,
} from '@everestsystems/content-core';

import { UIExecutionContext } from '@everestsystems/content-core';
import type { OnboardingQuestion as everest_onboarding_model_node_OnboardingQuestion } from "@pkg/everest.onboarding/types/OnboardingQuestion";
import type { OnboardingOptionToStepMapping as everest_onboarding_model_node_OnboardingOptionToStepMapping } from "@pkg/everest.onboarding/types/OnboardingOptionToStepMapping";
import type { OnboardingQuestionOption as everest_onboarding_model_node_OnboardingQuestionOption } from "@pkg/everest.onboarding/types/OnboardingQuestionOption";

/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */

type RequiredContext = {
  data: Record<string, any>;
  actions: {
    run: (...args: any[]) => any;
  };
};

type ApiError = {
  message: string;
  name: string;
  errorCode: string;
  stack?: string;
};

type InnerPromiseType<T> = T extends Promise<infer R> ? R : T;

type ActionResultOrError<T1 extends string | number | symbol, T2> = {[P in T1]?: T2} & {error?: ApiError};

type ArrayIfNeeded<C extends RequiredContext, K extends keyof C['data'], T> = C['data'][K] extends UIExecutionContext.UIModelNodeListType<any> ? T[] : T;

export namespace OnboardingQuestionOptionUI {
  /**
   * Onboarding Question Option
   */
  export type OnboardingQuestionOptionWithAssociation = OnboardingQuestionOption & {
    ["OnboardingQuestionOption-OnboardingQuestion"]?: Association<everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestionWithAssociation>;
    ["OnboardingOptionToStepMapping-OnboardingQuestionOption"]?: Association<everest_onboarding_model_node_OnboardingOptionToStepMapping.OnboardingOptionToStepMappingWithAssociation>[];
    };
  export interface IControllerClient extends everest_onboarding_model_node_OnboardingQuestionOption.IControllerClient {}

  export type OnboardingQuestionOption = everest_onboarding_model_node_OnboardingQuestionOption.OnboardingQuestionOption;
  export type SemanticKeyFields = Required<Pick<OnboardingQuestionOption, 'description' | 'onboardingQuestionUUID' | 'value'>>;
  export type CreationFields = Pick<OnboardingQuestionOption, 'uuid' | 'package' | 'externalId' | 'active' | 'description' | 'isSelected' | 'onboardingQuestionUUID' | 'orderIndex' | 'routeToNextQuestion' | 'value'>;
  export type UniqueFields = Pick<OnboardingQuestionOption, 'id' | 'uuid'>;
  export type UniqueWhereInput = (RequireAtLeastOne<UniqueFields> | SemanticKeyFields) & Partial<OnboardingQuestionOption>;
  export type ReadReturnType<U extends string | number | symbol = keyof OnboardingQuestionOption> = ReadReturnTypeGeneric<OnboardingQuestionOption, U>;

  class ActionRequest<C extends RequiredContext, FuncReturnType> {
    constructor(private readonly context: C, private readonly partialPayload: Record<string, any>) {
    }

    private async __run__(payload: any) {
      return this.context.actions.run(payload);
    }

    payload<K extends keyof C['data']>(nodeAlias: K): Record<string, any> {
      return {[nodeAlias]: this.partialPayload};
    }

    run<K extends keyof C['data']>(nodeAlias: K): Promise<ActionResultOrError<K, ArrayIfNeeded<C, K, InnerPromiseType<FuncReturnType>>>> {
      const payload = this.payload(nodeAlias);
      return this.__run__(payload);
    }
  }


  /** @deprecated - use {@link MODEL_URN} instead */
  export const name = 'urn:evst:everest:onboarding:model/node:OnboardingQuestionOption';
  export const MODEL_URN = 'urn:evst:everest:onboarding:model/node:OnboardingQuestionOption';
  /** @deprecated - use {@link MODEL_URN} instead */
  export const MODEL_ID = 'everest.onboarding/OnboardingQuestionOptionModel.OnboardingQuestionOption';
  export const MODEL_UUID = '82b3346e-3f39-422c-b3a8-ef78f989309b';

  /** @deprecated use ```client``` instead */
  export function create<C extends RequiredContext, U extends keyof OnboardingQuestionOption>(context: C, input: CreationFields, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<OnboardingQuestionOption, U>>> {
    const convertedArgs = {data: input, fieldList: fieldlist, options: options} as Record<string, any>
    const partialPayload = {action: 'create', ...convertedArgs}

    return new ActionRequest<C, Promise<WriteReturnType<OnboardingQuestionOption, U>>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function createMany<C extends RequiredContext, U extends keyof OnboardingQuestionOption>(context: C, input: CreationFields[], fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<OnboardingQuestionOption, U>[]>> {
    const convertedArgs = {data: input, fieldList: fieldlist, options: options} as Record<string, any>
    const partialPayload = {action: 'createMany', ...convertedArgs}

    return new ActionRequest<C, Promise<WriteReturnType<OnboardingQuestionOption, U>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function Delete<C extends RequiredContext>(context: C, where: UniqueWhereInput, options?: undefined): ActionRequest<C, Promise<Partial<OnboardingQuestionOption>[]>> {
    const convertedArgs = {where: where, options} as Record<string, any>
    const partialPayload = {action: 'delete', ...convertedArgs}

    return new ActionRequest<C, Promise<Partial<OnboardingQuestionOption>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function deleteAll<C extends RequiredContext>(context: C): ActionRequest<C, Promise<Partial<OnboardingQuestionOption>[]>> {
    const convertedArgs = {data: {}} as Record<string, any>
    const partialPayload = {action: 'deleteAll', ...convertedArgs}

    return new ActionRequest<C, Promise<Partial<OnboardingQuestionOption>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function deleteMany<C extends RequiredContext>(context: C, where: Filter<OnboardingQuestionOptionWithAssociation>): ActionRequest<C, Promise<Partial<OnboardingQuestionOption>[]>>;
  /** @deprecated use ```client``` instead */
  export function deleteMany<C extends RequiredContext>(context: C, where: Partial<OnboardingQuestionOption>): ActionRequest<C, Promise<Partial<OnboardingQuestionOption>[]>>;
  /** @deprecated use ```client``` instead */
  export function deleteMany<C extends RequiredContext>(context: C, where: Filter<OnboardingQuestionOption> | Partial<OnboardingQuestionOption>, options?: undefined): ActionRequest<C, Promise<Partial<OnboardingQuestionOption>[]>> {
    const convertedArgs = {data: {where: where}, options} as Record<string, any>
    const partialPayload = {action: 'deleteMany', ...convertedArgs}

    return new ActionRequest<C, Promise<Partial<OnboardingQuestionOption>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function query<C extends RequiredContext, U extends FieldSelector<OnboardingQuestionOptionWithAssociation>>(context: C, args: Omit<TypeSafeQueryArgType<OnboardingQuestionOptionWithAssociation>, 'draft'>, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<ReadReturnTypeWithAssociations<OnboardingQuestionOptionWithAssociation, U>[]>>;
  /** @deprecated use ```client``` instead */
  export function query<C extends RequiredContext, U extends keyof OnboardingQuestionOption, V extends string = 'ALL_FIELDS'>(context: C, args: Omit<TypeSafeQueryArgType<OnboardingQuestionOption>, 'draft'> | Omit<Omit<TypeSafeQueryArgType<OnboardingQuestionOption>, 'draft'>, 'where'> & { where?: Partial<OnboardingQuestionOption> }, fieldlist: ReadonlyArray<U | V> | 'ALL_FIELDS'): ActionRequest<C, Promise<ReadReturnType<V>[]>>;
  /** @deprecated use ```client``` instead */
  export function query<C extends RequiredContext>(context: C, args: Omit<TypeSafeQueryArgType<OnboardingQuestionOption>, 'draft'>, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS'): ActionRequest<C, Promise<unknown[]>> {
    const convertedArgs = {data: {...args, fieldList: fieldlist}} as Record<string, any>
    const partialPayload = {action: 'query', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function read<C extends RequiredContext, U extends FieldSelector<OnboardingQuestionOptionWithAssociation>>(context: C, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): ActionRequest<C, Promise<ReadReturnTypeWithAssociations<OnboardingQuestionOptionWithAssociation, U>>>;
  /** @deprecated use ```client``` instead */
  export function read<C extends RequiredContext, U extends keyof OnboardingQuestionOption>(context: C, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): ActionRequest<C, Promise<ReadReturnType<U>>>;
  /** @deprecated use ```client``` instead */
  export function read<C extends RequiredContext>(context: C, where: UniqueWhereInput, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): ActionRequest<C, Promise<unknown>> {
    const convertedArgs = {data: {where: where, fieldList: fieldlist}, options: options} as Record<string, any>
    const partialPayload = {action: 'read', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function update<C extends RequiredContext, U extends FieldSelector<OnboardingQuestionOptionWithAssociation>>(context: C, where: UniqueWhereInput, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<WriteReturnTypeWithAssociations<OnboardingQuestionOptionWithAssociation, U>>>;
  /** @deprecated use ```client``` instead */
  export function update<C extends RequiredContext, U extends keyof OnboardingQuestionOption>(context: C, where: Partial<OnboardingQuestionOption>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<WriteReturnType<OnboardingQuestionOption, U>>>;
  /** @deprecated use ```client``` instead */
  export function update<C extends RequiredContext>(context: C, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: undefined): ActionRequest<C, Promise<unknown>> {
    const convertedArgs = {where: where, data: data, fieldList: fieldlist, options} as Record<string, any>
    const partialPayload = {action: 'update', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function updateMany<C extends RequiredContext, U extends FieldSelector<OnboardingQuestionOptionWithAssociation>>(context: C, where: Filter<OnboardingQuestionOptionWithAssociation>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<WriteReturnTypeWithAssociations<OnboardingQuestionOptionWithAssociation, U>[]>>;
  /** @deprecated use ```client``` instead */
  export function updateMany<C extends RequiredContext, U extends keyof OnboardingQuestionOption>(context: C, where: Partial<OnboardingQuestionOption>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<WriteReturnType<OnboardingQuestionOption, U>[]>>;
  /** @deprecated use ```client``` instead */
  export function updateMany<C extends RequiredContext>(context: C, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: undefined): ActionRequest<C, Promise<unknown[]>> {
    const convertedArgs = {data: {where: where, data: data, fieldList: fieldlist}, options} as Record<string, any>
    const partialPayload = {action: 'updateMany', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function upsert<C extends RequiredContext, U extends keyof OnboardingQuestionOption & string>(context: C, data: Partial<OnboardingQuestionOption>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<OnboardingQuestionOption, U>>>;
  /** @deprecated use ```client``` instead */
  export function upsert<C extends RequiredContext, U extends keyof OnboardingQuestionOption & string>(context: C, where: Partial<OnboardingQuestionOption>, data: Partial<OnboardingQuestionOption>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<OnboardingQuestionOption, U>>>;
  /** @deprecated use ```client``` instead */
  export function upsert<C extends RequiredContext, U extends keyof OnboardingQuestionOption & string>(context: C, whereOrData: Partial<OnboardingQuestionOption>, dataOrFieldList?: Partial<OnboardingQuestionOption> | ReadonlyArray<U> | 'ALL_FIELDS', fieldlistOrOptions?: ReadonlyArray<U> | 'ALL_FIELDS' | Omit<UpsertOptionsArg, 'draft'>, maybeOptions?: Omit<UpsertOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<OnboardingQuestionOption, U>>> {
    const convertedArgs = {data: {where: whereOrData, data: dataOrFieldList, fieldList: fieldlistOrOptions}, options: maybeOptions} as Record<string, any>
    const partialPayload = {action: 'upsert', ...convertedArgs}

    return new ActionRequest<C, Promise<WriteReturnType<OnboardingQuestionOption, U>>>(context, partialPayload);
  }
  export namespace client {

    /** write a new object to the database. */
    export declare function create<U extends keyof OnboardingQuestionOption>(input: CreationFields, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): Promise<WriteReturnType<OnboardingQuestionOption, U>>

    /** write new objects to the database. */
    export declare function createMany<U extends keyof OnboardingQuestionOption>(input: CreationFields[], fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): Promise<WriteReturnType<OnboardingQuestionOption, U>[]>

    /** delete the object that is uniquely identified by the where condition. */
    export declare function Delete(where: UniqueWhereInput, options?: undefined): Promise<Partial<OnboardingQuestionOption>[]>

    /** delete all objects. */
    export declare function deleteAll(): Promise<Partial<OnboardingQuestionOption>[]>

    /** delete the objects that are identified by the where condition. */
    export declare function deleteMany(where: Filter<OnboardingQuestionOptionWithAssociation>): Promise<Partial<OnboardingQuestionOption>[]>;
    /** @deprecated use the alternative overloaded function */
    export declare function deleteMany(where: Partial<OnboardingQuestionOption>): Promise<Partial<OnboardingQuestionOption>[]>;
    export declare function deleteMany(where: Filter<OnboardingQuestionOption> | Partial<OnboardingQuestionOption>, options?: undefined): Promise<Partial<OnboardingQuestionOption>[]>

    /** returns a list of all objects matching the requirements. */
    export declare function query<U extends FieldSelector<OnboardingQuestionOptionWithAssociation>>(args: Omit<TypeSafeQueryArgType<OnboardingQuestionOptionWithAssociation>, 'draft'>, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<ReadReturnTypeWithAssociations<OnboardingQuestionOptionWithAssociation, U>[]>;
    /** @deprecated Use the alternative overloaded method for querying associations. */
    export declare function query<U extends keyof OnboardingQuestionOption, V extends string = 'ALL_FIELDS'>(args: Omit<TypeSafeQueryArgType<OnboardingQuestionOption>, 'draft'> | Omit<Omit<TypeSafeQueryArgType<OnboardingQuestionOption>, 'draft'>, 'where'> & { where?: Partial<OnboardingQuestionOption> }, fieldlist: ReadonlyArray<U | V> | 'ALL_FIELDS'): Promise<ReadReturnType<V>[]>;
    export declare function query(args: Omit<TypeSafeQueryArgType<OnboardingQuestionOption>, 'draft'>, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown[]>

    /** reads the object that is uniquely identified by the where condition. */
    export declare function read<U extends FieldSelector<OnboardingQuestionOptionWithAssociation>>(where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnTypeWithAssociations<OnboardingQuestionOptionWithAssociation, U>>;
    /** @deprecated Use the alternative overloaded method for reading associations. */
    export declare function read<U extends keyof OnboardingQuestionOption>(where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnType<U>>;
    export declare function read(where: UniqueWhereInput, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<unknown>

    /** writes modified values back to the database for a given node instance. */
    export declare function update<U extends FieldSelector<OnboardingQuestionOptionWithAssociation>>(where: UniqueWhereInput, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnTypeWithAssociations<OnboardingQuestionOptionWithAssociation, U>>;
    /** @deprecated Use the alternative overloaded method for writing with associations. */
    export declare function update<U extends keyof OnboardingQuestionOption>(where: Partial<OnboardingQuestionOption>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnType<OnboardingQuestionOption, U>>;
    export declare function update(where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: undefined): Promise<unknown>

    /** writes modified values back to the database for selected node instances. */
    export declare function updateMany<U extends FieldSelector<OnboardingQuestionOptionWithAssociation>>(where: Filter<OnboardingQuestionOptionWithAssociation>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnTypeWithAssociations<OnboardingQuestionOptionWithAssociation, U>[]>;
    /** @deprecated Use the alternative overloaded method for writing with associations. */
    export declare function updateMany<U extends keyof OnboardingQuestionOption>(where: Partial<OnboardingQuestionOption>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnType<OnboardingQuestionOption, U>[]>;
    export declare function updateMany(where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: undefined): Promise<unknown[]>

    /** creates or updates object identified by where. */
    export declare function upsert<U extends keyof OnboardingQuestionOption & string>(data: Partial<OnboardingQuestionOption>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<OnboardingQuestionOption, U>>;
    /** @deprecated use the overload without an explicit where clause */
    export declare function upsert<U extends keyof OnboardingQuestionOption & string>(where: Partial<OnboardingQuestionOption>, data: Partial<OnboardingQuestionOption>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<OnboardingQuestionOption, U>>;
    export declare function upsert<U extends keyof OnboardingQuestionOption & string>(whereOrData: Partial<OnboardingQuestionOption>, dataOrFieldList?: Partial<OnboardingQuestionOption> | ReadonlyArray<U> | 'ALL_FIELDS', fieldlistOrOptions?: ReadonlyArray<U> | 'ALL_FIELDS' | Omit<UpsertOptionsArg, 'draft'>, maybeOptions?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<OnboardingQuestionOption, U>>
  }
}
