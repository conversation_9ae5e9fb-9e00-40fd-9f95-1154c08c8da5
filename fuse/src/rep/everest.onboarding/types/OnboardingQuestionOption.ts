/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// ******************************************************************************
// Generated Everest Types
// ******************************************************************************

import type { Decimal, RoundingMode } from '@everestsystems/decimal';
import type {
  ArgType,
  Association,
  AssociationManagementArgs,
  AsyncResult,
  Controller,
  ControllerClientProvider,
  CreateOptionsArg,
  DataWithMetadata,
  DeleteOptionsArg,
  FieldSelector,
  Filter,
  flattenFieldSelector,
  GraphInstanceEntry,
  KernelRequestOptions,
  NonAssociationFields,
  Optional,
  PurgeArgs,
  ReadOptionsArg,
  ReadReturnType as ReadReturnTypeGeneric,
  ReadReturnTypeWithAssociations,
  RequireAtLeastOne,
  SemanticSearchArg,
  SemanticSearchRequestOptions,
  TypeSafeQueryArgType,
  UpdateManyOptionsArg,
  UpdateOptionsArg,
  UpsertOptionsArg,
  WriteReturnType,
  WriteReturnTypeWithAssociations,
} from '@everestsystems/content-core';
import type { EvstUUID as everest_appserver_primitive_UUID } from "@pkg/everest.appserver/types/primitives/UUID";
import type { EvstPackageName as everest_appserver_primitive_metadata_PackageName } from "@pkg/everest.appserver/types/primitives/metadata/PackageName";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { EvstUserEmail as everest_appserver_usermgmt_primitive_user_UserEmail } from "@pkg/everest.appserver.usermgmt/types/primitives/user/UserEmail";
import type { EvstDateTime as everest_appserver_primitive_DateTime } from "@pkg/everest.appserver/types/primitives/DateTime";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { OnboardingQuestion as everest_onboarding_model_node_OnboardingQuestion } from "@pkg/everest.onboarding/types/OnboardingQuestion";
import type { OnboardingOptionToStepMapping as everest_onboarding_model_node_OnboardingOptionToStepMapping } from "@pkg/everest.onboarding/types/OnboardingOptionToStepMapping";

/**
 * Types for OnboardingQuestionOption
 *
 * @everest-node-metadata='{"visibility":"public"}'
 */
export namespace OnboardingQuestionOption {
  export type SemanticKeyFields = Required<Pick<OnboardingQuestionOption, 'description' | 'onboardingQuestionUUID' | 'value'>>;
  export type CreationFields = Pick<OnboardingQuestionOption, 'uuid' | 'package' | 'externalId' | 'active' | 'description' | 'isSelected' | 'onboardingQuestionUUID' | 'orderIndex' | 'routeToNextQuestion' | 'value'>;
  export type UniqueFields = Pick<OnboardingQuestionOption, 'id' | 'uuid'>;
  export type UniqueWhereInput = (RequireAtLeastOne<UniqueFields> | SemanticKeyFields) & Partial<OnboardingQuestionOption>;
  export type ReadReturnType<U extends string | number | symbol = keyof OnboardingQuestionOption> = ReadReturnTypeGeneric<OnboardingQuestionOption, U>;

  export interface IControllerClient extends Omit<Controller<OnboardingQuestionOption>, 'all' | 'getHistory' | 'lookup' | 'purge' | 'queryWithMetadata' | 'semanticSearch' | 'upsertMany'> {}

  /**
   * Onboarding Question Option
   */
  export type OnboardingQuestionOption = {
    /** Database generated identifier of this record. */
    id: number;
    /** Database generated version of this record. */
    version?: number;
    uuid?: everest_appserver_primitive_UUID | null;
    /**
     * Indicate the package that owns the data.
     */
    package: everest_appserver_primitive_metadata_PackageName;
    /**
     * ID of the system from which this record was sourced
     */
    externalId?: everest_appserver_primitive_Text | null;
    /**
     * Indicates whether this record is usable for business users
     */
    active?: everest_appserver_primitive_TrueFalse | null;
    /**
     * User that created this record
     */
    createdBy: everest_appserver_usermgmt_primitive_user_UserEmail;
    /**
     * Date/time when the record was created
     */
    createdDate: everest_appserver_primitive_DateTime;
    /**
     * User that last modified the record
     */
    lastModifiedBy: everest_appserver_usermgmt_primitive_user_UserEmail;
    /**
     * Date/time of the last modification to the record
     */
    lastModifiedDate: everest_appserver_primitive_DateTime;
    description: everest_appserver_primitive_Text;
    /**
     * Records if the option was selected
     */
    isSelected?: everest_appserver_primitive_TrueFalse | null;
    onboardingQuestionUUID: everest_appserver_primitive_UUID;
    orderIndex: everest_appserver_primitive_Number;
    /**
     * When this answer is beeing choosen we reroute to question from this field
     */
    routeToNextQuestion?: everest_appserver_primitive_UUID | null;
    value: everest_appserver_primitive_Text;
    };
  /**
   * Onboarding Question Option
   */
  export type OnboardingQuestionOptionWithAssociation = OnboardingQuestionOption & {
    ["OnboardingQuestionOption-OnboardingQuestion"]?: Association<everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestionWithAssociation>;
    ["OnboardingOptionToStepMapping-OnboardingQuestionOption"]?: Association<everest_onboarding_model_node_OnboardingOptionToStepMapping.OnboardingOptionToStepMappingWithAssociation>[];
    };
  /** @deprecated - use {@link MODEL_URN} instead */
  export const name = 'urn:evst:everest:onboarding:model/node:OnboardingQuestionOption';
  export const MODEL_URN = 'urn:evst:everest:onboarding:model/node:OnboardingQuestionOption';
  /** @deprecated - use {@link MODEL_URN} instead */
  export const MODEL_ID = 'everest.onboarding/OnboardingQuestionOptionModel.OnboardingQuestionOption';
  export const MODEL_UUID = '82b3346e-3f39-422c-b3a8-ef78f989309b';

  /** @return a model controller instance for OnboardingQuestionOption. */
  export function client(env: ControllerClientProvider) {
    return env.controllerClient<OnboardingQuestionOption.IControllerClient>(MODEL_URN);
  }

  /** write a new object to the database. */
  export async function create<U extends keyof OnboardingQuestionOption>(env: ControllerClientProvider, input: CreationFields, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): Promise<WriteReturnType<OnboardingQuestionOption, U>> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).create(input, fields, options);
  }

  /** write new objects to the database. */
  export async function createMany<U extends keyof OnboardingQuestionOption>(env: ControllerClientProvider, input: CreationFields[], fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): Promise<WriteReturnType<OnboardingQuestionOption, U>[]> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).createMany(input, fields, options);
  }

  /** delete the object that is uniquely identified by the where condition. */
  export async function Delete(env: ControllerClientProvider, where: UniqueWhereInput): Promise<Partial<OnboardingQuestionOption>[]> {
    return (await client(env)).delete(where);
  }

  /** delete all objects. */
  export async function deleteAll(env: ControllerClientProvider): Promise<Partial<OnboardingQuestionOption>[]> {
    return (await client(env)).deleteAll();
  }

  /** delete the objects that are identified by the where condition. */
  export async function deleteMany(env: ControllerClientProvider, where: Filter<OnboardingQuestionOptionWithAssociation>): Promise<Partial<OnboardingQuestionOption>[]>;
  /** @deprecated use the alternative overloaded function */
  export async function deleteMany(env: ControllerClientProvider, where: Partial<OnboardingQuestionOption>): Promise<Partial<OnboardingQuestionOption>[]>;
  export async function deleteMany(env: ControllerClientProvider, where: Filter<OnboardingQuestionOption> | Partial<OnboardingQuestionOption>): Promise<Partial<OnboardingQuestionOption>[]> {
    return (await client(env)).deleteMany(where as Filter<OnboardingQuestionOption>);
  }

  /** returns a list of all objects matching the requirements. */
  export async function query<U extends FieldSelector<OnboardingQuestionOptionWithAssociation>>(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<OnboardingQuestionOptionWithAssociation>, 'draft'>, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<ReadReturnTypeWithAssociations<OnboardingQuestionOptionWithAssociation, U>[]>;
  /** @deprecated Use the alternative overloaded method for querying associations. */
  export async function query<U extends keyof OnboardingQuestionOption, V extends string = 'ALL_FIELDS'>(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<OnboardingQuestionOption>, 'draft'> | Omit<Omit<TypeSafeQueryArgType<OnboardingQuestionOption>, 'draft'>, 'where'> & { where?: Partial<OnboardingQuestionOption> }, fieldlist: ReadonlyArray<U | V> | 'ALL_FIELDS'): Promise<ReadReturnType<V>[]>;
  export async function query(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<OnboardingQuestionOption>, 'draft'>, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown[]> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).query(args, fields as ReadonlyArray<string>);
  }

  /** reads the object that is uniquely identified by the where condition. */
  export async function read<U extends FieldSelector<OnboardingQuestionOptionWithAssociation>>(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnTypeWithAssociations<OnboardingQuestionOptionWithAssociation, U>>;
  /** @deprecated Use the alternative overloaded method for reading associations. */
  export async function read<U extends keyof OnboardingQuestionOption>(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnType<U>>;
  export async function read(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<unknown> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).read(where, fields as ReadonlyArray<keyof OnboardingQuestionOption>, options);
  }

  /** writes modified values back to the database for a given node instance. */
  export async function update<U extends FieldSelector<OnboardingQuestionOptionWithAssociation>>(env: ControllerClientProvider, where: UniqueWhereInput, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnTypeWithAssociations<OnboardingQuestionOptionWithAssociation, U>>;
  /** @deprecated Use the alternative overloaded method for writing with associations. */
  export async function update<U extends keyof OnboardingQuestionOption>(env: ControllerClientProvider, where: Partial<OnboardingQuestionOption>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnType<OnboardingQuestionOption, U>>;
  export async function update(env: ControllerClientProvider, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).update(where, data, fields as ReadonlyArray<keyof OnboardingQuestionOption>);
  }

  /** writes modified values back to the database for selected node instances. */
  export async function updateMany<U extends FieldSelector<OnboardingQuestionOptionWithAssociation>>(env: ControllerClientProvider, where: Filter<OnboardingQuestionOptionWithAssociation>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnTypeWithAssociations<OnboardingQuestionOptionWithAssociation, U>[]>;
  /** @deprecated Use the alternative overloaded method for writing with associations. */
  export async function updateMany<U extends keyof OnboardingQuestionOption>(env: ControllerClientProvider, where: Partial<OnboardingQuestionOption>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnType<OnboardingQuestionOption, U>[]>;
  export async function updateMany(env: ControllerClientProvider, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown[]> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).updateMany(where, data, fields as ReadonlyArray<keyof OnboardingQuestionOption>);
  }

  /** creates or updates object identified by where. */
  export async function upsert<U extends keyof OnboardingQuestionOption & string>(env: ControllerClientProvider, data: Partial<OnboardingQuestionOption>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<OnboardingQuestionOption, U>>;
  /** @deprecated use the overload without an explicit where clause */
  export async function upsert<U extends keyof OnboardingQuestionOption & string>(env: ControllerClientProvider, where: Partial<OnboardingQuestionOption>, data: Partial<OnboardingQuestionOption>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<OnboardingQuestionOption, U>>;
  export async function upsert<U extends keyof OnboardingQuestionOption & string>(env: ControllerClientProvider, whereOrData: Partial<OnboardingQuestionOption>, dataOrFieldList?: Partial<OnboardingQuestionOption> | ReadonlyArray<U> | 'ALL_FIELDS', fieldlistOrOptions?: ReadonlyArray<U> | 'ALL_FIELDS' | Omit<UpsertOptionsArg, 'draft'>, maybeOptions?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<OnboardingQuestionOption, U>> {
    return (await client(env)).upsert(whereOrData, dataOrFieldList, fieldlistOrOptions, maybeOptions);
  }
}
