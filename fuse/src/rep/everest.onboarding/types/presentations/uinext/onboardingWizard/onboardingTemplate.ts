/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

import type { OnboardingQuestion as everest_onboarding_model_node_OnboardingQuestion } from "@pkg/everest.onboarding/types/OnboardingQuestion";
import type { OnboardingQuestionOption as everest_onboarding_model_node_OnboardingQuestionOption } from "@pkg/everest.onboarding/types/OnboardingQuestionOption";
import type { OnboardingStep as everest_onboarding_model_node_OnboardingStep } from "@pkg/everest.onboarding/types/OnboardingStep";
import type { OnboardingStepCategory as everest_onboarding_model_node_OnboardingStepCategory } from "@pkg/everest.onboarding/types/OnboardingStepCategory";
import type { OnboardingOptionToStepMapping as everest_onboarding_model_node_OnboardingOptionToStepMapping } from "@pkg/everest.onboarding/types/OnboardingOptionToStepMapping";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { EvstDecimal as everest_appserver_primitive_Decimal } from "@pkg/everest.appserver/types/primitives/Decimal";

import type {
  Filter,
  PresentationSession,
} from '@everestsystems/content-core';

/**
 * Generated backend types for OData presentation onboardingTemplate.
 */
export namespace onboardingTemplate {
  export namespace EntitySets {
    export namespace Questions {
      export namespace Query {
        export type Instance = {
          id?: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["id"] | undefined;
          uuid?: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["uuid"] | undefined;
          text?: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["text"] | undefined;
          title?: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["title"] | undefined;
          orderIndex?: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["orderIndex"] | undefined;
        };

        export type Order = {
          field: keyof Instance;
          ordering: 'asc' | 'desc';
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            where: Filter<Instance>;
            orderBy: ReadonlyArray<Order>;
            skip: number | undefined;
            take: number | undefined;
            fieldList: ReadonlyArray<keyof Instance>;
            count: boolean;
          };

          export type Response = {
            instances: Instance[];

            /**
             * If count was set to true on the request object, this property
             * must be set, otherwise it must evaluate to undefined.
             */
            count?: number | undefined;
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export type Implementation = {
        query: Query.Implementation;
      };
    }
    export namespace QuestionOptions {
      export namespace Query {
        export type Instance = {
          id?: everest_onboarding_model_node_OnboardingQuestionOption.OnboardingQuestionOption["id"] | undefined;
          uuid?: everest_onboarding_model_node_OnboardingQuestionOption.OnboardingQuestionOption["uuid"] | undefined;
          description?: everest_onboarding_model_node_OnboardingQuestionOption.OnboardingQuestionOption["description"] | undefined;
          value?: everest_onboarding_model_node_OnboardingQuestionOption.OnboardingQuestionOption["value"] | undefined;
          orderIndex?: everest_onboarding_model_node_OnboardingQuestionOption.OnboardingQuestionOption["orderIndex"] | undefined;
          isSelected?: everest_onboarding_model_node_OnboardingQuestionOption.OnboardingQuestionOption["isSelected"] | undefined;
          onboardingQuestionUUID?: everest_onboarding_model_node_OnboardingQuestionOption.OnboardingQuestionOption["onboardingQuestionUUID"] | undefined;
          routeToNextQuestion?: everest_onboarding_model_node_OnboardingQuestionOption.OnboardingQuestionOption["routeToNextQuestion"] | undefined;
        };

        export type Order = {
          field: keyof Instance;
          ordering: 'asc' | 'desc';
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            where: Filter<Instance>;
            orderBy: ReadonlyArray<Order>;
            skip: number | undefined;
            take: number | undefined;
            fieldList: ReadonlyArray<keyof Instance>;
            count: boolean;
          };

          export type Response = {
            instances: Instance[];

            /**
             * If count was set to true on the request object, this property
             * must be set, otherwise it must evaluate to undefined.
             */
            count?: number | undefined;
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export type Implementation = {
        query: Query.Implementation;
      };
    }
    export namespace Steps {
      export namespace Query {
        export type Instance = {
          id?: everest_onboarding_model_node_OnboardingStep.OnboardingStep["id"] | undefined;
          uuid?: everest_onboarding_model_node_OnboardingStep.OnboardingStep["uuid"] | undefined;
          title?: everest_onboarding_model_node_OnboardingStep.OnboardingStep["title"] | undefined;
          orderIndex?: everest_onboarding_model_node_OnboardingStep.OnboardingStep["orderIndex"] | undefined;
          status?: everest_onboarding_model_node_OnboardingStep.OnboardingStep["status"] | undefined;
          finishedAt?: everest_onboarding_model_node_OnboardingStep.OnboardingStep["finishedAt"] | undefined;
          note?: everest_onboarding_model_node_OnboardingStep.OnboardingStep["note"] | undefined;
          primaryLink?: everest_onboarding_model_node_OnboardingStep.OnboardingStep["primaryLink"] | undefined;
          secondaryLink?: everest_onboarding_model_node_OnboardingStep.OnboardingStep["secondaryLink"] | undefined;
          modelURN?: everest_onboarding_model_node_OnboardingStep.OnboardingStep["modelURN"] | undefined;
          type?: everest_onboarding_model_node_OnboardingStep.OnboardingStep["type"] | undefined;
          categoryUUID?: everest_onboarding_model_node_OnboardingStep.OnboardingStep["categoryUUID"] | undefined;
          isAlwaysVisible?: everest_onboarding_model_node_OnboardingStep.OnboardingStep["isAlwaysVisible"] | undefined;
        };

        export type Order = {
          field: keyof Instance;
          ordering: 'asc' | 'desc';
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            where: Filter<Instance>;
            orderBy: ReadonlyArray<Order>;
            skip: number | undefined;
            take: number | undefined;
            fieldList: ReadonlyArray<keyof Instance>;
            count: boolean;
          };

          export type Response = {
            instances: Instance[];

            /**
             * If count was set to true on the request object, this property
             * must be set, otherwise it must evaluate to undefined.
             */
            count?: number | undefined;
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export type Implementation = {
        query: Query.Implementation;
      };
    }
    export namespace StepCategories {
      export namespace Query {
        export type Instance = {
          id?: everest_onboarding_model_node_OnboardingStepCategory.OnboardingStepCategory["id"] | undefined;
          uuid?: everest_onboarding_model_node_OnboardingStepCategory.OnboardingStepCategory["uuid"] | undefined;
          title?: everest_onboarding_model_node_OnboardingStepCategory.OnboardingStepCategory["title"] | undefined;
          orderIndex?: everest_onboarding_model_node_OnboardingStepCategory.OnboardingStepCategory["orderIndex"] | undefined;
        };

        export type Order = {
          field: keyof Instance;
          ordering: 'asc' | 'desc';
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            where: Filter<Instance>;
            orderBy: ReadonlyArray<Order>;
            skip: number | undefined;
            take: number | undefined;
            fieldList: ReadonlyArray<keyof Instance>;
            count: boolean;
          };

          export type Response = {
            instances: Instance[];

            /**
             * If count was set to true on the request object, this property
             * must be set, otherwise it must evaluate to undefined.
             */
            count?: number | undefined;
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export type Implementation = {
        query: Query.Implementation;
      };
    }
    export namespace OptionToStepMappings {
      export namespace Query {
        export type Instance = {
          id?: everest_onboarding_model_node_OnboardingOptionToStepMapping.OnboardingOptionToStepMapping["id"] | undefined;
          uuid?: everest_onboarding_model_node_OnboardingOptionToStepMapping.OnboardingOptionToStepMapping["uuid"] | undefined;
          onboardingOptionUUID?: everest_onboarding_model_node_OnboardingOptionToStepMapping.OnboardingOptionToStepMapping["onboardingOptionUUID"] | undefined;
          onboardinStepUUID?: everest_onboarding_model_node_OnboardingOptionToStepMapping.OnboardingOptionToStepMapping["onboardinStepUUID"] | undefined;
        };

        export type Order = {
          field: keyof Instance;
          ordering: 'asc' | 'desc';
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            where: Filter<Instance>;
            orderBy: ReadonlyArray<Order>;
            skip: number | undefined;
            take: number | undefined;
            fieldList: ReadonlyArray<keyof Instance>;
            count: boolean;
          };

          export type Response = {
            instances: Instance[];

            /**
             * If count was set to true on the request object, this property
             * must be set, otherwise it must evaluate to undefined.
             */
            count?: number | undefined;
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export type Implementation = {
        query: Query.Implementation;
      };
    }
  }

  export namespace Actions {
    export namespace InitializeWizard {
      export type Input = Record<string, never>;

      export type Output = {
        firstQuestion: {
          id: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["id"];
          uuid: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["uuid"];
          text: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["text"];
          title: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["title"];
          orderIndex: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["orderIndex"];
        };
        totalQuestions: everest_appserver_primitive_Number;
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace GetQuestionWithOptions {
      export type Input = {
        questionUuid: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["uuid"];
      };

      export type Output = {
        question: {
          id: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["id"];
          uuid: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["uuid"];
          text: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["text"];
          title: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["title"];
          orderIndex: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["orderIndex"];
        };
        options: {
          id: everest_onboarding_model_node_OnboardingQuestionOption.OnboardingQuestionOption["id"];
          uuid: everest_onboarding_model_node_OnboardingQuestionOption.OnboardingQuestionOption["uuid"];
          description: everest_onboarding_model_node_OnboardingQuestionOption.OnboardingQuestionOption["description"];
          value: everest_onboarding_model_node_OnboardingQuestionOption.OnboardingQuestionOption["value"];
          orderIndex: everest_onboarding_model_node_OnboardingQuestionOption.OnboardingQuestionOption["orderIndex"];
          routeToNextQuestion: everest_onboarding_model_node_OnboardingQuestionOption.OnboardingQuestionOption["routeToNextQuestion"];
        }[];
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace SaveQuestionResponse {
      export type Input = {
        questionUuid: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["uuid"];
        selectedOptionUuids: everest_onboarding_model_node_OnboardingQuestionOption.OnboardingQuestionOption["uuid"][];
        selectedOptionValues: everest_appserver_primitive_Text[];
      };

      export type Output = {
        success: everest_appserver_primitive_TrueFalse;
        message: everest_appserver_primitive_Text;
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace GetNextQuestionByRouting {
      export type Input = {
        currentQuestionUuid: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["uuid"];
        selectedOptionUuids: everest_onboarding_model_node_OnboardingQuestionOption.OnboardingQuestionOption["uuid"][];
      };

      export type Output = {
        nextQuestion: {
          id: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["id"];
          uuid: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["uuid"];
          text: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["text"];
          title: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["title"];
          orderIndex: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["orderIndex"];
        };
        hasNext: everest_appserver_primitive_TrueFalse;
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace GetPreviousQuestion {
      export type Input = {
        currentQuestionUuid: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["uuid"];
      };

      export type Output = {
        previousQuestion: {
          id: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["id"];
          uuid: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["uuid"];
          text: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["text"];
          title: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["title"];
          orderIndex: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["orderIndex"];
        };
        hasPrevious: everest_appserver_primitive_TrueFalse;
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace GetWizardProgress {
      export type Input = Record<string, never>;

      export type Output = {
        currentStep: everest_appserver_primitive_Number;
        totalSteps: everest_appserver_primitive_Number;
        completedQuestions: {
          questionUuid: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["uuid"];
          selectedOptions: everest_appserver_primitive_Text[];
        }[];
        progressPercentage: everest_appserver_primitive_Decimal;
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace GetAvailableSteps {
      export type Input = {
        completedResponses: {
          questionUuid: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["uuid"];
          selectedOptionUuids: everest_onboarding_model_node_OnboardingQuestionOption.OnboardingQuestionOption["uuid"][];
        }[];
      };

      export type Output = {
        availableSteps: {
          id: everest_onboarding_model_node_OnboardingStep.OnboardingStep["id"];
          uuid: everest_onboarding_model_node_OnboardingStep.OnboardingStep["uuid"];
          title: everest_onboarding_model_node_OnboardingStep.OnboardingStep["title"];
          orderIndex: everest_onboarding_model_node_OnboardingStep.OnboardingStep["orderIndex"];
          status: everest_onboarding_model_node_OnboardingStep.OnboardingStep["status"];
          primaryLink: everest_onboarding_model_node_OnboardingStep.OnboardingStep["primaryLink"];
          type: everest_onboarding_model_node_OnboardingStep.OnboardingStep["type"];
          categoryTitle: everest_appserver_primitive_Text;
        }[];
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace ResetWizard {
      export type Input = Record<string, never>;

      export type Output = {
        success: everest_appserver_primitive_TrueFalse;
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace ValidateResponse {
      export type Input = {
        questionUuid: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["uuid"];
        selectedOptionUuids: everest_onboarding_model_node_OnboardingQuestionOption.OnboardingQuestionOption["uuid"][];
      };

      export type Output = {
        isValid: everest_appserver_primitive_TrueFalse;
        validationMessage: everest_appserver_primitive_Text;
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }
  }

  export type Implementation = {
    Questions: EntitySets.Questions.Implementation;
    QuestionOptions: EntitySets.QuestionOptions.Implementation;
    Steps: EntitySets.Steps.Implementation;
    StepCategories: EntitySets.StepCategories.Implementation;
    OptionToStepMappings: EntitySets.OptionToStepMappings.Implementation;
    InitializeWizard: Actions.InitializeWizard.Implementation;
    GetQuestionWithOptions: Actions.GetQuestionWithOptions.Implementation;
    SaveQuestionResponse: Actions.SaveQuestionResponse.Implementation;
    GetNextQuestionByRouting: Actions.GetNextQuestionByRouting.Implementation;
    GetPreviousQuestion: Actions.GetPreviousQuestion.Implementation;
    GetWizardProgress: Actions.GetWizardProgress.Implementation;
    GetAvailableSteps: Actions.GetAvailableSteps.Implementation;
    ResetWizard: Actions.ResetWizard.Implementation;
    ValidateResponse: Actions.ValidateResponse.Implementation;
  };
}

