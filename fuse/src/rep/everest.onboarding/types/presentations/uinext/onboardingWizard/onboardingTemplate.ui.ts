/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

import type { OnboardingQuestion as everest_onboarding_model_node_OnboardingQuestion } from "@pkg/everest.onboarding/types/OnboardingQuestion";
import type { OnboardingQuestionOption as everest_onboarding_model_node_OnboardingQuestionOption } from "@pkg/everest.onboarding/types/OnboardingQuestionOption";
import type { OnboardingStep as everest_onboarding_model_node_OnboardingStep } from "@pkg/everest.onboarding/types/OnboardingStep";
import type { OnboardingStepCategory as everest_onboarding_model_node_OnboardingStepCategory } from "@pkg/everest.onboarding/types/OnboardingStepCategory";
import type { OnboardingOptionToStepMapping as everest_onboarding_model_node_OnboardingOptionToStepMapping } from "@pkg/everest.onboarding/types/OnboardingOptionToStepMapping";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { EvstDecimal as everest_appserver_primitive_Decimal } from "@pkg/everest.appserver/types/primitives/Decimal";

import { createOdataClientRuntime } from '@everestsystems/content-core/lib/runtime/loader';

/**
 * Generated frontend types for OData presentation onboardingTemplate.
 */
export namespace onboardingTemplateUI {
  export namespace EntitySets {
    export namespace Questions {
      export namespace Get {
        export type Entity = {
          id?: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["id"] | undefined;
          uuid?: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["uuid"] | undefined;
          text?: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["text"] | undefined;
          title?: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["title"] | undefined;
          orderIndex?: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["orderIndex"] | undefined;
        };

        export type Filter =
          | PropertyFilter
          | [Filter, 'and', Filter]
          | [Filter, 'or', Filter];

        /**
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), filters must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to filter by its amount component.
         */
        export type PropertyFilter = {
          property: string;
          comparator: 'eq' | 'ne' | 'lt' | 'le' | 'gt' | 'ge' | 'contains';
          value: unknown;
        };

        /**
         * Specifies sorting for a property in ascending or descending order.
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), sorting must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to sort by its amount component.
         */
        export type OrderbyItem = {
          property: string;
          ordering: 'asc' | 'desc';
        };

        export interface Request {
          setFilter(filter: Filter): this;

          setOrderby(orderby: ReadonlyArray<OrderbyItem>): this;

          setSkip(skip: number): this;

          setTop(top: number): this;

          setSelect(select: ReadonlyArray<keyof Entity>): this;

          setCount(count: boolean): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entities: Entity[];
          count?: number | undefined;
        };
      }
    }
    export namespace QuestionOptions {
      export namespace Get {
        export type Entity = {
          id?: everest_onboarding_model_node_OnboardingQuestionOption.OnboardingQuestionOption["id"] | undefined;
          uuid?: everest_onboarding_model_node_OnboardingQuestionOption.OnboardingQuestionOption["uuid"] | undefined;
          description?: everest_onboarding_model_node_OnboardingQuestionOption.OnboardingQuestionOption["description"] | undefined;
          value?: everest_onboarding_model_node_OnboardingQuestionOption.OnboardingQuestionOption["value"] | undefined;
          orderIndex?: everest_onboarding_model_node_OnboardingQuestionOption.OnboardingQuestionOption["orderIndex"] | undefined;
          isSelected?: everest_onboarding_model_node_OnboardingQuestionOption.OnboardingQuestionOption["isSelected"] | undefined;
          onboardingQuestionUUID?: everest_onboarding_model_node_OnboardingQuestionOption.OnboardingQuestionOption["onboardingQuestionUUID"] | undefined;
          routeToNextQuestion?: everest_onboarding_model_node_OnboardingQuestionOption.OnboardingQuestionOption["routeToNextQuestion"] | undefined;
        };

        export type Filter =
          | PropertyFilter
          | [Filter, 'and', Filter]
          | [Filter, 'or', Filter];

        /**
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), filters must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to filter by its amount component.
         */
        export type PropertyFilter = {
          property: string;
          comparator: 'eq' | 'ne' | 'lt' | 'le' | 'gt' | 'ge' | 'contains';
          value: unknown;
        };

        /**
         * Specifies sorting for a property in ascending or descending order.
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), sorting must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to sort by its amount component.
         */
        export type OrderbyItem = {
          property: string;
          ordering: 'asc' | 'desc';
        };

        export interface Request {
          setFilter(filter: Filter): this;

          setOrderby(orderby: ReadonlyArray<OrderbyItem>): this;

          setSkip(skip: number): this;

          setTop(top: number): this;

          setSelect(select: ReadonlyArray<keyof Entity>): this;

          setCount(count: boolean): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entities: Entity[];
          count?: number | undefined;
        };
      }
    }
    export namespace Steps {
      export namespace Get {
        export type Entity = {
          id?: everest_onboarding_model_node_OnboardingStep.OnboardingStep["id"] | undefined;
          uuid?: everest_onboarding_model_node_OnboardingStep.OnboardingStep["uuid"] | undefined;
          title?: everest_onboarding_model_node_OnboardingStep.OnboardingStep["title"] | undefined;
          orderIndex?: everest_onboarding_model_node_OnboardingStep.OnboardingStep["orderIndex"] | undefined;
          status?: everest_onboarding_model_node_OnboardingStep.OnboardingStep["status"] | undefined;
          finishedAt?: everest_onboarding_model_node_OnboardingStep.OnboardingStep["finishedAt"] | undefined;
          note?: everest_onboarding_model_node_OnboardingStep.OnboardingStep["note"] | undefined;
          primaryLink?: everest_onboarding_model_node_OnboardingStep.OnboardingStep["primaryLink"] | undefined;
          secondaryLink?: everest_onboarding_model_node_OnboardingStep.OnboardingStep["secondaryLink"] | undefined;
          modelURN?: everest_onboarding_model_node_OnboardingStep.OnboardingStep["modelURN"] | undefined;
          type?: everest_onboarding_model_node_OnboardingStep.OnboardingStep["type"] | undefined;
          categoryUUID?: everest_onboarding_model_node_OnboardingStep.OnboardingStep["categoryUUID"] | undefined;
          isAlwaysVisible?: everest_onboarding_model_node_OnboardingStep.OnboardingStep["isAlwaysVisible"] | undefined;
        };

        export type Filter =
          | PropertyFilter
          | [Filter, 'and', Filter]
          | [Filter, 'or', Filter];

        /**
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), filters must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to filter by its amount component.
         */
        export type PropertyFilter = {
          property: string;
          comparator: 'eq' | 'ne' | 'lt' | 'le' | 'gt' | 'ge' | 'contains';
          value: unknown;
        };

        /**
         * Specifies sorting for a property in ascending or descending order.
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), sorting must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to sort by its amount component.
         */
        export type OrderbyItem = {
          property: string;
          ordering: 'asc' | 'desc';
        };

        export interface Request {
          setFilter(filter: Filter): this;

          setOrderby(orderby: ReadonlyArray<OrderbyItem>): this;

          setSkip(skip: number): this;

          setTop(top: number): this;

          setSelect(select: ReadonlyArray<keyof Entity>): this;

          setCount(count: boolean): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entities: Entity[];
          count?: number | undefined;
        };
      }
    }
    export namespace StepCategories {
      export namespace Get {
        export type Entity = {
          id?: everest_onboarding_model_node_OnboardingStepCategory.OnboardingStepCategory["id"] | undefined;
          uuid?: everest_onboarding_model_node_OnboardingStepCategory.OnboardingStepCategory["uuid"] | undefined;
          title?: everest_onboarding_model_node_OnboardingStepCategory.OnboardingStepCategory["title"] | undefined;
          orderIndex?: everest_onboarding_model_node_OnboardingStepCategory.OnboardingStepCategory["orderIndex"] | undefined;
        };

        export type Filter =
          | PropertyFilter
          | [Filter, 'and', Filter]
          | [Filter, 'or', Filter];

        /**
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), filters must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to filter by its amount component.
         */
        export type PropertyFilter = {
          property: string;
          comparator: 'eq' | 'ne' | 'lt' | 'le' | 'gt' | 'ge' | 'contains';
          value: unknown;
        };

        /**
         * Specifies sorting for a property in ascending or descending order.
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), sorting must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to sort by its amount component.
         */
        export type OrderbyItem = {
          property: string;
          ordering: 'asc' | 'desc';
        };

        export interface Request {
          setFilter(filter: Filter): this;

          setOrderby(orderby: ReadonlyArray<OrderbyItem>): this;

          setSkip(skip: number): this;

          setTop(top: number): this;

          setSelect(select: ReadonlyArray<keyof Entity>): this;

          setCount(count: boolean): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entities: Entity[];
          count?: number | undefined;
        };
      }
    }
    export namespace OptionToStepMappings {
      export namespace Get {
        export type Entity = {
          id?: everest_onboarding_model_node_OnboardingOptionToStepMapping.OnboardingOptionToStepMapping["id"] | undefined;
          uuid?: everest_onboarding_model_node_OnboardingOptionToStepMapping.OnboardingOptionToStepMapping["uuid"] | undefined;
          onboardingOptionUUID?: everest_onboarding_model_node_OnboardingOptionToStepMapping.OnboardingOptionToStepMapping["onboardingOptionUUID"] | undefined;
          onboardinStepUUID?: everest_onboarding_model_node_OnboardingOptionToStepMapping.OnboardingOptionToStepMapping["onboardinStepUUID"] | undefined;
        };

        export type Filter =
          | PropertyFilter
          | [Filter, 'and', Filter]
          | [Filter, 'or', Filter];

        /**
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), filters must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to filter by its amount component.
         */
        export type PropertyFilter = {
          property: string;
          comparator: 'eq' | 'ne' | 'lt' | 'le' | 'gt' | 'ge' | 'contains';
          value: unknown;
        };

        /**
         * Specifies sorting for a property in ascending or descending order.
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), sorting must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to sort by its amount component.
         */
        export type OrderbyItem = {
          property: string;
          ordering: 'asc' | 'desc';
        };

        export interface Request {
          setFilter(filter: Filter): this;

          setOrderby(orderby: ReadonlyArray<OrderbyItem>): this;

          setSkip(skip: number): this;

          setTop(top: number): this;

          setSelect(select: ReadonlyArray<keyof Entity>): this;

          setCount(count: boolean): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entities: Entity[];
          count?: number | undefined;
        };
      }
    }
  }

  export namespace Actions {
    export namespace SaveQuestionResponse {
      export namespace Execute {
        export type Input = {
          questionUuid: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["uuid"];
          selectedOptionUuids: everest_onboarding_model_node_OnboardingQuestionOption.OnboardingQuestionOption["uuid"][];
          selectedOptionValues: everest_appserver_primitive_Text[];
        };

        export type Output = {
          success: everest_appserver_primitive_TrueFalse;
          message: everest_appserver_primitive_Text;
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace ResetWizard {
      export namespace Execute {
        export type Input = Record<string, never>;

        export type Output = {
          success: everest_appserver_primitive_TrueFalse;
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }
  }

  export namespace Functions {
    export namespace InitializeWizard {
      export namespace Execute {
        export type Input = Record<string, never>;

        export type Output = {
          firstQuestion: {
            id: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["id"];
            uuid: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["uuid"];
            text: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["text"];
            title: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["title"];
            orderIndex: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["orderIndex"];
          };
          totalQuestions: everest_appserver_primitive_Number;
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace GetQuestionWithOptions {
      export namespace Execute {
        export type Input = {
          questionUuid: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["uuid"];
        };

        export type Output = {
          question: {
            id: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["id"];
            uuid: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["uuid"];
            text: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["text"];
            title: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["title"];
            orderIndex: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["orderIndex"];
          };
          options: {
            id: everest_onboarding_model_node_OnboardingQuestionOption.OnboardingQuestionOption["id"];
            uuid: everest_onboarding_model_node_OnboardingQuestionOption.OnboardingQuestionOption["uuid"];
            description: everest_onboarding_model_node_OnboardingQuestionOption.OnboardingQuestionOption["description"];
            value: everest_onboarding_model_node_OnboardingQuestionOption.OnboardingQuestionOption["value"];
            orderIndex: everest_onboarding_model_node_OnboardingQuestionOption.OnboardingQuestionOption["orderIndex"];
            routeToNextQuestion: everest_onboarding_model_node_OnboardingQuestionOption.OnboardingQuestionOption["routeToNextQuestion"];
          }[];
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace GetNextQuestionByRouting {
      export namespace Execute {
        export type Input = {
          currentQuestionUuid: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["uuid"];
          selectedOptionUuids: everest_onboarding_model_node_OnboardingQuestionOption.OnboardingQuestionOption["uuid"][];
        };

        export type Output = {
          nextQuestion: {
            id: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["id"];
            uuid: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["uuid"];
            text: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["text"];
            title: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["title"];
            orderIndex: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["orderIndex"];
          };
          hasNext: everest_appserver_primitive_TrueFalse;
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace GetPreviousQuestion {
      export namespace Execute {
        export type Input = {
          currentQuestionUuid: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["uuid"];
        };

        export type Output = {
          previousQuestion: {
            id: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["id"];
            uuid: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["uuid"];
            text: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["text"];
            title: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["title"];
            orderIndex: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["orderIndex"];
          };
          hasPrevious: everest_appserver_primitive_TrueFalse;
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace GetWizardProgress {
      export namespace Execute {
        export type Input = Record<string, never>;

        export type Output = {
          currentStep: everest_appserver_primitive_Number;
          totalSteps: everest_appserver_primitive_Number;
          completedQuestions: {
            questionUuid: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["uuid"];
            selectedOptions: everest_appserver_primitive_Text[];
          }[];
          progressPercentage: everest_appserver_primitive_Decimal;
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace GetAvailableSteps {
      export namespace Execute {
        export type Input = {
          completedResponses: {
            questionUuid: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["uuid"];
            selectedOptionUuids: everest_onboarding_model_node_OnboardingQuestionOption.OnboardingQuestionOption["uuid"][];
          }[];
        };

        export type Output = {
          availableSteps: {
            id: everest_onboarding_model_node_OnboardingStep.OnboardingStep["id"];
            uuid: everest_onboarding_model_node_OnboardingStep.OnboardingStep["uuid"];
            title: everest_onboarding_model_node_OnboardingStep.OnboardingStep["title"];
            orderIndex: everest_onboarding_model_node_OnboardingStep.OnboardingStep["orderIndex"];
            status: everest_onboarding_model_node_OnboardingStep.OnboardingStep["status"];
            primaryLink: everest_onboarding_model_node_OnboardingStep.OnboardingStep["primaryLink"];
            type: everest_onboarding_model_node_OnboardingStep.OnboardingStep["type"];
            categoryTitle: everest_appserver_primitive_Text;
          }[];
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace ValidateResponse {
      export namespace Execute {
        export type Input = {
          questionUuid: everest_onboarding_model_node_OnboardingQuestion.OnboardingQuestion["uuid"];
          selectedOptionUuids: everest_onboarding_model_node_OnboardingQuestionOption.OnboardingQuestionOption["uuid"][];
        };

        export type Output = {
          isValid: everest_appserver_primitive_TrueFalse;
          validationMessage: everest_appserver_primitive_Text;
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }
  }

  export namespace Permission {
    export namespace Check {
      export type Result = {
        dataSets: {
          OptionToStepMappings: {
            isGetPermitted: boolean;
          };
          QuestionOptions: {
            isGetPermitted: boolean;
          };
          Questions: {
            isGetPermitted: boolean;
          };
          StepCategories: {
            isGetPermitted: boolean;
          };
          Steps: {
            isGetPermitted: boolean;
          };
        };
        actions: {
          GetAvailableSteps: {
            isExecutePermitted: boolean;
          };
          GetNextQuestionByRouting: {
            isExecutePermitted: boolean;
          };
          GetPreviousQuestion: {
            isExecutePermitted: boolean;
          };
          GetQuestionWithOptions: {
            isExecutePermitted: boolean;
          };
          GetWizardProgress: {
            isExecutePermitted: boolean;
          };
          InitializeWizard: {
            isExecutePermitted: boolean;
          };
          ResetWizard: {
            isExecutePermitted: boolean;
          };
          SaveQuestionResponse: {
            isExecutePermitted: boolean;
          };
          ValidateResponse: {
            isExecutePermitted: boolean;
          };
        };
      };

      export interface Request {
        execute(): Promise<Response>;
      }

      export type Response = {
        result: Result;
      };
    }
  }

  export namespace Api {
    export type EntitySets = {
      Questions: {
        get: EntitySets.Questions.Get.Request;
      };
      QuestionOptions: {
        get: EntitySets.QuestionOptions.Get.Request;
      };
      Steps: {
        get: EntitySets.Steps.Get.Request;
      };
      StepCategories: {
        get: EntitySets.StepCategories.Get.Request;
      };
      OptionToStepMappings: {
        get: EntitySets.OptionToStepMappings.Get.Request;
      };
    };

    export type Actions = {
      SaveQuestionResponse: {
        execute: Actions.SaveQuestionResponse.Execute.Request;
      };
      ResetWizard: {
        execute: Actions.ResetWizard.Execute.Request;
      };
    };

    export type Functions = {
      InitializeWizard: {
        execute: Functions.InitializeWizard.Execute.Request;
      };
      GetQuestionWithOptions: {
        execute: Functions.GetQuestionWithOptions.Execute.Request;
      };
      GetNextQuestionByRouting: {
        execute: Functions.GetNextQuestionByRouting.Execute.Request;
      };
      GetPreviousQuestion: {
        execute: Functions.GetPreviousQuestion.Execute.Request;
      };
      GetWizardProgress: {
        execute: Functions.GetWizardProgress.Execute.Request;
      };
      GetAvailableSteps: {
        execute: Functions.GetAvailableSteps.Execute.Request;
      };
      ValidateResponse: {
        execute: Functions.ValidateResponse.Execute.Request;
      };
    };

    export interface Client {
      createEntitySetGetRequest<T extends 'Questions' | 'QuestionOptions' | 'Steps' | 'StepCategories' | 'OptionToStepMappings'>(entitySetName: T): Api.EntitySets[T]['get'];

      createActionExecuteRequest<T extends 'SaveQuestionResponse' | 'ResetWizard'>(actionName: T): Api.Actions[T]['execute'];

      createFunctionExecuteRequest<T extends 'InitializeWizard' | 'GetQuestionWithOptions' | 'GetNextQuestionByRouting' | 'GetPreviousQuestion' | 'GetWizardProgress' | 'GetAvailableSteps' | 'ValidateResponse'>(functionName: T): Api.Functions[T]['execute'];

      createPermissionCheckRequest(): Permission.Check.Request;
    }
  }

  export const client = createOdataClientRuntime<Api.Client>('everest.onboarding/uinext/onboardingWizard/onboardingTemplate');
}

