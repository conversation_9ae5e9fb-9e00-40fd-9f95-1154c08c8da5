/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

// ******************************************************************************
// Generated Everest Presentation Types
// ******************************************************************************

import type {
  ActionValidationResult,
  COMPONENT,
  CONFIGURATIONS,
  DATA,
  DynamicFieldsMetadata,
  GetElementType,
  DynamicFieldDefinition,
  DATA_SETS,
  DYNAMIC_FIELDS,
  FieldInstanceMetadata,
  FieldLevelMetadata,
  IDENTIFIER,
  ISession,
  METADATA,
  QueryInput,
  QueryReason,
  RoutineDeterminationResult,
  RoutineValidationContext,
  RoutineValidationResult,
  TableComponent,
  ValidateReason
} from '@everestsystems/content-core';

import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { EvstDecimal as everest_appserver_primitive_Decimal } from "@pkg/everest.appserver/types/primitives/Decimal";

export namespace wizardIntroPresentation {
  export type mode = 'main';

  export namespace dataSources {
    export namespace wizardIntroData {
      export type levels = {
        '': never;
        Header: {
          subtitle?: everest_appserver_primitive_Text | undefined | null;
          title?: everest_appserver_primitive_Text | undefined | null;
        };
        NavigationButtons: {
          backLabel?: everest_appserver_primitive_Text | undefined | null;
          canContinue?: everest_appserver_primitive_TrueFalse | undefined | null;
          canGoBack?: everest_appserver_primitive_TrueFalse | undefined | null;
          continueLabel?: everest_appserver_primitive_Text | undefined | null;
        };
        Progress: {
          currentStep?: everest_appserver_primitive_Number | undefined | null;
          progressPercentage?: everest_appserver_primitive_Decimal | undefined | null;
          stepLabel?: everest_appserver_primitive_Text | undefined | null;
          totalSteps?: everest_appserver_primitive_Number | undefined | null;
        };
        UserNameInput: {
          isValid?: everest_appserver_primitive_TrueFalse | undefined | null;
          placeholder?: everest_appserver_primitive_Text | undefined | null;
          userName?: everest_appserver_primitive_Text | undefined | null;
        };
        WelcomeContent: {
          description?: everest_appserver_primitive_Text | undefined | null;
          greeting?: everest_appserver_primitive_Text | undefined | null;
          question?: everest_appserver_primitive_Text | undefined | null;
        };
      };

      export type instanceMetadata = {
        '': never;
        Header: {
          subtitle?: FieldInstanceMetadata | undefined;
          title?: FieldInstanceMetadata | undefined;
        };
        NavigationButtons: {
          backLabel?: FieldInstanceMetadata | undefined;
          canContinue?: FieldInstanceMetadata | undefined;
          canGoBack?: FieldInstanceMetadata | undefined;
          continueLabel?: FieldInstanceMetadata | undefined;
        };
        Progress: {
          currentStep?: FieldInstanceMetadata | undefined;
          progressPercentage?: FieldInstanceMetadata | undefined;
          stepLabel?: FieldInstanceMetadata | undefined;
          totalSteps?: FieldInstanceMetadata | undefined;
        };
        UserNameInput: {
          isValid?: FieldInstanceMetadata | undefined;
          placeholder?: FieldInstanceMetadata | undefined;
          userName?: FieldInstanceMetadata | undefined;
        };
        WelcomeContent: {
          description?: FieldInstanceMetadata | undefined;
          greeting?: FieldInstanceMetadata | undefined;
          question?: FieldInstanceMetadata | undefined;
        };
      };

      export type levelMetadata = {
        '': never;
        Header: {
          subtitle?: FieldLevelMetadata | undefined;
          title?: FieldLevelMetadata | undefined;
        };
        NavigationButtons: {
          backLabel?: FieldLevelMetadata | undefined;
          canContinue?: FieldLevelMetadata | undefined;
          canGoBack?: FieldLevelMetadata | undefined;
          continueLabel?: FieldLevelMetadata | undefined;
        };
        Progress: {
          currentStep?: FieldLevelMetadata | undefined;
          progressPercentage?: FieldLevelMetadata | undefined;
          stepLabel?: FieldLevelMetadata | undefined;
          totalSteps?: FieldLevelMetadata | undefined;
        };
        UserNameInput: {
          isValid?: FieldLevelMetadata | undefined;
          placeholder?: FieldLevelMetadata | undefined;
          userName?: FieldLevelMetadata | undefined;
        };
        WelcomeContent: {
          description?: FieldLevelMetadata | undefined;
          greeting?: FieldLevelMetadata | undefined;
          question?: FieldLevelMetadata | undefined;
        };
      };

      export type levelConfigurations = {
        '': never;
        Header: never;
        NavigationButtons: never;
        Progress: never;
        UserNameInput: never;
        WelcomeContent: never;
      };

      export type parameters = never;

      export namespace callbacks {
        export namespace setUp {
          export type input = {
            session: ISession;
            mode: mode;
          };

          export type output = void;
        }

        export namespace query {
          export type queryParameters = never;

          export type queryMetadata = {
            Header?: levelMetadata['Header'];
            NavigationButtons?: levelMetadata['NavigationButtons'];
            Progress?: levelMetadata['Progress'];
            UserNameInput?: levelMetadata['UserNameInput'];
            WelcomeContent?: levelMetadata['WelcomeContent'];
          };

          export type queryConfigurations = {
            Header?: levelConfigurations['Header'];
            NavigationButtons?: levelConfigurations['NavigationButtons'];
            Progress?: levelConfigurations['Progress'];
            UserNameInput?: levelConfigurations['UserNameInput'];
            WelcomeContent?: levelConfigurations['WelcomeContent'];
          };

          export type queryData = {
            Header?: levels['Header'] & {
              [METADATA]?: instanceMetadata['Header'] | undefined;
            };
            NavigationButtons?: levels['NavigationButtons'] & {
              [METADATA]?: instanceMetadata['NavigationButtons'] | undefined;
            };
            Progress?: levels['Progress'] & {
              [METADATA]?: instanceMetadata['Progress'] | undefined;
            };
            UserNameInput?: levels['UserNameInput'] & {
              [METADATA]?: instanceMetadata['UserNameInput'] | undefined;
            };
            WelcomeContent?: levels['WelcomeContent'] & {
              [METADATA]?: instanceMetadata['WelcomeContent'] | undefined;
            };
          };

          export type input = {
            session: ISession;
            mode: mode;
            queryReason: QueryReason;
            queryInput: QueryInput;
            queryInstruction: {
              fields: ReadonlySet<keyof levels['']> | undefined;
              Header?: {
                fields: ReadonlySet<keyof levels['Header']> | undefined;
              };
              NavigationButtons?: {
                fields: ReadonlySet<keyof levels['NavigationButtons']> | undefined;
              };
              Progress?: {
                fields: ReadonlySet<keyof levels['Progress']> | undefined;
              };
              UserNameInput?: {
                fields: ReadonlySet<keyof levels['UserNameInput']> | undefined;
              };
              WelcomeContent?: {
                fields: ReadonlySet<keyof levels['WelcomeContent']> | undefined;
              };
            };
          };

          export type output = queryData;

          export type combinedOutput = queryData | {
            [DATA]: queryData;
            [METADATA]?: queryMetadata | undefined;
            [CONFIGURATIONS]?: queryConfigurations | undefined;
            [key: string]: never;
          };
        }

        export namespace update_UserNameInput {
          export type input = {
            session: ISession;
            mode: mode;
            fieldName: keyof levels['UserNameInput'];
            oldFieldValue: unknown;
            newFieldValue: unknown;
          };

          export type output = void;
        }
      }

      export namespace routines {
        export namespace continueToNext {
          export type input = {
            userName: everest_appserver_primitive_Text;
          };

          export type output = {
            success: everest_appserver_primitive_TrueFalse;
            validationMessage: everest_appserver_primitive_Text;
          };

          export type determineInput = {
            session: ISession;
            mode: mode;
            input: Pick<input, 'userName'>;
          };

          export type determineOutput = RoutineDeterminationResult;

          export type validateInput = {
            session: ISession;
            mode: mode;
            validateReason: ValidateReason;
            context: RoutineValidationContext;
            input: input;
          };

          export type validateOutput = RoutineValidationResult;

          export type executeInput = {
            session: ISession;
            mode: mode;
            input: input;
          };

          export type executeOutput = output;
        }

        export namespace goBack {
          export type input = never;

          export type output = void;

          export type determineInput = {
            session: ISession;
            mode: mode;
          };

          export type determineOutput = RoutineDeterminationResult;

          export type validateInput = {
            session: ISession;
            mode: mode;
            validateReason: ValidateReason;
            context: RoutineValidationContext;
          };

          export type validateOutput = RoutineValidationResult;

          export type executeInput = {
            session: ISession;
            mode: mode;
          };

          export type executeOutput = output;
        }

        export namespace initializeWizard {
          export type input = never;

          export type output = {
            currentStep: everest_appserver_primitive_Number;
            totalSteps: everest_appserver_primitive_Number;
          };

          export type determineInput = {
            session: ISession;
            mode: mode;
          };

          export type determineOutput = RoutineDeterminationResult;

          export type validateInput = {
            session: ISession;
            mode: mode;
            validateReason: ValidateReason;
            context: RoutineValidationContext;
          };

          export type validateOutput = RoutineValidationResult;

          export type executeInput = {
            session: ISession;
            mode: mode;
          };

          export type executeOutput = output;
        }
      }

      export interface implementation {
        setUp?(input: callbacks.setUp.input): Promise<callbacks.setUp.output>;

        query(input: callbacks.query.input): Promise<callbacks.query.combinedOutput>;

        update_UserNameInput(input: callbacks.update_UserNameInput.input): Promise<callbacks.update_UserNameInput.output>;

        determine_continueToNext?(input: routines.continueToNext.determineInput): Promise<routines.continueToNext.determineOutput>;

        validate_continueToNext?(input: routines.continueToNext.validateInput): Promise<routines.continueToNext.validateOutput>;

        execute_continueToNext(input: routines.continueToNext.executeInput): Promise<routines.continueToNext.executeOutput>;

        determine_goBack?(input: routines.goBack.determineInput): Promise<routines.goBack.determineOutput>;

        validate_goBack?(input: routines.goBack.validateInput): Promise<routines.goBack.validateOutput>;

        execute_goBack(input: routines.goBack.executeInput): Promise<routines.goBack.executeOutput>;

        determine_initializeWizard?(input: routines.initializeWizard.determineInput): Promise<routines.initializeWizard.determineOutput>;

        validate_initializeWizard?(input: routines.initializeWizard.validateInput): Promise<routines.initializeWizard.validateOutput>;

        execute_initializeWizard(input: routines.initializeWizard.executeInput): Promise<routines.initializeWizard.executeOutput>;
      }
    }
  }

  export type implementation = {
    wizardIntroData(): dataSources.wizardIntroData.implementation;
  };
}
