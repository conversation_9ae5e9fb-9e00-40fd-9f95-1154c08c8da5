// @i18n:everest.migration.base/migrationBase
// @i18n:everest.fin.integration.expense/expenseIntegration
import type { EvstExtractionProviders } from '@pkg/everest.fin.integration.base/types/enums/ExtractionProviders';
import type { CustomConfig } from '@pkg/everest.migration.base/public/config';
import {
  MigrationType,
  ModelDataload,
} from '@pkg/everest.migration.base/public/types';
import type { syncPresentationUI } from '@pkg/everest.onboarding/types/presentations/uinext/sync/sync.ui';
import { getObjectNameFromUrn } from '@pkg/everest.onboarding/utils/getObjectNameByUrn';

type Context = syncPresentationUI.context & {
  state: {
    objectURN: string;
    providerName: EvstExtractionProviders;
    operationMode: MigrationType;
    dataLoad: ModelDataload;
    customConfig: CustomConfig;
  };
};

export function dateRangeStatus(settings): string {
  const { startDate, endDate } = settings ?? {};

  if (!startDate && !endDate) {
    return '{{expenseIntegration.notSet}}';
  }

  if (startDate && endDate) {
    return `${startDate} - ${endDate}`;
  } else if (startDate) {
    return `${startDate} - /`;
  } else if (endDate) {
    return `/ - ${endDate}`;
  }

  return '{{expenseIntegration.notSet}}';
}

export async function getConfigurations(context: Context) {
  const { state, data } = context;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const config = data.config as any;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const settings = data.settings as any;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const connector = data.connector as any;

  const configs: {
    label: string;
    result: string;
    buttonActions: {
      shape: string;
      label: string;
      onClick: () => void;
      disabled?: boolean;
    }[];
  }[] = [];

  if (config?.baseConfigs?.useDateRange) {
    configs.push({
      label: '{{migrationBase.dateRange}}',
      result: dateRangeStatus(settings),
      buttonActions: [
        {
          shape: 'pill',
          label: '{{expenseIntegration.manage}}',
          onClick: () =>
            openConfigModal(
              context,
              '{{expenseIntegration.titleStartDate}}'.replace(
                '$title$',
                config?.templates?.overview?.title
              ),
              '/templates/everest.migration.base/uinext/configurations/dateRange',
              {
                settingsId: settings?.settingsId,
                providerName: state.providerName,
                startDate: settings?.startDate,
                endDate: settings?.endDate,
              }
            ),
        },
      ],
    });
  }

  return configs;
}

function openConfigModal(
  ctx: Context,
  title: string,
  templateUrl: string,
  initialState: Record<string, unknown>,
  size: CustomConfig['size'] = 'xsmall'
) {
  const { helpers, actions } = ctx;
  helpers.openModal({
    title,
    template: templateUrl,
    size,
    initialState,
    onClose: async () => {
      await actions.refetchUiModelData();
    },
  });
}

export function onOperationModeChange({ state }, value) {
  state.operationMode = value;
}

export function isOperationModeEditable(context: Context) {
  return getOperationModes(context).length > 1;
}

export function getOperationModes(context: Context) {
  const operationOptions = [];
  const configTypes = (context?.data?.config?.mapping as any)?.mappings?.api
    ?.type;

  // To do: We only look at load or match for now. In the future this has to be extended to support load and match aswell.
  if (configTypes?.technical) {
    operationOptions.push({
      value: MigrationType.Technical,
      label: 'Load',
    });
  }

  if (configTypes?.semantic) {
    operationOptions.push({
      value: MigrationType.Semantic,
      label: 'Match',
    });
  }

  if (operationOptions.length === 1) {
    context.state.operationMode = operationOptions[0].value;
  }
  return operationOptions;
}

export function onFailWhenExistChange({ state }, value: boolean) {
  state.failWhenExist = value;
}

export function hasNoSessionId(context: Context) {
  const { data } = context;

  return isConnected(context) && !data.connector.sessionId;
}

export function isDisconnected(context: Context) {
  const { data } = context;

  return !data.connector?.id;
}

export function isConnected(context: Context) {
  const { data } = context;

  return Boolean(data.connector?.id);
}

export async function onChangeProvider(
  { state }: Context,
  value: { selectedLines: EvstExtractionProviders[] }
) {
  state.providerName = value.selectedLines?.[0];
}

export async function nextClickFromProviderStep(context: Context) {
  const { presentationClient, state } = context;
  const { providerName, objectURN } = state;

  await presentationClient.setConfiguration({
    providerName,
    objectURN,
  });
}

export function onImportModeChange({ state }, value: string) {
  state.importMode = value;
}

export function isproviderName({ state }: Context) {
  return !state.providerName;
}

export function onPreviewDataChange({ state }, value: number) {
  state.isPreviewData = Boolean(value);
}

export function isSyncDisabled(context: Context) {
  return !isConnected(context);
}

export function isProviderSelected({ state }) {
  return !state.providerName;
}

export async function syncObject(context: Context, object: string) {
  const { helpers, state } = context;

  try {
    helpers.showNotificationMessage({
      key: 'sync',
      type: 'loading',
      message: `Syncing ${state.objectURN} data...`,
      duration: 5,
    });

    await sync(context);

    // Your sync logic would go here
    // For now just simulating with a timeout
    await new Promise((resolve) => setTimeout(resolve, 2000));

    helpers.showNotificationMessage({
      key: 'sync',
      type: 'success',
      message: `${object} data synced successfully`,
      duration: 5,
    });

    helpers.closeModal();

    // TODO: Create preview flow
    //   if (state.isPreviewData) {
    // helpers.navigate({
    //   to: '/templates/everest.onboard/uinext/dataBrowser/dataBrowser',
    //   closeCurrentTab: true,
    //   initialState: {
    //     objectType: object,
    //     entity: state.selectedEntity,
    //     importMode: state.importMode,
    //     failWhenExist: state.failWhenExist,
    //   },
    // });

    //     return;
    //   }

    // TODO: Create statiscal overview flow
    // helpers.navigate({
    //   to: '/templates/everest.onboard/uinext/reconciliation/statisticalView',
    //   closeCurrentTab: true,
    // });
    // eslint-disable-next-line unicorn/prefer-optional-catch-binding
  } catch (error) {
    helpers.showNotificationMessage({
      key: 'sync',
      type: 'error',
      message: `Failed to sync data. ${error.message}`,
      duration: 5,
    });
  }
}

export function getLines(context: Context) {
  return context.data.connectors?.map((connector) => {
    return {
      value: connector.title,
      label: connector.title,
      selected: connector.title === context.state.providerName,
      avatar: {
        src: connector.picture,
        text: connector.title,
      },
      tag: {
        text: connector.details,
        color: 'shamrock',
        tooltip: {
          text: 'Connected.',
        },
      },
    };
  });
}

export function getConfigurationTitle(context: Context) {
  const { state } = context;
  return `Provider: ${state.providerName}`;
}

export async function sync(context: Context) {
  const { actions, data } = context;

  if (!validateSyncRequirements(context)) {
    return;
  }

  if (data.connector.sessionId) {
    await saveConfigurations(context);
  }

  await performSync(context);

  await actions.refetchUiModelData();
}

function validateSyncRequirements(context: Context): boolean {
  const { state } = context;

  if (!state.providerName && !state.objectURN) {
    showErrorNotification(context, 'No provider selected');
    return false;
  }

  return true;
}

function showErrorNotification({ helpers }: Context, message: string) {
  helpers.showNotificationMessage({
    key: 'loading',
    type: 'error',
    message,
    duration: 4,
  });
}

async function saveConfigurations(context: Context) {
  const { presentationClient } = context;

  return presentationClient.saveConfigurations();
}

async function performSync(context: Context) {
  const { state, data, presentationClient } = context;
  const mapping = data?.config?.mapping as any;

  return await presentationClient.syncObject({
    mapping: {
      ...mapping,
      providerName: state.providerName,
    },
    fetching: {
      dataload: ModelDataload.Single,
      migrationType: state.operationMode,
      startDate: new Date(1, 0, 2024),
      endDate: undefined,
      extractionUUID: context.helpers.uuid(),
    },
  });
}

export function isCustomConfigurationsVisible(context: Context) {
  const { data } = context;

  return isConnected(context) && data.config?.customConfigs?.length > 0;
}

export function getCustomConfigurationTitle(context: Context) {
  const { state } = context;

  return `${getObjectNameFromUrn(state.objectURN)} Configuration`;
}
