package everest.onboarding

template presentation question1 {

  state {
    currentStep: Number<Int>
    totalSteps: Number<Int>
    selectedOption: Text
  }

  mode main {
    on header allow view
    on questionContent allow view
    on migrationOptions allow view, change
    on progress allow view
    on navigationButtons allow view
    allow actions continueToNext, goBack, initializeQuestion, selectOption
  }

  object data-source questionData {
    shape {
      child Header {
        title (editable: false): Text
        subtitle (editable: false): Text
      }

      child QuestionContent {
        question (editable: false): Text
        descriptions (editable: false): Text
      }

      child MigrationOptions {
        hasExistingData (editable: true): TrueFalse
        hasExistingDataLabel (editable: false): Text
        isNewSetup (editable: true): TrueFalse
        isNewSetupLabel (editable: false): Text
        selectedValue (editable: false): Text
      }

      child Progress {
        currentStep (editable: false): Number<Int>
        totalSteps (editable: false): Number<Int>
        progressPercentage (editable: false): Number<Decimal>
        stepLabel (editable: false): Text
      }

      child NavigationButtons {
        canGoBack (editable: false): TrueFalse
        canContinue (editable: false): TrueFalse
        backLabel (editable: false): Text
        continueLabel (editable: false): Text
      }
    }

    modifications {
      on MigrationOptions support update
    }

    routine continueToNext {
      inputs {
        selectedOption: Text
      }
      outputs {
        success: TrueFalse
        validationMessage: Text
        nextStep: Text
        currentStep: Number<Int>
      }
      properties {
        side-effects true
      }
    }

    routine goBack {
      properties {
        side-effects false
      }
    }

    routine initializeQuestion {
      outputs {
        currentStep: Number<Int>
        totalSteps: Number<Int>
        questionData: Text
      }
      properties {
        side-effects false
      }
    }

    routine selectOption {
      inputs {
        optionValue: Text
      }
      outputs {
        success: TrueFalse
      }
      properties {
        side-effects false
      }
    }
  }

  struct header {
    data questionData.Header
    fields *
  }

  struct questionContent {
    data questionData.QuestionContent
    fields *
  }

  struct migrationOptions {
    data questionData.MigrationOptions
    fields *
  }

  struct progress {
    data questionData.Progress
    fields *
  }

  struct navigationButtons {
    data questionData.NavigationButtons
    fields *
  }

  delegate action continueToNext to data-source<questionData>.continueToNext {
    inputs {
      selectedOption = migrationOptions.selectedValue
    }
    outputs {
      state.currentStep = currentStep
    }
  }

  delegate action goBack to data-source<questionData>.goBack

  delegate action initializeQuestion to data-source<questionData>.initializeQuestion {
    outputs {
      state.currentStep = currentStep
      state.totalSteps = totalSteps
    }
  }

  delegate action selectOption to data-source<questionData>.selectOption {
    inputs {
      optionValue = migrationOptions.selectedValue
    }
  }
}
