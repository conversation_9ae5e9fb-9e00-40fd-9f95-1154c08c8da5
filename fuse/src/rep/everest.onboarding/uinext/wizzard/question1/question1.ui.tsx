import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { Button, Progress, Spin, message, Radio, Space } from 'antd';
import { useHistory } from 'react-router-dom';
import { Typography, Icon } from '@everestsystems/design-system';

const { H1, H2, P } = Typography;

// Everest logo URL
const EVEREST_LOGO = 'https://appdev.internal.everest-erp.com/assets/everest-logo-DHvidv0T.svg';

const Question1Page: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(true);
  const [selectedOption, setSelectedOption] = useState<string>('');
  const [validationMessage, setValidationMessage] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const history = useHistory();

  // Mock presentation data and actions for now
  // TODO: Replace with actual presentation layer integration
  const [data, setData] = useState<any>(null);
  const [presentationLoading, setPresentationLoading] = useState<boolean>(false);

  const actions = {
    initializeQuestion: async () => {
      // Mock initialization
      setData({
        header: {
          title: 'Everest ERP Setup Wizard',
          subtitle: 'Welcome to Everest Systems'
        },
        questionContent: {
          question: 'Are you migrating from another accounting/ERP system?',
          description: 'This will help us customize your setup experience.'
        },
        migrationOptions: {
          hasExistingDataLabel: 'Yes, we have existing data to import',
          isNewSetupLabel: 'No, this is a new setup'
        },
        progress: {
          currentStep: 3,
          totalSteps: 9,
          progressPercentage: 33.33,
          stepLabel: '3 of 9'
        },
        navigationButtons: {
          canGoBack: true,
          canContinue: true,
          backLabel: 'Back',
          continueLabel: 'Continue'
        }
      });
    },
    selectOption: async ({ optionValue }: { optionValue: string }) => {
      console.log('Selected option:', optionValue);
      return { success: true };
    },
    continueToNext: async ({ selectedOption }: { selectedOption: string }) => {
      console.log('Continue with option:', selectedOption);
      return {
        success: true,
        validationMessage: '',
        nextStep: selectedOption === 'existing_data' ? 'migration' : 'newsetup'
      };
    },
    goBack: async () => {
      console.log('Going back');
    }
  };

  const refresh = async () => {
    await actions.initializeQuestion();
  };

  // Initialize question on component mount
  useEffect(() => {
    const initializeQuestion = async () => {
      try {
        setLoading(true);
        await actions.initializeQuestion();
        await refresh();
      } catch (error) {
        console.error('Error initializing question:', error);
        message.error('Failed to initialize question');
      } finally {
        setLoading(false);
      }
    };

    initializeQuestion();
  }, []); // Empty dependency array - only run once on mount

  // Handle option selection
  const handleOptionChange = useCallback((e: any) => {
    const value = e.target.value;
    setSelectedOption(value);
    setValidationMessage(''); // Clear validation message on selection change
    
    // Call selectOption action
    actions.selectOption({ optionValue: value }).catch((error: any) => {
      console.error('Error selecting option:', error);
    });
  }, [actions]);

  // Handle continue button click
  const handleContinue = useCallback(async () => {
    if (!selectedOption) {
      setValidationMessage('Please select an option to continue.');
      return;
    }

    try {
      setIsSubmitting(true);
      setValidationMessage('');

      const result = await actions.continueToNext({ selectedOption });
      
      if (result.success) {
        message.success('Selection saved successfully!');
        // Navigate to next step based on selection
        if (selectedOption === 'existing_data') {
          // Navigate to migration flow
          // history.push('/templates/everest.onboarding/uinext/wizzard/migration');
        } else {
          // Navigate to new setup flow
          // history.push('/templates/everest.onboarding/uinext/wizzard/newsetup');
        }
      } else {
        setValidationMessage(result.validationMessage || 'Please check your selection and try again.');
      }
    } catch (error) {
      console.error('Error continuing to next step:', error);
      message.error('Failed to proceed to next step');
    } finally {
      setIsSubmitting(false);
    }
  }, [selectedOption, actions, history]);

  // Handle back button click
  const handleBack = useCallback(async () => {
    try {
      await actions.goBack();
      // Navigate back to previous step (wizard intro)
      history.push('/templates/everest.onboarding/uinext/wizzard/wizardIntro');
    } catch (error) {
      console.error('Error going back:', error);
    }
  }, [actions, history]);

  if (loading || presentationLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Spin size="large" />
      </div>
    );
  }

  const headerData = data?.header;
  const questionData = data?.questionContent;
  const migrationData = data?.migrationOptions;
  const progressData = data?.progress;
  const navigationData = data?.navigationButtons;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Progress Bar */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="max-w-4xl mx-auto">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">Progress</span>
            <span className="text-sm text-gray-500">
              {progressData?.stepLabel || '3 of 9'}
            </span>
          </div>
          <Progress
            percent={progressData?.progressPercentage || 33.33}
            showInfo={false}
            strokeColor="#4F46E5"
            className="mb-0"
          />
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-6 py-8">
        <Spin spinning={isSubmitting}>
          {/* Header Section */}
          <div className="bg-blue-600 text-white rounded-lg p-8 mb-8">
            <div className="flex items-center space-x-4">
              <Icon name="settings" size="large" className="text-white" />
              <div>
                <H1 className="text-white mb-0">
                  {headerData?.title || 'Everest ERP Setup Wizard'}
                </H1>
                <P className="text-blue-100 mb-0">
                  {headerData?.subtitle || 'Welcome to Everest Systems'}
                </P>
              </div>
            </div>
          </div>

          {/* Question Content */}
          <div className="bg-white rounded-lg shadow-sm p-8 mb-8">
            <div className="space-y-6">
              <div>
                <H2 className="text-2xl font-semibold text-gray-900 mb-6">
                  {questionData?.question || 'Are you migrating from another accounting/ERP system?'}
                </H2>
                {questionData?.description && (
                  <P className="text-lg text-gray-700 mb-6">
                    {questionData.description}
                  </P>
                )}
              </div>

              {/* Migration Options */}
              <div className="space-y-4">
                <Radio.Group
                  value={selectedOption}
                  onChange={handleOptionChange}
                  className="w-full"
                >
                  <Space direction="vertical" className="w-full" size="large">
                    <div className="border border-gray-200 rounded-lg p-6 hover:border-blue-300 transition-colors">
                      <Radio value="existing_data" className="text-lg">
                        {migrationData?.hasExistingDataLabel || 'Yes, we have existing data to import'}
                      </Radio>
                    </div>
                    <div className="border border-gray-200 rounded-lg p-6 hover:border-blue-300 transition-colors">
                      <Radio value="new_setup" className="text-lg">
                        {migrationData?.isNewSetupLabel || 'No, this is a new setup'}
                      </Radio>
                    </div>
                  </Space>
                </Radio.Group>
                {validationMessage && (
                  <P className="text-red-500 text-sm mb-0">{validationMessage}</P>
                )}
              </div>
            </div>
          </div>

          {/* Navigation */}
          <div className="flex justify-between items-center">
            <Button
              size="large"
              onClick={handleBack}
              disabled={!navigationData?.canGoBack}
            >
              <Icon name="arrow-left" className="mr-2" />
              {navigationData?.backLabel || 'Back'}
            </Button>

            <Button
              type="primary"
              size="large"
              onClick={handleContinue}
              disabled={!navigationData?.canContinue || isSubmitting}
              loading={isSubmitting}
            >
              {navigationData?.continueLabel || 'Continue'}
              <Icon name="arrow-right" className="ml-2" />
            </Button>
          </div>

          {/* Step Indicators */}
          <div className="flex justify-center mt-8">
            <div className="flex space-x-2">
              {Array.from({ length: progressData?.totalSteps || 9 }, (_, index) => (
                <div
                  key={index}
                  className={`w-2 h-2 rounded-full ${
                    index === (progressData?.currentStep || 3) - 1
                      ? 'bg-blue-600'
                      : index < (progressData?.currentStep || 3) - 1
                      ? 'bg-blue-400'
                      : 'bg-gray-300'
                  }`}
                />
              ))}
            </div>
          </div>
        </Spin>
      </div>
    </div>
  );
};

export default Question1Page;
