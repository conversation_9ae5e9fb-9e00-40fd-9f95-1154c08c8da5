package everest.onboarding

template presentation wizardIntro {

  state {
    userName: Text
    currentStep: Number<Int>
    totalSteps: Number<Int>
  }

  mode main {
    on header allow view
    on welcomeContent allow view
    on userNameInput allow view, change
    on progress allow view
    on navigationButtons allow view
    allow actions continueToNext, goBack, initializeWizard
  }

  object data-source wizardIntroData {
    shape {
      child Header {
        title (editable: false): Text
        subtitle (editable: false): Text
      }

      child WelcomeContent {
        greeting (editable: false): Text
        `description` (editable: false): Text
        question (editable: false): Text
      }

      child UserNameInput {
        userName: Text
        placeholder (editable: false): Text
        isValid (editable: false): TrueFalse
      }

      child Progress {
        currentStep (editable: false): Number<Int>
        totalSteps (editable: false): Number<Int>
        progressPercentage (editable: false): Number<Decimal>
        stepLabel (editable: false): Text
      }

      child NavigationButtons {
        canGoBack (editable: false): TrueFalse
        canContinue (editable: false): TrueFalse
        backLabel (editable: false): Text
        continueLabel (editable: false): Text
      }
    }

    modifications {
      on UserNameInput support update
    }

    routine continueToNext {
      inputs {
        userName: Text
      }
      outputs {
        success: TrueFalse
        validationMessage: Text
      }
      properties {
        side-effects true
      }
    }

    routine goBack {
      properties {
        side-effects false
      }
    }

    routine initializeWizard {
      outputs {
        currentStep: Number<Int>
        totalSteps: Number<Int>
      }
      properties {
        side-effects false
      }
    }
  }

  struct header {
    data wizardIntroData.Header
    fields *
  }

  struct welcomeContent {
    data wizardIntroData.WelcomeContent
    fields *
  }

  struct userNameInput {
    data wizardIntroData.UserNameInput
    fields *
  }

  struct progress {
    data wizardIntroData.Progress
    fields *
  }

  struct navigationButtons {
    data wizardIntroData.NavigationButtons
    fields *
  }

  delegate action continueToNext to data-source<wizardIntroData>.continueToNext {
    inputs {
      userName = userNameInput.userName
    }
  }

  delegate action goBack to data-source<wizardIntroData>.goBack

  delegate action initializeWizard to data-source<wizardIntroData>.initializeWizard {
    outputs {
      state.currentStep = currentStep
      state.totalSteps = totalSteps
    }
  }
}
