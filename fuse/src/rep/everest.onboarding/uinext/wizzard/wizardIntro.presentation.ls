package everest.onboarding

template presentation wizardIntro {

  state {
    userName: Text
    currentStep: Number<Int>
    totalSteps: Number<Int>
  }

  mode main {
    on wizardHeader allow view
    on welcomeMessage allow view
    on userInput allow view, change
    on progressInfo allow view
    on navigation allow view
    allow actions continue, back
  }

  object data-source wizardData {
    shape {
      child WizardHeader {
        title: Text
        subtitle: Text
      }
      
      child WelcomeMessage {
        greeting: Text
        descriptions: Text
        question: Text
      }
      
      child UserInput {
        userName: Text
        placeholder: Text
      }
      
      child ProgressInfo {
        currentStep: Number<Int>
        totalSteps: Number<Int>
        progressPercentage: Number<Decimal>
      }
      
      child Navigation {
        canGoBack: TrueFalse
        canContinue: TrueFalse
        backLabel: Text
        continueLabel: Text
      }
    }
    
    modifications {
      on UserInput support update
    }
    
    routine continue {
      inputs {
        userName: Text
      }
      outputs {
        success: TrueFalse
        nextStep: Text
      }
      properties {
        side-effects true
      }
    }
    
    routine back {
      outputs {
        success: TrueFalse
        previousStep: Text
      }
      properties {
        side-effects false
      }
    }
    
    routine initialize {
      outputs {
        currentStep: Number<Int>
        totalSteps: Number<Int>
      }
      properties {
        side-effects false
      }
    }
  }

  struct wizardHeader {
    data wizardData.WizardHeader
    fields *
  }
  
  struct welcomeMessage {
    data wizardData.WelcomeMessage
    fields *
  }
  
  struct userInput {
    data wizardData.UserInput
    fields *
  }
  
  struct progressInfo {
    data wizardData.ProgressInfo
    fields *
  }
  
  struct navigation {
    data wizardData.Navigation
    fields *
  }

  delegate action continue to data-source<wizardData>.continue transitions-to view {
    inputs {
      userName = userInput.userName
    }
    outputs {
      state.currentStep = state.currentStep + 1
    }
  }

  delegate action back to data-source<wizardData>.back transitions-to view {
    outputs {
      state.currentStep = state.currentStep - 1
    }
  }

  transition action initialize to data-source<wizardData>.initialize transitions-to view {
    outputs {
      state.currentStep = currentStep
      state.totalSteps = totalSteps
    }
  }
}
