// @i18n:everest.migration.base/migrationBase
// @i18n:everest.fin.integration.expense/expenseIntegration
import type { EvstExtractionProviders } from '@pkg/everest.fin.integration.base/types/enums/ExtractionProviders';
import type { CustomConfig } from '@pkg/everest.migration.base/public/config';
import {
  MigrationType,
  ModelDataload,
} from '@pkg/everest.migration.base/public/types';
import type { uploadPresentationUI } from '@pkg/everest.onboarding/types/presentations/uinext/upload/upload.ui';

type Context = uploadPresentationUI.context & {
  state: {
    objectURN: string;
    providerName: EvstExtractionProviders;
  };
};

export function dateRangeStatus(settings): string {
  const { startDate, endDate } = settings ?? {};

  if (!startDate && !endDate) {
    return '{{expenseIntegration.notSet}}';
  }

  if (startDate && endDate) {
    return `${startDate} - ${endDate}`;
  } else if (startDate) {
    return `${startDate} - /`;
  } else if (endDate) {
    return `/ - ${endDate}`;
  }

  return '{{expenseIntegration.notSet}}';
}

export async function getConfigurations(context: Context) {
  const { state, data } = context;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const config = data.config as any;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const settings = data.settings as any;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const connector = data.connector as any;

  const configs: {
    label: string;
    result: string;
    buttonActions: {
      shape: string;
      label: string;
      onClick: () => void;
      disabled?: boolean;
    }[];
  }[] = [];

  if (config?.templates?.overview?.configurations?.base?.useDateRange) {
    configs.push({
      label: '{{migrationBase.dateRange}}',
      result: dateRangeStatus(settings),
      buttonActions: [
        {
          shape: 'pill',
          label: '{{expenseIntegration.manage}}',
          onClick: () =>
            openConfigModal(
              context,
              '{{expenseIntegration.titleStartDate}}'.replace(
                '$title$',
                config?.templates?.overview?.title
              ),
              '/templates/everest.migration.base/uinext/configurations/dateRange',
              {
                settingsId: settings?.settingsId,
                providerName: state.providerName,
                startDate: settings?.startDate,
                endDate: settings?.endDate,
              }
            ),
        },
      ],
    });
  }

  const customConfigs =
    config?.templates?.overview?.configurations?.custom?.map((c) => ({
      label: c.name,
      result: c.status,
      description: c.description,
      buttonActions: [
        {
          shape: 'pill',
          label: '{{expenseIntegration.manage}}',
          onClick: () =>
            openConfigModal(
              context,
              `${config?.templates?.overview?.title}: ${c.name}`,
              c.templateUrl,
              {
                connectorId: connector?.id,
                ...c.initialState,
              },
              c.size
            ),
        },
      ],
    }));
  if (customConfigs) {
    configs.push(...customConfigs);
  }

  return configs;
}

function openConfigModal(
  ctx: Context,
  title: string,
  templateUrl: string,
  initialState: Record<string, unknown>,
  size: CustomConfig['size'] = 'xsmall'
) {
  const { helpers, actions } = ctx;
  helpers.openModal({
    title,
    template: templateUrl,
    size,
    initialState,
    onClose: async () => {
      await actions.refetchUiModelData();
    },
  });
}

export function onFailWhenExistChange({ state }, value: boolean) {
  state.failWhenExist = value;
}

export async function onChangeProvider(
  { state, presentationClient }: Context,
  value: { selectedLines: EvstExtractionProviders[] }
) {
  const providerName = value.selectedLines?.[0];
  state.providerName = providerName;
  await presentationClient.selectProvider({ providerName });
}

export function onImportModeChange({ state }, value: string) {
  state.importMode = value;
}

export function isproviderName({ state }: Context) {
  return !state.providerName;
}

export function onPreviewDataChange({ state }, value: number) {
  state.isPreviewData = Boolean(value);
}

export function onFileUploadSuccess({ state, helpers }) {
  const files = state.form.fileUpload;
  if (files && files.length > 0) {
    helpers.showNotificationMessage({
      type: 'success',
      message: 'File uploaded successfully',
      duration: 4.5,
    });
  }
}

export function canProceedToNextStep({ state }) {
  if (state.activeStep === 0) {
    return true;
  }

  if (state.activeStep === 1) {
    return state.selectedEntity && state.form.fileUpload?.length > 0;
  }

  return false;
}

export function isProviderSelected({ state }) {
  return !state.providerName;
}

export async function syncObject(context: Context, object: string) {
  const { helpers, state } = context;

  try {
    helpers.showNotificationMessage({
      key: 'sync',
      type: 'loading',
      message: `Syncing ${state.objectURN} data...`,
      duration: 5,
    });

    await sync(context);

    // Your sync logic would go here
    // For now just simulating with a timeout
    await new Promise((resolve) => setTimeout(resolve, 2000));

    helpers.showNotificationMessage({
      key: 'sync',
      type: 'success',
      message: `${object} data synced successfully`,
      duration: 5,
    });

    helpers.closeModal();

    // TODO: Create preview flow
    //   if (state.isPreviewData) {
    // helpers.navigate({
    //   to: '/templates/everest.onboard/uinext/dataBrowser/dataBrowser',
    //   closeCurrentTab: true,
    //   initialState: {
    //     objectType: object,
    //     entity: state.selectedEntity,
    //     importMode: state.importMode,
    //     failWhenExist: state.failWhenExist,
    //   },
    // });

    //     return;
    //   }

    // TODO: Create statiscal overview flow
    // helpers.navigate({
    //   to: '/templates/everest.onboard/uinext/reconciliation/statisticalView',
    //   closeCurrentTab: true,
    // });
    // eslint-disable-next-line unicorn/prefer-optional-catch-binding
  } catch (error) {
    helpers.showNotificationMessage({
      key: 'sync',
      type: 'error',
      message: `Failed to sync ${object} data`,
      duration: 5,
    });
  }
}

export function getLines(context: Context) {
  return context.data.connectors.map((connector) => {
    return {
      value: connector.title,
      label: connector.title,
      selected: connector.title === context.state.providerName,
      avatar: {
        src: connector.picture,
        text: connector.title,
      },
      tag: {
        text: connector.details,
        color: 'shamrock',
        tooltip: {
          text: 'Connected.',
        },
      },
    };
  });
}

export function getConfigurationTitle(context: Context) {
  const { state } = context;
  return `Provider: ${state.providerName}`;
}

export async function sync(context: Context) {
  const { actions } = context;

  if (!validateSyncRequirements(context)) {
    return;
  }

  await performSync(context);

  await actions.refetchUiModelData();
}

function validateSyncRequirements(context: Context): boolean {
  const { state } = context;

  if (!state.providerName && !state.objectURN) {
    showErrorNotification(context, 'No provider selected');
    return false;
  }

  return true;
}

function showErrorNotification({ helpers }: Context, message: string) {
  helpers.showNotificationMessage({
    key: 'loading',
    type: 'error',
    message,
    duration: 4,
  });
}

async function performSync(context: Context) {
  const { state, presentationClient } = context;
  const mapping = findMapping(context);

  return await presentationClient.uploadObject({
    mapping: {
      ...mapping,
      providerName: state.providerName,
    },
    fetching: {
      dataload: ModelDataload.Single,
      migrationType: MigrationType.Semantic,
      startDate: new Date(1, 0, 2024),
      endDate: undefined,
      extractionUUID: context.helpers.uuid(),
    },
  });
}

function findMapping(context: Context) {
  const { state, data } = context;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const mapping = (data?.config?.executions as any)?.metadata?.find((row) => {
    return row.everestModel === state.objectURN;
  });
  return mapping;
}
