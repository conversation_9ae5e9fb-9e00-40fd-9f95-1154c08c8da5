package everest.onboarding

odata presentation onboardingTemplate {

  data-set Questions {
    supported-operations view
    field id: field<OnboardingQuestion.id>
    field uuid: field<OnboardingQuestion.uuid>
    field text: field<OnboardingQuestion.text>
    field title: field<OnboardingQuestion.title>
    field orderIndex: field<OnboardingQuestion.orderIndex>
  }

  data-set QuestionOptions {
    supported-operations view
    field id: field<OnboardingQuestionOption.id>
    field uuid: field<OnboardingQuestionOption.uuid>
    field `description`: field<OnboardingQuestionOption.`description`>
    field `value`: field<OnboardingQuestionOption.`value`>
    field orderIndex: field<OnboardingQuestionOption.orderIndex>
    field isSelected: field<OnboardingQuestionOption.isSelected>
    field onboardingQuestionUUID: field<OnboardingQuestionOption.onboardingQuestionUUID>
    field routeToNextQuestion: field<OnboardingQuestionOption.routeToNextQuestion>
  }

  data-set Steps {
    supported-operations view
    field id: field<OnboardingStep.id>
    field uuid: field<OnboardingStep.uuid>
    field title: field<OnboardingStep.title>
    field orderIndex: field<OnboardingStep.orderIndex>
    field status: field<OnboardingStep.status>
    field finishedAt: field<OnboardingStep.finishedAt>
    field note: field<OnboardingStep.note>
    field primaryLink: field<OnboardingStep.primaryLink>
    field secondaryLink: field<OnboardingStep.secondaryLink>
    field modelURN: field<OnboardingStep.modelURN>
    field `type`: field<OnboardingStep.`type`>
    field categoryUUID: field<OnboardingStep.categoryUUID>
    field isAlwaysVisible: field<OnboardingStep.isAlwaysVisible>
  }

  data-set StepCategories {
    supported-operations view
    field id: field<OnboardingStepCategory.id>
    field uuid: field<OnboardingStepCategory.uuid>
    field title: field<OnboardingStepCategory.title>
    field orderIndex: field<OnboardingStepCategory.orderIndex>
  }

  data-set OptionToStepMappings {
    supported-operations view
    field id: field<OnboardingOptionToStepMapping.id>
    field uuid: field<OnboardingOptionToStepMapping.uuid>
    field onboardingOptionUUID: field<OnboardingOptionToStepMapping.onboardingOptionUUID>
    field onboardinStepUUID: field<OnboardingOptionToStepMapping.onboardinStepUUID>
  }

  action InitializeWizard {
    outputs {
      firstQuestion: object<{
        id: field<OnboardingQuestion.id>
        uuid: field<OnboardingQuestion.uuid>
        text: field<OnboardingQuestion.text>
        title: field<OnboardingQuestion.title>
        orderIndex: field<OnboardingQuestion.orderIndex>
      }>
      totalQuestions: Number<Int>
    }
    properties {
      side-effects false
    }
  }

  action GetQuestionWithOptions {
    inputs {
      questionUuid: field<OnboardingQuestion.uuid>
    }
    outputs {
      question: object<{
        id: field<OnboardingQuestion.id>
        uuid: field<OnboardingQuestion.uuid>
        text: field<OnboardingQuestion.text>
        title: field<OnboardingQuestion.title>
        orderIndex: field<OnboardingQuestion.orderIndex>
      }>
      options: array<object<{
        id: field<OnboardingQuestionOption.id>
        uuid: field<OnboardingQuestionOption.uuid>
        `description`: field<OnboardingQuestionOption.`description`>
        `value`: field<OnboardingQuestionOption.`value`>
        orderIndex: field<OnboardingQuestionOption.orderIndex>
        routeToNextQuestion: field<OnboardingQuestionOption.routeToNextQuestion>
      }>>
    }
    properties {
      side-effects false
    }
  }

  action SaveQuestionResponse {
    inputs {
      questionUuid: field<OnboardingQuestion.uuid>
      selectedOptionUuids: array<field<OnboardingQuestionOption.uuid>>
      selectedOptionValues: array<Text>
    }
    outputs {
      success: TrueFalse
      message: Text
    }
    properties {
      side-effects true
    }
  }

  action GetNextQuestionByRouting {
    inputs {
      currentQuestionUuid: field<OnboardingQuestion.uuid>
      selectedOptionUuids: array<field<OnboardingQuestionOption.uuid>>
    }
    outputs {
      nextQuestion: object<{
        id: field<OnboardingQuestion.id>
        uuid: field<OnboardingQuestion.uuid>
        text: field<OnboardingQuestion.text>
        title: field<OnboardingQuestion.title>
        orderIndex: field<OnboardingQuestion.orderIndex>
      }>
      hasNext: TrueFalse
    }
    properties {
      side-effects false
    }
  }

  action GetPreviousQuestion {
    inputs {
      currentQuestionUuid: field<OnboardingQuestion.uuid>
    }
    outputs {
      previousQuestion: object<{
        id: field<OnboardingQuestion.id>
        uuid: field<OnboardingQuestion.uuid>
        text: field<OnboardingQuestion.text>
        title: field<OnboardingQuestion.title>
        orderIndex: field<OnboardingQuestion.orderIndex>
      }>
      hasPrevious: TrueFalse
    }
    properties {
      side-effects false
    }
  }

  action GetWizardProgress {
    outputs {
      currentStep: Number<Int>
      totalSteps: Number<Int>
      completedQuestions: array<object<{
        questionUuid: field<OnboardingQuestion.uuid>
        selectedOptions: array<Text>
      }>>
      progressPercentage: Number<Decimal>
    }
    properties {
      side-effects false
    }
  }

  action GetAvailableSteps {
    inputs {
      completedResponses: array<object<{
        questionUuid: field<OnboardingQuestion.uuid>
        selectedOptionUuids: array<field<OnboardingQuestionOption.uuid>>
      }>>
    }
    outputs {
      availableSteps: array<object<{
        id: field<OnboardingStep.id>
        uuid: field<OnboardingStep.uuid>
        title: field<OnboardingStep.title>
        orderIndex: field<OnboardingStep.orderIndex>
        status: field<OnboardingStep.status>
        primaryLink: field<OnboardingStep.primaryLink>
        `type`: field<OnboardingStep.`type`>
        categoryTitle: Text
      }>>
    }
    properties {
      side-effects false
    }
  }

  action ResetWizard {
    outputs {
      success: TrueFalse
    }
    properties {
      side-effects true
    }
  }

  action ValidateResponse {
    inputs {
      questionUuid: field<OnboardingQuestion.uuid>
      selectedOptionUuids: array<field<OnboardingQuestionOption.uuid>>
    }
    outputs {
      isValid: TrueFalse
      validationMessage: Text
    }
    properties {
      side-effects false
    }
  }
}
