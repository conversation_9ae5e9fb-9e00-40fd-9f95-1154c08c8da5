# Everest ERP Onboarding Wizard Specifications

## Overview

This document outlines the specifications for the Everest ERP Onboarding Wizard, a guided setup process that helps users configure their Everest system according to their specific business needs and requirements.

## Objectives

- Streamline the initial setup process for new Everest users
- Provide intelligent routing based on user responses
- Auto-configure system components where possible
- Reduce setup complexity through guided workflows
- Ensure all required configurations are completed before system activation

## User Flow Architecture

### Phase 1: User Introduction & Assessment
**Goal**: Establish user context and determine configuration requirements

### Phase 2: Core System Configuration
**Goal**: Configure essential system components based on user needs

### Phase 3: Business Process Setup
**Goal**: Configure business-specific processes and workflows

## Detailed Question Flow Specifications

### Q1: Introduction
**Purpose**: Welcome user and capture basic identification
**Type**: Text input
**Question**: "Hello, Welcome to the Everest systems. I am Onboarding Wizard and I will help you to set everything tailored to your needs. What should I call you?"

**UI Elements**:
- Text input field for name
- Continue button

**Routing**: Direct to Q2
**Validation**: Required field

---

### Q2: Greetings
**Purpose**: Personalize experience and transition to assessment
**Type**: Information display
**Message**: "Nice to meet you {name}! Let's jump into work!"

**UI Elements**:
- Personalized greeting
- Continue button

**Routing**: Direct to Q3

---

### Q3: Existing Systems
**Purpose**: Determine if user has existing data to migrate
**Type**: Single selection
**Question**: "Are you migrating from another accounting/ERP system?"

**Options**:
- Yes, we have existing data to import
- No, this is a new setup

**Routing**:
- Yes → Migration Planning Page
- No → Q4

**Backend Actions**: 
- Set migration flag in user profile
- Prepare data import workflows if needed

---

### Q4: Currency Requirements
**Purpose**: Configure currency settings for the organization
**Type**: Single selection
**Question**: "What currencies do you work with?"

**Options**:
- Single currency → Currency dropdown selector
- Multiple currencies
- Not sure what I need

**Routing**:
- Single currency → Auto-configure currency → Q5
- Multiple currencies → Currency Setup Page
- Not sure → Currency Planning Guide/AI Suggestions

**Backend Actions**:
- For single currency: Auto-configure entity currency settings
- For multiple: Enable multi-currency features
- Store currency configuration for entity setup

---

### Q5: Fiscal Calendar
**Purpose**: Setup fiscal year and accounting periods
**Type**: Action prompt
**Question**: "Setup your Fiscal Calendar?"

**UI Elements**:
- Setup button leading to Fiscal Calendar Setup Page
- Information about fiscal calendar importance

**Routing**: Direct to Fiscal Calendar Setup Page

**Backend Integration**: 
- Links to fiscal calendar creation process
- Must be completed before entity activation

---

### Q6: Chart of Accounts - Structure Type
**Purpose**: Determine CoA setup approach
**Type**: Single selection
**Question**: "Do you need industry-specific chart of accounts or you have your own?"

**Options**:
- Custom chart of accounts
- Industry-specific template
- Migration from existing structure
- Not sure / Need help determining

**Routing**:
- Custom → Chart of Accounts Setup Page → Q9
- Industry-specific → Q7
- Migration → File Upload Page → Q9
- Not sure → Chart of Accounts Planning Guide/AI Suggestions

**Backend Actions**:
- Set CoA creation method flag
- Prepare appropriate setup workflows

---

### Q7: Industry Type
**Purpose**: Select appropriate industry template for CoA
**Type**: Single selection
**Question**: "What type of industry does your company operate in?"

**Options**:
- Technology/Software
- Manufacturing
- Retail/E-commerce
- Professional Services (Law, Consulting, etc.)
- Healthcare
- Construction/Real Estate
- Non-profit Organization
- Financial Services
- Hospitality/Food Service
- Other (please specify)
- Not sure / Need help determining

**Routing**:
- Any industry selection → Q8
- Not sure → Chart of Accounts Planning Guide/AI Suggestions

**Backend Actions**:
- Store industry type for CoA template selection
- Prepare industry-specific account templates

---

### Q8: Account Number Length
**Purpose**: Configure account numbering system
**Type**: Single selection
**Question**: "How long do you want your account number to be?"

**Options**:
- 6 digits (Up to 4 levels of account hierarchy)
- 7 digits (Up to 5 levels of account hierarchy)
- 8 digits (Up to 6 levels of account hierarchy)
- 9 digits (Up to 7 levels of account hierarchy)
- 10 digits (Up to 8 levels of account hierarchy)
- Not sure / Need help determining

**Routing**:
- Any digit selection → AI Generated CoA → Q9
- Not sure → Chart of Accounts Planning Guide/AI Suggestions

**Backend Actions**:
- Configure automatic account numbering
- Generate industry-specific CoA based on Q7 and Q8 responses
- Set up account hierarchy structure

---

### Q9: Company Structure
**Purpose**: Determine entity setup requirements
**Type**: Single selection
**Question**: "How is your company organized?"

**Options**:
- Single legal entity
- Multiple entities (subsidiaries, international offices, etc.)
- Not sure / Need help determining

**Routing**:
- Single entity → Entity Setup Page
- Multiple entities → Multiple Entities Setup Page
- Not sure → Entity Planning Guide/AI Suggestions

**Backend Actions**:
- Configure entity hierarchy
- Set up consolidation requirements if multiple entities
- Prepare entity-specific configurations

## Supporting Pages and Workflows

### Migration Planning Page
**Purpose**: Handle data import from existing systems
**Features**:
- File upload interface
- Data mapping wizard
- Validation and error handling
- Progress tracking

### Currency Setup Page
**Purpose**: Configure multiple currency support
**Features**:
- Currency selection interface
- Exchange rate configuration
- Default currency designation
- Functional vs. transactional currency setup

### Currency Planning Guide/AI Suggestions
**Purpose**: Help users determine currency needs
**Features**:
- Business questionnaire
- AI-powered recommendations
- Currency requirement analysis
- Integration with main flow

### Fiscal Calendar Setup Page
**Purpose**: Configure fiscal year and accounting periods
**Features**:
- Calendar period type selection (Calendar Months)
- First starting month configuration
- Fiscal year preview
- Entity assignment interface

### Chart of Accounts Setup Page
**Purpose**: Manual CoA creation interface
**Features**:
- Account creation wizard
- Hierarchy management
- Import/export capabilities
- Validation and error checking

### Chart of Accounts Planning Guide/AI Suggestions
**Purpose**: Assist with CoA structure decisions
**Features**:
- Industry analysis
- Hierarchy recommendations
- Best practices guidance
- Template suggestions

### Entity Setup Page
**Purpose**: Configure single entity
**Features**:
- Entity information form
- Address and tax ID setup
- Currency and calendar assignment
- CoA assignment
- Offset account configuration

### Multiple Entities Setup Page
**Purpose**: Configure entity hierarchy
**Features**:
- Entity hierarchy builder
- Parent-child relationship management
- Consolidation setup
- Bulk entity configuration

## Technical Requirements

### Backend Integration Points
- Entity management system
- Chart of accounts configuration
- Fiscal calendar setup
- Currency management
- User profile management
- Migration tools interface

### Data Validation Rules
- Required field validation
- Business logic validation
- Data consistency checks
- Integration validation

### AI Integration Points
- Industry-specific recommendations
- CoA structure suggestions
- Currency requirement analysis
- Entity structure recommendations

## User Experience Requirements

### Progress Tracking
- Visual progress indicator
- Step completion status
- Ability to save and resume
- Back navigation capability

### Help and Guidance
- Contextual help for each question
- Tooltips and explanations
- Link to detailed documentation
- AI-powered assistance

### Error Handling
- Clear error messages
- Validation feedback
- Recovery options
- Support contact information

## Success Criteria

### Completion Metrics
- User successfully completes onboarding flow
- All required configurations are set
- System is ready for business operations
- User can proceed to core ERP functions

### Performance Metrics
- Reduced setup time compared to manual configuration
- Decreased support tickets during initial setup
- Higher user satisfaction scores
- Faster time-to-value for new customers

## Future Enhancements (Backlog)

### Additional Question Areas
- Business Model Configuration
- Customer Base Management
- Invoicing Preferences
- Email Configuration
- Priority-Based Setup

### Advanced Features
- Multi-language support
- Custom branding
- Advanced AI recommendations
- Integration with external systems
- Automated testing and validation

## Implementation Notes

### Phase 1 Priority
- Core question flow (Q1-Q9)
- Basic routing logic
- Essential backend integrations
- Minimal viable UI

### Phase 2 Enhancements
- AI-powered recommendations
- Advanced validation
- Enhanced user experience
- Comprehensive help system

### Phase 3 Advanced Features
- Migration tools integration
- Advanced entity management
- Custom configuration options
- Analytics and reporting

## Appendix

### Related Documentation
- Everest Entity Setup Guide
- Chart of Accounts Configuration
- Fiscal Calendar Management
- Currency Setup Instructions
- Customer Configuration Guide

### API Requirements
- Entity creation/management APIs
- CoA setup APIs
- Fiscal calendar APIs
- Currency configuration APIs
- User profile management APIs
