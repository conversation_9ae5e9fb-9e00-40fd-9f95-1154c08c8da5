/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// ******************************************************************************
// Generated Everest Types
// ******************************************************************************
import type ICustomerIssues from "@pkg/everest.base.issues.interface/types/CustomerIssuesInterface";
import { type IConnectivityLayerService } from "@everestsystems/content-core";
import type { EvstConnectivityLayerCallOptions as everest_appserver_primitive_metadata_interfaces_ConnectivityLayerCallOptions } from "@pkg/everest.appserver/types/primitives/metadata/interfaces/ConnectivityLayerCallOptions";
import type { EvstJSON as everest_appserver_primitive_JSON } from "@pkg/everest.appserver/types/primitives/JSON";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstExternalId as everest_appserver_primitive_ExternalId } from "@pkg/everest.appserver/types/primitives/ExternalId";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";

export const customerIssuesConnectivity: ICustomerIssues = {
  createIssue(connectivityLayer: IConnectivityLayerService, options: everest_appserver_primitive_metadata_interfaces_ConnectivityLayerCallOptions, title: everest_appserver_primitive_Text, body: everest_appserver_primitive_Text, files: everest_appserver_primitive_JSON[], labels: everest_appserver_primitive_JSON): Promise<everest_appserver_primitive_JSON> {
    return connectivityLayer.callInterface('everest.base.issues.interface/CustomerIssues', 'createIssue', options, title, body, files, labels) as Promise<everest_appserver_primitive_JSON>;
  },
  createGithubIssue(connectivityLayer: IConnectivityLayerService, options: everest_appserver_primitive_metadata_interfaces_ConnectivityLayerCallOptions, title: everest_appserver_primitive_Text, body: everest_appserver_primitive_Text, labels: everest_appserver_primitive_Text[], repositoryName?: everest_appserver_primitive_Text): Promise<everest_appserver_primitive_JSON> {
    return connectivityLayer.callInterface('everest.base.issues.interface/CustomerIssues', 'createGithubIssue', options, title, body, labels, repositoryName) as Promise<everest_appserver_primitive_JSON>;
  },
  createIssueComment(connectivityLayer: IConnectivityLayerService, options: everest_appserver_primitive_metadata_interfaces_ConnectivityLayerCallOptions, description: everest_appserver_primitive_Text, relatedIssueId: everest_appserver_primitive_ExternalId): Promise<everest_appserver_primitive_JSON> {
    return connectivityLayer.callInterface('everest.base.issues.interface/CustomerIssues', 'createIssueComment', options, description, relatedIssueId) as Promise<everest_appserver_primitive_JSON>;
  },
  fetchIssueComments(connectivityLayer: IConnectivityLayerService, options: everest_appserver_primitive_metadata_interfaces_ConnectivityLayerCallOptions, id: everest_appserver_primitive_ExternalId): Promise<everest_appserver_primitive_JSON> {
    return connectivityLayer.callInterface('everest.base.issues.interface/CustomerIssues', 'fetchIssueComments', options, id) as Promise<everest_appserver_primitive_JSON>;
  },
  deleteIssues(connectivityLayer: IConnectivityLayerService, options: everest_appserver_primitive_metadata_interfaces_ConnectivityLayerCallOptions, id: everest_appserver_primitive_ExternalId): Promise<everest_appserver_primitive_JSON> {
    return connectivityLayer.callInterface('everest.base.issues.interface/CustomerIssues', 'deleteIssues', options, id) as Promise<everest_appserver_primitive_JSON>;
  },
  updateSingleIssue(connectivityLayer: IConnectivityLayerService, options: everest_appserver_primitive_metadata_interfaces_ConnectivityLayerCallOptions, id: everest_appserver_primitive_ExternalId, title: everest_appserver_primitive_Text, body: everest_appserver_primitive_Text, files: everest_appserver_primitive_JSON[], labels: everest_appserver_primitive_JSON, status: everest_appserver_primitive_Text): Promise<everest_appserver_primitive_JSON> {
    return connectivityLayer.callInterface('everest.base.issues.interface/CustomerIssues', 'updateSingleIssue', options, id, title, body, files, labels, status) as Promise<everest_appserver_primitive_JSON>;
  },
  updateIssues(connectivityLayer: IConnectivityLayerService, options: everest_appserver_primitive_metadata_interfaces_ConnectivityLayerCallOptions, id: everest_appserver_primitive_ExternalId, title: everest_appserver_primitive_Text, body: everest_appserver_primitive_Text, files: everest_appserver_primitive_JSON[], labels: everest_appserver_primitive_JSON, status: everest_appserver_primitive_Text): Promise<everest_appserver_primitive_JSON> {
    return connectivityLayer.callInterface('everest.base.issues.interface/CustomerIssues', 'updateIssues', options, id, title, body, files, labels, status) as Promise<everest_appserver_primitive_JSON>;
  },
  fetchIssues(connectivityLayer: IConnectivityLayerService, options: everest_appserver_primitive_metadata_interfaces_ConnectivityLayerCallOptions, issueNumbers: everest_appserver_primitive_Number[]): Promise<everest_appserver_primitive_JSON> {
    return connectivityLayer.callInterface('everest.base.issues.interface/CustomerIssues', 'fetchIssues', options, issueNumbers) as Promise<everest_appserver_primitive_JSON>;
  },
  fetchRecentlyUpdatedIssues(connectivityLayer: IConnectivityLayerService, options: everest_appserver_primitive_metadata_interfaces_ConnectivityLayerCallOptions, hoursBack?: everest_appserver_primitive_Number): Promise<everest_appserver_primitive_JSON> {
    return connectivityLayer.callInterface('everest.base.issues.interface/CustomerIssues', 'fetchRecentlyUpdatedIssues', options, hoursBack) as Promise<everest_appserver_primitive_JSON>;
  },
  getProviderNames(connectivityLayer: IConnectivityLayerService): Promise<everest_appserver_primitive_Text[]> {
    return connectivityLayer.getProviderNames('everest.base.issues.interface/CustomerIssues');
  }
};
