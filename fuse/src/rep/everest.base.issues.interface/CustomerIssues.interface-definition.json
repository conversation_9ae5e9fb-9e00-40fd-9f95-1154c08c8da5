{"description": "Issue API interface", "actions": [{"actionName": "createIssue", "remote": "urn:evst:everest:base:remote:cas", "inputParameters": [{"name": "title", "type": "urn:evst:everest:appserver:primitive:Text"}, {"name": "body", "type": "urn:evst:everest:appserver:primitive:Text"}, {"name": "files", "type": "urn:evst:everest:appserver:primitive:JSON[]"}, {"name": "labels", "type": "urn:evst:everest:appserver:primitive:JSON"}], "outputValues": [{"name": "result", "type": "urn:evst:everest:appserver:primitive:JSON"}]}, {"actionName": "createGithubIssue", "remote": "urn:evst:everest:base:remote:cas", "inputParameters": [{"name": "title", "type": "urn:evst:everest:appserver:primitive:Text"}, {"name": "body", "type": "urn:evst:everest:appserver:primitive:Text"}, {"name": "labels", "type": "urn:evst:everest:appserver:primitive:Text[]"}, {"name": "repositoryName", "type": "urn:evst:everest:appserver:primitive:Text", "optional": true}], "outputValues": [{"name": "result", "type": "urn:evst:everest:appserver:primitive:JSON"}]}, {"actionName": "createIssueComment", "remote": "urn:evst:everest:base:remote:cas", "inputParameters": [{"name": "description", "type": "urn:evst:everest:appserver:primitive:Text"}, {"name": "relatedIssueId", "type": "urn:evst:everest:appserver:primitive:ExternalId"}], "outputValues": [{"name": "result", "type": "urn:evst:everest:appserver:primitive:JSON"}]}, {"actionName": "fetchIssueComments", "remote": "urn:evst:everest:base:remote:cas", "inputParameters": [{"name": "id", "type": "urn:evst:everest:appserver:primitive:ExternalId"}], "outputValues": [{"name": "result", "type": "urn:evst:everest:appserver:primitive:JSON"}]}, {"actionName": "deleteIssues", "remote": "urn:evst:everest:base:remote:cas", "inputParameters": [{"name": "id", "type": "urn:evst:everest:appserver:primitive:ExternalId"}], "outputValues": [{"name": "result", "type": "urn:evst:everest:appserver:primitive:JSON"}]}, {"actionName": "updateSingleIssue", "remote": "urn:evst:everest:base:remote:cas", "inputParameters": [{"name": "id", "type": "urn:evst:everest:appserver:primitive:ExternalId"}, {"name": "title", "type": "urn:evst:everest:appserver:primitive:Text"}, {"name": "body", "type": "urn:evst:everest:appserver:primitive:Text"}, {"name": "files", "type": "urn:evst:everest:appserver:primitive:JSON[]"}, {"name": "labels", "type": "urn:evst:everest:appserver:primitive:JSON"}, {"name": "status", "type": "urn:evst:everest:appserver:primitive:Text"}], "outputValues": [{"name": "result", "type": "urn:evst:everest:appserver:primitive:JSON"}]}, {"actionName": "updateIssues", "remote": "urn:evst:everest:base:remote:cas", "inputParameters": [{"name": "id", "type": "urn:evst:everest:appserver:primitive:ExternalId"}, {"name": "title", "type": "urn:evst:everest:appserver:primitive:Text"}, {"name": "body", "type": "urn:evst:everest:appserver:primitive:Text"}, {"name": "files", "type": "urn:evst:everest:appserver:primitive:JSON[]"}, {"name": "labels", "type": "urn:evst:everest:appserver:primitive:JSON"}, {"name": "status", "type": "urn:evst:everest:appserver:primitive:Text"}], "outputValues": [{"name": "result", "type": "urn:evst:everest:appserver:primitive:JSON"}]}, {"actionName": "fetchIssues", "remote": "urn:evst:everest:base:remote:cas", "inputParameters": [{"name": "issueNumbers", "type": "urn:evst:everest:appserver:primitive:Number[]", "description": "An array of GitHub issue numbers to fetch details for."}], "outputValues": [{"name": "result", "type": "urn:evst:everest:appserver:primitive:JSON"}]}, {"actionName": "fetchRecentlyUpdatedIssues", "remote": "urn:evst:everest:base:remote:cas", "inputParameters": [{"name": "hoursBack", "type": "urn:evst:everest:appserver:primitive:Number", "description": "Number of hours back to look for updated issues (default: 24)", "optional": true}], "outputValues": [{"name": "result", "type": "urn:evst:everest:appserver:primitive:JSON"}]}]}