{"version": 2, "uimodel": {"state": {"mode": "view", "adminEdit": false}, "nodes": {"headcountPlanDetail": {"type": "struct", "model": "urn:evst:everest:hr/planning:model/node:HeadcountPlanDetail", "query": {"where": {"id": "@state:param.id"}}, "fieldList": ["id", "departmentId", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "headcountCap", "remainingVacancies", "HeadcountPlanDetail-Department.departmentName", "headcountPlanId", "status", "budgetOwner", "HeadcountPlanDetail-HeadcountPlan.startDate"]}, "headcountPlan": {"type": "struct", "joinKey": "id-headcountPlanId", "parent": "headcountPlanDetail", "model": "urn:evst:everest:hr/planning:model/node:HeadcountPlan", "fieldList": ["id", "budgetAmount", "name", "currency", "startDate", "allocationType", "<PERSON><PERSON><PERSON><PERSON>"]}, "headcountPlanLine": {"type": "list", "model": "urn:evst:everest:hr/planning:model/node:HeadcountPlanLine", "query": "@controller:getHeadcountLineQuery()", "fieldList": ["id", "departmentId", "positionId", "entityId", "locationId", "expectedFillDate", "currency", "vacancies", "filledHeadcounts", "remainingHeadcounts", "forecastCost", "headcountOwner", "minimum", "maximum", "midpoint", "HeadcountPlanLine-Job.employeeId", "headcountPlanDetailId"]}, "headcountLineJobMapping": {"type": "list", "modelId": "everest.hr.planning/HeadcountPlanLineJobMappingModel.HeadcountPlanLineJobMapping", "parent": "headcountPlanLine", "joinKey": "id-headcountPlanLineId", "fieldList": ["id", "jobId", "headcountPlanLineId", "recruitmentId", "HeadcountPlanLineJobMapping-Recruitment.deadline", "recruitmentStatus", "recruitmentNumber"]}, "isAdmin": {"type": "struct", "modelId": "everest.hr.base/EmployeeModel.Employee", "isAdmin": {}}, "currentEmployee": {"type": "struct", "modelId": "everest.hr.base/EmployeeModel.Employee", "getCurrentEmployee": {}}, "associatedCompBand": {"type": "struct", "model": "urn:evst:everest:hr/base:model/node:CompensationBand", "findBestMatchedCompensationBand": {"departmentId": "@binding:headcountPlanDetail.departmentId"}, "fieldList": ["id", "positionId", "departmentId", "entityId", "locationId", "minimum", "maximum", "midpoint", "unit", "currency", "forecast"]}}}, "uicontroller": ["headcountPlanDetail.uicontroller.ts", "budgetAllocation.uicontroller.ts"], "uiview": {"templateType": "details", "title": "@controller:getTitle()", "i18n": ["everest.hr.base/hrbase", "headcountPlan"], "config": {"allowRefreshData": "true", "autoRefreshData": "true"}, "header": {"content": {"title": "@controller:getTitle()", "status": "@binding:headcountPlanDetail.status"}}, "sections": {"content": [{"component": "FieldGroup", "section": {"grid": {"size": "12"}, "editing": "@controller:isAdminEdit()"}, "props": {"type": "primary", "elements": [{"label": "{{department}}", "value": "@binding:headcountPlanDetail.departmentId"}, {"component": "InputNumber", "value": "@binding:headcountPlanDetail.allocatedBudget", "type": "amount", "parseAs": "currency", "format": "@binding:headcountPlan.currency"}, {"component": "InputNumber", "value": "@binding:headcountPlanDetail.remainingBudget", "type": "amount", "parseAs": "currency", "format": "@binding:headcountPlan.currency"}, {"value": "@binding:headcountPlanDetail.headcountCap", "component": "Input"}, {"value": "@binding:headcountPlanDetail.remainingVacancies", "component": "Input"}, {"value": "@binding:headcountPlanDetail.budgetOwner"}]}}, {"component": "Table", "section": {"title": "", "grid": {"size": "12"}, "actions": [{"label": "{{headcountPlan.add.headcount}}", "variant": "primary", "onClick": "@controller:BudgetAllocation.onAddHeadcountLineClick", "visible": "@controller:showAddLineButton()"}, {"label": "{{edit.headcount.lines}}", "variant": "secondary", "onClick": "@controller:setEditMode", "visible": "@controller:showAddLineButton()"}, {"label": "{{save.changes}}", "variant": "primary", "visible": "@controller:isEditMode()", "onClick": "@controller:setEditMode"}, {"label": "{{cancel}}", "variant": "secondary", "onClick": "@controller:setViewMode", "visible": "@controller:isEditMode()"}], "editing": "@controller:isEditingTable()"}, "props": {"rowNavigation": "/templates/everest.hr.planning/uinext/headcountPlanLine", "data": "@binding:headcountPlanLine", "variant": "white", "addRows": 1, "rowActions": {"columnPosition": 0, "width": "85", "initialDepth": 0, "actions": [{"label": "{{edit}}", "onClick": "@controller:onHeadcountLineClick"}, {"label": "{{delete}}", "onClick": "@controller:onDeleteHeadcountLineClick"}, {"label": "{{view.in.orgChart}}", "onClick": "@controller:openOrgChart"}]}, "columns": [{"field": "positionId", "fieldProps": {"nullContent": "custom", "customNullContent": "{{all.position}}"}}, {"field": "entityId", "fieldProps": {"nullContent": "custom", "customNullContent": "{{all.entity}}"}}, {"field": "locationId", "fieldProps": {"nullContent": "custom", "customNullContent": "{{all.location}}"}}, {"field": "expectedFillDate", "fieldProps": {"picker": "month", "format": "MMM yyyy"}}, {"field": "midpoint", "type": "amount", "fieldProps": {"initialValue": "@binding:associatedCompBand.midpoint", "parseAs": "currency", "format": "@binding:headcountPlan.currency"}}, {"field": "forecastCost", "type": "amount", "fieldProps": {"parseAs": "currency", "format": "@binding:headcountPlan.currency"}}, "headcountOwner"]}}]}, "actions": {"content": [{"label": "{{save}}", "variant": "primary", "onClick": "@controller:onSaveClick", "visible": "@controller:isAdminEdit()"}, {"label": "{{cancel}}", "onClick": "@controller:setAdminView", "visible": "@controller:isAdminEdit()"}, {"label": "{{actions}}", "variant": "primary", "actions": [{"label": "{{edit.plan.details}}", "onClick": "@controller:setAdminEdit", "visible": "@controller:isAdminView()"}, {"label": "{{unsubmit.budget.plan}}", "onClick": "@controller:unsubmitPlan", "visible": "@controller:isSubmitted()", "confirmation": {"description": "{{unsubmit.budget.description}}", "message": "{{unsubmit.budget.message}}"}}, {"label": "{{submit.for.review}}", "onClick": "@controller:submitPlan", "visible": "@controller:isUnsubmitted()", "confirmation": {"description": "{{submit.budget.description}}", "message": "{{submit.budget.message}}"}}, {"label": "{{approve.plan}}", "onClick": "@controller:approvePlan", "visible": "@controller:isSubmittedAndAdmin()"}, {"label": "{{reject.plan}}", "onClick": "@controller:rejectPlan", "visible": "@controller:isSubmittedAndAdmin()"}]}]}}}