{"version": 2, "uimodel": {"state": {"copyBudgetAllocation": false, "copyOpenHeadcount": false}, "nodes": {"headcountPlan": {"type": "struct", "modelId": "everest.hr.planning/HeadcountPlanModel.HeadcountPlan", "fieldList": ["id", "name", "budgetAmount"]}, "baseHeadcountPlan": {"type": "struct", "modelId": "everest.hr.planning/HeadcountPlanModel.HeadcountPlan", "query": {"where": {"id": "@state:headcountPlanId"}}, "fieldList": ["id", "name", "budgetAmount"]}}}, "uicontroller": ["createScenario.uicontroller.ts"], "uiview": {"i18n": ["everest.hr.base/hrbase", "headcountPlan"], "title": "@controller:getSectionTitle()", "sections": [{"component": "ActionGroup", "section": {"editing": true, "grid": {"size": "12"}}, "props": {"spacing": "small", "buttonActionsPosition": "right", "descriptionPosition": "rightSide", "data": "@binding:headcountPlan", "lines": [{"label": "{{headcountPlan.name}} *", "field": "name", "fieldProps": {"component": "Input", "value": "@binding:headcountPlan.name"}}, {"label": "{{headcountPlan.budgetAmount}} *", "field": "budgetAmount", "fieldProps": {"component": "InputNumber", "value": "@binding:headcountPlan.budgetAmount", "type": "amount", "initialValue": "@binding:baseHeadcountPlan.budgetAmount"}}, {"label": "{{headcountPlan.copy.budgetAllocation}}", "field": "copyBudgetAllocation", "fieldProps": {"component": "Checkbox", "value": "@state:copyBudgetAllocation", "onChange": "@controller:onCopyBudgetAllocationChange"}}, {"label": "{{headcountPlan.copy.openHeadcount}}", "field": "copyOpenHeadcount", "fieldProps": {"component": "Checkbox", "isDisabled": "@controller:isDisabledCopyOpenHeadcount()", "value": "@state:copyOpenHeadcount", "onChange": "@controller:onCopyOpenHeadcountChange"}}]}}], "actions": [{"variant": "primary", "label": "{{create}}", "onClick": "@controller:onCreateScenarioClick"}]}}