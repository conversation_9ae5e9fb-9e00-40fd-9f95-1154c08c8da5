import { PlainDate } from '@everestsystems/datetime';
import type { BudgetAllocationUiTemplate } from '@pkg/everest.hr.planning/types/uiTemplates/uinext/budgetAllocation.ui';

import { EvstSubmissionStatus } from '../types/enums/SubmissionStatus';
// @i18n:everest.hr.base/hrbase
// @i18n:headcountPlan

type Context = BudgetAllocationUiTemplate.BudgetAllocationContext & {
  state: {
    headcountPlanDetailId: number;
    disableDepartment: boolean;
    departmentId: number;
    adminEdit: boolean;
  };
};

export function getTitle(context: Context) {
  const { headcountPlanDetail, headcountPlan } = context.data;
  return `${headcountPlan?.name}: ${headcountPlanDetail?.['HeadcountPlanDetail-Department']?.departmentName}`;
}

export namespace BudgetAllocation {
  export function onAddHeadcountLineClick({ data, helpers, actions }: Context) {
    console.log('test');
    const { headcountPlanDetail, headcountPlan } = data;
    const startDate = PlainDate.from(headcountPlan.startDate); // January 1st of the given year
    const endDate = startDate.plus({ years: 1 }).minus({ days: 1 }); // Last Date of the given year
    helpers.openModal({
      template: '/templates/everest.hr.planning/uinext/headcountPlanLineModal',
      onClose: actions.refetchUiTemplate,
      initialState: {
        targetCurrency: headcountPlan.currency,
        mode: 'create',
        departmentId: headcountPlanDetail.departmentId,
        minDate: new Date(startDate.toISO()),
        maxDate: new Date(endDate.toISO()),
        currency: headcountPlan.currency,
        disableDepartment: true,
        headcountPlanDetailId: headcountPlanDetail.id,
        title: '{{headcountPlane.line.add}}',
      },
    });
  }
}

export function isEditingTable(context: Context) {
  return context.state.mode === 'edit';
}

export async function setEditMode({ state, actions }: Context) {
  if (state.mode === 'edit') {
    await actions.submit({});
    state.mode = 'view';
  } else {
    state.mode = 'edit';
  }
}

export function isEditMode(context: Context) {
  return context.state.mode === 'edit';
}

export function setViewMode({ state }: Context) {
  state.mode = 'view';
}

export async function submitPlan({ actions, helpers, state }: Context) {
  const result = await actions.run({
    headcountPlanDetail: {
      action: 'submitHeadcountPlanDetail',
      data: {
        headcountPlanDetailId: state.id,
      },
    },
  });
  if (result.error) {
    helpers.showToast({
      type: 'error',
      title: '{{error}}',
      message: '{{error.submit.plan.title}}',
    });
  } else {
    helpers.showToast({
      type: 'success',
      title: '{{success}}',
      message: '{{success.submit.plan.title}}',
    });
    await actions.refetchUiModelData();
  }
}

export async function unsubmitPlan({ actions, helpers, state }: Context) {
  console.log(state.id);
  const result = await actions.run({
    headcountPlanDetail: {
      action: 'update',
      data: {
        id: state.id,
        status: EvstSubmissionStatus.Unsubmitted,
      },
    },
  });
  if (result.error) {
    helpers.showToast({
      type: 'error',
      title: '{{error}}',
      message: '{{error.unsubmit.plan.title}}',
    });
  } else {
    helpers.showToast({
      type: 'success',
      title: '{{success}}',
      message: '{{success.unsubmit.plan.title}}',
    });
    await actions.refetchUiModelData();
  }
}

export function isUnsubmitted(context: Context) {
  const { headcountPlanDetail, currentEmployee } = context.data;
  return headcountPlanDetail?.status === EvstSubmissionStatus.Unsubmitted;
}

export function isSubmitted(context: Context) {
  const { headcountPlanDetail, currentEmployee, isAdmin } = context.data;
  return headcountPlanDetail?.status !== EvstSubmissionStatus.Unsubmitted;
}

export function showAddLineButton(context: Context) {
  const { state, data } = context;
  return (
    state.mode === 'view' &&
    data.headcountPlanDetail?.status === EvstSubmissionStatus?.Unsubmitted
  );
}

export function isSubmittedAndAdmin(context: Context) {
  const { isAdmin, headcountPlanDetail } = context.data;
  return (
    isAdmin && headcountPlanDetail?.status === EvstSubmissionStatus.Submitted
  );
}

// export function showActions(context: Context) {
//   return (
//     (isSubmittedAndAdmin(context) ||
//       isUnsubmittedAndBudgetOwner(context) ||
//       isSubmittedAndBudgetOwner(context)) &&
//     !context.state.adminEdit
//   );
// }

export async function approvePlan(context: Context) {
  const { actions, helpers, state } = context;
  const result = await actions.run({
    headcountPlanDetail: {
      action: 'reviewHeadcountPlanDetail',
      data: {
        headcountPlanDetailId: state.id,
        status: EvstSubmissionStatus.Approved,
      },
    },
  });
  if (result.error) {
    helpers.showToast({
      type: 'error',
      title: '{{error}}',
      message: '{{error.approve.headcount.plan.detail}}',
    });
  } else {
    helpers.showToast({
      type: 'success',
      title: '{{success}}',
      message: '{{success.approve.headcount.plan.detail}}',
    });
    await actions.refetchUiModelData();
  }
}

export async function rejectPlan(context: Context) {
  const { actions, helpers, state } = context;
  const result = await actions.run({
    headcountPlanDetail: {
      action: 'reviewHeadcountPlanDetail',
      data: {
        headcountPlanDetailId: state.id,
        status: EvstSubmissionStatus.Rejected,
      },
    },
  });
  if (result.error) {
    helpers.showToast({
      type: 'error',
      title: '{{error}}',
      message: '{{error.reject.headcount.plan.detail}}',
    });
  } else {
    helpers.showToast({
      type: 'success',
      title: '{{success}}',
      message: '{{success.reject.headcount.plan.detail}}',
    });
    await actions.refetchUiModelData();
  }
}

export function setAdminEdit(context: Context) {
  context.state.adminEdit = true;
}

export function isAdminView(context: Context) {
  return context.data.isAdmin && !context.state.adminEdit;
}

export async function onSaveClick(context: Context) {
  const { actions, helpers, data, state } = context;
  const { departmentId, allocatedBudget, headcountCap, budgetOwner } =
    data.headcountPlanDetail;
  const result = await actions.run({
    headcountPlanDetail: {
      action: 'update',
      data: {
        id: state.id,
        departmentId,
        allocatedBudget,
        headcountCap,
        budgetOwner,
      },
    },
  });
  if (result.error) {
    helpers.showToast({
      type: 'error',
      title: '{{error}}',
      message: '{{error.edit.headcount.plan.detail}}',
    });
  } else {
    helpers.showToast({
      type: 'success',
      title: '{{success}}',
      message: '{{success.edit.headcount.plan.detail}}',
    });
    state.adminEdit = false;
    await actions.refetchUiModelData();
  }
}

export function isAdminEdit(context: Context) {
  return context.data.isAdmin && context.state.adminEdit === true;
}

export function setAdminView(context: Context) {
  context.state.adminEdit = false;
}

export function getHeadcountLineQuery({ data }: Context) {
  return data?.headcountPlanDetail?.departmentId
    ? {
        where: {
          departmentId: data.headcountPlanDetail.departmentId,
        },
        orderBy: [
          {
            field: 'expectedFillDate',
            ordering: 'asc',
          },
        ],
      }
    : undefined;
}
