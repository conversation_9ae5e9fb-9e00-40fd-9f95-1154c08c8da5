// @i18n:everest.hr.base/hrbase
// @i18n:headcountPlan
import { UILifecycleHooks } from '@everestsystems/content-core';
import type { PlainDateLike } from '@everestsystems/datetime';
import { PlainDate } from '@everestsystems/datetime';
import { Decimal } from '@everestsystems/decimal';
import type { EvstCurrencyCodeType } from '@pkg/everest.base/types/primitives/CurrencyCodeType';
import { EvstHeadcountLineStatus } from '@pkg/everest.hr.planning/types/enums/HeadcountLineStatus';
import type { HeadcountPlanLineModalUiTemplate } from '@pkg/everest.hr.planning/types/uiTemplates/uinext/headcountPlanLineModal.ui';

type HeadcountPlanLineModalContext =
  HeadcountPlanLineModalUiTemplate.HeadcountPlanLineModalContext;

type Context = HeadcountPlanLineModalContext & {
  state: {
    departmentId: number;
    targetCurrency: EvstCurrencyCodeType;
    mode: 'create' | 'edit' | 'view';
    targetYear: number;
    headcountPlanDetailId: number;
    id?: number;
    minDate: Date;
    maxDate: Date;
    date: PlainDate;
    remainingVacancies: number;
    vacancies: number;
    prevCompBandMidPoint?: Decimal;
    headcountPlanId: number;
  };
};

UILifecycleHooks.onUpdate((ctx: Context) => {
  const { data, helpers, sharedState } = ctx;
  const { associatedCompBand, headcountPlanLine } = data;
  const nodeRef = data.headcountPlanLine?._nodeReference;
  const instance = sharedState.getNodeInstance(nodeRef);
  if (!headcountPlanLine?.midpoint && associatedCompBand?.midpoint) {
    helpers.set(instance, 'data.midpoint', associatedCompBand.midpoint);
  }
});

export function getBtnLabel({ state }: Context) {
  return state.mode === 'create'
    ? '{{create}}'
    : state.mode === 'edit'
      ? '{{save}}'
      : '{{edit}}';
}

export async function onPrimaryBtnClick(ctx: Context) {
  await (ctx.state.id ? onSaveClick(ctx) : onCreateClick(ctx));
}

export async function onCreateClick({
  helpers,
  actions,
  data,
  state,
}: Context) {
  let headcountPlanDetailId = data?.headcountPlanDetail?.id;
  if (!headcountPlanDetailId) {
    const { headcountPlanDetail } = await actions.run({
      headcountPlanDetail: {
        action: 'query',
        data: {
          where: {
            headcountPlanId: state.headcountPlanId,
            departmentId: data.headcountPlanLine.departmentId,
          },
          take: 1,
        },
      },
    });
    headcountPlanDetailId = headcountPlanDetail?.[0]?.id;
  }
  delete data.headcountPlanLine?._nodeReference;
  if (headcountPlanDetailId) {
    const result = await actions.run({
      headcountPlanLine: {
        action: 'createHeadcountPlanLine',
        data: {
          headcountPlanLine: {
            ...data.headcountPlanLine,
            vacancies: 1,
            forecastCost: new Decimal(
              data.headcountPlanLine.forecastCost
            ).dividedBy(state.vacancies),
            headcountPlanDetailId,
            creationType: EvstHeadcountLineStatus.Manual,
          },
          vacancies: state.vacancies,
        },
      },
    });
    if (result.headcountPlanLine) {
      helpers.showToast({
        type: 'success',
        title: '{{headcountPlan.create.success.title}}',
        message: '{{headcountPlan.create.success.message}}',
      });

      // Navigate back to the list view
      helpers.closeModal();
    } else {
      helpers.showToast({
        type: 'error',
        title: '{{headcountPlan.create.error.title}}',
        message: '{{headcountPlan.create.error.message}}',
      });
    }
  } else {
    helpers.showToast({
      type: 'error',
      title: '{{headcountPlan.create.error.title}}',
      message: '{{headcountPlan.create.error.message}}',
    });
  }
}

export async function onSaveClick({ helpers, data, actions }: Context) {
  delete data.headcountPlanLine?._nodeReference;
  const result = await actions.run({
    headcountPlanLine: {
      action: 'editHeadcountPlanLine',
      data: {
        headcountPlanLine: {
          ...data.headcountPlanLine,
          creationType: EvstHeadcountLineStatus.Manual,
          vacancies: 1,
        },
      },
    },
  });
  if (result.headcountPlanLine) {
    helpers.showToast({
      type: 'success',
      title: '{{headcountPlan.create.success.title}}',
      message: '{{headcountPlan.update.success.message}}',
    });
    helpers.closeModal();
  } else {
    helpers.showToast({
      type: 'error',
      title: '{{headcountPlan.create.error.title}}',
      message: '{{headcountPlan.update.error.message}}',
    });
  }
}

export function getAssociatedCompBandQuery({ data, state }: Context) {
  return data.headcountPlanLine?.departmentId
    ? {
        departmentId: data.headcountPlanLine.departmentId,
        positionId: data.headcountPlanLine.positionId,
        locationId: data.headcountPlanLine.locationId,
        entityId: data.headcountPlanLine.entityId,
        targetCurrency: data.headcountPlanLine.currency ?? state.targetCurrency,
      }
    : undefined;
}

export function getHeadcountPlanLineQuery({ state }: Context) {
  return state?.id
    ? {
        where: { id: state.id },
      }
    : undefined;
}

export function getRemainingVacancies({
  sharedState,
  state,
  data,
  helpers,
}: Context): object {
  const nodeRef = data.headcountPlanLine?._nodeReference;
  const instance = sharedState.getNodeInstance(nodeRef);
  return {
    title: '{{headcountPlan.available.vacancies}}',
    description: `${state.remainningVacancies}`,
    onClick: () => {
      helpers.set(instance, 'data.vacancies', state.remainingVacancies);
    },
  };
}

export function getForcastCost({ data }: Context) {
  const forecast =
    new Decimal(data.associatedCompBand?.midpoint?.amount ?? 0).times(
      data?.headcountPlanLine?.vacancies ?? 0
    ) ?? new Decimal(0);
  return forecast.toFixedShort();
}

export function getSuggestedMaximum({ data, sharedState, helpers }): object {
  if (data.associatedCompBand?.maximum) {
    const nodeRef = data.headcountPlanLine?._nodeReference;
    const instance = sharedState.getNodeInstance(nodeRef);
    return {
      description: `${data.associatedCompBand.maximum} ${data.headcountPlanLine.currency}`,
      title: '{{headcountPlant.suggest.maximum}}',
      onClick: () => {
        helpers.set(instance, 'data.maximum', data.associatedCompBand.maximum);
      },
    };
  }
}

export function getSuggestedMinimum({ data, sharedState, helpers }): object {
  const nodeRef = data.headcountPlanLine?._nodeReference;
  const instance = sharedState.getNodeInstance(nodeRef);
  if (data.associatedCompBand?.minimum) {
    return {
      description: `${data.associatedCompBand.minimum} ${data.headcountPlanLine.currency}`,
      title: '{{headcountPlant.suggest.minimum}}',
      onClick: () => {
        helpers.set(instance, 'data.minimum', data.associatedCompBand.minimum);
      },
    };
  }
}

export function onMinChange(
  { data, sharedState, helpers }: Context,
  min: Decimal
) {
  const { headcountPlanLine } = data;
  let midpoint: string | undefined;
  if (min && headcountPlanLine?.maximum) {
    midpoint = new Decimal(min)
      .plus(new Decimal(headcountPlanLine.maximum.amount))
      .dividedBy(2)
      .toFixedShort();
  }
  const nodeRef = headcountPlanLine?._nodeReference;
  const instance = sharedState.getNodeInstance(nodeRef);
  helpers.set(instance, 'data.midpoint', midpoint);
}

export function onMaxChange(
  { data, sharedState, helpers }: Context,
  max: Decimal
) {
  const { headcountPlanLine } = data;
  let midpoint: string | undefined;
  if (max && headcountPlanLine?.minimum) {
    midpoint = new Decimal(max)
      .plus(new Decimal(headcountPlanLine.minimum.amount))
      .dividedBy(2)
      .toFixedShort();
  }
  const nodeRef = headcountPlanLine?._nodeReference;
  const instance = sharedState.getNodeInstance(nodeRef);
  helpers.set(instance, 'data.midpoint', midpoint);
}

export function onVacanciesUpdate(ctx: Context, vacancies: number) {
  ctx.state.vacancies = vacancies;
  updateForecostCost(ctx);
}

export function updateForecostCost({
  data,
  sharedState,
  helpers,
  state,
}: Context) {
  const { headcountPlanLine } = data;
  if (
    headcountPlanLine?.expectedFillDate &&
    headcountPlanLine?.midpoint &&
    state?.vacancies > 0
  ) {
    const monthsToPay = monthsToEndOfYear(headcountPlanLine.expectedFillDate);
    const forecastCost = new Decimal(headcountPlanLine.midpoint.amount)
      .times((monthsToPay * state.vacancies) / 12)
      .toFixedShort();
    const nodeRef = data.headcountPlanLine?._nodeReference;
    const instance = sharedState.getNodeInstance(nodeRef);
    helpers.set(instance, 'data.forecastCost', forecastCost);
  }
}

export function monthsToEndOfYear(startDate: PlainDateLike): number {
  const d = PlainDate.from(startDate);
  return 12 - d.month + 1;
}

export function getIntialMidPoint({
  data: { associatedCompBand, headcountPlanLine },
}: Context) {
  return associatedCompBand?.midpoint ?? headcountPlanLine?.midpoint;
}

export function getIntialMinimum({
  data: { associatedCompBand, headcountPlanLine },
}: Context) {
  return associatedCompBand?.minimum ?? headcountPlanLine?.minimum;
}

export function getMode(context: Context) {
  const { mode, disableDepartment } = context.state;
  return mode === 'create' && !disableDepartment ? false : true;
}

// export function setHeadcountPlanDetailId(
//   { state, data }: Context,
//   value: number
// ) {
//   state.headcountPlanDetailId = data.headcountPlanDetail?.find(
//     (x) => x.departmentId === value
//   )?.id;
// }

export function setViewMode(context: Context) {
  context.state.mode = 'view';
}

export function isEditMode(context: Context) {
  return context.state.mode === 'edit';
}

export function isCreateOrEdit(context: Context) {
  return context.state.mode === 'create' || context.state.mode === 'edit';
}

export function setMidpoint(context: Context) {
  context.state.midpoint =
    context.data.headcountPlanDetail?.['midpoint'] ?? null;
}

export function hasLineId(ctx: Context) {
  return !!ctx.state.id;
}

export function getHeadcountPlanDetailQuery({ state }: Context) {
  return state?.headcountPlanDetailId
    ? {
        where: {
          id: state.headcountPlanDetailId,
        },
      }
    : undefined;
}
