{"version": 2, "uimodel": {"state": {"edit": false, "editingOpenHeadcount": false, "currentEditingLineId": null}, "nodes": {"headcountPlan": {"type": "struct", "query": {"where": {"id": "@state:headcountPlanId"}}, "model": "urn:evst:everest:hr/planning:model/node:HeadcountPlan", "fieldList": ["id", "budgetAmount", "name", "currency", "startDate", "allocationType", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "attritionRate", "raiseRate", "raiseDate", "salaryBudget", "salaryRaise", "burnRate", "attritionCost", "scenario", "status", "committedCost", "hcGrowthRate", "abs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "scenarios": {"type": "list", "query": "@controller:getScenariosQuery()", "model": "urn:evst:everest:hr/planning:model/node:HeadcountPlan", "fieldList": ["id", "startDate", "name", "budgetAmount", "plannedHeadcounts", "status", "committedCost", "abs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hcGrowthRate"]}, "headcountPlanDetail": {"type": "list", "model": "urn:evst:everest:hr/planning:model/node:HeadcountPlanDetail", "query": {"where": {"headcountPlanId": "@state:headcountPlanId"}, "orderBy": [{"field": "departmentId", "ordering": "asc"}]}, "fieldList": ["id", "departmentId", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "headcountCap", "remainingVacancies", "status", "budgetOwner", "midpoint", "burnRate", "forecastCost"]}, "headcountPlanLine": {"type": "list", "parent": "headcountPlanDetail", "joinKey": "headcountPlanDetailId-id", "model": "urn:evst:everest:hr/planning:model/node:HeadcountPlanLine", "fieldList": ["id", "departmentId", "positionId", "entityId", "locationId", "expectedFillDate", "currency", "vacancies", "filledHeadcounts", "remainingHeadcounts", "headcountPlanDetailId", "forecastCost", "headcountOwner", "minimum", "maximum", "midpoint", "HeadcountPlanLine-Job.employeeId"]}, "headcountLineJobMapping": {"type": "list", "modelId": "everest.hr.planning/HeadcountPlanLineJobMappingModel.HeadcountPlanLineJobMapping", "parent": "headcountPlanLine", "joinKey": "id-headcountPlanLineId", "fieldList": ["id", "jobId", "headcountPlanLineId", "recruitmentId", "HeadcountPlanLineJobMapping-Recruitment.deadline", "recruitmentStatus", "recruitmentNumber"]}, "salaryInfo": {"model": "urn:evst:everest:hr/planning:model/node:HeadcountPlan", "type": "struct", "getTotalHeadcounts": {"startDate": "@binding:headcountPlan.startDate"}, "fieldList": ["avgSalary", "headcountNumber"]}, "departmentTree": {"model": "urn:evst:everest:hr/planning:model/node:HeadcountPlan", "type": "struct", "detailedType": "table", "getSummaryHeadcount": {"id": "@state:headcountPlanId"}}, "monthlyHeadcount": {"model": "urn:evst:everest:hr/planning:model/node:HeadcountPlan", "type": "struct", "detailedType": "table", "getMonthlyHeadcount": {"id": "@state:headcountPlanId"}}, "compensationBands": {"model": "urn:evst:everest:hr/base:model/node:CompensationBand", "type": "list", "query": {}, "fieldList": []}}}, "uicontroller": ["headcountPlanDetail.uicontroller.ts", "planActions.uicontroller.ts", "modal.uicontroller.ts", "modalHandlers.uicontroller.ts", "stateHelpers.uicontroller.ts", "utils.uicontroller.ts"], "uiview": {"layout": "everest.base.ui/layouts/uilayout/segmented", "props": {"title": "@binding:headcountPlan.name", "i18n": ["everest.hr.base/hrbase", "headcountPlan"], "config": {"allowRefreshData": true, "autoRefreshData": true}, "stretch": true, "description": "@binding:headcountPlan.scenario", "status": "@binding:headcountPlan.status", "customActions": [{"label": "@controller:editButtonLabel()", "onClick": "@controller:onEditPlanClick", "variant": "primary"}, {"label": "Cancel", "onClick": "@controller:onCancelClick", "visible": "@state:edit"}, {"label": "Actions", "variant": "secondary", "visible": "@controller:returnViewMode()", "actions": [{"label": "{{headcountPlan.manage.comp.band}}", "variant": "tertiary", "onClick": "@controller:openCompensationBandModal"}, {"label": "{{headcountPlan.create.scenario}}", "variant": "tertiary", "onClick": "@controller:onCreateScenarioClick"}]}], "stepsContent": [{"title": "{{headcountPlan.overview}}", "components": [{"component": "<PERSON><PERSON>", "section": {"grid": {"size": "12"}}, "visible": "@controller:isMissingCompBands()", "props": {"variant": "warning", "title": "{{missing.comp.bands}}", "content": "{{headcountPlan.alert.missingCompBands}}", "action": {"label": "{{headcountPlan.manage.comp.band}}", "onClick": "@controller:openCompensationBandModal"}}}, {"component": "Summary", "visible": true, "section": {"title": "{{headcountPlan.fin.stats}}", "variant": "card", "grid": {"size": "12"}}, "props": {"columns": 4, "fields": [{"label": "{{headcountPlan.headcount.growth.rate}}", "parseAs": "percent", "value": "@binding:headcountPlan.hcGrowthRate"}, {"label": "{{headcountPlan.burnRate}}", "parseAs": "currency", "value": "@binding:headcountPlan.burnRate", "format": "@binding:headcountPlan.currency"}, {"label": "{{headcountPlan.budgetAmount}}", "parseAs": "currency", "value": "@binding:headcountPlan.budgetAmount", "format": "@binding:headcountPlan.currency"}, {"label": "{{headcountPlan.forecastCost}}", "parseAs": "currency", "value": "@binding:headcountPlan.committedCost", "format": "@binding:headcountPlan.currency", "variant": "@controller:getCommittedCostVariant()"}]}}, {"component": "SummaryText", "section": {"title": "{{headcountPlan.cost.summary}}", "grid": {"size": "4"}, "contained": true}, "props": {"summaryBelow": true, "summary": {"label": "{{headcountPlan.budgetAmount}}", "parseAs": "currency", "value": "@binding:headcountPlan.budgetAmount", "format": "@binding:headcountPlan.currency"}, "date": {}, "fields": [{"label": "{{headcountPlan.burnRate}}", "value": "@binding:headcountPlan.burnRate", "parseAs": "currency", "format": "@binding:headcountPlan.currency"}, {"label": "{{headcountPlan.hiringBudget}}", "value": "@binding:headcountPlan.hiringBudget", "parseAs": "currency", "format": "@binding:headcountPlan.currency"}, {"value": "@binding:headcountPlan.salaryRaise", "label": "{{headcountPlan.salaryRaise}}", "parseAs": "currency", "format": "@binding:headcountPlan.currency"}, {"label": "{{headcountPlan.attritionCost}}", "value": "@binding:headcountPlan.attritionCost", "parseAs": "currency", "format": "@binding:headcountPlan.currency"}], "variant": "small"}}, {"component": "FieldGroup", "section": {"grid": {"size": "8"}, "editing": "@state:edit", "contained": true}, "props": {"columns": 4, "elements": [{"label": "{{startDate}}", "value": "@binding:headcountPlan.startDate", "component": "DatePicker", "picker": "year", "action": "update", "isDisabled": true}, {"label": "{{currency}}", "value": "@binding:headcountPlan.currency", "action": "update", "isDisabled": true}, {"component": "InputNumber", "label": "{{headcountPlan.budgetAmount}}", "value": "@binding:headcountPlan.budgetAmount", "type": "amount", "parseAs": "currency", "format": "@binding:headcountPlan.currency", "nullContent": "custom", "customNullContent": "{{headcountPlan.not.set}}", "action": "update"}, {"component": "InputNumber", "label": "{{headcountPlan.burnRate}}", "action": "update", "value": "@binding:headcountPlan.salaryBudget", "type": "amount", "parseAs": "currency", "nullContent": "custom", "customNullContent": "Insufficient Data", "format": "@binding:headcountPlan.currency", "isEditing": false, "visible": false}, {"component": "InputNumber", "label": "{{headcountPlan.hiringBudget}}", "value": "@binding:headcountPlan.hiringBudget", "action": "update", "type": "amount", "parseAs": "currency", "format": "@binding:headcountPlan.currency", "isEditing": false, "visible": false}, {"component": "InputNumber", "label": "{{headcountPlan.unallocated.budget}}", "value": "@binding:headcountPlan.remainingBudget", "isEditing": false, "type": "amount", "parseAs": "currency", "format": "@binding:headcountPlan.currency", "nullContent": "custom", "customNullContent": "{{headcountPlan.not.available}}"}, {"label": "{{headcountPlan.salaryRaiseDate}}", "minDate": "@controller:getMinDate()", "maxDate": "@controller:getMaxDate()", "value": "@binding:headcountPlan.raiseDate", "action": "update", "component": "DatePicker", "picker": "month"}, {"component": "InputNumber", "label": "{{headcountPlan.salaryRaiseRate}}", "action": "update", "value": "@binding:headcountPlan.raiseRate", "precision": 2, "min": 0, "max": 100, "type": "percentage"}, {"component": "InputNumber", "label": "{{headcountPlan.raiseBudget}}", "value": "@binding:headcountPlan.salaryRaise", "type": "amount", "parseAs": "currency", "format": "@binding:headcountPlan.currency", "isEditing": false, "visible": false}, {"component": "InputNumber", "label": "{{headcountPlan.attritionRate}}", "action": "update", "value": "@binding:headcountPlan.attritionRate", "precision": 2, "min": 0, "max": 100, "type": "percentage"}, {"component": "InputNumber", "label": "{{headcountPlan.moneySavedByAttrition}}", "isEditing": false, "value": "@binding:headcountPlan.attritionCost", "type": "amount", "parseAs": "currency", "format": "@binding:headcountPlan.currency", "visible": false}]}}, {"component": "Table", "icon": "groups", "tooltip": {"text": "{{headcount.employee.count}}", "placement": "top"}, "section": {"editing": false, "grid": {"size": "12"}, "actions": [{"label": "{{view}}", "suffixCornerProp": "sign", "visible": false, "actions": [{"label": "{{headcountPlan.summary.only}}", "onClick": "@controller:onSelectSummaryClick", "disabled": "@controller:isDisabledSummaryOption()"}, {"label": "{{headcountPlan.monthly.detail}}", "onClick": "@controller:onSelectDetailClick", "disabled": "@controller:isDisabledDetailOption()"}]}]}, "props": {"data": "@binding:departmentTree", "headerSize": "small", "variant": "light", "hideFilters": false, "hideExpansionToggle": true, "rowBackgroundGetter": "@controller:getRowBackgroundColor", "treeView": {"initialDepth": 5, "rowContentKey": "data", "groupColumn": {"field": "departmentName", "headerName": "Department", "minWidth": 300, "pinned": "left", "onCellClicked": "@controller:onDepartmentHeaderClick"}}}}]}, {"title": "{{headcountPlan.monthly.detail}}", "components": [{"component": "Table", "icon": "groups", "tooltip": {"text": "{{headcount.employee.count}}", "placement": "top"}, "section": {"editing": false, "grid": {"size": "12"}, "actions": [{"label": "{{view}}", "suffixCornerProp": "sign", "visible": false, "actions": [{"label": "{{headcountPlan.summary.only}}", "onClick": "@controller:onSelectSummaryClick", "disabled": "@controller:isDisabledSummaryOption()"}, {"label": "{{headcountPlan.monthly.detail}}", "onClick": "@controller:onSelectDetailClick", "disabled": "@controller:isDisabledDetailOption()"}]}]}, "props": {"data": "@binding:monthlyHeadcount", "headerSize": "small", "variant": "light", "hideFilters": false, "hideExpansionToggle": true, "rowBackgroundGetter": "@controller:getRowBackgroundColor", "treeView": {"initialDepth": 5, "rowContentKey": "data", "groupColumn": {"field": "departmentName", "headerName": "Department", "minWidth": 300, "pinned": "left", "onCellClicked": "@controller:onDepartmentHeaderClick"}}}}]}, {"title": "{{headcountPlan.budget.allocation}}", "components": [{"component": "SectionGroup", "section": {"grid": {"size": "12"}}, "props": {"sections": [{"component": "Table", "section": {"actions": [{"label": "{{headcountPlan.allocate.budget}}", "variant": "primary", "onClick": "@controller:allocateBudget", "visible": false}, {"label": "{{submit.changes}}", "variant": "primary", "onClick": "@controller:saveDetailChanges"}], "grid": {"size": "12"}}, "props": {"data": "@binding:headcountPlanDetail", "addRows": false, "rowBackgroundGetter": "@controller:checkRemainingBudget", "variant": "white", "fullWidth": false, "rowSelection": false, "rowActions": {"minWidth": 85, "columnPosition": 0, "actions": [{"label": "{{view.details}}", "onClick": "@controller:onViewClick"}, {"label": "{{approve}}", "onClick": "@controller:approvePlan", "disabled": "@controller:disableApprove"}, {"label": "{{reject}}", "onClick": "@controller:rejectPlan", "disabled": "@controller:disableApprove"}]}, "columns": [{"field": "status", "cellVariant": {"variant": "badge", "matchers": "@controller:getMatchers()"}, "fieldProps": {"editing": false}}, {"field": "departmentId", "fieldProps": {"editing": false}}, {"field": "burnRate", "type": "amount", "fieldProps": {"parseAs": "currency", "format": "@binding:headcountPlan.currency", "nullContent": "custom", "customNullContent": "{{headcountPlan.not.set}}"}}, {"field": "midpoint", "type": "amount", "action": "update", "fieldProps": {"editing": "@controller:isUnsubmitted", "parseAs": "currency", "format": "@binding:headcountPlan.currency", "nullContent": "custom", "customNullContent": "{{headcountPlan.not.set}}"}}, {"field": "forecastCost", "type": "amount", "headerName": "{{headcountPlan.spent.budget}}", "fieldProps": {"parseAs": "currency", "format": "@binding:headcountPlan.currency"}}, {"field": "<PERSON><PERSON><PERSON><PERSON>", "type": "amount", "action": "update", "fieldProps": {"parseAs": "currency", "editing": "@controller:isUnsubmitted", "format": "@binding:headcountPlan.currency", "nullContent": "custom", "customNullContent": "{{headcountPlan.not.set}}"}}, {"field": "<PERSON><PERSON><PERSON><PERSON>", "type": "amount", "fieldProps": {"component": "InputNumber", "parseAs": "currency", "format": "@binding:headcountPlan.currency", "nullContent": "custom", "customNullContent": "{{headcountPlan.not.available}}", "hybridConfig": {"icon": "file", "iconPosition": "left", "variant": "form"}}}, {"headerName": "{{headcountPlan.headcountCap}}", "field": "headcountCap", "action": "update", "fieldProps": {"editing": "@controller:isUnsubmitted"}, "type": "amount"}, {"field": "remainingVacancies", "type": "amount"}, {"field": "budgetOwner", "fieldProps": {"editing": "@controller:isUnsubmitted"}, "action": "update"}]}}]}}]}, {"title": "{{headcountPlan.open.headcounts}}", "visible": "@controller:showHeadcountLines()", "components": [{"component": "SectionGroup", "section": {"grid": {"size": "12"}}, "props": {"sections": [{"component": "Table", "section": {"title": "", "grid": {"size": "12"}, "actions": [{"label": "{{actions}}", "actions": [{"label": "{{headcountPlan.add}}", "variant": "primary", "onClick": "@controller:onAddHeadcountLineClick"}]}]}, "props": {"data": "@binding:headcountPlanLine", "rowSelection": true, "onRowClicked": "@controller:onPlanLineClick", "variant": "light", "rowActions": {"columnPosition": -1, "width": "85", "initialDepth": 0, "actions": [{"label": "{{edit}}", "onClick": "@controller:onHeadcountLineClick"}, {"label": "{{delete}}", "onClick": "@controller:onDeleteHeadcountLineClick"}]}, "columns": [{"field": "departmentId"}, {"field": "positionId", "fieldProps": {"nullContent": "custom", "customNullContent": "{{all.position}}"}}, {"field": "entityId", "fieldProps": {"nullContent": "custom", "customNullContent": "{{all.entity}}"}}, {"field": "locationId", "fieldProps": {"nullContent": "custom", "customNullContent": "{{all.location}}"}}, "expectedFillDate", {"field": "midpoint", "type": "amount", "fieldProps": {"parseAs": "currency", "format": "@binding:headcountPlan.currency"}}, {"field": "forecastCost", "type": "amount", "fieldProps": {"parseAs": "currency", "format": "@binding:headcountPlan.currency"}}, {"field": "headcountOwner"}]}}]}}]}, {"title": "{{headcountPlan.scenario.list}}", "components": [{"component": "Table", "size": "12", "variant": "light", "data": "@binding:scenarios", "rowActions": {"columnPosition": 0, "width": "85", "initialDepth": 0, "actions": [{"label": "{{headcountPlan.compare}}", "onClick": "@controller:onCompareClick"}]}, "columns": [{"headerName": "{{name}}", "field": "name"}, {"field": "status", "cellVariant": {"variant": "badge", "matchers": "@controller:getMatchers()"}}, {"headerName": "{{headcountPlan.budgetAmount}}", "field": "budgetAmount", "fieldProps": {"parseAs": "currency", "type": "amount", "format": "@binding:headcountPlan.currency"}}, {"headerName": "{{headcountPlan.committedCost}}", "field": "committedCost", "fieldProps": {"parseAs": "currency", "type": "amount", "format": "@binding:headcountPlan.currency"}}, "abs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "plannedHeadcounts", "hcGrowthRate"]}]}]}}}