// @i18n:everest.hr.base/hrbase
// @i18n:headcountPlan

import type { PlainDateLike } from '@everestsystems/datetime';
import { PlainDate } from '@everestsystems/datetime';
import { Decimal } from '@everestsystems/decimal';
import type { EvstCurrencyCodeType } from '@pkg/everest.base/types/primitives/CurrencyCodeType';
import type { HeadcountTableSidebarUiTemplate } from '@pkg/everest.hr.planning/types/uiTemplates/uinext/headcountTableSidebar.ui';
import { isEmpty } from 'lodash';

type Context = HeadcountTableSidebarUiTemplate.HeadcountTableSidebarContext & {
  state: {
    departmentId: number;
    targetCurrency: EvstCurrencyCodeType;
    mode: 'create' | 'edit';
    headcountPlanDetailId: number;
    id?: number;
    firstDay: Date;
    lastDay: Date;
    remainingVacancies: number;
    cell: number;
    cellInput: number;
  };
};

export function getHeadcountTableLineQuery({ state }: Context) {
  if (!state.departmentId) {
    return [];
  }

  return {
    where: {
      departmentId: state.departmentId,
      ...(state.isPlan ? {} : { expectedFillDate: state.date }),
    },
    order: [['expectedFillDate', 'ASC']],
  };
}

export function updateCell(context: Context, value: number) {
  const { state } = context;
  state.cellInput = value;
}

export async function onPrimaryTableBtnClick(ctx: Context) {
  const { state } = ctx;

  const enteredValue: number = state.cellInput;
  const originalValue: number = state.cell;
  const shouldRemove = enteredValue < originalValue;

  await (shouldRemove ? onRemoveLinesClick(ctx) : onCreateLinesClick(ctx));
}

export async function onSecondaryTableBtnClick(ctx: Context) {
  const { helpers } = ctx;
  await helpers.closeSidebarTemplate();
}

export async function onRemoveLinesClick(ctx: Context) {
  const { data, helpers, actions, state } = ctx;
  const cellValue: number = state.cell;
  const enteredValue: number = state.cellInput;
  const linesToRemove = cellValue - enteredValue;
  const currentDetail = data.headcountPlanDetail[0];

  if (linesToRemove <= 0) {
    helpers.showToast({
      type: 'warning',
      title: '{{headcountPlan.remove.warning.invalidInput.title}}',
      message: '{{headcountPlan.remove.warning.invalidInput.message}}',
    });
    return;
  }

  try {
    // First try to get lines for specific month
    let availableLines = data.headcountPlanLine || [];
    if (availableLines.length === 0) {
      // If still no lines for that month, get all lines for this plan detail
      const allLinesResult = await actions.run({
        headcountPlanLine: {
          action: 'query',
          data: {
            where: {
              departmentId: state.departmentId,
            },
          },
          order: [['expectedFillDate', 'DESC']],
        },
      });
      availableLines = allLinesResult.headcountPlanLine[0] || [];
    }

    const removalCheck = canRemoveLines(availableLines.length, linesToRemove);

    if (!removalCheck.possible) {
      helpers.showToast({
        type: 'warning',
        title:
          removalCheck.availableCount === 0
            ? '{{headcountPlan.remove.warning.noLines.title}}'
            : '{{headcountPlan.remove.warning.partialLines.title}}',
        message:
          removalCheck.availableCount === 0
            ? '{{headcountPlan.remove.warning.noLines.message}}'
            : '{{headcountPlan.remove.warning.partialLines.message}}'.replace(
                '$available$',
                availableLines.length.toString()
              ),
      });
      return;
    }

    const linesToRemoveIds = availableLines
      .slice(-linesToRemove)
      .map((line) => line.id);

    // First, delete all lines
    const deleteResults = [];
    for (const id of linesToRemoveIds) {
      const deleteResult = await actions.run({
        headcountPlanLine: {
          action: 'delete',
          where: {
            id: id,
            departmentId: state.departmentId,
          },
        },
      });

      if (deleteResult.headcountPlanLine[0]) {
        deleteResults.push(deleteResult.headcountPlanLine[0]);
      }
    }

    const successfullyRemovedCount = deleteResults.length;

    // Then update the detail with total successful removals
    if (successfullyRemovedCount > 0) {
      const updateResult = await actions.run({
        headcountPlanDetail: {
          action: 'update',
          where: {
            id: data.headcountPlanDetail[0].id,
          },
          data: {
            headcountCap: Math.max(
              0,
              currentDetail.headcountCap - successfullyRemovedCount
            ),
          },
        },
      });

      if (updateResult.headcountPlanDetail[0]) {
        helpers.showToast({
          type: 'success',
          title:
            successfullyRemovedCount === 1
              ? '{{headcountPlan.remove.success.single.title}}'
              : '{{headcountPlan.remove.success.multiple.title}}',
          message:
            successfullyRemovedCount === 1
              ? '{{headcountPlan.remove.success.single.message}}'
              : '{{headcountPlan.remove.success.multiple.message}}',
        });
      } else {
        helpers.showToast({
          type: 'error',
          title: '{{headcountPlan.remove.error.title}}',
          message: '{{headcountPlan.remove.error.message}}',
        });
      }
    } else {
      helpers.showToast({
        type: 'error',
        title: '{{headcountPlan.remove.error.title}}',
        message: '{{headcountPlan.remove.error.noRemoval.message}}',
      });
    }
  } catch (error) {
    console.error('Error removing lines:', error);
    helpers.showToast({
      type: 'error',
      title: '{{headcountPlan.remove.error.title}}',
      message: '{{headcountPlan.remove.error.message}}',
    });
  }
  await helpers.currentModalSubmitCallback();
  await helpers.closeSidebarTemplate();
}

export async function onCreateLinesClick(ctx: Context) {
  const { data, helpers, actions, state } = ctx;
  const cellValue: number = state.cell || 0;
  const enteredValue: number = state.cellInput;
  const linesToAdd = enteredValue - cellValue;
  const currentDetail = data.headcountPlanDetail[0];
  const isPlan = state.isPlan;

  try {
    // First get the total number of existing lines for this detail
    const existingLinesResult = await actions.run({
      headcountPlanLine: {
        action: 'query',
        data: {
          where: {
            departmentId: state.departmentId,
            headcountPlanDetailId: state.headcountPlanDetailId,
          },
        },
      },
    });

    const existingLinesCount =
      existingLinesResult.headcountPlanLine[0].length || 0;

    const lineResults = [];
    if (isPlan) {
      const currentDate = new Date();
      const firstDay = state.firstDay;
      const lastDay = state.lastDay;

      const startDistributionDate =
        currentDate >= firstDay && currentDate <= lastDay
          ? currentDate
          : firstDay;

      // Always start from January of the plan year
      const planYear = firstDay.getFullYear();
      const totalMonths = 12;

      // For 6 lines across 12 months, we want one line every 2 months
      const monthSpacing = Math.floor(totalMonths / linesToAdd);
      let currentMonth = startDistributionDate.getMonth() + 1; // Start from current month, 1-12 format

      // Distribute lines across the year
      for (let i = 0; i < linesToAdd; i++) {
        const monthDate = new PlainDate(planYear, currentMonth, 1);

        // Create line for this month
        const lineResult = await actions.run({
          headcountPlanLine: {
            action: 'create',
            data: {
              departmentId: state.departmentId,
              expectedFillDate: monthDate,
              vacancies: 1,
              midpoint: data.headcountPlanDetail?.midpoint,
              headcountPlanDetailId: state.headcountPlanDetailId,
            },
          },
        });

        if (lineResult.headcountPlanLine[0]) {
          lineResults.push(lineResult.headcountPlanLine[0]);
        }

        // Move to next position (every 2 months in this case)
        currentMonth += monthSpacing;
      }
    } else {
      // First, create all lines
      for (let i = 0; i < linesToAdd; i++) {
        const lineResult = await actions.run({
          headcountPlanLine: {
            action: 'create',
            data: {
              departmentId: state.departmentId,
              expectedFillDate: state.date,
              vacancies: 1,
              midpoint: data.headcountPlanDetail?.midpoint,
              headcountPlanDetailId: state.headcountPlanDetailId,
            },
          },
        });

        if (lineResult.headcountPlanLine[0]) {
          lineResults.push(lineResult.headcountPlanLine[0]);
        }
      }
    }

    const successfullyAddedLines = lineResults.length;

    // Then update the detail with total successful additions
    if (successfullyAddedLines > 0) {
      if ((currentDetail.headcountCap || 0) <= existingLinesCount) {
        const updateResult = await actions.run({
          headcountPlanDetail: {
            action: 'update',
            where: {
              id: currentDetail.id,
            },
            data: {
              headcountCap:
                (currentDetail.headcountCap || 0) + successfullyAddedLines,
            },
          },
        });

        if (updateResult.headcountPlanDetail) {
          helpers.showToast({
            type: 'success',
            title:
              successfullyAddedLines === 1
                ? '{{headcountPlan.create.success.single.title}}'
                : '{{headcountPlan.create.success.multiple.title}}',
            message:
              successfullyAddedLines === 1
                ? '{{headcountPlan.create.success.single.message}}'
                : '{{headcountPlan.create.success.multiple.message}}',
          });
        } else {
          helpers.showToast({
            type: 'error',
            title: '{{headcountPlan.create.error.title}}',
            message: '{{headcountPlan.create.error.message}}',
          });
        }
      }
    } else {
      helpers.showToast({
        type: 'error',
        title: '{{headcountPlan.create.error.title}}',
        message: '{{headcountPlan.create.error.noLines.message}}',
      });
    }
  } catch (error) {
    console.error('Error creating lines:', error);
    helpers.showToast({
      type: 'error',
      title: '{{headcountPlan.create.error.title}}',
      message: '{{headcountPlan.create.error.message}}',
    });
  }
  await helpers.currentModalSubmitCallback();
  await helpers.closeSidebarTemplate();
}

// Helper to check if lines can be removed
function canRemoveLines(
  availableCount: number,
  requestedRemoval: number
): {
  possible: boolean;
  message: string;
  availableCount: number;
} {
  if (availableCount === 0) {
    return {
      possible: false,
      message: '{{headcountPlan.remove.noMoreLines}}',
      availableCount: 0,
    };
  }

  if (availableCount < requestedRemoval) {
    return {
      possible: false,
      message: '{{headcountPlan.remove.warning.partialLines.message}}'.replace(
        '$available$',
        availableCount.toString()
      ),
      availableCount,
    };
  }

  return {
    possible: true,
    message: '',
    availableCount,
  };
}

export function getAssociatedCompBandQuery({ data, state }: Context) {
  return isEmpty(data.headcountPlanLine)
    ? undefined
    : {
        departmentId: {
          $in: data.headcountPlanLine?.map((x) => x.departmentId),
        },
        positionId: {
          $in: data.headcountPlanLine?.map((x) => x.positionId),
        },
        locationId: {
          $in: data.headcountPlanLine?.map((x) => x.locationId),
        },
        entityId: {
          $in: data.headcountPlanLine?.map((x) => x.entityId),
        },
        targetCurrency: {
          $in: data.headcountPlanLine?.map((x) => x.currency) ?? [
            state.targetCurrency,
          ],
        },
      };
}

export function getSuggestedMaximum({ data, sharedState, helpers }): object {
  if (data.associatedCompBand?.maximum) {
    const nodeRef = data.headcountPlanLine?._nodeReference;
    const instance = sharedState.getNodeInstance(nodeRef);
    return {
      description: `${data.associatedCompBand.maximum} ${data.headcountPlanLine.currency}`,
      title: '{{headcountPlant.suggest.maximum}}',
      onClick: () => {
        helpers.set(instance, 'data.maximum', data.associatedCompBand.maximum);
      },
    };
  }
}

export function getSuggestedMinimum({ data, sharedState, helpers }): object {
  const nodeRef = data.headcountPlanLine?._nodeReference;
  const instance = sharedState.getNodeInstance(nodeRef);
  if (data.associatedCompBand?.minimum) {
    return {
      description: `${data.associatedCompBand.minimum} ${data.headcountPlanLine.currency}`,
      title: '{{headcountPlant.suggest.minimum}}',
      onClick: () => {
        helpers.set(instance, 'data.minimum', data.associatedCompBand.minimum);
      },
    };
  }
}

export function onMinChange(
  { data, sharedState, helpers }: Context,
  min: Decimal
) {
  const { associatedCompBand } = data;
  let midpoint: string | undefined;
  if (min && associatedCompBand?.maximum) {
    midpoint = new Decimal(min)
      .plus(new Decimal(associatedCompBand.maximum?.amount))
      .dividedBy(2)
      .toFixedShort();
  }
  const nodeRef = associatedCompBand?._nodeReference;
  const instance = sharedState.getNodeInstance(nodeRef);
  helpers.set(instance, 'data.midpoint', midpoint);
}

export function onMaxChange(
  { data, sharedState, helpers }: Context,
  max: Decimal
) {
  const { associatedCompBand } = data;
  let midpoint: string | undefined;
  if (max && associatedCompBand?.minimum) {
    midpoint = new Decimal(max)
      .plus(new Decimal(associatedCompBand.minimum?.amount))
      .dividedBy(2)
      .toFixedShort();
  }
  const nodeRef = associatedCompBand?._nodeReference;
  const instance = sharedState.getNodeInstance(nodeRef);
  helpers.set(instance, 'data.midpoint', midpoint);
}

export function monthsToEndOfYear(startDate: PlainDateLike): number {
  const d = PlainDate.from(startDate);
  return 12 - d.month + 1;
}

export function getMeanEmployeeCostQuery({ data, state }: Context) {
  const { firstDate, lastDate } = getPreviousMonthDates();
  const res = data.headcountPlanDetail?.departmentId
    ? {
        departmentId: data.headcountPlanDetail.departmentId,
        startDate: firstDate,
        endDate: lastDate,
        targetCurrency: state.headcountPlanCurrency,
        dataSource: state.employeeCostSource,
      }
    : undefined;
  return res;
}

function getPreviousMonthDates(): { firstDate: Date; lastDate: Date } {
  // Get the current date in UTC
  const now = new Date();

  // Create a new Date object for the first day of the current month in UTC
  const firstDayThisMonth = new Date(
    Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), 1)
  );

  // Subtract one day to get the last day of the previous month
  const lastDayPreviousMonth = new Date(firstDayThisMonth);
  lastDayPreviousMonth.setUTCDate(lastDayPreviousMonth.getUTCDate() - 1);

  // Create a new Date object for the first day of the previous month
  const firstDayPreviousMonth = new Date(
    Date.UTC(
      lastDayPreviousMonth.getUTCFullYear(),
      lastDayPreviousMonth.getUTCMonth(),
      1
    )
  );

  return {
    firstDate: firstDayPreviousMonth,
    lastDate: lastDayPreviousMonth,
  };
}
