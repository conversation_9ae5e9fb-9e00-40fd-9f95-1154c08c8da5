// @i18n:everest.hr.base/hrbase
// @i18n:headcountPlan
import { PlainDate } from '@everestsystems/datetime';
import { Decimal } from '@everestsystems/decimal';
import type { HeadcountPlanDetail } from '@pkg/everest.hr.planning/types/HeadcountPlanDetail';
import type { HeadcountPlanLine } from '@pkg/everest.hr.planning/types/HeadcountPlanLine';
import type { HeadcountPlanLineJobMapping } from '@pkg/everest.hr.planning/types/HeadcountPlanLineJobMapping';
import type { HeadcountPlanDetailUiTemplate } from '@pkg/everest.hr.planning/types/uiTemplates/uinext/headcountPlanDetail.ui';
import { isEmpty, omit } from 'lodash';

import { EvstSubmissionStatus } from '../types/enums/SubmissionStatus';

type RowData =
  | { nodeId: number; parentId: number; data: Record<string, unknown> }
  | Record<string, unknown>;

export type Row = {
  data: RowData;
  node?: {
    expanded?: boolean;
    isExpandable?: () => boolean;
  };
  column: { userProvidedColDef: Record<string, unknown> };
};

export type Context =
  HeadcountPlanDetailUiTemplate.HeadcountPlanDetailContext & {
    state: {
      edit: boolean;
      headcountPlanId: number;
      raiseBudget: number;
      attritionRateBudget: number;
      hiringBudget: number;
      tableView: undefined | 'detail';
      editingOpenHeadcount: boolean;
      currentEditingLineId?: number | null;
    };
  };

type HeadcountPlanDetailRow = {
  data: HeadcountPlanDetail.ReadReturnType;
};

type HeadcountPlanLineRow = {
  data: HeadcountPlanLine.HeadcountPlanLineWithAssociation;
};

type ChildrenDetailRowType = {
  data: HeadcountPlanLineJobMapping.HeadcountPlanLineJobMappingWithAssociation;
};

export function allocateBudget(
  { helpers, actions, state, data }: Context,
  departmentId?: number | undefined
): void {
  const allocatedDepartmentIds = data.headcountPlanDetail?.map(
    ({ departmentId }) => departmentId
  );
  helpers.openModal({
    template: '/templates/everest.hr.planning/uinext/headcountPlanDetailModal',
    onClose: async () => {
      await actions.refetchUiModelData({
        nodesToLoad: ['departmentTree'],
      });
    },
    title: '{{headcountPlan.detail.create}}',
    initialState: {
      headcountPlanId: state.headcountPlanId,
      ...(typeof departmentId === 'number' && { departmentId }),
      allocatedDepartmentIds,
      mode: 'create',
      headcountPlanCurrency: data.headcountPlan?.currency,
      remainingBudget: data.headcountPlan?.remainingBudget?.amount,
      startDate: data.headcountPlan?.startDate,
    },
  });
}

export function onRowClick(
  context: Context,
  rowData: HeadcountPlanDetailRow['data']
): void {
  onEditClick(context, { data: rowData }, 'view');
}

export function onViewClick(
  context: Context,
  { data: row }: HeadcountPlanDetailRow
): void {
  const { helpers } = context;
  helpers.navigate({
    to: `/templates/everest.hr.planning/uinext/budgetAllocation?id=${row?.id}`,
  });
}

export function onEditClick(
  { helpers, actions, data }: Context,
  { data: row }: HeadcountPlanDetailRow,
  mode: 'view' | 'edit' = 'edit'
) {
  helpers.openModal({
    template: '/templates/everest.hr.planning/uinext/headcountPlanDetailModal',
    onClose: actions.refetchUiModelData,
    title: '{{headcountPlan.detail}}',
    initialState: {
      id: row.id,
      mode: mode,
      headcountPlanCurrency: data.headcountPlan.currency,
      remainingBudget: data.headcountPlan.remainingBudget?.amount,
      allocatedBudgetAmount: row.allocatedBudget?.amount ?? null,
    },
  });
}

export function isRemainingBudgetEmpty({ data }: Context): boolean {
  return data.headcountPlan?.remainingBudget?.amount.equals(0);
}

export function onAddHeadcountLineClick(
  { data, helpers, actions }: Context,
  rowData: HeadcountPlanLineRow
): void {
  const startDate = PlainDate.from(data?.headcountPlan?.startDate); // January 1st of the given year
  const endDate = startDate.plus({ years: 1 }).minus({ days: 1 }); // Last Date of the given year

  helpers.openModal({
    template: '/templates/everest.hr.planning/uinext/headcountPlanLineModal',
    onClose: actions.refetchUiTemplate,
    initialState: {
      targetCurrency: data.headcountPlan?.currency,
      mode: 'create',
      minDate: new Date(startDate.toISO()),
      maxDate: new Date(endDate.toISO()),
      departmentId: rowData?.data?.departmentId,
      currency: data.headcountPlan?.currency,
      title: '{{headcountPlane.line.add}}',
      midpoint: rowData?.data?.midpoint,
      headcountPlanDetailId: rowData?.data?.id,
      disableDepartment: !!rowData?.data?.id,
      headcountPlanId: data.headcountPlan.id,
    },
  });
}

export async function onDeleteHeadcountLineClick(
  { actions }: Context,
  { data: row }: HeadcountPlanLineRow
): Promise<void> {
  await actions.run({
    headcountPlanLine: {
      action: 'delete',
      data: {
        id: row.id,
      },
    },
  });
  await actions.refetchUiModelData();
}

export async function onDeleteHeadcountPlanDetail(
  { actions, data }: Context,
  { data: row }: HeadcountPlanDetailRow
): Promise<void> {
  await actions.run({
    headcountPlanDetail: {
      action: 'delete',
      data: {
        id: row.id,
      },
    },
    headcountPlanLine: {
      action: 'deleteMany',
      data: {
        id: {
          $in: data.headcountPlanLine
            .filter((x) => {
              x.headcountPlanDetailId === row.id;
            })
            ?.map(({ id }) => id),
        },
      },
    },
  });
  await actions.refetchUiModelData();
}

export async function onHeadcountLineClick(
  { actions, helpers, data }: Context,
  { data: row }: HeadcountPlanLineRow
): Promise<void> {
  helpers.openModal({
    template: '/templates/everest.hr.planning/uinext/headcountPlanLineModal',
    onClose: actions.refetchUiModelData,
    initialState: {
      id: row.id,
      title: '{{headcountPlane.line.edit}}',
      mode: 'edit',
      disableDepartment: false,
      currency: data.headcountPlan.currency,
      headcountPlanDetailId: row.headcountPlanDetailId,
    },
  });
}

export function onPlanLineClick(
  { helpers, state }: Context,
  row: HeadcountPlanLineRow['data']
): void {
  const lineId = row.id;
  helpers.set(state, 'editingOpenHeadcount', true);
  helpers.set(state, 'currentEditingLineId', lineId);
}

export function isCellEditing(
  { state }: Context,
  row: HeadcountPlanLineRow['data']
): boolean {
  return state.editingOpenHeadcount && state.currentEditingLineId === row.id;
}

export function getHeadcountLineMappingQuery({
  data,
}: Context): Record<string, unknown> {
  const { headcountPlanLine } = data;
  return isEmpty(headcountPlanLine)
    ? undefined
    : {
        where: {
          headcountPlanLineId: {
            $in: headcountPlanLine?.map(({ id }) => id),
          },
        },
        draft: 'include',
      };
}

export function openOrgChart(
  { helpers }: Context,
  { data: row }: HeadcountPlanLineRow
): void {
  helpers.navigate({
    to: '/templates/everest.hr.base/uinext/orgChart/orgChart?showChildren=true',
    initialState: {
      empId: row['HeadcountPlanLine-Job'].employeeId,
      includeHeadcount: true,
      showChildren: true,
    },
  });
}

export function getStatusMatchers(): Record<string, unknown> {
  return {
    'In-Progress': 'orange',
    Done: 'success',
    Created: 'gray',
  };
}

export function viewRecruitment(
  { helpers }: Context,
  { data: row }: ChildrenDetailRowType
): void {
  helpers.navigate({
    to: `/templates/everest.hr.base/uinext/recruitment/recruitment?id=${row?.recruitmentId}`,
    initialState: { mode: 'view' },
    nodeParams: {
      Applicants: {
        filters: {
          status: {
            $in: ['new', 'inProcess'],
          },
        },
      },
    },
  });
}

export function getOpenHeadcountStatus(
  _: Context,
  { data: row }: ChildrenDetailRowType
): string {
  if (!row?.recruitmentId) {
    return 'Created';
  } else if (row?.['HeadcountPlanLineJobMapping-Job'].employeeId) {
    return 'Done';
  } else {
    return 'In-Progress';
  }
}

export function openDepartment(context: Context, row: Pick<Row, 'data'>): void {
  const { helpers } = context;
  helpers.navigate({
    to: `/templates/everest.hr.base/uinext/department/department?id=${
      row.data?.data?.['Department:id'] ?? row.data['Department:id']
    }&mode=view`,
  });
}

export function hasHeadcountPlanLine(ctx: Context): boolean {
  return !isEmpty(ctx.data.headcountPlanLine);
}

export function parseColumnDate(headerName: string): {
  date: string;
} {
  const monthDict = {
    Jan: '01',
    Feb: '02',
    Mar: '03',
    Apr: '04',
    May: '05',
    Jun: '06',
    Jul: '07',
    Aug: '08',
    Sep: '09',
    Oct: '10',
    Nov: '11',
    Dec: '12',
  };
  // Remove asterisk if present
  const cleanHeader = headerName.replace('*', '').trim();
  const [month, year] = cleanHeader.split(' ');
  // const date = new PlainDate(Number(year), monthDict[month], 1);
  const date = `${year}-${monthDict[month]}-01`;
  return { date };
}

export async function onEditTableCellClick(
  context: Context,
  { data: row, colDef }: Record<string, unknown>
): Promise<void> {
  const { data, helpers, actions } = context;
  const departmentId = row?.['nodeId'];
  const headcountPlanDetail = data.headcountPlanDetail.find(
    (d) => d.departmentId === departmentId
  );
  const initialState = {
    headcountPlanDetailId: headcountPlanDetail['id'],
    openHeadcounts: row['data'][colDef['field']],
    startDate: colDef['field'].split(':')[0],
    currency: data.headcountPlan.currency,
    departmentId,
  };
  const selectedMonth = PlainDate.from(colDef['field'].split(':')[0]);
  const currentDate = PlainDate.from(new Date().toISOString().split('T')[0]);
  if (PlainDate.compare(selectedMonth, currentDate) <= 0) {
    helpers.showToast({
      message: 'You Cannot Edit Past Months',
      type: 'warning',
    });
    return;
  }
  helpers.openModal({
    size: 'xsmall',
    template: `/templates/everest.hr.planning/uinext/editMixedHeadcountTableCell`,
    initialState,
    onClose: async () => {
      await actions.refetchUiModelData();
    },
  });
}

export function getMatchers(): Record<string, unknown> {
  return {
    '{{unsubmitted}}': 'shuttle-grey',
    '{{approved}}': 'shamrock',
    '{{rejected}}': 'amaranth',
    '{{submitted}}': 'jaffa',
  };
}

export async function approvePlan(
  context: Context,
  { data: rowData }
): Promise<void> {
  const { actions, helpers } = context;
  const result = await actions.run({
    headcountPlanDetail: {
      action: 'reviewHeadcountPlanDetail',
      data: {
        headcountPlanDetailId: rowData.id,
        status: EvstSubmissionStatus.Approved,
      },
    },
  });
  if (result.error) {
    helpers.showToast({
      type: 'error',
      title: '{{error}}',
      message: '{{error.approve.headcount.plan.detail}}',
    });
  } else {
    helpers.showToast({
      type: 'success',
      title: '{{success}}',
      message: '{{success.approve.headcount.plan.detail}}',
    });
    await actions.refetchUiModelData();
  }
}

export async function rejectPlan(
  context: Context,
  { data: rowData }: HeadcountPlanDetailRow
): Promise<void> {
  const { actions, helpers } = context;
  const result = await actions.run({
    headcountPlanDetail: {
      action: 'reviewHeadcountPlanDetail',
      data: {
        headcountPlanDetailId: rowData.id,
        status: EvstSubmissionStatus.Rejected,
      },
    },
  });
  if (result.error) {
    helpers.showToast({
      type: 'error',
      title: '{{error}}',
      message: '{{error.reject.headcount.plan.detail}}',
    });
  } else {
    helpers.showToast({
      type: 'success',
      title: '{{success}}',
      message: '{{success.reject.headcount.plan.detail}}',
    });
    await actions.refetchUiModelData();
  }
}

export function disableApprove(context: Context, { rowData }): boolean {
  return rowData?.status !== EvstSubmissionStatus.Submitted;
}

export function calculateRaiseBudget(context: Context) {
  const { data, state } = context;
  if (!data.headcountPlan?.startDate) {
    return;
  }

  const selectedRaiseMonths: number = calculateRaisedMonths(context);
  const raiseRate: number = Number(data.headcountPlan.raiseRate ?? 0);
  const raiseBudget: number =
    (Number(data.headcountPlan?.salaryBudget?.amount) / 12) *
    raiseRate *
    selectedRaiseMonths;
  state.raiseBudget = Number(Math.round(raiseBudget * 100) / 100);
  calculateHiringBudget(context);
}

export function calculateRaisedMonths(context: Context): number {
  const { data } = context;

  if (!data.headcountPlan.startDate || !data.headcountPlan.raiseDate) {
    return 0;
  }

  const startDate = PlainDate.from(data.headcountPlan.startDate.toString());
  const raiseDate = PlainDate.from(data.headcountPlan.raiseDate.toString());

  if (PlainDate.compare(raiseDate, startDate) === -1) {
    return 0;
  }

  const monthsDifference = raiseDate.diff(startDate, 'month').months;

  const raisedMonths = 12 - monthsDifference;

  return Math.max(0, raisedMonths);
}

export function calculateHiringBudget(context: Context) {
  const { data, state, helpers, sharedState } = context;
  if (data.headcountPlan?.budgetAmount) {
    const hiringBudget: number =
      Number(data.headcountPlan?.budgetAmount) -
      (state.raiseBudget ?? 0) -
      (Number(data.headcountPlan?.salaryBudget?.amount) ?? 0) +
      (state?.attritionRateBudget ?? 0);

    state.hiringBudget = Number(Math.round(hiringBudget * 100) / 100);

    // Update the headcountPlan data
    helpers.set(
      sharedState.getNodeInstance(data.headcountPlan._nodeReference),
      'data.hiringBudget',
      Number(hiringBudget).toFixed(0)
    );
  } else {
    state.hiringBudget = null;
  }
}

export function getMinDate({ data }: Context) {
  if (!data.headcountPlan?.startDate) {
    return new Date();
  }
  const planStartDate = new Date(data.headcountPlan.startDate.toString());
  return planStartDate; // January 1st of the current year
}

export function getMaxDate({ data }: Context) {
  if (!data.headcountPlan?.startDate) {
    // Return the last day of the current year if startDate is not set
    const currentYear = new Date().getFullYear();
    return new Date(currentYear, 11, 31);
  }
  const planYear = new Date(
    data.headcountPlan.startDate.toString()
  ).getFullYear();
  return new Date(planYear, 11, 31); // December 31st of the current year
}

export function calculateAttritionSavings(context: Context) {
  const { data, state } = context;
  const salaryBudget: number = Number(data.headcountPlan?.salaryBudget?.amount);
  const numberOfHeadcounts = Number(data.salaryInfo.headcountNumber);
  const averageSalary = Number(data.salaryInfo.avgSalary);
  const attritionRate: number = Number(data.headcountPlan?.attritionRate ?? 0);
  const annualAverageSalary: number = 12 * averageSalary;
  const attBudgetByCompensationData =
    attritionRate * numberOfHeadcounts * annualAverageSalary;
  const attBudgetByCustomInput: number = (salaryBudget / 12) * attritionRate;
  const attritionSavings: number =
    attBudgetByCompensationData === 0 && salaryBudget !== 0
      ? attBudgetByCustomInput
      : attBudgetByCompensationData;
  state.attritionRateBudget = Number(Math.round(attritionSavings * 100) / 100);
  calculateHiringBudget(context);
}

export function openCompensationBandModal(context: Context) {
  const { helpers, data } = context;
  helpers.openModal({
    size: 'large',
    template: '/templates/everest.hr.base/uinext/compensationBand/compBandList',
    initialState: {
      year: new Date(data.headcountPlan.startDate.toString()).getFullYear(),
      currency: data.headcountPlan.currency ?? 'EUR',
    },
  });
}

export async function onEditPlanClick(context: Context) {
  const { actions, data, helpers, state } = context;
  if (state.edit) {
    delete data.headcountPlan?._nodeReference;
    const result = await actions.run({
      headcountPlan: {
        action: 'update',
        data: {
          id: state.headcountPlanId,
          ...omit(data.headcountPlan, 'hiringBudget'),
        },
      },
    });
    if (result?.error) {
      helpers.showToast({
        type: 'error',
        title: '{{headcountPlan.update.error.title}}',
        message: '{{headcountPlan.update.error.message}}',
      });
    } else {
      helpers.showToast({
        type: 'success',
        title: '{{headcountPlan.update.success.title}}',
        message: '{{headcountPlan.update.success.message}}',
      });
    }
    await actions.refetchUiModelData();
    state.edit = false;
  } else {
    state.edit = true;
  }
}

export function editButtonLabel(context: Context) {
  return context.state.edit ? '{{save}}' : '{{edit}}';
}

export function onCancelClick(context: Context) {
  context.state.edit = false;
}

export function isAttritionVisible(context: Context) {
  const { state, data } = context;
  return state.edit ? true : Boolean(data.headcountPlan?.attritionRate);
}

export function getDepartmentName(_: Context, { data: rowData }) {
  const departmentCode = rowData?.data?.departmentCode;
  const departmentName = rowData?.data?.departmentName;
  return `${departmentCode} — ${departmentName}`;
}

export async function refreshEstimatedSalaryCost(context: Context) {
  const { data, actions, helpers } = context;
  const result = await actions.run({
    headcountPlan: {
      action: 'refreshEstimatedSalaryCost',
      data: {
        headcountPlanId: data?.headcountPlan?.id,
        budgetAmount: data?.headcountPlan?.budgetAmount,
        startDate: PlainDate.from(data.headcountPlan?.startDate).toISO(),
      },
    },
  });
  if (result.error) {
    helpers.showToast({
      message: '{{salaryEstimateRefreshError}}',
      type: 'error',
      title: '{{error}}',
    });
  } else {
    helpers.showToast({
      message: '{{salaryEstimateRefreshSuccess}}',
      type: 'success',
      title: '{{success}}',
    });
    await actions.refetchUiModelData();
  }
}

export function getTotalBurnRate({ data }: Context) {
  const { departmentTree } = data;
  const totalRow = departmentTree?.footerData?.find(
    (row) => row['data']['departmentName'] === 'Total'
  );
  return totalRow?.['data']?.['currentBurnRate'];
}

export function onSelectSummaryClick({ state, data }: Context) {
  state.tableView = undefined;
  const { departmentTree } = data;
  for (const col of departmentTree.columnDefs) {
    if (col.groupId === 'detail') {
      col.hide = true;
      for (const child of col.children as Record<string, unknown>[]) {
        child.hide = true;
      }
    }
  }
}

export function onSelectDetailClick({ state, data }: Context) {
  state.tableView = 'detail';
  const { departmentTree } = data;
  for (const col of departmentTree.columnDefs) {
    if (col.groupId === 'detail') {
      col.hide = false;
      for (const child of col.children as Record<string, unknown>[]) {
        child.hide = false;
      }
    }
  }
}

export function isDisabledSummaryOption({ state }: Context) {
  return state.tableView === undefined;
}

export function isDisabledDetailOption({ state }: Context) {
  return state.tableView === 'detail';
}

export function onTotalOpeningCellClick(
  ctx: Context,
  rowData: Record<string, unknown>
) {
  const { data: row, colDef } = rowData;
  const { data, helpers, actions } = ctx;
  const departmentId = row?.['nodeId'];
  const headcountPlanDetail = data.headcountPlanDetail.find(
    (d) => d.departmentId === departmentId
  );
  const initialState = {
    headcountPlanDetailId: headcountPlanDetail['id'],
    openHeadcounts: row['data'][colDef['field']],
    currency: data.headcountPlan.currency,
    departmentId,
  };
  helpers.openModal({
    size: 'xsmall',
    template: `/templates/everest.hr.planning/uinext/editOpeningHeadcount`,
    initialState,
    onClose: async () => {
      await actions.refetchUiModelData();
    },
  });
}

export function onDepartmentHeaderClick(
  ctx: Context,
  rowData: Record<string, unknown>
) {
  const { data, helpers, actions } = ctx;
  const headcountPlanDetail = data.headcountPlanDetail?.find(
    (detail) => detail?.departmentId === rowData['data']?.['nodeId']
  );
  if (headcountPlanDetail) {
    helpers.openModal({
      template:
        '/templates/everest.hr.planning/uinext/headcountPlanDetailModal',
      onClose: actions.refetchUiModelData,
      title: rowData.value as string,
      initialState: {
        id: headcountPlanDetail.id,
        mode: 'edit',
        headcountPlanCurrency: data.headcountPlan.currency,
        remainingBudget:
          rowData?.data?.['data']?.newHireCost ??
          data.headcountPlan?.remainingBudget?.amount,
        departmentId: headcountPlanDetail?.departmentId,
      },
    });
  }
}

export function onBudgetAllocationCellClick(
  ctx: Context,
  rowData: Record<string, unknown>
) {
  const { data: row } = rowData;
  const { data, helpers, actions } = ctx;
  const departmentId = row?.['nodeId'];
  const headcountPlanDetail = data.headcountPlanDetail.find(
    (d) => d.departmentId === departmentId
  );
  const initialState = {
    id: headcountPlanDetail['id'],
    headcountPlanCurrency: data.headcountPlan.currency,
    departmentId,
    newHireCost: rowData?.data?.['data']?.['newHireCost'],
    remainingBudget: data.headcountPlan?.remainingBudget?.amount,
    mode: 'edit',
  };
  helpers.openModal({
    size: 'small',
    template: `/templates/everest.hr.planning/uinext/headcountPlanDetailModal`,
    initialState,
    onClose: async () => {
      await actions.refetchUiModelData();
    },
  });
}

export function returnViewMode(context: Context) {
  return !context.state.edit;
}

export function getRowBackgroundColor(
  _: Context,
  { rowData }: Record<string, unknown>
) {
  if (
    rowData?.['data']?.['departmentName'] === 'Avg/Dept' ||
    rowData?.['data']?.['departmentName'] === 'Total'
  ) {
    return 'white';
  }
  return Number(rowData?.['data']?.['remainingBudget']) < 0
    ? 'sharpay'
    : 'white';
}

export function checkRemainingBudget(
  _: Context,
  { rowData }: Record<string, unknown>
) {
  const remainingBudget = new Decimal(
    rowData?.['remainingBudget']?.amount ?? 0
  );
  return Number(remainingBudget) < 0 ? 'sharpay' : 'white';
}

export function isExceededBudget(
  _: Context,
  { rowData }: Record<string, unknown>
) {
  return rowData?.['data']?.['remainingBudget'] < 0 ? 'sharpay' : 'white';
}

export async function onCreateScenarioClick(ctx: Context) {
  const { actions, data, helpers } = ctx;
  helpers.openModal({
    template: '/templates/everest.hr.planning/uinext/createScenario',
    onClose: actions.refetchUiModelData,
    size: 'xsmall',
    title: '{{headcountPlan.create.scenario}}',
    initialState: {
      headcountPlanId: data.headcountPlan.id,
    },
  });
}

export function onCompareClick(ctx: Context, rowData: Record<string, unknown>) {
  const { helpers } = ctx;
  helpers.openModal({
    template: '/templates/everest.hr.planning/uinext/scenarioComparision',
    size: 'large',
    initialState: {
      sourcePlanId: ctx.data.headcountPlan.id,
      targetPlanId: rowData?.['data']?.['id'],
    },
  });
}

export function getScenariosQuery(ctx: Context) {
  const { data } = ctx;
  return data.headcountPlan?.id
    ? {
        where: {
          id: {
            $ne: data.headcountPlan.id,
          },
          startDate: {
            $eq: data.headcountPlan.startDate,
          },
        },
      }
    : undefined;
}

export function getBudgetDiffVariant({
  data: {
    headcountPlan: {
      absBudgetVariance: { amount },
    },
  },
}: Context) {
  return new Decimal(amount).greaterThanOrEqualTo(0) ? 'positive' : 'negative';
}

export function getCommittedCostVariant({ data: { headcountPlan } }: Context) {
  return new Decimal(headcountPlan?.budgetAmount.amount).greaterThanOrEqualTo(
    headcountPlan?.committedCost.amount
  )
    ? 'positive'
    : 'warning';
}

export function showHeadcountLines(context: Context) {
  const { data } = context;
  return data.headcountPlan?.status !== EvstSubmissionStatus.Unsubmitted;
}

export function isMissingCompBands(context: Context) {
  const { data } = context;
  return isEmpty(data.compensationBands);
}

export async function saveDetailChanges({
  helpers,
  actions,
}: Context): Promise<void> {
  await actions.submit({
    onError: () => {
      helpers.showToast({
        type: 'error',
        title: '{{error}}',
        message: '{{headcountPlan.detail.update.error}}',
      });
    },
    onSuccess: async () => {
      helpers.showToast({
        type: 'success',
        title: '{{success}}',
        message: '{{headcountPlan.detail.update.success}}',
      });

      await actions.refetchUiModelData();
    },
  });
}

export function isUnsubmitted(_: Context, { rowData }) {
  return rowData?.status === EvstSubmissionStatus.Unsubmitted;
}
