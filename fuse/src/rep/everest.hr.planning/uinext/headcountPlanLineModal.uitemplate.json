{"version": 2, "uicontroller": "headcountPlanLineModal.uicontroller.ts", "uimodel": {"state": {"mode": "create", "vacancies": 1}, "nodes": {"headcountPlanLine": {"type": "struct", "model": "urn:evst:everest:hr/planning:model/node:HeadcountPlanLine", "query": "@controller:getHeadcountPlanLineQuery()", "fieldList": ["id", "departmentId", "positionId", "entityId", "locationId", "expectedFillDate", "currency", "vacancies", "headcountOwner", "currency", "minimum", "maximum", "midpoint", "forecastCost"]}, "headcountPlanDetail": {"type": "struct", "query": "@controller:getHeadcountPlanDetailQuery()", "model": "urn:evst:everest:hr/planning:model/node:HeadcountPlanDetail", "fieldList": ["id", "departmentId", "midpoint"]}, "associatedCompBand": {"type": "struct", "model": "urn:evst:everest:hr/base:model/node:CompensationBand", "findBestMatchedCompensationBand": "@controller:getAssociatedCompBandQuery()", "fieldList": ["id", "positionId", "departmentId", "entityId", "locationId", "minimum", "maximum", "midpoint", "unit", "currency", "forecast"]}, "entities": {"type": "list", "model": "urn:evst:everest:base:model/node:Entity", "query": {"where": {}}, "fieldList": ["id", "currency"]}}}, "uiview": {"templateType": "generic", "i18n": ["everest.hr.base/hrbase", "headcountPlan"], "config": {"allowRefreshData": "true"}, "title": "@state:title", "sections": {"background": "default", "content": [{"component": "FieldGroup", "section": {"grid": {"size": "12"}, "title": "{{headcountPlan.employment.detail}}", "editing": true}, "props": {"type": "primary", "elements": [{"label": "{{department}}", "value": "@binding:headcountPlanLine.departmentId", "initialValue": "@state:departmentId", "isDisabled": "@controller:getMode()", "semanticTypeUiParams": {"departmentIds": "@state:allocatedDepartmentIds"}, "action": "update"}, {"value": "@binding:headcountPlanLine.positionId", "action": "update"}, {"value": "@binding:headcountPlanLine.entityId", "action": "update"}, {"value": "@binding:headcountPlanLine.locationId", "action": "update"}]}}, {"component": "FieldGroup", "section": {"grid": {"size": "12"}, "title": "{{headcountPlan.payrange}}", "editing": true}, "props": {"elements": [{"component": "InputNumber", "value": "@binding:headcountPlanLine.midpoint", "isEditing": "true", "onChange": "@controller:updateForecostCost", "type": "amount", "parseAs": "currency", "initialValue": "@state:midpoint", "format": "@binding:headcountPlanLine.currency", "action": "update"}, {"value": "@binding:headcountPlanLine.currency", "initialValue": "@state:currency", "action": "update", "isDisabled": "true"}, {"label": "{{unit}}", "value": "Annually", "isDisabled": "true", "action": "update"}]}}, {"component": "FieldGroup", "section": {"grid": {"size": "12"}, "title": "{{headcountPlan.line.detail}}", "editing": true}, "props": {"elements": [{"value": "@binding:headcountPlanLine.headcountOwner", "isEditing": "true"}, {"component": "DatePicker", "label": "{{headcountPlan.starting.month}}", "value": "@binding:headcountPlanLine.expectedFillDate", "picker": "month", "minDate": "@state:minDate", "maxDate": "@state:maxDate", "onChange": "@controller:updateForecostCost"}, {"component": "InputNumber", "label": "{{headcountPlan.vacancies}}", "value": "@state:vacancies", "onChange": "@controller:onVacanciesUpdate", "min": 1, "action": "update", "initialValue": 1, "isDisabled": "@controller:hasLineId()"}, {"component": "InputNumber", "label": "{{headcountPlan.forecast.cost}}", "value": "@binding:headcountPlanLine.forecastCost", "type": "amount", "isDisabled": true, "parseAs": "currency", "format": "@binding:headcountPlanLine.currency", "action": "update"}]}}]}, "actions": {"content": [{"variant": "primary", "label": "@controller:getBtnLabel()", "onClick": "@controller:onPrimaryBtnClick"}]}}}