// @i18n:everest.hr.base/hrbase
// @i18n:headcountPlan
import { PlainDate } from '@everestsystems/datetime';
import { Decimal } from '@everestsystems/decimal';
import type { EvstCurrencyAmount } from '@pkg/everest.base/types/composites/CurrencyAmount';
import { HeadcountPlansUI } from '@pkg/everest.hr.base/types/HeadcountPlans.ui';
import type { CreateHeadcountPlanUiTemplate } from '@pkg/everest.hr.planning/types/uiTemplates/uinext/createHeadcountPlan.ui';

import { EvstHeadcountAllocationType } from '../types/enums/HeadcountAllocationType';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type Any = any;
type CreateHeadcountPlanContext =
  CreateHeadcountPlanUiTemplate.CreateHeadcountPlanContext;

type Context = CreateHeadcountPlanContext & {
  state: {
    headcountPlanId?: string;
  };
};

export async function onCreateClick(context: Context) {
  const { data, actions, helpers } = context;
  const startDate = PlainDate.from(data.headcountPlan.startDate);
  const endDate = new PlainDate(startDate?.year, 12, 31);
  const { headcountPlan } = data;
  try {
    const result = await actions.run({
      headcountPlan: {
        action: 'createHeadcountPlan',
        data: {
          headcountPlan: {
            name: headcountPlan.name,
            startDate: startDate,
            endDate: endDate,
            attritionRate: headcountPlan.attritionRate,
            raiseRate: headcountPlan.raiseRate,
            raiseDate: headcountPlan.raiseDate,
            budgetAmount: {
              amount: headcountPlan?.budgetAmount?.amount
                ? new Decimal(headcountPlan.budgetAmount?.amount)
                : new Decimal(0),
              currency: data.headcountPlan?.currency,
            },
            currency: data.headcountPlan.currency,
            allocationType: EvstHeadcountAllocationType.Department,
          },
        },
      },
    });

    if (result.headcountPlan?.id) {
      helpers.closeModal();
      helpers.navigate({
        to: `/templates/everest.hr.planning/uinext/headcountPlanDetail?headcountPlanId=${result.headcountPlan?.id}`,
        initialState: {
          edit: false,
        },
      });
      helpers.showToast({
        type: 'success',
        title: '{{success}}',
        message: '{{headcountPlan.create.success.message}}',
      });
    } else {
      helpers.showToast({
        type: 'error',
        title: '{{error}}',
        message: '{{headcountPlan.create.error.message}}',
      });
    }
  } catch (error) {
    helpers.showToast({
      type: 'error',
      title: '{{error}}',
      message:
        error instanceof Error
          ? error.message
          : '{{headcountPlan.create.error.message}}',
    });
  }
}

export async function createTableView(
  context: Context,
  planId: number,
  hiringBudget: EvstCurrencyAmount
) {
  const { helpers } = context;
  const entityId = null;

  try {
    const response: Any = await HeadcountPlansUI.create(context, {
      package: 'everest.hr.base',
      entityId,
      tableData: [],
      planId,
      currency: hiringBudget?.currency,
      hiringBudget: Number(hiringBudget?.amount),
    }).run('HeadcountPlans');
    if (response) {
      helpers.showToast({
        type: 'success',
        title: '{{headcountPlan.create.success.title}}',
        message: '{{headcountPlan.create.success.message}}',
      });
    }
  } catch (error) {
    helpers.showToast({
      type: 'error',
      title: '{{headcountPlan.create.error.title}}',
      message:
        error instanceof Error
          ? error.message
          : '{{headcountPlan.create.error.message}}',
    });
    throw error; // Re-throw the error to be caught in the calling function
  }
}

export function getMinYear() {
  return new Date(new Date().getFullYear() - 1, 0, 1);
}

export function setSalaryBudget(context: Context) {
  const { data, helpers, sharedState } = context;
  const { headcountNumber, avgSalary } = data.SalaryCost ?? {};
  if (headcountNumber && avgSalary) {
    const value = {
      amount: Math.round(Number(avgSalary * 100) / 100) * 12 * headcountNumber,
      currency: data.headcountPlan?.currency ?? 'USD',
    };
    helpers.set(
      sharedState.getNodeInstance(data.headcountPlan?._nodeReference),
      'data.salaryBudget',
      value
    );
  }
}

export function getInitialCurrency({ data }: Context) {
  return data.rootEntity?.currency ?? 'USD';
}

export function getHeadcountPlanQuery({ state }: Context) {
  return state?.headcountPlanId
    ? {
        where: {
          id: state.headcountPlanId,
        },
      }
    : undefined;
}

export function isCreatingScenario({ state }: Context) {
  return !!state?.headcountPlanId;
}

export function getInitialYear(_: Context) {
  return new Date(new Date().getFullYear(), 0, 1);
}
