{"version": 2, "uicontroller": "headcountPlanDetailModal.uicontroller.ts", "uimodel": {"state": {"employeeCostSource": "compensationBand", "distributionType": "none", "mode": "create"}, "nodes": {"headcountPlanDetail": {"type": "struct", "query": "@controller:getHeadcountPlanDetailQuery()", "model": "urn:evst:everest:hr/planning:model/node:HeadcountPlanDetail", "fieldList": ["id", "<PERSON><PERSON><PERSON><PERSON>", "headcountCap", "departmentId", "entityId", "headcountPlanId", "midpoint", "minSalary", "max<PERSON><PERSON><PERSON>", "budgetOwner", "currency"]}, "headcountPlan": {"type": "struct", "query": "@controller:getHeadcountPlanDetailQuery()", "model": "urn:evst:everest:hr/planning:model/node:HeadcountPlanDetail", "fieldList": ["id", "<PERSON><PERSON><PERSON><PERSON>", "headcountCap", "departmentId", "entityId", "headcountPlanId", "midpoint", "minSalary", "max<PERSON><PERSON><PERSON>", "budgetOwner", "currency"]}, "headcountPlanLine": {"type": "list", "query": {}, "model": "urn:evst:everest:hr/planning:model/node:HeadcountPlanLine", "fieldList": ["id", "departmentId", "positionId", "entityId", "locationId", "expectedFillDate", "currency", "vacancies", "filledHeadcounts", "remainingHeadcounts", "forecastCost", "headcountOwner", "minimum", "maximum", "midpoint", "currency"]}, "associatedCompBand": {"type": "struct", "model": "urn:evst:everest:hr/base:model/node:CompensationBand", "findBestMatchedCompensationBand": "@controller:getAssociatedCompBandQuery()", "fieldList": ["id", "positionId", "departmentId", "entityId", "locationId", "minimum", "maximum", "midpoint", "unit", "currency", "forecast"]}, "departmentHierarchy": {"type": "struct", "model": "urn:evst:everest:hr/base:model/node:DepartmentHierarchy"}, "meanEmployeeCost": {"type": "struct", "model": "urn:evst:everest:hr/base:model/node:Department", "getMeanEmployeeCostByDepartment": "@controller:getMeanEmployeeCostQuery()"}, "departments": {"type": "list", "query": {}, "model": "urn:evst:everest:hr/base:model/node:Department", "fieldList": ["departmentName", "id"]}}}, "uiview": {"i18n": ["everest.hr.base/hrbase", "headcountPlan"], "sections": {"background": "default", "content": [{"component": "FieldGroup", "section": {"grid": {"size": "12"}, "editing": true}, "props": {"type": "secondary", "elements": [{"label": "{{department}}", "isEditing": false, "value": "@binding:headcountPlanDetail.departmentId"}, {"label": "{{headcountPlan.hiringBudget}}", "component": "InputNumber", "type": "amount", "value": "@binding:headcountPlanDetail.allocatedBudget.amount", "isEditing": true, "parseAs": "currency", "format": "@state:headcountPlanCurrency", "actionSuggestion": "@controller:getMaxBudgetAllocation()"}, {"component": "InputNumber", "value": "@binding:headcountPlanDetail.midpoint.amount", "isEditing": true, "actionSuggestion": "@controller:getSuggestedMidpoint()"}, {"component": "InputNumber", "isEditing": true, "label": "{{headcountPlan.headcountCap}}", "value": "@binding:headcountPlanDetail.headcountCap", "actionSuggestion": "@controller:getSuggestedHeadcountCap()"}, {"value": "@binding:headcountPlanDetail.budgetOwner", "isEditing": true}]}}, {"component": "<PERSON><PERSON>", "visible": "@controller:showWarning()", "section": {"grid": {"size": 12}}, "props": {"variant": "warning", "title": "Warning", "content": "{{headcountPlan.headcountcap.warning}}"}}]}, "actions": {"content": [{"variant": "primary", "label": "{{save.changes}}", "onClick": "@controller:onSaveClick"}]}}}