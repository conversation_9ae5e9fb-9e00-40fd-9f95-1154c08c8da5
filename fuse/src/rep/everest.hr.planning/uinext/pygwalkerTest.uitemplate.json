{"version": 2, "uimodel": {"nodes": {}}, "uiview": {"title": "simple report builder", "list": true, "config": {"autoRefreshData": true, "stretch": true}, "header": {"content": {"title": "Everest Report Builder"}}, "i18n": "everest.python/python", "sections": {"content": [{"component": "<PERSON><PERSON><PERSON>", "section": {"grid": {"size": "12"}}, "props": {"src": "/api/apps/everest.hr.planning/py/pygwalkerTest.streamlit?embed=true&embed_options=light_theme", "height": 1400}}]}}}