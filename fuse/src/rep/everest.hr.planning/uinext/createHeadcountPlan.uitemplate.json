{"version": 2, "uicontroller": ["createHeadcountPlan.uicontroller.ts", "headcountPlanDetail.uicontroller.ts"], "uimodel": {"state": {"salaryBudget": "@controller:setSalaryBudget()", "isScenarioDisabled": true}, "nodes": {"rootEntity": {"type": "struct", "model": "urn:evst:everest:base:model/node:Entity", "query": {"where": {"nonLegal": true, "Entity-EntityHierarchy": {"parentId": null}}, "orderBy": [{"field": "lastModifiedDate", "ordering": "desc"}], "take": 1}, "fieldList": ["id", "currency"]}, "headcountPlan": {"type": "struct", "modelId": "everest.hr.planning/HeadcountPlanModel.HeadcountPlan", "fieldList": ["id", "name", "budgetAmount", "startDate", "currency", "allocationType", "<PERSON><PERSON><PERSON><PERSON>", "attritionRate", "raiseRate", "raiseDate", "salaryBudget"]}, "headcountInfo": {"type": "list", "query": {"where": {}}, "pagination": true, "model": "urn:evst:everest:hr/planning:model/node:HeadcountPlan", "fieldList": ["id", "startDate", "name", "scenario", "budgetAmount", "currency", "allocationType"]}, "HeadcountPlans": {"type": "struct", "modelId": "everest.hr.base/HeadcountPlansModel.HeadcountPlans"}, "SalaryCost": {"type": "struct", "modelId": "everest.hr.planning/HeadcountPlanModel.HeadcountPlan", "getTotalHeadcounts": {"startDate": "@binding:headcountPlan.startDate"}}}}, "uiview": {"i18n": ["everest.hr.base/hrbase", "headcountPlan"], "title": "{{headcountPlan.new.title}}", "sections": [{"component": "ActionGroup", "section": {"editing": true, "grid": {"size": "12"}}, "props": {"spacing": "small", "buttonActionsPosition": "right", "descriptionPosition": "rightSide", "data": "@binding:headcountPlan", "lines": [{"label": "{{headcountPlan.name}} *", "field": "name", "fieldProps": {"component": "Input", "value": "@binding:headcountPlan.name", "isDisabled": "@controller:isCreatingScenario()"}}, {"label": "{{currency}} *", "field": "currency", "fieldProps": {"isDisabled": true, "value": "@binding:headcountPlan.currency", "initialValue": "@controller:getInitialCurrency()"}}, {"label": "{{year}} *", "field": "startDate", "fieldProps": {"value": "@binding:headcountPlan.startDate", "component": "DatePicker", "minDate": "@controller:getMinYear()", "picker": "year", "isDisabled": "@controller:isCreatingScenario()", "initialValue": "@controller:getInitialYear()"}}, {"label": "{{headcountPlan.budgetAmount}}", "field": "budgetAmount", "fieldProps": {"component": "InputNumber", "value": "@binding:headcountPlan.budgetAmount", "type": "amount"}}]}}, {"component": "ActionGroup", "section": {"title": "{{headcountPlan.financial.params}}", "editing": true, "grid": {"size": "12"}}, "props": {"spacing": "small", "buttonActionsPosition": "right", "descriptionPosition": "rightSide", "data": "@binding:headcountPlan", "lines": [{"label": "{{headcountPlan.salaryRaiseRate}}", "field": "raiseRate", "fieldProps": {"component": "InputNumber", "value": "@binding:headcountPlan.raiseRate", "type": "percentage", "min": 0, "max": 100}}, {"label": "{{headcountPlan.salaryRaiseDate}}", "field": "startDate", "fieldProps": {"value": "@binding:headcountPlan.raiseDate", "component": "DatePicker", "minDate": "@controller:getMinDate()", "maxDate": "@controller:getMaxDate()", "picker": "month"}}, {"label": "{{headcountPlan.attritionRate}}", "field": "attritionRate", "fieldProps": {"component": "InputNumber", "value": "@binding:headcountPlan.attritionRate", "type": "percentage", "min": 0, "max": 100}}]}}], "actions": [{"variant": "primary", "label": "{{create}}", "onClick": "@controller:onCreateClick"}], "config": {"stretch": false}}}