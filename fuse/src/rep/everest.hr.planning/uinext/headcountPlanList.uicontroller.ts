// @i18n:everest.hr.base/hrbase
// @i18n:headcountPlan
import type { HeadcountPlanListUiTemplate } from '@pkg/everest.hr.planning/types/uiTemplates/uinext/headcountPlanList.ui';

import type { HeadcountPlan } from '../types/HeadcountPlan';

type HeadcountPlanListContext =
  HeadcountPlanListUiTemplate.HeadcountPlanListContext;

type Context = HeadcountPlanListContext;

export function openCreateDialog(context: Context): Promise<boolean> {
  const { helpers, data } = context;
  return data.compensationBands?.length === 0
    ? new Promise((resolve) => {
        helpers.openDialog({
          title: 'Create compensation bands?',
          message:
            'Compensation bands currently do not exist. These help in providing accurate cost estimations.',
          variant: 'info',
          actions: {
            secondary: {
              label: 'No',
              onClick: () => {
                resolve(true);
              },
            },
            primary: {
              label: 'Yes',
              onClick: () => {
                helpers.openModal({
                  template:
                    '/templates/everest.hr.base/uinext/compensationBand/compBandList',
                  initialState: { fromCreateHCPlan: true },
                  onClose: () => {
                    resolve(true);
                  },
                  onModalSubmit: () => {
                    resolve(true);
                  },
                });
              },
            },
          },
        });
      })
    : new Promise((resolve) => {
        resolve(true);
      });
}

export async function createHeadcountPlan(context: Context) {
  const { helpers, actions } = context;
  const compBandCreate = await openCreateDialog(context);
  if (!compBandCreate) {
    return;
  }
  helpers.closeModal();
  helpers.openModal({
    template: '/templates/everest.hr.planning/uinext/createHeadcountPlan',
    onClose: actions.refetchUiModelData,
    size: 'xsmall',
  });
}

export function openHeadcountDetail(
  { helpers }: Context,
  row: HeadcountPlan.ReadReturnType
): void {
  helpers.navigate({
    to: `/templates/everest.hr.planning/uinext/headcountPlanDetail?headcountPlanId=${row.id}`,
  });
}

export function getFormat(
  context: Context,
  { rowData }: Record<string, unknown>
) {
  return rowData['currency'];
}

export function getMatchers(): Record<string, unknown> {
  return {
    '{{unsubmitted}}': 'shuttle-grey',
    '{{approved}}': 'shamrock',
    '{{rejected}}': 'amaranth',
    '{{submitted}}': 'jaffa',
  };
}

export async function onCreateScenario(
  { actions, helpers }: Context,
  rowData: Record<string, unknown>
) {
  const headcountPlanId = rowData.data?.['id'];
  helpers.openModal({
    template: '/templates/everest.hr.planning/uinext/createScenario',
    onClose: actions.refetchUiModelData,
    size: 'xsmall',
    initialState: {
      headcountPlanId,
    },
  });
}
