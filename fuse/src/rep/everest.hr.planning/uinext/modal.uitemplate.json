{"version": 2, "uicontroller": ["modal.uicontroller.ts"], "uimodel": {"state": {"modalType": "", "currentValue": null, "newValue": null, "year": null, "department": null}, "nodes": {}}, "uiview": {"i18n": ["everest.hr.base/hrbase", "everest.hr.base/headcount"], "sections": [{"section": {"editing": true, "grid": {"size": "12"}}, "component": "FieldGroup", "props": {"elements": [{"component": "<PERSON><PERSON><PERSON>", "label": "@controller:getCurrentValueLabel()", "value": "@controller:formatCurrentValue()"}, {"component": "@controller:getInputComponent()", "label": "@controller:getNewValueLabel()", "placeholder": "@controller:getPlaceholder()", "value": "@state:newValue", "onChange": "@controller:setNewValue", "isEditing": true}]}}], "actions": [{"variant": "secondary", "label": "Cancel", "onClick": "@controller:cancel"}, {"variant": "primary", "label": "Save", "onClick": "@controller:save"}]}}