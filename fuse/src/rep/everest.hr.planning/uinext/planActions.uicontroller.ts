// @i18n:everest.hr.base/hrbase
import { HeadcountPlansUI } from '@pkg/everest.hr.base/types/HeadcountPlans.ui';

import type { Context } from './headcountPlanDetail.uicontroller';
import { isCreateMode, isEditMode } from './stateHelpers.uicontroller';

/* eslint-disable @typescript-eslint/no-explicit-any */
export async function savePlan(context: Context) {
  const { helpers, state, data }: any = context;

  state.saving = true;

  if (!state.entityId) {
    return;
  }

  helpers.showNotificationMessage({
    key: 'headcountPlan',
    type: 'loading',
    message: '{{headcountPlan.create}}',
    duration: 20,
  });

  try {
    const planResponse = await HeadcountPlansUI.create(context, {
      package: 'everest.hr.base',
      entityId: state.entityId,
      tableData: data.ReportData,
    });

    if (planResponse && 'id' in planResponse) {
      state.planId = planResponse.id;
      helpers.showNotificationMessage({
        key: 'headcountPlan',
        type: 'success',
        message: '{{headcountPlan.success}}',
        duration: 3,
      });
      goToPage(context, 'view');
    } else {
      throw new Error('Invalid response from HeadcountPlansUI.create');
    }
  } catch {
    helpers.showNotificationMessage({
      key: 'headcountPlan',
      type: 'error',
      message: '{{headcountPlan.error}}',
      duration: 5,
    });
  } finally {
    state.saving = false;
  }
}

export async function editPlan(context: Context) {
  const { helpers, state, data }: any = context;

  state.saving = true;

  if (!state.entityId) {
    return;
  }

  helpers.showNotificationMessage({
    key: 'headcountPlan',
    type: 'loading',
    message: '{{headcountPlan.edit.create}}',
    duration: 20,
  });

  try {
    const planResponse = await HeadcountPlansUI.update(
      context,
      { id: state.planId },
      {
        package: 'everest.hr.base',
        entityId: state.entityId,
        tableData: data.ReportData,
        budgetId: data.HeadcountPlanBudget.id,
      }
    );

    if (planResponse && 'id' in planResponse) {
      state.planId = planResponse.id;
      helpers.showNotificationMessage({
        key: 'headcountPlan',
        type: 'success',
        message: '{{headcountPlan.edit.success}}',
        duration: 3,
      });
      goToPage(context, 'view');
    } else {
      throw new Error('Invalid response from HeadcountPlansUI.update');
    }
  } catch {
    helpers.showNotificationMessage({
      key: 'headcountPlan',
      type: 'error',
      message: '{{headcountPlan.edit.error}}',
      duration: 5,
    });
  } finally {
    state.saving = false;
  }
}

export async function executeAction(context: Context) {
  await (isCreateMode(context)
    ? savePlan(context)
    : isEditMode(context)
      ? editPlan(context)
      : goToPage(context, 'edit'));
}

export function cancel(context: Context) {
  goToPage(context, 'view');
}

export function goToPage(context: Context, mode: string) {
  const { state, helpers } = context;

  const config: any = {
    to: `/templates/everest.hr.planning/uinext/headcountPlanDetail?mode=${mode}`,
    closeCurrentTab: true,
  };

  if (mode !== 'create') {
    config.initialState = {
      mode,
      planId: state.planId,
      entityId: state.entityId,
      headerId: state.headerId,
      dateRange: state.dateRange,
    };
  }

  helpers.navigate(config);
}
