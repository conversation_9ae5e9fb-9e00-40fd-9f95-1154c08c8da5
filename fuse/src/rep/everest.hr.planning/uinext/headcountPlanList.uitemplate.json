{"version": 2, "uicontroller": "headcountPlanList.uicontroller.ts", "uimodel": {"nodes": {"headcountPlans": {"type": "list", "query": {}, "pagination": true, "model": "urn:evst:everest:hr/planning:model/node:HeadcountPlan", "fieldList": ["id", "startDate", "name", "budgetAmount", "plannedHeadcounts", "status", "committedCost", "abs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hcGrowthRate"]}, "compensationBands": {"type": "list", "modelId": "everest.hr.base/CompensationBandModel.CompensationBand", "query": {}}}}, "uiview": {"layout": "everest.base.ui/layouts/uilayout/list", "props": {"title": "{{headcountPlan.title}}", "i18n": ["headcountPlan", "everest.hr.base/hrbase"], "onCreateClick": "@controller:createHeadcountPlan", "node": "headcountPlans", "onRowClicked": "@controller:openHeadcountDetail", "rowSelection": true, "rowActions": {"columnPosition": 0, "width": "85", "initialDepth": 0, "actions": [{"label": "{{headcountPlan.create.scenario}}", "variant": "primary", "onClick": "@controller:onCreateScenario"}]}, "columns": [{"headerName": "{{name}}", "field": "name"}, {"field": "status", "cellVariant": {"variant": "badge", "matchers": "@controller:getMatchers()"}}, {"headerName": "{{headcountPlan.budgetAmount}}", "field": "budgetAmount", "fieldProps": {"parseAs": "currency", "type": "amount", "format": "@controller:getFormat"}}, {"headerName": "{{headcountPlan.committedCost}}", "field": "committedCost", "fieldProps": {"parseAs": "currency", "type": "amount", "format": "@controller:getFormat"}}, "abs<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "plannedHeadcounts", "hcGrowthRate", {"headerName": "{{startDate}}", "field": "startDate"}]}}}