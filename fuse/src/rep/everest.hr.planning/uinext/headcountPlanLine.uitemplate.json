{"version": 2, "uimodel": {"state": {"mode": "view"}, "nodes": {"headcountPlanLine": {"type": "struct", "query": {"where": {"id": "@state:param.id"}}, "model": "urn:evst:everest:hr/planning:model/node:HeadcountPlanLine", "fieldList": ["id", "headcountPlanDetailId", "departmentId", "positionId", "entityId", "locationId", "expectedFillDate", "currency", "vacancies", "filledHeadcounts", "forecastCost", "headcountOwner", "minimum", "maximum", "midpoint", "HeadcountPlanLine-Job.employeeId", "HeadcountPlanLine-Department.departmentName"]}, "associatedCompBand": {"type": "struct", "model": "urn:evst:everest:hr/base:model/node:CompensationBand", "findBestMatchedCompensationBand": "@controller:getAssociatedCompBandQuery()", "fieldList": ["id", "positionId", "departmentId", "entityId", "locationId", "minimum", "maximum", "midpoint", "unit", "currency", "forecast"]}, "openHeadcounts": {"type": "list", "modelId": "everest.hr.planning/HeadcountPlanLineJobMappingModel.HeadcountPlanLineJobMapping", "parent": "headcountPlanLine", "joinKey": "id-headcountPlanLineId", "fieldList": ["id", "jobId", "headcountPlanLineId", "recruitmentId", "HeadcountPlanLineJobMapping-Recruitment.deadline", "recruitmentStatus", "recruitmentNumber", "employeeId", "HeadcountPlanLineJobMapping-Employee.name"]}}}, "uicontroller": ["headcountPlanLine.uicontroller.ts", "headcountPlanLineModal.uicontroller.ts"], "uiview": {"templateType": "details", "title": "@controller:getTitle()", "i18n": ["everest.hr.base/hrbase", "headcountPlan"], "config": {"allowRefreshData": "true", "autoRefreshData": "true"}, "header": {"content": {"title": "@controller:getTitle()"}}, "actions": {"content": [{"label": "@controller:getEditLabel()", "onClick": "@controller:editDetails", "variant": "primary"}, {"label": "{{cancel}}", "onClick": "@controller:cancel", "variant": "secondary", "visible": "@controller:isEditing()"}]}, "sections": {"content": [{"component": "FieldGroup", "section": {"grid": {"size": "12"}, "editing": "@controller:isEditing()"}, "props": {"type": "primary", "elements": [{"label": "{{currency}}", "value": "@binding:headcountPlanLine.currency", "isDisabled": true}, {"value": "@binding:headcountPlanLine.positionId", "action": "update"}, {"value": "@binding:headcountPlanLine.entityId", "action": "update"}, {"value": "@binding:headcountPlanLine.locationId", "action": "update"}, {"component": "InputNumber", "actionSuggestion": "@controller:getSuggestedMinimum()", "initialValue": "@controller:getInitialMinimum()", "value": "@binding:headcountPlanLine.minimum", "onChange": "@controller:onMinChange", "type": "amount", "action": "update", "parseAs": "currency", "format": "@binding:headcountPlanLine.currency"}, {"component": "InputNumber", "initialValue": "@controller:getInitialMaximum()", "value": "@binding:headcountPlanLine.maximum", "actionSuggestion": "@controller:getSuggestedMaximum()", "onChange": "@controller:onMaxChange", "type": "amount", "action": "update", "parseAs": "currency", "format": "@binding:headcountPlanLine.currency"}, {"component": "InputNumber", "value": "@binding:headcountPlanLine.midpoint", "isDisabled": "true", "onChange": "@controller:updateForecostCost", "type": "amount", "parseAs": "currency", "format": "@binding:headcountPlanLine.currency"}, {"label": "{{unit}}", "value": "Annually", "isDisabled": "true"}, {"component": "InputNumber", "label": "{{headcountPlan.forecast.cost}}", "value": "@binding:headcountPlanLine.forecastCost", "type": "amount", "parseAs": "currency", "isDisabled": true, "format": "@binding:headcountPlanLine.currency"}, {"component": "InputNumber", "action": "update", "value": "@binding:headcountPlanLine.vacancies"}, {"label": "{{startDate}}", "value": "@binding:headcountPlanLine.expectedFillDate", "action": "update", "component": "DatePicker", "picker": "month"}, {"value": "@binding:headcountPlanLine.headcountOwner", "action": "update"}]}}, {"component": "Table", "customId": "open-headcounts", "section": {"title": "{{headcountPlan.open.headcounts}}", "actions": [{"label": "{{headcountPlan.recruit}}", "variant": "primary", "onClick": "@controller:createRecruitment", "disabled": "@controller:isSelectedRowsDisabled()"}], "editing": false, "grid": {"size": "12"}}, "props": {"onRowSelectionChanged": "@controller:onSelectPlanLine", "data": "@binding:openHeadcounts", "variant": "dark", "fullWidth": false, "rowSelection": true, "rowActions": {"minWidth": 85, "columnPosition": -1, "actions": []}, "columns": [{"headerName": "{{status}}", "field": "id", "valueGetter": "@controller:getOpenHeadcountStatus", "cellVariant": {"variant": "badge", "matchers": "@controller:getStatusMatchers()"}}, {"headerName": "{{headcountPlan.associated.recruitment}}", "field": "recruitmentNumber", "cellLinkEmphasized": true, "cellNavigation": {"url": "/templates/everest.hr.base/uinext/recruitment/recruitment", "idProp": "recruitmentId"}, "fieldProps": {"nullContent": "custom", "customNullContent": "{{headcountPlan.tbd}}"}}, {"headerName": "{{headcountPlan.filled.employee}}", "field": "HeadcountPlanLineJobMapping-Employee.name", "cellNavigation": {"url": "/templates/everest.hr.base/uinext/employee/employee", "idProp": "employeeId"}, "fieldProps": {"nullContent": "custom", "customNullContent": "{{headcountPlan.tbd}}"}}]}}]}}}