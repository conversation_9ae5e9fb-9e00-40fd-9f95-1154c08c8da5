// @i18n:headcountPlan
// @i18n:everest.hr.base/hrbase
import { PlainDate } from '@everestsystems/datetime';
import type { Recruitment } from '@pkg/everest.hr.base/types/Recruitment';
import type { HeadcountPlanLine } from '@pkg/everest.hr.planning/types/HeadcountPlanLine';
import type { HeadcountPlanLineJobMapping } from '@pkg/everest.hr.planning/types/HeadcountPlanLineJobMapping';
import type { HeadcountPlanLineUiTemplate } from '@pkg/everest.hr.planning/types/uiTemplates/uinext/headcountPlanLine.ui';
import { isEmpty } from 'lodash';

type HeadcountPlanLineContext =
  HeadcountPlanLineUiTemplate.HeadcountPlanLineContext;

type Context = HeadcountPlanLineContext & {
  state: {
    // you can extend and improve the state type here
    message: string;
    id: number;
    selectedRows: number[];
  };
};

type HeadcountPlanLineRow = {
  data: HeadcountPlanLine.HeadcountPlanLineWithAssociation;
};

type ChildrenDetailRowType = {
  data: HeadcountPlanLineJobMapping.HeadcountPlanLineJobMappingWithAssociation;
};

export function isSelectedRowsDisabled({
  state,
  data: { openHeadcounts },
}: Context): boolean {
  const selectedHeadcounts = new Set(state.selectedRows);
  const invalidSelectedRow = openHeadcounts?.some(
    ({ id, recruitmentId }) => recruitmentId && selectedHeadcounts.has(id)
  );
  return isEmpty(state.selectedRows) || invalidSelectedRow;
}

export function onSelectPlanLine(
  { state }: Context,
  selectedRows: HeadcountPlanLineRow[]
) {
  state.selectedRows = selectedRows?.map(({ data: { id } }) => id) ?? [];
}

export function openRecruitment(
  _: Context,
  { data: row }: ChildrenDetailRowType
) {
  return row?.recruitmentId
    ? {
        url: `/templates/everest.hr.base/uinext/recruitment/recruitment`,
        modal: false,
        idProp: 'recruitmentId',
      }
    : undefined;
}

export async function onDeleteHeadcountLineClick(
  { actions }: Context,
  { data: row }: HeadcountPlanLineRow
) {
  await actions.run({
    headcountPlanLine: {
      action: 'delete',
      data: {
        id: row.id,
      },
    },
  });
}

export async function openHeadcount(
  { helpers, actions }: Context,
  { data: row }: HeadcountPlanLineRow
) {
  helpers.openModal({
    template: '/templates/everest.hr.base/uinext/recruitment/createRecruitment',
    initialState: {
      creationType: 'headcount',
      headcountPlanLine: {
        departmentId: row.departmentId,
        positionId: row.positionId,
        entityId: row.entityId,
        locationId: row.locationId,
        expectedFillDate: PlainDate.from(row.expectedFillDate).toISO(),
        vacancies: row.vacancies,
        hiringManagerEmployeeId: row?.['HeadcountPlanLine-Job']?.employeeId,
        id: row.id,
      },
    },
    onModalSubmit: async (recruitment: Recruitment.ReadReturnType) => {
      helpers.closeModal();
      await actions.run({
        headcountPlanLine: {
          action: 'matchRecruitmentToHeadcountPlanLine',
          data: {
            headcountPlanLineId: row.id,
            recruitmentId: recruitment.id,
          },
        },
      });
      await actions.refetchUiModelData();
    },
  });
}

export function getStatusMatchers() {
  return {
    'In-Progress': 'warning',
    Done: 'success',
    Created: 'neutral',
  };
}

export function viewRecruitment(
  { helpers }: Context,
  { data: row }: ChildrenDetailRowType
) {
  helpers.navigate({
    to: `/templates/everest.hr.base/uinext/recruitment/recruitment?id=${row?.recruitmentId}`,
    initialState: { mode: 'view' },
    nodeParams: {
      Applicants: {
        filters: {
          status: {
            $in: ['new', 'inProcess'],
          },
        },
      },
    },
  });
}

export function getOpenHeadcountStatus(
  _: Context,
  { data: row }: ChildrenDetailRowType
) {
  if (!row?.recruitmentId) {
    return 'Created';
  } else if (row?.employeeId) {
    return 'Done';
  } else {
    return 'In-Progress';
  }
}

export async function createRecruitment({
  helpers,
  actions,
  state,
  data,
}: Context) {
  const { headcountPlanLine, openHeadcounts } = data;
  const selectedHeadcounts = new Set(state.selectedRows);
  const invalidSelectedRow = openHeadcounts?.some(
    ({ id, recruitmentId }) => recruitmentId && selectedHeadcounts.has(id)
  );
  if (invalidSelectedRow) {
    helpers.showNotificationMessage({
      type: 'error',
      message: '{{headcountPlan.associated.recruitment.error}}',
    });
    return;
  }
  helpers.openModal({
    template: '/templates/everest.hr.base/uinext/recruitment/createRecruitment',
    initialState: {
      creationType: 'headcount',
      selectedOpenHeadcounts: state.selectedRows,
      headcountPlanLine: {
        departmentId: headcountPlanLine.departmentId,
        positionId: headcountPlanLine.positionId,
        entityId: headcountPlanLine.entityId,
        locationId: headcountPlanLine.locationId,
        expectedFillDate: PlainDate.from(
          headcountPlanLine.expectedFillDate
        ).toISO(),
        vacancies: state.selectedRows?.length,
        hiringManagerEmployeeId:
          headcountPlanLine?.['HeadcountPlanLine-Job']?.employeeId,
        id: headcountPlanLine.id,
      },
    },
    onModalSubmit: async (recruitment: Recruitment.ReadReturnType) => {
      helpers.closeModal();
      await actions.run({
        openHeadcounts: {
          action: 'updateMany',
          data: {
            where: {
              id: { $in: state.selectedRows },
              recruitmentId: {
                $eq: null,
              },
            },
            data: { recruitmentId: recruitment.id },
          },
        },
      });
      await actions.refetchUiModelData();
    },
  });
}

export function getTitle({ data }: Context) {
  const { headcountPlanLine } = data;
  return `{{headcountPlan.line}}: ${headcountPlanLine?.['HeadcountPlanLine-Department']?.departmentName}`;
}

export function isEditing({ state }: Context) {
  return state.mode === 'edit';
}

export async function editDetails({ state, actions }: Context) {
  if (state.mode === 'edit') {
    await actions.submit({});
    state.mode = 'view';
  } else {
    state.mode = 'edit';
  }
}

export function getEditLabel({ state }: Context) {
  return state.mode === 'edit' ? '{{save}}' : '{{edit.details}}';
}

export function cancel({ state }: Context) {
  state.mode = 'view';
}

export function getInitialMaximum({
  data: { associatedCompBand, headcountPlanLine },
}: Context) {
  return associatedCompBand?.maximum ?? headcountPlanLine?.maximum;
}

export function getInitialMinimum({
  data: { associatedCompBand, headcountPlanLine },
}: Context) {
  return associatedCompBand?.minimum ?? headcountPlanLine?.minimum;
}
