// @i18n:headcountPlan
// @i18n:everest.hr.base/hrbase
import type { CreateScenarioContext } from '@pkg/everest.hr.planning/types/uiTemplates/uinext/createScenario.ui';
type Context = CreateScenarioContext & {
  state: {
    headcountPlanId: number;
    scenarioName: string;
    copyBudgetAllocation: boolean;
    copyOpenHeadcount: boolean;
  };
};

export async function onCreateScenarioClick(context: Context) {
  const { data, actions, helpers, state } = context;
  const result = await actions.run({
    headcountPlan: {
      action: 'createScenario',
      data: {
        basePlanId: state.headcountPlanId,
        name: data.headcountPlan.name,
        newBudgetAmount: data.headcountPlan.budgetAmount.amount,
        options: {
          copyOpenHeadcount: state.copyOpenHeadcount,
          copyBudgetAllocation: state.copyBudgetAllocation,
        },
      },
    },
  });

  if (result?.error) {
    helpers.showToast({
      type: 'error',
      title: '{{error}}',
      message: '{{headcountPlan.failed.scenario.error.message}}',
    });
  } else {
    helpers.closeModal();
    helpers.navigate({
      to: `/templates/everest.hr.planning/uinext/headcountPlanDetail?headcountPlanId=${result.headcountPlan?.id}`,
      initialState: {
        edit: false,
      },
    });
    helpers.showToast({
      type: 'success',
      title: '{{success}}',
      message: '{{headcountPlan.created.scenario.message}}',
    });
  }
}

export function onCopyBudgetAllocationChange(ctx: Context) {
  const { state, helpers } = ctx;
  helpers.set(state, 'copyBudgetAllocation', !state.copyBudgetAllocation);
}

export function onCopyOpenHeadcountChange(ctx: Context) {
  const { state, helpers } = ctx;
  helpers.set(state, 'copyOpenHeadcount', !state.copyOpenHeadcount);
}

export function isDisabledCopyOpenHeadcount(ctx: Context) {
  const { state } = ctx;
  return !state.copyBudgetAllocation;
}

export function getSectionTitle(ctx: Context) {
  const { data } = ctx;
  return `{{headcountPlan.copy.from}} ${data.baseHeadcountPlan?.name}`;
}
