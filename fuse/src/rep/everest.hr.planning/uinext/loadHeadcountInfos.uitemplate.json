{"version": 2, "uimodel": {"nodes": {}}, "uiview": {"title": "load headcounts", "list": true, "config": {"autoRefreshData": true, "stretch": true}, "header": {"content": {"title": "load headcounts"}}, "i18n": "everest.python/python", "sections": {"content": [{"component": "<PERSON><PERSON><PERSON>", "section": {"grid": {"size": "12"}}, "props": {"src": "/api/apps/everest.hr.planning/py/convertHeadcount.streamlit?embed=true&embed_options=light_theme", "height": 1400}}]}}}