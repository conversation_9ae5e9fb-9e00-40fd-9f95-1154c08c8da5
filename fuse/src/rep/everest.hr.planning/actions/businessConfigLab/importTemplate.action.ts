import type { ISession } from '@everestsystems/content-core';
import { getTranslations } from '@everestsystems/content-core';
import { HeadcountHighlevelPlanData } from '@pkg/everest.hr.planning/types/HeadcountHighlevelPlanData';
import { TopLevelParameter } from '@pkg/everest.hr.planning/types/TopLevelParameter';

export default async function importTemplate(
  env: ISession,
  sourceConfigId: number,
  targetConfigurationId: number
): Promise<void> {
  const sourceConfiguration = await TopLevelParameter.query(
    env,
    {
      where: {
        id: sourceConfigId,
      },
    },
    'ALL_FIELDS'
  );

  const rawTranslations = await getTranslations(
    env,
    [
      'configurationNotFound',
      'sourceConfigurationHasNoData',
      'noMatchingYearsFound',
    ],
    'everest.hr.planning/businessConfigLab'
  );

  if (!sourceConfiguration) {
    throw new Error(rawTranslations[0]);
  }

  const sourceData = await HeadcountHighlevelPlanData.query(
    env,
    {
      where: {
        configurationId: sourceConfigId,
      },
    },
    [
      'year',
      'subscriptionCostPercentage',
      'subscriptionCost',
      'servicePercentOfAcv',
      'serviceEmployees',
      'revenuePerRep',
      'reps',
      'repToMgrRatio',
      'marketingPeople',
      'gACostPercent',
      'gACost',
      'devPeople',
      'devCostPerEmp',
      'avgNewCustomerDealSize',
      'configurationId',
    ]
  );

  if (!sourceData || sourceData.length === 0) {
    throw new Error(rawTranslations[1]);
  }

  const targetData = await HeadcountHighlevelPlanData.query(
    env,
    {
      where: {
        configurationId: targetConfigurationId,
      },
    },
    [
      'id',
      'year',
      'subscriptionCostPercentage',
      'subscriptionCost',
      'servicePercentOfAcv',
      'serviceEmployees',
      'revenuePerRep',
      'reps',
      'repToMgrRatio',
      'marketingPeople',
      'gACostPercent',
      'gACost',
      'devPeople',
      'devCostPerEmp',
      'avgNewCustomerDealSize',
      'configurationId',
    ]
  );

  const sourceYears = new Set<number>();
  for (const item of sourceData) {
    if (item.year && typeof item.year === 'number') {
      sourceYears.add(item.year);
    }
  }

  const targetYears = new Set<number>();
  if (targetData) {
    for (const item of targetData) {
      if (item.year && typeof item.year === 'number') {
        targetYears.add(item.year);
      }
    }
  }

  const newData = sourceData
    .filter((entry) => entry.year && targetYears.has(entry.year))
    .map((entry) => {
      return {
        ...entry,
        configurationId: targetConfigurationId,
      };
    });

  if (newData.length === 0) {
    throw new Error(rawTranslations[2]);
  }

  if (targetData && targetData.length > 0) {
    for (const item of targetData) {
      if (item.year && sourceYears.has(item.year)) {
        await HeadcountHighlevelPlanData.Delete(env, {
          id: item.id,
        });
      }
    }
  }

  await HeadcountHighlevelPlanData.createMany(env, newData);
}
