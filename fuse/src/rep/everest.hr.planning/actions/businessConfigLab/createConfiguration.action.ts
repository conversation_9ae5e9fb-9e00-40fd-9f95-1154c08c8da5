import type { ISession } from '@everestsystems/content-core';
import { getTranslations } from '@everestsystems/content-core';
import { HeadcountHighlevelPlanData } from '@pkg/everest.hr.planning/types/HeadcountHighlevelPlanData';
import { TopLevelParameter } from '@pkg/everest.hr.planning/types/TopLevelParameter';

export default async function createConfiguration(
  env: ISession,
  parameters: Partial<TopLevelParameter.TopLevelParameter>
): Promise<void> {
  const topLevelData = await TopLevelParameter.query(
    env,
    {
      where: {
        configurationName: parameters.configurationName,
      },
    },
    'ALL_FIELDS'
  );

  const rawTranslations = await getTranslations(
    env,
    ['configurationNameAlreadyExists', 'unexpectedErrorOccurred'],
    'everest.hr.planning/businessConfigLab'
  );

  const configExists = topLevelData.some(
    (config) => config.configurationName === parameters.configurationName
  );

  if (configExists) {
    throw new Error(rawTranslations[0]);
  }

  await TopLevelParameter.create(env, parameters);

  const newTopLevelData = await TopLevelParameter.query(
    env,
    {
      where: {
        configurationName: parameters.configurationName,
      },
    },
    'ALL_FIELDS'
  );

  const headcountData = [];
  for (
    let year = parameters.startYear;
    year < parameters.startYear + parameters.noYears;
    year++
  ) {
    headcountData.push({
      configurationId: newTopLevelData[0].id,
      year: year,
      reps: 0,
      devPeople: 0,
      repToMgrRatio: 0,
      gACost: 0,
      gACostPercent: 0,
      subscriptionCost: 0,
      subscriptionCostPercentage: 0,
      avgNewCustomerDealSize: 0,
      devCostPerEmp: 0,
      marketingPeople: 0,
      revenuePerRep: 0,
      serviceEmployees: 0,
      servicePercentOfAcv: 0,
    });
  }

  if (headcountData.length === parameters.noYears) {
    await HeadcountHighlevelPlanData.createMany(env, headcountData);
  } else {
    throw new Error(rawTranslations[1]);
  }
}
