{"version": 3, "uicontroller": "softwareEntitlementCloudCost.uicontroller.ts", "uimodel": {"presentation": {"urn": "urn:evst:everest:inv/itam/integration/cloudcosts:presentation:uinext/softwareEntitlementCloudCost", "parameters": {"id": "@state:id", "mode": "@state:param.mode"}}}, "uiview": {"i18n": "everest.inv.itam/software", "title": "{{cloudCosts.integration}}", "sections": {"content": [{"component": "FieldGroup", "customId": "softwareEntitlementCloudCostDetails", "section": {"grid": {"size": "1"}}, "props": {"type": "secondary", "presentationDataSet": "@dataSet:softwareEntitlementCloudCost", "fields": [{"field": "softwareEntitlementId", "fieldProps": {"initialValue": "@controller:getSoftwareEntitlementId()"}}, "cloudProviderAccountId", "cloudProductId"]}}]}, "actions": {"content": [{"variant": "primary", "label": "{{save}}", "align": "right", "presentationAction": "@action:save", "onClick": "@controller:save"}]}}}