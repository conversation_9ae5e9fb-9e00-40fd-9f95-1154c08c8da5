// @i18n:everest.inv.itam/software
import { executeAction } from '@pkg/everest.base/public/standard/ui/presentation/action';
import type { cloudProviderAccountLinksPresentationUI } from '@pkg/everest.inv.itam.integration.cloudcosts/types/presentations/uinext/cloudProviderAccountLinks.ui';

type CloudProviderAccountLinksContext =
  cloudProviderAccountLinksPresentationUI.context;

export type Context = CloudProviderAccountLinksContext & {
  state: {
    mode?: string;
    selectedLines: CloudProviderAccountLinksContext['data']['cloudProviderAccountLinks'];
  };
};

export function onRowSelectionChanged(
  context: Context,
  selectedLines: {
    data: CloudProviderAccountLinksContext['data']['cloudProviderAccountLinks'][number];
  }[]
) {
  const { state } = context;
  state.selectedLines ??= [];
  state.selectedLines = selectedLines.map((line) => line.data);
}

export async function saveCloudProviderLink(context: Context) {
  await executeAction(context, 'save', {
    notificationMessage: {
      loading: '{{saving}}',
      success: '{{successMessage}}',
    },
  });
}

export async function deleteCloudProviderLink(
  context: Context,
  row: {
    nodeInstance: {
      data: Context['data']['softwareEntitlements'][number];
    };
  }
) {
  await executeAction(context, 'deleteLink', {
    actionInput: {
      id: row.nodeInstance.data.id,
    },
    notificationMessage: {
      loading: '{{deleting}}',
      success: '{{successMessage}}',
    },
  });
}

export function deleteCloudProviderLinkDisabled(
  context: Context,
  row: {
    nodeInstance: {
      data: Context['data']['softwareEntitlements'][number];
    };
  }
) {
  return !row.nodeInstance?.data?.id;
}
