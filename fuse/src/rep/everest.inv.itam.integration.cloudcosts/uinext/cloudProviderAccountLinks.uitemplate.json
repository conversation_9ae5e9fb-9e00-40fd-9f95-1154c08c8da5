{"version": 3, "uicontroller": "cloudProviderAccountLinks.uicontroller.ts", "uimodel": {"presentation": {"urn": "urn:evst:everest:inv/itam/integration/cloudcosts:presentation:uinext/cloudProviderAccountLinks", "parameters": {"mode": "@state:param.mode"}}}, "uiview": {"templateType": "details", "title": "{{cloudCosts.integration}}", "i18n": "everest.inv.itam/software", "config": {"stretch": true, "allowRefreshData": true, "autoRefreshData": true}, "header": {"content": {"title": "{{cloudCosts.integration}}"}}, "sections": {"background": "grey", "content": [{"component": "Table", "customId": "cloudProviderAccountLinksList", "section": {"grid": {"size": "12"}, "editing": true, "actions": [{"label": "{{save}}", "onClick": "@controller:saveCloudProviderLink"}]}, "props": {"addRows": false, "suppressDelete": true, "onRowSelectionChanged": "@controller:onRowSelectionChanged", "presentationDataSet": "@dataSet:cloudProviderAccountLinks", "variant": "white", "masterDetail": {"openGroupsByDefault": false, "data": "@binding:softwareEntitlements", "suppressDeleteDetail": true, "groupField": "cloudProviderAccountId", "editing": false, "rowActions": {"actions": [{"content": "{{delete}}", "onClick": "@controller:deleteCloudProviderLink", "disabled": "@controller:deleteCloudProviderLinkDisabled"}]}}}}]}}}