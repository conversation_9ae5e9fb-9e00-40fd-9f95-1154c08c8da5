// @i18n:everest.inv.itam/software
import type { UIExecutionContext } from '@everestsystems/content-core';
import { executeAction } from '@pkg/everest.base/public/standard/ui/presentation/action';
import type { softwareEntitlementCloudCostPresentationUI } from '@pkg/everest.inv.itam.integration.cloudcosts/types/presentations/uinext/softwareEntitlementCloudCost.ui';

export async function save(
  context: UIExecutionContext.BasicExecutionContext
): Promise<void> {
  await executeAction(context, 'save', {
    notificationMessage: {
      loading: '{{saving}}',
      success: '{{successMessage}}',
    },
  });

  context.helpers.currentModalSubmitCallback();
  context.helpers.closeModal();
}

export function getSoftwareEntitlementId(
  context: softwareEntitlementCloudCostPresentationUI.context
) {
  return Number.parseInt(
    context.state?.softwareEntitlementId?.toString() ?? '0'
  );
}
