package everest.base.presentation.demo

node Order {
	description: 'Order'
	label: 'Order'

	field status enum<OrderStatus>{
		editable
		persisted
		label: 'Status'
	}

	field orderNumber Number<Int> {
		editable
		persisted
		label: 'Order Number'
	}

	field price composite<everest.base::CurrencyAmount>{
		editable
		persisted
		label: 'Price'
	}

	field document File {
		editable
		persisted
		label: 'Document'
	}

	association alias Lines for OrderLine-Order
}
