/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// ******************************************************************************
// Generated Everest Types
// ******************************************************************************

import type { Decimal, RoundingMode } from '@everestsystems/decimal';
import type {
  ArgType,
  Association,
  AssociationManagementArgs,
  AsyncResult,
  Controller,
  ControllerClientProvider,
  CreateOptionsArg,
  DataWithMetadata,
  DeleteOptionsArg,
  FieldSelector,
  Filter,
  flattenFieldSelector,
  GraphInstanceEntry,
  KernelRequestOptions,
  NonAssociationFields,
  Optional,
  PurgeArgs,
  ReadOptionsArg,
  ReadReturnType as ReadReturnTypeGeneric,
  ReadReturnTypeWithAssociations,
  RequireAtLeastOne,
  SemanticSearchArg,
  SemanticSearchRequestOptions,
  TypeSafeQueryArgType,
  UpdateManyOptionsArg,
  UpdateOptionsArg,
  UpsertOptionsArg,
  WriteReturnType,
  WriteReturnTypeWithAssociations,
} from '@everestsystems/content-core';
import type { EvstUUID as everest_appserver_primitive_UUID } from "@pkg/everest.appserver/types/primitives/UUID";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { EvstUserEmail as everest_appserver_usermgmt_primitive_user_UserEmail } from "@pkg/everest.appserver.usermgmt/types/primitives/user/UserEmail";
import type { EvstDateTime as everest_appserver_primitive_DateTime } from "@pkg/everest.appserver/types/primitives/DateTime";
import type { EvstFile as everest_appserver_primitive_File } from "@pkg/everest.appserver/types/primitives/File";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { EvstCurrencyAmount as everest_base_composite_CurrencyAmount } from "@pkg/everest.base/types/composites/CurrencyAmount";
import type { EvstOrderStatus as everest_base_presentation_demo_enum_OrderStatus } from "@pkg/everest.base.presentation.demo/types/enums/OrderStatus";
import type { OrderLine as everest_base_presentation_demo_model_node_OrderLine } from "@pkg/everest.base.presentation.demo/types/OrderLine";

/**
 * Types for Order
 *
 * @everest-node-metadata='{"visibility":"private"}'
 */
export namespace Order {
  export type CreationFields = Pick<Order, 'uuid' | 'externalId' | 'active' | 'document' | 'orderNumber' | 'price' | 'status'>;
  export type UniqueFields = Pick<Order, 'id' | 'uuid'>;
  export type UniqueWhereInput = (RequireAtLeastOne<UniqueFields>) & Partial<Order>;
  export type ReadReturnType<U extends string | number | symbol = keyof Order> = ReadReturnTypeGeneric<Order, U>;

  export interface IControllerClient extends Omit<Controller<Order>, 'all' | 'getHistory' | 'lookup' | 'purge' | 'queryWithMetadata' | 'semanticSearch' | 'upsertMany'> {}

  /**
   * Order
   */
  export type Order = {
    /** Database generated identifier of this record. */
    id: number;
    /** Database generated version of this record. */
    version?: number;
    uuid?: everest_appserver_primitive_UUID | null;
    /**
     * ID of the system from which this record was sourced
     */
    externalId?: everest_appserver_primitive_Text | null;
    /**
     * Indicates whether this record is usable for business users
     */
    active?: everest_appserver_primitive_TrueFalse | null;
    /**
     * User that created this record
     */
    createdBy: everest_appserver_usermgmt_primitive_user_UserEmail;
    /**
     * Date/time when the record was created
     */
    createdDate: everest_appserver_primitive_DateTime;
    /**
     * User that last modified the record
     */
    lastModifiedBy: everest_appserver_usermgmt_primitive_user_UserEmail;
    /**
     * Date/time of the last modification to the record
     */
    lastModifiedDate: everest_appserver_primitive_DateTime;
    document?: everest_appserver_primitive_File | null;
    orderNumber?: everest_appserver_primitive_Number | null;
    price?: everest_base_composite_CurrencyAmount | null;
    status?: everest_base_presentation_demo_enum_OrderStatus | null;
    };
  /**
   * Order
   */
  export type OrderWithAssociation = Order & {
    ["OrderLine-Order"]?: Association<everest_base_presentation_demo_model_node_OrderLine.OrderLineWithAssociation>[];
    ["Lines"]?: Association<everest_base_presentation_demo_model_node_OrderLine.OrderLineWithAssociation>[];
    };
  /** @deprecated - use {@link MODEL_URN} instead */
  export const name = 'urn:evst:everest:base/presentation/demo:model/node:Order';
  export const MODEL_URN = 'urn:evst:everest:base/presentation/demo:model/node:Order';
  /** @deprecated - use {@link MODEL_URN} instead */
  export const MODEL_ID = 'everest.base.presentation.demo/OrderModel.Order';
  export const MODEL_UUID = 'c82f9063-2226-4c51-86ff-6543406fb693';

  /** @return a model controller instance for Order. */
  export function client(env: ControllerClientProvider) {
    return env.controllerClient<Order.IControllerClient>(MODEL_URN);
  }

  /** write a new object to the database. */
  export async function create<U extends keyof Order>(env: ControllerClientProvider, input: CreationFields, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): Promise<WriteReturnType<Order, U>> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).create(input, fields, options);
  }

  /** write new objects to the database. */
  export async function createMany<U extends keyof Order>(env: ControllerClientProvider, input: CreationFields[], fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): Promise<WriteReturnType<Order, U>[]> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).createMany(input, fields, options);
  }

  /** delete the object that is uniquely identified by the where condition. */
  export async function Delete(env: ControllerClientProvider, where: UniqueWhereInput): Promise<Partial<Order>[]> {
    return (await client(env)).delete(where);
  }

  /** delete all objects. */
  export async function deleteAll(env: ControllerClientProvider): Promise<Partial<Order>[]> {
    return (await client(env)).deleteAll();
  }

  /** delete the objects that are identified by the where condition. */
  export async function deleteMany(env: ControllerClientProvider, where: Filter<OrderWithAssociation>): Promise<Partial<Order>[]>;
  /** @deprecated use the alternative overloaded function */
  export async function deleteMany(env: ControllerClientProvider, where: Partial<Order>): Promise<Partial<Order>[]>;
  export async function deleteMany(env: ControllerClientProvider, where: Filter<Order> | Partial<Order>): Promise<Partial<Order>[]> {
    return (await client(env)).deleteMany(where as Filter<Order>);
  }

  /** returns a list of all objects matching the requirements. */
  export async function query<U extends FieldSelector<OrderWithAssociation>>(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<OrderWithAssociation>, 'draft'>, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<ReadReturnTypeWithAssociations<OrderWithAssociation, U>[]>;
  /** @deprecated Use the alternative overloaded method for querying associations. */
  export async function query<U extends keyof Order, V extends string = 'ALL_FIELDS'>(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<Order>, 'draft'> | Omit<Omit<TypeSafeQueryArgType<Order>, 'draft'>, 'where'> & { where?: Partial<Order> }, fieldlist: ReadonlyArray<U | V> | 'ALL_FIELDS'): Promise<ReadReturnType<V>[]>;
  export async function query(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<Order>, 'draft'>, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown[]> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).query(args, fields as ReadonlyArray<string>);
  }

  /** reads the object that is uniquely identified by the where condition. */
  export async function read<U extends FieldSelector<OrderWithAssociation>>(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnTypeWithAssociations<OrderWithAssociation, U>>;
  /** @deprecated Use the alternative overloaded method for reading associations. */
  export async function read<U extends keyof Order>(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnType<U>>;
  export async function read(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<unknown> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).read(where, fields as ReadonlyArray<keyof Order>, options);
  }

  /** writes modified values back to the database for a given node instance. */
  export async function update<U extends FieldSelector<OrderWithAssociation>>(env: ControllerClientProvider, where: UniqueWhereInput, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnTypeWithAssociations<OrderWithAssociation, U>>;
  /** @deprecated Use the alternative overloaded method for writing with associations. */
  export async function update<U extends keyof Order>(env: ControllerClientProvider, where: Partial<Order>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnType<Order, U>>;
  export async function update(env: ControllerClientProvider, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).update(where, data, fields as ReadonlyArray<keyof Order>);
  }

  /** writes modified values back to the database for selected node instances. */
  export async function updateMany<U extends FieldSelector<OrderWithAssociation>>(env: ControllerClientProvider, where: Filter<OrderWithAssociation>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnTypeWithAssociations<OrderWithAssociation, U>[]>;
  /** @deprecated Use the alternative overloaded method for writing with associations. */
  export async function updateMany<U extends keyof Order>(env: ControllerClientProvider, where: Partial<Order>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnType<Order, U>[]>;
  export async function updateMany(env: ControllerClientProvider, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown[]> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).updateMany(where, data, fields as ReadonlyArray<keyof Order>);
  }

  /** creates or updates object identified by where. */
  export async function upsert<U extends keyof Order & string>(env: ControllerClientProvider, data: Partial<Order>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<Order, U>>;
  /** @deprecated use the overload without an explicit where clause */
  export async function upsert<U extends keyof Order & string>(env: ControllerClientProvider, where: Partial<Order>, data: Partial<Order>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<Order, U>>;
  export async function upsert<U extends keyof Order & string>(env: ControllerClientProvider, whereOrData: Partial<Order>, dataOrFieldList?: Partial<Order> | ReadonlyArray<U> | 'ALL_FIELDS', fieldlistOrOptions?: ReadonlyArray<U> | 'ALL_FIELDS' | Omit<UpsertOptionsArg, 'draft'>, maybeOptions?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<Order, U>> {
    return (await client(env)).upsert(whereOrData, dataOrFieldList, fieldlistOrOptions, maybeOptions);
  }
}
