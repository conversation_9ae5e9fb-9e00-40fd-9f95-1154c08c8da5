/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

import type { Order as everest_base_presentation_demo_model_node_Order } from "@pkg/everest.base.presentation.demo/types/Order";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { EvstOrderStatus as everest_base_presentation_demo_enum_OrderStatus } from "@pkg/everest.base.presentation.demo/types/enums/OrderStatus";
import type { EvstCurrencyAmount as everest_base_composite_CurrencyAmount } from "@pkg/everest.base/types/composites/CurrencyAmount";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";

import type {
  ODataPresentationFileInput,
  ODataPresentationFileOutput,
} from '@everestsystems/content-core';
import { createOdataClientRuntime } from '@everestsystems/content-core/lib/runtime/loader';

/**
 * Generated frontend types for OData presentation Orders.
 */
export namespace OrdersUI {
  export namespace EntitySets {
    export namespace Orders {
      export namespace Get {
        export type Entity = {
          id?: everest_base_presentation_demo_model_node_Order.Order["id"] | undefined;
          orderNumber?: everest_appserver_primitive_Number | undefined;
          status?: everest_base_presentation_demo_enum_OrderStatus | undefined;
          price?: everest_base_composite_CurrencyAmount | undefined;
          document?: everest_appserver_primitive_Text | undefined;
        };

        export type Filter =
          | PropertyFilter
          | [Filter, 'and', Filter]
          | [Filter, 'or', Filter];

        /**
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), filters must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to filter by its amount component.
         */
        export type PropertyFilter = {
          property: string;
          comparator: 'eq' | 'ne' | 'lt' | 'le' | 'gt' | 'ge' | 'contains';
          value: unknown;
        };

        /**
         * Specifies sorting for a property in ascending or descending order.
         * For composite properties (e.g., a property with multiple components
         * like amount and currency), sorting must target a single component
         * using an underscore. For example, if the entity property is price,
         * use price_amount to sort by its amount component.
         */
        export type OrderbyItem = {
          property: string;
          ordering: 'asc' | 'desc';
        };

        export interface Request {
          setFilter(filter: Filter): this;

          setOrderby(orderby: ReadonlyArray<OrderbyItem>): this;

          setSkip(skip: number): this;

          setTop(top: number): this;

          setSelect(select: ReadonlyArray<keyof Entity>): this;

          setCount(count: boolean): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entities: Entity[];
          count?: number | undefined;
        };
      }

      export namespace Post {
        export type Data = {
          orderNumber?: everest_appserver_primitive_Number | undefined;
          status?: everest_base_presentation_demo_enum_OrderStatus | undefined;
          price?: everest_base_composite_CurrencyAmount | undefined;
          document?: everest_appserver_primitive_Text | undefined;
        };

        export type Entity = {
          id: everest_base_presentation_demo_model_node_Order.Order["id"];
          orderNumber?: everest_appserver_primitive_Number | undefined;
          status?: everest_base_presentation_demo_enum_OrderStatus | undefined;
          price?: everest_base_composite_CurrencyAmount | undefined;
          document?: everest_appserver_primitive_Text | undefined;
        };

        export interface Request {
          setData(data: Data): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          entity: Entity;
        };
      }

      export namespace Patch {
        export type Data = {
          orderNumber?: everest_appserver_primitive_Number | null | undefined;
          status?: everest_base_presentation_demo_enum_OrderStatus | null | undefined;
          price?: everest_base_composite_CurrencyAmount | null | undefined;
          document?: everest_appserver_primitive_Text | null | undefined;
        };

        export interface Request {
          setId(id: number): this;

          setData(data: Data): this;

          execute(): Promise<Response>;
        }

        export type Response = Record<string, never>;
      }

      export namespace Delete {
        export interface Request {
          setId(id: number): this;

          execute(): Promise<Response>;
        }

        export type Response = Record<string, never>;
      }
    }
  }

  export namespace Actions {
    export namespace SetUpOrders {
      export namespace Execute {
        export type Input = Record<string, never>;

        export type Output = Record<string, never>;

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace CloseOrder {
      export namespace Execute {
        export type Input = {
          orderId: everest_base_presentation_demo_model_node_Order.Order["id"];
        };

        export type Output = Record<string, never>;

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace UploadDocument {
      export namespace Execute {
        export type Input = {
          orderId: everest_base_presentation_demo_model_node_Order.Order["id"];
          document: ODataPresentationFileInput;
        };

        export type Output = Record<string, never>;

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace DeleteDocument {
      export namespace Execute {
        export type Input = {
          orderId: everest_base_presentation_demo_model_node_Order.Order["id"];
        };

        export type Output = Record<string, never>;

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }
  }

  export namespace Functions {
    export namespace DownloadDocument {
      export namespace Execute {
        export type Input = {
          orderId: everest_base_presentation_demo_model_node_Order.Order["id"];
        };

        export type Output = {
          document: ODataPresentationFileOutput;
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }
  }

  export namespace Permission {
    export namespace Check {
      export type Result = {
        dataSets: {
          Orders: {
            isGetPermitted: boolean;
            isPostPermitted: boolean;
            isPatchPermitted: boolean;
            isDeletePermitted: boolean;
          };
        };
        actions: {
          CloseOrder: {
            isExecutePermitted: boolean;
          };
          DeleteDocument: {
            isExecutePermitted: boolean;
          };
          DownloadDocument: {
            isExecutePermitted: boolean;
          };
          SetUpOrders: {
            isExecutePermitted: boolean;
          };
          UploadDocument: {
            isExecutePermitted: boolean;
          };
        };
      };

      export interface Request {
        execute(): Promise<Response>;
      }

      export type Response = {
        result: Result;
      };
    }
  }

  export namespace Api {
    export type EntitySets = {
      Orders: {
        get: EntitySets.Orders.Get.Request;
        post: EntitySets.Orders.Post.Request;
        patch: EntitySets.Orders.Patch.Request;
        delete: EntitySets.Orders.Delete.Request;
      };
    };

    export type Actions = {
      SetUpOrders: {
        execute: Actions.SetUpOrders.Execute.Request;
      };
      CloseOrder: {
        execute: Actions.CloseOrder.Execute.Request;
      };
      UploadDocument: {
        execute: Actions.UploadDocument.Execute.Request;
      };
      DeleteDocument: {
        execute: Actions.DeleteDocument.Execute.Request;
      };
    };

    export type Functions = {
      DownloadDocument: {
        execute: Functions.DownloadDocument.Execute.Request;
      };
    };

    export interface Client {
      createEntitySetGetRequest<T extends 'Orders'>(entitySetName: T): Api.EntitySets[T]['get'];

      createEntitySetPostRequest<T extends 'Orders'>(entitySetName: T): Api.EntitySets[T]['post'];

      createEntitySetPatchRequest<T extends 'Orders'>(entitySetName: T): Api.EntitySets[T]['patch'];

      createEntitySetDeleteRequest<T extends 'Orders'>(entitySetName: T): Api.EntitySets[T]['delete'];

      createActionExecuteRequest<T extends 'SetUpOrders' | 'CloseOrder' | 'UploadDocument' | 'DeleteDocument'>(actionName: T): Api.Actions[T]['execute'];

      createFunctionExecuteRequest<T extends 'DownloadDocument'>(functionName: T): Api.Functions[T]['execute'];

      createPermissionCheckRequest(): Permission.Check.Request;
    }
  }

  export const client = createOdataClientRuntime<Api.Client>('everest.base.presentation.demo/storybook/odata/Orders');
}

