/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

import type { Order as everest_base_presentation_demo_model_node_Order } from "@pkg/everest.base.presentation.demo/types/Order";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { EvstOrderStatus as everest_base_presentation_demo_enum_OrderStatus } from "@pkg/everest.base.presentation.demo/types/enums/OrderStatus";
import type { EvstCurrencyAmount as everest_base_composite_CurrencyAmount } from "@pkg/everest.base/types/composites/CurrencyAmount";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstFile as everest_appserver_primitive_File } from "@pkg/everest.appserver/types/primitives/File";

import type {
  Filter,
  PresentationSession,
} from '@everestsystems/content-core';

/**
 * Generated backend types for OData presentation Orders.
 */
export namespace Orders {
  export namespace EntitySets {
    export namespace Orders {
      export namespace Query {
        export type Instance = {
          id?: everest_base_presentation_demo_model_node_Order.Order["id"] | undefined;
          orderNumber?: everest_appserver_primitive_Number | undefined;
          status?: everest_base_presentation_demo_enum_OrderStatus | undefined;
          price?: everest_base_composite_CurrencyAmount | undefined;
          document?: everest_appserver_primitive_Text | undefined;
        };

        export type Order = {
          field: keyof Instance;
          ordering: 'asc' | 'desc';
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            where: Filter<Instance>;
            orderBy: ReadonlyArray<Order>;
            skip: number | undefined;
            take: number | undefined;
            fieldList: ReadonlyArray<keyof Instance>;
            count: boolean;
          };

          export type Response = {
            instances: Instance[];

            /**
             * If count was set to true on the request object, this property
             * must be set, otherwise it must evaluate to undefined.
             */
            count?: number | undefined;
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export namespace Create {
        export type Input = {
          orderNumber?: everest_appserver_primitive_Number | undefined;
          status?: everest_base_presentation_demo_enum_OrderStatus | undefined;
          price?: everest_base_composite_CurrencyAmount | undefined;
          document?: everest_appserver_primitive_Text | undefined;
        };

        export type Instance = {
          id: everest_base_presentation_demo_model_node_Order.Order["id"];
          orderNumber?: everest_appserver_primitive_Number | undefined;
          status?: everest_base_presentation_demo_enum_OrderStatus | undefined;
          price?: everest_base_composite_CurrencyAmount | undefined;
          document?: everest_appserver_primitive_Text | undefined;
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            inputs: Input[];
          };

          export type Response = {
            instances: Instance[];
          };
        }

        export interface Implementation {
          execute(request: Execute.Request): Promise<Execute.Response>;
        }
      }

      export namespace Update {
        export type Data = {
          orderNumber?: everest_appserver_primitive_Number | null;
          status?: everest_base_presentation_demo_enum_OrderStatus | null;
          price?: everest_base_composite_CurrencyAmount | null;
          document?: everest_appserver_primitive_Text | null;
        };

        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            id: number;
            data: Data;
          };

          export type Response = void;
        }

        export interface Implementation {
          execute(input: Execute.Request): Promise<Execute.Response>;
        }
      }

      export namespace Delete {
        export namespace Execute {
          export type Request = {
            session: PresentationSession;
            ids: number[];
          };

          export type Response = void;
        }

        export interface Implementation {
          execute(input: Execute.Request): Promise<Execute.Response>;
        }
      }

      export type Implementation = {
        query: Query.Implementation;
        create: Create.Implementation;
        update: Update.Implementation;
        delete: Delete.Implementation;
      };
    }
  }

  export namespace Actions {
    export namespace SetUpOrders {
      export type Input = Record<string, never>;

      export type Output = Record<string, never>;

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace CloseOrder {
      export type Input = {
        orderId: everest_base_presentation_demo_model_node_Order.Order["id"];
      };

      export type Output = Record<string, never>;

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace UploadDocument {
      export type Input = {
        orderId: everest_base_presentation_demo_model_node_Order.Order["id"];
        document: everest_appserver_primitive_File;
      };

      export type Output = Record<string, never>;

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace DeleteDocument {
      export type Input = {
        orderId: everest_base_presentation_demo_model_node_Order.Order["id"];
      };

      export type Output = Record<string, never>;

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace DownloadDocument {
      export type Input = {
        orderId: everest_base_presentation_demo_model_node_Order.Order["id"];
      };

      export type Output = {
        document: everest_appserver_primitive_File;
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }
  }

  export type Implementation = {
    Orders: EntitySets.Orders.Implementation;
    SetUpOrders: Actions.SetUpOrders.Implementation;
    CloseOrder: Actions.CloseOrder.Implementation;
    UploadDocument: Actions.UploadDocument.Implementation;
    DeleteDocument: Actions.DeleteDocument.Implementation;
    DownloadDocument: Actions.DownloadDocument.Implementation;
  };
}

