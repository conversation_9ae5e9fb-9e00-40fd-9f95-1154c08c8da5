import React, { useEffect, useState } from 'react';
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  message,
  Upload,
} from 'antd';
import { OrdersUI } from '@pkg/everest.base.presentation.demo/types/presentations/storybook/odata/Orders.ui';

const { Option } = Select;

const STATUS_LABELS: Record<string, string> = {
  open: 'Open',
  inProgress: 'In Progress',
  closed: 'Closed',
};

const SimpleTable: React.FC = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [globalLoading, setGlobalLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingOrder, setEditingOrder] = useState<OrdersUI.EntitySets.Orders.Get.Entity | null>(null);
  const [form] = Form.useForm();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [sortField, setSortField] = useState('orderNumber');
  const [filters, setFilters] = useState<OrdersUI.EntitySets.Orders.Get.Filter | undefined>();

  useEffect(() => {
    fetchData();
  }, [currentPage, pageSize, sortField, sortOrder, filters]);

  const fetchData = async () => {
    setLoading(true);
    try {
      const request = OrdersUI.client
        .createEntitySetGetRequest('Orders')
        .setSkip((currentPage - 1) * pageSize)
        .setTop(pageSize)
        .setOrderby([{ property: sortField, ordering: sortOrder }]);

      if (filters) {
        request.setFilter(filters);
      }

      const response = await request.setCount(true).execute();
      setData(response.entities);
      setTotal(response.count || 0);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTableChange = (
    pagination: any,
    tableFilters: any,
    sorter: any
  ) => {
    setCurrentPage(pagination.current);
    setPageSize(pagination.pageSize);

    if (sorter.order) {
      setSortOrder(sorter.order === 'ascend' ? 'asc' : 'desc');
      setSortField(sorter.field);
    } else {
      setSortOrder('asc');
      setSortField('orderNumber');
    }

    if (tableFilters.status && tableFilters.status.length > 0) {
      setFilters({
        property: 'status',
        comparator: 'eq',
        value: tableFilters.status[0],
      });
    } else {
      setFilters(undefined);
    }
  };

  const handleAdd = () => {
    setEditingOrder(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (record: OrdersUI.EntitySets.Orders.Get.Entity) => {
    setEditingOrder(record);
    form.setFieldsValue({
      orderNumber: record.orderNumber,
      status: record.status,
      amount: record.price?.amount,
      currency: record.price?.currency,
    });
    setModalVisible(true);
  };

  const handleDelete = async (id: number) => {
    try {
      await OrdersUI.client
        .createEntitySetDeleteRequest('Orders')
        .setId(id)
        .execute();
      message.success('Order deleted successfully');
      fetchData();
    } catch {
      message.error('Failed to delete order');
    }
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      if (editingOrder) {
        await OrdersUI.client
          .createEntitySetPatchRequest('Orders')
          .setId(editingOrder.id!)
          .setData({
            orderNumber: values.orderNumber,
            status: values.status,
            price: { amount: values.amount, currency: values.currency },
          })
          .execute();
        message.success('Order updated successfully');
      } else {
        await OrdersUI.client
          .createEntitySetPostRequest('Orders')
          .setData({
            orderNumber: values.orderNumber,
            status: values.status,
            price: { amount: values.amount, currency: values.currency },
          })
          .execute();
        message.success('Order created successfully');
      }

      setModalVisible(false);
      fetchData();
    } catch {
      message.error('Failed to save order');
    } finally {
      setLoading(false);
    }
  };

  const handleSetupOrders = async () => {
    setGlobalLoading(true);
    try {
      await OrdersUI.client
        .createActionExecuteRequest('SetUpOrders')
        .setInput({})
        .execute();
      message.success('Orders setup successfully');
      fetchData();
    } catch {
      message.error('Failed to set up orders');
    } finally {
      setGlobalLoading(false);
    }
  };

  const handleCloseOrder = async (id: number) => {
    try {
      await OrdersUI.client
        .createActionExecuteRequest('CloseOrder')
        .setInput({ orderId: id })
        .execute();
      message.success('Order closed successfully');
      fetchData();
    } catch {
      message.error('Failed to close order');
    }
  };

  const handleUploadDocument = async (orderId: number, file: File) => {
    const reader = new FileReader();

    reader.onload = async (event) => {
      try {
        const fileContent = event.target?.result;
        if (fileContent && typeof fileContent === 'string') {
          const documentData: OrdersUI.Actions.UploadDocument.Execute.Input = {
            orderId: orderId,
            document: {
              fileName: file.name,
              mimeType: file.type,
              content: {
                format: 'base64',
                data: fileContent.split(',')[1], // Extract base64 data part
              },
            },
          };

          await OrdersUI.client
            .createActionExecuteRequest('UploadDocument')
            .setInput(documentData)
            .execute();
          message.success('Document uploaded successfully');
          fetchData();
        }
      } catch {
        message.error('Failed to upload document');
      }
    };

    reader.readAsDataURL(file);
  };

  const handleRemoveDocument = async (orderId: number) => {
    try {
      await OrdersUI.client
        .createActionExecuteRequest('DeleteDocument')
        .setInput({ orderId })
        .execute();
      message.success('Document removed successfully');
      fetchData();
    } catch {
      message.error('Failed to remove document');
    }
  };

  const handleDownloadDocument = async (orderId: number) => {
    try {
      const request = OrdersUI.client
        .createFunctionExecuteRequest('DownloadDocument')
        .setInput({ orderId });

      const response = await request.execute();
      const documentOutput = response.output.document;

      // Create an anchor element for downloading
      const a = window.document.createElement('a');
      const base64Data = await documentOutput.readAsBase64();
      a.href = `data:${documentOutput.getMimeType()};base64,${base64Data}`;
      a.download = documentOutput.getFileName();
      a.style.display = 'none';

      window.document.body.appendChild(a);
      a.click();
      window.document.body.removeChild(a);

      message.success('Document downloaded successfully');
    } catch {
      message.error('Failed to download document');
    }
  };

  const columns = [
    {
      title: 'Order Number',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      sorter: true,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      sorter: true,
      filters: [
        { text: 'Open', value: 'open' },
        { text: 'In Progress', value: 'inProgress' },
        { text: 'Closed', value: 'closed' },
      ],
      render: (status: string) => STATUS_LABELS[status] || status,
    },
    {
      title: 'Price',
      dataIndex: 'price',
      key: 'price',
      sorter: true,
      render: (price: { amount?: number; currency?: string } | undefined) =>
        price && price.amount !== undefined && price.currency
          ? `${price.currency} ${price.amount}`
          : '-',
    },
    {
      title: 'Document',
      key: 'document',
      render: (record: OrdersUI.EntitySets.Orders.Get.Entity) => (
        <>
          {!record.document ? (
            <Upload
              showUploadList={false}
              beforeUpload={(file) => {
                handleUploadDocument(record.id!, file);
                return false;  // Prevent automatic upload
              }}
            >
              <Button>Upload Document</Button>
            </Upload>
          ) : (
            <>
              <Button onClick={() => handleRemoveDocument(record.id!)}>
                Remove Document
              </Button>
              <Button onClick={() => handleDownloadDocument(record.id!)} style={{ marginLeft: 8 }}>
                Download Document
              </Button>
            </>
          )}
        </>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (record: OrdersUI.EntitySets.Orders.Get.Entity) => (
        <>
          {(record.status === 'open' || record.status === 'inProgress') && (
            <Button onClick={() => handleEdit(record)} style={{ marginRight: 8 }}>
              Edit
            </Button>
          )}
          {record.status === 'open' && (
            <Button onClick={() => handleDelete(record.id!)} danger>
              Delete
            </Button>
          )}
          {record.status === 'inProgress' && (
            <Button onClick={() => handleCloseOrder(record.id!)} style={{ marginLeft: 8 }}>
              Close
            </Button>
          )}
        </>
      ),
    },
  ];

  return (
    <div>
      <Button onClick={handleAdd} type="primary" style={{ marginBottom: 16 }}>
        Add Order
      </Button>
      <Button onClick={handleSetupOrders} style={{ marginBottom: 16, marginLeft: 8 }}>
        Setup Orders
      </Button>
      <Table
        columns={columns}
        dataSource={data}
        pagination={{ current: currentPage, pageSize, total }}
        loading={loading}
        onChange={handleTableChange}
        rowKey={(record) => record.id as number}
      />
      <Modal
        title={editingOrder ? "Edit Order" : "Add Order"}
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={handleSubmit}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="orderNumber"
            label="Order Number"
            rules={[{ required: true, message: "Please input the order number!" }]}
          >
            <InputNumber style={{ width: "100%" }} />
          </Form.Item>
          <Form.Item
            name="status"
            label="Status"
            rules={[{ required: true, message: "Please select the status!" }]}
          >
            <Select>
              {Object.entries(STATUS_LABELS).map(([value, label]) => (
                <Option key={value} value={value}>
                  {label}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="amount"
            label="Amount"
            rules={[{ required: true, message: "Please input the amount!" }]}
          >
            <InputNumber style={{ width: "100%" }} />
          </Form.Item>
          <Form.Item
            name="currency"
            label="Currency"
            rules={[{ required: true, message: "Please input the currency!" }]}
          >
            <Input />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default SimpleTable;
