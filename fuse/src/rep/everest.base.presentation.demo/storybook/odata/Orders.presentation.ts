import { setUpOrders } from '@pkg/everest.base.presentation.demo/orders/index';
import { EvstOrderStatus } from '@pkg/everest.base.presentation.demo/types/enums/OrderStatus';
import { Order } from '@pkg/everest.base.presentation.demo/types/Order';
import type { Orders } from '@pkg/everest.base.presentation.demo/types/presentations/storybook/odata/Orders';

export default {
  Orders: {
    query: {
      async execute({
        session,
        where,
        orderBy,
        skip,
        take,
        fieldList,
        count,
      }: Orders.EntitySets.Orders.Query.Execute.Request): Promise<Orders.EntitySets.Orders.Query.Execute.Response> {
        const instances = await Order.query(
          session,
          {
            where,
            orderBy,
            skip,
            take,
          },
          fieldList
        );

        const response: Orders.EntitySets.Orders.Query.Execute.Response = {
          instances,
        };

        if (count) {
          const queryResult = await Order.query(
            session,
            {
              where,
            },
            ['id']
          );

          response.count = queryResult.length;
        }

        return response;
      },
    },

    create: {
      async execute({
        session,
        inputs,
      }: Orders.EntitySets.Orders.Create.Execute.Request): Promise<Orders.EntitySets.Orders.Create.Execute.Response> {
        const instances = await Order.createMany(session, inputs);

        return {
          instances,
        };
      },
    },

    update: {
      async execute({
        session,
        id,
        data,
      }: Orders.EntitySets.Orders.Update.Execute.Request): Promise<Orders.EntitySets.Orders.Update.Execute.Response> {
        await Order.update(
          session,
          {
            id,
          },
          data
        );
      },
    },

    delete: {
      async execute({
        session,
        ids,
      }: Orders.EntitySets.Orders.Delete.Execute.Request): Promise<Orders.EntitySets.Orders.Delete.Execute.Response> {
        await Order.deleteMany(session, {
          id: {
            $in: ids,
          },
        });
      },
    },
  },

  SetUpOrders: {
    async execute({
      session,
    }: Orders.Actions.SetUpOrders.Execute.Request): Promise<Orders.Actions.SetUpOrders.Execute.Response> {
      await setUpOrders(session, 100);

      return {
        output: {},
      };
    },
  },

  CloseOrder: {
    async execute({
      session,
      input: { orderId },
    }: Orders.Actions.CloseOrder.Execute.Request): Promise<Orders.Actions.CloseOrder.Execute.Response> {
      await Order.update(
        session,
        {
          id: orderId,
        },
        {
          status: EvstOrderStatus.Closed,
        }
      );

      return {
        output: {},
      };
    },
  },

  UploadDocument: {
    async execute({
      session,
      input: { orderId, document },
    }: Orders.Actions.UploadDocument.Execute.Request): Promise<Orders.Actions.UploadDocument.Execute.Response> {
      /**
       * The value of document is the ID under which the uploaded file has been stored in the file storage.
       */
      await Order.update(
        session,
        {
          id: orderId,
        },
        {
          document,
        }
      );

      return {
        output: {},
      };
    },
  },

  DeleteDocument: {
    async execute({
      session,
      input: { orderId },
    }: Orders.Actions.DeleteDocument.Execute.Request): Promise<Orders.Actions.DeleteDocument.Execute.Response> {
      await Order.update(
        session,
        {
          id: orderId,
        },
        {
          document: null,
        }
      );

      return {
        output: {},
      };
    },
  },

  DownloadDocument: {
    async execute({
      session,
      input: { orderId },
    }: Orders.Actions.DownloadDocument.Execute.Request): Promise<Orders.Actions.DownloadDocument.Execute.Response> {
      const { document } = await Order.read(
        session,
        {
          id: orderId,
        },
        ['document']
      );

      return {
        output: {
          document,
        },
      };
    },
  },
} satisfies Orders.Implementation;
