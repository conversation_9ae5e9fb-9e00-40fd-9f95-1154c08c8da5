package everest.base.`presentation`.demo

odata presentation Orders {

  data-set Orders {
    supported-operations view, add, change,
      remove
    field id: field<Order.id>
    field orderNumber: Number<Int>
    field status: enum<OrderStatus>
    field price: composite<everest.base::CurrencyAmount>

    // The ID of the document in the file storage. It is explicitly typed as Text
    // to not immediately download the document when querying the data set.
    // Instead, the document can be downloaded using the DownloadDocument action.
    field document: Text
  }

  action SetUpOrders {
    properties {
      side-effects true
    }
  }

  action CloseOrder {
    inputs {
      orderId: field<Order.id>
    }
    properties {
      side-effects true
    }
  }

  action UploadDocument {
    inputs {
      orderId: field<Order.id>
      document: File
    }
    properties {
      side-effects true
    }
  }

  action DeleteDocument {
    inputs {
      orderId: field<Order.id>
    }
    properties {
      side-effects true
    }
  }

  action DownloadDocument {
    inputs {
      orderId: field<Order.id>
    }
    outputs {
      document: File
    }
    properties {
      side-effects false
    }
  }
}
