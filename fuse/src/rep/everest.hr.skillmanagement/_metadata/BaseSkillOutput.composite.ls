package everest.hr.skillmanagement
@metadata {
	active: true
}
public composite type BaseSkillOutput {
	@metadata {
		uuid: '6be1079d-fa54-46df-aed2-a5ae5e1ee50b'
	}
	skillId Id
	@metadata {
		uuid: 'cce5ff14-40fa-49bd-9977-b063c8d044bf'
	}
	skillName Text
	@metadata {
		uuid: 'bedab07c-b650-4cc7-9d61-b991844f38c8'
	}
	optional categories Array<Id>
	@metadata {
		uuid: '1122322b-ef5e-4ea3-80b3-1a1d95941049'
	}
	optional categoryMappings Array<Id>
	optional `description` Text
}
