// fuse/src/rep/everest.hr.skillmanagement/actions/createSkillWithCategories.spec.ts
import type { ISession } from '@everestsystems/content-core';
import type { EvstBaseSkillInput } from '@pkg/everest.hr.skillmanagement/types/composites/BaseSkillInput';
import { SkillCatalogue } from '@pkg/everest.hr.skillmanagement/types/SkillCatalogue';
import { SkillCategoryMapping } from '@pkg/everest.hr.skillmanagement/types/SkillCategoryMapping';

import createSkillWithCategories from './createSkillWithCategoriesInternal';

describe('createSkillWithCategories', () => {
  let env: ISession;

  beforeEach(() => {
    env = {} as ISession;
    jest.resetAllMocks();
  });

  it('should successfully create a skill with categories', async () => {
    // Arrange
    const mockSkillInput: EvstBaseSkillInput = {
      skillName: 'JavaScript',
      categories: [1, 2],
    };

    const mockCreatedSkill = {
      id: 1,
      skillName: 'JavaScript',
      description: 'Test Skill Description',
      createdBy: '<EMAIL>',
      createdDate: new Date(),
      lastModifiedBy: '<EMAIL>',
      lastModifiedDate: new Date(),
      version: 1,
    };

    const mockCreatedMappings = [
      {
        id: 101,
        skillId: 1,
        categoryId: 1,
        createdBy: '<EMAIL>',
        createdDate: new Date(),
        lastModifiedBy: '<EMAIL>',
        lastModifiedDate: new Date(),
      },
      {
        id: 102,
        skillId: 1,
        categoryId: 2,
        createdBy: '<EMAIL>',
        createdDate: new Date(),
        lastModifiedBy: '<EMAIL>',
        lastModifiedDate: new Date(),
      },
    ];

    const createSkillSpy = jest
      .spyOn(SkillCatalogue, 'create')
      .mockResolvedValue(mockCreatedSkill);

    const createMappingsSpy = jest
      .spyOn(SkillCategoryMapping, 'createMany')
      .mockResolvedValue(mockCreatedMappings);

    // Act
    const result = await createSkillWithCategories(env, mockSkillInput);

    // Assert
    expect(result).toEqual({
      skillId: mockCreatedSkill.id,
      skillName: mockCreatedSkill.skillName,
      description: mockCreatedSkill.description,
      categories: mockSkillInput.categories,
      categoryMappings: mockCreatedMappings.map((mapping) => mapping.id),
    });

    expect(createSkillSpy).toHaveBeenCalledWith(
      env,
      {
        skillName: mockSkillInput.skillName,
      },
      ['id', 'skillName', 'description']
    );

    expect(createMappingsSpy).toHaveBeenCalledWith(
      env,
      expect.arrayContaining([
        expect.objectContaining({
          skillId: mockCreatedSkill.id,
          categoryId: mockSkillInput.categories[0],
        }),
        expect.objectContaining({
          skillId: mockCreatedSkill.id,
          categoryId: mockSkillInput.categories[1],
        }),
      ])
    );
  });

  it('should throw error when skillName is not provided', async () => {
    // Arrange
    const mockSkillInput: EvstBaseSkillInput = {
      skillName: '',
      categories: [1],
    };

    const createSpy = jest.spyOn(SkillCatalogue, 'create');
    const createMappingsSpy = jest.spyOn(SkillCategoryMapping, 'createMany');

    // Act & Assert
    await expect(
      createSkillWithCategories(env, mockSkillInput)
    ).rejects.toThrow('Skill name is required');
    expect(createSpy).not.toHaveBeenCalled();
    expect(createMappingsSpy).not.toHaveBeenCalled();
  });

  it('should throw error when categories array is empty', async () => {
    // Arrange
    const mockSkillInput: EvstBaseSkillInput = {
      skillName: 'JavaScript',
      categories: [],
    };

    const createSpy = jest.spyOn(SkillCatalogue, 'create');
    const createMappingsSpy = jest.spyOn(SkillCategoryMapping, 'createMany');

    // Act & Assert
    await expect(
      createSkillWithCategories(env, mockSkillInput)
    ).rejects.toThrow('At least one category ID is required');
    expect(createSpy).not.toHaveBeenCalled();
    expect(createMappingsSpy).not.toHaveBeenCalled();
  });

  it('should handle category mapping creation failure', async () => {
    // Arrange
    const mockSkillInput: EvstBaseSkillInput = {
      skillName: 'JavaScript',
      categories: [1],
    };

    const mockCreatedSkill = {
      id: 1,
      skillName: 'JavaScript',
      createdBy: '<EMAIL>',
      createdDate: new Date(),
      lastModifiedBy: '<EMAIL>',
      lastModifiedDate: new Date(),
      version: 1,
    };

    jest.spyOn(SkillCatalogue, 'create').mockResolvedValue(mockCreatedSkill);
    jest
      .spyOn(SkillCategoryMapping, 'createMany')
      .mockRejectedValue(new Error('Mapping creation failed'));

    await expect(
      createSkillWithCategories(env, mockSkillInput)
    ).rejects.toThrow('Mapping creation failed');
  });
});
