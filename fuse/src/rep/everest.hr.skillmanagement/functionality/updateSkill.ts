import type { ControllerClientProvider } from '@everestsystems/content-core';
import type { EvstUpdateSkillType } from '@pkg/everest.hr.skillmanagement/types/composites/UpdateSkillType';
import { SkillCatalogue } from '@pkg/everest.hr.skillmanagement/types/SkillCatalogue';

export default async function updateSkill(
  env: ControllerClientProvider,
  skillDetails: EvstUpdateSkillType
): Promise<EvstUpdateSkillType> {
  const { skillName, skillId, description } = skillDetails;

  // Only validate skillName if it's being updated
  if (skillName !== undefined && (!skillName || skillName.trim() === '')) {
    throw new Error('Skill name is required and cannot be empty');
  }

  // Only check for duplicate names if skillName is being updated
  if (skillName !== undefined) {
    const existingSkill = await SkillCatalogue.read(
      env,
      { skillName: skillName.trim() },
      ['id']
    );

    if (existingSkill && existingSkill.id !== skillId) {
      throw new Error('A skill with this name already exists');
    }
  }

  // Build the update object with only the fields that are being updated
  const modifiedSkill: any = { id: skillId };

  if (skillName !== undefined) {
    modifiedSkill.skillName = skillName.trim();
  }

  if (description !== undefined) {
    modifiedSkill.description = description;
  }

  // Update skill
  const updatedSkill = await SkillCatalogue.update(
    env,
    { id: skillId },
    modifiedSkill,
    ['id', 'skillName', 'description']
  );

  const updatedSkillDetails = {
    skillName: updatedSkill.skillName,
    description: updatedSkill.description,
    skillId: updatedSkill.id,
  };

  return updatedSkillDetails;
}
