import type { Controller<PERSON>lientProvider } from '@everestsystems/content-core';
import type { EvstBaseSkillInput } from '@pkg/everest.hr.skillmanagement/types/composites/BaseSkillInput';
import type { EvstBaseSkillOutput } from '@pkg/everest.hr.skillmanagement/types/composites/BaseSkillOutput';

import { SkillCatalogue } from '../types/SkillCatalogue';

export default async function createSkillInternal(
  env: ControllerClientProvider,
  skillDetails: EvstBaseSkillInput
): Promise<EvstBaseSkillOutput> {
  const { skillName, description } = skillDetails;
  if (!skillName) {
    throw new Error('Skill name is required');
  }

  // Create the skill
  const createSkill = await SkillCatalogue.create(
    env,
    {
      skillName: skillName,
      description: description,
    },
    ['id', 'skillName', 'description']
  );
  const result = {
    skillId: createSkill.id,
    skillName: createSkill.skillName,
    description: createSkill.description,
  };
  return result;
}
