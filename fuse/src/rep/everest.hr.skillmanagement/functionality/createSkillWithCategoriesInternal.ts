import type { ControllerClientProvider } from '@everestsystems/content-core';
import type { EvstBaseSkillInput } from '@pkg/everest.hr.skillmanagement/types/composites/BaseSkillInput';
import type { EvstBaseSkillOutput } from '@pkg/everest.hr.skillmanagement/types/composites/BaseSkillOutput';
import { SkillCatalogue } from '@pkg/everest.hr.skillmanagement/types/SkillCatalogue';
import { SkillCategoryMapping } from '@pkg/everest.hr.skillmanagement/types/SkillCategoryMapping';

export default async function createSkillWithCategoriesInternal(
  env: ControllerClientProvider,
  skillDetails: EvstBaseSkillInput
): Promise<EvstBaseSkillOutput> {
  // Validate required fields
  const { skillName, categories, description } = skillDetails;
  if (!skillName) {
    throw new Error('Skill name is required');
  }

  if (!categories?.length) {
    throw new Error('At least one category ID is required');
  }

  // Create the skill
  const createdSkill = await SkillCatalogue.create(
    env,
    {
      skillName: skillName,
      description: description,
    },
    ['id', 'skillName', 'description']
  );

  // Create category mappings
  const categoryMappings = categories.map((categoryId) => ({
    categoryId,
    skillId: createdSkill.id,
  }));
  const createdMappings = await SkillCategoryMapping.createMany(
    env,
    categoryMappings
  );
  const createdMappingIds = createdMappings.map((mapping) => mapping.id);
  const result = {
    skillId: createdSkill.id,
    skillName: createdSkill.skillName,
    description: createdSkill.description,
    categories: categories,
    categoryMappings: createdMappingIds,
  };
  return result;
}
