import type { ISession } from '@everestsystems/content-core';
import { SkillCatalogue } from '@pkg/everest.hr.skillmanagement/types/SkillCatalogue';

import createSkillInternal from './createSkillInternal';

describe('createSkillInternal', () => {
  let env: ISession;

  beforeEach(() => {
    env = {} as ISession;
    jest.resetAllMocks();
  });

  it('should successfully create a skill', async () => {
    // Arrange
    const mockSkill = {
      skillName: 'JavaScript',
    };
    const mockCreatedSkill = {
      id: 1,
      skillName: 'JavaScript',
      description: 'Test Skill Description',
      createdBy: '<EMAIL>',
      createdDate: new Date(),
      lastModifiedBy: '<EMAIL>',
      lastModifiedDate: new Date(),
      version: 1,
    };

    const createSpy = jest
      .spyOn(SkillCatalogue, 'create')
      .mockResolvedValue(mockCreatedSkill);

    // Act
    const result = await createSkillInternal(env, mockSkill);

    // Assert
    expect(result).toEqual({
      skillId: 1,
      skillName: 'JavaScript',
      description: 'Test Skill Description',
    });
  });

  it('should throw error when skillName is empty string', async () => {
    // Arrange
    const mockSkill = {
      skillName: '',
    };
    const createSpy = jest.spyOn(SkillCatalogue, 'create');

    // Act & Assert
    await expect(createSkillInternal(env, mockSkill)).rejects.toThrow(
      'Skill name is required'
    );
    expect(createSpy).not.toHaveBeenCalled();
  });
});
