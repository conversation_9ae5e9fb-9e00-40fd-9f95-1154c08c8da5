import type { ISession } from '@everestsystems/content-core';
import type { EvstUpdateSkillType } from '@pkg/everest.hr.skillmanagement/types/composites/UpdateSkillType';
import { SkillCatalogue } from '@pkg/everest.hr.skillmanagement/types/SkillCatalogue';

import updateSkill from './updateSkill';

describe('updateSkill', () => {
  let env: ISession;

  beforeEach(() => {
    env = {} as ISession;
    jest.resetAllMocks();
  });

  it('should successfully update a skill name', async () => {
    // Arrange
    const skillId = 101;
    const newSkillName = 'JavaScript ES6';

    const skillUpdateInput: EvstUpdateSkillType = {
      skillId,
      skillName: newSkillName,
    };

    // Mock SkillCatalogue.read for duplicate check - no duplicate found
    jest.spyOn(SkillCatalogue, 'read').mockResolvedValue(null);

    // Mock SkillCatalogue.update to return updated skill
    const updatedSkill = {
      id: skillId,
      skillName: newSkillName,
    };
    jest.spyOn(SkillCatalogue, 'update').mockResolvedValue(updatedSkill);

    // Act
    const result = await updateSkill(env, skillUpdateInput);

    // Assert
    expect(result).toEqual({
      skillId,
      skillName: newSkillName,
    });

    expect(SkillCatalogue.read).toHaveBeenCalledWith(
      env,
      { skillName: newSkillName.trim() },
      ['id']
    );

    expect(SkillCatalogue.update).toHaveBeenCalledWith(
      env,
      { id: skillId },
      { id: skillId, skillName: newSkillName },
      ['id', 'skillName', 'description']
    );
  });

  it('should successfully update a skill description', async () => {
    // Arrange
    const skillId = 101;
    const newDescription = 'Updated description for the skill';

    const skillUpdateInput: EvstUpdateSkillType = {
      skillId,
      description: newDescription,
    };

    // Mock SkillCatalogue.update to return updated skill
    const updatedSkill = {
      id: skillId,
      description: newDescription,
    };
    jest.spyOn(SkillCatalogue, 'update').mockResolvedValue(updatedSkill);

    // Act
    const result = await updateSkill(env, skillUpdateInput);

    // Assert
    expect(result).toEqual({
      skillId,
      description: newDescription,
    });

    expect(SkillCatalogue.update).toHaveBeenCalledWith(
      env,
      { id: skillId },
      { id: skillId, description: newDescription },
      ['id', 'skillName', 'description']
    );
  });

  it('should successfully update both skill name and description', async () => {
    // Arrange
    const skillId = 101;
    const newSkillName = 'React';
    const newDescription = 'JavaScript library for building user interfaces';

    const skillUpdateInput: EvstUpdateSkillType = {
      skillId,
      skillName: newSkillName,
      description: newDescription,
    };

    // Mock SkillCatalogue.read for duplicate check - no duplicate found
    jest.spyOn(SkillCatalogue, 'read').mockResolvedValue(null);

    // Mock SkillCatalogue.update to return updated skill
    const updatedSkill = {
      id: skillId,
      skillName: newSkillName,
      description: newDescription,
    };
    jest.spyOn(SkillCatalogue, 'update').mockResolvedValue(updatedSkill);

    // Act
    const result = await updateSkill(env, skillUpdateInput);

    // Assert
    expect(result).toEqual({
      skillId,
      skillName: newSkillName,
      description: newDescription,
    });

    expect(SkillCatalogue.read).toHaveBeenCalledWith(
      env,
      { skillName: newSkillName.trim() },
      ['id']
    );

    expect(SkillCatalogue.update).toHaveBeenCalledWith(
      env,
      { id: skillId },
      { id: skillId, skillName: newSkillName, description: newDescription },
      ['id', 'skillName', 'description']
    );
  });

  it('should throw error when skill name is empty', async () => {
    // Arrange
    const skillId = 101;
    const emptySkillName = '';

    const skillUpdateInput: EvstUpdateSkillType = {
      skillId,
      skillName: emptySkillName,
    };

    const readSpy = jest.spyOn(SkillCatalogue, 'read');
    const updateSpy = jest.spyOn(SkillCatalogue, 'update');

    // Act & Assert
    await expect(updateSkill(env, skillUpdateInput)).rejects.toThrow(
      'Skill name is required and cannot be empty'
    );
    expect(readSpy).not.toHaveBeenCalled();
    expect(updateSpy).not.toHaveBeenCalled();
  });

  it('should throw error when skill name contains only whitespace', async () => {
    // Arrange
    const skillId = 101;
    const whitespaceSkillName = '   ';

    const skillUpdateInput: EvstUpdateSkillType = {
      skillId,
      skillName: whitespaceSkillName,
    };

    const readSpy = jest.spyOn(SkillCatalogue, 'read');
    const updateSpy = jest.spyOn(SkillCatalogue, 'update');

    // Act & Assert
    await expect(updateSkill(env, skillUpdateInput)).rejects.toThrow(
      'Skill name is required and cannot be empty'
    );
    expect(readSpy).not.toHaveBeenCalled();
    expect(updateSpy).not.toHaveBeenCalled();
  });

  it('should throw error when skill name already exists for another skill', async () => {
    // Arrange
    const skillId = 101;
    const existingSkillId = 102;
    const duplicateSkillName = 'Python';

    const skillUpdateInput: EvstUpdateSkillType = {
      skillId,
      skillName: duplicateSkillName,
    };

    // Mock SkillCatalogue.read to return existing skill with different ID
    const existingSkill = { id: existingSkillId };
    jest.spyOn(SkillCatalogue, 'read').mockResolvedValue(existingSkill);
    const updateSpy = jest.spyOn(SkillCatalogue, 'update');

    // Act & Assert
    await expect(updateSkill(env, skillUpdateInput)).rejects.toThrow(
      'A skill with this name already exists'
    );
    expect(SkillCatalogue.read).toHaveBeenCalledWith(
      env,
      { skillName: duplicateSkillName.trim() },
      ['id']
    );
    expect(updateSpy).not.toHaveBeenCalled();
  });
});
