/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// ******************************************************************************
// Generated Everest Types
// ******************************************************************************

import type { Decimal, RoundingMode } from '@everestsystems/decimal';
import type {
  ArgType,
  Association,
  AssociationManagementArgs,
  AsyncResult,
  Controller,
  ControllerClientProvider,
  CreateOptionsArg,
  DataWithMetadata,
  DeleteOptionsArg,
  FieldSelector,
  Filter,
  flattenFieldSelector,
  GraphInstanceEntry,
  KernelRequestOptions,
  NonAssociationFields,
  Optional,
  PurgeArgs,
  ReadOptionsArg,
  ReadReturnType as ReadReturnTypeGeneric,
  ReadReturnTypeWithAssociations,
  RequireAtLeastOne,
  SemanticSearchArg,
  SemanticSearchRequestOptions,
  TypeSafeQueryArgType,
  UpdateManyOptionsArg,
  UpdateOptionsArg,
  UpsertOptionsArg,
  WriteReturnType,
  WriteReturnTypeWithAssociations,
} from '@everestsystems/content-core';
import type { EvstJSON as everest_appserver_primitive_JSON } from "@pkg/everest.appserver/types/primitives/JSON";
import type { EvstUUID as everest_appserver_primitive_UUID } from "@pkg/everest.appserver/types/primitives/UUID";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { EvstUserEmail as everest_appserver_usermgmt_primitive_user_UserEmail } from "@pkg/everest.appserver.usermgmt/types/primitives/user/UserEmail";
import type { EvstDateTime as everest_appserver_primitive_DateTime } from "@pkg/everest.appserver/types/primitives/DateTime";
import type { EvstSkillStatus as everest_hr_skillmanagement_enum_SkillStatus } from "@pkg/everest.hr.skillmanagement/types/enums/SkillStatus";
import type { SkillCategoryMapping as everest_hr_skillmanagement_model_node_SkillCategoryMapping } from "@pkg/everest.hr.skillmanagement/types/SkillCategoryMapping";
import type { EmployeeSkillAssignment as everest_hr_skillmanagement_model_node_EmployeeSkillAssignment } from "@pkg/everest.hr.skillmanagement/types/EmployeeSkillAssignment";
import type { SkillRequirement as everest_hr_skillmanagement_model_node_SkillRequirement } from "@pkg/everest.hr.skillmanagement/types/SkillRequirement";

/**
 * Types for SkillCatalogue
 *
 * @everest-node-metadata='{"visibility":"private"}'
 */
export namespace SkillCatalogue {
  export type CreationFields = Pick<SkillCatalogue, 'uuid' | 'externalId' | 'active' | 'description' | 'skillName' | 'status'>;
  export type UniqueFields = Pick<SkillCatalogue, 'id' | 'uuid' | 'skillName'>;
  export type UniqueWhereInput = (RequireAtLeastOne<UniqueFields>) & Partial<SkillCatalogue>;
  export type ReadReturnType<U extends string | number | symbol = keyof SkillCatalogue> = ReadReturnTypeGeneric<SkillCatalogue, U>;

  export interface IControllerClient extends Omit<Controller<SkillCatalogue>, 'all' | 'getHistory' | 'lookup' | 'purge' | 'queryWithMetadata' | 'semanticSearch' | 'upsertMany'> {
    /** Creates sample data for skill management including categories, skills, assignments and project role requirements */
    sampleDataSkillManagement(): Promise<everest_appserver_primitive_JSON>;
  }

  /**
   * This model is for defining list of skills
   */
  export type SkillCatalogue = {
    /** Database generated identifier of this record. */
    id: number;
    /** Database generated version of this record. */
    version?: number;
    uuid?: everest_appserver_primitive_UUID | null;
    /**
     * ID of the system from which this record was sourced
     */
    externalId?: everest_appserver_primitive_Text | null;
    /**
     * Indicates whether this record is usable for business users
     */
    active?: everest_appserver_primitive_TrueFalse | null;
    /**
     * User that created this record
     */
    createdBy: everest_appserver_usermgmt_primitive_user_UserEmail;
    /**
     * Date/time when the record was created
     */
    createdDate: everest_appserver_primitive_DateTime;
    /**
     * User that last modified the record
     */
    lastModifiedBy: everest_appserver_usermgmt_primitive_user_UserEmail;
    /**
     * Date/time of the last modification to the record
     */
    lastModifiedDate: everest_appserver_primitive_DateTime;
    description?: everest_appserver_primitive_Text | null;
    skillName: everest_appserver_primitive_Text;
    status?: everest_hr_skillmanagement_enum_SkillStatus | null;
    };
  /**
   * This model is for defining list of skills
   */
  export type SkillCatalogueWithAssociation = SkillCatalogue & {
    ["SkillCategoryMapping-SkillCatalogue"]?: Association<everest_hr_skillmanagement_model_node_SkillCategoryMapping.SkillCategoryMappingWithAssociation>[];
    ["Mappings"]?: Association<everest_hr_skillmanagement_model_node_SkillCategoryMapping.SkillCategoryMappingWithAssociation>[];
    ["SkillAssignment-SkillCatalogue"]?: Association<everest_hr_skillmanagement_model_node_EmployeeSkillAssignment.EmployeeSkillAssignmentWithAssociation>[];
    ["Assignments"]?: Association<everest_hr_skillmanagement_model_node_EmployeeSkillAssignment.EmployeeSkillAssignmentWithAssociation>[];
    ["SkillRequirement-SkillCatalogue"]?: Association<everest_hr_skillmanagement_model_node_SkillRequirement.SkillRequirementWithAssociation>[];
    };
  /** @deprecated - use {@link MODEL_URN} instead */
  export const name = 'urn:evst:everest:hr/skillmanagement:model/node:SkillCatalogue';
  export const MODEL_URN = 'urn:evst:everest:hr/skillmanagement:model/node:SkillCatalogue';
  /** @deprecated - use {@link MODEL_URN} instead */
  export const MODEL_ID = 'everest.hr.skillmanagement/SkillCatalogueModel.SkillCatalogue';
  export const MODEL_UUID = 'bc4fc095-1578-4b45-922e-fea95d267802';

  /** @return a model controller instance for SkillCatalogue. */
  export function client(env: ControllerClientProvider) {
    return env.controllerClient<SkillCatalogue.IControllerClient>(MODEL_URN);
  }

  /** write a new object to the database. */
  export async function create<U extends keyof SkillCatalogue>(env: ControllerClientProvider, input: CreationFields, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): Promise<WriteReturnType<SkillCatalogue, U>> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).create(input, fields, options);
  }

  /** write new objects to the database. */
  export async function createMany<U extends keyof SkillCatalogue>(env: ControllerClientProvider, input: CreationFields[], fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): Promise<WriteReturnType<SkillCatalogue, U>[]> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).createMany(input, fields, options);
  }

  /** delete the object that is uniquely identified by the where condition. */
  export async function Delete(env: ControllerClientProvider, where: UniqueWhereInput): Promise<Partial<SkillCatalogue>[]> {
    return (await client(env)).delete(where);
  }

  /** delete all objects. */
  export async function deleteAll(env: ControllerClientProvider): Promise<Partial<SkillCatalogue>[]> {
    return (await client(env)).deleteAll();
  }

  /** delete the objects that are identified by the where condition. */
  export async function deleteMany(env: ControllerClientProvider, where: Filter<SkillCatalogueWithAssociation>): Promise<Partial<SkillCatalogue>[]>;
  /** @deprecated use the alternative overloaded function */
  export async function deleteMany(env: ControllerClientProvider, where: Partial<SkillCatalogue>): Promise<Partial<SkillCatalogue>[]>;
  export async function deleteMany(env: ControllerClientProvider, where: Filter<SkillCatalogue> | Partial<SkillCatalogue>): Promise<Partial<SkillCatalogue>[]> {
    return (await client(env)).deleteMany(where as Filter<SkillCatalogue>);
  }

  /** returns a list of all objects matching the requirements. */
  export async function query<U extends FieldSelector<SkillCatalogueWithAssociation>>(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<SkillCatalogueWithAssociation>, 'draft'>, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<ReadReturnTypeWithAssociations<SkillCatalogueWithAssociation, U>[]>;
  /** @deprecated Use the alternative overloaded method for querying associations. */
  export async function query<U extends keyof SkillCatalogue, V extends string = 'ALL_FIELDS'>(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<SkillCatalogue>, 'draft'> | Omit<Omit<TypeSafeQueryArgType<SkillCatalogue>, 'draft'>, 'where'> & { where?: Partial<SkillCatalogue> }, fieldlist: ReadonlyArray<U | V> | 'ALL_FIELDS'): Promise<ReadReturnType<V>[]>;
  export async function query(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<SkillCatalogue>, 'draft'>, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown[]> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).query(args, fields as ReadonlyArray<string>);
  }

  /** reads the object that is uniquely identified by the where condition. */
  export async function read<U extends FieldSelector<SkillCatalogueWithAssociation>>(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnTypeWithAssociations<SkillCatalogueWithAssociation, U>>;
  /** @deprecated Use the alternative overloaded method for reading associations. */
  export async function read<U extends keyof SkillCatalogue>(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnType<U>>;
  export async function read(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<unknown> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).read(where, fields as ReadonlyArray<keyof SkillCatalogue>, options);
  }

  /** writes modified values back to the database for a given node instance. */
  export async function update<U extends FieldSelector<SkillCatalogueWithAssociation>>(env: ControllerClientProvider, where: UniqueWhereInput, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnTypeWithAssociations<SkillCatalogueWithAssociation, U>>;
  /** @deprecated Use the alternative overloaded method for writing with associations. */
  export async function update<U extends keyof SkillCatalogue>(env: ControllerClientProvider, where: Partial<SkillCatalogue>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnType<SkillCatalogue, U>>;
  export async function update(env: ControllerClientProvider, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).update(where, data, fields as ReadonlyArray<keyof SkillCatalogue>);
  }

  /** writes modified values back to the database for selected node instances. */
  export async function updateMany<U extends FieldSelector<SkillCatalogueWithAssociation>>(env: ControllerClientProvider, where: Filter<SkillCatalogueWithAssociation>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnTypeWithAssociations<SkillCatalogueWithAssociation, U>[]>;
  /** @deprecated Use the alternative overloaded method for writing with associations. */
  export async function updateMany<U extends keyof SkillCatalogue>(env: ControllerClientProvider, where: Partial<SkillCatalogue>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnType<SkillCatalogue, U>[]>;
  export async function updateMany(env: ControllerClientProvider, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown[]> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).updateMany(where, data, fields as ReadonlyArray<keyof SkillCatalogue>);
  }

  /** creates or updates object identified by where. */
  export async function upsert<U extends keyof SkillCatalogue & string>(env: ControllerClientProvider, data: Partial<SkillCatalogue>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<SkillCatalogue, U>>;
  /** @deprecated use the overload without an explicit where clause */
  export async function upsert<U extends keyof SkillCatalogue & string>(env: ControllerClientProvider, where: Partial<SkillCatalogue>, data: Partial<SkillCatalogue>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<SkillCatalogue, U>>;
  export async function upsert<U extends keyof SkillCatalogue & string>(env: ControllerClientProvider, whereOrData: Partial<SkillCatalogue>, dataOrFieldList?: Partial<SkillCatalogue> | ReadonlyArray<U> | 'ALL_FIELDS', fieldlistOrOptions?: ReadonlyArray<U> | 'ALL_FIELDS' | Omit<UpsertOptionsArg, 'draft'>, maybeOptions?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<SkillCatalogue, U>> {
    return (await client(env)).upsert(whereOrData, dataOrFieldList, fieldlistOrOptions, maybeOptions);
  }
}
