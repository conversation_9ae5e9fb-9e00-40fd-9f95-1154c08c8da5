import type { EvstID as everest_appserver_primitive_ID } from "@pkg/everest.appserver/types/primitives/ID";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";

/**
 * Composite type.
 *
 * @everest-node-metadata='{"visibility":"public"}'
 */
export interface EvstBaseSkillOutput {
  skillId: everest_appserver_primitive_ID;
  skillName: everest_appserver_primitive_Text;
  categories?: everest_appserver_primitive_ID[] | null;
  categoryMappings?: everest_appserver_primitive_ID[] | null;
  description?: everest_appserver_primitive_Text | null;
}
