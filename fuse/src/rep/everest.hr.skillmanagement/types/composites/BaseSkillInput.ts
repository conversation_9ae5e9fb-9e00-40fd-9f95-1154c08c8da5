import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstID as everest_appserver_primitive_ID } from "@pkg/everest.appserver/types/primitives/ID";

/**
 * Composite type.
 *
 * @everest-node-metadata='{"visibility":"public"}'
 */
export interface EvstBaseSkillInput {
  skillName: everest_appserver_primitive_Text;
  description?: everest_appserver_primitive_Text | null;
  categories?: everest_appserver_primitive_ID[] | null;
}
