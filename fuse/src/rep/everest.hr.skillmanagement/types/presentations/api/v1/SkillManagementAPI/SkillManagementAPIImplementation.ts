/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstID as everest_appserver_primitive_ID } from "@pkg/everest.appserver/types/primitives/ID";
import type { EvstSkillLevelType as everest_hr_skillmanagement_primitive_SkillLevelType } from "@pkg/everest.hr.skillmanagement/types/primitives/SkillLevelType";
import type { EvstSkillApprovalStatus as everest_hr_skillmanagement_enum_SkillApprovalStatus } from "@pkg/everest.hr.skillmanagement/types/enums/SkillApprovalStatus";

import type { ControllerClientProvider } from '@everestsystems/content-core';

/**
 * Generated implementation types for API presentation SkillManagementAPI.
 */
export namespace SkillManagementAPIImplementation {
  export namespace Methods {
    export namespace createSkill {
      export type Input = {
        skillDetails: {
          skillName: everest_appserver_primitive_Text;
          categories?: everest_appserver_primitive_ID[];
          description?: everest_appserver_primitive_Text;
        };
      };

      export type Output = {
        skillId: everest_appserver_primitive_ID;
        skillName: everest_appserver_primitive_Text;
        description?: everest_appserver_primitive_Text | undefined;
        categories?: everest_appserver_primitive_ID[] | undefined;
        categoryMappings?: everest_appserver_primitive_ID[] | undefined;
      };

      export namespace Execute {
        export type Request = {
          session: ControllerClientProvider;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }
    export namespace deleteSkill {
      export type Input = {
        skillId: everest_appserver_primitive_ID;
      };

      export type Output = Record<string, never>;

      export namespace Execute {
        export type Request = {
          session: ControllerClientProvider;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }
    export namespace updateSkill {
      export type Input = {
        skillDetails: {
          skillId: everest_appserver_primitive_ID;
          skillName?: everest_appserver_primitive_Text;
          description?: everest_appserver_primitive_Text;
        };
      };

      export type Output = {
        skillId: everest_appserver_primitive_ID;
        skillName?: everest_appserver_primitive_Text | undefined;
        description?: everest_appserver_primitive_Text | undefined;
      };

      export namespace Execute {
        export type Request = {
          session: ControllerClientProvider;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }
    export namespace updateCategoriesForSkill {
      export type Input = {
        skillCategoryDetails: {
          skillId: everest_appserver_primitive_ID;
          categories: everest_appserver_primitive_ID[];
        };
      };

      export type Output = {
        skillId: everest_appserver_primitive_ID;
        categories: everest_appserver_primitive_ID[];
        categoryMappings: everest_appserver_primitive_ID[];
      };

      export namespace Execute {
        export type Request = {
          session: ControllerClientProvider;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }
    export namespace assignSkillToEmployees {
      export type Input = {
        skillAssignments: {
          skillId: everest_appserver_primitive_ID;
          skillLevelDetail: {
            id: everest_appserver_primitive_ID;
            skillLevel: everest_hr_skillmanagement_primitive_SkillLevelType;
          }[];
        };
      };

      export type Output = {
        assignments: {
          employeeId: everest_appserver_primitive_ID;
          assignmentId: everest_appserver_primitive_ID;
          skillId: everest_appserver_primitive_ID;
          skillLevel: everest_hr_skillmanagement_primitive_SkillLevelType;
          skillApprovalStatus?: everest_hr_skillmanagement_enum_SkillApprovalStatus | undefined;
        }[];
      };

      export namespace Execute {
        export type Request = {
          session: ControllerClientProvider;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }
    export namespace assignSkillsToEmployee {
      export type Input = {
        skillAssignments: {
          employeeId: everest_appserver_primitive_ID;
          skillLevelDetail: {
            id: everest_appserver_primitive_ID;
            skillLevel: everest_hr_skillmanagement_primitive_SkillLevelType;
          }[];
        };
      };

      export type Output = {
        assignments: {
          employeeId: everest_appserver_primitive_ID;
          assignmentId: everest_appserver_primitive_ID;
          skillId: everest_appserver_primitive_ID;
          skillLevel: everest_hr_skillmanagement_primitive_SkillLevelType;
          skillApprovalStatus?: everest_hr_skillmanagement_enum_SkillApprovalStatus | undefined;
        }[];
      };

      export namespace Execute {
        export type Request = {
          session: ControllerClientProvider;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }
    export namespace updateEmployeeSkillAssignment {
      export type Input = {
        employeeSkillAssignment: {
          employeeId: everest_appserver_primitive_ID;
          skillAssignmentId: everest_appserver_primitive_ID;
          skillLevel?: everest_appserver_primitive_ID;
          skillApprovalStatus?: everest_hr_skillmanagement_enum_SkillApprovalStatus;
        };
      };

      export type Output = {
        employeeId: everest_appserver_primitive_ID;
        assignmentId: everest_appserver_primitive_ID;
        skillId: everest_appserver_primitive_ID;
        skillLevel: everest_hr_skillmanagement_primitive_SkillLevelType;
        skillApprovalStatus?: everest_hr_skillmanagement_enum_SkillApprovalStatus | undefined;
      };

      export namespace Execute {
        export type Request = {
          session: ControllerClientProvider;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }
    export namespace deleteSkillAssignment {
      export type Input = {
        skillAssignmentId: everest_appserver_primitive_ID;
      };

      export type Output = Record<string, never>;

      export namespace Execute {
        export type Request = {
          session: ControllerClientProvider;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }
    export namespace addSkillRequirementToProjectPositions {
      export type Input = {
        skillRequirements: {
          skillId: everest_appserver_primitive_ID;
          skillLevelDetail: {
            id: everest_appserver_primitive_ID;
            skillLevel: everest_hr_skillmanagement_primitive_SkillLevelType;
          }[];
        };
      };

      export type Output = {
        requirements: {
          projectPositionId: everest_appserver_primitive_ID;
          requirementId: everest_appserver_primitive_ID;
          skillId: everest_appserver_primitive_ID;
          skillLevel: everest_hr_skillmanagement_primitive_SkillLevelType;
        }[];
      };

      export namespace Execute {
        export type Request = {
          session: ControllerClientProvider;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }
    export namespace addSkillRequirementsToProjectPosition {
      export type Input = {
        skillRequirements: {
          projectPositionId: everest_appserver_primitive_ID;
          skillLevelDetail: {
            id: everest_appserver_primitive_ID;
            skillLevel: everest_hr_skillmanagement_primitive_SkillLevelType;
          }[];
        };
      };

      export type Output = {
        requirements: {
          projectPositionId: everest_appserver_primitive_ID;
          requirementId: everest_appserver_primitive_ID;
          skillId: everest_appserver_primitive_ID;
          skillLevel: everest_hr_skillmanagement_primitive_SkillLevelType;
        }[];
      };

      export namespace Execute {
        export type Request = {
          session: ControllerClientProvider;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }
    export namespace updateProjectPositionSkillRequirement {
      export type Input = {
        projectPositionSkillRequirement: {
          projectPositionId: everest_appserver_primitive_ID;
          skillRequirementId: everest_appserver_primitive_ID;
          skillLevel: everest_hr_skillmanagement_primitive_SkillLevelType;
        };
      };

      export type Output = {
        projectPositionId: everest_appserver_primitive_ID;
        requirementId: everest_appserver_primitive_ID;
        skillId: everest_appserver_primitive_ID;
        skillLevel: everest_hr_skillmanagement_primitive_SkillLevelType;
      };

      export namespace Execute {
        export type Request = {
          session: ControllerClientProvider;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }
  }

  export type Implementation = {
    createSkill: Methods.createSkill.Implementation;
    deleteSkill: Methods.deleteSkill.Implementation;
    updateSkill: Methods.updateSkill.Implementation;
    updateCategoriesForSkill: Methods.updateCategoriesForSkill.Implementation;
    assignSkillToEmployees: Methods.assignSkillToEmployees.Implementation;
    assignSkillsToEmployee: Methods.assignSkillsToEmployee.Implementation;
    updateEmployeeSkillAssignment: Methods.updateEmployeeSkillAssignment.Implementation;
    deleteSkillAssignment: Methods.deleteSkillAssignment.Implementation;
    addSkillRequirementToProjectPositions: Methods.addSkillRequirementToProjectPositions.Implementation;
    addSkillRequirementsToProjectPosition: Methods.addSkillRequirementsToProjectPosition.Implementation;
    updateProjectPositionSkillRequirement: Methods.updateProjectPositionSkillRequirement.Implementation;
  };
}

