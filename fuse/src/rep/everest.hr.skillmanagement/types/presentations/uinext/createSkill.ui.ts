/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

// ******************************************************************************
// Generated Everest Presentation UI Types
// ******************************************************************************

import type {
  GetElementType,
  UIExecutionContext
} from '@everestsystems/content-core';

import type { SkillCategory as everest_hr_skillmanagement_model_node_SkillCategory } from "@pkg/everest.hr.skillmanagement/types/SkillCategory";
import type { EvstID as everest_appserver_primitive_ID } from "@pkg/everest.appserver/types/primitives/ID";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstJSON as everest_appserver_primitive_JSON } from "@pkg/everest.appserver/types/primitives/JSON";

export namespace createSkillPresentationUI {
  export namespace dataSets {
    export namespace categories {
      export type instance = {
        categoryName?: everest_hr_skillmanagement_model_node_SkillCategory.SkillCategoryWithAssociation['categoryName'] | undefined | null;
        id?: everest_hr_skillmanagement_model_node_SkillCategory.SkillCategoryWithAssociation['id'] | undefined | null;
        /** Unique UI identifier of the node instance */
        _nodeReference: string;
      };

      export type data = instance[];
    }
  }

  export namespace actions {
    export namespace createSkill {
      export type input = {
        categoryIds: everest_appserver_primitive_ID[];
        description: everest_appserver_primitive_Text;
        skillName: everest_appserver_primitive_Text;
      };
      export type output = {
        result: everest_appserver_primitive_JSON;
      };
    }
  }

  export type context = UIExecutionContext.BasicExecutionContext & {
    data: {
      categories: dataSets.categories.data;
    };
    state: Record<string, unknown>;
    presentationClient: {
      createSkill: (input: actions.createSkill.input) => Promise<actions.createSkill.output>;
    };
  }
}
