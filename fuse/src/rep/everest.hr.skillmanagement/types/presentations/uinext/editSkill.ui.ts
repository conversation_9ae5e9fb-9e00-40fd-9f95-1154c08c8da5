/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

// ******************************************************************************
// Generated Everest Presentation UI Types
// ******************************************************************************

import type {
  GetElementType,
  UIExecutionContext
} from '@everestsystems/content-core';

import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EmployeeSkillAssignment as everest_hr_skillmanagement_model_node_EmployeeSkillAssignment } from "@pkg/everest.hr.skillmanagement/types/EmployeeSkillAssignment";
import type { SkillCategoryMapping as everest_hr_skillmanagement_model_node_SkillCategoryMapping } from "@pkg/everest.hr.skillmanagement/types/SkillCategoryMapping";
import type { SkillCatalogue as everest_hr_skillmanagement_model_node_SkillCatalogue } from "@pkg/everest.hr.skillmanagement/types/SkillCatalogue";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";

export namespace editSkillPresentationUI {
  export namespace dataSets {
    export namespace page {
      export type instance = {
        title?: everest_appserver_primitive_Text | undefined | null;
        /** Unique UI identifier of the node instance */
        _nodeReference: string;
      };

      export type data = instance;
    }

    export namespace skillAssignmentTable {
      export type instance = {
        approvalStatus?: everest_hr_skillmanagement_model_node_EmployeeSkillAssignment.EmployeeSkillAssignment["approvalStatus"] | undefined | null;
        employee?: everest_hr_skillmanagement_model_node_EmployeeSkillAssignment.EmployeeSkillAssignment["employeeId"] | undefined | null;
        id?: everest_hr_skillmanagement_model_node_EmployeeSkillAssignment.EmployeeSkillAssignment["id"] | undefined | null;
        skillLevel?: everest_hr_skillmanagement_model_node_EmployeeSkillAssignment.EmployeeSkillAssignment["skillLevel"] | undefined | null;
        /** Unique UI identifier of the node instance */
        _nodeReference: string;
      };

      export type data = instance[];
    }

    export namespace skillDetailsFieldGroup {
      export type instance = {
        categories?: everest_hr_skillmanagement_model_node_SkillCategoryMapping.SkillCategoryMapping["categoryId"][] | undefined | null;
        createdBy?: everest_hr_skillmanagement_model_node_SkillCatalogue.SkillCatalogue["createdBy"] | undefined | null;
        createdDate?: everest_hr_skillmanagement_model_node_SkillCatalogue.SkillCatalogue["createdDate"] | undefined | null;
        description?: everest_hr_skillmanagement_model_node_SkillCatalogue.SkillCatalogue["description"] | undefined | null;
        id?: everest_hr_skillmanagement_model_node_SkillCatalogue.SkillCatalogue["id"] | undefined | null;
        lastModifiedBy?: everest_hr_skillmanagement_model_node_SkillCatalogue.SkillCatalogue["lastModifiedBy"] | undefined | null;
        lastModifiedDate?: everest_hr_skillmanagement_model_node_SkillCatalogue.SkillCatalogue["lastModifiedDate"] | undefined | null;
        skillName?: everest_hr_skillmanagement_model_node_SkillCatalogue.SkillCatalogue["skillName"] | undefined | null;
        /** Unique UI identifier of the node instance */
        _nodeReference: string;
      };

      export type data = instance;
    }
  }

  export namespace actions {
    export namespace cancel {
      export type input = never;
      export type output = void;
    }

    export namespace revise {
      export type input = never;
      export type output = void;
    }

    export namespace save {
      export type input = never;
      export type output = {
        result: everest_appserver_primitive_TrueFalse;
      };
    }
  }

  export type context = UIExecutionContext.BasicExecutionContext & {
    data: {
      page: dataSets.page.data;
      skillAssignmentTable: dataSets.skillAssignmentTable.data;
      skillDetailsFieldGroup: dataSets.skillDetailsFieldGroup.data;
    };
    state: Record<string, unknown>;
    presentationClient: {
      cancel: () => Promise<actions.cancel.output>;
      revise: () => Promise<actions.revise.output>;
      save: () => Promise<actions.save.output>;
    };
  }
}
