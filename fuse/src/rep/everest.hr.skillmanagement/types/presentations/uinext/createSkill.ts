/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

// ******************************************************************************
// Generated Everest Presentation Types
// ******************************************************************************

import type {
  ActionValidationResult,
  COMPONENT,
  CONFIGURATIONS,
  DATA,
  DynamicFieldsMetadata,
  GetElementType,
  DynamicFieldDefinition,
  DATA_SETS,
  DYNAMIC_FIELDS,
  FieldInstanceMetadata,
  FieldLevelMetadata,
  IDENTIFIER,
  ISession,
  METADATA,
  QueryInput,
  QueryReason,
  RoutineDeterminationResult,
  RoutineValidationContext,
  RoutineValidationResult,
  TableComponent,
  ValidateReason
} from '@everestsystems/content-core';

import type { EvstID as everest_appserver_primitive_ID } from "@pkg/everest.appserver/types/primitives/ID";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstJSON as everest_appserver_primitive_JSON } from "@pkg/everest.appserver/types/primitives/JSON";

export namespace createSkillPresentation {
  export type mode = 'create';

  export namespace actions {
    export namespace createSkill {
      export type input = {
        categoryIds: everest_appserver_primitive_ID[];
        description: everest_appserver_primitive_Text;
        skillName: everest_appserver_primitive_Text;
      };

      export type output = {
        result: everest_appserver_primitive_JSON;
      };

      export type validateInput = {
        session: ISession;
        input: input;
      }

      export type executeInput = {
        session: ISession;
        input: input;
      }

      export type execute = (
        executeInput: executeInput
      ) => Promise<output>;

      export type validate = (
        validateInput: validateInput
      ) => Promise<ActionValidationResult>;

      export type implementation =
        | execute
        | {
            validate?: validate;
            execute: execute;
          };
    }
  }

  export namespace dataSources {

  }

  export type implementation = {
    createSkill: actions.createSkill.implementation;
  };
}
