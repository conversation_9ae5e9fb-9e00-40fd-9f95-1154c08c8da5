/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

// ******************************************************************************
// Generated Everest Presentation Types
// ******************************************************************************

import type {
  ActionValidationResult,
  COMPONENT,
  CONFIGURATIONS,
  DATA,
  DynamicFieldsMetadata,
  GetElementType,
  DynamicFieldDefinition,
  DATA_SETS,
  DYNAMIC_FIELDS,
  FieldInstanceMetadata,
  FieldLevelMetadata,
  IDENTIFIER,
  ISession,
  METADATA,
  QueryInput,
  QueryReason,
  RoutineDeterminationResult,
  RoutineValidationContext,
  RoutineValidationResult,
  TableComponent,
  ValidateReason
} from '@everestsystems/content-core';

import type { EvstID as everest_appserver_primitive_ID } from "@pkg/everest.appserver/types/primitives/ID";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { SkillCategoryMapping as everest_hr_skillmanagement_model_node_SkillCategoryMapping } from "@pkg/everest.hr.skillmanagement/types/SkillCategoryMapping";
import type { SkillCatalogue as everest_hr_skillmanagement_model_node_SkillCatalogue } from "@pkg/everest.hr.skillmanagement/types/SkillCatalogue";
import type { EmployeeSkillAssignment as everest_hr_skillmanagement_model_node_EmployeeSkillAssignment } from "@pkg/everest.hr.skillmanagement/types/EmployeeSkillAssignment";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";

export namespace editSkillPresentation {
  export type mode = 'view' | 'edit';

  export namespace dataSources {
    export namespace SkillDetails {
      export type levels = {
        '': never;
        Page: {
          title?: everest_appserver_primitive_Text | undefined | null;
        };
        Skill: {
          categories?: everest_hr_skillmanagement_model_node_SkillCategoryMapping.SkillCategoryMapping["categoryId"][] | undefined | null;
          createdBy?: everest_hr_skillmanagement_model_node_SkillCatalogue.SkillCatalogue["createdBy"] | undefined | null;
          createdDate?: everest_hr_skillmanagement_model_node_SkillCatalogue.SkillCatalogue["createdDate"] | undefined | null;
          description?: everest_hr_skillmanagement_model_node_SkillCatalogue.SkillCatalogue["description"] | undefined | null;
          id?: everest_hr_skillmanagement_model_node_SkillCatalogue.SkillCatalogue["id"] | undefined | null;
          lastModifiedBy?: everest_hr_skillmanagement_model_node_SkillCatalogue.SkillCatalogue["lastModifiedBy"] | undefined | null;
          lastModifiedDate?: everest_hr_skillmanagement_model_node_SkillCatalogue.SkillCatalogue["lastModifiedDate"] | undefined | null;
          skillName?: everest_hr_skillmanagement_model_node_SkillCatalogue.SkillCatalogue["skillName"] | undefined | null;
        };
        SkillAssignments: {
          approvalStatus?: everest_hr_skillmanagement_model_node_EmployeeSkillAssignment.EmployeeSkillAssignment["approvalStatus"] | undefined | null;
          employee?: everest_hr_skillmanagement_model_node_EmployeeSkillAssignment.EmployeeSkillAssignment["employeeId"] | undefined | null;
          id?: everest_hr_skillmanagement_model_node_EmployeeSkillAssignment.EmployeeSkillAssignment["id"] | undefined | null;
          skillLevel?: everest_hr_skillmanagement_model_node_EmployeeSkillAssignment.EmployeeSkillAssignment["skillLevel"] | undefined | null;
        };
      };

      export type instanceMetadata = {
        '': never;
        Page: {
          title?: FieldInstanceMetadata | undefined;
        };
        Skill: {
          categories?: FieldInstanceMetadata | undefined;
          createdBy?: FieldInstanceMetadata | undefined;
          createdDate?: FieldInstanceMetadata | undefined;
          description?: FieldInstanceMetadata | undefined;
          id?: FieldInstanceMetadata | undefined;
          lastModifiedBy?: FieldInstanceMetadata | undefined;
          lastModifiedDate?: FieldInstanceMetadata | undefined;
          skillName?: FieldInstanceMetadata | undefined;
        };
        SkillAssignments: {
          approvalStatus?: FieldInstanceMetadata | undefined;
          employee?: FieldInstanceMetadata | undefined;
          id?: FieldInstanceMetadata | undefined;
          skillLevel?: FieldInstanceMetadata | undefined;
        };
      };

      export type levelMetadata = {
        '': never;
        Page: {
          title?: FieldLevelMetadata | undefined;
        };
        Skill: {
          categories?: FieldLevelMetadata | undefined;
          createdBy?: FieldLevelMetadata | undefined;
          createdDate?: FieldLevelMetadata | undefined;
          description?: FieldLevelMetadata | undefined;
          id?: FieldLevelMetadata | undefined;
          lastModifiedBy?: FieldLevelMetadata | undefined;
          lastModifiedDate?: FieldLevelMetadata | undefined;
          skillName?: FieldLevelMetadata | undefined;
        };
        SkillAssignments: {
          approvalStatus?: FieldLevelMetadata | undefined;
          employee?: FieldLevelMetadata | undefined;
          id?: FieldLevelMetadata | undefined;
          skillLevel?: FieldLevelMetadata | undefined;
        };
      };

      export type levelConfigurations = {
        '': never;
        Page: never;
        Skill: never;
        SkillAssignments: never;
      };

      export type parameters = {
        id: everest_appserver_primitive_ID | undefined;
      };

      export namespace callbacks {
        export namespace setUp {
          export type input = {
            session: ISession;
            mode: mode;
            parameters: parameters;
          };

          export type output = void;
        }

        export namespace query {
          export type queryParameters = never;

          export type queryMetadata = {
            Page?: levelMetadata['Page'];
            Skill?: levelMetadata['Skill'];
            SkillAssignments?: levelMetadata['SkillAssignments'];
          };

          export type queryConfigurations = {
            Page?: levelConfigurations['Page'];
            Skill?: levelConfigurations['Skill'];
            SkillAssignments?: levelConfigurations['SkillAssignments'];
          };

          export type queryData = {
            Page?: levels['Page'] & {
              [METADATA]?: instanceMetadata['Page'] | undefined;
            };
            Skill?: levels['Skill'] & {
              [METADATA]?: instanceMetadata['Skill'] | undefined;
            };
            SkillAssignments?: (levels['SkillAssignments'] & {
              [IDENTIFIER]: number | string;
              [METADATA]?: instanceMetadata['SkillAssignments'] | undefined;
            })[];
          };

          export type input = {
            session: ISession;
            mode: mode;
            parameters: parameters;
            queryReason: QueryReason;
            queryInput: QueryInput;
            queryInstruction: {
              fields: ReadonlySet<keyof levels['']> | undefined;
              Page?: {
                fields: ReadonlySet<keyof levels['Page']> | undefined;
              };
              Skill?: {
                fields: ReadonlySet<keyof levels['Skill']> | undefined;
              };
              SkillAssignments?: {
                fields: ReadonlySet<keyof levels['SkillAssignments']> | undefined;
                filters: Record<keyof levels['SkillAssignments'], unknown> | undefined;
                orders: ReadonlyArray<{
                  field: keyof levels['SkillAssignments'];
                  ordering: 'asc' | 'desc';
                }> | undefined;
                page: {
                  skip: number;
                  take: number;
                } | undefined;
              };
            };
          };

          export type output = queryData;

          export type combinedOutput = queryData | {
            [DATA]: queryData;
            [METADATA]?: queryMetadata | undefined;
            [CONFIGURATIONS]?: queryConfigurations | undefined;
            [key: string]: never;
          };
        }

        export namespace update_Skill {
          export type input = {
            session: ISession;
            mode: mode;
            parameters: parameters;
            fieldName: keyof levels['Skill'];
            oldFieldValue: unknown;
            newFieldValue: unknown;
          };

          export type output = void;
        }

        export namespace create_SkillAssignments {
          export type input = {
            session: ISession;
            mode: mode;
            parameters: parameters;
            isRestore: boolean;
            data: levels['SkillAssignments'];
          };

          export type output = unknown;
        }

        export namespace delete_SkillAssignments {
          export type input = {
            session: ISession;
            mode: mode;
            parameters: parameters;
            path: [unknown];
            isRestorable: boolean;
          };

          export type output = void;
        }

        export namespace update_SkillAssignments {
          export type input = {
            session: ISession;
            mode: mode;
            parameters: parameters;
            path: [unknown];
            fieldName: keyof levels['SkillAssignments'];
            oldFieldValue: unknown;
            newFieldValue: unknown;
          };

          export type output = void;
        }
      }

      export namespace routines {
        export namespace reset {
          export type input = never;

          export type output = void;

          export type determineInput = {
            session: ISession;
            mode: mode;
            parameters: parameters;
          };

          export type determineOutput = RoutineDeterminationResult;

          export type validateInput = {
            session: ISession;
            mode: mode;
            parameters: parameters;
            validateReason: ValidateReason;
            context: RoutineValidationContext;
          };

          export type validateOutput = RoutineValidationResult;

          export type executeInput = {
            session: ISession;
            mode: mode;
            parameters: parameters;
          };

          export type executeOutput = output;
        }

        export namespace save {
          export type input = never;

          export type output = {
            result: everest_appserver_primitive_TrueFalse;
          };

          export type determineInput = {
            session: ISession;
            mode: mode;
            parameters: parameters;
          };

          export type determineOutput = RoutineDeterminationResult;

          export type validateInput = {
            session: ISession;
            mode: mode;
            parameters: parameters;
            validateReason: ValidateReason;
            context: RoutineValidationContext;
          };

          export type validateOutput = RoutineValidationResult;

          export type executeInput = {
            session: ISession;
            mode: mode;
            parameters: parameters;
          };

          export type executeOutput = output;
        }
      }

      export interface implementation {
        setUp?(input: callbacks.setUp.input): Promise<callbacks.setUp.output>;

        query(input: callbacks.query.input): Promise<callbacks.query.combinedOutput>;

        update_Skill(input: callbacks.update_Skill.input): Promise<callbacks.update_Skill.output>;

        create_SkillAssignments(input: callbacks.create_SkillAssignments.input): Promise<callbacks.create_SkillAssignments.output>;

        delete_SkillAssignments(input: callbacks.delete_SkillAssignments.input): Promise<callbacks.delete_SkillAssignments.output>;

        update_SkillAssignments(input: callbacks.update_SkillAssignments.input): Promise<callbacks.update_SkillAssignments.output>;

        determine_reset?(input: routines.reset.determineInput): Promise<routines.reset.determineOutput>;

        validate_reset?(input: routines.reset.validateInput): Promise<routines.reset.validateOutput>;

        execute_reset(input: routines.reset.executeInput): Promise<routines.reset.executeOutput>;

        determine_save?(input: routines.save.determineInput): Promise<routines.save.determineOutput>;

        validate_save?(input: routines.save.validateInput): Promise<routines.save.validateOutput>;

        execute_save(input: routines.save.executeInput): Promise<routines.save.executeOutput>;
      }
    }
  }

  export type implementation = {
    SkillDetails(): dataSources.SkillDetails.implementation;
  };
}
