/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

// ******************************************************************************
// Generated Everest Presentation Types
// ******************************************************************************

import type {
  ActionValidationResult,
  COMPONENT,
  CONFIGURATIONS,
  DATA,
  DynamicFieldsMetadata,
  GetElementType,
  DynamicFieldDefinition,
  DATA_SETS,
  DYNAMIC_FIELDS,
  FieldInstanceMetadata,
  FieldLevelMetadata,
  IDENTIFIER,
  ISession,
  METADATA,
  QueryInput,
  QueryReason,
  RoutineDeterminationResult,
  RoutineValidationContext,
  RoutineValidationResult,
  TableComponent,
  ValidateReason
} from '@everestsystems/content-core';

import type { EvstID as everest_appserver_primitive_ID } from "@pkg/everest.appserver/types/primitives/ID";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { SkillChangeRequest as everest_hr_skillmanagement_model_node_SkillChangeRequest } from "@pkg/everest.hr.skillmanagement/types/SkillChangeRequest";
import type { EmployeeSkillAssignment as everest_hr_skillmanagement_model_node_EmployeeSkillAssignment } from "@pkg/everest.hr.skillmanagement/types/EmployeeSkillAssignment";
import type { SkillCategory as everest_hr_skillmanagement_model_node_SkillCategory } from "@pkg/everest.hr.skillmanagement/types/SkillCategory";
import type { SkillCatalogue as everest_hr_skillmanagement_model_node_SkillCatalogue } from "@pkg/everest.hr.skillmanagement/types/SkillCatalogue";
import type { SkillCategoryMapping as everest_hr_skillmanagement_model_node_SkillCategoryMapping } from "@pkg/everest.hr.skillmanagement/types/SkillCategoryMapping";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";

export namespace skillManagementPresentation {
  export type mode = 'view' | 'edit';

  export namespace actions {
    export namespace approveSkill {
      export type input = {
        skillAssignmentId: everest_appserver_primitive_ID;
      };

      export type output = {
        result: everest_appserver_primitive_TrueFalse;
      };

      export type validateInput = {
        session: ISession;
        input: input;
      }

      export type executeInput = {
        session: ISession;
        input: input;
      }

      export type execute = (
        executeInput: executeInput
      ) => Promise<output>;

      export type validate = (
        validateInput: validateInput
      ) => Promise<ActionValidationResult>;

      export type implementation =
        | execute
        | {
            validate?: validate;
            execute: execute;
          };
    }

    export namespace approveSkillChangeRequest {
      export type input = {
        skillChangeRequestId: everest_appserver_primitive_ID;
      };

      export type output = {
        result: everest_appserver_primitive_TrueFalse;
      };

      export type validateInput = {
        session: ISession;
        input: input;
      }

      export type executeInput = {
        session: ISession;
        input: input;
      }

      export type execute = (
        executeInput: executeInput
      ) => Promise<output>;

      export type validate = (
        validateInput: validateInput
      ) => Promise<ActionValidationResult>;

      export type implementation =
        | execute
        | {
            validate?: validate;
            execute: execute;
          };
    }

    export namespace bulkApproveSkillChangeRequests {
      export type input = {
        skillChangeRequestIds: everest_hr_skillmanagement_model_node_SkillChangeRequest.SkillChangeRequestWithAssociation['id'][];
      };

      export type output = {
        result: everest_appserver_primitive_TrueFalse;
      };

      export type validateInput = {
        session: ISession;
        input: input;
      }

      export type executeInput = {
        session: ISession;
        input: input;
      }

      export type execute = (
        executeInput: executeInput
      ) => Promise<output>;

      export type validate = (
        validateInput: validateInput
      ) => Promise<ActionValidationResult>;

      export type implementation =
        | execute
        | {
            validate?: validate;
            execute: execute;
          };
    }

    export namespace bulkApproveSkills {
      export type input = {
        skillAssignmentIds: everest_hr_skillmanagement_model_node_EmployeeSkillAssignment.EmployeeSkillAssignmentWithAssociation['id'][];
      };

      export type output = {
        result: everest_appserver_primitive_TrueFalse;
      };

      export type validateInput = {
        session: ISession;
        input: input;
      }

      export type executeInput = {
        session: ISession;
        input: input;
      }

      export type execute = (
        executeInput: executeInput
      ) => Promise<output>;

      export type validate = (
        validateInput: validateInput
      ) => Promise<ActionValidationResult>;

      export type implementation =
        | execute
        | {
            validate?: validate;
            execute: execute;
          };
    }

    export namespace bulkRejectSkillChangeRequests {
      export type input = {
        skillChangeRequestIds: everest_hr_skillmanagement_model_node_SkillChangeRequest.SkillChangeRequestWithAssociation['id'][];
      };

      export type output = {
        result: everest_appserver_primitive_TrueFalse;
      };

      export type validateInput = {
        session: ISession;
        input: input;
      }

      export type executeInput = {
        session: ISession;
        input: input;
      }

      export type execute = (
        executeInput: executeInput
      ) => Promise<output>;

      export type validate = (
        validateInput: validateInput
      ) => Promise<ActionValidationResult>;

      export type implementation =
        | execute
        | {
            validate?: validate;
            execute: execute;
          };
    }

    export namespace bulkRejectSkills {
      export type input = {
        skillAssignmentIds: everest_hr_skillmanagement_model_node_EmployeeSkillAssignment.EmployeeSkillAssignmentWithAssociation['id'][];
      };

      export type output = {
        result: everest_appserver_primitive_TrueFalse;
      };

      export type validateInput = {
        session: ISession;
        input: input;
      }

      export type executeInput = {
        session: ISession;
        input: input;
      }

      export type execute = (
        executeInput: executeInput
      ) => Promise<output>;

      export type validate = (
        validateInput: validateInput
      ) => Promise<ActionValidationResult>;

      export type implementation =
        | execute
        | {
            validate?: validate;
            execute: execute;
          };
    }

    export namespace deleteCategories {
      export type input = {
        categoryIds: everest_hr_skillmanagement_model_node_SkillCategory.SkillCategory["id"][];
      };

      export type output = void;

      export type validateInput = {
        session: ISession;
        input: input;
      }

      export type executeInput = {
        session: ISession;
        input: input;
      }

      export type execute = (
        executeInput: executeInput
      ) => Promise<output>;

      export type validate = (
        validateInput: validateInput
      ) => Promise<ActionValidationResult>;

      export type implementation =
        | execute
        | {
            validate?: validate;
            execute: execute;
          };
    }

    export namespace deleteEmployeeSkills {
      export type input = {
        skillAssignmentIds: everest_hr_skillmanagement_model_node_EmployeeSkillAssignment.EmployeeSkillAssignmentWithAssociation['id'][];
      };

      export type output = void;

      export type validateInput = {
        session: ISession;
        input: input;
      }

      export type executeInput = {
        session: ISession;
        input: input;
      }

      export type execute = (
        executeInput: executeInput
      ) => Promise<output>;

      export type validate = (
        validateInput: validateInput
      ) => Promise<ActionValidationResult>;

      export type implementation =
        | execute
        | {
            validate?: validate;
            execute: execute;
          };
    }

    export namespace deleteSkills {
      export type input = {
        skillIds: everest_hr_skillmanagement_model_node_SkillCatalogue.SkillCatalogue["id"][];
      };

      export type output = void;

      export type validateInput = {
        session: ISession;
        input: input;
      }

      export type executeInput = {
        session: ISession;
        input: input;
      }

      export type execute = (
        executeInput: executeInput
      ) => Promise<output>;

      export type validate = (
        validateInput: validateInput
      ) => Promise<ActionValidationResult>;

      export type implementation =
        | execute
        | {
            validate?: validate;
            execute: execute;
          };
    }

    export namespace rejectSkill {
      export type input = {
        skillAssignmentId: everest_appserver_primitive_ID;
      };

      export type output = {
        result: everest_appserver_primitive_TrueFalse;
      };

      export type validateInput = {
        session: ISession;
        input: input;
      }

      export type executeInput = {
        session: ISession;
        input: input;
      }

      export type execute = (
        executeInput: executeInput
      ) => Promise<output>;

      export type validate = (
        validateInput: validateInput
      ) => Promise<ActionValidationResult>;

      export type implementation =
        | execute
        | {
            validate?: validate;
            execute: execute;
          };
    }

    export namespace rejectSkillChangeRequest {
      export type input = {
        skillChangeRequestId: everest_appserver_primitive_ID;
      };

      export type output = {
        result: everest_appserver_primitive_TrueFalse;
      };

      export type validateInput = {
        session: ISession;
        input: input;
      }

      export type executeInput = {
        session: ISession;
        input: input;
      }

      export type execute = (
        executeInput: executeInput
      ) => Promise<output>;

      export type validate = (
        validateInput: validateInput
      ) => Promise<ActionValidationResult>;

      export type implementation =
        | execute
        | {
            validate?: validate;
            execute: execute;
          };
    }
  }

  export namespace dataSources {
    export namespace CategoriesWithSkills {
      export type levels = {
        '': {
          categoryName?: everest_hr_skillmanagement_model_node_SkillCategory.SkillCategory["categoryName"] | undefined | null;
          id?: everest_hr_skillmanagement_model_node_SkillCategory.SkillCategory["id"] | undefined | null;
          skills?: everest_hr_skillmanagement_model_node_SkillCategoryMapping.SkillCategoryMapping["skillId"][] | undefined | null;
        };
      };

      export type instanceMetadata = {
        '': {
          categoryName?: FieldInstanceMetadata | undefined;
          id?: FieldInstanceMetadata | undefined;
          skills?: FieldInstanceMetadata | undefined;
        };
      };

      export type levelMetadata = {
        '': {
          categoryName?: FieldLevelMetadata | undefined;
          id?: FieldLevelMetadata | undefined;
          skills?: FieldLevelMetadata | undefined;
        };
      };

      export type levelConfigurations = {
        '': {
          [DATA_SETS]?: {        
            CategoriesWithSkillsTable?: {
              [COMPONENT]?: TableComponent.ComponentConfiguration | undefined;
              categoryName?: TableComponent.FieldConfiguration | undefined;
              id?: TableComponent.FieldConfiguration | undefined;
              skills?: TableComponent.FieldConfiguration | undefined;
            };
          };
        };
      };

      export type parameters = never;

      export namespace callbacks {
        export namespace setUp {
          export type input = {
            session: ISession;
            mode: mode;
          };

          export type output = void;
        }

        export namespace query {
          export type queryParameters = never;

          export type queryMetadata = levelMetadata[''];

          export type queryConfigurations = levelConfigurations[''];

          export type queryData = (levels[''] & {
            [IDENTIFIER]?: number | string;
            [METADATA]?: instanceMetadata[''] | undefined;
          })[];

          export type input = {
            session: ISession;
            mode: mode;
            queryReason: QueryReason;
            queryInput: QueryInput;
            queryInstruction: {
              fields: ReadonlySet<keyof levels['']> | undefined;
              filters: Record<keyof levels[''], unknown> | undefined;
              orders: ReadonlyArray<{
                field: keyof levels[''];
                ordering: 'asc' | 'desc';
              }> | undefined;
              page: {
                skip: number;
                take: number;
              } | undefined;
            };
          };

          export type output = queryData;

          export type combinedOutput = queryData | {
            [DATA]: queryData;
            [METADATA]?: queryMetadata | undefined;
            [CONFIGURATIONS]?: queryConfigurations | undefined;
            [key: string]: never;
          };
        }
      }

      export interface implementation {
        setUp?(input: callbacks.setUp.input): Promise<callbacks.setUp.output>;

        query(input: callbacks.query.input): Promise<callbacks.query.combinedOutput>;
      }
    }

    export namespace SkillsWithCategories {
      export type levels = {
        '': {
          categories?: everest_hr_skillmanagement_model_node_SkillCategoryMapping.SkillCategoryMapping["categoryId"][] | undefined | null;
          description?: everest_hr_skillmanagement_model_node_SkillCatalogue.SkillCatalogue["description"] | undefined | null;
          id?: everest_hr_skillmanagement_model_node_SkillCatalogue.SkillCatalogue["id"] | undefined | null;
          skillName?: everest_hr_skillmanagement_model_node_SkillCatalogue.SkillCatalogue["skillName"] | undefined | null;
          status?: everest_hr_skillmanagement_model_node_SkillCatalogue.SkillCatalogue["status"] | undefined | null;
        };
      };

      export type instanceMetadata = {
        '': {
          categories?: FieldInstanceMetadata | undefined;
          description?: FieldInstanceMetadata | undefined;
          id?: FieldInstanceMetadata | undefined;
          skillName?: FieldInstanceMetadata | undefined;
          status?: FieldInstanceMetadata | undefined;
        };
      };

      export type levelMetadata = {
        '': {
          categories?: FieldLevelMetadata | undefined;
          description?: FieldLevelMetadata | undefined;
          id?: FieldLevelMetadata | undefined;
          skillName?: FieldLevelMetadata | undefined;
          status?: FieldLevelMetadata | undefined;
        };
      };

      export type levelConfigurations = {
        '': {
          [DATA_SETS]?: {        
            SkillsWithCategoriesTable?: {
              [COMPONENT]?: TableComponent.ComponentConfiguration | undefined;
              categories?: TableComponent.FieldConfiguration | undefined;
              description?: TableComponent.FieldConfiguration | undefined;
              id?: TableComponent.FieldConfiguration | undefined;
              skillName?: TableComponent.FieldConfiguration | undefined;
              status?: TableComponent.FieldConfiguration | undefined;
            };
          };
        };
      };

      export type parameters = never;

      export namespace callbacks {
        export namespace setUp {
          export type input = {
            session: ISession;
            mode: mode;
          };

          export type output = void;
        }

        export namespace query {
          export type queryParameters = never;

          export type queryMetadata = levelMetadata[''];

          export type queryConfigurations = levelConfigurations[''];

          export type queryData = (levels[''] & {
            [IDENTIFIER]?: number | string;
            [METADATA]?: instanceMetadata[''] | undefined;
          })[];

          export type input = {
            session: ISession;
            mode: mode;
            queryReason: QueryReason;
            queryInput: QueryInput;
            queryInstruction: {
              fields: ReadonlySet<keyof levels['']> | undefined;
              filters: Record<keyof levels[''], unknown> | undefined;
              orders: ReadonlyArray<{
                field: keyof levels[''];
                ordering: 'asc' | 'desc';
              }> | undefined;
              page: {
                skip: number;
                take: number;
              } | undefined;
            };
          };

          export type output = queryData;

          export type combinedOutput = queryData | {
            [DATA]: queryData;
            [METADATA]?: queryMetadata | undefined;
            [CONFIGURATIONS]?: queryConfigurations | undefined;
            [key: string]: never;
          };
        }
      }

      export interface implementation {
        setUp?(input: callbacks.setUp.input): Promise<callbacks.setUp.output>;

        query(input: callbacks.query.input): Promise<callbacks.query.combinedOutput>;
      }
    }

    export namespace Summary {
      export type levels = {
        '': {
          pendingCatalogUpdateApprovals?: everest_appserver_primitive_Number | undefined | null;
          pendingEmployeeSkillAssignments?: everest_appserver_primitive_Number | undefined | null;
          totalAssignments?: everest_appserver_primitive_Number | undefined | null;
          totalCategories?: everest_appserver_primitive_Number | undefined | null;
          totalSkills?: everest_appserver_primitive_Number | undefined | null;
        };
      };

      export type instanceMetadata = {
        '': {
          pendingCatalogUpdateApprovals?: FieldInstanceMetadata | undefined;
          pendingEmployeeSkillAssignments?: FieldInstanceMetadata | undefined;
          totalAssignments?: FieldInstanceMetadata | undefined;
          totalCategories?: FieldInstanceMetadata | undefined;
          totalSkills?: FieldInstanceMetadata | undefined;
        };
      };

      export type levelMetadata = {
        '': {
          pendingCatalogUpdateApprovals?: FieldLevelMetadata | undefined;
          pendingEmployeeSkillAssignments?: FieldLevelMetadata | undefined;
          totalAssignments?: FieldLevelMetadata | undefined;
          totalCategories?: FieldLevelMetadata | undefined;
          totalSkills?: FieldLevelMetadata | undefined;
        };
      };

      export type levelConfigurations = {
        '': never;
      };

      export type parameters = never;

      export namespace callbacks {
        export namespace setUp {
          export type input = {
            session: ISession;
            mode: mode;
          };

          export type output = void;
        }

        export namespace query {
          export type queryParameters = never;

          export type queryMetadata = levelMetadata[''];

          export type queryConfigurations = levelConfigurations[''];

          export type queryData = levels[''] & {
            [METADATA]?: instanceMetadata[''] | undefined;
          };

          export type input = {
            session: ISession;
            mode: mode;
            queryReason: QueryReason;
            queryInput: QueryInput;
            queryInstruction: {
              fields: ReadonlySet<keyof levels['']> | undefined;
            };
          };

          export type output = queryData;

          export type combinedOutput = queryData | {
            [DATA]: queryData;
            [METADATA]?: queryMetadata | undefined;
            [CONFIGURATIONS]?: queryConfigurations | undefined;
            [key: string]: never;
          };
        }
      }

      export interface implementation {
        setUp?(input: callbacks.setUp.input): Promise<callbacks.setUp.output>;

        query(input: callbacks.query.input): Promise<callbacks.query.combinedOutput>;
      }
    }
  }

  export type implementation = {
    approveSkill: actions.approveSkill.implementation;

    approveSkillChangeRequest: actions.approveSkillChangeRequest.implementation;

    bulkApproveSkillChangeRequests: actions.bulkApproveSkillChangeRequests.implementation;

    bulkApproveSkills: actions.bulkApproveSkills.implementation;

    bulkRejectSkillChangeRequests: actions.bulkRejectSkillChangeRequests.implementation;

    bulkRejectSkills: actions.bulkRejectSkills.implementation;

    CategoriesWithSkills(): dataSources.CategoriesWithSkills.implementation;

    deleteCategories: actions.deleteCategories.implementation;

    deleteEmployeeSkills: actions.deleteEmployeeSkills.implementation;

    deleteSkills: actions.deleteSkills.implementation;

    rejectSkill: actions.rejectSkill.implementation;

    rejectSkillChangeRequest: actions.rejectSkillChangeRequest.implementation;

    SkillsWithCategories(): dataSources.SkillsWithCategories.implementation;

    Summary(): dataSources.Summary.implementation;
  };
}
