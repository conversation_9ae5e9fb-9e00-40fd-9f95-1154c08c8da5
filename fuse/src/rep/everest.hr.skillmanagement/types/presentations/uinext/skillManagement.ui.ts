/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

// ******************************************************************************
// Generated Everest Presentation UI Types
// ******************************************************************************

import type {
  GetElementType,
  UIExecutionContext
} from '@everestsystems/content-core';

import type { SkillCategory as everest_hr_skillmanagement_model_node_SkillCategory } from "@pkg/everest.hr.skillmanagement/types/SkillCategory";
import type { SkillCategoryMapping as everest_hr_skillmanagement_model_node_SkillCategoryMapping } from "@pkg/everest.hr.skillmanagement/types/SkillCategoryMapping";
import type { EmployeeSkillAssignment as everest_hr_skillmanagement_model_node_EmployeeSkillAssignment } from "@pkg/everest.hr.skillmanagement/types/EmployeeSkillAssignment";
import type { SkillChangeRequest as everest_hr_skillmanagement_model_node_SkillChangeRequest } from "@pkg/everest.hr.skillmanagement/types/SkillChangeRequest";
import type { SkillCatalogue as everest_hr_skillmanagement_model_node_SkillCatalogue } from "@pkg/everest.hr.skillmanagement/types/SkillCatalogue";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { EvstID as everest_appserver_primitive_ID } from "@pkg/everest.appserver/types/primitives/ID";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";

export namespace skillManagementPresentationUI {
  export namespace dataSets {
    export namespace CategoriesWithSkillsTable {
      export type instance = {
        categoryName?: everest_hr_skillmanagement_model_node_SkillCategory.SkillCategory["categoryName"] | undefined | null;
        id?: everest_hr_skillmanagement_model_node_SkillCategory.SkillCategory["id"] | undefined | null;
        skills?: everest_hr_skillmanagement_model_node_SkillCategoryMapping.SkillCategoryMapping["skillId"][] | undefined | null;
        /** Unique UI identifier of the node instance */
        _nodeReference: string;
      };

      export type data = instance[];
    }

    export namespace EmployeeSkillsTable {
      export type instance = {
        approvalStatus?: everest_hr_skillmanagement_model_node_EmployeeSkillAssignment.EmployeeSkillAssignmentWithAssociation['approvalStatus'] | undefined | null;
        employeeId?: everest_hr_skillmanagement_model_node_EmployeeSkillAssignment.EmployeeSkillAssignmentWithAssociation['employeeId'] | undefined | null;
        id?: everest_hr_skillmanagement_model_node_EmployeeSkillAssignment.EmployeeSkillAssignmentWithAssociation['id'] | undefined | null;
        skillId?: everest_hr_skillmanagement_model_node_EmployeeSkillAssignment.EmployeeSkillAssignmentWithAssociation['skillId'] | undefined | null;
        skillLevel?: everest_hr_skillmanagement_model_node_EmployeeSkillAssignment.EmployeeSkillAssignmentWithAssociation['skillLevel'] | undefined | null;
        /** Unique UI identifier of the node instance */
        _nodeReference: string;
      };

      export type data = instance[];
    }

    export namespace PendingApprovalsTable {
      export type instance = {
        approvalStatus?: everest_hr_skillmanagement_model_node_EmployeeSkillAssignment.EmployeeSkillAssignmentWithAssociation['approvalStatus'] | undefined | null;
        employeeId?: everest_hr_skillmanagement_model_node_EmployeeSkillAssignment.EmployeeSkillAssignmentWithAssociation['employeeId'] | undefined | null;
        id?: everest_hr_skillmanagement_model_node_EmployeeSkillAssignment.EmployeeSkillAssignmentWithAssociation['id'] | undefined | null;
        skillId?: everest_hr_skillmanagement_model_node_EmployeeSkillAssignment.EmployeeSkillAssignmentWithAssociation['skillId'] | undefined | null;
        skillLevel?: everest_hr_skillmanagement_model_node_EmployeeSkillAssignment.EmployeeSkillAssignmentWithAssociation['skillLevel'] | undefined | null;
        /** Unique UI identifier of the node instance */
        _nodeReference: string;
      };

      export type data = instance[];
    }

    export namespace SkillChangeRequestsTable {
      export type instance = {
        id?: everest_hr_skillmanagement_model_node_SkillChangeRequest.SkillChangeRequestWithAssociation['id'] | undefined | null;
        justification?: everest_hr_skillmanagement_model_node_SkillChangeRequest.SkillChangeRequestWithAssociation['justification'] | undefined | null;
        proposedSkillName?: everest_hr_skillmanagement_model_node_SkillChangeRequest.SkillChangeRequestWithAssociation['proposedSkillName'] | undefined | null;
        requesterId?: everest_hr_skillmanagement_model_node_SkillChangeRequest.SkillChangeRequestWithAssociation['requesterId'] | undefined | null;
        requestType?: everest_hr_skillmanagement_model_node_SkillChangeRequest.SkillChangeRequestWithAssociation['requestType'] | undefined | null;
        reviewNotes?: everest_hr_skillmanagement_model_node_SkillChangeRequest.SkillChangeRequestWithAssociation['reviewNotes'] | undefined | null;
        skillId?: everest_hr_skillmanagement_model_node_SkillChangeRequest.SkillChangeRequestWithAssociation['skillId'] | undefined | null;
        status?: everest_hr_skillmanagement_model_node_SkillChangeRequest.SkillChangeRequestWithAssociation['status'] | undefined | null;
        submissionDate?: everest_hr_skillmanagement_model_node_SkillChangeRequest.SkillChangeRequestWithAssociation['submissionDate'] | undefined | null;
        /** Unique UI identifier of the node instance */
        _nodeReference: string;
      };

      export type data = instance[];
    }

    export namespace SkillsWithCategoriesTable {
      export type instance = {
        categories?: everest_hr_skillmanagement_model_node_SkillCategoryMapping.SkillCategoryMapping["categoryId"][] | undefined | null;
        description?: everest_hr_skillmanagement_model_node_SkillCatalogue.SkillCatalogue["description"] | undefined | null;
        id?: everest_hr_skillmanagement_model_node_SkillCatalogue.SkillCatalogue["id"] | undefined | null;
        skillName?: everest_hr_skillmanagement_model_node_SkillCatalogue.SkillCatalogue["skillName"] | undefined | null;
        status?: everest_hr_skillmanagement_model_node_SkillCatalogue.SkillCatalogue["status"] | undefined | null;
        /** Unique UI identifier of the node instance */
        _nodeReference: string;
      };

      export type data = instance[];
    }

    export namespace Summary {
      export type instance = {
        pendingCatalogUpdateApprovals?: everest_appserver_primitive_Number | undefined | null;
        pendingEmployeeSkillAssignments?: everest_appserver_primitive_Number | undefined | null;
        totalAssignments?: everest_appserver_primitive_Number | undefined | null;
        totalCategories?: everest_appserver_primitive_Number | undefined | null;
        totalSkills?: everest_appserver_primitive_Number | undefined | null;
        /** Unique UI identifier of the node instance */
        _nodeReference: string;
      };

      export type data = instance;
    }
  }

  export namespace actions {
    export namespace approveSkill {
      export type input = {
        skillAssignmentId: everest_appserver_primitive_ID;
      };
      export type output = {
        result: everest_appserver_primitive_TrueFalse;
      };
    }

    export namespace approveSkillChangeRequest {
      export type input = {
        skillChangeRequestId: everest_appserver_primitive_ID;
      };
      export type output = {
        result: everest_appserver_primitive_TrueFalse;
      };
    }

    export namespace bulkApproveSkillChangeRequests {
      export type input = never;
      export type output = {
        result: everest_appserver_primitive_TrueFalse;
      };
    }

    export namespace bulkApproveSkills {
      export type input = never;
      export type output = {
        result: everest_appserver_primitive_TrueFalse;
      };
    }

    export namespace bulkRejectSkillChangeRequests {
      export type input = never;
      export type output = {
        result: everest_appserver_primitive_TrueFalse;
      };
    }

    export namespace bulkRejectSkills {
      export type input = never;
      export type output = {
        result: everest_appserver_primitive_TrueFalse;
      };
    }

    export namespace cancel {
      export type input = never;
      export type output = void;
    }

    export namespace deleteCategories {
      export type input = never;
      export type output = void;
    }

    export namespace deleteEmployeeSkills {
      export type input = never;
      export type output = void;
    }

    export namespace deleteSkills {
      export type input = never;
      export type output = void;
    }

    export namespace rejectSkill {
      export type input = {
        skillAssignmentId: everest_appserver_primitive_ID;
      };
      export type output = {
        result: everest_appserver_primitive_TrueFalse;
      };
    }

    export namespace rejectSkillChangeRequest {
      export type input = {
        skillChangeRequestId: everest_appserver_primitive_ID;
      };
      export type output = {
        result: everest_appserver_primitive_TrueFalse;
      };
    }

    export namespace revise {
      export type input = never;
      export type output = void;
    }

    export namespace save {
      export type input = never;
      export type output = void;
    }
  }

  export type context = UIExecutionContext.BasicExecutionContext & {
    data: {
      CategoriesWithSkillsTable: dataSets.CategoriesWithSkillsTable.data;
      EmployeeSkillsTable: dataSets.EmployeeSkillsTable.data;
      PendingApprovalsTable: dataSets.PendingApprovalsTable.data;
      SkillChangeRequestsTable: dataSets.SkillChangeRequestsTable.data;
      SkillsWithCategoriesTable: dataSets.SkillsWithCategoriesTable.data;
      Summary: dataSets.Summary.data;
    };
    state: Record<string, unknown>;
    presentationClient: {
      approveSkill: (input: actions.approveSkill.input) => Promise<actions.approveSkill.output>;
      approveSkillChangeRequest: (input: actions.approveSkillChangeRequest.input) => Promise<actions.approveSkillChangeRequest.output>;
      bulkApproveSkillChangeRequests: () => Promise<actions.bulkApproveSkillChangeRequests.output>;
      bulkApproveSkills: () => Promise<actions.bulkApproveSkills.output>;
      bulkRejectSkillChangeRequests: () => Promise<actions.bulkRejectSkillChangeRequests.output>;
      bulkRejectSkills: () => Promise<actions.bulkRejectSkills.output>;
      cancel: () => Promise<actions.cancel.output>;
      deleteCategories: () => Promise<actions.deleteCategories.output>;
      deleteEmployeeSkills: () => Promise<actions.deleteEmployeeSkills.output>;
      deleteSkills: () => Promise<actions.deleteSkills.output>;
      rejectSkill: (input: actions.rejectSkill.input) => Promise<actions.rejectSkill.output>;
      rejectSkillChangeRequest: (input: actions.rejectSkillChangeRequest.input) => Promise<actions.rejectSkillChangeRequest.output>;
      revise: () => Promise<actions.revise.output>;
      save: () => Promise<actions.save.output>;
    };
  }
}
