/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// ******************************************************************************
// Generated Everest Types
// ******************************************************************************

import type { Decimal, RoundingMode } from '@everestsystems/decimal';
import type {
  ArgType,
  Association,
  AssociationManagementArgs,
  AsyncResult,
  Controller,
  ControllerClientProvider,
  CreateOptionsArg,
  DataWithMetadata,
  DeleteOptionsArg,
  FieldSelector,
  Filter,
  flattenFieldSelector,
  GraphInstanceEntry,
  KernelRequestOptions,
  NonAssociationFields,
  Optional,
  PurgeArgs,
  ReadOptionsArg,
  ReadReturnType as ReadReturnTypeGeneric,
  ReadReturnTypeWithAssociations,
  RequireAtLeastOne,
  SemanticSearchArg,
  SemanticSearchRequestOptions,
  TypeSafeQueryArgType,
  UpdateManyOptionsArg,
  UpdateOptionsArg,
  UpsertOptionsArg,
  WriteReturnType,
  WriteReturnTypeWithAssociations,
} from '@everestsystems/content-core';

import { UIExecutionContext } from '@everestsystems/content-core';
import type { SkillCategoryMapping as everest_hr_skillmanagement_model_node_SkillCategoryMapping } from "@pkg/everest.hr.skillmanagement/types/SkillCategoryMapping";
import type { EmployeeSkillAssignment as everest_hr_skillmanagement_model_node_EmployeeSkillAssignment } from "@pkg/everest.hr.skillmanagement/types/EmployeeSkillAssignment";
import type { SkillRequirement as everest_hr_skillmanagement_model_node_SkillRequirement } from "@pkg/everest.hr.skillmanagement/types/SkillRequirement";
import type { SkillCatalogue as everest_hr_skillmanagement_model_node_SkillCatalogue } from "@pkg/everest.hr.skillmanagement/types/SkillCatalogue";
import type orig_sampleDataSkillManagement from "@pkg/everest.hr.skillmanagement/actions/sampleDataSkillManagement.action";

/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */

type RequiredContext = {
  data: Record<string, any>;
  actions: {
    run: (...args: any[]) => any;
  };
};

type ApiError = {
  message: string;
  name: string;
  errorCode: string;
  stack?: string;
};

type InnerPromiseType<T> = T extends Promise<infer R> ? R : T;

type ActionResultOrError<T1 extends string | number | symbol, T2> = {[P in T1]?: T2} & {error?: ApiError};

type ArrayIfNeeded<C extends RequiredContext, K extends keyof C['data'], T> = C['data'][K] extends UIExecutionContext.UIModelNodeListType<any> ? T[] : T;

export namespace SkillCatalogueUI {
  /**
   * This model is for defining list of skills
   */
  export type SkillCatalogueWithAssociation = SkillCatalogue & {
    ["SkillCategoryMapping-SkillCatalogue"]?: Association<everest_hr_skillmanagement_model_node_SkillCategoryMapping.SkillCategoryMappingWithAssociation>[];
    ["Mappings"]?: Association<everest_hr_skillmanagement_model_node_SkillCategoryMapping.SkillCategoryMappingWithAssociation>[];
    ["SkillAssignment-SkillCatalogue"]?: Association<everest_hr_skillmanagement_model_node_EmployeeSkillAssignment.EmployeeSkillAssignmentWithAssociation>[];
    ["Assignments"]?: Association<everest_hr_skillmanagement_model_node_EmployeeSkillAssignment.EmployeeSkillAssignmentWithAssociation>[];
    ["SkillRequirement-SkillCatalogue"]?: Association<everest_hr_skillmanagement_model_node_SkillRequirement.SkillRequirementWithAssociation>[];
    };
  export interface IControllerClient extends everest_hr_skillmanagement_model_node_SkillCatalogue.IControllerClient {}

  export type SkillCatalogue = everest_hr_skillmanagement_model_node_SkillCatalogue.SkillCatalogue;
  export type CreationFields = Pick<SkillCatalogue, 'uuid' | 'externalId' | 'active' | 'description' | 'skillName' | 'status'>;
  export type UniqueFields = Pick<SkillCatalogue, 'id' | 'uuid' | 'skillName'>;
  export type UniqueWhereInput = (RequireAtLeastOne<UniqueFields>) & Partial<SkillCatalogue>;
  export type ReadReturnType<U extends string | number | symbol = keyof SkillCatalogue> = ReadReturnTypeGeneric<SkillCatalogue, U>;

  class ActionRequest<C extends RequiredContext, FuncReturnType> {
    constructor(private readonly context: C, private readonly partialPayload: Record<string, any>) {
    }

    private async __run__(payload: any) {
      return this.context.actions.run(payload);
    }

    payload<K extends keyof C['data']>(nodeAlias: K): Record<string, any> {
      return {[nodeAlias]: this.partialPayload};
    }

    run<K extends keyof C['data']>(nodeAlias: K): Promise<ActionResultOrError<K, ArrayIfNeeded<C, K, InnerPromiseType<FuncReturnType>>>> {
      const payload = this.payload(nodeAlias);
      return this.__run__(payload);
    }
  }

  type func_sampleDataSkillManagement = typeof orig_sampleDataSkillManagement;
  /** @deprecated use ```client.sampleDataSkillManagement``` instead */
  export function sampleDataSkillManagement<C extends RequiredContext>(context: C): ActionRequest<C, ReturnType<func_sampleDataSkillManagement>> {
    const partialPayload = {action: 'sampleDataSkillManagement', data: {}}

    return new ActionRequest<C, ReturnType<func_sampleDataSkillManagement>>(context, partialPayload);
  }
  /** @deprecated - use {@link MODEL_URN} instead */
  export const name = 'urn:evst:everest:hr/skillmanagement:model/node:SkillCatalogue';
  export const MODEL_URN = 'urn:evst:everest:hr/skillmanagement:model/node:SkillCatalogue';
  /** @deprecated - use {@link MODEL_URN} instead */
  export const MODEL_ID = 'everest.hr.skillmanagement/SkillCatalogueModel.SkillCatalogue';
  export const MODEL_UUID = 'bc4fc095-1578-4b45-922e-fea95d267802';

  /** @deprecated use ```client``` instead */
  export function create<C extends RequiredContext, U extends keyof SkillCatalogue>(context: C, input: CreationFields, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<SkillCatalogue, U>>> {
    const convertedArgs = {data: input, fieldList: fieldlist, options: options} as Record<string, any>
    const partialPayload = {action: 'create', ...convertedArgs}

    return new ActionRequest<C, Promise<WriteReturnType<SkillCatalogue, U>>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function createMany<C extends RequiredContext, U extends keyof SkillCatalogue>(context: C, input: CreationFields[], fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<SkillCatalogue, U>[]>> {
    const convertedArgs = {data: input, fieldList: fieldlist, options: options} as Record<string, any>
    const partialPayload = {action: 'createMany', ...convertedArgs}

    return new ActionRequest<C, Promise<WriteReturnType<SkillCatalogue, U>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function Delete<C extends RequiredContext>(context: C, where: UniqueWhereInput, options?: undefined): ActionRequest<C, Promise<Partial<SkillCatalogue>[]>> {
    const convertedArgs = {where: where, options} as Record<string, any>
    const partialPayload = {action: 'delete', ...convertedArgs}

    return new ActionRequest<C, Promise<Partial<SkillCatalogue>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function deleteAll<C extends RequiredContext>(context: C): ActionRequest<C, Promise<Partial<SkillCatalogue>[]>> {
    const convertedArgs = {data: {}} as Record<string, any>
    const partialPayload = {action: 'deleteAll', ...convertedArgs}

    return new ActionRequest<C, Promise<Partial<SkillCatalogue>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function deleteMany<C extends RequiredContext>(context: C, where: Filter<SkillCatalogueWithAssociation>): ActionRequest<C, Promise<Partial<SkillCatalogue>[]>>;
  /** @deprecated use ```client``` instead */
  export function deleteMany<C extends RequiredContext>(context: C, where: Partial<SkillCatalogue>): ActionRequest<C, Promise<Partial<SkillCatalogue>[]>>;
  /** @deprecated use ```client``` instead */
  export function deleteMany<C extends RequiredContext>(context: C, where: Filter<SkillCatalogue> | Partial<SkillCatalogue>, options?: undefined): ActionRequest<C, Promise<Partial<SkillCatalogue>[]>> {
    const convertedArgs = {data: {where: where}, options} as Record<string, any>
    const partialPayload = {action: 'deleteMany', ...convertedArgs}

    return new ActionRequest<C, Promise<Partial<SkillCatalogue>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function query<C extends RequiredContext, U extends FieldSelector<SkillCatalogueWithAssociation>>(context: C, args: Omit<TypeSafeQueryArgType<SkillCatalogueWithAssociation>, 'draft'>, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<ReadReturnTypeWithAssociations<SkillCatalogueWithAssociation, U>[]>>;
  /** @deprecated use ```client``` instead */
  export function query<C extends RequiredContext, U extends keyof SkillCatalogue, V extends string = 'ALL_FIELDS'>(context: C, args: Omit<TypeSafeQueryArgType<SkillCatalogue>, 'draft'> | Omit<Omit<TypeSafeQueryArgType<SkillCatalogue>, 'draft'>, 'where'> & { where?: Partial<SkillCatalogue> }, fieldlist: ReadonlyArray<U | V> | 'ALL_FIELDS'): ActionRequest<C, Promise<ReadReturnType<V>[]>>;
  /** @deprecated use ```client``` instead */
  export function query<C extends RequiredContext>(context: C, args: Omit<TypeSafeQueryArgType<SkillCatalogue>, 'draft'>, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS'): ActionRequest<C, Promise<unknown[]>> {
    const convertedArgs = {data: {...args, fieldList: fieldlist}} as Record<string, any>
    const partialPayload = {action: 'query', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function read<C extends RequiredContext, U extends FieldSelector<SkillCatalogueWithAssociation>>(context: C, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): ActionRequest<C, Promise<ReadReturnTypeWithAssociations<SkillCatalogueWithAssociation, U>>>;
  /** @deprecated use ```client``` instead */
  export function read<C extends RequiredContext, U extends keyof SkillCatalogue>(context: C, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): ActionRequest<C, Promise<ReadReturnType<U>>>;
  /** @deprecated use ```client``` instead */
  export function read<C extends RequiredContext>(context: C, where: UniqueWhereInput, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): ActionRequest<C, Promise<unknown>> {
    const convertedArgs = {data: {where: where, fieldList: fieldlist}, options: options} as Record<string, any>
    const partialPayload = {action: 'read', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function update<C extends RequiredContext, U extends FieldSelector<SkillCatalogueWithAssociation>>(context: C, where: UniqueWhereInput, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<WriteReturnTypeWithAssociations<SkillCatalogueWithAssociation, U>>>;
  /** @deprecated use ```client``` instead */
  export function update<C extends RequiredContext, U extends keyof SkillCatalogue>(context: C, where: Partial<SkillCatalogue>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<WriteReturnType<SkillCatalogue, U>>>;
  /** @deprecated use ```client``` instead */
  export function update<C extends RequiredContext>(context: C, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: undefined): ActionRequest<C, Promise<unknown>> {
    const convertedArgs = {where: where, data: data, fieldList: fieldlist, options} as Record<string, any>
    const partialPayload = {action: 'update', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function updateMany<C extends RequiredContext, U extends FieldSelector<SkillCatalogueWithAssociation>>(context: C, where: Filter<SkillCatalogueWithAssociation>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<WriteReturnTypeWithAssociations<SkillCatalogueWithAssociation, U>[]>>;
  /** @deprecated use ```client``` instead */
  export function updateMany<C extends RequiredContext, U extends keyof SkillCatalogue>(context: C, where: Partial<SkillCatalogue>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<WriteReturnType<SkillCatalogue, U>[]>>;
  /** @deprecated use ```client``` instead */
  export function updateMany<C extends RequiredContext>(context: C, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: undefined): ActionRequest<C, Promise<unknown[]>> {
    const convertedArgs = {data: {where: where, data: data, fieldList: fieldlist}, options} as Record<string, any>
    const partialPayload = {action: 'updateMany', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function upsert<C extends RequiredContext, U extends keyof SkillCatalogue & string>(context: C, data: Partial<SkillCatalogue>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<SkillCatalogue, U>>>;
  /** @deprecated use ```client``` instead */
  export function upsert<C extends RequiredContext, U extends keyof SkillCatalogue & string>(context: C, where: Partial<SkillCatalogue>, data: Partial<SkillCatalogue>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<SkillCatalogue, U>>>;
  /** @deprecated use ```client``` instead */
  export function upsert<C extends RequiredContext, U extends keyof SkillCatalogue & string>(context: C, whereOrData: Partial<SkillCatalogue>, dataOrFieldList?: Partial<SkillCatalogue> | ReadonlyArray<U> | 'ALL_FIELDS', fieldlistOrOptions?: ReadonlyArray<U> | 'ALL_FIELDS' | Omit<UpsertOptionsArg, 'draft'>, maybeOptions?: Omit<UpsertOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<SkillCatalogue, U>>> {
    const convertedArgs = {data: {where: whereOrData, data: dataOrFieldList, fieldList: fieldlistOrOptions}, options: maybeOptions} as Record<string, any>
    const partialPayload = {action: 'upsert', ...convertedArgs}

    return new ActionRequest<C, Promise<WriteReturnType<SkillCatalogue, U>>>(context, partialPayload);
  }
  export namespace client {
    /** Creates sample data for skill management including categories, skills, assignments and project role requirements */
    export declare function sampleDataSkillManagement(): ReturnType<func_sampleDataSkillManagement>
    /** write a new object to the database. */
    export declare function create<U extends keyof SkillCatalogue>(input: CreationFields, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): Promise<WriteReturnType<SkillCatalogue, U>>

    /** write new objects to the database. */
    export declare function createMany<U extends keyof SkillCatalogue>(input: CreationFields[], fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): Promise<WriteReturnType<SkillCatalogue, U>[]>

    /** delete the object that is uniquely identified by the where condition. */
    export declare function Delete(where: UniqueWhereInput, options?: undefined): Promise<Partial<SkillCatalogue>[]>

    /** delete all objects. */
    export declare function deleteAll(): Promise<Partial<SkillCatalogue>[]>

    /** delete the objects that are identified by the where condition. */
    export declare function deleteMany(where: Filter<SkillCatalogueWithAssociation>): Promise<Partial<SkillCatalogue>[]>;
    /** @deprecated use the alternative overloaded function */
    export declare function deleteMany(where: Partial<SkillCatalogue>): Promise<Partial<SkillCatalogue>[]>;
    export declare function deleteMany(where: Filter<SkillCatalogue> | Partial<SkillCatalogue>, options?: undefined): Promise<Partial<SkillCatalogue>[]>

    /** returns a list of all objects matching the requirements. */
    export declare function query<U extends FieldSelector<SkillCatalogueWithAssociation>>(args: Omit<TypeSafeQueryArgType<SkillCatalogueWithAssociation>, 'draft'>, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<ReadReturnTypeWithAssociations<SkillCatalogueWithAssociation, U>[]>;
    /** @deprecated Use the alternative overloaded method for querying associations. */
    export declare function query<U extends keyof SkillCatalogue, V extends string = 'ALL_FIELDS'>(args: Omit<TypeSafeQueryArgType<SkillCatalogue>, 'draft'> | Omit<Omit<TypeSafeQueryArgType<SkillCatalogue>, 'draft'>, 'where'> & { where?: Partial<SkillCatalogue> }, fieldlist: ReadonlyArray<U | V> | 'ALL_FIELDS'): Promise<ReadReturnType<V>[]>;
    export declare function query(args: Omit<TypeSafeQueryArgType<SkillCatalogue>, 'draft'>, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown[]>

    /** reads the object that is uniquely identified by the where condition. */
    export declare function read<U extends FieldSelector<SkillCatalogueWithAssociation>>(where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnTypeWithAssociations<SkillCatalogueWithAssociation, U>>;
    /** @deprecated Use the alternative overloaded method for reading associations. */
    export declare function read<U extends keyof SkillCatalogue>(where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnType<U>>;
    export declare function read(where: UniqueWhereInput, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<unknown>

    /** writes modified values back to the database for a given node instance. */
    export declare function update<U extends FieldSelector<SkillCatalogueWithAssociation>>(where: UniqueWhereInput, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnTypeWithAssociations<SkillCatalogueWithAssociation, U>>;
    /** @deprecated Use the alternative overloaded method for writing with associations. */
    export declare function update<U extends keyof SkillCatalogue>(where: Partial<SkillCatalogue>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnType<SkillCatalogue, U>>;
    export declare function update(where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: undefined): Promise<unknown>

    /** writes modified values back to the database for selected node instances. */
    export declare function updateMany<U extends FieldSelector<SkillCatalogueWithAssociation>>(where: Filter<SkillCatalogueWithAssociation>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnTypeWithAssociations<SkillCatalogueWithAssociation, U>[]>;
    /** @deprecated Use the alternative overloaded method for writing with associations. */
    export declare function updateMany<U extends keyof SkillCatalogue>(where: Partial<SkillCatalogue>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnType<SkillCatalogue, U>[]>;
    export declare function updateMany(where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: undefined): Promise<unknown[]>

    /** creates or updates object identified by where. */
    export declare function upsert<U extends keyof SkillCatalogue & string>(data: Partial<SkillCatalogue>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<SkillCatalogue, U>>;
    /** @deprecated use the overload without an explicit where clause */
    export declare function upsert<U extends keyof SkillCatalogue & string>(where: Partial<SkillCatalogue>, data: Partial<SkillCatalogue>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<SkillCatalogue, U>>;
    export declare function upsert<U extends keyof SkillCatalogue & string>(whereOrData: Partial<SkillCatalogue>, dataOrFieldList?: Partial<SkillCatalogue> | ReadonlyArray<U> | 'ALL_FIELDS', fieldlistOrOptions?: ReadonlyArray<U> | 'ALL_FIELDS' | Omit<UpsertOptionsArg, 'draft'>, maybeOptions?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<SkillCatalogue, U>>
  }
}
