package everest.hr.skillmanagement

node SkillCatalogue {
	description: 'This model is for defining list of skills'
	under-development

	field skillName Text {
		required
		editable
		unique
		persisted
		label: 'Skill Name'
	}

	field status enum<SkillStatus>{
		editable
		persisted
		label: 'Status'
		default: enum-item<SkillStatus.Active>
	}

	field `description` Text {
		editable
		persisted
		label: 'Description'
	}

	association alias Mappings for SkillCategoryMapping-SkillCatalogue

	association alias Assignments for SkillAssignment-SkillCatalogue

	generated association alias skillrequirement for SkillRequirement-SkillCatalogue
	action sampleDataSkillManagement(): JSON
}
