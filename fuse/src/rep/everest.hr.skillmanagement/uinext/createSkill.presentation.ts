import type { createSkillPresentation } from '@pkg/everest.hr.skillmanagement/types/presentations/uinext/createSkill';

import createSkill from '../functionality/createSkillInternal';
import createSkillWithCategories from '../functionality/createSkillWithCategoriesInternal';
import { SkillCatalogue } from '../types/SkillCatalogue';
export default {
  createSkill: {
    async validate({ session, input }) {
      const { skillName } = input;
      const skillList = await SkillCatalogue.query(session, {}, ['skillName']);
      const skillNames = skillList.map((skill) => skill.skillName);
      if (skillNames.includes(skillName)) {
        throw new Error('Skill already exists');
      }
      return true;
    },
    async execute({ session, input }) {
      const { categoryIds, skillName, description } = input;
      const skill = await (categoryIds && categoryIds.length > 0
        ? createSkillWithCategories(session, {
            skillName: skillName,
            categories: categoryIds,
            description: description,
          })
        : createSkill(session, {
            skillName: skillName,
            description: description,
          }));

      return {
        result: {
          success: true,
          id: skill?.skillId,
        },
      };
    },
  },
} satisfies createSkillPresentation.implementation;
