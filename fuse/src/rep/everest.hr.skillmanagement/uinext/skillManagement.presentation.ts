import type { ISession } from '@everestsystems/content-core';
import { IDENTIFIER } from '@everestsystems/content-core';
import { PlainDate } from '@everestsystems/datetime';
import { Employee } from '@pkg/everest.hr.base/types/Employee';

import { EmployeeSkillAssignment } from '../types/EmployeeSkillAssignment';
import { EvstSkillApprovalStatus } from '../types/enums/SkillApprovalStatus';
import { EvstSkillChangeRequestStatus } from '../types/enums/SkillChangeRequestStatus';
import { EvstSkillChangeRequestType } from '../types/enums/SkillChangeRequestType';
import { EvstSkillStatus } from '../types/enums/SkillStatus';
import type { skillManagementPresentation } from '../types/presentations/uinext/skillManagement';
import { SkillCatalogue } from '../types/SkillCatalogue';
import { SkillCategory } from '../types/SkillCategory';
import { SkillCategoryMapping } from '../types/SkillCategoryMapping';
import { SkillChangeRequest } from '../types/SkillChangeRequest';

const currentDate = PlainDate.from(new Date().toISOString());

async function getEmployeeUserId(session: ISession): Promise<number> {
  const client = await Employee.client(session);
  const employee = await client.getCurrentEmployee();
  return employee['id'] || null;
}

class SummaryDataSource
  implements skillManagementPresentation.dataSources.Summary.implementation
{
  public async query(
    input: skillManagementPresentation.dataSources.Summary.callbacks.query.input
  ): Promise<skillManagementPresentation.dataSources.Summary.callbacks.query.output> {
    const skills = await SkillCatalogue.query(
      input.session,
      {
        where: {
          status: EvstSkillStatus.Active,
        },
      },
      ['id']
    );
    const skillCount = skills.length;

    const categories = await SkillCategory.query(
      input.session,
      {
        where: {
          active: true,
        },
      },
      ['id']
    );
    const categoryCount = categories.length;

    const pendingEmployeeSkillAssignmentsQuery =
      await EmployeeSkillAssignment.query(
        input.session,
        {
          where: {
            approvalStatus: EvstSkillApprovalStatus.Pending,
          },
        },
        ['id']
      );
    const pendingEmployeeSkillAssignments =
      pendingEmployeeSkillAssignmentsQuery.length;

    // Get total skill assignments and calculate count
    const totalAssignments = await EmployeeSkillAssignment.query(
      input.session,
      {
        where: {},
      },
      ['id']
    );
    const totalAssignmentsCount = totalAssignments.length;

    const skillCatalogChangeRequests = await SkillChangeRequest.query(
      input.session,
      {
        where: {
          status: EvstSkillChangeRequestStatus.Pending,
        },
      },
      ['id', 'status']
    );

    const pendingCatalogUpdateApprovals = skillCatalogChangeRequests.length;

    const result = {
      totalSkills: skillCount,
      totalCategories: categoryCount,
      pendingEmployeeSkillAssignments,
      totalAssignments: totalAssignmentsCount,
      pendingCatalogUpdateApprovals,
    };

    return result;
  }
}

class SkillsWithCategoriesDataSource
  implements
    skillManagementPresentation.dataSources.SkillsWithCategories.implementation
{
  private skillsWithCategoriesData: skillManagementPresentation.dataSources.SkillsWithCategories.levels[''][];

  public async query(
    input: skillManagementPresentation.dataSources.SkillsWithCategories.callbacks.query.input
  ): Promise<skillManagementPresentation.dataSources.SkillsWithCategories.callbacks.query.output> {
    // Get all skills from the catalog
    input.queryInstruction.filters = {
      ...input.queryInstruction.filters,
      status: EvstSkillStatus.Active,
    };
    const skills = await SkillCatalogue.query(
      input.session,
      {
        where: input.queryInstruction.filters || {},
        orderBy: input.queryInstruction.orders || [
          { field: 'skillName', ordering: 'asc' },
        ],
      },
      ['id', 'skillName', 'Mappings.categoryId', 'status', 'description']
    );

    this.skillsWithCategoriesData = skills.map((skill) => ({
      [IDENTIFIER]: skill.id,
      id: skill.id,
      skillName: skill.skillName,
      categories: skill['Mappings'].map((mapping) => mapping.categoryId),
      description: skill.description,
    }));

    // Build the result in the expected format
    return this.skillsWithCategoriesData;
  }
}

class CategoriesWithSkillsDataSource
  implements
    skillManagementPresentation.dataSources.CategoriesWithSkills.implementation
{
  private categoriesWithSkillsData: skillManagementPresentation.dataSources.CategoriesWithSkills.levels[''][];

  public async query(
    input: skillManagementPresentation.dataSources.CategoriesWithSkills.callbacks.query.input
  ): Promise<skillManagementPresentation.dataSources.CategoriesWithSkills.callbacks.query.output> {
    // Get all categories
    const categories = await SkillCategory.query(
      input.session,
      {
        where: input.queryInstruction.filters || {},
        orderBy: input.queryInstruction.orders || [
          { field: 'categoryName', ordering: 'asc' },
        ],
      },
      ['id', 'categoryName', 'Mappings.skillId']
    );
    this.categoriesWithSkillsData = categories.map((category) => ({
      [IDENTIFIER]: category.id,
      id: category.id,
      categoryName: category.categoryName,
      skills: category['Mappings'].map((mapping) => mapping.skillId),
    }));

    // Build the result in the expected format
    return this.categoriesWithSkillsData;
  }
}

export default {
  Summary() {
    return new SummaryDataSource();
  },

  SkillsWithCategories() {
    return new SkillsWithCategoriesDataSource();
  },

  CategoriesWithSkills() {
    return new CategoriesWithSkillsDataSource();
  },

  approveSkill: {
    async execute({ session, input }) {
      const { skillAssignmentId } = input;
      await EmployeeSkillAssignment.update(
        session,
        { id: skillAssignmentId },
        { approvalStatus: EvstSkillApprovalStatus.Approved },
        ['id']
      );
      return { result: true };
    },
  },

  rejectSkill: {
    async execute({ session, input }) {
      const { skillAssignmentId } = input;
      await EmployeeSkillAssignment.update(
        session,
        { id: skillAssignmentId },
        { approvalStatus: EvstSkillApprovalStatus.Rejected },
        ['id']
      );
      return { result: true };
    },
  },

  bulkApproveSkills: {
    async execute({ session, input }) {
      const { skillAssignmentIds } = input;
      await EmployeeSkillAssignment.updateMany(
        session,
        { id: { $in: skillAssignmentIds } },
        { approvalStatus: EvstSkillApprovalStatus.Approved },
        ['id']
      );
      return { result: true };
    },
  },

  bulkRejectSkills: {
    async execute({ session, input }) {
      const { skillAssignmentIds } = input;
      await EmployeeSkillAssignment.updateMany(
        session,
        { id: { $in: skillAssignmentIds } },
        { approvalStatus: EvstSkillApprovalStatus.Rejected },
        ['id']
      );
      return { result: true };
    },
  },

  // New action to approve a single skill change request
  approveSkillChangeRequest: {
    async execute({ session, input }) {
      const { skillChangeRequestId } = input;

      // Get the skill change request details
      const request = await SkillChangeRequest.read(
        session,
        { id: skillChangeRequestId },
        ['id', 'requestType', 'skillId', 'proposedSkillName', 'status']
      );

      if (!request) {
        return { result: false };
      }

      // Process the request based on its type
      switch (request.requestType) {
        case EvstSkillChangeRequestType.Add: {
          // Create a new skill in the catalog
          if (request.proposedSkillName) {
            await SkillCatalogue.create(
              session,
              {
                skillName: request.proposedSkillName,
                status: EvstSkillStatus.Active,
              },
              ['id']
            );
          }
          break;
        }

        case EvstSkillChangeRequestType.Edit: {
          // Update the existing skill name
          if (request.skillId && request.proposedSkillName) {
            await SkillCatalogue.update(
              session,
              { id: request.skillId },
              { skillName: request.proposedSkillName },
              ['id']
            );
          }
          break;
        }

        case EvstSkillChangeRequestType.Deprecate: {
          // Mark the skill as inactive
          if (request.skillId) {
            await SkillCatalogue.update(
              session,
              { id: request.skillId },
              {
                status: EvstSkillStatus.Inactive,
              },
              ['id']
            );
          }
          break;
        }
      }

      const skillChangeRequestData = {
        status: EvstSkillChangeRequestStatus.Approved,
        resolutionDate: currentDate,
        reviewerId: await getEmployeeUserId(session),
      };

      // Update the request status to approved
      await SkillChangeRequest.update(
        session,
        { id: skillChangeRequestId },
        skillChangeRequestData,
        ['id']
      );

      return { result: true };
    },
  },

  // New action to reject a single skill change request
  rejectSkillChangeRequest: {
    async execute({ session, input }) {
      const { skillChangeRequestId } = input;

      const skillChangeRequestData = {
        status: EvstSkillChangeRequestStatus.Rejected,
        resolutionDate: currentDate,
        reviewerId: await getEmployeeUserId(session),
      };

      // Simply mark the request as rejected
      await SkillChangeRequest.update(
        session,
        { id: skillChangeRequestId },
        skillChangeRequestData
      );

      return { result: true };
    },
  },

  // New action to approve multiple skill change requests
  bulkApproveSkillChangeRequests: {
    async execute({ session, input }) {
      const { skillChangeRequestIds } = input;

      if (!skillChangeRequestIds || skillChangeRequestIds.length === 0) {
        return { result: false };
      }

      // Process each request individually
      for (const requestId of skillChangeRequestIds) {
        const request = await SkillChangeRequest.read(
          session,
          { id: requestId },
          ['id', 'requestType', 'skillId', 'proposedSkillName']
        );

        if (request) {
          // Process based on request type
          switch (request.requestType) {
            case EvstSkillChangeRequestType.Add: {
              if (request.proposedSkillName) {
                await SkillCatalogue.create(
                  session,
                  {
                    skillName: request.proposedSkillName,
                  },
                  ['id']
                );
              }
              break;
            }

            case EvstSkillChangeRequestType.Edit: {
              if (request.skillId && request.proposedSkillName) {
                await SkillCatalogue.update(
                  session,
                  { id: request.skillId },
                  { skillName: request.proposedSkillName },
                  ['id']
                );
              }
              break;
            }

            case EvstSkillChangeRequestType.Deprecate: {
              if (request.skillId) {
                await SkillCatalogue.update(
                  session,
                  { id: request.skillId },
                  {
                    status: EvstSkillStatus.Inactive,
                  },
                  ['id']
                );
              }
              break;
            }
          }
        }
      }

      const skillChangeRequestData = {
        status: EvstSkillChangeRequestStatus.Approved,
        resolutionDate: currentDate,
        reviewerId: await getEmployeeUserId(session),
      };

      // Update all requests to approved status
      await SkillChangeRequest.updateMany(
        session,
        { id: { $in: skillChangeRequestIds } },
        skillChangeRequestData,
        ['id']
      );

      return { result: true };
    },
  },

  // New action to reject multiple skill change requests
  bulkRejectSkillChangeRequests: {
    async execute({ session, input }) {
      const { skillChangeRequestIds } = input;

      if (!skillChangeRequestIds || skillChangeRequestIds.length === 0) {
        return { result: false };
      }

      const skillChangeRequestData = {
        status: EvstSkillChangeRequestStatus.Rejected,
        resolutionDate: currentDate,
        reviewerId: await getEmployeeUserId(session),
      };

      // Update all requests to rejected status
      await SkillChangeRequest.updateMany(
        session,
        { id: { $in: skillChangeRequestIds } },
        skillChangeRequestData,
        ['id']
      );

      return { result: true };
    },
  },

  deleteSkills: {
    async validate({ input }) {
      const { skillIds } = input;
      return skillIds.length > 0;
    },

    async execute({ session, input }) {
      const { skillIds } = input;
      if (!skillIds || skillIds.length === 0) {
        return;
      }
      await EmployeeSkillAssignment.deleteMany(session, {
        skillId: { $in: skillIds },
      });

      await SkillCategoryMapping.deleteMany(session, {
        skillId: { $in: skillIds },
      });

      await SkillCatalogue.deleteMany(session, {
        id: { $in: skillIds },
      });
    },
  },

  deleteCategories: {
    async validate({ input }) {
      const { categoryIds } = input;
      return categoryIds.length > 0;
    },

    async execute({ session, input }) {
      const { categoryIds } = input;
      if (!categoryIds || categoryIds.length === 0) {
        return;
      }

      //Delete the concerned skill category mapping
      await SkillCategoryMapping.deleteMany(session, {
        categoryId: { $in: categoryIds },
      });
      //Delete the concerned skill category

      await SkillCategory.deleteMany(session, {
        id: { $in: categoryIds },
      });
    },
  },

  deleteEmployeeSkills: {
    async validate({ input }) {
      const { skillAssignmentIds } = input;
      return skillAssignmentIds && skillAssignmentIds.length > 0;
    },

    async execute({ session, input }) {
      const { skillAssignmentIds } = input;
      if (!skillAssignmentIds || skillAssignmentIds.length === 0) {
        return;
      }
      // Delete the selected skill assignments
      await EmployeeSkillAssignment.deleteMany(session, {
        id: { $in: skillAssignmentIds },
      });
    },
  },
} satisfies skillManagementPresentation.implementation;
