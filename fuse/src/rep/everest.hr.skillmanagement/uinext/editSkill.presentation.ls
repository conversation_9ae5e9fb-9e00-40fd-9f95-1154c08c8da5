//@i18n:skillManagement
package everest.hr.skillmanagement

template presentation editSkill {

    state {
        id: Id
    }

    mode view {
        on page allow view
        on skillAssignmentTable allow view
        on skillDetailsFieldGroup allow view
        allow actions revise
    }

    mode edit {
        on page allow view
        on skillAssignmentTable allow view, change, add,
            remove
        on skillDetailsFieldGroup allow view, change
        allow actions save, cancel
    }

    object data-source SkillDetails with (id=state.id) {
        shape {
            child Page {
                title: Text
            }
            child Skill {
                id (visible: false): field<SkillCatalogue.id>
                skillName (required: true): field<SkillCatalogue.skillName>
                `description`: field<SkillCatalogue.`description`>
                createdBy (editable: false, visible: true): field<SkillCatalogue.createdBy>
                createdDate (editable: false): field<SkillCatalogue.createdDate>
                lastModifiedBy (editable: false): field<SkillCatalogue.lastModifiedBy>
                lastModifiedDate (editable: false): field<SkillCatalogue.lastModifiedDate>
                categories (label: '{{categories}}', editable: true, required: false): array<field<SkillCategoryMapping.categoryId>>
            }
            children SkillAssignments identified-via node<EmployeeSkillAssignment>  {
                id: field<EmployeeSkillAssignment.id>
                employee: field<EmployeeSkillAssignment.employeeId>
                skillLevel: field<EmployeeSkillAssignment.skillLevel>
                approvalStatus (editable: false): field<EmployeeSkillAssignment.approvalStatus>
            }

        }
        modifications {
            on SkillAssignments support create, update, delete
            on Skill support update
        }
        routine save {
            outputs {
                result: TrueFalse
            }
            properties {
                side-effects true
            }
        }
        routine reset {
            properties {
                side-effects false
            }
        }
    }

    struct skillDetailsFieldGroup {


        data SkillDetails.Skill
        fields *
    }

    list skillAssignmentTable {


        data SkillDetails.SkillAssignments
        fields *
    }

    struct page {
        data SkillDetails.Page

        field title
    }

    delegate action save to data-source<SkillDetails>.save transitions-to view

    delegate action cancel to data-source<SkillDetails>.reset transitions-to view

    transition action revise transitions-to edit

}
