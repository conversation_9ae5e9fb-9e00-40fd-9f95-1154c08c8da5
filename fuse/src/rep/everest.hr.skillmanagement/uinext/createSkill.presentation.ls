package everest.hr.skillmanagement

template presentation createSkill {

  array data-source categories node<SkillCategory>

  list categories {
    data categories

    field id (hidden: true)
    field categoryName
  }

  custom action createSkill {
    inputs {
      categoryIds: array<Id>
      skillName: Text
      `description`: Text
    }
    outputs {
      result: JSON
    }
    properties {
      side-effects true
    }
  }

  mode create {
    on categories allow view
    allow actions createSkill
  }

}

