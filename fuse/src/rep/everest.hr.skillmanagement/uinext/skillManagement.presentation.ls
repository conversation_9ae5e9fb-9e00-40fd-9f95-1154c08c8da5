//@i18n:skillManagement
package everest.hr.skillmanagement

template presentation skillManagement {

    mode view {
        on Summary allow view
        on SkillsWithCategoriesTable allow view
        on CategoriesWithSkillsTable allow view
        on EmployeeSkillsTable allow view
        on PendingApprovalsTable allow view
        on SkillChangeRequestsTable allow view
        allow actions approveSkill, rejectSkill, bulkApproveSkills,
            bulkRejectSkills, deleteSkills, deleteCategories,
            deleteEmployeeSkills, revise, approveSkillChangeRequest, 
            rejectSkillChangeRequest, bulkApproveSkillChangeRequests, 
            bulkRejectSkillChangeRequests
    }

    mode edit {
        on Summary allow view
        on SkillsWithCategoriesTable allow view
        on CategoriesWithSkillsTable allow view
        on EmployeeSkillsTable allow view, change, add, remove
        on PendingApprovalsTable allow view
        on SkillChangeRequestsTable allow view
        allow actions approveSkill, rejectSkill, bulkApproveSkills,
            bulkRejectSkills, deleteSkills, deleteCategories,
            deleteEmployeeSkills, save, cancel, approveSkillChangeRequest, 
            rejectSkillChangeRequest, bulkApproveSkillChangeRequests, 
            bulkRejectSkillChangeRequests
    }


    object data-source Summary {
        shape {
            totalSkills: Number<Int>
            totalCategories: Number<Int>
            pendingEmployeeSkillAssignments: Number<Int>
            pendingCatalogUpdateApprovals: Number<Int>
            totalAssignments: Number<Int>
        }
    }

    array data-source SkillsWithCategories {
        shape {
            id (hidden: true, filterable: false): field<SkillCatalogue.id>
            skillName (label: '{{skill}}', orderable: true, filterable: true): field<SkillCatalogue.skillName>
            `description`(orderable: false, filterable: false): field<SkillCatalogue.`description`>
            status(visible:false): field<SkillCatalogue.status>
            categories (label: '{{categories}}', orderable: false, filterable: false): array<field<SkillCategoryMapping.categoryId>>
        }
    }

    array data-source CategoriesWithSkills {
        shape {
            id (hidden: true, filterable: false): field<SkillCategory.id>
            categoryName (label: '{{category}}', orderable: true): field<SkillCategory.categoryName>
            skills (label: '{{skills}}', orderable: false, filterable: false): array<field<SkillCategoryMapping.skillId>>
        }
    }

    array data-source employeeSkills node<EmployeeSkillAssignment> 
        
    array data-source pendingApprovals node<EmployeeSkillAssignment>

    array data-source skillChangeRequests node<SkillChangeRequest>

    struct Summary {
        data Summary
        fields *
    }

    data-set SkillsWithCategoriesTable {
        component Table {
            pagination = true
        }
        data SkillsWithCategories
        fields *
    }

    data-set CategoriesWithSkillsTable {
        component Table {
            pagination = true
        }

        data CategoriesWithSkills
        fields *
    }


    data-set EmployeeSkillsTable {
        component Table {
            pagination = true
        }
        data employeeSkills
        field id (hidden: true, filterable: false)
        fields employeeId, skillId, skillLevel, approvalStatus
    }

    list PendingApprovalsTable {
        data pendingApprovals {
            where (
                approvalStatus = 'Pending'
            )
        }
        fields employeeId, skillId, skillLevel, approvalStatus
        field id (visible: false)
    }

    list SkillChangeRequestsTable {
        data skillChangeRequests 
        fields requesterId, requestType, skillId, proposedSkillName, justification, submissionDate, status, reviewNotes
        field id (visible: false)
    }

    delegate action save to data-source<employeeSkills>.save transitions-to view
    
    transition action revise transitions-to edit

    delegate action cancel to data-source<employeeSkills>.reset transitions-to view

    custom action bulkApproveSkills {
        inputs {
            skillAssignmentIds = '$PendingApprovalsTable[selected].id$'
        }
        outputs {
            result: TrueFalse
        }
        properties {
            affected-data-sets EmployeeSkillsTable, PendingApprovalsTable, Summary
            side-effects true
        }
    }

    custom action bulkRejectSkills {
        inputs {
            skillAssignmentIds = '$PendingApprovalsTable[selected].id$'
        }
        outputs {
            result: TrueFalse
        }
        properties {
            side-effects true
            affected-data-sets EmployeeSkillsTable,  PendingApprovalsTable, Summary
        }
    }

    custom action approveSkill {
        inputs {
            skillAssignmentId: Id
        }
        outputs {
            result: TrueFalse
        }
        properties {
            affected-data-sets EmployeeSkillsTable, PendingApprovalsTable, Summary
            side-effects true
        }
    }

    custom action rejectSkill {
        inputs {
            skillAssignmentId: Id
        }
        outputs {
            result: TrueFalse
        }
        properties {
            side-effects true
            affected-data-sets EmployeeSkillsTable,  PendingApprovalsTable, Summary
        }
    }

    custom action approveSkillChangeRequest {
        inputs {
            skillChangeRequestId: Id
        }
        outputs {
            result: TrueFalse
        }
        properties {
            affected-data-sets SkillChangeRequestsTable, SkillsWithCategoriesTable, Summary
            side-effects true
        }
    }

    custom action rejectSkillChangeRequest {
        inputs {
            skillChangeRequestId: Id
        }
        outputs {
            result: TrueFalse
        }
        properties {
            side-effects true
            affected-data-sets SkillChangeRequestsTable, Summary
        }
    }

    custom action bulkApproveSkillChangeRequests {
        inputs {
            skillChangeRequestIds = '$SkillChangeRequestsTable[selected].id$'
        }
        outputs {
            result: TrueFalse
        }
        properties {
            affected-data-sets SkillChangeRequestsTable, SkillsWithCategoriesTable, Summary
            side-effects true
        }
    }

    custom action bulkRejectSkillChangeRequests {
        inputs {
            skillChangeRequestIds = '$SkillChangeRequestsTable[selected].id$'
        }
        outputs {
            result: TrueFalse
        }
        properties {
            side-effects true
            affected-data-sets SkillChangeRequestsTable, Summary
        }
    }

    custom action deleteSkills {
        inputs {
            skillIds = '$SkillsWithCategoriesTable[selected].id$'
        }
        properties {
            side-effects true
            affected-data-sets EmployeeSkillsTable, SkillsWithCategoriesTable, CategoriesWithSkillsTable, Summary
        }
    }

    custom action deleteCategories {
        inputs {
            categoryIds = '$CategoriesWithSkillsTable[selected].id$'
        }
        properties {
            side-effects true
            affected-data-sets CategoriesWithSkillsTable, SkillsWithCategoriesTable, Summary
        }
    }

    custom action deleteEmployeeSkills {
        inputs {
            skillAssignmentIds = '$EmployeeSkillsTable[selected].id$'
        }
        properties {
            side-effects true
            affected-data-sets EmployeeSkillsTable, Summary
        }
    }
}
