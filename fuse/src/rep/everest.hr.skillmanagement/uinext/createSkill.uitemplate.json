{"version": 3, "uicontroller": "createSkill.uicontroller.ts", "uimodel": {"presentation": {"urn": "urn:evst:everest:hr/skillmanagement:presentation:uinext/createSkill"}}, "uiview": {"config": {"stretch": true, "allowRefreshData": true, "autoRefreshData": true}, "i18n": ["skillManagement"], "title": "{{create.skill}}", "actions": {"content": [{"variant": "primary", "label": "{{create}}", "onClick": "@controller:onCreate", "disabled": "@controller:isCreateDisabled()"}, {"label": "{{cancel}}", "variant": "secondary", "onClick": "@controller:onCancel"}]}, "sections": {"content": [{"component": "FieldGroup", "customId": "createSkillFieldGroup", "size": "12", "columns": 1, "elements": [{"component": "Input", "label": "{{skill.name}}", "name": "skillName", "isEditing": true}, {"component": "Input", "label": "{{description}}", "name": "description", "isEditing": true}, {"component": "Select", "mode": "multiple", "label": "{{categories}}", "name": "categoryIds", "idProp": "id", "textProp": "categoryName", "list": "@binding:categories", "isEditing": true}]}]}}}