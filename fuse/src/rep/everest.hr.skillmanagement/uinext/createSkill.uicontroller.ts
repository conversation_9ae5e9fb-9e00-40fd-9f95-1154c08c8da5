import { executeAction } from '@pkg/everest.base/public/standard/ui/presentation/action';

import type { createSkillPresentationUI } from '../types/presentations/uinext/createSkill.ui';

type CreateSkillContext = createSkillPresentationUI.context;

type Context = CreateSkillContext & {
  state: {
    form: {
      skillName: Text;
      categoryIds: Array<number>;
    };
  };
};

export function isCreateDisabled({ state }: Context) {
  return !state.form?.skillName || state.form?.skillName?.length < 2;
}

export function onCancel(context: Context) {
  context.helpers.closeModal();
}

export async function onCreate(context: Context) {
  const { helpers, state } = context;
  if (!state?.form['skillName']) {
    helpers.showToast({
      title: 'Skill Name is required',
      message: 'Please enter a skill name',
      type: 'error',
    });
    return;
  }
  const response = await executeAction(context, 'createSkill', {
    actionInput: {
      skillName: state?.form['skillName'],
      description: state?.form['description'],
      categoryIds: state?.form['categoryIds'],
    },
  });
  if (response?.result['success']) {
    helpers.showToast({
      title: 'Skill Created',
      message: 'Skill has been created successfully',
      type: 'success',
    });
    helpers.navigate({
      to:
        '/templates/everest.hr.skillmanagement/uinext/editSkill?id=' +
        response?.result['id'],
      initialState: {
        id: response?.result['id'],
        mode: 'view',
      },
    });
    helpers.closeModal();
    return;
  }
  helpers.showToast({
    title: 'Skill Creation Failed',
    message: 'Skill cannot be created',
    type: 'error',
  });
}
