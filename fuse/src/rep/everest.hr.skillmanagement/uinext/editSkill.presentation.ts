import { DATA, IDENTIFIER, METADATA } from '@everestsystems/content-core';
import { User } from '@pkg/everest.appserver.usermgmt/types/User';
import { SkillManagementAPI } from '@pkg/everest.hr.skillmanagement/types/presentations/api/v1/SkillManagementAPI/SkillManagementAPI';
import type { editSkillPresentation } from '@pkg/everest.hr.skillmanagement/types/presentations/uinext/editSkill';
import { set } from 'lodash';

import updateCategoriesForSkill from '../functionality/updateCategoriesForSkill';
import { EmployeeSkillAssignment } from '../types/EmployeeSkillAssignment';
import { EvstSkillApprovalStatus } from '../types/enums/SkillApprovalStatus';
import { SkillCatalogue } from '../types/SkillCatalogue';

type SkillUpdateData = Partial<{
  skillName: string;
  description: string;
}>;

// Type for assignment update data
type EmployeeSkillAssignmentUpdateData = Partial<{
  employeeId: number;
  skillLevel: number;
  approvalStatus: EvstSkillApprovalStatus;
}>;

class SkillDetails
  implements editSkillPresentation.dataSources.SkillDetails.implementation
{
  private data: editSkillPresentation.dataSources.SkillDetails.callbacks.query.queryData =
    {
      Page: {
        title: '',
      },
      Skill: {},
      SkillAssignments: [
        // Array of skill assignments will be populated in the query method
      ],
    };

  private metadata: editSkillPresentation.dataSources.SkillDetails.callbacks.query.queryMetadata =
    {
      Page: {},
      Skill: {},
      SkillAssignments: {},
    };

  // Storage for tracking changes to skill assignments
  private assignmentCreates: editSkillPresentation.dataSources.SkillDetails.callbacks.query.queryData['SkillAssignments'] =
    [];
  private assignmentUpdates: Map<
    number,
    editSkillPresentation.dataSources.SkillDetails.levels['SkillAssignments']
  > = new Map();
  private assignmentDeletes: Set<number> = new Set();

  // New: Track changes to the Skill object
  private skillUpdates: Partial<
    editSkillPresentation.dataSources.SkillDetails.levels['Skill']
  > = {};

  async query({
    session,
    parameters: { id },
    queryReason,
    queryInstruction,
    mode,
  }: editSkillPresentation.dataSources.SkillDetails.callbacks.query.input): Promise<editSkillPresentation.dataSources.SkillDetails.callbacks.query.combinedOutput> {
    // Check if we need to fetch fresh data - either we have no data yet
    // or an external request is forcing a refresh
    if (!this.data?.Skill?.id || queryReason === 'externalRequest') {
      // Only reset tracking on initial load, not on refresh
      if (!this.data?.Skill?.id) {
        this.resetAssignmentTracking();
      }

      const skillQueryResult = await SkillCatalogue.query(
        session,
        {
          where: {
            id: id,
          },
        },
        [
          'id',
          'skillName',
          'description',
          'createdBy',
          'createdDate',
          'lastModifiedBy',
          'lastModifiedDate',
          'Mappings.categoryId',
          'Assignments.id',
          'Assignments.employeeId',
          'Assignments.skillLevel',
          'Assignments.approvalStatus',
        ]
      );

      if (!skillQueryResult || skillQueryResult.length === 0) {
        return null;
      }

      const skill = skillQueryResult[0];
      let createdByName = '';
      if (skill.createdBy) {
        const createdUserQuery = await User.query(
          session,
          {
            where: {
              userId: skill.createdBy,
            },
          },
          ['fullName']
        );

        createdByName =
          createdUserQuery && createdUserQuery.length > 0
            ? (createdUserQuery[0]?.fullName as string)
            : skill.createdBy;
      }

      let lastModifiedByName = '';
      if (skill.lastModifiedBy) {
        const modifiedUserQuery = await User.query(
          session,
          {
            where: {
              userId: skill.lastModifiedBy,
            },
          },
          ['fullName']
        );
        lastModifiedByName =
          modifiedUserQuery && modifiedUserQuery.length > 0
            ? (modifiedUserQuery[0]?.fullName as string)
            : skill.lastModifiedBy;
      }

      this.data.Skill = {
        id: id,
        skillName: skill.skillName,
        description: skill.description,
        createdBy: createdByName,
        createdDate: skill.createdDate,
        lastModifiedBy: lastModifiedByName,
        lastModifiedDate: skill.lastModifiedDate,
        categories: skill['Mappings'].map((mapping) => mapping.categoryId),
      };

      // Apply any pending skill updates (this preserves edits across refreshes)
      if (Object.keys(this.skillUpdates).length > 0) {
        this.data.Skill = {
          ...this.data.Skill,
          ...this.skillUpdates,
        };
      }

      // Store the base assignments from database
      const baseAssignments = skillQueryResult[0]['Assignments'].map(
        (assignment) => ({
          [IDENTIFIER]: assignment.id,
          id: assignment.id,
          employee: assignment.employeeId,
          skillLevel: assignment.skillLevel,
          approvalStatus: assignment.approvalStatus,
        })
      );

      // Filter out assignments that are marked for deletion
      const filteredBaseAssignments = baseAssignments.filter(
        (assignment) => !this.assignmentDeletes.has(assignment.id)
      );

      this.data.Page = {
        title: skill.skillName,
      };

      // Apply any updates to existing assignments
      const updatedAssignments = filteredBaseAssignments.map((assignment) => {
        const updates = this.assignmentUpdates.get(assignment.id);
        return updates ? { ...assignment, ...updates } : assignment;
      });

      // Combine existing assignments with updates and creates
      this.data.SkillAssignments = [
        ...updatedAssignments,
        ...this.assignmentCreates,
      ];
    }
    if (mode === 'edit') {
      this.metadata.Skill.createdBy = {
        visible: false,
      };
      this.metadata.Skill.lastModifiedBy = {
        visible: false,
      };
      this.metadata.Skill.createdDate = {
        visible: false,
      };
      this.metadata.Skill.lastModifiedDate = {
        visible: false,
      };
    } else {
      this.metadata.Skill.createdBy = {
        visible: true,
      };
      this.metadata.Skill.lastModifiedBy = {
        visible: true,
      };
      this.metadata.Skill.createdDate = {
        visible: true,
      };
      this.metadata.Skill.lastModifiedDate = {
        visible: true,
      };
    }
    return {
      [DATA]: this.data,
      [METADATA]: {
        Skill:
          queryInstruction.Skill === undefined
            ? undefined
            : this.metadata.Skill,
      },
    };
  }

  async execute_reset(
    _input: editSkillPresentation.dataSources.SkillDetails.routines.reset.executeInput
  ): Promise<editSkillPresentation.dataSources.SkillDetails.routines.reset.executeOutput> {
    this.data.Skill = undefined;
    this.data.SkillAssignments = undefined;
    this.resetAssignmentTracking();
  }

  async execute_save({
    session,
  }: editSkillPresentation.dataSources.SkillDetails.routines.save.executeInput): Promise<editSkillPresentation.dataSources.SkillDetails.routines.save.executeOutput> {
    if (this.data?.Skill?.id) {
      // Update the skill details using tracked changes
      if (Object.keys(this.skillUpdates).length > 0) {
        const updateData: SkillUpdateData = {};

        // Only include fields that should be updated
        if (this.skillUpdates.skillName !== undefined) {
          updateData.skillName = this.skillUpdates.skillName;
        }

        if (this.skillUpdates.description !== undefined) {
          updateData.description = this.skillUpdates.description;
        }

        // Add other fields as needed
        if (Object.keys(updateData).length > 0) {
          await SkillManagementAPI.updateSkill.execute(session, {
            skillDetails: {
              skillId: this.data.Skill.id,
              skillName: this.skillUpdates.skillName,
              description: this.skillUpdates.description,
            },
          });
        }
      }

      // Update categories if they've been changed
      if (this.skillUpdates.categories !== undefined) {
        await updateCategoriesForSkill(session, {
          skillId: this.data.Skill.id,
          categories: this.data.Skill.categories,
        });
      }

      // Process assignment creates
      if (this.assignmentCreates.length > 0) {
        // Handle creation of new assignments
        const createInput = this.assignmentCreates.map((assignment) => ({
          skillId: this.data.Skill.id,
          employeeId: assignment.employee,
          skillLevel: assignment.skillLevel,
          approvalStatus: assignment.approvalStatus,
        }));
        // Replace with your actual create method
        await EmployeeSkillAssignment.createMany(session, createInput);
      }

      // Process assignment updates
      for (const [id, updates] of this.assignmentUpdates.entries()) {
        if (Object.keys(updates).length > 0) {
          const updateData: EmployeeSkillAssignmentUpdateData = {};

          if (updates.employee !== undefined) {
            updateData.employeeId = updates.employee;
          }
          if (updates.skillLevel !== undefined) {
            updateData.skillLevel = updates.skillLevel;
          }
          if (updates.approvalStatus !== undefined) {
            updateData.approvalStatus = updates.approvalStatus;
          }

          if (Object.keys(updateData).length > 0) {
            // Update the assignment
            await EmployeeSkillAssignment.update(session, { id }, updateData);
          }
        }
      }

      // Process assignment deletes
      if (this.assignmentDeletes.size > 0) {
        // Replace with your actual delete method
        await EmployeeSkillAssignment.deleteMany(session, {
          id: {
            $in: [...this.assignmentDeletes],
          },
        });
      }
    }

    // Clear data to force reload on next query
    this.data.Skill = undefined;
    this.data.SkillAssignments = undefined;
    this.resetAssignmentTracking();

    return {
      result: true,
    };
  }

  async update_Skill({
    fieldName,
    newFieldValue,
  }: editSkillPresentation.dataSources.SkillDetails.callbacks.update_Skill.input): Promise<editSkillPresentation.dataSources.SkillDetails.callbacks.update_Skill.output> {
    // Update the current data
    set(this.data.Skill, [fieldName], newFieldValue);

    // Track the change for later save
    set(this.skillUpdates, [fieldName], newFieldValue);
  }

  async update_SkillAssignments({
    path: [identifier],
    fieldName,
    newFieldValue,
  }: editSkillPresentation.dataSources.SkillDetails.callbacks.update_SkillAssignments.input): Promise<editSkillPresentation.dataSources.SkillDetails.callbacks.update_SkillAssignments.output> {
    // First, always update the currently displayed data
    const dataAssignment = this.data.SkillAssignments.find(
      (item) => item[IDENTIFIER] === identifier || item.id === identifier
    );
    if (dataAssignment) {
      set(dataAssignment, [fieldName], newFieldValue);
    }

    // Then update the tracking collections based on whether it's a new or existing assignment
    if (typeof identifier === 'string') {
      // It's a new assignment being edited (not yet saved to DB)
      const createAssignment = this.assignmentCreates.find(
        (create) => create[IDENTIFIER] === identifier
      );
      if (createAssignment) {
        set(createAssignment, [fieldName], newFieldValue);
      }
    } else if (typeof identifier === 'number') {
      // It's an existing assignment (has a DB ID)
      let updateAssignment = this.assignmentUpdates.get(identifier);
      if (!updateAssignment) {
        updateAssignment = {};
        this.assignmentUpdates.set(identifier, updateAssignment);
      }
      set(updateAssignment, [fieldName], newFieldValue);
    }
  }

  async create_SkillAssignments({
    session,
    data,
    isRestore,
  }: editSkillPresentation.dataSources.SkillDetails.callbacks.create_SkillAssignments.input): Promise<editSkillPresentation.dataSources.SkillDetails.callbacks.create_SkillAssignments.output> {
    if (isRestore) {
      // If restoring a previously deleted assignment
      this.assignmentDeletes.delete(data.id);

      // Add it back to the displayed data
      const assignment = this.data.SkillAssignments.find(
        (item) => item.id === data.id
      );

      if (!assignment) {
        // If it's not in the current data, add it back
        this.data.SkillAssignments.push({
          ...data,
          [IDENTIFIER]: data.id,
        });
      }

      return;
    } else {
      // If creating a new assignment
      const identifier = session.util.uuid.v4();
      const newAssignment = {
        ...data,
        approvalStatus: EvstSkillApprovalStatus.Approved,
        [IDENTIFIER]: identifier,
      };

      // Add to tracking
      this.assignmentCreates.push(newAssignment);

      // Add to displayed data
      this.data.SkillAssignments.push(newAssignment);

      return identifier;
    }
  }

  async delete_SkillAssignments({
    path: [identifier],
    isRestorable,
  }: editSkillPresentation.dataSources.SkillDetails.callbacks.delete_SkillAssignments.input): Promise<editSkillPresentation.dataSources.SkillDetails.callbacks.delete_SkillAssignments.output> {
    if (isRestorable) {
      // Mark for deletion and remove from UI
      if (typeof identifier === 'number') {
        this.assignmentDeletes.add(identifier);
      }

      // Remove from displayed data
      this.data.SkillAssignments = this.data.SkillAssignments.filter(
        (item) => item[IDENTIFIER] !== identifier
      );
    } else {
      // For non-restorable (temporary items), just remove them
      this.assignmentCreates = this.assignmentCreates.filter(
        (item) => item[IDENTIFIER] !== identifier
      );

      // Remove from displayed data
      this.data.SkillAssignments = this.data.SkillAssignments.filter(
        (item) => item[IDENTIFIER] !== identifier
      );
    }
  }

  // Helper method to reset assignment tracking
  private resetAssignmentTracking(): void {
    this.assignmentUpdates = new Map();
    this.assignmentCreates = [];
    this.assignmentDeletes = new Set();
    this.skillUpdates = {};
  }
}

export default {
  SkillDetails() {
    return new SkillDetails();
  },
} satisfies editSkillPresentation.implementation;
