package everest.hr.skillmanagement

api presentation SkillManagementAPI {
  action createSkill {
    inputs {
      skillDetails: object<{
        skillName: Text
        categories  (optional: true): array<Id>
        `description` (optional: true): Text
      }>
    }

    outputs {
      skillId: Id
      skillName: Text
      `description`(optional: true): Text
      categories (optional: true): Array<Id>
      categoryMappings (optional: true): Array<Id>
    }

    properties {
      side-effects false
    }
  }

  action deleteSkill {
    inputs {
      skillId: Id
    }

    outputs {
    }

    properties {
      side-effects true
    }
  }

  action updateSkill {
    inputs {
      skillDetails: object<{
        skillId: Id
        skillName(optional: true): Text
        `description` (optional: true): Text
      }>
    }

    outputs {
      skillId: Id
      skillName(optional: true): Text
      `description`(optional: true): Text
    }

    properties {
      side-effects true
    }
  }

  action updateCategoriesForSkill {
    inputs {
      skillCategoryDetails: object<{
        skillId: Id
        categories: array<Id>
      }>
    }

    outputs {
      skillId: Id
      categories: Array<Id>
      categoryMappings: Array<Id>
    }

    properties {
      side-effects true
    }
  }

  action assignSkillToEmployees {
    inputs {
      skillAssignments: object<{
        skillId: Id
        skillLevelDetail: array<object<{
          id: Id
          skillLevel: primitive<SkillLevelType>
        }>>
      }>
    }

    outputs {
      assignments: array<object<{
        employeeId: Id
        assignmentId: Id
        skillId: Id
        skillLevel: primitive<SkillLevelType>
        skillApprovalStatus  (optional: true): enum<SkillApprovalStatus>
      }>>
    }

    properties {
      side-effects true
    }
  }

  action assignSkillsToEmployee {
    inputs {
      skillAssignments: object<{
        employeeId: Id
        skillLevelDetail: array<object<{
          id: Id
          skillLevel: primitive<SkillLevelType>
        }>>
      }>
    }

    outputs {
      assignments: array<object<{
        employeeId: Id
        assignmentId: Id
        skillId: Id
        skillLevel: primitive<SkillLevelType>
        skillApprovalStatus  (optional: true): enum<SkillApprovalStatus>
      }>>
    }

    properties {
      side-effects true
    }
  }

  action updateEmployeeSkillAssignment {
    inputs {
      employeeSkillAssignment: object<{
        employeeId: Id
        skillAssignmentId: Id
        skillLevel  (optional: true): Id
        skillApprovalStatus  (optional: true): enum<SkillApprovalStatus>
      }>
    }

    outputs {
      employeeId: Id
      assignmentId: Id
      skillId: Id
      skillLevel: primitive<SkillLevelType>
      skillApprovalStatus (optional: true): enum<SkillApprovalStatus>
    }

    properties {
      side-effects true
    }
  }

  action deleteSkillAssignment {
    inputs {
      skillAssignmentId: Id
    }

    outputs {
    }

    properties {
      side-effects true
    }
  }

  action addSkillRequirementToProjectPositions {
    inputs {
      skillRequirements: object<{
        skillId: Id
        skillLevelDetail: array<object<{
          id: Id
          skillLevel: primitive<SkillLevelType>
        }>>
      }>
    }

    outputs {
      requirements: array<object<{
        projectPositionId: Id
        requirementId: Id
        skillId: Id
        skillLevel: primitive<SkillLevelType>
      }>>
    }

    properties {
      side-effects true
    }
  }

  action addSkillRequirementsToProjectPosition {
    inputs {
      skillRequirements: object<{
        projectPositionId: Id
        skillLevelDetail: array<object<{
          id: Id
          skillLevel: primitive<SkillLevelType>
        }>>
      }>
    }

    outputs {
      requirements: array<object<{
        projectPositionId: Id
        requirementId: Id
        skillId: Id
        skillLevel: primitive<SkillLevelType>
      }>>
    }

    properties {
      side-effects true
    }
  }

  action updateProjectPositionSkillRequirement {
    inputs {
      projectPositionSkillRequirement: object<{
        projectPositionId: Id
        skillRequirementId: Id
        skillLevel: primitive<SkillLevelType>
      }>
    }

    outputs {
      projectPositionId: Id
      requirementId: Id
      skillId: Id
      skillLevel: primitive<SkillLevelType>
    }

    properties {
      side-effects true
    }
  }
}
