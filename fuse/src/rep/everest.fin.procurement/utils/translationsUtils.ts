import type { ISession } from '@everestsystems/content-core';
import { getTranslations } from '@everestsystems/content-core';

class TranslationsCache {
  private centralProperty: Map<string, string> | undefined;

  private static readonly TRANSLATION_KEYS = [
    'purchaseOrders.vendor.required',
    'purchaseOrders.entity.required',
    'purchaseOrders.startDate.required',
    'purchaseOrders.status.approved',
    'purchaseOrders.status.pendingApproval',
    'purchaseOrders.status.rejected',
    'purchaseOrders.purchaseOrderItem.startDateGreater',
    'purchaseOrders.purchaseOrderItem.endDateGreater',
    'purchaseOrders.required',
  ];

  public async init(args: { session: ISession; bypassCaching?: boolean }) {
    const { session, bypassCaching = false } = args;

    if (this.centralProperty === undefined || bypassCaching) {
      const translations = await getTranslations(
        session,
        [
          'purchaseOrders.vendor.required',
          'purchaseOrders.entity.required',
          'purchaseOrders.startDate.required',
          'purchaseOrders.status.approved',
          'purchaseOrders.status.pendingApproval',
          'purchaseOrders.status.rejected',
          'purchaseOrders.purchaseOrderItem.startDateGreater',
          'purchaseOrders.purchaseOrderItem.endDateGreater',
          'purchaseOrders.required',
        ],
        'purchaseOrders'
      );

      this.centralProperty = new Map(
        TranslationsCache.TRANSLATION_KEYS.map((key, index) => [
          key,
          translations[index],
        ])
      );
    }
  }

  public get translations() {
    return this.centralProperty;
  }

  public getTranslationByName(value: string) {
    return this.centralProperty?.get(value);
  }
}
export default TranslationsCache;
