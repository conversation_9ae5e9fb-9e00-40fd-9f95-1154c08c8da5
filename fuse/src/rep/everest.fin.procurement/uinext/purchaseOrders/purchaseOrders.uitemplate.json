{"version": 3, "uicontroller": "purchaseOrders.uicontroller.ts", "uimodel": {"presentation": {"urn": "urn:evst:everest:fin/procurement:presentation:uinext/purchaseOrders/purchaseOrders", "parameters": {"mode": "view"}}}, "uiview": {"list": "true", "title": "{{purchaseOrders.title.plural}}", "i18n": "purchaseOrders", "config": {"stretch": true, "allowRefreshData": true, "autoRefreshData": true}, "header": {"variant": "compact", "content": {"title": "{{purchaseOrders.header}}"}}, "sections": {"content": [{"component": "Table", "customId": "purchaseOrdersTable", "section": {"grid": {"size": "12"}}, "props": {"presentationDataSet": "@dataSet:purchaseOrders", "variant": "light", "alwaysOnPagination": true, "fullWidth": true, "rowSelection": true, "rowBackgroundGetter": "@controller:getRowBackground", "onRowClicked": "@controller:navigate", "columns": [{"field": "referenceNumber", "headerName": "{{purchaseOrders.referenceNumber2}}"}, {"field": "description", "headerName": "{{purchaseOrders.description}}"}, {"field": "departmentId", "headerName": "{{purchaseOrders.departmentId}}"}, {"field": "startDate", "headerName": "{{purchaseOrders.startDate}}"}, {"field": "endDate", "headerName": "{{purchaseOrders.endDate}}"}, {"field": "totalAmount", "headerName": "{{purchaseOrders.totalAmount}}"}, {"field": "approvalStatus", "headerName": "{{purchaseOrders.approvalStatus}}", "cellVariant": {"variant": "badge", "matchers": {"Approved": "success", "Pending Approval": "mystic-grey", "Rejected": "danger"}}}]}}]}, "actions": {"shape": "pill", "content": [{"variant": "secondary", "label": "{{purchaseOrders.create}}", "actions": [{"label": "{{purchaseOrders.create}}", "onClick": "@controller:navigateToCreatePurchaseOrder"}, {"label": "{{purchaseOrders.purchaseRequest.create}}", "onClick": "@controller:openCreatePurchaseRequestModal"}]}, {"variant": "primary", "label": "{{purchaseOrders.action}}", "actions": [{"label": "{{purchaseOrders.delete}}", "presentationAction": "@action:deletePurchaseOrders", "notificationMessage": {"loading": "{{purchaseOrders.deletingPurchaseOrders}}", "success": "{{purchaseOrders.successDeleteOrders}}"}, "confirmation": {"message": "{{purchaseOrders.order.delete}}", "description": "{{purchaseOrders.order.deleteDescription}}", "confirmLabel": "{{purchaseOrders.delete}}"}}]}]}}}