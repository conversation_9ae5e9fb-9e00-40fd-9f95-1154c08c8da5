import { IDENTIFIER } from '@everestsystems/content-core';
import { PlainDate } from '@everestsystems/datetime';
import { GenericApprovalPolicy } from '@pkg/everest.base.approvals/public/types';
import { ApprovalPolicyHeader } from '@pkg/everest.base.approvals/types/ApprovalPolicyHeader';
import { EvstPurchaseOrderApprovalStatus } from '@pkg/everest.fin.procurement/types/enums/PurchaseOrderApprovalStatus';
import { EvstPurchaseOrderBudgetStatus } from '@pkg/everest.fin.procurement/types/enums/PurchaseOrderBudgetStatus';
import type { purchaseOrderPresentation } from '@pkg/everest.fin.procurement/types/presentations/uinext/purchaseOrders/purchaseOrder';
import { PurchaseOrderHeader } from '@pkg/everest.fin.procurement/types/PurchaseOrderHeader';
import type { PurchaseOrderLine } from '@pkg/everest.fin.procurement/types/PurchaseOrderLine';
import { PurchaseOrderLineAttachment } from '@pkg/everest.fin.procurement/types/PurchaseOrderLineAttachment';
import { getPurchaseOrderTotal } from '@pkg/everest.fin.procurement/utils/formula/total';
import TranslationsCache from '@pkg/everest.fin.procurement/utils/translationsUtils';
import { omit, set } from 'lodash';

type Line = Partial<
  purchaseOrderPresentation.dataSources.purchaseOrder.levels['Lines']
>;

interface PurchaseOrderData
  extends Partial<
    purchaseOrderPresentation.dataSources.purchaseOrder.levels['']
  > {
  Lines: Array<Line> | undefined;
}

class PurchaseOrderDataSource
  implements purchaseOrderPresentation.dataSources.purchaseOrder.implementation
{
  private poData: PurchaseOrderData | undefined = undefined;
  private translationsCache = new TranslationsCache();

  public async query({ session, parameters }) {
    const id = parameters.id;
    const mode = parameters.mode;
    await this.translationsCache.init({ session });
    if (this.poData === undefined) {
      if (id) {
        const { poHeader, poLines, attachments } =
          await this.queryPurchaseOrderData(id, session);

        // Custom Flags
        const {
          isAddRemovePurchaseLineDisabled,
          isSaveDraftButtonVisible,
          ifPurchaseOrderBudgetApproved,
          isDraft,
        } = this.getCustomFlags(poHeader, mode);
        const headerStatus = this.getPurchaseOrderHeaderStatus(poHeader);

        const Lines = poLines.map((line) => ({
          ...line,
          [IDENTIFIER]: line.id,
          lineUuid: line.uuid,
          attachments: attachments
            .filter((att) => att.purchaseOrderLineId === line.id)
            .map((att) => ({
              [IDENTIFIER]: att.id,
              fileId:
                att.fileId as PurchaseOrderLineAttachment.PurchaseOrderLineAttachment['fileId'],
              fileName:
                att.fileName as PurchaseOrderLineAttachment.PurchaseOrderLineAttachment['fileName'],
              id: att.id as PurchaseOrderLineAttachment.PurchaseOrderLineAttachment['id'],
            })),

          isLineNotApproved: true,
        }));
        let approvalPolicyStatus = {};
        if (!isDraft) {
          approvalPolicyStatus = await this.getApprovalPolicyStatus(
            id,
            session
          );
        }

        this.poData = {
          vendor: poHeader.vendorId,
          ...omit(poHeader, ['PurchaseOrderLine-PurchaseOrderHeader']),
          headerStatus,
          Lines: Lines,
          approvalPolicyStatus,
          //Custom-Flags
          isAddRemovePurchaseLineDisabled,
          isSaveDraftButtonVisible,
          ifPurchaseOrderBudgetApproved,
          isDraft,
        };
      } else {
        this.poData = {
          Lines: [],
          isAddRemovePurchaseLineDisabled: false,
          isSaveDraftButtonVisible: true,
          ifPurchaseOrderBudgetApproved: false,
          headerStatus: 'Draft',
        };
      }
    }
    return this.poData;
  }

  public async update({ fieldName, newFieldValue }) {
    set(this.poData, [fieldName], newFieldValue);
  }
  public async execute_reset() {
    this.poData = undefined;
  }

  public async execute_createPurchaseOrderLine({
    session,
    input: { lineData },
  }) {
    //Create new line in the data object
    const newLineUuid = session.util.uuid.v4();
    const newLine = {
      ...(lineData as Line),
      [IDENTIFIER]: newLineUuid,
      lineUuid: newLineUuid,
    };

    if (!this.poData) {
      this.poData = { Lines: [] };
    }
    if (!Array.isArray(this.poData.Lines)) {
      this.poData.Lines = [];
    }
    this.poData.Lines.push(newLine as Line);
    this.poData.totalAmount = getPurchaseOrderTotal(this.poData.Lines);
    return { lineId: newLine[IDENTIFIER] };
  }

  public async execute_updatePurchaseOrderLine({ input: { lineData } }) {
    //Update the lineData in this.poData.Lines using lineData.lineUuid
    const updatedLine: Line = lineData;
    this.poData = {
      ...this.poData,
      Lines: this.poData.Lines.map((line) =>
        line.lineUuid === updatedLine.lineUuid ? updatedLine : line
      ),
    };
    this.poData.totalAmount = getPurchaseOrderTotal(this.poData.Lines);
  }

  public async execute_deletePurchaseOrderLine({
    input: { purchaseOrderLineIdentifier },
  }) {
    //Delete the line from this.poData using lineId
    this.poData = {
      ...this.poData,
      Lines: this.poData.Lines.filter(
        (line) => line.lineUuid !== purchaseOrderLineIdentifier
      ),
    };
    this.poData.totalAmount = getPurchaseOrderTotal(this.poData.Lines);
    return { lineId: purchaseOrderLineIdentifier };
  }

  public async execute_approvePurchaseOrder({ session, input: { id } }) {
    const purchaseOrderHeaderClient = await PurchaseOrderHeader.client(session);
    const { purchaseOrderHeader } = await this.prepareDataToSave(id);
    const poHeader =
      await purchaseOrderHeaderClient.approvePO(purchaseOrderHeader);

    this.poData = undefined;

    return { poId: poHeader?.id };
  }

  public async execute_rejectPurchaseOrderLine({ session, input: { id } }) {
    const purchaseOrderHeaderClient = await PurchaseOrderHeader.client(session);
    const { purchaseOrderHeader } = await this.prepareDataToSave(id);
    const poHeader =
      await purchaseOrderHeaderClient.rejectPO(purchaseOrderHeader);

    this.poData = undefined;

    return { poId: poHeader?.id };
  }

  public async validate_save({ input: { saveAsDraft }, context }) {
    if (!saveAsDraft) {
      if (!this.poData.vendor) {
        context.addError(
          [],
          this.translationsCache.getTranslationByName(
            'purchaseOrders.vendor.required'
          ),
          'vendor'
        );
      }
      if (!this.poData.entityId) {
        context.addError(
          [],
          this.translationsCache.getTranslationByName(
            'purchaseOrders.entity.required'
          ),
          'entityId'
        );
      }
      if (!this.poData.startDate) {
        context.addError(
          [],
          this.translationsCache.getTranslationByName(
            'purchaseOrders.startDate.required'
          ),
          'startDate'
        );
      }

      if (
        this.poData.startDate &&
        this.poData.endDate &&
        PlainDate.compare(this.poData.endDate, this.poData.startDate) === -1
      ) {
        context.addError([], 'End Date cannot be before Start Date', 'endDate');
      }
      if (!this.poData.Lines || this.poData.Lines.length === 0) {
        return false;
      }
    }
  }

  public async execute_save({
    session,
    parameters: { id },
    input: { saveAsDraft },
  }) {
    const purchaseOrderHeaderClient = await PurchaseOrderHeader.client(session);

    const {
      purchaseOrderHeader,
      purchaseOrderLines,
      purchaseOrderLineAttachments,
    } = await this.prepareDataToSave(id);

    // Check if all the purchaseOrderLines are Approved and then update the flag in options 'approvePurchaseOrder' to true
    const allLinesApproved = purchaseOrderLines.every((line) => true);

    const options = {
      draft: saveAsDraft,
      activateDraftPurchaseOrder: !saveAsDraft && this.poData?.isDraft,
      approvePurchaseOrder: allLinesApproved,
    };

    const poHeader = await purchaseOrderHeaderClient.upsertPurchaseOrder(
      purchaseOrderHeader,
      purchaseOrderLines,
      purchaseOrderLineAttachments,
      options
    );
    this.poData = undefined;
    return { id: poHeader?.id };
  }

  private async queryPurchaseOrderData(id, session) {
    const poHeader = await PurchaseOrderHeader.read(
      session,
      { id },
      [
        'id',
        'approvalStatus',
        'budgetStatus',
        'description',
        'poOrigin',
        'entityId',
        'entityName',
        'startDate',
        'endDate',
        'totalAmount',
        'vendorId',
        'currency',
        {
          'PurchaseOrderLine-PurchaseOrderHeader': [
            'uuid',
            'id',
            'itemName',
            'itemId',
            'status',
            'unitOfMeasure',
            'quantity',
            'quantityOrAmount',
            'price',
            'total',
            'estimatedDeliveryDate',
            'deliveryDate',
            'description',
            'startDate',
            'endDate',
            'departmentId',
            'matchStatus',
          ],
        },
      ],
      {
        draft: 'include',
      }
    );
    const poLines = poHeader['PurchaseOrderLine-PurchaseOrderHeader'] ?? [];

    const attachments = await PurchaseOrderLineAttachment.query(
      session,
      {
        where: {
          purchaseOrderLineId: { $in: poLines.map((line) => line.id) },
        },
        draft: 'include',
      },
      ['id', 'fileName', 'fileId', 'purchaseOrderLineId']
    );
    return {
      poHeader,
      poLines,
      attachments,
    };
  }

  private async prepareDataToSave(id) {
    // Prepare header data
    const purchaseOrderHeader = {
      id: id,
      entityId: this.poData.entityId,
      startDate: this.poData.startDate,
      endDate: this.poData.endDate,
      description: this.poData.description,
      poOrigin: this.poData.poOrigin,
      entityName: this.poData.entityName,
      vendorId: this.poData.vendor,
      currency: this.poData.currency,
    } as PurchaseOrderHeader.PurchaseOrderHeader;

    // Prepare lines data
    const purchaseOrderLines =
      this.poData.Lines?.map(
        (line) =>
          ({
            id: line.id,
            itemName: line.itemName,
            itemId: line.itemId,
            status: line.status,
            unitOfMeasure: line.unitOfMeasure,
            quantity: line.quantity,
            quantityOrAmount: line.quantityOrAmount,
            price: line.price,
            estimatedDeliveryDate: line.estimatedDeliveryDate,
            deliveryDate: line.deliveryDate,
            description: line.description,
            startDate: line.startDate,
            endDate: line.endDate,
            departmentId: line.departmentId,
          }) as PurchaseOrderLine.PurchaseOrderLine
      ) || [];

    // Prepare attachments data
    const purchaseOrderLineAttachments =
      this.poData.Lines?.map(
        (line) =>
          line.attachments?.map(
            (attachment) =>
              ({
                id: attachment.id,
                fileId: attachment.fileId,
                fileName: attachment.fileName,
              }) as PurchaseOrderLineAttachment.PurchaseOrderLineAttachment
          ) || []
      ) || [];

    return {
      purchaseOrderHeader,
      purchaseOrderLines,
      purchaseOrderLineAttachments,
    };
  }

  private getCustomFlags(poHeader, mode) {
    const isAddRemovePurchaseLineDisabled =
      !poHeader?.approvalStatus &&
      poHeader?.approvalStatus === EvstPurchaseOrderApprovalStatus.Approved;
    const isSaveDraftButtonVisible =
      mode !== 'view' && poHeader?.$metadata.isDraft;
    const isDraft = poHeader?.$metadata.isDraft;
    const ifPurchaseOrderBudgetApproved =
      poHeader?.budgetStatus === EvstPurchaseOrderBudgetStatus.Approved;
    return {
      isAddRemovePurchaseLineDisabled,
      isSaveDraftButtonVisible,
      ifPurchaseOrderBudgetApproved,
      isDraft,
    };
  }
  private async getApprovalPolicyStatus(id, session) {
    const approvalPolicyHeaderClient =
      await ApprovalPolicyHeader.client(session);

    const documentType =
      GenericApprovalPolicy.ApprovalPolicyBusinessObject.PurchaseOrder;
    const documentId = id;
    const res: any = await approvalPolicyHeaderClient.getDocumentApprovalStatus(
      documentType,
      documentId
    );
    const status = res?.state;
    if (status) {
      status.userHasEmployeeRecord = res.userHasEmployeeRecord;
    }
    return status;
  }
  private getPurchaseOrderHeaderStatus(purchaseOrderHeader) {
    const statusMap = {
      approved: this.translationsCache.getTranslationByName(
        'purchaseOrders.status.approved'
      ),
      pendingApproval: 'purchaseOrders.status.pendingApproval',
      rejected: 'purchaseOrders.status.rejected',
    };
    const headerStatus = purchaseOrderHeader?.$metadata?.isDraft
      ? 'Draft'
      : statusMap[purchaseOrderHeader.approvalStatus];
    return headerStatus;
  }
}

export default {
  purchaseOrder: () => new PurchaseOrderDataSource(),
} satisfies purchaseOrderPresentation.implementation;
