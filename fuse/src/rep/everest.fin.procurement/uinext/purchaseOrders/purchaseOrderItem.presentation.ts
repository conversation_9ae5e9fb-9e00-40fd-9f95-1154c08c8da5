import { METADATA } from '@everestsystems/content-core';
import { PlainDate } from '@everestsystems/datetime';
import { Decimal } from '@everestsystems/decimal';
import type { EvstCurrencyAmount } from '@pkg/everest.base/types/composites/CurrencyAmount';
import { EvstUnitOfMeasureType } from '@pkg/everest.base/types/enums/UnitOfMeasureType';
import { EvstPurchaseOrderItemType } from '@pkg/everest.fin.procurement/types/enums/PurchaseOrderItemType';
import { EvstPurchaseOrderLineStatus } from '@pkg/everest.fin.procurement/types/enums/PurchaseOrderLineStatus';
import type { purchaseOrderPresentation } from '@pkg/everest.fin.procurement/types/presentations/uinext/purchaseOrders/purchaseOrder';
import type { purchaseOrderItemPresentation } from '@pkg/everest.fin.procurement/types/presentations/uinext/purchaseOrders/purchaseOrderItem';
import { calculateItemTotal } from '@pkg/everest.fin.procurement/utils/formula/total';
import TranslationsCache from '@pkg/everest.fin.procurement/utils/translationsUtils';
import { omit, set } from 'lodash';

interface PurchaseOrderItemData
  extends Partial<
    purchaseOrderItemPresentation.dataSources.purchaseOrderItem.levels['']
  > {
  purchaseOrderStartDate: PlainDate;
  purchaseOrderEndDate: PlainDate;
  attachments:
    | purchaseOrderItemPresentation.dataSources.purchaseOrderItem.levels['']['attachments']
    | undefined;
}

class PurchaseOrderItemDataSource
  implements
    purchaseOrderItemPresentation.dataSources.purchaseOrderItem.implementation
{
  private poItemData: PurchaseOrderItemData | undefined = undefined;
  private translationsCache = new TranslationsCache();

  public async query({ session, parameters }) {
    const lineData: purchaseOrderPresentation.dataSources.purchaseOrder.levels['Lines'] =
      parameters.lineData;
    const purchaseOrderStartDate = parameters.purchaseOrderStartDate;
    const purchaseOrderEndDate = parameters.purchaseOrderEndDate;
    await this.translationsCache.init({ session });

    if (this.poItemData === undefined) {
      if (lineData && Object.keys(lineData).length > 0) {
        const poLine = {
          lineUuid: lineData.lineUuid,
          id: lineData.id,
          itemName: lineData.itemName,
          itemId: lineData.itemId,
          status: lineData.status,
          unitOfMeasure: lineData.unitOfMeasure,
          quantityOrAmount: lineData.quantityOrAmount,
          quantity: lineData.quantity,
          price: lineData.price,
          total: lineData.total,
          poHeaderId: lineData.poHeaderId,
          estimatedDeliveryDate: lineData.estimatedDeliveryDate,
          startDate: lineData.startDate,
          endDate: lineData.endDate,
          departmentId: lineData.departmentId,
          deliveryDate: lineData.deliveryDate,
          description: lineData.description,
          purchaseOrderStartDate: purchaseOrderStartDate,
          purchaseOrderEndDate: purchaseOrderEndDate,
        };
        const attachments = lineData.attachments.map((attachment) => {
          return {
            id: attachment.id,
            fileName: attachment.fileName,
            fileId: attachment.fileId,
          };
        });

        this.poItemData = {
          ...poLine,
          attachments: attachments,
        };
      } else {
        this.poItemData = {
          purchaseOrderStartDate: purchaseOrderStartDate,
          purchaseOrderEndDate: purchaseOrderEndDate,
          total: { amount: new Decimal(0) },
          attachments: [],
        };
      }
    }
    return this.poItemData;
  }
  public async update({ fieldName, newFieldValue }) {
    // Calculate total if quantity or price is updated
    set(this.poItemData, [fieldName], newFieldValue);
    if (
      fieldName === 'quantityOrAmount' &&
      newFieldValue === EvstPurchaseOrderItemType.Amount
    ) {
      this.poItemData.quantity = new Decimal(1);
    }
    if (fieldName === 'quantity' || fieldName === 'price') {
      this.poItemData.total = {
        amount: calculateItemTotal(
          this.poItemData.price,
          this.poItemData.quantity
        ),
      } as EvstCurrencyAmount;
    }
  }

  public async validate_prepareLineDataToSave({ context }) {
    if (
      this.poItemData.startDate &&
      this.poItemData.purchaseOrderStartDate &&
      PlainDate.compare(
        this.poItemData.startDate,
        this.poItemData.purchaseOrderStartDate
      ) === -1
    ) {
      context.addError(
        [],
        this.translationsCache.getTranslationByName(
          'purchaseOrders.purchaseOrderItem.startDateGreater'
        ),
        'startDate'
      );
    }
    if (
      this.poItemData.endDate &&
      this.poItemData.purchaseOrderEndDate &&
      PlainDate.compare(
        this.poItemData.endDate,
        this.poItemData.purchaseOrderEndDate
      ) === 1
    ) {
      context.addError(
        [],
        this.translationsCache.getTranslationByName(
          'purchaseOrders.purchaseOrderItem.endDateGreater'
        ),
        'endDate'
      );
    }
    if (!this.poItemData.itemName) {
      context.addError(
        [],
        this.translationsCache.getTranslationByName('purchaseOrders.required'),
        'itemName'
      );
    }

    if (
      this.poItemData.quantityOrAmount === EvstPurchaseOrderItemType.Quantity &&
      !this.poItemData.unitOfMeasure
    ) {
      context.addError(
        [],
        this.translationsCache.getTranslationByName('purchaseOrders.required'),
        'unitOfMeasure'
      );
    }
    if (
      this.poItemData.quantityOrAmount === EvstPurchaseOrderItemType.Quantity &&
      !this.poItemData.quantity
    ) {
      context.addError(
        [],
        this.translationsCache.getTranslationByName('purchaseOrders.required'),
        'quantity'
      );
    }
    if (!this.poItemData.price) {
      context.addError(
        [],
        this.translationsCache.getTranslationByName('purchaseOrders.required'),
        'price'
      );
    }
  }
  public async execute_prepareLineDataToSave({ input: { attachments } }) {
    const data = {
      ...this.poItemData,
      unitOfMeasure:
        this.poItemData.unitOfMeasure ?? EvstUnitOfMeasureType.Each,
      status: EvstPurchaseOrderLineStatus.Undelivered,
      attachments: attachments.map((attachment) =>
        omit(attachment, ['downloadFileName'])
      ),
    } as purchaseOrderPresentation.dataSources.purchaseOrder.levels['Lines'];
    return { line: data };
  }
}

export default {
  purchaseOrderItem: () => new PurchaseOrderItemDataSource(),
} satisfies purchaseOrderItemPresentation.implementation;
