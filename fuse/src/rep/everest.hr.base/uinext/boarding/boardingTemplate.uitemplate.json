{"version": 2, "uicontroller": "boardingTemplate.uicontroller.ts", "uimodel": {"state": {"mode": "view"}, "nodes": {"BoardingTemplate": {"type": "struct", "modelId": "everest.hr.base/BoardingTemplateModel.BoardingTemplate", "query": "@controller:getBoardingTemplateQuery()", "fieldList": ["name", "type"]}, "OnboardingSteps": {"type": "list", "modelId": "everest.hr.base/BoardingStepsModel.BoardingSteps", "query": "@controller:getBoardingStepsQuery()", "fieldList": ["stage", "responsible"]}, "OffboardingSteps": {"type": "list", "modelId": "everest.hr.base/OffboardingStepsModel.OffboardingSteps", "query": "@controller:getBoardingStepsQuery()", "fieldList": ["stage", "responsible"]}}}, "uiview": {"layout": "everest.base.ui/layouts/uilayout/segmented", "props": {"tabTitle": "@binding:BoardingTemplate.name", "i18n": ["hrbase", "boarding"], "title": "@binding:BoardingTemplate.name", "status": "@binding:BoardingTemplate.type", "editing": {"title": "@controller:isEditMode()"}, "templateType": "generic", "background": "default", "customSections": [{"component": "H3", "content": "@controller:getBoardingStepsTitle()"}, {"component": "Table", "size": "12", "onRowClicked": "@controller:updateBoardingStep", "sectionActions": [{"label": "Add Boarding Step", "variant": "primary", "onClick": "@controller:updateOrCreateBoardingStep"}], "rowActions": {"pinned": "right", "actions": [{"content": "{{edit.step}}", "onClick": "@controller:updateOrCreateBoardingStep"}, {"content": "{{delete.step}}", "onClick": "@controller:deleteBoardingStep"}]}, "visible": "@controller:isOnboardingStepsVisible()", "columns": [{"field": "stage", "headerName": "{{name}}"}, {"field": "responsible", "headerName": "{{responsible}}"}], "data": "@binding:OnboardingSteps"}, {"component": "Table", "size": "12", "onRowClicked": "@controller:updateBoardingStep", "sectionActions": [{"label": "Add Boarding Step", "variant": "primary", "onClick": "@controller:updateOrCreateBoardingStep"}], "rowActions": {"pinned": "right", "actions": [{"content": "{{edit.step}}", "onClick": "@controller:updateOrCreateBoardingStep"}, {"content": "{{delete.step}}", "onClick": "@controller:deleteBoardingStep"}]}, "visible": "@controller:isOffboardingStepsVisible()", "columns": [{"field": "stage", "headerName": "{{name}}"}, {"field": "responsible", "headerName": "{{responsible}}"}], "data": "@binding:OffboardingSteps"}], "customActions": [{"variant": "primary", "label": "@controller:primaryButtonLabel()", "onClick": "@controller:primaryButtonAction"}, {"variant": "secondary", "label": "{{cancel}}", "onClick": "@controller:cancelEditing", "visible": "@controller:isEditMode()"}]}}}