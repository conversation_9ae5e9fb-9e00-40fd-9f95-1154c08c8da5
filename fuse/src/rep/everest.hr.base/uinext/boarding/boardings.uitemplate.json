{"version": 2, "uicontroller": "boarding.uicontroller.ts", "uimodel": {"state": {"mode": "view"}, "nodes": {"OnboardingJobs": {"type": "list", "loadBoardingJobs": {"where": {"onboardingStatus": "ongoing"}, "orderBy": [{"field": "startDate", "ordering": "asc"}]}, "fieldList": ["id", "labels"], "modelId": "everest.hr.base/JobModel.Job"}, "OffboardingJobs": {"type": "list", "loadBoardingJobs": {"where": {"offboardingStatus": "ongoing"}, "orderBy": [{"field": "endDate", "ordering": "asc"}]}, "fieldList": ["id", "labels"], "modelId": "everest.hr.base/JobModel.Job"}, "TagList": {"type": "list", "getLabelsForCurrentEmployee": {}, "modelId": "everest.hr.base/TagModel.Tag", "fieldList": ["tagTitle", "id", "tagColor", "visibleFlag"]}, "Tags": {"type": "list", "query": {}, "modelId": "everest.hr.base/TagModel.Tag", "fieldList": []}, "TagAssociation": {"type": "list", "query": "@controller:getTagNamesQuery()", "modelId": "everest.hr.base/TagAssociationModel.TagAssociation", "fieldList": []}}}, "uiview": {"layout": "everest.base.ui/layouts/uilayout/segmented", "props": {"i18n": ["hrbase", "boarding", "tags"], "autoRefreshData": true, "title": "{{on.off.boardings}}", "backgroundColor": "white", "stepsContent": [{"title": "{{onboardings}}", "components": [{"component": "Table", "sectionActions": [{"label": "{{create}}", "variant": "secondary", "onClick": "@controller:createBoarding"}, {"label": "{{actions}}", "variant": "primary", "actions": [{"label": "@controller:returnButtonLabel()", "onClick": "@controller:saveLabel"}, {"variant": "secondary", "label": "{{cancel.editing.labels}}", "onClick": "@controller:cancel", "visible": "@controller:stateLabelEdit()"}]}], "size": "12", "customId": "onboardingTable", "data": "@binding:OnboardingJobs", "node": "Jobs", "onRowClicked": "@controller:openEmployee", "columns": [{"headerName": "{{name}}", "field": "employeeName"}, {"headerName": "{{department}}", "field": "departmentName"}, {"headerName": "{{position}}", "field": "positionName"}, {"headerName": "{{manager}}", "field": "jobManagerDisplayName"}, {"headerName": "{{startDate}}", "field": "startDate", "fieldProps": {"parseAs": "date"}}, {"headerName": "{{boardingTemplate}}", "field": "Job-OnboardingTemplate.name"}, {"headerName": "{{labels}}", "field": "labels", "fieldProps": {"editing": "@controller:stateLabelEdit()", "bottomActions": [{"label": "{{create.new}}", "onClick": "@controller:navigateToLabelManagement"}]}}]}]}, {"title": "{{offboardings}}", "components": [{"component": "Table", "sectionActions": [{"label": "{{create}}", "variant": "secondary", "onClick": "@controller:createBoarding"}, {"label": "{{actions}}", "variant": "primary", "actions": [{"label": "@controller:returnButtonLabel()", "onClick": "@controller:saveLabel"}, {"variant": "secondary", "label": "{{cancel.editing.labels}}", "onClick": "@controller:cancel", "visible": "@controller:stateLabelEdit()"}]}], "size": "12", "customId": "onboardingTable", "data": "@binding:OffboardingJobs", "node": "Jobs", "onRowClicked": "@controller:openEmployee", "columns": [{"headerName": "{{name}}", "field": "employeeName"}, {"headerName": "{{department}}", "field": "departmentName"}, {"headerName": "{{position}}", "field": "positionName"}, {"headerName": "{{manager}}", "field": "jobManagerDisplayName"}, {"headerName": "{{endDate}}", "field": "endDate", "fieldProps": {"parseAs": "date"}}, {"headerName": "{{boardingTemplate}}", "field": "Job-OffboardingTemplate.name"}, {"headerName": "{{labels}}", "field": "labels", "fieldProps": {"editing": "@controller:stateLabelEdit()", "bottomActions": [{"label": "{{create.new}}", "onClick": "@controller:navigateToLabelManagement"}]}}]}]}]}}}