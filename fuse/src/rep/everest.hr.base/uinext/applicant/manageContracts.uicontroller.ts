// @i18n:everest.base/contractTemplates
import type { ManageContractsUiTemplate } from '@pkg/everest.hr.base/types/uiTemplates/uinext/applicant/manageContracts.ui';

type ManageContractsContext = ManageContractsUiTemplate.ManageContractsContext;

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type Any = any;

type Context = ManageContractsContext & {
  state: {
    // you can extend and improve the state type here
    id: Any;
  };
};

export function addContract(context: Context) {
  const { helpers, actions } = context;
  helpers.openModal({
    title: '{{contractTemplate.addModal.title}}',
    size: 'xsmall',
    template: `/templates/everest.base/uinext/createPDFTemplate?action=create`,
    onClose: actions.refetchUiModelData,
  });
}

export function getPDFTemplateItemData(context: Context) {
  const { data } = context;
  const PDFTemplates = data?.PDFTemplate;
  return PDFTemplates
    ? PDFTemplates.map((el) =>
        el.isActive ? { ...el, isActive: 'Yes' } : { ...el, isActive: 'No' }
      )
    : [];
}

export function editPDFTemplateItem(
  context: Context,
  row: { original: { id: string } }
) {
  const { actions, helpers, sandbox } = context;

  helpers.openModal({
    title: '{{contractTemplate.editModal.title}}',
    size: 'xsmall',
    template: `/templates/everest.base/uinext/createPDFTemplate?PDFTemplateId=${
      row.original.id
    }&action=${isSandboxActivated({ sandbox }) ? 'update' : 'view'}`,
    onClose: actions.refetchUiModelData,
  });
}

export async function deleteRow(context: Context, row: Any) {
  const { sandbox, helpers, actions } = context;
  if (!isSandboxActivated({ sandbox })) {
    return helpers.showToast({
      title: 'Denied',
      message: 'Impossible to delete. Activate a sandbox first.',
      type: 'warning',
    });
  }
  const model = `${row}:PDFTemplate`;
  await actions.submit({
    onSuccess: async () => {
      helpers.showToast({
        title: 'Delete',
        message: 'Successfully deleted data.',
        type: 'success',
      });
    },
    onError: () => {
      helpers.showToast({
        title: 'Error',
        message: 'Data not deleted.',
        type: 'error',
      });
    },
    transform: (formValues) => {
      const id = formValues[model]?.update?.id;
      return {
        PDFTemplate: {
          absoluteBOName: 'everest.base/PDFTemplateModel',
          deletePDFTemplate: { id },
        },
      };
    },
  });
}

function isSandboxActivated({ sandbox }) {
  return sandbox.enabled;
}
