{"version": 2, "uicontroller": "manageContracts.uicontroller.ts", "uimodel": {"state": {"mode": "view"}, "nodes": {"PDFTemplate": {"type": "list", "modelId": "everest.base/PDFTemplateModel.PDFTemplate", "query": {}, "fieldList": ["pdfTemplateNumber", "templateFileName", "templateFileId", "attachedTo", "isActive"]}}}, "uiview": {"templateType": "details", "tabTitle": "Manage Contracts", "i18n": "everest.base/contractTemplates", "grid": {"limited": true}, "autoRefreshData": true, "sections": [{"component": "Template", "link": "/templates/everest.fin.accounting/ui/genericHeader", "initialState": {"title": "{{contractTemplate.title}}"}}, {"component": "ButtonGroup", "actions": [{"variant": "primary", "label": "{{contractTemplate.addButton}}", "onClick": "@controller:addContract"}]}, {"component": "SecondaryTable", "margin": {"bottom": "46px"}, "size": "12", "absoluteBOName": "everest.fin.expense/PDFTemplateModel", "title": "{{contractTemplate.title}}", "columns": [{"Header": "{{contractTemplate.table.title}}", "dataBinding": ["pdfTemplateNumber"], "isSortable": true}, {"Header": "{{contractTemplate.table.name}}", "dataBinding": ["templateFileName"]}, {"Header": "{{contractTemplate.table.isActive}}", "dataBinding": ["isActive"]}, {"Header": "{{contractTemplate.table.attachedTo}}", "dataBinding": ["attachedTo"]}], "rowConfirmAction": {"label": "{{contractTemplate.table.delete}}", "icon": "trash", "content": {"icon": "trash", "text": "{{contractTemplate.table.delete.confirmation.text}}"}, "confirmAction": {"label": "{{contractTemplate.table.delete.confirmation.confirm}}", "onClick": "@controller:deleteRow"}, "cancelAction": {"label": "{{contractTemplate.table.delete.confirmation.cancel}}"}}, "data": "@bindingController(PDFTemplate, getPDFTemplateItemData())", "onRowClick": "@controller:editPDFTemplateItem"}]}}