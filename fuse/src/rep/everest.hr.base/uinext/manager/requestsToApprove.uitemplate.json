{"version": 2, "uicontroller": ["requestsToApprove.uicontroller.ts", "everest.hr.base/uinext/leaveRequest/leaveRequest.uicontroller.ts", "everest.hr.base/uinext/shared/helpers.uicontroller.js", "everest.fin.accounting/uinext/journalEntries.uicontroller.ts"], "uimodel": {"state": {"selectedTimesheetIds": [], "viewType": 0, "button": true}, "nodes": {"Employee": {"type": "struct", "getCurrentEmployee": {}, "modelId": "everest.hr.base/EmployeeModel.Employee"}, "Leaves": {"type": "list", "query": "@controller:getAllLeaves()", "modelId": "everest.hr.base/LeaveModel.Leave", "fieldList": ["absenceType", "days", "employeeId", "Leave-Employee.name", "Leave-Employee.displayName", "Leave-Employee.primaryJobManagerName", "endDate", "id", "leaveDescription", "startDate", "status"]}, "Users": {"type": "struct", "modelId": "everest.appserver.usermgmt/UserModel.User"}, "Notification": {"type": "struct", "modelId": "everest.appserver/NotificationModel.Notification"}, "ownEmployees": {"type": "list", "modelId": "everest.hr.base/EmployeeModel.Employee", "loadOwnEmployees": {"where": {}, "fieldlist": ["id", "name", "displayName", "email", "status", "category", "locationId", "employeeNumber"]}}, "LeaveStatus": {"type": "list", "query": {"where": {"dataType": "LeaveStatus", "language": "@controller:getLanguage()"}}, "modelId": "everest.appserver/ValueHelpModel.ValueHelp", "fieldList": ["text", "codeValue"]}, "LeaveTypes": {"type": "list", "query": {"where": {"dataType": "LeaveType", "language": "@controller:getLanguage()"}}, "modelId": "everest.appserver/ValueHelpModel.ValueHelp", "fieldList": ["text", "codeValue"]}, "expenseEmployees": {"type": "list", "modelId": "everest.hr.base/EmployeeModel.Employee", "loadEmployeesForHierarchy": {"where": {}, "fieldList": ["id", "name", "displayName"]}}, "ExpenseReportBase": {"type": "list", "modelId": "everest.fin.expense/ExpenseReportBaseModel.ExpenseReportBase", "fieldList": []}, "vendorBillsToReview": {"type": "list", "getAllVendorBillsToReview": {}, "modelId": "everest.fin.expense/VendorBillHeaderBaseModel.VendorBillHeaderBase", "fieldList": ["id", "vendorId", "bill<PERSON><PERSON><PERSON>", "entityId", "amountPaid", "currency", "billDate", "paymentStatus", "postingStatus", "containsPrepaidItem", "dueDate", "journalEntryNumber", "journalEntryId", "total", "status", "origin", "vendorBillHeaderNumber", "migrationConfigurationMode"]}, "expenseReportsToReview": {"type": "list", "getAllExpenseReportsToReview": {}, "modelId": "everest.fin.expense/ExpenseReportBaseModel.ExpenseReportBase", "fieldList": ["id", "employeeName", "expenseNumber", "submittedDate", "status", "paymentStatus", "entityId", "origin", "reimbursementCurrency", "reimbursableTotal", "migrationConfigurationMode"]}, "vendorBill": {"type": "list", "modelId": "everest.fin.expense/VendorBillHeaderBaseModel.VendorBillHeaderBase", "fieldList": []}, "Timesheets": {"type": "list", "query": "@controller:getTimesheetQuery()", "modelId": "everest.hr.base/TimesheetModel.Timesheet", "fieldList": ["id", "employeeId", "month", "year", "timesheetPolicyId", "timesheetPolicyStatus", "timesheetStatus", "totalBreakTime", "totalWorkDays", "totalWorkTime", "Timesheet-Employee.firstName", "Timesheet-Employee.lastName", "Timesheet-Employee.preferredFirstName", "Timesheet-Employee.preferredLastName", "Timesheet-Employee.displayName", "Timesheet-TimesheetPolicy.name"]}, "canReviewExpense": {"type": "struct", "modelId": "everest.appserver/permission/PolicyModel.Policy", "checkPermission": {"resource": "everest.fin.expense/ExpenseReportBaseModel.ExpenseReportBase", "action": "queryExpenseToReview", "checkConditions": true}}, "EmployeeAttachments": {"type": "list", "query": "@controller:getAllDocuments()", "modelId": "everest.hr.base/EmployeeAttachmentModel.EmployeeAttachment", "fieldList": ["id", "fileId", "fileName", "employeeId", "expiryDate", "status", "fileType", "employeeDocumentTypeLabel", "EmployeeAttachment-Employee.name", "EmployeeAttachment-Employee.displayName"]}, "NotificationCategory": {"type": "list", "modelId": "everest.appserver/NotificationCategoryModel.NotificationCategory"}, "Leave": {"type": "struct", "modelId": "everest.hr.base/LeaveModel.Leave"}, "isAdmin": {"type": "struct", "modelId": "everest.hr.base/EmployeeModel.Employee", "isAdmin": {}}, "canReviewJournalEntries": {"type": "struct", "modelId": "everest.appserver/permission/PolicyModel.Policy", "checkPermission": {"resource": "everest.fin.accounting/JournalEntryHeaderModel.JournalEntryHeader", "action": "getAllJournalEntriesToReview", "checkConditions": true}}, "journalEntriesToReview": {"type": "list", "getAllJournalEntriesToReview": "@controller:canReviewJournalEntries()", "modelId": "everest.fin.accounting/JournalEntryHeaderModel.JournalEntryHeader", "fieldList": []}, "approvalPolicyHeader": {"type": "struct", "modelId": "everest.base.approvals/ApprovalPolicyHeaderModel.ApprovalPolicyHeader", "fieldList": []}}}, "uiview": {"templateType": "details", "i18n": ["hrbase", "manager", "leaveRequest", "hrDashboard", "everest.fin.accounting/accounting"], "filePreviewId": "@controller:getDocumentFileId()", "title": "{{requests.to.approve}}", "config": {"stretch": true, "allowRefreshData": true, "autoRefreshData": true, "grid": {"limited": true}}, "header": {"size": "8", "content": {"title": "{{requests.to.approve}}"}, "keyDataConfig": {"idProp": "id", "textProp": "text", "data": "@controller:getViewData()", "value": "@controller:getCurrentView()", "onValueChange": "@controller:setView", "visible": "@controller:showViewToggle()"}}, "steps": {"variant": "default", "background": "grey", "content": [{"title": "{{absences}}", "badge": "@controller:getAbsenceCount()", "components": [{"component": "Table", "title": "{{absences}}", "customId": "requestsToApproveAbsencesTable", "size": "12", "data": "@binding:Leaves", "variant": "white-borderless", "rowSelection": true, "onRowSelectionChanged": "@controller:enableButton", "sectionActions": [{"label": "{{approve}}", "onClick": "@controller:approve<PERSON><PERSON>ves", "disabled": "@state:button", "variant": "primary"}, {"label": "{{reject}}", "onClick": "@controller:reject<PERSON><PERSON>ves", "disabled": "@state:button"}], "empty": {"variant": "primary-table"}, "columns": [{"headerName": "{{employee}}", "field": "Leave-Employee.displayName"}, {"field": "absenceType", "fieldProps": {"fontWeight": "bold"}, "headerName": "{{type}}", "valueGetter": "@controller:getAbsenceType"}, {"field": "startDate", "headerName": "{{startDate}}"}, {"field": "endDate", "headerName": "{{endDate}}"}, {"field": "days", "headerName": "{{work.days.requested}}"}, {"field": "Leave-Employee.primaryJobManagerName", "headerName": "{{manager}}"}, {"headerName": "{{notes}}", "field": "leaveDescription"}], "onRowClicked": "@controller:openLeaveRequest"}]}, {"title": "{{expenses}}", "badge": "@controller:getExpenseCount()", "visible": "@binding:canReviewExpense", "components": [{"component": "Table", "customId": "requestsToApproveExpensesTable", "title": "{{expenses}}", "size": "12", "variant": "white-borderless", "rowSelection": true, "onRowSelectionChanged": "@controller:onExpenseRowSelectionChanged", "data": "@binding:expenseReportsToReview", "sectionActions": [{"label": "{{approve}}", "onClick": "@controller:approveExpense", "variant": "primary", "disabled": "@controller:expensesActionsDisabledChecker()"}, {"label": "{{reject}}", "onClick": "@controller:rejectExpense", "variant": "secondary", "disabled": "@controller:expensesActionsDisabledChecker()"}], "columns": [{"headerName": "{{employeeExpense.number}}", "field": "expenseNumber", "fieldProps": {"fontWeight": "bold"}}, {"headerName": "{{employeeExpense.name}}", "field": "employeeName"}, {"headerName": "{{currency}}", "field": "reimbursementCurrency"}, {"headerName": "{{employeeExpense.amount}}", "field": "reimbursableTotal", "fieldProps": {"parseAs": "currency"}}, {"headerName": "{{date}}", "field": "submittedDate", "type": "date"}], "onRowClicked": "@controller:getExpenseDetails"}]}, {"title": "{{vendor.bills}}", "badge": "@controller:getBillCount()", "components": [{"component": "Table", "customId": "requestsToApproveVendorBillsTable", "title": "{{vendor.bills}}", "size": "12", "variant": "white-borderless", "rowSelection": true, "onRowSelectionChanged": "@controller:onVendorBillsRowSelectionChanged", "data": "@binding:vendorBillsToReview", "sectionActions": [{"label": "{{approve}}", "onClick": "@controller:approveBill", "variant": "primary", "disabled": "@controller:vendorBillsActionsDisabledChecker()"}, {"label": "{{reject}}", "onClick": "@controller:rejectBill", "variant": "secondary", "disabled": "@controller:vendorBillsActionsDisabledChecker()"}], "columns": [{"headerName": "{{vendor.number}}", "field": "vendorBillHeaderNumber"}, {"headerName": "{{vendor.name}}", "field": "vendorName"}, {"headerName": "{{vendor.bill.number}}", "field": "bill<PERSON><PERSON><PERSON>"}, {"headerName": "{{currency}}", "field": "currency"}, {"headerName": "{{amount}}", "field": "total", "fieldProps": {"parseAs": "currency"}}, {"headerName": "{{vendor.bill.date}}", "field": "billDate", "type": "date"}, {"headerName": "{{vendor.due.date}}", "field": "dueDate", "type": "date"}], "onRowClicked": "@controller:getVendorBillDetails"}]}, {"title": "{{time.tracking}}", "badge": "@controller:getTimesheetCount()", "components": [{"component": "Table", "customId": "requestsToApproveTimeSheetTable", "title": "{{time.tracking}}", "rowSelection": true, "onRowSelectionChanged": "@controller:onSelectTimesheetRequest", "fullWidth": true, "variant": "white-borderless", "size": 12, "data": "@binding:Timesheets", "onRowClicked": "@controller:onTimesheetRowClick", "sectionActions": [{"label": "{{approve}}", "variant": "primary", "onClick": "@controller:approveSelectedTimesheets", "disabled": "@controller:checkTimeTracking()"}, {"label": "{{reject}}", "variant": "secondary", "onClick": "@controller:rejectSelectedTimesheets", "disabled": "@controller:checkTimeTracking()"}], "columns": [{"headerName": "{{employee}}", "field": "Timesheet-Employee.displayName", "valueGetter": "@controller:getEmployeeName"}, {"headerName": "{{timesheetPolicy}}", "field": "Timesheet-TimesheetPolicy.name"}, {"headerName": "{{period}}", "field": "period", "valueGetter": "@controller:getPeriodText", "suppressSort": true}, {"headerName": "{{policyStatus}}", "field": "timesheetPolicyStatus", "cellVariant": {"variant": "badge", "matchers": "@controller:getPolicyStatusBadgeBgColor()"}}, {"headerName": "{{totalWorkDays}}", "field": "totalWorkDays"}, {"headerName": "{{totalWorkTime}}", "field": "totalWorkTime", "valueGetter": "@controller:getTotalWorkTimeText"}, {"headerName": "{{totalBreakTime}}", "field": "totalBreakTime", "valueGetter": "@controller:getTotalBreakTimeText"}]}]}, {"title": "{{documents}}", "badge": "@controller:getDocumentCount()", "components": [{"component": "Table", "customId": "requestsToApproveDocumentsTable", "title": "{{documents}}", "variant": "white-borderless", "size": "12", "rowSelection": true, "onRowSelectionChanged": "@controller:enableDocumentsButton", "sectionActions": [{"label": "{{approve}}", "onClick": "@controller:approveDocument", "disabled": "@state:button", "variant": "primary"}, {"label": "{{reject}}", "onClick": "@controller:rejectDocument", "disabled": "@state:button"}, {"label": "{{view}}", "onClick": "@controller:viewDocument", "disabled": "@state:button"}], "columns": [{"headerName": "{{employee.name}}", "field": "EmployeeAttachment-Employee.displayName", "isSortable": true}, {"headerName": "{{type}}", "field": "employeeDocumentTypeLabel", "isSortable": true}, {"headerName": "{{file.name}}", "field": "fileName", "isSortable": true, "onCellClicked": "@controller:downloadDocument"}, {"headerName": "{{expiryDate}}", "field": "expiryDate", "isSortable": true, "parseAs": "date"}, {"headerName": "{{status}}", "field": "status", "isSortable": true}], "data": "@binding:EmployeeAttachments"}]}, {"visible": "@binding:canReviewJournalEntries", "title": "{{journalEntries.title}}", "badge": "@controller:getJECount()", "components": [{"component": "Table", "customId": "requestsToApproveJournalEntriesTable", "title": "{{journalEntries.title}}", "variant": "white-borderless", "size": "12", "rowSelection": true, "onRowSelectionChanged": "@controller:enableJournalEntriesButton", "fullWidth": true, "data": "@binding:journalEntriesToReview", "onRowClicked": "@controller:getRowNavigation", "sectionActions": [{"label": "{{approve}}", "onClick": "@controller:approveSelectedJournalEntries", "disabled": "@controller:checkJournalEntries()", "variant": "primary"}, {"label": "{{reject}}", "onClick": "@controller:rejectSelectedJournalEntries", "disabled": "@controller:checkJournalEntries()"}], "columns": [{"headerName": "{{journalEntries.journalEntryNumber}}", "field": "journalEntryNumber", "fieldProps": {"fontWeight": "medium", "nullContent": "custom", "customNullContent": "Draft"}}, "journalEntryName", "description", "type", "postingDate", {"field": "journalEntryStatus", "valueGetter": "@controller:journalEntryStatusValueGetter", "fieldProps": {"nullContent": "dash"}, "cellVariant": {"variant": "badge", "matchers": {"Posted": "success", "Unposted": "mystic-grey", "default": "info"}}}, {"field": "approvalStatus", "valueGetter": "@controller:approvalStatusValueGetter", "fieldProps": {"nullContent": "dash"}, "cellVariant": {"variant": "badge", "matchers": {"Approved": "success", "Approved With Changes": "success", "Pending Approval": "jaffa", "Pending Submission": "jaffa", "Rejected": "danger", "default": "info"}}}, "entities"]}]}]}}}