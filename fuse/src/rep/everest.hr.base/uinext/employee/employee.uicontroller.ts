// @i18n:boarding
// @i18n:hrbase
// @i18n:employee
// @i18n:manager
// @i18n:leaveRequest
// @i18n:everest.base.encryption/encryption
// @i18n:applicant
// @i18n:requestDocument

import { UILifecycleHooks } from '@everestsystems/content-core';
import { PlainDate } from '@everestsystems/datetime';
import { Decimal } from '@everestsystems/decimal';
import type { UiChart } from '@pkg/everest.analytics/public/formats';
import { getUserPrivateKeyBase64 } from '@pkg/everest.base.encryption/public/utils.uicontroller';
import { EvstRequestStatus } from '@pkg/everest.base.encryption/types/enums/RequestStatus';
import type { Address } from '@pkg/everest.base/types/Address';
import { Bonus } from '@pkg/everest.hr.base/types/Bonus';
import type { Employee } from '@pkg/everest.hr.base/types/Employee';
import type { EmployeeAttachment } from '@pkg/everest.hr.base/types/EmployeeAttachment';
import { EvstCalendarUnit } from '@pkg/everest.hr.base/types/enums/CalendarUnit';
import { EvstEmployeeBoardingStatus } from '@pkg/everest.hr.base/types/enums/EmployeeBoardingStatus';
import { EvstEmployeeStatus } from '@pkg/everest.hr.base/types/enums/EmployeeStatus';
import { EvstLeaveOverlap } from '@pkg/everest.hr.base/types/enums/LeaveOverlap';
import { EvstLeaveStatus } from '@pkg/everest.hr.base/types/enums/LeaveStatus';
import { EvstLeaveType } from '@pkg/everest.hr.base/types/enums/LeaveType';
import { EvstRequestedDocumentsStage } from '@pkg/everest.hr.base/types/enums/RequestedDocumentsStage';
import { EvstStageNodeType } from '@pkg/everest.hr.base/types/enums/StageNodeType';
import { EvstTimelineStatus } from '@pkg/everest.hr.base/types/enums/TimelineStatus';
import type { Job } from '@pkg/everest.hr.base/types/Job';
import { Salary } from '@pkg/everest.hr.base/types/Salary';
import type { EmployeeUiTemplate } from '@pkg/everest.hr.base/types/uiTemplates/uinext/employee/employee.ui';
import { toPlainDate } from '@pkg/everest.hr.base/utils/Date/date';
import { isFunction, omit } from 'lodash';

type EmployeeContext = EmployeeUiTemplate.EmployeeContext;
type EncryptedNode = EmployeeUiTemplate.EncryptedNode;

// @i18n:everest.base.encryption/encryption
// eslint-disable-next-line @typescript-eslint/no-explicit-any
type Any = any;
type CompensationReportTimeRange = 'allTime' | 'last12Months';
export type Context = EmployeeContext & {
  data: {
    CompensationCube?: {
      data: UiChart.ChartData;
    };
  };
  state: {
    // you can extend and improve the state type here
    mode: 'edit' | 'view';
    editOwnProfile: boolean;
    activeSegment: number;
    formAddress: Partial<Address.Address>;
    form: {
      vacationPolicyId: number;
      sickPolicyId: number;
    };
    adjustmentDays: {
      year: number;
      value: number;
    };
    id: number;
    photoIsLoading: boolean;
    filter: 0 | 1;
    startDate: Date;
    button: boolean;
    leaveId: {
      id: number;
      mandatoryLeaveId: number | undefined;
      status: EvstLeaveStatus;
    }[];
    documentFileId: string;
    salary: 'edit' | 'view';
    year: number;
    disableAdjustment: boolean;
    boardingType: 'onboarding' | 'offboarding' | false;
    compensationReportTimeRange: CompensationReportTimeRange;
    today: Date;
    probationEditMode: 'default' | 'custom';
    probationEndDateRemoved: boolean;
    timesheetYear: number;
    absenceYears: number[];
  };
};

type DocumentRowType = { data: Partial<EmployeeAttachment.EmployeeAttachment> };

type BonusRowType = {
  data: Partial<Bonus.BonusWithAssociation>;
};

UILifecycleHooks.onInit((context: Context) => {
  const { sharedState, state } = context;
  sharedState.setFilters({
    DirectReports: {
      status: {
        $in: ['active', 'planned'],
      },
    },
  });
  state.absenceYears = getYears(context);
});

export function returnEdit(context: Context) {
  const { state, data } = context;

  return state?.mode === 'edit' && data?.isAdmin;
}

export function returnView(context: Context) {
  const { state } = context;

  return state?.mode === 'view';
}

export function stateModeEdit(context: Context) {
  const { state } = context;

  return state?.mode === 'edit';
}

export function returnViewAddress(context: Context) {
  const { state } = context;
  return state?.mode !== 'edit';
}

export function returnOwnEdit(context: Context) {
  const { state, data } = context;

  state.editOwnProfile = isCurrentEmployee(context);
  return state?.mode === 'edit' && (state?.editOwnProfile || data?.isAdmin);
}

export function showEditButton(context: Context) {
  const { data, state } = context;

  return (
    (data?.isCurrentEmployee || data?.isAdmin) && state?.activeSegment <= 3
  );
}

export function showCancelButton(context: Context) {
  const { state } = context;

  const edit = returnEdit(context);
  const editOwn = returnOwnEdit(context);
  return (edit || editOwn) && state?.activeSegment !== 5;
}

export function getAddress(context: Context) {
  const { data, state } = context;

  const { Address } = data;
  if (!state.formAddress) {
    state.formAddress = Address?.id ? Address : null;
  }
  const address = state.formAddress;
  if (address) {
    if (state.mode === 'view') {
      return `${address?.line1}\n${
        address?.line2 ? `${address.line2}, ` : ''
      }${address?.city}, ${
        address?.stateProvince ? `${address.stateProvince} ` : ''
      }${address?.zipCode}\n${address?.country}`;
    }
    return `${address?.line1}, ${
      address?.line2 ? `${address.line2}, ` : ''
    }${address?.city}, ${
      address?.stateProvince ? `${address.stateProvince},` : ''
    }${address?.zipCode}, ${address?.country}`;
  }
  return '- -';
}

export function stateView(context: Context) {
  const { state } = context;

  return state?.mode !== 'edit';
}

export function getLocation(context: Context) {
  const { data } = context;

  const locId = data?.Employee?.locationId;
  const loc = data?.Locations;
  if (locId) {
    return loc?.find((l: Any) => l?.id === locId)?.locationName;
  }
}

export function endDateVisible(context: Context) {
  const { data } = context;

  const endDate = data?.Employee?.endDate;
  return endDate || returnEdit(context) ? true : false;
}

export function getEntity(context: Context) {
  const { data } = context;

  const entId = data?.Employee?.entityId;
  const ent = data?.Entities;
  if (entId) {
    return ent?.find((entity: Any) => entity?.id === entId)?.entityName;
  }
}

export function getAccountDetailsLabel(context: Context) {
  const { data } = context;

  const bankAccountNumberType = data?.BankDetails?.bankAccountNumberType;
  switch (bankAccountNumberType) {
    case 'iban': {
      return '{{iban}}';
    }
    case 'achOrWire': {
      return '{{bankDetails.accountNumber}}';
    }
    case 'other': {
      return '{{bankDetails.accountDetails}}';
    }
    default: {
      return '{{iban}}';
    }
  }
}

export function getPaymentDetailsLabel(context: Context) {
  const { data } = context;

  const bankAccountNumberType = data?.BankDetails?.bankAccountNumberType;
  switch (bankAccountNumberType) {
    case 'iban': {
      return '{{bankDetails.ibanDetails}}';
    }
    case 'achOrWire': {
      return '{{bankDetails.achDetails}}';
    }
    case 'other': {
      return '{{bankDetails.accountDetails}}';
    }
    default: {
      return '';
    }
  }
}

export function getPaymentDetailsValue(context: Context) {
  const { data } = context;

  const bankDetails = data?.BankDetails;
  const bankAccountNumberType = bankDetails?.bankAccountNumberType;
  // Format properly bank account details in view mode
  switch (bankAccountNumberType) {
    case 'iban': {
      return [
        bankDetails.bankAccountHolderName,
        `{{iban}}: ${bankDetails.bankAccountNumber}`,
      ].join(`\n`);
    }
    case 'achOrWire': {
      const financialInstitutionId =
        bankDetails?.financialInstitutionId ?? '- -';
      return [
        bankDetails.bankAccountHolderName,
        `{{bankDetails.routingNumber}}: ${financialInstitutionId}`,
        `{{bankDetails.accountNumber}}: ${bankDetails.bankAccountNumber}`,
      ].join(`\n`);
    }
    case 'other': {
      return [bankDetails.bankAccountHolderName, bankDetails.bankAccountNumber]
        .filter(Boolean)
        .join(`\n`);
    }
    default: {
      return '';
    }
  }
}

export function isPaymentMethod(context: Context, method: Any) {
  const { data } = context;

  const bankAccountNumberType = data?.BankDetails?.bankAccountNumberType;
  return bankAccountNumberType === method;
}

export function getAccountHolderName(context: Context) {
  const { sharedState } = context;

  const person = sharedState?.getModelData('Person')?.instance?.data;
  const bankDetails = sharedState?.getModelData('BankDetails')?.instance?.data;
  const accountHolderName = bankDetails?.bankAccountHolderName;
  const { firstName, lastName } = person ?? {};
  if (returnEdit(context)) {
    return accountHolderName ?? `${firstName} ${lastName}`;
  }
  return accountHolderName;
}

export function setView(context: Context) {
  const { state, sharedState } = context;

  state.formAddress = undefined;
  state.mode = 'view';
  sharedState.reset(['Employee', 'Person', 'BankDetails']);
}

export function buttonEdit(context: Context) {
  const { state } = context;

  if (state?.mode === 'edit') {
    return '{{save}}';
  }
  return '{{edit.details}}';
}

export async function onSaveClick(context: Context) {
  const { state, sharedState, helpers, data } = context;
  const { editOwnProfile, mode } = state;
  const { CurrentPrimaryJob } = data;
  if (mode === 'edit') {
    const fields = sharedState?.getChangedFields();
    const currentJobStatus = CurrentPrimaryJob?.status;
    const employeeEndDate =
      fields?.Employee?.['instance']?.['data']?.['endDate'];
    const employeeProbationEndDate =
      fields?.Employee?.['instance']?.['data']?.['probationEndDate'];
    if (employeeProbationEndDate) {
      const originalProbationEndDate = employeeProbationEndDate?.original;
      const currentProbationEndDate = employeeProbationEndDate?.current;
      if (currentProbationEndDate === undefined && originalProbationEndDate) {
        state.probationEndDateRemoved = true;
      }
    }
    if (employeeEndDate) {
      const originalEndDate = employeeEndDate?.original;
      const currentEndDate = employeeEndDate?.current;
      const formattedCurrentEndDate = new Date(currentEndDate).toISOString();
      const formattedEndDate = new Date(currentEndDate)?.toLocaleDateString(
        'en-US',
        {
          month: '2-digit',
          day: '2-digit',
          year: 'numeric',
        }
      );
      // eslint-disable-next-line unicorn/no-negated-condition
      if (
        originalEndDate !== formattedCurrentEndDate &&
        originalEndDate < formattedCurrentEndDate &&
        currentJobStatus === EvstTimelineStatus.Active
      ) {
        await helpers.openDialog({
          variant: 'warning',
          title: 'Do you want to extend the active jobs?',
          message: `The employee end date is changed to ${formattedEndDate}. Do you want to extend the end date of the active jobs also?`,
          actions: {
            secondary: {
              label: '{{do.not.extend}}',
              onClick: async () => {
                helpers.closeModal();
                await (editOwnProfile
                  ? editMyProfile(context)
                  : editEmployeeProfile(context));
              },
            },
            primary: {
              label: '{{confirm.extension}}',
              onClick: async () => {
                helpers.closeModal();
                await (editOwnProfile
                  ? editMyProfile(context)
                  : editEmployeeProfile(context, currentEndDate));
              },
            },
          },
        });
      } else {
        await (editOwnProfile
          ? editMyProfile(context)
          : editEmployeeProfile(context));
      }
    } else {
      await (editOwnProfile
        ? editMyProfile(context)
        : editEmployeeProfile(context));
    }
  } else {
    state.mode = 'edit';
    state.formAddress = undefined;
    if (editOwnProfile) {
      state.activeSegment = 1;
    }
  }
}

export async function editEmployeeProfile(
  context: Context,
  currentEndDate?: Date
) {
  const { actions, state, data, sharedState, helpers } = context;
  const {
    Employee,
    BankDetails,
    TimesheetPolicy,
    AbsencePolicyEmployeeVacation,
    hasVacationPolicy,
    CurrentPrimaryJob,
  } = data;
  await actions.submit({
    onSuccess: async () => {
      await actions.refetchUiModelData();
      state.mode = 'view';
    },
    onError: async (error) => {
      if (
        error.error.message.includes(
          'Cannot change category to External as employee has Internal direct reports'
        )
      ) {
        state.categoryError = 'Invalid';
      }
    },
    transform: () => {
      const addedMgrJobs = new Set<Job.Job>();
      const address = state?.formAddress ?? {};
      const jobData = sharedState.getModelData('EmployeeJob')?.instance;
      for (const job of jobData) {
        addedMgrJobs.add(job?.data?.jobManager);
        if (job.action === 'create') {
          job.data.employeeId = Employee?.id;
        }
      }
      const userAssignments = [];
      const addedMgrIds = [];
      for (const job of addedMgrJobs) {
        const endDate = job?.endDate;
        addedMgrIds.push({
          id: job?.employeeId,
        });
        userAssignments.push({
          validFrom: new Date(),
          ...(endDate && { validTo: new Date(endDate) }),
          relatedNodeUUID: 'd9507ce2-0357-48b2-bb62-005838b4a661',
        });
      }
      const keysToExclude = new Set([
        '_nodeReference',
        'departments',
        'photo',
        'Employee-Entity',
      ]);
      let employeeData: Partial<Employee.EmployeeWithAssociation> =
        helpers.pick(
          Employee,
          Object.keys(Employee).filter((k) => !keysToExclude.has(k))
        );
      let personData = sharedState.getModelData('Person')?.instance?.data;
      personData = {
        ...personData,
        email: personData?.email ?? null,
      };
      if (personData._nodeReference) {
        delete personData._nodeReference;
      }
      if (personData._nodeParentReference) {
        delete personData._nodeParentReference;
      }
      const holidayCalendar = employeeData?.holidayCalendar ?? null;
      const bankFormValues =
        sharedState.getModelData(BankDetails)?.instance?.data;
      if (bankFormValues._nodeReference) {
        delete bankFormValues._nodeReference;
      }
      if (bankFormValues._nodeParentReference) {
        delete bankFormValues._nodeParentReference;
      }
      if (bankFormValues?.bankAccountNumberType === undefined) {
        bankFormValues.bankAccountNumberType = null;
      }
      if (bankFormValues?.financialInstitutionIdType === undefined) {
        bankFormValues.financialInstitutionIdType = null;
      }
      const adjustmentDays = state.adjustmentDays ?? null;
      const policy = hasVacationPolicy
        ? getEmployeeVacationPolicy(context)
        : getPlannedPolicy(context) ?? undefined;
      if (policy?.grantingType === 'byAccrual' && adjustmentDays) {
        employeeData = {
          ...employeeData,
          vacationDaysDelta: new Decimal(adjustmentDays?.value),
        };
      }
      if (state.probationEndDateRemoved) {
        employeeData = {
          ...employeeData,
          probationEndDate: null,
        };
        state.probationEndDateRemoved = false;
      }
      let tsPolicyQuery = undefined;
      if (
        TimesheetPolicy?.timesheetPolicyEmployee?.timesheetPolicyId !==
        TimesheetPolicy?.id
      ) {
        tsPolicyQuery = {
          ...(TimesheetPolicy?.timesheetPolicyEmployee?.id && {
            removeAssignedEmployees: {
              ids: [TimesheetPolicy.timesheetPolicyEmployee.id],
            },
          }),
        };
        if (TimesheetPolicy?.id && Employee?.id) {
          tsPolicyQuery = {
            ...tsPolicyQuery,
            assignEmployeesToTimesheet: {
              timesheetPolicyId: TimesheetPolicy.id,
              assignedEmployeeIds: [Employee.id],
              startDate: new Date().toISOString(),
              forceRevalidation: false,
            },
          };
        }
      }
      const absencePolicyId = hasVacationPolicy
        ? AbsencePolicyEmployeeVacation?.absencePolicyId
        : getPlannedPolicy(context)?.id;
      return {
        EmployeeUpdate: {
          updateEmployeeDetail: {
            person: personData,
            employee: {
              ...employeeData,
              holidayCalendar,
            },
            address,
            bankDetails: bankFormValues,
            currentEndDate,
            job: CurrentPrimaryJob?.id ? CurrentPrimaryJob : {},
          },
        },
        AbsenceCalculation:
          policy?.grantingType === 'fixed' &&
          adjustmentDays &&
          (data.hasVacationPolicy || getPlannedPolicy(context)?.id)
            ? {
                upsert: {
                  where: {
                    employeeId: employeeData?.id ?? Employee?.id,
                    year: adjustmentDays?.year,
                    absencePolicyId: absencePolicyId,
                  },
                  data: {
                    adjustmentDays: adjustmentDays?.value,
                  },
                },
              }
            : {},
        TimesheetPolicy: tsPolicyQuery,
      };
    },
  });
}

export async function editMyProfile(context: Context) {
  const { helpers, actions, state, data, sharedState } = context;
  const { Employee, BankDetails } = data;
  await actions.submit({
    onSuccess: async () => {
      helpers.showToast({
        title: '{{success}}',
        message: '{{successfully.edited.employee}}',
        type: 'success',
      });
      state.mode = 'view';
    },
    onError: () =>
      helpers.showToast({
        title: '{{error}}',
        message: '{{employee.edit.unsuccessful}}',
        type: 'error',
      }),
    transform: () => {
      const updateData: Pick<
        Employee.Employee,
        'id' | 'personalPhone' | 'personalEmail' | 'bankDetailsId'
      > = helpers.pick(Employee, [
        'id',
        'personalEmail',
        'personalPhone',
        'bankDetailsId',
      ]);
      const bankFormValues =
        sharedState.getModelData(BankDetails)?.instance?.data;
      if (bankFormValues?.bankAccountNumberType === undefined) {
        bankFormValues.bankAccountNumberType = null;
      }
      if (bankFormValues?.financialInstitutionIdType === undefined) {
        bankFormValues.financialInstitutionIdType = null;
      }
      return {
        Employee: {
          updateOwnEmployeeDetail: {
            employee: updateData,
            bankDetails: omit(bankFormValues, [
              '_nodeReference',
              '_nodeParentReference',
            ]),
          },
        },
      };
    },
  });
}

export function uploadDocument(context: Context) {
  const { actions, helpers, data } = context;

  const id = data?.Employee?.id;
  helpers.openModal({
    title: '{{upload.documents}}',
    size: 'large',
    template: `/templates/everest.hr.base/uinext/employee/uploadDocument?id=${id}&mode=edit`,
    onModalSubmit: actions.refetchUiModelData,
  });
}

export function getPositionNames({ data }: Context) {
  const { EmployeeJob } = data;
  const jobs = EmployeeJob?.filter((job: Any) => {
    return (
      (job.status === EvstTimelineStatus.Planned ||
        job.status === EvstTimelineStatus.Active) &&
      job.positionId
    );
  });
  const positionNames = jobs?.map((job: Any) => job?.['Job-Position']?.name);

  return positionNames?.join('/');
}

export function getAvatarSrc(context: Context) {
  const { data } = context;

  if (data?.Person?.photo) {
    return `/api/app/storage/${data.Person.photo}`;
  }
}

export function onAvatarAction(context: Context) {
  const { actions, state, helpers } = context;

  state.photoIsLoading = true;

  helpers.openModal({
    title: '{{photo}}',
    size: 'xsmall',
    template: `/templates/everest.hr.base/uinext/employee/employeePhoto?id=${state.id}`,
    onClose: async () => {
      try {
        await actions.refetchUiModelData();
      } finally {
        // We ensure that regardless of success or failure of the UI model data refetch, the
        // photo loading state is always set back to false.
        state.photoIsLoading = false;
      }
    },
  });
}

export function stateFilter(context: Context) {
  const { state } = context;

  state.filter = state?.filter ? 0 : 1;
}

export function setFilter(context: Context) {
  const { state } = context;

  return state?.filter === 1;
}

export function setEndDate(context: Context, e: Any) {
  const { sharedState, data, helpers } = context;

  const employee = sharedState.getNodeInstance(
    data['Employee']?._nodeReference
  );
  if (e === null || e === undefined) {
    helpers.set(employee, `data.endDate`, null);
  }
}

export function getDocuments(context: Context) {
  const { data } = context;

  const employeeAttachments = data?.EmployeeAttachments;
  return employeeAttachments;
}

export async function sendNotification(
  context: Context,
  notificationType: Any,
  subject: Any
) {
  const { data, actions, helpers } = context;

  const { Employee } = data;
  const notifiedUsers = [];
  try {
    const result = await actions.run({
      Users: {
        action: 'query',
        data: {
          where: { email: Employee?.email },
        },
        fieldList: ['userId'],
      },
    });
    const {
      NotificationCategory: [[notificationCategory]],
    } = await actions.run({
      NotificationCategory: {
        action: 'query',
        data: {
          where: { category: notificationType },
        },
        fieldList: ['id'],
      },
    });
    notifiedUsers.push(result?.Users[0]?.userId);
    if (notifiedUsers.length > 0) {
      await actions.run({
        Notification: {
          action: 'sendNotificationsV2',
          data: {
            notificationCategoryId: notificationCategory.id,
            subject: subject,
            link: `/templates/everest.hr.base/uinext/employee/employee?mode=view`,
            usersToNotify: notifiedUsers,
            message: subject,
            options: {
              slack: true,
              native: true,
              email: false,
            },
            includeFullLinkInMessage: true,
          },
        },
      });
    }
  } catch {
    helpers.showToast({
      title: '{{error}}',
      message: '{{notification.error.message}}',
      type: 'error',
    });
  }
}

export async function approveDocument(context: Context, rowData: Any) {
  const { actions, helpers, data } = context;

  const { Employee } = data;
  const attachmentId = rowData.data.id;
  const fileName = rowData.data.fileName;
  const notifiedUsers = [];
  const result = await actions.run({
    Users: {
      action: 'query',
      data: {
        where: { email: Employee?.email },
      },
      fieldList: ['userId'],
    },
  });
  notifiedUsers.push(result?.Users[0]?.userId);
  await actions.submit({
    onSuccess: async () => {
      helpers.showToast({
        title: '{{success}}',
        message: '{{approval.successfull}}',
        type: 'success',
      });
      await sendNotification(
        context,
        'Attachment Approved',
        `Your recently uploaded attachment ${fileName} has been approved.`
      );
      await actions.refetchUiModelData();
    },
    onError: () => {
      helpers.showToast({
        title: '{{error}}',
        message: '{{attachment.approval.error.message}}',
        type: 'error',
      });
    },
    transform: () => {
      return {
        EmployeeAttachments: {
          absoluteBOName: 'everest.hr.base/EmployeeAttachmentModel',
          updateEmployeeAttachmentStatus: {
            employee: {
              id: Employee?.id,
            },
            employeeAttachment: {
              id: attachmentId,
              status: 'approved',
            },
          },
        },
      };
    },
  });
}

export async function rejectDocument(context: Context, rowData: Any) {
  const { actions, helpers, data } = context;

  const { Employee } = data;
  const attachmentId = rowData.data.id;
  const fileName = rowData.data.fileName;
  const notifiedUsers = [];
  const result = await actions.run({
    Users: {
      action: 'query',
      data: {
        where: { email: Employee?.email },
      },
      fieldList: ['userId'],
    },
  });
  notifiedUsers.push(result?.Users[0]?.userId);
  await actions.submit({
    onSuccess: async () => {
      helpers.showToast({
        title: '{{success}}',
        message: '{{rejection.successful}}',
        type: 'success',
      });
      await sendNotification(
        context,
        'Attachment Rejected',
        `Your recently uploaded attachment ${fileName} has been rejected.`
      );
      await actions.refetchUiModelData();
    },
    onError: () => {
      helpers.showToast({
        title: '{{error}}',
        message: '{{attachment.rejection.error.message}}',
        type: 'error',
      });
    },
    transform: () => {
      return {
        EmployeeAttachments: {
          absoluteBOName: 'everest.hr.base/EmployeeAttachmentModel',
          updateEmployeeAttachmentStatus: {
            employee: {
              id: Employee?.id,
            },
            employeeAttachment: {
              id: attachmentId,
              status: 'rejected',
            },
          },
        },
      };
    },
  });
}

export async function unapproveDocument(context: Context, rowData: Any) {
  const { actions, helpers, data } = context;

  const attachmentId = rowData.data?.id;
  await actions.submit({
    onSuccess: async () => {
      helpers.showToast({
        title: '{{success}}',
        message: '{{unapproval.successful}}',
        type: 'success',
      });
      await actions.refetchUiModelData();
    },
    onError: () => {
      helpers.showToast({
        title: '{{error}}',
        message: '{{attachment.unapproval.error.message}}',
        type: 'error',
      });
    },
    transform: () => {
      return {
        EmployeeAttachments: {
          absoluteBOName: 'everest.hr.base/EmployeeAttachmentModel',
          updateEmployeeAttachmentStatus: {
            employee: {
              id: data?.Employee?.id,
            },
            employeeAttachment: {
              id: attachmentId,
              status: 'pending',
            },
          },
        },
      };
    },
  });
}

export function getAttachments(context: Context) {
  const { data } = context;

  const employeeAttachments = data?.EmployeeAttachments;
  return employeeAttachments;
}

/**
 * Work around to make upload component render files
 */
export function attachmentsExist(context: Context) {
  const { data } = context;

  const existed = !!data?.EmployeeAttachments;
  return existed;
}

export function isManager(context: Context) {
  const { data } = context;

  const { EmployeeJob, CurrentEmployee, isAdmin } = data;
  return (
    EmployeeJob?.find(
      (x: Any) =>
        x['Job-JobManager']?.['Job-Employee']?.id === CurrentEmployee?.id
    ) || isAdmin
  );
}

export function getIncludeSalaryValue(context: Context, rowData: Any) {
  return rowData.data.withSalary === true ? '{{yes}}' : undefined;
}

export function actionsVisible(context: Context) {
  const { data } = context;

  return isManager(context) || data?.isCurrentEmployee;
}

export function deleteVisible(context: Context) {
  const { data } = context;

  return data.isAdmin || data.isCurrentEmployee;
}

export function enableButton(context: Context, row: Any) {
  const { state, data } = context;

  if (isManager(context) || data?.isCurrentEmployee) {
    state.button = false;
    if (row?.length === 1 && row[0]?.mandatoryLeaveId) {
      state.button = true;
    }
    state.leaveId = row?.map((x: Any) => {
      return {
        id: x.data.id,
        mandatoryLeaveId: x.data.mandatoryLeaveId ?? null,
        status: x.data.status,
      };
    });
  } else {
    state.button = true;
  }
}

export function checkButton(context: Context) {
  const { state } = context;

  return state.button === false ? false : true;
}

export function approveDisabled(context: Context) {
  const { state } = context;

  if (checkButton(context)) {
    return true;
  } else {
    const ids = [];
    state?.leaveId?.map(({ id, status }: Any) => {
      if (status === 'requested') {
        ids.push(id);
      }
    });
    return ids?.length === 0;
  }
}

export function deleteDisabled(context: Context) {
  const { state, data } = context;

  return checkButton(context)
    ? true
    : state.leaveId?.length !== 1 ||
        (state.leaveId?.some((x) => x.mandatoryLeaveId) && !data.isAdmin);
}

export function getReimbursementCurrency(context: Context) {
  const { data } = context;

  const { Employee } = data;
  return Employee?.['Employee-Entity']?.currency;
}

export function hasDirectReports(context: Context) {
  const { data } = context;

  return (data?.DirectReports?.length ?? 0) !== 0;
}

export function openEmployee(context: Context, row: Any) {
  const { helpers, data } = context;

  const employeeId = row?.original?.employeeId;
  if (!employeeId) {
    return;
  }
  const canAccess = data?.DirectReports?.find(
    (emp: Any) => emp?.id === employeeId
  );
  if (!!canAccess || data?.isAdmin) {
    helpers.navigate({
      to: `/templates/everest.hr.base/uinext/employee/employee?id=${employeeId}&mode=view`,
    });
  } else {
    helpers.navigate({
      to: `/templates/everest.hr.base/uinext/orgChart/employeeOverview?id=${employeeId}&mode=view`,
    });
  }
}

export function setPositionSelect(_context: Context, { setRowCellValue }: Any) {
  setRowCellValue({
    rowCell: 'positionId',
    newValue: null,
  });
}

export function getTabTitle(context: Context) {
  const { data } = context;

  return data?.Person
    ? data?.Employee?.employeeNumber +
        ': ' +
        data?.Person?.firstName +
        ' ' +
        data?.Person?.lastName
    : '';
}

export function getFullName(context: Context) {
  const { data } = context;
  return data?.Person?.name ?? undefined;
}

export function getDisplayName(context: Context) {
  const { data } = context;
  return data?.Employee?.displayName ?? undefined;
}

export function isCurrentEmployee(context: Context) {
  const { data } = context;

  if (data?.isAdmin) {
    return false;
  }
  return data?.isCurrentEmployee ? true : false;
}

export function validateField(_context: Context) {
  return {
    validate: (value: Any) => {
      return !!value || '{{required}}*';
    },
  };
}

export function checkEmail(context: Context) {
  const { actions } = context;

  return {
    validate: async (value: Any) => {
      if (!value) {
        return true;
      }
      const emailRegex = new RegExp(/^[\w-.]+@([\w-]+\.)+[\w-]{2,4}$/);
      if (!emailRegex.test(value)) {
        return '{{email.format.error}}';
      }
      const emails = await actions.run({
        Employees: {
          action: 'query',
          where: { email: value },
        },
      });
      const emailAlreadyExists = emails?.Employees?.length > 1;
      return !emailAlreadyExists || '{{email.exists}}';
    },
  };
}

export function isEditDisabled(context: Context) {
  const { sharedState, state } = context;
  return state?.mode === 'edit' ? sharedState.validateAllFields() : false;
}

export function checkPhone(_context: Context) {
  return {
    validate: (value: Any) => {
      const phoneRegex = new RegExp(/^(00|\+)\d{5,15}$/);
      const isValid = value ? phoneRegex.test(value) : true;
      return isValid || '{{phone.format.error}}';
    },
  };
}

export function leaveRequest(context: Context) {
  const { helpers, data, state } = context;

  if (data?.isAdmin && !data?.isCurrentEmployee) {
    helpers.navigate({
      to: `/templates/everest.hr.base/uinext/leaveRequest/yourAbsences?empId=${
        state?.id ?? data?.Employee?.id
      }`,
      closeCurrentTab: false,
      initialState: {
        mode: 'create',
        form: {
          googleEvent: true,
        },
      },
    });
  } else {
    helpers.navigate({
      to: `/templates/everest.hr.base/uinext/leaveRequest/yourAbsences`,
      closeCurrentTab: false,
      initialState: {
        redirect: true,
        mode: 'create',
        form: {
          googleEvent: true,
        },
      },
    });
  }
}

export function checkRequestButton(context: Context) {
  const { state, data } = context;

  return data?.isCurrentEmployee && state.activeSegment === 3 ? true : false;
}

export function getPercent(_context: Context, row: Any) {
  const percent = row?.data?.percentage;
  return percent ? `${percent}%` : undefined;
}

export function getHolidayCalendars(context: Context) {
  const { data } = context;

  const { HolidayCalendars } = data;
  return HolidayCalendars?.map((calendar: Any) => {
    return {
      id: calendar?.id,
      name:
        calendar?.['HolidayCalendar-CountryISOV2']?.country +
        ' (' +
        calendar?.['HolidayCalendar-CountryISOSubdivisionV2']?.subdivision +
        ')',
    };
  });
}

export function getPeriodText(context: Context, { data }: Any) {
  const { controllers, language } = context;

  return controllers.getPeriod(data?.month, data?.year, language);
}

export function getTotalWorkTimeText(context: Context, { data }: Any) {
  const { controllers } = context;

  return controllers.convertTimespanToStr(data?.totalWorkTime);
}

export function getTotalBreakTimeText(context: Context, { data }: Any) {
  const { controllers } = context;

  return controllers.convertTimespanToStr(data?.totalBreakTime);
}

export function canReviewTimesheet(context: Context) {
  const { data } = context;

  return data.isAdmin === true;
}

export function onTrackTimeClick(context: Context) {
  const { helpers, state, data } = context;

  helpers.navigate({
    to: `/templates/everest.hr.base/uinext/timesheet/record/date-records?employeeId=${
      state?.id ?? data?.Employee?.id
    }`,
  });
}

export function hasVacationPolicy(context: Context) {
  const { data, isFetchingUiModelData, form } = context;

  if (form.formState?.isSubmitting) {
    return true;
  }
  if (!isFetchingUiModelData) {
    return data?.hasVacationPolicy;
  }
}

export function hasSickPolicy(context: Context) {
  const { data, isFetchingUiModelData } = context;

  if (!isFetchingUiModelData) {
    return data?.AbsencePolicyEmployeeSick?.absencePolicyId;
  }
}

export function getDirectReportsQuery(context: Context) {
  const { state, data } = context;

  return {
    employeeId: state?.id ?? data?.Employee?.id,
  };
}

export function setJobManager(
  _context: Context,
  value: Any,
  { setRowCellValue }: Any
) {
  if (!value) {
    setRowCellValue({
      rowCell: 'jobManager',
      newValue: null,
    });
  }
}

export function onAddressIconClick(context: Context) {
  const { helpers, data, state } = context;

  const setFormAddress = (value: Any) => {
    state.formAddress = value;
  };
  const { formAddress } = state;
  const { Address } = data;
  const initialAddress = formAddress ?? Address;
  helpers.openModal({
    title: '{{add.home.address}}',
    size: 'small',
    template: `/templates/everest.hr.base/uinext/employee/addressForm`,
    onModalSubmit: setFormAddress,
    initialState: {
      formAddress: initialAddress,
    },
  });
}

export function getAssignedRolesQuery(context: Context) {
  const { user, helpers } = context;

  const userData = helpers.cloneDeep(user);
  return userData.currentUserData?.userId
    ? {
        userId: userData.currentUserData.userId,
      }
    : undefined;
}

export function requestButtonVisible(context: Context) {
  const { data } = context;

  return data?.isCurrentEmployee || data?.isAdmin;
}

export function canAddDocument(context: Context) {
  const { data } = context;

  return data.isAdmin || data.isCurrentEmployee;
}

export function getDocumentFileId(context: Context) {
  const { state, data } = context;

  if (!state.documentFileId) {
    state.documentFileId = data.EmployeeAttachments?.[0]?.fileId;
  }
  return state.documentFileId;
}

export function viewDocument(context: Context, { data }: DocumentRowType) {
  const { helpers, actions } = context;
  const { employeeId, fileId } = data;
  helpers.openModal({
    title: '{{view.documents}}',
    template: `/templates/everest.hr.base/uinext/employee/viewDocument?id=${employeeId}&fileId=${fileId}`,
    onModalSubmit: actions.refetchUiModelData,
  });
}

export function isDeleteAttachmentDisabled(context: Context) {
  const { data } = context;

  const hasPermission = isCurrentEmployee(context) === true || data.isAdmin;
  return !hasPermission;
}

export async function deleteAttachment(context: Context, { data }: Any) {
  const { actions } = context;

  await actions.submit({
    transform: () => {
      return {
        EmployeeAttachments: {
          delete: {
            id: data.id,
          },
        },
      };
    },
  });
}

export function isReviewAttachmentDisabled(context: Context, { rowData }: Any) {
  const { data } = context;

  const hasPermission = data.isAdmin || isManager(context);
  const isReviewed = rowData?.status !== 'pending';
  return !hasPermission || isReviewed;
}

export function isUndoAttachmentDisabled(context: Context, { rowData }: Any) {
  const { data } = context;

  const hasPermission = data.isAdmin || isManager(context);
  const isPending = rowData?.status === 'pending';
  return !hasPermission || isPending;
}

export function getAssignmentMethod(context: Context, row: Any) {
  switch (row?.data?.method) {
    case 'defaultPosition': {
      return '{{position.auto-assigned}}';
    }
    case 'employeePosition': {
      return `{{employee.position.auto-assigned}}: ${row?.data?.positionName}`;
    }
    case 'employeeDirect': {
      return `{{employee.manual-assigned}}`;
    }
    case 'userAssignment': {
      return `{{user.assigned}}`;
    }
    default: {
      return '';
    }
  }
}

export function getStatus(context: Context, row: Any) {
  const today = new Date();
  if (row?.data?.startDate && new Date(row?.data?.startDate) < today) {
    if (row?.data?.endDate && new Date(row?.data?.endDate) > today) {
      return '{{active}}';
    } else if (row?.data?.endDate) {
      return '{{inactive}}';
    } else {
      return '{{active}}';
    }
  } else if (row?.data?.startDate && new Date(row?.data?.startDate) > today) {
    return '{{inactive}}';
  } else {
    return '{{active}}';
  }
}

export function getStartDate(context: Context, row: Any) {
  const { data } = context;

  if (row?.data?.positionId) {
    const startDate = data?.EmployeeJob?.find(
      (job: Any) => job.positionId === row?.data?.positionId
    )?.startDate;
    return startDate;
  } else {
    return row?.data?.startDate ?? row?.data?.validFrom;
  }
}

export function getEndDate(context: Context, row: Any) {
  const { data } = context;

  if (row?.data?.positionId) {
    const endDate = data?.EmployeeJob?.find(
      (job: Any) => job.positionId === row?.data?.positionId
    )?.endDate;
    return endDate;
  } else {
    return row?.data?.endDate ?? row?.data?.validTo;
  }
}

export function manageUserAccess(context: Context) {
  const { actions, helpers, data } = context;

  const id = data?.Employee?.id;
  helpers.openModal({
    title: '{{manage.employee.access}}',
    size: 'large',
    template: `/templates/everest.hr.base/uinext/securityManagement/manageEmployeeAccess?id=${id}&mode=edit`,
    onModalSubmit: actions.refetchUiModelData,
  });
}

export function manageUserAccessDisabled(context: Context) {
  const { sandbox } = context;
  return sandbox.activeSandboxId !== undefined && sandbox.activeSandboxId !== -1
    ? true
    : false;
}

export function getMatchers() {
  return {
    Rejected: 'sharpay',
    Approved: 'shamrock',
    Requested: 'orchid',
    rejected: 'sharpay',
    readyToDownload: 'shamrock',
  };
}

export function requestDocument(context: Context) {
  const { helpers, data, actions } = context;

  helpers.openModal({
    title: '{{request.documents}}',
    size: 'xsmall',
    template: `/templates/everest.hr.base/uinext/employee/requestDocument`,
    initialState: {
      employee: data?.Employee,
      action: 'create',
    },
    onClose: actions.refetchUiTemplate,
  });
}

export function getSalaryType(context: Context, rowData: Any) {
  const { data } = context;

  //Currently considers only monthly payout
  const payoutPeriod = data?.PayoutPeriod?.find(
    (pay: Any) => pay.codeValue === rowData?.data?.payoutPeriod
  )?.text;
  if (rowData?.data?.salaryType === 'fixed') {
    return `{{fixed.salary}} - ${payoutPeriod}`;
  } else if (rowData?.data?.salaryType === 'hourly') {
    return `{{hourly.wage}}- ${payoutPeriod}`;
  }
}

export function manageSalary(context: Context) {
  const { actions, data, helpers } = context;

  const id = data?.Employee?.id;
  helpers.openModal({
    title: '{{manage.salary}}',
    size: 'large',
    template: `/templates/everest.hr.base/uinext/employee/manageSalary?id=${id}&mode=edit`,
    onModalSubmit: async () => {
      helpers.closeModal();
      await actions.refetchUiModelData();
    },
  });
}

export function salaryEditing(context: Context) {
  const { state, data } = context;

  return state.salary === 'edit' && data?.isAdmin;
}

export function salaryNotEditing(context: Context) {
  const { state } = context;

  return state.salary === 'view';
}

export function cancelSalaryEdit(context: Context) {
  const { state } = context;

  state.salary = 'view';
}

export function getSalaryVariant(context: Context) {
  const { state } = context;

  if (state?.salary === 'edit') {
    return 'primary';
  } else if (state?.salary === 'view') {
    return 'secondary';
  }
}

export async function deleteSalary(context: Context, row: Any) {
  const { actions } = context;

  await actions.run({
    Salary: {
      action: 'delete',
      where: { id: row?.data?.id },
    },
  });
  await actions.refetchUiModelData();
}

export function getSalaryMatchers() {
  return {
    '{{inactive}}': 'mystic-grey',
    '{{active}}': 'shamrock',
    '{{planned}}': 'orchid',
  };
}

export function getSalaryStatus(
  _context: Context,
  rowData: {
    data: Partial<Salary.Salary>;
  }
) {
  if (
    new Date(rowData?.data?.startDate).getTime() <= Date.now() &&
    new Date(rowData?.data?.endDate).getTime() >= Date.now()
  ) {
    return '{{active}}';
  } else if (
    new Date(rowData?.data?.startDate).getTime() <= Date.now() &&
    !rowData?.data?.endDate
  ) {
    return '{{active}}';
  } else if (new Date(rowData?.data?.startDate).getTime() >= Date.now()) {
    return '{{planned}}';
  }
  {
    return '{{inactive}}';
  }
}

export function getBonusType(context: Context, row: Any) {
  const { data } = context;

  const { RecurrenceType } = data;
  return RecurrenceType?.find(
    (x: Any) => x?.codeValue === row?.data?.['Bonus-BonusType']?.recurrenceType
  )?.text;
}

export function getBenefitPaymentMonth(row: Any) {
  if (
    row?.data?.paymentPeriod &&
    row?.data?.['Benefit-BenefitType']?.recurrenceType === 'yearly'
  ) {
    return new Date(row?.data?.paymentPeriod).toLocaleString('default', {
      month: 'long',
    });
  } else if (
    row?.data?.paymentPeriod &&
    row?.data?.['Benefit-BenefitType']?.recurrenceType === 'oneTime'
  ) {
    return new Date(row?.data?.paymentPeriod).toLocaleString('default', {
      month: 'long',
      year: 'numeric',
    });
  } else {
    return '- -';
  }
}

export function manageBenefit(context: Context) {
  const { actions, data, helpers } = context;

  const id = data?.Employee?.id;
  helpers.openModal({
    title: '{{manage.benefits}}',
    size: 'large',
    template: `/templates/everest.hr.base/uinext/employee/manageBenefit?id=${id}&mode=edit&feat-delta=true`,
    onModalSubmit: actions.refetchUiModelData,
  });
}

export function getBenefitType(context: Context, row: Any) {
  const { data } = context;

  const { RecurrenceType } = data;
  return RecurrenceType?.find(
    (x: Any) =>
      x?.codeValue === row?.data?.['Benefit-BenefitType']?.recurrenceType
  )?.text;
}

export function getBonusPaymentMonth(row: Any) {
  if (
    row?.data?.paymentPeriod &&
    row?.data?.['Bonus-BonusType']?.recurrenceType === 'yearly'
  ) {
    return new Date(row?.data?.paymentPeriod).toLocaleString('default', {
      month: 'long',
    });
  } else if (
    row?.data?.paymentPeriod &&
    row?.data?.['Bonus-BonusType']?.recurrenceType === 'oneTime'
  ) {
    return new Date(row?.data?.paymentPeriod).toLocaleString('default', {
      month: 'long',
      year: 'numeric',
    });
  } else {
    return '- -';
  }
}

export function getBenefitAmount(context: Context, row: Any) {
  const { Decimal } = context;

  const amount = row?.data?.benefitAmount;
  return !amount || new Decimal(amount) === 0
    ? row?.data?.['Benefit-BenefitType']?.defaultAmount
    : amount;
}
async function setRequestedDocumentDownloaded(context: Context, id: number) {
  const { actions } = context;
  await actions.run({
    EmployeeRequestedDocument: {
      action: 'modifyRequestedDocument',
      data: {
        action: 'download',
        id,
      },
    },
  });
  await actions.refetchUiModelData({
    nodesToLoad: ['EmployeeRequestedDocuments', 'EmployeeAttachments'],
  });
}

export async function downloadDocument(context: Context, rowData: Any) {
  const { actions } = context;

  if (rowData?.data?.['EmployeeAttachment-EmployeeDocumentType']?.requestable) {
    await setRequestedDocumentDownloaded(
      context,
      rowData?.data?.employeeRequestedDocumentId
    );
  }
  const link = await actions.everestRequest('app/storage/fetch-url', {
    fileId: rowData?.data?.fileId,
  });
  const downloadLink = document.createElement('a');
  downloadLink.href = link.url;
  downloadLink.download = 'test';
  downloadLink.click();
  URL.revokeObjectURL(link.url);
}

export function getUserQuery(context: Context) {
  const { data } = context;

  const { EmployeePermissions } = data;
  const directPermissions = EmployeePermissions?.filter(
    (emp: Any) => emp.method === 'employeeDirect'
  );
  const createdUsers = directPermissions?.map((per: Any) => per.createdBy);
  return createdUsers
    ? {
        where: {
          userId: {
            $in: createdUsers,
          },
        },
      }
    : {
        where: {
          userId: null,
        },
      };
}

export function onEditJobsClick(context: Context) {
  const { helpers, data, actions } = context;

  const { EmployeeJob, Employee } = data;
  const jobIds = EmployeeJob?.map(({ id }: Any) => Number(id)) ?? [];
  if (Employee?.id) {
    helpers.openModal({
      template: `/templates/everest.hr.base/uinext/job/jobModalEdit`,
      size: 'large',
      title: '{{job.history}}',
      initialState: {
        jobIds,
        employeeId: Employee.id,
        employeeBOId: data?.employeeBusinessObject?.id,
      },
      onClose: async () => {
        await actions.refetchUiModelData();
      },
    });
  }
}

export function getTotalWorkingPercentage(context: Context) {
  const {
    data: { EmployeeJob },
  } = context;

  const totalWorkingPercentage = EmployeeJob?.filter(
    (job: Any) => job.status === 'active'
  )?.reduce((acc: Any, current: Any) => acc + Number(current.percentage), 0);
  return totalWorkingPercentage;
}

export function showJobAlert(context: Context) {
  const totalWorkingPercentage = getTotalWorkingPercentage(context);
  return totalWorkingPercentage && totalWorkingPercentage !== 100;
}

export function getJobAlertContent(context: Context) {
  const totalWorkingPercentage = getTotalWorkingPercentage(context);
  if (totalWorkingPercentage === 100) {
    return '';
  } else if (totalWorkingPercentage < 100) {
    return `${100 - totalWorkingPercentage}% of time resource is un-allocated`;
  } else {
    return `Exceeding ${totalWorkingPercentage - 100}% of time resource`;
  }
}

export function isAddJobDisabled(context: Context) {
  return getTotalWorkingPercentage(context) === 100;
}

export function getJobTypeText(
  _context: Context,
  { data }: { data: Partial<Job.Job> }
) {
  return data.isPrimary ? '{{primary}}' : '{{secondary}}';
}

export function jobTypeMatcher() {
  return {
    '{{primary}}': 'shamrock',
    '{{secondary}}': 'mystic-grey',
  };
}

export function getEmployeeJobQuery(context: Context) {
  const { state, data } = context;

  return state?.id || data?.Employee?.id
    ? {
        where: {
          employeeId: state?.id ?? data?.Employee?.id,
        },
        orderBy: [
          {
            field: 'startDate',
            ordering: 'desc',
          },
        ],
      }
    : undefined;
}

export function getPaymentMonth(row: Any) {
  return row?.data?.startDate
    ? new Date(row?.data?.paymentPeriod).toLocaleString('default', {
        month: 'long',
      })
    : new Date(row?.data?.paymentPeriod).toLocaleString('default', {
        month: 'long',
        year: 'numeric',
      });
}

export function viewSalaryReport(context: Context) {
  const { helpers } = context;
  helpers.openNotImplementedToast();
  return;
}

export function getVacationPolicyName(context: Context) {
  const { data } = context;
  if (data.AbsencePolicyEmployeeVacation?.id) {
    return data.AbsencePolicyEmployeeVacation?.[
      'AbsencePolicyEmployee-AbsencePolicy'
    ]?.name;
  } else {
    const plannedPolicy = data?.AbsencePoliciesEmployee?.find(
      (x) =>
        x.status === EvstTimelineStatus.Planned &&
        x.policyType === EvstLeaveType.Vacation
    )?.['AbsencePolicyEmployee-AbsencePolicy']?.name;
    return plannedPolicy ? `${plannedPolicy} ({{planned}})` : '{{none}}';
  }
}

export function getPlannedPolicy(context: Context) {
  const { AbsencePoliciesEmployee, AbsencePolicies } = context.data;
  const plannedPolicyId = AbsencePoliciesEmployee?.find(
    (x) =>
      x.status === EvstTimelineStatus.Planned &&
      x.policyType === EvstLeaveType.Vacation
  )?.absencePolicyId;
  if (plannedPolicyId) {
    const plannedPolicy = AbsencePolicies?.find(
      (x) => x.id === plannedPolicyId
    );
    return plannedPolicy ?? undefined;
  }
}

export function getSickPolicyName(context: Context) {
  const { data } = context;
  if (data.AbsencePolicyEmployeeSick?.id) {
    return data.AbsencePolicyEmployeeSick?.[
      'AbsencePolicyEmployee-AbsencePolicy'
    ]?.name;
  } else {
    const plannedPolicy = data.AbsencePoliciesEmployee?.find(
      (x) =>
        x.status === EvstTimelineStatus.Planned &&
        x.policyType === EvstLeaveType.SickLeave
    )?.['AbsencePolicyEmployee-AbsencePolicy']?.name;
    return plannedPolicy ? `${plannedPolicy} ({{planned}})` : '{{none}}';
  }
}

export function setYear(context: Context, e: Any) {
  const { state } = context;

  // eslint-disable-next-line unicorn/prefer-number-properties
  if (!e && isNaN(state.year)) {
    state.year = new Date().getFullYear();
  } else if (e) {
    state.year = e;
    if (getEmployeeVacationPolicy(context)?.grantingType !== 'fixed') {
      state.disableAdjustment = false;
      return;
    }
    if (
      state.adjustmentDays &&
      state.adjustmentDays?.year !== Number(state.year)
    ) {
      state.disableAdjustment = true;
    } else if (
      state.adjustmentDays &&
      state.adjustmentDays?.year === Number(state.year)
    ) {
      state.disableAdjustment = false;
    }
  }
}

export function getCurrentYear() {
  return new Date().getFullYear();
}

export function getYears(context: Context) {
  const { data } = context;
  const today = new Date();
  const startDate =
    data?.AllLeaves?.length > 0
      ? new Date(data?.AllLeaves[0]?.startDate)
      : today;
  startDate.setMinutes(startDate.getMinutes() + startDate?.getTimezoneOffset());
  const current = today?.getFullYear();
  const min =
    today?.getTime() < startDate?.getTime()
      ? today?.getFullYear()
      : startDate?.getFullYear();
  const max = current + 1;
  const years = [];
  for (let i = min; i <= max; i++) {
    years.push({ year: i });
  }
  return years;
}

export function openLeaveRequest(context: Context, row: Any) {
  const { helpers } = context;
  helpers.navigate({
    to: `/templates/everest.hr.base/uinext/leaveRequest/leaveDetails?id=${row?.id}&empId=${row?.employeeId}`,
    closeCurrentTab: false,
  });
}

export async function deleteLeaves(context: Context) {
  const { actions, helpers, state } = context;

  if (state.leaveId?.length !== 1) {
    return;
  }
  await actions.submit({
    onSuccess: async () => {
      helpers.showToast({
        title: '{{success}}',
        message: `{{delete.request.successful}}`,
        type: 'success',
      });
      await actions.refetchUiModelData();
    },
    onError: () => {
      helpers.showToast({
        title: '{{error}}',
        message: `{{delete.request.unsuccessful}}`,
        type: 'error',
      });
    },
    transform: () => {
      return {
        Leave: {
          absoluteBOName: 'everest.hr.base/LeaveModel',
          deleteLeave: { leave: state.leaveId[0] },
        },
      };
    },
  });
}

export function validateBankField(context: Context) {
  const { data, sharedState } = context;

  return {
    validate: (value: Any) => {
      const bankAccountNumberTypeExists =
        sharedState.getNodeInstance(data['BankDetails']?._nodeReference)?.data
          ?.bankAccountNumberType && !value;
      return !bankAccountNumberTypeExists || '{{required}}';
    },
  };
}

export async function onPhotoSuccess(context: Context, fileId: Any, file: Any) {
  await updatePhoto(context, fileId, file.name);
}

export async function onPhotoRemove(context: Context) {
  await updatePhoto(context, null, null);
}

export async function updatePhoto(
  context: Context,
  fileId: Any,
  fileName: Any
) {
  const { state, actions, data } = context;

  await actions.submit({
    transform: () => {
      return {
        Employee: {
          absoluteBOName: 'everest.hr.base/EmployeeModel',
          updateEmployeePhoto: {
            employeeId: state.id ?? data?.Employee?.id,
            photo: fileId,
            photoName: fileName,
          },
        },
      };
    },
  });
}

export function disableAdjustmentField(context: Context) {
  const { data, state } = context;

  const vacationPolicy = getEmployeeVacationPolicy(context);
  return (
    (data.hasVacationPolicy === false && !getPlannedPolicy(context)?.id) ||
    vacationPolicy?.entitlementType === 'unlimited' ||
    state.disableAdjustment
  );
}

export function setVacationAdjustment(context: Context, e: Any) {
  const { state } = context;

  state.adjustmentDays = { year: state.year, value: e };
}

export function getVacationAdjustment(context: Context) {
  const { data, state } = context;
  let adjustment: Decimal | number | string;
  const x = state.mode === 'view' ? '- -' : null;
  const policy = getEmployeeVacationPolicy(context);
  if (state.adjustmentDays) {
    if (policy?.grantingType === 'fixed') {
      adjustment =
        state.adjustmentDays?.year === Number(state.year)
          ? state.adjustmentDays?.value
          : data.AbsenceCalculations?.find(
              (x: Any) => x.year === Number(state.year)
            )?.adjustmentDays ?? x;
    } else {
      adjustment = state.adjustmentDays?.value ?? x;
    }
  } else {
    adjustment =
      policy?.grantingType === 'fixed'
        ? data.AbsenceCalculations?.find(
            (x: Any) => x.year === Number(state.year)
          )?.adjustmentDays ?? x
        : policy?.grantingType === 'byAccrual'
          ? data.Employee?.vacationDaysDelta ?? x
          : x;
  }
  return adjustment === '- -' ? adjustment : Number(adjustment);
}

export function vacationAdjustmentLabel(context: Context) {
  const { state } = context;

  const policy = getEmployeeVacationPolicy(context);
  return policy?.grantingType === 'fixed'
    ? `${state.year} {{vacation.adjustment}}`
    : '{{vacation.adjustment}}';
}

export function remainingDaysLabel(context: Context) {
  const policy = getEmployeeVacationPolicy(context);
  return policy?.grantingType === 'byAccrual'
    ? '{{remaining.vacation.days.today}}'
    : '{{employee.remaining.vacation.days}}';
}

export function returnDate(context: Context) {
  const { state } = context;
  const today = new Date();
  const todayPlain = new PlainDate(
    today.getFullYear(),
    today.getMonth() + 1,
    today.getDate()
  );
  const policy = getEmployeeVacationPolicy(context);
  return policy?.grantingType === 'byAccrual'
    ? todayPlain.toISO()
    : state?.year === todayPlain.year
      ? todayPlain.toISO()
      : state?.year > todayPlain.year
        ? new PlainDate(state?.year, 1, 1).toISO()
        : new PlainDate(state?.year, 12, 31).toISO();
}

export function getAbsenceType(context: Context, row: Any) {
  const { data } = context;
  const { LeaveTypes } = data;
  return row?.data?.mandatoryLeaveId
    ? 'Company Vacation'
    : LeaveTypes?.find((x: Any) => x?.codeValue === row?.data?.absenceType)
        ?.text;
}

export function getYearLeaves(context: Context) {
  const { data, helpers, state } = context;
  if (!helpers.isEmpty(data?.Employee)) {
    const currentYear = state?.year ?? new Date()?.getFullYear();
    const startDate = new PlainDate(currentYear, 1, 1).toISO();
    const endDate = new PlainDate(currentYear, 12, 31).toISO();
    return {
      where: {
        employeeId: data?.Employee?.id ?? null,
        $or: [
          {
            startDate: {
              $between: [startDate, endDate],
            },
          },
          {
            endDate: {
              $between: [startDate, endDate],
            },
          },
        ],
      },
      orderBy: ['startDate'],
    };
  }
}

export function getAbsenceSummaryLabel(context: Context) {
  const { state } = context;

  const grantingType = getEmployeeVacationPolicy(context)?.grantingType;
  return grantingType === 'fixed'
    ? `{{absence.summary}} (${state.year})`
    : '{{absence.summary}}';
}

export function showDeactivatedJobAlert(context: Context) {
  const { unassignedJobs } = getUnassignedJobs(context);
  return unassignedJobs?.length > 0 ? true : false;
}

export function getUnassignedJobs(context: Context) {
  const { data, helpers } = context;

  const unassignedJobs = [];
  let daysDifference;
  const { EmployeeJob, DirectReports } = data;
  if (!helpers.isEmpty(EmployeeJob)) {
    for (const job of EmployeeJob) {
      if (job?.endDate) {
        daysDifference = Math.round(
          (new Date(job?.endDate)?.getTime() - Date.now()) / (1000 * 3600 * 24)
        );
        // Check for direct reports
        const directReportJobs = DirectReports.filter(
          (directReportJob: Any) => directReportJob.jobManager === job.id
        );
        if (
          daysDifference <= 60 &&
          directReportJobs &&
          directReportJobs.length > 0 &&
          job?.reassignmentComplete !== true
        ) {
          unassignedJobs.push(job);
        }
      }
    }
  }
  return { unassignedJobs, daysDifference };
}

export function getDeactivatedJobAlert(context: Context) {
  const { unassignedJobs, daysDifference } = getUnassignedJobs(context);
  if (unassignedJobs?.length === 1 && daysDifference <= 0) {
    return `There is a job that has ended.`;
  } else if (unassignedJobs?.length > 1 && daysDifference > 0) {
    return `There are jobs that are ending soon.`;
  } else if (unassignedJobs?.length === 1 && daysDifference > 0) {
    return `There is a job that is ending soon.`;
  } else if (unassignedJobs?.length > 1 && daysDifference <= 0) {
    return `There are jobs that have ended.`;
  }
}

export async function navigateToReassign(context: Context) {
  const { helpers, state, actions, data } = context;

  const { unassignedJobs } = getUnassignedJobs(context);
  let jobIds = unassignedJobs?.map(({ id }: Any) => Number(id)) ?? [];
  if (jobIds?.length === 0) {
    jobIds = data?.EmployeeJob?.map(({ id }: Any) => Number(id)) ?? [];
  }
  helpers.openModal({
    template: `/templates/everest.hr.base/uinext/job/reassignDirectReports?feat-delta=true`,
    size: 'large',
    title: '{{reassign.direct.reports}}',
    initialState: {
      jobIds,
      empId: state?.id ?? data?.Employee?.id,
    },
    onModalSubmit: actions.refetchUiModelData,
  });
}

export function showEmailAlert(context: Context) {
  const { data, helpers } = context;
  const { EmployeeUser, Employee } = data;
  if (EmployeeUser?.email) {
    return false;
  } else {
    return !helpers.isEmpty(Employee) &&
      !helpers.isEmpty(EmployeeUser) &&
      new Date(Employee?.startDate) <= new Date() &&
      !showEmailMissingAlert(context)
      ? true
      : false;
  }
}

export function getCurrentEmployeeId(context: Context) {
  const { data, helpers, state } = context;
  const { Employee } = data;
  return state?.id || !helpers?.isEmpty(Employee)
    ? state?.id ?? Employee?.id
    : null;
}

export function getEmployeeDataQuery(context: Context) {
  const { state } = context;
  const fieldList = [
    'category',
    'contract',
    'employeeNumber',
    'endDate',
    'entityId',
    'personId',
    'id',
    'locationId',
    'name',
    'startDate',
    'status',
    'vacationDaysDelta',
    'bankAccountHolderName',
    'iban',
    'onboardingStatus',
    'email',
    'accessKeyId',
    'equipmentNumber',
    'personalEmail',
    'bankDetailsId',
    'workAddress',
    'personalPhone',
    'holidayCalendar',
    'Employee-Entity.currency',
    'offboardingStatus',
    'firstName',
    'middleName',
    'lastName',
    'preferredFirstName',
    'preferredLastName',
    'displayName',
    'allowEmployeeAccess',
    'probationDuration',
    'probationEndDate',
    'leavesOverlapStatus',
    'externalEmployeeId',
    'migrationConfigurationId',
    'employmentCategoryId',
    'workingDays',
    'weeklyWorkingHours',
  ];
  return state?.id
    ? {
        where: {
          id: Number(state.id),
        },
        fieldList,
      }
    : {
        where: {},
        fieldList,
      };
}

export function viewInOrgchart(context: Context) {
  const { data, helpers } = context;

  helpers.navigate({
    to: `/templates/everest.hr.base/uinext/orgChart/orgChart?empId=${data?.Employee?.id}`,
  });
}

export function getSalaryData(
  context: Context,
  _value: Any,
  row: Partial<Salary.Salary>
) {
  const valueObj = {
    messages: {
      description: {
        text: '{{encrypted}}',
        color: 'amaranth',
      },
    },
    actions: [
      {
        id: 'create-unmatched',
        icon: 'key',
        label: 'View',
        onClick: () => showDecryptedSalary(context, row),
      },
    ],
  };
  return valueObj;
}

export function getBonusAmountData(
  context: Context,
  _value: Any,
  row: Partial<Bonus.BonusWithAssociation>
) {
  const valueObj = {
    messages: {
      description: {
        text: '{{encrypted}}',
        color: 'amaranth',
      },
    },
    actions: [
      {
        id: 'create-unmatched',
        icon: 'key',
        label: 'View',
        onClick: () => showBonusDetail(context, row),
      },
    ],
  };
  return valueObj;
}

export function showBonusDetail(
  context: Context,
  bonus: Partial<Bonus.BonusWithAssociation>
) {
  const { helpers, data } = context;
  const { UserKey, Employee } = data;
  const userPrivateKey = getUserPrivateKeyBase64();
  if (!userPrivateKey || !UserKey.id) {
    helpers.showToast({
      title: '{{enc.user.key}}',
      message: '{{enc.use.key.not.activated}}',
      type: 'warning',
    });
    return;
  }
  helpers.openModal({
    template: '/templates/everest.hr.base/uinext/bonus/detail',
    size: 'small',
    initialState: {
      action: 'read',
      currency: Employee?.['Employee-Entity']?.currency,
      id: bonus.id,
      editing: false,
      userKeyId: UserKey.id,
      objectKeyId: bonus.objectKeyId,
    },
  });
}

export function showDecryptedSalary(
  { helpers, data },
  salary: Partial<Salary.Salary>
) {
  const { Employee, UserKey } = data;
  const userPrivateKey = getUserPrivateKeyBase64();
  if (!userPrivateKey || !UserKey.id) {
    helpers.showToast({
      title: '{{enc.user.key}}',
      message: '{{enc.use.key.not.activated}}',
      type: 'warning',
    });
    return;
  }
  helpers.openModal({
    template: '/templates/everest.hr.base/uinext/salary/detail',
    size: 'small',
    initialState: {
      id: salary.id,
      currency: Employee?.['Employee-Entity']?.currency,
      userKeyId: UserKey.id,
      objectKeyId: salary.keyId,
      editing: false,
      employeeName: Employee.name,
    },
  });
}

export function getMasterKeyQuery({ data: { isAdmin } }: Context) {
  return isAdmin ? {} : undefined;
}

export function carryoverDaysLabel(context: Context) {
  const { state } = context;
  return `Taken Carryover Days (${state.year - 1})`;
}

export function getCarryoverVacationDaysEmployee(context: Context) {
  return context.data.CarryoverDays?.carryoverDays ?? 0;
}

export function showCarryover(context: Context) {
  const { data } = context;
  return data.CarryoverDays?.carryoverDays > 0;
}

export function getColumns(context: Context) {
  return showCarryover(context) ? 5 : 4;
}

export function getCarryoverDate(context: Context) {
  return new PlainDate(context.state.year, 1, 1)?.toISO();
}

export async function onboardEmployee({ helpers, actions, data }: Context) {
  helpers.openModal({
    template: `/templates/everest.hr.base/uinext/boarding/createBoarding`,
    size: 'xsmall',
    title: '{{create.onboarding}}',
    initialState: {
      jobId: data.CurrentPrimaryJob.id,
      offboarding: false,
      withoutEmployee: false,
    },
    onModalSubmit: actions.refetchUiModelData,
  });
}

export async function offboardEmployee({ helpers, actions, data }: Context) {
  helpers.openModal({
    template: `/templates/everest.hr.base/uinext/boarding/createBoarding`,
    size: 'xsmall',
    title: '{{create.offboarding}}',
    initialState: {
      jobId: data.CurrentPrimaryJob.id,
      offboarding: true,
      withoutEmployee: false,
    },
    onModalSubmit: actions.refetchUiModelData,
  });
}

export function showBoardButton(context: Context) {
  const { data } = context;
  const latestJobStart = new Date(
    data?.EmployeeJob?.at(0)?.startDate
  ).getTime();
  return (
    data?.Employee?.onboardingStatus ===
      EvstEmployeeBoardingStatus.NotStarted &&
    data.Employee?.offboardingStatus !== EvstEmployeeBoardingStatus.Ongoing &&
    (Date.now() - latestJobStart <= 5_184_000_000 ||
      Date.now() - new Date(data.Employee.startDate).getTime() <=
        5_184_000_000) &&
    data?.isAdmin
  );
}

export function showOffboardButton(context: Context) {
  const { Employee, isAdmin } = context.data;
  const endDate = new Date(Employee?.endDate).getTime();
  return (
    Employee?.offboardingStatus !== EvstEmployeeBoardingStatus.Ongoing &&
    Employee?.offboardingStatus !== EvstEmployeeBoardingStatus.Completed &&
    endDate - Date.now() <= 5_184_000_000 &&
    isAdmin
  );
}

export function getBoardingStepsQuery(context: Context) {
  const { data, state } = context;
  const { onboardingStatus, offboardingStatus, id } = data.Employee ?? {};
  return id &&
    ((state.boardingType === 'onboarding' &&
      onboardingStatus === EvstEmployeeBoardingStatus.Ongoing) ||
      (state.boardingType === 'offboarding' &&
        offboardingStatus === EvstEmployeeBoardingStatus.Ongoing))
    ? {
        where: {
          boardingEmployeeId: data?.Employee?.id,
          stageNodeType: EvstStageNodeType.BoardingSteps,
        },
        fieldlist: [
          'id',
          'deadline',
          'Boarding-BoardingStages.responsible',
          'stageId',
          'Boarding-BoardingStages.stage',
          'completed',
          'lastModifiedBy',
          'Boarding-Job.onboardingTemplateId',
          'Boarding-BoardingStages.BoardingSteps-BoardingTemplate.name',
        ],
      }
    : undefined;
}

export function getBoardingMatchers() {
  return {
    ['Not Completed']: 'sharpay',
    Completed: 'shamrock',
  };
}

export function addBoardingStep({ actions, helpers, data, state }: Context) {
  const employeeId = data?.Employee.id;
  helpers.openModal({
    title:
      state?.boardingType === 'onboarding'
        ? '{{add.onboarding.step}}'
        : '{{add.offboarding.step}}',
    size: 'medium',
    template: `/templates/everest.hr.base/uinext/boarding/addStage`,
    initialState: {
      id: employeeId,
      boardingType: state.boardingType,
      boardingTemplateId:
        state.boardingType === 'onboarding'
          ? data?.BoardingSteps[0]?.['Boarding-Job']?.onboardingTemplateId
          : data?.OffBoardingSteps[0]?.['Boarding-Job']?.offboardingTemplateId,
    },
    onClose: async () => {
      await actions.refetchUiModelData();
    },
  });
}

export async function markComplete(context: Context, row: Any) {
  const { actions, helpers, data, state } = context;
  const boarding = [
    {
      id: row?.data?.id,
      completed: true,
      boardingEmployeeId: data?.Employee?.id,
    },
  ];
  const result = await actions.run({
    BoardingSteps: {
      action: 'markBoardingAsCompleted',
      data: {
        boarding,
        offboarding: state.boardingType === 'offboarding',
      },
    },
  });
  if (result.error) {
    helpers.showToast({
      title: '{{error}}',
      message: `{{markAsCompleted.unsuccessful}}`,
      type: 'error',
    });
  } else {
    helpers.showToast({
      title: '{{success}}',
      message: `{{markAsCompleted.successfull}}`,
      type: 'success',
    });
  }
  await actions.refetchUiModelData({
    nodesToLoad: ['BoardingSteps', 'OffBoardingSteps'],
  });
}

export async function deleteBoardingStep(context: Context, row: Any) {
  const { actions, helpers } = context;
  await actions.submit({
    onSuccess: async () => {
      helpers.showToast({
        title: '{{success}}',
        message: `{{step.deleted.successfully}}`,
        type: 'success',
      });
      await actions.refetchUiModelData();
    },
    onError: () => {
      helpers.showToast({
        title: '{{error}}',
        message: `{{error.deleting.step}}`,
        type: 'error',
      });
    },
    transform: () => {
      const boarding = { id: row?.data?.id };
      return { BoardingSteps: { deleteBoardingStage: { boarding } } };
    },
  });
}

export function checkBoardingVisible(context: Context) {
  const { state } = context;
  return state?.boardingType === 'onboarding';
}

export function editBoardingStep(context: Context, row: Any) {
  const { actions, helpers, state } = context;
  helpers.openModal({
    title:
      state?.boardingType === 'onboarding'
        ? '{{edit.onboarding.step}}'
        : '{{edit.offboarding.step}}',
    size: 'medium',
    template: `/templates/everest.hr.base/uinext/boarding/editStage`,
    initialState: { id: row?.data?.id, boardingType: state.boardingType },
    onClose: async () => {
      await actions.refetchUiModelData();
    },
  });
}

export async function completeBoarding(context: Context) {
  const { actions, helpers, data, state } = context;
  await actions.submit({
    onSuccess: async () => {
      helpers.showToast({
        title: '{{success}}',
        message: `{{markAsCompleted.successfull}}`,
        type: 'success',
      });
      state.activeSegment = 0;
      await actions.refetchUiModelData();
    },
    onError: () => {
      helpers.showToast({
        title: '{{error}}',
        message: `{{markAsCompleted.unsuccessful}}`,
        type: 'error',
      });
    },
    transform: () => {
      const boarding = [
        {
          completed: true,
          boardingEmployeeId: data?.Employee?.id,
        },
      ];
      return {
        BoardingSteps: {
          markBoardingAsCompleted: {
            boarding,
            offboarding: state.boardingType === 'offboarding',
            all: true,
          },
        },
      };
    },
  });
}

export async function inviteUser(context: Context) {
  const { helpers, data } = context;
  helpers.openModal({
    template: `/templates/everest.base.ui/userManagement/uinext/inviteUser`,
    initialState: {
      form: {
        firstName: data.Person.firstName,
        lastName: data.Person.lastName,
        email: data.Person.email,
      },
    },
    size: 'xsmall',
    title: '{{invite.user}}',
  });
}

export function generateDocument(context: Context) {
  const { actions, helpers, data } = context;

  const id = data?.Employee?.id;
  helpers.openModal({
    title: '{{generate.documents}}',
    size: 'large',
    template: `/templates/everest.hr.base/uinext/employee/documentGenerator?id=${id}`,
    onModalSubmit: actions.refetchUiModelData,
  });
}

export async function markInComplete(context: Context, row: Any) {
  const { actions, helpers, data, state } = context;
  const boarding = [
    {
      id: row?.data?.id,
      completed: false,
      boardingEmployeeId: data?.Employee?.id,
    },
  ];
  const result = await actions.run({
    BoardingSteps: {
      action: 'markBoardingAsCompleted',
      data: {
        boarding,
        offboarding: state.boardingType === 'offboarding',
      },
    },
  });
  if (result.error) {
    helpers.showToast({
      title: '{{error}}',
      message: `{{mark.as.incomplete.unsuccessful}}`,
      type: 'error',
    });
  } else {
    helpers.showToast({
      title: '{{success}}',
      message: `{{mark.as.incompleted.successfull}}`,
      type: 'success',
    });
  }
  await actions.refetchUiModelData({
    nodesToLoad: ['BoardingSteps', 'OffBoardingSteps'],
  });
}

// export function showVacationOverlap(context: Context) {
//   const { data } = context;
//   const companyVacations =
//     data.AllLeaves?.filter((x) => x.mandatoryLeaveId) ?? [];
//   const vacations =
//     data.AllLeaves?.filter(
//       (x) =>
//         (!x.mandatoryLeaveId && x.status === EvstLeaveStatus.Approved) ||
//         x.status === EvstLeaveStatus.Requested
//     ) ?? [];
//   if (companyVacations.length > 0) {
//     for (const compVac of companyVacations) {
//       const start = new Date(compVac.startDate);
//       const end = new Date(compVac.endDate);
//       start.setMinutes(start.getMinutes() + start.getTimezoneOffset());
//       end.setMinutes(end.getMinutes() + end.getTimezoneOffset());
//       const overlap = vacations?.filter((x) => {
//         const startDate = new Date(x.startDate);
//         const endDate = new Date(x.endDate);
//         startDate.setMinutes(
//           startDate.getMinutes() + startDate.getTimezoneOffset()
//         );
//         endDate.setMinutes(endDate.getMinutes() + endDate.getTimezoneOffset());
//         return (
//           (startDate.getTime() >= start.getTime() &&
//             startDate.getTime() <= end.getTime()) ||
//           (endDate.getTime() >= start.getTime() &&
//             endDate.getTime() <= end.getTime()) ||
//           (startDate.getTime() <= start.getTime() &&
//             endDate.getTime() >= end.getTime())
//         );
//       });
//       if (overlap.length > 0) {
//         return true;
//       }
//     }
//   }
//   return false;
// }

export function viewHistory({ helpers }: Context, row) {
  helpers.navigate({
    to: `/templates/everest.hr.base/uinext/job/jobModal?id=${row?.id}`,
  });
}

export function showJobStartDateAlert(context: Context) {
  const { data } = context;
  const { EmployeeJob, Employee } = data;
  const firstJob = EmployeeJob?.[EmployeeJob?.length - 1];
  const jobStartDate = new Date(firstJob?.startDate);
  const employeeStartDate = new Date(Employee?.startDate);
  const jobDate = new Date(
    jobStartDate?.getFullYear(),
    jobStartDate?.getMonth(),
    jobStartDate?.getDate()
  ).getTime();
  const employeeDate = new Date(
    employeeStartDate?.getFullYear(),
    employeeStartDate?.getMonth(),
    employeeStartDate?.getDate()
  ).getTime();
  return jobDate === employeeDate ? false : true;
}

export function hasUserKey({ data: { RelatedUserKey } }: Context) {
  return !!RelatedUserKey?.id;
}

export function hasCurrentUserKey(context: Context) {
  const { data } = context;
  const { UserKey } = data;
  return !!UserKey?.id;
}

export function hasNoCurrentUserKey(context: Context) {
  return !hasCurrentUserKey(context);
}

export function getRelatedObjectKeysQuery({ data }: Context) {
  const { EmployeeUser } = data;
  if (!EmployeeUser?.userId) {
    return undefined;
  }
  return {
    where: {
      userId: EmployeeUser.userId,
      encryptedObjectKey: {
        $eq: null,
      },
    },
  };
}

export function getObjectKeyRequestToApprove(context: Context) {
  const { data } = context;
  const { CurrentUser, isCurrentEmployee } = data;
  if (!isCurrentEmployee || !CurrentUser?.userId) {
    return undefined;
  }
  return {
    where: {
      approverUserId: CurrentUser.userId,
      status: EvstRequestStatus.Pending,
    },
  };
}

export async function openEditPolicyModal(context: Context) {
  const { helpers, data, actions } = context;
  const employeeId = data.Employee?.id;
  helpers.openModal({
    template:
      '/templates/everest.hr.base/uinext/employee/manageEmployeeAbsencePolicy',
    initialState: {
      id: employeeId,
    },
    onClose: async () => {
      await actions.refetchUiModelData();
    },
    size: 'large',
  });
}

export async function cancelBoarding(context: Context) {
  const { actions, helpers, data, state } = context;
  {
    await actions.submit({
      onSuccess: async () => {
        helpers.showToast({
          title: '{{success}}',
          message: `{{cancellation.successful}}`,
          type: 'success',
        });
        state.activeSegment = 0;
        await actions.refetchUiModelData();
      },
      onError: () => {
        helpers.showToast({
          title: '{{error}}',
          message: `{{cancellation.unsuccessful}}`,
          type: 'error',
        });
      },
      transform: () => {
        return {
          Boarding: {
            cancelOnboarding: {
              employeeId: data?.Employee?.id,
              offboarding: state.boardingType === 'offboarding',
            },
          },
        };
      },
    });
  }
}

function openEmployeeLogModal(
  context: Context,
  {
    title,
    param,
    onModalSubmit,
  }: {
    title: string;
    param?: Record<string, unknown> & { mode: 'create' | 'edit' | 'view' };
    onModalSubmit?: () => Promise<void> | void;
  }
) {
  const { helpers, actions, data } = context;
  const { Employee } = data;
  param = {
    employeeId: Employee.id,
    ...param,
  };
  const paramText =
    Object.keys(param).length > 0
      ? '?' +
        Object.keys(param)
          .map((key) => `${key}=${param[key]}`)
          .join('&')
      : '';
  helpers.openModal({
    title,
    template: `/templates/everest.hr.base/uinext/employeeLog/employeeLog${paramText}`,
    onModalSubmit: async () => {
      await actions.refetchUiModelData({
        nodesToLoad: ['EmployeeLogs'],
      });
      if (isFunction(onModalSubmit)) {
        await onModalSubmit();
      }
      helpers.closeModal();
    },
  });
}

export async function addEmployeeLog(context: Context) {
  openEmployeeLogModal(context, {
    title: '{{employee.log}}',
    param: {
      mode: 'create',
    },
  });
}

const exitInterviewLogTypeUUID = 'baf0452c-cbf3-472b-9f36-38c49185e47f';

export async function addExitInterview(context: Context, row) {
  const { actions } = context;
  openEmployeeLogModal(context, {
    title: '{{add.exitInterview}}',
    param: {
      mode: 'create',
      logTypeUUID: exitInterviewLogTypeUUID,
    },
    onModalSubmit: async () => {
      const exitInterviewStageId = row?.data?.id;
      if (exitInterviewStageId) {
        await markComplete(context, { data: { id: exitInterviewStageId } });
        await actions.refetchUiModelData({
          nodesToLoad: ['OffBoardingSteps'],
        });
      }
    },
  });
  // if (
  //   EmployeeLogs.some(
  //     (log) => log['EmployeeLogs-EmployeeLogType']?.name === 'Exit Interview'
  //   )
  // ) {
  //   helpers.showToast({
  //     title: 'Exit Interview',
  //     message: "There's already an exit interview for this employee",
  //     type: 'error',
  //   });
  //   state.activeSegment = 8;
  //   return;
  // }
}

const exitInterviewOffBoardingStepUUID = '26474723-bb48-4331-ac34-099ad976d942';

export function isExitInterviewDisabled(context: Context, row) {
  return (
    row?.rowData?.['Boarding-BoardingStages']?.uuid !==
    exitInterviewOffBoardingStepUUID
  );
}

export function showEmployeeLog(context: Context, row) {
  openEmployeeLogModal(context, {
    title: '{{employee.log}}',
    param: {
      mode: 'view',
      id: row?.data?.id,
    },
  });
}

export async function deleteEmployeeLog(context: Context, row) {
  const { actions } = context;
  await actions.run({
    EmployeeLogs: {
      action: 'deleteEmployeeLog',
      data: {
        payload: {
          id: row?.data?.id,
        },
      },
    },
  });
  await actions.refetchUiModelData({
    nodesToLoad: ['EmployeeLogs'],
  });
}

export function getObjectKeyRequest(context: Context) {
  const { data } = context;
  const { CurrentUser, isCurrentEmployee } = data;
  if (!isCurrentEmployee || !CurrentUser?.userId) {
    return undefined;
  }
  return {
    where: {
      createdBy: CurrentUser.userId,
      status: EvstRequestStatus.Pending,
    },
  };
}

export function getBoardingCompleted(context: Context, row) {
  return row?.data?.completed === true ? 'Yes' : 'No';
}

export async function onDeleteBonusClick(
  context: Context,
  { data }: BonusRowType
): Promise<void> {
  const { actions } = context;
  await actions.submit({
    loadingMessage: '{{emp.deleting.bonus}}',
    successMessage: '{{emp.delete.bonus.success}}',
    transform: () => ({
      Bonus: {
        delete: {
          id: data.id,
        },
      },
    }),
  });
}

export function onEditBonusClick(
  context: Context,
  { data: rowData }: BonusRowType
) {
  const { helpers, actions, data } = context;
  const { Employee, UserKey } = data;
  if (!rowData?.id) {
    return;
  }
  const relatedEmployeeIds = [Employee.id];
  helpers.openModal({
    title: '{{emp.bonus.update}}',
    size: 'small',
    template: `/templates/everest.hr.base/uinext/bonus/detail`,
    initialState: {
      action: 'update',
      id: rowData.id,
      editing: true,
      currency: Employee?.['Employee-Entity']?.currency,
      userKeyId: UserKey.id,
      objectKeyId: rowData.objectKeyId,
      relatedEmployeeIds,
      employeeId: Employee.id,
    },
    onModalSubmit: async () => {
      await actions.refetchUiModelData({ nodesToLoad: ['Bonus'] });
      helpers.closeModal();
    },
  });
}

export function deleteLabel(context: Context) {
  const { state } = context;
  return state?.leaveId?.[0]?.status === EvstLeaveStatus?.Requested
    ? '{{delete.absence.request}}'
    : '{{cancel.absence.request}}';
}

export function getAbsenceMatchers(context: Context, { rowData }) {
  return rowData?.mandatoryLeaveId ? 'apartment' : null;
}

export function createNewProfile(context: Context) {
  const { helpers, data } = context;
  return new Date(data?.Employee?.endDate) < new Date() &&
    data?.Employee?.status === EvstEmployeeStatus.Inactive
    ? helpers.navigate({
        to: `/templates/everest.hr.base/uinext/employee/createEmployee?newEndDate=${data?.Employee?.endDate}&empId=${data?.Employee?.id}`,
        closeCurrentTab: false,
        initialState: { personId: data?.Person?.id },
      })
    : helpers.openModal({
        template: `/templates/everest.hr.base/uinext/employee/confirmationModal?id=${data?.Employee?.id}&task=createProfile&personId=${data?.Person?.id}`,
        initialState: { id: data?.Employee?.id },
        size: 'xsmall',
        title: '{{confirm.termination.date}}',
      });
}

export async function activateEmployee(context: Context) {
  const { actions, helpers, data, state } = context;
  const activeProfiles = data?.EmployeeProfiles?.filter(
    (profile) =>
      profile.status === EvstEmployeeStatus.Active &&
      profile.id !== data?.Employee?.id
  );
  if (activeProfiles.length > 0 && !activeProfiles[0]?.endDate) {
    helpers.openModal({
      template: `/templates/everest.hr.base/uinext/employee/confirmationModal?id=${data?.Employee?.id}&task=activate`,
      initialState: { id: data?.Employee?.id },
      size: 'xsmall',
    });
  } else {
    await actions.submit({
      onSuccess: async () => {
        helpers.showToast({
          title: '{{success}}',
          message: `{{employee.activation.successfull}}`,
          type: 'success',
        });
        await actions.refetchUiModelData();
        state.activeSegment = 0;
      },
      onError: () => {
        helpers.showToast({
          title: '{{error}}',
          message: `{{employee.activation.error}}`,
          type: 'error',
        });
      },
      transform: () => {
        return {
          Employee: {
            activateEmployee: { id: data?.Employee?.id },
          },
        };
      },
    });
  }
}

export function employeeInactive({ data }: Context) {
  return data?.Employee?.status === EvstEmployeeStatus.Inactive ||
    data?.Employee?.status === EvstEmployeeStatus.Planned
    ? true
    : false;
}

export function employeeStartDateMissing({ data }: Context) {
  return data?.Employee?.startDate === undefined ? true : false;
}

export function employeeActive({ data }: Context) {
  return (data?.Employee?.status === EvstEmployeeStatus.Active ||
    data?.Employee?.status === EvstEmployeeStatus.Planned) &&
    data?.isAdmin
    ? true
    : false;
}

export async function deactivateEmployee(context: Context) {
  const { actions, helpers, data, state } = context;

  await actions.submit({
    onSuccess: async () => {
      helpers.showToast({
        title: '{{success}}',
        message: `{{employee.activation.successfull}}`,
        type: 'success',
      });
      await actions.refetchUiModelData();
      state.activeSegment = 0;
    },
    onError: () => {
      helpers.showToast({
        title: '{{error}}',
        message: `{{employee.activation.error}}`,
        type: 'error',
      });
    },
    transform: () => {
      return {
        Employee: {
          deactivateEmployee: { id: data?.Employee?.id },
        },
      };
    },
  });
}

export function openDeactivateEmployeeModal(context: Context) {
  const { helpers, data, actions } = context;
  helpers.openModal({
    template: `/templates/everest.hr.base/uinext/employee/confirmationModal?id=${data?.Employee?.id}&task=deactivate`,
    initialState: { id: data?.Employee?.id },
    size: 'xsmall',
    onModalSubmit: async () => {
      await actions.refetchUiModelData();
    },
  });
}

export function checkOffboardingVisible(context: Context) {
  const { state } = context;
  return state.boardingType === 'offboarding';
}

export function setBoardingState(context: Context) {
  const { data, state } = context;
  state.boardingType =
    data.Employee?.onboardingStatus === EvstEmployeeBoardingStatus.Ongoing
      ? 'onboarding'
      : data.Employee?.offboardingStatus === EvstEmployeeBoardingStatus.Ongoing
        ? 'offboarding'
        : false;
}

export function getEmployeeBOQuery(context: Context) {
  const { data, helpers, state } = context;

  const { Employee } = data;
  if (state?.id) {
    return {
      where: {
        headerKey: state.id,
      },
    };
  } else if (!helpers?.isEmpty(Employee)) {
    return {
      where: {
        headerKey: Employee?.id,
      },
    };
  }
}

export function getHistoryFields() {
  return [
    'Employee.accessKeyId',
    'Employee.name',
    'Employee.displayName',
    'Employee.firstName',
    'Employee.middleName',
    'Employee.lastName',
    'Employee.preferredFirstName',
    'Employee.preferredLastName',
    'Employee.category',
    'Employee.contract',
    'Employee.employeeNumber',
    'Employee.endDate',
    'Employee.entityName',
    'Employee.personId',
    'Employee.id',
    'Employee.locationName',
    'Employee.startDate',
    'Employee.status',
    'Employee.vacationDaysDelta',
    'Employee.bankAccountHolderName',
    'Employee.iban',
    'Employee.onboardingStatus',
    'Employee.email',
    'Employee.equipmentNumber',
    'Employee.personalEmail',
    'Employee.bankDetailsId',
    'Employee.workAddress',
    'Employee.personalPhone',
    'Employee.holidayCalendar',
    'Job.startDate',
    'Job.jobManagerName',
    'Job.jobManagerDisplayName',
    'Job.departmentName',
    'Job.positionName',
    'Job.percentage',
    'Job.regionName',
    'TimesheetPolicyEmployee.timesheetPolicyName',
    'AbsencePolicyEmployee.absencePolicyName',
  ];
}

export function getCreateProfileLabel(context: Context) {
  const { data } = context;

  return new Date(data?.Employee?.endDate) < new Date() &&
    data?.Employee?.status === EvstEmployeeStatus.Inactive
    ? '{{rehire}}'
    : '{{employee.create.new.profile}}';
}

export function getEmployeeBankDetailsId(context: Context) {
  const { data, helpers } = context;

  const { Employee } = data;
  return !helpers?.isEmpty(Employee) && Employee.bankDetailsId
    ? Employee.bankDetailsId
    : null;
}

export function getEmployeePersonId(context: Context) {
  const { data, helpers, state } = context;

  const { Employee } = data;
  return state?.personId
    ? { where: { id: state?.personId } }
    : !helpers?.isEmpty(Employee) && Employee?.personId
      ? { where: { id: Employee?.personId } }
      : undefined;
}

export function getEmployeeHolidayCalendar(context: Context) {
  const { data, helpers } = context;

  const { Employee } = data;
  return !helpers?.isEmpty(Employee) && Employee.holidayCalendar
    ? Employee.holidayCalendar
    : null;
}

export function viewEmployeeProfiles(context: Context) {
  const { helpers, data } = context;
  const empIds = data?.EmployeeProfiles.map((emp) => emp.id);
  helpers.openModal({
    title: '',
    template: `/templates/everest.hr.base/uinext/employee/employeeProfileList`,
    size: 'large',
    initialState: {
      empIds: empIds,
    },
  });
}

export function multipleEmployeeProfiles({ data }: Context) {
  return data?.EmployeeProfiles?.length > 1 && data?.isAdmin ? true : false;
}

export function showEmailMissingAlert(context: Context) {
  const { data } = context;
  const { Employee } = data;
  return Employee?.email ? false : true;
}

export function getEmployeeProfilesQuery({ data }: Context) {
  const { Person } = data;
  return Person?.id
    ? { where: { personId: Person.id }, fieldlist: ['id', 'status', 'endDate'] }
    : undefined;
}

export function buildDecryptedQuery(context: Context) {
  const userPrivateKey = getUserPrivateKeyBase64();
  const employeeId = getCurrentEmployeeId(context);
  return employeeId
    ? {
        employeeId,
        userPrivateKey,
      }
    : undefined;
}

export function getAssociatedSystemKeyId(context: Context, modelUrn: string) {
  const { data } = context;
  const { EncryptedNodes } = data;
  const systemKeyId = EncryptedNodes?.find((item) => item.modelUrn === modelUrn)
    ?.systemKeyId;
  return systemKeyId;
}

export namespace SalaryController {
  export function isSalaryEncrypted(context: Context) {
    const { data } = context;
    const { EncryptedNodes } = data;
    const isSalaryEncrypted = EncryptedNodes?.some(
      (n: Pick<EncryptedNode, 'modelUrn'>) => n.modelUrn === Salary.MODEL_URN
    );
    return isSalaryEncrypted;
  }

  export function disableCreateSalary(context: Context) {
    const isSalaryEncrypted = SalaryController.isSalaryEncrypted(context);
    const hasUserKey = hasCurrentUserKey(context);
    const canCreateSalary =
      (isSalaryEncrypted && hasUserKey) || !isSalaryEncrypted;
    return !canCreateSalary;
  }

  export function createSalary(context: Context) {
    const { helpers, actions, data } = context;
    const { Employee, Salary: SalaryList } = data;
    let initialState: Record<string, unknown> = {
      employeeId: Employee?.id,
      currency: Employee?.['Employee-Entity']?.currency,
      ...(helpers.isEmpty(SalaryList)
        ? { defaultStartDate: Employee.startDate }
        : {}),
    };

    if (SalaryController.isSalaryEncrypted(context)) {
      const {
        EmployeeJob,
        CurrentEmployee,
        RelatedUserKey,
        UserKey,
        CurrentUser,
        EmployeeUser,
      } = data;
      const userKeyIds = Array.from(
        new Set(
          [RelatedUserKey?.id, UserKey?.id]?.filter(
            (k: number | undefined) => !!k
          )
        )
      );
      const primaryManagerEmployeeId = EmployeeJob?.find(
        (job: Partial<Job.JobWithAssociation>) =>
          job.status === EvstTimelineStatus.Active && job.isPrimary === true
      )?.['Job-JobManager']?.['Job-Employee']?.id;
      const relatedEmployeeIds = [
        CurrentEmployee.id,
        Employee.id,
        primaryManagerEmployeeId,
      ];
      initialState = {
        ...initialState,
        isEncrypted: true,
        relatedUserKeys: [
          {
            userId: CurrentUser.userId,
            userKeyId: UserKey?.id,
            email: CurrentUser.email,
          },
          {
            userId: EmployeeUser.userId,
            userKeyId: RelatedUserKey,
            email: EmployeeUser.email,
          },
        ],
        userKeyIds,
        relatedEmployeeIds,
        systemKeyId: getAssociatedSystemKeyId(context, Salary.MODEL_URN),
      };
    } else {
      initialState = {
        ...initialState,
        isEncrypted: false,
      };
    }
    helpers.openModal({
      title: '{{create}}',
      size: 'medium',
      template: `/templates/everest.hr.base/uinext/salary/create`,
      initialState,
      onModalSubmit: async () => {
        helpers.closeModal();
        await actions.refetchUiModelData();
      },
    });
  }

  export function onEditSalaryClick(
    context: Context,
    { data: salary }: { data: Partial<Salary.Salary> }
  ) {
    const { helpers, data, actions } = context;
    const { Employee, UserKey } = data;
    const userPrivateKey = getUserPrivateKeyBase64();
    const isSalaryEncrypted = SalaryController.isSalaryEncrypted(context);
    if (!userPrivateKey && isSalaryEncrypted) {
      helpers.showToast({
        title: '{{enc.key.notfound}}',
        message: '{{enc.user.privateKey.warning}}',
        type: 'warning',
      });
      return;
    }
    helpers.openModal({
      template: '/templates/everest.hr.base/uinext/salary/detail',
      size: 'small',
      initialState: {
        isEncrypted: isSalaryEncrypted,
        id: salary.id,
        currency: Employee?.['Employee-Entity']?.currency,
        userKeyId: UserKey.id,
        objectKeyId: salary.keyId,
        editing: true,
        employeeId: Employee?.id,
        employeeName: Employee?.name,
      },
      onModalSubmit: async () => {
        await actions.refetchUiModelData({ nodesToLoad: ['Salary'] });
      },
    });
  }

  export async function onDeleteSalaryClick(
    { actions }: Context,
    { data }: { data: Partial<Salary.Salary> }
  ) {
    await actions.submit({
      loadingMessage: '{{emp.deleting.salary}}',
      successMessage: '{{emp.delete.salary.success}}',
      transform: () => ({
        Salary: {
          delete: {
            id: data.id,
          },
        },
      }),
    });
  }
}

export namespace BonusController {
  export function isBonusEncrypted(context: Context) {
    const { data } = context;
    const { EncryptedNodes } = data;
    const isEncrypted = EncryptedNodes?.some(
      (n: Pick<EncryptedNode, 'modelUrn'>) => n.modelUrn === Bonus.MODEL_URN
    );
    return isEncrypted;
  }

  export function disableCreateBonus(context: Context) {
    const isSalaryEncrypted = BonusController.isBonusEncrypted(context);
    const hasUserKey = hasCurrentUserKey(context);
    const canCreateBonus =
      (isSalaryEncrypted && hasUserKey) || !isSalaryEncrypted;
    return !canCreateBonus;
  }

  export function onCreateBonusClick(context: Context) {
    let initialState: Record<string, unknown>;
    const { helpers, actions, data } = context;
    const {
      EmployeeJob,
      CurrentEmployee,
      Employee,
      RelatedUserKey,
      UserKey,
      CurrentUser,
      EmployeeUser,
    } = data;
    if (BonusController.isBonusEncrypted(context)) {
      if (!UserKey?.id) {
        console.error('Does not have UserKey');
        return;
      }
      const userKeyIds = Array.from(
        new Set(
          [EmployeeUser?.id, UserKey?.id]?.filter(
            (k: number | undefined) => !!k
          )
        )
      );
      const primaryManagerEmployeeId = EmployeeJob?.find(
        (job: Partial<Job.JobWithAssociation>) =>
          job.status === EvstTimelineStatus.Active && job.isPrimary === true
      )?.['Job-JobManager']?.['Job-Employee']?.id;
      const relatedEmployeeIds = [
        CurrentEmployee.id,
        Employee.id,
        primaryManagerEmployeeId,
      ];
      initialState = {
        action: 'create',
        editing: true,
        currency: Employee?.['Employee-Entity']?.currency,
        employeeId: Employee?.id,
        relatedUserKeys: [
          {
            userId: CurrentUser.userId,
            userKeyId: UserKey?.id,
            email: CurrentUser.email,
          },
          {
            userId: EmployeeUser.userId,
            userKeyId: RelatedUserKey,
            email: EmployeeUser.email,
          },
        ],
        userKeyIds,
        relatedEmployeeIds,
        isEncrypted: true,
        systemKeyId: getAssociatedSystemKeyId(context, Bonus.MODEL_URN),
      };
    } else {
      initialState = {
        action: 'create',
        editing: true,
        currency: Employee?.['Employee-Entity']?.currency,
        employeeId: Employee?.id,
        isEncrypted: false,
        relatedEmployeeIds: [CurrentEmployee.id],
      };
    }
    helpers.openModal({
      title: '{{emp.bonus.create}}',
      size: 'small',
      template: `/templates/everest.hr.base/uinext/bonus/detail`,
      initialState,
      onModalSubmit: async () => {
        await actions.refetchUiModelData({ nodesToLoad: ['Bonus'] });
        helpers.closeModal();
      },
    });
  }
}

export async function setEmployeeAccess(context: Context, value: boolean) {
  const { data, actions } = context;
  await actions.run({
    Employee: {
      action: 'updateEverestAccess',
      data: {
        employee: {
          id: data.Employee.id,
          allowEmployeeAccess: value,
          email: data.Employee.email,
        },
      },
    },
  });
}

export function isEmployeeAccessEditDisabled(context: Context) {
  const { data } = context;
  if (
    data?.isAdmin === false ||
    data?.CurrentUser?.email === data?.Employee?.email
  ) {
    return true;
  }
  return false;
}

export function setProbationEdit(context: Context, e: Any) {
  const { sharedState, data, helpers } = context;
  const employee = sharedState.getNodeInstance(
    data['Employee']?._nodeReference
  );
  if (e === EvstCalendarUnit.Custom) {
    helpers.set(employee, 'data.probationDuration.probationValue', 0);
  }
}

export function calculateProbationEndDate(context: Context) {
  const { sharedState, data, helpers } = context;
  const employee = sharedState.getNodeInstance(
    data['Employee']?._nodeReference
  );
  const probationaryEndDate = new Date(employee.data.startDate);
  if (
    employee.data.probationDuration?.probationUnit ===
      EvstCalendarUnit.Months &&
    employee.data.probationDuration.probationValue > 0
  ) {
    const months = Math.round(employee.data.probationDuration.probationValue);
    probationaryEndDate.setMonth(probationaryEndDate.getMonth() + months);
    probationaryEndDate.setDate(probationaryEndDate.getDate() + -1);
    helpers.set(
      employee,
      'data.probationEndDate',
      toPlainDate(probationaryEndDate).toISO()
    );
  } else if (
    employee.data.probationDuration?.probationUnit === EvstCalendarUnit.Weeks &&
    employee.data.probationDuration.probationValue > 0
  ) {
    const days = Math.round(employee.data.probationDuration.probationValue) * 7;
    probationaryEndDate.setDate(probationaryEndDate.getDate() + days);
    helpers.set(
      employee,
      'data.probationEndDate',
      toPlainDate(probationaryEndDate).toISO()
    );
  } else {
    helpers.set(employee, 'data.probationEndDate', null);
  }
}

export function handleProbationValueEdit(context: Context, e: Any) {
  const { sharedState, data, helpers } = context;
  const employee = sharedState.getNodeInstance(
    data['Employee']?._nodeReference
  );
  if (e > 0) {
    helpers.set(
      employee,
      'data.probationDuration.probationValue',
      Math.round(e)
    );
  } else {
    helpers.set(employee, 'data.probationDuration.probationValue', 0);
  }
}

export function updateProbationEndDate(
  context: Context,
  e: Any,
  previousValue: Any
) {
  const { sharedState, data, helpers } = context;
  const employee = sharedState.getNodeInstance(
    data['Employee']?._nodeReference
  );
  if (!e) {
    helpers.set(employee, 'data.probationEndDate', null);
    return;
  }
  const previousStartDate = new Date(previousValue);
  const newStartDate = new Date(e);
  const probationEndDate = new Date(employee.data.probationEndDate);
  if (
    employee.data.probationDuration?.probationUnit !== EvstCalendarUnit.Custom
  ) {
    if (
      employee.data.probationDuration?.probationUnit ===
        EvstCalendarUnit.Months &&
      employee.data.probationDuration?.probationValue > 0
    ) {
      const months = employee.data.probationDuration?.probationValue ?? 0;
      newStartDate.setMonth(newStartDate.getMonth() + months);
      newStartDate.setDate(newStartDate.getDate() + -1);
      helpers.set(
        employee,
        'data.probationEndDate',
        toPlainDate(newStartDate).toISO()
      );
    } else if (
      employee.data.probationDuration?.probationUnit ===
        EvstCalendarUnit.Weeks &&
      employee.data.probationDuration?.probationValue > 0
    ) {
      const days = employee.data.probationDuration?.probationValue
        ? employee.data.probationDuration?.probationValue * 7
        : 0;
      newStartDate.setDate(newStartDate.getDate() + days);
      helpers.set(
        employee,
        'data.probationEndDate',
        toPlainDate(newStartDate).toISO()
      );
    } else {
      helpers.set(employee, 'data.probationEndDate', null);
    }
  } else if (
    employee.data.probationDuration?.probationUnit ===
      EvstCalendarUnit.Custom &&
    employee.data.probationEndDate &&
    previousStartDate?.getTime() !== e?.getTime() &&
    // eslint-disable-next-line unicorn/prefer-number-properties
    isFinite(+probationEndDate)
  ) {
    helpers.openDialog({
      variant: 'warning',
      title: '{{employee.startdate.changed}}',
      message: `{{employee.startdate.changed.message}}`,
      actions: {
        secondary: {
          label: 'Confirm',
          onClick: async () => {
            helpers.closeModal();
            const timeDifference =
              probationEndDate.getTime() - previousStartDate.getTime();
            const daysDifference = Math.round(
              timeDifference / (1000 * 3600 * 24)
            );
            newStartDate.setDate(newStartDate.getDate() + daysDifference);
            helpers.set(
              employee,
              `data.probationEndDate`,
              toPlainDate(newStartDate).toISO()
            );
          },
        },
        primary: {
          label: "Don't change",
          onClick: async () => {
            helpers.closeModal();
          },
        },
      },
    });
  }
  const dateWrapper = new Date(e);
  if (
    // eslint-disable-next-line unicorn/prefer-number-properties
    !isNaN(dateWrapper.getDate()) &&
    e.getTime() < new Date(data?.CurrentPrimaryJob?.startDate).getTime()
  ) {
    helpers.openDialog({
      variant: 'warning',
      title: '{{change.job.startDate}}',
      actions: {
        primary: {
          label: 'Confirm',
          onClick: async () => {
            helpers.closeModal();
            const job = sharedState.getNodeInstance(
              data['CurrentPrimaryJob']?._nodeReference
            );
            helpers.set(job, `data.startDate`, e);
          },
        },
        secondary: {
          label: "Don't change",
          onClick: async () => {
            helpers.closeModal();
          },
        },
      },
    });
  }
}

export function isProbationValueDisabled(context: Context) {
  const { state } = context;
  return state?.probationEditMode !== 'default';
}

export function isProbationEndDateDisabled(context: Context) {
  const { state } = context;
  return state?.probationEditMode !== 'custom';
}

export function setProbationEditMode(context: Context) {
  const { data, state } = context;
  state.probationEditMode =
    data.Employee?.probationDuration?.probationUnit === EvstCalendarUnit.Custom
      ? 'custom'
      : 'default';
}

export function getProbationDuration(context: Context) {
  const { data } = context;
  if (
    data.Employee?.probationDuration?.probationUnit === EvstCalendarUnit.Custom
  ) {
    return data.Employee?.probationDuration?.probationUnit;
  } else if (
    (data.Employee?.probationDuration?.probationUnit ===
      EvstCalendarUnit.Months ||
      data.Employee?.probationDuration?.probationUnit ===
        EvstCalendarUnit.Weeks) &&
    data.Employee?.probationDuration?.probationValue > 0
  ) {
    return (
      data.Employee?.probationDuration?.probationValue +
      ' ' +
      data.Employee?.probationDuration?.probationUnit
    );
  } else {
    return;
  }
}

export function getCurrentPrimaryJob(context: Context) {
  const { state, data } = context;
  return state?.id || data?.Employee?.id
    ? {
        where: {
          employeeId: state?.id ?? data?.Employee?.id,
          $and: [
            {
              $or: [
                { status: EvstTimelineStatus.Active },
                { status: EvstTimelineStatus.Planned },
              ],
              isPrimary: true,
            },
          ],
        },
        orderBy: [
          {
            field: 'startDate',
            ordering: 'asc',
          },
        ],
        take: 1,
      }
    : undefined;
}

export function getRequestedDocsQuery(context: Context) {
  return {
    employeeId: getCurrentEmployeeId(context),
    $and: [
      { stage: { $ne: EvstRequestedDocumentsStage.Downloaded } },
      { stage: { $ne: EvstRequestedDocumentsStage.ReadyToDownload } },
    ],
  };
}
export function getCurrentSecondaryJobs(context: Context) {
  const { state, data } = context;

  return state?.id || data?.Employee?.id
    ? {
        where: {
          employeeId: state?.id ?? data?.Employee?.id,
          $and: [
            {
              $or: [
                { status: EvstTimelineStatus.Active },
                { status: EvstTimelineStatus.Planned },
              ],
              isPrimary: false,
            },
          ],
        },
        orderBy: [
          {
            field: 'startDate',
            ordering: 'desc',
          },
        ],
      }
    : undefined;
}
export function addJob(context: Context) {
  const { helpers, data, actions } = context;
  helpers.openModal({
    template: '/templates/everest.hr.base/uinext/job/jobModal',
    size: 'large',
    title: '{{add.job}}',
    initialState: {
      employeeName: data?.Employee?.name,
      employeeId: data?.Employee?.id,
      action: 'create',
    },
    onClose: async () => {
      await actions.refetchUiModelData();
    },
  });
}

export function editJob(context: Context, rowData: Any) {
  const { helpers, actions } = context;
  helpers.openModal({
    template: '/templates/everest.hr.base/uinext/job/jobModal?feat-delta=true',
    size: 'large',
    initialState: {
      id: rowData?.data?.id,
      action: 'edit',
    },
    onModalSubmit: async () => {
      helpers.closeModal();
      await actions.refetchUiModelData();
    },
  });
}

export async function deleteJob(context: Context, row: Any) {
  const { actions, helpers, data } = context;

  const directReports = await actions.run({
    CreateJob: {
      action: 'getDirectReportsByEmployeeId',
      data: { employeeId: row?.data?.employeeId },
    },
  });
  if (directReports?.CreateJob && directReports?.CreateJob[0]?.length >= 1) {
    helpers.showToast({
      title: '{{warning}}',
      message: '{{job.delete.error}}',
      type: 'warning',
    });
    return;
  }
  await actions.run({
    EmployeeJob: {
      action: 'delete',
      where: { id: row?.data?.id },
    },
  });
  await actions.run({
    employeeBusinessObject: {
      action: 'update',
      where: {
        headerKey: data?.Employee?.id,
      },
    },
  });
  await actions.refetchUiModelData();
}

export function secondaryJobsVisible(context: Context) {
  const { data } = context;
  return data?.CurrentSecondaryJobs?.length >= 1 ? true : false;
}

export function showActivePrimaryJob(context: Context) {
  const { data } = context;
  return data?.CurrentPrimaryJob?.id ? true : false;
}

export function showNoActivePrimaryJob(context: Context) {
  const { data } = context;
  return data?.CurrentPrimaryJob?.id ? false : true;
}

export async function syncCompanyHolidays(context: Context) {
  const { helpers, data, actions } = context;
  const employeeId = data?.Employee?.id;

  const timeDiff = data.AllLeaves?.map((x) => {
    return {
      id: x.id,
      timezoneDiff: getTimezoneDifference(context, new Date(x.startDate)),
    };
  });
  const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

  await actions.submit({
    onSuccess: async () => {
      helpers.showToast({
        title: '{{success}}',
        message: '{{sync.company.holidays.successful}}',
        type: 'success',
      });
      await actions.refetchUiModelData();
    },
    onError: () => {
      helpers.showToast({
        title: '{{error}}',
        message: '{{sync.company.holidays.error.message}}',
        type: 'error',
      });
    },
    transform: () => {
      return {
        Employee: {
          absoluteBOName: 'everest.hr.base/LeaveModel',
          syncCompanyHolidaysToGoogleCalendar: {
            employeeId,
            timezoneDiff: timeDiff,
            timezone,
          },
        },
      };
    },
  });
}

export function syncButtonVisible(context: Context) {
  const { data } = context;

  return data?.isCurrentEmployee || data?.isAdmin;
}

export function getTimezoneDifference(context: Context, date: Date) {
  const timezoneOffset = -date.getTimezoneOffset();
  const sign = timezoneOffset >= 0 ? '+' : '-';
  const hours = Math.floor(Math.abs(timezoneOffset) / 60)
    .toString()
    .padStart(2, '0');
  const minutes = (Math.abs(timezoneOffset) % 60).toString().padStart(2, '0');

  return `${sign}${hours}${minutes}`;
}

export function getEmployeeVacationPolicy(context: Context) {
  const { data } = context;
  if (
    data?.AbsencePolicyEmployeeVacation &&
    data?.hasVacationPolicy !== undefined &&
    data?.AbsencePolicies
  ) {
    if (data?.hasVacationPolicy === true) {
      // eslint-disable-next-line no-unsafe-optional-chaining
      const { absencePolicyId } = data?.AbsencePolicyEmployeeVacation;
      const policy = data?.AbsencePolicies?.find(
        (y: Any) => y.id === absencePolicyId
      );
      return policy;
    } else if (data?.hasVacationPolicy === false) {
      return undefined;
    }
  }
}

export async function recalculateAbsences(context: Context) {
  const { actions, helpers, data } = context;

  const { Employee } = data;

  await actions.submit({
    onSuccess: async () => {
      helpers.showToast({
        title: '{{success}}',
        message: `{{employee.recalculate.absences.successful}}`,
        type: 'success',
      });
      await actions.refetchUiModelData();
    },
    onError: () => {
      helpers.showToast({
        title: '{{error}}',
        message: `{{employee.recalculate.absences.error}}`,
        type: 'error',
      });
    },
    transform: () => {
      return {
        Employee: {
          recalculateAbsenceDays: {
            employeeId: Employee?.id,
          },
        },
      };
    },
  });
}

export function hasOverlappingLeaves({ data }: Context) {
  return (
    data?.Employee?.leavesOverlapStatus === EvstLeaveOverlap.Overlapped &&
    data?.AbsencePolicyEmployeeVacation?.absencePolicyId !== undefined
  );
}

export function getEmployeeIdIfOverlapped(context: Context) {
  const { data } = context;
  if (data?.Employee?.leavesOverlapStatus !== EvstLeaveOverlap.NotOverlapped) {
    return getCurrentEmployeeId(context);
  }
}

export function setTimesheetYear(context: Context, e: Any) {
  const { state } = context;

  // eslint-disable-next-line unicorn/prefer-number-properties
  if (!e && isNaN(state.timesheetYear)) {
    state.timesheetYear = Number(new Date().getFullYear());
  } else if (e) {
    state.timesheetYear = e;
  }
  return state.timesheetYear;
}

export function getTimesheetYear(context: Context) {
  const { state } = context;
  return state?.timesheetYear ?? Number(new Date()?.getFullYear());
}

export function listTimesheetYears(context: Context) {
  const { data } = context;
  const { Employee } = data;
  const currentYear = Number(new Date()?.getFullYear());
  const startYear = Number(new Date(Employee?.startDate)?.getFullYear());
  const current = currentYear;
  const min = currentYear < startYear ? currentYear : startYear;
  const max = current + 1;
  const years = [];
  for (let i = min; i <= max; i++) {
    years.push({ year: i });
  }
  return years;
}

export function manageDocuments(context: Context) {
  const { actions, helpers, data } = context;

  const id = data?.Employee?.id;
  helpers.openModal({
    title: '{{employeeDocuments.documentList}}',
    size: 'large',
    template: `/templates/everest.hr.base/uinext/employee/employeeDocumentManagement?id=${id}&mode=edit`,
    onModalSubmit: actions.refetchUiModelData,
  });
}

export function moreDetailsExpanded(context: Context) {
  const { state } = context;
  return state?.formError ? true : false;
}

export function getDeviceLink(context: Context) {
  const { data } = context;
  return `/templates/everest.inv.itam/uinext/userDevices/userDevices?employeeId=${data?.Employee?.id}`;
}

export function getOffBoardingStepsQuery(context: Context) {
  const { data, state } = context;
  const { onboardingStatus, offboardingStatus, id } = data.Employee ?? {};
  return id &&
    ((state.boardingType === 'onboarding' &&
      onboardingStatus === EvstEmployeeBoardingStatus.Ongoing) ||
      (state.boardingType === 'offboarding' &&
        offboardingStatus === EvstEmployeeBoardingStatus.Ongoing))
    ? {
        where: {
          boardingEmployeeId: data?.Employee?.id,
          stageNodeType: EvstStageNodeType.OffboardingSteps,
        },
        fieldlist: [
          'id',
          'deadline',
          'Boarding-BoardingStages.responsible',
          'stageId',
          'Boarding-BoardingStages.stage',
          'Boarding-BoardingStages.uuid',
          'completed',
          'lastModifiedBy',
          'Boarding-Job.offboardingTemplateId',
          'Boarding-BoardingStages.OffboardingSteps-BoardingTemplate.name',
        ],
      }
    : undefined;
}

export async function changeEmployeeToPlanned(context: Context) {
  const { actions, helpers, data, state } = context;
  await actions.submit({
    onSuccess: async () => {
      helpers.showToast({
        title: '{{success}}',
        message: `{{employee.planned.successfull}}`,
        type: 'success',
      });
      await actions.refetchUiModelData();
      state.activeSegment = 0;
    },
    onError: () => {
      helpers.showToast({
        title: '{{error}}',
        message: `{{employee.planned.error}}`,
        type: 'error',
      });
    },
    transform: () => {
      return {
        Employee: {
          changeEmployeeToPlanned: { id: data?.Employee?.id },
        },
      };
    },
  });
}

export function showEmployeePlannedButton({ data }: Context) {
  return data?.Employee?.status === EvstEmployeeStatus.Active &&
    data?.isAdmin &&
    new Date(data?.Employee.startDate).getTime() > Date.now()
    ? true
    : false;
}

export function disabledManagerApproval(context: Context, row: Any) {
  const { rowData } = row;
  const docType: Any =
    rowData?.['EmployeeRequestedDocument-EmployeeDocumentTypeId'] || {};
  const hasRightToApprove: boolean =
    (!!docType.needManagerApproval || !!docType.needManagerGrades) &&
    !!isManager(context);

  const isInManagerApproval =
    rowData.stage === EvstRequestedDocumentsStage.ManagerApproval;
  return !(hasRightToApprove && isInManagerApproval);
}

export function disableRequestedDocUpload(context: Context, row: Any) {
  const { data } = context;
  const { rowData } = row || {};

  const hasPermission = data.isAdmin;
  const isInProcessingStage =
    rowData.stage === EvstRequestedDocumentsStage.Processing;
  return !(hasPermission && isInProcessingStage);
}

export function disabledReject(context: Context, row: Any) {
  const { rowData } = row || {};
  const { data } = context;

  const docType: Any =
    rowData?.['EmployeeRequestedDocument-EmployeeDocumentTypeId'] || {};
  const managerHasRightToApprove: boolean =
    (!!docType.needManagerApproval || !!docType.needManagerGrades) &&
    !!isManager(context);

  const isInManagerApproval =
    rowData.stage === EvstRequestedDocumentsStage.ManagerApproval;
  const managerCanReject = managerHasRightToApprove && isInManagerApproval;

  const isInHrApproval =
    rowData.stage === EvstRequestedDocumentsStage.HrApproval;
  const hrCanReject = data.isAdmin && isInHrApproval;

  return !(managerCanReject || hrCanReject);
}

export function requestedDocDownloadDisabled(context: Context, row: Any) {
  const { data } = context;
  const { rowData } = row || {};

  const hasPermission = data.isAdmin;
  const isInProperStage =
    rowData.stage === EvstRequestedDocumentsStage.HrApproval ||
    rowData.stage === EvstRequestedDocumentsStage.Processing;
  return !(hasPermission && isInProperStage);
}

export function requestedDocDownloadSignedDisabled(context: Context, row: Any) {
  const { data } = context;
  const { rowData } = row || {};

  const hasPermission =
    isCurrentEmployee(context) === true || data.isAdmin || isManager(context);
  const isInProperStage =
    rowData.stage === EvstRequestedDocumentsStage.ReadyToDownload;
  return !(hasPermission && isInProperStage);
}

export function isDeleteRequestedDocDisabled(context: Context, row: Any) {
  const { data } = context;
  const { rowData } = row;
  const docType = rowData?.['EmployeeRequestedDocument-EmployeeDocumentTypeId'];

  const hasPermission =
    isCurrentEmployee(context) === true ||
    data.isAdmin ||
    !disabledManagerApproval(context, row);

  const isInInitialStage =
    (rowData.stage === EvstRequestedDocumentsStage.ManagerApproval &&
      (docType.needManagerApproval || docType.needManagerGrades)) ||
    (rowData.stage === EvstRequestedDocumentsStage.HrApproval &&
      !docType.needManagerApproval &&
      !docType.needManagerGrades);
  return !((hasPermission && isInInitialStage) || data.isAdmin);
}

export async function editRequestedDoc(context: Context, row: Any) {
  const { actions, helpers, data } = context;
  const rowData = row?.data || {};

  helpers.openModal({
    title: '{{request.documents}}',
    size: 'xsmall',
    template: `/templates/everest.hr.base/uinext/employee/requestDocument`,
    initialState: {
      employee: data?.Employee,
      employeeRequestedDocumentId: rowData.id,
      action: 'update',
    },
    onClose: () => actions.refetchUiModelData(),
  });
}

export async function deleteRequestedDoc(
  context: Context,
  row: Any
): Promise<void> {
  const { actions, helpers } = context;
  const rowData = row?.data || {};

  await actions.run({
    EmployeeRequestedDocument: {
      action: 'modifyRequestedDocument',
      data: {
        id: rowData?.id,
        action: 'delete',
      },
    },
  });
  helpers.showToast({
    title: 'Success',
    message: '{{requestDoc.deleted}}',
    type: 'success',
  });

  await actions.refetchUiModelData({
    nodesToLoad: ['EmployeeRequestedDocuments', 'EmployeeAttachments'],
  });
}
export function previewRequestedDocument(context: Context, row: Any) {
  const { actions, helpers, data } = context;
  const rowData = row?.data || {};

  const selectedLocale = data?.UserConfig?.find(
    (item: Any) => item.key === 'dateFormat' && item.type === 'uiTableView'
  ) as Any;
  const dateFormatLocale = selectedLocale?.value?.dateFormat;

  const formatValue = context.localeList.find(
    (item: Any) => item.code === dateFormatLocale
  )?.dateFormat;

  helpers.openModal({
    title: '{{preview.unsigned}}',
    size: 'large',
    template: `/templates/everest.hr.base/uinext/employee/previewRequestedDocument`,
    initialState: {
      employeeRequestedDocumentId: rowData?.id,
      dateFormat: formatValue,
    },
    onClose: actions.refetchUiModelData,
  });
}

export function uploadRequestedDoc(context: Context, row: Any) {
  const { actions, helpers, data } = context;
  const rowData = row?.data || {};

  const id = data?.Employee?.id;
  helpers.openModal({
    title: '{{upload.documents}}',
    size: 'small',
    template: `/templates/everest.hr.base/uinext/employee/uploadRequestedDocument?id=${id}&mode=edit`,
    initialState: {
      id,
      employeeRequestedDocumentId: rowData?.id,
      employeeName: data?.Employee?.displayName,
      employeeDocumentTypeLabel: rowData?.employeeDocumentTypeLabel,
      employeeDocumentTypeId: rowData?.employeeDocumentTypeId,
    },
    onClose: () =>
      actions.refetchUiModelData({
        nodesToLoad: ['EmployeeRequestedDocuments', 'EmployeeAttachments'],
      }),
  });
}

export async function giveDocManagerApproval(context: Context, row: Any) {
  const { helpers, actions } = context;
  const rowData = row?.data || {};

  const docType = rowData?.['EmployeeRequestedDocument-EmployeeDocumentTypeId'];

  if (docType.needManagerGrades) {
    helpers.openModal({
      title: '{{requestedDoc.managerApproval.title}}',
      size: 'xsmall',
      template:
        '/templates/everest.hr.base/uinext/employee/requestedDocManagerApproval',
      initialState: {
        employeeId: rowData?.Employee?.id,
        requestedDocId: rowData?.id,
        documentTypeId: rowData?.employeeDocumentTypeId,
      },
      onModalSubmit: async () => {
        await context.actions.refetchUiModelData({
          nodesToLoad: ['EmployeeRequestedDocuments'],
        });
      },
    });
  } else {
    await actions.run({
      EmployeeRequestedDocument: {
        action: 'modifyRequestedDocument',
        data: {
          id: rowData?.id,
          action: 'approve',
        },
      },
    });

    helpers.showToast({
      title: 'Success',
      message: '{{requestDoc.approved.message}}',
      type: 'success',
    });
    await actions.refetchUiModelData({
      nodesToLoad: ['EmployeeRequestedDocuments'],
    });
  }
}

export async function rejectRequestedDoc(context: Context, row: Any) {
  const { helpers, actions } = context;
  const rowData = row?.data || {};

  await actions.run({
    EmployeeRequestedDocument: {
      action: 'modifyRequestedDocument',
      data: {
        id: rowData?.id,
        action: 'reject',
      },
    },
  });
  await actions.refetchUiModelData({
    nodesToLoad: ['EmployeeRequestedDocuments'],
  });

  helpers.showToast({
    title: 'Success',
    message: '{{requestDoc.rejected.message}}',
    type: 'success',
  });
}

export function openUserPinModal(context: Context) {
  const { helpers } = context;

  helpers.openModal({
    title: '{{emp.user.pin}}',
    template: `/templates/everest.hr.base/uinext/employee/employeeUserPin`,
  });
}

export function getEmployeeLogsQuery(context: Context) {
  const { data } = context;
  const { id } = data.Employee ?? {};
  return id
    ? {
        where: {
          employeeId: data?.Employee?.id,
        },
        orderBy: [{ field: 'createdDate', ordering: 'desc' }],
      }
    : undefined;
}

export function onCreateCategoryClick(context: Context) {
  const { helpers, actions } = context;

  helpers.openModal({
    template: `/templates/everest.hr.base/uinext/employmentCategory/employmentCategoryUpsertModal`,
    size: 'xsmall',
    onClose: async () => {
      await actions.refetchUiModelData();
    },
  });
}

export async function runAction(context: Context, row: Any) {
  const { helpers, data, actions, state } = context;
  const stage = row.data['Boarding-BoardingStages'].stage;
  if (stage === 'Handout Hardware') {
    helpers.navigate({
      to: `templates/everest.inv.itam/uinext/userDevices/userDevices?employeeId=${data?.Employee?.id}`,
    });
  } else if (stage === 'Activate Personnel Record') {
    await actions.submit({
      onSuccess: async () => {
        helpers.showToast({
          title: '{{success}}',
          message: `{{employee.activation.successfull}}`,
          type: 'success',
        });
        await actions.refetchUiModelData();
        state.activeSegment = 0;
      },
      onError: () => {
        helpers.showToast({
          title: '{{error}}',
          message: `{{employee.activation.error}}`,
          type: 'error',
        });
      },
      transform: () => {
        return {
          Employee: {
            activateEmployee: { id: data?.Employee?.id },
          },
        };
      },
    });
  }
}

export async function completedCheckboxChange(
  context: Context,
  value: boolean,
  row: Any
) {
  const rowData = { data: { id: row?.rowData?.id } };
  await (value
    ? markComplete(context, rowData)
    : markInComplete(context, rowData));
}

export function isOnboardingHistoryVisible({ helpers, data }: Context) {
  if (!helpers.isEmpty(data.EmployeeJob)) {
    for (const job of data.EmployeeJob) {
      if (job.onboardingStatus === EvstEmployeeBoardingStatus.Completed) {
        return true;
      }
    }
  }
  return false;
}

export function isOffboardingHistoryVisible({ helpers, data }: Context) {
  if (!helpers.isEmpty(data.EmployeeJob)) {
    for (const job of data.EmployeeJob) {
      if (job.offboardingStatus === EvstEmployeeBoardingStatus.Completed) {
        return true;
      }
    }
  }
  return false;
}

export function navigateToOnboardingHistory({
  actions,
  helpers,
  data,
}: Context) {
  const employeeId = data?.Employee.id;
  helpers.openModal({
    title: '{{onboarding.history}}',
    size: 'medium',
    template: `/templates/everest.hr.base/uinext/boarding/boardingHistory`,
    initialState: { id: employeeId, boardingType: 'onboarding' },
    onClose: async () => {
      await actions.refetchUiModelData();
    },
  });
}

export function navigateToOffboardingHistory({
  actions,
  helpers,
  data,
}: Context) {
  const employeeId = data?.Employee.id;
  helpers.openModal({
    title: '{{offboarding.history}}',
    size: 'medium',
    template: `/templates/everest.hr.base/uinext/boarding/boardingHistory`,
    initialState: { id: employeeId, boardingType: 'offboarding' },
    onClose: async () => {
      await actions.refetchUiModelData();
    },
  });
}

export function getBoardingTabName({ state, data }: Context) {
  const title =
    state.boardingType === 'onboarding'
      ? `{{OnboardingProgress}}`
      : `{{OffboardingProgress}}`;
  return `${title} - Template: ${
    state.boardingType === 'onboarding'
      ? data?.BoardingSteps[0]?.['Boarding-BoardingStages']?.[
          'BoardingSteps-BoardingTemplate'
        ]?.name
      : data?.OffBoardingSteps[0]?.['Boarding-BoardingStages']?.[
          'OffboardingSteps-BoardingTemplate'
        ]?.name
  }`;
}
