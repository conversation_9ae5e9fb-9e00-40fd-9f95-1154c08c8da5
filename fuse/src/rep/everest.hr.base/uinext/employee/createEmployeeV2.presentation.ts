import type { RoutineValidationContext } from '@everestsystems/content-core';
import {
  DATA,
  DYNAMIC_FIELDS,
  getTranslationsMap,
  METADATA,
} from '@everestsystems/content-core';
import { PlainDate } from '@everestsystems/datetime';
import { Address } from '@pkg/everest.base/types/Address';
import { Entity } from '@pkg/everest.base/types/Entity';
import type { EvstBankAccountNumberType } from '@pkg/everest.fin.base/types/enums/BankAccountNumberType';
import type { EvstFinancialInstutionIdType } from '@pkg/everest.fin.base/types/enums/FinancialInstutionIdType';
import createNewEmployeeProfile from '@pkg/everest.hr.base/actions/Employee/createNewEmployeeProfile.action';
import createPersonAndEmployeeWithJob from '@pkg/everest.hr.base/actions/Employee/createPersonAndEmployeeWithJob.action';
import { Employee } from '@pkg/everest.hr.base/types/Employee';
import { EvstCalendarUnit } from '@pkg/everest.hr.base/types/enums/CalendarUnit';
import { Location } from '@pkg/everest.hr.base/types/Location';
import { Person } from '@pkg/everest.hr.base/types/Person';
import { Position } from '@pkg/everest.hr.base/types/Position';
import { toJSDate } from '@pkg/everest.hr.base/utils/Date/date';
import { merge, set } from 'lodash';

import type { createEmployeeV2Presentation } from '../../types/presentations/uinext/employee/createEmployeeV2';

type Validation = (
  context: RoutineValidationContext,
  data: createEmployeeV2Presentation.dataSources.employee.callbacks.query.queryData
) => Promise<boolean>;

function getRequiredFieldsValidations(): Validation {
  return async (
    context: RoutineValidationContext,
    data: createEmployeeV2Presentation.dataSources.employee.callbacks.query.queryData
  ): Promise<boolean> => {
    let flag = true;
    if (!data.firstName) {
      context.addError([], 'Missing first name', 'firstName');
      flag = false;
    }
    if (!data.lastName) {
      context.addError([], 'Missing last name', 'lastName');
      flag = false;
    }
    if (!data.category) {
      context.addError([], 'Missing category', 'category');
      flag = false;
    }
    if (!data.startDate) {
      context.addError([], 'Missing start date', 'startDate');
      flag = false;
    }
    if (!data.Job.departmentId) {
      context.addError(['Job'], 'Missing department', 'departmentId');
      flag = false;
    }
    return flag;
  };
}

function getRequiredValidations(): Validation[] {
  return [getRequiredFieldsValidations()];
}

class EmployeeDataSource
  implements createEmployeeV2Presentation.dataSources.employee.implementation
{
  async execute_addAddress({
    input,
  }: createEmployeeV2Presentation.dataSources.employee.routines.addAddress.executeInput): Promise<createEmployeeV2Presentation.dataSources.employee.routines.addAddress.executeOutput> {
    this.setAddressData(input.address);
  }
  private data: createEmployeeV2Presentation.dataSources.employee.callbacks.query.queryData =
    {
      Header: {},
      WorkData: {},
      PersonalData: {},
      Job: {},
      ExpenseData: {},
      AbsenceData: {},
    };
  private metadata: createEmployeeV2Presentation.dataSources.employee.callbacks.query.queryMetadata =
    {
      ExpenseData: {},
    };
  private validationQueue: Validation[] = [];

  async update_AbsenceData({
    fieldName,
    newFieldValue,
  }: createEmployeeV2Presentation.dataSources.employee.callbacks.update_AbsenceData.input): Promise<createEmployeeV2Presentation.dataSources.employee.callbacks.update_AbsenceData.output> {
    set(this.data.AbsenceData, [fieldName], newFieldValue);
  }
  async update_ExpenseData({
    session,
    fieldName,
    newFieldValue,
  }: createEmployeeV2Presentation.dataSources.employee.callbacks.update_ExpenseData.input): Promise<createEmployeeV2Presentation.dataSources.employee.callbacks.update_ExpenseData.output> {
    set(this.data.ExpenseData, [fieldName], newFieldValue);
    if (fieldName === 'bankAccountNumberType' && newFieldValue) {
      const employeeTranslations: Map<string, string> =
        await getEmployeeTranslations(session);
      this.metadata.ExpenseData[DYNAMIC_FIELDS] = merge(
        this.metadata.ExpenseData[DYNAMIC_FIELDS],
        {
          bankAccountNumber: {
            visible: true,
            label:
              this.data.ExpenseData.bankAccountNumberType === 'iban'
                ? employeeTranslations.get('iban')
                : this.data.ExpenseData.bankAccountNumberType === 'achOrWire'
                  ? employeeTranslations.get('bankDetails.accountNumber')
                  : employeeTranslations.get('bankDetails.accountDetails'),
          },
          financialInstitutionIdType: {
            visible: true,
            label: employeeTranslations.get('bankDetails.bankCodeType'),
          },
          financialInstitutionId: {
            visible:
              this.data.ExpenseData.bankAccountNumberType === 'iban' ||
              this.data.ExpenseData.bankAccountNumberType === 'achOrWire'
                ? true
                : false,
            label:
              this.data.ExpenseData.bankAccountNumberType === 'iban'
                ? employeeTranslations.get('bankDetails.bankCode')
                : this.data.ExpenseData.bankAccountNumberType === 'achOrWire'
                  ? employeeTranslations.get('bankDetails.routingNumber')
                  : employeeTranslations.get('bankDetails.bankCode'),
          },
        }
      );
      this.data.ExpenseData.bankAccountHolderName =
        this.data.firstName + ' ' + this.data.lastName;
    }
  }
  async update_Job({
    session,
    fieldName,
    newFieldValue,
    oldFieldValue,
  }: createEmployeeV2Presentation.dataSources.employee.callbacks.update_Job.input): Promise<createEmployeeV2Presentation.dataSources.employee.callbacks.update_Job.output> {
    set(this.data.Job, [fieldName], newFieldValue);
    if (fieldName === 'positionId') {
      const position = await Position.read(
        session,
        { id: newFieldValue as number },
        ['name']
      );
      this.data.Header.description = position?.name;
    }
    if (fieldName === 'departmentId' && newFieldValue !== oldFieldValue) {
      this.data.Job.positionId = null;
    }
  }
  async update_PersonalData({
    fieldName,
    newFieldValue,
  }: createEmployeeV2Presentation.dataSources.employee.callbacks.update_PersonalData.input): Promise<createEmployeeV2Presentation.dataSources.employee.callbacks.update_PersonalData.output> {
    set(this.data.PersonalData, [fieldName], newFieldValue);
  }
  async update_WorkData({
    fieldName,
    newFieldValue,
  }: createEmployeeV2Presentation.dataSources.employee.callbacks.update_WorkData.input): Promise<createEmployeeV2Presentation.dataSources.employee.callbacks.update_WorkData.output> {
    set(this.data.WorkData, [fieldName], newFieldValue);
  }

  public async query({
    session,
    queryReason,
    parameters,
  }: createEmployeeV2Presentation.dataSources.employee.callbacks.query.input): Promise<createEmployeeV2Presentation.dataSources.employee.callbacks.query.combinedOutput> {
    if (parameters.employeeId && queryReason === 'externalRequest') {
      const employeeData = await Employee.read(
        session,
        { id: parameters.employeeId },
        [
          'firstName',
          'middleName',
          'lastName',
          'category',
          'startDate',
          'endDate',
          'preferredFirstName',
          'preferredLastName',
          'locationId',
          'entityId',
          'externalEmployeeId',
          'equipmentNumber',
          'accessKeyId',
          'probationEndDate',
          'probationDuration',
          'email',
          'personId',
          'personalEmail',
          'personalPhone',
        ]
      );

      //Employment data
      this.data.firstName = employeeData.firstName;
      this.data.middleName = employeeData.middleName;
      this.data.lastName = employeeData.lastName;
      this.data.category = employeeData.category;
      this.data.preferredFirstName = employeeData.preferredFirstName;
      this.data.preferredLastName = employeeData.preferredLastName;
      this.data.locationId = employeeData.locationId;
      this.data.entityId = employeeData.entityId;
      this.data.externalEmployeeId = employeeData.externalEmployeeId;
      this.data.equipmentNumber = employeeData.equipmentNumber;
      this.data.accessKeyId = employeeData.accessKeyId;
      this.data.probationEndDate = employeeData.probationEndDate;
      this.data.probationDuration = employeeData.probationDuration;

      const personData = await Person.read(
        session,
        { id: employeeData.personId },
        ['id', 'phoneNumber', 'gender', 'dateOfBirth', 'addressId']
      );
      if (employeeData.entityId) {
        const entity = await Entity.read(
          session,
          { id: employeeData.entityId },
          ['currency']
        );
        this.data.ExpenseData.reimbursementCurrency = entity.currency;
      }
      //Contact Data
      this.data.WorkData.workEmail = employeeData.email;
      this.data.WorkData.workPhone = personData.phoneNumber;

      //Personal Data
      this.data.PersonalData.gender = personData.gender;
      this.data.PersonalData.dateOfBirth = personData.dateOfBirth;
      this.data.PersonalData.personalEmail = employeeData.personalEmail;
      this.data.PersonalData.personalPhone = employeeData.personalPhone;
      if (personData.addressId) {
        const address = await Address.read(
          session,
          { id: personData.addressId },
          ['line1', 'line2', 'city', 'stateProvince', 'zipCode', 'country']
        );
        this.setAddressData(address);
      }
    }
    const title =
      this.data.firstName && this.data.lastName
        ? `${this.data.firstName} ${this.data.lastName}`
        : this.data.firstName || this.data.lastName || '';

    const tabTitle =
      this.data.firstName && this.data.lastName
        ? `${this.data.firstName} ${this.data.lastName}`
        : this.data.firstName || this.data.lastName || 'Create Employee';
    if (!this.data.probationDuration?.probationValue) {
      this.data.probationDuration = {
        ...this.data.probationDuration,
        probationValue: 0,
      };
    }
    if (!this.data.probationDuration?.probationUnit) {
      this.data.probationDuration = {
        ...this.data.probationDuration,
        probationUnit: EvstCalendarUnit.Months,
      };
    }
    if (
      this.data.probationDuration?.probationUnit !== EvstCalendarUnit.Custom
    ) {
      this.metadata.probationEndDate = {
        ...this.metadata.probationEndDate,
        editable: false,
      };
    }
    if (
      this.data.probationDuration?.probationUnit === EvstCalendarUnit.Custom
    ) {
      this.metadata.probationEndDate = {
        ...this.metadata.probationEndDate,
        editable: true,
      };
      this.data.probationDuration.probationValue = 0;
    }
    this.data.Job[METADATA] = {
      positionId: {
        typeParameters: {
          departmentId: this.data.Job.departmentId ?? undefined,
        },
      },
    };
    const employeeTranslations: Map<string, string> =
      await getEmployeeTranslations(session);
    if (queryReason === 'externalRequest') {
      this.metadata.ExpenseData[DYNAMIC_FIELDS] = {
        reimbursementCurrency: {
          type: { urn: 'urn:evst:everest:appserver:primitive:Text' },
          label: employeeTranslations.get('reimbursement.currency'),
        },
        bankAccountNumberType: {
          type: {
            urn: 'urn:evst:everest:fin/base:field/node:BankDetails.bankAccountNumberType',
          },
          label: employeeTranslations.get('payment.method'),
        },
        bankAccountHolderName: {
          type: { urn: 'urn:evst:everest:appserver:primitive:Text' },
          label: employeeTranslations.get('bankAccountHolder'),
        },
        bankAccountNumber: {
          type: { urn: 'urn:evst:everest:appserver:primitive:Text' },
          label: employeeTranslations.get('bankDetails.accountNumber'),
          visible: false,
        },
        financialInstitutionIdType: {
          type: {
            urn: 'urn:evst:everest:fin/base:field/node:BankDetails.financialInstitutionIdType',
          },
          label: employeeTranslations.get('bankDetails.bankCodeType'),
          visible: false,
        },
        financialInstitutionId: {
          type: { urn: 'urn:evst:everest:appserver:primitive:Text' },
          label: employeeTranslations.get('iban'),
          visible: false,
        },
      };
    }
    return {
      [DATA]: {
        ...this.data,
        Header: { ...this.data.Header, title, tabTitle },
      },
      [METADATA]: this.metadata,
    };
  }

  public async update({
    session,
    fieldName,
    newFieldValue,
  }: createEmployeeV2Presentation.dataSources.employee.callbacks.update.input): Promise<createEmployeeV2Presentation.dataSources.employee.callbacks.update.output> {
    set(this.data, [fieldName], newFieldValue);

    if (fieldName === 'category') {
      this.metadata.endDate = {
        visible: newFieldValue === 'External',
      };
    }
    if (fieldName === 'probationDuration') {
      if (newFieldValue?.['probationUnit'] === EvstCalendarUnit.Custom) {
        this.data.probationDuration.probationValue = 0;
      } else if (
        newFieldValue?.['probationUnit'] !== EvstCalendarUnit.Custom &&
        this.data.probationDuration.probationUnit &&
        this.data.probationDuration.probationValue
      ) {
        const probationEndDate = getProbationEndDate(
          this.data.startDate,
          this.data.probationDuration.probationValue,
          this.data.probationDuration.probationUnit
        );
        this.data.probationEndDate = probationEndDate;
      } else if (newFieldValue?.['probationValue'] === 0) {
        this.data.probationEndDate = null;
      }
    }
    if (
      fieldName === 'startDate' &&
      newFieldValue &&
      this.data.probationDuration?.probationValue &&
      this.data.probationDuration?.probationUnit &&
      this.data.probationDuration?.probationUnit !== EvstCalendarUnit.Custom
    ) {
      const day = (newFieldValue as PlainDate).day;
      const month = (newFieldValue as PlainDate).month;
      const year = (newFieldValue as PlainDate).year;
      const newDate = new PlainDate(year, month, day);
      const probationEndDate = getProbationEndDate(
        newDate,
        this.data.probationDuration.probationValue,
        this.data.probationDuration.probationUnit
      );
      this.data.probationEndDate = probationEndDate;
    } else if (
      fieldName === 'startDate' &&
      !newFieldValue &&
      this.data.probationDuration?.probationValue &&
      this.data.probationDuration?.probationUnit &&
      this.data.probationDuration?.probationUnit !== EvstCalendarUnit.Custom
    ) {
      this.data.probationEndDate = null;
    }
    if (fieldName === 'entityId') {
      const entity = await Entity.read(
        session,
        { id: newFieldValue as number },
        ['currency']
      );
      this.data.ExpenseData.reimbursementCurrency = entity.currency;
    }
    if (fieldName === 'locationId') {
      const location = await Location.read(
        session,
        { id: newFieldValue as number },
        ['holidayCalendarId']
      );
      this.data.AbsenceData.holidayCalendarId = location.holidayCalendarId;
    }
  }

  public async validate_save({
    context,
    validateReason,
  }: createEmployeeV2Presentation.dataSources.employee.routines.save.validateInput): Promise<createEmployeeV2Presentation.dataSources.employee.routines.save.validateOutput> {
    const validations = [...this.validationQueue];

    if (validateReason === 'executionRequest') {
      validations.push(...getRequiredValidations());
    }

    const failedValidations: Validation[] = [];

    for (const validation of validations) {
      const validationResult = await validation(context, this.data);
      if (validationResult === false) {
        failedValidations.push(validation);
      }
    }

    this.validationQueue = failedValidations;

    return validateReason === 'earlyEvaluation'
      ? true
      : failedValidations.length === 0;
  }

  public async execute_save({
    parameters,
    session,
  }: createEmployeeV2Presentation.dataSources.employee.routines.save.executeInput): Promise<createEmployeeV2Presentation.dataSources.employee.routines.save.executeOutput> {
    const addressData = this.data.PersonalData.address ?? {};
    const employeeData = {
      probationDuration: this.data.probationDuration,
      category: this.data.category,
      startDate: this.data.startDate,
      probationEndDate: this.data.probationEndDate,
      endDate: this.data.endDate,
      entityId: this.data.entityId,
      locationId: this.data.locationId,
      holidayCalendar: this.data.AbsenceData.holidayCalendarId,
      accessKeyId: this.data.accessKeyId,
      equipmentNumber: this.data.equipmentNumber,
      externalEmployeeId: this.data.externalEmployeeId,
      workAddress: this.data.WorkData.workAddress,
      personalEmail: this.data.PersonalData.personalEmail,
      personalPhone: this.data.PersonalData.personalPhone,
    };
    const personData = {
      firstName: this.data.firstName,
      lastName: this.data.lastName,
      middleName: this.data.middleName,
      preferredFirstName: this.data.preferredFirstName,
      preferredLastName: this.data.preferredLastName,
      email: this.data.WorkData.workEmail,
      phoneNumber: this.data.WorkData.workPhone,
      gender: this.data.PersonalData.gender,
      dateOfBirth: this.data.PersonalData.dateOfBirth,
      name: `${this.data.firstName} ${this.data.lastName}`,
      startDate: this.data.startDate,
      endDate: this.data.endDate,
    };
    const jobData = {
      percentage: this.data.Job.percentage,
      departmentId: this.data.Job.departmentId,
      jobManager: this.data.Job.jobManager,
      positionId: this.data.Job.positionId,
      regionId: this.data.Job.regionId,
    };
    const expenseData = {
      bankAccountNumberType: this.data.ExpenseData
        .bankAccountNumberType as EvstBankAccountNumberType,
      bankAccountHolderName: this.data.ExpenseData
        .bankAccountHolderName as string,
      bankAccountNumber: this.data.ExpenseData.bankAccountNumber as string,
      financialInstitutionIdType: this.data.ExpenseData
        .financialInstitutionIdType as EvstFinancialInstutionIdType,
      financialInstitutionId: this.data.ExpenseData
        .financialInstitutionId as string,
    };
    const policies = [];
    if (this.data.AbsenceData.vacationPolicyId) {
      policies.push({ id: this.data.AbsenceData.vacationPolicyId });
    }
    if (this.data.AbsenceData.sickPolicyId) {
      policies.push({ id: this.data.AbsenceData.sickPolicyId });
    }
    return parameters.employeeId
      ? await createNewEmployeeProfile(
          session,
          addressData,
          {
            ...personData,
            startDate: toJSDate(personData.startDate),
            endDate: toJSDate(personData.endDate),
            id: parameters.personId,
          },
          {
            ...employeeData,
            startDate: toJSDate(employeeData.startDate),
            endDate: toJSDate(employeeData.endDate),
          },
          jobData,
          [],
          policies,
          expenseData,
          parameters.newEndDate,
          parameters.employeeId
        )
      : await createPersonAndEmployeeWithJob(
          session,
          addressData,
          {
            ...personData,
            startDate: toJSDate(personData.startDate),
            endDate: toJSDate(personData.endDate),
          },
          {
            ...employeeData,
            startDate: toJSDate(employeeData.startDate),
            endDate: toJSDate(employeeData.endDate),
          },
          jobData,
          [],
          policies,
          expenseData
        );
  }
  private setAddressData(
    address: createEmployeeV2Presentation.dataSources.employee.levels['PersonalData']['address']
  ) {
    this.data.PersonalData.address = {
      line1: address?.line1,
      line2: address?.line2 ? `${address.line2}, ` : '',
      city: address?.city,
      stateProvince: address?.stateProvince,
      zipCode: address?.zipCode,
      country: address?.country,
    };
    this.data.PersonalData.addressForm = `${address?.line1}, ${
      address?.line2 ? `${address.line2}, ` : ''
    }${address?.city}, ${address?.stateProvince}, ${address?.zipCode}, ${address?.country}`;
  }
}

export default {
  employee() {
    return new EmployeeDataSource();
  },
} satisfies createEmployeeV2Presentation.implementation;

function getProbationEndDate(
  newStart: PlainDate,
  probationValue: number,
  probationUnit: EvstCalendarUnit
): PlainDate {
  if (!newStart) {
    return null;
  }
  if (probationUnit === EvstCalendarUnit.Months && probationValue > 0) {
    const months = probationValue;
    return newStart.plus({ months: months }).plus({ days: -1 });
  } else if (probationUnit === EvstCalendarUnit.Weeks && probationValue > 0) {
    const days = probationValue * 7;
    return newStart.plus({ days: days });
  } else {
    return null;
  }
}

async function getEmployeeTranslations(session) {
  return await getTranslationsMap(
    session,
    [
      'payment.method',
      'employee.bankAccount',
      'reimbursement.currency',
      'bankAccountHolder',
      'bankDetails.accountNumber',
      'bankDetails.accountNumber',
      'bankDetails.bankCodeType',
      'iban',
      'bankDetails.accountDetails',
      'bankDetails.routingNumber',
      'bankDetails.bankCode',
    ],
    'everest.hr.base/employee'
  );
}
