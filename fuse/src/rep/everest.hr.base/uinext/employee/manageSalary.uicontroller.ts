// @i18n:employee
// @i18n:everest.base.encryption/encryption

import {
  arrayBufferToBinaryStr,
  base64ToBinaryStr,
  binaryStrToArrayBuffer,
  binaryStrToBase64,
  createObjectKey,
  encryptObjectKey,
  encryptValue,
  exportObject<PERSON>ey,
  generateSalt,
  importPublic<PERSON>ey,
} from '@pkg/everest.base.encryption/public/utils.uicontroller';
import type { ObjectKey } from '@pkg/everest.base.encryption/types/ObjectKey';
import type { ManageSalaryUiTemplate } from '@pkg/everest.hr.base/types/uiTemplates/uinext/employee/manageSalary.ui';

type ManageSalaryContext = ManageSalaryUiTemplate.ManageSalaryContext;

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type Any = any;

type Context = ManageSalaryContext & {
  state: {
    // you can extend and improve the state type here
    add: Any;
  };
};

const SALARY_MODEL = 'Salary';

export function cancel(context: Context) {
  const { helpers } = context;

  helpers.closeModal();
}

export async function onSave(context: Context) {
  const { data, actions, helpers, sharedState } = context;
  const { Employee, MasterKey } = data;
  const { publicKey: publicMasterKeyBase64 } = MasterKey;
  let publicMasterKeyBuf: ArrayBuffer, publicMasterKey: CryptoKey;
  try {
    publicMasterKeyBuf = binaryStrToArrayBuffer(
      base64ToBinaryStr(publicMasterKeyBase64)
    );
    publicMasterKey = await importPublicKey(publicMasterKeyBuf);
  } catch {
    helpers.showToast({
      type: 'error',
      message: '{{enc.invalidKey}}',
    });
  }
  const salaryData = sharedState.getModelData(SALARY_MODEL)?.instance;
  for await (const { data: salary, action } of salaryData) {
    if (action === 'create') {
      salary.employeeId = (Employee as Any).id;
      const objectKey = await createObjectKey();
      const encryptionSalt = generateSalt();
      const objectKeyBuf = await exportObjectKey(objectKey);
      const encryptedObjectKey = await encryptObjectKey(
        objectKeyBuf,
        publicMasterKey
      );
      const encryptedSalary = await encryptValue(
        objectKey,
        salary.baseSalary.toString(),
        encryptionSalt
      );
      salary.encryptedSalary = binaryStrToBase64(
        arrayBufferToBinaryStr(encryptedSalary)
      );
      salary.encryptionSalt = binaryStrToBase64(
        arrayBufferToBinaryStr(encryptionSalt)
      );
      salary.encryptedObjectKey = binaryStrToBase64(
        arrayBufferToBinaryStr(encryptedObjectKey)
      );
      salary.baseSalary = null;
      const response = await actions.run({
        ObjectKey: {
          action: 'createObjectKey',
          data: {
            keyToCreate: {
              relatedPackage: 'everest.hr.base',
              modelId: 'everest.hr.base/SalaryModel.Salary',
              keySalt: salary.encryptionSalt,
              encryptedByMasterKey: salary.encryptedObjectKey,
            },
          },
        },
      });
      const symmetricKey = response?.ObjectKey as Partial<ObjectKey.ObjectKey>;
      salary.keyId = symmetricKey.id;
    }
    salary.endDate ??= null;
    delete salary._nodeReference;
  }
  await actions.submit({
    transform: () => ({
      Salary: salaryData,
    }),
    onSuccess: async () => {
      await helpers.currentModalSubmitCallback();
    },
  });
}

export function getSalaryType(context: Context, rowData: Any) {
  const { data } = context;

  //Currently considers only monthly payout
  const payoutPeriod = data?.PayoutPeriod?.find(
    (pay: Any) => pay.codeValue === rowData?.data?.payoutPeriod
  )?.text;
  if (rowData?.data?.baseSalary) {
    return `{{fixed.salary}} - ${payoutPeriod}`;
  } else if (rowData?.data?.hourlyWage) {
    return `{{hourly.wage}}- ${payoutPeriod}`;
  }
}

export function addSalary(context: Context) {
  const { state } = context;

  state.add = true;
}

export function getSalaryTypeOptions(context: Context) {
  const { data } = context;

  const options = [];
  data?.SalaryType?.forEach(
    (type: Any) => options?.push({ label: type?.text, value: type?.codeValue })
  );
  return options;
}

export function checkAddSalary(context: Context) {
  const { state } = context;

  return state?.add === true;
}

export function onManageMasterKeyClick({ helpers }: Context) {
  helpers.navigate({
    to: '/templates/everest.base.encryption/uinext/masterKeyMgmt',
    closeCurrentTab: false,
  });
}

export function hasNoMasterKey({ data, helpers }: Context) {
  return helpers.isEmpty(data.MasterKey);
}

export function isEditingRow(_context: Context, { rowData }: Any) {
  return !!rowData?.id;
}
