{"version": 3, "uicontroller": ["everest.hr.base/uinext/employee/employeeV2.uicontroller.ts", "everest.hr.base/uinext/shared/statusMatcher.uicontroller.ts", "everest.hr.base/uinext/shared/helpers.uicontroller.js"], "uimodel": {"presentation": {"urn": "urn:evst:everest:hr/base:presentation:uinext/employee/employeeV2", "parameters": {"mode": "view", "employeeId": "@state:id"}}}, "uiview": {"layout": "everest.base.ui/layouts/uilayout/segmented/avatar/avatar", "props": {"tabTitle": "@binding:header.tabTitle", "title": "@binding:header.title", "status": "@binding:header.status", "description": "@binding:header.description", "config": {"allowRefreshData": true, "stretch": true}, "i18n": ["hrbase", "employee", "leaveRequest", "everest.base.encryption/encryption", "securityManagement"], "stretch": true, "background": "grey", "avatar": {"isEditable": true, "size": "xlarge", "src": "@binding:header.photoSrc", "active": false, "fileId": "@binding:header.photo", "loading": "@state:photoIsLoading", "onSuccess": "@controller:onPhotoSuccess", "onRemove": "@controller:onPhotoRemove", "name": "@binding:header.title"}, "headerActions": {"direction": "vertical", "align": "right", "content": [{"label": "{{edit.details}}", "presentationAction": "@action:revise", "visible": "@controller:showEditButton()", "variant": "primary"}, {"label": "{{save}}", "onClick": "@controller:onSaveClick", "visible": "@controller:showCancelButton()", "variant": "primary"}, {"label": "{{cancel}}", "presentationAction": "@action:cancel", "visible": "@controller:showCancelButton()", "variant": "secondary"}, {"label": "{{more.actions}}", "variant": "secondary", "visible": "@binding:controlFlags.viewMode", "actions": [{"label": "{{employee.activate}}", "onClick": "@controller:activateEmployee", "variant": "secondary", "visible": "@controller:employeeInactive()", "disabled": "@controller:employeeStartDateMissing()", "tooltip": {"text": "{{enter.endDate}}", "placement": "right", "visible": "@controller:employeeStartDateMissing()"}}, {"label": "{{view.in.orgChart}}", "onClick": "@controller:viewInOrgchart", "variant": "secondary"}, {"label": "{{view.employee.profiles}}", "onClick": "@controller:viewEmployeeProfiles", "visible": "@controller:multipleEmployeeProfiles()", "variant": "secondary"}, {"label": "@controller:getCreateProfileLabel()", "onClick": "@controller:createNewProfile", "visible": "@binding:isAdmin", "variant": "secondary"}, {"label": "{{board.employee}}", "onClick": "@controller:boardEmployee", "visible": "@controller:showBoardButton()", "variant": "secondary"}, {"label": "{{offboard.employee}}", "onClick": "@controller:offboardEmployee", "visible": "@controller:showOffboardButton()", "variant": "secondary"}, {"label": "{{employee.deactivate}}", "onClick": "@controller:openDeactivateEmployeeModal", "variant": "secondary", "visible": "@controller:employeeActive()"}, {"label": "{{employee.change.to.planned}}", "onClick": "@controller:changeEmployeeToPlanned", "variant": "secondary", "visible": "@controller:showEmployeePlannedButton()"}]}]}, "stepsContent": [{"title": "{{employment.details}}", "components": [{"component": "FieldGroup", "section": {"grid": {"size": "12"}}, "props": {"columns": "6", "presentationDataSet": "@dataSet:employmentDetails", "fields": ["employeeNumber", "firstName", "middleName", "lastName", {"field": "startDate", "fieldProps": {"onChange": "@controller:updateProbationEndDate"}}, "endDate", "category", "entityId", "locationId", "probationDuration", "probationEndDate"], "moreDetails": {"label": "{{more.employee.details}}", "presentationDataSet": "@dataSet:employmentDetails", "fields": ["preferredFirstName", "preferredLastName", "accessKeyId", "equipmentNumber", "externalEmployeeId", {"field": "employeeDevices", "fieldProps": {"link": {"to": "@controller:getDeviceLink()"}}}]}}}, {"component": "<PERSON><PERSON>", "size": 12, "variant": "warning", "title": "Warning", "content": "{{getJobStartDateContent}}", "isVisible": "@binding:controlFlags.startDateMismatch"}, {"component": "UserInfo", "section": {"title": "{{jobs.and.reporting}}", "selectedGroup": 0, "grid": {"size": 12}, "actions": [{"label": "{{actions}}", "variant": "secondary", "visible": "@binding:controlFlags.isAdminVisible", "disabled": "@binding:controlFlags.editMode", "actions": [{"label": "{{add.job}}", "onClick": "@controller:addJob"}, {"label": "{{view.job.history}}", "variant": "secondary", "visible": "@binding:controlFlags.isAdminVisible", "onClick": "@controller:onEditJobsClick"}]}]}, "props": {"emptyMessage": "{{no.active.primary.job}}"}, "visible": "@binding:controlFlags.noPrimaryJob"}, {"component": "WidgetGroup", "customId": "employeeJobs", "visible": "@binding:controlFlags.hasPrimaryJob", "section": {"title": "{{jobs.and.reporting}}", "selectedGroup": 0, "grid": {"size": 12}, "actions": [{"label": "{{actions}}", "variant": "secondary", "visible": "@binding:controlFlags.isAdminVisible", "disabled": "@binding:controlFlags.editMode", "actions": [{"label": "{{add.job}}", "onClick": "@controller:addJob"}, {"label": "{{view.job.history}}", "variant": "secondary", "onClick": "@controller:onEditJobsClick"}]}]}, "props": {"widgets": [{"component": "FieldGroup", "customId": "jobsAndReportingFieldGroup", "section": {"grid": {"size": "12"}, "variant": "card"}, "label": "{{primary}}", "props": {"columns": "4", "presentationDataSet": "@dataSet:jobDetails"}}, {"component": "Table", "size": 12, "label": "{{secondary}}", "visible": "@binding:controlFlags.hasSecondaryJob", "props": {"data": "@binding:secondaryJobDetails", "rowActions": {"variant": "default", "columnLabel": "{{actions}}", "columnPosition": 0, "pinned": "left", "actions": [{"content": "{{edit.job}}", "onClick": "@controller:edit<PERSON><PERSON>"}, {"content": "{{delete.job}}", "onClick": "@controller:delete<PERSON><PERSON>"}]}, "columns": [{"field": "type", "cellVariant": {"variant": "badge", "matchers": "@controller:jobTypeMatcher()"}, "pinned": "left", "maxWidth": 100}, {"field": "status", "cellVariant": {"variant": "badge", "matchers": "@controller:getTimelineStatus('status')"}, "pinned": "left", "maxWidth": 100}, {"field": "positionId", "headerName": "{{position}}", "minWidth": 300, "fieldProps": {"fontWeight": "bold"}, "pinned": "left"}, "departmentId", "jobManagerDisplayName", "percentage", "regionId", "startDate", "endDate"]}}]}}, {"component": "FieldGroup", "section": {"title": "{{contact.information}}", "grid": {"size": "12"}}, "props": {"columns": "3", "presentationDataSet": "@dataSet:employeeContactDetails"}}]}, {"title": "{{personal.details}}", "components": [{"component": "FieldGroup", "section": {"grid": {"size": "12"}}, "props": {"columns": "3", "presentationDataSet": "@dataSet:personalDetails", "fields": ["name", {"field": "gender", "fieldProps": {"component": "Select", "size": "1", "label": "{{gender}}", "idProp": "value", "textProp": "text", "list": [{"text": "{{male}}", "value": "Male"}, {"text": "{{female}}", "value": "Female"}, {"text": "{{non.binary}}", "value": "Non Binary"}, {"text": "{{not.specified}}", "value": "Not Specified"}]}}, "dateOfBirth", "personalEmail", "personalPhone", {"field": "addressView", "fieldProps": {"label": "{{address}}", "multiline": true}}, {"field": "addressForm", "fieldProps": {"component": "Input", "label": "{{address}}", "multiline": false, "name": "home<PERSON>dd<PERSON>", "hybridConfig": {"icon": "location_on", "iconPosition": "left", "variant": "form", "onClick": "@controller:onAddressIconClick", "enableInputClick": true}, "isDisabled": false}}]}}, {"component": "FieldGroup", "section": {"title": "{{employee.bankAccount}}", "grid": {"size": "12"}}, "props": {"columns": "5", "presentationDataSet": "@dataSet:bankDetails"}}]}, {"title": "{{time.tracking}}", "visible": true, "components": [{"component": "FieldGroup", "section": {"grid": {"size": "12"}}, "props": {"columns": "3", "presentationDataSet": "@dataSet:timesheetPolicyData", "fields": [{"field": "timesheetPolicy", "fieldProps": {"component": "Select", "placeholder": "{{timetracking.policy}}", "idProp": "id", "textProp": "name", "value": "@binding:timesheetPolicyData.id", "list": "@binding:timesheetPolicies", "isEditing": "@binding:controlFlags.editMode", "size": 1}}]}}, {"component": "HybridInput", "variant": "secondary", "iconPosition": "right", "icon": "calendar", "action": "update", "size": "1", "field": {"component": "Select", "label": "{{filter.by.year}}", "allowClear": false, "idProp": "year", "textProp": "year", "isEditing": true, "value": "@binding:timesheetPolicyData.timesheetYear", "list": "@binding:timesheetPolicyData.timesheetYearList"}}, {"component": "Table", "size": "12", "variant": "white", "fullWidth": false, "title": "{{timetracking.history}}", "data": "@binding:timesheetData", "sectionActions": [{"label": "{{timetracking.track}}", "onClick": "@controller:onTrackTimeClick", "variant": "secondary"}], "rowNavigation": "/templates/everest.hr.base/uinext/timesheet/time-tracking", "columns": [{"headerName": "{{period}}", "field": "period", "fieldProps": {"fontWeight": "bold"}, "suppressSort": true}, "timesheetPolicyName", {"headerName": "{{status}}", "field": "timesheetStatus", "cellVariant": {"variant": "badge", "matchers": "@controller:getRecordStatusBadgeBgColor()"}}, {"headerName": "{{policyStatus}}", "field": "timesheetPolicyStatus", "cellVariant": {"variant": "badge", "matchers": "@controller:getPolicyStatusBadgeBgColor()"}}, {"headerName": "{{totalWorkDays}}", "field": "totalWorkDays"}, {"headerName": "{{totalWorkTime}}", "field": "totalWorkTimeText"}]}]}, {"title": "{{absences}}", "components": [{"component": "FieldGroup", "section": {"title": "{{assigned.policies}}", "grid": {"size": "12"}}, "props": {"columns": "4", "presentationDataSet": "@dataSet:absenceDetails", "fields": [{"field": "vacationPolicyId"}, {"field": "sickPolicyId"}, {"field": "holidayCalendarId"}, {"field": "adjustmentValue"}]}}, {"component": "HybridInput", "variant": "secondary", "iconPosition": "right", "icon": "calendar", "size": "1", "field": {"component": "Select", "label": "{{filter.by.year}}", "allowClear": false, "idProp": "year", "textProp": "year", "isEditing": true, "value": "@binding:absenceSetup.absenceYear", "list": "@binding:absenceSetup.absenceYearList"}}, {"component": "Summary", "section": {"title": "@binding:absenceSummary.summaryLabel", "variant": "card", "grid": {"size": "12"}}, "props": {"hideDivider": true, "columns": "@controller:getColumns()", "fields": [{"value": "@binding:absenceSummary.remainingVacationDays", "label": "@binding:absenceSummary.remainingVacationDaysLabel"}, {"label": "@binding:absenceSummary.carryoverDaysLabel", "value": "@binding:absenceSummary.carryoverDays", "visible": "@binding:controlFlags.showCarryover"}, {"label": "@binding:absenceSummary.requestedDaysLabel", "value": "@binding:absenceSummary.requestedDays"}, {"label": "@binding:absenceSummary.plannedDaysLabel", "value": "@binding:absenceSummary.plannedDays"}, {"label": "@binding:absenceSummary.takenDaysLabel", "value": "@binding:absenceSummary.takenDays"}]}}, {"component": "Table", "title": "{{absence.history}}", "customId": "absenceHistoryTable", "size": "12", "data": "@binding:absenceTableData", "variant": "white-borderless", "rowSelection": true, "onRowSelectionChanged": "@controller:enableButton", "onRowClicked": "@controller:openLeaveRequest", "sectionActions": [{"variant": "secondary", "label": "{{request.absence}}", "onClick": "@controller:leaveRequest", "visible": "@binding:controlFlags.isCurrentEmployeeOrAdmin", "actions": [{"label": "{{recalculate.absences}}", "onClick": "@controller:recalculateAbsences", "visible": "@binding:controlFlags.isAdmin"}]}, {"label": "{{sync.company.holidays}}", "onClick": "@controller:syncCompanyHolidays", "variant": "secondary", "visible": "@binding:controlFlags.isCurrentEmployeeOrAdmin"}, {"label": "{{actions}}", "visible": "@binding:controlFlags.isCurrentEmployeeOrAdmin", "variant": "primary", "actions": [{"label": "{{delete.cancel}}", "onClick": "@controller:deleteLeaves", "disabled": "@controller:deleteDisabled()", "tooltip": {"text": "{{pending.deleted.approved.canceled}}", "placement": "right"}, "visible": "@binding:controlFlags.isCurrentEmployeeOrAdmin", "confirmation": {"message": "@controller:deleteLabel()", "description": "{{manager.informed}}", "confirmLabel": "{{confirm}}", "cancelLabel": "{{go.back}}"}}, {"label": "{{approve}}", "onClick": "@controller:approve<PERSON><PERSON>ves", "visible": "@binding:controlFlags.isManagerOrAdmin"}, {"label": "{{reject}}", "onClick": "@controller:reject<PERSON><PERSON>ves", "visible": "@binding:controlFlags.isManagerOrAdmin"}]}], "empty": {"variant": "primary-table"}, "columns": [{"field": "absenceType", "fieldProps": {"fontWeight": "bold"}, "headerName": "{{type}}", "cellVariant": {"variant": "icon", "iconSize": "medium", "iconColor": "morning-blue", "matchers": "@controller:getAbsenceMatchers", "tooltip": "Company Vacation"}}, {"field": "createdDate", "initialHide": true}, {"field": "startDate"}, {"field": "endDate"}, {"field": "days"}, {"field": "status", "cellVariant": {"variant": "badge", "matchers": "@controller:getMatchers()"}}]}]}, {"title": "{{compensation}}", "components": [{"component": "Table", "title": "{{base.salary}}", "customId": "employeeBaseSalaryTable", "size": "12", "name": "salary", "sectionActions": [{"label": "{{create}}", "onClick": "@controller:SalaryController.createSalary", "variant": "secondary", "visible": "@binding:controlFlags.isAdmin"}], "rowActions": {"visible": "@binding:controlFlags.isAdmin", "actions": [{"content": "{{edit}}", "onClick": "@controller:SalaryController.onEditSalaryClick"}, {"content": "{{delete}}", "onClick": "@controller:SalaryController.onDeleteSalaryClick"}]}, "columns": [{"field": "baseSalary"}, {"field": "currency", "suppressSort": true}, {"field": "unit"}, {"field": "startDate", "parseAs": "date", "isSortable": true}, {"field": "endDate", "isSortable": true, "parseAs": "date"}, {"field": "status", "cellVariant": {"variant": "badge", "matchers": "@controller:getSalaryMatchers()"}}], "data": "@binding:salaryData"}, {"component": "Table", "title": "{{bonus}}", "customId": "employeeBonusTable", "size": "12", "rowActions": {"visible": "@binding:controlFlags.isAdmin", "actions": [{"content": "{{edit}}", "onClick": "@controller:onEditBonusClick"}, {"content": "{{delete}}", "onClick": "@controller:onDeleteBonusClick"}]}, "sectionActions": [{"label": "{{create}}", "onClick": "@controller:BonusController.onCreateBonusClick", "variant": "secondary", "visible": "@binding:controlFlags.isAdmin"}], "columns": ["bonusAmount", {"field": "currency", "suppressSort": true}, {"field": "bonusName", "isSortable": true}, "recurrenceType", "paymentPeriod", {"field": "startDate", "parseAs": "date", "isSortable": true}, {"field": "endDate", "isSortable": true, "parseAs": "date"}, {"field": "status", "cellVariant": {"variant": "badge", "matchers": "@controller:getSalaryMatchers()"}}], "data": "@binding:bonusData"}]}, {"title": "{{benefits}}", "components": [{"component": "Table", "title": "{{benefits}}", "size": "12", "rowActions": {"variant": "button", "columnLabel": "{{actions}}", "columnPosition": -1}, "sectionActions": [{"label": "{{manage.benefits}}", "onClick": "@controller:manageBenefit", "variant": "secondary", "visible": "@binding:controlFlags.isAdmin"}], "columns": ["benefitName", "recurrenceType", "companyContribution", "employeeContribution", "benefitAmount", "paymentPeriod", {"field": "startDate", "parseAs": "date", "isSortable": true}, {"field": "endDate", "isSortable": true, "parseAs": "date"}], "data": "@binding:benefitData"}]}, {"title": "{{permissions}}", "components": [{"component": "Block", "title": "", "sectionVariant": "card", "variant": "secondary", "size": 12, "columns": 12, "isEditing": true, "elements": [{"component": "Switch", "label": "{{allow.everest.access}}", "size": "12", "direction": "horizontal-reverse", "value": "@binding:employmentAccess.allowEmployeeAccess", "isDisabled": "@binding:controlFlags.isCurrentEmployee"}]}, {"component": "Table", "title": "{{assigned.permissions}}", "size": "12", "data": "@binding:permissionData", "variant": "white-borderless", "sectionActions": [{"label": "{{manage.employee.access}}", "onClick": "@controller:manageUserAccess", "visible": "@binding:controlFlags.isAdmin", "variant": "secondary"}], "empty": {"variant": "primary-table"}, "columns": ["permissionName", "permissionType", {"field": "assignment<PERSON><PERSON><PERSON>"}, {"field": "status"}, {"field": "startDate"}, {"field": "endDate"}]}]}, {"title": "{{emp.user.pin}}", "visible": true, "components": [{"component": "<PERSON><PERSON>", "visible": "@binding:controlFlags.encryptionRequestSentAlert", "section": {"grid": {"size": 12}}, "props": {"variant": "info", "title": "{{information}}", "content": "{{emp.user.key.request.sent}}"}}, {"component": "<PERSON><PERSON>", "visible": "@binding:controlFlags.encryptionRequestAlert", "section": {"grid": {"size": 12}}, "props": {"variant": "warning", "title": "{{warning}}", "content": "{{emp.user.key.warning}}", "action": {"label": "{{emp.user.key.request}}", "onClick": "@controller:sendUserKeyRequest"}}}, {"component": "<PERSON><PERSON>", "visible": "@binding:controlFlags.encryptionRequestToApproveAlert", "section": {"grid": {"size": 12}}, "props": {"variant": "warning", "title": "{{warning}}", "content": "@binding:controlFlags.encryptionRequestToApproveAlert", "action": {"label": "{{approve}}", "shape": "pill"}}}, {"component": "ActionGroup", "size": "12", "versionNew": true, "lines": [{"status": "@controller:getUserKeyStatus()", "label": "@controller:getLineLabel()", "buttonActions": [{"label": "{{actions}}", "shape": "pill", "disabled": "@controller:isNotCurrentEmployee()", "actions": [{"label": "{{user.pin.create}}", "onClick": "@controller:onCreateUserPinClick", "visible": "@binding:controlFlags.hasNoUserKey", "disabled": "@binding:controlFlags.hasNoMasterKey", "tooltip": {"text": "{{emp.no.master<PERSON><PERSON>}}", "visible": "@binding:controlFlags.hasNoMasterKey", "defaultVisible": false}}, {"label": "{{user.pin.change}}", "onClick": "@controller:onChangeUserKeyClick", "visible": false}, {"label": "{{user.pin.enter}}", "onClick": "@controller:onEnterUserKeyClick", "visible": "@controller:showEnterUserPin()"}, {"label": "{{remove}}", "onClick": "@controller:onRemoveUserKeyClick", "visible": "@controller:showRemoveUserPin()"}]}]}]}]}]}}}