// @i18n:employee
// @i18n:everest.base.encryption/encryption
import {
  arrayBufferToBinaryStr,
  base64ToBinaryStr,
  binaryStrToArrayBuffer,
  binaryStrToBase64,
  decryptO<PERSON><PERSON>ey,
  encryptO<PERSON><PERSON>ey,
  getUserPrivate<PERSON>eyBase64,
  importPrivate<PERSON><PERSON>,
  importPub<PERSON><PERSON>ey,
  setUserPrivateKey,
} from '@pkg/everest.base.encryption/public/utils.uicontroller';
import { EvstRequestStatus } from '@pkg/everest.base.encryption/types/enums/RequestStatus';
import { ObjectKeyMappingRequestUI } from '@pkg/everest.base.encryption/types/ObjectKeyMappingRequest.ui';
import type { ObjectKeyUserKeyMapping } from '@pkg/everest.base.encryption/types/ObjectKeyUserKeyMapping';
import { ObjectKeyUserKeyMappingUI } from '@pkg/everest.base.encryption/types/ObjectKeyUserKeyMapping.ui';
import { User<PERSON>eyUI } from '@pkg/everest.base.encryption/types/UserKey.ui';
import type { EmployeeUiTemplate } from '@pkg/everest.hr.base/types/uiTemplates/uinext/employee/employee.ui';

type EmployeeContext = EmployeeUiTemplate.EmployeeContext;

export type Context = EmployeeContext & {
  state: {
    id: number;
  };
};

export function hasNoMasterKey({ data: { MasterKey } }: Context) {
  const existed = !!MasterKey?.configId;
  return !existed;
}
export function onCreateUserPinClick(context: Context) {
  const { helpers, actions, data } = context;
  const { EmployeeUser } = data;
  helpers.openModal({
    size: 'small',
    template: '/templates/everest.base.encryption/uinext/enterUserPin',
    onClose: async () =>
      await actions.refetchUiModelData({
        nodesToLoad: [
          'RelatedUserKey',
          'RelatedUnsetObjectKeys',
          'UserKey',
          'ObjectKeyMappingRequest',
        ],
      }),
    initialState: {
      mode: 'create',
      userId: EmployeeUser?.userId,
    },
  });
}

export function getUserKeyStatus({ data }: Context) {
  const { RelatedUserKey } = data;
  if (!RelatedUserKey?.id) {
    return 'invalid';
  }
  const userPrivateKey = getUserPrivateKeyBase64();
  return userPrivateKey ? 'valid' : 'warning';
}

export function getLineLabel({ data }: Context) {
  const { RelatedUserKey } = data;
  if (!RelatedUserKey?.id) {
    return '{{emp.user.pin.notfound}}';
  }
  const userPrivateKey = getUserPrivateKeyBase64();
  return userPrivateKey
    ? '{{emp.user.key.activated}}'
    : '{{emp.user.key.not.activated}}';
}

export function hasNoUserKey({ data: { RelatedUserKey } }: Context) {
  return !RelatedUserKey?.id;
}

export function hasUserKey({ data: { RelatedUserKey } }: Context) {
  return !!RelatedUserKey?.id;
}

export function showEnterUserPin(context: Context) {
  const { data } = context;
  const { isCurrentEmployee } = data;
  const userPrivateKey = getUserPrivateKeyBase64();
  return !userPrivateKey && hasUserKey(context) && !!isCurrentEmployee;
}

export function onChangeUserKeyClick({ helpers }: Context) {
  helpers.openNotImplementedToast();
}

export async function onEnterUserKeyClick(context: Context) {
  const { actions, data, helpers } = context;
  helpers.openModal({
    size: 'small',
    template: '/templates/everest.base.encryption/uinext/enterUserPin',
    initialState: {
      mode: 'enter',
      userId: data.EmployeeUser?.userId,
    },
    onClose: async () =>
      await actions.refetchUiModelData({ nodesToLoad: ['RelatedUserKey'] }),
  });
}

export function showRemoveUserPin({ data: { RelatedUserKey } }: Context) {
  if (!RelatedUserKey?.id) {
    return false;
  }
  const userPrivateKey = getUserPrivateKeyBase64();
  return !!userPrivateKey;
}

export async function onRemoveUserKeyClick({ actions }: Context) {
  setUserPrivateKey(undefined);
  await actions.refetchUiModelData({
    nodesToLoad: ['Salary', 'Bonus'],
    discardChanges: true,
  });
}
export async function sendUserKeyRequest(context: Context) {
  const { data, helpers, actions } = context;
  const { RelatedUnsetObjectKeys, UserKey } = data;
  if (!UserKey?.id) {
    return;
  }
  helpers.showNotificationMessage({
    type: 'loading',
    key: 'sendingRequest',
    message: '{{emp.send.request}}',
    duration: 0,
  });
  try {
    const objectCreators: string[] = Array.from(
      new Set(RelatedUnsetObjectKeys?.map((item) => item.createdBy))
    );
    await ObjectKeyUserKeyMappingUI.updateMany(
      context,
      {
        id: {
          $in: RelatedUnsetObjectKeys?.map((item) => item.id),
        },
      },
      { userKeyId: UserKey.id },
      ['id']
    ).run('RelatedUnsetObjectKeys');
    for await (const approver of objectCreators) {
      await ObjectKeyMappingRequestUI.create(
        context,
        {
          approverUserId: approver,
          status: EvstRequestStatus.Pending,
          objectKeyMappingIds: RelatedUnsetObjectKeys?.filter(
            (item) => item.createdBy === approver
          )?.map((item) => item.id),
        },
        ['id']
      ).run('ObjectKeyMappingRequest');
    }
    helpers.closeNotificationMessage('sendingRequest');
    await actions.refetchUiModelData({
      nodesToLoad: ['RelatedUnsetObjectKeys', 'ObjectKeyMappingRequest'],
    });
    helpers.showToast({
      type: 'success',
      message: '{{emp.send.request.success}}',
    });
  } catch {
    helpers.closeNotificationMessage('sendingRequest');
    helpers.showToast({
      type: 'error',
      message: '{{emp.send.request.failed}}',
    });
  }
}

export function showRequestAlert(context: Context) {
  const { data } = context;
  const {
    RelatedUnsetObjectKeys,
    UserKey,
    ObjectKeyMappingRequest,
    isCurrentEmployee,
  } = data;
  return (
    isCurrentEmployee &&
    !!UserKey?.id &&
    !!RelatedUnsetObjectKeys &&
    RelatedUnsetObjectKeys.length > 0 &&
    (ObjectKeyMappingRequest?.length ?? 0) === 0
  );
}

export function showApproveAlert(context: Context) {
  const { data } = context;
  const { ObjectKeyMappingRequestToApprove, UserKey, isCurrentEmployee } = data;
  return (
    isCurrentEmployee &&
    !!UserKey?.id &&
    !!ObjectKeyMappingRequestToApprove &&
    ObjectKeyMappingRequestToApprove.length > 0
  );
}

export function showRequestSentAlert(context: Context) {
  const { data } = context;
  const { ObjectKeyMappingRequest, isCurrentEmployee } = data;
  return (
    isCurrentEmployee &&
    Array.isArray(ObjectKeyMappingRequest) &&
    ObjectKeyMappingRequest.length > 0
  );
}

export async function approveObjectAccessRequest(context: Context) {
  const { data, helpers, actions } = context;
  const { ObjectKeyMappingRequestToApprove, UserKey, CurrentUser } = data;
  const userPrivateKeyBase64 = getUserPrivateKeyBase64();
  if (!userPrivateKeyBase64) {
    helpers.showToast({
      title: '{{enc.key.notfound}}',
      message: '{{enc.user.privateKey.warning}}',
      type: 'warning',
    });
    return;
  }
  try {
    helpers.showNotificationMessage({
      type: 'loading',
      key: 'approveRequest',
      message: '{{emp.approve.request}}',
      duration: 0,
    });
    const userPrivateKeyBuf = binaryStrToArrayBuffer(
      base64ToBinaryStr(userPrivateKeyBase64)
    );
    const objectKeyMappingIds = ObjectKeyMappingRequestToApprove?.map(
      (item) => item.objectKeyMappingIds
    ).flat();
    const userIdsInRequest = ObjectKeyMappingRequestToApprove?.map(
      (item) => item.createdBy
    );
    const mappingToApproveRes = await ObjectKeyUserKeyMappingUI.query(
      context,
      {
        where: {
          id: {
            $in: objectKeyMappingIds,
          },
        },
      },
      [
        'id',
        'userKeyId',
        'objectKeyId',
        'validFrom',
        'userId',
        'encryptedObjectKey',
        'createdBy',
      ]
    ).run('ObjectKeyMappingToApprove');
    const objectMappingToApprove =
      mappingToApproveRes?.ObjectKeyMappingToApprove;
    const currentObjectKeyMappingRes = await ObjectKeyUserKeyMappingUI.query(
      context,
      {
        where: {
          objectKeyId: {
            $in: objectMappingToApprove.map((item) => item.objectKeyId),
          },
          userKeyId: UserKey.id,
          userId: CurrentUser.userId,
        },
      },
      [
        'id',
        'userKeyId',
        'objectKeyId',
        'validFrom',
        'userId',
        'encryptedObjectKey',
        'createdBy',
      ]
    ).run('CurrentObjectKeyMapping');
    const objectKeyMap: Map<number, ArrayBuffer> = new Map();
    const currentObjectKeyMapping: Partial<ObjectKeyUserKeyMapping.ReadReturnType>[] =
      currentObjectKeyMappingRes.CurrentObjectKeyMapping;
    const userPrivateKey = await importPrivateKey(userPrivateKeyBuf);
    for await (const mapping of currentObjectKeyMapping) {
      const encryptedObjectKeyBuf = binaryStrToArrayBuffer(
        base64ToBinaryStr(mapping.encryptedObjectKey)
      );
      const objectKeyBuf = await decryptObjectKey(
        userPrivateKey,
        encryptedObjectKeyBuf
      );
      objectKeyMap.set(mapping.objectKeyId, objectKeyBuf);
    }
    const userKeysInRequestRes = await UserKeyUI.query(
      context,
      {
        where: {
          userId: {
            $in: userIdsInRequest,
          },
        },
      },
      ['id', 'userId', 'publicKey']
    ).run('UserKey');
    const userKeysInRequest = userKeysInRequestRes.UserKey;
    const userKeyMap: Map<string, CryptoKey> = new Map();
    for await (const userKey of userKeysInRequest) {
      const publicKeyBuf = binaryStrToArrayBuffer(
        base64ToBinaryStr(userKey.publicKey)
      );
      const publicKey = await importPublicKey(publicKeyBuf);
      userKeyMap.set(userKey.userId, publicKey);
    }
    for await (const mapping of objectMappingToApprove) {
      const relatedObjectKey = objectKeyMap.get(mapping.objectKeyId);
      const relatedPublicKey = userKeyMap.get(mapping.userId);
      const encryptedObjectKey = await encryptObjectKey(
        relatedObjectKey,
        relatedPublicKey
      );
      const encryptedObjectKeyBase64 = binaryStrToBase64(
        arrayBufferToBinaryStr(encryptedObjectKey)
      );
      await ObjectKeyUserKeyMappingUI.update(
        context,
        {
          id: mapping.id,
        },
        {
          encryptedObjectKey: encryptedObjectKeyBase64,
        },
        [
          'id',
          'userKeyId',
          'objectKeyId',
          'validFrom',
          'userId',
          'encryptedObjectKey',
          'createdBy',
        ]
      ).run('ObjectKeyMappingToApprove');
    }
    await ObjectKeyMappingRequestUI.updateMany(
      context,
      {
        id: {
          $in: ObjectKeyMappingRequestToApprove?.map((request) => request.id),
        },
      },
      {
        status: EvstRequestStatus.Approved,
      },
      ['id']
    ).run('ObjectKeyMappingRequestToApprove');
    helpers.closeNotificationMessage('approveRequest');
    helpers.showToast({
      type: 'success',
      message: '{{emp.approve.request.success}}',
    });
    await actions.refetchUiModelData({
      nodesToLoad: ['ObjectKeyMappingRequestToApprove'],
    });
  } catch {
    helpers.showToast({
      type: 'error',
      message: '{{emp.approve.request.failed}}',
    });
  }
}

export function getObjectKeyRequest(context: Context) {
  const { data } = context;
  const { CurrentUser, isCurrentEmployee } = data;
  if (!isCurrentEmployee || !CurrentUser?.userId) {
    return undefined;
  }
  return {
    where: {
      createdBy: CurrentUser.userId,
      status: EvstRequestStatus.Pending,
    },
  };
}

export async function isNotCurrentEmployee({ data }: Context) {
  const { isCurrentEmployee } = data;
  return !isCurrentEmployee;
}

export function getAccessRequestToApproveMessage({ data }: Context) {
  const { ObjectKeyMappingRequestToApprove } = data;
  const len = ObjectKeyMappingRequestToApprove?.length ?? 0;
  return `${len} {{enc.request.to.access}}`;
}

export function getCurrentEmployeeId(context: Context) {
  const { data, helpers, state } = context;
  const { Employee } = data;
  return state?.id || !helpers?.isEmpty(Employee)
    ? state?.id ?? Employee?.id
    : null;
}

export function getEmployeeDataQuery(context: Context) {
  const { state } = context;
  const fieldList = [
    'category',
    'contract',
    'employeeNumber',
    'endDate',
    'entityId',
    'personId',
    'id',
    'locationId',
    'name',
    'startDate',
    'status',
    'vacationDaysDelta',
    'bankAccountHolderName',
    'iban',
    'onboardingStatus',
    'email',
    'accessKeyId',
    'equipmentNumber',
    'personalEmail',
    'bankDetailsId',
    'workAddress',
    'personalPhone',
    'holidayCalendar',
    'Employee-Entity.currency',
    'offboardingStatus',
    'firstName',
    'middleName',
    'lastName',
    'preferredFirstName',
    'preferredLastName',
    'displayName',
    'allowEmployeeAccess',
    'probationDuration',
    'probationEndDate',
    'leavesOverlapStatus',
    'externalEmployeeId',
    'migrationConfigurationId',
  ];
  return state?.id
    ? {
        where: {
          id: Number(state.id),
        },
        fieldList,
      }
    : {
        where: {},
        fieldList,
      };
}

export function getRelatedObjectKeysQuery({ data }: Context) {
  const { EmployeeUser } = data;
  if (!EmployeeUser?.userId) {
    return undefined;
  }
  return {
    where: {
      userId: EmployeeUser.userId,
      encryptedObjectKey: {
        $eq: null,
      },
    },
  };
}

export function buildDecryptedQuery(context: Context) {
  const userPrivateKey = getUserPrivateKeyBase64();
  const employeeId = getCurrentEmployeeId(context);
  return employeeId
    ? {
        employeeId,
        userPrivateKey,
      }
    : undefined;
}

export function getObjectKeyRequestToApprove(context: Context) {
  const { data } = context;
  const { CurrentUser, isCurrentEmployee } = data;
  if (!isCurrentEmployee || !CurrentUser?.userId) {
    return undefined;
  }
  return {
    where: {
      approverUserId: CurrentUser.userId,
      status: EvstRequestStatus.Pending,
    },
  };
}
