{"version": 2, "uicontroller": ["employeeUserPin.uicontroller.ts"], "uimodel": {"state": {"mode": "view", "editOwnProfile": false, "jobUpdate": false, "events": [], "loading": false, "documentFileId": null, "photoIsLoading": false, "compensationReportTimeRange": "last12Months"}, "nodes": {"CurrentUser": {"type": "struct", "modelId": "everest.appserver.usermgmt/UserModel.User", "readCurrent": {}, "fieldList": ["id", "userId", "email"]}, "Employee": {"type": "struct", "loadEmployeeProfile": "@controller:getEmployeeDataQuery()", "modelId": "everest.hr.base/EmployeeModel.Employee", "fieldList": ["category", "contract", "employeeNumber", "endDate", "entityId", "personId", "id", "locationId", "name", "startDate", "status", "vacationDaysDelta", "bankAccountHolderName", "iban", "onboardingStatus", "email", "accessKeyId", "equipmentNumber", "personalEmail", "bankDetailsId", "workAddress", "personalPhone", "holidayCalendar", "Employee-Entity.currency", "firstName", "middleName", "lastName", "preferredFirstName", "preferredLastName", "displayName", "probationDuration", "allowEmployeeAccess", "probationEndDate", "leavesOverlapStatus", "externalEmployeeId", "migrationConfigurationId"]}, "UserKey": {"type": "struct", "modelId": "everest.base.encryption/UserKeyModel.UserKey", "getUserKey": {}, "fieldList": ["id", "userPin", "encryptedPrivateKeyByUserPin", "encryptedPrivateKeyByObjectKey", "keySalt", "public<PERSON>ey", "objectKeyId", "userId"]}, "MasterKey": {"type": "struct", "modelId": "everest.base.encryption/MasterKeyModel.MasterKey", "getMasterKey": {}, "fieldList": ["public<PERSON>ey", "configId"]}, "RelatedUserKey": {"type": "struct", "modelId": "everest.hr.base/EmployeeModel.Employee", "getUserKeyByEmployeeId": {"employeeId": "@controller:getCurrentEmployeeId()"}}, "RelatedUnsetObjectKeys": {"type": "list", "modelId": "everest.base.encryption/ObjectKeyUserKeyMappingModel.ObjectKeyUserKeyMapping", "query": "@controller:getRelatedObjectKeysQuery()", "fieldList": ["id", "userKeyId", "objectKeyId", "validFrom", "userId", "encryptedObjectKey", "created<PERSON>y"]}, "ObjectKeyMappingToApprove": {"type": "struct", "modelId": "everest.base.encryption/ObjectKeyUserKeyMappingModel.ObjectKeyUserKeyMapping", "fieldList": ["id", "userKeyId", "objectKeyId", "validFrom", "userId", "encryptedObjectKey", "created<PERSON>y"]}, "CurrentObjectKeyMapping": {"type": "struct", "modelId": "everest.base.encryption/ObjectKeyUserKeyMappingModel.ObjectKeyUserKeyMapping", "fieldList": ["id", "userKeyId", "objectKeyId", "validFrom", "userId", "encryptedObjectKey", "created<PERSON>y"]}, "isCurrentEmployee": {"type": "struct", "modelId": "everest.hr.base/EmployeeModel.Employee", "isCurrentEmployee": {"employee": {"id": "@controller:getCurrentEmployeeId()"}}}, "EmployeeUser": {"type": "struct", "model": "urn:evst:everest:hr/base:model/node:Employee", "getUserDataByEmployeeId": {"employeeId": "@controller:getCurrentEmployeeId()"}, "fieldList": ["id", "userId", "email"]}, "Salary": {"type": "list", "getDecryptedSalary": "@controller:buildDecryptedQuery()", "modelId": "everest.hr.base/SalaryModel.Salary", "fieldList": ["id", "baseSalary", "startDate", "endDate", "unit", "keyId"]}, "Bonus": {"type": "list", "getDecryptedBonus": "@controller:buildDecryptedQuery()", "modelId": "everest.hr.base/BonusModel.Bonus", "fieldList": ["id", "employeeId", "bonusTypeId", "bonusAmount", "endDate", "paymentPeriod", "startDate", "Bonus-BonusType.id", "Bonus-BonusType.bonusName", "Bonus-BonusType.recurrenceType", "recurrenceType", "objectKeyId", "encryptedData", "bonusAmount"]}, "ObjectKeyMappingRequest": {"type": "list", "query": "@controller:getObjectKeyRequest()", "modelId": "everest.base.encryption/ObjectKeyMappingRequestModel.ObjectKeyMappingRequest", "fieldList": ["id", "status", "approverUserId", "objectKeyMappingIds", "created<PERSON>y"]}, "ObjectKeyMappingRequestToApprove": {"type": "list", "query": "@controller:getObjectKeyRequestToApprove()", "modelId": "everest.base.encryption/ObjectKeyMappingRequestModel.ObjectKeyMappingRequest", "fieldList": ["id", "status", "approverUserId", "objectKeyMappingIds", "created<PERSON>y"]}}}, "uiview": {"autoRefreshData": true, "allowRefreshData": true, "i18n": ["hrbase", "employee", "manager", "requestDocument", "securityManagement", "leaveRequest", "bankDetails", "applicant", "boarding", "everest.base.encryption/encryption"], "sections": {"content": [{"component": "<PERSON><PERSON>", "visible": "@controller:showRequestSentAlert()", "section": {"grid": {"size": "12"}}, "props": {"variant": "info", "title": "{{information}}", "content": "{{emp.user.key.request.sent}}"}}, {"component": "<PERSON><PERSON>", "visible": "@controller:showRequestAlert()", "section": {"grid": {"size": "12"}}, "props": {"variant": "warning", "title": "{{warning}}", "content": "{{emp.user.key.warning}}", "action": {"label": "{{emp.user.key.request}}", "onClick": "@controller:sendUserKeyRequest"}}}, {"component": "<PERSON><PERSON>", "visible": "@controller:showApproveAlert()", "section": {"grid": {"size": "12"}}, "props": {"variant": "warning", "title": "{{warning}}", "content": "@controller:getAccessRequestToApproveMessage()", "action": {"label": "{{approve}}", "shape": "pill", "onClick": "@controller:approveObjectAccessRequest"}}}, {"component": "ActionGroup", "size": "12", "versionNew": true, "lines": [{"status": "@controller:getUserKeyStatus()", "label": "@controller:getLineLabel()", "buttonActions": [{"label": "{{actions}}", "shape": "pill", "disabled": "@controller:isNotCurrentEmployee()", "actions": [{"label": "{{user.pin.create}}", "onClick": "@controller:onCreateUserPinClick", "visible": "@controller:hasNo<PERSON><PERSON><PERSON><PERSON>()", "disabled": "@controller:hasNoMasterKey()", "tooltip": {"text": "{{emp.no.master<PERSON><PERSON>}}", "visible": "@controller:hasNoMasterKey()", "defaultVisible": false}}, {"label": "{{user.pin.change}}", "onClick": "@controller:onChangeUserKeyClick", "visible": false}, {"label": "{{user.pin.enter}}", "onClick": "@controller:onEnterUserKeyClick", "visible": "@controller:showEnterUserPin()"}, {"label": "{{remove}}", "onClick": "@controller:onRemoveUserKeyClick", "visible": "@controller:showRemoveUserPin()"}]}]}]}]}}}