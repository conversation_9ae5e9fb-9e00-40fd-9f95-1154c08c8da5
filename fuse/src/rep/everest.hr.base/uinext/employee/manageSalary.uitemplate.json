{"version": 2, "uicontroller": ["everest.hr.base/uinext/employee/manageSalary.uicontroller.ts"], "uimodel": {"nodes": {"Employee": {"type": "struct", "query": {"where": {"id": "@state:param.id"}}, "modelId": "everest.hr.base/EmployeeModel.Employee", "fieldList": ["category", "contract", "employeeNumber", "endDate", "entityId", "personId", "id", "locationId", "name", "displayName", "startDate", "status", "vacationDaysDelta", "salary", "bankAccountHolderName", "iban", "onboardingStatus", "email", "accessKeyId", "equipmentNumber", "personalEmail", "bankDetailsId", "holidayCalendar"]}, "Person": {"type": "struct", "modelId": "everest.hr.base/PersonModel.Person", "parent": "Employee", "joinKey": "id-personId", "fieldList": ["address", "addressId", "dateOfBirth", "email", "endDate", "gender", "id", "name", "displayName", "personNumber", "phoneNumber", "photo", "photoName", "salutation", "startDate", "status", "lastName", "firstName", "preferredLastName", "preferredFirstName", "countryCode"]}, "EmployeeJob": {"type": "list", "query": {"where": {"employeeId": "@state:param.id"}}, "modelId": "everest.hr.base/JobModel.Job", "fieldList": ["departmentId", "employeeId", "endDate", "id", "percentage", "positionId", "Job-Position.positionNumber", "salary", "jobManager", "jobManagerDisplayName", "startDate", "regionId"]}, "Salary": {"type": "list", "query": {"where": {"employeeId": "@state:param.id"}, "orderBy": [{"field": "startDate", "ordering": "asc"}]}, "modelId": "everest.hr.base/SalaryModel.Salary", "fieldList": ["id", "employeeId", "baseSalary", "startDate", "endDate", "payoutPeriod", "salaryType", "uuid", "encryptedSalary", "keyId"]}, "PayoutPeriod": {"type": "list", "query": {"where": {"dataType": "PayoutPeriod", "language": "en"}}, "modelId": "everest.appserver/ValueHelpModel.ValueHelp", "fieldList": ["text", "codeValue"]}, "SalaryType": {"type": "list", "query": {"where": {"dataType": "SalaryType", "language": "en"}}, "modelId": "everest.appserver/ValueHelpModel.ValueHelp", "fieldList": ["text", "codeValue"]}, "MasterKey": {"type": "struct", "modelId": "everest.base.encryption/MasterKeyModel.MasterKey", "fieldList": ["public<PERSON>ey"], "getMasterKey": {}}, "ObjectKey": {"modelId": "everest.base.encryption/ObjectKeyModel.ObjectKey", "type": "struct", "fieldList": ["id", "encryptedByMasterKey", "keySalt", "relatedPackage", "modelId", "objectUUID"]}}}, "uiview": {"layout": "everest.base.ui/layouts/uilayout/landing", "props": {"i18n": ["hrbase", "employee", "everest.base.encryption/encryption"], "title": "{{manage.salary}}", "subtitle": "@binding:Employee.displayName", "config": {"allowRefreshData": true, "autoRefreshData": true}, "headerConfig": {"size": "8"}, "mainBlock": {"type": "secondary"}, "customSections": [{"component": "<PERSON><PERSON>", "visible": "@controller:hasNoMasterKey()", "section": {"grid": {"size": 12}}, "props": {"variant": "warning", "title": "Warning", "content": "{{enc.masterKey.warning}}", "action": {"label": "{{enc.manage.masterKey}}", "onClick": "@controller:onManageMasterKeyClick"}}}, {"component": "Table", "title": "{{salary}}", "size": "12", "name": "salary", "editing": true, "suppressDelete": false, "columns": [{"field": "employeeId", "visible": false}, {"headerName": "{{salary.amount}}", "field": "baseSalary", "isSortable": true, "fieldProps": {"isDisabled": "@controller:isEditingRow"}}, {"headerName": "{{salaryType}}", "field": "salaryType"}, {"headerName": "{{unit}}", "field": "payoutPeriod"}, {"headerName": "{{startDate}}", "field": "startDate", "parseAs": "date", "isSortable": true}, {"headerName": "{{endDate}}", "field": "endDate", "isSortable": true, "parseAs": "date"}], "data": "@binding:<PERSON><PERSON>", "addRowsOnEmpty": true}], "primaryAction": {"label": "{{save}}", "onClick": "@controller:onSave", "disabled": "@controller:hasNoMasterKey()"}, "secondaryAction": {"variant": "secondary", "label": "{{cancel}}", "onClick": "@controller:cancel"}}}}