/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable @typescript-eslint/no-explicit-any */
import type { RoutineValidationContext } from '@everestsystems/content-core';
import {
  DATA,
  DYNAMIC_FIELDS,
  getCurrentUser,
  getLanguage,
  getTranslationsMap,
  METADATA,
} from '@everestsystems/content-core';
import { PlainDate } from '@everestsystems/datetime';
import { Decimal } from '@everestsystems/decimal';
import { EvstRequestStatus } from '@pkg/everest.base.encryption/types/enums/RequestStatus';
import { MasterKey } from '@pkg/everest.base.encryption/types/MasterKey';
import { ObjectKeyMappingRequest } from '@pkg/everest.base.encryption/types/ObjectKeyMappingRequest';
import { ObjectKeyUserKeyMapping } from '@pkg/everest.base.encryption/types/ObjectKeyUserKeyMapping';
import { User<PERSON>ey } from '@pkg/everest.base.encryption/types/UserKey';
import { Entity } from '@pkg/everest.base/types/Entity';
import { BankDetails } from '@pkg/everest.fin.base/types/BankDetails';
import type { EvstBankAccountNumberType } from '@pkg/everest.fin.base/types/enums/BankAccountNumberType';
import type { EvstFinancialInstutionIdType } from '@pkg/everest.fin.base/types/enums/FinancialInstutionIdType';
import getUserDataByEmployeeId from '@pkg/everest.hr.base/actions/Employee/getUserDataByEmployeeId.action';
import getUserKeyByEmployeeId from '@pkg/everest.hr.base/actions/Employee/getUserKeyByEmployeeId.action';
import isCurrentEmployee from '@pkg/everest.hr.base/actions/Employee/isCurrentEmployee.action';
import loadEmployees from '@pkg/everest.hr.base/actions/Employee/loadEmployees.action';
import queryOwnEmployees from '@pkg/everest.hr.base/actions/Employee/queryOwnEmployees.action';
import recalculateAbsenceDays from '@pkg/everest.hr.base/actions/Employee/recalculateAbsenceDays.action';
import syncCompanyHolidays from '@pkg/everest.hr.base/actions/Employee/syncCompanyHolidaysToGoogleCalendar.action';
import tryGetCurrentEmployee from '@pkg/everest.hr.base/actions/Employee/tryGetCurrentEmployee.action';
import updateEmployeeDetail from '@pkg/everest.hr.base/actions/Employee/updateEmployeeDetail.action';
import bulkLeaveApprove from '@pkg/everest.hr.base/actions/Leave/bulkLeaveApprove.action';
import bulkLeaveReject from '@pkg/everest.hr.base/actions/Leave/bulkLeaveReject.action';
import deleteLeave from '@pkg/everest.hr.base/actions/Leave/deleteLeave.action';
import getRemainingFixedDays from '@pkg/everest.hr.base/actions/Leave/getRemainingFixedDays.action';
import getRemainingVacationDays from '@pkg/everest.hr.base/actions/Leave/getRemainingVacationDays.action';
import getTakenVacationDays from '@pkg/everest.hr.base/actions/Leave/getTakenVacationDays.action';
import loadEmployeePermissions from '@pkg/everest.hr.base/actions/securityManagement/loadEmployeePermissions.action';
import isAdmin from '@pkg/everest.hr.base/actions/shared/isAdmin.action';
import assignEmployeesToTimesheet from '@pkg/everest.hr.base/actions/TimesheetPolicy/assignEmployeesToTimesheet.action';
import getCurrentAssignedTSPolicy from '@pkg/everest.hr.base/actions/TimesheetPolicy/getCurrentAssignedTSPolicy.action';
import removeAssignedEmployees from '@pkg/everest.hr.base/actions/TimesheetPolicy/removeAssignedEmployees.action';
import { AbsenceCalculation } from '@pkg/everest.hr.base/types/AbsenceCalculation';
import type { AbsencePolicy } from '@pkg/everest.hr.base/types/AbsencePolicy';
import { AbsencePolicyEmployee } from '@pkg/everest.hr.base/types/AbsencePolicyEmployee';
import { EmployeeBusinessObject } from '@pkg/everest.hr.base/types/BOComposition/EmployeeBusinessObject';
import { EvstCalendarUnit } from '@pkg/everest.hr.base/types/enums/CalendarUnit';
import { EvstCarryoverType } from '@pkg/everest.hr.base/types/enums/CarryoverType';
import { EvstGrantingType } from '@pkg/everest.hr.base/types/enums/GrantingType';
import { EvstTimelineStatus } from '@pkg/everest.hr.base/types/enums/TimelineStatus';
import { EvstTimeRecordStatus } from '@pkg/everest.hr.base/types/enums/TimeRecordStatus';
import { Leave } from '@pkg/everest.hr.base/types/Leave';
import { Location } from '@pkg/everest.hr.base/types/Location';
import { Position } from '@pkg/everest.hr.base/types/Position';
import { Timesheet } from '@pkg/everest.hr.base/types/Timesheet';
import { toJSDate } from '@pkg/everest.hr.base/utils/Date/date';
import { merge, set } from 'lodash';

import type { employeeV2Presentation } from '../../types/presentations/uinext/employee/employeeV2';
type Any = any;
type Validation = (
  context: RoutineValidationContext,
  data: employeeV2Presentation.dataSources.employee.callbacks.query.queryData
) => Promise<boolean>;

function getRequiredFieldsValidations(): Validation {
  return async (
    context: RoutineValidationContext,
    data: employeeV2Presentation.dataSources.employee.callbacks.query.queryData
  ): Promise<boolean> => {
    let flag = true;
    if (!data.firstName) {
      context.addError([], 'Missing first name', 'firstName');
      flag = false;
    }
    if (!data.lastName) {
      context.addError([], 'Missing last name', 'lastName');
      flag = false;
    }
    if (!data.category) {
      context.addError([], 'Missing category', 'category');
      flag = false;
    }
    if (!data.startDate) {
      context.addError([], 'Missing start date', 'startDate');
      flag = false;
    }
    if (!data.Job.departmentId) {
      context.addError(['Job'], 'Missing department', 'departmentId');
      flag = false;
    }
    return flag;
  };
}

function getRequiredValidations(): Validation[] {
  return [getRequiredFieldsValidations()];
}

class EmployeeDataSource
  implements employeeV2Presentation.dataSources.employee.implementation
{
  private mode: employeeV2Presentation.mode | undefined;
  private data: employeeV2Presentation.dataSources.employee.callbacks.query.queryData =
    {
      Header: {},
      WorkData: {},
      PersonalData: {},
      Job: {},
      ExpenseData: {},
      AbsenceData: {},
      AbsenceSummary: {},
      EmployeeBusinessObject: {},
      SecondaryJobData: [],
      TimesheetPolicyData: {},
      EncryptionData: {},
      ControlFlags: {},
    };
  private metadata: employeeV2Presentation.dataSources.employee.callbacks.query.queryMetadata =
    {
      ExpenseData: {},
      AbsenceData: {},
      AbsenceSummary: {},
      ControlFlags: {},
      PersonalData: {},
    };
  private validationQueue: Validation[] = [];

  async execute_sendUserKeyRequest({
    session,
    input,
  }: employeeV2Presentation.dataSources.employee.routines.sendUserKeyRequest.executeInput): Promise<employeeV2Presentation.dataSources.employee.routines.sendUserKeyRequest.executeOutput> {
    try {
      const userKeyClient = await UserKey.client(session);
      const userKey = await userKeyClient.getUserKey();
      const objectCreators: string[] = Array.from(
        new Set(input.relatedUnsetObjectKeys?.map((item) => item.createdBy))
      );
      await ObjectKeyUserKeyMapping.updateMany(
        session,
        {
          id: {
            $in: input.relatedUnsetObjectKeys?.map((item) => item.id),
          },
        },
        { userKeyId: userKey.id },
        ['id']
      );
      for await (const approver of objectCreators) {
        await ObjectKeyMappingRequest.create(
          session,
          {
            approverUserId: approver,
            status: EvstRequestStatus.Pending,
            objectKeyMappingIds: input.relatedUnsetObjectKeys
              ?.filter((item) => item.createdBy === approver)
              ?.map((item) => item.id),
          },
          ['id']
        );
      }
      return { result: 'success' };
    } catch {
      return { result: 'error' };
    }
  }

  async execute_deleteLeave({
    session,
    input,
  }: employeeV2Presentation.dataSources.employee.routines.deleteLeave.executeInput): Promise<employeeV2Presentation.dataSources.employee.routines.deleteLeave.executeOutput> {
    try {
      await deleteLeave(session, input.leave);
      return { result: 'success' };
    } catch (error) {
      return { result: `${error.message}` };
    }
  }

  async execute_recalculateAbsences({
    session,
  }: employeeV2Presentation.dataSources.employee.routines.recalculateAbsences.executeInput): Promise<employeeV2Presentation.dataSources.employee.routines.recalculateAbsences.executeOutput> {
    const result = await recalculateAbsenceDays(session, this.data.id);
    return { recalculate: result };
  }

  async execute_syncCompanyHolidays({
    session,
    input,
  }: employeeV2Presentation.dataSources.employee.routines.syncCompanyHolidays.executeInput): Promise<employeeV2Presentation.dataSources.employee.routines.syncCompanyHolidays.executeOutput> {
    try {
      await syncCompanyHolidays(
        session,
        this.data.id,
        input.timezoneDiff,
        input.timezone
      );
      return { result: 'success' };
    } catch {
      return { result: 'error' };
    }
  }

  async execute_approveLeave({
    session,
    input,
  }: employeeV2Presentation.dataSources.employee.routines.approveLeave.executeInput): Promise<employeeV2Presentation.dataSources.employee.routines.approveLeave.executeOutput> {
    try {
      await bulkLeaveApprove(session, input.leaves);
      return { result: 'success' };
    } catch {
      return { result: 'error' };
    }
  }

  async execute_rejectLeave({
    session,
    input,
  }: employeeV2Presentation.dataSources.employee.routines.rejectLeave.executeInput): Promise<employeeV2Presentation.dataSources.employee.routines.rejectLeave.executeOutput> {
    try {
      await bulkLeaveReject(session, input.leaves);
      return { result: 'success' };
    } catch {
      return { result: 'error' };
    }
  }

  async update_TimesheetPolicyData({
    fieldName,
    newFieldValue,
  }: employeeV2Presentation.dataSources.employee.callbacks.update_TimesheetPolicyData.input): Promise<employeeV2Presentation.dataSources.employee.callbacks.update_TimesheetPolicyData.output> {
    set(this.data.TimesheetPolicyData, [fieldName], newFieldValue);
  }

  async execute_addAddress({
    input,
  }: employeeV2Presentation.dataSources.employee.routines.addAddress.executeInput): Promise<employeeV2Presentation.dataSources.employee.routines.addAddress.executeOutput> {
    this.setAddressData(input.address);
  }

  async update_AbsenceData({
    fieldName,
    newFieldValue,
  }: employeeV2Presentation.dataSources.employee.callbacks.update_AbsenceData.input): Promise<employeeV2Presentation.dataSources.employee.callbacks.update_AbsenceData.output> {
    set(this.data.AbsenceData, [fieldName], newFieldValue);
    this.data.AbsenceData.adjustmentValue = newFieldValue
      ? (newFieldValue as number)
      : null;
  }
  async update_ExpenseData({
    session,
    fieldName,
    newFieldValue,
  }: employeeV2Presentation.dataSources.employee.callbacks.update_ExpenseData.input): Promise<employeeV2Presentation.dataSources.employee.callbacks.update_ExpenseData.output> {
    set(this.data.ExpenseData, [fieldName], newFieldValue);
    if (fieldName === 'bankAccountNumberType' && newFieldValue) {
      const employeeTranslations: Map<string, string> =
        await getEmployeeTranslations(session);
      this.metadata.ExpenseData[DYNAMIC_FIELDS] = merge(
        this.metadata.ExpenseData[DYNAMIC_FIELDS],
        {
          financialInstitutionIdType: {
            visible: true,
            label: employeeTranslations.get('bankDetails.bankCodeType'),
          },
          financialInstitutionId: {
            visible:
              this.data.ExpenseData.bankAccountNumberType === 'iban' ||
              this.data.ExpenseData.bankAccountNumberType === 'achOrWire'
                ? true
                : false,
            label:
              this.data.ExpenseData.bankAccountNumberType === 'iban'
                ? employeeTranslations.get('bankDetails.bankCode')
                : this.data.ExpenseData.bankAccountNumberType === 'achOrWire'
                  ? employeeTranslations.get('bankDetails.routingNumber')
                  : employeeTranslations.get('bankDetails.bankCode'),
          },
        }
      );
      this.data.ExpenseData.bankAccountHolderName =
        this.data.firstName + ' ' + this.data.lastName;
    }
  }
  async update_Job({
    session,
    fieldName,
    newFieldValue,
    oldFieldValue,
  }: employeeV2Presentation.dataSources.employee.callbacks.update_Job.input): Promise<employeeV2Presentation.dataSources.employee.callbacks.update_Job.output> {
    set(this.data.Job, [fieldName], newFieldValue);
    if (fieldName === 'positionId') {
      const position = await Position.read(
        session,
        { id: newFieldValue as number },
        ['name']
      );
      this.data.Header.description = position?.name;
    }
    if (fieldName === 'departmentId' && newFieldValue !== oldFieldValue) {
      this.data.Job.positionId = null;
    }
  }
  async update_PersonalData({
    fieldName,
    newFieldValue,
  }: employeeV2Presentation.dataSources.employee.callbacks.update_PersonalData.input): Promise<employeeV2Presentation.dataSources.employee.callbacks.update_PersonalData.output> {
    set(this.data.PersonalData, [fieldName], newFieldValue);
  }
  async update_WorkData({
    fieldName,
    newFieldValue,
  }: employeeV2Presentation.dataSources.employee.callbacks.update_WorkData.input): Promise<employeeV2Presentation.dataSources.employee.callbacks.update_WorkData.output> {
    set(this.data.WorkData, [fieldName], newFieldValue);
  }

  public async query({
    session,
    parameters,
    mode,
    queryReason,
  }: employeeV2Presentation.dataSources.employee.callbacks.query.input): Promise<employeeV2Presentation.dataSources.employee.callbacks.query.combinedOutput> {
    this.mode = mode;
    if (mode === 'view') {
      this.metadata.PersonalData = {
        ...this.metadata.PersonalData,
        addressForm: {
          ...this.metadata.PersonalData.addressForm,
          visible: false,
        },
      };
      this.data.ControlFlags.cancelVisible = false;
      this.data.ControlFlags.editVisible = true;
      const admin = await isAdmin(session);
      const ownEmployees = await queryOwnEmployees(session, {}, ['id']);
      const currentEmployee = await tryGetCurrentEmployee(session);
      const manager = ownEmployees
        .map((emp) => emp.id)
        .includes(parameters.employeeId);
      const fieldList = [
        'id',
        'category',
        'contract',
        'employeeNumber',
        'endDate',
        'entityId',
        'personId',
        'id',
        'locationId',
        'name',
        'startDate',
        'status',
        'vacationDaysDelta',
        'bankAccountHolderName',
        'iban',
        'onboardingStatus',
        'email',
        'accessKeyId',
        'equipmentNumber',
        'personalEmail',
        'bankDetailsId',
        'workAddress',
        'personalPhone',
        'holidayCalendar',
        'Employee-Entity.currency',
        'offboardingStatus',
        'firstName',
        'middleName',
        'lastName',
        'preferredFirstName',
        'preferredLastName',
        'displayName',
        'allowEmployeeAccess',
        'probationDuration',
        'probationEndDate',
        'leavesOverlapStatus',
        'externalEmployeeId',
        'migrationConfigurationId',
        'Job-Employee.*',
        'Person-Employee.photo',
        'Person-Employee.phoneNumber',
        'Person-Employee.gender',
        'Person-Employee.dateOfBirth',
        'Person-Employee.addressId',
        'Person-Employee.Person-Address.line1',
        'Person-Employee.Person-Address.line2',
        'Person-Employee.Person-Address.city',
        'Person-Employee.Person-Address.stateProvince',
        'Person-Employee.Person-Address.zipCode',
        'Person-Employee.Person-Address.country',
        'AbsenceCalculation-Employee.*',
        'Leave-Employee.*',
        'Salary-Employee.*',
        'Employee-Bonus.*',
        'Employee-Bonus.Bonus-BonusType.bonusName',
        'Benefit-Employee.*',
        'Benefit-Employee.Benefit-BenefitType.benefitName',
        'Benefit-Employee.Benefit-BenefitType.recurrenceType',
      ] as Any;
      const employeeDataList =
        parameters.employeeId && (admin || manager)
          ? await loadEmployees(
              session,
              {
                id: parameters.employeeId,
                'Job-Employee.status': 'active',
              },
              fieldList
            )
          : await loadEmployees(
              session,
              {
                id: currentEmployee.id,
                'Job-Employee.status': 'active',
              },
              fieldList
            );
      const employeeTranslations: Map<string, string> =
        await getEmployeeTranslations(session);
      const employeeData = employeeDataList[0];
      const day = new Date(employeeData?.startDate).getDate();
      const month = new Date(employeeData?.startDate).getMonth();
      const year = new Date(employeeData?.startDate).getFullYear();
      const startDate = new PlainDate(year, month + 1, day);
      const endDay = new Date(employeeData?.endDate).getDate();
      const endMonth = new Date(employeeData?.endDate).getMonth();
      const endYear = new Date(employeeData?.endDate).getFullYear();
      const endDate = new PlainDate(endYear, endMonth + 1, endDay);
      //Employment data
      this.data.firstName = employeeData.firstName;
      this.data.middleName = employeeData.middleName;
      this.data.lastName = employeeData.lastName;
      this.data.category = employeeData.category;
      this.data.preferredFirstName = employeeData.preferredFirstName;
      this.data.preferredLastName = employeeData.preferredLastName;
      this.data.locationId = employeeData.locationId;
      this.data.entityId = employeeData.entityId;
      this.data.externalEmployeeId = employeeData.externalEmployeeId;
      this.data.equipmentNumber = employeeData.equipmentNumber;
      this.data.accessKeyId = employeeData.accessKeyId;
      this.data.probationEndDate = employeeData.probationEndDate;
      this.data.probationDuration = employeeData.probationDuration;
      this.data.startDate = startDate;
      this.data.endDate = endDate;
      this.data.employeeNumber = employeeData.employeeNumber;
      this.data.employeeDevices = employeeTranslations.get('employee.devices');
      this.data.ControlFlags.isAdminVisible = admin;
      this.data.id = employeeData.id;
      this.data.name = employeeData.name;
      this.data.allowEmployeeAccess = employeeData.allowEmployeeAccess;
      this.data.personId = employeeData.personId;
      this.data.addressId = employeeData?.['Person-Employee']?.addressId;
      this.data.vacationDaysDelta = employeeData?.vacationDaysDelta;
      if (employeeData.entityId) {
        const entity = await Entity.read(
          session,
          { id: employeeData.entityId },
          ['currency']
        );
        this.data.ExpenseData.reimbursementCurrency = entity.currency;
      }

      //Employee business object data

      const [employeeBusinessObject] = await EmployeeBusinessObject.query(
        session,
        {
          where: {
            headerKey: employeeData.id,
          },
        },
        ['id', 'headerKey']
      );
      if (employeeBusinessObject?.headerKey) {
        this.data.EmployeeBusinessObject.headerKey =
          employeeBusinessObject?.headerKey;
        this.data.EmployeeBusinessObject.id = employeeBusinessObject?.id;
      }
      if (!this.data.probationDuration?.probationUnit) {
        this.data.probationDuration = {
          ...this.data.probationDuration,
          probationUnit: EvstCalendarUnit.Months,
        };
      }
      if (
        this.data.probationDuration?.probationUnit !== EvstCalendarUnit.Custom
      ) {
        this.metadata.probationEndDate = {
          ...this.metadata.probationEndDate,
          editable: false,
        };
      }
      if (
        this.data.probationDuration?.probationUnit === EvstCalendarUnit.Custom
      ) {
        this.metadata.probationEndDate = {
          ...this.metadata.probationEndDate,
          editable: true,
        };
        this.data.probationDuration.probationValue = 0;
      }

      //Job Data
      const hrBaseTranslations: Map<string, string> =
        await getHrbaseTranslations(session);
      const currentPrimaryJob = employeeData?.['Job-Employee']?.find(
        (job) =>
          job.status === EvstTimelineStatus.Active && job.isPrimary === true
      );
      const currentSecondaryJobs = employeeData?.['Job-Employee']?.filter(
        (job) =>
          job.status === EvstTimelineStatus.Active && job.isPrimary === false
      );
      const firstJob = employeeData?.['Job-Employee']?.sort(
        (a, b) => a.startDate - b.startDate
      )[0];
      const jobStartDay = new Date(currentPrimaryJob.startDate).getDate();
      const jobStartMonth = new Date(currentPrimaryJob.startDate).getMonth();
      const jobStartYear = new Date(currentPrimaryJob.startDate).getFullYear();
      const jobStartDate = new PlainDate(
        jobStartYear,
        jobStartMonth + 1,
        jobStartDay
      );
      const jobEndDay = new Date(currentPrimaryJob.endDate).getDate();
      const jobEndMonth = new Date(currentPrimaryJob.endDate).getMonth();
      const jobEndYear = new Date(currentPrimaryJob.endDate).getFullYear();
      const jobEndDate = new PlainDate(jobEndYear, jobEndMonth + 1, jobEndDay);
      this.data.Job.departmentId = currentPrimaryJob.departmentId;
      this.data.Job.positionId = currentPrimaryJob.positionId;
      this.data.Job.percentage = currentPrimaryJob.percentage;
      this.data.Job.startDate = jobStartDate;
      this.data.Job.endDate = jobEndDate;
      this.data.Job.jobManager = currentPrimaryJob.jobManager;
      this.data.Job.type = currentPrimaryJob.isPrimary
        ? hrBaseTranslations.get('primary')
        : hrBaseTranslations.get('secondary');
      this.data.Job.regionName = currentPrimaryJob?.regionName;
      this.data.Job.regionId = currentPrimaryJob?.regionId;
      this.data.Job.id = currentPrimaryJob?.id;
      this.data.Job.status = currentPrimaryJob?.status;
      this.data.SecondaryJobData = currentSecondaryJobs.map((job) => {
        return {
          ...job,
          type: hrBaseTranslations.get('secondary'),
          percentage: job.percentage ? `${job.percentage}%` : undefined,
        };
      });
      this.data.Job[METADATA] = {
        positionId: {
          typeParameters: {
            departmentId: this.data.Job.departmentId ?? undefined,
          },
        },
      };

      //Header Data
      const currentSecondaryPositions = currentSecondaryJobs
        .map((job) => job.positionName)
        .join('/ ');
      this.data.Header.description =
        currentSecondaryPositions?.length > 0
          ? `${currentPrimaryJob?.positionName}/${currentSecondaryPositions}`
          : `${currentPrimaryJob?.positionName}`;
      this.data.Header.photo = employeeData?.['Person-Employee'].photo;
      this.data.Header.photoSrc = employeeData?.['Person-Employee'].photo
        ? `/api/app/storage/${employeeData?.['Person-Employee'].photo}`
        : '';
      this.data.Header.title = employeeData.displayName ?? employeeData.name;
      this.data.Header.tabTitle = `${employeeData.employeeNumber}: ${
        employeeData.displayName ?? employeeData.name
      }`;
      this.data.Header.status = employeeData.status;
      if (!this.data.probationDuration?.probationValue) {
        this.data.probationDuration = {
          ...this.data.probationDuration,
          probationValue: 0,
        };
      }

      //Contact Data
      this.data.WorkData.workEmail = employeeData.email;
      this.data.WorkData.workPhone =
        employeeData?.['Person-Employee'].phoneNumber;
      this.data.WorkData.workAddress = employeeData?.workAddress;

      //Personal Data
      this.data.PersonalData.name = employeeData?.name;
      this.data.PersonalData.gender = employeeData?.['Person-Employee'].gender;
      this.data.PersonalData.dateOfBirth =
        employeeData?.['Person-Employee'].dateOfBirth;
      this.data.PersonalData.personalEmail = employeeData.personalEmail;
      this.data.PersonalData.personalPhone = employeeData.personalPhone;
      if (employeeData?.['Person-Employee'].addressId) {
        const address = {
          line1: employeeData?.['Person-Employee']?.['Person-Address']?.line1,
          line2: employeeData?.['Person-Employee']?.['Person-Address']?.line2,
          city: employeeData?.['Person-Employee']?.['Person-Address']?.city,
          stateProvince:
            employeeData?.['Person-Employee']?.['Person-Address']
              ?.stateProvince,
          zipCode:
            employeeData?.['Person-Employee']?.['Person-Address']?.zipCode,
          country:
            employeeData?.['Person-Employee']?.['Person-Address']?.country,
        };
        this.setAddressData(address);
      }

      //Expense data
      let bankData: Partial<BankDetails.BankDetails>;
      if (employeeData?.bankDetailsId) {
        bankData = await BankDetails.read(
          session,
          { id: employeeData?.bankDetailsId },
          [
            'bankAccountNumberType',
            'bankAccountNumber',
            'bankAccountHolderName',
            'financialInstitutionIdType',
            'financialInstitutionId',
            'bankAccountNumberTypeText',
            'bankAccountType',
            'financialInstitutionName',
            'origin',
            'id',
          ]
        );
        this.setBankData(bankData);
      }
      this.metadata.ExpenseData[DYNAMIC_FIELDS] = {
        reimbursementCurrency: {
          type: { urn: 'urn:evst:everest:appserver:primitive:Text' },
          label: employeeTranslations.get('reimbursement.currency'),
          visible: true,
        },
        bankAccountNumberType: {
          type: {
            urn: 'urn:evst:everest:fin/base:field/node:BankDetails.bankAccountNumberType',
          },
          label: employeeTranslations.get('payment.method'),
          visible: true,
        },
        bankAccountNumber: {
          type: { urn: 'urn:evst:everest:appserver:primitive:Text' },
          label: employeeTranslations.get('bankDetails.accountNumber'),
          visible: this.data.ExpenseData.bankAccountNumberType ? true : false,
        },
        financialInstitutionIdType: {
          type: {
            urn: 'urn:evst:everest:fin/base:field/node:BankDetails.financialInstitutionIdType',
          },
          label: employeeTranslations.get('bankDetails.bankCodeType'),
          visible: true,
        },
        financialInstitutionId: {
          type: { urn: 'urn:evst:everest:appserver:primitive:Text' },
          label: employeeTranslations.get('iban'),
          visible: this.data.ExpenseData.bankAccountNumberType ? true : false,
        },
      };

      //Timesheet data
      const timesheetPolicy = await getCurrentAssignedTSPolicy(
        session,
        employeeData.id
      );
      this.data.TimesheetPolicyData = {
        ...this.data.TimesheetPolicyData,
        id: timesheetPolicy?.timesheetPolicyEmployee?.timesheetPolicyId,
        name: timesheetPolicy?.name,
      };
      this.data.existingTimesheetPolicy = this.data.TimesheetPolicyData?.id;
      this.data.existingTimesheetPolicyEmployeeId =
        timesheetPolicy?.timesheetPolicyEmployee?.id;
      if (queryReason === 'externalRequest') {
        this.data.TimesheetPolicyData.timesheetYear = Number(
          new Date().getFullYear()
        );
        const currentYear = Number(new Date()?.getFullYear());
        const startYear = Number(
          new Date(employeeData?.startDate)?.getFullYear()
        );
        const current = currentYear;
        const min = currentYear < startYear ? currentYear : startYear;
        const max = current + 1;
        const years = [];
        for (let i = min; i <= max; i++) {
          years.push({ year: i });
        }
        this.data.TimesheetPolicyData.timesheetYearList = years;
      }
      const timesheetData = await Timesheet.query(
        session,
        {
          where: {
            employeeId: employeeData.id,
            timesheetStatus: {
              $ne: EvstTimeRecordStatus.Draft,
            },
            year: this.data.TimesheetPolicyData.timesheetYear,
          },
        },
        [
          'id',
          'employeeId',
          'month',
          'year',
          'timesheetPolicyId',
          'timesheetPolicyStatus',
          'timesheetPolicyName',
          'timesheetStatus',
          'totalWorkDays',
          'totalWorkTime',
        ]
      );
      const language = getLanguage();
      this.data.TimesheetData = timesheetData.map((timesheet) => {
        return {
          ...timesheet,
          period: new Date(timesheet.year, timesheet.month).toLocaleDateString(
            language,
            {
              month: 'long',
              year: 'numeric',
            }
          ),
          totalWorkTimeText: getTotalWorkTimeText(timesheet.totalWorkTime),
        };
      });

      //Absence data
      const [firstAbsence] = await Leave.query(
        session,
        {
          where: {
            employeeId: employeeData.id,
          },
          orderBy: [
            {
              field: 'startDate',
              ordering: 'asc',
            },
          ],
          take: 1,
        },
        ['id', 'startDate', 'endDate']
      );
      if (queryReason === 'externalRequest') {
        this.data.AbsenceData.absenceYear = Number(new Date().getFullYear());
        this.data.AbsenceData.absenceYearList =
          getAbsenceYearList(firstAbsence);
      }
      const absenceStartDate = toJSDate(
        new PlainDate(this.data.AbsenceData.absenceYear, 1, 1)
      );
      const absenceEndDate = toJSDate(
        new PlainDate(this.data.AbsenceData.absenceYear, 12, 31)
      );
      this.data.AbsenceTableData = await Leave.query(
        session,
        {
          where: {
            employeeId: employeeData?.id,
            $or: [
              {
                startDate: {
                  $between: [absenceStartDate, absenceEndDate],
                },
              },
              {
                endDate: {
                  $between: [absenceStartDate, absenceEndDate],
                },
              },
            ],
          },
          orderBy: ['startDate'],
        },
        [
          'id',
          'startDate',
          'endDate',
          'absenceType',
          'status',
          'createdDate',
          'days',
          'employeeId',
          'mandatoryLeaveId',
        ]
      );
      const assignedAbsencePolicies = await AbsencePolicyEmployee.query(
        session,
        {
          where: {
            employeeId: employeeData.id,
          },
        },
        [
          'id',
          'absencePolicyId',
          'status',
          'policyType',
          'AbsencePolicyEmployee-AbsencePolicy.*',
          'AbsencePolicyEmployee-AbsencePolicy.AbsencePolicyCarryover-AbsencePolicy.carryoverType',
        ]
      );
      const currentVacationPolicy = assignedAbsencePolicies.find(
        (policy) =>
          policy.policyType === 'vacation' && policy.status === 'active'
      );
      const currentSickLeavePolicy = assignedAbsencePolicies.find(
        (policy) =>
          policy.policyType === 'sickLeave' && policy.status === 'active'
      );
      const plannedVacationPolicy = assignedAbsencePolicies.find(
        (policy) =>
          policy.policyType === 'vacation' && policy.status === 'planned'
      );
      const plannedSickLeavePolicy = assignedAbsencePolicies.find(
        (policy) =>
          policy.policyType === 'sickLeave' && policy.status === 'planned'
      );

      this.data.AbsenceData.vacationPolicy =
        currentVacationPolicy?.['AbsencePolicyEmployee-AbsencePolicy'] ??
        plannedVacationPolicy?.['AbsencePolicyEmployee-AbsencePolicy'];

      this.data.AbsenceData.vacationPolicyId = currentVacationPolicy?.[
        'AbsencePolicyEmployee-AbsencePolicy'
      ]
        ? currentVacationPolicy?.['AbsencePolicyEmployee-AbsencePolicy']?.name
        : plannedVacationPolicy?.['AbsencePolicyEmployee-AbsencePolicy']?.name
          ? `${plannedVacationPolicy?.['AbsencePolicyEmployee-AbsencePolicy']
              ?.name} ${hrBaseTranslations.get('planned')}`
          : hrBaseTranslations.get('none');

      this.data.AbsenceData.sickPolicyId = currentSickLeavePolicy?.[
        'AbsencePolicyEmployee-AbsencePolicy'
      ]
        ? currentSickLeavePolicy?.['AbsencePolicyEmployee-AbsencePolicy'].name
        : plannedSickLeavePolicy?.['AbsencePolicyEmployee-AbsencePolicy']?.name
          ? `${plannedSickLeavePolicy?.['AbsencePolicyEmployee-AbsencePolicy']
              ?.name} ${hrBaseTranslations.get('planned')}`
          : hrBaseTranslations.get('none');

      this.data.AbsenceData.holidayCalendarId = employeeData.holidayCalendar;
      const employeeVacationPolicy = assignedAbsencePolicies.find(
        (policy) =>
          policy.policyType === 'vacation' && policy.status === 'active'
      )?.['AbsencePolicyEmployee-AbsencePolicy'];

      const absenceCalculations = employeeData?.['AbsenceCalculation-Employee'];
      const defaultValue = this.data.ControlFlags.viewMode ? 0 : null;
      if (this.data.AbsenceData.adjustmentValueObject) {
        if (employeeVacationPolicy.grantingType === 'fixed') {
          this.data.AbsenceData.adjustmentValue =
            this.data.AbsenceData.adjustmentValueObject.year ===
            this.data.AbsenceData.absenceYear
              ? this.data.AbsenceData.adjustmentValueObject.value
              : absenceCalculations?.find(
                  (x: Any) => x.year === this.data.AbsenceData.absenceYear
                )?.adjustmentDays ?? defaultValue;
        } else {
          this.data.AbsenceData.adjustmentValue =
            this.data.AbsenceData.adjustmentValueObject?.value ?? defaultValue;
        }
      } else {
        this.data.AbsenceData.adjustmentValue =
          employeeVacationPolicy?.grantingType === 'fixed'
            ? absenceCalculations?.find(
                (x: Any) => x.year === this.data.AbsenceData.absenceYear
              )?.adjustmentDays ?? defaultValue
            : defaultValue;
      }
      this.metadata.AbsenceData.adjustmentValue = {
        ...this.metadata.AbsenceData.adjustmentValue,
        label:
          employeeVacationPolicy?.grantingType === 'fixed'
            ? `${this.data.AbsenceData.absenceYear} ${employeeTranslations.get(
                'vacation.adjustment'
              )}`
            : employeeTranslations.get('vacation.adjustment'),
      };

      this.data.AbsenceSummary.summaryLabel =
        employeeVacationPolicy?.grantingType === 'fixed'
          ? `${employeeTranslations.get('absence.summary')} (${
              this.data.AbsenceData.absenceYear
            })`
          : `${employeeTranslations.get('absence.summary')}`;

      this.data.AbsenceSummary.remainingVacationDaysLabel =
        employeeVacationPolicy?.grantingType === 'byAccrual'
          ? `${hrBaseTranslations.get('remaining.vacation.days.today')}`
          : hrBaseTranslations.get('employee.remaining.vacation.days');

      const requestDate = getRequestDate(
        employeeVacationPolicy,
        this.data.AbsenceData.absenceYear
      );
      const remainingVacationDays = await getRemainingVacationDays(
        session,
        employeeData.id,
        requestDate
      );
      this.data.AbsenceSummary.remainingVacationDays =
        remainingVacationDays.toString();

      const carryoverDays = await getRemainingFixedDays(
        session,
        employeeData.id,
        new PlainDate(this.data.AbsenceData.absenceYear, 1, 1),
        null
      );
      this.data.AbsenceSummary.carryoverDays =
        carryoverDays?.carryoverDays.toString() ?? '0';
      this.data.AbsenceSummary.carryoverDaysLabel = `Taken Carryover Days (${
        this.data.AbsenceData.absenceYear - 1
      })`;
      const leaveRequestTranslations: Map<string, string> =
        await getLeaveTranslations(session);
      const vacationDaysData = await getTakenVacationDays(
        session,
        this.data.AbsenceData.absenceYear,
        employeeData.id
      );
      this.data.AbsenceSummary.requestedDays = vacationDaysData.requested;
      this.data.AbsenceSummary.requestedDaysLabel =
        leaveRequestTranslations.get('requested.vacation');
      this.data.AbsenceSummary.takenDays = vacationDaysData.taken;
      this.data.AbsenceSummary.takenDaysLabel =
        leaveRequestTranslations.get('taken.vacation');
      this.data.AbsenceSummary.plannedDays = vacationDaysData.planned;
      this.data.AbsenceSummary.plannedDaysLabel = leaveRequestTranslations.get(
        'planned.approved.vacation'
      );
      this.data.AbsenceTableData = employeeData?.['Leave-Employee'];

      //Salary Data
      this.data.SalaryData = employeeData?.['Salary-Employee']?.map((data) => {
        return {
          ...data,
          currency: this.data.ExpenseData.reimbursementCurrency,
          status: getStatus(data?.startDate, data?.endDate, hrBaseTranslations),
        };
      });
      this.data.BonusData = employeeData?.['Employee-Bonus']?.map((data) => {
        return {
          ...data,
          currency: this.data.ExpenseData.reimbursementCurrency,
          bonusName: data?.['Bonus-BonusType']?.bonusName,
          paymentPeriod: data?.startDate
            ? new Date(data?.paymentPeriod).toLocaleString('default', {
                month: 'long',
              })
            : new Date(data?.paymentPeriod).toLocaleString('default', {
                month: 'long',
                year: 'numeric',
              }),
          status: getStatus(data?.startDate, data?.endDate, hrBaseTranslations),
        };
      });

      this.data.BenefitData = employeeData?.['Benefit-Employee']?.map(
        (data) => {
          return {
            ...data,
            benefitName: data?.['Benefit-BenefitType']?.benefitName,
            recurrenceType: data?.['Benefit-BenefitType']?.recurrenceType,
            status: getStatus(
              data?.startDate,
              data?.endDate,
              hrBaseTranslations
            ),
          };
        }
      );

      //Permission data
      // const permissionTranslations: Map<string, string> =
      //   await getPermissionTranslations(session);
      const permissionData = (await loadEmployeePermissions(
        session,
        employeeData.id
      )) as object[];
      this.data.PermissionData = permissionData?.map((data) => {
        return {
          ...data,
          startDate:
            data?.['method'] === 'userAssignment'
              ? data?.['validFrom']
              : data?.['startDate'],
          endDate:
            data?.['method'] === 'userAssignment'
              ? data?.['validTo']
              : data?.['endDate'],
          status:
            data?.['method'] === 'userAssignment'
              ? getStatus(
                  data?.['validFrom'],
                  data?.['validTo'],
                  hrBaseTranslations
                )
              : getStatus(
                  data?.['startDate'],
                  data?.['endDate'],
                  hrBaseTranslations
                ),
          assignmentMethod:
            data?.['method'] === 'defaultPosition'
              ? hrBaseTranslations.get('position.auto-assigned')
              : data?.['method'] === 'employeePosition'
                ? hrBaseTranslations.get('employee.position.auto-assigned')
                : data?.['method'] === 'employeeDirect'
                  ? `${hrBaseTranslations.get('manually.by')} ${data?.[
                      'createdUser'
                    ]}`
                  : data?.['method'] === 'userAssignment'
                    ? hrBaseTranslations.get('user.assigned')
                    : '',
        };
      });

      //Encryption data
      const currentUser = getCurrentUser(session);
      const objectKeyMappingRequest = await ObjectKeyMappingRequest.query(
        session,
        {
          where: {
            $or: [
              { createdBy: currentUser.id },
              { approverUserId: currentUser.id },
            ],
            status: EvstRequestStatus.Pending,
          },
        },
        ['id', 'status', 'approverUserId', 'objectKeyMappingIds', 'createdBy']
      );
      const objectKeyRequests = await ObjectKeyUserKeyMapping.query(
        session,
        {
          where: {
            userId: currentUser.id,
            encryptedObjectKey: {
              $eq: null,
            },
          },
        },
        [
          'id',
          'userKeyId',
          'objectKeyId',
          'validFrom',
          'userId',
          'encryptedObjectKey',
          'createdBy',
        ]
      );
      const pendingRequests = objectKeyMappingRequest.filter(
        (request) => request.createdBy === currentUser.id
      );
      const keyRequestToApprove = objectKeyMappingRequest.filter(
        (request) => request.approverUserId === currentUser.id
      );
      this.data.EncryptionData.objectKeyRequests = objectKeyRequests;
      this.data.EncryptionData.relatedUserKey = await getUserKeyByEmployeeId(
        session,
        employeeData.id
      );
      this.data.EncryptionData.employeeUserData = await getUserDataByEmployeeId(
        session,
        employeeData.id
      );
      const masterKeyClient = await MasterKey.client(session);
      this.data.EncryptionData.masterKeyData =
        await masterKeyClient.getMasterKey();

      //Control flags
      this.data.ControlFlags.viewMode = this.mode === 'view';
      this.data.ControlFlags.editMode = this.mode === 'edit';
      this.data.ControlFlags.hasPrimaryJob =
        currentPrimaryJob?.id !== undefined;
      this.data.ControlFlags.noPrimaryJob =
        !this.data.ControlFlags.hasPrimaryJob;
      this.data.ControlFlags.startDateMismatch =
        new Date(firstJob.startDate).toISOString() !==
        new Date(employeeData.startDate).toISOString();
      this.data.ControlFlags.hasSecondaryJob = currentSecondaryJobs.length > 0;
      this.data.ControlFlags.showCarryover = showCarryover(
        employeeVacationPolicy,
        this.data.AbsenceData.absenceYearList,
        this.data.AbsenceData.absenceYear,
        carryoverDays?.carryoverDays
      );
      this.data.ControlFlags.isManager = manager;
      this.data.ControlFlags.isAdmin = admin;
      this.data.ControlFlags.isManagerOrAdmin = manager || admin;
      this.data.ControlFlags.isCurrentEmployee = await isCurrentEmployee(
        session,
        { id: employeeData.id }
      );
      this.data.ControlFlags.isCurrentEmployeeOrAdmin =
        this.data.ControlFlags.isCurrentEmployee || admin;

      this.data.ControlFlags.encryptionRequestSentAlert =
        pendingRequests?.length > 0;
      this.data.ControlFlags.encryptionRequestToApproveAlert =
        keyRequestToApprove?.length > 0;
      this.data.ControlFlags.encryptionRequestAlert =
        objectKeyRequests?.length > 0 && (pendingRequests?.length ?? 0) === 0;
      this.data.ControlFlags.hasUserKey = this.data.EncryptionData
        .relatedUserKey?.id
        ? true
        : false;
      this.data.ControlFlags.hasNoUserKey = !this.data.ControlFlags.hasUserKey;
      this.data.ControlFlags.hasNoMasterKey =
        !this.data.EncryptionData.masterKeyData?.publicKey;
    }
    if (mode === 'edit') {
      this.data.ControlFlags.viewMode = this.mode === 'view';
      this.data.ControlFlags.editMode = this.mode === 'edit';
      this.metadata.PersonalData = {
        ...this.metadata.PersonalData,
        addressForm: {
          ...this.metadata.PersonalData.addressView,
          visible: false,
        },
      };
      this.data.ControlFlags.editVisible = false;
      this.data.ControlFlags.cancelVisible = true;
    }
    return {
      [DATA]: this.data,
      [METADATA]: this.metadata,
    };
  }

  public async update({
    session,
    fieldName,
    newFieldValue,
  }: employeeV2Presentation.dataSources.employee.callbacks.update.input): Promise<employeeV2Presentation.dataSources.employee.callbacks.update.output> {
    set(this.data, [fieldName], newFieldValue);

    if (fieldName === 'category') {
      this.metadata.endDate = {
        visible: newFieldValue === 'External',
      };
    }
    if (fieldName === 'probationDuration') {
      if (newFieldValue?.['probationUnit'] === EvstCalendarUnit.Custom) {
        this.data.probationDuration.probationValue = 0;
      } else if (
        newFieldValue?.['probationUnit'] !== EvstCalendarUnit.Custom &&
        this.data.probationDuration.probationUnit &&
        this.data.probationDuration.probationValue
      ) {
        const probationEndDate = getProbationEndDate(
          this.data.startDate,
          this.data.probationDuration.probationValue,
          this.data.probationDuration.probationUnit
        );
        this.data.probationEndDate = probationEndDate;
      } else if (newFieldValue?.['probationValue'] === 0) {
        this.data.probationEndDate = null;
      }
    }
    if (
      this.data.probationDuration?.probationUnit !== EvstCalendarUnit.Custom
    ) {
      this.metadata.probationEndDate = {
        ...this.metadata.probationEndDate,
        editable: false,
      };
    }
    if (
      this.data.probationDuration?.probationUnit === EvstCalendarUnit.Custom
    ) {
      this.metadata.probationEndDate = {
        ...this.metadata.probationEndDate,
        editable: true,
      };
      this.data.probationDuration.probationValue = 0;
    }
    if (
      fieldName === 'startDate' &&
      newFieldValue &&
      this.data.probationDuration?.probationValue &&
      this.data.probationDuration?.probationUnit &&
      this.data.probationDuration?.probationUnit !== EvstCalendarUnit.Custom
    ) {
      const day = (newFieldValue as PlainDate).day;
      const month = (newFieldValue as PlainDate).month;
      const year = (newFieldValue as PlainDate).year;
      const newDate = new PlainDate(year, month, day);
      const probationEndDate = getProbationEndDate(
        newDate,
        this.data.probationDuration.probationValue,
        this.data.probationDuration.probationUnit
      );
      this.data.probationEndDate = probationEndDate;
    } else if (
      fieldName === 'startDate' &&
      !newFieldValue &&
      this.data.probationDuration?.probationValue &&
      this.data.probationDuration?.probationUnit &&
      this.data.probationDuration?.probationUnit !== EvstCalendarUnit.Custom
    ) {
      this.data.probationEndDate = null;
    }
    if (fieldName === 'entityId') {
      const entity = await Entity.read(
        session,
        { id: newFieldValue as number },
        ['currency']
      );
      this.data.ExpenseData.reimbursementCurrency = entity.currency;
    }
    if (fieldName === 'locationId') {
      const location = await Location.read(
        session,
        { id: newFieldValue as number },
        ['holidayCalendarId']
      );
      this.data.AbsenceData.holidayCalendarId = location.holidayCalendarId;
    }
  }

  public async validate_save({
    context,
    validateReason,
  }: employeeV2Presentation.dataSources.employee.routines.save.validateInput): Promise<employeeV2Presentation.dataSources.employee.routines.save.validateOutput> {
    const validations = [...this.validationQueue];

    if (validateReason === 'executionRequest') {
      validations.push(...getRequiredValidations());
    }

    const failedValidations: Validation[] = [];

    for (const validation of validations) {
      const validationResult = await validation(context, this.data);
      if (validationResult === false) {
        failedValidations.push(validation);
      }
    }

    this.validationQueue = failedValidations;

    return validateReason === 'earlyEvaluation'
      ? true
      : failedValidations.length === 0;
  }

  public async execute_save({
    session,
    input,
  }: employeeV2Presentation.dataSources.employee.routines.save.executeInput): Promise<employeeV2Presentation.dataSources.employee.routines.save.executeOutput> {
    const addressData = this.data.PersonalData.address ?? {};
    let employeeData = {
      probationDuration: this.data.probationDuration,
      category: this.data.category,
      startDate: this.data.startDate,
      probationEndDate: this.data.probationEndDate,
      endDate: this.data.endDate,
      entityId: this.data.entityId,
      locationId: this.data.locationId,
      holidayCalendar: this.data.AbsenceData.holidayCalendarId,
      accessKeyId: this.data.accessKeyId,
      equipmentNumber: this.data.equipmentNumber,
      externalEmployeeId: this.data.externalEmployeeId,
      workAddress: this.data.WorkData.workAddress,
      personalEmail: this.data.PersonalData.personalEmail,
      personalPhone: this.data.PersonalData.personalPhone,
      id: this.data.id,
      vacationDaysDelta: this.data.vacationDaysDelta,
    };
    if (
      this.data.AbsenceData.vacationPolicy.grantingType === 'byAccrual' &&
      this.data.AbsenceData.adjustmentValue
    ) {
      employeeData = {
        ...employeeData,
        vacationDaysDelta: new Decimal(this.data.AbsenceData.adjustmentValue),
      };
    }
    const personData = {
      firstName: this.data.firstName,
      lastName: this.data.lastName,
      middleName: this.data.middleName,
      preferredFirstName: this.data.preferredFirstName,
      preferredLastName: this.data.preferredLastName,
      email: this.data.WorkData.workEmail,
      phoneNumber: this.data.WorkData.workPhone,
      gender: this.data.PersonalData.gender,
      dateOfBirth: this.data.PersonalData.dateOfBirth,
      name: `${this.data.firstName} ${this.data.lastName}`,
      startDate: this.data.startDate,
      endDate: this.data.endDate,
      addressId: this.data.addressId,
    };
    const jobData = {
      percentage: this.data.Job.percentage,
      departmentId: this.data.Job.departmentId,
      jobManager: this.data.Job.jobManager,
      positionId: this.data.Job.positionId,
      regionId: this.data.Job.regionId,
      employeeId: this.data.id,
      id: this.data.Job.id,
    };
    const expenseData = {
      bankAccountNumberType: this.data.ExpenseData
        .bankAccountNumberType as EvstBankAccountNumberType,
      bankAccountHolderName: this.data.ExpenseData
        .bankAccountHolderName as string,
      bankAccountNumber: this.data.ExpenseData.bankAccountNumber as string,
      financialInstitutionIdType: this.data.ExpenseData
        .financialInstitutionIdType as EvstFinancialInstutionIdType,
      financialInstitutionId: this.data.ExpenseData
        .financialInstitutionId as string,
    };
    const policies = [];
    if (this.data.AbsenceData.vacationPolicyId) {
      policies.push({ id: this.data.AbsenceData.vacationPolicyId });
    }
    if (this.data.AbsenceData.sickPolicyId) {
      policies.push({ id: this.data.AbsenceData.sickPolicyId });
    }
    await updateEmployeeDetail(
      session,
      {
        ...personData,
        startDate: toJSDate(personData.startDate),
        endDate: toJSDate(personData.endDate),
        id: this.data.personId,
      },
      {
        ...employeeData,
        startDate: toJSDate(employeeData.startDate),
        endDate: toJSDate(employeeData.endDate),
      },
      addressData,
      expenseData,
      input.currentEndDate?.toISOString() ?? undefined,
      jobData
    );
    if (
      this.data.AbsenceData.vacationPolicy.grantingType === 'fixed' &&
      this.data.AbsenceData.adjustmentValue
    ) {
      const absenceCalculationClient = await AbsenceCalculation.client(session);
      await absenceCalculationClient.upsert(
        {
          employeeId: employeeData?.id,
          year:
            this.data.AbsenceData.adjustmentValueObject?.year ??
            this.data.AbsenceData.absenceYear,
          absencePolicyId: Number(this.data.AbsenceData.vacationPolicy.id),
        },
        {
          adjustmentDays: new Decimal(this.data.AbsenceData.adjustmentValue),
        },
        ['id']
      );
    }
    if (
      this.data.existingTimesheetPolicy !== this.data.TimesheetPolicyData.id
    ) {
      await removeAssignedEmployees(
        session,
        [this.data.existingTimesheetPolicyEmployeeId],
        new Date()
      );
      if (this.data.TimesheetPolicyData.id && this.data.id) {
        await assignEmployeesToTimesheet(
          session,
          this.data.TimesheetPolicyData.id,
          [this.data.id],
          new Date(),
          false
        );
      }
    }
  }
  private setAddressData(
    address: employeeV2Presentation.dataSources.employee.levels['PersonalData']['address']
  ) {
    this.data.PersonalData.address = {
      line1: address?.line1,
      line2: address?.line2 ? `${address.line2}, ` : '',
      city: address?.city,
      stateProvince: address?.stateProvince,
      zipCode: address?.zipCode,
      country: address?.country,
    };
    this.data.PersonalData.addressForm = `${address?.line1}, ${
      address?.line2 ? `${address.line2}, ` : ''
    }${address?.city}, ${address?.stateProvince}, ${address?.zipCode}, ${address?.country}`;
    this.data.PersonalData.addressView = `${address?.line1}\n${
      address?.line2 ? `${address.line2}, ` : ''
    }${address?.city}, ${address?.stateProvince} ${address?.zipCode}\n${address?.country}`;
  }
  private setBankData(
    bankData: employeeV2Presentation.dataSources.employee.levels['ExpenseData']
  ) {
    if (bankData?.id) {
      this.data.ExpenseData.bankAccountNumberType =
        bankData.bankAccountNumberType;
      this.data.ExpenseData.bankAccountNumber = bankData.bankAccountNumber;
      this.data.ExpenseData.bankAccountType = bankData.bankAccountType;
      this.data.ExpenseData.bankAccountHolderName =
        bankData.bankAccountHolderName;
      this.data.ExpenseData.financialInstitutionIdType =
        bankData.financialInstitutionIdType;
      this.data.ExpenseData.financialInstitutionId =
        bankData.financialInstitutionId;
    } else {
      return;
    }
  }
}

export default {
  employee() {
    return new EmployeeDataSource();
  },
} satisfies employeeV2Presentation.implementation;

function getProbationEndDate(
  newStart: PlainDate,
  probationValue: number,
  probationUnit: EvstCalendarUnit
): PlainDate {
  if (!newStart) {
    return null;
  }
  if (probationUnit === EvstCalendarUnit.Months && probationValue > 0) {
    const months = probationValue;
    return newStart.plus({ months: months }).plus({ days: -1 });
  } else if (probationUnit === EvstCalendarUnit.Weeks && probationValue > 0) {
    const days = probationValue * 7;
    return newStart.plus({ days: days });
  } else {
    return null;
  }
}

async function getEmployeeTranslations(session) {
  return await getTranslationsMap(
    session,
    [
      'payment.method',
      'employee.bankAccount',
      'reimbursement.currency',
      'bankAccountHolder',
      'bankDetails.accountNumber',
      'bankDetails.accountNumber',
      'bankDetails.bankCodeType',
      'iban',
      'bankDetails.accountDetails',
      'bankDetails.routingNumber',
      'bankDetails.bankCode',
      'primary',
      'employee.devices',
      'vacation.adjustment',
      'absence.summary',
    ],
    'everest.hr.base/employee'
  );
}

async function getHrbaseTranslations(session) {
  return await getTranslationsMap(
    session,
    [
      'payment.method',
      'employee.bankAccount',
      'reimbursement.currency',
      'bankAccountHolder',
      'bankDetails.accountNumber',
      'bankDetails.accountNumber',
      'bankDetails.bankCodeType',
      'iban',
      'bankDetails.accountDetails',
      'bankDetails.routingNumber',
      'bankDetails.bankCode',
      'primary',
      'secondary',
      'none',
      'planned',
      'remaining.vacation.days.today',
      'employee.remaining.vacation.days',
      'active',
      'inactive',
      'planned',
      'position.auto-assigned',
      'employee.position.auto-assigned',
      'manually.by',
      'user.assigned',
    ],
    'everest.hr.base/hrbase'
  );
}

async function getLeaveTranslations(session) {
  return await getTranslationsMap(
    session,
    ['planned.approved.vacation', 'taken.vacation', 'requested.vacation'],
    'everest.hr.base/leaveRequest'
  );
}

// async function getEncryptionTranslations(session) {
//   return await getTranslationsMap(
//     session,
//     ['enc.user.key.notfound'],
//     'encryption'
//   );
// }

// async function getPermissionTranslations(session) {
//   return await getTranslationsMap(
//     session,
//     ['position.auto-assigned'],
//     'securtiyManagement'
//   );
// }

function getTotalWorkTimeText(timespan): string {
  if (!timespan || timespan <= 0) {
    return '0';
  }
  const MS_PER_HOUR = 1000 * 60 * 60;
  const MS_PER_MINUTE = 1000 * 60;
  const hours = Math.floor(timespan / MS_PER_HOUR);
  const mins = Math.floor((timespan - hours * MS_PER_HOUR) / MS_PER_MINUTE);
  const minStr = mins > 0 ? `${mins} min` : '';
  const hourStr = hours > 0 ? `${hours} hrs` : '';
  return mins + hours === 0 ? '0' : `${hourStr} ${minStr}`.trim();
}

function getAbsenceYearList(
  absence: Partial<Leave.Leave>
): Array<{ year: number }> {
  const today = new Date();
  const startDate = absence?.startDate ? new Date(absence?.startDate) : today;
  startDate.setMinutes(startDate.getMinutes() + startDate?.getTimezoneOffset());
  const current = today?.getFullYear();
  const min =
    today?.getTime() < startDate?.getTime()
      ? today?.getFullYear()
      : startDate?.getFullYear();
  const max = current + 1;
  const years = [];
  for (let i = min; i <= max; i++) {
    years.push({ year: i });
  }
  return years;
}

function getRequestDate(
  policy: Partial<AbsencePolicy.AbsencePolicy>,
  year: number
): PlainDate {
  const today = new Date();
  const todayPlain = new PlainDate(
    today.getFullYear(),
    today.getMonth() + 1,
    today.getDate()
  );
  return policy?.grantingType === 'byAccrual'
    ? todayPlain
    : year === todayPlain.year
      ? todayPlain
      : year > todayPlain.year
        ? new PlainDate(year, 1, 1)
        : new PlainDate(year, 12, 31);
}

function showCarryover(
  policy: Partial<AbsencePolicy.AbsencePolicy>,
  years: { year?: number }[],
  year: number,
  carryoverDays: number
) {
  const carryoverType =
    policy?.['AbsencePolicyCarryover-AbsencePolicy']?.[
      'AbsencePolicyCarryover-AbsencePolicy'
    ]?.carryoverType ?? null;
  if (
    policy?.grantingType === EvstGrantingType.Fixed &&
    carryoverType === EvstCarryoverType.Limited
  ) {
    if (carryoverDays === 0) {
      return false;
    }
    return years?.find((x) => x.year === year - 1) ? true : false;
  } else {
    return false;
  }
}

function getStatus(
  startDate: Date,
  endDate: Date,
  translation: Map<string, string>
) {
  if (
    new Date(startDate).getTime() <= Date.now() &&
    new Date(endDate).getTime() >= Date.now()
  ) {
    return translation.get('active');
  } else if (new Date(startDate).getTime() <= Date.now() && !endDate) {
    return translation.get('active');
  } else if (new Date(startDate).getTime() >= Date.now()) {
    return translation.get('planned');
  } else if (!startDate && !endDate) {
    return translation.get('active');
  } else {
    return translation.get('inactive');
  }
}
