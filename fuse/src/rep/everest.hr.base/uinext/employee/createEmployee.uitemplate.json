{"version": 2, "uicontroller": ["createEmployee.uicontroller.ts", "everest.hr.base/uinext/shared/fieldValidations.uicontroller.js"], "uimodel": {"state": {"name": "", "position": "", "timeOff": "annual", "form": {"reimbursementCurrency": ""}, "probationEditMode": "@controller:setProbationEditMode()"}, "nodes": {"Person": {"type": "struct", "query": "@controller:get<PERSON>erson<PERSON><PERSON>y()", "modelId": "everest.hr.base/PersonModel.Person", "fieldList": ["addressId", "endDate", "gender", "id", "name", "displayName", "personNumber", "phoneNumber", "photo", "photoName", "salutation", "startDate", "status", "firstName", "middleName", "lastName", "preferredFirstName", "preferredLastName", "email", "dateOfBirth"]}, "HolidayCalendars": {"type": "list", "modelId": "everest.hr.base/HolidayCalendarV2Model.HolidayCalendarV2", "query": {"where": {"status": "Active"}}, "fieldList": ["id", "countryCode", "subdivisionCode", "status", "HolidayCalendar-CountryISOV2.country", "HolidayCalendar-CountryISOSubdivisionV2.subdivision"]}, "Employee": {"type": "struct", "modelId": "everest.hr.base/EmployeeModel.Employee", "fieldList": ["category", "contract", "employeeNumber", "endDate", "entityId", "id", "locationId", "name", "displayName", "personId", "startDate", "status", "vacationDaysDelta", "iban", "bankAccountHolderName", "accessKeyId", "equipmentNumber", "personalEmail", "holidayCalendar", "workAddress", "personalPhone", "probationEndDate", "probationDuration", "externalEmployeeId", "employmentCategoryId", "workingDays", "weeklyWorkingHours"]}, "Department": {"type": "struct", "modelId": "everest.hr.base/DepartmentModel.Department"}, "Job": {"type": "struct", "modelId": "everest.hr.base/JobModel.Job", "fieldList": ["departmentId", "employeeId", "endDate", "id", "percentage", "positionId", "regionId", "salary", "startDate", "jobManager"]}, "Locations": {"type": "list", "query": {}, "modelId": "everest.hr.base/LocationModel.Location", "fieldList": ["id", "locationName", "holidayCalendarId"]}, "Entities": {"type": "list", "modelId": "everest.base/EntityModel.Entity", "query": {}, "fieldList": ["entityName", "id", "currency"]}, "Address": {"type": "struct", "query": "@controller:get<PERSON><PERSON><PERSON><PERSON><PERSON>y()", "modelId": "everest.base/AddressModel.Address"}, "User": {"type": "struct", "modelId": "everest.appserver.usermgmt/UserModel.User"}, "AbsencePolicies": {"type": "list", "modelId": "everest.hr.base/AbsencePolicyModel.AbsencePolicy", "query": {}, "fieldList": ["name", "id", "policyType"]}, "AbsencePolicyEmployeeVacation": {"type": "struct", "modelId": "everest.hr.base/AbsencePolicyEmployeeModel.AbsencePolicyEmployee"}, "AbsencePolicyEmployeeSick": {"type": "struct", "modelId": "everest.hr.base/AbsencePolicyEmployeeModel.AbsencePolicyEmployee"}, "BankDetails": {"type": "struct", "modelId": "everest.fin.base/BankDetailsModel.BankDetails", "fieldList": ["financialInstitutionName", "id", "financialInstitutionIdType", "financialInstitutionId", "bankAccountNumberType", "bankAccountNumber", "bankAccountType", "bankAccountHolderName", "bankAccountNumberTypeText", "origin"]}}}, "uiview": {"layout": "everest.base.ui/layouts/uilayout/segmented", "props": {"i18n": ["hrbase", "employee"], "title": "@controller:getName()", "stretch": true, "tabTitle": "{{create.employee}}", "description": "@state:position", "status": "{{new}}", "showAvatar": true, "customActions": [{"variant": "primary", "label": "@controller:createLabel()", "onClick": "@controller:createEmployee", "disabled": "@controller:isFormDisabled()"}], "stepsContent": [{"title": "{{employment.details}}", "components": [{"component": "Block", "customId": "employeeEmploymentDetails", "size": "12", "columns": "6", "isEditing": true, "sectionVariant": "card", "variant": "secondary", "elements": [{"component": "Input", "label": "{{first.name}} *", "size": "1", "value": "@binding:Person.firstName", "rules": "@controller:validateFirstName()"}, {"component": "Input", "label": "{{middle.name}}", "size": "1", "value": "@binding:<PERSON>.middleName"}, {"component": "Input", "label": "{{last.name}} *", "size": "1", "value": "@binding:Person.lastName", "rules": "@controller:validateLastName()"}, {"component": "Input", "label": "{{preferred.first.name}}", "value": "@binding:Person.preferredFirstName", "size": 1}, {"component": "Input", "label": "{{preferred.last.name}}", "value": "@binding:Person.preferredLastName", "size": 1}, {"label": "{{category}}", "component": "Select", "size": "1", "value": "@binding:Employee.employmentCategoryId", "bottomActions": [{"label": "{{create}}", "onClick": "@controller:onCreateCategoryClick"}]}, {"component": "DatePicker", "label": "{{startDate}} *", "size": "1", "format": "MM/dd/yyyy", "action": "create", "value": "@binding:Employee.startDate", "rules": "@controller:validateField()", "onChange": "@controller:updateProbationEndDate"}, {"component": "DatePicker", "label": "{{endDate}}", "size": "1", "format": "MM/dd/yyyy", "action": "create", "value": "@binding:Employee.endDate"}, {"component": "Select", "label": "{{entity}} *", "size": "1", "idProp": "id", "textProp": "entityName", "action": "create", "value": "@binding:Employee.entityId", "list": "@binding:Entities", "onChange": "@controller:setReimbursementCurrency", "rules": "@controller:validateField()"}, {"component": "Select", "label": "{{location}}", "size": "1", "idProp": "id", "textProp": "locationName", "action": "create", "value": "@binding:Employee.locationId", "list": "@binding:Locations", "onChange": "@controller:setHolidayCalendar"}, {"label": "{{probationDuration}}", "value": "@binding:Employee.probationDuration", "initialValue": "@controller:setProbationDuration()", "action": "create", "onChange": "@controller:calculateProbationEndDate", "compositeConfig": {"probationValue": {"isDisabled": "@controller:isProbationValueDisabled()", "onBlur": "@controller:handleProbationValueEdit"}, "probationUnit": {"allowClear": false, "value": "@binding:Employee.probationDuration.probationUnit", "onChange": "@controller:setProbationEdit"}}}, {"component": "DatePicker", "label": "{{probationEndDate}}", "size": "1", "format": "MM/dd/yyyy", "action": "create", "value": "@binding:Employee.probationEndDate", "isDisabled": "@controller:isProbationEndDateDisabled()", "onChange": "@controller:setCustomProbationEndDate", "minDate": "@controller:getStartDate()"}, {"label": "{{accessKeyId}}", "size": "1", "action": "create", "value": "@binding:Employee.accessKeyId"}, {"label": "{{equipmentNumber}}", "size": "1", "action": "create", "value": "@binding:Employee.equipmentNumber"}], "moreDetails": {"label": "{{more.employee.details}}", "expanded": "@controller:moreDetailsExpanded()", "elements": [{"label": "{{externalEmployeeId}}", "size": "1", "action": "create", "value": "@binding:Employee.externalEmployeeId"}]}}, {"component": "H4", "fontWeight": "bold", "content": "{{jobs.and.reporting}}", "size": "12"}, {"component": "Block", "customId": "employeeJobsAndReporting", "size": 12, "columns": "5", "isEditing": true, "sectionVariant": "card", "variant": "secondary", "elements": [{"label": "{{department}} *", "value": "@binding:Job.departmentId", "rules": "@controller:validateField()", "onChange": "@controller:set<PERSON>anager"}, {"label": "{{position}}", "size": "1", "action": "create", "value": "@binding:Job.positionId"}, {"component": "Select", "label": "{{manager}} *", "size": "1", "action": "create", "value": "@binding:Job.jobManager"}, {"component": "Select", "label": "{{region}}", "size": "1", "action": "create", "value": "@binding:Job.regionId"}, {"component": "Input", "label": "{{working.percentage}}", "action": "create", "size": "1", "value": "@bindingController(Job.percentage, getPercentage())"}], "moreDetails": {"label": "{{employee.workingSchedule}}", "expanded": "@controller:moreDetailsExpanded()", "elements": [{"label": "{{employee.workingDays}}", "value": "@binding:Employee.workingDays", "isEditing": true}, {"label": "{{employee.weekly.workingHours}}", "value": "@binding:Employee.weeklyWorkingHours", "isEditing": true}]}}, {"component": "H4", "fontWeight": "bold", "content": "{{employee.contactInfomation}}", "size": "12"}, {"component": "Block", "customId": "employeeContactInformation", "size": "12", "isEditing": true, "sectionVariant": "card", "variant": "secondary", "elements": [{"component": "Input", "label": "{{workEmail}}", "size": 1, "action": "create", "value": "@binding:Person.email", "isDisabled": "@controller:checkAddProfile()"}, {"component": "Input", "size": 1, "value": "@binding:Person.phoneNumber", "label": "{{workPhoneNumber}}", "action": "create"}, {"component": "Input", "label": "{{work.address}}", "value": "@binding:Employee.workAddress", "action": "create"}]}]}, {"title": "{{personal.details}}", "components": [{"component": "Block", "customId": "employeePersonalDetails", "size": 12, "columns": "4", "isEditing": true, "sectionVariant": "card", "variant": "secondary", "elements": [{"component": "Select", "size": "1", "label": "{{gender}}", "idProp": "value", "textProp": "text", "list": [{"text": "{{male}}", "value": "Male"}, {"text": "{{female}}", "value": "Female"}, {"text": "{{non.binary}}", "value": "Non Binary"}, {"text": "{{not.specified}}", "value": "Not Specified"}], "value": "@binding:Person.gender"}, {"component": "DatePicker", "label": "{{dateOfBirth}}", "size": 1, "format": "MM/dd/yyyy", "value": "@binding:Person.dateOfBirth", "rules": "@controller:dateOfBirthValidate()"}, {"component": "Input", "label": "{{personal.email}}", "size": 1, "value": "@binding:Employee.personalEmail"}, {"component": "Input", "label": "{{personal.phone.employee}}", "value": "@binding:Employee.personalPhone"}, {"component": "Input", "label": "{{address}}", "multiline": false, "visible": true, "name": "home<PERSON>dd<PERSON>", "hybridConfig": {"icon": "location_on", "iconPosition": "left", "variant": "form", "onClick": "@controller:onAddressIconClick", "enableInputClick": true}, "value": "@controller:get<PERSON><PERSON><PERSON>()", "isDisabled": false, "isEditing": true}]}, {"component": "H4", "fontWeight": "bold", "content": "{{employee.bankAccount}}", "size": "12"}, {"component": "Block", "customId": "employeeExpensePayment", "size": "12", "isEditing": true, "columns": 5, "sectionVariant": "card", "variant": "secondary", "elements": [{"component": "Input", "label": "{{reimbursement.currency}}", "name": "reimbursementCurrency", "isDisabled": true}, {"label": "{{payment.method}}", "action": "create", "value": "@binding:BankDetails.bankAccountNumberType"}, {"label": "{{bankAccountHolder}}", "action": "create", "value": "@bindingController(BankDetails.bankAccountHolderName, getAccountHolderName())"}, {"label": "@controller:getAccountDetailsLabel()", "action": "create", "value": "@binding:BankDetails.bankAccountNumber", "visible": "@controller:isPaymentMethodSelected()", "rules": "@controller:validateBankField()"}, {"label": "{{bankDetails.bankCodeType}}", "value": "@binding:BankDetails.financialInstitutionIdType", "visible": "@controller:isPaymentMethodSelected()", "action": "create", "rules": "@controller:validateBankField()"}, {"label": "{{bankDetails.routingNumber}}", "value": "@binding:BankDetails.financialInstitutionId", "visible": "@controller:isPaymentMethod('achOrWire')", "action": "create", "rules": "@controller:validateBankField()"}, {"label": "{{bankDetails.bankCode}}", "value": "@binding:BankDetails.financialInstitutionId", "visible": "@controller:isPaymentMethod('iban')", "action": "create", "rules": "@controller:validateBankField()"}]}]}, {"title": "{{absences}}", "components": [{"component": "Block", "customId": "employeeAbsences", "size": "12", "isEditing": true, "columns": 3, "sectionVariant": "card", "variant": "secondary", "elements": [{"component": "Select", "label": "{{vacation.policy}}", "action": "create", "size": "1", "idProp": "id", "textProp": "name", "name": "vacationPolicy", "list": "@controller:getAbsencePolicies('vacation')", "value": "@state:form.vacationPolicy"}, {"component": "Select", "label": "{{sick.policy}}", "size": "1", "action": "create", "idProp": "id", "textProp": "name", "name": "sickPolicy", "list": "@controller:getAbsencePolicies('sickLeave')", "value": "@state:form.sickPolicy"}, {"label": "{{holiday.calendar}}", "size": 1, "component": "Select", "action": "create", "idProp": "id", "textProp": "name", "list": "@controller:getHolidayCalendars()", "value": "@binding:Employee.holidayCalendar"}]}]}]}, "actions": {"content": [{"variant": "primary", "label": "{{create}}", "onClick": "@controller:createEmployee", "disabled": "@controller:isFormDisabled()"}]}}}