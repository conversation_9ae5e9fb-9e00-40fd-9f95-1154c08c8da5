{"version": 2, "uicontroller": ["employee.uicontroller.ts", "everest.hr.base/uinext/leaveRequest/leaveRequest.uicontroller.ts", "everest.hr.base/uinext/shared/helpers.uicontroller.js", "everest.hr.base/uinext/shared/fieldValidations.uicontroller.js", "everest.hr.base/uinext/shared/statusMatcher.uicontroller.ts"], "uimodel": {"state": {"mode": "view", "editOwnProfile": false, "jobUpdate": false, "events": [], "loading": false, "year": "@controller:setYear()", "documentFileId": null, "photoIsLoading": false, "boardingType": "@controller:setBoardingState()", "compensationReportTimeRange": "last12Months", "probationEditMode": "@controller:setProbationEditMode()", "timesheetYear": "@controller:setTimesheetYear()", "absenceYears": []}, "nodes": {"CurrentUser": {"type": "struct", "modelId": "everest.appserver.usermgmt/UserModel.User", "readCurrent": {}, "fieldList": ["id", "userId", "email"]}, "UserKey": {"type": "struct", "modelId": "everest.base.encryption/UserKeyModel.UserKey", "getUserKey": {}, "fieldList": ["id", "userPin", "encryptedPrivateKeyByUserPin", "encryptedPrivateKeyByObjectKey", "keySalt", "public<PERSON>ey", "objectKeyId", "userId"]}, "UserKeyInMappingRequest": {"type": "list", "modelId": "everest.base.encryption/UserKeyModel.UserKey", "fieldList": ["id", "userPin", "encryptedPrivateKeyByUserPin", "encryptedPrivateKeyByObjectKey", "keySalt", "public<PERSON>ey", "objectKeyId", "userId"]}, "MasterKey": {"type": "struct", "modelId": "everest.base.encryption/MasterKeyModel.MasterKey", "getMasterKey": {}, "fieldList": ["public<PERSON>ey", "configId"]}, "Employee": {"type": "struct", "loadEmployeeProfile": "@controller:getEmployeeDataQuery()", "modelId": "everest.hr.base/EmployeeModel.Employee", "fieldList": ["category", "contract", "employeeNumber", "endDate", "entityId", "personId", "id", "locationId", "name", "startDate", "status", "vacationDaysDelta", "bankAccountHolderName", "iban", "onboardingStatus", "email", "accessKeyId", "equipmentNumber", "personalEmail", "bankDetailsId", "workAddress", "personalPhone", "holidayCalendar", "Employee-Entity.currency", "firstName", "middleName", "lastName", "preferredFirstName", "preferredLastName", "displayName", "probationDuration", "allowEmployeeAccess", "probationEndDate", "leavesOverlapStatus", "externalEmployeeId", "migrationConfigurationId", "employmentCategoryId", "workingDays", "weeklyWorkingHours"]}, "RelatedUserKey": {"type": "struct", "modelId": "everest.hr.base/EmployeeModel.Employee", "getUserKeyByEmployeeId": {"employeeId": "@controller:getCurrentEmployeeId()"}}, "RelatedUnsetObjectKeys": {"type": "list", "modelId": "everest.base.encryption/ObjectKeyUserKeyMappingModel.ObjectKeyUserKeyMapping", "query": "@controller:getRelatedObjectKeysQuery()", "fieldList": ["id", "userKeyId", "objectKeyId", "validFrom", "userId", "encryptedObjectKey", "created<PERSON>y"]}, "ObjectKeyMappingToApprove": {"type": "struct", "modelId": "everest.base.encryption/ObjectKeyUserKeyMappingModel.ObjectKeyUserKeyMapping", "fieldList": ["id", "userKeyId", "objectKeyId", "validFrom", "userId", "encryptedObjectKey", "created<PERSON>y"]}, "CurrentObjectKeyMapping": {"type": "struct", "modelId": "everest.base.encryption/ObjectKeyUserKeyMappingModel.ObjectKeyUserKeyMapping", "fieldList": ["id", "userKeyId", "objectKeyId", "validFrom", "userId", "encryptedObjectKey", "created<PERSON>y"]}, "isCurrentEmployee": {"type": "struct", "modelId": "everest.hr.base/EmployeeModel.Employee", "isCurrentEmployee": {"employee": {"id": "@controller:getCurrentEmployeeId()"}}}, "CurrentEmployee": {"type": "struct", "modelId": "everest.hr.base/EmployeeModel.Employee", "getCurrentEmployee": {}, "fieldList": ["id", "email"]}, "isAdmin": {"type": "struct", "modelId": "everest.hr.base/EmployeeModel.Employee", "isAdmin": {}}, "LeaveStatus": {"type": "list", "query": {"where": {"dataType": "LeaveStatus", "language": "@controller:getLanguage()"}}, "modelId": "everest.appserver/ValueHelpModel.ValueHelp", "fieldList": ["text", "codeValue"]}, "HolidayCalendars": {"type": "list", "modelId": "everest.hr.base/HolidayCalendarV2Model.HolidayCalendarV2", "query": {"where": {"status": "Active"}}, "fieldList": ["id", "countryCode", "subdivisionCode", "status", "HolidayCalendar-CountryISOV2.country", "HolidayCalendar-CountryISOSubdivisionV2.subdivision"]}, "Person": {"type": "struct", "modelId": "everest.hr.base/PersonModel.Person", "query": "@controller:getEmployeePersonId()", "fieldList": ["address", "addressId", "dateOfBirth", "email", "endDate", "gender", "id", "name", "personNumber", "phoneNumber", "photo", "photoName", "salutation", "startDate", "status", "lastName", "firstName", "countryCode", "middleName", "preferredFirstName", "preferredLastName", "displayName", "emergencyContactName", "emergencyContactPhoneNumber", "emergencyContactAddress"]}, "Employees": {"type": "list", "modelId": "everest.hr.base/EmployeeModel.Employee"}, "CreateJob": {"type": "list", "modelId": "everest.hr.base/JobModel.Job"}, "DirectReports": {"type": "list", "modelId": "everest.hr.base/JobModel.Job", "getDirectReportsByEmployeeId": "@controller:getDirectReportsQuery()", "pagination": true, "fieldList": ["departmentId", "employeeId", "endDate", "id", "startDate", "status"]}, "EmployeeJob": {"type": "list", "query": "@controller:getEmployeeJobQuery()", "modelId": "everest.hr.base/JobModel.Job", "fieldList": ["departmentId", "employeeId", "endDate", "id", "percentage", "Job-Position.positionNumber", "salary", "active", "jobManager", "startDate", "regionId", "Job-Position.name", "Job-Department.departmentName", "Job-JobManager.Job-Employee.name", "Job-JobManager.Job-Employee.id", "Job-Region.regionName", "isPrimary", "positionId", "departmentId", "status", "reassignmentComplete", "jobManagerDisplayName", "onboardingStatus", "offboardingStatus"]}, "EmployeeUpdate": {"type": "struct", "modelId": "everest.hr.base/EmployeeModel.Employee"}, "Locations": {"type": "list", "query": {}, "modelId": "everest.hr.base/LocationModel.Location", "fieldList": ["id", "locationName", "holidayCalendarId"]}, "Entities": {"type": "list", "modelId": "everest.base/EntityModel.Entity", "query": {}, "fieldList": ["entityName", "id", "currency"]}, "Address": {"type": "struct", "parent": "Person", "joinKey": "addressId-id", "modelId": "everest.base/AddressModel.Address", "fieldList": ["addressNumber", "city", "country", "id", "line1", "line2", "stateProvince", "zipCode"]}, "YearLeaves": {"type": "list", "query": "@controller:getYearLeaves()", "modelId": "everest.hr.base/LeaveModel.Leave", "fieldList": ["absenceType", "days", "employeeId", "endDate", "id", "startDate", "status", "mandatoryLeaveId", "createdDate"]}, "AllLeaves": {"type": "list", "query": {"where": {"employeeId": "@controller:getCurrentEmployeeId()"}, "orderBy": [{"field": "startDate", "ordering": "asc"}]}, "modelId": "everest.hr.base/LeaveModel.Leave", "fieldList": ["absenceType", "days", "employeeId", "endDate", "id", "startDate", "status", "mandatoryLeaveId"]}, "Leave": {"type": "struct", "modelId": "everest.hr.base/LeaveModel.Leave"}, "Regions": {"type": "list", "query": {}, "modelId": "everest.hr.base/RegionModel.Region", "fieldList": ["id", "regionName"]}, "EmployeeAttachments": {"type": "list", "query": {"where": {"employeeId": "@controller:getCurrentEmployeeId()"}}, "modelId": "everest.hr.base/EmployeeAttachmentModel.EmployeeAttachment", "fieldList": ["id", "fileId", "fileName", "employeeId", "expiryDate", "status", "fileType", "employeeDocumentTypeID", "employeeRequestedDocumentId", "EmployeeAttachment-EmployeeDocumentType.requestable", "employeeDocumentTypeLabel"]}, "EmployeeUser": {"type": "struct", "model": "urn:evst:everest:hr/base:model/node:Employee", "getUserDataByEmployeeId": {"employeeId": "@controller:getCurrentEmployeeId()"}, "fieldList": ["id", "userId", "email"]}, "LeaveTypes": {"type": "list", "query": {"where": {"dataType": "LeaveType", "language": "@controller:getLanguage()"}}, "modelId": "everest.appserver/ValueHelpModel.ValueHelp", "fieldList": ["text", "codeValue"]}, "HolidayCalendar": {"type": "struct", "query": {"where": {"id": "@controller:getEmployeeHolidayCalendar()"}}, "modelId": "everest.hr.base/HolidayCalendarV2Model.HolidayCalendarV2", "fieldList": ["countryCode", "subdivisionCode", "id", "HolidayCalendar-CountryISOV2.country", "HolidayCalendar-CountryISOSubdivisionV2.subdivision"]}, "RemainingVacationDays": {"type": "struct", "getRemainingVacationDays": {"employeeId": "@controller:getCurrentEmployeeId()", "date": "@controller:returnDate()"}, "modelId": "everest.hr.base/LeaveModel.Leave"}, "CarryoverDays": {"type": "struct", "getRemainingFixedDays": {"employeeId": "@controller:getCurrentEmployeeId()", "absencePolicyId": null, "date": "@controller:getCarryoverDate()"}, "modelId": "everest.hr.base/LeaveModel.Leave"}, "TakenVacationDays": {"type": "struct", "getTakenVacationDays": {"year": "@state:year", "employeeId": "@controller:getCurrentEmployeeId()"}, "modelId": "everest.hr.base/LeaveModel.Leave"}, "HolidayCalendarDateMapping": {"type": "list", "parent": "HolidayCalendar", "joinKey": "id-holidayCalendarId", "modelId": "everest.hr.base/HolidayCalendarDateMappingV2Model.HolidayCalendarDateMappingV2"}, "HolidayList": {"type": "list", "parent": "HolidayCalendarDateMapping", "joinKey": "dateId-id", "modelId": "everest.base.calendar/HolidayDateModel.HolidayDate"}, "Country": {"type": "struct", "parent": "HolidayCalendar", "joinKey": "countryCode-alpha2Code", "modelId": "everest.base/CountryISOModel.CountryISO"}, "Weekends": {"type": "struct", "modelId": "everest.base.calendar/WeekendModel.Weekend", "parent": "Country", "joinKey": "countryCode-alpha2Code"}, "AbsencePolicyEmployeeSick": {"type": "struct", "query": {"where": {"employeeId": "@controller:getCurrentEmployeeId()", "policyType": "sickLeave", "status": "active"}}, "modelId": "everest.hr.base/AbsencePolicyEmployeeModel.AbsencePolicyEmployee", "fieldList": ["id", "absencePolicyId", "policyType", "AbsencePolicyEmployee-AbsencePolicy.name"]}, "AbsencePolicyEmployeeVacation": {"type": "struct", "query": {"where": {"employeeId": "@controller:getCurrentEmployeeId()", "policyType": "vacation", "status": "active"}}, "modelId": "everest.hr.base/AbsencePolicyEmployeeModel.AbsencePolicyEmployee", "fieldList": ["id", "absencePolicyId", "policyType", "AbsencePolicyEmployee-AbsencePolicy.name"]}, "AbsencePoliciesEmployee": {"type": "list", "query": {"where": {"employeeId": "@controller:getCurrentEmployeeId()"}}, "modelId": "everest.hr.base/AbsencePolicyEmployeeModel.AbsencePolicyEmployee", "model": "urn:evst:everest:hr/base:model/node:AbsencePolicyEmployee", "fieldList": ["id", "absencePolicyId", "status", "policyType", "AbsencePolicyEmployee-AbsencePolicy.name"]}, "VacationPolicy": {"type": "struct", "modelId": "everest.hr.base/AbsencePolicyEmployeeModel.AbsencePolicyEmployee"}, "SickPolicy": {"type": "struct", "modelId": "everest.hr.base/AbsencePolicyEmployeeModel.AbsencePolicyEmployee"}, "AbsenceCalculation": {"type": "struct", "modelId": "everest.hr.base/AbsenceCalculationModel.AbsenceCalculation"}, "AbsenceCalculations": {"type": "list", "modelId": "everest.hr.base/AbsenceCalculationModel.AbsenceCalculation", "query": {"where": {"employeeId": "@controller:getCurrentEmployeeId()"}}, "fieldList": ["adjustmentDays", "year"]}, "EmployeeRequestedDocument": {"type": "struct", "modelId": "everest.hr.base/EmployeeRequestedDocumentModel.EmployeeRequestedDocument"}, "EmployeeRequestedDocuments": {"type": "list", "modelId": "everest.hr.base/EmployeeRequestedDocumentModel.EmployeeRequestedDocument", "readRequestedDocument": {"where": "@controller:getRequestedDocsQuery()", "fields": ["id", "employeeId", "requestedBy", "stage", "tasks", "with<PERSON><PERSON><PERSON>", "employeeAttachmentId", "employeeDocumentTypeName", "employeeDocumentTypeId", "employeeDocumentTypeLabel", "createdDate", "EmployeeRequestedDocument-EmployeeDocumentTypeId.needManagerApproval", "EmployeeRequestedDocument-EmployeeDocumentTypeId.needManagerGrades"]}, "fieldList": ["id", "employeeId", "requestedBy", "stage", "tasks", "with<PERSON><PERSON><PERSON>", "employeeDocumentTypeName", "employeeDocumentTypeId", "employeeDocumentTypeLabel", "createdDate", "EmployeeRequestedDocument-EmployeeDocumentTypeId.needManagerApproval", "EmployeeRequestedDocument-EmployeeDocumentTypeId.needManagerGrades"]}, "UserConfig": {"type": "list", "modelId": "everest.appserver/userConfig/UserConfigModel.UserConfig", "queryUserConfig": {}}, "BankDetails": {"type": "struct", "modelId": "everest.fin.base/BankDetailsModel.BankDetails", "query": {"where": {"id": "@controller:getEmployeeBankDetailsId()"}}, "fieldList": ["id", "bankAccountNumberType", "bankAccountHolderName", "bankAccountNumber", "financialInstitutionIdType", "financialInstitutionId"]}, "TimesheetPolicy": {"type": "struct", "modelId": "everest.hr.base/TimesheetPolicyModel.TimesheetPolicy", "getCurrentAssignedTSPolicy": {"employeeId": "@controller:getCurrentEmployeeId()"}, "fieldList": ["id", "name"]}, "TimesheetPolicyList": {"type": "list", "modelId": "everest.hr.base/TimesheetPolicyModel.TimesheetPolicy", "query": {}, "fieldList": ["id", "name"]}, "Timesheet": {"type": "list", "modelId": "everest.hr.base/TimesheetModel.Timesheet", "query": {"where": {"employeeId": "@controller:getCurrentEmployeeId()", "timesheetStatus": {"$ne": "draft"}, "year": "@controller:getTimesheetYear()"}, "orderBy": ["year", "month"]}, "fieldList": ["id", "employeeId", "month", "year", "timesheetPolicyId", "timesheetPolicyStatus", "timesheetPolicyName", "timesheetStatus", "totalWorkDays", "totalWorkTime"]}, "NotificationCategory": {"type": "list", "modelId": "everest.appserver/NotificationCategoryModel.NotificationCategory"}, "Notification": {"type": "struct", "modelId": "everest.appserver/NotificationModel.Notification"}, "EmployeePermissions": {"type": "list", "loadEmployeePermissions": {"id": "@controller:getCurrentEmployeeId()"}, "modelId": "everest.hr.base/EmployeeModel.Employee", "fieldList": ["id", "permissionNodeId", "created<PERSON>y", "startDate", "endDate", "EmployeeMapping-Permission.name"], "pagination": true}, "Salary": {"type": "list", "getDecryptedSalary": "@controller:buildDecryptedQuery()", "modelId": "everest.hr.base/SalaryModel.Salary", "fieldList": ["id", "baseSalary", "startDate", "endDate", "unit", "keyId"]}, "PayoutPeriod": {"type": "list", "query": {"where": {"dataType": "PayoutPeriod", "language": "en"}}, "modelId": "everest.appserver/ValueHelpModel.ValueHelp", "fieldList": ["text", "codeValue"]}, "SalaryType": {"type": "list", "query": {"where": {"dataType": "SalaryType", "language": "en"}}, "modelId": "everest.appserver/ValueHelpModel.ValueHelp", "fieldList": ["text", "codeValue"]}, "Bonus": {"type": "list", "getDecryptedBonus": "@controller:buildDecryptedQuery()", "modelId": "everest.hr.base/BonusModel.Bonus", "fieldList": ["id", "employeeId", "bonusTypeId", "bonusAmount", "endDate", "paymentPeriod", "startDate", "Bonus-BonusType.id", "Bonus-BonusType.bonusName", "Bonus-BonusType.recurrenceType", "recurrenceType", "objectKeyId", "encryptedData", "bonusAmount"]}, "RecurrenceType": {"type": "list", "query": {"where": {"dataType": "RecurrenceType", "language": "en"}}, "modelId": "everest.appserver/ValueHelpModel.ValueHelp", "fieldList": ["text", "codeValue"]}, "Benefit": {"type": "list", "query": {"where": {"employeeId": "@controller:getCurrentEmployeeId()"}, "orderBy": [{"field": "startDate", "ordering": "asc"}]}, "modelId": "everest.hr.base/BenefitModel.Benefit", "fieldList": ["employeeId", "benefitId", "benefitAmount", "endDate", "paymentPeriod", "startDate", "Benefit-BenefitType.id", "Benefit-BenefitType.benefitName", "Benefit-BenefitType.recurrenceType", "Benefit-BenefitType.defaultAmount", "employeeContribution", "companyContribution"]}, "hasVacationPolicy": {"type": "struct", "modelId": "everest.hr.base/AbsencePolicyModel.AbsencePolicy", "hasVacationPolicy": {"employeeId": "@controller:getCurrentEmployeeId()"}}, "Jobs": {"type": "list", "modelId": "everest.hr.base/JobModel.Job", "fieldList": []}, "BoardingSteps": {"type": "list", "loadOwnOnboardingSteps": "@controller:getBoardingStepsQuery()", "modelId": "everest.hr.base/BoardingModel.Boarding"}, "EveAI": {"type": "struct", "modelId": "everest.base.ai/BaseAIModel.BaseAI"}, "ObjectKeyMappingRequest": {"type": "list", "query": "@controller:getObjectKeyRequest()", "modelId": "everest.base.encryption/ObjectKeyMappingRequestModel.ObjectKeyMappingRequest", "fieldList": ["id", "status", "approverUserId", "objectKeyMappingIds", "created<PERSON>y"]}, "ObjectKeyMappingRequestToApprove": {"type": "list", "query": "@controller:getObjectKeyRequestToApprove()", "modelId": "everest.base.encryption/ObjectKeyMappingRequestModel.ObjectKeyMappingRequest", "fieldList": ["id", "status", "approverUserId", "objectKeyMappingIds", "created<PERSON>y"]}, "Boarding": {"type": "struct", "modelId": "everest.hr.base/BoardingModel.Boarding"}, "EmployeeProfiles": {"type": "list", "modelId": "everest.hr.base/EmployeeModel.Employee", "loadEmployees": "@controller:getEmployeeProfilesQuery()", "fieldList": ["id", "status", "endDate"]}, "employeeBusinessObject": {"type": "struct", "modelId": "everest.hr.base/BOComposition/EmployeeBusinessObjectModel.EmployeeBusinessObject", "query": "@controller:getEmployeeBOQuery()", "fieldList": []}, "EncryptedNodes": {"model": "urn:evst:everest:base/encryption:model/node:EncryptedNode", "type": "list", "query": {}, "fieldList": ["id", "systemKeyId", "modelUrn", "encryptedFields"]}, "CurrentPrimaryJob": {"model": "urn:evst:everest:hr/base:model/node:Job", "type": "struct", "query": "@controller:getCurrentPrimaryJob()", "fieldList": ["departmentId", "employeeId", "endDate", "id", "percentage", "Job-Position.positionNumber", "salary", "active", "jobManager", "startDate", "regionId", "Job-Position.name", "Job-Department.departmentName", "Job-JobManager.Job-Employee.name", "Job-JobManager.Job-Employee.id", "Job-Region.regionName", "isPrimary", "positionId", "departmentId", "status", "reassignmentComplete", "jobManagerDisplayName"]}, "CurrentSecondaryJobs": {"model": "urn:evst:everest:hr/base:model/node:Job", "type": "list", "query": "@controller:getCurrentSecondaryJobs()", "fieldList": ["departmentId", "employeeId", "endDate", "id", "percentage", "Job-Position.positionNumber", "salary", "active", "jobManager", "startDate", "regionId", "Job-Position.name", "Job-Department.departmentName", "Job-JobManager.Job-Employee.name", "Job-JobManager.Job-Employee.id", "Job-Region.regionName", "isPrimary", "positionId", "departmentId", "status", "reassignmentComplete", "jobManagerDisplayName"]}, "AbsencePolicies": {"type": "list", "modelId": "everest.hr.base/AbsencePolicyModel.AbsencePolicy", "query": {}, "fieldList": []}, "hasBoardingPermissions": {"type": "struct", "model": "urn:evst:everest:appserver:model/node:permission/Policy", "checkPermission": {"resource": "everest.hr.base/EmployeeModel.Employee", "action": "boardJob", "checkConditions": false, "payload": null}}, "hasCreateEmployeeLogPermission": {"type": "struct", "model": "urn:evst:everest:appserver:model/node:permission/Policy", "checkPermission": {"resource": "everest.hr.base/EmployeeLogsModel.EmployeeLogs", "action": "createEmployeeLog", "checkConditions": false, "payload": null}}, "hasLoadEmployeeLogsPermission": {"type": "struct", "model": "urn:evst:everest:appserver:model/node:permission/Policy", "checkPermission": {"resource": "everest.hr.base/EmployeeLogsModel.EmployeeLogs", "action": "loadEmployeeLogs", "checkConditions": false, "payload": null}}, "AbsencePolicyCarryover": {"type": "struct", "parent": "VacationPolicy", "joinKey": "absencePolicyId-id", "modelId": "everest.hr.base/AbsencePolicyCarryoverModel.AbsencePolicyCarryover", "fieldList": ["carryoverType"]}, "OffBoardingSteps": {"type": "list", "loadOwnOnboardingSteps": "@controller:getOffBoardingStepsQuery()", "modelId": "everest.hr.base/BoardingModel.Boarding"}, "Users": {"type": "list", "modelId": "everest.appserver.usermgmt/UserModel.User", "fieldList": ["id", "userId"]}, "EmployeeLogs": {"type": "list", "loadEmployeeLogs": "@controller:getEmployeeLogsQuery()", "modelId": "everest.hr.base/EmployeeLogsModel.EmployeeLogs", "fieldList": ["id", "EmployeeLogs-EmployeeLogType.name", "log", "createdDate"]}, "WeekDay": {"type": "list", "query": {"where": {"dataType": "WeekDay", "language": "@controller:getLanguage()"}}, "modelId": "everest.appserver/ValueHelpModel.ValueHelp", "fieldList": ["text", "codeValue"]}}}, "uiview": {"layout": "everest.base.ui/layouts/uilayout/segmented/avatar/avatar", "props": {"autoRefreshData": true, "allowRefreshData": true, "i18n": ["hrbase", "employee", "manager", "requestDocument", "securityManagement", "leaveRequest", "bankDetails", "applicant", "boarding", "everest.base.encryption/encryption"], "title": "@controller:getDisplayName()", "tabTitle": "@controller:getTabTitle()", "showAvatar": true, "avatar": {"isEditable": true, "size": "xlarge", "src": "@controller:getAvatarSrc()", "active": false, "fileId": "@binding:Person.photo", "loading": "@state:photoIsLoading", "onSuccess": "@controller:onPhotoSuccess", "onRemove": "@controller:onPhotoRemove", "name": "@binding:Person.name"}, "history": {"model": "@binding:employeeBusinessObject", "fieldList": "@controller:get<PERSON><PERSON><PERSON><PERSON><PERSON>s()"}, "description": "@controller:getPositionNames()", "status": "@binding:Employee.status", "stretch": true, "headerActions": {"direction": "vertical", "align": "right", "content": [{"label": "@controller:buttonEdit()", "onClick": "@controller:onSaveClick", "visible": "@controller:showEditButton()", "variant": "primary", "disabled": "@controller:isEditDisabled()"}, {"label": "{{cancel}}", "onClick": "@controller:setView", "visible": "@controller:showCancelButton()", "variant": "secondary"}, {"label": "{{more.actions}}", "variant": "secondary", "visible": "@controller:stateView()", "actions": [{"label": "{{employee.activate}}", "onClick": "@controller:activateEmployee", "variant": "secondary", "visible": "@controller:employeeInactive()", "disabled": "@controller:employeeStartDateMissing()", "tooltip": {"text": "{{enter.endDate}}", "placement": "right", "visible": "@controller:employeeStartDateMissing()"}}, {"label": "{{view.in.orgChart}}", "onClick": "@controller:viewInOrgchart", "variant": "secondary"}, {"label": "{{view.employee.profiles}}", "onClick": "@controller:viewEmployeeProfiles", "visible": "@controller:multipleEmployeeProfiles()", "variant": "secondary"}, {"label": "@controller:getCreateProfileLabel()", "onClick": "@controller:createNewProfile", "visible": "@binding:isAdmin", "variant": "secondary"}, {"label": "{{board.employee}}", "onClick": "@controller:onboardEmployee", "visible": "@controller:showBoardButton()", "variant": "secondary"}, {"label": "{{offboard.employee}}", "onClick": "@controller:offboardEmployee", "visible": "@controller:showOffboardButton()", "variant": "secondary"}, {"label": "{{employee.deactivate}}", "onClick": "@controller:openDeactivateEmployeeModal", "variant": "secondary", "visible": "@controller:employeeActive()"}, {"label": "{{employee.change.to.planned}}", "onClick": "@controller:changeEmployeeToPlanned", "variant": "secondary", "visible": "@controller:showEmployeePlannedButton()"}, {"label": "{{emp.user.pin}}", "onClick": "@controller:openUserPinModal", "variant": "secondary"}, {"label": "{{onboarding.history}}", "onClick": "@controller:navigateToOnboardingHistory", "variant": "secondary", "visible": "@controller:isOnboardingHistoryVisible()"}, {"label": "{{offboarding.history}}", "onClick": "@controller:navigateToOffboardingHistory", "variant": "secondary", "visible": "@controller:isOffboardingHistoryVisible()"}]}]}, "stepsContent": [{"title": "{{employment.details}}", "components": [{"component": "FieldGroup", "customId": "employeeEmploymentDetails", "size": "12", "columns": 6, "sectionVariant": "card", "variant": "secondary", "elements": [{"component": "Read<PERSON>nly", "label": "{{id}}", "value": "@binding:Employee.employeeNumber", "isEditing": false}, {"component": "Input", "label": "{{first.name}} *", "value": "@binding:Person.firstName", "isEditing": "@controller:returnEdit()", "rules": "@controller:validateField()"}, {"component": "Input", "label": "{{middle.name}}", "value": "@binding:<PERSON>.middleName", "isEditing": "@controller:returnEdit()"}, {"component": "Input", "label": "{{last.name}} *", "value": "@binding:Person.lastName", "isEditing": "@controller:returnEdit()", "rules": "@controller:validateField()"}, {"component": "DatePicker", "label": "{{startDate}} *", "parseAs": "date", "format": "MM/dd/yyyy", "value": "@binding:Employee.startDate", "isEditing": "@controller:returnEdit()", "onChange": "@controller:updateProbationEndDate"}, {"component": "DatePicker", "label": "{{endDate}}", "parseAs": "date", "format": "MM/dd/yyyy", "value": "@binding:Employee.endDate", "isEditing": "@controller:returnEdit()", "isVisible": "@controller:endDateVisible()", "onChange": "@controller:setEndDate"}, {"label": "{{category}}", "component": "Select", "size": 1, "value": "@binding:Employee.employmentCategoryId", "isEditing": "@controller:returnEdit()", "bottomActions": [{"label": "{{create}}", "onClick": "@controller:onCreateCategoryClick"}]}, {"component": "Select", "label": "{{entity}} *", "absoluteBOName": "everest.hr.base/EmployeeModel", "idProp": "id", "textProp": "entityName", "value": "@binding:Employee.entityId", "isEditing": "@controller:returnEdit()", "rules": "@controller:validateField()"}, {"component": "Select", "label": "{{location}}", "idProp": "id", "textProp": "locationName", "value": "@binding:Employee.locationId", "isEditing": "@controller:returnEdit()", "isVisible": true}, {"component": "Read<PERSON>nly", "label": "{{probationDuration}}", "value": "@controller:getProbationDuration()", "isVisible": "@controller:returnView()"}, {"label": "{{probationDuration}}", "value": "@binding:Employee.probationDuration", "isEditing": "@controller:returnEdit()", "onChange": "@controller:calculateProbationEndDate", "isVisible": "@controller:returnEdit()", "compositeConfig": {"probationValue": {"isDisabled": "@controller:isProbationValueDisabled()", "onBlur": "@controller:handleProbationValueEdit"}, "probationUnit": {"allowClear": false, "onChange": "@controller:setProbationEdit"}}}, {"component": "DatePicker", "label": "{{probationEndDate}}", "format": "MM/dd/yyyy", "value": "@binding:Employee.probationEndDate", "minDate": "@controller:getStartDate()", "isDisabled": "@controller:isProbationEndDateDisabled()", "isEditing": "@controller:returnEdit()"}], "moreDetails": {"label": "{{more.employee.details}}", "expanded": "@controller:moreDetailsExpanded()", "elements": [{"component": "Input", "label": "{{preferred.first.name}}", "value": "@binding:Person.preferredFirstName", "isEditing": "@controller:returnEdit()"}, {"component": "Input", "label": "{{preferred.last.name}}", "value": "@binding:Person.preferredLastName", "isEditing": "@controller:returnEdit()"}, {"component": "Input", "label": "{{accessKeyId}}", "value": "@binding:Employee.accessKeyId", "isEditing": "@controller:returnEdit()"}, {"component": "Input", "label": "{{equipmentNumber}}", "value": "@binding:Employee.equipmentNumber", "isEditing": "@controller:returnEdit()"}, {"label": "{{externalEmployeeId}}", "size": "1", "action": "create", "value": "@binding:Employee.externalEmployeeId", "isEditing": "@controller:returnEdit()"}, {"component": "Input", "label": "{{devices}}", "value": "{{employee.devices}}", "isEditing": false, "link": {"to": "@controller:getDeviceLink()"}}]}}, {"component": "<PERSON><PERSON>", "size": 12, "variant": "warning", "title": "Warning", "content": "{{getJobStartDateContent}}", "isVisible": "@controller:showJobStartDateAlert()"}, {"component": "UserInfo", "section": {"title": "{{jobs.and.reporting}}", "selectedGroup": 0, "grid": {"size": 12}, "actions": [{"label": "{{actions}}", "variant": "secondary", "visible": "@binding:isAdmin", "disabled": "@controller:returnEdit", "actions": [{"label": "{{add.job}}", "onClick": "@controller:addJob"}, {"label": "{{view.job.history}}", "variant": "secondary", "visible": "@binding:isAdmin", "onClick": "@controller:onEditJobsClick"}]}]}, "props": {"emptyMessage": "{{no.active.primary.job}}"}, "visible": "@controller:showNoActivePrimaryJob()"}, {"component": "WidgetGroup", "customId": "employeeJobs", "visible": "@controller:showActivePrimaryJob()", "section": {"title": "{{jobs.and.reporting}}", "selectedGroup": 0, "grid": {"size": 12}, "actions": [{"label": "{{actions}}", "variant": "secondary", "visible": "@binding:isAdmin", "disabled": "@controller:returnEdit", "actions": [{"label": "{{add.job}}", "onClick": "@controller:addJob"}, {"label": "{{view.job.history}}", "variant": "secondary", "visible": "@binding:isAdmin", "onClick": "@controller:onEditJobsClick"}]}]}, "props": {"widgets": [{"component": "FieldGroup", "customId": "jobsAndReportingFieldGroup", "section": {"grid": {"size": "12"}, "variant": "card"}, "label": "{{primary}}", "props": {"visible": true, "columns": 4, "elements": [{"component": "Select", "label": "{{department}}", "value": "@binding:CurrentPrimaryJob.departmentId", "isEditing": "@controller:returnEdit()"}, {"component": "Select", "label": "{{position}}", "value": "@binding:CurrentPrimaryJob.positionId", "isEditing": "@controller:returnEdit()"}, {"component": "Select", "label": "{{manager}}", "value": "@binding:CurrentPrimaryJob.jobManager", "isEditing": "@controller:returnEdit()"}, {"component": "Select", "label": "{{type}}", "isEditing": "@controller:returnEdit()", "idProp": "value", "textProp": "text", "list": [{"value": true, "text": "{{primary}}"}, {"value": false, "text": "{{secondary}}"}], "value": "@binding:CurrentPrimaryJob.isPrimary"}, {"component": "InputNumber", "label": "{{allocation}}", "suffixProp": "%", "min": 1, "max": 100, "value": "@binding:CurrentPrimaryJob.percentage", "isEditing": "@controller:returnEdit()"}, {"component": "Select", "label": "{{region}}", "value": "@binding:CurrentPrimaryJob.regionId", "isEditing": "@controller:returnEdit()"}, {"component": "DatePicker", "label": "{{startDate}}", "parseAs": "date", "format": "MM/dd/yyyy", "value": "@binding:CurrentPrimaryJob.startDate", "isEditing": "@controller:returnEdit()"}, {"component": "DatePicker", "label": "{{endDate}}", "parseAs": "date", "format": "MM/dd/yyyy", "value": "@binding:CurrentPrimaryJob.endDate", "isEditing": "@controller:returnEdit()"}], "moreDetails": {"label": "{{employee.workingSchedule}}", "expanded": "@controller:moreDetailsExpanded()", "elements": [{"label": "{{employee.workingDays}}", "value": "@binding:Employee.workingDays", "textProp": "text", "valueProp": "codeValue", "list": "@binding:WeekDay", "isEditing": "@controller:returnEdit()"}, {"label": "{{employee.weekly.workingHours}}", "value": "@binding:Employee.weeklyWorkingHours", "isEditing": "@controller:returnEdit()"}]}}}, {"component": "Table", "size": 12, "label": "{{secondary}}", "visible": "@controller:secondaryJobsVisible()", "props": {"data": "@binding:CurrentSecondaryJobs", "rowActions": {"variant": "default", "columnLabel": "{{actions}}", "columnPosition": 0, "pinned": "left", "actions": [{"content": "{{edit.job}}", "onClick": "@controller:edit<PERSON><PERSON>"}, {"content": "{{delete.job}}", "onClick": "@controller:delete<PERSON><PERSON>"}]}, "columns": [{"headerName": "{{type}}", "field": "isPrimary", "cellVariant": {"variant": "badge", "matchers": "@controller:jobTypeMatcher()"}, "valueGetter": "@controller:getJobTypeText", "pinned": "left", "maxWidth": 100}, {"headerName": "{{status}}", "field": "status", "cellVariant": {"variant": "badge", "matchers": "@controller:getTimelineStatus('status')"}, "pinned": "left", "maxWidth": 100}, {"field": "positionId", "headerName": "{{position}}", "minWidth": 300, "fieldProps": {"fontWeight": "bold"}, "pinned": "left"}, {"field": "departmentId", "headerName": "{{department}}", "minWidth": 150}, {"field": "jobManagerDisplayName", "headerName": "{{manager}}", "minWidth": 150, "maxWidth": 300}, {"field": "percentage", "headerName": "{{working.percentage}}", "fieldProps": {"component": "Input"}, "valueGetter": "@controller:get<PERSON><PERSON><PERSON>"}, {"field": "regionId", "headerName": "{{region}}", "minWidth": 100, "maxWidth": 175}, {"field": "startDate", "headerName": "{{startDate}}", "maxWidth": 125}, {"field": "endDate", "headerName": "{{endDate}}", "maxWidth": 125}]}}]}}, {"component": "<PERSON><PERSON>", "size": 12, "variant": "warning", "title": "Warning", "content": "@controller:getJobAlertContent()", "isVisible": "@controller:show<PERSON>ob<PERSON><PERSON><PERSON>()"}, {"component": "<PERSON><PERSON>", "size": 12, "variant": "warning", "title": "Warning", "content": "@controller:getDeactivatedJobAlert()", "isVisible": "@controller:showDeactivatedJobAlert()", "action": {"label": "{{navigate.to.reassign}}", "onClick": "@controller:navigateToReassign"}}, {"component": "Table", "customId": "directReportTable", "size": "12", "data": "@binding:DirectReports", "title": "{{employee.directReports}}", "visible": "@controller:hasDirectReports()", "editing": false, "sectionActions": [{"label": "{{manage.direct.reports}}", "variant": "secondary", "visible": "@binding:isAdmin", "onClick": "@controller:navigateToReassign"}], "columns": [{"headerName": "{{employee}}", "field": "employeeDisplayName", "fieldProps": {"fontWeight": "bold"}}, {"headerName": "{{type}}", "field": "isPrimary", "cellVariant": {"variant": "badge", "matchers": "@controller:jobTypeMatcher()"}, "valueGetter": "@controller:getJobTypeText"}, {"headerName": "{{status}}", "field": "status", "cellVariant": {"variant": "badge", "matchers": "@controller:getTimelineStatus('status')"}}, {"headerName": "{{department}}", "field": "departmentName"}, {"headerName": "{{position}}", "field": "positionName"}, {"headerName": "{{category}}", "field": "Job-Employee.employeeCategory"}, {"headerName": "{{startDate}}", "field": "startDate"}, {"headerName": "{{endDate}}", "field": "endDate"}], "rowNavigation": {"url": "/templates/everest.hr.base/uinext/employee/employee", "idProp": "employeeId", "params": {"mode": "view"}}}, {"component": "<PERSON><PERSON>", "size": 12, "variant": "warning", "title": "Warning", "content": "{{no.user.with.email}}", "visible": "@controller:showEmail<PERSON><PERSON><PERSON>()", "action": {"label": "{{invite.user}}", "onClick": "@controller:inviteUser"}}, {"component": "<PERSON><PERSON>", "size": 12, "variant": "warning", "title": "Warning", "content": "{{no.employee.email}}", "visible": "@controller:showEmailMissingAlert()"}, {"component": "Block", "title": "{{contact.information}}", "customId": "employeeContactDetails", "size": "12", "sectionVariant": "card", "variant": "secondary", "elements": [{"component": "Input", "label": "{{work.email}}", "value": "@binding:Person.email", "isEditing": "@controller:returnEdit()", "tooltip": {"text": "{{employee.email.tooltip}}", "placement": "top"}}, {"component": "Input", "label": "{{work.phone}}", "value": "@binding:Person.phoneNumber", "isEditing": "@controller:returnEdit()"}, {"component": "Input", "label": "{{work.address}}", "value": "@binding:Employee.workAddress", "isEditing": "@controller:returnEdit()"}]}]}, {"title": "{{personal.details}}", "components": [{"component": "Block", "customId": "employeePersonalDetails", "size": "12", "columns": 3, "sectionVariant": "card", "variant": "secondary", "elements": [{"component": "Input", "label": "{{employee.name}}", "value": "@binding:Person.name", "isEditing": false}, {"component": "Select", "label": "{{gender}}", "idProp": "value", "textProp": "text", "isEditing": "@controller:returnEdit()", "list": [{"text": "{{male}}", "value": "Male"}, {"text": "{{female}}", "value": "Female"}, {"text": "{{non.binary}}", "value": "Non Binary"}, {"text": "{{not.specified}}", "value": "Not Specified"}], "action": "update", "value": "@binding:Person.gender"}, {"component": "DatePicker", "label": "{{dateOfBirth}}", "format": "MM/dd/yyyy", "parseAs": "date", "value": "@binding:Person.dateOfBirth", "isEditing": "@controller:returnEdit()", "rules": "@controller:dateOfBirthValidate()", "allowClear": true}, {"component": "Input", "label": "{{email}}", "value": "@binding:Employee.personalEmail", "isEditing": "@controller:returnOwnEdit()"}, {"component": "Input", "label": "{{employee.phone}}", "value": "@binding:Employee.personalPhone", "isEditing": "@controller:returnOwnEdit()", "rules": "@controller:checkPhone()"}, {"component": "Input", "label": "{{address}}", "multiline": true, "value": "@controller:get<PERSON><PERSON><PERSON>()", "isVisible": "@controller:return<PERSON><PERSON>w<PERSON><PERSON><PERSON>()", "isEditing": false}, {"component": "Input", "label": "{{address}}", "multiline": false, "name": "home<PERSON>dd<PERSON>", "hybridConfig": {"icon": "location_on", "iconPosition": "left", "onClick": "@controller:onAddressIconClick", "enableInputClick": true}, "value": "@controller:get<PERSON><PERSON><PERSON>()", "isVisible": "@controller:returnOwnEdit()", "isDisabled": false, "isEditing": true}], "moreDetails": {"label": "{{more.employee.details}}", "expanded": "@controller:moreDetailsExpanded()", "elements": [{"component": "Text", "value": "{{emergency.contact.details}}", "variant": "h1", "size": "3"}, {"component": "Input", "label": "{{name}}", "value": "@binding:Person.emergencyContactName", "isEditing": "@controller:returnEdit()"}, {"component": "Input", "label": "{{phone.number}}", "value": "@binding:Person.emergencyContactPhoneNumber", "isEditing": "@controller:returnEdit()"}, {"component": "Input", "label": "{{address}}", "value": "@binding:<PERSON>.emergencyContactAddress", "isEditing": "@controller:returnEdit()"}]}}, {"component": "Block", "size": "12", "title": "{{expense.payment.details}}", "customId": "employeeExpensePayment", "columns": 5, "sectionVariant": "card", "variant": "secondary", "elements": [{"label": "{{reimbursement.currency}}", "value": "@controller:getReimbursementCurrency()"}, {"label": "{{payment.method}}", "value": "@binding:BankDetails.bankAccountNumberType", "isEditing": "@controller:returnOwnEdit()"}, {"label": "{{bankAccountHolder}}", "value": "@bindingController(BankDetails.bankAccountHolderName, getAccountHolderName())", "isEditing": "@controller:returnOwnEdit()", "visible": "@controller:stateModeEdit()"}, {"label": "@controller:getAccountDetailsLabel()", "value": "@binding:BankDetails.bankAccountNumber", "isEditing": "@controller:returnOwnEdit()", "visible": "@controller:stateModeEdit()", "rules": "@controller:validateBankField()"}, {"label": "{{bankDetails.bankCodeType}}", "value": "@binding:BankDetails.financialInstitutionIdType", "isEditing": "@controller:returnOwnEdit()", "rules": "@controller:validateBankField()"}, {"label": "{{bankDetails.routingNumber}}", "value": "@binding:BankDetails.financialInstitutionId", "visible": "@controller:isPaymentMethod('achOrWire')", "isEditing": "@controller:returnOwnEdit()", "rules": "@controller:validateBankField()"}, {"label": "{{bankDetails.bankCode}}", "value": "@binding:BankDetails.financialInstitutionId", "visible": "@controller:isPaymentMethod('iban')", "isEditing": "@controller:returnOwnEdit()", "rules": "@controller:validateBankField()"}, {"label": "{{bankDetails.bankCode}}", "value": "@binding:BankDetails.financialInstitutionId", "visible": "@controller:isPaymentMethod('other')", "isEditing": "@controller:returnOwnEdit()", "rules": "@controller:validateBankField()"}, {"component": "Input", "label": "@controller:getPaymentDetailsLabel()", "value": "@controller:getPaymentDetailsValue()", "multiline": true, "visible": "@controller:stateView()", "rules": "@controller:validateBankField()"}]}, {"component": "Table", "customId": "documentsTable", "title": "{{documents}}", "size": "12", "rowActions": {"variant": "button", "columnLabel": "{{actions}}", "columnPosition": -1, "actions": [{"content": "{{approve}}", "onClick": "@controller:approveDocument", "disabled": "@controller:isReviewAttachmentDisabled"}, {"content": "{{reject}}", "onClick": "@controller:rejectDocument", "disabled": "@controller:isReviewAttachmentDisabled"}, {"content": "{{undo}}", "onClick": "@controller:unapproveDocument", "disabled": "@controller:isUndoAttachmentDisabled"}, {"content": "{{view}}", "onClick": "@controller:viewDocument"}, {"content": "{{delete}}", "disabled": "@controller:isDeleteAttachmentDisabled", "onClick": "@controller:deleteAttachment"}]}, "sectionActions": [{"label": "{{add.document}}", "onClick": "@controller:uploadDocument", "visible": "@controller:canAddDocument()", "variant": "secondary", "actions": [{"label": "{{generate.documents}}", "onClick": "@controller:generateDocument", "visible": "@binding:isAdmin", "variant": "secondary"}]}, {"label": "{{manage.required.documents}}", "onClick": "@controller:manageDocuments", "visible": "@binding:isAdmin()", "variant": "secondary"}], "columns": [{"headerName": "{{type}}", "field": "employeeDocumentTypeLabel", "isSortable": true}, {"headerName": "{{file.name}}", "field": "fileName", "isSortable": true, "onCellClicked": "@controller:downloadDocument"}, {"headerName": "{{expiryDate}}", "field": "expiryDate", "isSortable": true, "parseAs": "date"}, {"headerName": "{{status}}", "field": "status", "isSortable": true, "cellVariant": {"variant": "badge", "matchers": "@controller:getMatchers()"}}], "data": "@binding:EmployeeAttachments"}, {"component": "Table", "customId": "requestedDocumentsTable", "title": "{{requestedCertificates.title}}", "size": "12", "data": "@binding:EmployeeRequestedDocuments", "rowActions": {"variant": "button", "columnLabel": "{{actions}}", "columnPosition": -1, "actions": [{"content": "{{manager.approve}}", "onClick": "@controller:giveDocManagerApproval", "hidden": "@controller:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"content": "{{upload.requestedDoc}}", "onClick": "@controller:uploadRequestedDoc", "hidden": "@controller:disableRequestedDocUpload"}, {"content": "{{preview.unsigned}}", "onClick": "@controller:previewRequestedDocument", "hidden": "@controller:requestedDocDownloadDisabled"}, {"content": "{{edit.tasks}}", "onClick": "@controller:editRequestedDoc", "hidden": "@controller:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"content": "{{reject}}", "onClick": "@controller:rejectRequestedDoc", "hidden": "@controller:disabledReject"}, {"content": "{{delete}}", "onClick": "@controller:deleteRequestedDoc", "hidden": "@controller:isDeleteRequestedDocDisabled"}]}, "sectionActions": [{"label": "{{request.documents}}", "onClick": "@controller:requestDocument", "variant": "secondary"}], "columns": [{"headerName": "{{type}}", "field": "employeeDocumentTypeLabel", "isSortable": true}, {"headerName": "{{requested.date}}", "field": "createdDate", "isSortable": true, "parseAs": "date"}, {"headerName": "{{requestDocument.tasks}}", "field": "tasks", "isSortable": false}, {"headerName": "{{requestDocument.includeSalary}}", "field": "with<PERSON><PERSON><PERSON>", "isSortable": true, "valueGetter": "@controller:getIncludeSalaryValue"}, {"headerName": "{{requestedBy}}", "field": "requestedBy", "isSortable": true}, {"headerName": "{{status}}", "field": "stage", "isSortable": true, "cellVariant": {"variant": "badge", "matchers": "@controller:getMatchers()"}}]}]}, {"title": "{{time.tracking}}", "visible": true, "components": [{"component": "Block", "customId": "timesheetsFieldGroup", "sectionVariant": "card", "variant": "secondary", "size": "12", "columns": 4, "elements": [{"label": "{{timetracking.policy}}", "component": "Select", "placeholder": "{{timetracking.policy}}", "idProp": "id", "textProp": "name", "value": "@binding:TimesheetPolicy.id", "list": "@binding:TimesheetPolicyList", "isEditing": "@controller:returnEdit()", "size": 1}]}, {"component": "HybridInput", "variant": "secondary", "iconPosition": "right", "icon": "calendar", "action": "update", "size": "1", "field": {"component": "Select", "label": "{{filter.by.year}}", "allowClear": false, "idProp": "year", "textProp": "year", "isEditing": true, "value": "@state:timesheetYear", "list": "@controller:listTimesheetYears", "onChange": "@controller:setTimesheetYear"}}, {"component": "Table", "size": "12", "variant": "white", "fullWidth": false, "title": "{{timetracking.history}}", "data": "@binding:Timesheet", "sectionActions": [{"label": "{{timetracking.track}}", "onClick": "@controller:onTrackTimeClick", "variant": "secondary"}], "rowNavigation": "/templates/everest.hr.base/uinext/timesheet/time-tracking", "columns": [{"headerName": "{{period}}", "field": "period", "valueGetter": "@controller:getPeriodText", "fieldProps": {"fontWeight": "bold"}, "suppressSort": true}, "timesheetPolicyName", {"headerName": "{{status}}", "field": "timesheetStatus", "cellVariant": {"variant": "badge", "matchers": "@controller:getRecordStatusBadgeBgColor()"}}, {"headerName": "{{policyStatus}}", "field": "timesheetPolicyStatus", "cellVariant": {"variant": "badge", "matchers": "@controller:getPolicyStatusBadgeBgColor()"}}, {"headerName": "{{totalWorkDays}}", "field": "totalWorkDays"}, {"headerName": "{{totalWorkTime}}", "field": "totalWorkTime", "valueGetter": "@controller:getTotalWorkTimeText"}]}]}, {"title": "{{absences}}", "components": [{"component": "Block", "title": "{{assigned.policies}}", "customId": "employeeAssignedPolicy", "sectionVariant": "card", "variant": "secondary", "sectionActions": [{"label": "{{manage.employee.policies}}", "onClick": "@controller:openEditPolicyModal", "variant": "secondary", "visible": "@binding:isAdmin"}], "size": 12, "columns": 12, "elements": [{"component": "Read<PERSON>nly", "editing": false, "label": "{{vacation.policy}}", "size": 3, "value": "@controller:getVacationPolicyName()"}, {"component": "Read<PERSON>nly", "size": 3, "label": "{{sick.policy}}", "value": "@controller:getSickPolicyName()"}, {"label": "{{holiday.calendar}}", "size": 3, "component": "Select", "isEditing": "@controller:returnEdit()", "action": "update", "idProp": "id", "textProp": "name", "list": "@controller:getHolidayCalendars()", "value": "@binding:Employee.holidayCalendar"}, {"label": "@controller:vacationAdjustmentLabel()", "size": 3, "component": "Input", "isEditing": "@controller:returnEdit()", "isDisabled": "@controller:disableAdjustmentField()", "action": "update", "onChange": "@controller:setVacationAdjustment", "value": "@controller:getVacationAdjustment()"}]}, {"component": "HybridInput", "variant": "secondary", "iconPosition": "right", "icon": "calendar", "action": "update", "size": "1", "field": {"component": "Select", "label": "{{filter.by.year}}", "allowClear": false, "idProp": "year", "textProp": "year", "isEditing": true, "value": "@state:year", "list": "@controller:getYears", "onChange": "@controller:setYear"}}, {"component": "Statistic", "section": {"title": "@controller:getAbsenceSummaryLabel()", "variant": "card", "grid": {"size": "12"}}, "props": {"hideDivider": true, "columns": "@controller:getColumns()", "fields": [{"label": "@controller:remainingDaysLabel()", "value": "@controller:getRemainingVacationDaysEmployee()"}, {"label": "@controller:carryoverDaysLabel()", "value": "@controller:getCarryoverVacationDaysEmployee()", "visible": "@controller:showCarryover()"}, {"label": "{{requested.vacation}}", "value": "@controller:getTakenVacationDays('requested')"}, {"label": "{{planned.approved.vacation}}", "value": "@controller:getTakenVacationDays('planned')"}, {"label": "{{taken.vacation}}", "value": "@controller:getTakenVacationDays('taken')"}]}}, {"component": "<PERSON><PERSON>", "title": "{{incorrect.remaining.days.title}}", "variant": "warning", "content": "{{incorrect.remaining.days.message}}", "size": 12, "visible": "@controller:hasOverlappingLeaves()"}, {"component": "Table", "title": "{{absence.history}}", "customId": "absenceHistoryTable", "size": "12", "data": "@binding:YearLeaves", "variant": "white-borderless", "rowSelection": true, "onRowSelectionChanged": "@controller:enableButton", "onRowClicked": "@controller:openLeaveRequest", "sectionActions": [{"variant": "secondary", "label": "{{request.absence}}", "onClick": "@controller:leaveRequest", "visible": "@controller:requestButtonVisible()", "actions": [{"label": "{{recalculate.absences}}", "onClick": "@controller:recalculateAbsences", "visible": "@binding:isAdmin"}]}, {"label": "{{sync.company.holidays}}", "onClick": "@controller:syncCompanyHolidays", "variant": "tertiary", "visible": "@controller:syncButtonVisible()"}, {"label": "{{actions}}", "visible": "@controller:actionsVisible()", "variant": "primary", "actions": [{"label": "{{delete.cancel}}", "onClick": "@controller:deleteLeaves", "disabled": "@controller:deleteDisabled()", "tooltip": {"text": "{{pending.deleted.approved.canceled}}", "placement": "right"}, "visible": "@controller:deleteVisible()", "confirmation": {"message": "@controller:deleteLabel()", "description": "{{manager.informed}}", "confirmLabel": "{{confirm}}", "cancelLabel": "{{go.back}}"}}, {"label": "{{approve}}", "onClick": "@controller:approve<PERSON><PERSON>ves", "visible": "@controller:is<PERSON><PERSON><PERSON>()"}, {"label": "{{reject}}", "onClick": "@controller:reject<PERSON><PERSON>ves", "visible": "@controller:is<PERSON><PERSON><PERSON>()"}]}], "empty": {"variant": "primary-table"}, "columns": [{"field": "absenceType", "fieldProps": {"fontWeight": "bold"}, "headerName": "{{type}}", "cellVariant": {"variant": "icon", "iconSize": "medium", "iconColor": "morning-blue", "matchers": "@controller:getAbsenceMatchers", "tooltip": "Company Vacation"}}, {"field": "createdDate", "initialHide": true, "headerName": "{{createdDate}}"}, {"field": "startDate", "headerName": "{{startDate}}"}, {"field": "endDate", "headerName": "{{endDate}}"}, {"field": "days", "headerName": "{{work.days.requested}}"}, {"field": "status", "headerName": "{{status}}", "cellVariant": {"variant": "badge", "matchers": "@controller:getMatchers()"}}]}]}, {"title": "{{compensation}}", "components": [{"component": "Table", "title": "{{base.salary}}", "customId": "employeeBaseSalaryTable", "size": "12", "name": "salary", "sectionActions": [{"label": "{{create}}", "onClick": "@controller:SalaryController.createSalary", "variant": "secondary", "visible": "@binding:isAdmin", "disabled": "@controller:SalaryController.disableCreateSalary()", "tooltip": {"text": "{{enc.user.key.notfound}}", "visible": "@controller:SalaryController.disableCreateSalary()"}}], "rowActions": {"visible": "@binding:isAdmin", "actions": [{"content": "{{edit}}", "onClick": "@controller:SalaryController.onEditSalaryClick"}, {"content": "{{delete}}", "onClick": "@controller:SalaryController.onDeleteSalaryClick"}]}, "columns": [{"headerName": "{{salary.amount}}", "field": "baseSalary"}, {"headerName": "{{currency}}", "field": "currency", "valueGetter": "@controller:getReimbursementCurrency", "suppressSort": true}, {"headerName": "{{unit}}", "field": "unit"}, {"headerName": "{{startDate}}", "field": "startDate", "parseAs": "date", "isSortable": true}, {"headerName": "{{endDate}}", "field": "endDate", "isSortable": true, "parseAs": "date"}, {"field": "status", "headerName": "{{status}}", "valueGetter": "@controller:getSalaryStatus", "cellVariant": {"variant": "badge", "matchers": "@controller:getSalaryMatchers()"}}], "data": "@binding:<PERSON><PERSON>"}, {"component": "Table", "title": "{{bonus}}", "customId": "employeeBonusTable", "size": "12", "rowActions": {"visible": "@binding:isAdmin", "actions": [{"content": "{{edit}}", "onClick": "@controller:onEditBonusClick"}, {"content": "{{delete}}", "onClick": "@controller:onDeleteBonusClick"}]}, "sectionActions": [{"label": "{{create}}", "onClick": "@controller:BonusController.onCreateBonusClick", "variant": "secondary", "visible": "@binding:isAdmin", "disabled": "@controller:BonusController.disableCreateBonus()", "tooltip": {"text": "{{enc.user.key.notfound}}", "visible": "@controller:BonusController.disableCreateBonus()"}}], "columns": [{"headerName": "{{bonus.amount}}", "field": "bonusAmount"}, {"headerName": "{{currency}}", "field": "currency", "valueGetter": "@controller:getReimbursementCurrency", "suppressSort": true}, {"headerName": "{{bonus}}", "field": "Bonus-BonusType.bonusName", "isSortable": true}, {"headerName": "{{payment.period}}", "field": "recurrenceType"}, {"headerName": "{{payment.date}}", "field": "paymentPeriod"}, {"headerName": "{{startDate}}", "field": "startDate", "parseAs": "date", "isSortable": true}, {"headerName": "{{endDate}}", "field": "endDate", "isSortable": true, "parseAs": "date"}, {"field": "status", "headerName": "{{status}}", "valueGetter": "@controller:getSalaryStatus", "cellVariant": {"variant": "badge", "matchers": "@controller:getSalaryMatchers()"}}], "data": "@binding:Bonus"}]}, {"title": "{{benefits}}", "components": [{"component": "Table", "title": "{{benefits}}", "size": "12", "rowActions": {"variant": "button", "columnLabel": "{{actions}}", "columnPosition": -1}, "sectionActions": [{"label": "{{manage.benefits}}", "onClick": "@controller:manageBenefit", "variant": "secondary", "visible": "@binding:isAdmin"}], "columns": [{"headerName": "{{benefit}}", "field": "Benefit-BenefitType.benefitName"}, {"headerName": "{{benefitType}}", "field": "Benefit-BenefitType.recurrenceType", "valueGetter": "@controller:getBenefitType"}, {"headerName": "{{company.contribution}}", "field": "companyContribution"}, {"headerName": "{{employee.contribution}}", "field": "employeeContribution"}, {"headerName": "{{benefit.amount}}", "field": "benefitAmount"}, {"headerName": "{{payment.date}}", "field": "paymentPeriod", "fieldProps": {"valueGetter": "@controller:getPaymentMonth"}}, {"headerName": "{{startDate}}", "field": "startDate", "parseAs": "date", "isSortable": true}, {"headerName": "{{endDate}}", "field": "endDate", "isSortable": true, "parseAs": "date"}], "data": "@binding:Benefit"}]}, {"title": "{{permissions}}", "components": [{"component": "Block", "title": "", "sectionVariant": "card", "variant": "secondary", "size": 12, "columns": 12, "isEditing": true, "elements": [{"component": "Switch", "label": "{{allow.everest.access}}", "size": "12", "direction": "horizontal-reverse", "value": "@binding:Employee.allowEmployeeAccess", "onChange": "@controller:setEmployeeAccess", "isDisabled": "@controller:isEmployeeAccessEditDisabled()"}]}, {"component": "Table", "title": "{{assigned.permissions}}", "customId": "AssignedPermissionsTable", "size": "12", "data": "@binding:EmployeePermissions", "variant": "white-borderless", "sectionActions": [{"label": "{{manage.employee.access}}", "onClick": "@controller:manageUserAccess", "disabled": "@controller:manageUserAccessDisabled()", "visible": "@binding:isAdmin", "variant": "secondary"}], "empty": {"variant": "primary-table"}, "columns": [{"field": "permissionName", "headerName": "{{permission.permission}}"}, {"field": "permissionType", "headerName": "{{permission.type}}"}, {"field": "assignment<PERSON><PERSON><PERSON>", "headerName": "{{assignment.method}}", "autoSizeMinWidth": "md", "autoSizeMaxWidth": "lg", "valueGetter": "@controller:getAssignmentMethod"}, {"field": "lastModifiedUser", "headerName": "{{assigned.by}}"}, {"field": "status", "headerName": "{{status}}", "valueGetter": "@controller:getStatus"}, {"field": "startDate", "headerName": "{{startDate}}", "valueGetter": "@controller:getStartDate"}, {"field": "endDate", "headerName": "{{endDate}}", "valueGetter": "@controller:getEndDate"}]}]}, {"title": "{{onboarding}}", "visible": "@controller:checkBoardingVisible()", "components": [{"component": "Table", "customId": "boardingTable", "title": "@controller:getBoardingTabName()", "size": 12, "suppressDelete": true, "sectionActions": [{"label": "{{add.step}}", "variant": "secondary", "visible": "@binding:isAdmin", "onClick": "@controller:addBoardingStep"}, {"label": "{{more.actions}}", "visible": "@binding:hasBoardingPermissions", "variant": "secondary", "actions": [{"label": "{{complete.boarding}}", "onClick": "@controller:completeBoarding"}, {"label": "{{cancel.onboarding}}", "onClick": "@controller:cancelBoarding"}]}], "rowActions": {"pinned": "right", "visible": "@binding:hasBoardingPermissions", "actions": [{"content": "{{mark.as.completed}}", "onClick": "@controller:markComplete"}, {"content": "{{markAsInComplete}}", "onClick": "@controller:markInComplete"}, {"content": "{{edit.step}}", "visible": "@binding:isAdmin", "onClick": "@controller:editBoardingStep"}, {"content": "{{delete.step}}", "visible": "@binding:isAdmin", "onClick": "@controller:deleteBoardingStep"}, {"content": "{{run.action}}", "visible": "@binding:isAdmin", "onClick": "@controller:runAction"}]}, "columns": [{"field": "Boarding-BoardingStages.stage", "headerName": "{{name}}"}, {"field": "Boarding-BoardingStages.responsible", "headerName": "{{responsible}}"}, {"field": "deadline", "headerName": "{{deadline}}"}, {"field": "completed", "headerName": "{{completed}}", "fieldProps": {"component": "Checkbox", "isEditing": true, "onChange": "@controller:completedCheckboxChange"}}], "data": "@binding:BoardingSteps"}]}, {"title": "{{offboarding}}", "visible": "@controller:checkOffboardingVisible()", "components": [{"component": "Table", "customId": "boardingTable", "title": "@controller:getBoardingTabName()", "size": 12, "suppressDelete": true, "sectionActions": [{"label": "{{add.step}}", "variant": "secondary", "visible": "@binding:hasBoardingPermissions", "onClick": "@controller:addBoardingStep"}, {"label": "{{more.actions}}", "variant": "secondary", "visible": "@binding:hasBoardingPermissions", "actions": [{"label": "{{complete.offboarding}}", "onClick": "@controller:completeBoarding"}, {"label": "{{cancel.offonboarding}}", "onClick": "@controller:cancelBoarding"}, {"label": "{{add.exitInterview}}", "onClick": "@controller:addExitInterview"}]}], "rowActions": {"pinned": "right", "visible": "@binding:hasBoardingPermissions", "actions": [{"content": "{{add.exitInterview}}", "onClick": "@controller:addExitInterview", "disabled": "@controller:isExitInterviewDisabled"}, {"content": "{{mark.as.completed}}", "onClick": "@controller:markComplete"}, {"content": "{{markAsInComplete}}", "onClick": "@controller:markInComplete"}, {"content": "{{edit.step}}", "onClick": "@controller:editBoardingStep"}, {"content": "{{delete.step}}", "onClick": "@controller:deleteBoardingStep"}, {"content": "{{run.action}}", "visible": "@binding:isAdmin", "onClick": "@controller:runAction"}]}, "columns": [{"field": "Boarding-BoardingStages.stage", "headerName": "{{name}}"}, {"field": "Boarding-BoardingStages.responsible", "headerName": "{{responsible}}"}, {"field": "deadline", "headerName": "{{deadline}}"}, {"field": "completed", "headerName": "{{completed}}", "fieldProps": {"component": "Checkbox", "isEditing": true, "onChange": "@controller:completedCheckboxChange"}}], "data": "@binding:OffBoardingSteps"}]}, {"title": "{{employee.attendance.request}}", "visible": false, "isDisabled": true, "components": []}, {"title": "{{logs}}", "visible": "@binding:hasLoadEmployeeLogsPermission", "components": [{"component": "Table", "title": "{{employee.logs}}", "size": 12, "rowActions": {"actions": [{"content": "{{view.log}}", "onClick": "@controller:showEmployeeLog"}, {"content": "{{delete.log}}", "onClick": "@controller:deleteEmployeeLog"}]}, "sectionActions": [{"label": "{{add.Log}}", "variant": "secondary", "visible": "@binding:hasCreateEmployeeLogPermission", "onClick": "@controller:addEmployeeLog"}], "columns": [{"field": "id", "headerName": "id", "visible": false}, {"field": "EmployeeLogs-EmployeeLogType.name", "headerName": "{{log.type}}"}, {"field": "createdDate", "headerName": "{{date}}"}], "data": "@binding:EmployeeLogs"}]}]}}}