{"version": 2, "uicontroller": ["detail.uicontroller.ts"], "uimodel": {"state": {"userPrivateKeyBase64": null, "action": "read", "relatedEmployeeIds": []}, "nodes": {"Bonus": {"type": "struct", "modelId": "everest.hr.base/BonusModel.Bonus", "query": "@controller:getBonusQuery()", "fieldList": ["employeeId", "bonusTypeId", "bonusAmount", "endDate", "paymentPeriod", "startDate", "objectKeyId", "encryptedData"]}, "MasterKey": {"type": "struct", "modelId": "everest.base.encryption/MasterKeyModel.MasterKey", "fieldList": ["public<PERSON>ey"], "getMasterKey": {}}, "ObjectKey": {"modelId": "everest.base.encryption/ObjectKeyModel.ObjectKey", "type": "struct", "query": "@controller:getObjectKeyQuery()", "fieldList": ["id", "keySalt", "encryptedByMasterKey"]}, "ObjectKeyUserKeyMapping": {"type": "struct", "modelId": "everest.base.encryption/ObjectKeyUserKeyMappingModel.ObjectKeyUserKeyMapping", "query": "@controller:getObjectKeyUserKeyMappingQuery()", "fieldList": ["id", "userKeyId", "objectKeyId", "encryptedObjectKey", "validFrom"]}, "RelatedEmployeeData": {"type": "list", "modelId": "everest.hr.base/EmployeeModel.Employee", "query": "@controller:getRelatedEmployeeDataQuery()", "fieldList": ["id", "email", "name", "displayName"]}, "RelatedUserData": {"modelId": "everest.appserver.usermgmt/UserModel.User", "type": "list", "query": "@controller:getRelatedUserDataQuery()", "fieldList": ["userId", "email"]}, "RelatedUserKey": {"type": "list", "modelId": "everest.base.encryption/UserKeyModel.UserKey", "parent": "RelatedUserData", "joinKey": "userId-userId", "fieldList": ["id", "userPin", "encryptedPrivateKeyByUserPin", "encryptedPrivateKeyByObjectKey", "keySalt", "public<PERSON>ey", "objectKeyId", "userId"]}, "SystemKey": {"type": "struct", "model": "urn:evst:everest:base/encryption:model/node:SystemKey", "query": {"where": {"id": "@state:systemKeyId"}}, "fieldList": ["id", "public<PERSON>ey"]}}}, "uiview": {"layout": "everest.base.ui/layouts/uilayout/landing", "props": {"i18n": ["everest.base.encryption/encryption", "employee", "hrbase"], "title": "@controller:getTitle()", "config": {"allowRefreshData": true, "autoRefreshData": true}, "headerConfig": {"size": "8"}, "mainBlock": {"type": "secondary"}, "customSections": [{"component": "FieldGroup", "section": {"grid": {"size": "12"}, "editing": "@state:editing"}, "props": {"elements": [{"label": "{{currency}}", "field": "currency", "value": "@state:currency", "isDisabled": true}, {"component": "Select", "value": "@binding:Bonus.bonusTypeId", "bottomActions": [{"label": "{{manage.bonus.types}}", "onClick": "@controller:manageBonusTypes"}]}, {"label": "{{bonus.amount}}", "value": "@bindingController(Bonus.bonusAmount, getDecryptedBonusAmount())"}, {"label": "{{payment.date}}", "value": "@binding:Bonus.paymentPeriod"}, {"label": "{{startDate}}", "value": "@binding:Bonus.startDate"}, {"label": "{{endDate}}", "value": "@binding:Bonus.endDate"}]}}], "showPrimaryAction": false, "showSecondaryAction": false, "customActions": [{"variant": "secondary", "label": "{{close}}", "onClick": "@controller:onCloseClick"}, {"variant": "primary", "label": "{{save}}", "visible": "@controller:showSaveButton()", "onClick": "@controller:onSaveClick"}]}}}