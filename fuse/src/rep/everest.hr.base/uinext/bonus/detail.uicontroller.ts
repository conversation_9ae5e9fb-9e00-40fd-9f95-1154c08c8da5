// @i18n:hrbase
// @i18n:everest.base.encryption/encryption
// @i18n:employee

import { Decimal } from '@everestsystems/decimal';
import {
  arrayBufferToBinaryStr,
  binaryStrToBase64,
  createObjectKey,
  decryptRawDataObject,
  encryptObjectKey,
  encryptRawDataObject,
  exportObject<PERSON>ey,
  generateSalt,
  getKeySet,
  getUserPrivateKeyBase64,
  importPublicKey,
} from '@pkg/everest.base.encryption/public/utils.uicontroller';
import type { ObjectKey } from '@pkg/everest.base.encryption/types/ObjectKey';
import type { ObjectKeyUserKeyMapping } from '@pkg/everest.base.encryption/types/ObjectKeyUserKeyMapping';
import { Bonus } from '@pkg/everest.hr.base/types/Bonus';
import type { DetailUiTemplate } from '@pkg/everest.hr.base/types/uiTemplates/uinext/bonus/detail.ui';

type DetailContext = DetailUiTemplate.DetailContext;

type UserKeyType = {
  userId: string;
  userKeyId: number;
  email: string;
};
type ActionType = 'create' | 'read' | 'update';
type MutationActionType = Exclude<ActionType, 'read'>;
type Context = DetailContext & {
  state: {
    // you can extend and improve the state type here
    employeeId: number;
    action: ActionType;
    currency: string;
    relatedUserKeys: UserKeyType[];
    userKeyIds: number[];
    relatedEmployeeIds: number[];
    originalBonus: Decimal;
    objectKeyId: number;
    userKeyId: number;
    isEncrypted: boolean;
    systemKeyId: number;
  };
};
type CreateObjectKeyReturnType = {
  ObjectKey: ObjectKey.ReadReturnType;
};
type SubmitFn = (ctx: Context) => Promise<void>;

export function showSaveButton({ state: { action } }: Context) {
  return action !== 'read';
}

export async function onSaveClick(context: Context) {
  const { state } = context;
  const actionMap: Record<MutationActionType, SubmitFn> = {
    create: state.isEncrypted ? createEncryptedBonus : createRawBonus,
    update: state.isEncrypted ? updateEncryptedBonus : updateRawBonus,
  };
  const action: SubmitFn = actionMap[state.action];
  if (action) {
    await action(context);
  }
}

async function updateRawBonus({ data, actions, helpers }: Context) {
  const { Bonus } = data;
  await actions.submit({
    transform: () => ({
      Bonus: {
        update: {
          ...Bonus,
          endDate: Bonus?.endDate ?? null,
        },
      },
    }),
    onSuccess: () => {
      helpers.currentModalSubmitCallback();
      helpers.closeModal();
    },
  });
}

async function updateEncryptedBonus(context: Context) {
  const { data, state, helpers, actions } = context;
  const { Bonus, ObjectKey, ObjectKeyUserKeyMapping } = data;
  const isBonusChanged = !new Decimal(Bonus.bonusAmount).equals(
    state.originalBonus
  );
  let updateData: Bonus.CreationFields = {
    ...Bonus,
    bonusAmount: undefined,
  };
  if (isBonusChanged) {
    const privateKeyBase64 = getUserPrivateKeyBase64();
    const encryptedData = Bonus.encryptedData as Record<string, string>;
    if (
      ObjectKeyUserKeyMapping?.encryptedObjectKey &&
      privateKeyBase64 &&
      ObjectKey?.keySalt &&
      encryptedData?.bonusAmount
    ) {
      const { objectKey, salt } = await getKeySet(
        privateKeyBase64,
        ObjectKeyUserKeyMapping?.encryptedObjectKey,
        ObjectKey.keySalt
      );
      const iv = new Uint8Array(salt);
      const encryptedBonus = await encryptRawDataObject(
        Bonus,
        ['bonusAmount'],
        objectKey,
        iv
      );
      delete Bonus.bonusAmount;
      updateData = encryptedBonus;
    }
  }
  await actions.submit({
    transform: () => ({
      Bonus: {
        update: {
          ...updateData,
          endDate: Bonus?.endDate ?? null,
        },
      },
    }),
    onSuccess: () => {
      helpers.currentModalSubmitCallback();
      helpers.closeModal();
    },
  });
}
async function createRawBonus({ data, actions, helpers, state }: Context) {
  const { Bonus } = data;
  const { employeeId } = state;
  delete Bonus._nodeReference;
  await actions.submit({
    transform: () => ({
      Bonus: {
        create: {
          ...Bonus,
          employeeId,
        },
      },
    }),
    onSuccess: async () => {
      await helpers.currentModalSubmitCallback();
    },
  });
}
async function createEncryptedBonus(context: Context) {
  const { data, helpers, state, actions } = context;
  const { employeeId } = state;
  const {
    Bonus: rawBonusObject,
    MasterKey,
    RelatedUserKey,
    RelatedUserData,
    SystemKey,
  } = data;
  const { publicKey: publicMasterKeyBase64 } = MasterKey;
  let publicMasterKey: CryptoKey;
  try {
    publicMasterKey = await importPublicKey(publicMasterKeyBase64);
  } catch {
    helpers.showToast({
      type: 'error',
      message: '{{enc.invalidKey}}',
    });
    return;
  }
  rawBonusObject.employeeId = employeeId;
  const objectKey = await createObjectKey();
  const encryptionSalt = generateSalt();
  const objectKeyBuf = await exportObjectKey(objectKey);
  const encryptedObjectKey = await encryptObjectKey(
    objectKeyBuf,
    publicMasterKey
  );
  const encryptedBonus = await encryptRawDataObject(
    rawBonusObject,
    ['bonusAmount'],
    objectKey,
    encryptionSalt
  );
  const encryptionSaltBase64 = binaryStrToBase64(
    arrayBufferToBinaryStr(encryptionSalt)
  );
  const encryptedObjectKeyBase64 = binaryStrToBase64(
    arrayBufferToBinaryStr(encryptedObjectKey)
  );

  // encrypt object key using system key
  const publicSystemKey = await importPublicKey(SystemKey.publicKey);
  const encObjKeyBySystemKey = await encryptObjectKey(
    objectKeyBuf,
    publicSystemKey
  );
  const encObjKeyBySystemKeyBase64 = binaryStrToBase64(
    arrayBufferToBinaryStr(encObjKeyBySystemKey)
  );
  let response: CreateObjectKeyReturnType;

  try {
    response = await actions.run({
      ObjectKey: {
        action: 'createObjectKey',
        data: {
          keyToCreate: {
            relatedPackage: 'everest.hr.base',
            modelUrn: Bonus.MODEL_URN,
            keySalt: encryptionSaltBase64,
            encryptedByMasterKey: encryptedObjectKeyBase64,
            encryptedBySystemKey: encObjKeyBySystemKeyBase64,
            systemKeyId: SystemKey.id,
          },
        },
      },
    });
  } catch (error) {
    console.error(error);
    helpers.showToast({
      type: 'error',
      message: '{{enc.failedToCreateObjectKey}}',
    });
    return;
  }

  const { ObjectKey: createdObjectKey } = response;
  const userPublicKeys = new Map();
  for await (const userKey of RelatedUserKey) {
    const publicKey = await importPublicKey(userKey.publicKey);
    userPublicKeys.set(userKey.userId, { id: userKey.id, publicKey });
  }
  const mappingData: Array<ObjectKeyUserKeyMapping.CreationFields> = [];
  for await (const item of RelatedUserData) {
    const userKey = userPublicKeys.get(item.userId);
    if (userKey) {
      const encryptedObjectKeyByUserKeyBase64 = binaryStrToBase64(
        arrayBufferToBinaryStr(
          await encryptObjectKey(objectKeyBuf, userKey.publicKey)
        )
      );
      mappingData.push({
        objectKeyId: createdObjectKey.id,
        userKeyId: userKey.id,
        validFrom: new Date(),
        encryptedObjectKey: encryptedObjectKeyByUserKeyBase64,
        userId: item.userId,
      });
    } else {
      mappingData.push({
        objectKeyId: createdObjectKey.id,
        validFrom: new Date(),
        userId: item.userId,
      });
    }
  }
  try {
    await actions.run({
      ObjectKeyUserKeyMapping: {
        action: 'createMany',
        data: mappingData,
      },
    });
  } catch (error) {
    console.error(error);
    helpers.showToast({
      type: 'error',
      message: '{{enc.failedToCreateKeyMapping}}',
    });
  }

  await actions.submit({
    transform: () => ({
      Bonus: {
        create: {
          ...encryptedBonus,
          objectKeyId: createdObjectKey.id,
        },
      },
    }),
    onSuccess: async () => {
      await helpers.currentModalSubmitCallback();
    },
  });
}

export function onCloseClick({ helpers }: Context) {
  helpers.closeModal();
}

export function getCurrentLanguage({ language }: Context) {
  return language;
}

export function manageBonusTypes(context: Context) {
  const { actions, helpers } = context;
  helpers.openModal({
    title: '{{manage.bonus.types}}',
    size: 'large',
    template: `/templates/everest.hr.base/uinext/employee/manageBonusTypes?mode=edit`,
    onModalSubmit: actions.refetchUiModelData,
  });
}

export function getBonusQuery({ state }: Context) {
  return state?.id
    ? {
        where: {
          id: state.id,
        },
      }
    : undefined;
}

export function getRelatedUserDataQuery({ data, helpers }: Context) {
  const { RelatedEmployeeData } = data;
  const emails = RelatedEmployeeData?.map((employee) => employee.email);
  if (helpers.isEmpty(emails)) {
    return undefined;
  }
  return {
    where: {
      email: {
        $in: emails,
      },
    },
  };
}

export function getObjectKeyQuery({ state }: Context) {
  return state?.objectKeyId
    ? {
        where: {
          id: state.objectKeyId,
        },
      }
    : undefined;
}

export function getRelatedEmployeeDataQuery({ state }: Context) {
  return state.relatedEmployeeIds?.length > 0
    ? {
        where: {
          id: {
            $in: state.relatedEmployeeIds,
          },
        },
      }
    : undefined;
}

export async function getDecryptedBonusAmount(context: Context) {
  const { state, data } = context;
  const { Bonus, ObjectKey, ObjectKeyUserKeyMapping } = data;
  if (state.action === 'create') {
    return undefined;
  }
  if (Bonus?.bonusAmount) {
    return new Decimal(Bonus.bonusAmount);
  }
  const encryptedData = Bonus.encryptedData as Record<string, string>;
  const privateKeyBase64 = getUserPrivateKeyBase64();
  if (
    ObjectKeyUserKeyMapping?.encryptedObjectKey &&
    privateKeyBase64 &&
    ObjectKey?.keySalt &&
    encryptedData?.bonusAmount
  ) {
    try {
      const { objectKey, salt } = await getKeySet(
        privateKeyBase64,
        ObjectKeyUserKeyMapping?.encryptedObjectKey,
        ObjectKey.keySalt
      );
      const decryptedBonus = await decryptRawDataObject(
        Bonus,
        ['bonusAmount'],
        objectKey,
        salt
      );
      state.originalBonus = decryptedBonus.bonusAmount;
      return decryptedBonus.bonusAmount;
    } catch {
      state.invalidPin = true;
      return '{{enc.invalidKey}}';
    }
  }
  return '{{encrypted}}';
}

export function getObjectKeyUserKeyMappingQuery({ state }: Context) {
  return state.userKeyId && state.objectKeyId
    ? {
        where: {
          userKeyId: state.userKeyId,
          objectKeyId: state.objectKeyId,
        },
      }
    : undefined;
}

export function getTitle(context: Context) {
  const { data, state } = context;
  const employeeName = data?.RelatedEmployeeData?.find(
    (emp) => emp.id === state.employeeId
  )?.displayName;
  return state.action === 'update'
    ? `{{emp.bonus.update}}: ${employeeName}`
    : `{{add.bonus}}: ${employeeName}`;
}
