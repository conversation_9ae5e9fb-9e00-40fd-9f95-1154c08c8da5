{"version": 2, "uicontroller": "configuration.uicontroller.ts", "uimodel": {"nodes": {"Connectivity": {"type": "struct", "modelId": "everest.hr.base/EmployeeModel.Employee"}}}, "uiview": {"templateType": "details", "i18n": ["hrbase", "calendar"], "autoRefreshData": true, "title": "{{configuration.create}}", "sections": [{"component": "Block", "size": "12", "elements": [{"component": "Input", "label": "{{authorizationUrl}}", "isEditing": true, "action": "create", "value": "@binding:Connectivity.authorizationUrl"}, {"component": "Input", "label": "{{clientId}}", "isEditing": true, "action": "create", "value": "@binding:Connectivity.clientId"}, {"component": "Input", "label": "{{clientSecret}}", "isEditing": true, "action": "create", "value": "@binding:Connectivity.clientSecret"}, {"component": "Input", "label": "{{responseType}}", "isEditing": true, "action": "create", "value": "@binding:Connectivity.responseTypes"}, {"component": "Input", "label": "{{tokenEndpoint}}", "isEditing": true, "action": "create", "value": "@binding:Connectivity.tokenEndpoint"}, {"component": "Input", "label": "{{internalRedirectUrl}}", "isEditing": true, "action": "create", "value": "@binding:Connectivity.internalRedirectUrl"}]}, {"component": "ButtonGroup", "direction": "vertical", "start": "12", "end": "13", "actions": [{"variant": "primary", "label": "{{create}}", "onClick": "@controller:createConfiguration"}]}]}}