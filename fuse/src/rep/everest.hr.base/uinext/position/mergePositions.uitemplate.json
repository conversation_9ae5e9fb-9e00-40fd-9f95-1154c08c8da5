{"version": 2, "uicontroller": ["mergePositions.uicontroller.ts"], "uimodel": {"nodes": {"Positions": {"type": "list", "query": {"orderBy": ["positionNumber"]}, "modelId": "everest.hr.base/PositionModel.Position", "fieldList": ["Position-Department.departmentName", "Position-Department.departmentCode", "id", "name", "positionNumber", "jobProfileId", "employeeCount", "departmentName", "startDate", "endDate"], "pagination": true}}}, "uiview": {"templateType": "details", "i18n": ["position"], "header": {"content": {"title": "{{merge.positions}}"}}, "sections": {"content": [{"component": "HybridInput", "section": {"description": "{{merge.positions.source}}", "grid": {"size": "12"}}, "props": {"variant": "secondary", "field": {"name": "sources", "component": "Select", "mode": "multiple", "list": "@binding:Positions", "idProp": "id", "textProp": "name"}}}, {"component": "HybridInput", "section": {"description": "{{merge.positions.target}}", "grid": {"size": "12"}}, "props": {"variant": "secondary", "field": {"name": "target", "component": "Select", "list": "@controller:getFilteredPositions()", "idProp": "id", "textProp": "name"}}}]}, "actions": {"content": [{"label": "{{merge}}", "onClick": "@controller:onMergeClick", "disabled": "@controller:isMergeDisabled()", "confirmation": true}]}}}