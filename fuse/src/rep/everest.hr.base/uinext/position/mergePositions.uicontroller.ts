// @i18n:position
import type { MergePositionsUiTemplate } from '@pkg/everest.hr.base/types/uiTemplates/uinext/position/mergePositions.ui';
//@i18n :position

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type Any = any;
type Context = MergePositionsUiTemplate.MergePositionsContext & {
  state: {
    form: {
      sources: string[];
      target: string;
    };
  };
};

export async function onMergeClick(context: Context) {
  const { state, actions, helpers } = context;

  const result = await actions.run({
    Positions: {
      action: 'mergePosition',
      data: {
        sources: state.form.sources,
        target: state.form.target,
      },
    },
  });
  if (!result.error) {
    helpers.showToast({
      type: 'success',
      title: '{{merge.positions}}',
      message: '{{success.merge.positions}}',
    });
    await actions.refetchUiModelData();

    return;
  }
}

export function isMergeDisabled(context: Context) {
  const { state } = context;

  return !state?.form?.sources || !state?.form?.target;
}

export function getFilteredPositions(context: Context) {
  const { data, state } = context;
  if (state.form?.sources?.length > 0) {
    const selectedIds = new Set(
      state.form.sources.map((id) => id as unknown as number)
    );
    return data?.Positions.filter((position) => !selectedIds.has(position.id));
  } else {
    return [];
  }
}
