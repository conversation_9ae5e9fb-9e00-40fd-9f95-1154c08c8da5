{"version": 2, "uicontroller": ["detail.uicontroller.ts"], "uimodel": {"state": {"userPrivateKeyBase64": null}, "nodes": {"Salary": {"type": "struct", "query": {"where": {"id": "@state:id"}}, "modelId": "everest.hr.base/SalaryModel.Salary", "fieldList": ["id", "employeeId", "baseSalary", "startDate", "endDate", "unit", "encryptedData", "keyId", "Salary-ObjectKey.id"]}, "ObjectKey": {"modelId": "everest.base.encryption/ObjectKeyModel.ObjectKey", "type": "struct", "parent": "Salary", "joinKey": "keyId-id", "fieldList": ["id", "keySalt", "encryptedByMasterKey"]}, "ObjectKeyUserKeyMapping": {"type": "struct", "modelId": "everest.base.encryption/ObjectKeyUserKeyMappingModel.ObjectKeyUserKeyMapping", "query": "@controller:getObjectKeyUserKeyMappingQuery()", "fieldList": ["id", "userKeyId", "objectKeyId", "encryptedObjectKey", "validFrom"]}, "ExistingSalaries": {"type": "list", "modelId": "everest.hr.base/SalaryModel.Salary", "query": {"where": {"employeeId": "@state:employeeId", "id": {"$ne": "@state:id"}}}, "fieldList": ["id", "startDate", "endDate"]}}}, "uiview": {"layout": "everest.base.ui/layouts/uilayout/landing", "props": {"i18n": ["everest.base.encryption/encryption", "employee", "hrbase"], "title": "@controller:getTitle()", "config": {"allowRefreshData": true, "autoRefreshData": true}, "headerConfig": {"size": "8"}, "mainBlock": {"type": "secondary"}, "showPrimaryAction": "@state:editing", "showSecondaryAction": true, "customSections": [{"component": "FieldGroup", "section": {"grid": {"size": "12"}, "editing": "@state:editing"}, "props": {"elements": [{"label": "{{currency}}", "component": "<PERSON><PERSON><PERSON>", "value": "@state:currency"}, {"label": "{{salary.amount}}", "value": "@bindingController(Salary.baseSalary, getDecryptedSalary())"}, {"value": "@binding:Salary.unit"}, {"label": "{{startDate}}", "value": "@binding:Salary.startDate"}, {"label": "{{endDate}}", "value": "@binding:Salary.endDate"}]}}], "primaryAction": {"label": "{{save}}", "onClick": "@controller:onSaveClick"}, "secondaryAction": {"label": "{{close}}", "onClick": "@controller:onCloseClick"}}}}