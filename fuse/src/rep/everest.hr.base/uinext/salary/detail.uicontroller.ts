// @i18n:hrbase
// @i18n:employee
// @i18n:everest.base.encryption/encryption

import { Decimal } from '@everestsystems/decimal';
import {
  decryptRawDataObject,
  encryptRawDataObject,
  getKeySet,
  getUserPrivateKeyBase64,
} from '@pkg/everest.base.encryption/public/utils.uicontroller';
import type { Salary } from '@pkg/everest.hr.base/types/Salary';
import type { DetailUiTemplate } from '@pkg/everest.hr.base/types/uiTemplates/uinext/salary/detail.ui';
import { overlappingDateIntervals } from '@pkg/everest.hr.base/utils/Salary/overlappingDateIntervals';

type DetailContext = DetailUiTemplate.DetailContext;

type Context = DetailContext & {
  state: {
    currency: string;
    isEncrypted: boolean;
    id: number;
    userKeyId: number;
    editing: boolean;
    userPrivateKeyBase64: string;
    originalSalary: Decimal;
    objectKeyId: number;
    // you can extend and improve the state type here
  };
};

export function getActionLabel({ state }: Context) {
  return state.userPin ? '{{close}}' : '{{submit}}';
}

export function hasNotUserPrivateKey({ helpers }: Context) {
  const privateKeyBase64 = getUserPrivateKeyBase64();
  return helpers.isEmpty(privateKeyBase64);
}

export function hasUserPrivateKey(context: Context) {
  return !hasNotUserPrivateKey(context);
}

export async function getDecryptedSalary(context: Context) {
  const { state, data } = context;
  const { Salary, ObjectKey, ObjectKeyUserKeyMapping } = data;
  if (Salary.baseSalary) {
    return new Decimal(Salary.baseSalary);
  }
  const encryptedData = Salary.encryptedData as Record<string, string>;
  const privateKeyBase64 = getUserPrivateKeyBase64();
  if (
    ObjectKeyUserKeyMapping?.encryptedObjectKey &&
    privateKeyBase64 &&
    ObjectKey?.keySalt &&
    encryptedData?.baseSalary
  ) {
    try {
      const { objectKey, salt } = await getKeySet(
        privateKeyBase64,
        ObjectKeyUserKeyMapping?.encryptedObjectKey,
        ObjectKey.keySalt
      );
      const decryptedSalary = await decryptRawDataObject(
        Salary,
        ['baseSalary'],
        objectKey,
        salt
      );
      state.originalSalary = decryptedSalary.baseSalary;
      return decryptedSalary.baseSalary;
    } catch {
      state.invalidPin = true;
      return '{{enc.invalidKey}}';
    }
  }
  return '{{encrypted}}';
}

export function onCloseClick({ helpers }: Context) {
  helpers.closeModal();
}

export async function onSaveClick(context: Context) {
  const { state, data, helpers, actions } = context;
  const { Salary, ExistingSalaries } = data;
  if (
    overlappingDateIntervals(
      {
        startDate: new Date(Salary.startDate),
        endDate: Salary.endDate ? new Date(Salary.endDate) : null,
      },
      ExistingSalaries.map((salary) => ({
        startDate: new Date(salary.startDate),
        endDate: salary.endDate ? new Date(salary.endDate) : null,
      }))
    )
  ) {
    helpers.showToast({
      type: 'error',
      message: '{{salary.date.overlap}}',
    });
    return;
  }
  await (state.isEncrypted
    ? saveEncryptedSalary(context)
    : actions.submit({
        transform: () => ({
          Salary: {
            update: {
              ...Salary,
              endDate: Salary?.endDate ?? null,
            },
          },
        }),
        onSuccess: () => {
          helpers.currentModalSubmitCallback();
          helpers.closeModal();
        },
      }));
}

export async function saveEncryptedSalary(context: Context) {
  const { actions, data, helpers, state } = context;
  const { Salary, ObjectKey, ObjectKeyUserKeyMapping } = data;
  const isBaseSalaryChanged = !new Decimal(Salary.baseSalary).equals(
    state.originalSalary
  );
  let updateData: Salary.CreationFields = {
    ...Salary,
    baseSalary: undefined,
  };
  if (isBaseSalaryChanged) {
    const privateKeyBase64 = getUserPrivateKeyBase64();
    const encryptedData = Salary.encryptedData as Record<string, string>;
    if (
      ObjectKeyUserKeyMapping?.encryptedObjectKey &&
      privateKeyBase64 &&
      ObjectKey?.keySalt &&
      encryptedData?.baseSalary
    ) {
      const { objectKey, salt } = await getKeySet(
        privateKeyBase64,
        ObjectKeyUserKeyMapping?.encryptedObjectKey,
        ObjectKey.keySalt
      );
      const iv = new Uint8Array(salt);
      const encryptedSalaryObject = await encryptRawDataObject(
        Salary,
        ['baseSalary'],
        objectKey,
        iv
      );
      delete Salary.baseSalary;
      updateData = encryptedSalaryObject;
    }
  }

  await actions.submit({
    transform: () => ({
      Salary: {
        update: {
          ...updateData,
          endDate: Salary?.endDate ?? null,
        },
      },
    }),
    onSuccess: () => {
      helpers.currentModalSubmitCallback();
      helpers.closeModal();
    },
  });
}

export function getTitle(context: Context) {
  const { state } = context;
  return `{{base.salary}}: ${state.employeeName}`;
}

export function getObjectKeyUserKeyMappingQuery(context: Context) {
  const { state } = context;
  return state?.userKeyId && state?.objectKeyId
    ? {
        where: {
          userKeyId: state.userKeyId,
          objectKeyId: state.objectKeyId,
        },
      }
    : undefined;
}
