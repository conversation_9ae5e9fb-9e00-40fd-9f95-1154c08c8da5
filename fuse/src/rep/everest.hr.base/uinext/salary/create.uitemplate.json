{"version": 2, "uicontroller": ["create.uicontroller.ts"], "uimodel": {"nodes": {"Salary": {"type": "struct", "modelId": "everest.hr.base/SalaryModel.Salary", "fieldList": ["id", "employeeId", "baseSalary", "startDate", "endDate", "unit", "uuid", "encryptedData", "keyId", "Salary-ObjectKey.id", "Salary-Employee.name", "Salary-Employee.displayName"]}, "ObjectKey": {"modelId": "everest.base.encryption/ObjectKeyModel.ObjectKey", "type": "struct", "fieldList": ["id", "keySalt", "encryptedByMasterKey", "systemKeyId", "encryptedBySystemKey"]}, "MasterKey": {"type": "struct", "modelId": "everest.base.encryption/MasterKeyModel.MasterKey", "fieldList": ["public<PERSON>ey"], "getMasterKey": {}}, "ObjectKeyUserKeyMapping": {"type": "struct", "modelId": "everest.base.encryption/ObjectKeyUserKeyMappingModel.ObjectKeyUserKeyMapping", "fieldList": ["id", "objectKeyId", "userKeyId", "encryptedObjectKey", "validFrom", "validTo"]}, "RelatedEmployeeData": {"type": "list", "modelId": "everest.hr.base/EmployeeModel.Employee", "query": {"where": {"id": {"$in": "@state:relatedEmployeeIds"}}}, "fieldList": ["id", "email", "name", "displayName"]}, "RelatedUserData": {"modelId": "everest.appserver.usermgmt/UserModel.User", "type": "list", "query": "@controller:getRelatedUserDataQuery()", "fieldList": ["userId", "email"]}, "RelatedUserKey": {"type": "list", "modelId": "everest.base.encryption/UserKeyModel.UserKey", "parent": "RelatedUserData", "joinKey": "userId-userId", "fieldList": ["id", "userPin", "encryptedPrivateKeyByUserPin", "encryptedPrivateKeyByObjectKey", "keySalt", "public<PERSON>ey", "objectKeyId", "userId"]}, "SystemKey": {"type": "struct", "model": "urn:evst:everest:base/encryption:model/node:SystemKey", "query": {"where": {"id": "@state:systemKeyId"}}, "fieldList": ["id", "public<PERSON>ey"]}, "ExistingSalaries": {"type": "list", "modelId": "everest.hr.base/SalaryModel.Salary", "query": {"where": {"employeeId": "@state:employeeId"}}, "fieldList": ["id", "startDate", "endDate"]}}}, "uiview": {"layout": "everest.base.ui/layouts/uilayout/landing", "props": {"i18n": ["everest.base.encryption/encryption", "employee", "hrbase"], "title": "@controller:getTitle()", "config": {"allowRefreshData": true, "autoRefreshData": true}, "headerConfig": {"size": "8"}, "mainBlock": {"type": "secondary"}, "showSecondaryAction": false, "customSections": [{"component": "FieldGroup", "customId": "baseSalaryFieldGroup", "section": {"grid": {"size": "12"}, "editing": true}, "props": {"elements": [{"label": "{{currency}}", "component": "<PERSON><PERSON><PERSON>", "value": "@state:currency"}, {"label": "{{salary.amount}}", "value": "@binding:Salary.baseSalary"}, {"label": "{{unit}}", "value": "@binding:Salary.unit"}, {"label": "{{startDate}}", "value": "@binding:Salary.startDate", "initialValue": "@state:defaultStartDate"}, {"label": "{{endDate}}", "value": "@binding:Salary.endDate"}]}}], "primaryAction": {"variant": "primary", "label": "{{create}}", "onClick": "@controller:onCreateClick"}}}}