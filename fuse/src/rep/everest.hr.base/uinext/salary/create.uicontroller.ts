// @i18n:employee
// @i18n:everest.base.encryption/encryption

import {
  arrayBufferToBinaryStr,
  binaryStrToBase64,
  createObjectKey,
  encryptObjectKey,
  encryptRawDataObject,
  exportObjectKey,
  generateSalt,
  importPublicKey,
} from '@pkg/everest.base.encryption/public/utils.uicontroller';
import type { ObjectKey } from '@pkg/everest.base.encryption/types/ObjectKey';
import type { ObjectKeyUserKeyMapping } from '@pkg/everest.base.encryption/types/ObjectKeyUserKeyMapping';
import type { CreateUiTemplate } from '@pkg/everest.hr.base/types/uiTemplates/uinext/salary/create.ui';
import { overlappingDateIntervals } from '@pkg/everest.hr.base/utils/Salary/overlappingDateIntervals';

type CreateContext = CreateUiTemplate.CreateContext;

type UserKeyType = {
  userId: string;
  userKeyId: number;
  email: string;
};

type Context = CreateContext & {
  state: {
    currency: string;
    employeeId: number;
    relatedUserKeys: UserKeyType[];
    userKeyIds: number[];
    relatedEmployeeIds: number[];
    isEncrypted: boolean;
    defaultStartDate: Date;
  };
};

type CreateObjectKeyReturnType = {
  ObjectKey: ObjectKey.ReadReturnType;
};

export async function createEncryptedSalary(context: Context) {
  const { actions, data, state, helpers } = context;
  const { employeeId } = state;
  const {
    Salary: rawSalaryObject,
    MasterKey,
    RelatedUserKey,
    RelatedUserData,
    SystemKey,
  } = data;
  const { publicKey: publicMasterKeyBase64 } = MasterKey;
  let publicMasterKey: CryptoKey;
  try {
    publicMasterKey = await importPublicKey(publicMasterKeyBase64);
  } catch {
    helpers.showToast({
      type: 'error',
      message: '{{enc.invalidKey}}',
    });
    return;
  }
  rawSalaryObject.employeeId = employeeId;
  const objectKey = await createObjectKey();
  const encryptionSalt = generateSalt();
  const objectKeyBuf = await exportObjectKey(objectKey);
  const encryptedObjectKey = await encryptObjectKey(
    objectKeyBuf,
    publicMasterKey
  );
  const encryptedSalaryObject = await encryptRawDataObject(
    rawSalaryObject,
    ['baseSalary'],
    objectKey,
    encryptionSalt
  );
  const encryptionSaltBase64 = binaryStrToBase64(
    arrayBufferToBinaryStr(encryptionSalt)
  );
  const encryptedObjectKeyBase64 = binaryStrToBase64(
    arrayBufferToBinaryStr(encryptedObjectKey)
  );

  // encrypt object key using system key
  const publicSystemKey = await importPublicKey(SystemKey.publicKey);
  const encObjKeyBySystemKey = await encryptObjectKey(
    objectKeyBuf,
    publicSystemKey
  );
  const encObjKeyBySystemKeyBase64 = binaryStrToBase64(
    arrayBufferToBinaryStr(encObjKeyBySystemKey)
  );
  let response: CreateObjectKeyReturnType;
  try {
    response = await actions.run({
      ObjectKey: {
        action: 'createObjectKey',
        data: {
          keyToCreate: {
            relatedPackage: 'everest.hr.base',
            modelUrn: 'urn:evst:everest:hr/base:model/node:Salary',
            keySalt: encryptionSaltBase64,
            encryptedByMasterKey: encryptedObjectKeyBase64,
            encryptedBySystemKey: encObjKeyBySystemKeyBase64,
            systemKeyId: SystemKey.id,
          },
        },
      },
    });
  } catch (error) {
    console.error(error);
    helpers.showToast({
      type: 'error',
      message: '{{enc.failedToCreateObjectKey}}',
    });
    return;
  }

  const { ObjectKey: createdObjectKey } = response;
  const userPublicKeys = new Map();
  for await (const userKey of RelatedUserKey) {
    const publicKey = await importPublicKey(userKey.publicKey);
    userPublicKeys.set(userKey.userId, { id: userKey.id, publicKey });
  }
  const mappingData: Array<ObjectKeyUserKeyMapping.CreationFields> = [];
  for await (const item of RelatedUserData) {
    const userKey = userPublicKeys.get(item.userId);
    if (userKey) {
      const encryptedObjectKeyByUserKeyBase64 = binaryStrToBase64(
        arrayBufferToBinaryStr(
          await encryptObjectKey(objectKeyBuf, userKey.publicKey)
        )
      );
      mappingData.push({
        objectKeyId: createdObjectKey.id,
        userKeyId: userKey.id,
        validFrom: new Date(),
        encryptedObjectKey: encryptedObjectKeyByUserKeyBase64,
        userId: item.userId,
      });
    } else {
      mappingData.push({
        objectKeyId: createdObjectKey.id,
        validFrom: new Date(),
        userId: item.userId,
      });
    }
  }
  try {
    await actions.run({
      ObjectKeyUserKeyMapping: {
        action: 'createMany',
        data: mappingData,
      },
    });
  } catch {
    helpers.showToast({
      type: 'error',
      message: '{{enc.failedToCreateKeyMapping}}',
    });
  }

  await actions.submit({
    transform: () => ({
      Salary: {
        create: {
          ...encryptedSalaryObject,
          keyId: createdObjectKey.id,
        },
      },
    }),
    onSuccess: async () => {
      await helpers.currentModalSubmitCallback();
    },
  });
}

export async function onCreateClick(context: Context) {
  const { state, actions, helpers, data } = context;
  const { Salary, ExistingSalaries } = data;
  if (
    overlappingDateIntervals(
      {
        startDate: new Date(Salary.startDate),
        endDate: Salary.endDate ? new Date(Salary.endDate) : null,
      },
      ExistingSalaries.map((salary) => ({
        startDate: new Date(salary.startDate),
        endDate: salary.endDate ? new Date(salary.endDate) : null,
      }))
    )
  ) {
    helpers.showToast({
      type: 'error',
      message: '{{salary.date.overlap}}',
    });
    return;
  }
  if (state.isEncrypted) {
    await createEncryptedSalary(context);
  } else {
    const { employeeId } = state;
    delete Salary._nodeReference;
    try {
      await actions.run({
        Salary: {
          action: 'create',
          data: {
            ...Salary,
            employeeId,
          },
        },
      });
      await helpers.currentModalSubmitCallback();
    } catch (error) {
      throw new Error('{{salary.create.failed}}', { cause: error });
    }
  }
}

export function getRelatedUserDataQuery({ data, helpers }: Context) {
  const { RelatedEmployeeData } = data;
  const emails = RelatedEmployeeData?.map((employee) => employee.email);
  if (helpers.isEmpty(emails)) {
    return undefined;
  }
  return {
    where: {
      email: {
        $in: emails,
      },
    },
  };
}

export function getTitle(context: Context) {
  const { data, state } = context;
  const employeeName = data?.RelatedEmployeeData?.find(
    (emp) => emp.id === state.employeeId
  )?.displayName;
  return `{{add.base.salary}}: ${employeeName}`;
}
