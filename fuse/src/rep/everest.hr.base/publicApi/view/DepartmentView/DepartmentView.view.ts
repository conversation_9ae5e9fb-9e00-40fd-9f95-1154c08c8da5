import type { Comp<PERSON><PERSON><PERSON> } from '@everestsystems/content-core/lib/analytics/tables';
import {
  fromModel,
  PipelineBuilder,
} from '@pkg/everest.analytics/public/tableProcessing/pipelineBuilder';
import type { TdpTsViewQueryArgs } from '@pkg/everest.analytics/public/views/types';
import { Department } from '@pkg/everest.hr.base/types/Department';

/**
 * DepartmentView providing access to departments from HR base
 * This view accepts filters and orders results by department name
 */

/**
{
  "uimodel": {
    "nodes": {
      "theRequest": {
        "type": "list",
        "model": "urn:evst:everest:hr/base:model/view:publicApi/view/DepartmentView/DepartmentView",
        "fieldList": [
          "id", "departmentName"
        ]
      }
    },
    "payload": {
      "theRequest": {
        "action": "query",
        "data": {}
      }
    }
  }
}
 */

export default async function query(
  _args: TdpTsViewQueryArgs
): Promise<ComputeJson> {
  const pipeline = new PipelineBuilder(Department.MODEL_URN);

  // Define the Department view with relevant fields, focusing on identification
  const departmentView = fromModel(Department, pipeline)
    .select([
      'id',
      'departmentName',
    ] as const)
    .order(['departmentName']);

  // Set required execution flags
  pipeline.setFlag('everest.analytics', 'executionBackend', 'kernel');

  // Build and return the result
  return pipeline.build([departmentView.unwrap()]);
}