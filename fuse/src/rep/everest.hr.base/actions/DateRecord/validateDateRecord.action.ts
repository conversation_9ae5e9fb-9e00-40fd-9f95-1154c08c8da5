import type { ISession } from '@everestsystems/content-core';
import {
  getLanguage,
  getTranslation,
  getTranslations,
  ValidationError,
} from '@everestsystems/content-core';
import { Decimal } from '@everestsystems/decimal';
import { HolidayDate } from '@pkg/everest.base.calendar/types/HolidayDate';
import { EvstWeekDay } from '@pkg/everest.hr.base/types/enums/WeekDay';
import { HolidayCalendarDateMappingV2 } from '@pkg/everest.hr.base/types/HolidayCalendarDateMappingV2';
import { HolidayCalendarV2 } from '@pkg/everest.hr.base/types/HolidayCalendarV2';

import { BreakPolicy } from '../../types/BreakPolicy';
import { DateRecord } from '../../types/DateRecord';
import { Employee } from '../../types/Employee';
import { EvstLeaveStatus } from '../../types/enums/LeaveStatus';
import { EvstTimeRecordStatus } from '../../types/enums/TimeRecordStatus';
import { Leave } from '../../types/Leave';
import { TimeRecord } from '../../types/TimeRecord';
import { WorkDayPolicy } from '../../types/WorkDayPolicy';
import { WorkWeekPolicy } from '../../types/WorkWeekPolicy';
import {
  MS_PER_HOUR,
  MS_PER_MINUTE,
  OUT_OF_POLICY_CODE,
  OVERTIME_WORK,
  VIOLATE_BREAK_POLICY,
  VIOLATE_WORKDAY_POLICY,
  VIOLATE_WORKWEEK_POLICY,
} from '../shared/constants';
import { getTimespan, getWeekRange, sortTimeRecords } from '../shared/utils';
import getCurrentAssignedTSPolicy from '../TimesheetPolicy/getCurrentAssignedTSPolicy.action';

let language: string;
/**
 *
 * @param dateRecord
 * @param workRecords
 * @param workDayPolicy
 * @param strictMode
 * @returns
 */
async function validateDateRecordByWorkDayPolicy(
  env: ISession,
  dateRecord: DateRecord.DateRecord,
  workRecords: TimeRecord.TimeRecord[],
  workDayPolicy: WorkDayPolicy.WorkDayPolicy | undefined,
  strictMode = false
): Promise<string[]> | never {
  if (!workDayPolicy) {
    return [];
  }
  const [errorMsgTemplate, maxWorkHoursTemplate, validWorkTimeTemplate] =
    await getTranslations(
      env,
      [
        'ts.error.workdayPolicy.msg',
        'ts.error.workdayPolicy.maxWorkingHours.msg',
        'ts.error.workdayPolicy.validWorkTime',
      ],
      'everest.hr.base/timesheet'
    );
  const dateRecordErrors: string[] = [];
  if (!workDayPolicy.active) {
    const errorMsg = errorMsgTemplate.replace(
      '$error$',
      dateRecord.date.toLocaleString(language, {
        weekday: 'long',
      })
    );
    if (strictMode) {
      throw new ValidationError(errorMsg, OUT_OF_POLICY_CODE);
    }
    return [`${VIOLATE_WORKDAY_POLICY}_${errorMsg}`];
  }
  const maxWorkTimePerDay = workDayPolicy.maxWorkingHours * MS_PER_HOUR;
  let totalWorkTimePerDay = 0;
  for (const record of workRecords) {
    const workTime = getTimespan(record.startAt, record.endAt) ?? 0;
    totalWorkTimePerDay += workTime;
    const maxWorkHourMsg = maxWorkHoursTemplate
      .replace(
        '$date$',
        dateRecord.date.toLocaleString(language, {
          weekday: 'long',
        })
      )
      .replace('$hour$', workDayPolicy.maxWorkingHours.toString());
    if (totalWorkTimePerDay > maxWorkTimePerDay) {
      if (strictMode) {
        throw new ValidationError(maxWorkHourMsg, VIOLATE_WORKDAY_POLICY);
      }
      dateRecordErrors.push(`${VIOLATE_WORKDAY_POLICY}_${maxWorkHourMsg}`);
    }
    if (workTime > maxWorkTimePerDay) {
      if (strictMode) {
        throw new ValidationError(maxWorkHourMsg, VIOLATE_WORKDAY_POLICY);
      }
      dateRecordErrors.push(`${VIOLATE_WORKDAY_POLICY}_${maxWorkHourMsg}`);
    }
    const validWorkTimeMsg = validWorkTimeTemplate
      .replace('$start$', workDayPolicy.startAt)
      .replace('$end$', workDayPolicy.endAt);
    if (
      record.startAt < workDayPolicy.startAt ||
      record.endAt > workDayPolicy.endAt
    ) {
      if (strictMode) {
        throw new ValidationError(validWorkTimeMsg, VIOLATE_WORKDAY_POLICY);
      }
      dateRecordErrors.push(`${VIOLATE_WORKDAY_POLICY}_${validWorkTimeMsg}`);
    }
  }
  return dateRecordErrors;
}

async function validateDateRecordByWorkWeekPolicy(
  env: ISession,
  timeRecords: MutateTimeRecordType[],
  savedDateRecordsInWeek: DateRecord.DateRecord[],
  workWeekPolicy: Partial<WorkWeekPolicy.WorkWeekPolicy> | undefined,
  strictMode = false
): Promise<string[]> | never {
  const dateRecordErrors: string[] = [];
  if (!workWeekPolicy) {
    return [];
  }
  const maxWorkHoursTemplate = await getTranslation(
    env,
    'ts.error.workweekPolicy.maxWorkingHours.msg',
    'everest.hr.base/timesheet'
  );
  const totalWorkHoursInRecordDate = timeRecords
    .map(({ startAt, endAt }) => getTimespan(startAt, endAt))
    .reduce((prev, current) => prev + current, 0);

  const totalSavedWorkHoursInWeek = savedDateRecordsInWeek
    .map(({ workTime }) => workTime)
    .reduce((prev, current) => prev + current, 0);

  const totalWorkHours = totalWorkHoursInRecordDate + totalSavedWorkHoursInWeek;

  const maxWorkTimePerWeek =
    (workWeekPolicy.maxWorkingHours ?? 0) * MS_PER_HOUR;

  if (totalWorkHours > maxWorkTimePerWeek) {
    const errorMessage = maxWorkHoursTemplate.replace(
      '$hour$',
      workWeekPolicy.maxWorkingHours.toString()
    );
    if (strictMode) {
      throw new ValidationError(errorMessage, VIOLATE_WORKWEEK_POLICY);
    } else {
      dateRecordErrors.push(`${VIOLATE_WORKWEEK_POLICY}_${errorMessage}`);
    }
  }

  return dateRecordErrors;
}

async function validateDateRecordByBreakPolicy(
  env: ISession,
  sortedTimeRecords: MutateTimeRecordType[],
  breakPolicies: BreakPolicy.BreakPolicy[] | undefined,
  strictMode = false
): Promise<string[]> | never {
  if (!breakPolicies?.length || breakPolicies?.length === 0) {
    return [];
  }
  const errorMsgTemplate = await getTranslation(
    env,
    'ts.error.breakPolicy.msg',
    'everest.hr.base/timesheet'
  );
  const dateRecordErrors: string[] = [];
  let totalBreak = 0,
    totalWork = 0;
  for (let index = 0; index < sortedTimeRecords.length; index++) {
    const current = sortedTimeRecords[index];
    const prev =
      index > 0 ? sortedTimeRecords[index - 1] : { endAt: current.startAt };
    totalWork += getTimespan(current.startAt, current.endAt);
    totalBreak += getTimespan(prev.endAt, current.startAt);
  }
  for (const breakPolicy of breakPolicies) {
    if (
      breakPolicy &&
      totalBreak < breakPolicy.breakLength * MS_PER_MINUTE &&
      totalWork > breakPolicy.shiftLength * MS_PER_HOUR
    ) {
      const errorMessage = errorMsgTemplate
        .replace('$shiftLength$', breakPolicy.shiftLength.toString())
        .replace('$breakLength$', breakPolicy.breakLength.toString());
      if (strictMode) {
        throw new ValidationError(errorMessage, VIOLATE_BREAK_POLICY);
      } else {
        dateRecordErrors.push(`${VIOLATE_BREAK_POLICY}_${errorMessage}`);
      }
    }
  }
  return dateRecordErrors;
}

async function getApprovedOrRequestedLeaveRequestOnRecordDate(
  env: ISession,
  recordDate: Date,
  employeeId: number
): Promise<Partial<Leave.Leave>[]> {
  const leaveRequests = await Leave.query(
    env,
    {
      where: {
        employeeId,
        status: {
          $in: [EvstLeaveStatus.Approved, EvstLeaveStatus.Requested],
        },
        startDate: {
          $lte: recordDate,
        },
        endDate: {
          $gte: recordDate,
        },
      },
    },
    ['id', 'startDate', 'endDate', 'status', 'days']
  );

  return leaveRequests;
}

export async function getHolidaysInDateRange(
  env: ISession,
  startDate: Date,
  endDate: Date,
  employeeId: number
): Promise<HolidayDate.HolidayDate[]> {
  const employee = await Employee.read(env, { id: employeeId }, [
    'id',
    'holidayCalendar',
  ]);

  if (!employee?.holidayCalendar) {
    return [];
  }

  const holidayCalendar = await HolidayCalendarV2.read(
    env,
    { id: employee.holidayCalendar },
    ['id']
  );
  const holidayCalendarMapping = await HolidayCalendarDateMappingV2.query(
    env,
    {
      where: {
        holidayCalendarId: holidayCalendar.id,
      },
    },
    ['dateId']
  );
  const holidayDateIds = holidayCalendarMapping?.map((value) => value.dateId);
  const holidays = await HolidayDate.query(
    env,
    {
      where: {
        id: {
          $in: holidayDateIds,
        },
        holidayDate: {
          $between: [startDate, endDate],
        },
      },
    },
    ['id', 'holidayDate', 'holidayName']
  );
  return holidays /** TODO: types should be fixed (https://github.com/everestsystems/appserver/issues/8950) */ as HolidayDate.HolidayDate[];
}

export type MutateTimeRecordType = Partial<TimeRecord.TimeRecord> &
  Pick<TimeRecord.TimeRecord, 'startAt' | 'endAt' | 'id' | 'comment'>;

export default async function validateDateRecord(
  env: ISession,
  dateRecordId: number,
  keepRecord = false
): Promise<void> | never {
  language = getLanguage();
  const [dateRecordInDb, timeRecords] = await Promise.all([
    await DateRecord.read(env, { id: dateRecordId }, 'ALL_FIELDS'),
    await TimeRecord.query(
      env,
      {
        where: { dateRecordId },
      },
      'ALL_FIELDS'
    ),
  ]);
  const assignedTimesheetPolicy = await getCurrentAssignedTSPolicy(
    env,
    dateRecordInDb.employeeId
  );
  const isStrictMode = keepRecord ? false : assignedTimesheetPolicy.strictMode;
  const query = {
    where: {
      timesheetPolicyId: assignedTimesheetPolicy.id,
    },
  };
  // Check if dateRecord is holiday
  let validationErrors: string[] = [];
  const startDate = new Date(dateRecordInDb.date);
  startDate.setHours(0, 0, 0);
  const endDate = new Date(dateRecordInDb.date);
  endDate.setHours(23, 59, 59);
  const holidays = await getHolidaysInDateRange(
    env,
    startDate,
    endDate,
    dateRecordInDb.employeeId
  );
  if (holidays?.length > 0) {
    const holidayWorkTemplate = await getTranslation(
      env,
      'ts.error.holiday.work',
      'everest.hr.base/timesheet'
    );
    const holidayWorkErrorMsg = holidayWorkTemplate.replace(
      '$date$',
      dateRecordInDb.date.toLocaleDateString(language, {
        day: '2-digit',
        month: 'short',
        year: 'numeric',
      })
    );
    throw new ValidationError(holidayWorkErrorMsg, OUT_OF_POLICY_CODE);
  }
  /**
   * Allow employees record on canceled/rejected leave requests
   */
  const leaveRequests = await getApprovedOrRequestedLeaveRequestOnRecordDate(
    env,
    dateRecordInDb.date,
    dateRecordInDb.employeeId
  );
  const fullDayPTO = leaveRequests?.filter((pto) => {
    return pto.days.equals(new Decimal(1));
  });
  const ptoWorkTemplate = await getTranslation(
    env,
    'ts.error.pto.work',
    'everest.hr.base/timesheet'
  );
  if (fullDayPTO?.length > 0) {
    const ptoWorkErrorMessage = ptoWorkTemplate.replace(
      '$date$',
      dateRecordInDb.date.toLocaleDateString(language, {
        day: '2-digit',
        month: 'short',
        year: 'numeric',
      })
    );
    throw new ValidationError(ptoWorkErrorMessage, OUT_OF_POLICY_CODE);
  }
  const WeekDays = [
    EvstWeekDay.Sunday,
    EvstWeekDay.Monday,
    EvstWeekDay.Tuesday,
    EvstWeekDay.Wednesday,
    EvstWeekDay.Thursday,
    EvstWeekDay.Friday,
    EvstWeekDay.Saturday,
  ];
  const [[workDayPolicy], [workWeekPolicy], breakPolicies] = await Promise.all([
    await WorkDayPolicy.query(
      env,
      {
        where: {
          timesheetPolicyId: assignedTimesheetPolicy.id,
          weekDay: WeekDays[dateRecordInDb.date.getDay()],
        },
        take: 1,
      },
      'ALL_FIELDS'
    ),
    await WorkWeekPolicy.query(env, { ...query, take: 1 }, 'ALL_FIELDS'),
    await BreakPolicy.query(env, query, 'ALL_FIELDS'),
  ]);
  const workDayPolicyErrors = await validateDateRecordByWorkDayPolicy(
    env,
    dateRecordInDb,
    timeRecords,
    workDayPolicy,
    isStrictMode
  );
  const [firstDateOfWeek, lastDateOfWeek] = getWeekRange(dateRecordInDb.date);
  const savedDateRecordsInWeek: DateRecord.DateRecord[] =
    await DateRecord.query(
      env,
      {
        where: {
          $and: [
            {
              date: {
                $between: [firstDateOfWeek, lastDateOfWeek],
              },
              id: {
                $ne: dateRecordInDb.id,
              },
              employeeId: dateRecordInDb.employeeId,
            },
          ],
        },
      },
      'ALL_FIELDS'
    );
  const workWeekPolicyErrors = await validateDateRecordByWorkWeekPolicy(
    env,
    timeRecords,
    savedDateRecordsInWeek,
    workWeekPolicy,
    isStrictMode
  );

  if (workWeekPolicyErrors?.length === 0) {
    const invalidDateRecordsByWeekPolicy = savedDateRecordsInWeek?.filter(
      (dateRecord) => {
        const hasError = dateRecord.errorMessages?.some((msg: string) =>
          msg.includes(VIOLATE_WORKWEEK_POLICY)
        );
        return (
          hasError && dateRecord.recordStatus === EvstTimeRecordStatus.Draft
        );
      }
    );
    const removedWeekPolicyError = invalidDateRecordsByWeekPolicy?.map(
      (dateRecord) => {
        const errorMessages = dateRecord.errorMessages?.filter(
          (msg) => !msg.includes(VIOLATE_WORKWEEK_POLICY)
        );
        return {
          ...dateRecord,
          errorMessages,
        };
      }
    );
    await Promise.all(
      removedWeekPolicyError.map(async (dateRecord) => {
        await DateRecord.update(
          env,
          {
            id: dateRecord.id,
          },
          {
            errorMessages: dateRecord.errorMessages,
          }
        );
      })
    );
  }

  const sortedTimeRecords = timeRecords.sort(sortTimeRecords);
  const breakPolicyErrors = await validateDateRecordByBreakPolicy(
    env,
    sortedTimeRecords,
    breakPolicies,
    isStrictMode
  );

  validationErrors = [
    ...validationErrors,
    ...workDayPolicyErrors,
    ...workWeekPolicyErrors,
    ...breakPolicyErrors,
  ];

  const errorsChanged =
    dateRecordInDb.errorMessages?.sort().join('') !==
    validationErrors?.sort().join('');

  if (errorsChanged) {
    await DateRecord.update(
      env,
      {
        id: dateRecordInDb.id,
      },
      {
        errorMessages: validationErrors,
      }
    );
  }
}

/**
 * Mark Existed Date Records as Overtime Work after create Leave Request
 * @param env Current Session
 * @param leaveRequest Leave Request
 */
export async function addOvertimeMessageToDateRecord(
  env: ISession,
  leaveRequest: Partial<Leave.Leave> &
    Required<
      Pick<
        Leave.Leave,
        'startDate' | 'endDate' | 'employeeId' | 'status' | 'days'
      >
    >
): Promise<void> {
  const fullDay = new Decimal(leaveRequest.days).equals(new Decimal(1));
  // Allow users to create date record on half-day PTO
  if (!fullDay) {
    return;
  }
  const dateRecordsInRange = await DateRecord.query(
    env,
    {
      where: {
        date: {
          $between: [leaveRequest.startDate, leaveRequest.endDate],
        },
        employeeId: leaveRequest.employeeId,
      },
    },
    ['id', 'recordStatus', 'errorMessages', 'date']
  );
  const errorMsgTemplate = await getTranslation(
    env,
    'ts.error.pto.work',
    'everest.hr.base/timesheet'
  );
  await Promise.all(
    dateRecordsInRange.map(async (dateRecord) => {
      const errorMessage = errorMsgTemplate.replace(
        '$date$',
        dateRecord.date.toLocaleDateString(language, {
          day: '2-digit',
          month: 'short',
          year: 'numeric',
        })
      );
      const errorMessages = dateRecord.errorMessages?.find((msg) =>
        msg.includes(OVERTIME_WORK)
      )
        ? dateRecord.errorMessages
        : [
            ...(dateRecord?.errorMessages ?? []),
            `${OVERTIME_WORK}_${errorMessage}`,
          ];
      await DateRecord.update(
        env,
        {
          id: dateRecord.id,
        },
        {
          errorMessages,
        }
      );
    })
  );
}

/**
 * Remove Overtime Message from Existed Date Records after reject/cancel/delete Leave Request
 * @param env Current Session
 * @param leaveRequest Leave Request
 */
export async function removeOvertimeMessageFromDateRecord(
  env: ISession,
  leaveRequest: Partial<Leave.Leave> &
    Required<
      Pick<Leave.Leave, 'startDate' | 'endDate' | 'employeeId' | 'status'>
    >
): Promise<void> {
  const dateRecordsInRange = await DateRecord.query(
    env,
    {
      where: {
        date: {
          $between: [leaveRequest.startDate, leaveRequest.endDate],
        },
        employeeId: leaveRequest.employeeId,
      },
    },
    ['id', 'recordStatus', 'errorMessages']
  );
  await Promise.all(
    dateRecordsInRange.map(async (dateRecord) => {
      const errorMessages = dateRecord.errorMessages?.filter(
        (msg) => !msg.includes(OVERTIME_WORK)
      );
      await DateRecord.update(
        env,
        {
          id: dateRecord.id,
        },
        {
          errorMessages,
        }
      );
    })
  );
}
