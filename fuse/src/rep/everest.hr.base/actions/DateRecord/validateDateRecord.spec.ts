/**
 * everest.hr.base Unit Tests for Actions
 *
 * @group everest.hr.base/actions
 */
import { getTranslation, type ISession } from '@everestsystems/content-core';
import type { TestTeardown } from '@everestsystems/content-core/lib/tests';
import { setupTest } from '@everestsystems/content-core/lib/tests';
import { Decimal } from '@everestsystems/decimal';
import { HolidayDate } from '@pkg/everest.base.calendar/types/HolidayDate';
import { DateRecord } from '@pkg/everest.hr.base/types/DateRecord';
import { Employee } from '@pkg/everest.hr.base/types/Employee';
import { EvstLeaveStatus } from '@pkg/everest.hr.base/types/enums/LeaveStatus';
import { EvstLeaveType } from '@pkg/everest.hr.base/types/enums/LeaveType';
import { EvstTimeRecordStatus } from '@pkg/everest.hr.base/types/enums/TimeRecordStatus';
import { HolidayCalendarDateMappingV2 } from '@pkg/everest.hr.base/types/HolidayCalendarDateMappingV2';
import { HolidayCalendarV2 } from '@pkg/everest.hr.base/types/HolidayCalendarV2';
import { Leave } from '@pkg/everest.hr.base/types/Leave';
import { Person } from '@pkg/everest.hr.base/types/Person';
import { TimesheetPolicy } from '@pkg/everest.hr.base/types/TimesheetPolicy';

import { OVERTIME_WORK } from '../shared/constants';
import validateDateRecord, {
  addOvertimeMessageToDateRecord,
  getHolidaysInDateRange,
  removeOvertimeMessageFromDateRecord,
} from './validateDateRecord.action';

describe('actions/validateDateRecord', () => {
  let session: ISession;
  let teardown: TestTeardown;
  let HolidayCalendarV2Client: HolidayCalendarV2.IControllerClient;
  let HolidayDateClient: HolidayDate.IControllerClient;
  let HolidayCalendarDateMappingV2Client: HolidayCalendarDateMappingV2.IControllerClient;
  let PersonClient: Person.IControllerClient;
  let EmployeeClient: Employee.IControllerClient;
  let DateRecordClient: DateRecord.IControllerClient;
  let TimesheetPolicyClient: TimesheetPolicy.IControllerClient;
  let LeaveClient: Leave.IControllerClient;
  let person: Partial<Person.Person>;
  let employee: Partial<Employee.Employee>;

  beforeAll(async () => {
    ({ session, teardown } = await setupTest({}));

    PersonClient = await Person.client(session);
    EmployeeClient = await Employee.client(session);
    TimesheetPolicyClient = await TimesheetPolicy.client(session);

    person = await PersonClient.create(
      {
        name: 'Test Person',
      },
      ['id', 'name']
    );

    employee = await EmployeeClient.create(
      {
        name: person?.name,
        email: '<EMAIL>',
        personId: person?.id,
      },
      ['id']
    );

    await TimesheetPolicyClient.create(
      {
        name: 'Test Timesheet Policy',
        startDate: new Date(2023, 11, 1),
        endDate: new Date(2023, 11, 31),
      },
      ['id']
    );
  });

  afterAll(() => teardown());

  it('should return Holidays in Date range', async () => {
    HolidayCalendarV2Client = await HolidayCalendarV2.client(session);
    HolidayDateClient = await HolidayDate.client(session);
    HolidayCalendarDateMappingV2Client =
      await HolidayCalendarDateMappingV2.client(session);

    const holidayCalendar = await HolidayCalendarV2Client.create(
      { countryCode: 'DE', subdivisionCode: 'DE-BY' },
      ['id']
    );

    const holidaysNotAssigned = await getHolidaysInDateRange(
      session,
      new Date(2023, 11, 1),
      new Date(2023, 11, 31),
      employee?.id
    );

    expect(holidaysNotAssigned?.length).toBe(0);

    await EmployeeClient.update(
      {
        id: employee?.id,
      },
      { holidayCalendar: holidayCalendar?.id }
    );

    const holidayDate = await HolidayDateClient.create(
      {
        holidayDate: new Date(2023, 11, 25),
        holidayName: 'Test Holiday',
        halfDay: false,
      },
      ['id']
    );

    const holidayCalendarDateMapping =
      await HolidayCalendarDateMappingV2Client.create(
        {
          dateId: holidayDate?.id,
          holidayCalendarId: holidayCalendar?.id,
        },
        ['id']
      );

    const holidaysAssigned = await getHolidaysInDateRange(
      session,
      new Date(2023, 11, 1),
      new Date(2023, 11, 31),
      employee?.id
    );

    expect(holidaysAssigned?.length).toBe(1);
    expect(holidaysAssigned?.[0]?.holidayName).toBe('Test Holiday');
    expect(holidaysAssigned?.[0]?.holidayDate).toStrictEqual(
      new Date(2023, 11, 25)
    );

    // Cleanup
    await HolidayDateClient.delete({
      id: holidayDate?.id,
    });
    await HolidayCalendarDateMappingV2Client.delete({
      id: holidayCalendarDateMapping?.id,
    });
  });

  it('should validate Date Records', async () => {
    DateRecordClient = await DateRecord.client(session);

    const dateRecord = await DateRecordClient.create(
      {
        employeeId: employee?.id,
        date: new Date(2023, 11, 25),
        recordStatus: EvstTimeRecordStatus.Approved,
      },
      ['id']
    );

    expect(await validateDateRecord(session, dateRecord?.id)).toBeUndefined();

    // Cleanup
    await DateRecordClient.delete({ id: dateRecord?.id });
  });

  it('should add overtime message to Date Record', async () => {
    DateRecordClient = await DateRecord.client(session);
    LeaveClient = await Leave.client(session);

    const dateRecord = await DateRecordClient.create(
      {
        employeeId: employee?.id,
        date: new Date(2023, 11, 25),
        recordStatus: EvstTimeRecordStatus.Approved,
        errorMessages: [],
      },
      ['id', 'errorMessages']
    );

    expect(dateRecord?.id).toBeDefined();
    expect(dateRecord?.errorMessages).toEqual([]);

    const leave = await LeaveClient.create(
      {
        absenceType: EvstLeaveType.SickLeave,
        employeeId: employee?.id,
        startDate: new Date(2023, 11, 24),
        endDate: new Date(2023, 11, 26),
        status: EvstLeaveStatus.Approved,
        days: new Decimal(1),
      },
      ['id', 'startDate', 'endDate', 'employeeId', 'status', 'days']
    );

    expect(leave?.id).toBeDefined();

    const leaveRequest: Partial<Leave.Leave> &
      Required<
        Pick<
          Leave.Leave,
          'startDate' | 'endDate' | 'employeeId' | 'status' | 'days'
        >
      > = {
      startDate: leave?.startDate,
      endDate: leave?.endDate,
      employeeId: leave?.employeeId,
      status: leave?.status,
      days: leave?.days,
    };

    await addOvertimeMessageToDateRecord(session, leaveRequest);

    const dateRecordInDB = await DateRecordClient.read(
      {
        id: dateRecord?.id,
      },
      ['id', 'errorMessages']
    );
    const errorMsgTemplate = await getTranslation(
      session,
      'ts.error.pto.work',
      'everest.hr.base/timesheet'
    );
    const errorMessage = errorMsgTemplate.replace('$date$', 'Dec 25, 2023');
    expect(dateRecordInDB).toBeDefined();
    expect(dateRecordInDB?.errorMessages).toEqual([
      `OvertimeWork_${errorMessage}`,
    ]);

    // Test adding the message when it's already present
    await addOvertimeMessageToDateRecord(session, leaveRequest);

    const newDateRecordInDB = await DateRecordClient.read(
      {
        id: dateRecord?.id,
      },
      ['id', 'errorMessages']
    );

    expect(newDateRecordInDB).toBeDefined();
    expect(newDateRecordInDB?.errorMessages).toEqual([
      `OvertimeWork_${errorMessage}`,
    ]);

    // Test with full day leave (should not add message)
    const updatedLeaveRequest = {
      ...leaveRequest,
      days: new Decimal(3),
    };

    await addOvertimeMessageToDateRecord(session, updatedLeaveRequest);

    const fullDayDateRecordInDB = await DateRecordClient.read(
      {
        id: dateRecord?.id,
      },
      ['id', 'errorMessages']
    );

    expect(fullDayDateRecordInDB).toBeDefined();
    expect(fullDayDateRecordInDB?.errorMessages).toEqual([
      `OvertimeWork_${errorMessage}`,
    ]);

    // Cleanup
    await DateRecordClient.delete({ id: dateRecord?.id });
    await LeaveClient.delete({ id: leave?.id });
  });

  it('should remove overtime message to Date Record', async () => {
    DateRecordClient = await DateRecord.client(session);
    LeaveClient = await Leave.client(session);

    const dateRecord = await DateRecordClient.create(
      {
        employeeId: employee?.id,
        date: new Date(2023, 11, 25),
        recordStatus: EvstTimeRecordStatus.Approved,
        errorMessages: ['Test Error Message', OVERTIME_WORK],
      },
      ['id', 'errorMessages']
    );

    expect(dateRecord?.id).toBeDefined();
    expect(dateRecord?.errorMessages?.length).toBe(2);

    const leave = await LeaveClient.create(
      {
        absenceType: EvstLeaveType.SickLeave,
        employeeId: employee?.id,
        startDate: new Date(2023, 11, 24),
        endDate: new Date(2023, 11, 26),
        status: EvstLeaveStatus.Approved,
      },
      ['id', 'startDate', 'endDate', 'employeeId', 'status']
    );

    expect(leave?.id).toBeDefined();

    const leaveRequest: Partial<Leave.Leave> &
      Required<
        Pick<Leave.Leave, 'startDate' | 'endDate' | 'employeeId' | 'status'>
      > = {
      startDate: leave?.startDate,
      endDate: leave?.endDate,
      employeeId: leave?.employeeId,
      status: leave?.status,
    };

    await removeOvertimeMessageFromDateRecord(session, leaveRequest);

    const dateRecordInDB = await DateRecordClient.read(
      {
        id: dateRecord?.id,
      },
      ['id', 'errorMessages']
    );

    expect(dateRecordInDB).toBeDefined();
    expect(dateRecordInDB?.errorMessages?.length).toBe(1);
    expect(dateRecordInDB?.errorMessages?.[0]).toEqual('Test Error Message');

    // Cleanup
    await DateRecordClient.delete({ id: dateRecord?.id });
    await LeaveClient.delete({ id: leave?.id });
  });
});
