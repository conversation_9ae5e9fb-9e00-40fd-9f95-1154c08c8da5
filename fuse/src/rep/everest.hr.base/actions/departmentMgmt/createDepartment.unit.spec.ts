import type { ISession } from '@everestsystems/content-core';
import {
  createFailureResult,
  createSuccessResult,
  LoadMessage,
} from '@pkg/everest.hr.base/actions/departmentMgmt/utils/loadUtils';
import type { EvstDepartmentTransformedData } from '@pkg/everest.hr.base/types/composites/DepartmentTransformedData';
import { Department } from '@pkg/everest.hr.base/types/Department';
import { DepartmentHierarchy } from '@pkg/everest.hr.base/types/DepartmentHierarchy';
import { omit } from 'lodash';

import createDepartment from './createDepartment.action';

jest.mock('@everestsystems/content-core', () => ({
  parallel: jest.fn((array, callback) =>
    Promise.all(array.map((element) => callback(element)))
  ),
  ISession: jest.fn(),
}));

jest.mock(
  '@pkg/everest.hr.base/actions/departmentMgmt/utils/loadUtils',
  () => ({
    createSuccessResult: jest.fn(),
    createFailureResult: jest.fn(),
    LoadMessage: {
      SUCCESS: 'Success',
      CREATE_ERROR: 'Create Error',
    },
  })
);

jest.mock('@pkg/everest.hr.base/types/Department', () => ({
  Department: {
    create: jest.fn(),
    update: jest.fn(),
  },
}));

jest.mock('@pkg/everest.hr.base/types/DepartmentHierarchy', () => ({
  DepartmentHierarchy: {
    create: jest.fn(),
  },
}));

jest.mock('lodash', () => ({
  omit: jest.fn((obj, keys) => {
    const result = { ...obj };
    for (const key of keys) {
      delete result[key];
    }
    return result;
  }),
}));

describe('createDepartment', () => {
  const mockEnv = {} as ISession;
  const mockConfig = {} as any;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should successfully create departments', async () => {
    const mockData: EvstDepartmentTransformedData[] = [
      {
        departmentName: 'HR',

        parentId: 1,
        startDate: new Date(),
      },
      {
        departmentName: 'Finance',

        parentId: 2,
        startDate: new Date(),
      },
    ];

    const mockCreatedDepartment = {
      id: 1,
      departmentName: 'HR',
      departmentNumber: 'HR001',
    };

    (Department.create as jest.Mock).mockResolvedValue(mockCreatedDepartment);
    (createSuccessResult as jest.Mock).mockReturnValue({ success: true });

    const result = await createDepartment(mockEnv, mockData, mockConfig);

    expect(result).toHaveLength(2);
    expect(Department.create).toHaveBeenCalledTimes(2);
    expect(DepartmentHierarchy.create).toHaveBeenCalledTimes(2);
    expect(createSuccessResult).toHaveBeenCalledTimes(2);
    expect(createSuccessResult).toHaveBeenCalledWith(1, LoadMessage.SUCCESS);
  });

  it('should handle errors and return failure results', async () => {
    const mockData: EvstDepartmentTransformedData[] = [
      {
        departmentName: 'HR',

        parentId: 1,
        startDate: new Date(),
      },
    ];

    const mockError = new Error('Creation failed');
    (Department.create as jest.Mock).mockRejectedValue(mockError);
    (createFailureResult as jest.Mock).mockReturnValue({ success: false });

    const result = await createDepartment(mockEnv, mockData, mockConfig);

    expect(result).toHaveLength(1);
    expect(Department.create).toHaveBeenCalledTimes(1);
    expect(createFailureResult).toHaveBeenCalledTimes(1);
    expect(createFailureResult).toHaveBeenCalledWith(
      undefined,
      LoadMessage.CREATE_ERROR,
      mockError
    );
  });

  it('should update department code if it is not set', async () => {
    const mockData: EvstDepartmentTransformedData[] = [
      {
        departmentName: 'HR',

        parentId: 1,
        startDate: new Date(),
      },
    ];

    const mockCreatedDepartment = {
      id: 1,
      departmentName: 'HR',
      departmentNumber: 'HR001',
      departmentCode: null,
    };

    (Department.create as jest.Mock).mockResolvedValue(mockCreatedDepartment);
    (createSuccessResult as jest.Mock).mockReturnValue({ success: true });

    await createDepartment(mockEnv, mockData, mockConfig);

    expect(Department.update).toHaveBeenCalledWith(
      mockEnv,
      { id: 1 },
      { ...mockCreatedDepartment, departmentCode: 'HR001' }
    );
  });

  it('should not update department code if it is already set', async () => {
    const mockData: EvstDepartmentTransformedData[] = [
      {
        departmentName: 'HR',

        departmentCode: 'HRCODE',
        parentId: 1,
        startDate: new Date(),
      },
    ];

    const mockCreatedDepartment = {
      id: 1,
      departmentName: 'HR',
      departmentNumber: 'HR001',
      departmentCode: 'HRCODE',
    };

    (Department.create as jest.Mock).mockResolvedValue(mockCreatedDepartment);
    (createSuccessResult as jest.Mock).mockReturnValue({ success: true });

    await createDepartment(mockEnv, mockData, mockConfig);

    expect(Department.update).not.toHaveBeenCalled();
  });

  it('should create department hierarchy', async () => {
    const mockData: EvstDepartmentTransformedData[] = [
      {
        departmentName: 'HR',

        parentId: 1,
        startDate: new Date(),
      },
    ];

    const mockCreatedDepartment = {
      id: 2,
      departmentName: 'HR',
    };

    (Department.create as jest.Mock).mockResolvedValue(mockCreatedDepartment);
    (createSuccessResult as jest.Mock).mockReturnValue({ success: true });

    await createDepartment(mockEnv, mockData, mockConfig);

    expect(DepartmentHierarchy.create).toHaveBeenCalledWith(mockEnv, {
      nodeId: 2,
      departmentNumber: 2,
      parentId: 1,
    });
  });

  it('should omit parentId when creating department', async () => {
    const mockData: EvstDepartmentTransformedData[] = [
      {
        departmentName: 'HR',
        parentId: 1,
        startDate: new Date(),
      },
    ];

    const mockCreatedDepartment = {
      id: 1,
      departmentName: 'HR',
      departmentNumber: 'HR001',
    };

    (Department.create as jest.Mock).mockResolvedValue(mockCreatedDepartment);
    (createSuccessResult as jest.Mock).mockReturnValue({ success: true });

    await createDepartment(mockEnv, mockData, mockConfig);

    expect(omit).toHaveBeenCalledWith(mockData[0], ['parentId']);
    expect(Department.create).toHaveBeenCalledWith(
      mockEnv,
      { departmentName: 'HR', startDate: expect.any(Date) },
      expect.any(Array)
    );
  });
  it('should throw an error if startDate is not provided', async () => {
    const mockData: EvstDepartmentTransformedData[] = [
      {
        departmentName: 'HR',
        parentId: 1,
        startDate: undefined,
      },
    ];

    (createFailureResult as jest.Mock).mockReturnValue({ success: false });

    const result = await createDepartment(mockEnv, mockData, mockConfig);

    expect(result).toHaveLength(1);
    expect(Department.create).not.toHaveBeenCalled();
    expect(createFailureResult).toHaveBeenCalledTimes(1);
    expect(createFailureResult).toHaveBeenCalledWith(
      undefined,
      LoadMessage.CREATE_ERROR,
      new Error('Start Date is required when creating a Department')
    );
  });
});
