import { type ISession, parallel } from '@everestsystems/content-core';
import type { EvstLoadFunctionReturnType } from '@pkg/everest.base/types/composites/LoadFunctionReturnType';
import type { EvstMigrationIntegrationConfig } from '@pkg/everest.base/types/composites/MigrationIntegrationConfig';
import {
  createFailureResult,
  createSuccessResult,
  LoadMessage,
} from '@pkg/everest.hr.base/actions/departmentMgmt/utils/loadUtils';
import type { EvstDepartmentTransformedData } from '@pkg/everest.hr.base/types/composites/DepartmentTransformedData';
import { Department } from '@pkg/everest.hr.base/types/Department';
import { DepartmentHierarchy } from '@pkg/everest.hr.base/types/DepartmentHierarchy';
import { omit } from 'lodash';

export default async function createDepartment(
  env: ISession,
  data: EvstDepartmentTransformedData[],
  _config: EvstMigrationIntegrationConfig
): Promise<EvstLoadFunctionReturnType[]> {
  return await parallel(data, async (departmentData) => {
    try {
      const result = await loadDepartment(env, departmentData);
      return createSuccessResult(result.id, LoadMessage.SUCCESS);
    } catch (error) {
      return createFailureResult(undefined, LoadMessage.CREATE_ERROR, error);
    }
  });
}

async function loadDepartment(
  env: ISession,
  data: EvstDepartmentTransformedData
): Promise<Partial<Department.Department>> {
  if (!data.startDate) {
    throw new Error('Start Date is required when creating a Department');
  }

  const creationData: Department.CreationFields = {
    ...omit(data, ['parentId']),
    startDate: data.startDate,
  };
  const department = await Department.create(env, creationData, [
    'id',
    'departmentName',
    'departmentCode',
    'departmentNumber',
    'startDate',
    'endDate',
    'status',
    'externalDepartmentId',
  ]);

  if (!department?.departmentCode) {
    department.departmentCode = department?.departmentNumber;
    await Department.update(env, { id: department.id }, department);
  }

  await DepartmentHierarchy.create(env, {
    nodeId: department.id,
    departmentNumber: department.id,
    parentId: data?.parentId,
  });

  return department;
}
