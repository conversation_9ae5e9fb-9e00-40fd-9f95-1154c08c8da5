import {
  getTranslations,
  Group,
  type ISession,
} from '@everestsystems/content-core';
import { User } from '@pkg/everest.appserver.usermgmt/types/User';
import { Notification } from '@pkg/everest.appserver/types/Notification';
import { NotificationCategory } from '@pkg/everest.appserver/types/NotificationCategory';
import type { Employee } from '@pkg/everest.hr.base/types/Employee';
import type { MandatoryLeave } from '@pkg/everest.hr.base/types/MandatoryLeave';
import {
  formatDateAsYMD,
  formatLongDate,
} from '@pkg/everest.hr.base/utils/Date/date';

import recalculateAbsenceDays from '../Employee/recalculateAbsenceDays.action';
import { filterApprovalsInSandbox, getBaseURL } from '../Notification/utils';

type MandatoryLeavesObject = {
  [action: string]: Partial<MandatoryLeave.MandatoryLeave>[];
};

type Message = {
  blocks: {
    type?: string;
    text?: {
      type: string;
      text: string;
      emoji?: boolean;
    };
    elements?: {
      type: string;
      text: {
        type: string;
        emoji: boolean;
        text: string;
      };
      action_id: string;
      value: string;
      style: string;
      url: string;
    }[];
  }[];
};

let leaveRequestTranslations: string[];
let hrBaseTranslations: string[];
let absencePolicyTranslations: string[];

function recalculationFailedMessage(link: string, employeesList: string) {
  const message = {
    blocks: [
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: leaveRequestTranslations?.[0],
          emoji: true,
        },
      },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: leaveRequestTranslations?.[1]
            .replace('*Employees List:*', '\n*Employees List:*\n')
            .replace('$employeesList$', employeesList),
        },
      },
      {
        type: 'actions',
        elements: [
          {
            type: 'button',
            text: {
              type: 'plain_text',
              emoji: true,
              text: leaveRequestTranslations?.[2],
            },
            action_id: 'button-action',
            value: 'review',
            style: 'primary',
            url: link,
          },
        ],
      },
    ],
  };
  return message;
}

function recalculationSucceededMessage(
  notifyEmployees: boolean,
  affectedEmployees: string
) {
  const message = {
    blocks: [
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: leaveRequestTranslations?.[3],
          emoji: true,
        },
      },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `${leaveRequestTranslations?.[4].replace(
            '$notifyEmployees$',
            notifyEmployees ? `\n${absencePolicyTranslations?.[0]}` : ''
          )}${
            affectedEmployees && affectedEmployees.length > 0
              ? `\n*${leaveRequestTranslations?.[6]}:*\n${affectedEmployees}`
              : ''
          }`,
        },
      },
    ],
  };
  return message;
}

async function recalculationFailed(
  env: ISession,
  relativeUrl: string,
  assignedEmployees: Partial<Employee.Employee>[],
  employeesUpdatedSuccessfully: number[],
  nativeNotificationMsg: string,
  recipient: string[]
) {
  const absoluteUrl = `${getBaseURL(env)}${relativeUrl}`;
  const RECALCULATION_FAILED = 'Absences Recalculation Failed';

  const employeesNotUpdated = formatEmployeesInfo(
    assignedEmployees,
    employeesUpdatedSuccessfully,
    true
  );

  // sending a notification of failure
  await sendNotification(
    env,
    RECALCULATION_FAILED,
    recalculationFailedMessage(absoluteUrl, employeesNotUpdated),
    nativeNotificationMsg,
    relativeUrl,
    recipient
  );
}

function companyVacationsMessage(
  companyVacations: Partial<MandatoryLeave.MandatoryLeave>[],
  status: string
) {
  let text = `*${status}:*\n\n`;

  for (const companyVacation of companyVacations) {
    const startDate = formatLongDate(companyVacation?.startDate);
    const endDate = companyVacation?.endDate
      ? formatDateAsYMD(companyVacation?.endDate) ===
        formatDateAsYMD(companyVacation?.startDate)
        ? undefined
        : formatLongDate(companyVacation?.endDate)
      : undefined;

    const description =
      companyVacation?.description && companyVacation.description !== ''
        ? companyVacation.description
        : hrBaseTranslations?.[1];

    text += `${
      endDate
        ? `${startDate} - ${endDate}`
        : `${startDate} ${
            companyVacation?.halfDay ? `(${hrBaseTranslations?.[2]})` : ''
          }`
    }\n*${hrBaseTranslations?.[0]}:* ${description}\n\n`;
  }

  return text;
}

function formatNotificationsMessage(
  mandatoryLeavesObject: MandatoryLeavesObject
) {
  let text = '';

  if (
    mandatoryLeavesObject?.create &&
    mandatoryLeavesObject.create.length > 0
  ) {
    text += companyVacationsMessage(
      mandatoryLeavesObject.create,
      hrBaseTranslations?.[3]
    );
  }

  if (
    mandatoryLeavesObject?.update &&
    mandatoryLeavesObject.update.length > 0
  ) {
    if (text !== '') {
      text += `${'='.repeat(36)}\n\n`;
    }
    text += companyVacationsMessage(
      mandatoryLeavesObject.update,
      hrBaseTranslations?.[4]
    );
  }

  if (
    mandatoryLeavesObject?.delete &&
    mandatoryLeavesObject.delete.length > 0
  ) {
    if (text !== '') {
      text += `${'='.repeat(36)}\n\n`;
    }
    text += companyVacationsMessage(
      mandatoryLeavesObject.delete,
      hrBaseTranslations?.[5]
    );
  }

  const message = {
    blocks: [
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: leaveRequestTranslations?.[5],
          emoji: true,
        },
      },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text,
        },
        accessory: {
          type: 'image',
          image_url:
            'https://api.slack.com/img/blocks/bkb_template_images/notifications.png',
          alt_text: 'calendar thumbnail',
        },
      },
    ],
  };
  return message;
}

async function sendNotification(
  env: ISession,
  notificationCategory: string,
  message: Message,
  nativeNotificationMsg: string,
  relativeUrl: string,
  recipients: string[]
) {
  const notificationClient = await Notification.client(env);
  const singleflightGroup = new Group();

  const notificationCategories = await singleflightGroup.do(
    notificationCategory,
    async () =>
      await NotificationCategory.query(
        env,
        { where: { category: notificationCategory } },
        ['id']
      )
  );

  const notificationCategoryId = notificationCategories?.[0]?.id;

  const slackMsg = JSON.stringify(message);

  if (
    notificationCategoryId &&
    slackMsg &&
    nativeNotificationMsg &&
    recipients.length > 0
  ) {
    recipients = await filterApprovalsInSandbox(env, recipients);
    await notificationClient.sendNotificationsV2(
      notificationCategoryId,
      relativeUrl,
      slackMsg,
      nativeNotificationMsg,
      '',
      recipients,
      { email: false, slack: true }
    );
  }
}

function compareStartDates(
  date1: Date | string | number,
  date2: Date | string | number
) {
  date1 = new Date(date1);
  date2 = new Date(date2);

  return date1 < date2 ? -1 : date1 > date2 ? 1 : 0;
}

export default async function recalculateAssignedEmployeesAbsences(
  env: ISession,
  absencePolicyId: number,
  assignedEmployees: Partial<Employee.Employee>[],
  recipientId?: string,
  mandatoryLeavesObject?: MandatoryLeavesObject
) {
  assignedEmployees.sort((employee1, employee2) =>
    compareStartDates(employee1?.startDate, employee2?.startDate)
  );
  const employeesUpdatedSuccessfully = [];
  const employeesIdsIfUpdated = [];
  leaveRequestTranslations = await getTranslations(
    env,
    [
      'workdays.recalculation.failed',
      'failed.recalculation.markdown.text',
      'company.vacation.tab',
      'workdays.recalculation.suceeded',
      'successful.recalculation.markdown.text',
      'company.vacation.changes',
      'affected.employees',
    ],
    'everest.hr.base/leaveRequest'
  );

  hrBaseTranslations = await getTranslations(
    env,
    [
      'description',
      'no.descripion',
      'half.day',
      'new',
      'modified',
      'cancelled',
      'name',
      'id',
    ],
    'everest.hr.base/hrbase'
  );

  absencePolicyTranslations = await getTranslations(
    env,
    ['notifying.assigned.employees'],
    'everest.hr.base/absencePolicy'
  );

  const userClient = await User.client(env);
  let nativeNotificationMsg =
    'Recalculating requested work days for policy assigned employees background task';

  const relativeUrl = `/templates/everest.hr.base/uinext/absencePolicy/absencePolicy?id=${absencePolicyId}`;
  const RECALCULATION_SUCCEEDED = 'Absences Recalculation Succeeded';
  const NOTIFYING_EMPLOYEES = 'Notifying Assigned Employees';
  const recipient = recipientId
    ? [recipientId]
    : env?.serverEnvironment?.currentUser?.id
      ? [env?.serverEnvironment?.currentUser?.id]
      : [];
  const recipients = [];

  if (assignedEmployees && assignedEmployees.length > 0) {
    for (const assignedEmployee of assignedEmployees) {
      if (mandatoryLeavesObject) {
        const employeeUser = await userClient.read(
          {
            email: assignedEmployee?.email,
          },
          ['userId']
        );

        if (employeeUser) {
          recipients.push(employeeUser?.userId);
        }
      }

      try {
        const { successful, changed } = await recalculateAbsenceDays(
          env,
          assignedEmployee?.id
        );
        if (successful) {
          employeesUpdatedSuccessfully.push(assignedEmployee?.id);
          if (changed) {
            employeesIdsIfUpdated.push(assignedEmployee?.id);
          }
        } else {
          await recalculationFailed(
            env,
            relativeUrl,
            assignedEmployees,
            employeesUpdatedSuccessfully,
            nativeNotificationMsg,
            recipient
          );
          return;
        }
      } catch {
        await recalculationFailed(
          env,
          relativeUrl,
          assignedEmployees,
          employeesUpdatedSuccessfully,
          nativeNotificationMsg,
          recipient
        );
        return;
      }
    }
  }

  const updatedEmployees = formatEmployeesInfo(
    assignedEmployees,
    employeesIdsIfUpdated
  );
  // sending a notification of success
  await sendNotification(
    env,
    RECALCULATION_SUCCEEDED,
    recalculationSucceededMessage(
      mandatoryLeavesObject !== undefined,
      updatedEmployees
    ),
    nativeNotificationMsg,
    relativeUrl,
    recipient
  );

  if (mandatoryLeavesObject) {
    // notifying assigned employees
    nativeNotificationMsg =
      'Notifying assigned employees with company vacations changes';

    await sendNotification(
      env,
      NOTIFYING_EMPLOYEES,
      formatNotificationsMessage(mandatoryLeavesObject),
      nativeNotificationMsg,
      relativeUrl,
      recipients
    );
  }
}

function formatEmployeesInfo(
  employees: Partial<Employee.Employee>[],
  employeesIds: number[],
  notIncluded: boolean = false
) {
  const filteredEmployees = employees.filter(({ id }) => {
    if (notIncluded) {
      return !employeesIds.includes(id);
    }
    return employeesIds.includes(id);
  });
  const employeesList = filteredEmployees
    .map(({ name, id }) => {
      return `*${hrBaseTranslations?.[6]}:* ${name}\n*${hrBaseTranslations?.[7]}:* ${id}\n\n`;
    })
    .join('');
  return employeesList;
}
