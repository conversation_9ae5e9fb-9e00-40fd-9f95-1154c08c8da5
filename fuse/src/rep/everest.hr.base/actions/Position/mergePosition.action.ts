import type { ISession } from '@everestsystems/content-core';
import { EmployeeBusinessObject } from '@pkg/everest.hr.base/types/BOComposition/EmployeeBusinessObject';
import { CompensationBand } from '@pkg/everest.hr.base/types/CompensationBand';
import { HiringManager } from '@pkg/everest.hr.base/types/HiringManager';
import { Job } from '@pkg/everest.hr.base/types/Job';
import { PositionPermissionMapping } from '@pkg/everest.hr.base/types/PositionPermissionMapping';
import { Recruitment } from '@pkg/everest.hr.base/types/Recruitment';

import { Position } from '../../types/Position';

export default async function mergePosition(
  env: ISession,
  sources: Position.UniqueFields['id'][],
  target: Position.UniqueFields['id']
): Promise<void> {
  //if the target exists in the sources throw an error
  if (sources.includes(target)) {
    throw new Error('Target position is in the sources');
  }
  if (!sources || sources?.length === 0) {
    throw new Error('No sources provided');
  }
  if (!target) {
    throw new Error('No target provided');
  }

  const positionsToBeChanged = (await Position.query(
    env,
    {
      where: {
        id: {
          $in: sources,
        },
      },
    },
    []
  )) as Partial<Position.Position>[];

  const { earliestStartDate, latestEndDate } =
    findDateRange(positionsToBeChanged);
  const targetPosition = await Position.read(env, { id: target }, ['id']);
  await Position.update(
    env,
    { id: targetPosition.id },
    { startDate: earliestStartDate, endDate: latestEndDate }
  );

  if (!targetPosition) {
    throw new Error('Target position not found');
  }
  if (!positionsToBeChanged || positionsToBeChanged?.length === 0) {
    throw new Error('No positions found to be changed');
  }

  const promises: Promise<number>[] = positionsToBeChanged.map(
    async (position) => {
      // change the hiring managers
      await HiringManager.updateMany(
        env,
        {
          positionId: position.id,
        },
        {
          positionId: targetPosition.id,
        },
        ['id']
      );

      //change the recuitment
      await Recruitment.updateMany(
        env,
        {
          positionId: position.id,
        },
        {
          positionId: targetPosition.id,
        },
        ['id']
      );

      //change the job
      const updatedJobs = await Job.updateMany(
        env,
        { positionId: position.id },
        {
          positionId: targetPosition.id,
        },
        ['id', 'employeeId']
      );

      //change the employeeBOs
      const employeeIds = updatedJobs.map((job) => job.employeeId);
      const BOUpdatePromises = employeeIds.map(async (employeeId) => {
        await updateEmployeeBO(env, employeeId);
      });
      await Promise.allSettled(BOUpdatePromises);

      //change the PositionPermissionMapping
      await PositionPermissionMapping.updateMany(
        env,
        {
          positionId: position.id,
        },
        {
          positionId: targetPosition.id,
        }
      );

      //change the CompensationBand
      await CompensationBand.updateMany(
        env,
        {
          positionId: position.id,
        },
        {
          positionId: targetPosition.id,
        }
      );

      //delete the position
      await Position.Delete(env, { id: position.id });

      return position.id;
    }
  );

  const results = await Promise.allSettled(promises);
  const errors = results.filter((result) => result.status === 'rejected');
  if (errors.length > 0) {
    throw new Error(
      `Error merging positions: ${errors
        .map((error) => error.reason)
        .join(', ')}`
    );
  }
}

async function updateEmployeeBO(env: ISession, id: number) {
  //Check if BO exists
  const existingBO = await EmployeeBusinessObject.read(
    env,
    {
      headerKey: id,
    },
    ['id']
  );
  // eslint-disable-next-line unicorn/prefer-ternary
  if (existingBO && existingBO.id) {
    //Update employee BO
    await EmployeeBusinessObject.update(
      env,
      {
        headerKey: id,
      },
      {
        operationBusinessEvent: 'Changed',
        operationBusinessDescription: 'Job Position information updated',
      }
    );
  } else {
    //Create employee BO
    await EmployeeBusinessObject.create(env, {
      headerKey: id,
      operationBusinessEvent: 'Changed',
      operationBusinessDescription: 'Job Position information updated',
    });
  }
}

function findDateRange(positions: Partial<Position.Position>[]): {
  earliestStartDate: Date | undefined;
  latestEndDate: Date | undefined;
} {
  if (positions.length === 0) {
    return {
      earliestStartDate: undefined,
      latestEndDate: undefined,
    };
  }

  let earliestStartDate: Date | undefined;
  let latestEndDate: Date | undefined;

  // Check if any endDate is undefined or missing
  const hasUndefinedEndDate = positions.some(
    (position) => position.endDate === undefined || position.endDate === null
  );

  // If any endDate is undefined/missing, set latestEndDate to undefined
  if (hasUndefinedEndDate) {
    latestEndDate = null;
  }

  for (const position of positions) {
    // Find earliest start date
    if (
      position.startDate &&
      (!earliestStartDate ||
        new Date(position.startDate) < new Date(earliestStartDate))
    ) {
      earliestStartDate = position.startDate;
    }

    // Find latest end date (only if no undefined endDates exist)
    if (
      !hasUndefinedEndDate &&
      position.endDate &&
      (!latestEndDate || new Date(position.endDate) > new Date(latestEndDate))
    ) {
      latestEndDate = position.endDate;
    }
  }

  return {
    earliestStartDate,
    latestEndDate,
  };
}
