// @i18n:requestDocument
import type { ISession } from '@everestsystems/content-core';
import { getTranslations, NotFoundError } from '@everestsystems/content-core';
import type { Entity } from '@pkg/everest.base/types/Entity';
import type { EmployeeDocumentTypes } from '@pkg/everest.hr.base/types/EmployeeDocumentTypes';
import { EmployeeRequestedDocument } from '@pkg/everest.hr.base/types/EmployeeRequestedDocument';
import type { Job } from '@pkg/everest.hr.base/types/Job';
import type { Person } from '@pkg/everest.hr.base/types/Person';
import type { Salary } from '@pkg/everest.hr.base/types/Salary';
import { DateTime } from 'luxon';

import { Employee } from '../../types/Employee';
import modifyRequestedDocumentAction from './modifyRequestedDocument.action';
// eslint-disable-next-line @typescript-eslint/no-explicit-any
type Any = any;

type payload = {
  filledText: string;
  fromPayload: boolean;
  warnings: string[];
  salaryData?: string;
};

type config = {
  dateFormat?: string;
  approve?: boolean;
  preview?: boolean;
};

type RequestedDocumentFileType = {
  file?: Any;
  fileName: string;
  fileId?: string;
  payload?: payload;
};

export default async function generateRequestedDocumentPDF(
  env: ISession,
  id: number,
  config: config,
  payload: payload
): Promise<RequestedDocumentFileType> | never {
  let requestedDoc: Partial<EmployeeRequestedDocument.EmployeeRequestedDocument>;
  let documentType: Partial<EmployeeDocumentTypes.EmployeeDocumentTypes>;
  let salaryItem: Partial<Salary.Salary>;
  let filledText: string;
  let entityItem: Partial<Entity.Entity>;
  let requestedDocObject: Any = { salary: undefined, tasks: undefined };
  let salaryData: string;
  let grades: {
    title: string;
    grade: string;
  }[];
  let i18n: Record<string, string> = {};
  const requiredKeys = [];
  const warnings = [];

  const { approve, preview } = config || {};
  const dateFormat = config?.dateFormat || 'MM/dd/yyyy';

  if (id) {
    [requestedDoc] = await EmployeeRequestedDocument.query(
      env,
      { where: { id } },
      [
        'id',
        'employeeDocumentTypeId',
        'employeeId',
        'withSalary',
        'createdDate',
        'tasks',
        'grades',
        'EmployeeRequestedDocument-EmployeeDocumentTypeId.templateFileId',
        'EmployeeRequestedDocument-EmployeeDocumentTypeId.label',
        'EmployeeRequestedDocument-EmployeeDocumentTypeId.includeTasks',
        'EmployeeRequestedDocument-EmployeeDocumentTypeId.needManagerGrades',
        'EmployeeRequestedDocument-EmployeeDocumentTypeId.templateText',
      ]
    );
  }

  if (!!requestedDoc && requestedDoc?.id) {
    documentType =
      requestedDoc?.['EmployeeRequestedDocument-EmployeeDocumentTypeId'];
  }
  const templateFileId = documentType?.templateFileId;
  if (!templateFileId) {
    throw new Error(
      'PDF Template not Found. Upload a Template File in the Document Type'
    );
  }
  const employeeQuery = await Employee.query(
    env,
    { where: { id: +requestedDoc?.employeeId } },
    [
      'id',
      'personId',
      'startDate',
      'endDate',
      'entityId',
      'Job-Employee.positionName',
      'Job-Employee.startDate',
      'Person-Employee.name',
      'Person-Employee.gender',
      'Person-Employee.dateOfBirth',
      ...(requestedDoc.withSalary
        ? [
            'Salary-Employee.baseSalary',
            'Salary-Employee.unit',
            'Salary-Employee.startDate',
            'Salary-Employee.endDate',
            'Employee-Entity.currency',
          ]
        : []),
    ]
  );
  if (!employeeQuery?.[0]) {
    throw new NotFoundError(
      `Employee id=${requestedDoc?.employeeId} not found`
    );
  }
  const employee: Partial<Employee.Employee> =
    employeeQuery?.[0] as unknown as Partial<Employee.Employee>;

  const person: Partial<Person.Person> = employee?.['Person-Employee'];

  const jobData: Partial<Job.Job> = employee?.['Job-Employee']?.sort?.(
    (a, b) => new Date(b.startDate).getTime() - new Date(a.startDate).getTime()
  )?.[0];

  if (requestedDoc.withSalary || documentType.needManagerGrades) {
    const orderedIds = [
      'salary',
      'veryGood',
      'good',
      'satisfactory',
      'sufficient',
      'overallAssessment',
      'performance',
      'qualityOfWork',
      'socialBehavior',
      'technicalKnowledge',
      'workEthic',
      'workUnderPressure',
    ];
    const translations = await getTranslations(
      env,
      [
        'requestedDoc.sallary.follows',
        'grades.veryGood',
        'grades.good',
        'grades.satisfactory',
        'grades.sufficient',
        'requestDocument.overallAssessment',
        'requestDocument.performance',
        'requestDocument.qualityOfWork',
        'requestDocument.socialBehavior',
        'requestDocument.technicalKnowledge',
        'requestDocument.workEthic',
        'requestDocument.workUnderPressure',
      ],
      'everest.hr.base/requestDocument'
    );

    i18n = Object.fromEntries(
      orderedIds.map((id, index) => [id, translations[index]])
    );
  }

  if (payload) {
    filledText = payload.filledText;
    requestedDocObject.fromPayload = true;
  } else {
    if (requestedDoc.withSalary) {
      salaryItem = employee?.['Salary-Employee']?.filter?.(
        (item) =>
          new Date(item.startDate) <= new Date(requestedDoc.createdDate) &&
          (!item.endDate ||
            new Date(item.endDate) >= new Date(requestedDoc.createdDate))
      )?.[0];

      entityItem = employee?.['Employee-Entity'] || {};

      if (salaryItem) {
        salaryData = `${i18n?.salary} ${salaryItem?.baseSalary} ${entityItem?.currency} ${salaryItem?.unit}`;
      } else {
        warnings.push(`Missing: Salary`);
      }
    }

    if (documentType.includeTasks) {
      requiredKeys.push('tasks');
      requestedDocObject.tasks =
        requestedDoc?.tasks
          ?.trimEnd?.()
          ?.split('\n')
          .map((task) => {
            return {
              title: `${task}`,
            };
          }) || [];
    }
    if (documentType.needManagerGrades) {
      requiredKeys.push('grades');
      const gradeOrder = [
        'technicalKnowledge',
        'performance',
        'workEthic',
        'qualityOfWork',
        'workUnderPressure',
        'socialBehavior',
        'overallAssessment',
      ];
      const gradeLabels = {
        1: 'veryGood',
        2: 'good',
        3: 'satisfactory',
        4: 'sufficient',
      };
      grades = Object.entries(requestedDoc?.grades)
        .map(([title, value]) => ({
          key: title,
          title: i18n?.[title] || title,
          grade: i18n?.[gradeLabels[value]] || gradeLabels[value],
        }))
        .sort((a, b) => gradeOrder.indexOf(a.key) - gradeOrder.indexOf(b.key));
      requestedDocObject.grades = grades;
    }
    requestedDocObject = {
      ...requestedDocObject,
      employeeName: person.name,
      birthDate: DateTime.fromJSDate(person.dateOfBirth).toFormat(dateFormat),
      employmentStartDate: DateTime.fromJSDate(employee.startDate).toFormat(
        dateFormat
      ),
      employmentEndDate: employee.endDate
        ? DateTime.fromJSDate(employee.endDate).toFormat(dateFormat)
        : '',
      job: jobData.positionName,
      date: DateTime.fromJSDate(new Date()).toFormat(dateFormat),
      fromPayload: false,
    };
    requiredKeys.push(
      'employeeName',
      'birthDate',
      'employmentStartDate',
      'job',
      'date'
    );

    for (const iterator of requiredKeys) {
      if (!requestedDocObject[iterator]) {
        warnings.push(`Missing: ${iterator}`);
      }
    }

    filledText = fillText(documentType.templateText, requestedDocObject);
  }

  let pdfBuffer;
  try {
    const filledDocx = await env.generatorEngine.instantiateTemplate(
      {
        payload: {
          _type: 'rawXml',
          xml: convertRichTextToOOXML(filledText),
          replaceParagraph: true,
        },
        date: DateTime.fromJSDate(new Date()).toFormat(dateFormat),
      },
      templateFileId?.toString?.()
    );

    pdfBuffer = await env?.generatorEngine?.convertToPdf(filledDocx);
  } catch (error) {
    throw new Error(`Could not create a PDF due to: ${error}`);
  }

  const fileName = `${documentType.label}_${person.name}__unsigned.pdf`;
  let savedFile;
  if (approve) {
    await modifyRequestedDocumentAction(env, requestedDoc.id, 'hrApprove');
  }
  if (preview) {
    [savedFile] = await env.fileStorage.uploadFile([
      { fileName, mimetype: 'application/pdf', body: pdfBuffer },
    ]);
    return {
      fileId: savedFile?.fileId,
      fileName,
      payload: {
        filledText,
        salaryData,
        warnings,
        fromPayload: requestedDocObject?.fromPayload || false,
      },
    };
  } else {
    return {
      file: pdfBuffer,
      fileName,
    };
  }
}
interface RichTextSegment {
  text: string;
  bold?: boolean;
  italic?: boolean;
}

function parseRichText(input: string): RichTextSegment[] {
  const regex = /(\*\*([^*]+)\*\*)|(\*([^*]+)\*)|(\n)|([^\n*]+)/g;
  const segments: RichTextSegment[] = [];
  let match;

  while ((match = regex.exec(input)) !== null) {
    if (match[1]) {
      segments.push({ text: match[2], bold: true });
    } else if (match[3]) {
      segments.push({ text: match[4], italic: true });
    } else if (match[5]) {
      segments.push({ text: '\n' });
    } else if (match[6]) {
      segments.push({ text: match[6] });
    }
  }

  return segments;
}

function convertRichTextToOOXML(input: string): string {
  const segments = parseRichText(input);
  let ooxml = '<w:p>';

  for (const segment of segments) {
    if (segment.text === '\n') {
      ooxml += '<w:r><w:br/></w:r>';
      continue;
    }

    ooxml += '<w:r>';

    if (segment.bold || segment.italic) {
      ooxml += '<w:rPr>';
      if (segment.bold) {
        ooxml += '<w:b/>';
      }
      if (segment.italic) {
        ooxml += '<w:i/>';
      }
      ooxml += '</w:rPr>';
    }

    ooxml += `<w:t xml:space="preserve">${segment.text}</w:t>`;
    ooxml += '</w:r>';
  }

  ooxml += '</w:p>';
  return ooxml;
}

function fillText(templateText: string, data: Any): string {
  let result = templateText || '';

  // Process array iterations first ({{#array}}...{{/}})
  const arrayPattern = /{{#([^}]+)}}([\S\s]*?){{\/}}/g;
  result = result.replaceAll(arrayPattern, (match, arrayName, content) => {
    const array = getNestedValue(data, arrayName);

    if (!array || !Array.isArray(array)) {
      return ''; // If array doesn't exist or is not an array, return empty string
    }

    // Join without adding any extra whitespace
    return array
      .map((item) => {
        // Create a merged context to allow accessing both item properties and global data
        const context = { ...data, ...item };

        // Replace placeholders in the content for each array item
        let itemContent = content;
        const placeholderPattern = /{{([^}]+)}}/g;
        itemContent = itemContent.replaceAll(
          placeholderPattern,
          (placeholder, key) => {
            const value = getNestedValue(context, key);
            // Handle null/undefined values properly
            return value && value !== null ? String(value) : placeholder;
          }
        );
        return itemContent;
      })
      .join('');
  });

  // Process simple replacements ({{value}})
  const placeholderPattern = /{{([^}]+)}}/g;
  result = result.replaceAll(placeholderPattern, (match, key) => {
    const value = getNestedValue(data, key);
    // Handle null/undefined values properly
    return value && value !== null ? String(value) : match;
  });

  return result;
}

function getNestedValue(obj: Any, path: string): Any {
  const keys = path.split('.');
  let result = obj;

  for (const key of keys) {
    if (result === undefined || result === null) {
      return undefined;
    }

    result = result[key.trim()];
  }

  return result;
}
