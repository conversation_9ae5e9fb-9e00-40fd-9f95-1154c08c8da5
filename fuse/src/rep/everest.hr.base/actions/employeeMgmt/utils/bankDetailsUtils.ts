import type { ISession } from '@everestsystems/content-core';
import { BankDetails } from '@pkg/everest.fin.base/types/BankDetails';
import type { EvstEmployeeTransformedData } from '@pkg/everest.hr.base/types/composites/EmployeeTransformedData';
import { Employee } from '@pkg/everest.hr.base/types/Employee';

export async function createBankDetails(
  env: ISession,
  data: EvstEmployeeTransformedData
): Promise<Partial<BankDetails.BankDetails>> {
  const bankData = {
    bankAccountNumber: data?.employeeBankDetails?.bankAccountNumber,
    bankAccountType: data?.employeeBankDetails?.bankAccountType,
    bankAccountHolderName: data?.employeeBankDetails?.bankAccountHolderName,
    bankAccountNumberType: data?.employeeBankDetails?.bankAccountNumberType,
  } as BankDetails.CreationFields;

  const bankDetails = await BankDetails.create(env, bankData, ['id']);
  await Employee.update(
    env,
    { id: data.id },
    { bankDetailsId: bankDetails.id },
    ['id']
  );
  return bankDetails;
}

export async function updateBankDetails(
  env: ISession,
  data: EvstEmployeeTransformedData
): Promise<Partial<BankDetails.BankDetails>> {
  const bankData = {
    bankAccountNumber: data?.employeeBankDetails?.bankAccountNumber,
    bankAccountType: data?.employeeBankDetails?.bankAccountType,
    bankAccountHolderName: data?.employeeBankDetails?.bankAccountHolderName,
    bankAccountNumberType: data?.employeeBankDetails?.bankAccountNumberType,
  } as Partial<BankDetails.BankDetails>;

  const bankDetails = await BankDetails.update(
    env,
    { id: data?.employeeBankDetails?.id },
    bankData,
    ['id']
  );
  await Employee.update(
    env,
    { id: data.id },
    { bankDetailsId: bankDetails.id },
    ['id']
  );
  return bankDetails;
}
