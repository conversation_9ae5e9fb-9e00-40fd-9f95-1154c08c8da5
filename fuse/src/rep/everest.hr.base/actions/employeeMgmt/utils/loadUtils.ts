import { type ISession, ValidationError } from '@everestsystems/content-core';
import type { Address } from '@pkg/everest.base/types/Address';
import type { EvstAddressTransformedData } from '@pkg/everest.base/types/composites/AddressTransformedData';
import type { EvstLoadFunctionReturnType } from '@pkg/everest.base/types/composites/LoadFunctionReturnType';
import type { EvstEmployeeTransformedData } from '@pkg/everest.hr.base/types/composites/EmployeeTransformedData';
import { Employee } from '@pkg/everest.hr.base/types/Employee';
import { omit } from 'lodash';

import { createAddress, updateAddress } from './addressUtils';
import { createBankDetails, updateBankDetails } from './bankDetailsUtils';
import { createEmployee, updateEmployee } from './employeeUtils';
import { createJob, updateJob } from './jobUtils';
import { createPerson, updatePerson } from './personUtils';

export enum Mode {
  Create,
  Update,
}

export enum LoadMessage {
  SUCCESS = 'Person Loaded Successfully',
  UNEXPECTED_ERROR = 'Unexpected error during initialization',
  UNKNOWN_ERROR = 'Unknown error',
  CREATE_ERROR = 'Failed to create person',
  UPDATE_ERROR = 'Failed to update person',
}

export async function validateUpdateEmployee(
  env: ISession,
  data: EvstEmployeeTransformedData
): Promise<void> {
  if (!data.id) {
    throw new ValidationError('Transformed Data has no id', 'MODE_ERROR');
  }

  // check if employee exists if id is available
  const employee = await Employee.read(env, { id: data.id }, ['id']);
  if (!employee) {
    throw new ValidationError('Person not found', 'MODE_ERROR');
  }
  // check if required fields exist
  if (!data?.personFirstName || !data?.personLastName) {
    const fields = [];
    if (!data?.personFirstName) {
      fields.push('First Name');
    }
    if (!data?.personLastName) {
      fields.push('Last Name');
    }
    const error = `Person record does not have the following field(s): ${fields.join(
      ', '
    )}`;
    throw new ValidationError(error, 'MISSING_FIELDS_ERROR');
  }
}

export async function runCreateFlow(
  env: ISession,
  data: EvstEmployeeTransformedData
): Promise<Partial<Employee.Employee>> {
  // Create Home Address
  let homeAddress: Partial<Address.Address> | undefined;
  if (
    data?.homeAddress &&
    !Object.values(
      omit(data?.homeAddress, ['id', 'line2', 'attention', 'stateProvince'])
    ).includes(undefined) &&
    !Object.values(
      omit(data?.homeAddress, ['id', 'line2', 'attention', 'stateProvince'])
    ).includes(null) &&
    !Object.values(
      omit(data?.homeAddress, ['id', 'line2', 'attention', 'stateProvince'])
    ).includes('')
  ) {
    homeAddress = await createAddress(env, data.homeAddress);
  }

  data = {
    ...data,
    homeAddress: homeAddress as EvstAddressTransformedData,
  };

  // Create Person
  const person = await createPerson(env, data);

  data = {
    ...data,
    personId: person?.id,
  };

  // Create Employee
  const employee = await createEmployee(env, data);

  data = {
    ...data,
    id: employee?.id,
  };

  // Create Job (including Position)
  await createJob(env, data);

  // Create Bank Details
  await createBankDetails(env, data);
  return employee;
}

export async function runUpdateFlow(
  env: ISession,
  data: EvstEmployeeTransformedData
): Promise<Partial<Employee.Employee>> {
  // Create / Update Home Address
  let homeAddress: Partial<Address.Address> | undefined;
  if (
    data?.homeAddress &&
    !Object.values(
      omit(data?.homeAddress, ['id', 'line2', 'attention', 'stateProvince'])
    ).includes(undefined) &&
    !Object.values(
      omit(data?.homeAddress, ['id', 'line2', 'attention', 'stateProvince'])
    ).includes(null) &&
    !Object.values(
      omit(data?.homeAddress, ['id', 'line2', 'attention', 'stateProvince'])
    ).includes('')
  ) {
    homeAddress = await (data?.homeAddress?.id
      ? updateAddress(env, data.homeAddress, data.homeAddress.id)
      : createAddress(env, data.homeAddress));
  }

  data = {
    ...data,
    homeAddress: homeAddress as EvstAddressTransformedData,
  };

  // Create / Update Person
  const person = await (data?.personId
    ? updatePerson(env, data)
    : createPerson(env, data));

  data = {
    ...data,
    personId: person?.id,
  };

  // Update Employee
  const employee = await updateEmployee(env, data);

  // Create / Update Job (including Position)
  if (data?.jobId) {
    await updateJob(env, data);
  } else if (data?.departmentId || data?.positionId) {
    await createJob(env, data);
  } else {
    //do nothing
  }

  // Create / Update Bank Details
  data?.employeeBankDetails?.id
    ? await updateBankDetails(env, data)
    : await createBankDetails(env, data);

  return employee;
}

export function createSuccessResult(
  id: number,
  message?: LoadMessage,
  messageSuffix?: string
): EvstLoadFunctionReturnType {
  const loadDetailMessage = message
    ? `${message}${messageSuffix ? `: ${messageSuffix}` : ''}`
    : null;
  return {
    hasLoadFailed: false,
    loadDetailMessage,
    nodeReference: {
      id,
      modelUrn: Employee.MODEL_URN,
    },
  };
}

export function createFailureResult(
  id: number | undefined,
  messagePrefix: LoadMessage,
  error?: Error
): EvstLoadFunctionReturnType {
  const loadDetailMessage = `${messagePrefix}: ${
    error?.message ?? LoadMessage.UNKNOWN_ERROR
  }`;

  return {
    hasLoadFailed: true,
    loadDetailMessage,
    nodeReference:
      id === undefined
        ? null
        : {
            id,
            modelUrn: Employee.MODEL_URN,
          },
  };
}
