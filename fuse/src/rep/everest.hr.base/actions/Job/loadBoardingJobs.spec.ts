import type { ISession } from '@everestsystems/content-core';

import { Job } from '../../types/Job';
import loadBoardingJobs from './loadBoardingJobs.action';

jest.mock('../../types/Job', () => ({
  Job: {
    query: jest.fn(),
  },
}));

describe('loadBoardingJobs', () => {
  const mockEnv: ISession = {} as ISession;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should call Job.query with correct parameters', async () => {
    const mockWhere = { status: 'active' };
    const mockOrderBy = ['startDate', 'id'];
    const mockJobs = [
      { id: 1, startDate: '2023-01-01', jobManagerDisplayName: '<PERSON>' },
      { id: 2, startDate: '2023-01-02', jobManagerDisplayName: '<PERSON>' },
    ];

    (Job.query as jest.Mock).mockResolvedValue(mockJobs);

    const result = await loadBoardingJobs(mockEnv, mockWhere, mockOrderBy);

    expect(Job.query).toHaveBeenCalledWith(
      mockEnv,
      {
        where: mockWhere,
        orderBy: mockOrderBy,
      },
      [
        'id',
        'startDate',
        'jobManagerDisplayName',
        'positionName',
        'departmentName',
        'employeeName',
        'endDate',
        'employeeId',
        'labels',
        'Job-OnboardingTemplate.name',
        'Job-OffboardingTemplate.name',
      ]
    );
    expect(result).toEqual(mockJobs);
  });

  it('should call Job.query without orderBy if not provided', async () => {
    const mockWhere = { status: 'inactive' };
    const mockJobs = [
      {
        id: 3,
        startDate: '2023-02-01',
        jobManagerDisplayName: 'Alice Johnson',
      },
    ];

    (Job.query as jest.Mock).mockResolvedValue(mockJobs);

    const result = await loadBoardingJobs(mockEnv, mockWhere);

    expect(Job.query).toHaveBeenCalledWith(
      mockEnv,
      {
        where: mockWhere,
        orderBy: undefined,
      },
      [
        'id',
        'startDate',
        'jobManagerDisplayName',
        'positionName',
        'departmentName',
        'employeeName',
        'endDate',
        'employeeId',
        'labels',
        'Job-OnboardingTemplate.name',
        'Job-OffboardingTemplate.name',
      ]
    );
    expect(result).toEqual(mockJobs);
  });

  it('should handle empty result from Job.query', async () => {
    const mockWhere = { status: 'completed' };
    const mockJobs = [];

    (Job.query as jest.Mock).mockResolvedValue(mockJobs);

    const result = await loadBoardingJobs(mockEnv, mockWhere);

    expect(Job.query).toHaveBeenCalled();
    expect(result).toEqual([]);
  });

  it('should handle error from Job.query', async () => {
    const mockWhere = { status: 'error' };
    const mockError = new Error('Database error');

    (Job.query as jest.Mock).mockRejectedValue(mockError);

    await expect(loadBoardingJobs(mockEnv, mockWhere)).rejects.toThrow(
      'Database error'
    );
  });

  it('should pass empty where object if not provided', async () => {
    const mockJobs = [
      { id: 4, startDate: '2023-03-01', jobManagerDisplayName: 'Bob Williams' },
    ];

    (Job.query as jest.Mock).mockResolvedValue(mockJobs);

    await loadBoardingJobs(mockEnv, {});

    expect(Job.query).toHaveBeenCalledWith(
      mockEnv,
      {
        where: {},
        orderBy: undefined,
      },
      expect.any(Array)
    );
  });
});
