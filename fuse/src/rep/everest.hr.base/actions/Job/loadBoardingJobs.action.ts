import type { ISession } from '@everestsystems/content-core';

import { Job } from '../../types/Job';

export default async function loadBoardingJobs(
  env: ISession,
  where?: Record<string, unknown>,
  orderBy?: string[]
): Promise<unknown> {
  const jobs = await Job.query(
    env,
    {
      where,
      orderBy,
    },
    [
      'id',
      'startDate',
      'jobManagerDisplayName',
      'positionName',
      'departmentName',
      'employeeName',
      'endDate',
      'employeeId',
      'labels',
      'Job-OnboardingTemplate.name',
      'Job-OffboardingTemplate.name',
    ]
  );
  return jobs;
}
