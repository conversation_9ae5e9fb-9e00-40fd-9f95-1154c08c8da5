import type {
  <PERSON>tch<PERSON><PERSON><PERSON>,
  IControllerEnv,
  ISession,
  ServerEnvironmentProvider,
  ServerEnvironmentService,
} from '@everestsystems/content-core';
import { getTranslations, Group } from '@everestsystems/content-core';
import { Notification } from '@pkg/everest.appserver/types/Notification';
import { NotificationCategory } from '@pkg/everest.appserver/types/NotificationCategory';

import { Applicant } from '../../types/Applicant';
import { ApplicantStages } from '../../types/ApplicantStages';
import { InterviewFeedback } from '../../types/InterviewFeedback';
import checkApplicantCompatibility from './checkApplicantCompatibility.action';

type Message = {
  blocks: {
    type?: string;
    text?: {
      type: string;
      text: string;
      emoji?: boolean;
    };
    elements?: {
      type: string;
      text: {
        type: string;
        emoji: boolean;
        text: string;
      };
      action_id: string;
      value: string;
      style: string;
      url: string;
    }[];
  }[];
};

type ExtendedApplicant = Partial<Applicant.Applicant> & {
  data?: Partial<Applicant.Applicant>;
};

const nativeNotificationMsg =
  "Rechecking applicants' compatibility in the background";

const OPERATION_FINISHED = 'Compatibility Recheck Finished';

let hrBaseTranslations: string[];

export default async function redoCompatibilityChecks(
  env: ISession &
    IControllerEnv &
    FetchProvider &
    ServerEnvironmentProvider &
    ServerEnvironmentService,
  recipientId: string | undefined,
  relativeUrl: string,
  applicants: ExtendedApplicant[],
  jobDescription: string | null,
  viabilityCriteria: string | null
) {
  hrBaseTranslations = await getTranslations(
    env,
    [
      'name',
      'id',
      'compatibility.recheck.finished',
      'compatibility.results.update.failed',
      'compatibility.results.update.successful',
    ],
    'everest.hr.base/hrbase'
  );

  const recipient = recipientId ? [recipientId] : [];

  if (
    jobDescription === undefined ||
    jobDescription === '' ||
    viabilityCriteria === undefined ||
    viabilityCriteria === ''
  ) {
    const applicantsIds = applicants.map(({ id }) => id);
    await Applicant.updateMany(
      env,
      { id: { $in: applicantsIds } },
      { compatibilitySummary: undefined, compatibilityScore: undefined }
    );
    // notification for a successful deletion of old compatibility results
    const sectionMessage = hrBaseTranslations?.[4];
    const message = notificationMessage(sectionMessage);
    await sendNotification(env, message, relativeUrl, recipient);
    return;
  }

  const assessmentMappings = {
    '++': 2,
    '+': 1,
    '/': 0,
    '-': -1,
    '--': -2,
  };
  const applicantsFailedChecks = [];

  for (const applicant of applicants) {
    const applicantId = applicant?.id ?? applicant?.data?.id;
    const applicantName = applicant?.name ?? applicant?.data?.name;
    const resume = applicant?.resume ?? applicant?.data?.resume;
    const shortIntroduction =
      applicant?.shortIntroduction ?? applicant?.data?.shortIntroduction;
    if (!resume || !shortIntroduction) {
      continue;
    }
    const applicantStages = await ApplicantStages.query(
      env,
      { where: { applicantId, stageName: { $ne: null } } },
      ['note']
    );

    const stagesNotes: string[] = [];
    if (applicantStages && applicantStages.length > 0) {
      for (const stage of applicantStages) {
        if (stage?.note) {
          stagesNotes.push(stage.note);
        }
      }
    }

    const interviewFeedbacks = await InterviewFeedback.query(
      env,
      { where: { applicantId } },
      ['feedbackText', 'overallAssessment']
    );

    const feedbacks: string[] = [];
    let assessment = 0;
    if (interviewFeedbacks && interviewFeedbacks.length > 0) {
      for (const feedback of interviewFeedbacks) {
        if (feedback?.feedbackText) {
          feedbacks.push(feedback.feedbackText);
        }
        if (feedback?.overallAssessment) {
          assessment += assessmentMappings[feedback.overallAssessment];
        }
      }
    }

    const inputData = {
      fields: {
        jobDescription:
          jobDescription === null
            ? applicant?.data?.['Applicant-Recruitment']?.['jobDescription']
            : jobDescription,
        viabilityCriteria:
          viabilityCriteria === null
            ? applicant?.data?.['Applicant-Recruitment']?.['viabilityCriteria']
            : viabilityCriteria,
        applicantCriteria: shortIntroduction,
        fileId: resume,
        stagesNotes,
        feedbacks,
        overallAssessment:
          feedbacks.length > 0
            ? `${(assessment / (2 * feedbacks.length)) * 100}% Good`
            : null,
      },
    };

    try {
      const compatibilityResponse = (await checkApplicantCompatibility(
        env,
        inputData
      )) as unknown as string;
      const result = JSON.parse(compatibilityResponse);
      const { compatibilitySummary, compatibilityScore } = result;
      if (compatibilitySummary && compatibilityScore) {
        await Applicant.update(
          env,
          { id: applicantId },
          { compatibilitySummary, compatibilityScore }
        );
      }
    } catch {
      applicantsFailedChecks.push({ id: applicantId, name: applicantName });
    }
  }

  if (applicantsFailedChecks.length > 0) {
    // failed notification for some applicants
    const applicantsList = formatApplicantsInfo(applicantsFailedChecks);
    const sectionMessage = hrBaseTranslations?.[3]
      .replace('*Applicants List:*', '\n*Applicants List:*\n')
      .replace('$applicantsList$', applicantsList);
    const message = notificationMessage(sectionMessage);
    await sendNotification(env, message, relativeUrl, recipient);
    return;
  }
  // success notification for all applicants
  const sectionMessage = hrBaseTranslations?.[4];
  const message = notificationMessage(sectionMessage);
  await sendNotification(env, message, relativeUrl, recipient);
}

function formatApplicantsInfo(applicants: Partial<Applicant.Applicant>[]) {
  const applicantsList = applicants
    .map(({ name, id }) => {
      return `*${hrBaseTranslations?.[0]}:* ${name}\n*${hrBaseTranslations?.[1]}:* ${id}\n\n`;
    })
    .join('');
  return applicantsList;
}

function notificationMessage(section: string) {
  const message = {
    blocks: [
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: hrBaseTranslations?.[2],
          emoji: true,
        },
      },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: section,
        },
      },
    ],
  };
  return message;
}

async function sendNotification(
  env: ISession,
  message: Message,
  relativeUrl: string,
  recipients: string[]
) {
  const notificationClient = await Notification.client(env);
  const singleflightGroup = new Group();

  const notificationCategories = await singleflightGroup.do(
    OPERATION_FINISHED,
    async () =>
      await NotificationCategory.query(
        env,
        { where: { category: OPERATION_FINISHED } },
        ['id']
      )
  );

  const notificationCategoryId = notificationCategories?.[0]?.id;

  const slackMsg = JSON.stringify(message);

  if (
    notificationCategoryId &&
    slackMsg &&
    nativeNotificationMsg &&
    recipients.length > 0
  ) {
    await notificationClient.sendNotificationsV2(
      notificationCategoryId,
      relativeUrl,
      slackMsg,
      nativeNotificationMsg,
      '',
      recipients,
      { email: false, slack: true }
    );
  }
}
