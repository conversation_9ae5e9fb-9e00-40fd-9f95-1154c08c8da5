# Example scenario to explore

> Please do not implement in this file, it is example

## Example HR scenario (Force video recording)

* Create a new Department via API with data below then save as reference name "Department"

   |startDate|endDate                |departmentManager|departmentName                 |departmentCode                     |
   |---------|-----------------------|-----------------|-------------------------------|-----------------------------------|
   |{{today}}|{{date-after: 10 days}}|<PERSON>|{{random-string-with-length:5}}|DPT-{{random-string-with-length:5}}|

* Go to the Departments page
* On the Departments page, click on the "{{Department.departmentCode}} — {{Department.departmentName}}" department name to open the details page
* On the page header, click on the button label "Edit" and wait for the page is loaded completely
* On the Edit Department page, update the Department details with data below

   |Start Date            |End Date               |Department Number                     |Department Name                 |Description                     |Department Head|
   |----------------------|-----------------------|--------------------------------------|--------------------------------|--------------------------------|---------------|
   |{{date-after: 5 days}}|{{date-after: 15 days}}|Number-{{random-string-with-length:5}}|{{random-string-with-length:10}}|{{random-string-with-length:10}}|               |

* On the page header, click on the button label "Cancel" and wait for the page is loaded completely
* On the header, check that the status "Active" is displayed correctly
* Check the Header section is rendered successfully with right department number "{{Department.departmentCode}}" and department name "{{Department.departmentName}}"
* On the Department Details page, check that Department Details section displays information correctly

   |Department Name              |Description|Department Head                 |Number of Employees|Start Date              |End Date              |
   |-----------------------------|-----------|--------------------------------|-------------------|------------------------|----------------------|
   |{{Department.departmentName}}|––         |{{Department.departmentManager}}|0                  |{{Department.startDate}}|{{Department.endDate}}|
