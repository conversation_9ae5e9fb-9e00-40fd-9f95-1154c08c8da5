/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// ******************************************************************************
// Generated Everest Types
// ******************************************************************************

import type { Decimal, RoundingMode } from '@everestsystems/decimal';
import type {
  ArgType,
  Association,
  AssociationManagementArgs,
  AsyncResult,
  Controller,
  ControllerClientProvider,
  CreateOptionsArg,
  DataWithMetadata,
  DeleteOptionsArg,
  FieldSelector,
  Filter,
  flattenFieldSelector,
  GraphInstanceEntry,
  KernelRequestOptions,
  NonAssociationFields,
  Optional,
  PurgeArgs,
  ReadOptionsArg,
  ReadReturnType as ReadReturnTypeGeneric,
  ReadReturnTypeWithAssociations,
  RequireAtLeastOne,
  SemanticSearchArg,
  SemanticSearchRequestOptions,
  TypeSafeQueryArgType,
  UpdateManyOptionsArg,
  UpdateOptionsArg,
  UpsertOptionsArg,
  WriteReturnType,
  WriteReturnTypeWithAssociations,
} from '@everestsystems/content-core';
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";

/** Generated types for View DepartmentView */
export namespace DepartmentView {
  export type DepartmentView = {
    id?: everest_appserver_primitive_Number | null;
    departmentName?: everest_appserver_primitive_Text | null;
    };

  export type UniqueFields = Partial<Pick<DepartmentView, 'id' | 'departmentName'>>;
  export type UniqueWhereInput = Partial<DepartmentView>;
  export type ReadReturnType<U extends string | number | symbol = keyof DepartmentView> = ReadReturnTypeGeneric<DepartmentView, U>;

  export interface IControllerClient extends Omit<Controller<DepartmentView>, 'all' | 'create' | 'createMany' | 'delete' | 'deleteAll' | 'deleteMany' | 'explainRead' | 'exportMany' | 'getHistory' | 'importMany' | 'lock' | 'lookup' | 'purge' | 'semanticSearch' | 'unlock' | 'update' | 'updateMany' | 'upsert' | 'upsertMany'> {}

  /** @return a model controller instance for DepartmentView. */
  export function client(env: ControllerClientProvider) {
    return env.controllerClient<DepartmentView.IControllerClient>(MODEL_URN);
  }

  /** returns a list of all objects matching the requirements. */
  export async function query<U extends keyof DepartmentView, V extends string = 'ALL_FIELDS'>(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<DepartmentView>, 'draft'> | Omit<Omit<TypeSafeQueryArgType<DepartmentView>, 'draft'>, 'where'> & { where?: Partial<DepartmentView> }, fieldlist: ReadonlyArray<U | V> | 'ALL_FIELDS'): Promise<ReadReturnType<V>[]>;
  export async function query(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<DepartmentView>, 'draft'>, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown[]> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).query(args, fields as ReadonlyArray<string>);
  }

  export async function queryWithMetadata<U extends keyof DepartmentView = keyof DepartmentView>(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<DepartmentView>, 'draft'>, fieldlist: ReadonlyArray<U>): Promise<DataWithMetadata<DepartmentView, U>> {
    return (await client(env)).queryWithMetadata(args, fieldlist);
  }

  /** @deprecated Use the alternative overloaded method for reading associations. */
  export async function read<U extends keyof DepartmentView>(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnType<U>>;
  export async function read(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<unknown> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).read(where, fields as ReadonlyArray<keyof DepartmentView>, options);
  }

  export const MODEL_URN: string = 'urn:evst:everest:hr/base:model/view:publicApi/view/DepartmentView/DepartmentView';
}
