/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// ******************************************************************************
// Generated Everest Types
// ******************************************************************************

import type { Decimal, RoundingMode } from '@everestsystems/decimal';
import type {
  ArgType,
  Association,
  AssociationManagementArgs,
  AsyncResult,
  Controller,
  ControllerClientProvider,
  CreateOptionsArg,
  DataWithMetadata,
  DeleteOptionsArg,
  FieldSelector,
  Filter,
  flattenFieldSelector,
  GraphInstanceEntry,
  KernelRequestOptions,
  NonAssociationFields,
  Optional,
  PurgeArgs,
  ReadOptionsArg,
  ReadReturnType as ReadReturnTypeGeneric,
  ReadReturnTypeWithAssociations,
  RequireAtLeastOne,
  SemanticSearchArg,
  SemanticSearchRequestOptions,
  TypeSafeQueryArgType,
  UpdateManyOptionsArg,
  UpdateOptionsArg,
  UpsertOptionsArg,
  WriteReturnType,
  WriteReturnTypeWithAssociations,
} from '@everestsystems/content-core';

import { UIExecutionContext } from '@everestsystems/content-core';
import type { Employee as everest_hr_base_model_node_Employee } from "@pkg/everest.hr.base/types/Employee";
import type { Department as everest_hr_base_model_node_Department } from "@pkg/everest.hr.base/types/Department";
import type { Region as everest_hr_base_model_node_Region } from "@pkg/everest.hr.base/types/Region";
import type { HiringManager as everest_hr_base_model_node_HiringManager } from "@pkg/everest.hr.base/types/HiringManager";
import type { Position as everest_hr_base_model_node_Position } from "@pkg/everest.hr.base/types/Position";
import type { Job as everest_hr_base_model_node_Job } from "@pkg/everest.hr.base/types/Job";
import type { PositionHeadcount as everest_hr_base_model_node_PositionHeadcount } from "@pkg/everest.hr.base/types/PositionHeadcount";
import type { JobHierarchy as everest_hr_base_model_node_JobHierarchy } from "@pkg/everest.hr.base/types/JobHierarchy";
import type { Boarding as everest_hr_base_model_node_Boarding } from "@pkg/everest.hr.base/types/Boarding";
import type { BoardingTemplate as everest_hr_base_model_node_BoardingTemplate } from "@pkg/everest.hr.base/types/BoardingTemplate";
import type orig_createJob from "@pkg/everest.hr.base/actions/Job/createJob.action";
import type orig_getDirectReportsByEmployeeId from "@pkg/everest.hr.base/actions/Job/getDirectReportsByEmployeeId.action";
import type orig_getIndirectReportsByEmployeeId from "@pkg/everest.hr.base/actions/Job/getIndirectReportsByEmployeeId.action";
import type orig_getReportsByEmployeeId from "@pkg/everest.hr.base/actions/Job/getReportsByEmployeeId.action";
import type orig_updateJobs from "@pkg/everest.hr.base/actions/Job/updateJobs.action";
import type orig_terminateJob from "@pkg/everest.hr.base/actions/Job/terminateJob.action";
import type orig_loadOrgChartData from "@pkg/everest.hr.base/actions/Job/loadOrgChartData.action";
import type orig_validateJobPercentage from "@pkg/everest.hr.base/actions/Job/validateJobPercentage.action";
import type orig_reassignDirectReports from "@pkg/everest.hr.base/actions/Job/reassignDirectReports.action";
import type orig_headcount from "@pkg/everest.hr.base/analytics/actions/headcount.action";
import type orig_hasNotActivePrimaryJob from "@pkg/everest.hr.base/actions/Job/hasNotActivePrimaryJob.action";
import type orig_validateDates from "@pkg/everest.hr.base/actions/Job/validateDates.action";
import type orig_updateJob from "@pkg/everest.hr.base/actions/Job/updateJob.action";
import type orig_addJobToEmployee from "@pkg/everest.hr.base/actions/Job/addJobToEmployee.action";
import type orig_beforeUpdateOrCreatePermissionNode from "@pkg/everest.hr.base/actions/shared/beforeUpdateOrCreatePermissionNode.action";
import type orig_loadBoardableJobs from "@pkg/everest.hr.base/actions/Job/loadBoardableJobs.action";
import type orig_boardJob from "@pkg/everest.hr.base/actions/Job/boardJob.action";
import type orig_loadBoardingJobs from "@pkg/everest.hr.base/actions/Job/loadBoardingJobs.action";

/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */

type RequiredContext = {
  data: Record<string, any>;
  actions: {
    run: (...args: any[]) => any;
  };
};

type ApiError = {
  message: string;
  name: string;
  errorCode: string;
  stack?: string;
};

type InnerPromiseType<T> = T extends Promise<infer R> ? R : T;

type ActionResultOrError<T1 extends string | number | symbol, T2> = {[P in T1]?: T2} & {error?: ApiError};

type ArrayIfNeeded<C extends RequiredContext, K extends keyof C['data'], T> = C['data'][K] extends UIExecutionContext.UIModelNodeListType<any> ? T[] : T;

export namespace JobUI {
  /**
   * Job Node connects the employee with their positions
   */
  export type JobWithAssociation = Job & {
    ["Job-Employee"]?: Association<everest_hr_base_model_node_Employee.EmployeeWithAssociation>;
    ["Employee"]?: Association<everest_hr_base_model_node_Employee.EmployeeWithAssociation>;
    ["Job-Department"]?: Association<everest_hr_base_model_node_Department.DepartmentWithAssociation>;
    ["Job-Region"]?: Association<everest_hr_base_model_node_Region.RegionWithAssociation>;
    ["HiringManager-Job"]?: Association<everest_hr_base_model_node_HiringManager.HiringManagerWithAssociation>;
    ["Job-Position"]?: Association<everest_hr_base_model_node_Position.PositionWithAssociation>;
    ["Job-JobManager"]?: Association<everest_hr_base_model_node_Job.JobWithAssociation>;
    ["HeadCount-Job"]?: Association<everest_hr_base_model_node_PositionHeadcount.PositionHeadcountWithAssociation>;
    ["JobHierarchy-Job"]?: Association<everest_hr_base_model_node_JobHierarchy.JobHierarchyWithAssociation>;
    ["NodeId-Job"]?: Association<everest_hr_base_model_node_JobHierarchy.JobHierarchyWithAssociation>;
    ["ParentId-Job"]?: Association<everest_hr_base_model_node_JobHierarchy.JobHierarchyWithAssociation>;
    ["Boarding-Job"]?: Association<everest_hr_base_model_node_Boarding.BoardingWithAssociation>[];
    ["Job-OnboardingTemplate"]?: Association<everest_hr_base_model_node_BoardingTemplate.BoardingTemplateWithAssociation>;
    ["Job-OffboardingTemplate"]?: Association<everest_hr_base_model_node_BoardingTemplate.BoardingTemplateWithAssociation>;
    };
  export interface IControllerClient extends everest_hr_base_model_node_Job.IControllerClient {}

  export type Job = everest_hr_base_model_node_Job.Job;
  export type CreationFields = Pick<Job, 'uuid' | 'externalId' | 'active' | 'operationBusinessEvent' | 'operationBusinessDescription' | 'assignmentType' | 'departmentId' | 'employeeId' | 'endDate' | 'isPrimary' | 'jobManager' | 'jobNumber' | 'offboardingStatus' | 'offboardingTemplateId' | 'onboardingStatus' | 'onboardingTemplateId' | 'percentage' | 'positionId' | 'reassignmentComplete' | 'regionId' | 'replacementManagerId' | 'salary' | 'startDate'>;
  export type UniqueFields = Pick<Job, 'id' | 'uuid' | 'jobNumber'>;
  export type UniqueWhereInput = (RequireAtLeastOne<UniqueFields>) & Partial<Job>;
  export type ReadReturnType<U extends string | number | symbol = keyof Job> = ReadReturnTypeGeneric<Job, U>;

  class ActionRequest<C extends RequiredContext, FuncReturnType> {
    constructor(private readonly context: C, private readonly partialPayload: Record<string, any>) {
    }

    private async __run__(payload: any) {
      return this.context.actions.run(payload);
    }

    payload<K extends keyof C['data']>(nodeAlias: K): Record<string, any> {
      return {[nodeAlias]: this.partialPayload};
    }

    run<K extends keyof C['data']>(nodeAlias: K): Promise<ActionResultOrError<K, ArrayIfNeeded<C, K, InnerPromiseType<FuncReturnType>>>> {
      const payload = this.payload(nodeAlias);
      return this.__run__(payload);
    }
  }

  type func_createJob = typeof orig_createJob;
  /** @deprecated use ```client.createJob``` instead */
  export function createJob<C extends RequiredContext>(context: C, args: { job: Parameters<func_createJob>[1] }): ActionRequest<C, ReturnType<func_createJob>> {
    const partialPayload = {action: 'createJob', data: args}

    return new ActionRequest<C, ReturnType<func_createJob>>(context, partialPayload);
  }
  type func_getDirectReportsByEmployeeId = typeof orig_getDirectReportsByEmployeeId;
  /** @deprecated use ```client.getDirectReportsByEmployeeId``` instead */
  export function getDirectReportsByEmployeeId<C extends RequiredContext>(context: C, args: { employeeId: Parameters<func_getDirectReportsByEmployeeId>[1]; where?: Parameters<func_getDirectReportsByEmployeeId>[2]; orderBy?: Parameters<func_getDirectReportsByEmployeeId>[3] }): ActionRequest<C, ReturnType<func_getDirectReportsByEmployeeId>> {
    const partialPayload = {action: 'getDirectReportsByEmployeeId', data: args}

    return new ActionRequest<C, ReturnType<func_getDirectReportsByEmployeeId>>(context, partialPayload);
  }
  type func_getIndirectReportsByEmployeeId = typeof orig_getIndirectReportsByEmployeeId;
  /** @deprecated use ```client.getIndirectReportsByEmployeeId``` instead */
  export function getIndirectReportsByEmployeeId<C extends RequiredContext>(context: C, args: { employeeId: Parameters<func_getIndirectReportsByEmployeeId>[1]; where?: Parameters<func_getIndirectReportsByEmployeeId>[2]; orderBy?: Parameters<func_getIndirectReportsByEmployeeId>[3] }): ActionRequest<C, ReturnType<func_getIndirectReportsByEmployeeId>> {
    const partialPayload = {action: 'getIndirectReportsByEmployeeId', data: args}

    return new ActionRequest<C, ReturnType<func_getIndirectReportsByEmployeeId>>(context, partialPayload);
  }
  type func_getReportsByEmployeeId = typeof orig_getReportsByEmployeeId;
  /** @deprecated use ```client.getReportsByEmployeeId``` instead */
  export function getReportsByEmployeeId<C extends RequiredContext>(context: C, args: { employeeId: Parameters<func_getReportsByEmployeeId>[1]; showIndirectReports: Parameters<func_getReportsByEmployeeId>[2]; where?: Parameters<func_getReportsByEmployeeId>[3]; orderBy?: Parameters<func_getReportsByEmployeeId>[4] }): ActionRequest<C, ReturnType<func_getReportsByEmployeeId>> {
    const partialPayload = {action: 'getReportsByEmployeeId', data: args}

    return new ActionRequest<C, ReturnType<func_getReportsByEmployeeId>>(context, partialPayload);
  }
  type func_updateJobs = typeof orig_updateJobs;
  /** @deprecated use ```client.updateJobs``` instead */
  export function updateJobs<C extends RequiredContext>(context: C, args: { employeeId: Parameters<func_updateJobs>[1]; createJobs?: Parameters<func_updateJobs>[2]; updateJobs?: Parameters<func_updateJobs>[3]; deleteJobs?: Parameters<func_updateJobs>[4] }): ActionRequest<C, ReturnType<func_updateJobs>> {
    const partialPayload = {action: 'updateJobs', data: args}

    return new ActionRequest<C, ReturnType<func_updateJobs>>(context, partialPayload);
  }
  type func_terminateJob = typeof orig_terminateJob;
  /** @deprecated use ```client.terminateJob``` instead */
  export function terminateJob<C extends RequiredContext>(context: C, args: { job: Parameters<func_terminateJob>[1]; substituteId?: Parameters<func_terminateJob>[2] }): ActionRequest<C, ReturnType<func_terminateJob>> {
    const partialPayload = {action: 'terminateJob', data: args}

    return new ActionRequest<C, ReturnType<func_terminateJob>>(context, partialPayload);
  }
  type func_loadOrgChartData = typeof orig_loadOrgChartData;
  /** @deprecated use ```client.loadOrgChartData``` instead */
  export function loadOrgChartData<C extends RequiredContext>(context: C): ActionRequest<C, ReturnType<func_loadOrgChartData>> {
    const partialPayload = {action: 'loadOrgChartData', data: {}}

    return new ActionRequest<C, ReturnType<func_loadOrgChartData>>(context, partialPayload);
  }
  type func_validateJobPercentage = typeof orig_validateJobPercentage;
  /** @deprecated use ```client.validateJobPercentage``` instead */
  export function validateJobPercentage<C extends RequiredContext>(context: C, args: { percentage: Parameters<func_validateJobPercentage>[1]; employeeId: Parameters<func_validateJobPercentage>[2] }): ActionRequest<C, ReturnType<func_validateJobPercentage>> {
    const partialPayload = {action: 'validateJobPercentage', data: args}

    return new ActionRequest<C, ReturnType<func_validateJobPercentage>>(context, partialPayload);
  }
  type func_reassignDirectReports = typeof orig_reassignDirectReports;
  /** @deprecated use ```client.reassignDirectReports``` instead */
  export function reassignDirectReports<C extends RequiredContext>(context: C, args: { jobId: Parameters<func_reassignDirectReports>[1]; replacementManagerId: Parameters<func_reassignDirectReports>[2]; jobsToReassign: Parameters<func_reassignDirectReports>[3] }): ActionRequest<C, ReturnType<func_reassignDirectReports>> {
    const partialPayload = {action: 'reassignDirectReports', data: args}

    return new ActionRequest<C, ReturnType<func_reassignDirectReports>>(context, partialPayload);
  }
  type func_headcount = typeof orig_headcount;
  /** @deprecated use ```client.headcount``` instead */
  export function headcount<C extends RequiredContext>(context: C, args: { startDate: Parameters<func_headcount>[1]; endDate: Parameters<func_headcount>[2]; entityId?: Parameters<func_headcount>[3]; consolidated?: Parameters<func_headcount>[4] }): ActionRequest<C, ReturnType<func_headcount>> {
    const partialPayload = {action: 'headcount', data: args}

    return new ActionRequest<C, ReturnType<func_headcount>>(context, partialPayload);
  }
  type func_hasNotActivePrimaryJob = typeof orig_hasNotActivePrimaryJob;
  /** @deprecated use ```client.hasNotActivePrimaryJob``` instead */
  export function hasNotActivePrimaryJob<C extends RequiredContext>(context: C, args: { employeeId: Parameters<func_hasNotActivePrimaryJob>[1]; startDate: Parameters<func_hasNotActivePrimaryJob>[2]; jobId?: Parameters<func_hasNotActivePrimaryJob>[3] }): ActionRequest<C, ReturnType<func_hasNotActivePrimaryJob>> {
    const partialPayload = {action: 'hasNotActivePrimaryJob', data: args}

    return new ActionRequest<C, ReturnType<func_hasNotActivePrimaryJob>>(context, partialPayload);
  }
  type func_validateDates = typeof orig_validateDates;
  /** @deprecated use ```client.validateDates``` instead */
  export function validateDates<C extends RequiredContext>(context: C, args: { startDate: Parameters<func_validateDates>[1]; endDate: Parameters<func_validateDates>[2] }): ActionRequest<C, ReturnType<func_validateDates>> {
    const partialPayload = {action: 'validateDates', data: args}

    return new ActionRequest<C, ReturnType<func_validateDates>>(context, partialPayload);
  }
  type func_updateJob = typeof orig_updateJob;
  /** @deprecated use ```client.updateJob``` instead */
  export function updateJob<C extends RequiredContext>(context: C, args: { job: Parameters<func_updateJob>[1] }): ActionRequest<C, ReturnType<func_updateJob>> {
    const partialPayload = {action: 'updateJob', data: args}

    return new ActionRequest<C, ReturnType<func_updateJob>>(context, partialPayload);
  }
  type func_addJobToEmployee = typeof orig_addJobToEmployee;
  /** @deprecated use ```client.addJobToEmployee``` instead */
  export function addJobToEmployee<C extends RequiredContext>(context: C, args: { job: Parameters<func_addJobToEmployee>[1] }): ActionRequest<C, ReturnType<func_addJobToEmployee>> {
    const partialPayload = {action: 'addJobToEmployee', data: args}

    return new ActionRequest<C, ReturnType<func_addJobToEmployee>>(context, partialPayload);
  }
  type func_beforeUpdateOrCreatePermissionNode = typeof orig_beforeUpdateOrCreatePermissionNode;
  /** @deprecated use ```client.beforeUpdateOrCreatePermissionNode``` instead */
  export function beforeUpdateOrCreatePermissionNode<C extends RequiredContext>(context: C, args: { msg: Parameters<func_beforeUpdateOrCreatePermissionNode>[1] }): ActionRequest<C, ReturnType<func_beforeUpdateOrCreatePermissionNode>> {
    const partialPayload = {action: 'beforeUpdateOrCreatePermissionNode', data: args}

    return new ActionRequest<C, ReturnType<func_beforeUpdateOrCreatePermissionNode>>(context, partialPayload);
  }
  type func_loadBoardableJobs = typeof orig_loadBoardableJobs;
  /** @deprecated use ```client.loadBoardableJobs``` instead */
  export function loadBoardableJobs<C extends RequiredContext>(context: C, args: { type: Parameters<func_loadBoardableJobs>[1] }): ActionRequest<C, ReturnType<func_loadBoardableJobs>> {
    const partialPayload = {action: 'loadBoardableJobs', data: args}

    return new ActionRequest<C, ReturnType<func_loadBoardableJobs>>(context, partialPayload);
  }
  type func_boardJob = typeof orig_boardJob;
  /** @deprecated use ```client.boardJob``` instead */
  export function boardJob<C extends RequiredContext>(context: C, args: { jobs: Parameters<func_boardJob>[1]; offboarding: Parameters<func_boardJob>[2] }): ActionRequest<C, ReturnType<func_boardJob>> {
    const partialPayload = {action: 'boardJob', data: args}

    return new ActionRequest<C, ReturnType<func_boardJob>>(context, partialPayload);
  }
  type func_loadBoardingJobs = typeof orig_loadBoardingJobs;
  /** @deprecated use ```client.loadBoardingJobs``` instead */
  export function loadBoardingJobs<C extends RequiredContext>(context: C, args: { where?: Parameters<func_loadBoardingJobs>[1]; orderBy?: Parameters<func_loadBoardingJobs>[2] }): ActionRequest<C, ReturnType<func_loadBoardingJobs>> {
    const partialPayload = {action: 'loadBoardingJobs', data: args}

    return new ActionRequest<C, ReturnType<func_loadBoardingJobs>>(context, partialPayload);
  }
  /** @deprecated - use {@link MODEL_URN} instead */
  export const name = 'urn:evst:everest:hr/base:model/node:Job';
  export const MODEL_URN = 'urn:evst:everest:hr/base:model/node:Job';
  /** @deprecated - use {@link MODEL_URN} instead */
  export const MODEL_ID = 'everest.hr.base/JobModel.Job';
  export const MODEL_UUID = 'b4adb1bf-1319-41b6-a197-de687f3ea79a';

  /** @deprecated use ```client``` instead */
  export function create<C extends RequiredContext, U extends keyof Job>(context: C, input: CreationFields, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: CreateOptionsArg): ActionRequest<C, Promise<WriteReturnType<Job, U>>> {
    const convertedArgs = {data: input, fieldList: fieldlist, options: options} as Record<string, any>
    const partialPayload = {action: 'create', ...convertedArgs}

    return new ActionRequest<C, Promise<WriteReturnType<Job, U>>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function createMany<C extends RequiredContext, U extends keyof Job>(context: C, input: CreationFields[], fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: CreateOptionsArg): ActionRequest<C, Promise<WriteReturnType<Job, U>[]>> {
    const convertedArgs = {data: input, fieldList: fieldlist, options: options} as Record<string, any>
    const partialPayload = {action: 'createMany', ...convertedArgs}

    return new ActionRequest<C, Promise<WriteReturnType<Job, U>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function Delete<C extends RequiredContext>(context: C, where: UniqueWhereInput, options?: DeleteOptionsArg): ActionRequest<C, Promise<Partial<Job>[]>> {
    const convertedArgs = {where: where, options} as Record<string, any>
    const partialPayload = {action: 'delete', ...convertedArgs}

    return new ActionRequest<C, Promise<Partial<Job>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function deleteAll<C extends RequiredContext>(context: C): ActionRequest<C, Promise<Partial<Job>[]>> {
    const convertedArgs = {data: {}} as Record<string, any>
    const partialPayload = {action: 'deleteAll', ...convertedArgs}

    return new ActionRequest<C, Promise<Partial<Job>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function deleteMany<C extends RequiredContext>(context: C, where: Filter<JobWithAssociation>, options?: DeleteOptionsArg): ActionRequest<C, Promise<Partial<Job>[]>>;
  /** @deprecated use ```client``` instead */
  export function deleteMany<C extends RequiredContext>(context: C, where: Partial<Job>, options?: DeleteOptionsArg): ActionRequest<C, Promise<Partial<Job>[]>>;
  /** @deprecated use ```client``` instead */
  export function deleteMany<C extends RequiredContext>(context: C, where: Filter<Job> | Partial<Job>, options?: DeleteOptionsArg): ActionRequest<C, Promise<Partial<Job>[]>> {
    const convertedArgs = {data: {where: where}, options} as Record<string, any>
    const partialPayload = {action: 'deleteMany', ...convertedArgs}

    return new ActionRequest<C, Promise<Partial<Job>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function query<C extends RequiredContext, U extends FieldSelector<JobWithAssociation>>(context: C, args: TypeSafeQueryArgType<JobWithAssociation>, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<ReadReturnTypeWithAssociations<JobWithAssociation, U>[]>>;
  /** @deprecated use ```client``` instead */
  export function query<C extends RequiredContext, U extends keyof Job, V extends string = 'ALL_FIELDS'>(context: C, args: TypeSafeQueryArgType<Job> | Omit<TypeSafeQueryArgType<Job>, 'where'> & { where?: Partial<Job> }, fieldlist: ReadonlyArray<U | V> | 'ALL_FIELDS'): ActionRequest<C, Promise<ReadReturnType<V>[]>>;
  /** @deprecated use ```client``` instead */
  export function query<C extends RequiredContext>(context: C, args: TypeSafeQueryArgType<Job>, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS'): ActionRequest<C, Promise<unknown[]>> {
    const convertedArgs = {data: {...args, fieldList: fieldlist}} as Record<string, any>
    const partialPayload = {action: 'query', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function read<C extends RequiredContext, U extends FieldSelector<JobWithAssociation>>(context: C, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | ReadOptionsArg): ActionRequest<C, Promise<ReadReturnTypeWithAssociations<JobWithAssociation, U>>>;
  /** @deprecated use ```client``` instead */
  export function read<C extends RequiredContext, U extends keyof Job>(context: C, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | ReadOptionsArg): ActionRequest<C, Promise<ReadReturnType<U>>>;
  /** @deprecated use ```client``` instead */
  export function read<C extends RequiredContext>(context: C, where: UniqueWhereInput, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: number | Date | ReadOptionsArg): ActionRequest<C, Promise<unknown>> {
    const convertedArgs = {data: {where: where, fieldList: fieldlist}, options: options} as Record<string, any>
    const partialPayload = {action: 'read', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function update<C extends RequiredContext, U extends FieldSelector<JobWithAssociation>>(context: C, where: UniqueWhereInput, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: UpdateOptionsArg): ActionRequest<C, Promise<WriteReturnTypeWithAssociations<JobWithAssociation, U>>>;
  /** @deprecated use ```client``` instead */
  export function update<C extends RequiredContext, U extends keyof Job>(context: C, where: Partial<Job>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: UpdateOptionsArg): ActionRequest<C, Promise<WriteReturnType<Job, U>>>;
  /** @deprecated use ```client``` instead */
  export function update<C extends RequiredContext>(context: C, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: UpdateOptionsArg): ActionRequest<C, Promise<unknown>> {
    const convertedArgs = {where: where, data: data, fieldList: fieldlist, options} as Record<string, any>
    const partialPayload = {action: 'update', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function updateMany<C extends RequiredContext, U extends FieldSelector<JobWithAssociation>>(context: C, where: Filter<JobWithAssociation>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: UpdateManyOptionsArg): ActionRequest<C, Promise<WriteReturnTypeWithAssociations<JobWithAssociation, U>[]>>;
  /** @deprecated use ```client``` instead */
  export function updateMany<C extends RequiredContext, U extends keyof Job>(context: C, where: Partial<Job>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: UpdateManyOptionsArg): ActionRequest<C, Promise<WriteReturnType<Job, U>[]>>;
  /** @deprecated use ```client``` instead */
  export function updateMany<C extends RequiredContext>(context: C, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: UpdateManyOptionsArg): ActionRequest<C, Promise<unknown[]>> {
    const convertedArgs = {data: {where: where, data: data, fieldList: fieldlist}, options} as Record<string, any>
    const partialPayload = {action: 'updateMany', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function upsert<C extends RequiredContext, U extends keyof Job & string>(context: C, data: Partial<Job>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: UpsertOptionsArg): ActionRequest<C, Promise<WriteReturnType<Job, U>>>;
  /** @deprecated use ```client``` instead */
  export function upsert<C extends RequiredContext, U extends keyof Job & string>(context: C, where: Partial<Job>, data: Partial<Job>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: UpsertOptionsArg): ActionRequest<C, Promise<WriteReturnType<Job, U>>>;
  /** @deprecated use ```client``` instead */
  export function upsert<C extends RequiredContext, U extends keyof Job & string>(context: C, whereOrData: Partial<Job>, dataOrFieldList?: Partial<Job> | ReadonlyArray<U> | 'ALL_FIELDS', fieldlistOrOptions?: ReadonlyArray<U> | 'ALL_FIELDS' | UpsertOptionsArg, maybeOptions?: UpsertOptionsArg): ActionRequest<C, Promise<WriteReturnType<Job, U>>> {
    const convertedArgs = {data: {where: whereOrData, data: dataOrFieldList, fieldList: fieldlistOrOptions}, options: maybeOptions} as Record<string, any>
    const partialPayload = {action: 'upsert', ...convertedArgs}

    return new ActionRequest<C, Promise<WriteReturnType<Job, U>>>(context, partialPayload);
  }
  export namespace client {
    /** Create Job */
    export declare function createJob(args: { job: Parameters<func_createJob>[1] }): ReturnType<func_createJob>
    /** Get DirectReports by Employee Id */
    export declare function getDirectReportsByEmployeeId(args: { employeeId: Parameters<func_getDirectReportsByEmployeeId>[1]; where?: Parameters<func_getDirectReportsByEmployeeId>[2]; orderBy?: Parameters<func_getDirectReportsByEmployeeId>[3] }): ReturnType<func_getDirectReportsByEmployeeId>
    /** Get IndirectReports by Employee Id */
    export declare function getIndirectReportsByEmployeeId(args: { employeeId: Parameters<func_getIndirectReportsByEmployeeId>[1]; where?: Parameters<func_getIndirectReportsByEmployeeId>[2]; orderBy?: Parameters<func_getIndirectReportsByEmployeeId>[3] }): ReturnType<func_getIndirectReportsByEmployeeId>
    /** Get Reports by Employee Id */
    export declare function getReportsByEmployeeId(args: { employeeId: Parameters<func_getReportsByEmployeeId>[1]; showIndirectReports: Parameters<func_getReportsByEmployeeId>[2]; where?: Parameters<func_getReportsByEmployeeId>[3]; orderBy?: Parameters<func_getReportsByEmployeeId>[4] }): ReturnType<func_getReportsByEmployeeId>
    /** Update Job Percentage */
    export declare function updateJobs(args: { employeeId: Parameters<func_updateJobs>[1]; createJobs?: Parameters<func_updateJobs>[2]; updateJobs?: Parameters<func_updateJobs>[3]; deleteJobs?: Parameters<func_updateJobs>[4] }): ReturnType<func_updateJobs>
    /** Terminate secondary Job */
    export declare function terminateJob(args: { job: Parameters<func_terminateJob>[1]; substituteId?: Parameters<func_terminateJob>[2] }): ReturnType<func_terminateJob>
    /** some description */
    export declare function loadOrgChartData(): ReturnType<func_loadOrgChartData>
    /** Validate Job Percentage */
    export declare function validateJobPercentage(args: { percentage: Parameters<func_validateJobPercentage>[1]; employeeId: Parameters<func_validateJobPercentage>[2] }): ReturnType<func_validateJobPercentage>
    /** Create Job */
    export declare function reassignDirectReports(args: { jobId: Parameters<func_reassignDirectReports>[1]; replacementManagerId: Parameters<func_reassignDirectReports>[2]; jobsToReassign: Parameters<func_reassignDirectReports>[3] }): ReturnType<func_reassignDirectReports>
    /** Generates headcount information by department for a given time range */
    export declare function headcount(args: { startDate: Parameters<func_headcount>[1]; endDate: Parameters<func_headcount>[2]; entityId?: Parameters<func_headcount>[3]; consolidated?: Parameters<func_headcount>[4] }): ReturnType<func_headcount>
    /** some description */
    export declare function hasNotActivePrimaryJob(args: { employeeId: Parameters<func_hasNotActivePrimaryJob>[1]; startDate: Parameters<func_hasNotActivePrimaryJob>[2]; jobId?: Parameters<func_hasNotActivePrimaryJob>[3] }): ReturnType<func_hasNotActivePrimaryJob>
    /** some description */
    export declare function validateDates(args: { startDate: Parameters<func_validateDates>[1]; endDate: Parameters<func_validateDates>[2] }): ReturnType<func_validateDates>
    /** Update Job */
    export declare function updateJob(args: { job: Parameters<func_updateJob>[1] }): ReturnType<func_updateJob>
    /** Add Job to Employee */
    export declare function addJobToEmployee(args: { job: Parameters<func_addJobToEmployee>[1] }): ReturnType<func_addJobToEmployee>
    /** Change end date to 23:59:00 before creating or updating employee */
    export declare function beforeUpdateOrCreatePermissionNode(args: { msg: Parameters<func_beforeUpdateOrCreatePermissionNode>[1] }): ReturnType<func_beforeUpdateOrCreatePermissionNode>
    /** some description */
    export declare function loadBoardableJobs(args: { type: Parameters<func_loadBoardableJobs>[1] }): ReturnType<func_loadBoardableJobs>
    /** board Job */
    export declare function boardJob(args: { jobs: Parameters<func_boardJob>[1]; offboarding: Parameters<func_boardJob>[2] }): ReturnType<func_boardJob>
    /** some description */
    export declare function loadBoardingJobs(args: { where?: Parameters<func_loadBoardingJobs>[1]; orderBy?: Parameters<func_loadBoardingJobs>[2] }): ReturnType<func_loadBoardingJobs>
    /** write a new object to the database. */
    export declare function create<U extends keyof Job>(input: CreationFields, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: CreateOptionsArg): Promise<WriteReturnType<Job, U>>

    /** write new objects to the database. */
    export declare function createMany<U extends keyof Job>(input: CreationFields[], fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: CreateOptionsArg): Promise<WriteReturnType<Job, U>[]>

    /** delete the object that is uniquely identified by the where condition. */
    export declare function Delete(where: UniqueWhereInput, options?: DeleteOptionsArg): Promise<Partial<Job>[]>

    /** delete all objects. */
    export declare function deleteAll(): Promise<Partial<Job>[]>

    /** delete the objects that are identified by the where condition. */
    export declare function deleteMany(where: Filter<JobWithAssociation>, options?: DeleteOptionsArg): Promise<Partial<Job>[]>;
    /** @deprecated use the alternative overloaded function */
    export declare function deleteMany(where: Partial<Job>, options?: DeleteOptionsArg): Promise<Partial<Job>[]>;
    export declare function deleteMany(where: Filter<Job> | Partial<Job>, options?: DeleteOptionsArg): Promise<Partial<Job>[]>

    /** returns a list of all objects matching the requirements. */
    export declare function query<U extends FieldSelector<JobWithAssociation>>(args: TypeSafeQueryArgType<JobWithAssociation>, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<ReadReturnTypeWithAssociations<JobWithAssociation, U>[]>;
    /** @deprecated Use the alternative overloaded method for querying associations. */
    export declare function query<U extends keyof Job, V extends string = 'ALL_FIELDS'>(args: TypeSafeQueryArgType<Job> | Omit<TypeSafeQueryArgType<Job>, 'where'> & { where?: Partial<Job> }, fieldlist: ReadonlyArray<U | V> | 'ALL_FIELDS'): Promise<ReadReturnType<V>[]>;
    export declare function query(args: TypeSafeQueryArgType<Job>, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown[]>

    /** reads the object that is uniquely identified by the where condition. */
    export declare function read<U extends FieldSelector<JobWithAssociation>>(where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | ReadOptionsArg): Promise<ReadReturnTypeWithAssociations<JobWithAssociation, U>>;
    /** @deprecated Use the alternative overloaded method for reading associations. */
    export declare function read<U extends keyof Job>(where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | ReadOptionsArg): Promise<ReadReturnType<U>>;
    export declare function read(where: UniqueWhereInput, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: number | Date | ReadOptionsArg): Promise<unknown>

    /** writes modified values back to the database for a given node instance. */
    export declare function update<U extends FieldSelector<JobWithAssociation>>(where: UniqueWhereInput, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: UpdateOptionsArg): Promise<WriteReturnTypeWithAssociations<JobWithAssociation, U>>;
    /** @deprecated Use the alternative overloaded method for writing with associations. */
    export declare function update<U extends keyof Job>(where: Partial<Job>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: UpdateOptionsArg): Promise<WriteReturnType<Job, U>>;
    export declare function update(where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: UpdateOptionsArg): Promise<unknown>

    /** writes modified values back to the database for selected node instances. */
    export declare function updateMany<U extends FieldSelector<JobWithAssociation>>(where: Filter<JobWithAssociation>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: UpdateManyOptionsArg): Promise<WriteReturnTypeWithAssociations<JobWithAssociation, U>[]>;
    /** @deprecated Use the alternative overloaded method for writing with associations. */
    export declare function updateMany<U extends keyof Job>(where: Partial<Job>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: UpdateManyOptionsArg): Promise<WriteReturnType<Job, U>[]>;
    export declare function updateMany(where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: UpdateManyOptionsArg): Promise<unknown[]>

    /** creates or updates object identified by where. */
    export declare function upsert<U extends keyof Job & string>(data: Partial<Job>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: UpsertOptionsArg): Promise<WriteReturnType<Job, U>>;
    /** @deprecated use the overload without an explicit where clause */
    export declare function upsert<U extends keyof Job & string>(where: Partial<Job>, data: Partial<Job>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: UpsertOptionsArg): Promise<WriteReturnType<Job, U>>;
    export declare function upsert<U extends keyof Job & string>(whereOrData: Partial<Job>, dataOrFieldList?: Partial<Job> | ReadonlyArray<U> | 'ALL_FIELDS', fieldlistOrOptions?: ReadonlyArray<U> | 'ALL_FIELDS' | UpsertOptionsArg, maybeOptions?: UpsertOptionsArg): Promise<WriteReturnType<Job, U>>
  }
}
