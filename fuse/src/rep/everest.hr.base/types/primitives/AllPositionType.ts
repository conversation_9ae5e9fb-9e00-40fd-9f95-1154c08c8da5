/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// ******************************************************************************
// Generated Everest Types
// ******************************************************************************

import { Instant, PlainDateTime, PlainDate, PlainTime } from '@everestsystems/datetime';
import { Decimal, RoundingMode } from '@everestsystems/decimal';
import { ConnectivityLayerCallOptions } from '@everestsystems/content-core';
import type { EvstID as everest_appserver_primitive_ID } from "@pkg/everest.appserver/types/primitives/ID";

/**
 * Primitive type.
 *
 * @everest-node-metadata='{"visibility":"public"}'
 */
export type EvstAllPositionType = everest_appserver_primitive_ID;
