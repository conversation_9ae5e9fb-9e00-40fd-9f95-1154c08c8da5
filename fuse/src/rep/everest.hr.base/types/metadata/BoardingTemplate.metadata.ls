package everest.hr.base

node BoardingTemplate {
	description: 'Stores metadata about boarding templates.'
	semantic-key: name, `type`

	field name Text {
		required
		editable
		persisted
		label: 'Name'
	}

	field `type` enum<BoardingType>{
		required
		editable
		persisted
		label: 'Type'
	}

	generated association alias boardingsteps for BoardingSteps-BoardingTemplate

	generated association alias offboardingsteps for OffboardingSteps-BoardingTemplate

	generated association alias job for Job-OnboardingTemplate

	generated association alias job for Job-OffboardingTemplate
	action updateOrCreateBoardingStep(optional onboardingStep: node<BoardingSteps>, optional offboardingStep: node<OffboardingSteps>)
	action updateOrCreateBoardingTemplate(boardingTemplate: node<BoardingTemplate>): Number<Int>
	action deleteBoardingStep(boardingStepId: field<BoardingSteps.id>, boardingType: Text)
	action loadBoardingTemplates(`type`: field<BoardingTemplate.`type`>): Array<node<BoardingTemplate>>
}
