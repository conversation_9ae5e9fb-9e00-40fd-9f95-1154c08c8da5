package everest.hr.base

node Job {
	description: 'Job Node connects the employee with their positions'
	draftable
	default-actions: {
		create, createMany, update,
		updateMany, delete, deleteMany,
		deleteAll, query, read,
		getHistory, lock, unlock,
		upsert, importMany, exportMany,
		purge
	}

	field departmentId primitive<ActiveDepartmentType>{
		editable
		lockable
		persisted
		label: 'Department ID'
	}

	field employeeId Id {
		required
		editable
		lockable
		persisted
		label: 'Employee ID'
	}

	field endDate Date {
		editable
		persisted
		label: 'End Date'
	}

	field jobManager primitive<EmployeeManagerType>{
		editable
		persisted
		label: 'Job Manager'
		description: 'Job ID of job manager'
	}

	field jobNumber number-range<Job>{
		unique
		persisted
		label: 'Job #'
	}

	field percentage Number<Int> {
		editable
		persisted
		label: 'Percentage'
	}

	field positionId primitive<AllPositionType>{
		editable
		lockable
		persisted
		label: 'Position ID'
	}

	field regionId primitive<ActiveRegionType>{
		editable
		persisted
		label: 'Region ID'
	}

	field salary Number<Decimal> {
		editable
		persisted
		label: 'Salary'
	}

	field startDate Date {
		editable
		persisted
		label: 'Start Date'
	}

	field employeeName Relationship<Text> {
		editable
		label: 'Employee Name'
		description: 'Employee name obtained by association between the Job and Employee'
		relation: Employee.name
	}

	field positionName Relationship<Text> {
		editable
		label: 'Position Name'
		relation: position.name
	}

	field isPrimary TrueFalse {
		editable
		persisted
		label: 'Primary Job'
	}

	field assignmentType enum<JobAssignmentType>{
		editable
		persisted
		label: 'Job Assignment Type'
	}

	field category Relationship<Text> {
		editable
		label: 'Category'
		relation: Employee.category
	}

	field departmentName Relationship<Text> {
		editable
		label: 'Department Name'
		relation: department.departmentName
	}

	field status enum<TimelineStatus>{
		formula
		label: 'Status'
	}

	field replacementManagerId primitive<EmployeeReassignManager>{
		editable
		persisted
		label: 'Replacement Manager Id'
		description: 'Job ID of the person replacing this job as job manager'
	}

	field reassignmentComplete TrueFalse {
		editable
		persisted
		label: 'Reassignment Completed'
	}

	field jobManagerName Relationship<Text> {
		editable
		label: 'Job Manager Name'
		relation: jobmanager.Employee.name
	}

	field regionName Relationship<Text> {
		label: 'Region Name'
		relation: region.regionName
	}

	field employeeDisplayName Relationship<Text> {
		label: 'Employee Display Name'
		relation: Employee.displayName
	}

	field jobManagerDisplayName Relationship<Text> {
		label: 'Job Manager Display Name'
		relation: jobmanager.Employee.displayName
	}

	field offboardingStatus enum<EmployeeBoardingStatus>{
		editable
		persisted
		label: 'Off-Boarding Status'
		default: enum-item<EmployeeBoardingStatus.notStarted>
	}

	field onboardingStatus enum<EmployeeBoardingStatus>{
		editable
		persisted
		label: 'On-Boarding Status'
		default: enum-item<EmployeeBoardingStatus.notStarted>
	}

	field onboardingTemplateId Id {
		editable
		persisted
		label: 'Onboarding Template ID'
	}

	field offboardingTemplateId Id {
		editable
		persisted
		label: 'Offboarding Template ID'
	}

	field labels Relationship<Array<Number<Int>>>{
		editable
		label: 'Labels'
		relation: Employee.labels
		default: NaN
	}

	association alias Employee for Job-Employee

	generated association alias department for Job-Department

	generated association alias region for Job-Region

	generated association alias position for Job-Position

	generated association alias jobmanager for Job-JobManager

	generated association alias onboardingtemplate for Job-OnboardingTemplate

	generated association alias offboardingtemplate for Job-OffboardingTemplate

	generated association alias hiringmanager for HiringManager-Job

	generated association alias job for Job-JobManager

	generated association alias headcount for HeadCount-Job

	generated association alias jobhierarchy for JobHierarchy-Job

	generated association alias nodeid for NodeId-Job

	generated association alias parentid for ParentId-Job

	generated association alias boarding for Boarding-Job
	action createJob(job: node<Job>)
	action getDirectReportsByEmployeeId(employeeId: field<Employee.id>, optional where: JSON, optional orderBy: JSON): Array<node<Job>>
	action getIndirectReportsByEmployeeId(employeeId: field<Employee.id>, optional where: JSON, optional orderBy: JSON): Array<node<Job>>
	action getReportsByEmployeeId(employeeId: field<Employee.id>, showIndirectReports: TrueFalse, optional where: JSON, optional orderBy: JSON): Array<node<Job>>
	action updateJobs(employeeId: field<Employee.id>, optional createJobs: Array<node<Job>>, optional updateJobs: Array<node<Job>>, optional deleteJobs: Array<node<Job>>)
	action terminateJob(job: node<Job>, optional substituteId: field<Employee.id>): node<Job>
	query action loadOrgChartData(): Array<node<Job>>
	query action validateJobPercentage(percentage: Number<Int>, employeeId: field<Employee.id>): TrueFalse
	action reassignDirectReports(jobId: field<Job.id>, replacementManagerId: field<Job.replacementManagerId>, jobsToReassign: Array<node<Job>>)
	query action headcount(startDate: DateTime, endDate: DateTime, optional entityId: field<everest.base::Entity.id>, optional consolidated: TrueFalse): JSON
	query action hasNotActivePrimaryJob(employeeId: field<Employee.id>, startDate: field<Job.startDate>, optional jobId: field<Job.id>): TrueFalse
	query action validateDates(startDate: field<Job.startDate>, endDate: field<Job.endDate>): TrueFalse
	action updateJob(job: node<Job>)
	action addJobToEmployee(job: node<Job>): node<Job>
	action beforeUpdateOrCreatePermissionNode(msg: JSON)
	query action loadBoardableJobs(`type`: Text): Array<node<Job>>
	action boardJob(jobs: Array<node<Job>>, offboarding: TrueFalse)
	action loadBoardingJobs(optional where: JSON, optional orderBy: JSON): Array<node<Job>>
}

association Job-Employee {
	source: Job
	sourceField: employeeId
	target: Employee
	targetField: id
	kind: static
	type: reference
	multiplicity: many-to-one
	description: 'Referential association between Job and Employee'
}

association Job-Department {
	source: Job
	sourceField: departmentId
	target: Department
	targetField: id
	kind: static
	type: reference
	multiplicity: many-to-one
	description: ''
}

association Job-Region {
	source: Job
	sourceField: regionId
	target: Region
	targetField: id
	kind: static
	type: reference
	multiplicity: many-to-one
	description: ''
}

association Job-Position {
	source: Job
	sourceField: positionId
	target: Position
	targetField: id
	kind: static
	type: reference
	multiplicity: many-to-one
	description: ''
}

association Job-JobManager {
	source: Job
	sourceField: jobManager
	target: Job
	targetField: id
	kind: static
	type: reference
	multiplicity: many-to-one
	description: ''
}

association Job-OnboardingTemplate {
	source: Job
	sourceField: onboardingTemplateId
	target: BoardingTemplate
	targetField: id
	kind: static
	type: reference
	multiplicity: many-to-one
	description: ''
}

association Job-OffboardingTemplate {
	source: Job
	sourceField: offboardingTemplateId
	target: BoardingTemplate
	targetField: id
	kind: static
	type: reference
	multiplicity: many-to-one
	description: ''
}
