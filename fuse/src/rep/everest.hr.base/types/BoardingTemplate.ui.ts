/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// ******************************************************************************
// Generated Everest Types
// ******************************************************************************

import type { Decimal, RoundingMode } from '@everestsystems/decimal';
import type {
  ArgType,
  Association,
  AssociationManagementArgs,
  AsyncResult,
  Controller,
  ControllerClientProvider,
  CreateOptionsArg,
  DataWithMetadata,
  DeleteOptionsArg,
  FieldSelector,
  Filter,
  flattenFieldSelector,
  GraphInstanceEntry,
  KernelRequestOptions,
  NonAssociationFields,
  Optional,
  PurgeArgs,
  ReadOptionsArg,
  ReadReturnType as ReadReturnTypeGeneric,
  ReadReturnTypeWithAssociations,
  RequireAtLeastOne,
  SemanticSearchArg,
  SemanticSearchRequestOptions,
  TypeSafeQueryArgType,
  UpdateManyOptionsArg,
  UpdateOptionsArg,
  UpsertOptionsArg,
  WriteReturnType,
  WriteReturnTypeWithAssociations,
} from '@everestsystems/content-core';

import { UIExecutionContext } from '@everestsystems/content-core';
import type { BoardingSteps as everest_hr_base_model_node_BoardingSteps } from "@pkg/everest.hr.base/types/BoardingSteps";
import type { OffboardingSteps as everest_hr_base_model_node_OffboardingSteps } from "@pkg/everest.hr.base/types/OffboardingSteps";
import type { Job as everest_hr_base_model_node_Job } from "@pkg/everest.hr.base/types/Job";
import type { BoardingTemplate as everest_hr_base_model_node_BoardingTemplate } from "@pkg/everest.hr.base/types/BoardingTemplate";
import type orig_updateOrCreateBoardingStep from "@pkg/everest.hr.base/actions/Boarding/updateOrCreateBoardingStep.action";
import type orig_updateOrCreateBoardingTemplate from "@pkg/everest.hr.base/actions/Boarding/updateOrCreateBoardingTemplate.action";
import type orig_deleteBoardingStep from "@pkg/everest.hr.base/actions/Boarding/deleteBoardingStep.action";
import type orig_loadBoardingTemplates from "@pkg/everest.hr.base/actions/Boarding/loadBoardingTemplates.action";

/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */

type RequiredContext = {
  data: Record<string, any>;
  actions: {
    run: (...args: any[]) => any;
  };
};

type ApiError = {
  message: string;
  name: string;
  errorCode: string;
  stack?: string;
};

type InnerPromiseType<T> = T extends Promise<infer R> ? R : T;

type ActionResultOrError<T1 extends string | number | symbol, T2> = {[P in T1]?: T2} & {error?: ApiError};

type ArrayIfNeeded<C extends RequiredContext, K extends keyof C['data'], T> = C['data'][K] extends UIExecutionContext.UIModelNodeListType<any> ? T[] : T;

export namespace BoardingTemplateUI {
  /**
   * Stores metadata about boarding templates.
   */
  export type BoardingTemplateWithAssociation = BoardingTemplate & {
    ["BoardingSteps-BoardingTemplate"]?: Association<everest_hr_base_model_node_BoardingSteps.BoardingStepsWithAssociation>[];
    ["OffboardingSteps-BoardingTemplate"]?: Association<everest_hr_base_model_node_OffboardingSteps.OffboardingStepsWithAssociation>[];
    ["Job-OnboardingTemplate"]?: Association<everest_hr_base_model_node_Job.JobWithAssociation>[];
    ["Job-OffboardingTemplate"]?: Association<everest_hr_base_model_node_Job.JobWithAssociation>[];
    };
  export interface IControllerClient extends everest_hr_base_model_node_BoardingTemplate.IControllerClient {}

  export type BoardingTemplate = everest_hr_base_model_node_BoardingTemplate.BoardingTemplate;
  export type SemanticKeyFields = Required<Pick<BoardingTemplate, 'name' | 'type'>>;
  export type CreationFields = Pick<BoardingTemplate, 'uuid' | 'externalId' | 'active' | 'name' | 'type'>;
  export type UniqueFields = Pick<BoardingTemplate, 'id' | 'uuid'>;
  export type UniqueWhereInput = (RequireAtLeastOne<UniqueFields> | SemanticKeyFields) & Partial<BoardingTemplate>;
  export type ReadReturnType<U extends string | number | symbol = keyof BoardingTemplate> = ReadReturnTypeGeneric<BoardingTemplate, U>;

  class ActionRequest<C extends RequiredContext, FuncReturnType> {
    constructor(private readonly context: C, private readonly partialPayload: Record<string, any>) {
    }

    private async __run__(payload: any) {
      return this.context.actions.run(payload);
    }

    payload<K extends keyof C['data']>(nodeAlias: K): Record<string, any> {
      return {[nodeAlias]: this.partialPayload};
    }

    run<K extends keyof C['data']>(nodeAlias: K): Promise<ActionResultOrError<K, ArrayIfNeeded<C, K, InnerPromiseType<FuncReturnType>>>> {
      const payload = this.payload(nodeAlias);
      return this.__run__(payload);
    }
  }

  type func_updateOrCreateBoardingStep = typeof orig_updateOrCreateBoardingStep;
  /** @deprecated use ```client.updateOrCreateBoardingStep``` instead */
  export function updateOrCreateBoardingStep<C extends RequiredContext>(context: C, args: { onboardingStep?: Parameters<func_updateOrCreateBoardingStep>[1]; offboardingStep?: Parameters<func_updateOrCreateBoardingStep>[2] }): ActionRequest<C, ReturnType<func_updateOrCreateBoardingStep>> {
    const partialPayload = {action: 'updateOrCreateBoardingStep', data: args}

    return new ActionRequest<C, ReturnType<func_updateOrCreateBoardingStep>>(context, partialPayload);
  }
  type func_updateOrCreateBoardingTemplate = typeof orig_updateOrCreateBoardingTemplate;
  /** @deprecated use ```client.updateOrCreateBoardingTemplate``` instead */
  export function updateOrCreateBoardingTemplate<C extends RequiredContext>(context: C, args: { boardingTemplate: Parameters<func_updateOrCreateBoardingTemplate>[1] }): ActionRequest<C, ReturnType<func_updateOrCreateBoardingTemplate>> {
    const partialPayload = {action: 'updateOrCreateBoardingTemplate', data: args}

    return new ActionRequest<C, ReturnType<func_updateOrCreateBoardingTemplate>>(context, partialPayload);
  }
  type func_deleteBoardingStep = typeof orig_deleteBoardingStep;
  /** @deprecated use ```client.deleteBoardingStep``` instead */
  export function deleteBoardingStep<C extends RequiredContext>(context: C, args: { boardingStepId: Parameters<func_deleteBoardingStep>[1]; boardingType: Parameters<func_deleteBoardingStep>[2] }): ActionRequest<C, ReturnType<func_deleteBoardingStep>> {
    const partialPayload = {action: 'deleteBoardingStep', data: args}

    return new ActionRequest<C, ReturnType<func_deleteBoardingStep>>(context, partialPayload);
  }
  type func_loadBoardingTemplates = typeof orig_loadBoardingTemplates;
  /** @deprecated use ```client.loadBoardingTemplates``` instead */
  export function loadBoardingTemplates<C extends RequiredContext>(context: C, args: { type: Parameters<func_loadBoardingTemplates>[1] }): ActionRequest<C, ReturnType<func_loadBoardingTemplates>> {
    const partialPayload = {action: 'loadBoardingTemplates', data: args}

    return new ActionRequest<C, ReturnType<func_loadBoardingTemplates>>(context, partialPayload);
  }
  /** @deprecated - use {@link MODEL_URN} instead */
  export const name = 'urn:evst:everest:hr/base:model/node:BoardingTemplate';
  export const MODEL_URN = 'urn:evst:everest:hr/base:model/node:BoardingTemplate';
  /** @deprecated - use {@link MODEL_URN} instead */
  export const MODEL_ID = 'everest.hr.base/BoardingTemplateModel.BoardingTemplate';
  export const MODEL_UUID = '465f937d-accf-4331-809e-d18f35fcdc45';

  /** @deprecated use ```client``` instead */
  export function create<C extends RequiredContext, U extends keyof BoardingTemplate>(context: C, input: CreationFields, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<BoardingTemplate, U>>> {
    const convertedArgs = {data: input, fieldList: fieldlist, options: options} as Record<string, any>
    const partialPayload = {action: 'create', ...convertedArgs}

    return new ActionRequest<C, Promise<WriteReturnType<BoardingTemplate, U>>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function createMany<C extends RequiredContext, U extends keyof BoardingTemplate>(context: C, input: CreationFields[], fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<BoardingTemplate, U>[]>> {
    const convertedArgs = {data: input, fieldList: fieldlist, options: options} as Record<string, any>
    const partialPayload = {action: 'createMany', ...convertedArgs}

    return new ActionRequest<C, Promise<WriteReturnType<BoardingTemplate, U>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function Delete<C extends RequiredContext>(context: C, where: UniqueWhereInput, options?: undefined): ActionRequest<C, Promise<Partial<BoardingTemplate>[]>> {
    const convertedArgs = {where: where, options} as Record<string, any>
    const partialPayload = {action: 'delete', ...convertedArgs}

    return new ActionRequest<C, Promise<Partial<BoardingTemplate>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function deleteAll<C extends RequiredContext>(context: C): ActionRequest<C, Promise<Partial<BoardingTemplate>[]>> {
    const convertedArgs = {data: {}} as Record<string, any>
    const partialPayload = {action: 'deleteAll', ...convertedArgs}

    return new ActionRequest<C, Promise<Partial<BoardingTemplate>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function deleteMany<C extends RequiredContext>(context: C, where: Filter<BoardingTemplateWithAssociation>): ActionRequest<C, Promise<Partial<BoardingTemplate>[]>>;
  /** @deprecated use ```client``` instead */
  export function deleteMany<C extends RequiredContext>(context: C, where: Partial<BoardingTemplate>): ActionRequest<C, Promise<Partial<BoardingTemplate>[]>>;
  /** @deprecated use ```client``` instead */
  export function deleteMany<C extends RequiredContext>(context: C, where: Filter<BoardingTemplate> | Partial<BoardingTemplate>, options?: undefined): ActionRequest<C, Promise<Partial<BoardingTemplate>[]>> {
    const convertedArgs = {data: {where: where}, options} as Record<string, any>
    const partialPayload = {action: 'deleteMany', ...convertedArgs}

    return new ActionRequest<C, Promise<Partial<BoardingTemplate>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function query<C extends RequiredContext, U extends FieldSelector<BoardingTemplateWithAssociation>>(context: C, args: Omit<TypeSafeQueryArgType<BoardingTemplateWithAssociation>, 'draft'>, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<ReadReturnTypeWithAssociations<BoardingTemplateWithAssociation, U>[]>>;
  /** @deprecated use ```client``` instead */
  export function query<C extends RequiredContext, U extends keyof BoardingTemplate, V extends string = 'ALL_FIELDS'>(context: C, args: Omit<TypeSafeQueryArgType<BoardingTemplate>, 'draft'> | Omit<Omit<TypeSafeQueryArgType<BoardingTemplate>, 'draft'>, 'where'> & { where?: Partial<BoardingTemplate> }, fieldlist: ReadonlyArray<U | V> | 'ALL_FIELDS'): ActionRequest<C, Promise<ReadReturnType<V>[]>>;
  /** @deprecated use ```client``` instead */
  export function query<C extends RequiredContext>(context: C, args: Omit<TypeSafeQueryArgType<BoardingTemplate>, 'draft'>, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS'): ActionRequest<C, Promise<unknown[]>> {
    const convertedArgs = {data: {...args, fieldList: fieldlist}} as Record<string, any>
    const partialPayload = {action: 'query', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function read<C extends RequiredContext, U extends FieldSelector<BoardingTemplateWithAssociation>>(context: C, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): ActionRequest<C, Promise<ReadReturnTypeWithAssociations<BoardingTemplateWithAssociation, U>>>;
  /** @deprecated use ```client``` instead */
  export function read<C extends RequiredContext, U extends keyof BoardingTemplate>(context: C, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): ActionRequest<C, Promise<ReadReturnType<U>>>;
  /** @deprecated use ```client``` instead */
  export function read<C extends RequiredContext>(context: C, where: UniqueWhereInput, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): ActionRequest<C, Promise<unknown>> {
    const convertedArgs = {data: {where: where, fieldList: fieldlist}, options: options} as Record<string, any>
    const partialPayload = {action: 'read', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function update<C extends RequiredContext, U extends FieldSelector<BoardingTemplateWithAssociation>>(context: C, where: UniqueWhereInput, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<WriteReturnTypeWithAssociations<BoardingTemplateWithAssociation, U>>>;
  /** @deprecated use ```client``` instead */
  export function update<C extends RequiredContext, U extends keyof BoardingTemplate>(context: C, where: Partial<BoardingTemplate>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<WriteReturnType<BoardingTemplate, U>>>;
  /** @deprecated use ```client``` instead */
  export function update<C extends RequiredContext>(context: C, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: undefined): ActionRequest<C, Promise<unknown>> {
    const convertedArgs = {where: where, data: data, fieldList: fieldlist, options} as Record<string, any>
    const partialPayload = {action: 'update', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function updateMany<C extends RequiredContext, U extends FieldSelector<BoardingTemplateWithAssociation>>(context: C, where: Filter<BoardingTemplateWithAssociation>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<WriteReturnTypeWithAssociations<BoardingTemplateWithAssociation, U>[]>>;
  /** @deprecated use ```client``` instead */
  export function updateMany<C extends RequiredContext, U extends keyof BoardingTemplate>(context: C, where: Partial<BoardingTemplate>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<WriteReturnType<BoardingTemplate, U>[]>>;
  /** @deprecated use ```client``` instead */
  export function updateMany<C extends RequiredContext>(context: C, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: undefined): ActionRequest<C, Promise<unknown[]>> {
    const convertedArgs = {data: {where: where, data: data, fieldList: fieldlist}, options} as Record<string, any>
    const partialPayload = {action: 'updateMany', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function upsert<C extends RequiredContext, U extends keyof BoardingTemplate & string>(context: C, data: Partial<BoardingTemplate>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<BoardingTemplate, U>>>;
  /** @deprecated use ```client``` instead */
  export function upsert<C extends RequiredContext, U extends keyof BoardingTemplate & string>(context: C, where: Partial<BoardingTemplate>, data: Partial<BoardingTemplate>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<BoardingTemplate, U>>>;
  /** @deprecated use ```client``` instead */
  export function upsert<C extends RequiredContext, U extends keyof BoardingTemplate & string>(context: C, whereOrData: Partial<BoardingTemplate>, dataOrFieldList?: Partial<BoardingTemplate> | ReadonlyArray<U> | 'ALL_FIELDS', fieldlistOrOptions?: ReadonlyArray<U> | 'ALL_FIELDS' | Omit<UpsertOptionsArg, 'draft'>, maybeOptions?: Omit<UpsertOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<BoardingTemplate, U>>> {
    const convertedArgs = {data: {where: whereOrData, data: dataOrFieldList, fieldList: fieldlistOrOptions}, options: maybeOptions} as Record<string, any>
    const partialPayload = {action: 'upsert', ...convertedArgs}

    return new ActionRequest<C, Promise<WriteReturnType<BoardingTemplate, U>>>(context, partialPayload);
  }
  export namespace client {
    /** Update or Create Boarding Step */
    export declare function updateOrCreateBoardingStep(args: { onboardingStep?: Parameters<func_updateOrCreateBoardingStep>[1]; offboardingStep?: Parameters<func_updateOrCreateBoardingStep>[2] }): ReturnType<func_updateOrCreateBoardingStep>
    /** Update or Create Boarding Template */
    export declare function updateOrCreateBoardingTemplate(args: { boardingTemplate: Parameters<func_updateOrCreateBoardingTemplate>[1] }): ReturnType<func_updateOrCreateBoardingTemplate>
    /** some description */
    export declare function deleteBoardingStep(args: { boardingStepId: Parameters<func_deleteBoardingStep>[1]; boardingType: Parameters<func_deleteBoardingStep>[2] }): ReturnType<func_deleteBoardingStep>
    /** Load Boarding Templates */
    export declare function loadBoardingTemplates(args: { type: Parameters<func_loadBoardingTemplates>[1] }): ReturnType<func_loadBoardingTemplates>
    /** write a new object to the database. */
    export declare function create<U extends keyof BoardingTemplate>(input: CreationFields, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): Promise<WriteReturnType<BoardingTemplate, U>>

    /** write new objects to the database. */
    export declare function createMany<U extends keyof BoardingTemplate>(input: CreationFields[], fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): Promise<WriteReturnType<BoardingTemplate, U>[]>

    /** delete the object that is uniquely identified by the where condition. */
    export declare function Delete(where: UniqueWhereInput, options?: undefined): Promise<Partial<BoardingTemplate>[]>

    /** delete all objects. */
    export declare function deleteAll(): Promise<Partial<BoardingTemplate>[]>

    /** delete the objects that are identified by the where condition. */
    export declare function deleteMany(where: Filter<BoardingTemplateWithAssociation>): Promise<Partial<BoardingTemplate>[]>;
    /** @deprecated use the alternative overloaded function */
    export declare function deleteMany(where: Partial<BoardingTemplate>): Promise<Partial<BoardingTemplate>[]>;
    export declare function deleteMany(where: Filter<BoardingTemplate> | Partial<BoardingTemplate>, options?: undefined): Promise<Partial<BoardingTemplate>[]>

    /** returns a list of all objects matching the requirements. */
    export declare function query<U extends FieldSelector<BoardingTemplateWithAssociation>>(args: Omit<TypeSafeQueryArgType<BoardingTemplateWithAssociation>, 'draft'>, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<ReadReturnTypeWithAssociations<BoardingTemplateWithAssociation, U>[]>;
    /** @deprecated Use the alternative overloaded method for querying associations. */
    export declare function query<U extends keyof BoardingTemplate, V extends string = 'ALL_FIELDS'>(args: Omit<TypeSafeQueryArgType<BoardingTemplate>, 'draft'> | Omit<Omit<TypeSafeQueryArgType<BoardingTemplate>, 'draft'>, 'where'> & { where?: Partial<BoardingTemplate> }, fieldlist: ReadonlyArray<U | V> | 'ALL_FIELDS'): Promise<ReadReturnType<V>[]>;
    export declare function query(args: Omit<TypeSafeQueryArgType<BoardingTemplate>, 'draft'>, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown[]>

    /** reads the object that is uniquely identified by the where condition. */
    export declare function read<U extends FieldSelector<BoardingTemplateWithAssociation>>(where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnTypeWithAssociations<BoardingTemplateWithAssociation, U>>;
    /** @deprecated Use the alternative overloaded method for reading associations. */
    export declare function read<U extends keyof BoardingTemplate>(where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnType<U>>;
    export declare function read(where: UniqueWhereInput, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<unknown>

    /** writes modified values back to the database for a given node instance. */
    export declare function update<U extends FieldSelector<BoardingTemplateWithAssociation>>(where: UniqueWhereInput, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnTypeWithAssociations<BoardingTemplateWithAssociation, U>>;
    /** @deprecated Use the alternative overloaded method for writing with associations. */
    export declare function update<U extends keyof BoardingTemplate>(where: Partial<BoardingTemplate>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnType<BoardingTemplate, U>>;
    export declare function update(where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: undefined): Promise<unknown>

    /** writes modified values back to the database for selected node instances. */
    export declare function updateMany<U extends FieldSelector<BoardingTemplateWithAssociation>>(where: Filter<BoardingTemplateWithAssociation>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnTypeWithAssociations<BoardingTemplateWithAssociation, U>[]>;
    /** @deprecated Use the alternative overloaded method for writing with associations. */
    export declare function updateMany<U extends keyof BoardingTemplate>(where: Partial<BoardingTemplate>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnType<BoardingTemplate, U>[]>;
    export declare function updateMany(where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: undefined): Promise<unknown[]>

    /** creates or updates object identified by where. */
    export declare function upsert<U extends keyof BoardingTemplate & string>(data: Partial<BoardingTemplate>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<BoardingTemplate, U>>;
    /** @deprecated use the overload without an explicit where clause */
    export declare function upsert<U extends keyof BoardingTemplate & string>(where: Partial<BoardingTemplate>, data: Partial<BoardingTemplate>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<BoardingTemplate, U>>;
    export declare function upsert<U extends keyof BoardingTemplate & string>(whereOrData: Partial<BoardingTemplate>, dataOrFieldList?: Partial<BoardingTemplate> | ReadonlyArray<U> | 'ALL_FIELDS', fieldlistOrOptions?: ReadonlyArray<U> | 'ALL_FIELDS' | Omit<UpsertOptionsArg, 'draft'>, maybeOptions?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<BoardingTemplate, U>>
  }
}
