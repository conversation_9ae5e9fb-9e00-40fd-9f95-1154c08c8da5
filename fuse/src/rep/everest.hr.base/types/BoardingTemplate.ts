/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// ******************************************************************************
// Generated Everest Types
// ******************************************************************************

import type { Decimal, RoundingMode } from '@everestsystems/decimal';
import type {
  ArgType,
  Association,
  AssociationManagementArgs,
  AsyncResult,
  Controller,
  ControllerClientProvider,
  CreateOptionsArg,
  DataWithMetadata,
  DeleteOptionsArg,
  FieldSelector,
  Filter,
  flattenFieldSelector,
  GraphInstanceEntry,
  KernelRequestOptions,
  NonAssociationFields,
  Optional,
  PurgeArgs,
  ReadOptionsArg,
  ReadReturnType as ReadReturnTypeGeneric,
  ReadReturnTypeWithAssociations,
  RequireAtLeastOne,
  SemanticSearchArg,
  SemanticSearchRequestOptions,
  TypeSafeQueryArgType,
  UpdateManyOptionsArg,
  UpdateOptionsArg,
  UpsertOptionsArg,
  WriteReturnType,
  WriteReturnTypeWithAssociations,
} from '@everestsystems/content-core';
import type { BoardingSteps as everest_hr_base_model_node_BoardingSteps } from "@pkg/everest.hr.base/types/BoardingSteps";
import type { OffboardingSteps as everest_hr_base_model_node_OffboardingSteps } from "@pkg/everest.hr.base/types/OffboardingSteps";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstUUID as everest_appserver_primitive_UUID } from "@pkg/everest.appserver/types/primitives/UUID";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { EvstUserEmail as everest_appserver_usermgmt_primitive_user_UserEmail } from "@pkg/everest.appserver.usermgmt/types/primitives/user/UserEmail";
import type { EvstDateTime as everest_appserver_primitive_DateTime } from "@pkg/everest.appserver/types/primitives/DateTime";
import type { EvstBoardingType as everest_hr_base_enum_BoardingType } from "@pkg/everest.hr.base/types/enums/BoardingType";
import type { Job as everest_hr_base_model_node_Job } from "@pkg/everest.hr.base/types/Job";

/**
 * Types for BoardingTemplate
 *
 * @everest-node-metadata='{"visibility":"public"}'
 */
export namespace BoardingTemplate {
  export type SemanticKeyFields = Required<Pick<BoardingTemplate, 'name' | 'type'>>;
  export type CreationFields = Pick<BoardingTemplate, 'uuid' | 'externalId' | 'active' | 'name' | 'type'>;
  export type UniqueFields = Pick<BoardingTemplate, 'id' | 'uuid'>;
  export type UniqueWhereInput = (RequireAtLeastOne<UniqueFields> | SemanticKeyFields) & Partial<BoardingTemplate>;
  export type ReadReturnType<U extends string | number | symbol = keyof BoardingTemplate> = ReadReturnTypeGeneric<BoardingTemplate, U>;

  export interface IControllerClient extends Omit<Controller<BoardingTemplate>, 'all' | 'getHistory' | 'lookup' | 'purge' | 'queryWithMetadata' | 'semanticSearch' | 'upsertMany'> {
    /** Update or Create Boarding Step */
    updateOrCreateBoardingStep(onboardingStep?: everest_hr_base_model_node_BoardingSteps.BoardingSteps, offboardingStep?: everest_hr_base_model_node_OffboardingSteps.OffboardingSteps): Promise<void>;
    /** Update or Create Boarding Template */
    updateOrCreateBoardingTemplate(boardingTemplate: BoardingTemplate): Promise<everest_appserver_primitive_Number>;
    /** some description */
    deleteBoardingStep(boardingStepId: everest_hr_base_model_node_BoardingSteps.BoardingSteps["id"], boardingType: everest_appserver_primitive_Text): Promise<void>;
    /** Load Boarding Templates */
    loadBoardingTemplates(type: BoardingTemplate["type"]): Promise<BoardingTemplate[]>;
  }

  /**
   * Stores metadata about boarding templates.
   */
  export type BoardingTemplate = {
    /** Database generated identifier of this record. */
    id: number;
    /** Database generated version of this record. */
    version?: number;
    uuid?: everest_appserver_primitive_UUID | null;
    /**
     * ID of the system from which this record was sourced
     */
    externalId?: everest_appserver_primitive_Text | null;
    /**
     * Indicates whether this record is usable for business users
     */
    active?: everest_appserver_primitive_TrueFalse | null;
    /**
     * User that created this record
     */
    createdBy: everest_appserver_usermgmt_primitive_user_UserEmail;
    /**
     * Date/time when the record was created
     */
    createdDate: everest_appserver_primitive_DateTime;
    /**
     * User that last modified the record
     */
    lastModifiedBy: everest_appserver_usermgmt_primitive_user_UserEmail;
    /**
     * Date/time of the last modification to the record
     */
    lastModifiedDate: everest_appserver_primitive_DateTime;
    name: everest_appserver_primitive_Text;
    type: everest_hr_base_enum_BoardingType;
    };
  /**
   * Stores metadata about boarding templates.
   */
  export type BoardingTemplateWithAssociation = BoardingTemplate & {
    ["BoardingSteps-BoardingTemplate"]?: Association<everest_hr_base_model_node_BoardingSteps.BoardingStepsWithAssociation>[];
    ["OffboardingSteps-BoardingTemplate"]?: Association<everest_hr_base_model_node_OffboardingSteps.OffboardingStepsWithAssociation>[];
    ["Job-OnboardingTemplate"]?: Association<everest_hr_base_model_node_Job.JobWithAssociation>[];
    ["Job-OffboardingTemplate"]?: Association<everest_hr_base_model_node_Job.JobWithAssociation>[];
    };
  /** @deprecated - use {@link MODEL_URN} instead */
  export const name = 'urn:evst:everest:hr/base:model/node:BoardingTemplate';
  export const MODEL_URN = 'urn:evst:everest:hr/base:model/node:BoardingTemplate';
  /** @deprecated - use {@link MODEL_URN} instead */
  export const MODEL_ID = 'everest.hr.base/BoardingTemplateModel.BoardingTemplate';
  export const MODEL_UUID = '465f937d-accf-4331-809e-d18f35fcdc45';

  /** @return a model controller instance for BoardingTemplate. */
  export function client(env: ControllerClientProvider) {
    return env.controllerClient<BoardingTemplate.IControllerClient>(MODEL_URN);
  }

  /** write a new object to the database. */
  export async function create<U extends keyof BoardingTemplate>(env: ControllerClientProvider, input: CreationFields, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): Promise<WriteReturnType<BoardingTemplate, U>> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).create(input, fields, options);
  }

  /** write new objects to the database. */
  export async function createMany<U extends keyof BoardingTemplate>(env: ControllerClientProvider, input: CreationFields[], fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): Promise<WriteReturnType<BoardingTemplate, U>[]> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).createMany(input, fields, options);
  }

  /** delete the object that is uniquely identified by the where condition. */
  export async function Delete(env: ControllerClientProvider, where: UniqueWhereInput): Promise<Partial<BoardingTemplate>[]> {
    return (await client(env)).delete(where);
  }

  /** delete all objects. */
  export async function deleteAll(env: ControllerClientProvider): Promise<Partial<BoardingTemplate>[]> {
    return (await client(env)).deleteAll();
  }

  /** delete the objects that are identified by the where condition. */
  export async function deleteMany(env: ControllerClientProvider, where: Filter<BoardingTemplateWithAssociation>): Promise<Partial<BoardingTemplate>[]>;
  /** @deprecated use the alternative overloaded function */
  export async function deleteMany(env: ControllerClientProvider, where: Partial<BoardingTemplate>): Promise<Partial<BoardingTemplate>[]>;
  export async function deleteMany(env: ControllerClientProvider, where: Filter<BoardingTemplate> | Partial<BoardingTemplate>): Promise<Partial<BoardingTemplate>[]> {
    return (await client(env)).deleteMany(where as Filter<BoardingTemplate>);
  }

  /** returns a list of all objects matching the requirements. */
  export async function query<U extends FieldSelector<BoardingTemplateWithAssociation>>(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<BoardingTemplateWithAssociation>, 'draft'>, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<ReadReturnTypeWithAssociations<BoardingTemplateWithAssociation, U>[]>;
  /** @deprecated Use the alternative overloaded method for querying associations. */
  export async function query<U extends keyof BoardingTemplate, V extends string = 'ALL_FIELDS'>(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<BoardingTemplate>, 'draft'> | Omit<Omit<TypeSafeQueryArgType<BoardingTemplate>, 'draft'>, 'where'> & { where?: Partial<BoardingTemplate> }, fieldlist: ReadonlyArray<U | V> | 'ALL_FIELDS'): Promise<ReadReturnType<V>[]>;
  export async function query(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<BoardingTemplate>, 'draft'>, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown[]> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).query(args, fields as ReadonlyArray<string>);
  }

  /** reads the object that is uniquely identified by the where condition. */
  export async function read<U extends FieldSelector<BoardingTemplateWithAssociation>>(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnTypeWithAssociations<BoardingTemplateWithAssociation, U>>;
  /** @deprecated Use the alternative overloaded method for reading associations. */
  export async function read<U extends keyof BoardingTemplate>(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnType<U>>;
  export async function read(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<unknown> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).read(where, fields as ReadonlyArray<keyof BoardingTemplate>, options);
  }

  /** writes modified values back to the database for a given node instance. */
  export async function update<U extends FieldSelector<BoardingTemplateWithAssociation>>(env: ControllerClientProvider, where: UniqueWhereInput, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnTypeWithAssociations<BoardingTemplateWithAssociation, U>>;
  /** @deprecated Use the alternative overloaded method for writing with associations. */
  export async function update<U extends keyof BoardingTemplate>(env: ControllerClientProvider, where: Partial<BoardingTemplate>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnType<BoardingTemplate, U>>;
  export async function update(env: ControllerClientProvider, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).update(where, data, fields as ReadonlyArray<keyof BoardingTemplate>);
  }

  /** writes modified values back to the database for selected node instances. */
  export async function updateMany<U extends FieldSelector<BoardingTemplateWithAssociation>>(env: ControllerClientProvider, where: Filter<BoardingTemplateWithAssociation>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnTypeWithAssociations<BoardingTemplateWithAssociation, U>[]>;
  /** @deprecated Use the alternative overloaded method for writing with associations. */
  export async function updateMany<U extends keyof BoardingTemplate>(env: ControllerClientProvider, where: Partial<BoardingTemplate>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnType<BoardingTemplate, U>[]>;
  export async function updateMany(env: ControllerClientProvider, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown[]> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).updateMany(where, data, fields as ReadonlyArray<keyof BoardingTemplate>);
  }

  /** creates or updates object identified by where. */
  export async function upsert<U extends keyof BoardingTemplate & string>(env: ControllerClientProvider, data: Partial<BoardingTemplate>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<BoardingTemplate, U>>;
  /** @deprecated use the overload without an explicit where clause */
  export async function upsert<U extends keyof BoardingTemplate & string>(env: ControllerClientProvider, where: Partial<BoardingTemplate>, data: Partial<BoardingTemplate>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<BoardingTemplate, U>>;
  export async function upsert<U extends keyof BoardingTemplate & string>(env: ControllerClientProvider, whereOrData: Partial<BoardingTemplate>, dataOrFieldList?: Partial<BoardingTemplate> | ReadonlyArray<U> | 'ALL_FIELDS', fieldlistOrOptions?: ReadonlyArray<U> | 'ALL_FIELDS' | Omit<UpsertOptionsArg, 'draft'>, maybeOptions?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<BoardingTemplate, U>> {
    return (await client(env)).upsert(whereOrData, dataOrFieldList, fieldlistOrOptions, maybeOptions);
  }
}
