import type { Department as everest_hr_base_model_node_Department } from "@pkg/everest.hr.base/types/Department";

/**
 * Composite Type of Department Transformed Data
 *
 * @everest-node-metadata='{"visibility":"public"}'
 */
export interface EvstDepartmentTransformedData {
  startDate?: everest_hr_base_model_node_Department.Department["startDate"] | null;
  departmentCode?: everest_hr_base_model_node_Department.Department["departmentCode"] | null;
  id?: everest_hr_base_model_node_Department.Department["id"] | null;
  departmentDescription?: everest_hr_base_model_node_Department.Department["departmentDescription"] | null;
  status?: everest_hr_base_model_node_Department.Department["status"] | null;
  parentId?: everest_hr_base_model_node_Department.Department["id"] | null;
  departmentName: everest_hr_base_model_node_Department.Department["departmentName"];
  endDate?: everest_hr_base_model_node_Department.Department["endDate"] | null;
  externalDepartmentId?: everest_hr_base_model_node_Department.Department["externalDepartmentId"] | null;
  migrationConfigurationId?: everest_hr_base_model_node_Department.Department["migrationConfigurationId"] | null;
}
