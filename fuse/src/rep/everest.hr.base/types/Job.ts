/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// ******************************************************************************
// Generated Everest Types
// ******************************************************************************

import type { Decimal, RoundingMode } from '@everestsystems/decimal';
import type {
  ArgType,
  Association,
  AssociationManagementArgs,
  AsyncResult,
  Controller,
  ControllerClientProvider,
  CreateOptionsArg,
  DataWithMetadata,
  DeleteOptionsArg,
  FieldSelector,
  Filter,
  flattenFieldSelector,
  GraphInstanceEntry,
  KernelRequestOptions,
  NonAssociationFields,
  Optional,
  PurgeArgs,
  ReadOptionsArg,
  ReadReturnType as ReadReturnTypeGeneric,
  ReadReturnTypeWithAssociations,
  RequireAtLeastOne,
  SemanticSearchArg,
  SemanticSearchRequestOptions,
  TypeSafeQueryArgType,
  UpdateManyOptionsArg,
  UpdateOptionsArg,
  UpsertOptionsArg,
  WriteReturnType,
  WriteReturnTypeWithAssociations,
} from '@everestsystems/content-core';
import type { Employee as everest_hr_base_model_node_Employee } from "@pkg/everest.hr.base/types/Employee";
import type { EvstJSON as everest_appserver_primitive_JSON } from "@pkg/everest.appserver/types/primitives/JSON";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { EvstDateTime as everest_appserver_primitive_DateTime } from "@pkg/everest.appserver/types/primitives/DateTime";
import type { Entity as everest_base_model_node_Entity } from "@pkg/everest.base/types/Entity";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstUUID as everest_appserver_primitive_UUID } from "@pkg/everest.appserver/types/primitives/UUID";
import type { EvstUserEmail as everest_appserver_usermgmt_primitive_user_UserEmail } from "@pkg/everest.appserver.usermgmt/types/primitives/user/UserEmail";
import type { EvstJobAssignmentType as everest_hr_base_enum_JobAssignmentType } from "@pkg/everest.hr.base/types/enums/JobAssignmentType";
import type { EvstActiveDepartmentType as everest_hr_base_primitive_ActiveDepartmentType } from "@pkg/everest.hr.base/types/primitives/ActiveDepartmentType";
import type { EvstID as everest_appserver_primitive_ID } from "@pkg/everest.appserver/types/primitives/ID";
import type { EvstDate as everest_appserver_primitive_Date } from "@pkg/everest.appserver/types/primitives/Date";
import type { EvstEmployeeManagerType as everest_hr_base_primitive_EmployeeManagerType } from "@pkg/everest.hr.base/types/primitives/EmployeeManagerType";
import type { EvstEmployeeBoardingStatus as everest_hr_base_enum_EmployeeBoardingStatus } from "@pkg/everest.hr.base/types/enums/EmployeeBoardingStatus";
import type { EvstAllPositionType as everest_hr_base_primitive_AllPositionType } from "@pkg/everest.hr.base/types/primitives/AllPositionType";
import type { EvstActiveRegionType as everest_hr_base_primitive_ActiveRegionType } from "@pkg/everest.hr.base/types/primitives/ActiveRegionType";
import type { EvstEmployeeReassignManager as everest_hr_base_primitive_EmployeeReassignManager } from "@pkg/everest.hr.base/types/primitives/EmployeeReassignManager";
import type { EvstDecimal as everest_appserver_primitive_Decimal } from "@pkg/everest.appserver/types/primitives/Decimal";
import type { EvstTimelineStatus as everest_hr_base_enum_TimelineStatus } from "@pkg/everest.hr.base/types/enums/TimelineStatus";
import type { Department as everest_hr_base_model_node_Department } from "@pkg/everest.hr.base/types/Department";
import type { Region as everest_hr_base_model_node_Region } from "@pkg/everest.hr.base/types/Region";
import type { HiringManager as everest_hr_base_model_node_HiringManager } from "@pkg/everest.hr.base/types/HiringManager";
import type { Position as everest_hr_base_model_node_Position } from "@pkg/everest.hr.base/types/Position";
import type { PositionHeadcount as everest_hr_base_model_node_PositionHeadcount } from "@pkg/everest.hr.base/types/PositionHeadcount";
import type { JobHierarchy as everest_hr_base_model_node_JobHierarchy } from "@pkg/everest.hr.base/types/JobHierarchy";
import type { Boarding as everest_hr_base_model_node_Boarding } from "@pkg/everest.hr.base/types/Boarding";
import type { BoardingTemplate as everest_hr_base_model_node_BoardingTemplate } from "@pkg/everest.hr.base/types/BoardingTemplate";

/**
 * Types for Job
 *
 * @everest-node-metadata='{"visibility":"public"}'
 */
export namespace Job {
  export type CreationFields = Pick<Job, 'uuid' | 'externalId' | 'active' | 'operationBusinessEvent' | 'operationBusinessDescription' | 'assignmentType' | 'departmentId' | 'employeeId' | 'endDate' | 'isPrimary' | 'jobManager' | 'jobNumber' | 'offboardingStatus' | 'offboardingTemplateId' | 'onboardingStatus' | 'onboardingTemplateId' | 'percentage' | 'positionId' | 'reassignmentComplete' | 'regionId' | 'replacementManagerId' | 'salary' | 'startDate'>;
  export type UniqueFields = Pick<Job, 'id' | 'uuid' | 'jobNumber'>;
  export type UniqueWhereInput = (RequireAtLeastOne<UniqueFields>) & Partial<Job>;
  export type ReadReturnType<U extends string | number | symbol = keyof Job> = ReadReturnTypeGeneric<Job, U>;

  export interface IControllerClient extends Omit<Controller<Job>, 'all' | 'lookup' | 'queryWithMetadata' | 'semanticSearch' | 'upsertMany'> {
    /** Create Job */
    createJob(job: Job): Promise<void>;
    /** Get DirectReports by Employee Id */
    getDirectReportsByEmployeeId(employeeId: everest_hr_base_model_node_Employee.Employee["id"], where?: everest_appserver_primitive_JSON, orderBy?: everest_appserver_primitive_JSON): Promise<Job[]>;
    /** Get IndirectReports by Employee Id */
    getIndirectReportsByEmployeeId(employeeId: everest_hr_base_model_node_Employee.Employee["id"], where?: everest_appserver_primitive_JSON, orderBy?: everest_appserver_primitive_JSON): Promise<Job[]>;
    /** Get Reports by Employee Id */
    getReportsByEmployeeId(employeeId: everest_hr_base_model_node_Employee.Employee["id"], showIndirectReports: everest_appserver_primitive_TrueFalse, where?: everest_appserver_primitive_JSON, orderBy?: everest_appserver_primitive_JSON): Promise<Job[]>;
    /** Update Job Percentage */
    updateJobs(employeeId: everest_hr_base_model_node_Employee.Employee["id"], createJobs?: Job[], updateJobs?: Job[], deleteJobs?: Job[]): Promise<void>;
    /** Terminate secondary Job */
    terminateJob(job: Job, substituteId?: everest_hr_base_model_node_Employee.Employee["id"]): Promise<Job>;
    /** some description */
    loadOrgChartData(): Promise<Job[]>;
    /** Validate Job Percentage */
    validateJobPercentage(percentage: everest_appserver_primitive_Number, employeeId: everest_hr_base_model_node_Employee.Employee["id"]): Promise<everest_appserver_primitive_TrueFalse>;
    /** Create Job */
    reassignDirectReports(jobId: Job["id"], replacementManagerId: Job["replacementManagerId"], jobsToReassign: Job[]): Promise<void>;
    /** Generates headcount information by department for a given time range */
    headcount(startDate: everest_appserver_primitive_DateTime, endDate: everest_appserver_primitive_DateTime, entityId?: everest_base_model_node_Entity.Entity["id"], consolidated?: everest_appserver_primitive_TrueFalse): Promise<everest_appserver_primitive_JSON>;
    /** some description */
    hasNotActivePrimaryJob(employeeId: everest_hr_base_model_node_Employee.Employee["id"], startDate: Job["startDate"], jobId?: Job["id"]): Promise<everest_appserver_primitive_TrueFalse>;
    /** some description */
    validateDates(startDate: Job["startDate"], endDate: Job["endDate"]): Promise<everest_appserver_primitive_TrueFalse>;
    /** Update Job */
    updateJob(job: Job): Promise<void>;
    /** Add Job to Employee */
    addJobToEmployee(job: Job): Promise<Job>;
    /** Change end date to 23:59:00 before creating or updating employee */
    beforeUpdateOrCreatePermissionNode(msg: everest_appserver_primitive_JSON): Promise<void>;
    /** some description */
    loadBoardableJobs(type: everest_appserver_primitive_Text): Promise<Job[]>;
    /** board Job */
    boardJob(jobs: Job[], offboarding: everest_appserver_primitive_TrueFalse): Promise<void>;
    /** some description */
    loadBoardingJobs(where?: everest_appserver_primitive_JSON, orderBy?: everest_appserver_primitive_JSON): Promise<Job[]>;
  }

  /**
   * Job Node connects the employee with their positions
   */
  export type Job = {
    /** Database generated identifier of this record. */
    id: number;
    /** Database generated version of this record. */
    version?: number;
    uuid?: everest_appserver_primitive_UUID | null;
    /**
     * ID of the system from which this record was sourced
     */
    externalId?: everest_appserver_primitive_Text | null;
    /**
     * Indicates whether this record is usable for business users
     */
    active?: everest_appserver_primitive_TrueFalse | null;
    /**
     * User that created this record
     */
    createdBy: everest_appserver_usermgmt_primitive_user_UserEmail;
    /**
     * Date/time when the record was created
     */
    createdDate: everest_appserver_primitive_DateTime;
    /**
     * User that last modified the record
     */
    lastModifiedBy: everest_appserver_usermgmt_primitive_user_UserEmail;
    /**
     * Date/time of the last modification to the record
     */
    lastModifiedDate: everest_appserver_primitive_DateTime;
    /**
     * Contains application-specific details about the last operation. Can be listed using the getHistory default action.
     */
    operationBusinessEvent?: everest_appserver_primitive_Text | null;
    /**
     * Contains additional user-provided context for the last operation. Can be listed using the getHistory default action.
     */
    operationBusinessDescription?: everest_appserver_primitive_Text | null;
    assignmentType?: everest_hr_base_enum_JobAssignmentType | null;
    category?: everest_appserver_primitive_Text | null;
    departmentId?: everest_hr_base_primitive_ActiveDepartmentType | null;
    departmentName?: everest_appserver_primitive_Text | null;
    employeeDisplayName?: everest_appserver_primitive_Text | null;
    employeeId: everest_appserver_primitive_ID;
    /**
     * Employee name obtained by association between the Job and Employee
     */
    employeeName?: everest_appserver_primitive_Text | null;
    endDate?: everest_appserver_primitive_Date | null;
    isPrimary?: everest_appserver_primitive_TrueFalse | null;
    /**
     * Job ID of job manager
     */
    jobManager?: everest_hr_base_primitive_EmployeeManagerType | null;
    jobManagerDisplayName?: everest_appserver_primitive_Text | null;
    jobManagerName?: everest_appserver_primitive_Text | null;
    jobNumber?: everest_appserver_primitive_Text | null;
    labels?: everest_appserver_primitive_Number | null;
    offboardingStatus?: everest_hr_base_enum_EmployeeBoardingStatus | null;
    offboardingTemplateId?: everest_appserver_primitive_ID | null;
    onboardingStatus?: everest_hr_base_enum_EmployeeBoardingStatus | null;
    onboardingTemplateId?: everest_appserver_primitive_ID | null;
    percentage?: everest_appserver_primitive_Number | null;
    positionId?: everest_hr_base_primitive_AllPositionType | null;
    positionName?: everest_appserver_primitive_Text | null;
    reassignmentComplete?: everest_appserver_primitive_TrueFalse | null;
    regionId?: everest_hr_base_primitive_ActiveRegionType | null;
    regionName?: everest_appserver_primitive_Text | null;
    /**
     * Job ID of the person replacing this job as job manager
     */
    replacementManagerId?: everest_hr_base_primitive_EmployeeReassignManager | null;
    salary?: everest_appserver_primitive_Decimal | null;
    startDate?: everest_appserver_primitive_Date | null;
    status?: everest_hr_base_enum_TimelineStatus | null;
    };
  /**
   * Job Node connects the employee with their positions
   */
  export type JobWithAssociation = Job & {
    ["Job-Employee"]?: Association<everest_hr_base_model_node_Employee.EmployeeWithAssociation>;
    ["Employee"]?: Association<everest_hr_base_model_node_Employee.EmployeeWithAssociation>;
    ["Job-Department"]?: Association<everest_hr_base_model_node_Department.DepartmentWithAssociation>;
    ["Job-Region"]?: Association<everest_hr_base_model_node_Region.RegionWithAssociation>;
    ["HiringManager-Job"]?: Association<everest_hr_base_model_node_HiringManager.HiringManagerWithAssociation>;
    ["Job-Position"]?: Association<everest_hr_base_model_node_Position.PositionWithAssociation>;
    ["Job-JobManager"]?: Association<JobWithAssociation>;
    ["HeadCount-Job"]?: Association<everest_hr_base_model_node_PositionHeadcount.PositionHeadcountWithAssociation>;
    ["JobHierarchy-Job"]?: Association<everest_hr_base_model_node_JobHierarchy.JobHierarchyWithAssociation>;
    ["NodeId-Job"]?: Association<everest_hr_base_model_node_JobHierarchy.JobHierarchyWithAssociation>;
    ["ParentId-Job"]?: Association<everest_hr_base_model_node_JobHierarchy.JobHierarchyWithAssociation>;
    ["Boarding-Job"]?: Association<everest_hr_base_model_node_Boarding.BoardingWithAssociation>[];
    ["Job-OnboardingTemplate"]?: Association<everest_hr_base_model_node_BoardingTemplate.BoardingTemplateWithAssociation>;
    ["Job-OffboardingTemplate"]?: Association<everest_hr_base_model_node_BoardingTemplate.BoardingTemplateWithAssociation>;
    };
  /** @deprecated - use {@link MODEL_URN} instead */
  export const name = 'urn:evst:everest:hr/base:model/node:Job';
  export const MODEL_URN = 'urn:evst:everest:hr/base:model/node:Job';
  /** @deprecated - use {@link MODEL_URN} instead */
  export const MODEL_ID = 'everest.hr.base/JobModel.Job';
  export const MODEL_UUID = 'b4adb1bf-1319-41b6-a197-de687f3ea79a';

  /** @return a model controller instance for Job. */
  export function client(env: ControllerClientProvider) {
    return env.controllerClient<Job.IControllerClient>(MODEL_URN);
  }

  /** write a new object to the database. */
  export async function create<U extends keyof Job>(env: ControllerClientProvider, input: CreationFields, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: CreateOptionsArg): Promise<WriteReturnType<Job, U>> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).create(input, fields, options);
  }

  /** write new objects to the database. */
  export async function createMany<U extends keyof Job>(env: ControllerClientProvider, input: CreationFields[], fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: CreateOptionsArg): Promise<WriteReturnType<Job, U>[]> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).createMany(input, fields, options);
  }

  /** delete the object that is uniquely identified by the where condition. */
  export async function Delete(env: ControllerClientProvider, where: UniqueWhereInput, options?: DeleteOptionsArg): Promise<Partial<Job>[]> {
    return (await client(env)).delete(where, options);
  }

  /** delete all objects. */
  export async function deleteAll(env: ControllerClientProvider): Promise<Partial<Job>[]> {
    return (await client(env)).deleteAll();
  }

  /** delete the objects that are identified by the where condition. */
  export async function deleteMany(env: ControllerClientProvider, where: Filter<JobWithAssociation>, options?: DeleteOptionsArg): Promise<Partial<Job>[]>;
  /** @deprecated use the alternative overloaded function */
  export async function deleteMany(env: ControllerClientProvider, where: Partial<Job>, options?: DeleteOptionsArg): Promise<Partial<Job>[]>;
  export async function deleteMany(env: ControllerClientProvider, where: Filter<Job> | Partial<Job>, options?: DeleteOptionsArg): Promise<Partial<Job>[]> {
    return (await client(env)).deleteMany(where, options);
  }

  /**
   * purge all objects matching the input.
   */
  export async function purge(env: ControllerClientProvider, args: PurgeArgs): Promise<void> {
    return (await client(env)).purge(args);
  }

  /** returns a list of all objects matching the requirements. */
  export async function query<U extends FieldSelector<JobWithAssociation>>(env: ControllerClientProvider, args: TypeSafeQueryArgType<JobWithAssociation>, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<ReadReturnTypeWithAssociations<JobWithAssociation, U>[]>;
  /** @deprecated Use the alternative overloaded method for querying associations. */
  export async function query<U extends keyof Job, V extends string = 'ALL_FIELDS'>(env: ControllerClientProvider, args: TypeSafeQueryArgType<Job> | Omit<TypeSafeQueryArgType<Job>, 'where'> & { where?: Partial<Job> }, fieldlist: ReadonlyArray<U | V> | 'ALL_FIELDS'): Promise<ReadReturnType<V>[]>;
  export async function query(env: ControllerClientProvider, args: TypeSafeQueryArgType<Job>, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown[]> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).query(args, fields as ReadonlyArray<string>);
  }

  /** reads the object that is uniquely identified by the where condition. */
  export async function read<U extends FieldSelector<JobWithAssociation>>(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | ReadOptionsArg): Promise<ReadReturnTypeWithAssociations<JobWithAssociation, U>>;
  /** @deprecated Use the alternative overloaded method for reading associations. */
  export async function read<U extends keyof Job>(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | ReadOptionsArg): Promise<ReadReturnType<U>>;
  export async function read(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: number | Date | ReadOptionsArg): Promise<unknown> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).read(where, fields as ReadonlyArray<keyof Job>, options);
  }

  /** writes modified values back to the database for a given node instance. */
  export async function update<U extends FieldSelector<JobWithAssociation>>(env: ControllerClientProvider, where: UniqueWhereInput, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: UpdateOptionsArg): Promise<WriteReturnTypeWithAssociations<JobWithAssociation, U>>;
  /** @deprecated Use the alternative overloaded method for writing with associations. */
  export async function update<U extends keyof Job>(env: ControllerClientProvider, where: Partial<Job>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: UpdateOptionsArg): Promise<WriteReturnType<Job, U>>;
  export async function update(env: ControllerClientProvider, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: UpdateOptionsArg): Promise<unknown> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).update(where, data, fields as ReadonlyArray<keyof Job>, options);
  }

  /** writes modified values back to the database for selected node instances. */
  export async function updateMany<U extends FieldSelector<JobWithAssociation>>(env: ControllerClientProvider, where: Filter<JobWithAssociation>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: UpdateManyOptionsArg): Promise<WriteReturnTypeWithAssociations<JobWithAssociation, U>[]>;
  /** @deprecated Use the alternative overloaded method for writing with associations. */
  export async function updateMany<U extends keyof Job>(env: ControllerClientProvider, where: Partial<Job>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: UpdateManyOptionsArg): Promise<WriteReturnType<Job, U>[]>;
  export async function updateMany(env: ControllerClientProvider, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: UpdateManyOptionsArg): Promise<unknown[]> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).updateMany(where, data, fields as ReadonlyArray<keyof Job>, options);
  }

  /** creates or updates object identified by where. */
  export async function upsert<U extends keyof Job & string>(env: ControllerClientProvider, data: Partial<Job>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: UpsertOptionsArg): Promise<WriteReturnType<Job, U>>;
  /** @deprecated use the overload without an explicit where clause */
  export async function upsert<U extends keyof Job & string>(env: ControllerClientProvider, where: Partial<Job>, data: Partial<Job>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: UpsertOptionsArg): Promise<WriteReturnType<Job, U>>;
  export async function upsert<U extends keyof Job & string>(env: ControllerClientProvider, whereOrData: Partial<Job>, dataOrFieldList?: Partial<Job> | ReadonlyArray<U> | 'ALL_FIELDS', fieldlistOrOptions?: ReadonlyArray<U> | 'ALL_FIELDS' | UpsertOptionsArg, maybeOptions?: UpsertOptionsArg): Promise<WriteReturnType<Job, U>> {
    return (await client(env)).upsert(whereOrData, dataOrFieldList, fieldlistOrOptions, maybeOptions);
  }
}
