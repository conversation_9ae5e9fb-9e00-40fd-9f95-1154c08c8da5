package everest.hr.base
@metadata {
	description: 'Composite Type of Department Transformed Data'
	active: true
}
public composite type DepartmentTransformedData {
	@metadata {
		uuid: 'ea83ebf6-c9b5-4b5a-a0a3-6cf5d3ce85ca'
	}
	optional startDate field<Department.startDate>
	@metadata {
		uuid: '7dabb79c-9721-4469-972c-6311efe61563'
	}
	optional departmentCode field<Department.departmentCode>
	@metadata {
		uuid: '4457d587-87d8-43e6-b132-503d635014b4'
	}
	optional id field<Department.id>
	@metadata {
		uuid: '39cadb3c-af74-46ec-8198-0c8172b36de7'
	}
	optional departmentDescription field<Department.departmentDescription>
	@metadata {
		uuid: 'e8d45f9c-a4d0-49b9-8719-fab9f1082ee0'
	}
	optional status field<Department.status>
	@metadata {
		uuid: 'aa333a8a-b24c-47ac-9780-537ddf58392d'
	}
	optional parentId field<Department.id>
	@metadata {
		uuid: '6935e000-ad65-4c33-a90f-b6ae24e7d417'
	}
	departmentName field<Department.departmentName>
	@metadata {
		uuid: '218cf6cf-f390-457b-a6ba-c8f0fba7a3d4'
	}
	optional endDate field<Department.endDate>
	@metadata {
		uuid: 'd7840dce-fc2f-41dd-bbd8-c84119857181'
	}
	optional externalDepartmentId field<Department.externalDepartmentId>
	@metadata {
		uuid: '1fae4622-b2e1-475a-83da-fa8beddecbe9'
	}
	optional migrationConfigurationId field<Department.migrationConfigurationId>
}
