package everest.hr.base
@metadata {
  label: 'Department'
  runValidationForChecktable: false
  editable: false
  visible: true
  required: true
  extraData: """{
  "ui": {
    "view": {
      "display": "${departmentCode} ${departmentName}"
    },
    "edit": {
      "component": "Select",
      "display": "${departmentCode} ${departmentName}"
    }
  },
  "fieldList": [
    "id",
    "departmentName",
    "departmentNumber",
    "departmentCode"
  ],
  "actions": {
    "edit": {
      "query": {
        "orderBy": [
          "departmentCode"
        ],
        "where": {}
      }
    },
    "view": {
      "read": {
        "where": {
          "id": "${field.value}"
        }
      }
    }
  }
}"""
  active: true
  checkTableTarget: field<Department.id>
}
public primitive type AllDepartments extends Id {
}
