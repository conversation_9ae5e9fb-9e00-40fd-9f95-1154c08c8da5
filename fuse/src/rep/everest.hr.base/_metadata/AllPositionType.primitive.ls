package everest.hr.base
@metadata {
  label: 'Position'
  runValidationForChecktable: false
  editable: true
  visible: true
  required: true
  extraData: """{
  "ui": {
    "edit": {
      "component": "Select",
      "display": "${positionNumber} ${name}"
    },
    "view": {
      "display": "${positionNumber} ${name}"
    }
  },
  "actions": {
    "view": {
      "query": {
        "where": {
          "id": "${field.value}"
        }
      }
    },
    "edit": {
      "query": {}
    }
  },
  "fieldList": [
    "id",
    "name",
    "positionNumber"
  ]
}"""
  active: true
  checkTableTarget: field<Position.id>
}
public primitive type AllPositionType extends Id {
}
