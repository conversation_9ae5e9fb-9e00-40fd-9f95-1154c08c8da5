import { getTranslation, METADATA } from '@everestsystems/content-core';
import { User } from '@pkg/everest.appserver.usermgmt/types/User';
import { Employee } from '@pkg/everest.hr.base/types/Employee';
import { Person } from '@pkg/everest.hr.base/types/Person';
import type { editUserDetailPresentation } from '@pkg/everest.hr.base/types/presentations/personModals/edit/editUserDetail';
import { isEmpty, set } from 'lodash';

export class UserDataSource
  implements editUserDetailPresentation.dataSources.user.implementation
{
  private _user: editUserDetailPresentation.dataSources.user.callbacks.query.combinedOutput;

  async query({
    session,
  }: editUserDetailPresentation.dataSources.user.callbacks.query.input): Promise<editUserDetailPresentation.dataSources.user.callbacks.query.combinedOutput> {
    if (this._user) {
      return this._user;
    }

    const isInSb = (session.sandboxService?.currentSandbox?.id ?? 0) > 0;

    // Get the current user
    const currentUser = await User.read(
      session,
      { id: session.serverEnvironment.currentUser.dbId },
      ['id', 'givenName', 'familyName', 'email']
    );

    this._user = {
      id: currentUser.id,
      firstName: currentUser.givenName,
      lastName: currentUser.familyName,
      email: currentUser.email,
      fullName: `${currentUser.givenName} ${currentUser.familyName}`,
      canSetProfileImage: false,
      [METADATA]: {
        firstName: {
          editable: !isInSb,
        },
        lastName: {
          editable: !isInSb,
        },
        profileImageSource: {
          editable: false,
        },
      },
    };

    if (isEmpty(this._user.email)) {
      return this._user;
    }

    // Get Employee data
    const employeeClient = await Employee.client(session);
    const currentEmployee = await employeeClient.getCurrentEmployee(['email']);
    if (!currentEmployee) {
      return this._user;
    }

    // Get person data for avatar photo
    const currentPerson = await Person.query(
      session,
      { where: { email: this._user.email } },
      ['photo', 'photoName']
    );

    const { photo, photoName } = currentPerson[0] ?? {};

    this._user = {
      ...this._user,
      profileImageId: photo,
      profileImageSource: photo
        ? `/api/app/storage/${currentPerson[0].photo}`
        : null,
      profileImageName: photoName,
      canSetProfileImage: !isInSb,
      [METADATA]: {
        ...this._user[METADATA],
        profileImageId: {
          editable: !isInSb,
        },
      },
    };

    return this._user;
  }

  update({
    fieldName,
    newFieldValue,
  }: editUserDetailPresentation.dataSources.user.callbacks.update.input): Promise<editUserDetailPresentation.dataSources.user.callbacks.update.output> {
    switch (fieldName) {
      case 'firstName':
      case 'lastName': {
        set(this._user, [fieldName], newFieldValue);
        set(
          this._user,
          'fullName',
          `${this._user.firstName} ${this._user.lastName}`
        );
        break;
      }
    }
    return Promise.resolve();
  }

  async validate_save?({
    session,
    context,
  }: editUserDetailPresentation.dataSources.user.routines.save.validateInput): Promise<editUserDetailPresentation.dataSources.user.routines.save.validateOutput> {
    if (isEmpty(this._user.firstName)) {
      const validationMessage = await getTranslation(
        session,
        'editUserDetail.validation.firstNameRequired',
        'everest.hr.base/personModals'
      );
      context.addError([], validationMessage, 'firstName');
    }

    if (isEmpty(this._user.lastName)) {
      const validationMessage = await getTranslation(
        session,
        'editUserDetail.validation.lastNameRequired',
        'everest.hr.base/personModals'
      );
      context.addError([], validationMessage, 'lastName');
    }

    if ((session.sandboxService?.currentSandbox?.id ?? 0) > 0) {
      const message = await getTranslation(
        session,
        'editUserDetail.validation.noChangeInSandbox',
        'everest.hr.base/personModals'
      );
      return {
        passed: false,
        message,
      };
    }
  }
  async execute_save({
    session,
  }: editUserDetailPresentation.dataSources.user.routines.save.executeInput): Promise<editUserDetailPresentation.dataSources.user.routines.save.executeOutput> {
    // Update user display name
    await User.update(
      session,
      { id: this._user.id },
      { givenName: this._user.firstName, familyName: this._user.lastName },
      ['uuid']
    );
    // Update user avatar
    if (this._user.canSetProfileImage) {
      await Person.update(
        session,
        { email: this._user.email },
        {
          photo: this._user.profileImageId,
          photoName: this._user.profileImageName,
        },
        ['uuid']
      );
    }
  }

  async validate_updateAvatar({
    session,
  }: editUserDetailPresentation.dataSources.user.routines.updateAvatar.validateInput): Promise<editUserDetailPresentation.dataSources.user.routines.updateAvatar.validateOutput> {
    if (this._user.canSetProfileImage === true) {
      return true;
    }

    const message = await getTranslation(
      session,
      'editUserDetail.validation.cannotUpdateAvatar',
      'everest.hr.base/personModals'
    );
    return {
      passed: false,
      message,
    };
  }

  execute_updateAvatar({
    input: { fileId, fileName },
  }: editUserDetailPresentation.dataSources.user.routines.updateAvatar.executeInput): Promise<editUserDetailPresentation.dataSources.user.routines.updateAvatar.executeOutput> {
    this._user.profileImageId = isEmpty(fileName) ? null : fileId;
    this._user.profileImageSource = isEmpty(fileName)
      ? null
      : `/api/app/storage/${fileId}`;
    this._user.profileImageName = isEmpty(fileName) ? null : fileName;

    return Promise.resolve();
  }
}
