{"version": 2, "uicontroller": "connector.uicontroller.ts", "uimodel": {"nodes": {"integrationConnectivity": {"type": "list", "modelId": "everest.connectorengine/IntegrationConnectivityModel.IntegrationConnectivity", "fieldList": ["integrationName"]}, "selectedIntegrationConnectivity": {"type": "struct", "modelId": "everest.connectorengine/IntegrationConnectivityModel.IntegrationConnectivity", "fieldList": ["id", "acceptHeader", "authTokenParamsInBody", "authorizationUrl", "baseUrl", "httpRequestMethodCallback", "noClientIdAndClientSecretParamsInBody", "integrationName", "internalRedirectUrl", "refreshTokenParamsInBody", "responseTypes", "secret<PERSON>ey", "tokenEndpoint"]}, "secret": {"type": "struct", "modelId": "everest.appserver/SecretModel.Secret"}, "selectedConnector": {"type": "struct", "modelId": "everest.connectorengine/ConnectorEngineModel.ConnectorEngine", "fieldList": ["id", "config<PERSON><PERSON><PERSON>", "name", "connectorType"]}, "rippling": {"type": "struct", "modelId": "everest.fin.integration.rippling/RipplingModel.Rippling"}}, "state": {"connectorTypes": [{"text": "REST", "value": "REST"}, {"text": "OAuth 2", "value": "OAuth2"}, {"text": "Configuration", "value": "Configuration"}]}}, "uiview": {"i18n": ["everest.connectorengine/connectorEngine", "rippling"], "title": "{{connectorEngine.create}}", "sections": [{"component": "Block", "customId": "createConnector", "size": "12", "type": "secondary", "columns": 4, "elements": [{"label": "{{rippling.connection-name}}", "name": "modalSelectedConnectionName", "isEditing": true, "size": 3}, {"component": "Select", "label": "{{rippling.connectorType}}", "idProp": "value", "textProp": "text", "list": "@state:connectorTypes", "name": "modalType", "isEditing": true, "disabledValue": ["REST", "OAuth2"], "size": 1}, {"label": "Client ID", "name": "client_id", "value": "@controller:getClientIdForModal()", "isEditing": true, "visible": "@controller:oAuthFieldsVisibleForModal()", "size": 2}, {"label": "Client Secret", "name": "client_secret", "type": "password", "value": "@controller:getClientSecretForModal()", "isEditing": true, "visible": "@controller:oAuthFieldsVisibleForModal()", "size": 2}, {"label": "API Token", "name": "api_token", "value": "@controller:getAPITokenForModal()", "isEditing": true, "visible": "@controller:restFieldsVisibleForModal()", "size": 4}, {"label": "Authorization URL", "name": "authorizationUrl", "value": "@state:modalSelectedConnectivity.authorizationUrl", "isEditing": true, "visible": "@controller:isExpertViewModal()", "size": 1}, {"label": "Base URL", "name": "baseUrl", "value": "@state:modalSelectedConnectivity.baseUrl", "isEditing": true, "visible": "@controller:isExpertViewModal('Both')", "size": 1}, {"label": "HTTP Request Callback", "name": "httpRequestMethodCallback", "value": "@state:modalSelectedConnectivity.httpRequestMethodCallback", "isEditing": true, "visible": "@controller:isExpertViewModal('Both')", "size": 1}, {"label": "Response Types", "name": "responseTypes", "value": "@state:modalSelectedConnectivity.responseTypes", "isEditing": true, "visible": "@controller:isExpertViewModal('Both')", "size": 1}, {"label": "Internal Redirect URL", "name": "internalRedirectUrl", "value": "@state:modalSelectedConnectivity.internalRedirectUrl", "isEditing": true, "visible": "@controller:isExpertViewModal()", "size": 2}, {"label": "Accept Header", "name": "acceptHeader", "value": "@state:modalSelectedConnectivity.acceptHeader", "isEditing": true, "visible": "@controller:isExpertViewModal('Both')", "size": 1}, {"label": "No Client in Body", "name": "noClientIdAndClientSecretParamsInBody", "value": "@controller:getTextFromBooleanForModal('noClientIdAndClientSecretParamsInBody')", "isEditing": true, "visible": "@controller:isExpertViewModal()", "size": 1}, {"label": "Token Endpoint", "name": "tokenEndpoint", "value": "@state:modalSelectedConnectivity.tokenEndpoint", "isEditing": true, "visible": "@controller:isExpertViewModal()", "size": 2}, {"label": "Refresh <PERSON> in Body", "name": "refreshTokenParamsInBody", "value": "@controller:getTextFromBooleanForModal('refreshTokenParamsInBody')", "isEditing": true, "visible": "@controller:isExpertViewModal()", "size": 1}, {"label": "<PERSON><PERSON> Token in Body", "name": "authTokenParamsInBody", "value": "@controller:getTextFromBooleanForModal('authTokenParamsInBody')", "isEditing": true, "visible": "@controller:isExpertViewModal()", "size": 1}]}, {"component": "ButtonGroup", "direction": "horizontal", "actions": [{"variant": "secondary", "label": "@controller:expertViewButtonNameForModal()", "onClick": "@controller:switchExpertViewModal", "disabled": "@controller:isExpertViewDisabledForModal()"}, {"variant": "primary", "label": "@state:buttonTitle", "onClick": "@controller:createOrEdit", "disabled": "@controller:isModalButtonDisabled()"}]}]}}