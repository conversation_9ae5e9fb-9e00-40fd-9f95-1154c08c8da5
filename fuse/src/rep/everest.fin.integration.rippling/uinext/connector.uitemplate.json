{"version": 2, "uicontroller": "connector.uicontroller.ts", "uimodel": {"nodes": {"integrationConnectivity": {"type": "list", "modelId": "everest.connectorengine/IntegrationConnectivityModel.IntegrationConnectivity", "fieldList": ["integrationName"]}, "selectedIntegrationConnectivity": {"type": "struct", "modelId": "everest.connectorengine/IntegrationConnectivityModel.IntegrationConnectivity", "fieldList": ["id", "acceptHeader", "authTokenParamsInBody", "authorizationUrl", "baseUrl", "httpRequestMethodCallback", "noClientIdAndClientSecretParamsInBody", "integrationName", "internalRedirectUrl", "refreshTokenParamsInBody", "responseTypes", "secret<PERSON>ey", "tokenEndpoint"]}, "clientId": {"type": "struct", "modelId": "everest.appserver/SecretModel.Secret"}, "clientSecret": {"type": "struct", "modelId": "everest.appserver/SecretModel.Secret"}, "apiToken": {"type": "struct", "modelId": "everest.appserver/SecretModel.Secret"}, "selectedConnector": {"type": "struct", "modelId": "everest.connectorengine/ConnectorEngineModel.ConnectorEngine", "fieldList": ["id", "config<PERSON><PERSON><PERSON>", "name", "connectorType"]}, "rippling": {"type": "struct", "modelId": "everest.fin.integration.rippling/RipplingModel.Rippling"}, "handleAuthRedirect": {"type": "struct", "model": "urn:evst:everest:fin/integration/rippling:model/node:Rippling"}}}, "uiview": {"i18n": ["everest.connectorengine/connectorEngine", "rippling"], "title": "{{connectorEngine.create}}", "config": {"allowRefreshData": true, "autoRefreshData": true}, "header": {"content": {"title": "{{rippling.connector.manage}}"}}, "sections": [{"component": "Block", "customId": "ripplingConnector", "size": "12", "type": "secondary", "columns": 4, "elements": [{"component": "Select", "label": "{{rippling.select-connection}}", "idProp": "integrationName", "textProp": "integrationName", "isEditing": true, "list": "@state:connectivityList", "onChange": "@controller:selectConnection", "name": "selectedConnectionName", "size": 3, "bottomActions": [{"label": "{{rippling.create-connection}}", "onClick": "@controller:createNewConnection"}]}, {"component": "Select", "label": "{{rippling.connectorType}}", "idProp": "value", "textProp": "text", "list": "@state:connectorTypes", "name": "type", "isEditing": true, "isDisabled": true, "size": 1}, {"label": "Client ID", "name": "client_id", "value": "@state:secrets.clientId", "isEditing": true, "isDisabled": true, "visible": "@controller:oAuthFieldsVisible()", "size": 2}, {"label": "Client Secret", "name": "client_secret", "type": "password", "value": "@state:secrets.clientSecret", "isEditing": true, "isDisabled": true, "visible": "@controller:oAuthFieldsVisible()", "size": 2}, {"label": "API Token", "name": "api_token", "value": "@state:secrets.apiToken", "isEditing": true, "isDisabled": true, "visible": "@controller:restFieldsVisible()", "size": 4}, {"label": "Authorization URL", "value": "@state:selectedConnectivity.authorizationUrl", "visible": "@controller:isExpertView()", "size": 1}, {"label": "Base URL", "value": "@state:selectedConnectivity.baseUrl", "visible": "@controller:isExpertView('Both')", "size": 1}, {"label": "HTTP Request Callback", "value": "@state:selectedConnectivity.httpRequestMethodCallback", "visible": "@controller:isExpertView('Both')", "size": 1}, {"label": "Response Types", "value": "@state:selectedConnectivity.responseTypes", "visible": "@controller:isExpertView('Both')", "size": 1}, {"label": "Internal Redirect URL", "value": "@state:selectedConnectivity.internalRedirectUrl", "visible": "@controller:isExpertView()", "size": 2}, {"label": "Accept Header", "value": "@state:selectedConnectivity.acceptHeader", "visible": "@controller:isExpertView('Both')", "size": 1}, {"label": "No Client in Body", "list": "@state:booleanValues", "value": "@controller:getTextFromBoolean('noClientIdAndClientSecretParamsInBody')", "visible": "@controller:isExpertView()", "size": 1}, {"label": "Token Endpoint", "value": "@state:selectedConnectivity.tokenEndpoint", "visible": "@controller:isExpertView()", "size": 2}, {"label": "Refresh <PERSON> in Body", "list": "@state:booleanValues", "value": "@controller:getTextFromBoolean('refreshTokenParamsInBody')", "visible": "@controller:isExpertView()", "size": 1}, {"label": "<PERSON><PERSON> Token in Body", "list": "@state:booleanValues", "value": "@controller:getTextFromBoolean('authTokenParamsInBody')", "visible": "@controller:isExpertView()", "size": 1}, {"label": "@controller:getTestLabel()", "component": "ProgressBar", "progress": "@controller:getTestProgressValue()", "variant": "@controller:getTestVariant()", "started": "@controller:getTestStarted()", "size": 4}, {"component": "Editor", "label": "{{rippling.test-response}}", "value": "@state:response", "action": "create", "isEditing": false, "editorConfig": {"language": "json", "height": 150}, "isVisible": "@controller:isExpertView('Both')", "size": 4}]}, {"component": "ButtonGroup", "direction": "horizontal", "actions": [{"variant": "secondary", "label": "@controller:expert<PERSON>iewButtonName()", "onClick": "@controller:switchExpertView", "disabled": "@controller:isExpertViewDisabled()"}, {"variant": "secondary", "label": "{{connectorEngine.test}}", "onClick": "@controller:test", "disabled": "@controller:isTestDisabled()"}, {"variant": "primary", "label": "{{rippling.actions}}", "actions": [{"label": "{{connectorEngine.authenticate}}", "onClick": "@controller:authorize", "disabled": "@controller:connectionNotSelected()", "visible": "@controller:oAuthFieldsVisible()"}, {"label": "{{connectorEngine.edit}}", "onClick": "@controller:editExistingConnection", "disabled": "@controller:connectionNotSelected()"}, {"label": "{{connectorEngine.delete}}", "onClick": "@controller:deleteSelectedConnection", "disabled": "@controller:connectionNotSelected()", "confirmation": {"message": "{{rippling.delete-connection}}"}}, {"label": "{{rippling.goToIntegration}}", "onClick": "@controller:goToIntegration", "disabled": "@controller:goToIntegrationDisabled()"}]}]}]}}