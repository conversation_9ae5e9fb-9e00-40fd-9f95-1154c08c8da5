// @i18n:everest.connectorengine/connectorEngine
// @i18n:rippling
import { UILifecycleHooks } from '@everestsystems/content-core';
import type { ConnectorEngine } from '@pkg/everest.connectorengine/types/ConnectorEngine';
import { ConnectorEngineUI } from '@pkg/everest.connectorengine/types/ConnectorEngine.ui';
import type { IntegrationConnectivity } from '@pkg/everest.connectorengine/types/IntegrationConnectivity';
import { IntegrationConnectivityUI } from '@pkg/everest.connectorengine/types/IntegrationConnectivity.ui';
import { EvstExtractionProviders } from '@pkg/everest.fin.integration.base/types/enums/ExtractionProviders';
import type { ConnectorUiTemplate } from '@pkg/everest.fin.integration.rippling/types/uiTemplates/uinext/connector.ui';
import { isEqual, isNil, omit } from 'lodash';

import type { AuthenticateReturn } from '../actions/oAuth/authenticate.action';
import { RipplingUI } from '../types/Rippling.ui';
import {
  RIPPLING_ACCEPT_HEADER,
  RIPPLING_AUTH_TOKEN_PARAMS_IN_BODY,
  RIPPLING_HTTP_REQUEST_METHOD_CALLBACK,
  RIPPLING_INTERNAL_REDIRECT_URL,
  RIPPLING_NO_CLIENT_ID_AND_CLIENT_SECRET_PARAMS_IN_BODY,
  RIPPLING_PACKAGE,
  RIPPLING_REFRESH_TOKEN_PARAMS_IN_BODY,
  RIPPLING_RESPONSE_TYPES,
  RIPPLING_TOKEN_ENDPOINT,
  RipplingConnectivityInfo,
} from '../utils/consts';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type Any = any;

export type Context = ConnectorUiTemplate.ConnectorContext & {
  data: {
    handleAuthRedirect: {
      stateIsValid: boolean;
      codeIsSet: boolean;
      authenticated: boolean;
    };
  };
  state: {
    selectedConnection: string;
    currentConnectorId: ConnectorEngine.ConnectorEngine['id'];
    response: string;
    isTestExecuted: boolean;
    isTestSuccessful: boolean;
    isExpertView: boolean;
    previousInstance: Any;
    selectedConnector: Partial<ConnectorEngine.ConnectorEngine>;
    connectivityList: Pick<
      IntegrationConnectivity.IntegrationConnectivity,
      'integrationName'
    >[];
    selectedConnectivity: Partial<IntegrationConnectivity.IntegrationConnectivity>;
    secrets: {
      clientId: string;
      clientSecret: string;
      apiToken: string;
    };
    param: {
      code: string;
      state: string;
    };
    form: {
      type?: string;
      selectedConnectionName?: string;
      modalSelectedConnectionName?: string;
      modalType?: string;
      client_id?: string;
      client_secret?: string;
      api_token?: string;
      baseUrl?: string;
      authorizationUrl?: string;
      tokenEndpoint?: string;
      refreshTokenParamsInBody?: string;
      authTokenParamsInBody?: string;
      noClientIdAndClientSecretParamsInBody?: string;
      acceptHeader?: string;
      httpRequestMethodCallback?: string;
      responseTypes?: string;
      internalRedirectUrl?: string;
    };
  };
};

type ModalForm = {
  modalSelectedConnectionName: string;
  modalType: string;
  client_id: string;
  client_secret: string;
  api_token: string;
  authorizationUrl: string;
  baseUrl: string;
  httpRequestMethodCallback: string;
  responseTypes: string;
  internalRedirectUrl: string;
  acceptHeader: string;
  noClientIdAndClientSecretParamsInBody: string;
  tokenEndpoint: string;
  refreshTokenParamsInBody: string;
  authTokenParamsInBody: string;
};

type ModalSecrets = {
  clientId: string;
  clientSecret: string;
  apiToken: string;
};

export async function fetchLatestConnector(context: Context) {
  const { helpers, state } = context;

  const response = await ConnectorEngineUI.query(
    context,
    {
      where: { package: RIPPLING_PACKAGE },
    },
    ['id', 'configValues', 'name', 'connectorType']
  ).run('selectedConnector');

  if (response.error) {
    await helpers.showToast({
      title: 'Error',
      message: response.error.message,
      type: 'error',
    });
    return null;
  }

  if (response.selectedConnector && response.selectedConnector.length > 0) {
    state.selectedConnector = response.selectedConnector[0];
    state.currentConnectorId = state.selectedConnector.id;
    return response.selectedConnector[0];
  }
  return null;
}

export async function fetchConnectivityList(context: Context) {
  const { helpers, state } = context;

  const response = await IntegrationConnectivityUI.query(
    context,
    {
      where: { package: RIPPLING_PACKAGE },
    },
    ['integrationName']
  ).run('integrationConnectivity');

  if (response.error) {
    await helpers.showToast({
      title: 'Error',
      message: response.error.message,
      type: 'error',
    });
    return [];
  }

  if (
    response.integrationConnectivity &&
    response.integrationConnectivity.length > 0
  ) {
    state.connectivityList = response.integrationConnectivity[0];
    return response.integrationConnectivity[0];
  }
  return [];
}

export async function fetchSelectedConnectivity(
  context: Context,
  connectionName: string
) {
  const { helpers, state } = context;

  const response = await IntegrationConnectivityUI.query(
    context,
    {
      where: { integrationName: connectionName },
    },
    [
      'id',
      'acceptHeader',
      'authTokenParamsInBody',
      'authorizationUrl',
      'baseUrl',
      'httpRequestMethodCallback',
      'noClientIdAndClientSecretParamsInBody',
      'integrationName',
      'internalRedirectUrl',
      'refreshTokenParamsInBody',
      'responseTypes',
      'secretKey',
      'tokenEndpoint',
    ]
  ).run('selectedIntegrationConnectivity');

  if (response.error) {
    await helpers.showToast({
      title: 'Error',
      message: response.error.message,
      type: 'error',
    });
    return null;
  }

  if (
    response.selectedIntegrationConnectivity &&
    response.selectedIntegrationConnectivity.length > 0
  ) {
    state.selectedConnectivity = response.selectedIntegrationConnectivity[0];
    state.form.selectedConnectionName = connectionName;
    return response.selectedIntegrationConnectivity[0];
  }
  return null;
}

export async function fetchConnectionSecrets(
  context: Context,
  connectionName: string,
  connectorType?: string
) {
  const { helpers, state } = context;

  const response = await RipplingUI.fetchCorrectSecrets(context, {
    connection: connectionName,
    connectorType,
  }).run('rippling');

  if (response.error) {
    await helpers.showToast({
      title: 'Error',
      message: response.error.message,
      type: 'error',
    });
    state.secrets = { clientId: '', clientSecret: '', apiToken: '' };
    state.form.type = '';
  }

  state.secrets = response.rippling.secrets;
  // This bit needed to just render a type of Configuration since this type can't actually be stored on the node.
  // Configuration type is used to just have access to the integration's custom configuration
  if (connectorType) {
    state.form.type =
      connectorType === 'REST' && !response.rippling.secrets.apiToken
        ? 'Configuration'
        : connectorType;
  } else if (response.rippling.connectorType) {
    state.form.type =
      response.rippling.connectorType === 'REST' &&
      !response.rippling.secrets.apiToken
        ? 'Configuration'
        : response.rippling.connectorType;
  }
}

export async function checkIfAuthenticated(
  context: Context,
  connectionName?: string
) {
  const { state, helpers } = context;

  if (state.redirected) {
    return;
  }

  const result = await RipplingUI.saveAndCheckAuthCode(context, {
    code: state.param.code,
    state: state.param.state,
    connectionName: connectionName ?? getSelectedConnection(context),
  }).run('handleAuthRedirect');

  state.authenticated = result.handleAuthRedirect.authenticated;

  if (state.param.code && state.param.state) {
    let title = '';
    let message = '';
    let type = '';

    if (result.handleAuthRedirect.authenticated) {
      title = 'Success';
      message = '{{rippling.authentication.success}}';
      type = 'success';
    } else if (result.handleAuthRedirect.authenticated === false) {
      title = 'Error';
      message = '{{rippling.authentication.fail}}';
      type = 'error';
    }

    helpers.showNotificationMessage({
      title,
      message,
      type: type as 'success' | 'error',
    });

    helpers.navigate({
      to: '/templates/everest.fin.integration.rippling/uinext/connector',
      closeCurrentTab: true,
      initialState: {
        authenticated: result.handleAuthRedirect.authenticated,
        param: {
          code: '',
          state: '',
        },
        redirected: true,
      },
    });
  }
}

export async function getRequiredData(context: Context) {
  await fetchConnectivityList(context);

  const connector = await fetchLatestConnector(context);

  if (
    isNil(connector) ||
    !(connector?.configValues as { integrationName?: string })?.integrationName
  ) {
    return;
  }

  const connectionName = (connector.configValues as { integrationName: string })
    .integrationName;

  await fetchSelectedConnectivity(context, connectionName);

  await fetchConnectionSecrets(
    context,
    connectionName,
    connector.connectorType
  );

  await checkIfAuthenticated(context);
}

// ==============================================
// Lifecycle Hooks
// ==============================================

UILifecycleHooks.onInit((context: Context) => {
  const { state } = context;

  if (!state.buttonTitle) {
    getRequiredData(context).catch(console.error);
  }
});

// ==============================================
// Util
// ==============================================

export const isModalButtonDisabled = (context: Context) => {
  const { state } = context;
  const form = state.form as unknown as ModalForm;
  const previousSecrets = state.previousSecrets as ModalSecrets;

  const emptyFieldsCondition =
    !form.modalSelectedConnectionName ||
    !form.modalType ||
    (form.modalType === 'OAuth2' &&
      (Object.values(omit(form, ['api_token'])).includes('') ||
        Object.values(omit(form, ['api_token'])).includes(undefined))) ||
    (form.modalType === 'REST' &&
      (Object.values(omit(form, ['client_id', 'client_secret'])).includes('') ||
        Object.values(omit(form, ['client_id', 'client_secret'])).includes(
          undefined
        )));

  const correctValuesCondition =
    (!!form?.authTokenParamsInBody &&
      !['true', 'false'].includes(
        form?.authTokenParamsInBody?.toLowerCase()
      )) ||
    (!!form?.refreshTokenParamsInBody &&
      !['true', 'false'].includes(
        form?.refreshTokenParamsInBody?.toLowerCase()
      )) ||
    (!!form?.noClientIdAndClientSecretParamsInBody &&
      !['true', 'false'].includes(
        form?.noClientIdAndClientSecretParamsInBody?.toLowerCase()
      ));

  const completeEqualityCondition =
    isEqual(
      {
        ...omit(form, [
          'modalSelectedConnectionName',
          'modalType',
          'client_id',
          'client_secret',
          'api_token',
        ]),
        noClientIdAndClientSecretParamsInBody:
          form.noClientIdAndClientSecretParamsInBody?.toLowerCase() === 'true'
            ? true
            : false,
        refreshTokenParamsInBody:
          form.refreshTokenParamsInBody?.toLowerCase() === 'true'
            ? true
            : false,
        authTokenParamsInBody:
          form.authTokenParamsInBody?.toLowerCase() === 'true' ? true : false,
      },
      omit(
        state.modalSelectedConnectivity as Partial<IntegrationConnectivity.IntegrationConnectivity>,
        ['integrationName', 'secretKey', 'id', 'uuid']
      )
    ) &&
    form.modalSelectedConnectionName ===
      state?.previousSelectedConnectionName &&
    form.modalType === state?.previousType &&
    form.client_id === previousSecrets?.clientId &&
    form.client_secret === previousSecrets?.clientSecret &&
    form.api_token === previousSecrets?.apiToken;

  return (
    emptyFieldsCondition || correctValuesCondition || completeEqualityCondition
  );
};

export function getTextFromBoolean(context: Context, fieldName: string) {
  const { state } = context;
  return fieldName && state?.selectedConnectivity?.[fieldName]
    ? 'True'
    : 'False';
}

export function getTextFromBooleanForModal(
  context: Context,
  fieldName: string
) {
  const { state } = context;
  return fieldName &&
    (
      state?.modalSelectedConnectivity as Partial<IntegrationConnectivity.IntegrationConnectivity>
    )?.[fieldName]
    ? 'True'
    : 'False';
}

export const connectionNotSelected = (context: Context) => {
  return !getSelectedConnection(context);
};

export async function selectConnection(context: Context, value: string) {
  const { state, sharedState } = context;

  // we need to reset the shared state so that everything gets overwritten when another connectivity is selected
  sharedState.reset();
  // in case a test was running, we clear the test state values
  state.response = '';
  state.isTestExecuted = false;
  state.isTestSuccessful = false;

  if (value) {
    await fetchSelectedConnectivity(context, value);
    await fetchConnectionSecrets(context, value);

    await RipplingUI.upsertConnectorOnly(context, {
      connector: {
        name: getName(context),
        connectorType: state.form.type,
      },
      integrationConnectivity: state.selectedConnectivity,
    }).run('rippling');
    await fetchLatestConnector(context);
    if (state.form.type === 'OAuth2') {
      await checkIfAuthenticated(context);
    }
  }
}

export function getIntegrationName({ data }: Context) {
  return data?.selectedConnector?.configValues?.['integrationName'];
}

export function getSelectedConnection(context: Context) {
  const { state } = context;

  return state?.form?.selectedConnectionName ?? getIntegrationName(context);
}

export function getName(_: Context) {
  return EvstExtractionProviders.Rippling;
}

export function goToIntegration(context: Context) {
  const { helpers } = context;
  helpers.navigate({
    to: 'templates/everest.migration.base/uinext/overview?providerName=Rippling',
    closeCurrentTab: true,
  });
}

export function goToIntegrationDisabled(context: Context) {
  const { state } = context;
  const condition =
    !state.form?.type ||
    (state.form?.type === 'REST' && !state.secrets.apiToken) ||
    (state.form?.type === 'OAuth2' && !state.authenticated);
  return condition || !state.form.selectedConnectionName;
}

export function isTestDisabled(context: Context) {
  const { state } = context;
  const condition =
    !state.form?.type ||
    state.form?.type === 'Configuration' ||
    (state.form?.type === 'REST' && !state.secrets.apiToken) ||
    (state.form?.type === 'OAuth2' && !state.authenticated);
  return condition || !state.form.selectedConnectionName;
}

export function oAuthFieldsVisible(context: Context) {
  const { state } = context;
  return state?.form?.type === 'OAuth2';
}

export function restFieldsVisible(context: Context) {
  const { state } = context;
  return state?.form?.type === 'REST';
}

export function oAuthFieldsVisibleForModal(context: Context) {
  const { state } = context;
  return (state.form as unknown as ModalForm).modalType === 'OAuth2';
}

export function restFieldsVisibleForModal(context: Context) {
  const { state } = context;
  return (state.form as unknown as ModalForm).modalType === 'REST';
}

// ==============================================
// Secret
// ==============================================

export function getClientIdForModal(context: Context) {
  const { state } = context;
  return state?.clientId || state?.secrets?.clientId;
}

export function getClientSecretForModal(context: Context) {
  const { state } = context;
  return state?.clientSecret || state?.secrets?.clientSecret;
}

export function getAPITokenForModal(context: Context) {
  const { state } = context;
  return state?.apiToken || state?.secrets?.apiToken;
}

// ==============================================
// Expert View
// ==============================================

export function isExpertView(context: Context, type: string = 'OAuth2') {
  const { state } = context;
  const condition = type === 'Both' ? true : state.form?.type === type;

  return (condition && state.isExpertView) ?? false;
}

export function isExpertViewModal(context: Context, type: string = 'OAuth2') {
  const { state } = context;
  const condition =
    type === 'Both'
      ? true
      : (state.form as unknown as ModalForm).modalType === type;

  return (condition && state.isExpertView) ?? false;
}

export function switchExpertView(context: Context) {
  const { state } = context;
  state.isExpertView = !isExpertView(context, 'Both');
}

export function switchExpertViewModal(context: Context) {
  const { state } = context;
  state.isExpertView = !isExpertViewModal(context, 'Both');
}

export function expertViewButtonName(context: Context) {
  // when expert view is enabled, the button name is 'Regular View';
  // and vice versa (i.e., 'Expert View') if expert view is disabled
  return isExpertView(context, 'Both')
    ? '{{rippling.regular-view}}'
    : '{{rippling.expert-view}}';
}

export function expertViewButtonNameForModal(context: Context) {
  // when expert view is enabled, the button name is 'Regular View';
  // and vice versa (i.e., 'Expert View') if expert view is disabled
  return isExpertViewModal(context, 'Both')
    ? '{{rippling.regular-view}}'
    : '{{rippling.expert-view}}';
}

export function isExpertViewDisabled(context: Context) {
  const { state } = context;
  return !state.form?.type || state.form?.type === 'Configuration';
}

export function isExpertViewDisabledForModal(context: Context) {
  const { state } = context;
  const modalForm = state.form as unknown as ModalForm;
  return !modalForm?.modalType || modalForm?.modalType === 'Configuration';
}

// ==============================================
// Integration Connectivity
// ==============================================

export function createNewConnection(context: Context) {
  const { helpers, sharedState, state } = context;

  helpers.openModal({
    title: '{{rippling.create-connection}}',
    size: 'medium',
    template:
      '/templates/everest.fin.integration.rippling/uinext/createConnector',
    initialState: {
      form: {
        modalSelectedConnectionName: '',
        modalType: '',
        client_id: '',
        client_secret: '',
        api_token: '',
        baseUrl: RipplingConnectivityInfo.Production.baseUrl,
        authorizationUrl: RipplingConnectivityInfo.Production.authURL,
        tokenEndpoint: RIPPLING_TOKEN_ENDPOINT,
        refreshTokenParamsInBody: 'True',
        authTokenParamsInBody: 'True',
        noClientIdAndClientSecretParamsInBody: 'True',
        acceptHeader: RIPPLING_ACCEPT_HEADER,
        httpRequestMethodCallback: RIPPLING_HTTP_REQUEST_METHOD_CALLBACK,
        responseTypes: RIPPLING_RESPONSE_TYPES,
        internalRedirectUrl: RIPPLING_INTERNAL_REDIRECT_URL,
      },
      modalSelectedConnectivity: {
        authorizationUrl: RipplingConnectivityInfo.Production.authURL,
        baseUrl: RipplingConnectivityInfo.Production.baseUrl,
        httpRequestMethodCallback: RIPPLING_HTTP_REQUEST_METHOD_CALLBACK,
        responseTypes: RIPPLING_RESPONSE_TYPES,
        internalRedirectUrl: RIPPLING_INTERNAL_REDIRECT_URL,
        acceptHeader: RIPPLING_ACCEPT_HEADER,
        noClientIdAndClientSecretParamsInBody:
          RIPPLING_NO_CLIENT_ID_AND_CLIENT_SECRET_PARAMS_IN_BODY,
        tokenEndpoint: RIPPLING_TOKEN_ENDPOINT,
        refreshTokenParamsInBody: RIPPLING_REFRESH_TOKEN_PARAMS_IN_BODY,
        authTokenParamsInBody: RIPPLING_AUTH_TOKEN_PARAMS_IN_BODY,
      },
      buttonTitle: '{{connectorEngine.create}}',
    },
    onModalSubmit: async (payload) => {
      const {
        modalForm,
        modalConnectivity,
        modalSecrets,
        modalConnectionName,
        modalConnectorType,
      } = payload;
      state.form = { ...modalForm };
      state.form.type = modalConnectorType;
      state.form.selectedConnectionName = modalConnectionName;
      state.selectedConnectivity = modalConnectivity;
      state.isTestExecuted = false;
      state.isTestSuccessful = false;
      state.response = '';
      state.secrets = {
        clientId: modalSecrets.client_id,
        clientSecret: modalSecrets.client_secret,
        apiToken: modalSecrets.api_token,
      };
      sharedState.reset();
      await fetchLatestConnector(context);
      await fetchConnectivityList(context);
      if (modalConnectorType === 'OAuth2') {
        await checkIfAuthenticated(context);
      }
    },
    onClose: () => {
      state.buttonTitle = '';
    },
  });
}

export function editExistingConnection(context: Context) {
  const { helpers, sharedState, state } = context;

  helpers.openModal({
    title: '{{rippling.edit-connection}}',
    size: 'medium',
    template:
      '/templates/everest.fin.integration.rippling/uinext/createConnector',
    initialState: {
      form: {
        modalSelectedConnectionName: state.form.selectedConnectionName,
        modalType: state.form.type,
        client_id: state.secrets.clientId,
        client_secret: state.secrets.clientSecret,
        api_token: state.secrets.apiToken,
        baseUrl: state.selectedConnectivity.baseUrl,
        authorizationUrl: state.selectedConnectivity.authorizationUrl,
        tokenEndpoint: state.selectedConnectivity.tokenEndpoint,
        refreshTokenParamsInBody: state.selectedConnectivity
          .refreshTokenParamsInBody
          ? 'True'
          : 'False',
        authTokenParamsInBody: state.selectedConnectivity.authTokenParamsInBody
          ? 'True'
          : 'False',
        noClientIdAndClientSecretParamsInBody: state.selectedConnectivity
          .noClientIdAndClientSecretParamsInBody
          ? 'True'
          : 'False',
        acceptHeader: state.selectedConnectivity.acceptHeader,
        httpRequestMethodCallback:
          state.selectedConnectivity.httpRequestMethodCallback,
        responseTypes: state.selectedConnectivity.responseTypes,
        internalRedirectUrl: state.selectedConnectivity.internalRedirectUrl,
      },
      modalSelectedConnectivity: state.selectedConnectivity,
      previousSelectedConnectionName: state.form.selectedConnectionName,
      previousType: state.form.type,
      previousSecrets: {
        clientId: state.secrets.clientId,
        clientSecret: state.secrets.clientSecret,
        apiToken: state.secrets.apiToken,
      },
      buttonTitle: '{{connectorEngine.save}}',
    },
    onModalSubmit: async (payload) => {
      const {
        modalForm,
        modalConnectivity,
        modalSecrets,
        modalConnectionName,
        modalConnectorType,
      } = payload;
      state.form = { ...modalForm };
      state.form.type = modalConnectorType;
      state.form.selectedConnectionName = modalConnectionName;
      state.selectedConnectivity = modalConnectivity;
      state.isTestExecuted = false;
      state.isTestSuccessful = false;
      state.response = '';
      state.secrets = {
        clientId: modalSecrets.client_id,
        clientSecret: modalSecrets.client_secret,
        apiToken: modalSecrets.api_token,
      };
      sharedState.reset();
      await fetchLatestConnector(context);
      await fetchConnectivityList(context);
      if (modalConnectorType === 'OAuth2') {
        await checkIfAuthenticated(context);
      }
    },
    onClose: () => {
      state.buttonTitle = '';
    },
  });
}

export async function deleteSelectedConnection(context: Context) {
  const { helpers, actions, state, sharedState } = context;

  try {
    await actions.run({
      rippling: {
        action: 'deleteRipplingConnector',
        data: {
          selectedIntegrationConnectivity: state.selectedConnectivity,
        },
      },
    });
  } catch (error) {
    helpers.showToast({
      title: 'Error',
      type: 'error',
      message: error.message,
    });
    return;
  }

  helpers.showToast({
    title: 'Success',
    type: 'success',
    message: 'Connector deleted successfully',
  });

  state.currentConnectorId = undefined;
  state.isExpertView = false;
  state.previousInstance = undefined;
  state.param = {
    code: '',
    state: '',
  };
  state.form = undefined;
  state.response = '';
  state.isTestExecuted = false;
  state.isTestSuccessful = false;
  sharedState.reset();

  await fetchConnectivityList(context);
}

// ==============================================
// Connector Engine
// ==============================================

export async function createOrEdit(context: Context) {
  const { state, actions, helpers } = context;

  const form = state.form as unknown as ModalForm;
  const client_id = form.client_id;
  const client_secret = form.client_secret;
  const api_token = form.api_token;
  const connectorType = form.modalType;
  const connectionName = form.modalSelectedConnectionName;
  const name = getName(context);
  const condition =
    !connectorType ||
    (connectorType === 'REST' && (isNil(api_token) || !api_token)) ||
    (connectorType === 'OAuth2' &&
      (isNil(client_id) ||
        !client_id ||
        isNil(client_secret) ||
        !client_secret));

  if (
    condition ||
    isNil(name) ||
    !name ||
    isNil(connectionName) ||
    !connectionName
  ) {
    helpers.showToast({
      type: 'error',
      title: 'Error',
      message:
        'Missing required information. Please check the info of your selected connection.',
    });
    return;
  }

  // there is a 1:1 relationship between the name of the secret key and the integration name
  state.modalSelectedConnectivity = {
    ...(state.modalSelectedConnectivity as Partial<IntegrationConnectivity.IntegrationConnectivity>),
    ...omit(form, [
      'modalSelectedConnectionName',
      'modalType',
      'client_id',
      'client_secret',
      'api_token',
    ]),
    noClientIdAndClientSecretParamsInBody:
      form.noClientIdAndClientSecretParamsInBody.toLowerCase() === 'true'
        ? true
        : false,
    refreshTokenParamsInBody:
      form.refreshTokenParamsInBody.toLowerCase() === 'true' ? true : false,
    authTokenParamsInBody:
      form.authTokenParamsInBody.toLowerCase() === 'true' ? true : false,
    secretKey: connectionName,
    integrationName: connectionName,
  };

  const response = await actions.run({
    rippling: {
      action: 'upsertRipplingConnector',
      data: {
        connector: {
          name,
          connectorType,
        },
        integrationConnectivity: state.modalSelectedConnectivity,
        clientId: client_id,
        clientSecret: client_secret,
        apiToken: api_token,
      },
    },
  });
  if (
    !response ||
    response?.error ||
    !response?.rippling ||
    response.rippling?.error ||
    !response.rippling?.connectorId
  ) {
    helpers.showNotificationMessage({
      title: 'Error',
      message: 'Could not apply your changes',
      type: 'error',
    });
    return;
  }
  state.currentConnectorId = response.rippling.connectorId;
  helpers.showNotificationMessage({
    title: 'Success',
    message: 'Changes applied successfully',
    type: 'success',
  });
  await helpers.currentModalSubmitCallback({
    modalForm: form,
    modalConnectivity: state.modalSelectedConnectivity,
    modalSecrets: {
      client_id,
      client_secret,
      api_token,
    },
    modalConnectionName: connectionName,
    modalConnectorType: connectorType,
  });
  helpers.closeModal();
}

// =====================
// Authorization
// =====================

export async function authorize(ctx: Context) {
  const { helpers, state } = ctx;

  const connectionInfo = {
    connectionName: state.form.selectedConnectionName,
    authUrl: state.selectedConnectivity.authorizationUrl,
    responseType: state.selectedConnectivity.responseTypes,
    clientId: state.secrets.clientId,
    redirectUrl: state.selectedConnectivity.internalRedirectUrl,
  };

  const res = await RipplingUI.authenticate(ctx, { connectionInfo }).run(
    'rippling'
  );

  if (res.error) {
    helpers.showToast({
      title: 'Error',
      message: 'Failed to authenticate',
      type: 'error',
    });
    return;
  }

  const { rippling: authRes } = res as { rippling: AuthenticateReturn };
  if (authRes.isRefreshed) {
    helpers.showToast({
      title: 'Success',
      message: 'Session has been successfully authenticated',
      type: 'success',
    });
    state.authenticated = true;
  } else {
    window.open(authRes.authorizationLink, '_self');
  }
}

// =====================
// Test Connection
// =====================

export async function test(context: Context) {
  const { actions, state, helpers } = context;

  const connector = {
    id: state.currentConnectorId ?? state?.selectedConnector?.id,
    type: state.form.type,
  };

  if (!connector.id) {
    helpers.showToast({
      title: 'Error',
      message: 'Connector data is missing for the selected connection',
      type: 'error',
    });
    return;
  }

  // test if fetching the company information works
  // we test it based on the selected connection and not on the stored one
  const responseCompanyInfo = await actions.run({
    rippling: {
      action: 'testRipplingConnector',
      data: {
        connector,
        integrationName: state.form.selectedConnectionName,
      },
    },
  });

  state.response =
    responseCompanyInfo?.error ??
    responseCompanyInfo.rippling?.error ??
    responseCompanyInfo.rippling;
  state.isTestExecuted = true;
  state.isTestSuccessful =
    responseCompanyInfo?.error ||
    responseCompanyInfo.rippling?.error ||
    !responseCompanyInfo?.rippling
      ? false
      : true;
  if (responseCompanyInfo?.error || responseCompanyInfo.rippling?.error) {
    helpers.showToast({
      title: 'Error',
      message:
        responseCompanyInfo?.error ?? responseCompanyInfo.rippling?.error,
      type: 'error',
    });
  }
}

export function getTestProgressValue({ state }: Context) {
  // - 0: test not started
  // - 50: error during testing
  // - 100: test successful
  return state.isTestSuccessful ? 100 : state.isTestExecuted ? 50 : 0;
}

export function getTestVariant({ state }: Context) {
  return !state.isTestSuccessful && state.isTestExecuted ? 'error' : '';
}

export function getTestStarted({ state }: Context) {
  return state.isTestExecuted ?? false;
}

export function getTestLabel({ state }: Context) {
  return state.isTestSuccessful
    ? '{{rippling.test-successful}}'
    : state.isTestExecuted
      ? '{{rippling.test-failed}}'
      : '{{rippling.test-not-started}}';
}
