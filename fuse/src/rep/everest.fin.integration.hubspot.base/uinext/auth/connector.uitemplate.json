{"version": 3, "uicontroller": ["connector.uicontroller.ts"], "uimodel": {"state": {"id": null, "state": "", "code": "", "mode": "view"}, "presentation": {"urn": "urn:evst:everest:fin/integration/hubspot/base:presentation:uinext/auth/connector", "parameters": {"mode": "@state:mode", "id": "@state:id", "code": "@state:code", "state": "@state:state"}}}, "uiview": {"i18n": ["everest.connectorengine/connectorEngine", "hubspot.base"], "title": "{{hubspot.base.connector.manage}}", "config": {"allowRefreshData": true, "autoRefreshData": true}, "header": {"content": {"title": "{{hubspot.base.connector.manage}}"}}, "sections": {"content": [{"component": "FieldGroup", "customId": "hubSpotConnectorMain", "section": {"grid": {"size": "12"}, "title": "{{hubspot.base.connector.main}}"}, "props": {"columns": "1", "presentationDataSet": "@dataSet:connectorMain", "fields": [{"field": "name", "fieldProps": {"label": "{{hubspot.base.connector.name}}"}}]}}, {"component": "FieldGroup", "customId": "huSpotConnectorConnectionSettings", "section": {"grid": {"size": "12"}, "title": "{{hubspot.base.connector.connectionSettings}}"}, "props": {"columns": "2", "presentationDataSet": "@dataSet:connectorSettings", "fields": [{"field": "baseUrl", "fieldProps": {"label": "{{hubspot.base.connector.baseUrl}}"}}, {"field": "authorizationUrl", "fieldProps": {"label": "{{hubspot.base.connector.authorizationUrl}}"}}, {"field": "clientId", "fieldProps": {"label": "{{hubspot.base.connector.clientId}}"}}, {"field": "clientSecret", "fieldProps": {"label": "{{hubspot.base.connector.clientSecret}}", "type": "@controller:ConnectorUIController.getClientSecretFieldType()"}}]}}]}, "actions": {"content": [{"variant": "primary", "label": "{{connectorEngine.save}}", "presentationAction": "@action:saveConnector", "disabled": "@controller:ConnectorUIController.isSaveConnectorDisabled()", "onClick": "@controller:ConnectorUIController.saveConnectorOnClick"}, {"variant": "secondary", "label": "{{connectorEngine.test}}", "presentationAction": "@action:testConnector", "onClick": "@controller:ConnectorUIController.testConnectorOnClick"}, {"variant": "primary", "presentationAction": "@action:generateAuthorisationState", "onClick": "@controller:ConnectorUIController.authenticateOnClick"}]}}}