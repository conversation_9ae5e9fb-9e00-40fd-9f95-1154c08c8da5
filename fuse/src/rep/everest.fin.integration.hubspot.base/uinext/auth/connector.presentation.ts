import {
  DATA,
  getTranslation,
  type ISession,
  METADATA,
} from '@everestsystems/content-core';
import { HubSpot } from '@pkg/everest.fin.integration.hubspot.base/types/HubSpot';
import type { connectorPresentation } from '@pkg/everest.fin.integration.hubspot.base/types/presentations/uinext/auth/connector';
import { HubSpotConnector } from '@pkg/everest.fin.integration.hubspot.base/utils/connector';
import {
  HUBSPOT_CONNECTIVITY_INFO,
  HUBSPOT_CONNECTOR_NAME,
} from '@pkg/everest.fin.integration.hubspot.base/utils/consts';
import { HubSpotOAuth } from '@pkg/everest.fin.integration.hubspot.base/utils/oAuth';
import { omit, pick, set } from 'lodash';

export class ConnectorClass
  implements connectorPresentation.dataSources.Connector.implementation
{
  // INTERNAL DATA

  #connector:
    | connectorPresentation.dataSources.Connector.levels['']
    | undefined = undefined;

  // PRIVATE METHODS

  private async fetchData(
    session: ISession,
    parameters: connectorPresentation.dataSources.Connector.callbacks.query.input['parameters']
  ) {
    if (parameters.id) {
      const existingConnectorData =
        await HubSpotConnector.getHubSpotActiveConnector(
          session,
          parameters.id
        );

      if (!existingConnectorData) {
        throw new Error('Connector not found!');
      }

      const connectivity = await HubSpotConnector.getConnectivity(
        session,
        parameters.id
      );
      const client = await HubSpotOAuth.initOAuthClient(
        session,
        existingConnectorData,
        connectivity
      );

      this.#connector = {
        id: existingConnectorData.id,
        name: existingConnectorData.name,
        integrationName: connectivity.integrationName,
        baseUrl: connectivity.baseUrl,
        authorizationUrl: connectivity.authorizationUrl,
        clientId: client.configuration.clientId,
        clientSecret: client.configuration.clientSecret,
        isStateCorrect: false,
      };
    } else if (parameters.state && parameters.code) {
      //   // noop
      // }
      const stateConnectivity = await HubSpotOAuth.getOAuthStateConnectivity(
        session,
        parameters.state
      );
      if (stateConnectivity) {
        this.#connector = {
          name: stateConnectivity.connectorName,
          integrationName: stateConnectivity.integrationName,
          baseUrl: stateConnectivity.baseUrl,
          authorizationUrl: stateConnectivity.authURL,
          clientId: stateConnectivity.clientId,
          clientSecret: stateConnectivity.clientSecret,
          state: parameters.state,
          code: parameters.code,
          isStateCorrect: true,
        };
      } else {
        throw new Error('State is invalid!');
      }
    } else {
      this.#connector = {
        name: HUBSPOT_CONNECTOR_NAME,
        integrationName: HUBSPOT_CONNECTIVITY_INFO.integrationName,
        baseUrl: HUBSPOT_CONNECTIVITY_INFO.baseUrl,
        clientId: HUBSPOT_CONNECTIVITY_INFO.clientId,
        clientSecret: HUBSPOT_CONNECTIVITY_INFO.clientSecret,
        authorizationUrl: HUBSPOT_CONNECTIVITY_INFO.authURL,
        isStateCorrect: false,
      };
    }
  }

  private async getDataWithMetadata(
    _session: ISession
  ): Promise<connectorPresentation.dataSources.Connector.callbacks.query.combinedOutput> {
    // we don't send clientSecret to frontend if we edit connector or OAuth state is correct
    return {
      [DATA]: {
        ...omit(this.#connector, ['clientSecret']),
        ...(this.#connector.id || this.#connector.isStateCorrect
          ? { clientSecret: '****' }
          : pick(this.#connector, ['clientSecret'])),
      },
      [METADATA]: {
        integrationName: {
          editable: !this.#connector.isStateCorrect && !this.#connector.id,
          fieldProps: {
            component: 'Select',
            idProp: 'value',
            textProp: 'text',
          },
        },
      },
    };
  }

  // PUBLIC IMPLEMENTATION

  async query(
    input: connectorPresentation.dataSources.Connector.callbacks.query.input
  ): Promise<connectorPresentation.dataSources.Connector.callbacks.query.combinedOutput> {
    if (!this.#connector || input.queryReason === 'externalRequest') {
      await this.fetchData(input.session, input.parameters);
    }
    return this.getDataWithMetadata(input.session);
  }

  async update(
    input: connectorPresentation.dataSources.Connector.callbacks.update.input
  ): Promise<connectorPresentation.dataSources.Connector.callbacks.update.output> {
    set(this.#connector, input.fieldName, input.newFieldValue);
  }

  async validate_generateAuthorisationState(
    _input: connectorPresentation.dataSources.Connector.routines.generateAuthorisationState.validateInput
  ): Promise<connectorPresentation.dataSources.Connector.routines.generateAuthorisationState.validateOutput> {
    if (
      !this.#connector.baseUrl ||
      !this.#connector.authorizationUrl ||
      !this.#connector.clientId ||
      !this.#connector.clientSecret
    ) {
      return false;
    }

    return true;
  }

  public async determine_generateAuthorisationState(
    input: connectorPresentation.dataSources.Connector.routines.generateAuthorisationState.determineInput
  ): Promise<connectorPresentation.dataSources.Connector.routines.generateAuthorisationState.determineOutput> {
    const label = await (input.mode === 'view'
      ? getTranslation(
          input.session,
          'connectorEngine.reauthenticate',
          'everest.connectorengine/connectorEngine'
        )
      : getTranslation(
          input.session,
          'connectorEngine.authenticate',
          'everest.connectorengine/connectorEngine'
        ));

    return {
      label,
    };
  }

  async execute_generateAuthorisationState(
    input: connectorPresentation.dataSources.Connector.routines.generateAuthorisationState.executeInput
  ): Promise<connectorPresentation.dataSources.Connector.routines.generateAuthorisationState.executeOutput> {
    const connectivityInfo = {
      connectorId: this.#connector.id,
      connectorName: this.#connector.name,
      integrationName: this.#connector.integrationName,
      authURL: this.#connector.authorizationUrl,
      baseUrl: this.#connector.baseUrl,
      clientId: this.#connector.clientId,
      clientSecret: this.#connector.clientSecret,
    };

    const oauthState = await HubSpotOAuth.createNewOAuthState(
      input.session,
      connectivityInfo
    );

    const authorizationUrl = await HubSpotOAuth.createAuthorizationUrl(
      input.session,
      oauthState,
      connectivityInfo
    );

    return {
      authorizationUrl,
    };
  }

  async validate_saveConnector(
    _input: connectorPresentation.dataSources.Connector.routines.saveConnector.validateInput
  ): Promise<connectorPresentation.dataSources.Connector.routines.saveConnector.validateOutput> {
    return this.#connector.isStateCorrect;
  }

  async execute_saveConnector(
    input: connectorPresentation.dataSources.Connector.routines.saveConnector.executeInput
  ): Promise<connectorPresentation.dataSources.Connector.routines.saveConnector.executeOutput> {
    const client = await HubSpot.client(input.session);
    const id = await client.saveHubSpotConnector(
      this.#connector.name,
      this.#connector.integrationName,
      this.#connector.baseUrl,
      this.#connector.authorizationUrl,
      this.#connector.clientId,
      this.#connector.clientSecret,
      this.#connector.code
    );

    await HubSpotOAuth.clearOAuthSate(input.session, this.#connector.state);

    this.#connector = undefined;
    input.parameters.state = undefined;
    input.parameters.code = undefined;

    return {
      id,
    };
  }

  async execute_testConnector(
    input: connectorPresentation.dataSources.Connector.routines.testConnector.executeInput
  ): Promise<connectorPresentation.dataSources.Connector.routines.testConnector.executeOutput> {
    try {
      await HubSpotOAuth.refreshToken(input.session, this.#connector.id);
    } catch (error: unknown) {
      return {
        error: true,
        message: error instanceof Error ? error.message : String(error),
      };
    }

    return {
      error: false,
      message: '',
    };
  }
}

export const connectorImplementation: connectorPresentation.implementation = {
  Connector: () => new ConnectorClass(),
};

export default connectorImplementation;
