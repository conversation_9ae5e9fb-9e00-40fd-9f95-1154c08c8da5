import { getTranslations, type ISession } from '@everestsystems/content-core';
import type { EvstNodeReference } from '@pkg/everest.appserver/types/composites/metadata/NodeReference';
import { toDateTime } from '@pkg/everest.base/public/utils/Date/date';
import type { CreditMemoLine } from '@pkg/everest.fin.accounting/types/CreditMemoLine';
import { EvstAccountConfigurationType } from '@pkg/everest.fin.accounting/types/enums/AccountConfigurationType';
import { EvstCreditMemoType } from '@pkg/everest.fin.accounting/types/enums/CreditMemoType';
import { EvstRevenueSchedulingEvent } from '@pkg/everest.fin.accounting/types/enums/RevenueSchedulingEvent';
import { InvoiceLine } from '@pkg/everest.fin.accounting/types/InvoiceLine';
import { ManualCreditDebitMemoTypeMapping } from '@pkg/everest.fin.processor.common/CreditMemoCommon/actions/manualCreditDebitMemoTypeMapping.action';
import type { QueryCreditMemoAccountingData } from '@pkg/everest.fin.processor.common/public/types/QueryCreditMemoAccountingData';
import getAccountsByAssignmentProxy from '@pkg/everest.fin.processor.common/public/utils/getAccountsByAssignmentProxy';
import getEntityBookWithControl from '@pkg/everest.fin.processor.common/public/utils/getEntityBookWithControl';
import isCreateJournalEntryDisabled from '@pkg/everest.fin.processor.common/public/utils/isCreateJournalEntryDisabled';
import type { EvstEntityBook } from '@pkg/everest.fin.processor/types/composites/EntityBook';

export namespace QueryCreditMemoAccountingDataAction {
  export interface CreditMemoErrorMessages {
    invoiceAROffsetAccount: string;
    creditMemoTypeError: string;
    creditMemoAccountError: string;
    salesTaxAccountError: string;
    oneTimeDiscountAccountError: string;
    lateFeesAccountError: string;
  }

  export async function execute(
    session: ISession,
    _object: EvstNodeReference,
    entityBooks: Array<EvstEntityBook>,
    previousStageResult: QueryCreditMemoAccountingData.PreviousStageResult,
    processParameters: QueryCreditMemoAccountingData.ProcessParameters
  ): Promise<QueryCreditMemoAccountingData.Result> {
    const isJECreationDisabled = await isCreateJournalEntryDisabled(session);

    if (isJECreationDisabled && !processParameters?.forceJECreation) {
      return;
    }

    const entityBook = getEntityBookWithControl(entityBooks);

    const {
      creditMemoData: { creditMemoHeader, creditMemoLines },
    } = previousStageResult;

    const errorMessages =
      await QueryCreditMemoAccountingDataAction.getCreditMemoErrorTranslations(
        session
      );

    const manualCreditMemoAccountType =
      ManualCreditDebitMemoTypeMapping.toAccountConfigurationType(
        creditMemoHeader.manualCreditMemoType as ManualCreditDebitMemoTypeMapping.ManualCreditMemoTypeOptions
      );
    const accounts = await getAccountsByAssignmentProxy(
      session,
      entityBook.id,
      [
        EvstAccountConfigurationType.CustomerReceivables,
        EvstAccountConfigurationType.LateFees,
        EvstAccountConfigurationType.SalesTax,
        manualCreditMemoAccountType,
      ]
    );

    const creditAccountId = accounts.customerReceivables?.accountId;

    if (!creditAccountId) {
      throw new Error(errorMessages.invoiceAROffsetAccount);
    }

    const manualCreditMemoAccountId =
      accounts[manualCreditMemoAccountType]?.accountId;
    const salesTaxAccountId = accounts.salesTax?.accountId;
    const lateFeesAccountId = accounts.lateFees?.accountId;

    let debitAccountId: number;
    let invoiceLineAccountMap: Map<number, number> = new Map();

    const { creditMemoType, invoiceId, manualCreditMemoType } =
      creditMemoHeader;

    if (
      (creditMemoType === EvstCreditMemoType.EarlyRenewal && invoiceId) ||
      creditMemoType === EvstCreditMemoType.CreditRebill
    ) {
      const invoiceLineIds = creditMemoLines.map(
        (creditMemoLine) => creditMemoLine.createdFromId
      );
      invoiceLineAccountMap = await getInvoiceLineAccountMap(
        session,
        invoiceLineIds,
        entityBook
      );
    } else if (manualCreditMemoType) {
      debitAccountId = manualCreditMemoAccountId;
    } else if (creditMemoType === EvstCreditMemoType.LateFees) {
      debitAccountId = lateFeesAccountId;

      if (!debitAccountId) {
        throw new Error(
          `${errorMessages.creditMemoAccountError} ${creditMemoHeader.manualCreditMemoType}`
        );
      }
    } else {
      throw new Error(errorMessages.creditMemoTypeError);
    }

    if (!salesTaxAccountId) {
      throw new Error(errorMessages.salesTaxAccountError);
    }

    return {
      creditAccountId,
      debitAccountId,
      invoiceLineAccountMap,
      salesTaxAccountId,
    };
  }

  export async function getCreditMemoErrorTranslations(
    session: ISession
  ): Promise<QueryCreditMemoAccountingDataAction.CreditMemoErrorMessages> {
    const [
      invoiceAROffsetAccount,
      creditMemoTypeError,
      creditMemoAccountError,
      salesTaxAccountError,
      oneTimeDiscountAccountError,
      lateFeesAccountError,
    ] = await getTranslations(
      session,
      [
        'creditMemo.journalEntry.error.invoiceAROffsetAccount',
        'creditMemo.journalEntry.validation.creditMemoType',
        'creditMemo.journalEntry.error.accountAssociatedWithType',
        'creditMemo.create.salesTax.error.message',
        'creditMemo.create.oneTimeDiscount.error.message',
        'creditMemo.create.lateFees.error.message',
      ],
      'everest.fin.accounting/creditMemo'
    );
    return {
      invoiceAROffsetAccount,
      creditMemoTypeError,
      creditMemoAccountError,
      salesTaxAccountError,
      oneTimeDiscountAccountError,
      lateFeesAccountError,
    };
  }

  export async function getInvoiceLineAccountMap(
    session: ISession,
    creditMemoCreatedFromIds: CreditMemoLine.CreditMemoLine['createdFromId'][],
    entityBook: EvstEntityBook
  ): Promise<Map<number, number>> {
    const invoiceLineAccountMap: Map<number, number> = new Map();
    const { bookId, entityId } = entityBook;

    const invoiceLines = await InvoiceLine.query(
      session,
      {
        where: {
          id: { $in: creditMemoCreatedFromIds },
        },
      },
      [
        'id',
        'lineType',
        'invoiceDate',
        {
          'InvoiceLine-InvoicePlanLine': [
            {
              'InvoicePlanLine-InvoicePlan': [
                {
                  'InvoicePlan-SalesOrderProductLine': [
                    {
                      'RevenueContractLineMapping-SalesOrderProductLine': [
                        {
                          'RevenueContractLineMapping-RevenueContractLine': [
                            'isClosed',
                            {
                              'RevenueContractLineHierarchy-RevenueContractLine':
                                ['parentId'],
                              'RevenueContractLine-PerformanceObligation': [
                                {
                                  'PerformanceObligation-RevenueSchedulingTemplate':
                                    ['revenueSchedulingEvent'],
                                },
                                {
                                  'PerformanceObligationAccountMapping-PerformanceObligation':
                                    [
                                      'deferredRevenueAccountId',
                                      'revenueAccountId',
                                      'entityId',
                                      'bookId',
                                    ],
                                },
                              ],
                            },
                          ],
                        },
                      ],
                    },
                  ],
                },
              ],
            },
          ],
          'InvoiceLine-Product': [
            {
              'POBProductLine-Product': [
                'effectiveStartDate',
                'effectiveEndDate',
                {
                  'POBProductLine-PerformanceObligation': [
                    {
                      'PerformanceObligation-RevenueSchedulingTemplate': [
                        'revenueSchedulingEvent',
                      ],
                    },
                    {
                      'PerformanceObligationAccountMapping-PerformanceObligation':
                        [
                          'deferredRevenueAccountId',
                          'revenueAccountId',
                          'entityId',
                          'bookId',
                        ],
                    },
                  ],
                },
              ],
            },
          ],
        },
      ]
    );
    for (const invoiceLine of invoiceLines) {
      const pobFromRevContractLine = invoiceLine[
        'InvoiceLine-InvoicePlanLine'
      ]?.['InvoicePlanLine-InvoicePlan']?.[
        'InvoicePlan-SalesOrderProductLine'
      ]?.['RevenueContractLineMapping-SalesOrderProductLine']
        ?.filter(
          (e) =>
            !e['RevenueContractLineMapping-RevenueContractLine'].isClosed &&
            !e['RevenueContractLineMapping-RevenueContractLine'][
              'RevenueContractLineHierarchy-RevenueContractLine'
            ]?.parentId
        )
        ?.at(0)?.['RevenueContractLineMapping-RevenueContractLine'][
        'RevenueContractLine-PerformanceObligation'
      ];
      const pobFromProduct = invoiceLine['InvoiceLine-Product'][
        'POBProductLine-Product'
      ].find((e) =>
        toDateTime(e.effectiveStartDate) <=
          toDateTime(invoiceLine.invoiceDate) && e.effectiveEndDate
          ? toDateTime(e.effectiveEndDate) >=
            toDateTime(invoiceLine.invoiceDate)
          : true
      )?.['POBProductLine-PerformanceObligation'];
      const source = pobFromRevContractLine || pobFromProduct;
      const pobAccountMapping = source?.[
        'PerformanceObligationAccountMapping-PerformanceObligation'
      ]?.find((item) => item.bookId === bookId && item.entityId === entityId);
      const targetAccount =
        source['PerformanceObligation-RevenueSchedulingTemplate']
          .revenueSchedulingEvent ===
        EvstRevenueSchedulingEvent.RevenueArrangementCreation
          ? pobAccountMapping.deferredRevenueAccountId
          : pobAccountMapping.revenueAccountId;

      invoiceLineAccountMap.set(invoiceLine.id, targetAccount);
    }

    return invoiceLineAccountMap;
  }
}

export default QueryCreditMemoAccountingDataAction.execute;
