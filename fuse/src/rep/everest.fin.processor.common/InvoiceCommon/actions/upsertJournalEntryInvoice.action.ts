import type { ISession } from '@everestsystems/content-core';
import type { PlainDate } from '@everestsystems/datetime';
import { Decimal } from '@everestsystems/decimal';
import type { EvstNodeReference } from '@pkg/everest.appserver/types/composites/metadata/NodeReference';
import type { Entity } from '@pkg/everest.base/types/Entity';
import type {
  JEHeaderForCreation,
  JELineForCreation,
} from '@pkg/everest.fin.accounting/public/journalEntry/journalEntryTypes';
import type { Account } from '@pkg/everest.fin.accounting/types/Account';
import type { Book } from '@pkg/everest.fin.accounting/types/Book';
import type { Customer } from '@pkg/everest.fin.accounting/types/Customer';
import type { EntityBook } from '@pkg/everest.fin.accounting/types/EntityBook';
import { EvstInvoiceLineType } from '@pkg/everest.fin.accounting/types/enums/InvoiceLineType';
import { EvstJournalEntryLineType } from '@pkg/everest.fin.accounting/types/enums/JournalEntryLineType';
import { EvstJournalEntryStatus } from '@pkg/everest.fin.accounting/types/enums/JournalEntryStatus';
import { EvstJournalEntryType } from '@pkg/everest.fin.accounting/types/enums/JournalEntryType';
import { EvstWorkflowApproval } from '@pkg/everest.fin.accounting/types/enums/WorkflowApproval';
import type { Invoice } from '@pkg/everest.fin.accounting/types/Invoice';
import { JournalEntryHeader } from '@pkg/everest.fin.accounting/types/JournalEntryHeader';
import { JournalEntryLine } from '@pkg/everest.fin.accounting/types/JournalEntryLine';
import type { UpsertJournalEntryInvoice } from '@pkg/everest.fin.processor.common/public/types/UpsertJournalEntryInvoice';
import getEntityBookWithControl from '@pkg/everest.fin.processor.common/public/utils/getEntityBookWithControl';
import type { EvstEntityBook } from '@pkg/everest.fin.processor/types/composites/EntityBook';
import { isEmpty, uniqBy } from 'lodash';

export namespace UpsertJournalEntryInvoiceAction {
  export async function execute(
    session: ISession,
    object: EvstNodeReference,
    entityBooks: Array<EvstEntityBook>,
    previousStageResult: UpsertJournalEntryInvoice.PreviousStageResult,
    processParameters: UpsertJournalEntryInvoice.ProcessParameters
  ): Promise<UpsertJournalEntryInvoice.Result | undefined> {
    const { invoiceData, invoiceAccountingData } = previousStageResult;
    if (isEmpty(invoiceData)) {
      throw new Error(
        `Error reading invoiceData from previous stage result for object with ID(${object.id})`
      );
    }
    if (isEmpty(invoiceAccountingData)) {
      throw new Error(
        `Error reading invoiceAccountingData from previous stage result for object with ID(${object.id})`
      );
    }

    const entityBook = getEntityBookWithControl(entityBooks);

    // False is default value
    const forceJECreation = processParameters?.forceJECreation ?? false;

    const {
      defaultDeferredRevenueAccountId,
      lateFeeAccountId,
      salesTaxAccountId,
      oneTimeDiscountAccountId,
      customerReceivablesAccountId,
      performanceObligations,
      revenueContractLines,
      invoiceLinesWithJournalEntries,
      isFirstJournalEntry,
      invoiceJournalEntryHeader,
      currentJournalEntryLines,
    } = invoiceAccountingData;

    const { invoiceHeader } = invoiceData;

    const invoiceLines = invoiceData.invoiceLines.filter(
      (item) =>
        currentJournalEntryLines?.some(
          (line) => line.sourceTransLineId === item.id
        ) || !invoiceLinesWithJournalEntries.includes(item.id)
    );

    if (isEmpty(invoiceLines)) {
      return;
    }

    const invoiceLineLateFeeDates =
      invoiceLines
        .filter((invoiceLine) => Boolean(invoiceLine.lateFeeDate))
        ?.map((invoiceLine) => invoiceLine.lateFeeDate) ?? [];

    const journalEntryDates = uniqBy(
      [invoiceHeader.invoiceDate, ...invoiceLineLateFeeDates],
      (plainDate) => plainDate.toISO()
    );

    for (const journalEntryDate of journalEntryDates) {
      const invoiceLinesFilteredByDate = invoiceLines.filter((invoiceLine) =>
        journalEntryDate.equals(invoiceHeader.invoiceDate)
          ? !invoiceLine.lateFeeDate
          : invoiceLine.lateFeeDate &&
            journalEntryDate.equals(invoiceLine.lateFeeDate)
      );

      const journalEntryLines = buildJournalEntryLines(
        invoiceHeader,
        invoiceLinesFilteredByDate,
        invoiceHeader.Customer.businessPartnerId,
        customerReceivablesAccountId,
        defaultDeferredRevenueAccountId,
        lateFeeAccountId,
        salesTaxAccountId,
        oneTimeDiscountAccountId,
        performanceObligations,
        revenueContractLines,
        isFirstJournalEntry,
        journalEntryDate,
        entityBook.id
      );

      if (journalEntryLines.length > 0) {
        const journalEntryHeaderClient =
          await JournalEntryHeader.client(session);

        if (invoiceJournalEntryHeader?.id) {
          await JournalEntryLine.deleteMany(session, {
            id: {
              $in: currentJournalEntryLines.map((item) => item.id),
            },
          });
          await journalEntryHeaderClient.updateJournalEntry(
            {
              id: invoiceJournalEntryHeader?.id,
              postingDate: invoiceData.invoiceHeader.invoiceDate,
              approvalStatus: EvstWorkflowApproval.ApprovedWithChanges,
            } as JournalEntryHeader.JournalEntryHeader,
            journalEntryLines as JournalEntryLine.JournalEntryLine[]
          );
        } else {
          const journalEntryHeader = buildJournalEntryHeader(
            invoiceHeader.id,
            journalEntryDate,
            entityBook.bookId
          );
          await journalEntryHeaderClient.createSystemJournalEntry(
            journalEntryHeader as JournalEntryHeader.JournalEntryHeader,
            journalEntryLines as JournalEntryLine.JournalEntryLine[],
            undefined,
            undefined,
            undefined,
            undefined,
            forceJECreation
          );
        }
      }
    }
  }

  type InvoiceAccountingData =
    UpsertJournalEntryInvoice.PreviousStageResult['invoiceAccountingData'];
  type InvoiceData =
    UpsertJournalEntryInvoice.PreviousStageResult['invoiceData'];

  export function buildJournalEntryLines(
    invoice: InvoiceData['invoiceHeader'],
    invoiceLines: InvoiceData['invoiceLines'],
    businessPartnerId: Customer.Customer['businessPartnerId'],
    customerReceivablesAccountId: Account.Account['id'],
    defaultDeferredRevenueAccountId: Account.Account['id'],
    lateFeeAccountId: InvoiceAccountingData['lateFeeAccountId'],
    salesTaxAccountId: InvoiceAccountingData['salesTaxAccountId'],
    oneTimeDiscountAccountId: InvoiceAccountingData['oneTimeDiscountAccountId'],
    performanceObligations: InvoiceAccountingData['performanceObligations'],
    revenueContractLines: InvoiceAccountingData['revenueContractLines'],
    isFirstJournalEntry: boolean,
    journalEntryDate: PlainDate,
    entityBookId: EntityBook.EntityBook['id']
  ): JELineForCreation[] {
    if (invoiceLines.length === 0) {
      return [];
    }

    const baseJournalEntryLine: UpsertJournalEntryInvoice.BaseJournalEntry = {
      businessPartnerId,
      transactionDate: journalEntryDate,
      sourceTransLine: 'invoiceLineNumber',
      sourceTransLinePath: 'InvoiceLine.id',
      sourceTransLineType: 'InvoiceLine',
      transactionalCurrency: invoice.currency,
      entityId: invoice.entityId,
    };

    let jeLines: Omit<JELineForCreation, 'jelIndex' | 'jeLocalLineNumber'>[] =
      [];

    const normalInvoiceJELines = buildNormalJournalEntryLines(
      invoiceLines,
      salesTaxAccountId,
      invoice.entityId,
      customerReceivablesAccountId,
      defaultDeferredRevenueAccountId,
      baseJournalEntryLine,
      performanceObligations,
      revenueContractLines,
      entityBookId
    );
    jeLines = jeLines.concat(normalInvoiceJELines);

    const oneTimeDiscountInvoiceJELines = buildOneTimeDiscountJournalEntryLines(
      invoiceLines,
      oneTimeDiscountAccountId,
      invoice.entityId,
      customerReceivablesAccountId,
      baseJournalEntryLine
    );
    jeLines = jeLines.concat(oneTimeDiscountInvoiceJELines);

    const lateFeeInvoiceJELines = buildLateFeeJournalEntryLines(
      invoiceLines,
      lateFeeAccountId,
      invoice.entityId,
      customerReceivablesAccountId,
      baseJournalEntryLine
    );
    jeLines = jeLines.concat(lateFeeInvoiceJELines);

    if (isFirstJournalEntry) {
      const salesTaxInvoiceJELines = buildSalesTaxJournalEntryLines(
        invoice,
        invoiceLines,
        salesTaxAccountId,
        customerReceivablesAccountId,
        businessPartnerId
      );
      jeLines = jeLines.concat(salesTaxInvoiceJELines);
    }

    const journalEntryLines = Array.from(Object.values(jeLines));

    let jeLocalLineNumber = 1;
    const journalEntryLinesWithNumber = journalEntryLines.map(
      (journalEntryLines) => {
        jeLocalLineNumber += 1;
        return {
          ...journalEntryLines,
          jeLocalLineNumber: jeLocalLineNumber - 1,
        };
      }
    );

    return journalEntryLinesWithNumber;
  }

  export function buildNormalJournalEntryLines(
    invoiceLines: InvoiceData['invoiceLines'],
    salesTaxAccountId: InvoiceAccountingData['salesTaxAccountId'],
    entityId: Entity.Entity['id'],
    customerReceivablesAccountId: InvoiceAccountingData['customerReceivablesAccountId'],
    defaultDeferredRevenueAccountId: InvoiceAccountingData['defaultDeferredRevenueAccountId'],
    baseJournalEntryLine: UpsertJournalEntryInvoice.BaseJournalEntry,
    performanceObligations: InvoiceAccountingData['performanceObligations'],
    revenueContractLines: InvoiceAccountingData['revenueContractLines'],
    entityBookId: EntityBook.EntityBook['id']
  ): Omit<JELineForCreation, 'jelIndex' | 'jeLocalLineNumber'>[] {
    const jeLines: Omit<JELineForCreation, 'jelIndex' | 'jeLocalLineNumber'>[] =
      [];

    const normalInvoiceLines = invoiceLines.filter(
      (invoiceLine) =>
        invoiceLine.lineType !== EvstInvoiceLineType.OneTimeDiscount &&
        invoiceLine.lineType !== EvstInvoiceLineType.LateFee
    );
    for (const normalInvoiceLine of normalInvoiceLines) {
      /**
       * @desc: For usage order is defaultRevenueAccountId, direct orders defaultDeferredRevAccountId
       */

      const { id, productId, lineType, salesOrderId, businessUnit, taxAmount } =
        normalInvoiceLine;

      const performanceObligation = performanceObligations.find(
        (performanceObligation) =>
          performanceObligation['POBProductLine-PerformanceObligation']?.some(
            (item) => item['POBProductLine-Product']?.id === productId
          )
      );
      const pobAccountMapping = performanceObligation
        ? performanceObligation[
            'PerformanceObligationAccountMapping-PerformanceObligation'
          ]?.find((item) => item.entityBookId === entityBookId)
        : null;

      let revenueDefaultAccountId = [
        EvstInvoiceLineType.OneTime,
        EvstInvoiceLineType.Recurring,
        EvstInvoiceLineType.Commit,
      ].includes(lineType)
        ? pobAccountMapping?.deferredRevenueAccountId
        : pobAccountMapping?.revenueAccountId;

      const revenueContractLine = revenueContractLines.find(
        (revenueContractLine) => {
          const salesOrderProduct =
            revenueContractLine?.[
              'RevenueContractLine-SalesOrderProductLine'
            ]?.['SalesOrderProductLine-SalesOrderProduct'];
          const product = salesOrderProduct?.['SalesOrderProduct-Product'];
          const invoiceLine =
            salesOrderProduct?.['SalesOrderProduct-SalesOrder']?.[
              'InvoiceLine-SalesOrder'
            ]?.[0];
          return invoiceLine?.id === id && product?.id === productId;
        }
      );
      if (
        lineType === EvstInvoiceLineType.Overage &&
        revenueContractLine &&
        revenueContractLine.variableConsideration?.amount?.greaterThan(
          new Decimal(0)
        )
      ) {
        revenueDefaultAccountId = pobAccountMapping?.deferredRevenueAccountId;
      }

      jeLines.push(
        {
          ...baseJournalEntryLine,
          sourceTransLineId: id,
          salesOrderId,
          businessUnitId: businessUnit,
          accountId: revenueDefaultAccountId ?? defaultDeferredRevenueAccountId,
          credit: {
            amount: normalInvoiceLine.totalPrice.amount,
          },
        },
        {
          ...baseJournalEntryLine,
          sourceTransLineId: id,
          salesOrderId,
          businessUnitId: businessUnit,
          accountId: customerReceivablesAccountId,
          debit: {
            amount: normalInvoiceLine.amount.amount,
          },
        }
      );

      if (taxAmount?.amount?.greaterThan(0)) {
        if (!salesTaxAccountId) {
          /**
           * Translation not needed
           */
          throw new Error(
            `Couldn't find salesTaxAccount for entity with id ${entityId}. Go to Quote to Cash > Invoice Configuration and select the salesTaxAccount for this entity.`
          );
        }

        jeLines.push({
          ...baseJournalEntryLine,
          sourceTransLineId: id,
          salesOrderId,
          businessUnitId: businessUnit,
          accountId: salesTaxAccountId,
          credit: {
            amount: taxAmount.amount,
          },
        });
      }
    }

    return jeLines;
  }

  export function buildOneTimeDiscountJournalEntryLines(
    invoiceLines: InvoiceData['invoiceLines'],
    oneTimeDiscountAccountId: InvoiceAccountingData['oneTimeDiscountAccountId'],
    entityId: Entity.Entity['id'],
    customerReceivablesAccountId: number,
    baseJournalEntryLine: UpsertJournalEntryInvoice.BaseJournalEntry
  ): Omit<JELineForCreation, 'jelIndex' | 'jeLocalLineNumber'>[] {
    const jeLines: Omit<JELineForCreation, 'jelIndex' | 'jeLocalLineNumber'>[] =
      [];

    const oneTimeDiscountLine = invoiceLines.find(
      (invoiceLine) =>
        invoiceLine.lineType === EvstInvoiceLineType.OneTimeDiscount
    );

    if (oneTimeDiscountLine) {
      const { id, salesOrderId, businessUnit, totalPrice } =
        oneTimeDiscountLine;

      if (!oneTimeDiscountAccountId) {
        /**
         * Translation not needed
         */
        throw new Error(
          `Couldn't find oneTimeDiscountAccount for entity with id ${entityId}. Go to Quote to Cash > Invoice Configuration and select the oneTimeDiscountAccount for this entity.`
        );
      }

      jeLines.push(
        {
          ...baseJournalEntryLine,
          sourceTransLineId: id,
          salesOrderId,
          businessUnitId: businessUnit,
          accountId: customerReceivablesAccountId,
          credit: {
            amount: totalPrice.amount.times(-1),
          },
        },
        {
          ...baseJournalEntryLine,
          sourceTransLineId: id,
          salesOrderId,
          businessUnitId: businessUnit,
          accountId: oneTimeDiscountAccountId,
          debit: {
            amount: oneTimeDiscountLine.amount.amount.times(-1),
          },
        }
      );
    }

    return jeLines;
  }

  export function buildLateFeeJournalEntryLines(
    invoiceLines: InvoiceData['invoiceLines'],
    lateFeeAccountId: InvoiceAccountingData['lateFeeAccountId'],
    entityId: Entity.Entity['id'],
    customerReceivablesAccountId: number,
    baseJournalEntryLine: UpsertJournalEntryInvoice.BaseJournalEntry
  ): Omit<JELineForCreation, 'jelIndex' | 'jeLocalLineNumber'>[] {
    const jeLines: Omit<JELineForCreation, 'jelIndex' | 'jeLocalLineNumber'>[] =
      [];

    const lateFeeLines = invoiceLines.filter(
      (invoiceLine) => invoiceLine.lineType === EvstInvoiceLineType.LateFee
    );
    if (lateFeeLines.length > 0) {
      if (!lateFeeAccountId) {
        /**
         * Translation not needed
         */
        throw new Error(
          `Couldn't find lateFeesAccount for entity with id ${entityId}. Go to Quote to Cash > Invoice Configuration and select the lateFeesAccount for this entity.`
        );
      }
      for (const lateFeeLine of lateFeeLines) {
        const { id, salesOrderId, businessUnit, totalPrice, amount } =
          lateFeeLine;

        jeLines.push(
          {
            ...baseJournalEntryLine,
            sourceTransLineId: id,
            salesOrderId,
            businessUnitId: businessUnit,
            accountId: lateFeeAccountId,
            credit: {
              amount: totalPrice.amount,
            },
          },
          {
            ...baseJournalEntryLine,
            sourceTransLineId: id,
            salesOrderId,
            businessUnitId: businessUnit,
            accountId: customerReceivablesAccountId,
            debit: {
              amount: amount.amount,
            },
          }
        );
      }
    }

    return jeLines;
  }

  export function buildSalesTaxJournalEntryLines(
    invoice: InvoiceData['invoiceHeader'],
    invoiceLines: InvoiceData['invoiceLines'],
    salesTaxAccountId: InvoiceAccountingData['salesTaxAccountId'],
    customerReceivablesAccountId: number,
    businessPartnerId: Customer.Customer['businessPartnerId']
  ): Omit<JELineForCreation, 'jelIndex' | 'jeLocalLineNumber'>[] {
    const jeLines: Omit<JELineForCreation, 'jelIndex' | 'jeLocalLineNumber'>[] =
      [];

    const isAllInvoiceLinesWithoutTaxAmountAmount = invoiceLines.every(
      (invoiceLine) =>
        !invoiceLine?.taxAmount?.amount ||
        invoiceLine.taxAmount.amount.equals(0)
    );
    if (
      isAllInvoiceLinesWithoutTaxAmountAmount &&
      invoice.totalTax.amount.greaterThan(0)
    ) {
      if (!salesTaxAccountId) {
        /**
         * Translation not needed
         */
        throw new Error(
          `Couldn't find salesTaxAccount for entity with id ${invoice.entityId}. Go to Quote to Cash > Invoice Configuration and select the salesTaxAccount for this entity.`
        );
      }

      const baseJelObject = {
        businessPartnerId,
        transactionDate: invoice.invoiceDate,
        transactionalCurrency: invoice.currency,
        entityId: invoice.entityId,
      };
      jeLines.push(
        {
          ...baseJelObject,
          accountId: salesTaxAccountId,
          credit: {
            amount: invoice.totalTax.amount,
          },
        },
        {
          ...baseJelObject,
          accountId: customerReceivablesAccountId,
          debit: {
            amount: invoice.totalTax.amount,
          },
        }
      );
    }

    return jeLines;
  }

  export function buildJournalEntryHeader(
    invoiceId: Invoice.Invoice['id'],
    postingDate: PlainDate,
    bookId: Book.Book['id']
  ): JEHeaderForCreation {
    return {
      postingDate,
      sourceFinancialDocumentId: invoiceId,
      sourceFinancialDocument: 'invoiceNumber',
      sourceFinancialDocumentPath: 'Invoice.id',
      sourceFinancialDocumentType: 'Invoice',
      type: EvstJournalEntryType.Invoice,
      approvalStatus: EvstWorkflowApproval.Approved,
      journalEntryStatus: EvstJournalEntryStatus.Posted,
      journalEntryName: `From Invoice`,
      bookId,
    };
  }

  export async function getExistingJournalEntryLines(
    session: ISession,
    journalEntryId: Entity.Entity['id']
  ): Promise<Awaited<ReturnType<typeof JournalEntryLine.query>>> {
    const existingJELines = await JournalEntryLine.query(
      session,
      {
        where: {
          journalEntryHeaderId: journalEntryId,
          jeLineType: EvstJournalEntryLineType.Standard,
        },
      },
      [
        'id',
        'sourceTransLineId',
        'sourceTransLineType',
        'accountId',
        'debit',
        'credit',
        'functionalCurrency',
        'entityId',
        'transactionalCurrency',
        'jeLocalLineNumber',
        'businessPartnerId',
      ]
    );

    return existingJELines;
  }
}

export default UpsertJournalEntryInvoiceAction.execute;
