import type { ISession } from '@everestsystems/content-core';
import { Decimal } from '@everestsystems/decimal';
import { toPlainDate } from '@pkg/everest.base/public/utils/Date/date';
import { EvstInvoiceLineType } from '@pkg/everest.fin.accounting/types/enums/InvoiceLineType';
import { EvstJournalEntryStatus } from '@pkg/everest.fin.accounting/types/enums/JournalEntryStatus';
import { EvstJournalEntryType } from '@pkg/everest.fin.accounting/types/enums/JournalEntryType';
import { EvstWorkflowApproval } from '@pkg/everest.fin.accounting/types/enums/WorkflowApproval';
import { JournalEntryHeader } from '@pkg/everest.fin.accounting/types/JournalEntryHeader';
import type { QueryInvoiceData } from '@pkg/everest.fin.processor.base/public/types/QueryInvoiceData';
import { UpsertJournalEntryInvoiceAction } from '@pkg/everest.fin.processor.common/InvoiceCommon/actions/upsertJournalEntryInvoice.action';
import type { QueryInvoiceAccountingData } from '@pkg/everest.fin.processor.common/public/types/QueryInvoiceAccountingData';
import type { EvstEntityBook } from '@pkg/everest.fin.processor/types/composites/EntityBook';

jest.mock(
  '@pkg/everest.fin.accounting/types/BOComposition/InvoiceBusinessObject'
);
jest.mock('@pkg/everest.fin.accounting/types/InvoiceLine');
jest.mock('@pkg/everest.fin.accounting/types/Customer');
jest.mock('@pkg/everest.fin.accounting/types/PerformanceObligation');
jest.mock('@pkg/everest.fin.accounting/types/RevenueContractLine');
jest.mock('@pkg/everest.fin.accounting/types/InvoiceConfig');
jest.mock('@pkg/everest.fin.accounting/types/JournalEntryHeader');
jest.mock('@pkg/everest.fin.accounting/types/JournalEntryLine');

describe('UpsertJournalEntryInvoice', () => {
  const mockSession = {} as ISession;

  const invoiceBusinessObjectId = 1;
  const businessPartnerId = 2;
  const salesOrderId = 3;
  const entityId = 4;
  const entityBookId = 17;
  const bookId = 18;
  const defaultDeferredRevenueAccountId = 100;
  const customerReceivablesAccountId = 200;
  const bookDate = toPlainDate('2023-07-01');
  const salesTaxAccountId = 300;
  const oneTimeDiscountAccountId = 400;
  const lateFeeAccountId = 500;

  const createSystemJournalEntryMock = jest.fn();
  const updateJournalEntryMock = jest.fn();

  beforeAll(() => {});

  beforeEach(() => {
    jest.clearAllMocks();
    JournalEntryHeader.client = jest.fn().mockResolvedValue({
      createSystemJournalEntry: createSystemJournalEntryMock,
      updateJournalEntry: updateJournalEntryMock,
    });
  });

  describe('When successfully create normal (with tax), oneTimeDiscount and lateFee Journal Entry', () => {
    const invoiceHeader: QueryInvoiceData.Result['invoiceHeader'] = {
      id: 5,
      businessPartnerId: 6,
      currency: 'USD',
      invoiceDate: toPlainDate('2023-07-01'),
      invoiceNumber: 'INV-001',
      totalTax: { amount: new Decimal(10), currency: 'USD' },
      entityId,
      createdFromId: 101,
      Customer: { businessPartnerId },
      SalesOrder: { bookDate },
    };
    const invoiceLines: QueryInvoiceData.Result['invoiceLines'] = [
      {
        lineType: EvstInvoiceLineType.Recurring,
        id: 7,
        productId: 8,
        businessUnit: 100,
        amount: { amount: new Decimal(100), currency: 'USD' },
        taxAmount: { amount: new Decimal(10), currency: 'USD' },
        totalPrice: { amount: new Decimal(110), currency: 'USD' },
        salesOrderId,
      },
      {
        lineType: EvstInvoiceLineType.OneTimeDiscount,
        id: 9,
        productId: 10,
        businessUnit: 200,
        amount: { amount: new Decimal(200), currency: 'USD' },
        totalPrice: { amount: new Decimal(300), currency: 'USD' },
        salesOrderId,
      },
      {
        lineType: EvstInvoiceLineType.LateFee,
        id: 11,
        productId: 12,
        businessUnit: 300,
        amount: { amount: new Decimal(400), currency: 'USD' },
        totalPrice: { amount: new Decimal(500), currency: 'USD' },
        salesOrderId,
      },
    ];

    const performanceObligations = [
      {
        id: 13,
        'POBProductLine-PerformanceObligation': [
          { 'POBProductLine-Product': { id: invoiceLines[0].productId } },
        ],
        'PerformanceObligationAccountMapping-PerformanceObligation': [
          {
            bookId: 15,
            entityId: 16,
            entityBookId: entityBookId,
            revenueAccountId: 700,
            deferredRevenueAccountId: 600,
          },
        ],
      },
    ] as unknown as QueryInvoiceAccountingData.Result['performanceObligations'];

    it('should return undefined and call the createSystemJournalEntry with right journal entry header and lines', async () => {
      const forceJECreation = undefined;
      const result = await UpsertJournalEntryInvoiceAction.execute(
        mockSession,
        {
          modelUrn: 'urn:everest:Invoice',
          id: invoiceBusinessObjectId,
        },
        [{ id: entityBookId, bookId }] as Array<EvstEntityBook>,
        {
          invoiceData: {
            invoiceHeader,
            invoiceLines,
          },
          invoiceAccountingData: {
            performanceObligations,
            defaultDeferredRevenueAccountId,
            salesTaxAccountId,
            oneTimeDiscountAccountId,
            lateFeeAccountId,
            isFirstJournalEntry: true,
            customerReceivablesAccountId,
            invoiceLinesWithJournalEntries: [],
            revenueContractLines: [],
            invoiceJournalEntryHeader: undefined,
            currentJournalEntryLines: undefined,
          },
        },
        {
          forceJECreation,
        }
      );

      expect(result).toBeUndefined();

      const baseExpected = {
        businessPartnerId,
        transactionDate: invoiceHeader.invoiceDate,
        sourceTransLine: 'invoiceLineNumber',
        sourceTransLinePath: 'InvoiceLine.id',
        sourceTransLineType: 'InvoiceLine',
        transactionalCurrency: 'USD',
        entityId: invoiceHeader.entityId,
      };
      expect(createSystemJournalEntryMock).toHaveBeenCalledTimes(1);
      expect(createSystemJournalEntryMock).toHaveBeenNthCalledWith(
        1,
        {
          postingDate: invoiceHeader.invoiceDate,
          sourceFinancialDocumentId: invoiceHeader.id,
          sourceFinancialDocument: 'invoiceNumber',
          sourceFinancialDocumentPath: 'Invoice.id',
          sourceFinancialDocumentType: 'Invoice',
          type: EvstJournalEntryType.Invoice,
          approvalStatus: EvstWorkflowApproval.Approved,
          journalEntryStatus: EvstJournalEntryStatus.Posted,
          journalEntryName: `From Invoice`,
          bookId,
        },
        [
          {
            ...baseExpected,
            sourceTransLineId: invoiceLines[0].id,
            salesOrderId: invoiceLines[0].salesOrderId,
            businessUnitId: invoiceLines[0].businessUnit,
            accountId:
              performanceObligations[0][
                'PerformanceObligationAccountMapping-PerformanceObligation'
              ][0].deferredRevenueAccountId,
            jeLocalLineNumber: 1,
            credit: {
              amount: invoiceLines[0].totalPrice.amount,
            },
          },
          {
            ...baseExpected,
            sourceTransLineId: invoiceLines[0].id,
            salesOrderId: invoiceLines[0].salesOrderId,
            businessUnitId: invoiceLines[0].businessUnit,
            accountId: customerReceivablesAccountId,
            jeLocalLineNumber: 2,
            debit: {
              amount: invoiceLines[0].amount.amount,
            },
          },
          {
            ...baseExpected,
            sourceTransLineId: invoiceLines[0].id,
            salesOrderId: invoiceLines[0].salesOrderId,
            businessUnitId: invoiceLines[0].businessUnit,
            accountId: salesTaxAccountId,
            jeLocalLineNumber: 3,
            credit: {
              amount: invoiceLines[0].taxAmount.amount,
            },
          },
          {
            ...baseExpected,
            sourceTransLineId: invoiceLines[1].id,
            salesOrderId: invoiceLines[1].salesOrderId,
            businessUnitId: invoiceLines[1].businessUnit,
            accountId: customerReceivablesAccountId,
            jeLocalLineNumber: 4,
            credit: {
              amount: invoiceLines[1].totalPrice.amount.times(-1),
            },
          },
          {
            ...baseExpected,
            sourceTransLineId: invoiceLines[1].id,
            salesOrderId: invoiceLines[1].salesOrderId,
            businessUnitId: invoiceLines[1].businessUnit,
            accountId: oneTimeDiscountAccountId,
            jeLocalLineNumber: 5,
            debit: {
              amount: invoiceLines[1].amount.amount.times(-1),
            },
          },
          {
            ...baseExpected,
            sourceTransLineId: invoiceLines[2].id,
            salesOrderId: invoiceLines[2].salesOrderId,
            businessUnitId: invoiceLines[2].businessUnit,
            accountId: lateFeeAccountId,
            jeLocalLineNumber: 6,
            credit: {
              amount: invoiceLines[2].totalPrice.amount,
            },
          },
          {
            ...baseExpected,
            sourceTransLineId: invoiceLines[2].id,
            salesOrderId: invoiceLines[2].salesOrderId,
            businessUnitId: invoiceLines[2].businessUnit,
            accountId: customerReceivablesAccountId,
            jeLocalLineNumber: 7,
            debit: {
              amount: invoiceLines[2].amount.amount,
            },
          },
        ],
        undefined,
        undefined,
        undefined,
        undefined,
        !!forceJECreation
      );
    });
  });

  describe('When successfully create normal overage with variableConsideration', () => {
    const invoiceHeader: QueryInvoiceData.Result['invoiceHeader'] = {
      id: 5,
      businessPartnerId: 6,
      currency: 'USD',
      invoiceDate: toPlainDate('2023-07-01'),
      invoiceNumber: 'INV-001',
      totalTax: { amount: new Decimal(0), currency: 'USD' },
      entityId,
      createdFromId: 101,
      Customer: { businessPartnerId },
      SalesOrder: { bookDate },
    };

    const invoiceLines: QueryInvoiceData.Result['invoiceLines'] = [
      {
        lineType: EvstInvoiceLineType.Overage,
        id: 7,
        productId: 8,
        businessUnit: 100,
        amount: { amount: new Decimal(100), currency: 'USD' },
        totalPrice: { amount: new Decimal(100), currency: 'USD' },
        salesOrderId,
      },
    ];

    const performanceObligations = [
      {
        id: 13,
        'POBProductLine-PerformanceObligation': [
          { 'POBProductLine-Product': { id: invoiceLines[0].productId } },
        ],
        'PerformanceObligationAccountMapping-PerformanceObligation': [
          {
            bookId: 15,
            entityId: 16,
            entityBookId: entityBookId,
            revenueAccountId: 700,
            deferredRevenueAccountId: 600,
          },
        ],
      },
    ] as unknown as QueryInvoiceAccountingData.Result['performanceObligations'];

    const revenueContractLines: QueryInvoiceAccountingData.Result['revenueContractLines'] =
      [
        {
          id: 20,
          variableConsideration: {
            amount: new Decimal(250),
            currency: 'USD',
          },
          'RevenueContractLine-SalesOrderProductLine': {
            'SalesOrderProductLine-SalesOrderProduct': {
              'SalesOrderProduct-Product': {
                id: invoiceLines[0].productId,
              },
              'SalesOrderProduct-SalesOrder': {
                'InvoiceLine-SalesOrder': [{ id: invoiceLines[0].id }],
              },
            },
          },
        },
      ];

    it('should return undefined and call the createSystemJournalEntry with right journal entry header and lines', async () => {
      const forceJECreation = undefined;
      const result = await UpsertJournalEntryInvoiceAction.execute(
        mockSession,
        {
          modelUrn: 'urn:everest:Invoice',
          id: invoiceBusinessObjectId,
        },
        [{ id: entityBookId, bookId }] as Array<EvstEntityBook>,
        {
          invoiceData: {
            invoiceHeader,
            invoiceLines,
          },
          invoiceAccountingData: {
            performanceObligations,
            defaultDeferredRevenueAccountId,
            salesTaxAccountId,
            oneTimeDiscountAccountId,
            lateFeeAccountId,
            isFirstJournalEntry: true,
            customerReceivablesAccountId,
            revenueContractLines,
            invoiceLinesWithJournalEntries: [],
            invoiceJournalEntryHeader: undefined,
            currentJournalEntryLines: undefined,
          },
        },
        {
          forceJECreation,
        }
      );

      expect(result).toBeUndefined();

      const baseExpected = {
        businessPartnerId,
        transactionDate: invoiceHeader.invoiceDate,
        sourceTransLine: 'invoiceLineNumber',
        sourceTransLinePath: 'InvoiceLine.id',
        sourceTransLineType: 'InvoiceLine',
        transactionalCurrency: 'USD',
        entityId: invoiceHeader.entityId,
      };
      expect(createSystemJournalEntryMock).toHaveBeenCalledTimes(1);
      expect(createSystemJournalEntryMock).toHaveBeenNthCalledWith(
        1,
        {
          postingDate: invoiceHeader.invoiceDate,
          sourceFinancialDocumentId: invoiceHeader.id,
          sourceFinancialDocument: 'invoiceNumber',
          sourceFinancialDocumentPath: 'Invoice.id',
          sourceFinancialDocumentType: 'Invoice',
          type: EvstJournalEntryType.Invoice,
          approvalStatus: EvstWorkflowApproval.Approved,
          journalEntryStatus: EvstJournalEntryStatus.Posted,
          journalEntryName: `From Invoice`,
          bookId,
        },
        [
          {
            ...baseExpected,
            sourceTransLineId: invoiceLines[0].id,
            salesOrderId: invoiceLines[0].salesOrderId,
            businessUnitId: invoiceLines[0].businessUnit,
            accountId:
              performanceObligations[0][
                'PerformanceObligationAccountMapping-PerformanceObligation'
              ][0].deferredRevenueAccountId,
            jeLocalLineNumber: 1,
            credit: {
              amount: invoiceLines[0].totalPrice.amount,
            },
          },
          {
            ...baseExpected,
            sourceTransLineId: invoiceLines[0].id,
            salesOrderId: invoiceLines[0].salesOrderId,
            businessUnitId: invoiceLines[0].businessUnit,
            accountId: customerReceivablesAccountId,
            jeLocalLineNumber: 2,
            debit: {
              amount: invoiceLines[0].amount.amount,
            },
          },
        ],
        undefined,
        undefined,
        undefined,
        undefined,
        !!forceJECreation
      );
    });
  });

  describe('When successfully create normal overage without variableConsideration', () => {
    const invoiceHeader: QueryInvoiceData.Result['invoiceHeader'] = {
      id: 5,
      businessPartnerId: 6,
      currency: 'USD',
      invoiceDate: toPlainDate('2023-07-01'),
      invoiceNumber: 'INV-001',
      totalTax: { amount: new Decimal(0), currency: 'USD' },
      entityId,
      createdFromId: 101,
      Customer: { businessPartnerId },
      SalesOrder: { bookDate },
    };

    const invoiceLines: QueryInvoiceData.Result['invoiceLines'] = [
      {
        lineType: EvstInvoiceLineType.Overage,
        id: 7,
        productId: 8,
        businessUnit: 100,
        amount: { amount: new Decimal(100), currency: 'USD' },
        totalPrice: { amount: new Decimal(100), currency: 'USD' },
        salesOrderId,
      },
    ];

    const performanceObligations = [
      {
        id: 13,
        'PerformanceObligationAccountMapping-PerformanceObligation': [
          {
            bookId: 15,
            entityId: 16,
            entityBookId: entityBookId,
            revenueAccountId: 700,
            deferredRevenueAccountId: 600,
          },
        ],
        'POBProductLine-PerformanceObligation': [
          { 'POBProductLine-Product': { id: invoiceLines[0].productId } },
        ],
      },
    ] as unknown as QueryInvoiceAccountingData.Result['performanceObligations'];

    const revenueContractLines: QueryInvoiceAccountingData.Result['revenueContractLines'] =
      [
        {
          id: 20,
          'RevenueContractLine-SalesOrderProductLine': {
            'SalesOrderProductLine-SalesOrderProduct': {
              'SalesOrderProduct-Product': {
                id: invoiceLines[0].productId,
              },
              'SalesOrderProduct-SalesOrder': {
                'InvoiceLine-SalesOrder': [{ id: invoiceLines[0].id }],
              },
            },
          },
        },
      ];

    it('should return undefined and call the createSystemJournalEntry with right journal entry header and lines', async () => {
      const forceJECreation = undefined;
      const result = await UpsertJournalEntryInvoiceAction.execute(
        mockSession,
        {
          modelUrn: 'urn:everest:Invoice',
          id: invoiceBusinessObjectId,
        },
        [
          {
            id: entityBookId,
            bookId,
          },
        ] as Array<EvstEntityBook>,
        {
          invoiceData: {
            invoiceHeader,
            invoiceLines,
          },
          invoiceAccountingData: {
            performanceObligations,
            defaultDeferredRevenueAccountId,
            salesTaxAccountId,
            oneTimeDiscountAccountId,
            lateFeeAccountId,
            isFirstJournalEntry: true,
            customerReceivablesAccountId,
            revenueContractLines,
            invoiceLinesWithJournalEntries: [],
            invoiceJournalEntryHeader: undefined,
            currentJournalEntryLines: undefined,
          },
        },
        {
          forceJECreation,
        }
      );

      expect(result).toBeUndefined();

      const baseExpected = {
        businessPartnerId,
        transactionDate: invoiceHeader.invoiceDate,
        sourceTransLine: 'invoiceLineNumber',
        sourceTransLinePath: 'InvoiceLine.id',
        sourceTransLineType: 'InvoiceLine',
        transactionalCurrency: 'USD',
        entityId: invoiceHeader.entityId,
      };
      expect(createSystemJournalEntryMock).toHaveBeenCalledTimes(1);
      expect(createSystemJournalEntryMock).toHaveBeenNthCalledWith(
        1,
        {
          postingDate: invoiceHeader.invoiceDate,
          sourceFinancialDocumentId: invoiceHeader.id,
          sourceFinancialDocument: 'invoiceNumber',
          sourceFinancialDocumentPath: 'Invoice.id',
          sourceFinancialDocumentType: 'Invoice',
          type: EvstJournalEntryType.Invoice,
          approvalStatus: EvstWorkflowApproval.Approved,
          journalEntryStatus: EvstJournalEntryStatus.Posted,
          journalEntryName: `From Invoice`,
          bookId,
        },
        [
          {
            ...baseExpected,
            sourceTransLineId: invoiceLines[0].id,
            salesOrderId: invoiceLines[0].salesOrderId,
            businessUnitId: invoiceLines[0].businessUnit,
            accountId:
              performanceObligations[0][
                'PerformanceObligationAccountMapping-PerformanceObligation'
              ][0].revenueAccountId,
            jeLocalLineNumber: 1,
            credit: {
              amount: invoiceLines[0].totalPrice.amount,
            },
          },
          {
            ...baseExpected,
            sourceTransLineId: invoiceLines[0].id,
            salesOrderId: invoiceLines[0].salesOrderId,
            businessUnitId: invoiceLines[0].businessUnit,
            accountId: customerReceivablesAccountId,
            jeLocalLineNumber: 2,
            debit: {
              amount: invoiceLines[0].amount.amount,
            },
          },
        ],
        undefined,
        undefined,
        undefined,
        undefined,
        !!forceJECreation
      );
    });
  });

  describe('When successfully create normal invoice lines with sales tax only in invoice (no Perfomance Obligation)', () => {
    const invoiceHeader: QueryInvoiceData.Result['invoiceHeader'] = {
      id: 5,
      businessPartnerId: 6,
      currency: 'USD',
      invoiceDate: toPlainDate('2023-07-01'),
      invoiceNumber: 'INV-001',
      totalTax: { amount: new Decimal(100), currency: 'USD' },
      entityId,
      createdFromId: 101,
      Customer: { businessPartnerId },
      SalesOrder: { bookDate },
    };

    const invoiceLines: QueryInvoiceData.Result['invoiceLines'] = [
      {
        lineType: EvstInvoiceLineType.Recurring,
        id: 7,
        productId: 8,
        businessUnit: 100,
        amount: { amount: new Decimal(100), currency: 'USD' },
        totalPrice: { amount: new Decimal(100), currency: 'USD' },
        salesOrderId,
      },
    ];

    const performanceObligations: QueryInvoiceAccountingData.Result['performanceObligations'] =
      [];

    it('should return undefined and call the createSystemJournalEntry with right journal entry header and lines', async () => {
      const forceJECreation = undefined;
      const result = await UpsertJournalEntryInvoiceAction.execute(
        mockSession,
        {
          modelUrn: 'urn:everest:Invoice',
          id: invoiceBusinessObjectId,
        },
        [{ id: entityBookId, bookId }] as Array<EvstEntityBook>,
        {
          invoiceData: {
            invoiceHeader,
            invoiceLines,
          },
          invoiceAccountingData: {
            performanceObligations,
            defaultDeferredRevenueAccountId,
            salesTaxAccountId,
            oneTimeDiscountAccountId,
            lateFeeAccountId,
            isFirstJournalEntry: true,
            customerReceivablesAccountId,
            revenueContractLines: [],
            invoiceLinesWithJournalEntries: [],
            invoiceJournalEntryHeader: undefined,
            currentJournalEntryLines: undefined,
          },
        },
        {
          forceJECreation,
        }
      );

      expect(result).toBeUndefined();

      const baseExpected = {
        businessPartnerId,
        transactionDate: invoiceHeader.invoiceDate,
        sourceTransLine: 'invoiceLineNumber',
        sourceTransLinePath: 'InvoiceLine.id',
        sourceTransLineType: 'InvoiceLine',
        transactionalCurrency: 'USD',
        entityId: invoiceHeader.entityId,
      };
      expect(createSystemJournalEntryMock).toHaveBeenCalledTimes(1);
      expect(createSystemJournalEntryMock).toHaveBeenNthCalledWith(
        1,
        {
          postingDate: invoiceHeader.invoiceDate,
          sourceFinancialDocumentId: invoiceHeader.id,
          sourceFinancialDocument: 'invoiceNumber',
          sourceFinancialDocumentPath: 'Invoice.id',
          sourceFinancialDocumentType: 'Invoice',
          type: EvstJournalEntryType.Invoice,
          approvalStatus: EvstWorkflowApproval.Approved,
          journalEntryStatus: EvstJournalEntryStatus.Posted,
          journalEntryName: `From Invoice`,
          bookId,
        },
        [
          {
            ...baseExpected,
            sourceTransLineId: invoiceLines[0].id,
            salesOrderId: invoiceLines[0].salesOrderId,
            businessUnitId: invoiceLines[0].businessUnit,
            accountId: defaultDeferredRevenueAccountId,
            jeLocalLineNumber: 1,
            credit: {
              amount: invoiceLines[0].totalPrice.amount,
            },
          },
          {
            ...baseExpected,
            sourceTransLineId: invoiceLines[0].id,
            salesOrderId: invoiceLines[0].salesOrderId,
            businessUnitId: invoiceLines[0].businessUnit,
            accountId: customerReceivablesAccountId,
            jeLocalLineNumber: 2,
            debit: {
              amount: invoiceLines[0].amount.amount,
            },
          },
          {
            businessPartnerId,
            transactionDate: invoiceHeader.invoiceDate,
            transactionalCurrency: invoiceHeader.currency,
            entityId: invoiceHeader.entityId,
            accountId: salesTaxAccountId,
            jeLocalLineNumber: 3,
            credit: {
              amount: invoiceHeader.totalTax.amount,
            },
          },
          {
            businessPartnerId,
            transactionDate: invoiceHeader.invoiceDate,
            transactionalCurrency: invoiceHeader.currency,
            entityId: invoiceHeader.entityId,
            accountId: customerReceivablesAccountId,
            jeLocalLineNumber: 4,
            debit: {
              amount: invoiceHeader.totalTax.amount,
            },
          },
        ],
        undefined,
        undefined,
        undefined,
        undefined,
        !!forceJECreation
      );
    });
  });

  describe('When successfully create late fees on existing invoice with sales tax only in invoice (no Perfomance Obligation)', () => {
    const invoiceHeader: QueryInvoiceData.Result['invoiceHeader'] = {
      id: 5,
      businessPartnerId: 6,
      currency: 'USD',
      invoiceDate: toPlainDate('2023-07-01'),
      invoiceNumber: 'INV-001',
      totalTax: { amount: new Decimal(100), currency: 'USD' },
      entityId,
      createdFromId: 101,
      Customer: { businessPartnerId },
      SalesOrder: { bookDate },
    };

    const invoiceLines: QueryInvoiceData.Result['invoiceLines'] = [
      {
        lineType: EvstInvoiceLineType.Recurring,
        id: 7,
        productId: 8,
        businessUnit: 100,
        amount: { amount: new Decimal(100), currency: 'USD' },
        totalPrice: { amount: new Decimal(100), currency: 'USD' },
        salesOrderId,
      },
      {
        lineType: EvstInvoiceLineType.LateFee,
        id: 8,
        lateFeeDate: toPlainDate('2023-09-01'),
        amount: { amount: new Decimal(100), currency: 'USD' },
        totalPrice: { amount: new Decimal(100), currency: 'USD' },
        salesOrderId,
      },
    ];

    const performanceObligations: QueryInvoiceAccountingData.Result['performanceObligations'] =
      [];

    it('should return undefined and call the createSystemJournalEntry with right journal entry header and lines', async () => {
      const forceJECreation = undefined;
      const result = await UpsertJournalEntryInvoiceAction.execute(
        mockSession,
        {
          modelUrn: 'urn:everest:Invoice',
          id: invoiceBusinessObjectId,
        },
        [{ bookId }] as Array<EvstEntityBook>,
        {
          invoiceData: {
            invoiceHeader,
            invoiceLines,
          },
          invoiceAccountingData: {
            performanceObligations,
            defaultDeferredRevenueAccountId,
            salesTaxAccountId,
            oneTimeDiscountAccountId,
            lateFeeAccountId,
            isFirstJournalEntry: false,
            customerReceivablesAccountId,
            revenueContractLines: [],
            invoiceLinesWithJournalEntries: [invoiceLines[0].id],
            invoiceJournalEntryHeader: {
              id: 1,
              postingDate: invoiceHeader.invoiceDate,
            },
            currentJournalEntryLines: [
              { id: 1, sourceTransLineId: invoiceLines[0].id } as any,
            ],
          },
        },
        {
          forceJECreation,
        }
      );

      expect(result).toBeUndefined();

      const baseExpected = {
        businessPartnerId,
        transactionDate: invoiceLines[1].lateFeeDate,
        sourceTransLine: 'invoiceLineNumber',
        sourceTransLinePath: 'InvoiceLine.id',
        sourceTransLineType: 'InvoiceLine',
        transactionalCurrency: 'USD',
        entityId: invoiceHeader.entityId,
      };
      expect(createSystemJournalEntryMock).toHaveBeenCalledTimes(1);
      expect(updateJournalEntryMock).not.toHaveBeenCalled();
      expect(createSystemJournalEntryMock).toHaveBeenNthCalledWith(
        1,
        {
          postingDate: invoiceLines[1].lateFeeDate,
          sourceFinancialDocumentId: invoiceHeader.id,
          sourceFinancialDocument: 'invoiceNumber',
          sourceFinancialDocumentPath: 'Invoice.id',
          sourceFinancialDocumentType: 'Invoice',
          type: EvstJournalEntryType.Invoice,
          approvalStatus: EvstWorkflowApproval.Approved,
          journalEntryStatus: EvstJournalEntryStatus.Posted,
          journalEntryName: `From Invoice`,
          bookId,
        },
        [
          {
            ...baseExpected,
            businessUnitId: invoiceLines[1].businessUnit,
            sourceTransLineId: invoiceLines[1].id,
            salesOrderId: invoiceLines[1].salesOrderId,
            transactionDate: invoiceLines[1].lateFeeDate,
            transactionalCurrency: invoiceHeader.currency,
            entityId: invoiceHeader.entityId,
            accountId: lateFeeAccountId,
            jeLocalLineNumber: 1,
            credit: {
              amount: invoiceLines[1].totalPrice.amount,
            },
          },
          {
            ...baseExpected,
            businessPartnerId,
            sourceTransLineId: invoiceLines[1].id,
            salesOrderId: invoiceLines[1].salesOrderId,
            businessUnitId: invoiceLines[1].businessUnit,
            accountId: customerReceivablesAccountId,
            jeLocalLineNumber: 2,
            debit: {
              amount: invoiceLines[1].amount.amount,
            },
          },
        ],
        undefined,
        undefined,
        undefined,
        undefined,
        !!forceJECreation
      );
    });
  });
});
