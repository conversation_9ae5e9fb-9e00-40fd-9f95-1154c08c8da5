import { getTranslation, type ISession } from '@everestsystems/content-core';
import type { EvstNodeReference } from '@pkg/everest.appserver/types/composites/metadata/NodeReference';
import { EvstAccountConfigurationType } from '@pkg/everest.fin.accounting/types/enums/AccountConfigurationType';
import { ManualCreditDebitMemoTypeMapping } from '@pkg/everest.fin.processor.common/CreditMemoCommon/actions/manualCreditDebitMemoTypeMapping.action';
import type { QueryDebitMemoAccountingData } from '@pkg/everest.fin.processor.common/public/types/QueryDebitMemoAccountingData';
import getAccountsByAssignmentProxy from '@pkg/everest.fin.processor.common/public/utils/getAccountsByAssignmentProxy';
import getEntityBookWithControl from '@pkg/everest.fin.processor.common/public/utils/getEntityBookWithControl';
import type { EvstEntityBook } from '@pkg/everest.fin.processor/types/composites/EntityBook';

export namespace QueryDebitMemoAccountingDataAction {
  export async function execute(
    session: ISession,
    _object: EvstNodeReference,
    entityBooks: Array<EvstEntityBook>,
    previousStageResult: QueryDebitMemoAccountingData.PreviousStageResult,
    _processParameters: QueryDebitMemoAccountingData.ProcessParameters
  ): Promise<QueryDebitMemoAccountingData.Result> {
    const entityBook = getEntityBookWithControl(entityBooks);

    const {
      debitMemoData: { debitMemoHeader, debitMemoLines },
    } = previousStageResult;

    const hasSomeDebitMemoLineWithTaxAmount = debitMemoLines.some(
      (debitMemoLine) => debitMemoLine?.taxAmount?.amount?.greaterThan(0)
    );

    const debitMemoAccountType =
      ManualCreditDebitMemoTypeMapping.toAccountConfigurationType(
        debitMemoHeader.debitMemoType as ManualCreditDebitMemoTypeMapping.ManualCreditMemoTypeOptions
      );
    const accounts = await getAccountsByAssignmentProxy(
      session,
      entityBook.id,
      [
        EvstAccountConfigurationType.CustomerReceivables,
        debitMemoAccountType,
        hasSomeDebitMemoLineWithTaxAmount
          ? EvstAccountConfigurationType.SalesTax
          : null,
      ]
    );

    const debitAccountId = accounts[debitMemoAccountType]?.accountId;
    const creditAccountId = accounts.customerReceivables?.accountId;
    const salesTaxAccountId = accounts.salesTax?.accountId;

    if (!creditAccountId) {
      const translation = await getTranslation(
        session,
        'debitMemo.missingCreditAccountId',
        'everest.fin.accounting/debitMemo'
      );
      throw new Error(translation);
    }

    if (!debitAccountId) {
      const translation = await getTranslation(
        session,
        'debitMemo.missingDebitAccountId',
        'everest.fin.accounting/debitMemo'
      );
      throw new Error(`${translation} ${debitMemoHeader.debitMemoType}`);
    }

    if (hasSomeDebitMemoLineWithTaxAmount && !salesTaxAccountId) {
      const errorMessage = await getTranslation(
        session,
        'creditMemo.create.salesTax.error.message',
        'everest.fin.accounting/creditMemo'
      );
      throw new Error(errorMessage);
    }

    return {
      creditAccountId,
      debitAccountId,
      salesTaxAccountId,
    };
  }
}

export default QueryDebitMemoAccountingDataAction.execute;
