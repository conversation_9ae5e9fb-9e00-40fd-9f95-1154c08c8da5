/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// ******************************************************************************
// Generated Everest Types
// ******************************************************************************

import type { Decimal, RoundingMode } from '@everestsystems/decimal';
import type {
  ArgType,
  Association,
  AssociationManagementArgs,
  AsyncResult,
  Controller,
  ControllerClientProvider,
  CreateOptionsArg,
  DataWithMetadata,
  DeleteOptionsArg,
  FieldSelector,
  Filter,
  flattenFieldSelector,
  GraphInstanceEntry,
  KernelRequestOptions,
  NonAssociationFields,
  Optional,
  PurgeArgs,
  ReadOptionsArg,
  ReadReturnType as ReadReturnTypeGeneric,
  ReadReturnTypeWithAssociations,
  RequireAtLeastOne,
  SemanticSearchArg,
  SemanticSearchRequestOptions,
  TypeSafeQueryArgType,
  UpdateManyOptionsArg,
  UpdateOptionsArg,
  UpsertOptionsArg,
  WriteReturnType,
  WriteReturnTypeWithAssociations,
} from '@everestsystems/content-core';

import { UIExecutionContext } from '@everestsystems/content-core';
import type { BankTransactionMatchTransient as everest_fin_integration_bank_matching_model_node_BankTransactionMatchTransient } from "@pkg/everest.fin.integration.bank.matching/types/BankTransactionMatchTransient";
import type orig_afterUpdateJournalEntry from "@pkg/everest.fin.integration.bank.matching/actions/ruleActions/afterUpdateJournalEntry.action";
import type orig_beforeDeleteOrVoidJournalEntry from "@pkg/everest.fin.integration.bank.matching/actions/ruleActions/beforeDeleteOrVoidJournalEntry.action";
import type orig_automatchInitiatedPayment from "@pkg/everest.fin.integration.bank.matching/actions/automatchInitiatedPayment.action";
import type orig_clearMatches from "@pkg/everest.fin.integration.bank.matching/actions/clearMatches.action";
import type orig_createBankEntrySuggestion from "@pkg/everest.fin.integration.bank.matching/actions/createBankEntrySuggestion.action";
import type orig_createBankTransactionMatchSuggestions from "@pkg/everest.fin.integration.bank.matching/actions/createBankTransactionMatchSuggestions.action";
import type orig_createSFDForMatching from "@pkg/everest.fin.integration.bank.matching/actions/createSFDForMatching.action";
import type orig_getAllUnmatchedDocuments from "@pkg/everest.fin.integration.bank.matching/actions/getAllUnmatchedDocuments.action";
import type orig_getMatchAttachments from "@pkg/everest.fin.integration.bank.matching/actions/getMatchAttachments.action";
import type orig_matchBankTransactions from "@pkg/everest.fin.integration.bank.matching/actions/matchBankTransactions.action";
import type orig_rejectSuggestion from "@pkg/everest.fin.integration.bank.matching/actions/rejectSuggestion.action";
import type orig_unmatchBankTransaction from "@pkg/everest.fin.integration.bank.matching/actions/unmatchBankTransaction.action";
import type orig_updateBankTransactionMatch from "@pkg/everest.fin.integration.bank.matching/actions/updateBankTransactionMatch.action";
import type orig_createSuggestionAfterTransactionCreation from "@pkg/everest.fin.integration.bank.matching/actions/ruleActions/createSuggestionAfterTransactionCreation.action";
import type orig_createMultipleBankTransactionMatchSuggestions from "@pkg/everest.fin.integration.bank.matching/actions/createMultipleBankTransactionMatchSuggestions.action";
import type orig_callAutoMatch from "@pkg/everest.fin.integration.bank.matching/actions/ruleActions/callAutoMatch.action";
import type orig_createCreditCardEntrySuggestion from "@pkg/everest.fin.integration.bank.matching/actions/createCreditCardEntrySuggestion.action";
import type orig_createFundTransferSuggestion from "@pkg/everest.fin.integration.bank.matching/actions/createFundTransferSuggestion.action";
import type orig_matchUnsuggestedDocuments from "@pkg/everest.fin.integration.bank.matching/actions/matchUnsuggestedDocuments.action";
import type orig_getUnmatchedJournalEntryLines from "@pkg/everest.fin.integration.bank.matching/actions/getUnmatchedJournalEntryLines.action";
import type orig_createNetZeroImpactSuggestion from "@pkg/everest.fin.integration.bank.matching/actions/createNetZeroImpactSuggestion.action";
import type orig_createBankEntrySampleData from "@pkg/everest.fin.integration.bank.matching/actions/testUtils/createBankEntrySampleData.action";
import type orig_getDocumentsAndCreateSuggestions from "@pkg/everest.fin.integration.bank.matching/actions/getDocumentsAndCreateSuggestions.action";
import type orig_executeUnreconciliationProcess from "@pkg/everest.fin.integration.bank.matching/actions/ruleActions/executeUnreconciliationProcess.action";
import type orig_createSuggestionsFromUnmatchedDocuments from "@pkg/everest.fin.integration.bank.matching/actions/createSuggestionsFromUnmatchedDocuments.action";
import type orig_getUnmatchedJournalEntryLinesForFindMatches from "@pkg/everest.fin.integration.bank.matching/actions/getUnmatchedJournalEntryLinesForFindMatches.action";
import type orig_removeMatchIdFromTransaction from "@pkg/everest.fin.integration.bank.matching/actions/ruleActions/removeMatchIdFromTransaction.action";
import type orig_acceptBulkMatches from "@pkg/everest.fin.integration.bank.matching/actions/acceptBulkMatches.action";
import type orig_updateCrossPeriodStatusOnMatch from "@pkg/everest.fin.integration.bank.matching/actions/updateCrossPeriodStatusOnMatch.action";
import type orig_updateCrossPeriodStatusOnUnmatch from "@pkg/everest.fin.integration.bank.matching/actions/updateCrossPeriodStatusOnUnmatch.action";
import type orig_beforeUpsertFundTransfer from "@pkg/everest.fin.integration.bank.matching/actions/ruleActions/beforeUpsertFundTransfer.action";

/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */

type RequiredContext = {
  data: Record<string, any>;
  actions: {
    run: (...args: any[]) => any;
  };
};

type ApiError = {
  message: string;
  name: string;
  errorCode: string;
  stack?: string;
};

type InnerPromiseType<T> = T extends Promise<infer R> ? R : T;

type ActionResultOrError<T1 extends string | number | symbol, T2> = {[P in T1]?: T2} & {error?: ApiError};

type ArrayIfNeeded<C extends RequiredContext, K extends keyof C['data'], T> = C['data'][K] extends UIExecutionContext.UIModelNodeListType<any> ? T[] : T;

export namespace BankTransactionMatchTransientUI {
  /**
   * Transient node to attach custom actions 
   */
  export type BankTransactionMatchTransientWithAssociation = BankTransactionMatchTransient & {

    };
  export interface IControllerClient extends everest_fin_integration_bank_matching_model_node_BankTransactionMatchTransient.IControllerClient {}

  export type BankTransactionMatchTransient = everest_fin_integration_bank_matching_model_node_BankTransactionMatchTransient.BankTransactionMatchTransient;
  export type CreationFields = Partial<Pick<BankTransactionMatchTransient, 'status'>> & Pick<BankTransactionMatchTransient, 'externalId' | 'active'>;
  export type UniqueFields = Pick<BankTransactionMatchTransient, 'id'>;
  export type UniqueWhereInput = (RequireAtLeastOne<UniqueFields>) & Partial<BankTransactionMatchTransient>;
  export type ReadReturnType<U extends string | number | symbol = keyof BankTransactionMatchTransient> = ReadReturnTypeGeneric<BankTransactionMatchTransient, U>;

  class ActionRequest<C extends RequiredContext, FuncReturnType> {
    constructor(private readonly context: C, private readonly partialPayload: Record<string, any>) {
    }

    private async __run__(payload: any) {
      return this.context.actions.run(payload);
    }

    payload<K extends keyof C['data']>(nodeAlias: K): Record<string, any> {
      return {[nodeAlias]: this.partialPayload};
    }

    run<K extends keyof C['data']>(nodeAlias: K): Promise<ActionResultOrError<K, ArrayIfNeeded<C, K, InnerPromiseType<FuncReturnType>>>> {
      const payload = this.payload(nodeAlias);
      return this.__run__(payload);
    }
  }

  type func_afterUpdateJournalEntry = typeof orig_afterUpdateJournalEntry;
  /** @deprecated use ```client.afterUpdateJournalEntry``` instead */
  export function afterUpdateJournalEntry<C extends RequiredContext>(context: C, args: { msg: Parameters<func_afterUpdateJournalEntry>[1] }): ActionRequest<C, ReturnType<func_afterUpdateJournalEntry>> {
    const partialPayload = {action: 'afterUpdateJournalEntry', data: args}

    return new ActionRequest<C, ReturnType<func_afterUpdateJournalEntry>>(context, partialPayload);
  }
  type func_beforeDeleteOrVoidJournalEntry = typeof orig_beforeDeleteOrVoidJournalEntry;
  /** @deprecated use ```client.beforeDeleteOrVoidJournalEntry``` instead */
  export function beforeDeleteOrVoidJournalEntry<C extends RequiredContext>(context: C, args: { msg: Parameters<func_beforeDeleteOrVoidJournalEntry>[1] }): ActionRequest<C, ReturnType<func_beforeDeleteOrVoidJournalEntry>> {
    const partialPayload = {action: 'beforeDeleteOrVoidJournalEntry', data: args}

    return new ActionRequest<C, ReturnType<func_beforeDeleteOrVoidJournalEntry>>(context, partialPayload);
  }
  type func_automatchInitiatedPayment = typeof orig_automatchInitiatedPayment;
  /** @deprecated use ```client.automatchInitiatedPayment``` instead */
  export function automatchInitiatedPayment<C extends RequiredContext>(context: C, args: { newBankTransaction: Parameters<func_automatchInitiatedPayment>[1] }): ActionRequest<C, ReturnType<func_automatchInitiatedPayment>> {
    const partialPayload = {action: 'automatchInitiatedPayment', data: args}

    return new ActionRequest<C, ReturnType<func_automatchInitiatedPayment>>(context, partialPayload);
  }
  type func_clearMatches = typeof orig_clearMatches;
  /** @deprecated use ```client.clearMatches``` instead */
  export function clearMatches<C extends RequiredContext>(context: C, args: { sourceJournalEntryLineIds: Parameters<func_clearMatches>[1] }): ActionRequest<C, ReturnType<func_clearMatches>> {
    const partialPayload = {action: 'clearMatches', data: args}

    return new ActionRequest<C, ReturnType<func_clearMatches>>(context, partialPayload);
  }
  type func_createBankEntrySuggestion = typeof orig_createBankEntrySuggestion;
  /** @deprecated use ```client.createBankEntrySuggestion``` instead */
  export function createBankEntrySuggestion<C extends RequiredContext>(context: C, args: { where: Parameters<func_createBankEntrySuggestion>[1]; bankEntryHeaderId: Parameters<func_createBankEntrySuggestion>[2] }): ActionRequest<C, ReturnType<func_createBankEntrySuggestion>> {
    const partialPayload = {action: 'createBankEntrySuggestion', data: args}

    return new ActionRequest<C, ReturnType<func_createBankEntrySuggestion>>(context, partialPayload);
  }
  type func_createBankTransactionMatchSuggestions = typeof orig_createBankTransactionMatchSuggestions;
  /** @deprecated use ```client.createBankTransactionMatchSuggestions``` instead */
  export function createBankTransactionMatchSuggestions<C extends RequiredContext>(context: C, args: { where: Parameters<func_createBankTransactionMatchSuggestions>[1]; autoCreate?: Parameters<func_createBankTransactionMatchSuggestions>[2] }): ActionRequest<C, ReturnType<func_createBankTransactionMatchSuggestions>> {
    const partialPayload = {action: 'createBankTransactionMatchSuggestions', data: args}

    return new ActionRequest<C, ReturnType<func_createBankTransactionMatchSuggestions>>(context, partialPayload);
  }
  type func_createSFDForMatching = typeof orig_createSFDForMatching;
  /** @deprecated use ```client.createSFDForMatching``` instead */
  export function createSFDForMatching<C extends RequiredContext>(context: C, args: { bankTransactions: Parameters<func_createSFDForMatching>[1]; suggestions: Parameters<func_createSFDForMatching>[2]; paymentDetails?: Parameters<func_createSFDForMatching>[3] }): ActionRequest<C, ReturnType<func_createSFDForMatching>> {
    const partialPayload = {action: 'createSFDForMatching', data: args}

    return new ActionRequest<C, ReturnType<func_createSFDForMatching>>(context, partialPayload);
  }
  type func_getAllUnmatchedDocuments = typeof orig_getAllUnmatchedDocuments;
  /** @deprecated use ```client.getAllUnmatchedDocuments``` instead */
  export function getAllUnmatchedDocuments<C extends RequiredContext>(context: C, args: { accountId: Parameters<func_getAllUnmatchedDocuments>[1]; getAllEntityAccounts?: Parameters<func_getAllUnmatchedDocuments>[2] }): ActionRequest<C, ReturnType<func_getAllUnmatchedDocuments>> {
    const partialPayload = {action: 'getAllUnmatchedDocuments', data: args}

    return new ActionRequest<C, ReturnType<func_getAllUnmatchedDocuments>>(context, partialPayload);
  }
  type func_getMatchAttachments = typeof orig_getMatchAttachments;
  /** @deprecated use ```client.getMatchAttachments``` instead */
  export function getMatchAttachments<C extends RequiredContext>(context: C, args: { bankTransactionIds: Parameters<func_getMatchAttachments>[1] }): ActionRequest<C, ReturnType<func_getMatchAttachments>> {
    const partialPayload = {action: 'getMatchAttachments', data: args}

    return new ActionRequest<C, ReturnType<func_getMatchAttachments>>(context, partialPayload);
  }
  type func_matchBankTransactions = typeof orig_matchBankTransactions;
  /** @deprecated use ```client.matchBankTransactions``` instead */
  export function matchBankTransactions<C extends RequiredContext>(context: C, args: { bankTransactions: Parameters<func_matchBankTransactions>[1]; suggestions: Parameters<func_matchBankTransactions>[2]; status?: Parameters<func_matchBankTransactions>[3]; autoMatchReason?: Parameters<func_matchBankTransactions>[4] }): ActionRequest<C, ReturnType<func_matchBankTransactions>> {
    const partialPayload = {action: 'matchBankTransactions', data: args}

    return new ActionRequest<C, ReturnType<func_matchBankTransactions>>(context, partialPayload);
  }
  type func_rejectSuggestion = typeof orig_rejectSuggestion;
  /** @deprecated use ```client.rejectSuggestion``` instead */
  export function rejectSuggestion<C extends RequiredContext>(context: C, args: { suggestion: Parameters<func_rejectSuggestion>[1] }): ActionRequest<C, ReturnType<func_rejectSuggestion>> {
    const partialPayload = {action: 'rejectSuggestion', data: args}

    return new ActionRequest<C, ReturnType<func_rejectSuggestion>>(context, partialPayload);
  }
  type func_unmatchBankTransaction = typeof orig_unmatchBankTransaction;
  /** @deprecated use ```client.unmatchBankTransaction``` instead */
  export function unmatchBankTransaction<C extends RequiredContext>(context: C, args: { bankTransactionId: Parameters<func_unmatchBankTransaction>[1]; isVoided?: Parameters<func_unmatchBankTransaction>[2]; executeHook?: Parameters<func_unmatchBankTransaction>[3] }): ActionRequest<C, ReturnType<func_unmatchBankTransaction>> {
    const partialPayload = {action: 'unmatchBankTransaction', data: args}

    return new ActionRequest<C, ReturnType<func_unmatchBankTransaction>>(context, partialPayload);
  }
  type func_updateBankTransactionMatch = typeof orig_updateBankTransactionMatch;
  /** @deprecated use ```client.updateBankTransactionMatch``` instead */
  export function updateBankTransactionMatch<C extends RequiredContext>(context: C, args: { message: Parameters<func_updateBankTransactionMatch>[1] }): ActionRequest<C, ReturnType<func_updateBankTransactionMatch>> {
    const partialPayload = {action: 'updateBankTransactionMatch', data: args}

    return new ActionRequest<C, ReturnType<func_updateBankTransactionMatch>>(context, partialPayload);
  }
  type func_createSuggestionAfterTransactionCreation = typeof orig_createSuggestionAfterTransactionCreation;
  /** @deprecated use ```client.createSuggestionAfterTransactionCreation``` instead */
  export function createSuggestionAfterTransactionCreation<C extends RequiredContext>(context: C, args: { msg: Parameters<func_createSuggestionAfterTransactionCreation>[1] }): ActionRequest<C, ReturnType<func_createSuggestionAfterTransactionCreation>> {
    const partialPayload = {action: 'createSuggestionAfterTransactionCreation', data: args}

    return new ActionRequest<C, ReturnType<func_createSuggestionAfterTransactionCreation>>(context, partialPayload);
  }
  type func_createMultipleBankTransactionMatchSuggestions = typeof orig_createMultipleBankTransactionMatchSuggestions;
  /** @deprecated use ```client.createMultipleBankTransactionMatchSuggestions``` instead */
  export function createMultipleBankTransactionMatchSuggestions<C extends RequiredContext>(context: C, args: { bankTransactionIds: Parameters<func_createMultipleBankTransactionMatchSuggestions>[1] }): ActionRequest<C, ReturnType<func_createMultipleBankTransactionMatchSuggestions>> {
    const partialPayload = {action: 'createMultipleBankTransactionMatchSuggestions', data: args}

    return new ActionRequest<C, ReturnType<func_createMultipleBankTransactionMatchSuggestions>>(context, partialPayload);
  }
  type func_callAutoMatch = typeof orig_callAutoMatch;
  /** @deprecated use ```client.callAutoMatch``` instead */
  export function callAutoMatch<C extends RequiredContext>(context: C, args: { msg: Parameters<func_callAutoMatch>[1] }): ActionRequest<C, ReturnType<func_callAutoMatch>> {
    const partialPayload = {action: 'callAutoMatch', data: args}

    return new ActionRequest<C, ReturnType<func_callAutoMatch>>(context, partialPayload);
  }
  type func_createCreditCardEntrySuggestion = typeof orig_createCreditCardEntrySuggestion;
  /** @deprecated use ```client.createCreditCardEntrySuggestion``` instead */
  export function createCreditCardEntrySuggestion<C extends RequiredContext>(context: C, args: { where: Parameters<func_createCreditCardEntrySuggestion>[1]; creditCardEntryHeaderId: Parameters<func_createCreditCardEntrySuggestion>[2] }): ActionRequest<C, ReturnType<func_createCreditCardEntrySuggestion>> {
    const partialPayload = {action: 'createCreditCardEntrySuggestion', data: args}

    return new ActionRequest<C, ReturnType<func_createCreditCardEntrySuggestion>>(context, partialPayload);
  }
  type func_createFundTransferSuggestion = typeof orig_createFundTransferSuggestion;
  /** @deprecated use ```client.createFundTransferSuggestion``` instead */
  export function createFundTransferSuggestion<C extends RequiredContext>(context: C, args: { bankTransactionIds: Parameters<func_createFundTransferSuggestion>[1]; fundTransferHeaderId: Parameters<func_createFundTransferSuggestion>[2] }): ActionRequest<C, ReturnType<func_createFundTransferSuggestion>> {
    const partialPayload = {action: 'createFundTransferSuggestion', data: args}

    return new ActionRequest<C, ReturnType<func_createFundTransferSuggestion>>(context, partialPayload);
  }
  type func_matchUnsuggestedDocuments = typeof orig_matchUnsuggestedDocuments;
  /** @deprecated use ```client.matchUnsuggestedDocuments``` instead */
  export function matchUnsuggestedDocuments<C extends RequiredContext>(context: C, args: { bankTransactions: Parameters<func_matchUnsuggestedDocuments>[1]; documents: Parameters<func_matchUnsuggestedDocuments>[2] }): ActionRequest<C, ReturnType<func_matchUnsuggestedDocuments>> {
    const partialPayload = {action: 'matchUnsuggestedDocuments', data: args}

    return new ActionRequest<C, ReturnType<func_matchUnsuggestedDocuments>>(context, partialPayload);
  }
  type func_getUnmatchedJournalEntryLines = typeof orig_getUnmatchedJournalEntryLines;
  /** @deprecated use ```client.getUnmatchedJournalEntryLines``` instead */
  export function getUnmatchedJournalEntryLines<C extends RequiredContext>(context: C, args: { accountId: Parameters<func_getUnmatchedJournalEntryLines>[1]; journalEntryTypes: Parameters<func_getUnmatchedJournalEntryLines>[2]; accountingPeriodId?: Parameters<func_getUnmatchedJournalEntryLines>[3]; skip?: Parameters<func_getUnmatchedJournalEntryLines>[4]; take?: Parameters<func_getUnmatchedJournalEntryLines>[5] }): ActionRequest<C, ReturnType<func_getUnmatchedJournalEntryLines>> {
    const partialPayload = {action: 'getUnmatchedJournalEntryLines', data: args}

    return new ActionRequest<C, ReturnType<func_getUnmatchedJournalEntryLines>>(context, partialPayload);
  }
  type func_createNetZeroImpactSuggestion = typeof orig_createNetZeroImpactSuggestion;
  /** @deprecated use ```client.createNetZeroImpactSuggestion``` instead */
  export function createNetZeroImpactSuggestion<C extends RequiredContext>(context: C, args: { where: Parameters<func_createNetZeroImpactSuggestion>[1]; netZeroImpactId: Parameters<func_createNetZeroImpactSuggestion>[2] }): ActionRequest<C, ReturnType<func_createNetZeroImpactSuggestion>> {
    const partialPayload = {action: 'createNetZeroImpactSuggestion', data: args}

    return new ActionRequest<C, ReturnType<func_createNetZeroImpactSuggestion>>(context, partialPayload);
  }
  type func_createBankEntrySampleData = typeof orig_createBankEntrySampleData;
  /** @deprecated use ```client.createBankEntrySampleData``` instead */
  export function createBankEntrySampleData<C extends RequiredContext>(context: C, args: { amount: Parameters<func_createBankEntrySampleData>[1]; transactionDate: Parameters<func_createBankEntrySampleData>[2]; accountName: Parameters<func_createBankEntrySampleData>[3] }): ActionRequest<C, ReturnType<func_createBankEntrySampleData>> {
    const partialPayload = {action: 'createBankEntrySampleData', data: args}

    return new ActionRequest<C, ReturnType<func_createBankEntrySampleData>>(context, partialPayload);
  }
  type func_getDocumentsAndCreateSuggestions = typeof orig_getDocumentsAndCreateSuggestions;
  /** @deprecated use ```client.getDocumentsAndCreateSuggestions``` instead */
  export function getDocumentsAndCreateSuggestions<C extends RequiredContext>(context: C, args: { bankTransactions: Parameters<func_getDocumentsAndCreateSuggestions>[1]; autoCreate?: Parameters<func_getDocumentsAndCreateSuggestions>[2]; currentUserId?: Parameters<func_getDocumentsAndCreateSuggestions>[3] }): ActionRequest<C, ReturnType<func_getDocumentsAndCreateSuggestions>> {
    const partialPayload = {action: 'getDocumentsAndCreateSuggestions', data: args}

    return new ActionRequest<C, ReturnType<func_getDocumentsAndCreateSuggestions>>(context, partialPayload);
  }
  type func_executeUnreconciliationProcess = typeof orig_executeUnreconciliationProcess;
  /** @deprecated use ```client.executeUnreconciliationProcess``` instead */
  export function executeUnreconciliationProcess<C extends RequiredContext>(context: C, args: { msg: Parameters<func_executeUnreconciliationProcess>[1] }): ActionRequest<C, ReturnType<func_executeUnreconciliationProcess>> {
    const partialPayload = {action: 'executeUnreconciliationProcess', data: args}

    return new ActionRequest<C, ReturnType<func_executeUnreconciliationProcess>>(context, partialPayload);
  }
  type func_createSuggestionsFromUnmatchedDocuments = typeof orig_createSuggestionsFromUnmatchedDocuments;
  /** @deprecated use ```client.createSuggestionsFromUnmatchedDocuments``` instead */
  export function createSuggestionsFromUnmatchedDocuments<C extends RequiredContext>(context: C, args: { bankTransactions: Parameters<func_createSuggestionsFromUnmatchedDocuments>[1]; documents: Parameters<func_createSuggestionsFromUnmatchedDocuments>[2] }): ActionRequest<C, ReturnType<func_createSuggestionsFromUnmatchedDocuments>> {
    const partialPayload = {action: 'createSuggestionsFromUnmatchedDocuments', data: args}

    return new ActionRequest<C, ReturnType<func_createSuggestionsFromUnmatchedDocuments>>(context, partialPayload);
  }
  type func_getUnmatchedJournalEntryLinesForFindMatches = typeof orig_getUnmatchedJournalEntryLinesForFindMatches;
  /** @deprecated use ```client.getUnmatchedJournalEntryLinesForFindMatches``` instead */
  export function getUnmatchedJournalEntryLinesForFindMatches<C extends RequiredContext>(context: C, args: { coaAccountIds: Parameters<func_getUnmatchedJournalEntryLinesForFindMatches>[1]; reconciliationStartDate: Parameters<func_getUnmatchedJournalEntryLinesForFindMatches>[2]; currency: Parameters<func_getUnmatchedJournalEntryLinesForFindMatches>[3]; journalEntryTypes: Parameters<func_getUnmatchedJournalEntryLinesForFindMatches>[4]; accountingPeriodId?: Parameters<func_getUnmatchedJournalEntryLinesForFindMatches>[5]; skip?: Parameters<func_getUnmatchedJournalEntryLinesForFindMatches>[6]; take?: Parameters<func_getUnmatchedJournalEntryLinesForFindMatches>[7] }): ActionRequest<C, ReturnType<func_getUnmatchedJournalEntryLinesForFindMatches>> {
    const partialPayload = {action: 'getUnmatchedJournalEntryLinesForFindMatches', data: args}

    return new ActionRequest<C, ReturnType<func_getUnmatchedJournalEntryLinesForFindMatches>>(context, partialPayload);
  }
  type func_removeMatchIdFromTransaction = typeof orig_removeMatchIdFromTransaction;
  /** @deprecated use ```client.removeMatchIdFromTransaction``` instead */
  export function removeMatchIdFromTransaction<C extends RequiredContext>(context: C, args: { msg: Parameters<func_removeMatchIdFromTransaction>[1] }): ActionRequest<C, ReturnType<func_removeMatchIdFromTransaction>> {
    const partialPayload = {action: 'removeMatchIdFromTransaction', data: args}

    return new ActionRequest<C, ReturnType<func_removeMatchIdFromTransaction>>(context, partialPayload);
  }
  type func_acceptBulkMatches = typeof orig_acceptBulkMatches;
  /** @deprecated use ```client.acceptBulkMatches``` instead */
  export function acceptBulkMatches<C extends RequiredContext>(context: C, args: { transactions: Parameters<func_acceptBulkMatches>[1]; sfds: Parameters<func_acceptBulkMatches>[2] }): ActionRequest<C, ReturnType<func_acceptBulkMatches>> {
    const partialPayload = {action: 'acceptBulkMatches', data: args}

    return new ActionRequest<C, ReturnType<func_acceptBulkMatches>>(context, partialPayload);
  }
  type func_updateCrossPeriodStatusOnMatch = typeof orig_updateCrossPeriodStatusOnMatch;
  /** @deprecated use ```client.updateCrossPeriodStatusOnMatch``` instead */
  export function updateCrossPeriodStatusOnMatch<C extends RequiredContext>(context: C, args: { journalEntryLineIds: Parameters<func_updateCrossPeriodStatusOnMatch>[1]; bankTransactionIds: Parameters<func_updateCrossPeriodStatusOnMatch>[2] }): ActionRequest<C, ReturnType<func_updateCrossPeriodStatusOnMatch>> {
    const partialPayload = {action: 'updateCrossPeriodStatusOnMatch', data: args}

    return new ActionRequest<C, ReturnType<func_updateCrossPeriodStatusOnMatch>>(context, partialPayload);
  }
  type func_updateCrossPeriodStatusOnUnmatch = typeof orig_updateCrossPeriodStatusOnUnmatch;
  /** @deprecated use ```client.updateCrossPeriodStatusOnUnmatch``` instead */
  export function updateCrossPeriodStatusOnUnmatch<C extends RequiredContext>(context: C, args: { journalEntryLineIds: Parameters<func_updateCrossPeriodStatusOnUnmatch>[1]; bankTransactionIds: Parameters<func_updateCrossPeriodStatusOnUnmatch>[2] }): ActionRequest<C, ReturnType<func_updateCrossPeriodStatusOnUnmatch>> {
    const partialPayload = {action: 'updateCrossPeriodStatusOnUnmatch', data: args}

    return new ActionRequest<C, ReturnType<func_updateCrossPeriodStatusOnUnmatch>>(context, partialPayload);
  }
  type func_beforeUpsertFundTransfer = typeof orig_beforeUpsertFundTransfer;
  /** @deprecated use ```client.beforeUpsertFundTransfer``` instead */
  export function beforeUpsertFundTransfer<C extends RequiredContext>(context: C, args: { msg: Parameters<func_beforeUpsertFundTransfer>[1] }): ActionRequest<C, ReturnType<func_beforeUpsertFundTransfer>> {
    const partialPayload = {action: 'beforeUpsertFundTransfer', data: args}

    return new ActionRequest<C, ReturnType<func_beforeUpsertFundTransfer>>(context, partialPayload);
  }
  /** @deprecated - use {@link MODEL_URN} instead */
  export const name = 'urn:evst:everest:fin/integration/bank/matching:model/node:BankTransactionMatchTransient';
  export const MODEL_URN = 'urn:evst:everest:fin/integration/bank/matching:model/node:BankTransactionMatchTransient';
  /** @deprecated - use {@link MODEL_URN} instead */
  export const MODEL_ID = 'everest.fin.integration.bank.matching/BankTransactionMatchTransientModel.BankTransactionMatchTransient';
  export const MODEL_UUID = '6d20eb9c-5504-408f-ac2d-0302e599b4ae';

  /** @deprecated use ```client``` instead */
  export function create<C extends RequiredContext, U extends keyof BankTransactionMatchTransient>(context: C, input: CreationFields, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<BankTransactionMatchTransient, U>>> {
    const convertedArgs = {data: input, fieldList: fieldlist, options: options} as Record<string, any>
    const partialPayload = {action: 'create', ...convertedArgs}

    return new ActionRequest<C, Promise<WriteReturnType<BankTransactionMatchTransient, U>>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function createMany<C extends RequiredContext, U extends keyof BankTransactionMatchTransient>(context: C, input: CreationFields[], fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<BankTransactionMatchTransient, U>[]>> {
    const convertedArgs = {data: input, fieldList: fieldlist, options: options} as Record<string, any>
    const partialPayload = {action: 'createMany', ...convertedArgs}

    return new ActionRequest<C, Promise<WriteReturnType<BankTransactionMatchTransient, U>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function Delete<C extends RequiredContext>(context: C, where: UniqueWhereInput, options?: undefined): ActionRequest<C, Promise<Partial<BankTransactionMatchTransient>[]>> {
    const convertedArgs = {where: where, options} as Record<string, any>
    const partialPayload = {action: 'delete', ...convertedArgs}

    return new ActionRequest<C, Promise<Partial<BankTransactionMatchTransient>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function deleteAll<C extends RequiredContext>(context: C): ActionRequest<C, Promise<Partial<BankTransactionMatchTransient>[]>> {
    const convertedArgs = {data: {}} as Record<string, any>
    const partialPayload = {action: 'deleteAll', ...convertedArgs}

    return new ActionRequest<C, Promise<Partial<BankTransactionMatchTransient>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function deleteMany<C extends RequiredContext>(context: C, where: Filter<BankTransactionMatchTransientWithAssociation>): ActionRequest<C, Promise<Partial<BankTransactionMatchTransient>[]>>;
  /** @deprecated use ```client``` instead */
  export function deleteMany<C extends RequiredContext>(context: C, where: Partial<BankTransactionMatchTransient>): ActionRequest<C, Promise<Partial<BankTransactionMatchTransient>[]>>;
  /** @deprecated use ```client``` instead */
  export function deleteMany<C extends RequiredContext>(context: C, where: Filter<BankTransactionMatchTransient> | Partial<BankTransactionMatchTransient>, options?: undefined): ActionRequest<C, Promise<Partial<BankTransactionMatchTransient>[]>> {
    const convertedArgs = {data: {where: where}, options} as Record<string, any>
    const partialPayload = {action: 'deleteMany', ...convertedArgs}

    return new ActionRequest<C, Promise<Partial<BankTransactionMatchTransient>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function query<C extends RequiredContext, U extends FieldSelector<BankTransactionMatchTransientWithAssociation>>(context: C, args: Omit<TypeSafeQueryArgType<BankTransactionMatchTransientWithAssociation>, 'draft'>, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<ReadReturnTypeWithAssociations<BankTransactionMatchTransientWithAssociation, U>[]>>;
  /** @deprecated use ```client``` instead */
  export function query<C extends RequiredContext, U extends keyof BankTransactionMatchTransient, V extends string = 'ALL_FIELDS'>(context: C, args: Omit<TypeSafeQueryArgType<BankTransactionMatchTransient>, 'draft'> | Omit<Omit<TypeSafeQueryArgType<BankTransactionMatchTransient>, 'draft'>, 'where'> & { where?: Partial<BankTransactionMatchTransient> }, fieldlist: ReadonlyArray<U | V> | 'ALL_FIELDS'): ActionRequest<C, Promise<ReadReturnType<V>[]>>;
  /** @deprecated use ```client``` instead */
  export function query<C extends RequiredContext>(context: C, args: Omit<TypeSafeQueryArgType<BankTransactionMatchTransient>, 'draft'>, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS'): ActionRequest<C, Promise<unknown[]>> {
    const convertedArgs = {data: {...args, fieldList: fieldlist}} as Record<string, any>
    const partialPayload = {action: 'query', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function read<C extends RequiredContext, U extends FieldSelector<BankTransactionMatchTransientWithAssociation>>(context: C, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): ActionRequest<C, Promise<ReadReturnTypeWithAssociations<BankTransactionMatchTransientWithAssociation, U>>>;
  /** @deprecated use ```client``` instead */
  export function read<C extends RequiredContext, U extends keyof BankTransactionMatchTransient>(context: C, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): ActionRequest<C, Promise<ReadReturnType<U>>>;
  /** @deprecated use ```client``` instead */
  export function read<C extends RequiredContext>(context: C, where: UniqueWhereInput, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): ActionRequest<C, Promise<unknown>> {
    const convertedArgs = {data: {where: where, fieldList: fieldlist}, options: options} as Record<string, any>
    const partialPayload = {action: 'read', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function update<C extends RequiredContext, U extends FieldSelector<BankTransactionMatchTransientWithAssociation>>(context: C, where: UniqueWhereInput, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<WriteReturnTypeWithAssociations<BankTransactionMatchTransientWithAssociation, U>>>;
  /** @deprecated use ```client``` instead */
  export function update<C extends RequiredContext, U extends keyof BankTransactionMatchTransient>(context: C, where: Partial<BankTransactionMatchTransient>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<WriteReturnType<BankTransactionMatchTransient, U>>>;
  /** @deprecated use ```client``` instead */
  export function update<C extends RequiredContext>(context: C, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: undefined): ActionRequest<C, Promise<unknown>> {
    const convertedArgs = {where: where, data: data, fieldList: fieldlist, options} as Record<string, any>
    const partialPayload = {action: 'update', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function updateMany<C extends RequiredContext, U extends FieldSelector<BankTransactionMatchTransientWithAssociation>>(context: C, where: Filter<BankTransactionMatchTransientWithAssociation>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<WriteReturnTypeWithAssociations<BankTransactionMatchTransientWithAssociation, U>[]>>;
  /** @deprecated use ```client``` instead */
  export function updateMany<C extends RequiredContext, U extends keyof BankTransactionMatchTransient>(context: C, where: Partial<BankTransactionMatchTransient>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<WriteReturnType<BankTransactionMatchTransient, U>[]>>;
  /** @deprecated use ```client``` instead */
  export function updateMany<C extends RequiredContext>(context: C, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: undefined): ActionRequest<C, Promise<unknown[]>> {
    const convertedArgs = {data: {where: where, data: data, fieldList: fieldlist}, options} as Record<string, any>
    const partialPayload = {action: 'updateMany', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function upsert<C extends RequiredContext, U extends keyof BankTransactionMatchTransient & string>(context: C, data: Partial<BankTransactionMatchTransient>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<BankTransactionMatchTransient, U>>>;
  /** @deprecated use ```client``` instead */
  export function upsert<C extends RequiredContext, U extends keyof BankTransactionMatchTransient & string>(context: C, where: Partial<BankTransactionMatchTransient>, data: Partial<BankTransactionMatchTransient>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<BankTransactionMatchTransient, U>>>;
  /** @deprecated use ```client``` instead */
  export function upsert<C extends RequiredContext, U extends keyof BankTransactionMatchTransient & string>(context: C, whereOrData: Partial<BankTransactionMatchTransient>, dataOrFieldList?: Partial<BankTransactionMatchTransient> | ReadonlyArray<U> | 'ALL_FIELDS', fieldlistOrOptions?: ReadonlyArray<U> | 'ALL_FIELDS' | Omit<UpsertOptionsArg, 'draft'>, maybeOptions?: Omit<UpsertOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<BankTransactionMatchTransient, U>>> {
    const convertedArgs = {data: {where: whereOrData, data: dataOrFieldList, fieldList: fieldlistOrOptions}, options: maybeOptions} as Record<string, any>
    const partialPayload = {action: 'upsert', ...convertedArgs}

    return new ActionRequest<C, Promise<WriteReturnType<BankTransactionMatchTransient, U>>>(context, partialPayload);
  }
  export namespace client {
    /** Update bank transaction matching or remove from rejected list, when the journal entry is updated */
    export declare function afterUpdateJournalEntry(args: { msg: Parameters<func_afterUpdateJournalEntry>[1] }): ReturnType<func_afterUpdateJournalEntry>
    /** Unmatch or delete a bank transaction matching, when the journal entry is deleted or voided */
    export declare function beforeDeleteOrVoidJournalEntry(args: { msg: Parameters<func_beforeDeleteOrVoidJournalEntry>[1] }): ReturnType<func_beforeDeleteOrVoidJournalEntry>
    /** Auto match a bank transaction using E2E ID from the integrator */
    export declare function automatchInitiatedPayment(args: { newBankTransaction: Parameters<func_automatchInitiatedPayment>[1] }): ReturnType<func_automatchInitiatedPayment>
    /** Clear rejected suggestions and unmatched documents */
    export declare function clearMatches(args: { sourceJournalEntryLineIds: Parameters<func_clearMatches>[1] }): ReturnType<func_clearMatches>
    /** Create match suggestion with provided bank entry for the provided bank transaction */
    export declare function createBankEntrySuggestion(args: { where: Parameters<func_createBankEntrySuggestion>[1]; bankEntryHeaderId: Parameters<func_createBankEntrySuggestion>[2] }): ReturnType<func_createBankEntrySuggestion>
    /** Create match suggestions for the provided bank transactions */
    export declare function createBankTransactionMatchSuggestions(args: { where: Parameters<func_createBankTransactionMatchSuggestions>[1]; autoCreate?: Parameters<func_createBankTransactionMatchSuggestions>[2] }): ReturnType<func_createBankTransactionMatchSuggestions>
    /** Create source financial document for matching bank tranactions */
    export declare function createSFDForMatching(args: { bankTransactions: Parameters<func_createSFDForMatching>[1]; suggestions: Parameters<func_createSFDForMatching>[2]; paymentDetails?: Parameters<func_createSFDForMatching>[3] }): ReturnType<func_createSFDForMatching>
    /** find all unmatched source financial documents */
    export declare function getAllUnmatchedDocuments(args: { accountId: Parameters<func_getAllUnmatchedDocuments>[1]; getAllEntityAccounts?: Parameters<func_getAllUnmatchedDocuments>[2] }): ReturnType<func_getAllUnmatchedDocuments>
    /** Get attachments linked to a match */
    export declare function getMatchAttachments(args: { bankTransactionIds: Parameters<func_getMatchAttachments>[1] }): ReturnType<func_getMatchAttachments>
    /** Match bank tranactions with the provided documents */
    export declare function matchBankTransactions(args: { bankTransactions: Parameters<func_matchBankTransactions>[1]; suggestions: Parameters<func_matchBankTransactions>[2]; status?: Parameters<func_matchBankTransactions>[3]; autoMatchReason?: Parameters<func_matchBankTransactions>[4] }): ReturnType<func_matchBankTransactions>
    /** Reject suggestion */
    export declare function rejectSuggestion(args: { suggestion: Parameters<func_rejectSuggestion>[1] }): ReturnType<func_rejectSuggestion>
    /** Unmatch a bank transaction match */
    export declare function unmatchBankTransaction(args: { bankTransactionId: Parameters<func_unmatchBankTransaction>[1]; isVoided?: Parameters<func_unmatchBankTransaction>[2]; executeHook?: Parameters<func_unmatchBankTransaction>[3] }): ReturnType<func_unmatchBankTransaction>
    /** Update bank tranaction match after Journal entry BO is created */
    export declare function updateBankTransactionMatch(args: { message: Parameters<func_updateBankTransactionMatch>[1] }): ReturnType<func_updateBankTransactionMatch>
    /** create suggestions after bank transaction creation */
    export declare function createSuggestionAfterTransactionCreation(args: { msg: Parameters<func_createSuggestionAfterTransactionCreation>[1] }): ReturnType<func_createSuggestionAfterTransactionCreation>
    /** Find and update 1:M or M:1 bank transaction suggestions */
    export declare function createMultipleBankTransactionMatchSuggestions(args: { bankTransactionIds: Parameters<func_createMultipleBankTransactionMatchSuggestions>[1] }): ReturnType<func_createMultipleBankTransactionMatchSuggestions>
    /** Call auto match action to match intitiated payments */
    export declare function callAutoMatch(args: { msg: Parameters<func_callAutoMatch>[1] }): ReturnType<func_callAutoMatch>
    /** Create match suggestion with provided credit card entry for the provided bank transaction */
    export declare function createCreditCardEntrySuggestion(args: { where: Parameters<func_createCreditCardEntrySuggestion>[1]; creditCardEntryHeaderId: Parameters<func_createCreditCardEntrySuggestion>[2] }): ReturnType<func_createCreditCardEntrySuggestion>
    /** Create match suggestion with provided fund transfer for the provided bank transaction */
    export declare function createFundTransferSuggestion(args: { bankTransactionIds: Parameters<func_createFundTransferSuggestion>[1]; fundTransferHeaderId: Parameters<func_createFundTransferSuggestion>[2] }): ReturnType<func_createFundTransferSuggestion>
    /** Match selections from find matches */
    export declare function matchUnsuggestedDocuments(args: { bankTransactions: Parameters<func_matchUnsuggestedDocuments>[1]; documents: Parameters<func_matchUnsuggestedDocuments>[2] }): ReturnType<func_matchUnsuggestedDocuments>
    /** Get unmatched journal entry lines for a specific account and accounting period */
    export declare function getUnmatchedJournalEntryLines(args: { accountId: Parameters<func_getUnmatchedJournalEntryLines>[1]; journalEntryTypes: Parameters<func_getUnmatchedJournalEntryLines>[2]; accountingPeriodId?: Parameters<func_getUnmatchedJournalEntryLines>[3]; skip?: Parameters<func_getUnmatchedJournalEntryLines>[4]; take?: Parameters<func_getUnmatchedJournalEntryLines>[5] }): ReturnType<func_getUnmatchedJournalEntryLines>
    /** some description */
    export declare function createNetZeroImpactSuggestion(args: { where: Parameters<func_createNetZeroImpactSuggestion>[1]; netZeroImpactId: Parameters<func_createNetZeroImpactSuggestion>[2] }): ReturnType<func_createNetZeroImpactSuggestion>
    /** some description */
    export declare function createBankEntrySampleData(args: { amount: Parameters<func_createBankEntrySampleData>[1]; transactionDate: Parameters<func_createBankEntrySampleData>[2]; accountName: Parameters<func_createBankEntrySampleData>[3] }): ReturnType<func_createBankEntrySampleData>
    /** Get unmatched documents and suggestions. It can be called directly or as a task */
    export declare function getDocumentsAndCreateSuggestions(args: { bankTransactions: Parameters<func_getDocumentsAndCreateSuggestions>[1]; autoCreate?: Parameters<func_getDocumentsAndCreateSuggestions>[2]; currentUserId?: Parameters<func_getDocumentsAndCreateSuggestions>[3] }): ReturnType<func_getDocumentsAndCreateSuggestions>
    /** Execute unreconcile process after unmatch bank transaction */
    export declare function executeUnreconciliationProcess(args: { msg: Parameters<func_executeUnreconciliationProcess>[1] }): ReturnType<func_executeUnreconciliationProcess>
    /** Create suggestions to a bank transaction from the selected unmatched documents */
    export declare function createSuggestionsFromUnmatchedDocuments(args: { bankTransactions: Parameters<func_createSuggestionsFromUnmatchedDocuments>[1]; documents: Parameters<func_createSuggestionsFromUnmatchedDocuments>[2] }): ReturnType<func_createSuggestionsFromUnmatchedDocuments>
    /** some description */
    export declare function getUnmatchedJournalEntryLinesForFindMatches(args: { coaAccountIds: Parameters<func_getUnmatchedJournalEntryLinesForFindMatches>[1]; reconciliationStartDate: Parameters<func_getUnmatchedJournalEntryLinesForFindMatches>[2]; currency: Parameters<func_getUnmatchedJournalEntryLinesForFindMatches>[3]; journalEntryTypes: Parameters<func_getUnmatchedJournalEntryLinesForFindMatches>[4]; accountingPeriodId?: Parameters<func_getUnmatchedJournalEntryLinesForFindMatches>[5]; skip?: Parameters<func_getUnmatchedJournalEntryLinesForFindMatches>[6]; take?: Parameters<func_getUnmatchedJournalEntryLinesForFindMatches>[7] }): ReturnType<func_getUnmatchedJournalEntryLinesForFindMatches>
    /** Removes match IDs from transactions when bank transaction matches are deleted */
    export declare function removeMatchIdFromTransaction(args: { msg: Parameters<func_removeMatchIdFromTransaction>[1] }): ReturnType<func_removeMatchIdFromTransaction>
    /** Accept bulk matches */
    export declare function acceptBulkMatches(args: { transactions: Parameters<func_acceptBulkMatches>[1]; sfds: Parameters<func_acceptBulkMatches>[2] }): ReturnType<func_acceptBulkMatches>
    /** Update by period match status after matching bank transactions with journal entries */
    export declare function updateCrossPeriodStatusOnMatch(args: { journalEntryLineIds: Parameters<func_updateCrossPeriodStatusOnMatch>[1]; bankTransactionIds: Parameters<func_updateCrossPeriodStatusOnMatch>[2] }): ReturnType<func_updateCrossPeriodStatusOnMatch>
    /** Update by period match status after unmatching bank transactions with journal entries */
    export declare function updateCrossPeriodStatusOnUnmatch(args: { journalEntryLineIds: Parameters<func_updateCrossPeriodStatusOnUnmatch>[1]; bankTransactionIds: Parameters<func_updateCrossPeriodStatusOnUnmatch>[2] }): ReturnType<func_updateCrossPeriodStatusOnUnmatch>
    /** Prevent editing of fund transfers that are already fully matched */
    export declare function beforeUpsertFundTransfer(args: { msg: Parameters<func_beforeUpsertFundTransfer>[1] }): ReturnType<func_beforeUpsertFundTransfer>
    /** write a new object to the database. */
    export declare function create<U extends keyof BankTransactionMatchTransient>(input: CreationFields, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): Promise<WriteReturnType<BankTransactionMatchTransient, U>>

    /** write new objects to the database. */
    export declare function createMany<U extends keyof BankTransactionMatchTransient>(input: CreationFields[], fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): Promise<WriteReturnType<BankTransactionMatchTransient, U>[]>

    /** delete the object that is uniquely identified by the where condition. */
    export declare function Delete(where: UniqueWhereInput, options?: undefined): Promise<Partial<BankTransactionMatchTransient>[]>

    /** delete all objects. */
    export declare function deleteAll(): Promise<Partial<BankTransactionMatchTransient>[]>

    /** delete the objects that are identified by the where condition. */
    export declare function deleteMany(where: Filter<BankTransactionMatchTransientWithAssociation>): Promise<Partial<BankTransactionMatchTransient>[]>;
    /** @deprecated use the alternative overloaded function */
    export declare function deleteMany(where: Partial<BankTransactionMatchTransient>): Promise<Partial<BankTransactionMatchTransient>[]>;
    export declare function deleteMany(where: Filter<BankTransactionMatchTransient> | Partial<BankTransactionMatchTransient>, options?: undefined): Promise<Partial<BankTransactionMatchTransient>[]>

    /** returns a list of all objects matching the requirements. */
    export declare function query<U extends FieldSelector<BankTransactionMatchTransientWithAssociation>>(args: Omit<TypeSafeQueryArgType<BankTransactionMatchTransientWithAssociation>, 'draft'>, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<ReadReturnTypeWithAssociations<BankTransactionMatchTransientWithAssociation, U>[]>;
    /** @deprecated Use the alternative overloaded method for querying associations. */
    export declare function query<U extends keyof BankTransactionMatchTransient, V extends string = 'ALL_FIELDS'>(args: Omit<TypeSafeQueryArgType<BankTransactionMatchTransient>, 'draft'> | Omit<Omit<TypeSafeQueryArgType<BankTransactionMatchTransient>, 'draft'>, 'where'> & { where?: Partial<BankTransactionMatchTransient> }, fieldlist: ReadonlyArray<U | V> | 'ALL_FIELDS'): Promise<ReadReturnType<V>[]>;
    export declare function query(args: Omit<TypeSafeQueryArgType<BankTransactionMatchTransient>, 'draft'>, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown[]>

    /** reads the object that is uniquely identified by the where condition. */
    export declare function read<U extends FieldSelector<BankTransactionMatchTransientWithAssociation>>(where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnTypeWithAssociations<BankTransactionMatchTransientWithAssociation, U>>;
    /** @deprecated Use the alternative overloaded method for reading associations. */
    export declare function read<U extends keyof BankTransactionMatchTransient>(where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnType<U>>;
    export declare function read(where: UniqueWhereInput, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<unknown>

    /** writes modified values back to the database for a given node instance. */
    export declare function update<U extends FieldSelector<BankTransactionMatchTransientWithAssociation>>(where: UniqueWhereInput, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnTypeWithAssociations<BankTransactionMatchTransientWithAssociation, U>>;
    /** @deprecated Use the alternative overloaded method for writing with associations. */
    export declare function update<U extends keyof BankTransactionMatchTransient>(where: Partial<BankTransactionMatchTransient>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnType<BankTransactionMatchTransient, U>>;
    export declare function update(where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: undefined): Promise<unknown>

    /** writes modified values back to the database for selected node instances. */
    export declare function updateMany<U extends FieldSelector<BankTransactionMatchTransientWithAssociation>>(where: Filter<BankTransactionMatchTransientWithAssociation>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnTypeWithAssociations<BankTransactionMatchTransientWithAssociation, U>[]>;
    /** @deprecated Use the alternative overloaded method for writing with associations. */
    export declare function updateMany<U extends keyof BankTransactionMatchTransient>(where: Partial<BankTransactionMatchTransient>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnType<BankTransactionMatchTransient, U>[]>;
    export declare function updateMany(where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: undefined): Promise<unknown[]>

    /** creates or updates object identified by where. */
    export declare function upsert<U extends keyof BankTransactionMatchTransient & string>(data: Partial<BankTransactionMatchTransient>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<BankTransactionMatchTransient, U>>;
    /** @deprecated use the overload without an explicit where clause */
    export declare function upsert<U extends keyof BankTransactionMatchTransient & string>(where: Partial<BankTransactionMatchTransient>, data: Partial<BankTransactionMatchTransient>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<BankTransactionMatchTransient, U>>;
    export declare function upsert<U extends keyof BankTransactionMatchTransient & string>(whereOrData: Partial<BankTransactionMatchTransient>, dataOrFieldList?: Partial<BankTransactionMatchTransient> | ReadonlyArray<U> | 'ALL_FIELDS', fieldlistOrOptions?: ReadonlyArray<U> | 'ALL_FIELDS' | Omit<UpsertOptionsArg, 'draft'>, maybeOptions?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<BankTransactionMatchTransient, U>>
  }
}
