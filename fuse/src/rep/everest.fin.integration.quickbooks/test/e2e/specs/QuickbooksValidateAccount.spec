# QuickBooks Migration E2E Test with valiating accounts
> [UAT] - Validate account and department mapping overrides QBO account on synced objects
@UA-252-8
 
## Validate account and department mapping overrides QBO account on synced objects
* Go to the Integration Settings page
* On the Integration Setting page, set Enable Task Queue to "true"
* On the Integration Setting page, fill Max. item count to run locally to "10"
* On the Integration Setting page, set Allow Changes to Integrated Items to "true"
* On the page header, click on the "Save" button and wait for the right toast message to appear
* Go to the Quickbooks page
* On the Quickbooks page, switch to the "Configuration" segment
* On the QuickBooks Configuration page, click on the "Manage" button under the "Master Data" section and wait for modal displayed
* On the Master Data configuration modal, fill data to "Entity" section with following data

   |Entity Id|
   |---------|
   |Flow SNC.|

* On the modal, click on the "Save" button and wait for success message
* On the Quickbooks page, switch to the "Semantic Mappings" segment
* On the QuickBooks Semantic Mappings page, perform "Sync" action on the master data for "Accounts" type
* On the QuickBooks Semantic Mappings page, wait for "Accounts" sync completely
* Wait until the center message disappears
* On the QuickBooks Semantic Mappings page, perform "Edit Matchings (Presentation Modal)" action on the master data for "Accounts" type
* Wait for the modal is displayed and loaded completely
* On the Mapping modal, fill mapping following data

   |Quickbooks Accounts            |Everest Account |Everest Department|
   |-------------------------------|----------------|------------------|
   |end-with:Accounts Payable (A/P)|Accounts Payable|                  |
   |end-with:Contract labor        |Contractors     |Accounting        |

* On the modal, click on the "Save" button and wait for success message
* Close modal if opened
* Click on the Refresh Data Icon
* On the QuickBooks Semantic Mappings page, check that on the master data displays information correctly

   |Data Type                |Objects in Quickbooks|Skipped|Mapped to Everest|Integrated|Status|
   |-------------------------|---------------------|-------|-----------------|----------|------|
   |Departments from Accounts|1                    |0      |1                |0         |Mapped|

* On the Quickbooks page, switch to the "Technical Mappings" segment
* On the QuickBooks Technical Mappings page, perform "Sync" action on the master data for "Vendors" type
* On the QuickBooks Technical Mappings page, wait for "Master Data" sync completely
* Wait until the center message disappears
* Go to the Vendors page
* Open the Vendors page, click on row that has data below

   |Name|
   |----|
   |Maze|

* On the modal, click on the "Save" button and wait until the page is updated successfully
* On the Vendors page, check that the Vendors table displays rows

   |Number         |Name|Vendor Type|Entity      |Status|Migration Source|
   |---------------|----|-----------|------------|------|----------------|
   |start-with:VEN-|Maze|Supplier   | Flow SNC.  |Active|Quickbooks      |

* Go to the Quickbooks page
* Click on the Refresh Data Icon
* On the Quickbooks page, switch to the "Technical Mappings" segment
* On the QuickBooks Technical Mappings page, perform "Sync" action on the transaction data for "Journal Entries" type
* On the QuickBooks Technical Mappings page, wait for "Transaction Data" sync completely
* Wait until the center message disappears
* Go to the Journal Entries page
* Click on the row in the Journal Entries table with the data

   |Number        |Journal Entry Name|Type  |Journal Entry Status|Approval Status|Entities |Migration Mode|
   |--------------|------------------|------|--------------------|---------------|---------|--------------|
   |start-with:JE-|start-with:56 Bill|Manual|Posted              |Approved       |Flow SNC.|Migration     |

* Check the Journal Entry Details table displays information correctly

   |Account                     |Debit    |Credit   |Description       |Business Partner|Department      |
   |----------------------------|---------|---------|------------------|----------------|----------------|
   |end-with: — Accounts Payable|         |5,000.00 | ––               |Maze            |––              |
   |end-with: — Contractors     |5,000.00 |         |Test Vendor Credit|Maze            |DPT-8 Accounting|

* Go to the Quickbooks page
* On the Quickbooks page, switch to the "Technical Mappings" segment
* On the QuickBooks Technical Mappings page, perform "Sync" action on the transaction data for "Vendor Bills" type
* On the QuickBooks Technical Mappings page, wait for "Transaction Data" sync completely
* Wait until the center message disappears
* Go to the Vendor Bills page
* Check that Vendor Bills table displays a row with data below

   |Number       |Vendor|Reference Number|Currency |Total Amount|Entity   |Bill Date |Approval Status|Payment Status|Posting Status|Migration Mode|Origin    |
   |-------------|------|----------------|---------|------------|---------|----------|---------------|--------------|--------------|--------------|----------|
   |start-with:VB|Maze  |QBO - 56        |US Dollar|5,000.00    |Flow SNC.|12/06/2024|Approved       |Unpaid        |Unposted      |Migration     |Quickbooks|

* On the Vendor Bills page, click on row below

   |Vendor|Reference Number|
   |------|----------------|
   |Maze  |QBO - 56        |

* Check that Vendor Bill Line table displays correctly

   |Description       |Department      |Account             |Amount   |
   |------------------|----------------|--------------------|---------|
   |Test Vendor Credit|DPT-8 Accounting|end-with:Contractors|5,000.00 |
