import { baseModalSteps } from '@pkg/everest.base.test/test/e2e/steps/BaseModalSteps.evertest';
import { baseSteps } from '@pkg/everest.base.test/test/e2e/steps/BaseSteps.evertest';
import {
  EMPTY_COLUMN_HEADER,
  LONG_TIME,
  TIMEOUT_USAGE_STATUS,
} from '@pkg/everest.base.test/test/e2e/utils/constants/constant.evertest';
import assert from 'assert';
import { ContinueOnFailure, Step, Table } from 'gauge-ts';
import { QuickbooksPage } from './page.evertest';
import { logger } from '@pkg/everest.base.test/test/e2e/utils/logger.evertest';
import { ELEMENT_STATE } from '@pkg/everest.base.test/test/e2e/enums/enums.evertest';

let quickbooksPage: QuickbooksPage;

export default class QuickbooksSteps {
  @Step(
    'On the Quickbooks page, click on Edit Connection Details button and wait for modal displayed'
  )
  public async clickEditConnectionDetailsButton() {
    quickbooksPage = new QuickbooksPage();
    await quickbooksPage.connectionDetailsFieldGroup.clickOnButton(
      'Edit Connection Details'
    );
    await baseModalSteps.waitForModalOpenedAndLoaded();
  }

  @Step('On the Quickbooks page, switch to the <segment> segment')
  public async openSegmentSpecific(segment: string) {
    quickbooksPage = new QuickbooksPage();
    await quickbooksPage.segment.selectSubsegment(segment);
    await baseSteps.waitForLoadState();
  }

  @Step(
    'On the QuickBooks Configuration page, click on the <button> button under the <section> section and wait for modal displayed'
  )
  public async clickButtonOnConfigurationSegment(
    button: string,
    section: string
  ) {
    quickbooksPage = new QuickbooksPage();
    await quickbooksPage.configurationActionGroup
      .buttonGroupTemplateUnderItem(section)
      .clickButton(button);
    await baseModalSteps.waitForModalOpenedAndLoaded();
  }

  ///////////////////////////////////////
  ////////// Master Data Table //////////
  ///////////////////////////////////////
  @Step(
    'On the QuickBooks Technical Mappings page, perform <Action> action on the master data for <type> type'
  )
  public async performActionOnMasterDataTable(action: string, type: string) {
    quickbooksPage = new QuickbooksPage();

    // Wait for sync to complete with a timeout, similar to waitForDataSyncCompletely
    const maxWaitTimeMs = LONG_TIME; // 1 minutes timeout
    const startTime = Date.now();
    do {
      // Interval time for waiting
      await quickbooksPage.getPage().waitForTimeout(TIMEOUT_USAGE_STATUS / 2);

      // Check if table is ready
      await quickbooksPage.technicalMappingsMasterDataTable.waitForTableLoading();

      let allHeader;
      try {
        allHeader =
          await quickbooksPage.technicalMappingsMasterDataTable.getAllHeaders();
      } catch (e) {
        logger.warn(`Error when getting all headers, retrying`);
        continue;
      }

      const count = allHeader.filter(
        (item) => item === EMPTY_COLUMN_HEADER
      ).length;
      console.log(count);
      if (count === 1) {
        await quickbooksPage.technicalMappingsMasterDataTable.clickActionCellAndSelectItemByRelevantData(
          [type],
          action
        );
        break;
      }
    } while (Date.now() - startTime < maxWaitTimeMs);

    // If we exit the loop due to timeout
    if (Date.now() - startTime >= maxWaitTimeMs) {
      throw new Error(
        `Timed out waiting for master data table to load completely before performing action "${action}" on type "${type}"`
      );
    }
  }

  @ContinueOnFailure()
  @Step(
    'On the QuickBooks Technical Mappings page, check that on the master data displays information correctly <table>'
  )
  public async checkMasterDataDetailsDisplayCorrectly(infoTable: Table) {
    quickbooksPage = new QuickbooksPage();
    await quickbooksPage.technicalMappingsMasterDataTable.waitForTableLoading();
    await quickbooksPage.technicalMappingsMasterDataTable.compareContainsSnapshotByInputHeader(
      infoTable
    );
  }

  @ContinueOnFailure()
  @Step(
    'On the QuickBooks Technical Mappings page, check that the Master Data includes at least <expectValue> <type> marked as <status>'
  )
  public async checkValueInMasterData(
    expectValue: string,
    type: string,
    status: string
  ) {
    const quickbooksPage = new QuickbooksPage();
    // Retrieve the actual value of Master Data records
    const actualValue =
      await quickbooksPage.technicalMappingsMasterDataTable.getCellValueByHeaderNameAndRelevantData(
        status,
        [type]
      );
    // Validate and compare the actual value with the expected value
    assert(
      Number(actualValue) >= Number(expectValue),
      `The Master Data includes only "${actualValue}" "${type}", which is less than the expected minimum of "${expectValue}".`
    );
  }

  @Step(
    'On the QuickBooks Technical Mappings page, open the <type> page under Master Data'
  )
  public async openPageUnderMasterData(type: string) {
    quickbooksPage = new QuickbooksPage();
    await quickbooksPage.technicalMappingsMasterDataTable.waitForTableLoading();
    await quickbooksPage.technicalMappingsMasterDataTable.clickCellByHeaderAndRelevantData(
      'Type',
      [type]
    );
    await baseSteps.waitForTabRebuildingCompletely();
  }

  ////////////////////////////////////////////
  ////////// Transaction Data Table //////////
  ////////////////////////////////////////////
  @Step(
    'On the QuickBooks Technical Mappings page, perform <Action> action on the transaction data for <type> type'
  )
  public async performActionOnTransactionDataTable(
    action: string,
    type: string
  ) {
    quickbooksPage = new QuickbooksPage();

    // Wait for sync to complete with a timeout
    const maxWaitTimeMs = LONG_TIME * 3; // 3 minutes timeout
    const startTime = Date.now();

    await quickbooksPage.technicalMappingsTransactionDataTable.waitForTableLoading();

    do {
      // Interval time for waiting
      await quickbooksPage.getPage().waitForTimeout(TIMEOUT_USAGE_STATUS / 2);

      // Check if table is ready
      await quickbooksPage.technicalMappingsTransactionDataTable.waitForTableLoading();

      let allHeader;
      try {
        allHeader =
          await quickbooksPage.technicalMappingsTransactionDataTable.getAllHeaders();
      } catch (e) {
        logger.warn(`Error when getting all headers, retrying`);
        continue;
      }

      const count = allHeader.filter(
        (item) => item === EMPTY_COLUMN_HEADER
      ).length;
      if (count === 1) {
        await quickbooksPage.technicalMappingsTransactionDataTable.clickActionCellAndSelectItemByRelevantData(
          [type],
          action
        );
        break;
      }
    } while (Date.now() - startTime < maxWaitTimeMs);

    // If we exit the loop due to timeout
    if (Date.now() - startTime >= maxWaitTimeMs) {
      throw new Error(
        `Timed out waiting for transaction data table to load completely before performing action "${action}" on type "${type}"`
      );
    }
  }

  @ContinueOnFailure()
  @Step(
    'On the QuickBooks Technical Mappings page, check that the Transaction Data includes at least <expectValue> <type> marked as <status>'
  )
  public async checkValueInTransactionData(
    expectValue: string,
    type: string,
    status: string
  ) {
    const quickbooksPage = new QuickbooksPage();
    // Retrieve the actual value of Transaction Data records
    const actualValue =
      await quickbooksPage.technicalMappingsTransactionDataTable.getCellValueByHeaderNameAndRelevantData(
        status,
        [type]
      );
    // Validate and compare the actual value with the expected value
    assert(
      Number(actualValue) >= Number(expectValue),
      `The Transaction Data includes only "${actualValue}" "${type}", which is less than the expected minimum of "${expectValue}".`
    );
  }

  @Step(
    'On the QuickBooks Technical Mappings page, open the <type> page under Transaction Data'
  )
  public async openPageUnderTransactionData(type: string) {
    quickbooksPage = new QuickbooksPage();
    await quickbooksPage.technicalMappingsTransactionDataTable.waitForTableLoading();
    await quickbooksPage.technicalMappingsTransactionDataTable.clickCellByHeaderAndRelevantData(
      'Type',
      [type]
    );
    await baseSteps.waitForTabRebuildingCompletely();
  }

  @Step(
    'On the QuickBooks Technical Mappings page, open value under <headerName> column for <type> on <tableType> table'
  )
  public async openValueUnderColumn(
    headerName: string,
    type: string,
    tableType: string
  ) {
    const maxRetries = 3;
    let attempt = 0;
    let isClickable = false;

    while (attempt < maxRetries) {
      try {
        quickbooksPage = new QuickbooksPage();
        const tableLocator =
          tableType === 'Master Data'
            ? quickbooksPage.technicalMappingsMasterDataTable
            : quickbooksPage.technicalMappingsTransactionDataTable;
        await tableLocator.waitForTableLoading();
        isClickable = await tableLocator.isRowClickableByRelevantData([type]);

        if (isClickable) {
          await tableLocator.clickCellByHeaderAndRelevantData(headerName, [
            type,
          ]);
          await baseSteps.waitForTabRebuildingCompletely();
          return; // Success, exit the method
        }

        // If not clickable, increment attempt and retry
        attempt++;
        if (attempt < maxRetries) {
          // Wait a bit before retrying
          await baseSteps.waitForAddSeconds("2")
        }
      } catch (error) {
        attempt++;
        if (attempt < maxRetries) {
          // Wait a bit before retrying
          await baseSteps.waitForAddSeconds("2")
        } else {
          throw new Error(
            `Failed to open value under column after ${maxRetries} attempts: ${error}`
          );
        }
      }
    }

    // If we've exhausted all retries and it's still not clickable
    if (!isClickable) {
      throw new Error(
        `Row with type '${type}' was not clickable after ${maxRetries} attempts`
      );
    }
  }

  @Step(
    'On the QuickBooks Technical Mappings page, wait for <dataType> sync completely'
  )
  public async waitForDataSyncCompletely(dataType: string) {
    quickbooksPage = new QuickbooksPage();
    // Wait for sync to complete with a timeout
    const maxWaitTimeMs = LONG_TIME * 3; // 3 minutes timeout
    const startTime = Date.now();
    const tableLocator =
      dataType === 'Master Data'
        ? quickbooksPage.technicalMappingsMasterDataTable
        : quickbooksPage.technicalMappingsTransactionDataTable;
    do {
      // Interval time for waiting
      await quickbooksPage.getPage().waitForTimeout(TIMEOUT_USAGE_STATUS / 2);
      // Get current status of the first row for checking
      await tableLocator.waitForTableLoading();
      let allHeader;
      try {
        allHeader = await tableLocator.getAllHeaders();
      } catch (e) {
        logger.warn(`Error when getting all headers, retrying`);
        continue;
      }
      const count = allHeader.filter(
        (item) => item === EMPTY_COLUMN_HEADER
      ).length;
      if (count === 1) {
        await tableLocator.waitForTableLoading();
        break;
      }
    } while (Date.now() - startTime < maxWaitTimeMs);
  }

  ///////////////////////////////////////
  /// Sematic Master Data Table /////////
  ///////////////////////////////////////
  @Step(
    'On the QuickBooks Semantic Mappings page, perform <Action> action on the master data for <type> type'
  )
  public async performActionOnSematicMasterDataTable(
    action: string,
    type: string
  ) {
    quickbooksPage = new QuickbooksPage();
    await quickbooksPage.semanticMappingsMasterDataTable.waitForTableLoading();
    await quickbooksPage.semanticMappingsMasterDataTable.clickActionCellAndSelectItemByRelevantData(
      [type],
      action
    );
  }
  @ContinueOnFailure()
  @Step(
    'On the QuickBooks Semantic Mappings page, check that on the master data displays information correctly <table>'
  )
  public async checkSematicMasterDataDetailsDisplayCorrectly(infoTable: Table) {
    quickbooksPage = new QuickbooksPage();
    await quickbooksPage.semanticMappingsMasterDataTable.waitForTableLoading();
    await quickbooksPage.semanticMappingsMasterDataTable.compareContainsSnapshotByInputHeader(
      infoTable
    );
  }

  @Step(
    'On the QuickBooks Semantic Mappings page, wait for <dataType> sync completely'
  )
  public async waitForSematicDataSyncCompletely(dataType: string) {
    quickbooksPage = new QuickbooksPage();
    // Wait for sync to complete with a timeout
    const maxWaitTimeMs = LONG_TIME * 2; // 2 minutes timeout
    const startTime = Date.now();
    let currentStatus = 'Not Synced';
    do {
      // Get current status of the first row for checking
      await quickbooksPage.semanticMappingsMasterDataTable.waitForTableLoading();
      if (
        (
          await quickbooksPage.semanticMappingsMasterDataTable.cellByHeaderAndRelevantData(
            'Status',
            [dataType]
          )
        ).isVisible()
      ) {
        currentStatus =
          await quickbooksPage.semanticMappingsMasterDataTable.getCellValueByHeaderNameAndRelevantData(
            'Status',
            [dataType]
          );
      }

      if (currentStatus !== 'Running') {
        await quickbooksPage.semanticMappingsMasterDataTable.waitForTableLoading();
        break;
      }
      // Interval time for waiting
      await quickbooksPage.getPage().waitForTimeout(TIMEOUT_USAGE_STATUS / 2);
    } while (Date.now() - startTime < maxWaitTimeMs);
  }


  @ContinueOnFailure()
  @Step(
    'On the QuickBooks Technical Mappings page, check that on the transaction data displays information correctly <table>'
  )
  public async checkTechnicalMappingDetailsDisplayCorrectly(infoTable: Table) {
    quickbooksPage = new QuickbooksPage();
    await quickbooksPage.technicalMappingsTransactionDataTable.waitForTableLoading();
    await quickbooksPage.technicalMappingsTransactionDataTable.compareContainsSnapshotByInputHeader(
      infoTable
    );
  }
}
