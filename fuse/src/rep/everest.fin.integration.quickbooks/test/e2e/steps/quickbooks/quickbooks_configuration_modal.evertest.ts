import { BaseModal } from '@pkg/everest.base.test/test/e2e/pages/BaseModal.evertest';
import { UITemplateActionGroup } from '@pkg/everest.base.test/test/e2e/pages/templateSectionModels/actionGroup.evertest';
import { UITemplateFieldGroup } from '@pkg/everest.base.test/test/e2e/pages/templateSectionModels/fieldGroup.evertest';

export class QuickbooksConfigurationModal extends BaseModal {
  readonly quickbooksConfigurationActionGroup: UITemplateActionGroup;
  readonly defaultFieldGroup: UITemplateFieldGroup;
  protected dynamicFieldGroup: UITemplateFieldGroup;

  constructor() {
    super();
    this.quickbooksConfigurationActionGroup = new UITemplateActionGroup(
      `${this.modalSelector} [data-testid="quickbooksConfigurationActionGroup"]`
    );
    this.defaultFieldGroup = new UITemplateFieldGroup( `${this.modalSelector} [data-testid="defaultValuesFieldGroup"]`)

  }

  getFieldGroup(sectionName: string) {
    return new UITemplateFieldGroup(
      `${this.modalSelector} [data-testid="fieldGroup${sectionName.replaceAll(
        /\s+/g,
        ''
      )}"]`
    );
  }
}
