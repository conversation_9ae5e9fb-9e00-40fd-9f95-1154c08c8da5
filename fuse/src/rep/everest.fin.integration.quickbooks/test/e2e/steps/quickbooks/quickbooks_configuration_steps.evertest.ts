import { Step, Table } from 'gauge-ts';
import { QuickbooksConfigurationModal } from '@pkg/everest.fin.integration.quickbooks/test/e2e/steps/quickbooks/quickbooks_configuration_modal.evertest';
import { convertAllVariableInTableToValueV2 } from '@pkg/everest.base.test/test/e2e/utils/data/convertDataUtils.evertest';

let quickbooksConfigurationModal: QuickbooksConfigurationModal;

export default class QuickbooksConfigurationSteps {
  @Step([
    'On the Migration Mode modal, tick <option> option',
    'On the Transactional Data Import Mode modal, tick <option> option',
  ])
  public async selectQuickbooksConfigurationModeOption(
    option: string
  ): Promise<void> {
    quickbooksConfigurationModal = new QuickbooksConfigurationModal();
    await quickbooksConfigurationModal.quickbooksConfigurationActionGroup.checkItem(
      option
    );
  }

  @Step(
    'On the Master Data configuration modal, fill data to <sessionName> section with following data <table>'
  )
  public async fillMasterData(
    sessionName: string,
    infoTable: Table
  ): Promise<void> {
    quickbooksConfigurationModal = new QuickbooksConfigurationModal();
    infoTable = await convertAllVariableInTableToValueV2(infoTable);
    await quickbooksConfigurationModal
      .getFieldGroup(sessionName)
      .fillForm(infoTable);
  }

  @Step(
    'On the Maintain Default Values modal, fill data with following data <table>'
  )
  public async fillDefaultValues(
    infoTable: Table
  ): Promise<void> {
    quickbooksConfigurationModal = new QuickbooksConfigurationModal();
    infoTable = await convertAllVariableInTableToValueV2(infoTable);
    await quickbooksConfigurationModal.defaultFieldGroup.fillForm(infoTable);
  }
}
