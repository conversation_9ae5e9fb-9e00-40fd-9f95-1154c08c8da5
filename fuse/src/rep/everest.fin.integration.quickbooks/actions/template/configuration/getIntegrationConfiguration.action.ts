import type { ISession } from '@everestsystems/content-core';
import { AmortizationScheduleHeader } from '@pkg/everest.fin.accounting/types/AmortizationScheduleHeader';
import { InvoicePlan } from '@pkg/everest.fin.accounting/types/InvoicePlan';
import { JournalEntryHeader } from '@pkg/everest.fin.accounting/types/JournalEntryHeader';
import { JournalEntryLine } from '@pkg/everest.fin.accounting/types/JournalEntryLine';
import { SalesOrder } from '@pkg/everest.fin.accounting/types/SalesOrder';
import { DeltaVariableTypes } from '@pkg/everest.fin.integration.base.ui/public/enums/DeltaVariableTypes';
import type { IntegrationConfig } from '@pkg/everest.fin.integration.base.ui/public/types/IntegrationConfig';
import { getMigrationModeSessionVariable } from '@pkg/everest.fin.integration.base.ui/public/utils/sessionVariable/getMigrationModeSessionVariable';
import {
  getSelectedEntity,
  getSessionId,
  getSessionVariableConfigValueOrDefault,
  saveAndReturnDefaultSessionVariableConfig,
} from '@pkg/everest.fin.integration.base.ui/public/utils/sessionVariable/sessionVariable';
import { EvstExtractionProviders } from '@pkg/everest.fin.integration.base/types/enums/ExtractionProviders';
import { EvstStagingStatus } from '@pkg/everest.fin.integration.base/types/enums/StagingStatus';
import {
  accountStagingParamsV2,
  amortizationScheduleStagingParams,
  bankEntryStagingFromCheckParams,
  bankEntryStagingFromDepositParams,
  bankEntryStagingFromExpenseParams,
  bankEntryStagingFromFundTransferParams,
  billPaymentStagingParams,
  billStagingParams,
  classStagingParams,
  creditCardEntryFromCreditCardPaymentStagingParams,
  creditCardEntryFromPurchaseStagingParams,
  creditMemoStagingParams,
  currencyStagingParams,
  customerPaymentStagingParams,
  customerStagingParams,
  defaultAccountMappingV2,
  defaultAmortizationScheduleMapping,
  defaultBankEntryFromCheckMapping,
  defaultBankEntryFromDepositMapping,
  defaultBankEntryFromExpenseMapping,
  defaultBankEntryFromFundTransferMapping,
  defaultBillMapping,
  defaultBillPaymentMapping,
  defaultClassMapping,
  defaultcreditCardEntryFromCreditCardPaymentMapping,
  defaultcreditCardEntryFromPurchaseMapping,
  defaultCreditMemoMapping,
  defaultCurrencyMapping,
  defaultCustomerMapping,
  defaultCustomerPaymentMapping,
  defaultDepartmentMapping,
  defaultEmployeeExpenseFromBillMapping,
  defaultEmployeeFromVendorMapping,
  defaultEmployeeMapping,
  defaultEntityMapping,
  defaultFiscalCalendarMapping,
  defaultInvoiceMapping,
  defaultJournalEntryMapping,
  defaultPaymentMethodMapping,
  defaultPaymentTermMapping,
  defaultProductMapping,
  defaultRevenuePlanMapping,
  defaultVendorCreditMapping,
  defaultVendorMapping,
  defaultVendorPaymentTermMapping,
  departmentFromAccountStagingParams,
  departmentStagingParams,
  employeeExpenseFromBillStagingParams,
  employeeExpensePaymentFromBillPaymentStagingParams,
  employeeExpensePaymentFrommBillPaymentMapping,
  employeeFromVendorStagingParams,
  employeeStagingParams,
  entiyStagingParams,
  fiscalCalendarStagingParams,
  fundTransferFromPurchaseMapping,
  fundTransferFromPurchaseStagingParams,
  fundTransferMapping,
  fundTransferStagingParams,
  invoiceStagingParams,
  journalEntryStagingParams,
  PACKAGE_NAME,
  paymentMethodStagingParams,
  paymentTermStagingParams,
  productStagingParams,
  ProviderModel,
  revenuePlanStagingParams,
  vendorCreditStagingParams,
  vendorPaymentTermStagingParams,
  vendorStagingParams,
} from '@pkg/everest.fin.integration.quickbooks/utils/constants';
import { DeltaVariableKey } from '@pkg/everest.fin.integration.quickbooks/utils/deltaVariable/deltaVariable';
import { companyStartYear } from '@pkg/everest.fin.integration.quickbooks/utils/sessionVariable/companyStartYear';
import { createBillsAsDraftOnFailure } from '@pkg/everest.fin.integration.quickbooks/utils/sessionVariable/createBillsAsDraftOnFailure';
import {
  defaultManualCreditMemoType,
  isEarlyRenewalCreditMemoInvoiceLineLinkRequired,
  selectedCreditMemoIdsForSkippingCreditMemos,
} from '@pkg/everest.fin.integration.quickbooks/utils/sessionVariable/creditMemo';
import { defaultBankAccountCustomerPayment } from '@pkg/everest.fin.integration.quickbooks/utils/sessionVariable/defaultBankAccountCustomerPayment';
import { selectedDiscountProductIds } from '@pkg/everest.fin.integration.quickbooks/utils/sessionVariable/discountProducts';
import { entitySessionVariable } from '@pkg/everest.fin.integration.quickbooks/utils/sessionVariable/entitySessionVariable';
import {
  expenseEmployeeDisplayNameSubstring,
  expenseEmployeeEmailDomain,
  filterEmployeeFromVendorByEmailDomain,
  filterEmployeeFromVendorByName,
  filterEmployeeFromVendorWhenExistInEverest,
} from '@pkg/everest.fin.integration.quickbooks/utils/sessionVariable/expenseEmployeeMode';
import { selectedFinanceChargeProductIds } from '@pkg/everest.fin.integration.quickbooks/utils/sessionVariable/financeChargeProducts';
import {
  isInvoicePlanLinkRequired,
  isSalesOrderRequired,
  selectedInvoiceIdsForSkippingInvoices,
} from '@pkg/everest.fin.integration.quickbooks/utils/sessionVariable/invoice';
import { journalEntryCreationAndReversalForCreditMemo } from '@pkg/everest.fin.integration.quickbooks/utils/sessionVariable/journalEntryCreationAndReversalForCreditMemo';
import { journalEntryCreationAndReversalForInvoices } from '@pkg/everest.fin.integration.quickbooks/utils/sessionVariable/journalEntryCreationAndReversalForInvoices';
import { journalMaxTimePeriod } from '@pkg/everest.fin.integration.quickbooks/utils/sessionVariable/journalMaxTimePeriod';
import { masterDataImportMode } from '@pkg/everest.fin.integration.quickbooks/utils/sessionVariable/masterDataImportMode';
import { onlyOpenVendorBilllDataMode } from '@pkg/everest.fin.integration.quickbooks/utils/sessionVariable/onlyOpenVendorBilllDataMode';
import {
  defaultPaymentMethod,
  requireInvoiceLinkForPayment,
  selectedPaymentIdsForSkippingPayments,
} from '@pkg/everest.fin.integration.quickbooks/utils/sessionVariable/payment';
import { qboApiMinorVersion } from '@pkg/everest.fin.integration.quickbooks/utils/sessionVariable/qboApiMinorVersion';
import { qboImportFailWhenExistMode } from '@pkg/everest.fin.integration.quickbooks/utils/sessionVariable/qboImportFailWhenExistMode';
import { selectedCustomerIdsForSkipping } from '@pkg/everest.fin.integration.quickbooks/utils/sessionVariable/selectedCustomerIdsForSkipping';
import { selectedVendorForFundTransferFromPurchase } from '@pkg/everest.fin.integration.quickbooks/utils/sessionVariable/selectedVendorForFundTransferFromPurchase';
import { selectedVendorForIntegrateBankEntryExpenses } from '@pkg/everest.fin.integration.quickbooks/utils/sessionVariable/selectedVendorForIntegrateBankEntryExpenses';
import { selectedVendorForSkippingBills } from '@pkg/everest.fin.integration.quickbooks/utils/sessionVariable/selectedVendorForSkippingBills';
import { selectedVendorIdsForSkipping } from '@pkg/everest.fin.integration.quickbooks/utils/sessionVariable/selectedVendorIdsForSkipping';
import { skipRevenuePlanZeroAmountCheckMode } from '@pkg/everest.fin.integration.quickbooks/utils/sessionVariable/skipRevenuePlanZeroAmountCheck';
import { upperBoundOnMigrationTransactionDate } from '@pkg/everest.fin.integration.quickbooks/utils/sessionVariable/upperBoundOnMigrationTransactionDate';

export default async function getConfiguration(
  env: ISession
): Promise<IntegrationConfig> {
  const [selectedEntity, sessionId] = await Promise.all([
    getSelectedEntity(env, PACKAGE_NAME),
    getSessionId(env, PACKAGE_NAME),
  ]);

  const migrationModeSessionVariable =
    getMigrationModeSessionVariable(PACKAGE_NAME);

  const [
    masterDataImportModeValue,
    companyStartYearValue,
    migrationModeValue,
    filterEmployeeFromVendorByNameValue,
    expenseEmployeeDisplayNameSubstringValue,
    filterEmployeeFromVendorByEmailValue,
    expenseEmployeeEmailDomainValue,
    filterEmployeeFromVendorWhenExistInEverestValue,
    isSalesOrderRequiredValue,
    isInvoicePlanLinkRequiredValue,
    journalEntryCreationAndReversalForInvoicesValue,
    journalEntryCreationAndReversalForCreditMemoValue,
    selectedInvoiceIdsForSkippingInvoicesValue,
    selectedCreditMemoIdsForSkippingCreditMemosValue,
    defaultManualCreditMemoTypeValue,
    isEarlyRenewalCreditMemoInvoiceLineLinkRequiredValue,
    journalMaxTimePeriodValue,
    selectedVendorForSkippingBillsValue,
    selectedVendorForIntegrateBankEntryExpensesValue,
    defaultBankAccountCustomerPaymentValue,
    defaultPaymentMethodValue,
    selectedVendorForFundTransferFromPurchaseValue,
    createBillsAsDraftOnFailureValue,
    requireInvoiceLinkForPaymentValue,
    selectedPaymentIdsForSkippingPaymentsValue,
    selectedDiscountProductIdsValue,
    selectedFinanceChargeProductIdsValue,
    qboImportFailWhenExistModeValue,
    skipRevenuePlanZeroAmountCheckModeValue,
    upperBoundOnMigrationTransactionDateValue,
    qboApiMinorVersionValue,
    onlyOpenVendorBilllDataModeValue,
    selectedCustomerIdsForSkippingValue,
    selectedVendorIdsForSkippingValue,
  ] = await Promise.all([
    getSessionVariableConfigValueOrDefault(
      env,
      sessionId,
      masterDataImportMode.configKey,
      masterDataImportMode.defaultValue
    ),
    saveAndReturnDefaultSessionVariableConfig(env, sessionId, companyStartYear),
    saveAndReturnDefaultSessionVariableConfig(
      env,
      sessionId,
      migrationModeSessionVariable
    ),
    saveAndReturnDefaultSessionVariableConfig(
      env,
      sessionId,
      filterEmployeeFromVendorByName
    ),
    saveAndReturnDefaultSessionVariableConfig(
      env,
      sessionId,
      expenseEmployeeDisplayNameSubstring
    ),
    saveAndReturnDefaultSessionVariableConfig(
      env,
      sessionId,
      filterEmployeeFromVendorByEmailDomain
    ),
    saveAndReturnDefaultSessionVariableConfig(
      env,
      sessionId,
      expenseEmployeeEmailDomain
    ),
    saveAndReturnDefaultSessionVariableConfig(
      env,
      sessionId,
      filterEmployeeFromVendorWhenExistInEverest
    ),
    saveAndReturnDefaultSessionVariableConfig(
      env,
      sessionId,
      isSalesOrderRequired
    ),
    saveAndReturnDefaultSessionVariableConfig(
      env,
      sessionId,
      isInvoicePlanLinkRequired
    ),
    saveAndReturnDefaultSessionVariableConfig(
      env,
      sessionId,
      journalEntryCreationAndReversalForInvoices
    ),
    saveAndReturnDefaultSessionVariableConfig(
      env,
      sessionId,
      journalEntryCreationAndReversalForCreditMemo
    ),
    saveAndReturnDefaultSessionVariableConfig(
      env,
      sessionId,
      selectedInvoiceIdsForSkippingInvoices
    ),
    saveAndReturnDefaultSessionVariableConfig(
      env,
      sessionId,
      selectedCreditMemoIdsForSkippingCreditMemos
    ),
    saveAndReturnDefaultSessionVariableConfig(
      env,
      sessionId,
      defaultManualCreditMemoType
    ),
    saveAndReturnDefaultSessionVariableConfig(
      env,
      sessionId,
      isEarlyRenewalCreditMemoInvoiceLineLinkRequired
    ),
    getSessionVariableConfigValueOrDefault(
      env,
      sessionId,
      journalMaxTimePeriod.configKey,
      journalMaxTimePeriod.defaultValue
    ),
    getSessionVariableConfigValueOrDefault(
      env,
      sessionId,
      selectedVendorForSkippingBills.configKey,
      selectedVendorForSkippingBills.defaultValue
    ),
    getSessionVariableConfigValueOrDefault(
      env,
      sessionId,
      selectedVendorForIntegrateBankEntryExpenses.configKey,
      selectedVendorForIntegrateBankEntryExpenses.defaultValue
    ),
    getSessionVariableConfigValueOrDefault(
      env,
      sessionId,
      defaultBankAccountCustomerPayment.configKey,
      defaultBankAccountCustomerPayment.defaultValue
    ),
    getSessionVariableConfigValueOrDefault(
      env,
      sessionId,
      defaultPaymentMethod.configKey,
      defaultPaymentMethod.defaultValue
    ),
    getSessionVariableConfigValueOrDefault(
      env,
      sessionId,
      selectedVendorForFundTransferFromPurchase.configKey,
      selectedVendorForFundTransferFromPurchase.defaultValue
    ),
    getSessionVariableConfigValueOrDefault(
      env,
      sessionId,
      createBillsAsDraftOnFailure.configKey,
      createBillsAsDraftOnFailure.defaultValue
    ),
    saveAndReturnDefaultSessionVariableConfig(
      env,
      sessionId,
      requireInvoiceLinkForPayment
    ),
    getSessionVariableConfigValueOrDefault(
      env,
      sessionId,
      selectedPaymentIdsForSkippingPayments.configKey,
      selectedPaymentIdsForSkippingPayments.defaultValue
    ),
    getSessionVariableConfigValueOrDefault(
      env,
      sessionId,
      selectedDiscountProductIds.configKey,
      selectedDiscountProductIds.defaultValue
    ),
    getSessionVariableConfigValueOrDefault(
      env,
      sessionId,
      selectedFinanceChargeProductIds.configKey,
      selectedFinanceChargeProductIds.defaultValue
    ),
    getSessionVariableConfigValueOrDefault(
      env,
      sessionId,
      qboImportFailWhenExistMode.configKey,
      qboImportFailWhenExistMode.defaultValue
    ),
    getSessionVariableConfigValueOrDefault(
      env,
      sessionId,
      skipRevenuePlanZeroAmountCheckMode.configKey,
      skipRevenuePlanZeroAmountCheckMode.defaultValue
    ),
    getSessionVariableConfigValueOrDefault(
      env,
      sessionId,
      upperBoundOnMigrationTransactionDate.configKey,
      upperBoundOnMigrationTransactionDate.defaultValue
    ),
    getSessionVariableConfigValueOrDefault(
      env,
      sessionId,
      qboApiMinorVersion.configKey,
      qboApiMinorVersion.defaultValue
    ),
    getSessionVariableConfigValueOrDefault(
      env,
      sessionId,
      onlyOpenVendorBilllDataMode.configKey,
      onlyOpenVendorBilllDataMode.defaultValue
    ),
    getSessionVariableConfigValueOrDefault(
      env,
      sessionId,
      selectedCustomerIdsForSkipping.configKey,
      selectedCustomerIdsForSkipping.defaultValue
    ),
    getSessionVariableConfigValueOrDefault(
      env,
      sessionId,
      selectedVendorIdsForSkipping.configKey,
      selectedVendorIdsForSkipping.defaultValue
    ),
  ]);

  const lastSyncDeltaVariable = {
    key: DeltaVariableKey.MetaDataLastUpdatedTime,
    sessionId: sessionId,
    type: DeltaVariableTypes.DeltaVariableSessionAware,
  };

  const quickBooksConfig: IntegrationConfig = {
    general: {
      package: PACKAGE_NAME,
      providerName: EvstExtractionProviders.Quickbooks,
      enableDataSyncRunTracking: true,
      integrationSessionId: sessionId,
      enableManualLink: true,
      enableBusinessUserStagingView: true,
    },
    templates: {
      masterData: {
        hideColumns: [EvstStagingStatus.NeedsInteraction],
      },
      overview: {
        title: 'Quickbooks',
        hasOneEntityPerConnector: true,
        editConnectorTemplateLink:
          '/templates/everest.fin.integration.quickbooks/uinext/connector',
        showLastSyncColumn: true,
        showStatusColumn: true,
        testConnectionOnOverview: true,
        showCreateConnectionOverview: true,
        configurations: {
          base: { useDateRange: true },
          custom: [
            {
              name: 'General',
              description: 'Modes, Upper Bound Transaction Date',
              initialState: {
                sessionId,
                sessionVariablesLevels: [
                  {
                    title: 'General',
                    configs: [
                      {
                        sessionVariableConfig: migrationModeSessionVariable,
                        value: migrationModeValue,
                      },
                      {
                        sessionVariableConfig:
                          upperBoundOnMigrationTransactionDate,
                        value: upperBoundOnMigrationTransactionDateValue,
                      },
                      {
                        sessionVariableConfig: qboImportFailWhenExistMode,
                        value: qboImportFailWhenExistModeValue,
                      },
                      {
                        sessionVariableConfig: masterDataImportMode,
                        value: masterDataImportModeValue,
                      },
                      {
                        sessionVariableConfig: qboApiMinorVersion,
                        value: qboApiMinorVersionValue,
                      },
                    ],
                  },
                ],
              },
            },
            {
              name: 'Master Data',
              description: 'Customer, Entity, Fiscal Calendar, Vendor',
              initialState: {
                sessionId,
                sessionVariablesLevels: [
                  {
                    title: 'Entity',
                    configs: [
                      {
                        sessionVariableConfig: entitySessionVariable,
                        value: Number(selectedEntity?.id),
                      },
                    ],
                  },
                  {
                    title: 'Fiscal Calendar',
                    configs: [
                      {
                        sessionVariableConfig: companyStartYear,
                        value: companyStartYearValue,
                      },
                    ],
                  },
                  {
                    title: 'Customer',
                    configs: [
                      {
                        sessionVariableConfig: selectedCustomerIdsForSkipping,
                        value: selectedCustomerIdsForSkippingValue,
                      },
                    ],
                  },
                  {
                    title: 'Vendor',
                    configs: [
                      {
                        sessionVariableConfig: filterEmployeeFromVendorByName,
                        value: filterEmployeeFromVendorByNameValue,
                      },
                      {
                        sessionVariableConfig:
                          expenseEmployeeDisplayNameSubstring,
                        value: expenseEmployeeDisplayNameSubstringValue,
                      },
                      {
                        sessionVariableConfig:
                          filterEmployeeFromVendorByEmailDomain,
                        value: filterEmployeeFromVendorByEmailValue,
                      },
                      {
                        sessionVariableConfig: expenseEmployeeEmailDomain,
                        value: expenseEmployeeEmailDomainValue,
                      },
                      {
                        sessionVariableConfig:
                          filterEmployeeFromVendorWhenExistInEverest,
                        value: filterEmployeeFromVendorWhenExistInEverestValue,
                      },
                      {
                        sessionVariableConfig: selectedVendorIdsForSkipping,
                        value: selectedVendorIdsForSkippingValue,
                      },
                    ],
                  },
                ],
              },
            },
            {
              name: 'Transaction Data',
              description:
                'Journal, Vendor Bills, Invoices, Credit Memo, Customer Payments, Fund Transfers, Bank Entries, Revenue Plan',
              initialState: {
                sessionId,
                sessionVariablesLevels: [
                  {
                    title: 'Journal',
                    configs: [
                      {
                        sessionVariableConfig: journalMaxTimePeriod,
                        value: journalMaxTimePeriodValue,
                      },
                    ],
                  },
                  {
                    title: 'Vendor Bills',
                    configs: [
                      {
                        sessionVariableConfig: createBillsAsDraftOnFailure,
                        value: createBillsAsDraftOnFailureValue,
                      },
                      {
                        sessionVariableConfig: onlyOpenVendorBilllDataMode,
                        value: onlyOpenVendorBilllDataModeValue,
                      },
                      {
                        sessionVariableConfig: selectedVendorForSkippingBills,
                        value: selectedVendorForSkippingBillsValue,
                      },
                    ],
                  },
                  {
                    title: 'Invoice',
                    configs: [
                      {
                        sessionVariableConfig: isSalesOrderRequired,
                        value: isSalesOrderRequiredValue,
                      },
                      {
                        sessionVariableConfig: isInvoicePlanLinkRequired,
                        value: isInvoicePlanLinkRequiredValue,
                      },
                      {
                        sessionVariableConfig:
                          journalEntryCreationAndReversalForInvoices,
                        value: journalEntryCreationAndReversalForInvoicesValue,
                      },
                      {
                        sessionVariableConfig:
                          selectedInvoiceIdsForSkippingInvoices,
                        value: selectedInvoiceIdsForSkippingInvoicesValue,
                      },
                    ],
                  },
                  {
                    title: 'Credit Memo',
                    configs: [
                      {
                        sessionVariableConfig:
                          journalEntryCreationAndReversalForCreditMemo,
                        value:
                          journalEntryCreationAndReversalForCreditMemoValue,
                      },
                      {
                        sessionVariableConfig:
                          selectedCreditMemoIdsForSkippingCreditMemos,
                        value: selectedCreditMemoIdsForSkippingCreditMemosValue,
                      },
                      {
                        sessionVariableConfig: defaultManualCreditMemoType,
                        value: defaultManualCreditMemoTypeValue,
                      },
                      {
                        sessionVariableConfig:
                          isEarlyRenewalCreditMemoInvoiceLineLinkRequired,
                        value:
                          isEarlyRenewalCreditMemoInvoiceLineLinkRequiredValue,
                      },
                    ],
                  },
                  {
                    title: 'Customer Payments',
                    configs: [
                      {
                        sessionVariableConfig:
                          defaultBankAccountCustomerPayment,
                        value: defaultBankAccountCustomerPaymentValue,
                      },
                      {
                        sessionVariableConfig: defaultPaymentMethod,
                        value: defaultPaymentMethodValue,
                      },
                      {
                        sessionVariableConfig: requireInvoiceLinkForPayment,
                        value: requireInvoiceLinkForPaymentValue,
                      },
                      {
                        sessionVariableConfig:
                          selectedPaymentIdsForSkippingPayments,
                        value: selectedPaymentIdsForSkippingPaymentsValue,
                      },
                      {
                        sessionVariableConfig: selectedDiscountProductIds,
                        value: selectedDiscountProductIdsValue,
                      },
                      {
                        sessionVariableConfig: selectedFinanceChargeProductIds,
                        value: selectedFinanceChargeProductIdsValue,
                      },
                    ],
                  },
                  {
                    title: 'Fund Transfers',
                    configs: [
                      {
                        sessionVariableConfig:
                          selectedVendorForFundTransferFromPurchase,
                        value: selectedVendorForFundTransferFromPurchaseValue,
                      },
                    ],
                  },
                  {
                    title: 'Bank Entries',
                    configs: [
                      {
                        sessionVariableConfig:
                          selectedVendorForIntegrateBankEntryExpenses,
                        value: selectedVendorForIntegrateBankEntryExpensesValue,
                      },
                    ],
                  },
                  {
                    title: 'Revenue Plan',
                    configs: [
                      {
                        sessionVariableConfig:
                          skipRevenuePlanZeroAmountCheckMode,
                        value: skipRevenuePlanZeroAmountCheckModeValue,
                      },
                    ],
                  },
                ],
              },
            },
          ],
        },
      },
    },
    executions: {
      metadata: [
        {
          displayName: 'Entity',
          displayNamePlural: 'Entities', // multiple entities does not exist on QBO
          everestModel: entiyStagingParams.everestModel,
          providerModel: entiyStagingParams.providerModel,
          originalIdKey: entiyStagingParams.originalIdKey,
          mappings: {
            api: {
              name: defaultEntityMapping,
              type: {
                technical: true,
                semantic: false, // entity can be selected via Configuration tab - no need for a semantic mapping
              },
            },
          },
          applicationUrl: '/uicode/entities',
          lastSyncDeltaVariable,
        },
        {
          displayName: 'Currency',
          displayNamePlural: 'Currencies',
          everestModel: currencyStagingParams.everestModel,
          providerModel: currencyStagingParams.providerModel,
          originalIdKey: currencyStagingParams.originalIdKey,
          mappings: {
            api: {
              name: defaultCurrencyMapping,
              type: {
                technical: true,
                semantic: true,
              },
            },
          },
          applicationUrl: '/templates/everest.base/uinext/activeCurrency',
          lastSyncDeltaVariable,
        },
        {
          displayName: 'Fiscal Calendar',
          displayNamePlural: 'Fiscal Calendars',
          everestModel: fiscalCalendarStagingParams.everestModel,
          providerModel: fiscalCalendarStagingParams.providerModel,
          originalIdKey: fiscalCalendarStagingParams.originalIdKey,
          mappings: {
            api: {
              name: defaultFiscalCalendarMapping,
              type: {
                technical: true,
                semantic: false, // fiscal calendar is not referenced later - therefore no need to semantically match it
              },
            },
          },
          applicationUrl:
            '/templates/everest.fin.accounting/accountingPeriod/uinext/fiscalCalendars',
          lastSyncDeltaVariable,
        },
        {
          displayName: 'Department',
          displayNamePlural: 'Departments',
          everestModel: departmentStagingParams.everestModel,
          providerModel: departmentStagingParams.providerModel,
          originalIdKey: departmentStagingParams.originalIdKey,
          mappings: {
            api: {
              name: defaultDepartmentMapping,
              type: {
                technical: true,
                semantic: true,
              },
            },
          },
          applicationUrl:
            '/templates/everest.hr.base/uinext/department/departmentHierarchy',
          lastSyncDeltaVariable,
        },
        {
          displayName: 'Department from Class',
          displayNamePlural: 'Departments from Classes',
          everestModel: classStagingParams.everestModel,
          providerModel: classStagingParams.providerModel,
          originalIdKey: classStagingParams.originalIdKey,
          mappings: {
            api: {
              name: defaultClassMapping,
              type: {
                technical: true,
                semantic: true,
              },
            },
          },
          applicationUrl:
            '/templates/everest.hr.base/uinext/department/departmentHierarchy',
          lastSyncDeltaVariable,
        },
        {
          displayName: 'Account',
          displayNamePlural: 'Accounts',
          everestModel: accountStagingParamsV2.everestModel,
          providerModel: accountStagingParamsV2.providerModel,
          originalIdKey: accountStagingParamsV2.originalIdKey,
          mappings: {
            api: {
              name: defaultAccountMappingV2,
              type: {
                technical: true,
                semantic: true,
              },
            },
          },
          applicationUrl:
            '/templates/everest.fin.accounting/uinext/chartOfAccountsList',
          lastSyncDeltaVariable,
        },
        {
          displayName: 'Departments from Accounts',
          displayNamePlural: 'Departments from Accounts',
          everestModel: departmentFromAccountStagingParams.everestModel,
          providerModel: departmentFromAccountStagingParams.providerModel,
          originalIdKey: departmentFromAccountStagingParams.originalIdKey,
          mappings: {
            api: {
              name: defaultAccountMappingV2,
              type: {
                semantic: true,
              },
            },
          },
          applicationUrl:
            '/templates/everest.hr.base/uinext/department/departmentHierarchy',
          lastSyncDeltaVariable,
        },
        {
          displayName: 'Vendor Payment Term',
          displayNamePlural: 'Vendor Payment Terms',
          everestModel: vendorPaymentTermStagingParams.everestModel,
          providerModel: vendorPaymentTermStagingParams.providerModel,
          originalIdKey: vendorPaymentTermStagingParams.originalIdKey,
          mappings: {
            api: {
              name: defaultVendorPaymentTermMapping,
              type: {
                technical: true,
                semantic: true,
              },
            },
          },
          applicationUrl:
            '/admintool?model=urn%3Aevst%3Aeverest%3Afin%2Fexpense%3Amodel%2Fnode%3AVendorPaymentTerm',
          lastSyncDeltaVariable,
        },
        {
          displayName: 'Vendor',
          displayNamePlural: 'Vendors',
          everestModel: vendorStagingParams.everestModel,
          providerModel: vendorStagingParams.providerModel,
          originalIdKey: vendorStagingParams.originalIdKey,
          mappings: {
            api: {
              name: defaultVendorMapping,
              type: {
                technical: true,
                semantic: true,
              },
            },
          },
          applicationUrl:
            '/templates/everest.fin.expense/uinext/vendorMgmt/vendors',
          lastSyncDeltaVariable,
        },
        {
          displayName: 'Payment Term',
          displayNamePlural: 'Payment Terms',
          everestModel: paymentTermStagingParams.everestModel,
          providerModel: paymentTermStagingParams.providerModel,
          originalIdKey: paymentTermStagingParams.originalIdKey,
          mappings: {
            api: {
              name: defaultPaymentTermMapping,
              type: {
                technical: true,
                semantic: true,
              },
            },
          },
          applicationUrl:
            '/templates/everest.fin.accounting/qtc/invoiceConfiguration/uinext/settings',
          lastSyncDeltaVariable,
        },
        {
          displayName: 'Payment Method',
          displayNamePlural: 'Payment Methods',
          everestModel: paymentMethodStagingParams.everestModel,
          providerModel: paymentMethodStagingParams.providerModel,
          originalIdKey: paymentMethodStagingParams.originalIdKey,
          mappings: {
            api: {
              name: defaultPaymentMethodMapping,
              type: {
                technical: false,
                semantic: true,
              },
            },
          },
          applicationUrl: '',
          lastSyncDeltaVariable,
        },
        {
          displayName: 'Customer',
          displayNamePlural: 'Customers',
          everestModel: customerStagingParams.everestModel,
          providerModel: customerStagingParams.providerModel,
          originalIdKey: customerStagingParams.originalIdKey,
          mappings: {
            api: {
              name: defaultCustomerMapping,
              type: {
                technical: true,
                semantic: true,
              },
            },
          },
          applicationUrl:
            '/templates/everest.fin.accounting/qtc/customer/uinext/customers',
          lastSyncDeltaVariable,
        },
        {
          displayName: 'Product',
          displayNamePlural: 'Products',
          everestModel: productStagingParams.everestModel,
          providerModel: productStagingParams.providerModel,
          originalIdKey: productStagingParams.originalIdKey,
          mappings: {
            api: {
              name: defaultProductMapping,
              type: {
                technical: true,
                semantic: true,
              },
            },
          },
          applicationUrl:
            '/templates/everest.fin.accounting/qtc/masterData/uinext/products',
          lastSyncDeltaVariable,
        },
        {
          displayName: 'Employee',
          displayNamePlural: 'Employees',
          everestModel: employeeStagingParams.everestModel,
          providerModel: employeeStagingParams.providerModel,
          originalIdKey: employeeStagingParams.originalIdKey,
          mappings: {
            api: {
              name: defaultEmployeeMapping,
              type: {
                technical: true,
                semantic: true,
              },
            },
          },
          applicationUrl:
            '/templates/everest.hr.base/uinext/employee/employeeAdminList',
          lastSyncDeltaVariable,
        },
        {
          displayName: 'Employee from Vendor',
          displayNamePlural: 'Employees from Vendors',
          everestModel: employeeFromVendorStagingParams.everestModel,
          providerModel: employeeFromVendorStagingParams.providerModel,
          originalIdKey: employeeFromVendorStagingParams.originalIdKey,
          mappings: {
            api: {
              name: defaultEmployeeFromVendorMapping,
              type: {
                technical: true,
                semantic: true,
              },
            },
          },
          applicationUrl:
            '/templates/everest.hr.base/uinext/employee/employeeAdminList',
          lastSyncDeltaVariable,
        },
      ],
      businessData: [
        {
          displayName: 'Journal Entry',
          displayNamePlural: 'Journal Entries',
          everestModel: journalEntryStagingParams.everestModel,
          providerModel: journalEntryStagingParams.providerModel,
          originalIdKey: journalEntryStagingParams.originalIdKey,
          mappings: {
            api: {
              name: defaultJournalEntryMapping,
              type: {
                technical: true,
                semantic: false,
              },
            },
          },
          applicationUrl: '@uicode:journalEntries',
          lastSyncDeltaVariable: {
            key: DeltaVariableKey.LastModifiedDate,
            sessionId: sessionId,
            type: DeltaVariableTypes.DeltaVariableSessionAware,
          },
        },
        {
          displayName: 'Vendor Bill',
          displayNamePlural: 'Vendor Bills',
          everestModel: billStagingParams.everestModel,
          providerModel: billStagingParams.providerModel,
          originalIdKey: billStagingParams.originalIdKey,
          mappings: {
            api: {
              name: defaultBillMapping,
              type: {
                technical: true,
                semantic: false,
              },
            },
          },
          applicationUrl:
            '/templates/everest.fin.expense/uinext/vendorMgmt/vendorBills',
          lastSyncDeltaVariable,
        },
        {
          displayName: 'Vendor Bill Payments',
          displayNamePlural: 'Vendor Bill Payments',
          everestModel: billPaymentStagingParams.everestModel,
          providerModel: billPaymentStagingParams.providerModel,
          originalIdKey: billPaymentStagingParams.originalIdKey,
          mappings: {
            api: {
              name: defaultBillPaymentMapping,
              type: {
                technical: true,
                semantic: false,
              },
            },
          },
          applicationUrl:
            '/templates/everest.fin.expense/payment/uinext/paymentsHistory',
          lastSyncDeltaVariable,
        },
        {
          displayName: 'Employee Expense from Bill',
          displayNamePlural: 'Employee Expenses from Bills',
          everestModel: employeeExpenseFromBillStagingParams.everestModel,
          providerModel: employeeExpenseFromBillStagingParams.providerModel,
          originalIdKey: employeeExpenseFromBillStagingParams.originalIdKey,
          mappings: {
            api: {
              name: defaultEmployeeExpenseFromBillMapping,
              type: {
                technical: true,
                semantic: false,
              },
            },
          },
          applicationUrl:
            '/templates/everest.fin.expense/uinext/expenseMgmt/employeeExpenses',
          lastSyncDeltaVariable,
        },
        {
          displayName: 'Employee Expense Payments',
          displayNamePlural: 'Employee Expense Payments',
          everestModel:
            employeeExpensePaymentFromBillPaymentStagingParams.everestModel,
          providerModel:
            employeeExpensePaymentFromBillPaymentStagingParams.providerModel,
          originalIdKey:
            employeeExpensePaymentFromBillPaymentStagingParams.originalIdKey,
          mappings: {
            api: {
              name: employeeExpensePaymentFrommBillPaymentMapping,
              type: {
                technical: true,
                semantic: false,
              },
            },
          },
          applicationUrl:
            '/templates/everest.fin.expense/payment/uinext/paymentsHistory',
          lastSyncDeltaVariable,
        },
        {
          displayName: 'Vendor Credit',
          displayNamePlural: 'Vendor Credits',
          everestModel: vendorCreditStagingParams.everestModel,
          providerModel: vendorCreditStagingParams.providerModel,
          originalIdKey: vendorCreditStagingParams.originalIdKey,
          mappings: {
            api: {
              name: defaultVendorCreditMapping,
              type: {
                technical: true,
                semantic: false,
              },
            },
          },
          applicationUrl:
            '/templates/everest.fin.expense/uinext/vendorMgmt/vendorCredits',
          lastSyncDeltaVariable,
        },
        {
          displayName: 'Invoice',
          displayNamePlural: 'Invoices',
          everestModel: invoiceStagingParams.everestModel,
          providerModel: invoiceStagingParams.providerModel,
          originalIdKey: invoiceStagingParams.originalIdKey,
          mappings: {
            api: {
              name: defaultInvoiceMapping,
              type: {
                technical: true,
                semantic: false,
              },
            },
          },
          enableExpertDataMode: {
            technical: {
              modelUrn:
                'urn:evst:everest:fin/integration/quickbooks:model/node:QuickBooks',
              entryPointFunctionName: 'loadDataFromExpertDataMode',
              linking: true,
            },
          },
          applicationUrl:
            '/templates/everest.fin.accounting/qtc/invoicing/uinext/invoicing',
          lastSyncDeltaVariable,
        },
        // This one we hide, necessary to show as an option on linking tab
        {
          displayName: 'InvoicePlan',
          displayNamePlural: 'InvoicePlans',
          everestModel: InvoicePlan.MODEL_URN,
          providerModel: ProviderModel.InvoicePlan,
          originalIdKey: 'id',
          mappings: {
            api: {
              name: '',
              type: {
                technical: false,
              },
            },
          },
          defaultValues: {
            semanticDefaultFieldsModal: {
              nodesData: [
                {
                  nodeUrn: SalesOrder.MODEL_URN,
                  fields: [{ field: 'businessPartnerId', label: 'Customer' }],
                },
              ],
            },
          },
          applicationUrl: '',
          // there is no delta variable for invoice plan as it is only used for linking
        },
        {
          displayName: 'Credit Memo',
          displayNamePlural: 'Credit Memos',
          everestModel: creditMemoStagingParams.everestModel,
          providerModel: creditMemoStagingParams.providerModel,
          originalIdKey: creditMemoStagingParams.originalIdKey,
          mappings: {
            api: {
              name: defaultCreditMemoMapping,
              type: {
                technical: true,
                semantic: false,
              },
            },
          },
          applicationUrl:
            '/templates/everest.fin.accounting/qtc/creditMemo/uinext/creditMemos',
          lastSyncDeltaVariable,
        },
        {
          displayName: 'Customer Payment',
          displayNamePlural: 'Customer Payments',
          everestModel: customerPaymentStagingParams.everestModel,
          providerModel: customerPaymentStagingParams.providerModel,
          originalIdKey: customerPaymentStagingParams.originalIdKey,
          mappings: {
            api: {
              name: defaultCustomerPaymentMapping,
              type: {
                technical: true,
                semantic: false,
              },
            },
          },
          applicationUrl:
            '/templates/everest.fin.accounting/qtc/payment/uinext/payments',
          lastSyncDeltaVariable,
        },
        {
          displayName: 'Fund Transfer',
          displayNamePlural: 'Fund Transfers',
          everestModel: fundTransferStagingParams.everestModel,
          providerModel: fundTransferStagingParams.providerModel,
          originalIdKey: fundTransferStagingParams.originalIdKey,
          mappings: {
            api: {
              name: fundTransferMapping,
              type: {
                technical: true,
                semantic: false,
              },
            },
          },
          applicationUrl:
            '/templates/everest.fin.integration.bank/uinext/fundTransfer/fundTransfers',
          lastSyncDeltaVariable,
        },
        {
          displayName: 'Fund Transfer from Purchase',
          displayNamePlural: 'Fund Transfers from Purchases',
          everestModel: fundTransferFromPurchaseStagingParams.everestModel,
          providerModel: fundTransferFromPurchaseStagingParams.providerModel,
          originalIdKey: fundTransferFromPurchaseStagingParams.originalIdKey,
          mappings: {
            api: {
              name: fundTransferFromPurchaseMapping,
              type: {
                technical: true,
                semantic: false,
              },
            },
          },
          applicationUrl:
            '/templates/everest.fin.integration.bank/uinext/fundTransfer/fundTransfers',
          lastSyncDeltaVariable,
        },
        {
          displayName: 'Bank Entry from Deposit',
          displayNamePlural: 'Bank Entries from Deposits',
          everestModel: bankEntryStagingFromDepositParams.everestModel,
          providerModel: bankEntryStagingFromDepositParams.providerModel,
          originalIdKey: bankEntryStagingFromDepositParams.originalIdKey,
          mappings: {
            api: {
              name: defaultBankEntryFromDepositMapping,
              type: {
                technical: true,
                semantic: false,
              },
            },
          },
          applicationUrl:
            '/templates/everest.fin.integration.bank/uinext/bankEntry/bankEntries?feat-delta=true',
          lastSyncDeltaVariable,
        },
        {
          displayName: 'Bank Entry from Check',
          displayNamePlural: 'Bank Entries from Checks',
          everestModel: bankEntryStagingFromCheckParams.everestModel,
          providerModel: bankEntryStagingFromCheckParams.providerModel,
          originalIdKey: bankEntryStagingFromCheckParams.originalIdKey,
          mappings: {
            api: {
              name: defaultBankEntryFromCheckMapping,
              type: {
                technical: true,
                semantic: false,
              },
            },
          },
          applicationUrl:
            '/templates/everest.fin.integration.bank/uinext/bankEntry/bankEntries?feat-delta=true',
          lastSyncDeltaVariable,
        },
        {
          displayName: 'Bank Entry from Expense',
          displayNamePlural: 'Bank Entries from Expenses',
          everestModel: bankEntryStagingFromExpenseParams.everestModel,
          providerModel: bankEntryStagingFromExpenseParams.providerModel,
          originalIdKey: bankEntryStagingFromExpenseParams.originalIdKey,
          mappings: {
            api: {
              name: defaultBankEntryFromExpenseMapping,
              type: {
                technical: true,
                semantic: false,
              },
            },
          },
          applicationUrl:
            '/templates/everest.fin.integration.bank/uinext/bankEntry/bankEntries?feat-delta=true',
          lastSyncDeltaVariable,
        },
        {
          displayName: 'Bank Entry from Fund Transfer',
          displayNamePlural: 'Bank Entries from Fund Transfers',
          everestModel: bankEntryStagingFromFundTransferParams.everestModel,
          providerModel: bankEntryStagingFromFundTransferParams.providerModel,
          originalIdKey: bankEntryStagingFromFundTransferParams.originalIdKey,
          mappings: {
            api: {
              name: defaultBankEntryFromFundTransferMapping,
              type: {
                technical: true,
                semantic: false,
              },
            },
          },
          applicationUrl:
            '/templates/everest.fin.integration.bank/uinext/bankEntry/bankEntries?feat-delta=true',
          lastSyncDeltaVariable,
        },
        {
          displayName: 'Credit Card Entry from Credit Card Payment',
          displayNamePlural: 'Credit Card Entries from Credit Card Payments',
          everestModel:
            creditCardEntryFromCreditCardPaymentStagingParams.everestModel,
          providerModel:
            creditCardEntryFromCreditCardPaymentStagingParams.providerModel,
          originalIdKey:
            creditCardEntryFromCreditCardPaymentStagingParams.originalIdKey,
          mappings: {
            api: {
              name: defaultcreditCardEntryFromCreditCardPaymentMapping,
              type: {
                technical: true,
                semantic: false,
              },
            },
          },
          applicationUrl:
            '/templates/everest.fin.integration.bank/uinext/creditCardEntry/creditCardEntries',
          lastSyncDeltaVariable,
        },
        {
          displayName: 'Credit Card Entry from Purchase',
          displayNamePlural: 'Credit Card Entries from Purchases',
          everestModel: creditCardEntryFromPurchaseStagingParams.everestModel,
          providerModel: creditCardEntryFromPurchaseStagingParams.providerModel,
          originalIdKey: creditCardEntryFromPurchaseStagingParams.originalIdKey,
          mappings: {
            api: {
              name: defaultcreditCardEntryFromPurchaseMapping,
              type: {
                technical: true,
                semantic: false,
              },
            },
          },
          applicationUrl:
            '/templates/everest.fin.integration.bank/uinext/creditCardEntry/creditCardEntries',
          lastSyncDeltaVariable,
        },
        {
          displayName: 'Revenue Plan',
          displayNamePlural: 'Revenue Plans',
          everestModel: revenuePlanStagingParams.everestModel,
          providerModel: revenuePlanStagingParams.providerModel,
          originalIdKey: revenuePlanStagingParams.originalIdKey,
          mappings: {
            file: {
              name: defaultRevenuePlanMapping,
              type: {
                technical: true,
                semantic: false,
              },
            },
          },
          applicationUrl: '/reports/revenueWaterfall',
          // there is no delta variable for revenue plan as it is a CSV import
        },
        {
          displayName: 'Amortization Schedule',
          displayNamePlural: 'Amortization Schedules',
          everestModel: amortizationScheduleStagingParams.everestModel,
          providerModel: amortizationScheduleStagingParams.providerModel,
          originalIdKey: amortizationScheduleStagingParams.originalIdKey,
          mappings: {
            file: {
              name: defaultAmortizationScheduleMapping,
              type: {
                technical: true,
              },
            },
          },
          defaultValues: {
            technicalDefaultFieldsModal: {
              nodesData: [
                {
                  nodeUrn: JournalEntryHeader.MODEL_URN,
                  fields: [{ field: 'postingDate', label: 'Go Live Date' }],
                },
                {
                  nodeUrn: JournalEntryLine.MODEL_URN,
                  fields: [
                    { field: 'transactionalCurrency' },
                    { field: 'accountId', label: 'Prepaid Account' },
                  ],
                },
                {
                  nodeUrn: AmortizationScheduleHeader.MODEL_URN,
                  fields: [{ field: 'amortizationStartDate' }],
                },
              ],
            },
          },
          applicationUrl: '@uicode:amortizationSchedules',
          // there is no delta variable for amortization schedule as it is a CSV import
        },
      ],
    },
  };
  return quickBooksConfig;
}
