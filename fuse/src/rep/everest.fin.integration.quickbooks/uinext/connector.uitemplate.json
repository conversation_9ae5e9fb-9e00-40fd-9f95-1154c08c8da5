{"version": 2, "uicontroller": "connector.uicontroller.ts", "uimodel": {"nodes": {"integrationConnectivity": {"type": "list", "modelId": "everest.connectorengine/IntegrationConnectivityModel.IntegrationConnectivity", "fieldList": ["integrationName"], "query": {"where": {"package": "everest.fin.integration.quickbooks"}}}, "selectedIntegrationConnectivity": {"type": "struct", "modelId": "everest.connectorengine/IntegrationConnectivityModel.IntegrationConnectivity", "fieldList": ["id", "acceptHeader", "authTokenParamsInBody", "authorizationUrl", "baseUrl", "httpRequestMethodCallback", "noClientIdAndClientSecretParamsInBody", "integrationName", "internalRedirectUrl", "refreshTokenParamsInBody", "responseTypes", "secret<PERSON>ey", "tokenEndpoint"], "query": "@controller:buildIntegrationConnectivityQueryArguments()"}, "secret": {"type": "struct", "modelId": "everest.appserver/SecretModel.Secret", "read": "@controller:buildSecretReadArguments()"}, "selectedConnector": {"type": "struct", "modelId": "everest.connectorengine/ConnectorEngineModel.ConnectorEngine", "fieldList": ["id", "config<PERSON><PERSON><PERSON>"], "query": {"where": {"package": "everest.fin.integration.quickbooks"}}}, "quickbooks": {"type": "struct", "modelId": "everest.fin.integration.quickbooks/QuickBooksModel.QuickBooks"}}}, "uiview": {"i18n": ["everest.connectorengine/connectorEngine", "quickbooks"], "title": "{{connectorEngine.create}}", "sections": [{"component": "<PERSON><PERSON>", "section": {"grid": {"size": "12"}}, "visible": "@controller:isSandbox()", "props": {"variant": "warning", "title": "Warning", "content": "{{quickbooks.sandbox-authenticate}}"}}, {"component": "Block", "size": "12", "type": "secondary", "customId": "connectionDetailsFieldGroup", "columns": 4, "elements": [{"component": "Select", "label": "{{quickbooks.select-connection}}", "idProp": "integrationName", "textProp": "integrationName", "value": "@controller:getSelectedConnection()", "isEditing": true, "list": "@binding:integrationConnectivity", "onChange": "@controller:selectConnection", "name": "selectedConnectionName", "size": 4, "bottomActions": [{"label": "{{quickbooks.create-connection}}", "onClick": "@controller:createNewConnection"}]}, {"label": "Client ID", "name": "client_id", "value": "@controller:geClientId()", "isEditing": true, "size": 2}, {"label": "Client Secret", "name": "client_secret", "type": "password", "value": "@controller:getClientSecret()", "isEditing": true, "size": 2}, {"label": "Authorization URL", "value": "@binding:selectedIntegrationConnectivity.authorizationUrl", "isEditing": true, "visible": "@controller:isExpertView()", "size": 1}, {"label": "Base URL", "value": "@binding:selectedIntegrationConnectivity.baseUrl", "isEditing": true, "visible": "@controller:isExpertView()", "size": 1}, {"label": "HTTP Request Callback", "value": "@binding:selectedIntegrationConnectivity.httpRequestMethodCallback", "isEditing": true, "visible": "@controller:isExpertView()", "size": 1}, {"label": "Response Types", "value": "@binding:selectedIntegrationConnectivity.responseTypes", "isEditing": true, "visible": "@controller:isExpertView()", "size": 1}, {"label": "Internal Redirect URL", "value": "@binding:selectedIntegrationConnectivity.internalRedirectUrl", "isEditing": true, "visible": "@controller:isExpertView()", "size": 2}, {"label": "Accept Header", "value": "@binding:selectedIntegrationConnectivity.acceptHeader", "isEditing": true, "visible": "@controller:isExpertView()", "size": 1}, {"label": "No Client in Body", "value": "@binding:selectedIntegrationConnectivity.noClientIdAndClientSecretParamsInBody", "isEditing": true, "visible": "@controller:isExpertView()", "size": 1}, {"label": "Token Endpoint", "value": "@binding:selectedIntegrationConnectivity.tokenEndpoint", "isEditing": true, "visible": "@controller:isExpertView()", "size": 2}, {"label": "Refresk Token in Body", "value": "@binding:selectedIntegrationConnectivity.refreshTokenParamsInBody", "isEditing": true, "visible": "@controller:isExpertView()", "size": 1}, {"label": "<PERSON><PERSON> Token in Body", "value": "@binding:selectedIntegrationConnectivity.authTokenParamsInBody", "isEditing": true, "visible": "@controller:isExpertView()", "size": 1}, {"label": "@controller:getTestLabel()", "component": "ProgressBar", "progress": "@controller:getTestProgressValue()", "variant": "@controller:getTestVariant()", "started": "@controller:getTestStarted()", "size": 4}, {"component": "Editor", "label": "{{quickbooks.test}}", "value": "@state:response", "action": "create", "isEditing": false, "editorConfig": {"language": "json", "height": 150}, "isVisible": "@controller:isExpertView()", "size": 4}, {"component": "Editor", "label": "Count Response", "value": "@state:objectCounts", "isEditing": false, "editorConfig": {"language": "json", "height": 150}, "isVisible": "@controller:hasCountObjects()", "size": 4}]}, {"component": "ButtonGroup", "direction": "horizontal", "actions": [{"variant": "secondary", "label": "{{connectorEngine.donwloadCount}}", "onClick": "@controller:downloadCsv", "visible": "@controller:isDownloadCountVisible()"}, {"variant": "secondary", "label": "{{connectorEngine.countObjects}}", "onClick": "@controller:countObjects", "visible": "@controller:isCountObjectVisible()"}, {"variant": "secondary", "label": "@controller:expert<PERSON>iewButtonName()", "onClick": "@controller:switchExpertView"}, {"variant": "secondary", "label": "{{connectorEngine.test}}", "onClick": "@controller:test"}, {"variant": "primary", "label": "{{connectorEngine.save}}", "onClick": "@controller:save"}, {"variant": "primary", "label": "{{connectorEngine.authenticate}}", "onClick": "@controller:authorize", "disabled": "@controller:isSandbox()"}]}]}}