package everest.fin.integration.base

@metadata {
	active: true
}
public enum ExtractionProviders {
	@metadata {
		description: 'Salesforce'
		englishText: 'Salesforce'
	}
	Salesforce
	@metadata {
		description: 'Avalara'
		englishText: 'Avalara'
	}
	Avalara
	@metadata {
		description: 'Quickbooks'
		englishText: 'Quickbooks'
	}
	Quickbooks
	@metadata {
		description: 'BillDotCom'
		englishText: 'BillDotCom'
	}
	BillDotCom
	@metadata {
		description: 'Stripe'
		englishText: 'Stripe'
	}
	Stripe
	@metadata {
		description: 'Everest'
		englishText: 'Everest'
	}
	Everest
	@metadata {
		description: 'Airbase integration'
		englishText: 'Airbase'
	}
	Airbase
	@metadata {
		description: 'Intacct integration'
		englishText: 'Intacct'
	}
	Intacct
	@metadata {
		description: 'Expert Data Mode'
		englishText: 'Expert Data Mode'
	}
	ExpertDataMode
	@metadata {
		description: 'Data is coming from a migration file upload'
		englishText: 'Migration File'
	}
	MigrationFile
	@metadata {
		description: 'Xero'
		englishText: 'Xero'
	}
	Xero
	@metadata {
		description: 'test scenarios'
		englishText: 'Test'
	}
	Test
	@metadata {
		description: 'Gusto'
		englishText: 'Gusto'
	}
	Gusto
	@metadata {
		description: 'Ramp Integration'
		englishText: 'Ramp'
	}
	Ramp
	@metadata {
		description: 'Rippling'
		englishText: 'Rippling'
	}
	Rippling
	@metadata {
		description: 'NetSuite Migration'
		englishText: 'NetSuite'
	}
	NetSuite
	@metadata {
		description: 'Navan Integration'
		englishText: 'Navan'
	}
	Navan
	@metadata {
		description: 'ADP Integration'
		englishText: 'ADP'
	}
	ADP
	@metadata {
		description: 'Cloudflare Saas Cost'
		englishText: 'Cloudflare'
	}
	Cloudflare
	@metadata {
		description: 'Anrok'
		englishText: 'Anrok'
	}
	Anrok
	@metadata {
		description: 'Navan CSV'
		englishText: 'Navan (CSV)'
	}
	NavanCSV
	@metadata {
		description: 'OpenAI CCM integration'
		englishText: 'OpenAI'
	}
	OpenAI
	@metadata {
		description: 'Github'
		englishText: 'Github'
	}
	Github
	@metadata {
		description: 'Azure'
		englishText: 'Azure'
	}
	Azure
	@metadata {
		description: 'Slack CC Provider'
		englishText: 'Slack'
	}
	Slack
	@metadata {
		description: 'MS365'
		englishText: 'MS365'
	}
	MS365
	@metadata {
		description: 'Anthropic'
		englishText: 'Anthropic'
	}
	Anthropic
	@metadata {
		description: 'Opencost'
		englishText: 'Opencost'
	}
	Opencost
	@metadata {
		description: 'Google Workspace'
		englishText: 'Google Workspace'
	}
	GWorkspace
	@metadata {
		description: 'AWS'
		englishText: 'AWS'
	}
	AWS
	@metadata {
		englishText: 'EverestAI'
	}
	EverestAI
	@metadata {
		englishText: 'People Cost'
	}
	PeopleCost
	@metadata {
		description: 'Payroll CSV Import'
		englishText: 'Payroll CSV Import'
	}
	PayrollCSVImport
	@metadata {
		description: 'Subscriptions'
		englishText: 'Subscriptions'
	}
	Subscriptions
	@metadata {
		description: 'Brex'
		englishText: 'Brex'
	}
	Brex
	@metadata {
		description: 'HubSpot'
		englishText: 'HubSpot'
	}
	HubSpot
	@metadata {
		description: 'Ruddr'
		englishText: 'Ruddr'
	}
	Ruddr
	@metadata {
		description: 'Divvy'
		englishText: 'Divvy'
	}
	Divvy
	@metadata {
		description: 'Lexware'
		englishText: 'Lexware'
	}
	Lexware
	@metadata {
		description: 'Expensify'
		englishText: 'Expensify'
	}
	Expensify
}
