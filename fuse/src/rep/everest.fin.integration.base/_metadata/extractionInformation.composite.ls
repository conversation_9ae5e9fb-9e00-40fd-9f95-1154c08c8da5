package everest.fin.integration.base
@metadata {
	description: 'Result type of the extraction information'
	active: true
}
public composite type extractionInformation {
	@metadata {
		uuid: 'c123a856-d671-4f98-84d1-73e3ca38776e'
	}
	extractionSource enum<ExtractionSource>
	@metadata {
		uuid: '414404df-ccb6-4268-b0b0-c45c92227e21'
	}
	extractionName Text
	@metadata {
		uuid: 'b08507a6-3681-45d6-96d8-96f1cdc8c77f'
	}
	packageName PackageName
	@metadata {
		uuid: '514b9eb1-ceee-465d-8741-71726931b0d9'
	}
	actionNode ModelUrn
	@metadata {
		uuid: '514b9eb1-ceee-465d-8741-71726931b0d9'
	}
	actionUUID UUID
	@metadata {
		uuid: '45636962-dbb7-4b64-a9fc-fac8c9f4d448'
	}
	optional existingExtractionActionId Id
}
