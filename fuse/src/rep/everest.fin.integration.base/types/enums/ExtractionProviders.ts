/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// ******************************************************************************
// Generated Everest Types
// ******************************************************************************


/**
 * Enum type.
 *
 * @everest-node-metadata='{"visibility":"public"}'
 */
export enum EvstExtractionProviders {
  /** Salesforce */
  Salesforce = "Salesforce",
  /** Avalara */
  Avalara = "Avalara",
  /** Quickbooks */
  Quickbooks = "Quickbooks",
  /** BillDotCom */
  BillDotCom = "BillDotCom",
  /** Stripe */
  Stripe = "Stripe",
  /** Everest */
  Everest = "Everest",
  /** Airbase integration */
  Airbase = "Airbase",
  /** Intacct integration */
  Intacct = "Intacct",
  /** Expert Data Mode */
  ExpertDataMode = "ExpertDataMode",
  /** Data is coming from a migration file upload */
  MigrationFile = "MigrationFile",
  /** Xero */
  Xero = "Xero",
  /** test scenarios */
  Test = "Test",
  /** Gusto */
  Gusto = "Gusto",
  /** Ramp Integration */
  Ramp = "Ramp",
  /** Rippling */
  Rippling = "Rippling",
  /** NetSuite Migration */
  NetSuite = "NetSuite",
  /** Navan Integration */
  Navan = "Navan",
  /** ADP Integration */
  ADP = "ADP",
  /** Cloudflare Saas Cost */
  Cloudflare = "Cloudflare",
  /** Anrok */
  Anrok = "Anrok",
  /** Navan CSV */
  NavanCSV = "NavanCSV",
  /** OpenAI CCM integration */
  OpenAI = "OpenAI",
  /** Github */
  Github = "Github",
  /** Azure */
  Azure = "Azure",
  /** Slack CC Provider */
  Slack = "Slack",
  /** MS365 */
  MS365 = "MS365",
  /** Anthropic */
  Anthropic = "Anthropic",
  /** Opencost */
  Opencost = "Opencost",
  /** Google Workspace */
  GWorkspace = "GWorkspace",
  /** AWS */
  AWS = "AWS",
  EverestAI = "EverestAI",
  PeopleCost = "PeopleCost",
  /** Payroll CSV Import */
  PayrollCSVImport = "PayrollCSVImport",
  /** Subscriptions */
  Subscriptions = "Subscriptions",
  /** Brex */
  Brex = "Brex",
  /** HubSpot */
  HubSpot = "HubSpot",
  /** Ruddr */
  Ruddr = "Ruddr",
  /** Divvy */
  Divvy = "Divvy",
  /** Lexware */
  Lexware = "Lexware",
  /** Expensify */
  Expensify = "Expensify",
}
