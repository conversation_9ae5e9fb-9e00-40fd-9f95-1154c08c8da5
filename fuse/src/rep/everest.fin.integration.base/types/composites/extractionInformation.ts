import type { EvstExtractionSource as everest_fin_integration_base_enum_ExtractionSource } from "@pkg/everest.fin.integration.base/types/enums/ExtractionSource";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstPackageName as everest_appserver_primitive_metadata_PackageName } from "@pkg/everest.appserver/types/primitives/metadata/PackageName";
import type { EvstModelUrn as everest_appserver_primitive_metadata_ModelUrn } from "@pkg/everest.appserver/types/primitives/metadata/ModelUrn";
import type { EvstUUID as everest_appserver_primitive_UUID } from "@pkg/everest.appserver/types/primitives/UUID";
import type { EvstID as everest_appserver_primitive_ID } from "@pkg/everest.appserver/types/primitives/ID";

/**
 * Result type of the extraction information
 *
 * @everest-node-metadata='{"visibility":"public"}'
 */
export interface EvstextractionInformation {
  extractionSource: everest_fin_integration_base_enum_ExtractionSource;
  extractionName: everest_appserver_primitive_Text;
  packageName: everest_appserver_primitive_metadata_PackageName;
  actionNode: everest_appserver_primitive_metadata_ModelUrn;
  actionUUID: everest_appserver_primitive_UUID;
  existingExtractionActionId?: everest_appserver_primitive_ID | null;
}
