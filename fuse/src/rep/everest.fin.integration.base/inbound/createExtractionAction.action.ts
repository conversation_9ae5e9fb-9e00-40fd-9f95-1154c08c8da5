import type { ISession } from '@everestsystems/content-core';
import { EverestArtifact } from '@pkg/everest.appserver/types/metadata/EverestArtifact';
import { EverestInterfaceLookupTable } from '@pkg/everest.appserver/types/metadata/EverestInterfaceLookupTable';
import {
  createExtractionActionContent,
  createExtractionActionDefinition,
} from '@pkg/everest.fin.integration.base/inbound/utils/extractionActionCreator';
import { ExtractionAction } from '@pkg/everest.fin.integration.base/types/ExtractionAction';
import { camelCase } from 'lodash';

import type { EvstextractionInformation } from '../types/composites/extractionInformation';

export const Extract_Action_Name = 'extract';
export const Extract_Interface_Name =
  'everest.fin.integration.base/inbound/extraction';

export default async function createExtractionAction(
  env: ISession,
  extractionInformation: EvstextractionInformation
): Promise<void> {
  const {
    extractionName,
    extractionSource,
    actionNode,
    packageName,
    existingExtractionActionId,
    actionUUID
  } = extractionInformation;

  let camelCaseName = camelCase(extractionName);
  let actionId = existingExtractionActionId;

  if (actionId) {
    const { name: actionName } = await ExtractionAction.read(
      env,
      { id: actionId },
      ['name']
    );
    camelCaseName = camelCase(actionName);
  } else {
    const { id } = await ExtractionAction.create(
      env,
      {
        name: extractionName,
        extractionSource,
        package: packageName,
        uuid: actionUUID
      },
      ['id']
    );
    actionId = id;
  }

  if (!existingExtractionActionId) {
    await EverestArtifact.create(env, {
      fileName: `${packageName}/actions/extractActions/${camelCaseName}.action.ts`,
      content: createExtractionActionContent(camelCaseName),
    });

    await EverestArtifact.create(env, {
      fileName: `${packageName}/actions/extractActions/${camelCaseName}.action-definition.json`,
      content: JSON.stringify(
        createExtractionActionDefinition(actionNode),
        null,
        2
      ),
    });
    const lookupClient = await EverestInterfaceLookupTable.client(env);
    await lookupClient.create({
      actionName: camelCaseName,
      interfaceActionName: Extract_Action_Name,
      interfaceName: Extract_Interface_Name,
      modelURN: actionNode,
      package: packageName,
      providerName: camelCaseName,
    });
  }
}
