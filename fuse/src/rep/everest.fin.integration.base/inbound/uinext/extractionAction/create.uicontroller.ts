import type { EvstExtractionSource } from '@pkg/everest.fin.integration.base/types/enums/ExtractionSource';
import { ExtractionActionUI } from '@pkg/everest.fin.integration.base/types/ExtractionAction.ui';
import type { CreateUiTemplate } from '@pkg/everest.fin.integration.base/types/uiTemplates/inbound/uinext/extractionAction/create.ui';

type CreateContext = CreateUiTemplate.CreateContext;

type Context = CreateContext & {
  state: {
    form: {
      extractionSource: string;
      extractionName: string;
      actionNode: string;
      packageName: string;
      extractionActionId: number | undefined;
    };
    selectedPacakge: string;
    parentIdentifierPathEnabled: boolean;
    isModal: boolean;
  };
};

export async function getOnApply(context: Context) {
  const { state, helpers, actions } = context;
  const {
    extractionSource,
    extractionName,
    actionNode,
    packageName,
    extractionActionId,
  } = state.form;

  /* const result = await actions.run({
    extraction: {
      action: 'createExtractionAction',
      data: {
        input: {
          extractionProvider,
          providerModel,
          parentIdentifierPath,
          extractionSource,
          isHierarchy,
          everestModel,
          extractionName,
          dataUniqueField,
          actionNode,
          packageName,
        },
      },
    },
  }); */

  const result = await ExtractionActionUI.createExtractionAction(context, {
    input: {
      extractionSource: extractionSource as EvstExtractionSource,
      extractionName,
      actionNode,
      packageName,
      existingExtractionActionId: extractionActionId,
      actionUUID: context.helpers.uuid(),
    },
  }).run('extraction');
  console.log('RESULT', result);

  if (result.error) {
    helpers.showNotificationMessage({
      type: 'error',
      message: result.error.message,
      duration: 2,
    });
  } else {
    helpers.showNotificationMessage({
      type: 'success',
      message: 'Extraction function created successfully',
      duration: 2,
    });

    if (state.isModal) {
      helpers.currentModalSubmitCallback({ name: extractionName });
    } else {
      await actions.refetchUiModelData();
      helpers.navigate({
        to: `/templates/everest.fin.integration.base/inbound/uinext/extractionAction/list`,
        closeCurrentTab: true,
      });
    }
  }
}

export async function setSelectedPackage(context: Context, value) {
  const { state } = context;
  state.selectedPacakge = value;
}

export async function getEverestActionNodes(context: Context) {
  const { state, data } = context;
  return data.everestNode.filter(
    (node) => node.package === state.selectedPacakge
  );
}

export async function onIsHierarchyChange(context: Context, value: string) {
  const { state } = context;
  state.parentIdentifierPathEnabled = !value;
}

export async function isExistingActionSelected({ state }: Context) {
  return state.form.extractionActionId !== undefined;
}
