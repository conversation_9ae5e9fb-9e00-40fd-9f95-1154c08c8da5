import type { ISession } from '@everestsystems/content-core';
import type {
  CurrencyRevalDataType,
  CurrencyRevalDataWithOptionsType,
  CurrencyRevalOptions,
} from '@pkg/everest.fin.accounting/public/currencyRevaluation/types';
import { CurrencyRevaluation } from '@pkg/everest.fin.accounting/types/CurrencyRevaluation';
import type { CurrencyRevaluationTypes } from '@pkg/everest.fin.processor.common/public/types/currencyRevaluation';
import type { EvstEntityBook } from '@pkg/everest.fin.processor/types/composites/EntityBook';

export default async function calculateCashAccountsRevaluation(
  session: ISession,
  entityBook: EvstEntityBook,
  _previousStageResult: CurrencyRevaluationTypes.PreviousStageResult,
  processParams: CurrencyRevaluationTypes.ProcessParameters
): Promise<CurrencyRevalDataWithOptionsType> {
  const currencyRevalClient = await CurrencyRevaluation.client(session);
  const revalData = (await currencyRevalClient.calculateCashAccountsRevaluation(
    entityBook.entityId,
    processParams.revaluationDate,
    entityBook.bookId
  )) as CurrencyRevalDataType[];

  const options = {
    recognizeUnrealizedGain: false,
    recognizeUnrealizedLoss: true,
  } as CurrencyRevalOptions;

  return {
    revalData,
    options,
  };
}
