/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// ******************************************************************************
// Generated Everest Types
// ******************************************************************************

import type { Decimal, RoundingMode } from '@everestsystems/decimal';
import type {
  ArgType,
  Association,
  AssociationManagementArgs,
  AsyncResult,
  Controller,
  ControllerClientProvider,
  CreateOptionsArg,
  DataWithMetadata,
  DeleteOptionsArg,
  FieldSelector,
  Filter,
  flattenFieldSelector,
  GraphInstanceEntry,
  KernelRequestOptions,
  NonAssociationFields,
  Optional,
  PurgeArgs,
  ReadOptionsArg,
  ReadReturnType as ReadReturnTypeGeneric,
  ReadReturnTypeWithAssociations,
  RequireAtLeastOne,
  SemanticSearchArg,
  SemanticSearchRequestOptions,
  TypeSafeQueryArgType,
  UpdateManyOptionsArg,
  UpdateOptionsArg,
  UpsertOptionsArg,
  WriteReturnType,
  WriteReturnTypeWithAssociations,
} from '@everestsystems/content-core';
import type { EvstUnknownObject as everest_appserver_primitive_UnknownObject } from "@pkg/everest.appserver/types/primitives/UnknownObject";
import type { EvstEntityBook as everest_fin_processor_composite_EntityBook } from "@pkg/everest.fin.processor/types/composites/EntityBook";
import type { EvstUUID as everest_appserver_primitive_UUID } from "@pkg/everest.appserver/types/primitives/UUID";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { EvstUserEmail as everest_appserver_usermgmt_primitive_user_UserEmail } from "@pkg/everest.appserver.usermgmt/types/primitives/user/UserEmail";
import type { EvstDateTime as everest_appserver_primitive_DateTime } from "@pkg/everest.appserver/types/primitives/DateTime";

/**
 * Types for FinProcHGBRoutine
 *
 * @everest-node-metadata='{"visibility":"public"}'
 */
export namespace FinProcHGBRoutine {
  export type UniqueFields = Pick<FinProcHGBRoutine, 'id' | 'uuid'>;
  export type UniqueWhereInput = (RequireAtLeastOne<UniqueFields>) & Partial<FinProcHGBRoutine>;
  export type ReadReturnType<U extends string | number | symbol = keyof FinProcHGBRoutine> = ReadReturnTypeGeneric<FinProcHGBRoutine, U>;

  export interface IControllerClient extends Omit<Controller<FinProcHGBRoutine>, 'all' | 'create' | 'createMany' | 'delete' | 'deleteAll' | 'deleteMany' | 'exportMany' | 'getHistory' | 'importMany' | 'lock' | 'lookup' | 'purge' | 'query' | 'queryWithMetadata' | 'read' | 'semanticSearch' | 'unlock' | 'update' | 'updateMany' | 'upsert' | 'upsertMany'> {
    /** [HGB] Calculates non cash accounts for revaluation */
    calculateNonCashAccountsRevaluation(entityBook: everest_fin_processor_composite_EntityBook, previousStageResult: everest_appserver_primitive_UnknownObject, processParams: everest_appserver_primitive_UnknownObject): Promise<everest_appserver_primitive_UnknownObject>;
    /** [HGB] Calculates cash accounts for revaluation */
    calculateCashAccountsRevaluation(entityBook: everest_fin_processor_composite_EntityBook, previousStageResult: everest_appserver_primitive_UnknownObject, processParams: everest_appserver_primitive_UnknownObject): Promise<everest_appserver_primitive_UnknownObject>;
  }

  /**
   * Financial Processor HGB Routine
   */
  export type FinProcHGBRoutine = {
    /** Database generated identifier of this record. */
    id: number;
    /** Database generated version of this record. */
    version?: number;
    uuid?: everest_appserver_primitive_UUID | null;
    /**
     * ID of the system from which this record was sourced
     */
    externalId?: everest_appserver_primitive_Text | null;
    /**
     * Indicates whether this record is usable for business users
     */
    active?: everest_appserver_primitive_TrueFalse | null;
    /**
     * User that created this record
     */
    createdBy: everest_appserver_usermgmt_primitive_user_UserEmail;
    /**
     * Date/time when the record was created
     */
    createdDate: everest_appserver_primitive_DateTime;
    /**
     * User that last modified the record
     */
    lastModifiedBy: everest_appserver_usermgmt_primitive_user_UserEmail;
    /**
     * Date/time of the last modification to the record
     */
    lastModifiedDate: everest_appserver_primitive_DateTime;
    };
  /**
   * Financial Processor HGB Routine
   */
  export type FinProcHGBRoutineWithAssociation = FinProcHGBRoutine & {

    };
  /** @deprecated - use {@link MODEL_URN} instead */
  export const name = 'urn:evst:everest:fin/processor/hgb:model/node:FinProcHGBRoutine';
  export const MODEL_URN = 'urn:evst:everest:fin/processor/hgb:model/node:FinProcHGBRoutine';
  /** @deprecated - use {@link MODEL_URN} instead */
  export const MODEL_ID = 'everest.fin.processor.hgb/FinProcHGBRoutineModel.FinProcHGBRoutine';
  export const MODEL_UUID = '09e36c25-77aa-4c82-96dc-5e18c05e343d';

  /** @return a model controller instance for FinProcHGBRoutine. */
  export function client(env: ControllerClientProvider) {
    return env.controllerClient<FinProcHGBRoutine.IControllerClient>(MODEL_URN);
  }
}
