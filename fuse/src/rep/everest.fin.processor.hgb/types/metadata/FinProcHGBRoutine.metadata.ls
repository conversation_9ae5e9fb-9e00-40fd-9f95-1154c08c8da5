package everest.fin.processor.hgb

node FinProcHGBRoutine {
	description: 'Financial Processor HGB Routine'
	documentation: 'Financial Processor HGB Routine is an API entry point for routine algorithm actions'
	label: 'Financial Processor HGB Routine'
	query action calculateNonCashAccountsRevaluation(entityBook: composite<everest.fin.processor::EntityBook>, previousStageResult: UnknownObject, processParams: UnknownObject): UnknownObject
	query action calculateCashAccountsRevaluation(entityBook: composite<everest.fin.processor::EntityBook>, previousStageResult: UnknownObject, processParams: UnknownObject): UnknownObject
}
