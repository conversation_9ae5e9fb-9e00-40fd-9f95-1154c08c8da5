import { getTranslations, type ISession } from '@everestsystems/content-core';
import { Entity } from '@pkg/everest.base/types/Entity';
import { Account } from '@pkg/everest.fin.accounting/types/Account';
import { CreditCardEntryHeader } from '@pkg/everest.fin.accounting/types/CreditCardEntryHeader';
import { JournalEntryHeader } from '@pkg/everest.fin.accounting/types/JournalEntryHeader';
import { ExpenseReportBase } from '@pkg/everest.fin.expense/types/ExpenseReportBase';
import { OutboundPaymentHeaderBase } from '@pkg/everest.fin.expense/types/OutboundPaymentHeaderBase';
import { Vendor } from '@pkg/everest.fin.expense/types/Vendor';
import { VendorBillHeaderBase } from '@pkg/everest.fin.expense/types/VendorBillHeaderBase';
import type { IntegrationConfig } from '@pkg/everest.fin.integration.base.ui/public/types/IntegrationConfig';
import { EvstExtractionProviders } from '@pkg/everest.fin.integration.base/types/enums/ExtractionProviders';
import { EvstStagingStatus } from '@pkg/everest.fin.integration.base/types/enums/StagingStatus';
import { getNavanSettings } from '@pkg/everest.fin.integration.navan/actions/utils/navanSettingsFacade';
import {
  NAVAN_MAPPING__EXPENSE_CSV__ACCOUNT,
  NAVAN_MAPPING__EXPENSE_CSV__CREDIT_CARD_ENTRY,
  NAVAN_MAPPING__EXPENSE_CSV__DEPARTMENT,
  NAVAN_MAPPING__EXPENSE_CSV__EMPLOYEE,
  NAVAN_MAPPING__EXPENSE_CSV__ENTITY,
  NAVAN_MAPPING__EXPENSE_CSV__EXPENSE,
  NAVAN_MAPPING__EXPENSE_CSV__PAID_STATEMENT_JOURNAL_ENTRY,
  NAVAN_MAPPING__EXPENSE_CSV__PAID_STATEMENT_OUTBOUND_PAYMENT,
  NAVAN_MAPPING__EXPENSE_CSV__REBATE__JOURNAL_ENTRY,
  NAVAN_MAPPING__EXPENSE_CSV__REPAYMENT__JOURNAL_ENTRY,
  NAVAN_MAPPING__EXPENSE_CSV__VENDOR,
  NAVAN_MAPPING__EXPENSE_CSV__VENDOR_BILL,
  NAVAN_ORIGINAL_ID_KEY_ID,
  NAVAN_ORIGINAL_ID_KEY_LEGAL_ENTITY,
  NAVAN_ORIGINAL_ID_KEY_SYNTHETIC_KEY_LEGAL_ENTITY_AND_CARDHOLDER_EMAIL,
  NAVAN_ORIGINAL_ID_KEY_SYNTHETIC_KEY_LEGAL_ENTITY_AND_GL_CODE_NUMBER,
  NAVAN_ORIGINAL_ID_KEY_SYNTHETIC_KEY_STATEMENT,
  NAVAN_ORIGINAL_ID_KEY_TRANSACTION_DEPARTMENT,
  NAVAN_PACKAGE,
  NAVAN_PROVIDER_MODEL__EXPENSE_CSV__ACCOUNT,
  NAVAN_PROVIDER_MODEL__EXPENSE_CSV__DEPARTMENT,
  NAVAN_PROVIDER_MODEL__EXPENSE_CSV__EMPLOYEE,
  NAVAN_PROVIDER_MODEL__EXPENSE_CSV__ENTITY,
  NAVAN_PROVIDER_MODEL__EXPENSE_CSV__EXPENSE_REPORT_BASE,
  NAVAN_PROVIDER_MODEL__EXPENSE_CSV__PAID_STATEMENT_JOURNAL_ENTRY,
  NAVAN_PROVIDER_MODEL__EXPENSE_CSV__PAID_STATEMENT_OUTBOUND_PAYMENT,
  NAVAN_PROVIDER_MODEL__EXPENSE_CSV__PURCHASE_OR_REFUND__CREDIT_CARD_ENTRY,
  NAVAN_PROVIDER_MODEL__EXPENSE_CSV__REBATE__JOURNAL_ENTRY,
  NAVAN_PROVIDER_MODEL__EXPENSE_CSV__REPAYMENT__JOURNAL_ENTRY,
  NAVAN_PROVIDER_MODEL__EXPENSE_CSV__VENDOR,
  NAVAN_PROVIDER_MODEL__EXPENSE_CSV__VENDOR_BILL,
} from '@pkg/everest.fin.integration.navan/utils/constants';
import { Department } from '@pkg/everest.hr.base/types/Department';
import { Employee } from '@pkg/everest.hr.base/types/Employee';

export default async function getConfigurationCSV(
  env: ISession
): Promise<IntegrationConfig> {
  const [
    navan_expenseTemplateCSV_title,
    navan_expenseTemplateCSV_masterData_entity_displayName,
    navan_expenseTemplateCSV_masterData_entity_displayNamePlural,
    navan_expenseTemplateCSV_masterData_employee_displayName,
    navan_expenseTemplateCSV_masterData_employee_displayNamePlural,
    navan_expenseTemplateCSV_masterData_department_displayName,
    navan_expenseTemplateCSV_masterData_department_displayNamePlural,
    navan_expenseTemplateCSV_masterData_vendor_displayName,
    navan_expenseTemplateCSV_masterData_vendor_displayNamePlural,
    navan_expenseTemplateCSV_masterData_account_displayName,
    navan_expenseTemplateCSV_masterData_account_displayNamePlural,
    navan_expenseTemplateCSV_businessData_manualTransaction_displayName,
    navan_expenseTemplateCSV_businessData_manualTransaction_displayNamePlural,
    navan_expenseTemplateCSV_businessData_manualTransactionVB_displayName,
    navan_expenseTemplateCSV_businessData_manualTransactionVB_displayNamePlural,
    navan_expenseTemplateCSV_businessData_purchaseOrRefund_displayName,
    navan_expenseTemplateCSV_businessData_purchaseOrRefund_displayNamePlural,
    navan_expenseTemplateCSV_businessData_rebate_displayName,
    navan_expenseTemplateCSV_businessData_rebate_displayNamePlural,
    navan_expenseTemplateCSV_businessData_repayment_displayName,
    navan_expenseTemplateCSV_businessData_repayment_displayNamePlural,
    navan_expenseTemplateCSV_businessData_statementJournalEntry_displayName,
    navan_expenseTemplateCSV_businessData_statementJournalEntry_displayNamePlural,
    navan_expenseTemplateCSV_businessData_statementOutboundPayment_displayName,
    navan_expenseTemplateCSV_businessData_statementOutboundPayment_displayNamePlural,
    navan_expenseTemplateCSV_configurations_custom_defaultAccounts,
    navan_expenseTemplateCSV_configurations_custom_settings,
  ] = await getTranslations(
    env,
    [
      'navan.expenseTemplateCSV.title',
      'navan.expenseTemplateCSV.masterData.entity.displayName',
      'navan.expenseTemplateCSV.masterData.entity.displayNamePlural',
      'navan.expenseTemplateCSV.masterData.employee.displayName',
      'navan.expenseTemplateCSV.masterData.employee.displayNamePlural',
      'navan.expenseTemplateCSV.masterData.department.displayName',
      'navan.expenseTemplateCSV.masterData.department.displayNamePlural',
      'navan.expenseTemplateCSV.masterData.vendor.displayName',
      'navan.expenseTemplateCSV.masterData.vendor.displayNamePlural',
      'navan.expenseTemplateCSV.masterData.account.displayName',
      'navan.expenseTemplateCSV.masterData.account.displayNamePlural',
      'navan.expenseTemplateCSV.businessData.manualTransaction.displayName',
      'navan.expenseTemplateCSV.businessData.manualTransaction.displayNamePlural',
      'navan.expenseTemplateCSV.businessData.manualTransactionVB.displayName',
      'navan.expenseTemplateCSV.businessData.manualTransactionVB.displayNamePlural',
      'navan.expenseTemplateCSV.businessData.purchaseOrRefund.displayName',
      'navan.expenseTemplateCSV.businessData.purchaseOrRefund.displayNamePlural',
      'navan.expenseTemplateCSV.businessData.rebate.displayName',
      'navan.expenseTemplateCSV.businessData.rebate.displayNamePlural',
      'navan.expenseTemplateCSV.businessData.repayment.displayName',
      'navan.expenseTemplateCSV.businessData.repayment.displayNamePlural',
      'navan.expenseTemplateCSV.businessData.statementJournalEntry.displayName',
      'navan.expenseTemplateCSV.businessData.statementJournalEntry.displayNamePlural',
      'navan.expenseTemplateCSV.businessData.statementOutboundPayment.displayName',
      'navan.expenseTemplateCSV.businessData.statementOutboundPayment.displayNamePlural',
      'navan.expenseTemplateCSV.configurations.custom.defaultAccounts',
      'navan.expenseTemplateCSV.configurations.custom.settings',
    ],
    'everest.fin.integration.navan/navan'
  );

  const { uploadPaidStatement, paymentDate } = await getNavanSettings(env);

  return {
    general: {
      package: NAVAN_PACKAGE,
      providerName: EvstExtractionProviders.NavanCSV,
      enableDataSyncRunTracking: true,
      useWithoutConnector: true,
      enableBusinessUserStagingView: true,
      enableActivateFailures: true,
    },
    templates: {
      masterData: {
        hideColumns: [
          EvstStagingStatus.NeedsInteraction,
          EvstStagingStatus.FAILED,
          EvstStagingStatus.Drafted,
        ],
      },
      transactionData: {
        hideColumns: [EvstStagingStatus.NeedsInteraction, 'actions'],
      },
      overview: {
        title: navan_expenseTemplateCSV_title,
        showStatusColumn: true,
        showLastSyncColumn: true,
        configurations: {
          base: {
            useStartDate: false,
            useModelExclusions: false,
          },
          custom: [
            {
              name: navan_expenseTemplateCSV_configurations_custom_defaultAccounts,
              status: '',
              templateUrl:
                '/templates/everest.fin.integration.navan/uinext/configuration/defaultAccounts',
              size: 'large',
            },
            {
              name: navan_expenseTemplateCSV_configurations_custom_settings,
              status: '',
              templateUrl:
                '/templates/everest.fin.integration.navan/uinext/navanSettings',
              size: 'medium',
            },
          ],
        },
        useOverviewUpload: true,
        uploadAlertValues: uploadPaidStatement
          ? {
              alertMessage: paymentDate
                ? `Current paid statement date is ${paymentDate.toString()}`
                : 'Payment date is not set. Paid statement creation will fail',
              alertTitle: 'Paid Statement Date:',
              alertVariant: paymentDate ? 'info' : 'warning',
            }
          : undefined,
      },
    },
    executions: {
      collectBusinessData: false,
      metadata: [
        {
          displayName: navan_expenseTemplateCSV_masterData_entity_displayName,
          displayNamePlural:
            navan_expenseTemplateCSV_masterData_entity_displayNamePlural,
          everestModel: Entity.MODEL_URN,
          providerModel: NAVAN_PROVIDER_MODEL__EXPENSE_CSV__ENTITY,
          mappings: {
            file: {
              name: NAVAN_MAPPING__EXPENSE_CSV__ENTITY,
              originalIdKey: NAVAN_ORIGINAL_ID_KEY_LEGAL_ENTITY,
              type: {
                semantic: true,
                technical: true,
              },
            },
          },
          applicationUrl: '@uicode:entities',
        },
        {
          displayName: navan_expenseTemplateCSV_masterData_employee_displayName,
          displayNamePlural:
            navan_expenseTemplateCSV_masterData_employee_displayNamePlural,
          everestModel: Employee.MODEL_URN,
          providerModel: NAVAN_PROVIDER_MODEL__EXPENSE_CSV__EMPLOYEE,
          mappings: {
            file: {
              name: NAVAN_MAPPING__EXPENSE_CSV__EMPLOYEE,
              originalIdKey:
                NAVAN_ORIGINAL_ID_KEY_SYNTHETIC_KEY_LEGAL_ENTITY_AND_CARDHOLDER_EMAIL,
              type: {
                semantic: true,
                technical: true,
              },
            },
          },
          aiOptions: {
            rules: [
              `The Source and target items both have a name property. Use that one to match.
            The Source item name property has the format <fullname> (<email>). The target item name property has the format <fullname> (<email>?).
            If both have a email, then the email needs to be exactly the same in source and target.
            If the email is missing in the target, then use the fullname only to match. Also here there needs to be an exact match.`,
            ],
          },
          applicationUrl:
            '/templates/everest.hr.base/uinext/employee/employeeAdminList',
        },
        {
          displayName:
            navan_expenseTemplateCSV_masterData_department_displayName,
          displayNamePlural:
            navan_expenseTemplateCSV_masterData_department_displayNamePlural,
          everestModel: Department.MODEL_URN,
          providerModel: NAVAN_PROVIDER_MODEL__EXPENSE_CSV__DEPARTMENT,
          mappings: {
            file: {
              name: NAVAN_MAPPING__EXPENSE_CSV__DEPARTMENT,
              originalIdKey: NAVAN_ORIGINAL_ID_KEY_TRANSACTION_DEPARTMENT,
              type: {
                semantic: true,
                technical: true,
              },
            },
          },
          applicationUrl:
            '/templates/everest.hr.base/uinext/department/departmentHierarchy',
        },
        {
          displayName: navan_expenseTemplateCSV_masterData_vendor_displayName,
          displayNamePlural:
            navan_expenseTemplateCSV_masterData_vendor_displayNamePlural,
          everestModel: Vendor.MODEL_URN,
          providerModel: NAVAN_PROVIDER_MODEL__EXPENSE_CSV__VENDOR,
          mappings: {
            file: {
              name: NAVAN_MAPPING__EXPENSE_CSV__VENDOR,
              originalIdKey:
                NAVAN_ORIGINAL_ID_KEY_SYNTHETIC_KEY_LEGAL_ENTITY_AND_CARDHOLDER_EMAIL,
              type: {
                semantic: true,
                technical: true,
              },
            },
          },
          applicationUrl:
            '/templates/everest.fin.expense/uinext/vendorMgmt/vendors',
        },
        {
          displayName: navan_expenseTemplateCSV_masterData_account_displayName,
          displayNamePlural:
            navan_expenseTemplateCSV_masterData_account_displayNamePlural,
          everestModel: Account.MODEL_URN,
          providerModel: NAVAN_PROVIDER_MODEL__EXPENSE_CSV__ACCOUNT,
          mappings: {
            file: {
              name: NAVAN_MAPPING__EXPENSE_CSV__ACCOUNT,
              originalIdKey:
                NAVAN_ORIGINAL_ID_KEY_SYNTHETIC_KEY_LEGAL_ENTITY_AND_GL_CODE_NUMBER,
              type: {
                semantic: true,
                technical: true,
              },
            },
          },
          exportOptions: {
            additionalFields: [
              {
                field: 'entityId',
                headerName: 'Entity Id',
              },
              {
                field: 'entityName',
                headerName: 'Entity',
              },
            ],
          },
          applicationUrl:
            '/templates/everest.fin.accounting/uinext/chartOfAccountsList',
        },
      ],
      businessData: [
        {
          displayName:
            navan_expenseTemplateCSV_businessData_manualTransaction_displayName,
          displayNamePlural:
            navan_expenseTemplateCSV_businessData_manualTransaction_displayNamePlural,
          everestModel: ExpenseReportBase.MODEL_URN,
          providerModel: NAVAN_PROVIDER_MODEL__EXPENSE_CSV__EXPENSE_REPORT_BASE,
          mappings: {
            file: {
              name: NAVAN_MAPPING__EXPENSE_CSV__EXPENSE,
              originalIdKey: NAVAN_ORIGINAL_ID_KEY_ID,
              type: {
                technical: true,
              },
            },
          },
          applicationUrl:
            '/templates/everest.fin.expense/uinext/expenseMgmt/employeeExpenses',
        },
        {
          displayName:
            navan_expenseTemplateCSV_businessData_manualTransactionVB_displayName,
          displayNamePlural:
            navan_expenseTemplateCSV_businessData_manualTransactionVB_displayNamePlural,
          everestModel: VendorBillHeaderBase.MODEL_URN,
          providerModel: NAVAN_PROVIDER_MODEL__EXPENSE_CSV__VENDOR_BILL,
          mappings: {
            file: {
              name: NAVAN_MAPPING__EXPENSE_CSV__VENDOR_BILL,
              originalIdKey: NAVAN_ORIGINAL_ID_KEY_ID,
              type: {
                technical: true,
              },
            },
          },
          applicationUrl:
            '/templates/everest.fin.expense/uinext/vendorMgmt/vendorBills',
        },
        {
          displayName:
            navan_expenseTemplateCSV_businessData_purchaseOrRefund_displayName,
          displayNamePlural:
            navan_expenseTemplateCSV_businessData_purchaseOrRefund_displayNamePlural,
          everestModel: CreditCardEntryHeader.MODEL_URN,
          providerModel:
            NAVAN_PROVIDER_MODEL__EXPENSE_CSV__PURCHASE_OR_REFUND__CREDIT_CARD_ENTRY,
          mappings: {
            file: {
              name: NAVAN_MAPPING__EXPENSE_CSV__CREDIT_CARD_ENTRY,
              originalIdKey: NAVAN_ORIGINAL_ID_KEY_ID,
              type: {
                technical: true,
              },
            },
          },
          applicationUrl:
            '/templates/everest.fin.integration.bank/uinext/creditCardEntry/creditCardEntries',
        },
        {
          displayName: navan_expenseTemplateCSV_businessData_rebate_displayName,
          displayNamePlural:
            navan_expenseTemplateCSV_businessData_rebate_displayNamePlural,
          everestModel: JournalEntryHeader.MODEL_URN,
          providerModel:
            NAVAN_PROVIDER_MODEL__EXPENSE_CSV__REBATE__JOURNAL_ENTRY,
          mappings: {
            file: {
              name: NAVAN_MAPPING__EXPENSE_CSV__REBATE__JOURNAL_ENTRY,
              originalIdKey: NAVAN_ORIGINAL_ID_KEY_ID,
              type: {
                technical: true,
              },
            },
          },
          applicationUrl:
            '/templates/everest.fin.accounting/uinext/journalEntries',
        },
        {
          displayName:
            navan_expenseTemplateCSV_businessData_repayment_displayName,
          displayNamePlural:
            navan_expenseTemplateCSV_businessData_repayment_displayNamePlural,
          everestModel: JournalEntryHeader.MODEL_URN,
          providerModel:
            NAVAN_PROVIDER_MODEL__EXPENSE_CSV__REPAYMENT__JOURNAL_ENTRY,
          mappings: {
            file: {
              name: NAVAN_MAPPING__EXPENSE_CSV__REPAYMENT__JOURNAL_ENTRY,
              originalIdKey: NAVAN_ORIGINAL_ID_KEY_ID,
              type: {
                technical: true,
              },
            },
          },
          applicationUrl:
            '/templates/everest.fin.accounting/uinext/journalEntries',
        },
        ...(uploadPaidStatement
          ? [
              {
                displayName:
                  navan_expenseTemplateCSV_businessData_statementOutboundPayment_displayName,
                displayNamePlural:
                  navan_expenseTemplateCSV_businessData_statementOutboundPayment_displayNamePlural,
                everestModel: OutboundPaymentHeaderBase.MODEL_URN,
                providerModel:
                  NAVAN_PROVIDER_MODEL__EXPENSE_CSV__PAID_STATEMENT_OUTBOUND_PAYMENT,
                mappings: {
                  file: {
                    name: NAVAN_MAPPING__EXPENSE_CSV__PAID_STATEMENT_OUTBOUND_PAYMENT,
                    originalIdKey:
                      NAVAN_ORIGINAL_ID_KEY_SYNTHETIC_KEY_STATEMENT,
                    type: {
                      technical: true,
                    },
                  },
                },
                applicationUrl:
                  '/templates/everest.fin.expense/payment/uinext/paymentsHistory',
              },
              {
                displayName:
                  navan_expenseTemplateCSV_businessData_statementJournalEntry_displayName,
                displayNamePlural:
                  navan_expenseTemplateCSV_businessData_statementJournalEntry_displayNamePlural,
                everestModel: JournalEntryHeader.MODEL_URN,
                providerModel:
                  NAVAN_PROVIDER_MODEL__EXPENSE_CSV__PAID_STATEMENT_JOURNAL_ENTRY,
                mappings: {
                  file: {
                    name: NAVAN_MAPPING__EXPENSE_CSV__PAID_STATEMENT_JOURNAL_ENTRY,
                    originalIdKey:
                      NAVAN_ORIGINAL_ID_KEY_SYNTHETIC_KEY_STATEMENT,
                    type: {
                      technical: true,
                    },
                  },
                },
                applicationUrl:
                  '/templates/everest.fin.accounting/uinext/journalEntries',
              },
            ]
          : []),
      ],
    },
  };
}
