import type {
  Controller<PERSON>lient<PERSON>rovider,
  <PERSON>tchProvider,
  Response,
  ServerEnvironmentProvider,
} from '@everestsystems/content-core';
import { log } from '@everestsystems/content-core';

import fetchToken from './fetchToken.action';
import { executeGitHubGraphQL } from './utils/graphql';
import { getGitHubRepoInfo } from './utils/repo';

// Note: Rate limiting removed since stagger delay in supportCaseSyncHook handles the main 2AM spike issue

// Type definitions (assuming these might be shared or moved eventually)
interface ProjectFieldValue {
  fieldName: string;
  value: string;
}

interface InternalGitHubIssue {
  // Standard Issue Fields
  number: number;
  state: string; // e.g., "OPEN", "CLOSED"
  title: string;
  body: string;
  assignees: Array<{ login: string }>;
  labels: Array<{ name: string }>;
  // Project Fields
  projectFields?: ProjectFieldValue[];
}

/**
 * Fetches core details and project fields for a batch of issues using a single GraphQL query.
 */
async function getIssueDetailsBatchGraphQL(
  issueNumbers: number[],
  repoOwner: string,
  repoName: string,
  projectTitle: string,
  token: string,
  env: FetchProvider & ControllerClientProvider
): Promise<Map<number, InternalGitHubIssue>> {
  // Returns map of number -> full issue detail
  if (issueNumbers.length === 0) {
    return new Map();
  }

  // Note: Rate limiting removed - stagger delay in supportCaseSyncHook handles coordination

  // Construct the GraphQL query with aliases for each issue
  // Now fetching standard fields + project fields
  const issueQueries = issueNumbers
    .map(
      (num) => `
    issue${num}: issue(number: ${num}) {
      number
      state
      title
      body
      assignees(first: 10) { nodes { login } }
      labels(first: 20) { nodes { name } }
      projectItems(first: 10) {
        nodes {
          project {
            title
          }
          fieldValues(first: 20) {
            nodes {
              ... on ProjectV2ItemFieldSingleSelectValue {
                name
                field { ... on ProjectV2FieldCommon { name } }
              }
              ... on ProjectV2ItemFieldTextValue {
                text
                field { ... on ProjectV2FieldCommon { name } }
              }
              # Add other field types if needed (e.g., Date, Number)
            }
          }
        }
      }
    }`
    )
    .join('\n');

  const query = `
    query GetIssueDetailsBatch {
      repository(owner: "${repoOwner}", name: "${repoName}") {
        ${issueQueries}
      }
    }
  `;

  // Define the expected response structure (more detailed now)
  type GraphQLIssueNode = {
    number: number;
    state: string;
    title: string;
    body: string;
    assignees?: { nodes?: Array<{ login: string }> };
    labels?: { nodes?: Array<{ name: string }> };
    projectItems?: {
      nodes?: Array<{
        project: { title: string };
        fieldValues: {
          nodes: Array<{
            name?: string;
            text?: string;
            field?: { name: string };
          }>;
        };
      }>;
    };
  };

  type BatchedResponse = {
    data?: {
      repository?: {
        [alias: string]: GraphQLIssueNode | null; // e.g., issue18323
      };
    };
    errors?: Array<{
      message: string;
      type?: string;
      path?: Array<string | number>;
    }>;
  };

  let response: BatchedResponse | null;
  try {
    response = await executeGitHubGraphQL<BatchedResponse>(
      query,
      token,
      {},
      env
    );
  } catch (error) {
    log.error(
      '[getIssueDetailsBatchGraphQL] executeGitHubGraphQL failed',
      error
    );
    // Check if it's a rate limit error
    if (
      error instanceof Error &&
      error.message.toLowerCase().includes('rate limit')
    ) {
      log.warn('[getIssueDetailsBatchGraphQL] Hit rate limit');
      throw new Error('Rate limit exceeded - please retry later');
    }
    // Re-throw the error to be caught by fetchIssues
    throw error;
  }

  const results = new Map<number, InternalGitHubIssue>();

  // If the response is null or undefined after the call (even if no error was thrown)
  // This might indicate an issue handled within executeGitHubGraphQL without throwing
  if (!response) {
    log.error(
      '[getIssueDetailsBatchGraphQL] executeGitHubGraphQL returned null/undefined response'
    );
    // Throw a generic error, or specific one if possible
    throw new Error('Failed to fetch data from GitHub GraphQL API.');
  }

  // Handle top-level GraphQL errors present in the response payload
  if (response.errors && response.errors.length > 0) {
    log.error(
      '[getIssueDetailsBatchGraphQL] GraphQL query returned errors in payload',
      response.errors
    );
    // Check if any errors are rate limit related
    const hasRateLimit = response.errors.some(
      (error) =>
        error.message.toLowerCase().includes('rate limit') ||
        error.type === 'RATE_LIMITED'
    );

    if (hasRateLimit) {
      throw new Error('Rate limit exceeded in GraphQL response');
    }

    // Decide if this constitutes a full failure. Often, it does.
    // Throw an error including details from the GraphQL errors.
    const errorMessages = response.errors.map((e) => e.message).join('; ');
    throw new Error(`GraphQL query failed: ${errorMessages}`);
  }

  // Check specifically for the repository data after handling errors
  if (!response.data?.repository) {
    log.warn(
      '[getIssueDetailsBatchGraphQL] No repository data in GraphQL response payload (after checking for errors)'
    );
    // Even if there were no explicit errors, missing repository data is unexpected.
    // Consider throwing an error here as well, as the query likely didn't work as intended.
    throw new Error('GraphQL response missing expected repository data.');
    // return results; // Previous behavior: Return empty map (Masks the error)
  }

  const repoData = response.data.repository;

  // Parse the response, extracting data for each issue alias
  for (const issueNumber of issueNumbers) {
    const alias = `issue${issueNumber}`;
    const issueData = repoData[alias];

    // If an individual issue wasn't found (e.g., wrong ID), skip it
    if (!issueData) {
      log.warn(
        `[getIssueDetailsBatchGraphQL] Issue ${issueNumber} not found in repository ${repoOwner}/${repoName}.`
      );
      continue;
    }

    const projectFields: ProjectFieldValue[] = [];
    if (issueData.projectItems?.nodes) {
      for (const projectItem of issueData.projectItems.nodes) {
        // Make comparison case-insensitive
        if (
          projectItem.project.title.toLowerCase() === projectTitle.toLowerCase()
        ) {
          const fieldValues = projectItem.fieldValues.nodes;
          for (const fieldValue of fieldValues) {
            if (fieldValue.field?.name) {
              if (fieldValue.name) {
                projectFields.push({
                  fieldName: fieldValue.field.name,
                  value: fieldValue.name,
                });
              } else if (fieldValue.text) {
                projectFields.push({
                  fieldName: fieldValue.field.name,
                  value: fieldValue.text,
                });
              }
            }
          }
        }
      }
    }

    // Construct the full InternalGitHubIssue object
    results.set(issueNumber, {
      number: issueData.number,
      state: issueData.state,
      title: issueData.title,
      body: issueData.body,
      assignees: issueData.assignees?.nodes || [],
      labels: issueData.labels?.nodes || [],
      projectFields: projectFields,
    });
  }

  return results;
}

/**
 * Fetches details (including project fields) for a specific list of GitHub issues using GraphQL.
 */
export async function fetchIssues(
  env: FetchProvider & ServerEnvironmentProvider & ControllerClientProvider,
  issueNumbers: number[] // Now takes specific issue numbers
): Promise<Response<InternalGitHubIssue[]>> {
  // Returns array of issues
  if (!issueNumbers || issueNumbers.length === 0) {
    return {
      status: 200,
      statusText: 'OK',
      data: [], // Return empty array if no numbers provided
    };
  }

  const token = await fetchToken(env);
  const { owner, name, projectTitle } = getGitHubRepoInfo(
    env.serverEnvironment.FQDN
  );

  const fetchedIssuesMap = new Map<number, InternalGitHubIssue>();

  try {
    // reduce batch size to be more conservative with rate limits
    // smaller batches = more requests but less complex queries
    const batchSize = 15; // reduced from 30 to be more conservative
    const issueNumberBatches: number[][] = [];
    for (let i = 0; i < issueNumbers.length; i += batchSize) {
      issueNumberBatches.push(issueNumbers.slice(i, i + batchSize));
    }

    log.info(
      `[fetchIssues] Processing ${issueNumbers.length} issues in ${issueNumberBatches.length} batches of max ${batchSize}`
    );

    // Fetch details for each batch using GraphQL with error resilience
    for (
      let batchIndex = 0;
      batchIndex < issueNumberBatches.length;
      batchIndex++
    ) {
      const batchNumbers = issueNumberBatches[batchIndex];

      try {
        log.debug(
          `[fetchIssues] Processing batch ${batchIndex + 1}/${
            issueNumberBatches.length
          } with ${batchNumbers.length} issues`
        );

        const batchResults = await getIssueDetailsBatchGraphQL(
          batchNumbers,
          owner,
          name,
          projectTitle,
          token,
          env
        );

        // Merge batch results into the main map
        for (const [issueNum, issueData] of batchResults.entries()) {
          fetchedIssuesMap.set(issueNum, issueData);
        }

        log.debug(
          `[fetchIssues] Batch ${
            batchIndex + 1
          } completed successfully, fetched ${batchResults.size} issues`
        );
      } catch (batchError) {
        // Log the batch error but continue with other batches
        log.error(
          `[fetchIssues] Batch ${
            batchIndex + 1
          } failed for issues [${batchNumbers.join(
            ', '
          )}], continuing with remaining batches`,
          batchError
        );

        // Log rate limit errors for monitoring
        if (
          batchError instanceof Error &&
          batchError.message.toLowerCase().includes('rate limit')
        ) {
          log.warn(`[fetchIssues] Rate limit hit on batch ${batchIndex + 1}`);
        }

        // Don't throw - continue with other batches
      }
    }

    // Convert map values to an array, preserving original order potentially lost in Map
    const finalIssueList = issueNumbers
      .map((num) => fetchedIssuesMap.get(num))
      .filter((issue): issue is InternalGitHubIssue => issue !== undefined);

    log.info(
      `[fetchIssues] Successfully fetched ${finalIssueList.length}/${issueNumbers.length} requested issues`
    );

    return {
      status: 200,
      statusText: 'OK',
      data: finalIssueList,
    };
  } catch (error: unknown) {
    let status = 500;
    let statusText = 'Internal Server Error'; // Default

    if (typeof error === 'object' && error !== null) {
      // Prioritize specific status/statusText if the error object has them
      status = (error as { status?: number }).status || 500;
      statusText =
        (error as { statusText?: string }).statusText ||
        // If no statusText, but there is a message, use that
        (error as { message?: string })?.message ||
        // Fallback based on status
        (status === 500 ? 'Internal Server Error' : 'Error');
    } else if (error instanceof Error) {
      // If it's a standard Error, use its message directly
      statusText = error.message;
      // Keep status as 500 unless more specific info is available elsewhere
    } else {
      // Handle non-object, non-Error types (e.g., string thrown)
      statusText = String(error);
    }

    log.error(
      `[fetchIssues] Error fetching issue details: ${statusText}`,
      error // Log the full error object for detailed debugging
    );
    return {
      status,
      statusText, // Return the refined status text
      data: [],
    };
  }
}

export default fetchIssues;
