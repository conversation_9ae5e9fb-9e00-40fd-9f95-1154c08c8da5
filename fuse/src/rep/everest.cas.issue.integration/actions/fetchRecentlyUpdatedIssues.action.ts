import type {
  Controller<PERSON>lient<PERSON>rovider,
  FetchProvider,
  Response,
  ServerEnvironmentProvider,
} from '@everestsystems/content-core';
import { log } from '@everestsystems/content-core';

import fetchToken from './fetchToken.action';
import { executeGitHubGraphQL } from './utils/graphql';
import { getGitHubRepoInfo } from './utils/repo';

interface ProjectFieldValue {
  fieldName: string;
  value: string;
}

interface InternalGitHubIssue {
  number: number;
  state: string;
  title: string;
  body: string;
  assignees: Array<{ login: string }>;
  labels: Array<{ name: string }>;
  projectFields?: ProjectFieldValue[];
  updatedAt: string; // ISO date string
}

/**
 * Fetches issues that have been updated within the specified time period using GraphQL search.
 */
async function getRecentlyUpdatedIssuesGraphQL(
  repoOwner: string,
  repoName: string,
  projectTitle: string,
  token: string,
  env: FetchProvider & ControllerClientProvider,
  hoursBack = 24 // default to last 24 hours
): Promise<InternalGitHubIssue[]> {
  // calculate the date for filtering (UTC)
  const sinceDate = new Date();
  sinceDate.setHours(sinceDate.getHours() - hoursBack);
  const sinceDateISO = sinceDate.toISOString();

  log.info(
    `[getRecentlyUpdatedIssuesGraphQL] Fetching issues updated since ${sinceDateISO}`
  );

  // use github's search api through graphql to find recently updated issues
  // this is way more efficient than fetching all issues
  const query = `
    query GetRecentlyUpdatedIssues($searchQuery: String!, $first: Int!) {
      search(query: $searchQuery, type: ISSUE, first: $first) {
        issueCount
        pageInfo {
          hasNextPage
          endCursor
        }
        edges {
          node {
            ... on Issue {
              number
              state
              title
              body
              updatedAt
              assignees(first: 10) { 
                nodes { login } 
              }
              labels(first: 20) { 
                nodes { name } 
              }
              projectItems(first: 10) {
                nodes {
                  project {
                    title
                  }
                  fieldValues(first: 20) {
                    nodes {
                      ... on ProjectV2ItemFieldSingleSelectValue {
                        name
                        field { ... on ProjectV2FieldCommon { name } }
                      }
                      ... on ProjectV2ItemFieldTextValue {
                        text
                        field { ... on ProjectV2FieldCommon { name } }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  `;

  // construct search query for github
  // search for issues in the specific repo that were updated after the date
  const searchQuery = `repo:${repoOwner}/${repoName} is:issue updated:>=${sinceDateISO}`;

  const variables = {
    searchQuery,
    first: 100, // max per page, we'll handle pagination if needed
  };

  type SearchResponse = {
    data?: {
      search?: {
        issueCount: number;
        pageInfo: {
          hasNextPage: boolean;
          endCursor: string;
        };
        edges: Array<{
          node: {
            number: number;
            state: string;
            title: string;
            body: string;
            updatedAt: string;
            assignees?: { nodes?: Array<{ login: string }> };
            labels?: { nodes?: Array<{ name: string }> };
            projectItems?: {
              nodes?: Array<{
                project: { title: string };
                fieldValues: {
                  nodes: Array<{
                    name?: string;
                    text?: string;
                    field?: { name: string };
                  }>;
                };
              }>;
            };
          };
        }>;
      };
    };
    errors?: Array<{
      message: string;
      type?: string;
      path?: Array<string | number>;
    }>;
  };

  let response: SearchResponse | null;
  try {
    response = await executeGitHubGraphQL<SearchResponse>(
      query,
      token,
      variables,
      env
    );
  } catch (error) {
    log.error(
      '[getRecentlyUpdatedIssuesGraphQL] executeGitHubGraphQL failed',
      error
    );
    throw error;
  }

  if (!response) {
    log.error(
      '[getRecentlyUpdatedIssuesGraphQL] executeGitHubGraphQL returned null/undefined response'
    );
    throw new Error('Failed to fetch data from GitHub GraphQL API.');
  }

  if (response.errors && response.errors.length > 0) {
    log.error(
      '[getRecentlyUpdatedIssuesGraphQL] GraphQL query returned errors',
      response.errors
    );
    const errorMessages = response.errors.map((e) => e.message).join('; ');
    throw new Error(`GraphQL search query failed: ${errorMessages}`);
  }

  if (!response.data?.search) {
    log.warn(
      '[getRecentlyUpdatedIssuesGraphQL] No search data in GraphQL response'
    );
    return [];
  }

  const searchData = response.data.search;
  log.info(
    `[getRecentlyUpdatedIssuesGraphQL] Found ${searchData.issueCount} recently updated issues`
  );

  const results: InternalGitHubIssue[] = [];

  // process the search results
  for (const edge of searchData.edges) {
    const issueData = edge.node;

    const projectFields: ProjectFieldValue[] = [];
    if (issueData.projectItems?.nodes) {
      for (const projectItem of issueData.projectItems.nodes) {
        if (
          projectItem.project.title.toLowerCase() === projectTitle.toLowerCase()
        ) {
          const fieldValues = projectItem.fieldValues.nodes;
          for (const fieldValue of fieldValues) {
            if (fieldValue.field?.name) {
              if (fieldValue.name) {
                projectFields.push({
                  fieldName: fieldValue.field.name,
                  value: fieldValue.name,
                });
              } else if (fieldValue.text) {
                projectFields.push({
                  fieldName: fieldValue.field.name,
                  value: fieldValue.text,
                });
              }
            }
          }
        }
      }
    }

    results.push({
      number: issueData.number,
      state: issueData.state,
      title: issueData.title,
      body: issueData.body,
      updatedAt: issueData.updatedAt,
      assignees: issueData.assignees?.nodes || [],
      labels: issueData.labels?.nodes || [],
      projectFields: projectFields,
    });
  }

  // todo: handle pagination if hasNextPage is true and we need more results
  // for now, 100 issues updated in last 24h should be plenty for most repos
  if (searchData.pageInfo.hasNextPage) {
    log.warn(
      `[getRecentlyUpdatedIssuesGraphQL] Found more than 100 recently updated issues. Consider implementing pagination or reducing time window.`
    );
  }

  return results;
}

/**
 * Fetches issues that have been updated recently (default: last 24 hours).
 * This is much more efficient than fetching all issues as it only gets what changed.
 */
export async function fetchRecentlyUpdatedIssues(
  env: FetchProvider & ServerEnvironmentProvider & ControllerClientProvider,
  hoursBack = 24 // how many hours back to look for updates
): Promise<Response<InternalGitHubIssue[]>> {
  const token = await fetchToken(env);
  const { owner, name, projectTitle } = getGitHubRepoInfo(
    env.serverEnvironment.FQDN
  );

  try {
    log.info(
      `[fetchRecentlyUpdatedIssues] Fetching issues updated in last ${hoursBack} hours`
    );

    const recentIssues = await getRecentlyUpdatedIssuesGraphQL(
      owner,
      name,
      projectTitle,
      token,
      env,
      hoursBack
    );

    log.info(
      `[fetchRecentlyUpdatedIssues] Successfully fetched ${recentIssues.length} recently updated issues`
    );

    return {
      status: 200,
      statusText: 'OK',
      data: recentIssues,
    };
  } catch (error: unknown) {
    let status = 500;
    let statusText = 'Internal Server Error';

    if (typeof error === 'object' && error !== null) {
      status = (error as { status?: number }).status || 500;
      statusText =
        (error as { statusText?: string }).statusText ||
        (error as { message?: string })?.message ||
        (status === 500 ? 'Internal Server Error' : 'Error');
    } else if (error instanceof Error) {
      statusText = error.message;
    } else {
      statusText = String(error);
    }

    log.error(
      `[fetchRecentlyUpdatedIssues] Error fetching recently updated issues: ${statusText}`,
      error
    );

    return {
      status,
      statusText,
      data: [],
    };
  }
}

export default fetchRecentlyUpdatedIssues;
