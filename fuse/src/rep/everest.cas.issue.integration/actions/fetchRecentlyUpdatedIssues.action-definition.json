{"description": "Fetches GitHub issues that have been updated within the specified time period using GraphQL search. Much more efficient than fetching all issues.", "supportedModels": ["urn:evst:everest:cas/issue/integration:model/node:CasCustomerIssues"], "inputParameters": [{"name": "hoursBack", "type": "urn:evst:everest:appserver:primitive:Number", "description": "Number of hours back to look for updated issues (default: 24)", "optional": true}], "outputValues": [{"name": "result", "type": "urn:evst:everest:appserver:primitive:JSON", "description": "An array containing recently updated issues with their details."}], "exposeApi": true, "isQuery": true, "isInstanceMethod": false}