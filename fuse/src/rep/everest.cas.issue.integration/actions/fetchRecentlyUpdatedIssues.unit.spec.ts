import { fetchRecentlyUpdatedIssues } from './fetchRecentlyUpdatedIssues.action';
import fetchToken from './fetchToken.action';
import { executeGitHubGraphQL } from './utils/graphql';
import { getGitHubRepoInfo } from './utils/repo';

// Mock dependencies
jest.mock('./fetchToken.action');
jest.mock('./utils/graphql');
jest.mock('./utils/repo');

describe('fetchRecentlyUpdatedIssues', () => {
  const mockEnv = {
    serverEnvironment: { FQDN: 'test-tenant.example.com' },
    connectivityLayer: {},
    fetchProvider: {},
  } as unknown as Parameters<typeof fetchRecentlyUpdatedIssues>[0];

  const mockToken = 'ghp_test_token_123';
  const mockRepoInfo = {
    owner: 'test-org',
    name: 'test-repo',
    projectTitle: 'Test Project',
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Setup default mocks
    (fetchToken as jest.Mock).mockResolvedValue(mockToken);
    (getGitHubRepoInfo as jest.Mock).mockReturnValue(mockRepoInfo);
  });

  describe('successful fetching', () => {
    it('should fetch recently updated issues using search API', async () => {
      const mockSearchResponse = {
        data: {
          search: {
            issueCount: 2,
            pageInfo: {
              hasNextPage: false,
              endCursor: 'cursor123',
            },
            edges: [
              {
                node: {
                  number: 123,
                  state: 'OPEN',
                  title: 'Recently Updated Issue 1',
                  body: 'Body 1',
                  updatedAt: '2025-01-07T12:00:00Z',
                  assignees: { nodes: [{ login: 'user1' }] },
                  labels: { nodes: [{ name: 'bug' }] },
                  projectItems: {
                    nodes: [
                      {
                        project: { title: 'Test Project' },
                        fieldValues: {
                          nodes: [
                            {
                              name: 'High',
                              field: { name: 'Priority' },
                            },
                          ],
                        },
                      },
                    ],
                  },
                },
              },
              {
                node: {
                  number: 456,
                  state: 'CLOSED',
                  title: 'Recently Updated Issue 2',
                  body: 'Body 2',
                  updatedAt: '2025-01-07T11:00:00Z',
                  assignees: { nodes: [] },
                  labels: { nodes: [] },
                  projectItems: { nodes: [] },
                },
              },
            ],
          },
        },
      };

      (executeGitHubGraphQL as jest.Mock).mockResolvedValue(mockSearchResponse);

      const result = await fetchRecentlyUpdatedIssues(mockEnv, 24);

      expect(result.status).toBe(200);
      expect(result.data).toHaveLength(2);

      expect(result.data[0]).toEqual({
        number: 123,
        state: 'OPEN',
        title: 'Recently Updated Issue 1',
        body: 'Body 1',
        updatedAt: '2025-01-07T12:00:00Z',
        assignees: [{ login: 'user1' }],
        labels: [{ name: 'bug' }],
        projectFields: [{ fieldName: 'Priority', value: 'High' }],
      });

      expect(result.data[1]).toEqual({
        number: 456,
        state: 'CLOSED',
        title: 'Recently Updated Issue 2',
        body: 'Body 2',
        updatedAt: '2025-01-07T11:00:00Z',
        assignees: [],
        labels: [],
        projectFields: [],
      });

      // Verify GraphQL search query was used
      expect(executeGitHubGraphQL).toHaveBeenCalledWith(
        expect.stringContaining('search(query: $searchQuery'),
        mockToken,
        expect.objectContaining({
          searchQuery: expect.stringContaining(
            'repo:test-org/test-repo is:issue updated:>='
          ),
          first: 100,
        }),
        mockEnv
      );
    });

    it('should use default 24 hours when no hoursBack specified', async () => {
      const mockSearchResponse = {
        data: {
          search: {
            issueCount: 0,
            pageInfo: { hasNextPage: false },
            edges: [],
          },
        },
      };

      (executeGitHubGraphQL as jest.Mock).mockResolvedValue(mockSearchResponse);

      await fetchRecentlyUpdatedIssues(mockEnv);

      // Verify search query includes date constraint
      const callArgs = (executeGitHubGraphQL as jest.Mock).mock.calls[0];
      const variables = callArgs[2];
      expect(variables.searchQuery).toMatch(/updated:>=/);
    });

    it('should handle custom hoursBack parameter', async () => {
      const mockSearchResponse = {
        data: {
          search: {
            issueCount: 0,
            pageInfo: { hasNextPage: false },
            edges: [],
          },
        },
      };

      (executeGitHubGraphQL as jest.Mock).mockResolvedValue(mockSearchResponse);

      await fetchRecentlyUpdatedIssues(mockEnv, 48);

      // Verify 48 hours is used in search query
      const callArgs = (executeGitHubGraphQL as jest.Mock).mock.calls[0];
      const variables = callArgs[2];
      expect(variables.searchQuery).toContain(
        'repo:test-org/test-repo is:issue updated:>='
      );
    });

    it('should handle empty search results', async () => {
      const mockSearchResponse = {
        data: {
          search: {
            issueCount: 0,
            pageInfo: { hasNextPage: false, endCursor: '' },
            edges: [],
          },
        },
      };

      (executeGitHubGraphQL as jest.Mock).mockResolvedValue(mockSearchResponse);

      const result = await fetchRecentlyUpdatedIssues(mockEnv, 24);

      expect(result.status).toBe(200);
      expect(result.data).toEqual([]);
    });

    it('should warn about pagination when more than 100 results', async () => {
      const mockSearchResponse = {
        data: {
          search: {
            issueCount: 150,
            pageInfo: { hasNextPage: true, endCursor: 'cursor123' },
            edges: Array.from({ length: 100 }, (_, i) => ({
              node: {
                number: i + 1,
                state: 'OPEN',
                title: `Issue ${i + 1}`,
                body: `Body ${i + 1}`,
                updatedAt: '2025-01-07T12:00:00Z',
                assignees: { nodes: [] },
                labels: { nodes: [] },
                projectItems: { nodes: [] },
              },
            })),
          },
        },
      };

      (executeGitHubGraphQL as jest.Mock).mockResolvedValue(mockSearchResponse);

      const result = await fetchRecentlyUpdatedIssues(mockEnv, 24);

      expect(result.status).toBe(200);
      expect(result.data).toHaveLength(100);
      // Would normally check for warning log here
    });
  });

  describe('error handling', () => {
    it('should handle GraphQL execution errors', async () => {
      const mockError = new Error('Search API failed');
      (executeGitHubGraphQL as jest.Mock).mockRejectedValue(mockError);

      const result = await fetchRecentlyUpdatedIssues(mockEnv, 24);

      expect(result.status).toBe(500);
      expect(result.statusText).toBe('Search API failed');
      expect(result.data).toEqual([]);
    });

    it('should handle GraphQL response errors', async () => {
      const mockErrorResponse = {
        data: null,
        errors: [
          { message: 'Search query syntax error' },
          { message: 'Rate limit exceeded' },
        ],
      };

      (executeGitHubGraphQL as jest.Mock).mockResolvedValue(mockErrorResponse);

      const result = await fetchRecentlyUpdatedIssues(mockEnv, 24);

      expect(result.status).toBe(500);
      expect(result.statusText).toContain('GraphQL search query failed');
      expect(result.data).toEqual([]);
    });

    it('should handle missing search data', async () => {
      const mockResponse = {
        data: { search: null },
      };

      (executeGitHubGraphQL as jest.Mock).mockResolvedValue(mockResponse);

      const result = await fetchRecentlyUpdatedIssues(mockEnv, 24);

      expect(result.status).toBe(200);
      expect(result.data).toEqual([]);
    });

    it('should handle null response from executeGitHubGraphQL', async () => {
      (executeGitHubGraphQL as jest.Mock).mockResolvedValue(null);

      const result = await fetchRecentlyUpdatedIssues(mockEnv, 24);

      expect(result.status).toBe(500);
      expect(result.statusText).toContain(
        'Failed to fetch data from GitHub GraphQL API'
      );
      expect(result.data).toEqual([]);
    });
  });

  describe('project fields parsing', () => {
    it('should correctly parse project fields from matching project', async () => {
      const mockSearchResponse = {
        data: {
          search: {
            issueCount: 1,
            pageInfo: { hasNextPage: false },
            edges: [
              {
                node: {
                  number: 789,
                  state: 'OPEN',
                  title: 'Issue with Project Fields',
                  body: 'Body',
                  updatedAt: '2025-01-07T12:00:00Z',
                  assignees: { nodes: [] },
                  labels: { nodes: [] },
                  projectItems: {
                    nodes: [
                      {
                        project: { title: 'Test Project' },
                        fieldValues: {
                          nodes: [
                            {
                              name: 'Critical',
                              field: { name: 'Severity' },
                            },
                            {
                              text: 'Backend issue',
                              field: { name: 'Component' },
                            },
                          ],
                        },
                      },
                      {
                        project: { title: 'Other Project' }, // Should be ignored
                        fieldValues: {
                          nodes: [
                            {
                              name: 'Low',
                              field: { name: 'Priority' },
                            },
                          ],
                        },
                      },
                    ],
                  },
                },
              },
            ],
          },
        },
      };

      (executeGitHubGraphQL as jest.Mock).mockResolvedValue(mockSearchResponse);

      const result = await fetchRecentlyUpdatedIssues(mockEnv, 24);

      expect(result.data[0].projectFields).toEqual([
        { fieldName: 'Severity', value: 'Critical' },
        { fieldName: 'Component', value: 'Backend issue' },
      ]);
    });

    it('should handle issues without project fields', async () => {
      const mockSearchResponse = {
        data: {
          search: {
            issueCount: 1,
            pageInfo: { hasNextPage: false },
            edges: [
              {
                node: {
                  number: 999,
                  state: 'OPEN',
                  title: 'Simple Issue',
                  body: 'Body',
                  updatedAt: '2025-01-07T12:00:00Z',
                  assignees: { nodes: [] },
                  labels: { nodes: [] },
                  projectItems: { nodes: [] },
                },
              },
            ],
          },
        },
      };

      (executeGitHubGraphQL as jest.Mock).mockResolvedValue(mockSearchResponse);

      const result = await fetchRecentlyUpdatedIssues(mockEnv, 24);

      expect(result.data[0].projectFields).toEqual([]);
    });
  });

  describe('integration with repo info', () => {
    it('should use repo info to construct search query', async () => {
      const mockSearchResponse = {
        data: {
          search: {
            issueCount: 0,
            pageInfo: { hasNextPage: false },
            edges: [],
          },
        },
      };

      (executeGitHubGraphQL as jest.Mock).mockResolvedValue(mockSearchResponse);

      await fetchRecentlyUpdatedIssues(mockEnv, 24);

      expect(getGitHubRepoInfo).toHaveBeenCalledWith('test-tenant.example.com');
      expect(fetchToken).toHaveBeenCalledWith(mockEnv);

      const callArgs = (executeGitHubGraphQL as jest.Mock).mock.calls[0];
      const variables = callArgs[2];
      expect(variables.searchQuery).toContain('repo:test-org/test-repo');
    });
  });
});
