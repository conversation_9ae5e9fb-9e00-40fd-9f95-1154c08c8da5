/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// ******************************************************************************
// Generated Everest Types
// ******************************************************************************

import type { Decimal, RoundingMode } from '@everestsystems/decimal';
import type {
  ArgType,
  Association,
  AssociationManagementArgs,
  AsyncResult,
  Controller,
  ControllerClientProvider,
  CreateOptionsArg,
  DataWithMetadata,
  DeleteOptionsArg,
  FieldSelector,
  Filter,
  flattenFieldSelector,
  GraphInstanceEntry,
  KernelRequestOptions,
  NonAssociationFields,
  Optional,
  PurgeArgs,
  ReadOptionsArg,
  ReadReturnType as ReadReturnTypeGeneric,
  ReadReturnTypeWithAssociations,
  RequireAtLeastOne,
  SemanticSearchArg,
  SemanticSearchRequestOptions,
  TypeSafeQueryArgType,
  UpdateManyOptionsArg,
  UpdateOptionsArg,
  UpsertOptionsArg,
  WriteReturnType,
  WriteReturnTypeWithAssociations,
} from '@everestsystems/content-core';

import { UIExecutionContext } from '@everestsystems/content-core';
import type { CasCustomerIssues as everest_cas_issue_integration_model_node_CasCustomerIssues } from "@pkg/everest.cas.issue.integration/types/CasCustomerIssues";
import type orig_createIssue from "@pkg/everest.cas.issue.integration/actions/createIssue.action";
import type orig_deleteIssues from "@pkg/everest.cas.issue.integration/actions/deleteIssues.action";
import type orig_updateIssues from "@pkg/everest.cas.issue.integration/actions/updateIssues.action";
import type orig_fetchIssues from "@pkg/everest.cas.issue.integration/actions/fetchIssues.action";
import type orig_updateSingleIssue from "@pkg/everest.cas.issue.integration/actions/updateSingleIssue.action";
import type orig_offboardCustomer from "@pkg/everest.cas.issue.integration/actions/customerTenant/offboardCustomer.action";
import type orig_createGithubIssue from "@pkg/everest.cas.issue.integration/actions/createGithubIssue.action";
import type orig_fetchRecentlyUpdatedIssues from "@pkg/everest.cas.issue.integration/actions/fetchRecentlyUpdatedIssues.action";

/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */

type RequiredContext = {
  data: Record<string, any>;
  actions: {
    run: (...args: any[]) => any;
  };
};

type ApiError = {
  message: string;
  name: string;
  errorCode: string;
  stack?: string;
};

type InnerPromiseType<T> = T extends Promise<infer R> ? R : T;

type ActionResultOrError<T1 extends string | number | symbol, T2> = {[P in T1]?: T2} & {error?: ApiError};

type ArrayIfNeeded<C extends RequiredContext, K extends keyof C['data'], T> = C['data'][K] extends UIExecutionContext.UIModelNodeListType<any> ? T[] : T;

export namespace CasCustomerIssuesUI {
  /**
   * node for CasCustomerIssues
   */
  export type CasCustomerIssuesWithAssociation = CasCustomerIssues & {

    };
  export interface IControllerClient extends everest_cas_issue_integration_model_node_CasCustomerIssues.IControllerClient {}

  export type CasCustomerIssues = everest_cas_issue_integration_model_node_CasCustomerIssues.CasCustomerIssues;
  export type CreationFields = Pick<CasCustomerIssues, 'uuid' | 'externalId' | 'active' | 'body' | 'labels' | 'title'>;
  export type UniqueFields = Pick<CasCustomerIssues, 'id' | 'uuid'>;
  export type UniqueWhereInput = (RequireAtLeastOne<UniqueFields>) & Partial<CasCustomerIssues>;
  export type ReadReturnType<U extends string | number | symbol = keyof CasCustomerIssues> = ReadReturnTypeGeneric<CasCustomerIssues, U>;

  class ActionRequest<C extends RequiredContext, FuncReturnType> {
    constructor(private readonly context: C, private readonly partialPayload: Record<string, any>) {
    }

    private async __run__(payload: any) {
      return this.context.actions.run(payload);
    }

    payload<K extends keyof C['data']>(nodeAlias: K): Record<string, any> {
      return {[nodeAlias]: this.partialPayload};
    }

    run<K extends keyof C['data']>(nodeAlias: K): Promise<ActionResultOrError<K, ArrayIfNeeded<C, K, InnerPromiseType<FuncReturnType>>>> {
      const payload = this.payload(nodeAlias);
      return this.__run__(payload);
    }
  }

  type func_createIssue = typeof orig_createIssue;
  /** @deprecated use ```client.createIssue``` instead */
  export function createIssue<C extends RequiredContext>(context: C, args: { title: Parameters<func_createIssue>[1]; body: Parameters<func_createIssue>[2]; files: Parameters<func_createIssue>[3]; labels: Parameters<func_createIssue>[4] }): ActionRequest<C, ReturnType<func_createIssue>> {
    const partialPayload = {action: 'createIssue', data: args}

    return new ActionRequest<C, ReturnType<func_createIssue>>(context, partialPayload);
  }
  type func_deleteIssues = typeof orig_deleteIssues;
  /** @deprecated use ```client.deleteIssues``` instead */
  export function deleteIssues<C extends RequiredContext>(context: C, args: { id: Parameters<func_deleteIssues>[1] }): ActionRequest<C, ReturnType<func_deleteIssues>> {
    const partialPayload = {action: 'deleteIssues', data: args}

    return new ActionRequest<C, ReturnType<func_deleteIssues>>(context, partialPayload);
  }
  type func_updateIssues = typeof orig_updateIssues;
  /** @deprecated use ```client.updateIssues``` instead */
  export function updateIssues<C extends RequiredContext>(context: C, args: { id: Parameters<func_updateIssues>[1]; title: Parameters<func_updateIssues>[2]; body: Parameters<func_updateIssues>[3]; files: Parameters<func_updateIssues>[4]; labels: Parameters<func_updateIssues>[5]; status: Parameters<func_updateIssues>[6] }): ActionRequest<C, ReturnType<func_updateIssues>> {
    const partialPayload = {action: 'updateIssues', data: args}

    return new ActionRequest<C, ReturnType<func_updateIssues>>(context, partialPayload);
  }
  type func_fetchIssues = typeof orig_fetchIssues;
  /** @deprecated use ```client.fetchIssues``` instead */
  export function fetchIssues<C extends RequiredContext>(context: C, args: { issueNumbers: Parameters<func_fetchIssues>[1] }): ActionRequest<C, ReturnType<func_fetchIssues>> {
    const partialPayload = {action: 'fetchIssues', data: args}

    return new ActionRequest<C, ReturnType<func_fetchIssues>>(context, partialPayload);
  }
  type func_updateSingleIssue = typeof orig_updateSingleIssue;
  /** @deprecated use ```client.updateSingleIssue``` instead */
  export function updateSingleIssue<C extends RequiredContext>(context: C, args: { id: Parameters<func_updateSingleIssue>[1]; title: Parameters<func_updateSingleIssue>[2]; body: Parameters<func_updateSingleIssue>[3]; files: Parameters<func_updateSingleIssue>[4]; labels: Parameters<func_updateSingleIssue>[5]; status: Parameters<func_updateSingleIssue>[6] }): ActionRequest<C, ReturnType<func_updateSingleIssue>> {
    const partialPayload = {action: 'updateSingleIssue', data: args}

    return new ActionRequest<C, ReturnType<func_updateSingleIssue>>(context, partialPayload);
  }
  type func_offboardCustomer = typeof orig_offboardCustomer;
  /** @deprecated use ```client.offboardCustomer``` instead */
  export function offboardCustomer<C extends RequiredContext>(context: C, args: { tenantId: Parameters<func_offboardCustomer>[1] }): ActionRequest<C, ReturnType<func_offboardCustomer>> {
    const partialPayload = {action: 'offboardCustomer', data: args}

    return new ActionRequest<C, ReturnType<func_offboardCustomer>>(context, partialPayload);
  }
  type func_createGithubIssue = typeof orig_createGithubIssue;
  /** @deprecated use ```client.createGithubIssue``` instead */
  export function createGithubIssue<C extends RequiredContext>(context: C, args: { title: Parameters<func_createGithubIssue>[1]; body: Parameters<func_createGithubIssue>[2]; labels: Parameters<func_createGithubIssue>[3]; repositoryName?: Parameters<func_createGithubIssue>[4] }): ActionRequest<C, ReturnType<func_createGithubIssue>> {
    const partialPayload = {action: 'createGithubIssue', data: args}

    return new ActionRequest<C, ReturnType<func_createGithubIssue>>(context, partialPayload);
  }
  type func_fetchRecentlyUpdatedIssues = typeof orig_fetchRecentlyUpdatedIssues;
  /** @deprecated use ```client.fetchRecentlyUpdatedIssues``` instead */
  export function fetchRecentlyUpdatedIssues<C extends RequiredContext>(context: C, args: { hoursBack?: Parameters<func_fetchRecentlyUpdatedIssues>[1] }): ActionRequest<C, ReturnType<func_fetchRecentlyUpdatedIssues>> {
    const partialPayload = {action: 'fetchRecentlyUpdatedIssues', data: args}

    return new ActionRequest<C, ReturnType<func_fetchRecentlyUpdatedIssues>>(context, partialPayload);
  }
  /** @deprecated - use {@link MODEL_URN} instead */
  export const name = 'urn:evst:everest:cas/issue/integration:model/node:CasCustomerIssues';
  export const MODEL_URN = 'urn:evst:everest:cas/issue/integration:model/node:CasCustomerIssues';
  /** @deprecated - use {@link MODEL_URN} instead */
  export const MODEL_ID = 'everest.cas.issue.integration/CasCustomerIssuesModel.CasCustomerIssues';
  export const MODEL_UUID = '3114abb8-be81-487a-af9f-1619b7897b29';

  /** @deprecated use ```client``` instead */
  export function create<C extends RequiredContext, U extends keyof CasCustomerIssues>(context: C, input: CreationFields, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<CasCustomerIssues, U>>> {
    const convertedArgs = {data: input, fieldList: fieldlist, options: options} as Record<string, any>
    const partialPayload = {action: 'create', ...convertedArgs}

    return new ActionRequest<C, Promise<WriteReturnType<CasCustomerIssues, U>>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function createMany<C extends RequiredContext, U extends keyof CasCustomerIssues>(context: C, input: CreationFields[], fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<CasCustomerIssues, U>[]>> {
    const convertedArgs = {data: input, fieldList: fieldlist, options: options} as Record<string, any>
    const partialPayload = {action: 'createMany', ...convertedArgs}

    return new ActionRequest<C, Promise<WriteReturnType<CasCustomerIssues, U>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function Delete<C extends RequiredContext>(context: C, where: UniqueWhereInput, options?: undefined): ActionRequest<C, Promise<Partial<CasCustomerIssues>[]>> {
    const convertedArgs = {where: where, options} as Record<string, any>
    const partialPayload = {action: 'delete', ...convertedArgs}

    return new ActionRequest<C, Promise<Partial<CasCustomerIssues>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function deleteAll<C extends RequiredContext>(context: C): ActionRequest<C, Promise<Partial<CasCustomerIssues>[]>> {
    const convertedArgs = {data: {}} as Record<string, any>
    const partialPayload = {action: 'deleteAll', ...convertedArgs}

    return new ActionRequest<C, Promise<Partial<CasCustomerIssues>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function deleteMany<C extends RequiredContext>(context: C, where: Filter<CasCustomerIssuesWithAssociation>): ActionRequest<C, Promise<Partial<CasCustomerIssues>[]>>;
  /** @deprecated use ```client``` instead */
  export function deleteMany<C extends RequiredContext>(context: C, where: Partial<CasCustomerIssues>): ActionRequest<C, Promise<Partial<CasCustomerIssues>[]>>;
  /** @deprecated use ```client``` instead */
  export function deleteMany<C extends RequiredContext>(context: C, where: Filter<CasCustomerIssues> | Partial<CasCustomerIssues>, options?: undefined): ActionRequest<C, Promise<Partial<CasCustomerIssues>[]>> {
    const convertedArgs = {data: {where: where}, options} as Record<string, any>
    const partialPayload = {action: 'deleteMany', ...convertedArgs}

    return new ActionRequest<C, Promise<Partial<CasCustomerIssues>[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function query<C extends RequiredContext, U extends FieldSelector<CasCustomerIssuesWithAssociation>>(context: C, args: Omit<TypeSafeQueryArgType<CasCustomerIssuesWithAssociation>, 'draft'>, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<ReadReturnTypeWithAssociations<CasCustomerIssuesWithAssociation, U>[]>>;
  /** @deprecated use ```client``` instead */
  export function query<C extends RequiredContext, U extends keyof CasCustomerIssues, V extends string = 'ALL_FIELDS'>(context: C, args: Omit<TypeSafeQueryArgType<CasCustomerIssues>, 'draft'> | Omit<Omit<TypeSafeQueryArgType<CasCustomerIssues>, 'draft'>, 'where'> & { where?: Partial<CasCustomerIssues> }, fieldlist: ReadonlyArray<U | V> | 'ALL_FIELDS'): ActionRequest<C, Promise<ReadReturnType<V>[]>>;
  /** @deprecated use ```client``` instead */
  export function query<C extends RequiredContext>(context: C, args: Omit<TypeSafeQueryArgType<CasCustomerIssues>, 'draft'>, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS'): ActionRequest<C, Promise<unknown[]>> {
    const convertedArgs = {data: {...args, fieldList: fieldlist}} as Record<string, any>
    const partialPayload = {action: 'query', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function read<C extends RequiredContext, U extends FieldSelector<CasCustomerIssuesWithAssociation>>(context: C, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): ActionRequest<C, Promise<ReadReturnTypeWithAssociations<CasCustomerIssuesWithAssociation, U>>>;
  /** @deprecated use ```client``` instead */
  export function read<C extends RequiredContext, U extends keyof CasCustomerIssues>(context: C, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): ActionRequest<C, Promise<ReadReturnType<U>>>;
  /** @deprecated use ```client``` instead */
  export function read<C extends RequiredContext>(context: C, where: UniqueWhereInput, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): ActionRequest<C, Promise<unknown>> {
    const convertedArgs = {data: {where: where, fieldList: fieldlist}, options: options} as Record<string, any>
    const partialPayload = {action: 'read', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function update<C extends RequiredContext, U extends FieldSelector<CasCustomerIssuesWithAssociation>>(context: C, where: UniqueWhereInput, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<WriteReturnTypeWithAssociations<CasCustomerIssuesWithAssociation, U>>>;
  /** @deprecated use ```client``` instead */
  export function update<C extends RequiredContext, U extends keyof CasCustomerIssues>(context: C, where: Partial<CasCustomerIssues>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<WriteReturnType<CasCustomerIssues, U>>>;
  /** @deprecated use ```client``` instead */
  export function update<C extends RequiredContext>(context: C, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: undefined): ActionRequest<C, Promise<unknown>> {
    const convertedArgs = {where: where, data: data, fieldList: fieldlist, options} as Record<string, any>
    const partialPayload = {action: 'update', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function updateMany<C extends RequiredContext, U extends FieldSelector<CasCustomerIssuesWithAssociation>>(context: C, where: Filter<CasCustomerIssuesWithAssociation>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<WriteReturnTypeWithAssociations<CasCustomerIssuesWithAssociation, U>[]>>;
  /** @deprecated use ```client``` instead */
  export function updateMany<C extends RequiredContext, U extends keyof CasCustomerIssues>(context: C, where: Partial<CasCustomerIssues>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): ActionRequest<C, Promise<WriteReturnType<CasCustomerIssues, U>[]>>;
  /** @deprecated use ```client``` instead */
  export function updateMany<C extends RequiredContext>(context: C, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: undefined): ActionRequest<C, Promise<unknown[]>> {
    const convertedArgs = {data: {where: where, data: data, fieldList: fieldlist}, options} as Record<string, any>
    const partialPayload = {action: 'updateMany', ...convertedArgs}

    return new ActionRequest<C, Promise<unknown[]>>(context, partialPayload);
  }

  /** @deprecated use ```client``` instead */
  export function upsert<C extends RequiredContext, U extends keyof CasCustomerIssues & string>(context: C, data: Partial<CasCustomerIssues>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<CasCustomerIssues, U>>>;
  /** @deprecated use ```client``` instead */
  export function upsert<C extends RequiredContext, U extends keyof CasCustomerIssues & string>(context: C, where: Partial<CasCustomerIssues>, data: Partial<CasCustomerIssues>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<CasCustomerIssues, U>>>;
  /** @deprecated use ```client``` instead */
  export function upsert<C extends RequiredContext, U extends keyof CasCustomerIssues & string>(context: C, whereOrData: Partial<CasCustomerIssues>, dataOrFieldList?: Partial<CasCustomerIssues> | ReadonlyArray<U> | 'ALL_FIELDS', fieldlistOrOptions?: ReadonlyArray<U> | 'ALL_FIELDS' | Omit<UpsertOptionsArg, 'draft'>, maybeOptions?: Omit<UpsertOptionsArg, 'draft'>): ActionRequest<C, Promise<WriteReturnType<CasCustomerIssues, U>>> {
    const convertedArgs = {data: {where: whereOrData, data: dataOrFieldList, fieldList: fieldlistOrOptions}, options: maybeOptions} as Record<string, any>
    const partialPayload = {action: 'upsert', ...convertedArgs}

    return new ActionRequest<C, Promise<WriteReturnType<CasCustomerIssues, U>>>(context, partialPayload);
  }
  export namespace client {
    /** Creates a GitHub issue in the specified repository. */
    export declare function createIssue(args: { title: Parameters<func_createIssue>[1]; body: Parameters<func_createIssue>[2]; files: Parameters<func_createIssue>[3]; labels: Parameters<func_createIssue>[4] }): ReturnType<func_createIssue>
    /** some description */
    export declare function deleteIssues(args: { id: Parameters<func_deleteIssues>[1] }): ReturnType<func_deleteIssues>
    /** Fetches issues from the specified GitHub repository. */
    export declare function updateIssues(args: { id: Parameters<func_updateIssues>[1]; title: Parameters<func_updateIssues>[2]; body: Parameters<func_updateIssues>[3]; files: Parameters<func_updateIssues>[4]; labels: Parameters<func_updateIssues>[5]; status: Parameters<func_updateIssues>[6] }): ReturnType<func_updateIssues>
    /** Fetches details for specific GitHub issues using GraphQL. */
    export declare function fetchIssues(args: { issueNumbers: Parameters<func_fetchIssues>[1] }): ReturnType<func_fetchIssues>
    /** some description */
    export declare function updateSingleIssue(args: { id: Parameters<func_updateSingleIssue>[1]; title: Parameters<func_updateSingleIssue>[2]; body: Parameters<func_updateSingleIssue>[3]; files: Parameters<func_updateSingleIssue>[4]; labels: Parameters<func_updateSingleIssue>[5]; status: Parameters<func_updateSingleIssue>[6] }): ReturnType<func_updateSingleIssue>
    /** Offboarding of a customer */
    export declare function offboardCustomer(args: { tenantId: Parameters<func_offboardCustomer>[1] }): ReturnType<func_offboardCustomer>
    /** Creates a GitHub issue in a specified repository, or a default one. */
    export declare function createGithubIssue(args: { title: Parameters<func_createGithubIssue>[1]; body: Parameters<func_createGithubIssue>[2]; labels: Parameters<func_createGithubIssue>[3]; repositoryName?: Parameters<func_createGithubIssue>[4] }): ReturnType<func_createGithubIssue>
    /** Fetches GitHub issues that have been updated within the specified time period using GraphQL search. Much more efficient than fetching all issues. */
    export declare function fetchRecentlyUpdatedIssues(args: { hoursBack?: Parameters<func_fetchRecentlyUpdatedIssues>[1] }): ReturnType<func_fetchRecentlyUpdatedIssues>
    /** write a new object to the database. */
    export declare function create<U extends keyof CasCustomerIssues>(input: CreationFields, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): Promise<WriteReturnType<CasCustomerIssues, U>>

    /** write new objects to the database. */
    export declare function createMany<U extends keyof CasCustomerIssues>(input: CreationFields[], fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): Promise<WriteReturnType<CasCustomerIssues, U>[]>

    /** delete the object that is uniquely identified by the where condition. */
    export declare function Delete(where: UniqueWhereInput, options?: undefined): Promise<Partial<CasCustomerIssues>[]>

    /** delete all objects. */
    export declare function deleteAll(): Promise<Partial<CasCustomerIssues>[]>

    /** delete the objects that are identified by the where condition. */
    export declare function deleteMany(where: Filter<CasCustomerIssuesWithAssociation>): Promise<Partial<CasCustomerIssues>[]>;
    /** @deprecated use the alternative overloaded function */
    export declare function deleteMany(where: Partial<CasCustomerIssues>): Promise<Partial<CasCustomerIssues>[]>;
    export declare function deleteMany(where: Filter<CasCustomerIssues> | Partial<CasCustomerIssues>, options?: undefined): Promise<Partial<CasCustomerIssues>[]>

    /** returns a list of all objects matching the requirements. */
    export declare function query<U extends FieldSelector<CasCustomerIssuesWithAssociation>>(args: Omit<TypeSafeQueryArgType<CasCustomerIssuesWithAssociation>, 'draft'>, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<ReadReturnTypeWithAssociations<CasCustomerIssuesWithAssociation, U>[]>;
    /** @deprecated Use the alternative overloaded method for querying associations. */
    export declare function query<U extends keyof CasCustomerIssues, V extends string = 'ALL_FIELDS'>(args: Omit<TypeSafeQueryArgType<CasCustomerIssues>, 'draft'> | Omit<Omit<TypeSafeQueryArgType<CasCustomerIssues>, 'draft'>, 'where'> & { where?: Partial<CasCustomerIssues> }, fieldlist: ReadonlyArray<U | V> | 'ALL_FIELDS'): Promise<ReadReturnType<V>[]>;
    export declare function query(args: Omit<TypeSafeQueryArgType<CasCustomerIssues>, 'draft'>, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown[]>

    /** reads the object that is uniquely identified by the where condition. */
    export declare function read<U extends FieldSelector<CasCustomerIssuesWithAssociation>>(where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnTypeWithAssociations<CasCustomerIssuesWithAssociation, U>>;
    /** @deprecated Use the alternative overloaded method for reading associations. */
    export declare function read<U extends keyof CasCustomerIssues>(where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnType<U>>;
    export declare function read(where: UniqueWhereInput, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<unknown>

    /** writes modified values back to the database for a given node instance. */
    export declare function update<U extends FieldSelector<CasCustomerIssuesWithAssociation>>(where: UniqueWhereInput, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnTypeWithAssociations<CasCustomerIssuesWithAssociation, U>>;
    /** @deprecated Use the alternative overloaded method for writing with associations. */
    export declare function update<U extends keyof CasCustomerIssues>(where: Partial<CasCustomerIssues>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnType<CasCustomerIssues, U>>;
    export declare function update(where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: undefined): Promise<unknown>

    /** writes modified values back to the database for selected node instances. */
    export declare function updateMany<U extends FieldSelector<CasCustomerIssuesWithAssociation>>(where: Filter<CasCustomerIssuesWithAssociation>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnTypeWithAssociations<CasCustomerIssuesWithAssociation, U>[]>;
    /** @deprecated Use the alternative overloaded method for writing with associations. */
    export declare function updateMany<U extends keyof CasCustomerIssues>(where: Partial<CasCustomerIssues>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnType<CasCustomerIssues, U>[]>;
    export declare function updateMany(where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: undefined): Promise<unknown[]>

    /** creates or updates object identified by where. */
    export declare function upsert<U extends keyof CasCustomerIssues & string>(data: Partial<CasCustomerIssues>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<CasCustomerIssues, U>>;
    /** @deprecated use the overload without an explicit where clause */
    export declare function upsert<U extends keyof CasCustomerIssues & string>(where: Partial<CasCustomerIssues>, data: Partial<CasCustomerIssues>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<CasCustomerIssues, U>>;
    export declare function upsert<U extends keyof CasCustomerIssues & string>(whereOrData: Partial<CasCustomerIssues>, dataOrFieldList?: Partial<CasCustomerIssues> | ReadonlyArray<U> | 'ALL_FIELDS', fieldlistOrOptions?: ReadonlyArray<U> | 'ALL_FIELDS' | Omit<UpsertOptionsArg, 'draft'>, maybeOptions?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<CasCustomerIssues, U>>
  }
}
