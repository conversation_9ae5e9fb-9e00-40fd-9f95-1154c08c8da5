package everest.cas.issue.integration

node CasCustomerIssues {
	description: 'node for CasCustomerIssues'

	field body Text {
		persisted
		label: 'Body'
	}

	field labels JSON {
		persisted
		label: 'Labels'
	}

	field title Text {
		persisted
		label: 'Title'
	}
	action createIssue(title: Text, body: Text, files: Array<JSON>, labels: JSON): JSON
	action deleteIssues(id: Text): JSON
	action updateIssues(id: Text, title: Text, body: Text, files: Array<JSON>, labels: JSON, status: Text): JSON
	query action fetchIssues(issueNumbers: Array<Number<Int>>): JSON
	action updateSingleIssue(id: Text, title: Text, body: Text, files: Array<JSON>, labels: JSON, status: Text): JSON
	action offboardCustomer(tenantId: Text): Array<JSON>
	action createGithubIssue(title: Text, body: Text, labels: Array<Text>, optional repositoryName: Text): JSON
	query action fetchRecentlyUpdatedIssues(optional hoursBack: Number<Int>): JSO<PERSON>
}
