/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// ******************************************************************************
// Generated Everest Types
// ******************************************************************************

import type { Decimal, RoundingMode } from '@everestsystems/decimal';
import type {
  ArgType,
  Association,
  AssociationManagementArgs,
  AsyncResult,
  Controller,
  ControllerClientProvider,
  CreateOptionsArg,
  DataWithMetadata,
  DeleteOptionsArg,
  FieldSelector,
  Filter,
  flattenFieldSelector,
  GraphInstanceEntry,
  KernelRequestOptions,
  NonAssociationFields,
  Optional,
  PurgeArgs,
  ReadOptionsArg,
  ReadReturnType as ReadReturnTypeGeneric,
  ReadReturnTypeWithAssociations,
  RequireAtLeastOne,
  SemanticSearchArg,
  SemanticSearchRequestOptions,
  TypeSafeQueryArgType,
  UpdateManyOptionsArg,
  UpdateOptionsArg,
  UpsertOptionsArg,
  WriteReturnType,
  WriteReturnTypeWithAssociations,
} from '@everestsystems/content-core';
import type { EvstJSON as everest_appserver_primitive_JSON } from "@pkg/everest.appserver/types/primitives/JSON";
import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstExternalId as everest_appserver_primitive_ExternalId } from "@pkg/everest.appserver/types/primitives/ExternalId";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { EvstUUID as everest_appserver_primitive_UUID } from "@pkg/everest.appserver/types/primitives/UUID";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { EvstUserEmail as everest_appserver_usermgmt_primitive_user_UserEmail } from "@pkg/everest.appserver.usermgmt/types/primitives/user/UserEmail";
import type { EvstDateTime as everest_appserver_primitive_DateTime } from "@pkg/everest.appserver/types/primitives/DateTime";

/**
 * Types for CasCustomerIssues
 *
 * @everest-node-metadata='{"visibility":"public"}'
 */
export namespace CasCustomerIssues {
  export type CreationFields = Pick<CasCustomerIssues, 'uuid' | 'externalId' | 'active' | 'body' | 'labels' | 'title'>;
  export type UniqueFields = Pick<CasCustomerIssues, 'id' | 'uuid'>;
  export type UniqueWhereInput = (RequireAtLeastOne<UniqueFields>) & Partial<CasCustomerIssues>;
  export type ReadReturnType<U extends string | number | symbol = keyof CasCustomerIssues> = ReadReturnTypeGeneric<CasCustomerIssues, U>;

  export interface IControllerClient extends Omit<Controller<CasCustomerIssues>, 'all' | 'getHistory' | 'lookup' | 'purge' | 'queryWithMetadata' | 'semanticSearch' | 'upsertMany'> {
    /** Creates a GitHub issue in the specified repository. */
    createIssue(title: everest_appserver_primitive_Text, body: everest_appserver_primitive_Text, files: everest_appserver_primitive_JSON[], labels: everest_appserver_primitive_JSON): Promise<everest_appserver_primitive_JSON>;
    /** some description */
    deleteIssues(id: everest_appserver_primitive_ExternalId): Promise<everest_appserver_primitive_JSON>;
    /** Fetches issues from the specified GitHub repository. */
    updateIssues(id: everest_appserver_primitive_ExternalId, title: everest_appserver_primitive_Text, body: everest_appserver_primitive_Text, files: everest_appserver_primitive_JSON[], labels: everest_appserver_primitive_JSON, status: everest_appserver_primitive_Text): Promise<everest_appserver_primitive_JSON>;
    /** Fetches details for specific GitHub issues using GraphQL. */
    fetchIssues(issueNumbers: everest_appserver_primitive_Number[]): Promise<everest_appserver_primitive_JSON>;
    /** some description */
    updateSingleIssue(id: everest_appserver_primitive_ExternalId, title: everest_appserver_primitive_Text, body: everest_appserver_primitive_Text, files: everest_appserver_primitive_JSON[], labels: everest_appserver_primitive_JSON, status: everest_appserver_primitive_Text): Promise<everest_appserver_primitive_JSON>;
    /** Offboarding of a customer */
    offboardCustomer(tenantId: everest_appserver_primitive_Text): Promise<everest_appserver_primitive_JSON[]>;
    /** Creates a GitHub issue in a specified repository, or a default one. */
    createGithubIssue(title: everest_appserver_primitive_Text, body: everest_appserver_primitive_Text, labels: everest_appserver_primitive_Text[], repositoryName?: everest_appserver_primitive_Text): Promise<everest_appserver_primitive_JSON>;
    /** Fetches GitHub issues that have been updated within the specified time period using GraphQL search. Much more efficient than fetching all issues. */
    fetchRecentlyUpdatedIssues(hoursBack?: everest_appserver_primitive_Number): Promise<everest_appserver_primitive_JSON>;
  }

  /**
   * node for CasCustomerIssues
   */
  export type CasCustomerIssues = {
    /** Database generated identifier of this record. */
    id: number;
    /** Database generated version of this record. */
    version?: number;
    uuid?: everest_appserver_primitive_UUID | null;
    /**
     * ID of the system from which this record was sourced
     */
    externalId?: everest_appserver_primitive_Text | null;
    /**
     * Indicates whether this record is usable for business users
     */
    active?: everest_appserver_primitive_TrueFalse | null;
    /**
     * User that created this record
     */
    createdBy: everest_appserver_usermgmt_primitive_user_UserEmail;
    /**
     * Date/time when the record was created
     */
    createdDate: everest_appserver_primitive_DateTime;
    /**
     * User that last modified the record
     */
    lastModifiedBy: everest_appserver_usermgmt_primitive_user_UserEmail;
    /**
     * Date/time of the last modification to the record
     */
    lastModifiedDate: everest_appserver_primitive_DateTime;
    body?: everest_appserver_primitive_Text | null;
    labels?: everest_appserver_primitive_JSON | null;
    title?: everest_appserver_primitive_Text | null;
    };
  /**
   * node for CasCustomerIssues
   */
  export type CasCustomerIssuesWithAssociation = CasCustomerIssues & {

    };
  /** @deprecated - use {@link MODEL_URN} instead */
  export const name = 'urn:evst:everest:cas/issue/integration:model/node:CasCustomerIssues';
  export const MODEL_URN = 'urn:evst:everest:cas/issue/integration:model/node:CasCustomerIssues';
  /** @deprecated - use {@link MODEL_URN} instead */
  export const MODEL_ID = 'everest.cas.issue.integration/CasCustomerIssuesModel.CasCustomerIssues';
  export const MODEL_UUID = '3114abb8-be81-487a-af9f-1619b7897b29';

  /** @return a model controller instance for CasCustomerIssues. */
  export function client(env: ControllerClientProvider) {
    return env.controllerClient<CasCustomerIssues.IControllerClient>(MODEL_URN);
  }

  /** write a new object to the database. */
  export async function create<U extends keyof CasCustomerIssues>(env: ControllerClientProvider, input: CreationFields, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): Promise<WriteReturnType<CasCustomerIssues, U>> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).create(input, fields, options);
  }

  /** write new objects to the database. */
  export async function createMany<U extends keyof CasCustomerIssues>(env: ControllerClientProvider, input: CreationFields[], fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<CreateOptionsArg, 'draft'>): Promise<WriteReturnType<CasCustomerIssues, U>[]> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).createMany(input, fields, options);
  }

  /** delete the object that is uniquely identified by the where condition. */
  export async function Delete(env: ControllerClientProvider, where: UniqueWhereInput): Promise<Partial<CasCustomerIssues>[]> {
    return (await client(env)).delete(where);
  }

  /** delete all objects. */
  export async function deleteAll(env: ControllerClientProvider): Promise<Partial<CasCustomerIssues>[]> {
    return (await client(env)).deleteAll();
  }

  /** delete the objects that are identified by the where condition. */
  export async function deleteMany(env: ControllerClientProvider, where: Filter<CasCustomerIssuesWithAssociation>): Promise<Partial<CasCustomerIssues>[]>;
  /** @deprecated use the alternative overloaded function */
  export async function deleteMany(env: ControllerClientProvider, where: Partial<CasCustomerIssues>): Promise<Partial<CasCustomerIssues>[]>;
  export async function deleteMany(env: ControllerClientProvider, where: Filter<CasCustomerIssues> | Partial<CasCustomerIssues>): Promise<Partial<CasCustomerIssues>[]> {
    return (await client(env)).deleteMany(where as Filter<CasCustomerIssues>);
  }

  /** returns a list of all objects matching the requirements. */
  export async function query<U extends FieldSelector<CasCustomerIssuesWithAssociation>>(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<CasCustomerIssuesWithAssociation>, 'draft'>, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<ReadReturnTypeWithAssociations<CasCustomerIssuesWithAssociation, U>[]>;
  /** @deprecated Use the alternative overloaded method for querying associations. */
  export async function query<U extends keyof CasCustomerIssues, V extends string = 'ALL_FIELDS'>(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<CasCustomerIssues>, 'draft'> | Omit<Omit<TypeSafeQueryArgType<CasCustomerIssues>, 'draft'>, 'where'> & { where?: Partial<CasCustomerIssues> }, fieldlist: ReadonlyArray<U | V> | 'ALL_FIELDS'): Promise<ReadReturnType<V>[]>;
  export async function query(env: ControllerClientProvider, args: Omit<TypeSafeQueryArgType<CasCustomerIssues>, 'draft'>, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown[]> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).query(args, fields as ReadonlyArray<string>);
  }

  /** reads the object that is uniquely identified by the where condition. */
  export async function read<U extends FieldSelector<CasCustomerIssuesWithAssociation>>(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnTypeWithAssociations<CasCustomerIssuesWithAssociation, U>>;
  /** @deprecated Use the alternative overloaded method for reading associations. */
  export async function read<U extends keyof CasCustomerIssues>(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<U> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<ReadReturnType<U>>;
  export async function read(env: ControllerClientProvider, where: UniqueWhereInput, fieldlist: ReadonlyArray<unknown> | 'ALL_FIELDS', options?: number | Date | Omit<ReadOptionsArg, 'draft'>): Promise<unknown> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).read(where, fields as ReadonlyArray<keyof CasCustomerIssues>, options);
  }

  /** writes modified values back to the database for a given node instance. */
  export async function update<U extends FieldSelector<CasCustomerIssuesWithAssociation>>(env: ControllerClientProvider, where: UniqueWhereInput, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnTypeWithAssociations<CasCustomerIssuesWithAssociation, U>>;
  /** @deprecated Use the alternative overloaded method for writing with associations. */
  export async function update<U extends keyof CasCustomerIssues>(env: ControllerClientProvider, where: Partial<CasCustomerIssues>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnType<CasCustomerIssues, U>>;
  export async function update(env: ControllerClientProvider, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).update(where, data, fields as ReadonlyArray<keyof CasCustomerIssues>);
  }

  /** writes modified values back to the database for selected node instances. */
  export async function updateMany<U extends FieldSelector<CasCustomerIssuesWithAssociation>>(env: ControllerClientProvider, where: Filter<CasCustomerIssuesWithAssociation>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnTypeWithAssociations<CasCustomerIssuesWithAssociation, U>[]>;
  /** @deprecated Use the alternative overloaded method for writing with associations. */
  export async function updateMany<U extends keyof CasCustomerIssues>(env: ControllerClientProvider, where: Partial<CasCustomerIssues>, data: Partial<CreationFields>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS'): Promise<WriteReturnType<CasCustomerIssues, U>[]>;
  export async function updateMany(env: ControllerClientProvider, where: Partial<unknown>, data: Partial<unknown>, fieldlist?: ReadonlyArray<unknown> | 'ALL_FIELDS'): Promise<unknown[]> {
    const fields = fieldlist === 'ALL_FIELDS' ? [] : fieldlist;
    return (await client(env)).updateMany(where, data, fields as ReadonlyArray<keyof CasCustomerIssues>);
  }

  /** creates or updates object identified by where. */
  export async function upsert<U extends keyof CasCustomerIssues & string>(env: ControllerClientProvider, data: Partial<CasCustomerIssues>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<CasCustomerIssues, U>>;
  /** @deprecated use the overload without an explicit where clause */
  export async function upsert<U extends keyof CasCustomerIssues & string>(env: ControllerClientProvider, where: Partial<CasCustomerIssues>, data: Partial<CasCustomerIssues>, fieldlist?: ReadonlyArray<U> | 'ALL_FIELDS', options?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<CasCustomerIssues, U>>;
  export async function upsert<U extends keyof CasCustomerIssues & string>(env: ControllerClientProvider, whereOrData: Partial<CasCustomerIssues>, dataOrFieldList?: Partial<CasCustomerIssues> | ReadonlyArray<U> | 'ALL_FIELDS', fieldlistOrOptions?: ReadonlyArray<U> | 'ALL_FIELDS' | Omit<UpsertOptionsArg, 'draft'>, maybeOptions?: Omit<UpsertOptionsArg, 'draft'>): Promise<WriteReturnType<CasCustomerIssues, U>> {
    return (await client(env)).upsert(whereOrData, dataOrFieldList, fieldlistOrOptions, maybeOptions);
  }
}
