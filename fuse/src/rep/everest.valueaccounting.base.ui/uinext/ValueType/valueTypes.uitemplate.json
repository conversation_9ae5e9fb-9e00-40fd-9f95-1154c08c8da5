{"version": 3, "uimodel": {"presentation": {"urn": "urn:evst:everest:valueaccounting/base/ui:presentation:uinext/ValueType/valueTypes", "parameters": {"mode": "view"}}}, "uiview": {"config": {"stretch": true, "allowRefreshData": true, "autoRefreshData": true}, "i18n": ["everest.valueaccounting.base/valueAccounting"], "title": "{{value.types}}", "header": {"wrapWithGrid": true, "margin": {}, "content": {"title": "{{value.types}}"}}, "actions": {"direction": "vertical", "content": [{"variant": "primary", "label": "{{edit}}", "presentationAction": "@action:revise"}, {"variant": "primary", "label": "{{save}}", "presentationAction": "@action:save", "notificationMessage": {"loading": "{{save.loading.message}}", "success": "{{save.success.message}}"}}, {"variant": "secondary", "label": "{{cancel}}", "presentationAction": "@action:cancel"}]}, "sections": {"content": [{"component": "Table", "section": {"title": "{{value.types}}", "grid": {"start": "1", "size": "12"}}, "props": {"presentationDataSet": "@dataSet:valueTypesTable", "addRowsOnEmpty": false}}]}}}