import { aiBaseModalSteps } from '@pkg/everest.aispecify/test/e2e/steps/AIBaseModalSteps.evertest';
import { AIProjectDetailsPage } from '@pkg/everest.aispecify/test/e2e/steps/aiProjectDetails/aiProjectDetailsPage.evertest';
import { ELEMENT_STATE } from '@pkg/everest.base.test/test/e2e/enums/enums.evertest';
import { baseSteps } from '@pkg/everest.base.test/test/e2e/steps/BaseSteps.evertest';
import {
  LONG_TIME,
  REFRESH_TIME,
} from '@pkg/everest.base.test/test/e2e/utils/constants/constant.evertest';
import {
  convertAllVariableInTableToValueV2,
  convertVariablesInStringV2,
} from '@pkg/everest.base.test/test/e2e/utils/data/convertDataUtils.evertest';
import {
  getDataFolderPath,
  saveValueToSpecStoreKey,
} from '@pkg/everest.base.test/test/e2e/utils/utilities.evertest';
import { strict } from 'assert';
import { Step, ContinueOnFailure, Table } from 'gauge-ts';
import * as path from 'path';
import * as fs from 'fs';
import { expect } from '@playwright/test';

let aiProjectDetailsPage: AIProjectDetailsPage;
export default class AIProjectDetailsSteps {
  @Step('On the AI Project Details page, go back to the Dashboard page')
  async goBackToDashboard() {
    aiProjectDetailsPage = new AIProjectDetailsPage();
    await aiProjectDetailsPage.backToDashboardBtn.click();
    await baseSteps.waitForPageLoadedCompletely();
  }

  @ContinueOnFailure()
  @Step('On the AI Project Details page, check the page title is <title>')
  public async checkPageTitle(expectedTitle: string) {
    aiProjectDetailsPage = new AIProjectDetailsPage();
    expectedTitle = await convertVariablesInStringV2(expectedTitle);
    strict(
      await aiProjectDetailsPage.projectManagementHeader
        .getByText(expectedTitle)
        .isVisible(),
      `We expect text "${expectedTitle}" to be visible on the header, but it was not`
    );
  }

  @ContinueOnFailure()
  @Step('On the AI Project Details page, check the Project title is <title>')
  public async checkProjectTitle(expectedTitle: string) {
    aiProjectDetailsPage = new AIProjectDetailsPage();
    expectedTitle = await convertVariablesInStringV2(expectedTitle);
    strict(
      await aiProjectDetailsPage.projectTitle
        .getByText(new RegExp(expectedTitle))
        .isVisible(),
      `We expect text "${expectedTitle}" to be visible on the header, but it was not`
    );
  }

  @ContinueOnFailure()
  @Step(
    'On the AI Project Details page, check there is generated Project title'
  )
  public async checkProjectTitleIsGenerated() {
    aiProjectDetailsPage = new AIProjectDetailsPage();
    const actualTitle = await aiProjectDetailsPage.projectTitle.textContent();

    strict(
      actualTitle !== null && actualTitle.trim().length > 0,
      `Expected project title to be generated, but it was empty or missing`
    );
  }

  @Step('On the AI Project Details page, click on the Requirements tab')
  public async clickRequirementsTab() {
    aiProjectDetailsPage = new AIProjectDetailsPage();
    await aiProjectDetailsPage.requirementsTab.waitFor({
      state: ELEMENT_STATE.VISIBLE,
    });
    try {
      await aiProjectDetailsPage.requirementsTab.click();
    } catch (error) {
      throw new Error(`Failed to click on the Requirements tab: ${error}`);
    }
  }

  @ContinueOnFailure()
  @Step(
    'On the AI Project Details page, check there is some requirements generated'
  )
  public async checkRequirementsGenerated() {
    aiProjectDetailsPage = new AIProjectDetailsPage();
    await aiProjectDetailsPage.requirementsTab.waitFor({
      state: ELEMENT_STATE.VISIBLE,
    });
    strict(
      await aiProjectDetailsPage.requirementCard.first().isVisible(),
      `We expect the requirement card to be visible, but it was not`
    );
    strict(
      (await aiProjectDetailsPage.requirementCard.count()) > 0,
      `We expect at least one requirement card to be visible, but it was not`
    );
  }

  @ContinueOnFailure()
  @Step(
    'On the AI Project Details page, check the requirements with data below <condition> visible <table>'
  )
  public async checkRequirementsVisible(condition: string, table: Table) {
    aiProjectDetailsPage = new AIProjectDetailsPage();
    const requirements = await aiProjectDetailsPage.requirementCard.all();

    if (requirements.length === 0) {
      throw new Error('No requirements found on the page');
    }

    // Convert the table to a format we can work with
    table = await convertAllVariableInTableToValueV2(table);
    const isVisible = condition.toLowerCase() === 'is';

    for (const row of table.getTableRows()) {
      const [title, description, priority, type] = row.getCellValues();

      let matchFound = false;

      for (const req of requirements) {
        const textContent = await req.textContent();
        if (
          textContent?.includes(title) &&
          textContent.includes(description) &&
          textContent.includes(priority) &&
          textContent.includes(type)
        ) {
          matchFound = true;
          break;
        }
      }

      if (isVisible) {
        strict(
          matchFound,
          `Expected requirement with title "${title}" to be visible, but it was not found.`
        );
      } else {
        strict(
          !matchFound,
          `Expected requirement with title "${title}" to not be visible, but it was found.`
        );
      }
    }
  }

  @ContinueOnFailure()
  @Step(
    'On the AI Project Details page, check the number of Requirements is more than <number>'
  )
  public async checkNumberOfRequirementsMoreThanGiven(number: string) {
    aiProjectDetailsPage = new AIProjectDetailsPage();
    number = await convertVariablesInStringV2(number);
    // convert the number string to a number
    const total = eval(number);
    strict(
      (await aiProjectDetailsPage.requirementCard.count()) > total,
      `We expect the number of requirements to be more than ${total}, but it was not`
    );
  }

  @ContinueOnFailure()
  @Step(
    'On the AI Project Details page, check for the requirement card with title <title> is visible'
  )
  public async checkRequirementCardVisible(title: string) {
    aiProjectDetailsPage = new AIProjectDetailsPage();
    title = await convertVariablesInStringV2(title);
    // Extract the name after the colon
    const match = title.match(/:\s*(.+)$/);
    const name = match ? match[1].trim() : null;
    if (!name) {
      throw new Error(
        `Could not extract requirement name from title: "${title}". Please ensure the title contains ': <requirement name>'.`
      );
    }
    const requirementCard = aiProjectDetailsPage.requirementCard
      .filter({ hasText: name })
      .first();
    await requirementCard.waitFor({ state: ELEMENT_STATE.VISIBLE });
    strict(
      await requirementCard.isVisible(),
      `We expect the requirement card with title "${name}" to be visible, but it was not`
    );
  }

  @Step('On the AI Project Details page, select the first requirement card')
  public async clickFirstRequirementCard() {
    aiProjectDetailsPage = new AIProjectDetailsPage();
    await aiProjectDetailsPage.requirementCard
      .first()
      .waitFor({ state: ELEMENT_STATE.VISIBLE });
    try {
      await aiProjectDetailsPage.requirementCard.first().click();
    } catch (error) {
      throw new Error(
        `Failed to click on the first requirement card: ${error}`
      );
    }
  }

  @Step(
    'On the AI Project Details page, click on the Manually Add button on tab header and wait for the modal to open'
  )
  public async clickManualAddButton() {
    aiProjectDetailsPage = new AIProjectDetailsPage();
    await aiProjectDetailsPage.manualAddBtn.waitFor({
      state: ELEMENT_STATE.VISIBLE,
    });
    try {
      await aiProjectDetailsPage.manualAddBtn.click();
      await aiBaseModalSteps.waitForAIModalOpenedAndLoaded();
    } catch (error) {
      throw new Error(`Failed to click on the Manual Add button: ${error}`);
    }
  }

  @Step(
    'On the AI Project Details page, click on the Improve button on tab header'
  )
  public async clickImproveButton() {
    aiProjectDetailsPage = new AIProjectDetailsPage();
    await aiProjectDetailsPage.improveBtn.waitFor({
      state: ELEMENT_STATE.VISIBLE,
    });
    try {
      await aiProjectDetailsPage.improveBtn.click();
      await aiBaseModalSteps.waitForAIModalOpenedAndLoaded();
    } catch (error) {
      throw new Error(`Failed to click on the Improve button: ${error}`);
    }
  }

  @Step(
    'On the AI Project Details page, select <option> option from the Generate button on tab header'
  )
  public async selectOptionFromGenerateButton(option: string) {
    aiProjectDetailsPage = new AIProjectDetailsPage();
    await aiProjectDetailsPage.generateBtn.waitFor({
      state: ELEMENT_STATE.VISIBLE,
    });
    const validOptions = ['generate more', 'with prompt'];
    if (!validOptions.includes(option.toLowerCase())) {
      throw new Error(
        `Invalid option: ${option}. Valid options are: ${validOptions.join(
          ', '
        )}`
      );
    }
    try {
      await aiProjectDetailsPage.generateBtn.click();
      switch (option.toLowerCase()) {
        case 'generate more':
          await aiProjectDetailsPage.requirementSection
            .getByRole('button', { name: option })
            .click();
          await aiBaseModalSteps.waitForAIModalOpenedAndLoaded();
          break;
        case 'with prompt':
          await aiProjectDetailsPage.requirementSection
            .getByRole('button', { name: option })
            .click();
          await aiBaseModalSteps.waitForAIModalOpenedAndLoaded();
          break;
        default:
          throw new Error(`Unknown option: ${option}`);
      }
    } catch (error) {
      throw new Error(`Failed to click on the Generate button: ${error}`);
    }
  }

  @Step(
    'On the AI Project Details page, click on the Edit Requirement button on the first requirement card'
  )
  public async clickEditRequirementButton() {
    aiProjectDetailsPage = new AIProjectDetailsPage();
    try {
      await aiProjectDetailsPage.editRequirementBtn.first().click();
      await aiBaseModalSteps.waitForAIModalOpenedAndLoaded();
    } catch (error) {
      throw new Error(
        `Failed to click on the Edit Requirement button: ${error}`
      );
    }
  }

  @Step(
    'On the AI Project Details page, delete Requirements on until there is <number> Requirements left'
  )
  public async deleteRequirementUntilLeft(num: string) {
    aiProjectDetailsPage = new AIProjectDetailsPage();
    num = await convertVariablesInStringV2(num);
    try {
      while (
        (await aiProjectDetailsPage.requirementCard.count()) > parseInt(num, 10)
      ) {
        const initialCount = await aiProjectDetailsPage.requirementCard.count();

        await aiProjectDetailsPage.deleteRequirementBtn.first().click();

        // Wait for the count to decrease
        await expect
          .poll(
            async () => await aiProjectDetailsPage.requirementCard.count(),
            {
              message: 'Waiting for a requirement card to be removed',
            }
          )
          .toBeLessThan(initialCount);

        // Wait 2 second before deleting the next one
        await new Promise((res) => setTimeout(res, REFRESH_TIME));
      }
    } catch (error) {
      throw new Error(
        `Failed to click on the Delete Requirement button: ${
          error instanceof Error ? error.message : error
        }`
      );
    }
  }

  @Step(
    'On the AI Project Details page, get the number of Requirements and save it to reference <refName>'
  )
  public async checkNumberOfRequirements(refName: string) {
    aiProjectDetailsPage = new AIProjectDetailsPage();
    const numberOfRequirements =
      await aiProjectDetailsPage.requirementCard.count();
    // Save the number of requirements to the reference
    saveValueToSpecStoreKey(refName, numberOfRequirements);
  }

  @Step(
    'On the AI Project Details page, check the number of Requirements is <number> and save it to reference <refName>'
  )
  public async checkNumberOfRequirementsIs(number: string, refName: string) {
    number = await convertVariablesInStringV2(number);
    // Evaluate the number string, supporting expressions like "4 - 1"
    const total = parseInt(number, 10);
    aiProjectDetailsPage = new AIProjectDetailsPage();
    await aiProjectDetailsPage.requirementCard.first().waitFor({
      state: ELEMENT_STATE.VISIBLE,
    });
    const actualNumber = await aiProjectDetailsPage.requirementCard.count();
    strict(
      actualNumber === total,
      `We expect the number of requirements to be ${total}, but it was ${actualNumber}`
    );
    // Save the actual number of requirements to the reference
    saveValueToSpecStoreKey(refName, actualNumber);
  }

  @Step(
    'On the AI Project Details page, click on the Review Requirements button on the tab header and wait for the modal to open'
  )
  public async clickReviewRequirementsButton() {
    aiProjectDetailsPage = new AIProjectDetailsPage();
    await aiProjectDetailsPage.reviewRequirementBtn.waitFor({
      state: ELEMENT_STATE.VISIBLE,
      timeout: LONG_TIME,
    });
    try {
      await aiProjectDetailsPage.reviewRequirementBtn.click();
      await aiBaseModalSteps.waitForAIModalOpenedAndLoaded();
    } catch (error) {
      throw new Error(
        `Failed to click on the Review Requirements button: ${error}`
      );
    }
  }

  @Step(
    'On the AI Project Details page, click on Export button and save the file name with key <key>'
  )
  public async exportProject(key: string) {
    aiProjectDetailsPage = new AIProjectDetailsPage();
    const [download] = await Promise.all([
      aiProjectDetailsPage.getPage().waitForEvent('download'),
      aiProjectDetailsPage.exportBtn.click(),
    ]);

    const targetFolderLocation = getDataFolderPath();
    await download.saveAs(
      `${targetFolderLocation}/${download.suggestedFilename()}`
    );
    saveValueToSpecStoreKey(key, download.suggestedFilename());
  }

  @Step(
    'Check that the JSON file is downloaded successfully with the correct file name <fileName>'
  )
  public async verifyJsonFileIsDownloaded(fileName: string) {
    fileName = await convertVariablesInStringV2(fileName);
    const folderLocation = getDataFolderPath();
    const filePath = path.join(folderLocation, fileName);
    const fileExists = fs.existsSync(filePath);
    if (!fileExists) {
      throw new Error(
        `File ${fileName} does not exist in the folder ${folderLocation}`
      );
    }
  }
}
