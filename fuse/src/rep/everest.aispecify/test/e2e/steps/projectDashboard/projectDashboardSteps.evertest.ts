import { ProjectDashboardPage } from '@pkg/everest.aispecify/test/e2e/steps/projectDashboard/projectDashboardPage.evertest';
import { ELEMENT_STATE } from '@pkg/everest.base.test/test/e2e/enums/enums.evertest';
import { baseSteps } from '@pkg/everest.base.test/test/e2e/steps/BaseSteps.evertest';
import { convertVariablesInStringV2 } from '@pkg/everest.base.test/test/e2e/utils/data/convertDataUtils.evertest';
import { logger } from '@pkg/everest.base.test/test/e2e/utils/logger.evertest';
import {
  getDataFolderPath,
  saveValueToSpecStoreKey,
} from '@pkg/everest.base.test/test/e2e/utils/utilities.evertest';
import { strict, strictEqual } from 'assert';
import { ContinueOnFailure, Step } from 'gauge-ts';

let projectDashboardPage: ProjectDashboardPage;

export default class ProjectDashboardSteps {
  @ContinueOnFailure()
  @Step('On the Project Dashboard page, check the page title is <title>')
  public async checkPageTitle(expectedTitle: string) {
    projectDashboardPage = new ProjectDashboardPage();
    expectedTitle = await convertVariablesInStringV2(expectedTitle);
    strict(
      await projectDashboardPage.projectManagementHeader
        .getByText(expectedTitle)
        .isVisible(),
      `We expect text "${expectedTitle}" to be visible on the header, but it was not`
    );
  }

  @ContinueOnFailure()
  @Step('On the Project Dashboard page, check that the project card is visible')
  public async checkProjectCardIsVisible() {
    projectDashboardPage = new ProjectDashboardPage();
    await projectDashboardPage.projectCard.waitFor({
      state: ELEMENT_STATE.VISIBLE,
    });
    const isVisible = await projectDashboardPage.projectCard.isVisible();
    strictEqual(isVisible, true, 'Project card is not visible');
  }

  @Step('On the Project Dashboard page, click on the Project with name <name>')
  public async clickOnProjectCard(name: string) {
    projectDashboardPage = new ProjectDashboardPage();
    const cardLocator = projectDashboardPage.projectCardLocatorByName(name);
    await cardLocator.click().catch(() => {
      throw new Error(`Cannot click Project card with name "${name}"`);
    });
    await projectDashboardPage.waitForLoadingSpinnerToAppear();
    await projectDashboardPage.waitForLoadingSpinnerToDisappear();
    await baseSteps.waitForPageLoadedCompletely();
  }

  @Step('On the Project Dashboard page, click on the first Project card')
  public async clickOnFirstProjectCard() {
    projectDashboardPage = new ProjectDashboardPage();
    await projectDashboardPage.projectCard
      .first()
      .click()
      .catch(() => {
        throw new Error('Cannot click the first Project card');
      });
    await projectDashboardPage.waitForLoadingSpinnerToAppear();
    await projectDashboardPage.waitForLoadingSpinnerToDisappear();
    await baseSteps.waitForPageLoadedCompletely();
  }

  @Step('On the Project Dashboard page, click on <button> button on the header')
  public async clickOnButtonOnHeader(name: string) {
    projectDashboardPage = new ProjectDashboardPage();
    const buttonValues = Object.values(projectDashboardPage.buttonOnHeader);
    if (!buttonValues.includes(name)) {
      throw new Error(
        `Button with name "${name}" not found. Available buttons: ${buttonValues.join(
          ', '
        )}`
      );
    }

    // Find and click the appropriate button based on the name
    const buttonLocator = projectDashboardPage.getHeaderButtonByName(name);
    await buttonLocator.click().catch(() => {
      throw new Error(`Cannot click "${name}" button on the header`);
    });
  }

  @Step('On the Project Dashboard page, delete all projects if any exist')
  public async deleteAllProjectIfAny() {
    projectDashboardPage = new ProjectDashboardPage();
    const projectCardNumber = await projectDashboardPage.projectCard.count();
    if (projectCardNumber === 0) {
      logger.info('No project card found. Skipping deletion.');
      return;
    }
    logger.info(
      `Found "${projectCardNumber}" project card(s). Deleting them all.`
    );
    try {
      for (let i = 0; i < projectCardNumber; i++) {
        const card = projectDashboardPage.projectCard.nth(0); // alway delete the first card
        await card.hover();
        await card
          .locator(projectDashboardPage.buttonOnCardByLabel('delete'))
          .click();
        await baseSteps.waitForPageLoadedCompletely();
      }
    } catch (error) {
      throw new Error(`Cannot delete all projects. Error: ${error}`);
    }
  }

  @Step(
    'On the Project Dashboard page, download project with name <name> and save the file name with key <key>'
  )
  public async downloadProject(name: string, key: string) {
    projectDashboardPage = new ProjectDashboardPage();
    name = await convertVariablesInStringV2(name);
    try {
      const cardLocator = projectDashboardPage.projectCardLocatorByName(name);
      await cardLocator.hover();
      const [download] = await Promise.all([
        projectDashboardPage.getPage().waitForEvent('download'),
        cardLocator
          .locator(projectDashboardPage.buttonOnCardByLabel('download'))
          .click(),
      ]);
      const targetFolderLocation = getDataFolderPath();
      await download.saveAs(
        `${targetFolderLocation}/${download.suggestedFilename()}`
      );
      saveValueToSpecStoreKey(key, download.suggestedFilename());
    } catch (error) {
      throw new Error(`Cannot download project "${name}". Error: ${error}`);
    }
  }
}
