# AI Specify V2 First Time User Flow

// Preconditions: make sure delete all projects before testing

* Turn off Sandbox mode
* Turn on the Sandbox mode and store the sandbox name into "Sandbox" key
* Go to the AI Specify page
* On the Project Dashboard page, delete all projects if any exist
* Close all opened tabs

## User is able to start a new project from the first time with a single prompt

// Create a project using Prompt full flow (First-time User)

* Go to the AI Specify page
* On the first time user page, click on the "New Project" button to create a project
* On the first time user page, fill out the prompt input with data "Create a simple calculator"
* On the first time user page, click on the submit button
* On the AI Project Details page, check the page title is "Project Editor"
* On the AI Project Details page, go back to the Dashboard page
* On the Project Dashboard page, check the page title is "Project Dashboard"
* On the Project Dashboard page, check that the project card is visible
* On the Project Dashboard page, click on the first Project card
* On the AI Project Details page, check there is generated Project title

// User is able to manage requirements

* On the AI Project Details page, click on the Requirements tab
* On the AI Project Details page, check there is some requirements generated
* On the AI Project Details page, get the number of Requirements and save it to reference "numberOfRequirements"
* On the AI Project Details page, click on the Manually Add button on tab header and wait for the modal to open
* On the AI modal, check if the title is "Create Requirement"
* On the Create Requirement modal, fill the form with the data below and save to reference "AddRequirement"

  |                 Title *                  |                  Description *                  | Priority |   Type   |
  | ---------------------------------------- | ----------------------------------------------- | -------- | -------- |
  | Title - {{random-string-with-length: 5}} | Description - {{random-string-with-length: 10}} | High     | Security |

* On the Create Requirement modal, click on the "Create" button
* On the AI Project Details page, check the number of Requirements is "{{eval: {{numberOfRequirements}} + 1}}" and save it to reference "numberOfRequirements"
* On the AI Project Details page, check the requirements with data below "is" visible

  |          Title           |          Description           |          Priority           |          Type           |
  | ------------------------ | ------------------------------ | --------------------------- | ----------------------- |
  | {{AddRequirement.Title}} | {{AddRequirement.Description}} | {{AddRequirement.Priority}} | {{AddRequirement.Type}} |

* On the AI Project Details page, select the first requirement card
* On the AI Project Details page, click on the Improve button on tab header
* On the Improve Requirement(s) modal, fill the form with the data below

  | Describe how you want to improve the selected requirements |
  | ---------------------------------------------------------- |
  | Add Security considerations for user data protection       |

* On the Improve Requirement(s) modal, click on the Improve Selected button and wait for the modal to close
* On the AI Project Details page, check the requirements with data below "is not" visible

  |          Title           |          Description           |          Priority           |          Type           |
  | ------------------------ | ------------------------------ | --------------------------- | ----------------------- |
  | {{AddRequirement.Title}} | {{AddRequirement.Description}} | {{AddRequirement.Priority}} | {{AddRequirement.Type}} |

* On the AI Project Details page, click on the Edit Requirement button on the first requirement card
* On the Edit Requirement modal, fill the form with the data below and save to reference "EditRequirement"

  |                 Title *                  |                  Description *                  | Priority |   Type   |
  | ---------------------------------------- | ----------------------------------------------- | -------- | -------- |
  | Title - {{random-string-with-length: 5}} | Description - {{random-string-with-length: 10}} | Low      | Frontend |

* On the Edit Requirement modal, click on the "Save Changes" button
* On the AI Project Details page, check the requirements with data below "is" visible

  |           Title           |           Description           |           Priority           |           Type           |
  | ------------------------- | ------------------------------- | ---------------------------- | ------------------------ |
  | {{EditRequirement.Title}} | {{EditRequirement.Description}} | {{EditRequirement.Priority}} | {{EditRequirement.Type}} |

* On the AI Project Details page, delete Requirements on until there is "3" Requirements left
* On the AI Project Details page, check the number of Requirements is "3" and save it to reference "numberOfRequirements"
* On the AI Project Details page, select "Generate More" option from the Generate button on tab header
* On the Review Requirements modal, click on the "Select All" button
* On the Review Requirements modal, click on the "Approve" button
* On the AI Project Details page, check the number of Requirements is more than "{{numberOfRequirements}}"
* On the AI Project Details page, get the number of Requirements and save it to reference "numberOfRequirements"
* On the AI Project Details page, select "With Prompt" option from the Generate button on tab header
* On the Generate Requirement(s) modal, fill the form with the data below and save as reference "GeneratedRequirement"

  |   Describe your project features, pages or any details you want AI to consider    |
  | --------------------------------------------------------------------------------- |
  | Create a requirement and name it: AI Generated - {{random-string-with-length: 5}} |

* On the Generate Requirement(s) modal, click on the Analyze & Generate button and wait for the right toast message to appear
* Check that a message appears on the top right of the page

  |          Title          |                          Content                           |
  | ----------------------- | ---------------------------------------------------------- |
  | Analysis is in progress | You will be prompted to review the generated requirements. |

* Wait until the right toast message disappears
* On the AI Project Details page, click on the Review Requirements button on the tab header and wait for the modal to open
* On the Review Requirements modal, click on the "Select All" button
* On the Review Requirements modal, click on the "Reject" button
* On the Reject Requirement modal, fill the reason input with "Reason - {{random-string-with-length: 10}}"
* On the AI modal, click on the "Reject Suggestion" button
* On the AI Project Details page, check the number of Requirements is "{{numberOfRequirements}}" and save it to reference "numberOfRequirements"

## User is able to import and export the spec

|   Project Name    |           File Name            |
| ----------------- | ------------------------------ |
| Simple Calculator | aiSimpleCalculatorProject.docx |

* Download files from E2ETemplateAttachment node

|  File Name  |
| ----------- |
| <File Name> |

* Convert Docx to Json file <File Name> and save the file name to key "jsonFileName"

// Import a project on the first time user page

* Go to the AI Specify page
* On the first time user page, click on the Import Project button
* On the Import Project Modal, upload json with file name "{{jsonFileName}}"
* On the AI modal, click on the "Import" button
* Wait for the page to load completely

// Export a project on the Project Dashboard page

* Go to the AI Specify page
* On the Project Dashboard page, download project with name <Project Name> and save the file name with key "exportedJsonFile1"
* Check that the JSON file is downloaded successfully with the correct file name "{{exportedJsonFile1}}"

// Import a project on the Project Dashboard page

* On the Project Dashboard page, delete all projects if any exist
* On the Project Dashboard page, click on "Import" button on the header
* On the Import Project Modal, upload json with file name "{{jsonFileName}}"
* On the AI modal, click on the "Import" button
* Wait for the page to load completely

// Export a project on the AI Project Details page

* On the Project Dashboard page, click on the Project with name <Project Name>
* On the AI Project Details page, click on Export button and save the file name with key "exportedJsonFile2"
* Check that the JSON file is downloaded successfully with the correct file name "{{exportedJsonFile2}}"
