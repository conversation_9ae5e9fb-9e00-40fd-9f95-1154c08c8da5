/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstJSON as everest_appserver_primitive_JSON } from "@pkg/everest.appserver/types/primitives/JSON";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { AISpecProject as everest_aispecify_model_node_AISpecProject } from "@pkg/everest.aispecify/types/AISpecProject";

import { createOdataClientRuntime } from '@everestsystems/content-core/lib/runtime/loader';

/**
 * Generated frontend types for OData presentation AISpecProjectActions.
 */
export namespace AISpecProjectActionsUI {
  export namespace EntitySets { }

  export namespace Actions {
    export namespace InitializeProject {
      export namespace Execute {
        export type Input = {
          name: everest_appserver_primitive_Text;
          description: everest_appserver_primitive_Text;
          projectType: everest_appserver_primitive_Text;
          textInput: everest_appserver_primitive_Text;
          files: everest_appserver_primitive_JSON[];
          projectLanguage: everest_appserver_primitive_Text;
          isEveProject: everest_appserver_primitive_TrueFalse;
          evePackageId: everest_appserver_primitive_Number;
          aiModel: everest_appserver_primitive_Text;
          packageName: everest_appserver_primitive_Text;
        };

        export type Output = {
          projectId: everest_aispecify_model_node_AISpecProject.AISpecProject["id"];
          success: everest_appserver_primitive_TrueFalse;
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace InitializeProjectFromGitHub {
      export namespace Execute {
        export type Input = {
          url: everest_appserver_primitive_Text;
          githubToken?: everest_appserver_primitive_Text | undefined;
        };

        export type Output = {
          projectId: everest_aispecify_model_node_AISpecProject.AISpecProject["id"];
          success: everest_appserver_primitive_TrueFalse;
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }

    export namespace SetProjectSecret {
      export namespace Execute {
        export type Input = {
          projectId: everest_aispecify_model_node_AISpecProject.AISpecProject["id"];
          providerName: everest_appserver_primitive_Text;
          secretValue: everest_appserver_primitive_Text;
        };

        export type Output = {
          success: everest_appserver_primitive_TrueFalse;
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }
  }

  export namespace Functions {
    export namespace GetProjectSecret {
      export namespace Execute {
        export type Input = {
          projectId: everest_aispecify_model_node_AISpecProject.AISpecProject["id"];
          providerName: everest_appserver_primitive_Text;
        };

        export type Output = {
          secretExists: everest_appserver_primitive_TrueFalse;
          secretValue: everest_appserver_primitive_Text;
        };

        export interface Request {
          setInput(input: Input): this;

          execute(): Promise<Response>;
        }

        export type Response = {
          output: Output;
        };
      }
    }
  }

  export namespace Permission {
    export namespace Check {
      export type Result = {
        actions: {
          GetProjectSecret: {
            isExecutePermitted: boolean;
          };
          InitializeProject: {
            isExecutePermitted: boolean;
          };
          InitializeProjectFromGitHub: {
            isExecutePermitted: boolean;
          };
          SetProjectSecret: {
            isExecutePermitted: boolean;
          };
        };
      };

      export interface Request {
        execute(): Promise<Response>;
      }

      export type Response = {
        result: Result;
      };
    }
  }

  export namespace Api {
    export type EntitySets = Record<string, never>;

    export type Actions = {
      InitializeProject: {
        execute: Actions.InitializeProject.Execute.Request;
      };
      InitializeProjectFromGitHub: {
        execute: Actions.InitializeProjectFromGitHub.Execute.Request;
      };
      SetProjectSecret: {
        execute: Actions.SetProjectSecret.Execute.Request;
      };
    };

    export type Functions = {
      GetProjectSecret: {
        execute: Functions.GetProjectSecret.Execute.Request;
      };
    };

    export interface Client {
      createActionExecuteRequest<T extends 'InitializeProject' | 'InitializeProjectFromGitHub' | 'SetProjectSecret'>(actionName: T): Api.Actions[T]['execute'];

      createFunctionExecuteRequest<T extends 'GetProjectSecret'>(functionName: T): Api.Functions[T]['execute'];

      createPermissionCheckRequest(): Permission.Check.Request;
    }
  }

  export const client = createOdataClientRuntime<Api.Client>('everest.aispecify/modules/backend/AISpecProject/AISpecProjectActions');
}

