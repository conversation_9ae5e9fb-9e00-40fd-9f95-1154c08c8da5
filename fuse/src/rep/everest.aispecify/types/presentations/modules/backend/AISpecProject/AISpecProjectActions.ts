/* eslint-disable @eslint-community/eslint-comments/disable-enable-pair */
/* eslint-disable unused-imports/no-unused-imports */
/* eslint-disable simple-import-sort/imports */
/* eslint-disable simple-import-sort/exports */
/* eslint-disable no-restricted-imports */
/* eslint-disable no-restricted-exports */
/* eslint-disable @typescript-eslint/no-empty-interface */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/consistent-type-imports */

import type { EvstText as everest_appserver_primitive_Text } from "@pkg/everest.appserver/types/primitives/Text";
import type { EvstJSON as everest_appserver_primitive_JSON } from "@pkg/everest.appserver/types/primitives/JSON";
import type { EvstTrueFalse as everest_appserver_primitive_TrueFalse } from "@pkg/everest.appserver/types/primitives/TrueFalse";
import type { EvstNumber as everest_appserver_primitive_Number } from "@pkg/everest.appserver/types/primitives/Number";
import type { AISpecProject as everest_aispecify_model_node_AISpecProject } from "@pkg/everest.aispecify/types/AISpecProject";

import type { PresentationSession } from '@everestsystems/content-core';

/**
 * Generated backend types for OData presentation AISpecProjectActions.
 */
export namespace AISpecProjectActions {
  export namespace EntitySets { }

  export namespace Actions {
    export namespace InitializeProject {
      export type Input = {
        name: everest_appserver_primitive_Text;
        description: everest_appserver_primitive_Text;
        projectType: everest_appserver_primitive_Text;
        textInput: everest_appserver_primitive_Text;
        files: everest_appserver_primitive_JSON[];
        projectLanguage: everest_appserver_primitive_Text;
        isEveProject: everest_appserver_primitive_TrueFalse;
        evePackageId: everest_appserver_primitive_Number;
        aiModel: everest_appserver_primitive_Text;
        packageName: everest_appserver_primitive_Text;
      };

      export type Output = {
        projectId: everest_aispecify_model_node_AISpecProject.AISpecProject["id"];
        success: everest_appserver_primitive_TrueFalse;
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace InitializeProjectFromGitHub {
      export type Input = {
        url: everest_appserver_primitive_Text;
        githubToken?: everest_appserver_primitive_Text;
      };

      export type Output = {
        projectId: everest_aispecify_model_node_AISpecProject.AISpecProject["id"];
        success: everest_appserver_primitive_TrueFalse;
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace GetProjectSecret {
      export type Input = {
        projectId: everest_aispecify_model_node_AISpecProject.AISpecProject["id"];
        providerName: everest_appserver_primitive_Text;
      };

      export type Output = {
        secretExists: everest_appserver_primitive_TrueFalse;
        secretValue: everest_appserver_primitive_Text;
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }

    export namespace SetProjectSecret {
      export type Input = {
        projectId: everest_aispecify_model_node_AISpecProject.AISpecProject["id"];
        providerName: everest_appserver_primitive_Text;
        secretValue: everest_appserver_primitive_Text;
      };

      export type Output = {
        success: everest_appserver_primitive_TrueFalse;
      };

      export namespace Execute {
        export type Request = {
          session: PresentationSession;
          input: Input;
        };

        export type Response = {
          output: Output;
        };
      }

      export interface Implementation {
        execute(request: Execute.Request): Promise<Execute.Response>;
      }
    }
  }

  export type Implementation = {
    InitializeProject: Actions.InitializeProject.Implementation;
    InitializeProjectFromGitHub: Actions.InitializeProjectFromGitHub.Implementation;
    GetProjectSecret: Actions.GetProjectSecret.Implementation;
    SetProjectSecret: Actions.SetProjectSecret.Implementation;
  };
}

