import type {
  ControllerClientProvider,
  ISession,
} from '@everestsystems/content-core';
import { log } from '@everestsystems/content-core';
import { AISpecProject } from '@pkg/everest.aispecify/types/AISpecProject';
import { TenantSecret } from '@pkg/everest.appserver/types/TenantSecret';

import type { GithubToken } from './githubTypes';

/**
 * Universal Tenant Secret Management Utilities
 *
 * This module provides reusable functions for reading and upserting tenant-specific secrets
 * in the Everest platform. It focuses on storing authentication keys, tokens, and credentials
 * for various providers. It can be used across all actions in the aispecify package.
 */

export interface TenantSecretData {
  package: string;
  key: string;
  value?: string;
  isEditableByOthers?: boolean;
  isGlobal: false;
}

export interface TenantSecretReadOptions {
  package: string;
  key: string;
}

export interface TenantSecretUpsertOptions {
  package: string;
  key: string;
  value: string;
  isEditableByOthers?: boolean;
}

/**
 * Github provider secret structure
 */
export interface GithubSecret {
  /** Github personal access token or App token following Github token conventions */
  token: GithubToken;
}

/**
 * Union type for all supported secret types
 */
export type ProviderSecret = GithubSecret | string;

/**
 * Read a tenant secret from the secure storage
 *
 * @param session - The session provider (ISession or ControllerClientProvider)
 * @param options - Tenant secret read options
 * @returns Promise with the tenant secret data
 * @throws Error if tenant secret is not found or cannot be read
 */
async function readTenantSecret(
  session: ISession | ControllerClientProvider,
  options: TenantSecretReadOptions
): Promise<TenantSecretData> {
  try {
    const tenantSecretClient = await TenantSecret.client(session);
    const secret = await tenantSecretClient.get({
      package: options.package,
      key: options.key,
    });

    log.debug(
      `Successfully read tenant secret: ${options.package}/${options.key}`
    );

    return {
      package: options.package,
      key: options.key,
      value: secret.value,
      isEditableByOthers: secret.isEditableByOthers,
      isGlobal: false,
    };
  } catch (error) {
    log.error(
      `Failed to read tenant secret ${options.package}/${options.key}:`,
      error
    );
    throw new Error(
      `Failed to read tenant secret ${options.package}/${options.key}: ${error.message}`
    );
  }
}

/**
 * Upsert (create or update) a tenant secret in the secure storage
 *
 * @param session - The session provider (ISession or ControllerClientProvider)
 * @param options - Tenant secret upsert options
 * @returns Promise that resolves when the tenant secret is saved
 * @throws Error if tenant secret cannot be saved
 */
async function upsertTenantSecret(
  session: ISession | ControllerClientProvider,
  options: TenantSecretUpsertOptions
): Promise<void> {
  try {
    const tenantSecretClient = await TenantSecret.client(session);
    await tenantSecretClient.save({
      package: options.package,
      key: options.key,
      value: options.value,
      isEditableByOthers: options.isEditableByOthers ?? true,
    });

    log.debug(
      `Successfully upserted tenant secret: ${options.package}/${options.key}`
    );
  } catch (error) {
    log.error(
      `Failed to upsert tenant secret ${options.package}/${options.key}:`,
      error
    );
    throw new Error(
      `Failed to upsert tenant secret ${options.package}/${options.key}: ${error.message}`
    );
  }
}

/**
 * Read a tenant secret and parse it as JSON if it's a complex structure, or return as string
 *
 * @param session - The session provider (ISession or ControllerClientProvider)
 * @param options - Tenant secret read options
 * @returns Promise with the parsed data or string value
 * @throws Error if tenant secret is not found or cannot be read
 */
async function readTenantSecretValue<T = ProviderSecret>(
  session: ISession | ControllerClientProvider,
  options: TenantSecretReadOptions
): Promise<T> {
  try {
    const secret = await readTenantSecret(session, options);

    if (!secret.value) {
      throw new Error('Tenant secret value is empty');
    }

    // Try to parse as JSON first
    try {
      return JSON.parse(secret.value) as T;
    } catch {
      // If parsing fails, return as string (for simple tokens)
      return secret.value as T;
    }
  } catch (error) {
    if (error.message.includes('Failed to read tenant secret')) {
      throw error;
    }

    log.error(
      `Failed to read tenant secret value ${options.package}/${options.key}:`,
      error
    );
    throw new Error(
      `Failed to read tenant secret value ${options.package}/${options.key}: ${error.message}`
    );
  }
}

/**
 * Upsert a tenant secret by automatically handling JSON or string data
 *
 * @param session - The session provider (ISession or ControllerClientProvider)
 * @param options - Tenant secret upsert options (value will be handled automatically)
 * @param data - The data to store (string for simple tokens, object for complex secrets)
 * @returns Promise that resolves when the tenant secret is saved
 * @throws Error if tenant secret cannot be saved
 */
async function upsertTenantSecretValue(
  session: ISession | ControllerClientProvider,
  options: Omit<TenantSecretUpsertOptions, 'value'>,
  data: ProviderSecret
): Promise<void> {
  try {
    // If data is a string, store as-is. If object, stringify it.
    const value = typeof data === 'string' ? data : JSON.stringify(data);

    await upsertTenantSecret(session, {
      ...options,
      value,
    });

    log.debug(
      `Successfully upserted tenant secret value: ${options.package}/${options.key}`
    );
  } catch (error) {
    log.error(
      `Failed to upsert tenant secret value ${options.package}/${options.key}:`,
      error
    );
    throw new Error(
      `Failed to upsert tenant secret value ${options.package}/${options.key}: ${error.message}`
    );
  }
}

/**
 * Delete a tenant secret from the secure storage
 *
 * @param session - The session provider (ISession or ControllerClientProvider)
 * @param options - Tenant secret read options
 * @returns Promise that resolves when the tenant secret is deleted
 * @throws Error if tenant secret cannot be deleted
 */
async function deleteTenantSecret(
  session: ISession | ControllerClientProvider,
  options: TenantSecretReadOptions
): Promise<void> {
  try {
    const tenantSecretClient = await TenantSecret.client(session);
    await tenantSecretClient.remove({
      package: options.package,
      key: options.key,
    });

    log.debug(
      `Successfully deleted tenant secret: ${options.package}/${options.key}`
    );
  } catch (error) {
    log.error(
      `Failed to delete tenant secret ${options.package}/${options.key}:`,
      error
    );
    throw new Error(
      `Failed to delete tenant secret ${options.package}/${options.key}: ${error.message}`
    );
  }
}

// ============================================================================
// HIGH-LEVEL PROJECT-SPECIFIC SECRET FUNCTIONS
// ============================================================================

/**
 * Interface for project additional details that stores secret keys
 */
interface ProjectSecretKeys {
  secretKeys?: Record<string, string>;
  [key: string]: unknown;
}

/**
 * Read a project-specific provider secret
 *
 * @param session - The session provider (must be ISession to access uuid)
 * @param projectId - The AISpecProject ID
 * @param providerName - The name of the provider (e.g., 'openai', 'github')
 * @returns Promise with the secret value (string for simple tokens, object for complex secrets)
 * @throws Error if project or secret is not found
 *
 * @example
 * ```typescript
 * // Read Github secret for a project
 * const githubSecret = await readProjectProviderSecret<GithubSecret>(session, projectId, 'github');
 *
 * // Read simple token
 * const token = await readProjectProviderSecret<string>(session, projectId, 'some-api');
 * ```
 */
export async function readProjectProviderSecret<T = ProviderSecret>(
  session: ISession,
  projectId: number,
  providerName: string
): Promise<T> {
  try {
    // Read the project to get additionalDetails
    const project = await AISpecProject.read(session, { id: projectId }, [
      'additionalDetails',
    ]);

    if (!project.additionalDetails) {
      throw new Error(
        `No secret found for provider '${providerName}' in project ${projectId}`
      );
    }

    // Parse additionalDetails to get secret keys
    const additionalDetails = JSON.parse(
      project.additionalDetails as string
    ) as ProjectSecretKeys;
    const secretKey = additionalDetails.secretKeys?.[providerName];

    if (!secretKey) {
      throw new Error(
        `No secret key found for provider '${providerName}' in project ${projectId}`
      );
    }

    // Read the actual secret using the stored key
    return await readTenantSecretValue<T>(session, {
      package: 'everest.aispecify',
      key: secretKey,
    });
  } catch (error) {
    log.error(
      `Failed to read project provider secret for ${providerName} in project ${projectId}:`,
      error
    );
    throw error;
  }
}

/**
 * Save a project-specific provider secret
 *
 * @param session - The session provider (must be ISession to access uuid)
 * @param projectId - The AISpecProject ID
 * @param providerName - The name of the provider (e.g., 'openai', 'github')
 * @param secret - The secret to save (string for simple tokens, object for complex secrets)
 * @returns Promise that resolves when the secret is saved
 * @throws Error if project cannot be found or secret cannot be saved
 *
 * @example
 * ```typescript
 * // Save Github token for a project
 * await saveProjectProviderSecret(session, projectId, 'github', {
 *   token: 'ghp_1234567890abcdef1234567890abcdef12345678'
 * } as GithubSecret);
 *
 * // Save simple token
 * await saveProjectProviderSecret(session, projectId, 'some-api', 'simple-token-value');
 * ```
 */
export async function saveProjectProviderSecret(
  session: ISession,
  projectId: number,
  providerName: string,
  secret: ProviderSecret
): Promise<void> {
  try {
    // Read the project to get current additionalDetails
    const project = await AISpecProject.read(session, { id: projectId }, [
      'additionalDetails',
    ]);

    // Parse existing additionalDetails or create new structure
    let additionalDetails: ProjectSecretKeys = {};
    if (project.additionalDetails) {
      additionalDetails = JSON.parse(
        project.additionalDetails as string
      ) as ProjectSecretKeys;
    }

    // Ensure secretKeys object exists
    if (!additionalDetails.secretKeys) {
      additionalDetails.secretKeys = {};
    }

    // Check if we already have a secret key for this provider
    let secretKey = additionalDetails.secretKeys[providerName];

    // If no secret key exists, generate a new UUID
    if (!secretKey) {
      secretKey = session.util.uuid.v4();
      additionalDetails.secretKeys[providerName] = secretKey;

      // Update the project with the new secret key
      await AISpecProject.update(
        session,
        { id: projectId },
        {
          additionalDetails: JSON.stringify(additionalDetails),
        }
      );
    }

    // Save the actual secret using the secret key
    await upsertTenantSecretValue(
      session,
      {
        package: 'everest.aispecify',
        key: secretKey,
        isEditableByOthers: true,
      },
      secret
    );

    log.debug(
      `Successfully saved project provider secret for ${providerName} in project ${projectId}`
    );
  } catch (error) {
    log.error(
      `Failed to save project provider secret for ${providerName} in project ${projectId}:`,
      error
    );
    throw error;
  }
}

/**
 * Check if a project-specific provider secret exists
 *
 * @param session - The session provider (must be ISession to access uuid)
 * @param projectId - The AISpecProject ID
 * @param providerName - The name of the provider (e.g., 'openai', 'github')
 * @returns Promise that resolves to true if secret exists, false otherwise
 */
export async function projectProviderSecretExists(
  session: ISession,
  projectId: number,
  providerName: string
): Promise<boolean> {
  try {
    await readProjectProviderSecret(session, projectId, providerName);
    return true;
  } catch {
    return false;
  }
}

/**
 * List all provider names that have secrets configured for a project
 *
 * @param session - The session provider (must be ISession to access uuid)
 * @param projectId - The AISpecProject ID
 * @returns Promise with array of provider names that have secrets configured
 */
export async function listProjectProviders(
  session: ISession,
  projectId: number
): Promise<string[]> {
  try {
    // Read the project to get additionalDetails
    const project = await AISpecProject.read(session, { id: projectId }, [
      'additionalDetails',
    ]);

    if (!project.additionalDetails) {
      return [];
    }

    // Parse additionalDetails to get secret keys
    const additionalDetails = JSON.parse(
      project.additionalDetails as string
    ) as ProjectSecretKeys;

    return Object.keys(additionalDetails.secretKeys || {});
  } catch (error) {
    log.error(
      `Failed to list project providers for project ${projectId}:`,
      error
    );
    return [];
  }
}

/**
 * Delete all secrets for a project and clear the project's additionalDetails
 *
 * @param session - The session provider (must be ISession to access uuid)
 * @param projectId - The AISpecProject ID
 * @returns Promise that resolves when all secrets are deleted
 * @throws Error if project cannot be found or secrets cannot be deleted
 *
 * @example
 * ```typescript
 * // Delete all secrets for a project
 * await deleteAllProjectSecrets(session, projectId);
 * ```
 */
export async function deleteAllProjectSecrets(
  session: ISession,
  projectId: number
): Promise<void> {
  try {
    // Read the project to get additionalDetails
    const project = await AISpecProject.read(session, { id: projectId }, [
      'additionalDetails',
    ]);

    if (!project.additionalDetails) {
      log.info(`No secrets found for project ${projectId}`);
      return;
    }

    // Parse additionalDetails to get secret keys
    const additionalDetails = JSON.parse(
      project.additionalDetails as string
    ) as ProjectSecretKeys;

    const secretKeys = additionalDetails.secretKeys || {};
    const providerNames = Object.keys(secretKeys);

    if (providerNames.length === 0) {
      log.info(`No secrets found for project ${projectId}`);
      return;
    }

    // Delete all secrets
    const deletePromises = providerNames.map(async (providerName) => {
      const secretKey = secretKeys[providerName];
      try {
        await deleteTenantSecret(session, {
          package: 'everest.aispecify',
          key: secretKey,
        });
        log.info(
          `Successfully deleted secret for provider '${providerName}' in project ${projectId}`
        );
      } catch (error) {
        log.error(
          `Failed to delete secret for provider '${providerName}' in project ${projectId}:`,
          error
        );
        throw error;
      }
    });

    // Wait for all deletions to complete
    await Promise.all(deletePromises);

    // Clear the project's additionalDetails secretKeys
    const updatedAdditionalDetails = { ...additionalDetails };
    delete updatedAdditionalDetails.secretKeys;

    // Update the project with cleared secret keys
    await AISpecProject.update(
      session,
      { id: projectId },
      {
        additionalDetails: JSON.stringify(updatedAdditionalDetails),
      }
    );

    log.info(
      `Successfully deleted all ${providerNames.length} secrets for project ${projectId}`
    );
  } catch (error) {
    log.error(`Failed to delete all secrets for project ${projectId}:`, error);
    throw error;
  }
}
