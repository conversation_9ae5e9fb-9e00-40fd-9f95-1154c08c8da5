import type { EvstHttpRequestMethod } from '@pkg/everest.appserver/types/enums/HttpRequestMethod';

/**
 * Github token types that can read private repositories with proper permissions
 * - ghp_: Classic personal access tokens (full repository access)
 * - github_pat_: Fine-grained personal access tokens (granular permissions)
 * - ghu_: GitHub App user-to-server tokens (if app has repository permissions)
 */
export type GithubToken =
  | `ghp_${string}` // Classic PATs - can read private repos
  | `github_pat_${string}` // Fine-grained PATs - can read private repos
  | `ghu_${string}`; // GitHub App user tokens - can read private repos if configured

/**
 * HTTP request options interface for Github API calls
 */
export interface GitHubApiRequestOptions {
  url: URL;
  method: EvstHttpRequestMethod;
  headers: Record<string, string>;
  timeout?: number;
}

/**
 * Type guard to check if a string is a valid Github token that can read private repos
 */
export function isGithubToken(token: string): token is GithubToken {
  return (
    token.startsWith('ghp_') ||
    token.startsWith('github_pat_') ||
    token.startsWith('ghu_')
  );
}

/**
 * Injects Github token into HTTP request headers
 */
export function injectGithubToken(
  req: GitHubApiRequestOptions,
  token?: GithubToken
): GitHubApiRequestOptions {
  if (!token) {
    return req;
  }

  const { headers = {}, ...rest } = req;

  return {
    ...rest,
    headers: {
      ...headers,
      Authorization: `Bearer ${token}`,
    },
  };
}
