import type { ISession } from '@everestsystems/content-core';
import { log } from '@everestsystems/content-core';
import { callAIWithFallback } from '@pkg/everest.aispecify/actions/utils/aiHelper';

import { getLightweightProjectContext } from '../utils/projectContextUtils';

type GenerateOverviewReturnData = {
  projectId: number;
  requestedWordCount: number;
  actualWordCount: number;
  summaryType: string;
  overview: string;
  generatedAt: string;
};

/**
 * Generates a project overview using AI - optimized for speed
 * @param session The current session
 * @param projectId The project ID
 * @param wordCount The number of words for the summary
 * @param summaryType The type of summary requested by the user
 * @returns The generated overview data
 */
export async function generateProjectOverview(
  session: ISession,
  projectId: number,
  wordCount: number,
  summaryType: string
): Promise<{ data: GenerateOverviewReturnData }> {
  try {
    log.info(
      `Generating project overview for project ID: ${projectId}, wordCount: ${wordCount}, summaryType: ${summaryType}`
    );

    // Parse ID to number
    const numericProjectId = projectId;

    if (Number.isNaN(numericProjectId)) {
      throw new TypeError('Invalid project ID');
    }

    // Validate inputs
    if (!wordCount || wordCount <= 0) {
      throw new Error('Word count must be a positive number');
    }

    if (!summaryType || summaryType.trim() === '') {
      throw new Error('Summary type is required');
    }

    // Get lightweight project context (optimized for speed)
    const projectContext = await getLightweightProjectContext(
      session,
      numericProjectId
    );

    // Create messages for the AI based on summary type
    let promptContent = '';
    const wordCountNum = wordCount;

    switch (summaryType) {
      case 'executive': {
        promptContent = `Create an executive summary of the project specification in approximately ${wordCountNum} words. Focus on the business value, key features, and strategic importance.`;
        break;
      }
      case 'technical': {
        promptContent = `Create a technical summary of the project specification in approximately ${wordCountNum} words. Focus on the architecture, technologies, and implementation details.`;
        break;
      }
      case 'stakeholder': {
        promptContent = `Create a stakeholder-friendly summary of the project specification in approximately ${wordCountNum} words. Focus on benefits, timeline, and expected outcomes.`;
        break;
      }
      case 'bolt': {
        // Use the word count from UI, but cap at 2250 words if no specific limit is provided or if it exceeds reasonable limits
        const maxWordCount = wordCountNum && wordCountNum > 0 ? wordCountNum : 2250;
        promptContent = `Create a comprehensive Bolt app development summary in approximately ${maxWordCount} words. This summary should be optimized for AI-powered app generation and include all essential details needed to build the application. Structure your response with the following sections:

✅ **1. Intent**
Clearly state what you're building in 100-200 words.
- Primary purpose and core functionality
- Target audience and their needs
- Main value proposition
- Key problems being solved

Example format:
"I want to build a [type of application] with [key features] that helps [target users] to [main benefit/solution]."

✅ **2. Functionality**
List key features or use cases the app should support (300-500 words).
- Core user actions and workflows
- Primary features with brief descriptions
- User roles and permissions
- Key interactions and behaviors
- Integration requirements
- Data management needs

Example format:
- Users can [action 1]
- [User type] can [action 2]
- System supports [feature 3]
- Integration with [external service]

✅ **3. Platform & Scope**
Indicate platform, constraints, and technical scope (200-300 words).
- Target platform (web, mobile, desktop)
- Device compatibility requirements
- Browser support needs
- Performance requirements
- Scalability considerations
- Technical constraints or limitations
- Deployment environment

Example format:
"Build this as a [platform type] using [technology stack]. Must support [requirements] and handle [scale/performance needs]."

✅ **4. Data Models**
Provide entities and their attributes, especially for data-heavy apps (400-600 words).
- Core entities and their relationships
- Key attributes for each entity
- Data validation rules
- Database schema considerations
- Data flow between entities
- Storage requirements
- Backup and recovery needs

Example format:
- Entity1: id, attribute1, attribute2, relationship_id
- Entity2: id, name, description, status, created_date
- Relationships: Entity1 belongs_to Entity2

✅ **5. UI/UX Guidance**
Describe UI behavior, look, and ideal user flow (500-800 words).
- Overall design aesthetic and principles
- Key screens and layouts
- Navigation patterns and user flow
- Interactive elements and behaviors
- Responsive design requirements
- Accessibility considerations
- User feedback mechanisms
- Loading states and error handling
- Visual hierarchy and information architecture

Example format:
"[Feature] should be displayed as [layout type]. The [screen] should show [elements] with [behavior]. User flow: [step 1] → [step 2] → [outcome]."

✅ **6. Tech Preferences or Requirements**
Specify stack constraints, libraries, or API integrations (400-600 words).
- Frontend framework and libraries
- Backend technology stack
- Database technology and rationale
- Authentication and authorization approach
- External APIs and third-party services
- Development tools and build process
- Testing framework and approach
- Deployment and hosting requirements
- Monitoring and analytics tools

Example format:
"Use [framework] for frontend, [backend tech] for API, [database] for data storage. Integrate with [external services]. Deploy on [platform]."

✅ **7. Style or Design Preferences**
Mention design preferences, themes, and visual requirements (300-400 words).
- Design system and style guide
- Color palette and branding
- Typography and font choices
- Component library preferences
- Theme support (dark/light mode)
- Mobile-first or desktop-first approach
- Animation and interaction preferences
- Icon and imagery style
- Layout and spacing principles

Example format:
"Keep design [style preference], use [CSS framework], include [theme features]. Visual style should be [aesthetic] with [specific requirements]."

🔧 **8. Technical Implementation Details**
Specify detailed development requirements (600-800 words).
- API endpoints and data structures
- Security measures and data protection
- Performance optimization strategies
- Caching and data management
- Real-time features implementation
- Offline capabilities (if needed)
- Search and filtering functionality
- File upload and media handling
- Notification systems
- Error handling and logging
- Testing strategy and quality assurance

📱 **9. User Experience Flow**
Map out the complete user journey (400-500 words).
- Onboarding process and first-time user experience
- Main workflow paths through the application
- User registration and authentication flow
- Core feature usage patterns
- Edge cases and error scenarios
- User feedback and support mechanisms
- Notification and communication flows
- Data export and import processes

🚀 **10. Development and Launch Strategy**
Outline development approach and go-to-market considerations (300-400 words).
- MVP features vs. future enhancements
- Development phases and milestones
- Testing and quality assurance approach
- Deployment and release strategy
- User acquisition and retention strategies
- Success metrics and KPIs
- Feedback collection and iteration plans
- Scaling considerations for growth
- Maintenance and support requirements

Make this summary comprehensive enough that an AI system could understand exactly what to build, how to build it, and why it matters. Include specific technical details, user experience considerations, and business context that would be essential for successful app development. Ensure the total word count stays within the specified limit to maintain optimal performance.`;
        break;
      }
      case 'lovable': {
        promptContent = `Create a comprehensive Lovable app development summary in approximately ${wordCountNum} words. All the sections include recommended word counts but you might have to change them if the approximate word count limit is too high or too low. This summary should be optimized for AI-powered app generation and include all essential details needed to build the application. Structure your response with the following sections:

🧠 **1. Overview (150-250 words)**
Clearly explain:
- What the app does and its primary purpose
- Who the target users are and their needs
- The core user journey from start to finish
- Why this app should be built and its value proposition
- Key problems it solves

🧱 **2. Architecture Plan (300-500 words)**
Outline the technical foundation:
- Frontend framework recommendation (React, Vue, Angular, etc.)
- Backend technology stack (Node.js, Python, etc.)
- Database schema choice and rationale (SQL vs NoSQL)
- Authentication and authorization approach
- Any external APIs or third-party integrations needed
- Hosting and deployment considerations
- Performance and scalability requirements

👩‍🎨 **3. UI/UX Design (600-800 words)**
Detail the user interface requirements:
- Overall design aesthetic and brand guidelines
- Key screens and their layouts (wireframe descriptions)
- Navigation patterns and user flow
- Interactive elements and their behaviors
- Responsive design considerations for mobile/desktop
- Accessibility requirements
- Color schemes, typography, and visual hierarchy
- User feedback mechanisms (loading states, error messages, success confirmations)

⚙️ **4. Core Features (800-1200 words)**
Break down the main functionality:
- Primary features with detailed descriptions
- User interactions and expected behaviors
- Data input/output requirements
- Business logic and rules
- Integration points with external services
- Real-time features (if applicable)
- Offline capabilities (if needed)
- Search and filtering functionality
- User management and permissions

🔧 **5. Technical Implementation (400-600 words)**
Specify development requirements:
- API endpoints and data structures
- Database models and relationships
- Third-party service integrations
- Security measures and data protection
- Performance optimization strategies
- Testing approach and quality assurance
- Deployment and DevOps considerations
- Monitoring and analytics setup

📱 **6. User Experience Flow (300-400 words)**
Map out the complete user journey:
- Onboarding process and first-time user experience
- Main workflow paths through the application
- Edge cases and error handling
- User feedback and support mechanisms
- Notification systems and communication flows

🚀 **7. Launch Strategy (200-300 words)**
Outline go-to-market considerations:
- MVP features vs. future enhancements
- User acquisition and retention strategies
- Success metrics and KPIs
- Feedback collection and iteration plans
- Scaling considerations for growth

Make this summary comprehensive enough that an AI system could understand exactly what to build, how to build it, and why it matters. Include specific technical details, user experience considerations, and business context that would be essential for successful app development.`;
        break;
      }
      case 'detailed': {
        promptContent = `Create a detailed summary of the project specification in approximately ${wordCountNum} words. Include comprehensive information about all aspects of the project.`;
        break;
      }
      default: {
        promptContent = `Create a comprehensive summary of the project specification in approximately ${wordCountNum} words.`;
      }
    }

    const messages = [
      {
        role: 'system',
        content: `You are an AI assistant specialized in generating project overviews based on software specifications. Your task is to generate a concise and informative overview in approximately ${wordCountNum} words.
        Do not include any explanations or surrounding text. Answer only with the overview content.
        `,
      },
      {
        role: 'user',
        content: `I need you to generate a project overview based on the following project information and requirements. ${promptContent}

## Project Information
Name: ${projectContext.project.name}
Description: ${projectContext.project.description}

## Project Requirements (${projectContext.requirementsCount} total)
${projectContext.requirements}

Please generate a clear and concise overview that captures the essence of the project. The overview should be well-structured and easy to understand.`,
      },
    ];

    // Call the AI to generate the overview (with shorter timeout for speed)
    let aiResponse: string;
    try {
      // Use the callAIWithFallback utility with optimized settings
      aiResponse = await callAIWithFallback(
        session,
        messages,
        numericProjectId,
        {
          temperature: 0.7,
          topP: 0.9,
          timeoutMs: 120_000, // 2 minutes timeout (much faster)
          maxTokens: Math.max(wordCount * 2, 1000),
        }
      );
    } catch (error) {
      log.error('Error calling AI services:', error);
      throw new Error(
        `Failed to generate overview: ${error.message}. Please try again with a different AI model.`
      );
    }

    if (!aiResponse) {
      throw new Error('Empty response from AI model');
    }

    // Format generated content
    const generatedOverview = aiResponse.trim();

    // Calculate actual word count
    const actualWordCount = generatedOverview.split(/\s+/).length;

    // Log a preview of the generated content
    log.info(
      `Generated overview preview: ${generatedOverview.slice(0, 100)}...`
    );

    // Return data in the same format as artifacts
    return {
      data: {
        projectId: numericProjectId,
        requestedWordCount: wordCount,
        actualWordCount: actualWordCount,
        summaryType: summaryType,
        overview: generatedOverview,
        generatedAt: new Date().toISOString(),
      },
    };
  } catch (error) {
    log.error('Error generating project overview', error);
    error.message = `Error generating project overview: ${error.message}`;
    throw error;
  }
}
