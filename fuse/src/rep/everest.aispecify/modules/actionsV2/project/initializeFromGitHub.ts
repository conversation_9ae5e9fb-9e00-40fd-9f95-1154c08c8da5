import type { ISession } from '@everestsystems/content-core';
import { log } from '@everestsystems/content-core';
import { callAIWithFallback } from '@pkg/everest.aispecify/actions/utils/aiHelper';
import { DEFAULT_AI_MODEL_CONFIG } from '@pkg/everest.aispecify/modules/actionsV2/project/constants';
import { AIModelConfig } from '@pkg/everest.aispecify/types/AIModelConfig';
import { AISpecInput } from '@pkg/everest.aispecify/types/AISpecInput';
import { AISpecProject } from '@pkg/everest.aispecify/types/AISpecProject';
import { EvstAISpecProjectTypes } from '@pkg/everest.aispecify/types/enums/AISpecProjectTypes';
import { EvstHttpRequestMethod } from '@pkg/everest.appserver/types/enums/HttpRequestMethod';

import { generateRequirementsFromText } from '../requirements/generateRequirementsFromText';
import {
  type GithubToken,
  injectGithubToken,
  isGithubToken,
} from '../utils/githubTypes';
import throwOnAppdevMain from '../utils/projectGuard';

interface GitHubRepo {
  id: number;
  name: string;
  full_name: string;
  description: string | null;
  html_url: string;
  clone_url: string;
  ssh_url: string;
  default_branch: string;
  language: string | null;
  languages_url: string;
  stargazers_count: number;
  forks_count: number;
  open_issues_count: number;
  size: number;
  created_at: string;
  updated_at: string;
  pushed_at: string;
  topics: string[];
}

interface GitHubTreeItem {
  path: string;
  mode: string;
  type: 'blob' | 'tree';
  sha: string;
  size?: number;
  url: string;
}

interface GitHubTree {
  sha: string;
  url: string;
  tree: GitHubTreeItem[];
  truncated: boolean;
}

interface GitHubFile {
  name: string;
  path: string;
  sha: string;
  size: number;
  url: string;
  html_url: string;
  git_url: string;
  download_url: string;
  type: string;
  content: string;
  encoding: string;
}

interface InitializeFromGitHubResponse {
  success: boolean;
  message: string;
  projectId?: number;
}

/**
 * Initialize a project by analyzing a GitHub repository
 * Fetches repository details, analyzes all relevant files, and generates requirements
 * Creates a new project with AI-generated name and description
 */
export async function initializeFromGitHub(
  session: ISession,
  url: string,
  token?: string
): Promise<InitializeFromGitHubResponse> {
  throwOnAppdevMain(session);
  try {
    log.info(`Initializing project from GitHub URL: ${url}`);

    // Extract repository information from URL
    const repoInfo = extractRepoInfoFromUrl(url);
    if (!repoInfo) {
      return {
        success: false,
        message:
          'Invalid GitHub URL format. Please provide a valid GitHub repository URL.',
      };
    }

    if (token && !isGithubToken(token)) {
      return {
        success: false,
        message: 'Invalid Token received. Please provide a valid Token.',
      };
    }

    // Fetch repository details
    const repo = await getRepositoryDetails(
      session,
      repoInfo.owner,
      repoInfo.repo,
      token as GithubToken // passed the type guard
    );

    if (!repo) {
      return {
        success: false,
        message: `Repository not found or inaccessible: ${repoInfo.owner}/${repoInfo.repo}`,
      };
    }

    // Get the full repository tree
    const tree = await getRepositoryTree(session, repo, token as GithubToken);

    // Get all relevant files
    const relevantFiles = await getRelevantFiles(
      session,
      repo,
      tree,
      token as GithubToken
    );

    // Generate project name and description based on repository information
    const projectDetails = await generateProjectNameAndDescription(
      session,
      repo
    );

    // Create the AISpecProject
    const project = await AISpecProject.create(session, {
      name: projectDetails.name,
      description: projectDetails.description,
      projectType: EvstAISpecProjectTypes.Github,
      active: true,
      isEveProject: false,
      additionalDetails: JSON.stringify({
        version: 2,
        githubRepo: repo.full_name,
        githubUrl: repo.html_url,
      }),
    });

    const projectId = project.id;

    // Create AI model config for the project
    try {
      await AIModelConfig.create(session, {
        projectId,
        ...DEFAULT_AI_MODEL_CONFIG,
      });
    } catch (error) {
      log.error('Error setting AI model for project:', error);
    }

    // Store repository URL as an input
    await AISpecInput.create(session, {
      content: { url: url, repoName: repo.full_name },
      type: 'URL',
      subType: 'githubRepo',
      projectId: projectId,
    });

    // Build context for AI analysis
    const context = buildAnalysisContext(
      repo,
      { sha: '', url: '', tree: [], truncated: false },
      relevantFiles
    );

    // Prepare the text input for requirement generation
    const textInput = `
# GitHub Repository Analysis: ${repo.full_name}

**Repository URL:** ${url}
**Description:** ${repo.description || 'No description available'}
**Primary Language:** ${repo.language || 'Unknown'}
**Stars:** ${repo.stargazers_count}
**Topics:** ${repo.topics.join(', ') || 'None'}

## Repository Context and File Analysis

${context}

## Task
Based on the above repository information and file contents, generate comprehensive software requirements that cover:
1. Functional Requirements - What the system does
2. Non-functional Requirements - Performance, security, scalability, etc.
3. Technical Requirements - Specific technologies, frameworks, databases
4. Architectural Requirements - Design patterns, architectural patterns
5. Integration Requirements - External services, APIs, dependencies

Generate requirements that accurately reflect the actual implementation found in the repository.
`;

    // Generate requirements using the existing action with auto-approve
    const requirementsResult = await generateRequirementsFromText(session, {
      text: textInput,
      projectId: projectId,
      autoApprove: true,
      changeRequestMetadata: {
        actionType: 'initialize',
        initializerAction: 'initializeFromGitHub',
      },
    });

    // Get the number of created requirements
    const createdRequirementsCount =
      requirementsResult.requirements?.length || 0;

    log.info(
      `Successfully analyzed repository ${repo.full_name}. Created ${createdRequirementsCount} requirements.`
    );

    return {
      success: true,
      projectId: projectId,
      message: `Successfully analyzed repository, created ${createdRequirementsCount} requirements.`,
    };
  } catch (error) {
    log.error('Error initializing from GitHub:', error);
    return {
      success: false,
      message: `Error analyzing repository: ${error.message}`,
    };
  }
}

/**
 * Extract owner and repo name from GitHub URL
 */
function extractRepoInfoFromUrl(
  url: string
): { owner: string; repo: string } | null {
  try {
    const parsedUrl = new URL(url);
    const pathParts = parsedUrl.pathname
      .split('/')
      .filter((part) => part.length > 0);

    if (pathParts.length >= 2) {
      return {
        owner: pathParts[0],
        repo: pathParts[1],
      };
    }
    return null;
  } catch {
    return null;
  }
}

/**
 * Fetch repository details from GitHub API
 */
async function getRepositoryDetails(
  session: ISession,
  owner: string,
  repo: string,
  token?: GithubToken
): Promise<GitHubRepo | null> {
  const headers: Record<string, string> = {
    Accept: 'application/vnd.github.v3+json',
    'User-Agent': 'Everest-AI-Specify',
  };

  try {
    const response = await session.fetchProvider.request(
      injectGithubToken(
        {
          url: new URL(`https://api.github.com/repos/${owner}/${repo}`),
          method: EvstHttpRequestMethod.GET,
          headers,
        },
        token
      )
    );

    if (response.status === 200) {
      return response.data as GitHubRepo;
    } else {
      log.warn(
        `Repository not found: ${owner}/${repo} (status: ${response.status})`
      );
      return null;
    }
  } catch (error) {
    log.warn(`Error fetching repository details for ${owner}/${repo}:`, error);
    return null;
  }
}

/**
 * Fetch the full repository tree
 */
async function getRepositoryTree(
  session: ISession,
  repo: GitHubRepo,
  token?: GithubToken
): Promise<GitHubTree> {
  const headers: Record<string, string> = {
    Accept: 'application/vnd.github.v3+json',
    'User-Agent': 'Everest-AI-Specify',
  };

  const treeUrl = `https://api.github.com/repos/${repo.full_name}/git/trees/${
    repo.default_branch || 'main'
  }?recursive=1`;

  try {
    const response = await session.fetchProvider.request(
      injectGithubToken(
        {
          url: new URL(treeUrl),
          method: EvstHttpRequestMethod.GET,
          headers,
        },
        token
      )
    );

    if (response.status === 200) {
      return response.data as GitHubTree;
    } else {
      log.warn(
        `Could not fetch tree for ${repo.full_name}: ${response.status}`
      );
      return { sha: '', url: '', tree: [], truncated: false };
    }
  } catch (error) {
    log.warn(`Error fetching tree for ${repo.full_name}:`, error);
    return { sha: '', url: '', tree: [], truncated: false };
  }
}

/**
 * Get all relevant files from the repository
 */
async function getRelevantFiles(
  session: ISession,
  repo: GitHubRepo,
  tree: GitHubTree,
  token?: GithubToken
): Promise<GitHubFile[]> {
  const headers: Record<string, string> = {
    Accept: 'application/vnd.github.v3+json',
    'User-Agent': 'Everest-AI-Specify',
  };

  // Comprehensive patterns for relevant files
  const relevantFilePatterns = [
    // Documentation
    /^readme\.(md|txt|rst)$/i,
    /^changelog\.(md|txt|rst)$/i,
    /^contributing\.(md|txt|rst)$/i,
    /^license\.(md|txt|rst)?$/i,
    /^docs?\/.+\.(md|txt|rst)$/i,
    /^architecture\.(md|txt|rst)$/i,
    /^design\.(md|txt|rst)$/i,

    // Configuration files
    /^package\.json$/,
    /^package-lock\.json$/,
    /^yarn\.lock$/,
    /^requirements\.txt$/,
    /^Pipfile$/,
    /^pyproject\.toml$/,
    /^Cargo\.toml$/,
    /^pom\.xml$/,
    /^build\.gradle$/,
    /^Gemfile$/,
    /^composer\.json$/,
    /^setup\.py$/,
    /^\.env\.example$/,
    /^\.env\.template$/,
    /^config\/.+\.(json|yaml|yml|toml|ini)$/,

    // API specifications
    /^openapi\.(json|yaml|yml)$/i,
    /^swagger\.(json|yaml|yml)$/i,
    /^api\/.+\.(json|yaml|yml)$/i,

    // Source code entry points
    /^(src\/)?main\.(js|ts|py|java|go|rs|cpp|c)$/,
    /^(src\/)?index\.(js|ts|html)$/,
    /^(src\/)?app\.(js|ts|py)$/,
    /^(src\/)?server\.(js|ts|py)$/,
    /^(lib\/)?index\.(js|ts)$/,

    // Deployment and infrastructure
    /^docker-compose\.ya?ml$/,
    /^Dockerfile$/,
    /^\.dockerignore$/,
    /^\.github\/workflows\/.+\.ya?ml$/,
    /^\.gitlab-ci\.ya?ml$/,
    /^Procfile$/,
    /^app\.ya?ml$/,
    /^serverless\.ya?ml$/,
    /^terraform\/.+\.tf$/,

    // Database and schema
    /^migrations?\/.+\.(sql|js|ts)$/,
    /^schema\.(sql|json|yaml|yml)$/,
    /^database\.(sql|json|yaml|yml)$/,
    /^models?\/.+\.(js|ts|py|sql)$/,

    // Test files (limited)
    /^tests?\/(setup|config|fixtures)\.(js|ts|py)$/,
    /^test\/(setup|config|fixtures)\.(js|ts|py)$/,
  ];

  const relevantTreeItems = tree.tree.filter(
    (item) =>
      item.type === 'blob' &&
      relevantFilePatterns.some((pattern) => pattern.test(item.path)) &&
      (item.size || 0) < 200_000 // Increased from 100KB to 200KB
  );

  log.info(
    `Found ${relevantTreeItems.length} relevant files in ${repo.full_name}`
  );

  // TODO (Gopalji): Improve file fetching to handle large number of file fetches
  // Fetch file contents one by one to avoid any batch processing issues
  const files: GitHubFile[] = [];

  for (let i = 0; i < relevantTreeItems.length; i++) {
    const item = relevantTreeItems[i];
    log.info(
      `Fetching file ${i + 1}/${relevantTreeItems.length}: ${item.path}`
    );

    try {
      const fileResponse = await session.fetchProvider.request(
        injectGithubToken(
          {
            url: new URL(
              `https://api.github.com/repos/${repo.full_name}/contents/${item.path}`
            ),
            method: EvstHttpRequestMethod.GET,
            headers,
          },
          token
        )
      );

      if (fileResponse.status === 200) {
        const fileData = fileResponse.data as GitHubFile;
        // Decode base64 content
        try {
          fileData.content = fileData.content.replaceAll(/[\n\r]+/g, '');
          fileData.content = session.util.encoding
            .toBuffer(fileData.content, 'base64')
            .toString('utf8');
          files.push(fileData);
          log.info(
            `Successfully fetched ${item.path} (${fileData.content.length} chars)`
          );
        } catch (decodeError) {
          log.warn(
            `Could not decode content for ${item.path}: ${decodeError.message}`
          );
        }
      } else {
        log.warn(
          `Could not fetch file ${item.path}: ${fileResponse.status} - ${fileResponse.statusText}`
        );
      }
    } catch (error) {
      log.warn(`Error fetching file ${item.path}: ${error.message}`);
    }
  }

  log.info(
    `Successfully fetched ${files.length} files out of ${relevantTreeItems.length} relevant files`
  );
  return files;
}

/**
 * Build comprehensive analysis context from repository data
 */
function buildAnalysisContext(
  repo: GitHubRepo,
  tree: GitHubTree,
  files: GitHubFile[]
): string {
  let context = '';

  // Repository metadata
  context += `# Repository Analysis: ${repo.full_name}\n\n`;
  context += `**Description:** ${
    repo.description || 'No description provided'
  }\n`;
  context += `**Language:** ${repo.language || 'Not specified'}\n`;
  context += `**Stars:** ${repo.stargazers_count}\n`;
  context += `**Forks:** ${repo.forks_count}\n`;
  context += `**Open Issues:** ${repo.open_issues_count}\n`;
  context += `**Size:** ${repo.size} KB\n`;
  context += `**Topics:** ${repo.topics.join(', ') || 'None'}\n`;
  context += `**Last Updated:** ${repo.updated_at}\n\n`;

  // File structure overview
  context += `## Repository Structure\n\n`;
  const directories = new Set<string>();
  for (const item of tree.tree) {
    if (item.type === 'tree') {
      directories.add(item.path);
    }
  }

  const sortedDirs = Array.from(directories).sort();
  for (const dir of sortedDirs.slice(0, 20)) {
    context += `- ${dir}/\n`;
  }

  if (sortedDirs.length > 20) {
    context += `... and ${sortedDirs.length - 20} more directories\n`;
  }
  context += '\n';

  // File contents with prioritization
  context += `## Key Files Analysis\n\n`;

  // Prioritize files for analysis
  const priorityPatterns = [
    /^readme\.(md|txt|rst)$/i,
    /^package\.json$/,
    /^requirements\.txt$/,
    /^(src\/)?main\.(js|ts|py|java|go|rs|cpp|c)$/,
    /^(src\/)?index\.(js|ts|html)$/,
    /^openapi\.(json|yaml|yml)$/i,
    /^swagger\.(json|yaml|yml)$/i,
    /^docker-compose\.ya?ml$/,
    /^Dockerfile$/,
  ];

  const prioritizedFiles = files.sort((a, b) => {
    const aPriority = priorityPatterns.findIndex((pattern) =>
      pattern.test(a.path)
    );
    const bPriority = priorityPatterns.findIndex((pattern) =>
      pattern.test(b.path)
    );

    if (aPriority !== -1 && bPriority !== -1) {
      return aPriority - bPriority;
    }
    if (aPriority !== -1) {
      return -1;
    }
    if (bPriority !== -1) {
      return 1;
    }
    return a.path.localeCompare(b.path);
  });

  for (const file of prioritizedFiles) {
    context += `### ${file.path}\n\n`;
    // Truncate very long files to keep context manageable
    const content =
      file.content.length > 5000
        ? file.content.slice(0, 5000) + '\n... (truncated)'
        : file.content;
    context += '```\n' + content + '\n```\n\n';
  }

  return context;
}

/**
 * Generate project name and description from repository information using AI
 */
async function generateProjectNameAndDescription(
  session: ISession,
  repo: GitHubRepo
): Promise<{ name: string; description: string }> {
  try {
    // Create context for AI
    const repoContext = `
      Repository Name: ${repo.name}
      Full Name: ${repo.full_name}
      Description: ${repo.description || 'No description available'}
      Language: ${repo.language || 'Unknown'}
      Stars: ${repo.stargazers_count}
      Forks: ${repo.forks_count}
      Open Issues: ${repo.open_issues_count}
      Topics: ${repo.topics.join(', ')}
    `;

    // Create messages for the AI
    const messages = [
      {
        role: 'system',
        content: `You are an AI assistant specialized in analyzing GitHub repositories and generating appropriate project names and descriptions. Your task is to analyze the provided repository information and generate a concise project name and a more detailed project description.`,
      },
      {
        role: 'user',
        content: `Based on the following GitHub repository information, generate a concise project name (max 50 characters) and a more detailed project description (max 200 characters). Format your response as a JSON object with 'name' and 'description' fields.

${repoContext}`,
      },
    ];

    // Use the callAIWithFallback utility
    const aiResponse = await callAIWithFallback(session, messages, undefined, {
      timeoutMs: 60_000, // 1 minute timeout
    });

    // Try to extract JSON from the response
    let jsonContent = aiResponse;

    // First, look for JSON code blocks
    const jsonBlockMatch = aiResponse.match(/```(?:json)?\s*([\S\s]*?)```/);
    if (jsonBlockMatch && jsonBlockMatch[1]) {
      jsonContent = jsonBlockMatch[1].trim();
    } else {
      // If no code block, try to find object pattern
      const jsonMatch = aiResponse.match(
        /{\s*".*"\s*:\s*".*"\s*,\s*".*"\s*:\s*".*"\s*}/s
      );
      if (jsonMatch) {
        jsonContent = jsonMatch[0];
      }
    }

    // Try to parse the JSON content
    try {
      const parsed = JSON.parse(jsonContent);

      if (
        parsed &&
        typeof parsed === 'object' &&
        parsed.name &&
        parsed.description
      ) {
        return {
          name: parsed.name,
          description: parsed.description,
        };
      }
    } catch {
      // Fallback to original requirements if AI processing fails
      return {
        name: repo.name,
        description: repo.description || 'GitHub repository analysis project',
      };
    }
  } catch (error) {
    log.error('Error generating project name and description:', error);
    return {
      name: repo.name,
      description: repo.description || 'GitHub repository analysis project',
    };
  }
}
