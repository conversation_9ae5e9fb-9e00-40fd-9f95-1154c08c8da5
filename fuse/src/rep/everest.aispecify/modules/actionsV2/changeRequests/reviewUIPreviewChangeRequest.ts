// eslint-disable-next-line eslint-comments/disable-enable-pair
/* eslint-disable @typescript-eslint/no-explicit-any */
import type { ISession } from '@everestsystems/content-core';
import { log } from '@everestsystems/content-core';
import { markChangeRequestError } from '@pkg/everest.aispecify/modules/actionsV2/changeRequests/changeRequestWorkflow';
import { AIChangeRequest } from '@pkg/everest.aispecify/types/AIChangeRequest';
import { AISpecProject } from '@pkg/everest.aispecify/types/AISpecProject';
import { AIUIPreview } from '@pkg/everest.aispecify/types/AIUIPreview';
import { EvstAIChangeRequestStatus as ChangeRequestStatus } from '@pkg/everest.aispecify/types/enums/AIChangeRequestStatus';
import { isString } from 'lodash';

export interface ReviewUIPreviewChangeRequestInput {
  changeRequestId: number;
  approved: boolean;
  rejectionReason?: string;
  selectedPageIndices?: number[]; // Indices of pages to approve from the generated list
}

export interface ReviewUIPreviewChangeRequestResponse {
  success: boolean;
  message: string;
  changeRequestId: number;
  uiPreviewIds?: number[];
  generatedContent?: string;
  pages?: UIPreviewPage[];
  version?: string;
  quality?: string;
  totalGenTime?: number;
  status: string;
  totalGenerated?: number;
  totalApproved?: number;
}

type UIPreviewPage = {
  pageName: string;
  htmlContent?: string; // New field name from generateUIPreview
  genTimer?: number;
};

export type ChangeRequestUIPreviewData = {
  id: number | null;
  content: string; // The full content string
  pages: UIPreviewPage[]; // Array of generated pages
  quality: string;
  version: string;
  totalGenTime: number;
  generatedContent: string;
};

/**
 * Reviews (approves or rejects) a UI preview change request with selective approval
 * Creates AIUIPreview entries for selected pages
 */
export async function reviewUIPreviewChangeRequest(
  session: ISession,
  input: ReviewUIPreviewChangeRequestInput
): Promise<ReviewUIPreviewChangeRequestResponse> {
  try {
    log.info(
      `Processing selective review for UI preview change request ID: ${input.changeRequestId}, approved: ${input.approved}`
    );

    // Get the change request
    const changeRequest = await AIChangeRequest.read(
      session,
      { id: input.changeRequestId },
      ['id', 'projectId', 'artifactType', 'status', 'data', 'active', 'old']
    );

    if (!changeRequest) {
      throw new Error(
        `Change request with ID ${input.changeRequestId} not found`
      );
    }

    if (changeRequest.status === ChangeRequestStatus.Success) {
      throw new Error(
        `Change request ${input.changeRequestId} is in success status.`
      );
    }

    // Extract UI preview data from change request
    const changeRequestData = changeRequest.data as {
      content: ChangeRequestUIPreviewData[];
    };
    const requestData = changeRequestData.content[0];

    log.info(`Change request data: ${JSON.stringify(requestData)}`);

    // Parse the pages data
    const pages = isString(requestData?.pages)
      ? JSON.parse(requestData.pages)
      : requestData?.pages;

    if (!pages || !Array.isArray(pages)) {
      throw new Error(
        `Change request ${input.changeRequestId} is missing required data (UI preview pages)`
      );
    }

    const generatedPages: UIPreviewPage[] = pages;

    if (!input.approved) {
      // Rejection logic
      if (input.selectedPageIndices && input.selectedPageIndices.length > 0) {
        // Partial rejection - remove selected pages, keep the rest
        const remainingPages = generatedPages.filter(
          (_, index) => !input.selectedPageIndices.includes(index)
        );

        if (remainingPages.length === 0) {
          // All pages rejected - mark as error
          await AIChangeRequest.update(
            session,
            { id: input.changeRequestId },
            {
              status: ChangeRequestStatus.Error,
              data: {
                error: input.rejectionReason || 'All UI preview pages rejected',
                rejectedPages: generatedPages,
              },
            }
          );

          return {
            success: true,
            message:
              input.rejectionReason ||
              'All UI preview pages rejected. Change request marked as error.',
            changeRequestId: input.changeRequestId,
            status: ChangeRequestStatus.Error,
          };
        } else {
          // Some pages remain - keep change request active
          await AIChangeRequest.update(
            session,
            { id: input.changeRequestId },
            {
              status: ChangeRequestStatus.RequiresApproval,
              data: {
                ...requestData,
                pages: remainingPages,
              },
            }
          );

          return {
            success: true,
            message: `Partial rejection: ${input.selectedPageIndices.length} pages rejected. ${remainingPages.length} pages remaining for review.`,
            changeRequestId: input.changeRequestId,
            status: ChangeRequestStatus.RequiresApproval,
          };
        }
      } else {
        // Full rejection
        await AIChangeRequest.update(
          session,
          { id: input.changeRequestId },
          {
            status: ChangeRequestStatus.Error,
            data: {
              error: input.rejectionReason || 'UI preview pages rejected',
              rejectedPages: generatedPages,
            },
          }
        );

        return {
          success: true,
          message:
            input.rejectionReason ||
            'UI preview pages rejected. Change request marked as error.',
          changeRequestId: input.changeRequestId,
          status: ChangeRequestStatus.Error,
        };
      }
    }

    // Approval logic
    // Determine which pages to create
    const pagesToCreate =
      input.selectedPageIndices && input.selectedPageIndices.length > 0
        ? input.selectedPageIndices.map((index) => generatedPages[index])
        : generatedPages; // If no specific selection, approve all

    // Get the current version for UI previews
    const existingPreviews = await AIUIPreview.query(
      session,
      { where: { projectId: changeRequest.projectId } },
      ['htmlVersion', 'id', 'pageName']
    );

    const currentVersion =
      existingPreviews.length > 0
        ? Math.max(...existingPreviews.map((p) => Number(p.htmlVersion) || 0))
        : 0;

    const newVersion = currentVersion + 1;

    // Create or update AIUIPreview entries for selected pages
    const createdUIPreviewIds: number[] = [];

    for (const pageData of pagesToCreate) {
      try {
        // Check if a UI preview with the same page name already exists for this project
        const existingPreviews = await AIUIPreview.query(
          session,
          {
            where: {
              projectId: changeRequest.projectId,
              pageName: pageData.pageName,
            },
          },
          ['id', 'pageName']
        );

        const existingPreview =
          existingPreviews.length > 0 ? existingPreviews[0] : null;

        let uiPreviewId: number;

        if (existingPreview) {
          // Update existing UI preview
          await AIUIPreview.update(
            session,
            {
              id: existingPreview.id,
            },
            {
              htmlContent: pageData.htmlContent,
              htmlVersion: requestData.version,
              quality: requestData.quality,
              genTimer: String(pageData.genTimer),
            }
          );
          uiPreviewId = existingPreview.id;
          log.info(
            `Updated existing UI Preview record for page "${pageData.pageName}" with ID: ${uiPreviewId}, genTimer: ${pageData.genTimer}ms`
          );
        } else {
          // Create new UI preview
          const uiPreview = await AIUIPreview.create(
            session,
            {
              htmlContent: pageData.htmlContent,
              htmlVersion: requestData.version,
              projectId: changeRequest.projectId,
              quality: requestData.quality,
              genTimer: String(pageData.genTimer),
              pageName: pageData.pageName,
            },
            [
              'id',
              'htmlContent',
              'htmlVersion',
              'quality',
              'genTimer',
              'pageName',
            ]
          );
          uiPreviewId = uiPreview.id;
          log.info(
            `Created new UI Preview record for page "${pageData.pageName}" with ID: ${uiPreviewId}, genTimer: ${pageData.genTimer}ms`
          );
        }

        createdUIPreviewIds.push(uiPreviewId);
      } catch (error) {
        log.error(
          `Failed to create/update UI Preview record for page "${pageData.pageName}": ${error.message}`
        );
        // Continue with other pages even if one fails
      }
    }

    if (createdUIPreviewIds.length === 0) {
      throw new Error('Failed to create any UI Preview records');
    }

    // Check if all pages have been processed
    const remainingPages = generatedPages.filter(
      (_, index) => !input.selectedPageIndices?.includes(index)
    );

    const allProcessed = input.selectedPageIndices
      ? remainingPages.length === 0
      : true; // If no specific selection, we're processing all

    if (allProcessed) {
      // All pages processed - mark as success
      await AIChangeRequest.update(
        session,
        { id: input.changeRequestId },
        {
          status: ChangeRequestStatus.Success,
          data: {
            id: createdUIPreviewIds,
            content: requestData.content, // Keep the original content
            pages: pagesToCreate,
            totalGenerated: generatedPages.length,
            totalApproved: pagesToCreate.length,
          },
        }
      );

      return {
        success: true,
        message: `Change request completed: created ${createdUIPreviewIds.length} UI preview pages out of ${generatedPages.length} generated`,
        changeRequestId: input.changeRequestId,
        uiPreviewIds: createdUIPreviewIds,
        generatedContent: requestData.content,
        pages: remainingPages,
        version: String(newVersion),
        quality: requestData.quality,
        totalGenTime: requestData.totalGenTime,
        status: ChangeRequestStatus.Success,
        totalGenerated: generatedPages.length,
        totalApproved: pagesToCreate.length,
      };
    } else {
      // Partial processing - keep change request active with remaining pages
      await AIChangeRequest.update(
        session,
        { id: input.changeRequestId },
        {
          status: ChangeRequestStatus.RequiresApproval, // Keep as requires approval
          data: {
            ...requestData,
            pages: remainingPages, // Only remaining pages
          },
        }
      );

      return {
        success: true,
        message: `Partial approval: created ${createdUIPreviewIds.length} UI preview pages. ${remainingPages.length} pages remaining for review.`,
        changeRequestId: input.changeRequestId,
        uiPreviewIds: createdUIPreviewIds,
        generatedContent: requestData.content,
        pages: remainingPages,
        version: String(newVersion),
        quality: requestData.quality,
        totalGenTime: requestData.totalGenTime,
        status: ChangeRequestStatus.RequiresApproval,
        totalGenerated: generatedPages.length,
        totalApproved: pagesToCreate.length,
      };
    }
  } catch (error) {
    log.error(
      error instanceof Error
        ? error.message
        : 'Error processing UI preview change request'
    );

    // Try to update change request status to error if possible
    try {
      await markChangeRequestError(session, input.changeRequestId, error);
    } catch (updateError) {
      log.error('Error updating change request status to error', updateError);
    }

    error.message = `REVIEW_UI_PREVIEW_CHANGE_REQUEST: ${error.message}`;
    throw error;
  }
}
