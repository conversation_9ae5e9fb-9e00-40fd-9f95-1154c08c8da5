/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable @typescript-eslint/no-explicit-any */
import type { ISession } from '@everestsystems/content-core';
import { log } from '@everestsystems/content-core';
import { callAIWithFallback } from '@pkg/everest.aispecify/actions/utils/aiHelper';
import {
  fixMarkdownHeaders,
  fixMermaidDiagrams,
} from '@pkg/everest.aispecify/actions/utils/markdownHelpers';
import { reviewUIPreviewChangeRequest } from '@pkg/everest.aispecify/modules/actionsV2/changeRequests/reviewUIPreviewChangeRequest';
import { AISpecProject } from '@pkg/everest.aispecify/types/AISpecProject';
import { AISpecRequirement } from '@pkg/everest.aispecify/types/AISpecRequirement';
import { AIUIPreview } from '@pkg/everest.aispecify/types/AIUIPreview';
import { EvstAIChangeRequestType } from '@pkg/everest.aispecify/types/enums/AIChangeRequestType';
import { EvstSpecArtifactType } from '@pkg/everest.aispecify/types/enums/SpecArtifactType';

import {
  createChangeRequest,
  markChangeRequestError,
  markChangeRequestSuccess,
  updateChangeRequestWithContent,
} from '../../changeRequests/changeRequestWorkflow';
import {
  calculateNextVersion,
  extractArtifactContent,
  findExistingArtifact,
  getArtifactPrompt,
} from '../utils/artifactUtils';
import {
  extractPageNamesFromPagesArtifact,
  getPageDetailsByName,
} from '../utils/pageUtils';
import { cleanHtmlContent } from './utils';

/**
 * Checks for existing UI previews with similar or exact page names to prevent duplicates
 * @param session The current session
 * @param projectId The project ID
 * @param pageNames Array of page names to check for duplicates
 * @returns Object with existing previews and filtered page names
 */
async function checkForExistingUIPreviewDuplicates(
  session: ISession,
  projectId: number,
  pageNames: string[]
): Promise<{
  existingPreviews: any[];
  filteredPageNames: string[];
  duplicatePageNames: string[];
}> {
  try {
    // Get all existing UI previews for this project
    const existingPreviews = await AIUIPreview.query(
      session,
      { where: { projectId: projectId } },
      ['id', 'pageName', 'htmlVersion']
    );

    if (!existingPreviews || existingPreviews.length === 0) {
      log.info(`No existing UI previews found for project ${projectId}`);
      return {
        existingPreviews: [],
        filteredPageNames: pageNames,
        duplicatePageNames: [],
      };
    }

    const existingPageNames = existingPreviews
      .map((preview) => preview.pageName?.toLowerCase() || '')
      .filter((name) => name.length > 0);
    const duplicatePageNames: string[] = [];
    const filteredPageNames: string[] = [];

    // Check each page name for duplicates
    for (const pageName of pageNames) {
      const lowercasePageName = pageName.toLowerCase();

      // Check for exact match
      const exactMatch = existingPageNames.includes(lowercasePageName);

      // Check for similar matches (contains or is contained)
      const similarMatch = existingPageNames.some((existingName) => {
        if (existingName === lowercasePageName) {
          return true;
        } // exact match

        // Remove common words and check for similarity
        const cleanExisting = existingName
          .replaceAll(/\b(page|screen|view|interface|panel|form|modal)\b/gi, '')
          .trim();
        const cleanNew = lowercasePageName
          .replaceAll(/\b(page|screen|view|interface|panel|form|modal)\b/gi, '')
          .trim();

        if (cleanExisting.length > 0 && cleanNew.length > 0) {
          return (
            cleanExisting.includes(cleanNew) || cleanNew.includes(cleanExisting)
          );
        }

        return false;
      });

      if (exactMatch || similarMatch) {
        duplicatePageNames.push(pageName);
        const matchingPreview = existingPreviews.find(
          (preview) =>
            preview.pageName?.toLowerCase() === lowercasePageName ||
            existingPageNames.some((existingName) => {
              const cleanExisting = existingName
                .replaceAll(
                  /\b(page|screen|view|interface|panel|form|modal)\b/gi,
                  ''
                )
                .trim();
              const cleanNew = lowercasePageName
                .replaceAll(
                  /\b(page|screen|view|interface|panel|form|modal)\b/gi,
                  ''
                )
                .trim();
              return (
                cleanExisting.length > 0 &&
                cleanNew.length > 0 &&
                (cleanExisting.includes(cleanNew) ||
                  cleanNew.includes(cleanExisting))
              );
            })
        );
        log.info(
          `Found duplicate UI preview for page "${pageName}": existing preview ID ${matchingPreview?.id}`
        );
      } else {
        filteredPageNames.push(pageName);
      }
    }

    log.info(
      `Duplicate check complete: ${duplicatePageNames.length} duplicates found, ${filteredPageNames.length} new pages to generate`
    );

    return {
      existingPreviews,
      filteredPageNames,
      duplicatePageNames,
    };
  } catch (error) {
    log.error('Error checking for duplicate UI previews:', error);
    // In case of error, return original page names to avoid blocking generation
    return {
      existingPreviews: [],
      filteredPageNames: pageNames,
      duplicatePageNames: [],
    };
  }
}

/**
 * Enhanced context summarization that preserves ALL critical design information
 * Always uses AI to create a comprehensive summary and persists it to the project
 */
async function summarizeContext(
  session: ISession,
  projectId: number,
  contextInfo: string
): Promise<string> {
  log.info(
    `Starting AI summarization for project ${projectId}, content length: ${contextInfo.length}`
  );

  const summarizationPrompt = `CRITICAL: You must preserve ALL design-related information while summarizing this project context for UI generation.

MANDATORY PRESERVATION REQUIREMENTS:
1. ALL color specifications (hex codes, color names, palettes, schemes)
2. ALL typography details (fonts, sizes, weights, styles)
3. ALL spacing and layout specifications
4. ALL component styling requirements
5. ALL theme details and visual identity elements
6. ALL branding guidelines and design constraints
7. ALL UI patterns and interaction specifications
8. ALL responsive design requirements

SUMMARIZATION STRATEGY:
- Preserve 100% of design specifications verbatim
- Condense non-design content (user flows, business logic) while keeping key points
- Maintain the structure with clear sections for each artifact type
- Keep ALL technical styling details intact
- Preserve any design system or component library references
- Focus on core functionality and features
- Essential user flows and critical business requirements

NEVER OMIT OR TRUNCATE:
- Color codes or color names
- Font specifications
- Spacing values
- Border radius, shadows, or visual effects
- Component styling rules
- Theme configurations
- Visual hierarchy specifications

Context to summarize (PRESERVE ALL DESIGN DETAILS):
${contextInfo}`;

  const summary = await callAIWithFallback(
    session,
    [{ role: 'user', content: summarizationPrompt }],
    projectId,
    {
      temperature: 0.2, // Lower temperature for more precise preservation
      topP: 0.9,
      timeoutMs: 100_000, // Longer timeout for thorough summarization
    }
  );

  if (!summary) {
    throw new Error('AI summarization failed and returned empty response');
  }

  log.info(
    `AI summarization completed for project ${projectId}, summary length: ${summary.length}`
  );

  // Immediately persist the context summary to the project
  try {
    log.info(`Persisting context summary to project ${projectId}`);

    // Get current project details
    const project = await AISpecProject.read(session, { id: projectId }, [
      'id',
      'additionalDetails',
    ]);

    if (project) {
      // Get existing additionalDetails or create new object
      const currentAdditionalDetails =
        (project.additionalDetails as Record<string, any>) || {};

      // Add uiContextSummary to additionalDetails
      const updatedAdditionalDetails = {
        ...currentAdditionalDetails,
        uiContextSummary: summary,
        uiContextSummaryUpdatedAt: new Date().toISOString(),
      };

      // Update the project with the new additionalDetails
      await AISpecProject.update(
        session,
        { id: projectId },
        { additionalDetails: updatedAdditionalDetails }
      );

      log.info(
        `Successfully persisted context summary to project ${projectId}`
      );
    } else {
      log.warn(
        `Project ${projectId} not found for context summary persistence`
      );
    }
  } catch (error) {
    log.error(
      `Failed to persist context summary to project ${projectId}: ${error.message}`
    );
    // Continue with summarization even if persistence fails
  }

  return summary;
}

/**
 * Intelligent page prioritization based on project type and requirements
 */
function prioritizeRequirements(requirements: any[]): any[] {
  if (!requirements || requirements.length === 0) {
    return [];
  }

  // Sort by priority and type, keeping only the most relevant ones
  return requirements
    .sort((a, b) => {
      const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
      const aPriority = priorityOrder[a.priority?.toLowerCase()] || 1;
      const bPriority = priorityOrder[b.priority?.toLowerCase()] || 1;
      return bPriority - aPriority;
    })
    .slice(0, 8); // Limit to top 8 requirements
}

/**
 * Enhanced UI generation with optimizations for quality and efficiency
 * @param session The current session
 * @param projectId The project ID
 * @param name Optional name for the UI preview
 * @param customPrompt Optional custom prompt for generation
 * @param quality Generation quality setting for speed vs quality trade-offs
 * @param maxPages Maximum number of pages to generate
 * @param useCache Whether to use cached results
 * @param changeRequestMetadata Optional metadata for the change request
 * @param autoApprove Whether to auto approve the change request
 * @returns The generated UI preview artifact data
 */
export async function generateUIPreview(
  session: ISession,
  projectId: number,
  name?: string,
  customPrompt?: string,
  quality: 'fast' | 'balanced' | 'premium' = 'balanced',
  maxPages?: number,
  useCache: boolean = false,
  autoApprove: boolean = true,
  changeRequestMetadata?: Record<string, unknown>
): Promise<{ data: Record<string, unknown> }> {
  let changeRequestId: number | null = null;

  try {
    log.info(`Generating UI preview for project ID: ${projectId}`);

    const numericProjectId = projectId;
    if (Number.isNaN(numericProjectId)) {
      throw new TypeError('Invalid project ID');
    }

    // Get project information
    const projectData = await AISpecProject.query(
      session,
      { where: { id: numericProjectId } },
      ['id', 'name', 'description', 'version', 'projectLanguage']
    );
    if (!projectData || projectData.length === 0) {
      throw new Error(`Project with ID ${projectId} not found`);
    }
    const project = projectData[0];

    // Get the five main artifact types that will inform UI preview generation
    const artifactTypes = [
      EvstSpecArtifactType.Pages,
      EvstSpecArtifactType.Design,
      EvstSpecArtifactType.UserFlows,
      EvstSpecArtifactType.UserStories,
      EvstSpecArtifactType.Automation,
    ];

    // Fetch existing artifacts of the main types using findExistingArtifact
    const artifacts = await Promise.all(
      artifactTypes.map((type) =>
        findExistingArtifact(session, numericProjectId, type)
      )
    );

    // Extract content from found artifacts
    const latestArtifacts: Record<string, string> = {};

    for (const [index, artifact] of artifacts.entries()) {
      const typeKey = artifactTypes[index] as string;
      if (artifact) {
        latestArtifacts[typeKey] = extractArtifactContent(artifact);
        log.info(
          `Found ${artifactTypes[index]} artifact with content length: ${latestArtifacts[typeKey].length}`
        );
      } else {
        log.info(
          `No ${artifactTypes[index]} artifact found for project ${projectId}`
        );
      }
    }

    // Get requirements data as fallback context
    const requirementsData = await AISpecRequirement.query(
      session,
      { where: { projectId: numericProjectId } },
      ['id', 'title', 'description', 'priority', 'type']
    );

    // Optimize requirements based on priority and relevance
    const prioritizedRequirements = prioritizeRequirements(requirementsData);

    // Get the appropriate prompt using the utility function
    const prompt = await getArtifactPrompt(
      session,
      numericProjectId,
      EvstSpecArtifactType.UIPreview,
      customPrompt,
      project.name,
      project.projectLanguage
    );

    // Build context information from artifacts and requirements - PRESERVE ALL CONTENT
    let contextInfo = '';

    // Add artifact information WITHOUT truncation to preserve all design details
    const pagesKey = EvstSpecArtifactType.Pages as string;
    const designKey = EvstSpecArtifactType.Design as string;
    const userFlowsKey = EvstSpecArtifactType.UserFlows as string;
    const userStoriesKey = EvstSpecArtifactType.UserStories as string;
    const automationKey = EvstSpecArtifactType.Automation as string;

    if (latestArtifacts[pagesKey]) {
      const content = latestArtifacts[pagesKey];
      contextInfo += `## Pages Specification\n${content}\n\n`;
    }

    if (latestArtifacts[designKey]) {
      const content = latestArtifacts[designKey];
      // Design artifact is critical - preserve ALL content for styling, themes, colors, etc.
      contextInfo += `## Design Specification (PRIMARY DESIGN SOURCE)\n${content}\n\n`;
    }

    if (latestArtifacts[userFlowsKey]) {
      const content = latestArtifacts[userFlowsKey];
      contextInfo += `## User Flows\n${content}\n\n`;
    }

    if (latestArtifacts[userStoriesKey]) {
      const content = latestArtifacts[userStoriesKey];
      contextInfo += `## User Stories\n${content}\n\n`;
    }

    if (latestArtifacts[automationKey]) {
      const content = latestArtifacts[automationKey];
      contextInfo += `## Automation Strategy\n${content}\n\n`;
    }

    // Add prioritized requirements as additional context
    if (prioritizedRequirements && prioritizedRequirements.length > 0) {
      const requirementsInfo = prioritizedRequirements
        .map(
          (req) =>
            `### ${req.title || 'Untitled Requirement'}\n**Priority:** ${
              req.priority || 'Not specified'
            }\n**Description:** ${req.description || 'No description provided'}`
        )
        .join('\n\n');
      contextInfo += `## Key Requirements\n${requirementsInfo}\n\n`;
    }

    // Always create AI-generated context summary and persist it to project
    log.info('Creating AI-generated context summary for all artifacts');
    const uiContextSummary = await summarizeContext(
      session,
      numericProjectId,
      contextInfo
    );

    // Use summarized version for AI generation to stay within limits
    contextInfo = uiContextSummary;

    // Check if we have sufficient context to generate UI previews
    const hasArtifacts = Object.keys(latestArtifacts).length > 0;
    const hasRequirements = requirementsData && requirementsData.length > 0;

    if (!hasArtifacts && !hasRequirements) {
      throw new Error(
        `No artifacts (Pages, Design, UserFlows, UserStories) or requirements found for project ID ${projectId}. Please generate these artifacts first.`
      );
    }

    log.info(
      `Context built with ${
        Object.keys(latestArtifacts).length
      } artifacts and ${requirementsData?.length || 0} requirements`
    );

    // Create change request for new artifact generation
    const changeRequestResult = await createChangeRequest({
      session,
      artifactId: null, // null for new artifact generation
      artifactType: EvstSpecArtifactType.UIPreview,
      projectId: numericProjectId,
      existingArtifactContent: null, // null for new artifact
      changeType: EvstAIChangeRequestType.Create,
      metadata: changeRequestMetadata || {
        actionType: 'generate',
        initializerAction: 'generateUIPreview',
      },
    });

    changeRequestId = changeRequestResult.changeRequestId;

    // STEP 1: Extract exact page names from the Pages artifact
    log.info('Step 1: Extracting exact page names from Pages artifact');

    // Use the pageUtils to extract page names from the Pages artifact
    const extractedPageNames = await extractPageNamesFromPagesArtifact(
      session,
      numericProjectId
    );

    let pagesToGenerate;
    if (!extractedPageNames || extractedPageNames.length === 0) {
      // Fallback to AI planning if no pages found in artifact
      log.warn('No pages found in Pages artifact, falling back to AI planning');

      const planningSystemContent = `You are an AI specialized in analyzing project specifications and requirements to determine what pages need to be generated for a UI preview.
                   Your task is to analyze the provided information and create a structured plan of pages to generate.
                   
                   IMPORTANT INSTRUCTIONS:
                   1. Generate ONLY 3-4 ESSENTIAL pages maximum - focus on quality over quantity.
                   2. Choose the most important pages that showcase the core functionality.
                   3. MANDATORY: Skip all login, registration, authentication, and password-related pages.
                   4. MANDATORY: Always include at least one main page, dashboard, or overview page.
                   5. For each page, determine the page name and a detailed description of its purpose/content.
                   6. Return a JSON array with this structure: [{"pageName": "Page Name", "description": "Detailed description of page purpose and key components"}]
                   7. CRITICAL: Respond ONLY with a valid JSON array, no explanations or other text.
                   
                   FORBIDDEN PAGE TYPES (DO NOT GENERATE):
                   - Authentication: Login, Log in, Sign in, Signin, Register, Registration, Sign up, Signup, Auth, Forgot password, Reset password, Verify, Verification, Confirm, Confirmation
                   - Error pages: Error, 404, 403, 500, Not found, Page not found, Unauthorized, Forbidden, Server error, Maintenance
                   - Utility pages: Loading, Placeholder, Coming soon, Under construction, Terms, Privacy, Policy, Legal, Cookie, GDPR
                   - Support pages: Help, FAQ, Support, Contact, About us, About, Sitemap, Search results, Empty state, No results
                   
                   REQUIRED PAGE TYPES (ALWAYS INCLUDE AT LEAST ONE):
                   - Core pages: Dashboard, Main page, Home page, Overview page, Summary, Analytics, Landing, Index, Welcome, App, Application
                   - Management: Admin, Admin panel, Management, Manage, Control panel, Console, Workspace, Portal, Hub
                   - Business functionality: Project, Projects, Task, Tasks, User, Users, Customer, Customers, Order, Orders, Product, Products, Inventory, Report, Reports, Settings, Configuration, Profile, Account, Billing, Subscription, Plan, Team, Teams, Organization, Company, Business, Data, Database, Analytics, Metrics, Statistics, Workflow, Process, Campaign, Campaigns, Content, Media, Files, Documents, Gallery, Library`;

      const planningUserContent = `Analyze the following project information and determine what pages need to be generated:
                   
                   # Project Information
                   Name: ${project.name}
                   Description: ${project.description}
                   Version: ${project.version || '1.0.0'}
                   
                   # Context Information
                   ${contextInfo}
                   
                   Please analyze and return a JSON array of pages to generate with their descriptions.`;

      const planningMessages = [
        {
          role: 'system',
          content: planningSystemContent,
        },
        {
          role: 'user',
          content: planningUserContent,
        },
      ];

      // Get page planning from AI as fallback
      const planningResponse = await callAIWithFallback(
        session,
        planningMessages,
        numericProjectId,
        {
          temperature: 0.3, // Lower temperature for more consistent planning
          topP: 0.9,
          timeoutMs: 120_000, // 2 minute timeout for planning
        }
      );

      if (!planningResponse) {
        throw new Error(
          'AI planning failed: Empty response from AI planning model'
        );
      }

      // Clean and parse planning response
      let cleanedPlanningResponse = planningResponse.trim();
      const planningCodeBlockRegex = /```json\s*([\S\s]*?)```/g;
      const planningMatches = [
        ...cleanedPlanningResponse.matchAll(planningCodeBlockRegex),
      ];
      if (planningMatches.length > 0) {
        cleanedPlanningResponse = planningMatches[0][1].trim();
      }

      let aiPagesToGenerate: Array<{ pageName: string; description: string }>;
      try {
        aiPagesToGenerate = JSON.parse(cleanedPlanningResponse);
        if (!Array.isArray(aiPagesToGenerate)) {
          throw new TypeError('Planning response is not an array');
        }
      } catch (parseError) {
        throw new Error(
          `AI planning failed: Invalid JSON response - ${parseError.message}`
        );
      }

      // Use AI-generated pages as fallback
      pagesToGenerate = aiPagesToGenerate;
    } else {
      // Use the exact page names from the Pages artifact
      log.info(
        `Found ${
          extractedPageNames.length
        } pages in Pages artifact: ${extractedPageNames.join(', ')}`
      );

      // Get detailed information for each page from the Pages artifact
      const pagesWithDetails: Array<{ pageName: string; description: string }> =
        [];

      for (const pageName of extractedPageNames) {
        const pageDetails = await getPageDetailsByName(
          session,
          numericProjectId,
          pageName
        );
        const description =
          pageDetails?.description ||
          `${pageName} page as defined in the Pages artifact`;

        pagesWithDetails.push({
          pageName: pageName,
          description: description,
        });
      }

      pagesToGenerate = pagesWithDetails;
      log.info(
        `Using exact page names from Pages artifact: ${pagesToGenerate
          .map((p) => p.pageName)
          .join(', ')}`
      );
    }

    // FILTER OUT UNWANTED PAGES AND ENSURE MAIN/MANAGEMENT PAGES
    log.info(
      'Filtering pages to skip unwanted pages and prioritize main/management pages'
    );

    const unwantedPageKeywords = [
      // Authentication pages
      'login',
      'log in',
      'signin',
      'sign in',
      'sign-in',
      'register',
      'registration',
      'signup',
      'sign up',
      'sign-up',
      'auth',
      'authentication',
      'forgot password',
      'reset password',
      'verify',
      'verification',
      'confirm',
      'confirmation',
      // Error and utility pages
      'error',
      '404',
      '403',
      '500',
      'not found',
      'page not found',
      'unauthorized',
      'forbidden',
      'server error',
      'maintenance',
      'loading',
      'placeholder',
      'coming soon',
      'under construction',
      // Less useful pages for previews
      'terms',
      'privacy',
      'policy',
      'legal',
      'cookie',
      'gdpr',
      'help',
      'faq',
      'support',
      'contact',
      'about us',
      'about',
      'sitemap',
      'search results',
      'empty state',
      'no results',
    ];

    const essentialPageKeywords = [
      // Main application pages
      'dashboard',
      'main',
      'home',
      'overview',
      'summary',
      'analytics',
      'landing',
      'index',
      'welcome',
      'app',
      'application',
      // Management and admin pages
      'admin',
      'admin panel',
      'management',
      'manage',
      'control panel',
      'console',
      'workspace',
      'portal',
      'hub',
      // Core business functionality
      'project',
      'projects',
      'task',
      'tasks',
      'user',
      'users',
      'customer',
      'customers',
      'order',
      'orders',
      'product',
      'products',
      'inventory',
      'report',
      'reports',
      'settings',
      'configuration',
      'profile',
      'account',
      'billing',
      'subscription',
      'plan',
      'team',
      'teams',
      'organization',
      'company',
      'business',
      'data',
      'database',
      'analytics',
      'metrics',
      'statistics',
      'workflow',
      'process',
      'campaign',
      'campaigns',
      'content',
      'media',
      'files',
      'documents',
      'gallery',
      'library',
    ];

    // Filter out unwanted pages
    const filteredPages = pagesToGenerate.filter((page) => {
      const pageNameLower = page.pageName.toLowerCase();
      const pageDescriptionLower = page.description.toLowerCase();

      // Check if page is unwanted (auth, error, utility pages, etc.)
      const isUnwantedPage = unwantedPageKeywords.some(
        (keyword) =>
          pageNameLower.includes(keyword) ||
          pageDescriptionLower.includes(keyword)
      );

      if (isUnwantedPage) {
        log.info(`Skipping unwanted page: ${page.pageName}`);
        return false;
      }

      return true;
    });

    // Check if we have at least one essential page (main/management/dashboard)
    const hasEssentialPage = filteredPages.some((page) => {
      const pageNameLower = page.pageName.toLowerCase();
      const pageDescriptionLower = page.description.toLowerCase();

      return essentialPageKeywords.some(
        (keyword) =>
          pageNameLower.includes(keyword) ||
          pageDescriptionLower.includes(keyword)
      );
    });

    // If no essential page exists, add one or prioritize existing pages
    let finalPagesToGenerate = filteredPages;

    if (!hasEssentialPage && filteredPages.length > 0) {
      // Try to find a page that could serve as main page (first non-unwanted page)
      log.info(
        'No essential page found, promoting first available page as main page'
      );
      // Keep the existing pages as they are - they'll serve as main content
    } else if (!hasEssentialPage && filteredPages.length === 0) {
      // If all pages were filtered out or no pages exist, create a dashboard
      log.info(
        'All pages were filtered out or no pages exist, adding default dashboard'
      );
      finalPagesToGenerate = [
        {
          pageName: 'Dashboard',
          description:
            'Main dashboard page showcasing key features and overview of the application',
        },
      ];
    } else if (hasEssentialPage) {
      // Prioritize essential pages by moving them to the front
      const essentialPages = filteredPages.filter((page) => {
        const pageNameLower = page.pageName.toLowerCase();
        const pageDescriptionLower = page.description.toLowerCase();
        return essentialPageKeywords.some(
          (keyword) =>
            pageNameLower.includes(keyword) ||
            pageDescriptionLower.includes(keyword)
        );
      });

      const otherPages = filteredPages.filter((page) => {
        const pageNameLower = page.pageName.toLowerCase();
        const pageDescriptionLower = page.description.toLowerCase();
        return !essentialPageKeywords.some(
          (keyword) =>
            pageNameLower.includes(keyword) ||
            pageDescriptionLower.includes(keyword)
        );
      });

      // Put essential pages first to ensure they get generated if there are limits
      finalPagesToGenerate = [...essentialPages, ...otherPages];
      log.info(
        `Prioritized ${essentialPages.length} essential pages: ${essentialPages
          .map((p) => p.pageName)
          .join(', ')}`
      );
    }

    // Update pagesToGenerate with filtered and prioritized pages
    pagesToGenerate = finalPagesToGenerate;

    log.info(
      `After filtering: ${
        pagesToGenerate.length
      } pages to generate: ${pagesToGenerate.map((p) => p.pageName).join(', ')}`
    );

    // Apply quality-based limits and timeouts
    const qualitySettings = {
      fast: { maxPages: 2, timeout: 90_000, temperature: 0.7 },
      balanced: { maxPages: 3, timeout: 180_000, temperature: 0.8 },
      premium: { maxPages: 4, timeout: 240_000, temperature: 0.8 },
    };

    const currentQuality = quality;
    const settings = qualitySettings[currentQuality];
    const finalMaxPages = maxPages || settings.maxPages;

    // Limit pages based on quality setting
    if (pagesToGenerate.length > finalMaxPages) {
      pagesToGenerate = pagesToGenerate.slice(0, finalMaxPages);
      log.info(
        `Limited to ${finalMaxPages} pages for ${currentQuality} quality mode`
      );
    }

    // Check for existing duplicates before proceeding
    const pageNamesToCheck = pagesToGenerate.map((p) => p.pageName);
    const duplicateCheck = await checkForExistingUIPreviewDuplicates(
      session,
      numericProjectId,
      pageNamesToCheck
    );

    // Filter out pages that already have UI previews
    if (duplicateCheck.duplicatePageNames.length > 0) {
      log.info(
        `Skipping ${
          duplicateCheck.duplicatePageNames.length
        } pages that already have UI previews: ${duplicateCheck.duplicatePageNames.join(
          ', '
        )}`
      );

      // Filter pagesToGenerate to only include non-duplicate pages
      pagesToGenerate = pagesToGenerate.filter((page) =>
        duplicateCheck.filteredPageNames.includes(page.pageName)
      );

      if (pagesToGenerate.length === 0) {
        log.info(
          'All requested pages already have UI previews. No new pages to generate.'
        );

        // Mark change request as complete and successful if all requested pages already have UI previews
        await markChangeRequestSuccess(session, changeRequestId, null);

        return {
          data: {
            success: true,
            message: 'All requested pages already have UI previews',
            uiPreviewIds: duplicateCheck.existingPreviews.map((p) =>
              String(p.id)
            ),
            duplicatePageNames: duplicateCheck.duplicatePageNames,
            pagesGenerated: 0,
            totalGenTime: 0,
            existingPages: duplicateCheck.existingPreviews.map((p) => ({
              id: p.id,
              pageName: p.pageName || 'Unknown',
              htmlVersion: p.htmlVersion || 'Unknown',
            })),
          },
        };
      }
    }

    log.info(
      `Step 1 Complete: Planned ${
        pagesToGenerate.length
      } pages: ${pagesToGenerate.map((p) => p.pageName).join(', ')}`
    );

    // STEP 2: Generate pages with quality-optimized settings
    log.info(`Step 2: Generating pages in ${currentQuality} quality mode`);

    // Enhanced prompt based on quality level
    const getQualityPrompt = (quality: string) => {
      switch (quality) {
        case 'fast': {
          return 'Create a clean, functional page with essential components. Focus on core functionality over visual polish.';
        }
        case 'premium': {
          return `Create an EXTRAORDINARY, cutting-edge page that showcases premium design excellence:
          
          PREMIUM DESIGN FEATURES (MANDATORY):
          - Advanced glassmorphism effects with multiple backdrop-blur layers
          - Sophisticated gradient combinations using colors from design specification
          - Creative use of shadows, glows, and depth effects
          - Innovative layout patterns and asymmetrical designs
          - Advanced micro-interactions and hover states
          - Premium typography with creative font weight combinations
          - Artistic use of spacing and negative space
          - Creative icon arrangements and visual hierarchies
          - Advanced grid systems with creative breakouts
          - Sophisticated color psychology and brand storytelling
          
          VISUAL INNOVATION REQUIREMENTS:
          - Use creative gradient combinations with colors from design specification
          - Implement advanced shadow systems with appropriate opacity levels
          - Create depth with multiple backdrop-blur layers
          - Use creative border-radius combinations: rounded-3xl, rounded-tl-3xl
          - Implement sophisticated hover animations: hover:scale-105, hover:rotate-1
          - Create visual interest with creative transforms and transitions
          - Use advanced opacity and blend modes for depth
          - Implement creative loading states and skeleton screens
          
          PREMIUM UI PATTERNS:
          - Floating action buttons with creative positioning
          - Advanced card designs with creative layouts
          - Sophisticated navigation with creative indicators
          - Premium form designs with creative validation states
          - Advanced data visualization with creative charts
          - Creative use of badges, tags, and status indicators
          - Sophisticated modal and overlay designs
          - Premium empty states with creative illustrations using icons`;
        }
        default: {
          return 'Create a professional, well-designed page with modern UI components and good visual hierarchy.';
        }
      }
    };

    // Generate all pages concurrently with quality-optimized settings
    const pageGenerationPromises = pagesToGenerate.map(
      async (pageInfo, index) => {
        const pageStartTime = Date.now(); // Track generation time for this page
        log.info(
          `Starting generation for page ${index + 1}/${
            pagesToGenerate.length
          }: ${pageInfo.pageName}`
        );

        // Quality-specific system prompts with distinct requirements
        const getSystemPrompt = (quality: string) => {
          const baseRequirements = `
                   CRITICAL JSON FORMAT REQUIREMENTS - MUST FOLLOW EXACTLY:
                   1. Return ONLY valid JSON in this exact format: {"pageName": "${pageInfo.pageName}", "pageContent": "HTML_CONTENT_HERE"}
                   2. ESCAPE ALL QUOTES in HTML content using \\" (backslash quote)
                   3. ESCAPE ALL NEWLINES in HTML content using \\n
                   4. ESCAPE ALL BACKSLASHES in HTML content using \\\\
                   5. NO EXPLANATIONS, NO MARKDOWN, NO CODE BLOCKS - ONLY RAW JSON
                   6. Ensure the JSON is valid and parseable
                   
                   BASIC REQUIREMENTS:
                   - Use Tailwind CSS CDN: <script src="https://cdn.tailwindcss.com"></script>
                   - MANDATORY: Use ONLY ICONS - NO IMAGES ANYWHERE (use Heroicons SVG icons)
                   - Use proper DOCTYPE and meta tags
                   - Ensure clean HTML structure with proper spacing
                   - Use semantic HTML elements
                   
                   EXAMPLE VALID JSON FORMAT:
                   {"pageName": "Dashboard", "pageContent": "<!DOCTYPE html>\\n<html lang=\\"en\\">\\n<head>\\n  <meta charset=\\"UTF-8\\">\\n  <meta name=\\"viewport\\" content=\\"width=device-width, initial-scale=1.0\\">\\n  <title>Dashboard</title>\\n  <script src=\\"https://cdn.tailwindcss.com\\"></script>\\n</head>\\n<body class=\\"p-6\\">\\n  <div class=\\"container mx-auto\\">\\n    <h1 class=\\"text-3xl font-bold\\">Dashboard</h1>\\n  </div>\\n</body>\\n</html>"}`;

          switch (quality) {
            case 'fast': {
              return `You are a UI developer focused on creating FAST, FUNCTIONAL pages with streamlined styling.
                   
                   FAST QUALITY GUIDELINES - PRIORITIZE SIMPLICITY:
                   - PREFER basic Tailwind classes based on design specification: p-4, m-2, border
                   - FOCUS ON simple layouts, basic cards, standard buttons
                   - AVOID complex effects when possible: minimize gradients, shadows, animations
                   - Basic navigation: Simple horizontal list with text links
                   - Simple content areas: Basic divs with minimal padding
                   - Standard typography: text-xl, text-base, text-sm primarily
                   - Basic responsive: sm: and md: breakpoints primarily
                   - Prioritize functionality and speed over visual complexity
                   - Keep styling clean and minimal
                   
                   FAST MODE PREFERENCES (use when simple alternatives work):
                   - PREFER basic backgrounds over backdrop-blur effects
                   - PREFER simple gradients (bg-gradient-to-r) over complex ones
                   - PREFER shadow-sm over shadow-xl, shadow-2xl
                   - PREFER simple hover states over complex transforms
                   - PREFER rounded-md over rounded-xl
                   
                   FLEXIBILITY: Use slightly more advanced styling when essential for functionality or basic usability, but always prioritize simplicity and performance.
                   
                   ${baseRequirements}`;
            }

            case 'premium': {
              return `You are an ELITE UI/UX designer creating EXTRAORDINARY, cutting-edge pages that showcase premium design excellence.
                   
                   PREMIUM QUALITY REQUIREMENTS - MAXIMUM CREATIVITY:
                   - MANDATORY: Advanced glassmorphism with backdrop-blur-xl, backdrop-blur-2xl
                   - MANDATORY: Complex gradients using colors from design specification
                   - MANDATORY: Advanced shadows with appropriate opacity levels
                   - MANDATORY: Creative border-radius: rounded-3xl, rounded-tl-3xl, rounded-br-3xl
                   - MANDATORY: Sophisticated animations: hover:scale-105, hover:rotate-1, transition-all duration-500
                   - MANDATORY: Advanced spacing: space-y-8, gap-8, p-8, m-6
                   - MANDATORY: Premium typography: text-4xl, text-6xl, font-black, font-extralight
                   - MANDATORY: Creative layouts: asymmetrical grids, overlapping elements
                   - MANDATORY: Advanced responsive: sm:, md:, lg:, xl:, 2xl: breakpoints
                   - MANDATORY: Multiple backdrop layers for depth
                   - MANDATORY: Creative positioning: absolute, relative, z-index variations
                   
                   PREMIUM VISUAL EFFECTS (ALL REQUIRED):
                   - Floating elements with creative positioning
                   - Multiple gradient overlays using design specification colors
                   - Advanced card designs with creative shapes
                   - Sophisticated navigation with animated indicators
                   - Premium form designs with creative validation states
                   - Advanced data visualization with creative charts using CSS
                   - Creative use of opacity and blend modes for depth
                   - Innovative icon arrangements and visual hierarchies
                   
                   PREMIUM UI PATTERNS (MANDATORY):
                   - Hero sections with overlapping elements
                   - Floating action buttons with creative shadows
                   - Advanced sidebar designs with glassmorphism
                   - Creative modal and overlay designs
                   - Premium empty states with artistic icon compositions
                   - Sophisticated loading states and skeleton screens
                   - Advanced grid systems with creative breakouts
                   
                   ${baseRequirements}`;
            }

            default: {
              // balanced
              return `You are a professional UI/UX designer creating well-designed pages with modern UI components.
                   
                   BALANCED QUALITY REQUIREMENTS - PROFESSIONAL DESIGN:
                   - Use appropriate Tailwind classes based on design specification
                   - Professional gradients using colors from design specification
                   - Modern shadows: shadow-lg, shadow-xl for depth
                   - Smooth animations: hover:scale-105, transition-all duration-300
                   - Professional spacing: space-y-6, gap-6, p-6, m-4
                   - Modern typography: text-2xl, text-3xl, font-bold, font-semibold
                   - Professional layouts: grid layouts, flex designs
                   - Good responsive design: sm:, md:, lg: breakpoints
                   - Modern UI components: cards, buttons, navigation
                   - Professional color palette consistent with design specification
                   
                   BALANCED FEATURES:
                   - Glassmorphism effects with appropriate opacity levels
                   - Modern navigation with hover effects
                   - Professional card designs with rounded corners
                   - Standard form designs with focus states
                   - Modern button styles with appropriate gradients
                   - Professional typography hierarchy
                   - Good visual hierarchy and spacing
                   
                   ${baseRequirements}`;
            }
          }
        };

        const singlePageSystemContent = getSystemPrompt(currentQuality);

        // Check if design artifact exists - required for UI preview generation
        const designKey = EvstSpecArtifactType.Design as string;
        const hasDesignArtifact = latestArtifacts[designKey];

        if (!hasDesignArtifact) {
          throw new Error(
            `Design artifact is required for UI preview generation. Please generate a Design specification first for project "${project.name}" (ID: ${projectId}). UI previews must be based on explicit design specifications to ensure consistency and accuracy.`
          );
        }

        // Extract colors and themes explicitly from design artifact
        const designGuidelines = `
                   🎨 DESIGN ARTIFACT (READ CAREFULLY - THIS IS YOUR COLOR BIBLE) 🎨
                   ${latestArtifacts[designKey]}
                   
                   🚨 MANDATORY COLOR EXTRACTION & IMPLEMENTATION 🚨
                   
                   STEP 1 - COLOR EXTRACTION (REQUIRED):
                   Read the design artifact above and extract EVERY color mentioned:
                   - Primary colors (main brand colors)
                   - Secondary colors (accent colors)
                   - Background colors
                   - Text colors
                   - Button colors
                   - Border colors
                   - Gradient colors
                   - Any hex codes (#123456)
                   - Any color names (red, blue, green, etc.)
                   - Any RGB/HSL values
                   
                   STEP 2 - MANDATORY COLOR IMPLEMENTATION:
                   For EVERY UI element, you MUST use colors from the design artifact:
                   - Backgrounds: Use specified background colors ONLY
                   - Text: Use specified text colors ONLY  
                   - Buttons: Use specified button/accent colors ONLY
                   - Borders: Use specified border colors ONLY
                   - Cards/Containers: Use specified container colors ONLY
                   - Links: Use specified link colors ONLY
                   - Navigation: Use specified navigation colors ONLY
                   
                   STEP 3 - TAILWIND COLOR CONVERSION:
                   Convert design colors to Tailwind CSS:
                   - For hex colors: bg-[#FF6B6B], text-[#2E86C1], border-[#28B463]
                   - For named colors: bg-red-500, text-blue-600, border-green-500
                   - For gradients: from-[#FF6B6B] to-[#2E86C1]
                   
                   🔥 FORBIDDEN DEFAULT COLORS 🔥
                   DO NOT USE these default colors unless specified in design artifact:
                   - purple-*, blue-*, slate-*, gray-*, indigo-*, violet-*
                   - Any Tailwind default colors not mentioned in design artifact
                   
                   ✅ REQUIRED COLOR COMPLIANCE CHECK ✅
                   Before using ANY color class, ask yourself:
                   1. Is this color explicitly mentioned in the design artifact?
                   2. Am I using the exact color specified?
                   3. Have I avoided all default colors not in the design?
                   
                   🎯 EXAMPLE IMPLEMENTATIONS:
                   - Design says "primary: #E74C3C" → Use bg-[#E74C3C], text-[#E74C3C]
                   - Design says "background: light gray" → Use bg-gray-100 or bg-[#F8F9FA]
                   - Design says "accent: gold" → Use bg-yellow-500 or bg-[#FFD700]
                   - Design says "text: dark navy" → Use text-navy-900 or text-[#1B263B]`;

        const singlePageUserContent = `Create an exceptional "${
          pageInfo.pageName
        }" page for "${project.name}":

                   PROJECT CONTEXT:
                   - Name: ${project.name}
                   - Description: ${project.description}
                   - Page Purpose: ${pageInfo.description}

                   CONTEXT INFORMATION:
                   ${contextInfo}

                   ${prompt}

                   QUALITY-SPECIFIC REQUIREMENTS:
                   ${getQualityPrompt(currentQuality)}

                   ${designGuidelines}

                   TECHNICAL REQUIREMENTS (ALWAYS REQUIRED):
                   1. FRAMEWORK AND ICONS:
                      - Use Tailwind CSS script tag: <script src="https://cdn.tailwindcss.com"></script>
                      - Use ONLY SVG icons (Heroicons style) - NO img tags or external images
                      - For user avatars: use icon placeholders with colored backgrounds
                      - For logos: use simple SVG icon designs
                      - For illustrations: use icon combinations or simple geometric shapes
                      - Example icon usage: <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">...</svg>
                   
                   2. RESPONSIVE DESIGN:
                      - Mobile-first approach with responsive classes
                      - Use appropriate grid and flex layouts
                      - Responsive text and spacing
                      - Responsive navigation and components
                   
                   3. MODERN UI COMPONENTS:
                      - Professional form inputs with appropriate focus states
                      - Modern navigation with hover effects
                      - Status badges and indicators as needed
                      - Interactive elements with smooth transitions
                   
                   CRITICAL: Follow the design artifact specifications exactly when provided. If no design artifact is available, create clean, professional pages appropriate for the application context.
                   Use ONLY Tailwind CSS classes and SVG icons - NO custom CSS, NO images.`;

        const singlePageMessages = [
          {
            role: 'system',
            content: singlePageSystemContent,
          },
          {
            role: 'user',
            content: singlePageUserContent,
          },
        ];

        const pageResponse = await callAIWithFallback(
          session,
          singlePageMessages,
          numericProjectId,
          {
            temperature: settings.temperature,
            topP: 0.95, // Higher quality responses
            timeoutMs: settings.timeout, // Quality-based timeout
          }
        );

        if (!pageResponse) {
          throw new Error(
            `AI generation failed for page "${pageInfo.pageName}": Empty response from AI model`
          );
        }

        // Enhanced JSON parsing with multiple fallback strategies
        let pageData;
        let cleanedResponse = pageResponse.trim();

        log.info(
          `Raw AI response for ${
            pageInfo.pageName
          } (first 200 chars): ${cleanedResponse.slice(0, 200)}`
        );

        // Strategy 1: Try to extract JSON from code blocks
        const codeBlockRegex = /```json\s*([\S\s]*?)```/g;
        const matches = [...cleanedResponse.matchAll(codeBlockRegex)];
        if (matches.length > 0) {
          cleanedResponse = matches[0][1].trim();
          log.info(`Extracted from code block for ${pageInfo.pageName}`);
        }

        // Strategy 2: Check if response starts with HTML (common error case)
        if (
          cleanedResponse.startsWith('<!DOCTYPE') ||
          cleanedResponse.startsWith('<html')
        ) {
          log.warn(
            `AI returned raw HTML instead of JSON for ${pageInfo.pageName}, wrapping in JSON structure`
          );

          // Use JSON.stringify to properly escape the HTML content
          try {
            pageData = {
              pageName: pageInfo.pageName,
              pageContent: cleanedResponse,
            };

            // Validate by attempting to stringify and parse
            const testJson = JSON.stringify(pageData);
            JSON.parse(testJson);

            log.info(
              `Successfully wrapped raw HTML in JSON for ${pageInfo.pageName}`
            );
          } catch (wrapError) {
            log.error(
              `Failed to wrap HTML in JSON for ${pageInfo.pageName}: ${wrapError.message}`
            );
            // Fall through to other strategies
            pageData = null;
          }
        }

        // Strategy 3: Try direct JSON parsing (if not already parsed)
        if (!pageData) {
          try {
            pageData = JSON.parse(cleanedResponse);
            log.info(`Direct JSON parsing successful for ${pageInfo.pageName}`);
          } catch (parseError) {
            log.warn(
              `Direct JSON parsing failed for ${pageInfo.pageName}: ${parseError.message}`
            );

            // Strategy 4: Try to find JSON object boundaries
            try {
              const jsonStart = cleanedResponse.indexOf('{"pageName"');
              const jsonEnd = cleanedResponse.lastIndexOf('}') + 1;

              if (jsonStart !== -1 && jsonEnd > jsonStart) {
                // eslint-disable-next-line unicorn/prefer-string-slice
                const extractedJson = cleanedResponse.substring(
                  jsonStart,
                  jsonEnd
                );
                pageData = JSON.parse(extractedJson);
                log.info(
                  `JSON boundary extraction successful for ${pageInfo.pageName}`
                );
              } else {
                throw new Error('Could not find JSON boundaries');
              }
            } catch (boundaryError) {
              log.warn(
                `JSON boundary extraction failed for ${pageInfo.pageName}: ${boundaryError.message}`
              );

              // Strategy 5: Use regex to extract pageName and pageContent separately
              try {
                const pageNameMatch = cleanedResponse.match(
                  /"pageName"\s*:\s*"([^"]+)"/
                );
                const pageContentMatch = cleanedResponse.match(
                  /"pageContent"\s*:\s*"([\S\s]*?)"\s*}?\s*$/
                );

                if (pageNameMatch && pageContentMatch) {
                  // Unescape the content
                  const unescapedContent = pageContentMatch[1]
                    .replaceAll('\\"', '"')
                    .replaceAll('\\n', '\n')
                    .replaceAll('\\t', '\t')
                    .replaceAll('\\r', '\r')
                    .replaceAll('\\\\', '\\');

                  pageData = {
                    pageName: pageNameMatch[1],
                    pageContent: unescapedContent,
                  };
                  log.info(
                    `Regex extraction successful for ${pageInfo.pageName}`
                  );
                } else {
                  throw new Error(
                    'Could not extract pageName and pageContent with regex'
                  );
                }
              } catch (regexError) {
                log.warn(
                  `Regex extraction failed for ${pageInfo.pageName}: ${regexError.message}`
                );

                // Strategy 6: Check if response contains HTML without JSON wrapper
                if (
                  cleanedResponse.includes('<html') ||
                  cleanedResponse.includes('<!DOCTYPE')
                ) {
                  log.warn(
                    `Found HTML content without JSON wrapper for ${pageInfo.pageName}, creating JSON structure`
                  );

                  // Extract just the HTML part if there's extra text
                  let htmlContent = cleanedResponse;
                  const htmlStart = cleanedResponse.indexOf('<!DOCTYPE');
                  if (htmlStart === -1) {
                    const htmlTagStart = cleanedResponse.indexOf('<html');
                    if (htmlTagStart !== -1) {
                      htmlContent = cleanedResponse.slice(
                        Math.max(0, htmlTagStart)
                      );
                    }
                  } else {
                    htmlContent = cleanedResponse.slice(Math.max(0, htmlStart));
                  }

                  pageData = {
                    pageName: pageInfo.pageName,
                    pageContent: cleanHtmlContent(htmlContent),
                  };
                  log.info(
                    `Created JSON structure from HTML content for ${pageInfo.pageName}`
                  );
                } else {
                  // Strategy 7: Last resort - save original response
                  log.warn(
                    `All parsing strategies failed for ${pageInfo.pageName}, saving original AI response`
                  );

                  pageData = {
                    pageName: pageInfo.pageName,
                    pageContent: pageResponse.trim(), // Use original AI response
                  };

                  log.warn(
                    `Used original AI response for ${pageInfo.pageName} due to parsing failure`
                  );
                }
              }
            }
          }
        }

        // Validate the final result
        if (!pageData || !pageData.pageName || !pageData.pageContent) {
          throw new Error(
            `AI generation failed for page "${pageInfo.pageName}": Missing required fields after all parsing attempts`
          );
        }

        // Clean the HTML content to remove escape characters and formatting issues
        pageData.pageContent = cleanHtmlContent(pageData.pageContent);
        log.info(`Cleaned HTML content for ${pageInfo.pageName}`);

        // Additional validation: ensure pageContent looks like HTML
        if (
          !pageData.pageContent.includes('<html') &&
          !pageData.pageContent.includes('<!DOCTYPE')
        ) {
          log.warn(
            `Generated content for ${pageInfo.pageName} doesn't appear to be valid HTML, wrapping in basic structure`
          );
          // Use neutral styling that doesn't override design artifact specifications
          pageData.pageContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${pageData.pageName}</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="p-4">
    ${pageData.pageContent}
</body>
</html>`;
        }

        // Calculate generation time for this page
        const pageEndTime = Date.now();
        const genTimer = pageEndTime - pageStartTime;

        log.info(
          `✓ Generated ${pageInfo.pageName} successfully in ${genTimer}ms`
        );

        // Return page data with generation time
        return {
          ...pageData,
          genTimer: genTimer,
        };
      }
    );

    // Wait for all pages to complete
    log.info('Waiting for all pages to complete...');
    let generatedPagesData;
    try {
      generatedPagesData = await Promise.all(pageGenerationPromises);
    } catch (error) {
      log.error('Error in page generation promises:', error);
      throw new Error(`Page generation failed: ${error.message}`);
    }

    // Validate final results with detailed logging
    log.info(`Generated pages data length: ${generatedPagesData.length}`);
    if (generatedPagesData.length === 0) {
      log.error(
        'No pages were generated successfully. Check AI responses and JSON parsing.'
      );
      throw new Error(
        'No pages generated successfully - all page generation attempts failed'
      );
    }

    // Log successful pages
    const successfulPages = generatedPagesData.filter(
      (page) => page && page.pageName && page.pageContent
    );
    log.info(
      `Successfully generated ${successfulPages.length} out of ${generatedPagesData.length} pages`
    );

    if (successfulPages.length === 0) {
      log.error('All generated pages are invalid or missing required fields');
      throw new Error(
        'No valid pages generated - all pages missing required fields'
      );
    }

    // Use only successful pages
    generatedPagesData = successfulPages;

    // Validate each page object
    for (const page of generatedPagesData) {
      if (!page.pageName || !page.pageContent) {
        throw new Error('Invalid page object structure from AI model');
      }
    }

    log.info(
      `Generation Complete: Successfully generated ${
        generatedPagesData.length
      } pages: ${generatedPagesData.map((p) => p.pageName).join(', ')}`
    );

    // Find existing UI Preview artifact to determine versioning
    const existingArtifact = await findExistingArtifact(
      session,
      numericProjectId,
      EvstSpecArtifactType.UIPreview
    );
    const versionInfo = calculateNextVersion(existingArtifact, false);

    // Generate semantic version string
    const version = `v${versionInfo.majorVersionNumber}.${versionInfo.minorVersionNumber}.${versionInfo.patchNumber}`;

    // Create combined content for backward compatibility
    let generatedContent = `# UI Preview for ${project.name}\n\n`;
    generatedContent += `## Overview\n\nThis UI preview contains ${generatedPagesData.length} essential pages that showcase the core functionality of ${project.name}.\n\n`;
    generatedContent += `### Generated Pages:\n${generatedPagesData
      .map((p, i) => `${i + 1}. **${p.pageName}**`)
      .join('\n')}\n\n`;

    for (const [index, page] of generatedPagesData.entries()) {
      generatedContent += `## ${index + 1}. ${page.pageName}\n\n`;
      generatedContent += `### HTML Code\n\n`;
      generatedContent += `\`\`\`html\n${page.pageContent}\n\`\`\`\n\n`;
    }

    generatedContent = fixMermaidDiagrams(
      fixMarkdownHeaders(generatedContent.trim())
    );

    const contentToReturn = {
      generatedContent: generatedContent,
      uiPreviewIds: [],
      version: version,
      quality: currentQuality,
      pagesGenerated: generatedPagesData.length,
      totalGenTime: generatedPagesData.reduce(
        (total, page) => total + (page.genTimer || 0),
        0
      ),
      pages: generatedPagesData.map((page) => ({
        pageName: page.pageName,
        genTimer: page.genTimer,
        htmlContent: cleanHtmlContent(page.pageContent),
      })),
      uiContextSummary: uiContextSummary, // Include context summary for persistence
    };

    // Update change request with generated content
    await updateChangeRequestWithContent(
      session,
      changeRequestId,
      [contentToReturn],
      autoApprove
    );

    if (autoApprove) {
      // Approve Change Request
      await reviewUIPreviewChangeRequest(session, {
        changeRequestId: changeRequestId,
        approved: true,
      });
      // Return normal response
      return {
        data: {
          success: true,
          ...contentToReturn,
          changeRequestId: String(changeRequestId),
        },
      };
    } else {
      // return empty response because approval is required
      return {
        data: {
          success: false,
          generatedContent: '',
          uiPreviewIds: [],
          version: version,
          quality: currentQuality,
          pagesGenerated: 0,
          totalGenTime: generatedPagesData.reduce(
            (total, page) => total + (page.genTimer || 0),
            0
          ),
          pages: [],
          changeRequestId: String(changeRequestId),
        },
      };
    }
  } catch (error) {
    log.error('Error generating UI preview', error);
    await markChangeRequestError(session, changeRequestId, error);
    error.message = `UIPREVIEW: ${error.message}`;
    throw error;
  }
}
