import type { ISession } from '@everestsystems/content-core';
import { log } from '@everestsystems/content-core';
import { callAIWithFallback } from '@pkg/everest.aispecify/actions/utils/aiHelper';
import { AISpecInput } from '@pkg/everest.aispecify/types/AISpecInput';
import { AISpecProject } from '@pkg/everest.aispecify/types/AISpecProject';
import { EvstHttpRequestMethod } from '@pkg/everest.appserver/types/enums/HttpRequestMethod';

import { generateRequirementsFromText } from '../requirements/generateRequirementsFromText';

type GithubToken = `github_${string}` | `ghp_${string}`;

// Define the input type for the action
export type GenerateRequirementsFromGitHubInput = {
  projectId: string;
  maxRepos?: number;
  token?: string;
};

// Define the response type to match the pattern used in generateRequirementsFromText
export type GenerateRequirementsFromGitHubResponse = {
  requirements: Array<{
    id: number;
    title: string;
    description: string;
    priority: string;
    type: string;
    projectId: number;
  }>;
  success: boolean;
  changeRequestId?: number;
  requiresApproval?: boolean;
  message?: string;
};

// GitHub API interfaces
interface GitHubRepo {
  id: number;
  name: string;
  full_name: string;
  description: string | null;
  html_url: string;
  clone_url: string;
  language: string | null;
  stargazers_count: number;
  forks_count: number;
  topics: string[];
  updated_at: string;
  size: number;
  default_branch?: string;
}

interface GitHubSearchResponse {
  total_count: number;
  incomplete_results: boolean;
  items: GitHubRepo[];
}

interface GitHubFile {
  name: string;
  path: string;
  sha: string;
  size: number;
  url: string;
  html_url: string;
  git_url: string;
  download_url: string | null;
  type: string;
  content?: string;
  encoding?: string;
}

interface GitHubTreeItem {
  path: string;
  mode: string;
  type: string;
  sha: string;
  size?: number;
  url: string;
}

interface GitHubTree {
  sha: string;
  url: string;
  tree: GitHubTreeItem[];
  truncated: boolean;
}

interface AnalyzedRepo {
  repo: GitHubRepo;
  relevantFiles: GitHubFile[];
  extractedRequirements: string[];
}

interface GitHubResearchResult {
  searchQuery: string;
  totalReposFound: number;
  analyzedRepos: AnalyzedRepo[];
  consolidatedRequirements: string[];
  timestamp: string;
}

function isGithubToken(token: string): token is GithubToken {
  return token.startsWith('ghp_') || token.startsWith('github_');
}

function injectToken(req: any, token?: GithubToken) {
  if (!token) {
    return req;
  }
  const { headers = {}, ...rest } = req;

  return {
    ...rest,
    headers: {
      ...headers,
      Authorization: `Bearer ${token}`,
    },
  };
}

/**
 * Performs GitHub research for a project by searching for related repositories,
 * analyzing their code, and extracting requirements using AI
 *
 * @param session Session environment
 * @param input Input parameters including projectId, maxRepos, and autoApprove
 * @returns Promise with GitHub research results
 */
export async function generateRequirementsFromGitHubResearch(
  session: ISession,
  input: GenerateRequirementsFromGitHubInput
): Promise<GenerateRequirementsFromGitHubResponse> {
  try {
    const { projectId, maxRepos, token } = input;

    log.info(`Starting GitHub research for project ID: ${projectId}`);

    if (token && !isGithubToken(token)) {
      throw new Error('Invalid Token received. Please provide a valid Token.');
    }

    if (Number.isNaN(Number.parseInt(projectId))) {
      throw new TypeError('Invalid project ID');
    }

    const projectData = await AISpecProject.query(
      session,
      { where: { id: Number.parseInt(projectId) } },
      ['id', 'name', 'description']
    );

    if (!projectData || projectData.length === 0) {
      throw new Error(`Project with ID ${projectId} not found`);
    }

    const project = projectData[0];
    const projectName = project.name;
    const projectDescription = project.description || '';

    // Always generate search terms using AI based on project information
    const searchTerms = await generateSearchTermsFromProject(
      session,
      projectName,
      projectDescription,
      5
    );
    log.info(`Generated AI search terms: ${searchTerms}`);

    const searchQuery = searchTerms;
    const maxReposToAnalyze = maxRepos || 10;

    // 3. Search GitHub repositories
    const repos = await searchGitHubRepositories(
      session,
      searchQuery,
      maxReposToAnalyze,
      token as GithubToken
    );

    if (repos.length === 0) {
      log.warn('No repositories found for the search query');
      return {
        requirements: [],
        success: true,
      };
    }

    log.info(`Found ${repos.length} repositories to analyze`);

    // 4. Analyze each repository
    const analyzedRepos: AnalyzedRepo[] = [];
    for (const repo of repos) {
      try {
        log.info(`Analyzing repository: ${repo.full_name}`);
        const analyzed = await analyzeRepository(
          session,
          repo,
          Number.parseInt(projectId),
          token as GithubToken
        );
        analyzedRepos.push(analyzed);
      } catch (error) {
        log.error(`Error analyzing repository ${repo.full_name}:`, error);
        // Continue with other repositories
      }
    }

    if (analyzedRepos.length === 0) {
      log.warn('No repositories could be analyzed successfully');
      return {
        requirements: [],
        success: true,
      };
    }

    // 5. Consolidate findings
    const result = await consolidateFindings(
      session,
      analyzedRepos,
      Number.parseInt(projectId),
      searchQuery
    );

    log.info(`GitHub research completed for project ${projectId}`);

    // Deduplicate and prioritize the consolidated requirements
    const finalRequirements = await deduplicateAndPrioritizeRequirements(
      session,
      result.consolidatedRequirements,
      searchQuery
    );

    // Build a comprehensive text context from the GitHub research
    const textContext = `
GitHub Research Results for: ${searchQuery}

Total Repositories Analyzed: ${result.analyzedRepos.length}

${result.analyzedRepos
  .map(
    (analyzed, index) => `
Repository ${index + 1}: ${analyzed.repo.full_name}
- Description: ${analyzed.repo.description || 'No description'}
- Language: ${analyzed.repo.language || 'Unknown'}
- Stars: ${analyzed.repo.stargazers_count}
- Topics: ${analyzed.repo.topics.join(', ') || 'None'}

Key Features and Requirements Found:
${analyzed.extractedRequirements.map((req) => `- ${req}`).join('\n')}
`
  )
  .join('\n---\n')}

Consolidated Requirements:
${finalRequirements.map((req) => `- ${req}`).join('\n')}
`;

    // Save analyzed repositories as AISpecInput records
    try {
      const inputPromises = result.analyzedRepos.map((analyzed) => {
        // Create content object with repository URL and analyzed content
        const repoContent = {
          url: analyzed.repo.html_url,
          name: analyzed.repo.full_name,
          description: analyzed.repo.description || '',
          language: analyzed.repo.language || '',
          stars: analyzed.repo.stargazers_count,
          topics: analyzed.repo.topics,
          analyzedFiles: analyzed.relevantFiles.map((file) => ({
            path: file.path,
            content: file.content,
          })),
          extractedRequirements: analyzed.extractedRequirements,
        };

        return AISpecInput.create(session, {
          content: repoContent,
          type: 'URL',
          subType: 'githubRepo',
          projectId: Number.parseInt(projectId),
        });
      });

      await Promise.all(inputPromises);
      log.info(
        `Created ${result.analyzedRepos.length} AISpecInput records for GitHub repositories`
      );
    } catch (error) {
      log.warn(
        'Failed to create AISpecInput records for GitHub repositories:',
        error
      );
      // Continue with requirement generation even if storing fails
    }

    // Use generateRequirementsFromText with the consolidated context
    const requirementsResult = await generateRequirementsFromText(session, {
      text: textContext,
      projectId: Number.parseInt(projectId),
      autoApprove: false,
    });

    // Return the result from generateRequirementsFromText
    return requirementsResult;
  } catch (error) {
    log.error('Error in generateRequirementsFromGitHubResearch:', error);
    return {
      requirements: [],
      success: false,
    };
  }
}

/**
 * Search GitHub repositories using the search API
 */
async function searchGitHubRepositories(
  session: ISession,
  query: string,
  maxRepos: number,
  token?: GithubToken
): Promise<GitHubRepo[]> {
  const headers: Record<string, string> = {
    Accept: 'application/vnd.github.v3+json',
    'User-Agent': 'Everest-AI-Specify',
  };

  // Search for repositories
  const searchUrl = `https://api.github.com/search/repositories?q=${encodeURIComponent(
    query
  )}&sort=stars&order=desc&per_page=${Math.min(maxRepos, 100)}`;

  const response = await session.fetchProvider.request(
    injectToken(
      {
        url: new URL(searchUrl),
        method: EvstHttpRequestMethod.GET,
        headers,
      },
      token
    )
  );

  if (response.status !== 200) {
    throw new Error(
      `GitHub API error: ${response.status} ${response.statusText}`
    );
  }

  const searchResult = response.data as GitHubSearchResponse;
  return searchResult.items.slice(0, maxRepos);
}

/**
 * Analyze a single repository to extract relevant information
 */
async function analyzeRepository(
  session: ISession,
  repo: GitHubRepo,
  projectId: number,
  token?: GithubToken
): Promise<AnalyzedRepo> {
  const headers: Record<string, string> = {
    Accept: 'application/vnd.github.v3+json',
    'User-Agent': 'Everest-AI-Specify',
  };

  // Get repository tree to find relevant files
  const treeUrl = `https://api.github.com/repos/${repo.full_name}/git/trees/${
    repo.default_branch || 'main'
  }?recursive=1`;

  let tree: GitHubTree;
  try {
    const treeResponse = await session.fetchProvider.request(
      injectToken(
        {
          url: new URL(treeUrl),
          method: EvstHttpRequestMethod.GET,
          headers,
        },
        token
      )
    );

    if (treeResponse.status === 200) {
      tree = treeResponse.data as GitHubTree;
    } else {
      log.warn(
        `Could not fetch tree for ${repo.full_name}: ${treeResponse.status}`
      );
      tree = { sha: '', url: '', tree: [], truncated: false };
    }
  } catch (error) {
    log.warn(`Error fetching tree for ${repo.full_name}:`, error);
    tree = { sha: '', url: '', tree: [], truncated: false };
  }

  // Filter for relevant files
  const relevantFiles = await getRelevantFiles(
    session,
    repo,
    tree,
    headers,
    token
  );

  // Extract requirements using AI
  const extractedRequirements = await extractRequirementsFromFiles(
    session,
    repo,
    relevantFiles,
    projectId
  );

  return {
    repo,
    relevantFiles,
    extractedRequirements,
  };
}

/**
 * Get relevant files from repository (README, package.json, requirements.txt, etc.)
 */
async function getRelevantFiles(
  session: ISession,
  repo: GitHubRepo,
  tree: GitHubTree,
  headers: Record<string, string>,
  token?: GithubToken
): Promise<GitHubFile[]> {
  const relevantFilePatterns = [
    /^readme\.(md|txt|rst)$/i,
    /^package\.json$/,
    /^requirements\.txt$/,
    /^Cargo\.toml$/,
    /^pom\.xml$/,
    /^build\.gradle$/,
    /^Gemfile$/,
    /^composer\.json$/,
    /^setup\.py$/,
    /^\.env\.example$/,
    /^docker-compose\.ya?ml$/,
    /^Dockerfile$/,
    /^\.github\/workflows\/.+\.ya?ml$/,
    /^docs?\/.+\.(md|txt|rst)$/i,
    /^architecture\.(md|txt|rst)$/i,
    /^design\.(md|txt|rst)$/i,
  ];

  const relevantTreeItems = tree.tree
    .filter(
      (item) =>
        item.type === 'blob' &&
        relevantFilePatterns.some((pattern) => pattern.test(item.path)) &&
        (item.size || 0) < 100_000 // Skip very large files
    )
    .slice(0, 20); // Limit to 20 files max

  const files: GitHubFile[] = [];

  for (const item of relevantTreeItems) {
    try {
      const fileResponse = await session.fetchProvider.request(
        injectToken(
          {
            url: new URL(item.url),
            method: EvstHttpRequestMethod.GET,
            headers,
          },
          token
        )
      );

      if (fileResponse.status === 200) {
        const fileData = fileResponse.data as GitHubFile;

        // Decode content if it's base64 encoded
        if (fileData.content && fileData.encoding === 'base64') {
          try {
            // remove newline characters from file content
            fileData.content = fileData.content.replaceAll(/[\n\r]+/g, '');
            fileData.content = session.util.encoding
              .toBuffer(fileData.content, 'base64')
              .toString('utf8');
          } catch (decodeError) {
            log.warn(`Could not decode content for ${item.path}:`, decodeError);
            fileData.content = '';
          }
        }

        files.push(fileData);
      }
    } catch (error) {
      log.warn(`Error fetching file ${item.path}:`, error);
    }
  }

  return files;
}

/**
 * Extract requirements from files using AI analysis (including tech stack and patterns as technical requirements)
 */
async function extractRequirementsFromFiles(
  session: ISession,
  repo: GitHubRepo,
  files: GitHubFile[],
  projectId: number
): Promise<string[]> {
  if (files.length === 0) {
    return [];
  }

  // Prepare content for AI analysis
  const fileContents = files
    .map((file) => ({
      url: file.url,
      content: file.content || '',
    }))
    .slice(0, 10); // Limit to first 10 files to avoid token limits

  const prompt = `
You are analyzing a GitHub repository to extract software requirements. Based on the following files from the repository "${
    repo.name
  }", extract a comprehensive list of requirements.

Repository: ${repo.name}
Description: ${repo.description || 'No description'}

Files to analyze:
${fileContents
  .map(
    (file, index) =>
      `${index + 1}. ${file.url}:\n${file.content.slice(0, 2000)}`
  )
  .join('\n\n')}

Extract ALL types of requirements from this codebase:
- Functional requirements (what the system does)
- Non-functional requirements (performance, security, scalability, etc.)
- Technical requirements (specific technologies, frameworks, databases as requirements)
- Architectural requirements (design patterns, architectural patterns as requirements)
- User requirements (user stories, use cases)
- Business requirements (goals, constraints)

Return a JSON array of requirement strings. Each requirement should be clear, specific, and actionable.

Example format:
[
  "The system must provide user authentication and authorization",
  "The application should support real-time data synchronization",
  "The system must use React as the frontend framework",
  "The application should implement the MVC architectural pattern",
  "The API must follow RESTful design principles",
  "The system should use PostgreSQL for data persistence"
]

IMPORTANT: Return ONLY the JSON array. Do not include any markdown formatting, code blocks, or additional text. Do not wrap the response in \`\`\`json or \`\`\`.

Focus on extracting 15-25 comprehensive requirements that cover all aspects of the system.

DO NOT include any additional text or explanations in your response. Only return the JSON array of requirements.
`;

  const aiResponse = await callAIWithFallback(
    session,
    [{ role: 'user', content: prompt }],
    projectId
  );

  // Parse AI response
  let cleanResponse = aiResponse.trim();

  // Clean markdown code blocks if present
  cleanResponse = cleanResponse.replaceAll(/^```(?:json|JSON)?\s*/gm, '');
  cleanResponse = cleanResponse.replaceAll(/```\s*$/gm, '');

  // Try to extract JSON array using regex if needed
  const jsonMatch =
    cleanResponse.match(/\[\s*{.*}\s*]/s) || cleanResponse.match(/\[[\S\s]*?]/);

  if (jsonMatch) {
    cleanResponse = jsonMatch[0];
  }

  let requirements: string[] = [];

  try {
    const parsed = JSON.parse(cleanResponse);
    if (Array.isArray(parsed)) {
      requirements = parsed.filter(
        (req) => typeof req === 'string' && req.trim().length > 0
      );
    }
  } catch {
    // Fallback to original requirements if AI processing fails
    requirements = [...new Set(requirements)]; // Remove exact duplicates
  }

  return requirements.slice(0, 25); // Increased limit since we're including everything
}

/**
 * Consolidate findings from all analyzed repositories
 */
async function consolidateFindings(
  session: ISession,
  analyzedRepos: AnalyzedRepo[],
  projectId: number,
  searchQuery: string
): Promise<GitHubResearchResult> {
  // Consolidate requirements
  const allRequirements = analyzedRepos.flatMap(
    (repo) => repo.extractedRequirements
  );
  const consolidatedRequirements = await deduplicateAndPrioritizeRequirements(
    session,
    allRequirements,
    searchQuery
  );

  return {
    searchQuery,
    totalReposFound: analyzedRepos.length,
    analyzedRepos,
    consolidatedRequirements,
    timestamp: new Date().toISOString(),
  };
}

/**
 * Deduplicate and prioritize requirements using AI
 */
async function deduplicateAndPrioritizeRequirements(
  session: ISession,
  requirements: string[],
  _searchQuery: string
): Promise<string[]> {
  if (requirements.length === 0) {
    return [];
  }

  const prompt = `
You have extracted the following requirements from multiple GitHub repositories:

${requirements.map((req, index) => `${index + 1}. ${req}`).join('\n')}

Please:
1. Remove duplicate or very similar requirements
2. Merge related requirements where appropriate
3. Prioritize the most important and actionable requirements
4. Ensure each requirement is clear and specific

Format your response as a JSON array of requirement strings.

IMPORTANT: Return ONLY the JSON array. Do not include any markdown formatting, code blocks, or additional text. Do not wrap the response in \`\`\`json or \`\`\`.

Example format:
[
  "The system must provide user authentication and authorization",
  "The application should support real-time data synchronization",
  "The system must use React as the frontend framework",
  "The application should implement the MVC architectural pattern",
  "The API must follow RESTful design principles",
  "The system should use PostgreSQL for data persistence"
]

DO NOT include any additional text or explanations in your response. Only return the JSON array of requirements.

`;

  try {
    const aiResponse = await callAIWithFallback(
      session,
      [{ role: 'user', content: prompt }],
      0 // Use a default project ID for AI calls
    );

    // Parse AI response
    let cleanResponse = aiResponse.trim();

    // Clean markdown code blocks if present
    cleanResponse = cleanResponse.replaceAll(/^```(?:json|JSON)?\s*/gm, '');
    cleanResponse = cleanResponse.replaceAll(/```\s*$/gm, '');

    // Try to extract JSON array using regex if needed
    const jsonMatch =
      cleanResponse.match(/\[\s*{.*}\s*]/s) ||
      cleanResponse.match(/\[[\S\s]*?]/);

    if (jsonMatch) {
      cleanResponse = jsonMatch[0];
    }

    let consolidatedRequirements: string[] = [];

    try {
      const parsed = JSON.parse(cleanResponse);
      if (Array.isArray(parsed)) {
        consolidatedRequirements = parsed.filter(
          (req) => typeof req === 'string' && req.trim().length > 0
        );
      }
    } catch {
      // Fallback to original requirements if AI processing fails
      consolidatedRequirements = [...new Set(requirements)]; // Remove exact duplicates
    }

    return consolidatedRequirements.slice(0, 20);
  } catch (error) {
    log.error('Error deduplicating and prioritizing requirements:', error);
    // Return original requirements with basic deduplication
    return [...new Set(requirements)].slice(0, 20);
  }
}

/**
 * Generate search terms from project information using AI
 */
async function generateSearchTermsFromProject(
  session: ISession,
  projectName: string,
  projectDescription: string,
  queryLength: number
): Promise<string> {
  const prompt = `
You are generating a concise GitHub search query for finding relevant repositories. Based on the project name and description, generate a short search query of 4-5 words maximum.

Project Name: ${projectName}
Project Description: ${projectDescription}

Generate a focused search query that will find the most relevant repositories on GitHub. The query should be:
- Maximum 4-5 words
- Focus on the main technology/domain
- Use common terms that developers would use
- Avoid overly specific or niche terms

Examples of good queries:
- "react typescript web app"
- "python machine learning"
- "nodejs express api"
- "vue.js dashboard"
- "flutter mobile app"

Return only the search query as a simple string. Do not include quotes, brackets, or any additional formatting.

IMPORTANT: Return ONLY the search query string. No JSON, no markdown, no additional text.
`;

  try {
    const aiResponse = await callAIWithFallback(
      session,
      [{ role: 'user', content: prompt }],
      0 // Use a default project ID for AI calls
    );

    // Clean and trim the response
    let searchQuery = aiResponse.trim();

    // Remove any quotes or extra formatting
    searchQuery = searchQuery.replaceAll(/["']/g, '');
    searchQuery = searchQuery.replaceAll(/^\[|]$/g, '');

    // Ensure it's not too long (split and take first queryLength words max)
    const words = searchQuery.split(/\s+/).filter((word) => word.length > 0);
    if (words.length > queryLength) {
      searchQuery = words.slice(0, queryLength).join(' ');
    }

    // Fallback if empty or too short
    if (!searchQuery || searchQuery.length < 3) {
      // Extract key terms from project name and description
      const projectWords = projectName.toLowerCase().split(/\s+/);
      const descWords = projectDescription.toLowerCase().split(/\s+/);
      const allWords = [...projectWords, ...descWords]
        .filter((word) => word.length > 2)
        .slice(0, 4);
      searchQuery = allWords.join(' ');
    }

    log.info(`Generated search query: "${searchQuery}"`);
    return searchQuery;
  } catch (error) {
    log.error('Error generating search terms from project info:', error);

    // Fallback: use project name
    const fallbackQuery = projectName
      .toLowerCase()
      .split(/\s+/)
      .slice(0, 3)
      .join(' ');
    log.info(`Using fallback search query: "${fallbackQuery}"`);
    return fallbackQuery;
  }
}
