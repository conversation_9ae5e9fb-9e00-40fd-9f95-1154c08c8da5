import type { ISession } from '@everestsystems/content-core';
import { log } from '@everestsystems/content-core';
import type { AISpecProjectActions } from '@pkg/everest.aispecify/types/presentations/modules/backend/AISpecProject/AISpecProjectActions';

import { initializeFromGitHub } from '../../actionsV2/project/initializeFromGitHub';
import {
  initializeProject,
  type InitiateProjectInput,
} from '../../actionsV2/project/initializeProject';
import throwOnAppdevMain from '../../actionsV2/utils/projectGuard';
import {
  type GithubSecret,
  projectProviderSecretExists,
  readProjectProviderSecret,
  saveProjectProviderSecret,
} from '../../actionsV2/utils/tenantSecretUtils';

const implementation: AISpecProjectActions.Implementation = {
  InitializeProject: {
    async execute({
      session,
      input,
    }: AISpecProjectActions.Actions.InitializeProject.Execute.Request): Promise<AISpecProjectActions.Actions.InitializeProject.Execute.Response> {
      throwOnAppdevMain(session);
      try {
        log.info('Executing InitiateProject action');

        // Call the implementation function with the provided inputs
        const result = await initializeProject(session as unknown as ISession, {
          name: input.name || undefined,
          description: input.description || undefined,
          projectType: input.projectType || undefined,
          textInput: input.textInput || undefined,
          files:
            input.files && input.files.length > 0 ? input.files : undefined,
          projectLanguage: input.projectLanguage || undefined,
          isEveProject: input.isEveProject,
          evePackageId: input.evePackageId || undefined,
          aiModel: input.aiModel || undefined,
          packageName: input.packageName || undefined,
        } satisfies InitiateProjectInput);

        // Return the response in the format expected by the presentation layer
        return {
          output: {
            projectId: result.projectId,
            success: result.success,
          },
        };
      } catch (error) {
        log.error('Error in InitializeProject action', error);
        throw error;
      }
    },
  },
  InitializeProjectFromGitHub: {
    async execute({
      session,
      input,
    }: AISpecProjectActions.Actions.InitializeProjectFromGitHub.Execute.Request): Promise<AISpecProjectActions.Actions.InitializeProjectFromGitHub.Execute.Response> {
      try {
        log.info('Executing InitializeProjectFromGitHub action');

        // If a GitHub token is provided, we could store it for the project
        // For now, we'll just pass it to the initialization function
        // TODO: Enhance initializeFromGitHub to accept optional token

        // Initialize project from GitHub repository
        const result = await initializeFromGitHub(
          session as unknown as ISession,
          input.url
        );

        if (!result.success) {
          throw new Error(`GitHub initialization failed: ${result.message}`);
        }

        // If a GitHub token was provided and project was created successfully, store it
        if (input.githubToken && result.projectId) {
          try {
            await saveProjectProviderSecret(
              session as unknown as ISession,
              result.projectId,
              'github',
              { token: input.githubToken } as GithubSecret
            );
            log.info(`Stored GitHub token for project ${result.projectId}`);
          } catch (tokenError) {
            log.warn('Failed to store GitHub token:', tokenError);
            // Don't fail the project creation if token storage fails
          }
        }

        log.info(
          `Project initialization completed successfully. ${result.message}`
        );

        // Return the response in the format expected by the presentation layer
        return result.projectId
          ? {
              output: {
                projectId: result.projectId,
                success: result.success,
              },
            }
          : {
              output: {
                projectId: 0,
                success: false,
              },
            };
      } catch (error) {
        log.error('Error in InitializeProjectFromGitHub action', error);
        return {
          output: {
            projectId: 0,
            success: false,
          },
        };
      }
    },
  },
  GetProjectSecret: {
    async execute({
      session,
      input,
    }: AISpecProjectActions.Actions.GetProjectSecret.Execute.Request): Promise<AISpecProjectActions.Actions.GetProjectSecret.Execute.Response> {
      try {
        log.info(
          `Getting secret for provider '${input.providerName}' in project ${input.projectId}`
        );

        // Check if the secret exists
        const exists = await projectProviderSecretExists(
          session as unknown as ISession,
          input.projectId,
          input.providerName
        );

        if (!exists) {
          return {
            output: {
              secretExists: false,
              secretValue: '',
            },
          };
        }

        // Read the secret value
        const secret = await readProjectProviderSecret<string>(
          session as unknown as ISession,
          input.projectId,
          input.providerName
        );

        // If it's a complex object (like GithubSecret), extract the token
        const secretValue: string =
          typeof secret === 'object' && secret !== null
            ? (secret as { token?: string }).token || JSON.stringify(secret)
            : secret;

        return {
          output: {
            secretExists: true,
            secretValue,
          },
        };
      } catch (error) {
        log.error(
          `Error getting secret for provider '${input.providerName}' in project ${input.projectId}:`,
          error
        );
        return {
          output: {
            secretExists: false,
            secretValue: '',
          },
        };
      }
    },
  },
  SetProjectSecret: {
    async execute({
      session,
      input,
    }: AISpecProjectActions.Actions.SetProjectSecret.Execute.Request): Promise<AISpecProjectActions.Actions.SetProjectSecret.Execute.Response> {
      try {
        log.info(
          `Setting secret for provider '${input.providerName}' in project ${input.projectId}`
        );

        // Determine the secret format based on provider name
        const secretToStore: string | GithubSecret =
          input.providerName.toLowerCase() === 'github'
            ? ({ token: input.secretValue } as GithubSecret)
            : input.secretValue;

        await saveProjectProviderSecret(
          session as unknown as ISession,
          input.projectId,
          input.providerName,
          secretToStore
        );

        log.info(
          `Successfully stored secret for provider '${input.providerName}' in project ${input.projectId}`
        );

        return {
          output: {
            success: true,
          },
        };
      } catch (error) {
        log.error(
          `Error setting secret for provider '${input.providerName}' in project ${input.projectId}:`,
          error
        );
        return {
          output: {
            success: false,
          },
        };
      }
    },
  },
};

export default implementation;
