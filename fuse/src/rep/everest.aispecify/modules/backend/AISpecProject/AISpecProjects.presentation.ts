import { log } from '@everestsystems/content-core';
import { deleteAllProjectSecrets } from '@pkg/everest.aispecify/modules/actionsV2/utils/tenantSecretUtils';
import { AIAssistSpecProject } from '@pkg/everest.aispecify/types/AIAssistSpecProject';
import { AIChangeRequest } from '@pkg/everest.aispecify/types/AIChangeRequest';
import { AIDefaultPrompts } from '@pkg/everest.aispecify/types/AIDefaultPrompts';
import { AIDerivedArtifact } from '@pkg/everest.aispecify/types/AIDerivedArtifact';
import { AIModelConfig } from '@pkg/everest.aispecify/types/AIModelConfig';
import { AIProjectAPI } from '@pkg/everest.aispecify/types/AIProjectAPI';
import { AIProjectExternalToolNotes } from '@pkg/everest.aispecify/types/AIProjectExternalToolNotes';
import { AIProjectInputs } from '@pkg/everest.aispecify/types/AIProjectInputs';
import { AIProjectManualNote } from '@pkg/everest.aispecify/types/AIProjectManualNote';
import { AIProjectOverview } from '@pkg/everest.aispecify/types/AIProjectOverview';
import { AIProjectPrompts } from '@pkg/everest.aispecify/types/AIProjectPrompts';
import { AIProjectTechStack } from '@pkg/everest.aispecify/types/AIProjectTechStack';
import { AIProjectURL } from '@pkg/everest.aispecify/types/AIProjectURL';
import { AIResearchRequirements } from '@pkg/everest.aispecify/types/AIResearchRequirements';
import { AISecurityCompliance } from '@pkg/everest.aispecify/types/AISecurityCompliance';
import { AISpecification } from '@pkg/everest.aispecify/types/AISpecification';
import { AISpecProject } from '@pkg/everest.aispecify/types/AISpecProject';
import { AISpecRequirement } from '@pkg/everest.aispecify/types/AISpecRequirement';
import type { EvstAISpecProjectTypes } from '@pkg/everest.aispecify/types/enums/AISpecProjectTypes';
import type { AISpecProjects } from '@pkg/everest.aispecify/types/presentations/modules/backend/AISpecProject/AISpecProjects';
import { EverestPackage } from '@pkg/everest.appserver/types/metadata/EverestPackage';
import throwOnAppdevMain from '../../actionsV2/utils/projectGuard';
// import { EvstAISpecProjectLanguages } from '@pkg/everest.aispecify/types/enums/AISpecProjectLanguages';

type AssociatedAIModelConfig = {
  id: number;
  projectId: number;
  primaryProvider: string;
  primaryModelCode: string;
  fallbackProvider: string;
  fallbackModelCode: string;
};

export default {
  AISpecProjects: {
    query: {
      async execute({
        session,
        where,
        orderBy,
        skip,
        take,
        count,
      }: AISpecProjects.EntitySets.AISpecProjects.Query.Execute.Request): Promise<AISpecProjects.EntitySets.AISpecProjects.Query.Execute.Response> {
        try {
          const aiSpecProjects = await AISpecProject.query(
            session,
            {
              where,
              orderBy,
              skip,
              take,
            },
            [
              'id',
              'uuid',
              'active',
              'createdDate',
              'lastModifiedDate',
              'name',
              'description',
              'sharedUserIds',
              'isEveProject',
              'evePackageId',
              'additionalDetails',
              'projectType',
              'projectLanguage',
              'generatedAppLink',
            ]
          );

          // Filter projects to only show version 2
          const filteredProjects = aiSpecProjects.filter((project) => {
            if (!project.additionalDetails) {
              // If no additionalDetails, this is not a version 2 project
              return false;
            }

            try {
              const additionalDetails =
                typeof project.additionalDetails === 'string'
                  ? JSON.parse(project.additionalDetails)
                  : project.additionalDetails;

              // Only show projects with version 2
              return additionalDetails.version === 2;
            } catch (error) {
              log.error(
                'Error parsing additionalDetails for project filtering:',
                error
              );
              // If parsing fails, exclude the project
              return false;
            }
          });

          const everestPackageClient = await EverestPackage.client(session);

          const packageIds = [
            ...new Set(filteredProjects.map((project) => project.evePackageId)),
          ];

          const packageDictPromisesList = await Promise.all(
            packageIds.map(async (id) => {
              if (!id) {
                return null;
              }

              try {
                const { packageName } = await everestPackageClient.read(
                  {
                    id,
                  },
                  ['id', 'packageName']
                );
                return { id, name: packageName };
              } catch (error) {
                log.error('Error reading package name:', error);
                return null;
              }
            })
          );

          const filteredPackageDictList = packageDictPromisesList.filter(
            Boolean
          ) as { id: number; name: string }[];

          const packageDict: Record<number, string> = {};
          for (const pkg of filteredPackageDictList) {
            packageDict[pkg.id] = pkg.name;
          }

          const modelConfigs = await AIModelConfig.query(
            session,
            {
              where: {
                projectId: {
                  $in: filteredProjects.map((project) => project.id),
                },
              },
            },
            [
              'id',
              'primaryProvider',
              'primaryModelCode',
              'fallbackModelCode',
              'fallbackProvider',
              'projectId',
            ]
          );

          const modelConfigDict: Record<number, AssociatedAIModelConfig> = {};
          for (const modelConfig of modelConfigs) {
            modelConfigDict[modelConfig.projectId] = {
              id: modelConfig.id,
              projectId: modelConfig.projectId,
              primaryProvider: modelConfig.primaryProvider,
              primaryModelCode: modelConfig.primaryModelCode,
              fallbackProvider: modelConfig.fallbackProvider,
              fallbackModelCode: modelConfig.fallbackModelCode,
            };
          }

          // Map the projects to the expected format
          const instances: AISpecProjects.EntitySets.AISpecProjects.Query.Instance[] =
            filteredProjects.map((project) => {
              return {
                id: project.id,
                name: project.name,
                description: project.description,
                active: project.active,
                createdDate: project.createdDate,
                lastModifiedDate: project.lastModifiedDate,
                sharedUserIds: project.sharedUserIds,
                isEveProject: project.isEveProject,
                evePackageId: project.evePackageId,
                evePackageName: packageDict[project.evePackageId] || null,
                additionalDetails: project.additionalDetails,
                projectType: project.projectType as EvstAISpecProjectTypes,
                projectLanguage: project.projectLanguage, //as EvstAISpecProjectLanguages
                generatedAppLink: project.generatedAppLink,
                modelConfig: modelConfigDict[project.id],
              };
            });

          return {
            instances,
            count: count ? instances.length : undefined,
          };
        } catch (error) {
          log.error('Error in AISpecProject query execute', error);
          throw error;
        }
      },
    },

    create: {
      async execute({
        session,
        inputs,
      }: AISpecProjects.EntitySets.AISpecProjects.Create.Execute.Request): Promise<AISpecProjects.EntitySets.AISpecProjects.Create.Execute.Response> {
        try {
          throwOnAppdevMain(session);
          // Create projects with direct fields
          const projectsToCreate = inputs.map((input) => {
            // Parse existing additionalDetails or create new object
            let additionalDetails: Record<string, unknown> = {};
            if (input.additionalDetails) {
              try {
                additionalDetails =
                  typeof input.additionalDetails === 'string'
                    ? JSON.parse(input.additionalDetails)
                    : input.additionalDetails;
              } catch (error) {
                log.error('Error parsing additionalDetails:', error);
                additionalDetails = {};
              }
            }

            // Set version to 2 for new projects in AISpecProjects
            additionalDetails.version = 2;

            return {
              name: input.name,
              description: input.description,
              active: true,
              sharedUserIds: input.sharedUserIds,
              isEveProject: input.isEveProject,
              evePackageId: input.evePackageId,
              additionalDetails: JSON.stringify(additionalDetails),
              projectType: input.projectType as EvstAISpecProjectTypes,
              projectLanguage: input.projectLanguage,
            };
          });

          const createdProjects = await AISpecProject.createMany(
            session,
            projectsToCreate
          );

          // Map the created projects to the expected Instance type
          // Create default AI model configuration for each new project
          for (const project of createdProjects) {
            try {
              await AIModelConfig.create(session, {
                projectId: project.id,
                primaryModelCode: 'gemini-2.5-flash-preview-05-20',
                primaryProvider: 'google',
                fallbackModelCode: 'gpt-4o',
                fallbackProvider: 'openai',
              });
              log.info(
                `Created default AI model configuration for new project ${project.id}`
              );
            } catch (configError) {
              log.error(
                'Error creating default AI model configuration:',
                configError
              );
              // Continue with project creation even if model config creation fails
            }
          }

          const instances: AISpecProjects.EntitySets.AISpecProjects.Create.Instance[] =
            createdProjects.map((project) => ({
              id: project.id,
              name: project.name,
              description: project.description,
              active: project.active,
              createdDate: project.createdDate,
              lastModifiedDate: project.lastModifiedDate,
              sharedUserIds: project.sharedUserIds,
              isEveProject: project.isEveProject,
              evePackageId: project.evePackageId,
              additionalDetails: project.additionalDetails,
              projectType: project.projectType as EvstAISpecProjectTypes,
              projectLanguage: project.projectLanguage,
            }));

          return {
            instances,
          };
        } catch (error) {
          log.error('Error in AISpecProject create execute', error);
          throw error;
        }
      },
    },

    update: {
      async execute({
        session,
        id,
        data,
      }: AISpecProjects.EntitySets.AISpecProjects.Update.Execute.Request): Promise<AISpecProjects.EntitySets.AISpecProjects.Update.Execute.Response> {
        try {
          // Update the project directly with the new values
          await AISpecProject.update(
            session,
            {
              id,
            },
            {
              name: data.name,
              description: data.description,
              sharedUserIds: data.sharedUserIds, // Include sharedUserIds when updating project
              isEveProject: data.isEveProject,
              evePackageId: data.evePackageId,
              additionalDetails: data.additionalDetails,
              projectType: data.projectType as EvstAISpecProjectTypes,
              projectLanguage: data.projectLanguage,
            }
          );
        } catch (error) {
          log.error('Error in AISpecProject update execute', error);
          throw error;
        }
      },
    },

    delete: {
      async execute({
        session,
        ids,
      }: AISpecProjects.EntitySets.AISpecProjects.Delete.Execute.Request): Promise<AISpecProjects.EntitySets.AISpecProjects.Delete.Execute.Response> {
        try {
          // Helper function to delete entities of a specific type
          const deleteEntities = async (
            projectId: number,
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            entityType: any,
            entityName: string
          ) => {
            try {
              // Find entities for this project
              const entities = await entityType.query(
                session,
                {
                  where: {
                    projectId: projectId,
                  },
                },
                ['id']
              );

              // Delete the found entities
              if (entities.length > 0) {
                await entityType.deleteMany(session, {
                  projectId: projectId,
                });
                log.info(
                  `Deleted ${entities.length} ${entityName} entities for project ${projectId}`
                );
              }
              return entities.length;
            } catch (error) {
              log.error(
                `Error deleting ${entityName} entities for project ${projectId}:`,
                error
              );
              // Continue with project deletion even if entity deletion fails
              return 0;
            }
          };

          // Delete all project secrets in parallel first
          const secretDeletionPromises = ids.map(async (projectId) => {
            try {
              await deleteAllProjectSecrets(session, projectId);
              log.info(`Deleted all secrets for project ${projectId}`);
            } catch (secretError) {
              log.error(
                `Error deleting secrets for project ${projectId}:`,
                secretError
              );
              // Continue with project deletion even if secret deletion fails
            }
          });

          // Wait for all secret deletions to complete
          await Promise.all(secretDeletionPromises);

          // Delete associated entities for each project
          for (const projectId of ids) {
            try {
              // Delete all associated entities
              await Promise.all([
                deleteEntities(
                  projectId,
                  AIProjectTechStack,
                  'AIProjectTechStack'
                ),
                deleteEntities(
                  projectId,
                  AIAssistSpecProject,
                  'AIAssistSpecProject'
                ),
                deleteEntities(
                  projectId,
                  AIResearchRequirements,
                  'AIResearchRequirements'
                ),
                deleteEntities(projectId, AIModelConfig, 'AIModelConfig'),
                deleteEntities(projectId, AIProjectAPI, 'ProjectAPIs'),
                deleteEntities(
                  projectId,
                  AISecurityCompliance,
                  'AISecurityCompliance'
                ),
                deleteEntities(projectId, AISpecification, 'AISpecification'),
                deleteEntities(projectId, AIProjectInputs, 'AIProjectInputs'),
                deleteEntities(
                  projectId,
                  AIProjectManualNote,
                  'AIProjectManualNote'
                ),
                deleteEntities(
                  projectId,
                  AIProjectExternalToolNotes,
                  'AIProjectExternalToolNotes'
                ),
                deleteEntities(projectId, AIProjectURL, 'AIProjectURL'),
                deleteEntities(projectId, AIProjectPrompts, 'AIProjectPrompts'),
                deleteEntities(
                  projectId,
                  AIDerivedArtifact,
                  'AIDerivedArtifact'
                ),
                deleteEntities(projectId, AIDefaultPrompts, 'AIDefaultPrompts'),
                deleteEntities(projectId, AIChangeRequest, 'AIChangeRequest'),
                deleteEntities(
                  projectId,
                  AISpecRequirement,
                  'AISpecRequirement'
                ),
                deleteEntities(
                  projectId,
                  AIProjectOverview,
                  'AIProjectOverview'
                ),
              ]);

              log.info(
                `Deleted all associated entities for project ${projectId}`
              );
            } catch (error) {
              log.error(
                `Error deleting associated entities for project ${projectId}:`,
                error
              );
              throw error;
            }
          }

          // Delete the projects
          await AISpecProject.deleteMany(session, {
            id: {
              $in: ids,
            },
          });
        } catch (error) {
          log.error('Error in AISpecProject delete execute', error);
          throw error;
        }
      },
    },
  },
};
