package everest.aispecify

odata presentation AISpecProjectActions {
    action InitializeProject {
        inputs {
            name: Text
            `description`: Text
            projectType: Text
            textInput: Text
            files: array<JSON> 
            projectLanguage: Text
            isEveProject: TrueFalse
            evePackageId: Number<Int>
            aiModel: Text
            packageName: Text
        }
        outputs {
            projectId: field<AISpecProject.id>
            success: TrueFalse
        }
        properties {
            side-effects true
        }
    }
    action InitializeProjectFromGitHub {
        inputs {
            url: Text
            githubToken (optional: true): Text
        }
        outputs {
            projectId: field<AISpecProject.id>
            success: TrueFalse
        }
        properties {
            side-effects true
        }
    }
    
    action GetProjectSecret {
        inputs {
            projectId: field<AISpecProject.id>
            providerName: Text
        }
        outputs {
            secretExists: TrueFalse
            secretValue: Text
        }
        properties {
            side-effects false
        }
    }
    
    action SetProjectSecret {
        inputs {
            projectId: field<AISpecProject.id>
            providerName: Text
            secretValue: Text
        }
        outputs {
            success: TrueFalse
        }
        properties {
            side-effects true
        }
    }
}
