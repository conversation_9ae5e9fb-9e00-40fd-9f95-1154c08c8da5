{"version": 2, "uicontroller": "transactionTransform.uicontroller.js", "uimodel": {"state": {}, "nodes": {"staging": {"type": "struct", "modelId": "everest.fin.integration.base/StagingModel.Staging", "fieldList": []}, "upload": {"type": "struct", "modelId": "everest.appserver/UploadModel.Upload", "fieldList": []}, "mappings": {"type": "list", "modelId": "everest.fin.integration.base/MappingsModel.Mappings", "query": {"where": {"subject": "BankTransaction", "isActive": true}}, "fieldList": ["name", "package", "id"]}, "transactions": {"type": "struct", "modelId": "everest.fin.integration.bank/BankTransactionNewModel.BankTransactionNew", "fieldList": []}, "transactionUpload": {"type": "struct", "modelId": "everest.fin.accounting/TransactionFileUploadModel.TransactionFileUpload", "fieldList": []}}}, "uiview": {"i18n": "everest.fin.integration.base/extractedData", "sections": [{"component": "Block", "size": "12", "type": "secondary", "elements": [{"component": "Select", "label": "{{extract.selectDataMapping}}", "idProp": "id", "textProp": "text", "isEditing": true, "list": "@bindingController(mappings, getMappingList())", "onChange": "@controller:onSelectMappingChange", "value": "@binding:transactionUpload.mappingId"}, {"component": "Editor", "size": "3", "label": "{{extract.originalData}}", "value": "@state:originalData", "name": "originalData", "isEditing": true, "editorConfig": {"language": "json", "height": 300, "options": {"formatOnPaste": true, "formatOnType": true}}}, {"component": "Editor", "size": "3", "label": "{{staging.transformedData}}", "value": "@state:transformedData", "name": "transformedData", "isEditing": true, "editorConfig": {"language": "json", "height": 300, "options": {"formatOnPaste": true, "formatOnType": true}}}]}], "actions": [{"variant": "primary", "label": "Map", "onClick": "@controller:transform"}]}}