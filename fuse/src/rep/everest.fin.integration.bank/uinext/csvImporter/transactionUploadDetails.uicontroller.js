// @i18n:dashboard
// @i18n:everest.fin.integration.base/staging
// Note [Job scheduler V2 migration]
function isBackgroundResponse(response) {
  return (
    response !== undefined &&
    (response.backgroundJobId !== undefined || response.jobId !== undefined)
  );
}

function getFileName({ data }) {
  return data?.dataUploads?.[0]?.name || data?.dataUploads?.[0]?.originalName;
}

async function map({ helpers, state, data }) {
  const originalData = data?.rawData?.rowData[0];
  const ids = state.selectedIds;
  helpers.openModal({
    title: '{{staging.transform}}',
    initialState: ids?.length
      ? { stagingIds: ids, originalData }
      : {
          dataExtractionId: state?.param?.dataExtractionId,
          originalData,
        },
    template: `/templates/everest.fin.integration.bank/uinext/csvImporter/transactionTransform`,
    onModalSubmit: async () => await actions.refetchUiModelData(),
  });
}

function setActiveState({ state, actions, helpers }, stateToSet) {
  return async () => {
    helpers.showNotificationMessage({
      key: 'setActiveState',
      type: 'loading',
      message: 'Processing...',
      duration: 3,
    });

    const result = await actions.run({
      staging: {
        action: 'setActiveState',
        data: {
          state: stateToSet,
          ids: state.selectedIds,
        },
      },
    });
    await actions.refetchUiModelData();
    if (result?.staging?.success) {
      helpers.showToast({
        type: 'success',
        title: 'Success',
        message: `${result?.staging?.count} ${
          result?.staging?.count === 1 ? 'item' : 'items'
        } set to ${
          result?.staging?.state === 'active' ? 'Billable' : 'Unbillable'
        } state`,
      });
    } else {
      helpers.showToast({
        type: 'error',
        title: 'Error',
        message: `Error setting the state to ${
          result?.staging?.state === 'active' ? 'Billable' : 'Unbillable'
        }`,
      });
    }
    helpers.closeNotificationMessage('setActiveState');
  };
}

function getStatusBadgeColor({}, { rowData }) {
  if (rowData?.status === 'PENDING') {
    return 'warning';
  }
  if (rowData?.status === 'Integrated') {
    return 'shamrock';
  }
  if (rowData?.status === 'FAILED') {
    return 'danger';
  }

  return 'neutral';
}

function onSelectionChanged({ state }, selectedRows) {
  state.selectedIds = selectedRows.map((x) => x.id);
  state.billableUnBillableBtnDisabled = state.selectedIds.length === 0;
}

async function processTransactions({ helpers, actions, state, data }) {
  const key = 'transactionSaving';

  helpers.showNotificationMessage({
    key,
    type: 'loading',
    message: 'Processing',
    duration: 0,
  });

  //Get account id from upload tansaction node when it came from job scheduler
  if (!state.accountId) {
    state.accountId = Number(data?.transactionUpload?.[0]?.accountId);
    state.currency = data?.transactionUpload?.[0]?.currency;
  }
  let reconciliationStartDate;
  if (data?.transactionUpload?.[0]?.reconciliationStartDate) {
    reconciliationStartDate = new Date(
      data?.transactionUpload?.[0]?.reconciliationStartDate
    );
  }

  const result = await actions.run({
    transactions: {
      action: 'runStagingTransaction',
      data: {
        dataExtractionId: state.param.dataExtractionId,
        accountData: {
          accountId: state.accountId,
          currency: state.currency,
          reconciliationStartDate,
          additionalInformation: 'CSV',
        },
      },
    },
  });

  helpers.closeNotificationMessage(key);
  await actions.refetchUiModelData();

  if (result.transactions?.success > 0) {
    helpers.showNotificationMessage({
      message: `${result.transactions.success} process were successful`,
      type: 'success',
      duration: 2,
    });

    helpers.navigate({
      to: `/templates/everest.fin.integration.bank/uinext/dashboard`,
    });
  }
  if (result.transactions?.errors?.length > 0) {
    helpers.showNotificationMessage({
      message: `${result.transactions.errors.length} process has failed`,
      type: 'warning',
      duration: 2,
    });
  }
  if (isBackgroundResponse(result.transactions)) {
    helpers.openDialog({
      hideConfirmButton: true,
      key: 'loading',
      type: 'success',
      title: 'Importing Transactions',
      message:
        'Large files may take some time to import. Feel free to leave this screen; the transactions will be automatically added to your account once the import is complete.',
      duration: 3,
      actions: {
        secondary: {
          label: '{{banking.csvImporter.ok}}',
          onClick: () => {
            helpers.closeModal();
            helpers.navigate({
              to: `/templates/everest.fin.integration.bank/uinext/dashboard?activeSegment=1`,
            });
          },
        },
      },
    });
    /*helpers.showNotificationMessage({
      message: `Background job created for processing the transactions`,
      type: 'success',
      duration: 2,
    });
    helpers.navigate({
      to: `/templates/everest.base.ui/jobScheduler/uinext/backgroundJob/view?id=${result.transactions.backgroundJobId}`,
    });*/
  }
}
