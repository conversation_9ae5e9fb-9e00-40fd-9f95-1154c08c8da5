{"version": 3, "uicontroller": "transactionImport.uicontroller.ts", "uimodel": {"state": {"hasError": false, "isPrimaryButtonDisabled": true}, "presentation": {"urn": "urn:evst:everest:fin/integration/bank:presentation:uinext/csvImporter/transactionImport", "parameters": {"mode": "view", "dataExtractionId": "@state:param.dataExtractionId", "accountId": "@state:accountId", "currency": "@state:currency", "type": "transformedData"}}}, "uiview": {"templateType": "list", "i18n": ["everest.fin.integration.base/staging", "everest.fin.accounting/accounting", "dashboard"], "title": "{{banking.help.banking.transactions.uploadDetails}}", "config": {"allowRefreshData": true, "autoRefreshData": true}, "header": {"content": {"title": "@controller:getFileName()"}}, "actions": {"content": [{"label": "@controller:getPrimaryButtonName()", "onClick": "@controller:processTransactions", "disabled": "@state:isPrimaryButtonDisabled", "variant": "primary", "align": "right"}, {"label": "{{banking.help.banking.transactions.uploadDifferntFile}}", "onClick": "@controller:map", "variant": "secondary", "align": "left"}]}, "sections": {"content": [{"component": "<PERSON><PERSON>", "section": {"grid": {"size": "12"}}, "visible": "@state:hasError", "props": {"variant": "error", "content": "{{ importTransaction.errorMsg }}"}}, {"component": "Table", "customId": "transformedDataTable", "section": {"title": "{{banking.help.banking.transactions.transactionRecords}}", "grid": {"size": "12"}, "filter": {"quickFilters": [{"label": "{{banking.help.banking.transactions.errors}}", "value": ["FAILED"], "field": "status", "comparator": "$notIn"}]}}, "props": {"data": "@binding:transformedData", "showRowCount": true, "pagination": true, "columns": [{"headerName": "{{banking.entries.importStatus}}", "field": "status", "cellVariant": {"variant": "badge", "matchers": "@controller:getStatusBadgeColor"}}, {"field": "errorMessage"}, {"headerName": "{{banking.section.table.date}}", "field": "externalCreationDate"}, {"headerName": "{{banking.section.table.source}}", "field": "account", "autoSizeMinWidth": "md", "cellVariant": {"variant": "icon", "iconSize": "medium", "matchers": "@controller:getIconType"}, "fieldProps": {"fontWeight": "medium"}}, {"field": "amount"}, {"field": "description"}, {"field": "payeeOrPayor"}]}}]}}}