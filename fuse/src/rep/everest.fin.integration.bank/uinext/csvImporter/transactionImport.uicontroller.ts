// @i18n:everest.fin.accounting/accounting
// @i18n:dashboard

// Note [Job scheduler V2 migration]
import { UILifecycleHooks } from '@everestsystems/content-core';
import type { transactionImportPresentationUI } from '@pkg/everest.fin.integration.bank/types/presentations/uinext/csvImporter/transactionImport.ui';
import type { BankTransactionImporterUiTemplate } from '@pkg/everest.fin.integration.bank/types/uiTemplates/uinext/csvImporter/bankTransactionImporter.ui';

interface ProcessResult {
  successCounts: number;
  errorCounts: number;
}
type BankTransactionImporterContext =
  BankTransactionImporterUiTemplate.BankTransactionImporterContext;

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type Any = any;

type Context = BankTransactionImporterContext &
  transactionImportPresentationUI.context & {
    form: Any;
    state: {
      form: Any;
      bankAccountId: number;
      currency: string;
      mappingId?: number;
      selectedPackage?: string;
      transactionType?: string;
      entityIds: number[];
    };
  };

export function isBackgroundResponse(response) {
  return (
    response !== undefined &&
    (response.backgroundJobId !== undefined || response.jobId !== undefined)
  );
}

// eslint-disable-next-line @typescript-eslint/no-misused-promises
UILifecycleHooks.onInit(async (context: Context) => {
  await setDefaultFilters(context);
});

export async function setDefaultFilters(context: Context) {
  const { helpers, actions, state } = context;
  const key = 'transactionSaving';

  // Show loading notification
  helpers.showNotificationMessage({
    key,
    type: 'loading',
    message: 'Processing',
    duration: 0,
  });

  const { successCounts, errorCounts } =
    (await context.presentationClient.runProcess({
      dataExtractionId: (state.param as { dataExtractionId: string })
        .dataExtractionId,
      accountId: state.accountId.toString(),
      currency: state.currency,
      performSave: false,
    })) as unknown as ProcessResult;

  await actions.refetchUiModelData();

  const totalCount = successCounts + errorCounts;

  // Show success notification
  helpers.showNotificationMessage({
    key,
    message: `{{banking.help.banking.transactions.process.success}}`.replaceAll(
      '$TransactionCount$',
      totalCount.toString()
    ),
    type: 'success',
    duration: 2,
  });

  // Handle errors if any
  if (errorCounts > 0) {
    state.hasError = true;
    if (successCounts === 0) {
      state.HideImportButton = true;
    }
    helpers.closeNotificationMessage(key);
  }

  state.isPrimaryButtonDisabled = false;
}

export function getFileName({ data }) {
  return data?.dataUploads?.[0]?.name || data?.dataUploads?.[0]?.originalName;
}

export function getPrimaryButtonName({ state }) {
  return state?.hasError
    ? state?.HideImportButton
      ? 'Cancel'
      : '{{banking.help.banking.transactions.importSkipErrors}}'
    : '{{banking.help.banking.transactions.import}}';
}

export async function map({ helpers, state }, _cardData: Any) {
  helpers.closeModal();
  helpers.openModal({
    title:
      state?.transactionType === 'card'
        ? '{{banking.help.banking.transactions.uploadAndExtractCard}}'
        : '{{banking.help.banking.transactions.uploadAndExtract}}',
    template: `/templates/everest.fin.integration.bank/uinext/csvImporter/bankTransactionImporter`,
    onModalSubmit: () => {
      // Run callback immediately and move the closing process to the next tick
      helpers.currentModalSubmitCallback();
      helpers.closeModal();
    },
    initialState: {
      bankAccountId: state?.accountId,
      currency: state?.currency,
      entityName: state?.entityName,
      transactionType: state?.transactionType,
      entityIds: state?.entityIds,
    },
    size: 'xsmall',
  });
}

type RowData = {
  status: string;
};

type valueProp = {
  rowData: RowData;
};

export function getStatusBadgeColor(_context, { rowData }: valueProp) {
  const statusColorMap = {
    Pending: 'warning',
    Waiting: 'shamrock',
    FAILED: 'danger',
  };

  return statusColorMap?.[rowData?.status] || 'neutral';
}
export function getIconType({ state }) {
  return state?.transactionType === 'card' ? 'credit_card' : 'account_balance';
}

export async function processTransactions(context: Context) {
  const { helpers, actions, state } = context;
  // Handle early exit case
  if (state.HideImportButton) {
    helpers.closeModal();
    return;
  }

  // Show loading notification
  const NOTIFICATION_KEY = 'transactionSaving';
  helpers.showNotificationMessage({
    key: NOTIFICATION_KEY,
    type: 'loading',
    message: 'Processing',
    duration: 0,
  });

  try {
    const result = (await context.presentationClient.runProcess({
      dataExtractionId: (state.param as { dataExtractionId: string })
        .dataExtractionId,
      accountId: state.accountId.toString(),
      currency: state.currency,
      performSave: true,
    })) as unknown as ProcessResult;
    const { successCounts } = result;

    helpers.closeNotificationMessage(NOTIFICATION_KEY);
    await actions.refetchUiModelData();

    // Handle successful transactions
    if (successCounts > 0) {
      handleSuccessfulImport(helpers, successCounts);
    }

    // Handle failed transactions - Currently, we are skiping the errors(even though error occurs!!)
    // if (errorCounts > 0) {
    //   handleFailedImport(helpers, errorCounts);
    // }

    // Handle background processing for large files
    if (isBackgroundResponse(result)) {
      handleBackgroundProcessing(helpers);
    }
  } catch {
    helpers.closeNotificationMessage(NOTIFICATION_KEY);
    helpers.showNotificationMessage({
      key: NOTIFICATION_KEY,
      message: 'Error processing transactions',
      type: 'error',
      duration: 2,
    });
  }
}

function handleSuccessfulImport(helpers, successCount) {
  helpers.showNotificationMessage({
    message: `{{banking.help.banking.transactions.import.success}}`.replace(
      '$TransactionCount$',
      successCount
    ),
    type: 'success',
    duration: 3,
  });
  helpers.currentModalSubmitCallback();
  helpers.closeModal();
}

function handleBackgroundProcessing(helpers) {
  helpers.openDialog({
    hideConfirmButton: true,
    type: 'success',
    title: 'Importing Transactions',
    message: '{{banking.help.banking.transactions.importLargeFiles}}',
    duration: 3,
    actions: {
      secondary: {
        label: '{{banking.csvImporter.ok}}',
        onClick: () => {
          helpers.currentModalSubmitCallback();
          helpers.closeModal();
          helpers.navigate({
            to: `/templates/everest.fin.integration.bank/uinext/dashboard?activeSegment=1`,
          });
        },
      },
    },
  });
}
