{"version": 2, "uicontroller": "transactionUploadDetails.uicontroller.js", "uimodel": {"state": {"visibleSection": "transformed", "selectedIds": [], "billableUnBillableBtnDisabled": true}, "nodes": {"transactionUpload": {"type": "list", "modelId": "everest.fin.integration.bank/BankTransactionFileUploadModel.BankTransactionFileUpload", "query": {"where": {"extractedDataId": "@state:param.dataExtractionId"}}, "fieldList": ["name", "originalName", "accountId", "currency", "reconciliationStartDate"]}, "staging": {"type": "struct", "modelId": "everest.fin.integration.base/StagingModel.Staging", "fieldList": []}, "transactions": {"type": "struct", "modelId": "everest.fin.integration.bank/BankTransactionNewModel.BankTransactionNew", "fieldList": []}, "transformedData": {"pagination": true, "modelId": "everest.fin.integration.bank/BankTransactionNewModel.BankTransactionNew", "queryTransactionStagingData": {"args": {"dataExtractionId": "@state:param.dataExtractionId", "type": "transformedData"}, "orderBy": [{"field": "createdDate", "ordering": "desc"}]}, "type": "struct", "detailedType": "table", "fieldList": []}, "rawData": {"modelId": "everest.fin.integration.base/StagingModel.Staging", "queryStagedData": {"args": {"dataExtractionId": "@state:param.dataExtractionId", "type": "rawData"}, "orderBy": [{"field": "createdDate", "ordering": "desc"}]}, "type": "struct", "detailedType": "table", "pagination": true, "fieldList": []}}}, "uiview": {"templateType": "list", "i18n": ["everest.fin.accounting/accounting", "everest.fin.integration.base/staging"], "title": "{{banking.help.banking.transactions.uploadDetails}}", "config": {"allowRefreshData": true, "autoRefreshData": true}, "header": {"content": {"title": "@controller:getFileName()", "status": "{{banking.help.banking.transactions.dataUpload}}"}}, "actions": {"content": [{"label": "{{banking.help.banking.transactions.processTransactions}}", "onClick": "@controller:processTransactions", "variant": "primary"}, {"label": "{{banking.help.banking.transactions.remapRawData}}", "onClick": "@controller:map", "variant": "secondary"}]}, "sections": {"content": [{"component": "WidgetGroup", "section": {"title": "{{banking.help.banking.transactions.transactionRecords}}", "actions": [{"label": "Actions", "variant": "primary", "actions": [{"label": "{{banking.help.banking.transactions.dataUpload.inactive}}", "onClick": "@controller:setActiveState('inactive')", "disabled": "@state:billableUnBillableBtnDisabled"}, {"label": "{{banking.help.banking.transactions.dataUpload.active}}", "onClick": "@controller:setActiveState('active')", "disabled": "@state:billableUnBillableBtnDisabled"}]}], "grid": {"size": "12"}}, "props": {"widgets": [{"label": "{{staging.normalizedData}}", "component": "Table", "section": {"customId": "transformedDataTable", "grid": {"size": "12"}}, "props": {"data": "@binding:transformedData", "rowSelection": true, "hideFilters": true, "onRowSelectionChanged": "@controller:onSelectionChanged", "showRowCount": true, "columns": [{"field": "status", "cellVariant": {"variant": "badge", "matchers": "@controller:getStatusBadgeColor"}}]}}, {"label": "{{staging.rawData}}", "component": "Table", "section": {"customId": "rawDataTable", "grid": {"size": "12"}}, "props": {"data": "@binding:rawData", "hideFilters": true, "showRowCount": true}}]}}]}}}