{"version": 2, "uicontroller": "bankTransactionConfigMapper.uicontroller.ts", "uimodel": {"state": {"configData": {"inputs": [], "transformations": [], "outputs": [], "edges": []}, "autoPreview": false}, "nodes": {"mapping": {"type": "struct", "modelId": "everest.fin.integration.base/MappingsModel.Mappings", "fieldList": []}, "everestPackage": {"type": "list", "query": {"where": {}, "orderBy": ["packageName"]}, "modelId": "everest.appserver/metadata/EverestPackageModel.EverestPackage", "fieldList": ["packageName"]}, "extractedData": {"type": "struct", "modelId": "everest.fin.integration.base/ExtractedDataModel.ExtractedData", "fieldList": []}, "upload": {"type": "struct", "modelId": "everest.appserver/UploadModel.Upload", "fieldList": []}, "fakeBankData": {"type": "list", "query": {"where": {"accountId": -1}}, "modelId": "everest.fin.accounting/BankEntryHeaderNewModel.BankEntryHeaderNew", "fieldList": []}, "transactions": {"type": "struct", "modelId": "everest.fin.integration.bank/BankTransactionNewModel.BankTransactionNew", "fieldList": []}}}, "uiview": {"i18n": "everest.fin.integration.base/mapping", "sections": [{"component": "FieldGroup", "size": "12", "title": "Mapping Information", "type": "secondary", "elements": [{"component": "Input", "label": "{{mapping.name}}", "value": "@binding:mapping.name", "isEditing": true, "action": "create", "placeholder": "Enter a unique name", "onChange": "@controller:previewConfigMap"}, {"component": "Select", "label": "{{mapping.package}}", "isEditing": true, "isDisabled": true, "action": "create", "value": "@bindingController(mapping.package, getBankPackage)", "idProp": "packageName", "textProp": "packageName", "list": "@binding:everestPackage"}, {"component": "Input", "label": "{{mapping.subject}}", "value": "@bindingController(mapping.subject, getBankMappingSubject)", "size": 2, "isDisabled": true, "isEditing": true, "action": "create"}]}, {"component": "Table", "size": "12", "variant": "dark", "customId": "bankEntryLinesTable", "pagination": true, "showRowCount": true, "suppressDelete": false, "addRowsOnEmpty": true, "editing": true, "addRows": true, "title": "Mapping Table", "columns": [{"field": "sourceInput", "headerName": "CSV Source Field", "fieldProps": {"onChange": "@controller:previewConfigMap"}}, {"headerName": "Target BT Field", "field": "targetInput", "fieldProps": {"component": "Select", "onChange": "@controller:previewConfigMap", "idProp": "displayName", "textProp": "displayName", "list": "@controller:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "placeholder": "-- Select Type --"}}, {"field": "defaultInput", "headerName": "Default Value", "fieldProps": {"placeholder": "Optional", "onChange": "@controller:previewConfigMap"}}, {"headerName": "Type", "field": "inputType", "fieldProps": {"component": "Select", "onChange": "@controller:previewConfigMap", "idProp": "name", "textProp": "displayName", "list": [{"id": 1, "name": "string", "displayName": "String"}, {"id": 2, "name": "number", "displayName": "Number"}], "placeholder": "-- Select Type --"}}], "data": "@binding:fakeBankData"}, {"component": "FieldGroup", "size": "12", "title": "Mapping Details", "type": "secondary", "elements": [{"component": "Editor", "size": "12", "label": "{{mapping.configValue}}", "value": "@binding:mapping.configValue", "action": "create", "isEditing": true, "editorConfig": {"language": "json", "height": 200, "options": {"formatOnPaste": true, "formatOnType": true}}}]}, {"component": "ButtonGroup", "actions": [{"variant": "primary", "label": "{{mapping.create}}", "onClick": "@controller:getOnApply()"}]}]}}