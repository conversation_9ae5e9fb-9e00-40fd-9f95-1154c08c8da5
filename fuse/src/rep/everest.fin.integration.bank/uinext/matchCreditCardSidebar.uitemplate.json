{"version": 2, "uicontroller": ["matchSidebar.uicontroller.ts", "matchSidebarUtils.uicontroller.ts"], "uimodel": {"state": {"mode": "single", "actionGroupType": "radio", "selectedItems": [], "bankTransactionIds": [], "type": "creditCard"}, "nodes": {"postingPeriods": {"type": "struct", "modelId": "everest.fin.accounting/AccountingPeriodModel.AccountingPeriod"}, "transactions": {"type": "list", "query": {"where": {"id": {"$in": "@controller:getBankTransactionIds()"}}, "orderBy": [{"field": "externalCreationDate", "ordering": "desc"}, {"field": "id", "ordering": "desc"}]}, "fieldList": ["bankAccountId", "matchStatus", "matchingTransaction", "bankTransactionNumber", "accountName", "amount", "currency", "externalCreationDate", "payeeOrPayor", "externalDescription", "entityId", "id", "entity", "BankTransactionNew-BankAccount.BankAccount-FinancialInstitution.logoId", "BankTransactionNew-BankAccount.BankAccount-FinancialInstitution.logoUrl", "fullmatchCount", "partialMatchCount", "matchMessage", "coaAccountId", "BankTransactionNew-BankAccount.coaAccountType", "notes", "matchId", "transactionPaymentMethod"], "modelId": "everest.fin.integration.bank/BankTransactionNewModel.BankTransactionNew"}, "bankTransactionMatchesTransient": {"type": "list", "modelId": "everest.fin.integration.bank.matching/BankTransactionMatchTransientModel.BankTransactionMatchTransient"}, "bankTransactionMatches": {"type": "list", "parent": "transactions", "joinKey": "id-matchId", "modelId": "everest.fin.integration.bank/BankTransactionMatchModel.BankTransactionMatch", "fieldList": ["id", "status", "autoMatchReason", "errorReason"]}, "matchDocuments": {"type": "list", "parent": "bankTransactionMatches", "query": {"orderBy": [{"field": "sortingPriority", "ordering": "asc"}, {"field": "isAFullMatch", "ordering": "desc"}, {"field": "suggestionConfidence", "ordering": "desc"}, {"field": "amount", "ordering": "desc"}]}, "joinKey": "id-bankTransactionMatchId", "modelId": "everest.fin.integration.bank/MatchSourceFinancialDocumentModel.MatchSourceFinancialDocument", "fieldList": ["id", "bankTransactionMatchId", "sourceJournalEntryLineId", "amount", "isAFullMatch", "sfdId", "suggestionConfidence", "journalEntryHeaderId", "journalEntryLineDescription", "postingDate", "business<PERSON><PERSON>ner", "sfdNumber", "sboNumber", "matchDocument", "matchDocumentWithoutLink"]}, "sbos": {"type": "list", "parent": "matchDocuments", "joinKey": "id-matchSFDID", "modelId": "everest.fin.integration.bank/MatchSourceBusinessObjectModel.MatchSourceBusinessObject", "query": {"draft": "include"}, "fieldList": ["id", "matchSFDID", "sboId", "sboNumber", "sboType", "sboName"]}, "attachments": {"type": "list", "getMatchAttachments": {"bankTransactionIds": "@controller:getBankTransactionIds()"}, "modelId": "everest.fin.integration.bank.matching/BankTransactionMatchTransientModel.BankTransactionMatchTransient"}, "netZeroImpact": {"type": "struct", "modelId": "everest.fin.integration.bank/NetZeroImpactModel.NetZeroImpact"}, "payment": {"type": "struct", "fieldList": ["paymentType", "bankFeeAmount"], "modelId": "everest.fin.accounting/PaymentModel.Payment"}, "creditCardEntryHeader": {"type": "list", "modelId": "everest.fin.accounting/CreditCardEntryHeaderModel.CreditCardEntryHeader", "fieldList": ["id", "creditCardEntryName", "creditCardEntryDescription", "creditCardEntryHeaderNumber", "transactionDate", "postingPeriod", "postingPeriodId", "entityId", "entity", "currency", "journalEntryNumber", "journalEntryHeaderId", "status", "containsPrepaidLines", "containsBillableItem", "accountId", "origin", "externalUrl"]}, "creditCardEntryLines": {"type": "list", "modelId": "everest.fin.accounting/CreditCardEntryLineModel.CreditCardEntryLine", "fieldList": ["creditCardEntryHeaderId", "departmentId", "businessPartnerId", "description", "accountId", "amount", "id", "account", "accountNumber", "lineType", "amortizationScheduleHeaderId", "employeeId", "businessUnitId", "billableExpense", "customerId"]}}}, "uiview": {"config": {"autoRefreshData": true, "allowRefreshData": true, "alwaysNewTab": true, "sidebar": {"stickyFooter": [{"component": "SummaryText", "visible": "@controller:isSummaryVisible()", "summary": {"label": "@controller:getSummaryTextLabel()", "parseAs": "currency", "value": "@controller:getTotalAmountSelected()"}, "fields": [{"label": "Difference", "parseAs": "currency", "value": "@controller:getDifference()"}], "variant": "small"}]}}, "templateType": "generic", "i18n": ["dashboard", "creditCardEntry", "everest.fin.accounting/fundTransfer", "everest.fin.accounting/customerPayment"], "header": {"content": {"status": "@controller:getStatus()", "title": "@controller:getAvatarTitle()", "description": "@controller:getAvatarDescription()", "showAvatar": true, "avatar": {"isEditable": false, "size": "large", "avatarList": "@controller:getAvatarList()"}}, "fieldConfig": {"status": {"tooltip": {"text": "@controller:getStatusTooltip()"}}}}, "sections": {"content": [{"component": "ActionGroup", "title": "@controller:getMatchTitle()", "versionNew": true, "customId": "MultiMatchActionGroup", "isEditing": true, "variant": "@state:actionGroupType", "data": "@controller:getCreditCardActionGroupData()", "onChangedValue": "@controller:onMatchOptionChange", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "spacing": "small"}, {"component": "Block", "size": "1", "isVisible": "@controller:isAutoMatchReasonVisible()", "elements": [{"component": "Read<PERSON>nly", "label": "Match Reason", "value": "@binding:bankTransactionMatches[0].autoMatchReason"}]}, {"component": "Block", "size": "1", "isVisible": "@controller:isErrorReasonVisible()", "elements": [{"component": "Read<PERSON>nly", "label": "Match Reason", "value": "@binding:bankTransactionMatches[0].errorReason"}]}, {"component": "FieldGroup", "visible": "@controller:isPaymentDetailsVisible()", "section": {"title": "{{paymentDetails}}", "editing": true, "grid": {"size": 12}, "variant": "card"}, "props": {"columns": "1", "elements": [{"label": "{{payment.bankFeeAmount}}", "initialValue": "@controller:getSuggestionsWithPostedSFDAmount()", "value": "@binding:payment.bankFeeAmount"}, {"initialValue": "@controller:getPaymentTypeInitialValue()", "value": "@binding:payment.paymentType"}]}}, {"component": "FieldGroup", "size": "12", "type": "secondary", "editing": false, "columns": "1", "elements": [{"label": "{{banking.transaction.notes}}", "value": "@controller:getNotes()", "multiline": true}]}, {"component": "FilePreview", "visible": "@controller:isAttachmentPreviewVisible()", "section": {"title": "Related Attachments", "description": "@controller:getAttachmentDescription()", "grid": {"size": "12"}}, "props": {"fileList": "@controller:getAttachments()", "customEmptyMessage": "@controller:getEmptyMessage()"}}, {"component": "ActionGroup", "customId": "creditCardEntryDetails", "visible": "@controller:isCreateCreditCardEntryModalVisible()", "section": {"title": "{{creditCard.details}}", "grid": {"size": 12}, "editing": true, "secondaryActions": [{"label": "{{fundTransfer.createInModal}}", "variant": "support-mystic-grey", "icon": {"type": "copy", "size": "xsmall", "weight": "bold"}, "onClick": "@controller:createCreditCardEntryInModal"}]}, "props": {"buttonActionsPosition": "right", "spacing": "small", "lines": [{"label": "Account", "fieldProps": {"component": "Select", "idProp": "id", "textProp": "accountId", "allowClear": true, "onChange": "@controller:onChangeAccountId", "isEditing": true, "value": "@binding:creditCardEntryLines.accountId", "semanticTypeUiParams": "@controller:getAccountSemanticTypeUiParams()"}}, {"label": "Department", "fieldProps": {"component": "Select", "idProp": "id", "textProp": "departmentId", "allowClear": true, "onChange": "@controller:onChangeDepartmentId", "isEditing": true, "value": "@binding:creditCardEntryLines.departmentId"}}]}}]}, "actions": {"content": [{"variant": "@controller:getButtonType()", "label": "@controller:getPrimaryButtonLabel()", "disabled": "@controller:isPrimaryButtonDisabled()", "align": "right", "onClick": "@controller:apply"}, {"variant": "secondary", "label": "Find matches", "disabled": "@controller:isSecondaryButtonDisabled()", "visible": "@controller:isFindMatchesButtonVisible()", "onClick": "@controller:findMatches"}]}}}