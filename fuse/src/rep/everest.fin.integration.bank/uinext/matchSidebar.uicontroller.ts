// @i18n:everest.fin.accounting/fundTransfer
// @i18n:dashboard
// @i18n:everest.fin.accounting/bankEntry
// @i18n:creditCardEntry
import type { UIExecutionContext } from '@everestsystems/content-core';
import type { PlainDate } from '@everestsystems/datetime';
import { Decimal } from '@everestsystems/decimal';
import {
  getCurrencySymbol,
  toCurrencyValueString,
} from '@pkg/everest.base/public/currency/precision';
import {
  getUIModelFromSharedState,
  setUIModelInSharedState,
} from '@pkg/everest.base/public/utils/ui/sharedStateUtils.ui';
import type { EvstCurrencyAmount } from '@pkg/everest.base/types/composites/CurrencyAmount';
import type { EvstBaseCurrency } from '@pkg/everest.base/types/enums/BaseCurrency';
import { toPlainDate } from '@pkg/everest.fin.accounting/public/utils/Date/date';
import { AccountingPeriodUI } from '@pkg/everest.fin.accounting/types/AccountingPeriod.ui';
import { BankEntryHeaderNewUI } from '@pkg/everest.fin.accounting/types/BankEntryHeaderNew.ui';
import { CreditCardEntryHeaderUI } from '@pkg/everest.fin.accounting/types/CreditCardEntryHeader.ui';
import { EvstAccountSubType } from '@pkg/everest.fin.accounting/types/enums/AccountSubType';
import { EvstAccountType } from '@pkg/everest.fin.accounting/types/enums/AccountType';
import { EvstbankEntryStatus } from '@pkg/everest.fin.accounting/types/enums/bankEntryStatus';
import { EvstCreditCardStatus } from '@pkg/everest.fin.accounting/types/enums/CreditCardStatus';
import { EvstFundTransferLineAccountType } from '@pkg/everest.fin.accounting/types/enums/FundTransferLineAccountType';
import { EvstFundTransferPaymentStatus } from '@pkg/everest.fin.accounting/types/enums/FundTransferPaymentStatus';
import { EvstJournalEntryType } from '@pkg/everest.fin.accounting/types/enums/JournalEntryType';
import { EvstPaymentType } from '@pkg/everest.fin.accounting/types/enums/PaymentType';
import type { EvstTransferReason } from '@pkg/everest.fin.accounting/types/enums/TransferReason';
import { FundTransferHeaderUI } from '@pkg/everest.fin.accounting/types/FundTransferHeader.ui';
import { EvstBankTransactionMatchStatus } from '@pkg/everest.fin.base/types/enums/BankTransactionMatchStatus';
import { EvstSBOType } from '@pkg/everest.fin.integration.bank/types/enums/SBOType';
import { NetZeroImpactUI } from '@pkg/everest.fin.integration.bank/types/NetZeroImpact.ui';
import { isEmpty, isNil, uniq } from 'lodash';

import { BankTransactionNewUI } from '../types/BankTransactionNew.ui';
import { EvstTransactionPaymentMethod } from '../types/enums/TransactionPaymentMethod';
import type { CreditCardEntryUiTemplate } from '../types/uiTemplates/uinext/creditCardEntry/creditCardEntry.ui';
import type { MatchCreditCardSidebarUiTemplate } from '../types/uiTemplates/uinext/matchCreditCardSidebar.ui';
import type { MatchSidebarUiTemplate } from '../types/uiTemplates/uinext/matchSidebar.ui';
type CreditCardEntryHeader = CreditCardEntryUiTemplate.CreditCardEntryHeader;
type BankTransactionNew = MatchSidebarUiTemplate.BankTransactionNew;
type MatchSidebarContext = MatchSidebarUiTemplate.MatchSidebarContext;
type MatchSidebarData = MatchSidebarUiTemplate.MatchSidebarData;
type MatchCreditCardSidebarData =
  MatchCreditCardSidebarUiTemplate.MatchCreditCardSidebarData;
type CreditCardData = {
  creditCardEntryHeader: MatchCreditCardSidebarData['creditCardEntryHeader'];
  creditCardEntryLines: MatchCreditCardSidebarData['creditCardEntryLines'];
};
//Fund Tranfer types
type AccountType = 'sending' | 'receiving';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type Any = any;

export interface MatchSuggestion {
  label?: string | { text: string; onClick: Any };
  value: number;
  description: string;
  result?: Any;
  disabled: boolean;
  selected: boolean;
}

export type Context = MatchSidebarContext & {
  data: MatchSidebarData & CreditCardData;
  state: {
    // you can extend and improve the state type here
    bankTransactionIds: string;
    matched: boolean;
    mode: 'single' | 'multiple';
    editBE: boolean;
    selectedItems: Any[];
    accountId: number;
    departmentId: number;
    vendorBillAttachments?: Any[];
    postingPeriod: {
      id: number;
      periodName: string;
      canBePosted: boolean;
    };
    /**
     * The untracked state can be used to store caches without triggering
     * more template updates.
     **/
    _untracked: {
      exchangeRatesCache: Map<string, Any>;
    };
    initiallyLoaded: boolean; // Used to check whether the sidebar has selected the suggested match by default(only once)
    type: 'bank' | 'creditCard';
    isSameCurrencyforFT: boolean;
    isFTFieldsDisabled: boolean;
    refreshDynamicFields: boolean; //Currently used to refresh FT dynamic fields
  };
} & {
  helpers: UIExecutionContext.Helpers;
};

const BANK_ENTRY = 'Bank Entry';
const CREDIT_CARD_ENTRY = 'Credit Card Entry';

export function isStatusMatched(context: Context, status: string) {
  return (
    status === 'matched' || status === 'autoMatched' || status === 'pending'
  );
}

export enum EntryTypes {
  BankEntry = -1,
  CreditCardEntry = -2,
  FundTransfer = -3,
  NetZeroImpact = -4,
  MatchWithoutEntry = -5,
}

export function getPaymentTypeInitialValue({ data }: Context) {
  const transactionPaymentType =
    data?.transactions?.[0]?.transactionPaymentMethod;

  switch (transactionPaymentType) {
    case EvstTransactionPaymentMethod.ACH: {
      return EvstPaymentType.Ach;
    }
    case EvstTransactionPaymentMethod.WIRE: {
      return EvstPaymentType.Wire;
    }
    default: {
      break;
    }
  }
}

export function getBankTransactionIds(context: Context) {
  const { state } = context;
  return state.bankTransactionIds.split(',').map(Number);
}

export function getBankTransactionMatchQuery(context: Context) {
  const { data } = context;
  const bankTransactionIds = (data?.transactions ?? []).map((t) => t.id);
  if (bankTransactionIds?.length > 0) {
    return {
      where: {
        bankTransactionId: {
          $in: bankTransactionIds,
        },
      },
    };
  }
}

export function getMatchDocumentQuery(context: Context) {
  const { data } = context;
  const bankTransactionMatchIds = (data?.bankTransactionMatches ?? []).map(
    (t) => t.id
  );
  if (bankTransactionMatchIds?.length > 0) {
    return {
      where: {
        bankTransactionMatchId: {
          $in: bankTransactionMatchIds,
        },
        orderBy: [
          { field: 'sortingPriority', ordering: 'asc' },
          { field: 'isAFullMatch', ordering: 'desc' },
          {
            field: 'suggestionConfidence',
            ordering: 'desc',
          },
          {
            field: 'amount',
            ordering: 'desc',
          },
        ],
      },
    };
  }
}

export function getSBOQuery(context: Context) {
  const { data } = context;
  const matchSFDIDs = (data?.matchDocuments ?? []).map((t) => t.id);
  if (matchSFDIDs?.length > 0) {
    return {
      where: {
        matchSFDID: {
          $in: matchSFDIDs,
        },
      },
    };
  }
}
// Need this query to fetch some extra details
export async function getFTQuery(context: Context) {
  const { data } = context;
  if (data?.matchDocuments?.length > 0) {
    const ids = [];
    for (const md of data.matchDocuments) {
      if (md.sfdNumber?.startsWith('FT-')) {
        ids.push(md.sfdId);
      }
    }
    if (ids.length > 0) {
      return { where: { id: { $in: ids } } };
    }
  }
}

export function getEntitiesQuery(context: Context) {
  const { isFundTransfer } = getSendingAndReceivingBankDetails(context) ?? {};
  if (isFundTransfer) {
    return {
      query: { where: {} },
      fieldList: ['entityName', 'id', 'EntityBook-Entity.chartOfAccountsId'],
    };
  }
}

export function getbankFeeConfigQuery(context: Context) {
  const { data } = context;
  const { entities = [] } = data ?? {};
  const { isFundTransfer, sendingTransaction } =
    getSendingAndReceivingBankDetails(context) ?? {};
  const entityId = sendingTransaction?.entityId;

  if (isFundTransfer && entities.length > 0) {
    const entity = entities.find((e) => e.id === entityId);
    const coaId = entity?.['EntityBook-Entity']?.[0]?.chartOfAccountsId;
    if (coaId > 0) {
      return { where: { coaId } };
    }
  }
}

export function getStatus(context: Context) {
  const { data } = context;
  const matches = data.bankTransactionMatches;
  if (!matches) {
    return '';
  }
  if (matches.length > 0) {
    return matches[0].status.toUpperCase();
  }
  return 'UNMATCHED';
}

const getBankLogo = (logoUrl: string) => {
  if (!logoUrl) {
    return null;
  }

  // Check if the logo is a URL
  if (logoUrl.startsWith('http://') || logoUrl.startsWith('https://')) {
    return logoUrl;
  }

  // If not a URL, assume it's a base64-encoded image
  return `data:image/png;base64,${logoUrl}`;
};

export function getAvatarList(context: Context) {
  const { data } = context;
  const { transactions } = data;
  if (transactions && transactions.length > 0) {
    const [transaction] = transactions; // All selected transactions belong to a single bank account
    const accountName = 'BankTransactionNew-BankAccount';
    const fsName = 'BankAccount-FinancialInstitution';
    const { isFundTransfer, sendingTransaction, receivingTransaction } =
      getSendingAndReceivingBankDetails(context) ?? {};
    if (isFundTransfer) {
      const sendingFs = sendingTransaction?.[accountName]?.[fsName];
      const receivingFs = receivingTransaction?.[accountName]?.[fsName];
      let sendingBankLogo = '';
      if (sendingFs?.logoUrl) {
        sendingBankLogo = getBankLogo(sendingFs.logoUrl);
      } else if (sendingFs?.logoId) {
        sendingBankLogo = `/api/app/storage/${sendingFs.logoId}`;
      }
      let receivingBankLogo = '';
      if (receivingFs?.logoUrl) {
        receivingBankLogo = getBankLogo(receivingFs.logoUrl);
      } else if (receivingFs?.logoId) {
        receivingBankLogo = `/api/app/storage/${receivingFs.logoId}`;
      }
      const avatarList = [
        {
          isEditable: false,
          size: 'large',
          src: receivingBankLogo,
          name: receivingTransaction?.accountName,
        },
        {
          isEditable: false,
          size: 'large',
          src: sendingBankLogo,
          name: sendingTransaction?.accountName,
        },
      ];
      return avatarList;
    } else {
      const fs = transaction?.[accountName]?.[fsName];
      let bankLogo = '';
      if (fs?.logoUrl) {
        bankLogo = getBankLogo(fs.logoUrl);
      } else if (fs?.logoId) {
        bankLogo = fs.logoId;
      }
      const avatarList = [
        {
          isEditable: false,
          size: 'large',
          src: bankLogo,
          name: transaction?.accountName,
        },
      ];
      return avatarList;
    }
  }
  return [];
}

export function getStatusTooltip({ data }: Context) {
  const matches = data.bankTransactionMatches;

  if (matches?.length > 0 && matches[0].status === 'pending') {
    return '{{dashboard.pendingMessage}}';
  }
}

export function calculateTotalBankTransactionAmount(
  context: Context
): EvstCurrencyAmount {
  const { data, Decimal } = context;
  const { transactions = [] } = data ?? {};
  let totalAmount: Decimal = new Decimal(0);

  if (transactions && transactions.length > 0) {
    const { transactions } = data;
    for (const transaction of transactions) {
      const coaAccountType =
        transaction['BankTransactionNew-BankAccount']?.coaAccountType;
      const amount =
        coaAccountType === EvstAccountType.Liability
          ? new Decimal(transaction?.amount?.amount ?? 0).times(-1)
          : new Decimal(transaction?.amount?.amount ?? 0);
      totalAmount = totalAmount.plus(amount);
    }
  }
  return { amount: totalAmount };
}

export function getAvatarTitle(context: Context) {
  const { data, helpers } = context;
  const transactions = data?.transactions;
  if (transactions && transactions.length > 0) {
    if (transactions.length === 2) {
      const { isFundTransfer } =
        getSendingAndReceivingBankDetails(context) ?? {};

      if (isFundTransfer) {
        // Fund transfer

        const sendingTransaction = transactions.find((bt) =>
          new Decimal(bt.amount?.amount).lessThan(new Decimal(0))
        );

        const title = helpers.parseValue({
          value: new Decimal(sendingTransaction.amount?.amount).toFixed(2),
          format: sendingTransaction.currency,
          parseAs: 'currency',
        });
        return title;
      }
    }
    const totalAmount = calculateTotalBankTransactionAmount(context);
    const title = helpers.parseValue({
      value: totalAmount?.amount?.toFixed(),
      format: data.transactions[0].currency,
      parseAs: 'currency',
    });

    return title;
  }
  return '';
}

function getSendingAndReceivingBankDetails(context: Context) {
  const { data } = context;
  const { transactions = [] } = data ?? {};

  const sendingTransaction = transactions.find((bt) =>
    new Decimal(bt.amount.amount).lessThan(new Decimal(0))
  );

  const receivingTransaction = transactions.find((bt) =>
    new Decimal(bt.amount.amount).greaterThan(new Decimal(0))
  );

  const sendingBank = sendingTransaction?.['BankTransactionNew-BankAccount'];
  const receivingBank =
    receivingTransaction?.['BankTransactionNew-BankAccount'];

  const isFundTransfer =
    !!sendingBank &&
    !!receivingBank &&
    sendingBank?.accountId !== receivingBank?.accountId;

  return {
    isFundTransfer,
    sendingTransaction,
    receivingTransaction,
    sendingBank,
    receivingBank,
  };
}

export function getAvatarDescription(context: Context) {
  const { data, helpers } = context;

  if (!data?.transactions || data.transactions.length === 0) {
    return '';
  }
  const { transactions } = data ?? {};
  let fullDescription = '';

  if (transactions?.length === 2) {
    const { isFundTransfer, sendingTransaction, receivingTransaction } =
      getSendingAndReceivingBankDetails(context) ?? {};

    if (isFundTransfer) {
      // Fund transfer
      fullDescription = `${sendingTransaction.accountName} → ${receivingTransaction.accountName}`;
      return fullDescription;
    }
  }

  for (const transaction of transactions) {
    const { externalCreationDate, payeeOrPayor, externalDescription } =
      transaction;
    const date = helpers.parseValue({
      value: new Date(toPlainDate(externalCreationDate).toISO()),
      parseAs: 'date',
    });
    fullDescription = `${fullDescription} \n ${date}`;
    if (payeeOrPayor) {
      fullDescription = `${fullDescription}, ${payeeOrPayor}`;
    }
    if (externalDescription) {
      fullDescription = `${fullDescription}, ${externalDescription}`;
    }
  }
  return fullDescription;
}

export function getAvatarName(context: Context) {
  const { data } = context;
  const { transactions } = data ?? {};

  if (!transactions || transactions.length === 0) {
    return '';
  }
  const defaultName = transactions[0].accountName;

  if (transactions?.length === 1) {
    return defaultName;
  } else {
    const { isFundTransfer, sendingTransaction, receivingTransaction } =
      getSendingAndReceivingBankDetails(context) ?? {};

    return isFundTransfer
      ? `${sendingTransaction.accountName.at(
          0
        )} ${receivingTransaction.accountName.at(0)}`
      : defaultName;
  }
}

export function getMatchTitle(context: Context) {
  const { data, state } = context;
  const { transactions, matchDocuments } = data;
  const { isFundTransfer } = getSendingAndReceivingBankDetails(context) ?? {};

  if (transactions && transactions.length > 0) {
    if (isFundTransfer && isMatched(context)) {
      state.actionGroupType = null;
    } else if (
      !matchDocuments ||
      matchDocuments.length === 0 ||
      (!areAllSendingTransactions(context) &&
        !areAllReceivingTransactions(context))
    ) {
      state.actionGroupType = 'radio';
    } else {
      const allFullMatches = (matchDocuments ?? []).every(
        (md) => md.isAFullMatch
      );
      state.actionGroupType = isMatched(context)
        ? null
        : allFullMatches
          ? 'radio'
          : 'checkbox';
    }
    const [{ matchMessage, matchStatus }] = transactions; // All the transaction will have the same value
    if (isStatusMatched(context, matchStatus)) {
      return 'Matched to';
    }
    return matchMessage;
  }
  return '';
}

export function isFundTransferDetailBlockVisible(context: Context) {
  const { data, state, sharedState } = context;
  const transactions = data?.transactions;

  if (!transactions || transactions.length !== 2) {
    return false;
  }

  const fundTransferDetails = getSendingAndReceivingBankDetails(context);
  if (!fundTransferDetails?.isFundTransfer) {
    return false;
  }

  if (state.refreshDynamicFields) {
    sharedState.reset(['fundTransfer']);
    state.refreshDynamicFields = false;
  }

  const { sendingTransaction, receivingTransaction } = fundTransferDetails;
  const [{ matchStatus }] = transactions;

  const allSameCurrency =
    new Set([sendingTransaction?.currency, receivingTransaction?.currency])
      .size === 1;

  state.isFTFieldsDisabled =
    (allSameCurrency &&
      new Decimal(sendingTransaction?.amount?.amount)
        .absoluteValue()
        .equals(receivingTransaction?.amount?.amount)) ||
    !context?.data?.bankFeeConfiguration?.bankFeeAccountId;
  setUIModelInSharedState(
    context,
    'fundTransfer',
    'isIntercompany',
    isIntercompanyFT(context)
  );
  state.isSameCurrencyforFT = allSameCurrency;

  return matchStatus !== EvstBankTransactionMatchStatus.Matched;
}

function getBankTransaction(context: Context, accountType: AccountType) {
  const bankDetails = getSendingAndReceivingBankDetails(context);
  return accountType === 'sending'
    ? bankDetails.sendingTransaction
    : bankDetails.receivingTransaction;
}

function formatCurrencyAmount(
  amount: Decimal | number,
  currency?: string
): string {
  if (!currency) {
    return amount instanceof Decimal
      ? amount.toFixed(2)
      : Number(amount).toFixed(2);
  }
  return toCurrencyValueString(
    amount instanceof Decimal ? amount : new Decimal(amount),
    currency as EvstBaseCurrency
  );
}

// fundTransfer.bankFee is a dynamic field, so value initialization is required
export function getDefaultFee(context: Context) {
  const bankDetails = getSendingAndReceivingBankDetails(context);
  if (!bankDetails) {
    return undefined;
  }
  return new Decimal(0);
}

export function getAmounts(
  context: Context,
  accountType: string
): string | undefined {
  const bankDetails = getSendingAndReceivingBankDetails(context);
  if (!bankDetails?.isFundTransfer) {
    return undefined;
  }

  const { sendingTransaction, receivingTransaction } = bankDetails;
  const isSendingAcc = accountType === 'sending';

  // Get or initialize the fund transfer data
  const fundTransferData =
    getUIModelFromSharedState(context, 'fundTransfer') || {};
  let { sendingAmount } = fundTransferData as {
    sendingAmount?: Decimal;
  };

  if (!sendingAmount) {
    const amount = new Decimal(
      sendingTransaction.amount?.amount ?? 0
    ).absoluteValue();
    setUIModelInSharedState(
      context,
      'fundTransfer',
      'sendingAmount' as Any,
      amount
    );
    sendingAmount = amount;
  }

  const transaction = isSendingAcc ? sendingTransaction : receivingTransaction;
  const amount = isSendingAcc
    ? new Decimal(sendingAmount)
    : new Decimal(transaction.amount?.amount ?? 0);

  return formatCurrencyAmount(amount, transaction.amount?.currency);
}

export function getFundTransferBankDetails(
  context: Context,
  accountType: string
) {
  const bankDetails = getSendingAndReceivingBankDetails(context);
  if (!bankDetails) {
    return undefined;
  }

  const field = accountType === 'sending' ? 'sendingBank' : 'receivingBank';
  return bankDetails[field];
}

export function getAccountTitle(context: Context, accountType: string) {
  const labelPrefix = accountType === 'sending' ? 'From' : 'To';
  const bankDetails = getSendingAndReceivingBankDetails(context);
  if (!bankDetails) {
    return labelPrefix;
  }

  const bankAccount = getFundTransferBankDetails(context, accountType);
  const bankName =
    bankAccount?.bankName ??
    (accountType === 'sending'
      ? bankDetails.sendingTransaction?.accountName
      : bankDetails.receivingTransaction?.accountName);
  return bankAccount ? `${labelPrefix} ${bankName}` : labelPrefix;
}

export function getAvatar(context: Context, accountType: string) {
  const bankDetails = getSendingAndReceivingBankDetails(context);
  if (!bankDetails) {
    return '';
  }

  const isSendingAcc = accountType === 'sending';
  const accountName = 'BankTransactionNew-BankAccount';
  const fsName = 'BankAccount-FinancialInstitution';

  const transaction = getBankTransaction(
    context,
    isSendingAcc ? 'sending' : 'receiving'
  );
  const fundTransferDetail = transaction?.[accountName]?.[fsName];

  if (!fundTransferDetail) {
    return '';
  }

  if (fundTransferDetail.logoUrl) {
    return getBankLogo(fundTransferDetail.logoUrl);
  } else if (fundTransferDetail.logoId) {
    return `/api/app/storage/${fundTransferDetail.logoId}`;
  }

  return '';
}

export function getAccountAmount(
  context: Context,
  accountType: AccountType
): string {
  const bankDetails = getSendingAndReceivingBankDetails(context);
  if (!bankDetails) {
    return accountType === 'sending' ? '(0.00)' : '0.00';
  }

  const { helpers } = context;
  const transaction = getBankTransaction(context, accountType);

  // Calculate total amount including bank fees
  const amount = transaction?.amount?.amount || 0;
  const bankFee = transaction?.bankFee || 0;
  let totalAccountAmount =
    accountType === 'sending'
      ? new Decimal(amount).plus(bankFee)
      : new Decimal(amount).minus(bankFee);

  // Apply negative sign for from accounts
  if (accountType === 'sending') {
    totalAccountAmount = totalAccountAmount.absoluteValue().times(-1);
  }

  // Handle case where amount is NaN
  if (totalAccountAmount.isNaN()) {
    const currency = transaction?.currency;
    const defaultAmount = accountType === 'sending' ? '(0.00)' : '0.00';

    if (!currency) {
      return defaultAmount;
    }

    const formattedDefault =
      accountType === 'sending'
        ? `(${getCurrencySymbol(currency as EvstBaseCurrency) ?? ''}0.00)`
        : '0.00';

    return String(
      helpers.parseValue({
        value: formattedDefault,
        format: currency,
        parseAs: 'currency',
      })
    );
  }

  if (!transaction?.currency) {
    return totalAccountAmount.toFixed(2);
  }

  return String(
    helpers.parseValue({
      value: totalAccountAmount.toFixed(2),
      format: transaction.currency,
      parseAs: 'currency',
    })
  );
}

export async function getAccountDescriptionLabel(
  context: Context,
  accountType: string
): Promise<string> {
  const bankDetails = getSendingAndReceivingBankDetails(context);
  if (!bankDetails) {
    return '- -';
  }

  const { data, state } = context;
  const transaction = getBankTransaction(
    context,
    accountType === 'sending' ? 'sending' : 'receiving'
  );

  if (!transaction || !transaction?.id) {
    return '- -';
  }

  const entityName = transaction.entity;
  const accountNumber =
    transaction['BankTransactionNew-BankAccount']?.accountNumber;
  const accountCurrency = transaction?.currency || '';
  const postingPeriodName =
    state?.postingPeriod?.periodName ||
    data?.postingPeriodFT?.['periodName'] ||
    '';
  const postingPeriod = postingPeriodName ? `,${postingPeriodName}` : '';

  return `${entityName},${accountCurrency},${accountNumber} — ${transaction.accountName}${postingPeriod}`;
}

export function getAccountCurrencyDescription(
  context: Context,
  accountType: string
) {
  const bankDetails = getSendingAndReceivingBankDetails(context);
  if (!bankDetails) {
    return '- -';
  }

  const transaction = getBankTransaction(
    context,
    accountType === 'sending' ? 'sending' : 'receiving'
  );
  return transaction?.currency ?? '- -';
}

export function getTooltipFor(context: Context, fieldName: string) {
  const { state, data } = context;
  const errorMessages = {
    bankFeeNoAcc: '{{fundTransfer.noAccount.bankFee.error.message}}',
    ftSendingErrorTooltip:
      '{{fundTransfer.noAccount.bankFee.sendingAmount.error.message}}',
    ftReceivedErrorTooltip:
      '{{fundTransfer.noAccount.bankFee.receivingAmount.error.message}}',
    sameCurrencyAmount: '{{fundTransfer.sameAccountAndCurrency.error.message}}',
  };

  const fieldsToMessages = {
    sendingFields: ['ftSendingAmounts', 'sendingBankFee'],
    receivingFields: ['ftReceivedAmounts', 'receivingBankFee'],
  };

  const relevantFTFields = new Set([
    ...fieldsToMessages.sendingFields,
    ...fieldsToMessages.receivingFields,
  ]);

  // Handle same currency scenario
  if (
    state?.isSameCurrencyforFT &&
    relevantFTFields.has(fieldName) &&
    state?.isFTFieldsDisabled
  ) {
    // Check for unequal amounts in same currency
    const { sendingTransaction, receivingTransaction } =
      getSendingAndReceivingBankDetails(context);
    const sendingAmount = sendingTransaction?.amount?.amount;
    const receivingAmount = receivingTransaction?.amount?.amount;
    if (
      sendingAmount &&
      receivingAmount &&
      !new Decimal(sendingAmount).absoluteValue().equals(receivingAmount)
    ) {
      if (fieldsToMessages.sendingFields.includes(fieldName)) {
        return { text: errorMessages.ftSendingErrorTooltip };
      }
      if (fieldsToMessages.receivingFields.includes(fieldName)) {
        return { text: errorMessages.ftReceivedErrorTooltip };
      }
    }
    // Handle disabled fields due to same currency and amount
    return { text: errorMessages.sameCurrencyAmount };
  }

  // Handle missing bank fee configuration
  if (!data?.bankFeeConfiguration?.id) {
    if (fieldsToMessages.sendingFields.includes(fieldName)) {
      return {
        text:
          fieldName === 'sendingBankFee'
            ? errorMessages.bankFeeNoAcc
            : errorMessages.ftSendingErrorTooltip,
      };
    }
    if (fieldsToMessages.receivingFields.includes(fieldName)) {
      return {
        text:
          fieldName === 'receivingBankFee'
            ? errorMessages.bankFeeNoAcc
            : errorMessages.ftReceivedErrorTooltip,
      };
    }
  }

  return undefined;
}

export async function adjustBankValues(
  context: Context,
  type: 'sending' | 'receiving',
  operation: 'amount' | 'fee'
): Promise<void> {
  const bankDetails = getSendingAndReceivingBankDetails(context);
  if (!bankDetails) {
    return;
  }

  const { sendingTransaction, receivingTransaction } = bankDetails;
  if (
    new Decimal(sendingTransaction?.amount?.amount)
      .absoluteValue()
      .equals(
        new Decimal(receivingTransaction?.amount?.amount).absoluteValue()
      ) &&
    context.state?.isSameCurrencyforFT
  ) {
    return;
  }

  const fundTransferData =
    getUIModelFromSharedState(context, 'fundTransfer') || {};

  const isSending = type === 'sending';
  const transaction = isSending ? sendingTransaction : receivingTransaction;

  // Determine which value to use for calculation based on type and operation
  const fieldToRead = isSending
    ? operation === 'fee'
      ? 'sendingAmount'
      : 'sendingFee'
    : operation === 'fee'
      ? 'receivingAmount'
      : 'receivingFee';

  const fieldToWrite = isSending
    ? operation === 'fee'
      ? 'sendingFee'
      : 'sendingAmount'
    : operation === 'fee'
      ? 'receivingFee'
      : 'receivingAmount';

  const baseValue = fundTransferData[fieldToRead] || new Decimal(0);

  // Skip if receiving amount is zero specifically for receiving fee calculations
  if (
    type === 'receiving' &&
    operation === 'fee' &&
    new Decimal(baseValue).equals(new Decimal(0))
  ) {
    return;
  }

  const totalAmount = new Decimal(
    transaction?.amount?.amount ?? 0
  ).absoluteValue();

  let newValue;
  if (operation === 'fee') {
    newValue = isSending
      ? totalAmount.minus(baseValue) // sendingFee = totalAmount - sendingAmount
      : new Decimal(baseValue).minus(totalAmount); // receivingFee = receivingAmount - totalAmount
  } else {
    newValue = isSending
      ? totalAmount.minus(baseValue) // sendingAmount = totalAmount - sendingFee
      : totalAmount.plus(baseValue); // receivingAmount = totalAmount + receivingFee
  }

  if (newValue.lessThan(new Decimal(0))) {
    return;
  }

  setUIModelInSharedState(
    context,
    'fundTransfer',
    fieldToWrite as Any,
    newValue
  );

  await checkFTCurrencies(context);
}

export async function adjustSendingFee(context: Context): Promise<void> {
  return adjustBankValues(context, 'sending', 'fee');
}

export async function adjustSendingAmount(context: Context): Promise<void> {
  return adjustBankValues(context, 'sending', 'amount');
}

export async function adjustReceivingFee(context: Context): Promise<void> {
  return adjustBankValues(context, 'receiving', 'fee');
}

export async function adjustReceivingAmount(context: Context): Promise<void> {
  return adjustBankValues(context, 'receiving', 'amount');
}

export async function checkFTCurrencies(context: Context) {
  const { state } = context;
  const bankDetails = getSendingAndReceivingBankDetails(context);
  if (!bankDetails) {
    return;
  }
  const { sendingTransaction, receivingTransaction } = bankDetails;

  const allSameCurrency =
    new Set([sendingTransaction?.currency, receivingTransaction?.currency])
      .size === 1;

  if (allSameCurrency) {
    state.isSameCurrencyforFT = true;
  } else if (sendingTransaction?.currency && receivingTransaction?.currency) {
    state.isSameCurrencyforFT = false;
  }
}

export async function isBankFeeInputDisabled(context: Context) {
  return context?.state?.isFTFieldsDisabled;
}

export function validateAmount(
  context: Context,
  type: 'sending' | 'receiving' | 'bankFee'
) {
  return {
    validate: (value: string | number) => {
      if (isNil(value)) {
        return 'Required!';
      }

      const numValue = Number(value);
      if (Number.isNaN(numValue)) {
        return 'Required!';
      }

      if (numValue < 0) {
        return type === 'bankFee'
          ? '{{fundTransfer.bankFeePositive.error.message}}'
          : 'Required!';
      }

      if (type === 'bankFee') {
        return undefined;
      }

      const { sendingTransaction, receivingTransaction } =
        getSendingAndReceivingBankDetails(context) ?? {};

      if (type === 'sending') {
        const totalAmount = new Decimal(
          sendingTransaction?.amount?.amount ?? 0
        ).absoluteValue();

        const bankFee = new Decimal(totalAmount).minus(numValue);
        if (bankFee.lessThan(new Decimal(0))) {
          return '{{fundTransfer.invalidSendingAmount.error.message}}';
        }
      } else if (type === 'receiving') {
        const totalAmount = new Decimal(
          receivingTransaction?.amount?.amount ?? 0
        ).absoluteValue();

        const bankFee = new Decimal(value).minus(totalAmount);
        if (bankFee.lessThan(new Decimal(0))) {
          return '{{fundTransfer.invalidReceivingAmount.error.message}}';
        }
      }

      return undefined;
    },
  };
}

export function isTotalBankTransactionAmountZero({ data }: Context) {
  const { transactions = [] } = data ?? {};
  let totalAmount = new Decimal(0);
  for (const { amount } of transactions) {
    totalAmount = totalAmount.plus(new Decimal(amount.amount ?? 0));
  }
  return totalAmount.toString() === '0.00';
}

export function transactionsBelongToSameBankAccount({ data }: Context) {
  return (data.transactions ?? []).every(
    (t) => t.coaAccountId === data.transactions[0].coaAccountId
  );
}

export function insertActionOptions(
  context: Context,
  options: MatchSuggestion[]
): MatchSuggestion[] {
  const { data, state } = context;
  const { transactions = [] } = data ?? {};
  if (transactions.length > 0) {
    if (transactions.length === 2) {
      const { isFundTransfer } =
        getSendingAndReceivingBankDetails(context) ?? {};

      if (isFundTransfer && isNotMatched(context)) {
        const option = {
          label: isIntercompanyFT(context)
            ? '{{fundTransfer.new.interCompany.msg}}'
            : '{{fundTransfer.new.msg}}',
          value: EntryTypes.FundTransfer,
          description: '',
          selected: state.selectedItems.some(
            (item) => item.value === EntryTypes.FundTransfer
          ),
          disabled: false,
        };
        const isFTAlreadyAdded = state.selectedItems.find(
          (si) => si.value === EntryTypes.FundTransfer
        );
        if (!isFTAlreadyAdded) {
          state.selectedItems = [option];
        }
        options = [option];
        return options;
      }
    }
    if (isNotMatched(context)) {
      const itemsNotSelected = options.length === 0;
      const netZero = isTotalBankTransactionAmountZero(context);
      const areAllSameAccount = transactionsBelongToSameBankAccount(context);

      if (netZero && areAllSameAccount) {
        const otherOptions = [
          {
            label: 'New Net Zero Impact',
            value: EntryTypes.NetZeroImpact,
            description: '',
            selected: false,
            disabled: false,
          },
          {
            label: 'Match without Entry',
            value: EntryTypes.MatchWithoutEntry,
            description: '',
            selected: false,
            disabled: false,
          },
        ];
        options = [...options, ...otherOptions];
      } else {
        const canSelectNewBankEntry =
          state.actionGroupType !== 'checkbox' ||
          !isPaymentDetailsVisible(context);

        const option = {
          label: 'New bank entry',
          value: EntryTypes.BankEntry,
          description: '',
          selected:
            canSelectNewBankEntry &&
            state.selectedItems.some(
              (item) => item.value === EntryTypes.BankEntry
            ),
          disabled: !canSelectNewBankEntry,
        };
        options.push(option);
        const isBEAlreadyAdded = state.selectedItems.find(
          (si) => si.value === EntryTypes.BankEntry
        );
        if (itemsNotSelected && !isBEAlreadyAdded) {
          state.selectedItems = [option];
        }
      }
      return options;
    }
    return options;
  }
  return options;
}

export function getActionGroupData(context: Context) {
  const allMatchSuggestions = getActionGroupOptions(context);
  return insertActionOptions(context, allMatchSuggestions);
}

export function setAsDefault(context: Context, amount: Decimal) {
  const totalBankTransactionAmount =
    calculateTotalBankTransactionAmount(context);
  return totalBankTransactionAmount?.amount.equals(amount);
}

export function getActionGroupOptions(context: Context) {
  const { data, helpers, state } = context;
  const { matchDocuments, transactions } = data;

  const allMatchSuggestions: MatchSuggestion[] = [];
  const netZero =
    isTotalBankTransactionAmountZero(context) &&
    transactionsBelongToSameBankAccount(context);
  if (!netZero && matchDocuments && matchDocuments.length > 0) {
    const [{ currency }] = transactions;
    const previouslyShownMatchDocument = new Set<string>();
    for (const sfd of matchDocuments) {
      const {
        amount,
        businessPartner,
        journalEntryLineDescription,
        postingDate: docPostingDate,
        id,
        sfdNumber,
        sfdId,
        matchDocument,
      } = sfd;
      // this check is to remove duplicate items during M:1 matching
      if (previouslyShownMatchDocument.has(matchDocument)) {
        continue;
      } else {
        previouslyShownMatchDocument.add(matchDocument);
      }

      if (
        sfdNumber?.startsWith('FT-') &&
        !areAllSendingTransactions(context) &&
        !areAllReceivingTransactions(context) &&
        allMatchSuggestions.length > 0
      ) {
        // Ignore receiving transfer
        continue;
      }

      const value = id;
      const label = {
        text: matchDocument,
        onClick: (text: string) => openLink(context, text),
      };
      const parsedAmount =
        sfdNumber?.startsWith('FT-') &&
        new Decimal(amount).lessThan(new Decimal(0))
          ? helpers.parseValue({
              value: new Decimal(amount).absoluteValue(),
              format: currency,
              parseAs: 'currency',
            })
          : helpers.parseValue({
              value: amount,
              format: currency,
              parseAs: 'currency',
            });

      const postingDate = helpers.parseValue({
        value: docPostingDate,
        parseAs: 'date',
      });
      let description = ` ${
        postingDate ? (postingDate as string) + ', ' : ''
      }${parsedAmount}`;

      if (sfdNumber?.startsWith('FT-') && data?.fundTransfers?.length > 0) {
        // If it is fund transfer
        const ft = data?.fundTransfers?.find((ft) => ft.id === sfdId);
        description = ` ${parsedAmount}, ${postingDate}, ${ft?.fromAccountName} → ${ft?.toAccountName}`;
      }

      if (businessPartner) {
        description = `${description}, ${businessPartner}`;
      }
      if (journalEntryLineDescription && !sfdNumber?.startsWith('FT-')) {
        //journalEntryLineDescription is the bankFee details for FT, hence not required to show in UI
        description = `${description}, ${journalEntryLineDescription}`;
      }
      let option: MatchSuggestion;
      if (isMatched(context)) {
        option = {
          label,
          value,
          description,
          disabled: true,
          selected: false,
        };
      } else {
        const optionWithoutSelect: Omit<MatchSuggestion, 'selected'> = {
          label,
          value,
          description,
          // result: {
          //   content: { label: `Confidence: ${suggestionConfidence}` },
          // },
          disabled: false,
        };
        if (
          state.selectedItems.length === 0 &&
          setAsDefault(context, amount) &&
          !state.initiallyLoaded
        ) {
          option = { ...optionWithoutSelect, selected: true };
          state.selectedItems = [option];
          state.initiallyLoaded = true;
        } else {
          option = {
            ...optionWithoutSelect,
            selected: state.selectedItems.some(
              (item) => item.value === optionWithoutSelect.value
            ),
          };
        }
      }
      allMatchSuggestions.push(option);
    }
  }
  return allMatchSuggestions;
}

export function areAllReceivingTransactions(context: Context) {
  const { data } = context;
  const { transactions = [] } = data;
  return transactions.every((bt) =>
    new Decimal(bt.amount.amount).greaterThan(new Decimal(0))
  );
}

export function areAllSendingTransactions(context: Context) {
  const { data } = context;
  const { transactions = [] } = data;
  return transactions.every((bt) =>
    new Decimal(bt.amount.amount).lessThan(new Decimal(0))
  );
}

export function setBankFeeAmount(context: Context) {
  const amount = getSuggestionsWithPostedSFDAmount(context);

  setUIModelInSharedState(context, 'payment', 'bankFeeAmount', amount);
}

export function onMatchOptionChange(
  context: Context,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  { value, allValues }
) {
  const { state } = context;

  state.selectedItems = allValues.filter((item) => item.selected);
  if (isPaymentDetailsVisible(context)) {
    setBankFeeAmount(context);
  }
}

export function isAutoMatchReasonVisible(context: Context) {
  const { data } = context;
  const { transactions, bankTransactionMatches } = data;
  if (
    transactions &&
    bankTransactionMatches &&
    transactions.length > 0 &&
    bankTransactionMatches.length > 0 &&
    isMatched(context)
  ) {
    return !!bankTransactionMatches[0]?.autoMatchReason;
  }
  return false;
}

export function isErrorReasonVisible(context: Context) {
  const { data } = context;
  const { transactions, bankTransactionMatches } = data;
  if (
    transactions &&
    bankTransactionMatches &&
    transactions.length > 0 &&
    bankTransactionMatches.length > 0
  ) {
    const [{ matchStatus }] = transactions;
    return matchStatus === 'needsAttention';
  }
  return false;
}

export function getNotes({ data }: Context) {
  const transaction = data?.transactions?.[0];
  const notes = transaction?.notes;
  if (!notes) {
    return undefined;
  }
  try {
    const parsedData = JSON.parse(notes);
    if (
      typeof parsedData === 'object' &&
      typeof parsedData.author === 'string' &&
      typeof parsedData.date === 'string' &&
      typeof parsedData.content === 'string'
    ) {
      return [
        {
          content: parsedData.content,
          date: parsedData.date,
          author: parsedData.author,
        },
      ];
    }

    return null;
  } catch {
    return notes?.trim();
  }
}
export async function noteOnSubmit(context: Context, noteContent: string) {
  const { actions, data, helpers, user } = context;

  if (isEmpty(noteContent) || data?.transactions.length > 1) {
    //extra validation
    return;
  }
  const noteData = {
    author: (user.currentUserData as any)?.fullName,
    date: new Date(),
    content: noteContent.trim(),
  };
  const jsonNoteData = JSON.stringify(noteData); // Converting Json data inot Json string to store in notes (string datatype)
  const transactionId = data?.transactions?.[0]?.id;
  await BankTransactionNewUI.update(
    context,
    {
      id: transactionId,
    },
    {
      notes: jsonNoteData,
    }
  ).run('transactions');
  await actions.refetchUiModelData({ nodesToLoad: ['transactions'] });
  helpers.currentSidebarSubmitCallback();
}

export async function noteOnDelete(context: Context) {
  const { actions, data, helpers } = context;

  if (data?.transactions.length > 1) {
    return;
  }
  const transactionId = data?.transactions?.[0]?.id;
  await BankTransactionNewUI.update(
    context,
    {
      id: transactionId,
    },
    {
      notes: null,
    }
  ).run('transactions');
  await actions.refetchUiModelData({ nodesToLoad: ['transactions'] });
  helpers.currentSidebarSubmitCallback();
}

export function isNotesVisible(context: Context) {
  return getBankTransactionIds(context).length === 1;
}

export function isAttachmentPreviewVisible(context: Context) {
  const { data } = context;
  if (data.transactions?.length === 2) {
    const { isFundTransfer } = getSendingAndReceivingBankDetails(context) ?? {};
    return isFundTransfer && isMatched(context);
  }
  return (data?.bankTransactionMatches ?? []).length > 0;
}

export function getAttachmentDescription(context: Context) {
  const { data, state } = context;
  const { selectedItems } = state;

  const selectedItemsWithBE = selectedItems.filter(
    (item) =>
      item.value !== EntryTypes.BankEntry &&
      item.value !== EntryTypes.CreditCardEntry
  );
  if (selectedItemsWithBE.length === 0) {
    return '';
  } else if (selectedItemsWithBE.length > 1) {
    return '{{dashboard.multipleAttachmentDescription}}';
  }
  const { attachments, sbos } = data;
  const sbo = sbos.find(
    (sbo) => sbo.matchSFDID === selectedItemsWithBE[0].value
  );
  const selectedAttachments = (attachments ?? []).filter(
    (attachment) => attachment.matchSFDID === sbo?.matchSFDID
  );
  let source = '';
  switch (sbo?.sboType) {
    case EvstSBOType.VendorBill: {
      source = '{{dashboard.bill}}';
      break;
    }
    case EvstSBOType.ExpenseReport: {
      source = '{{dashboard.expenseReport}}';
      break;
    }
    case EvstSBOType.Invoice: {
      source = '{{dashboard.invoice}}';
      break;
    }
    case EvstSBOType.PayrollDetail: {
      source = '{{dashboard.payrollDetail}}';
      break;
    }
    // No default
  }
  const length = selectedAttachments.length;

  const descriptions = selectedAttachments?.map((a) => a.description) ?? [];
  return length > 0
    ? `Attached to ${source} ${uniq(descriptions).join(', ')}`
    : '';
}

export async function getAttachments(context: Context) {
  const { data, state } = context;
  const allAttachments = data?.attachments ?? [];
  const matchStatus = data?.transactions?.[0]?.matchStatus;
  if (isStatusMatched(context, matchStatus)) {
    return allAttachments;
  }
  const { selectedItems } = state;
  const sfdIds = new Set(selectedItems.map((item) => item.value));
  const selectedAttachments = allAttachments.filter((attachment) =>
    sfdIds.has(attachment.matchSFDID)
  );
  return selectedAttachments;
}

export function getEmptyMessage(context: Context) {
  const { data, state } = context;
  let emptyMessage = 'No attachments to preview';
  const matchStatus = data?.transactions?.[0]?.matchStatus;
  if (isStatusMatched(context, matchStatus)) {
    return emptyMessage;
  }
  const { selectedItems } = state;
  if (selectedItems.length === 0) {
    emptyMessage = 'Select a match to preview attachments';
  }
  return emptyMessage;
}

export function isSummaryVisible(context: Context) {
  const { data } = context;
  const { transactions } = data;
  const { isFundTransfer } = getSendingAndReceivingBankDetails(context) ?? {};

  return (
    transactions &&
    !isFundTransfer &&
    (transactions[0].matchStatus === 'suggested' ||
      transactions[0].matchStatus === 'unmatched')
  );
}

export function isFundTransferSummaryVisible(context: Context) {
  const { data } = context;
  const { transactions } = data;
  const { isFundTransfer } = getSendingAndReceivingBankDetails(context) ?? {};

  return (
    transactions &&
    isFundTransfer &&
    (transactions[0].matchStatus === 'suggested' ||
      transactions[0].matchStatus === 'unmatched')
  );
}

export function getSummaryTextLabel(context: Context) {
  const { state } = context;
  const itemsSelected = state.selectedItems.length;
  return `Total (${itemsSelected} selected)`;
}

export function calculateTotalAmountSelected(context: Context) {
  const { state, data } = context;

  let totalAmount = new Decimal(0);
  const { selectedItems } = state;
  for (const item of selectedItems) {
    const matchDocumentId = item.value;
    if (matchDocumentId === EntryTypes.BankEntry) {
      continue; // bank entry
    }
    if (matchDocumentId === EntryTypes.FundTransfer) {
      continue; // fund transfer
    }
    if (matchDocumentId === EntryTypes.CreditCardEntry) {
      continue; // credit card entry
    }
    const matchDocument = data.matchDocuments.find(
      (md) => md.id === matchDocumentId
    );
    const amount = new Decimal(matchDocument?.amount ?? 0);
    totalAmount = totalAmount.plus(amount);
  }
  return totalAmount;
}

export function getTotalAmountSelected(context: Context) {
  const totalAmount = calculateTotalAmountSelected(context);
  return totalAmount.toFixed(2);
}

export function getDifference(context: Context) {
  const totalBankTransactionAmount =
    calculateTotalBankTransactionAmount(context);
  let totalSelectedAmount = calculateTotalAmountSelected(context);

  if (context.data?.payment?.bankFeeAmount?.amount) {
    totalSelectedAmount = totalSelectedAmount.minus(
      context.data?.payment?.bankFeeAmount?.amount
    );
  }

  return totalBankTransactionAmount?.amount
    ?.minus(totalSelectedAmount)
    .toFixed(2);
}

export function getFTCurrency(context: Context, type: string) {
  const { sendingTransaction, receivingTransaction } =
    getSendingAndReceivingBankDetails(context) ?? {};
  return type === 'sendingCurrency'
    ? sendingTransaction?.currency
    : receivingTransaction?.currency;
}

export function isBankEntrySelected(context: Context) {
  const { selectedItems } = context.state;
  return selectedItems.some(({ value }) => value === EntryTypes.BankEntry);
}

export function isFundTransferSelected(context: Context) {
  const { selectedItems } = context.state;
  return selectedItems.some(({ value }) => value === EntryTypes.FundTransfer);
}

export function isCreditCardEntrySelected(context: Context) {
  const { selectedItems } = context.state;
  return selectedItems.some(
    ({ value }) => value === EntryTypes.CreditCardEntry
  );
}

export function isNetZeroImpactSelected(context: Context) {
  const { selectedItems } = context.state;
  return selectedItems.some(({ value }) => value === EntryTypes.NetZeroImpact);
}

export function isMatchWithoutEntrySelected(context: Context) {
  const { selectedItems } = context.state;
  return selectedItems.some(
    ({ value }) => value === EntryTypes.MatchWithoutEntry
  );
}

export function getButtonType(context: Context) {
  return isMatched(context) ? 'secondary' : 'primary';
}

export function getPrimaryButtonLabel(context: Context) {
  if (isMatched(context)) {
    return 'Unmatch';
  }
  const { selectedItems } = context.state;
  if (selectedItems.length === 0) {
    return 'Match';
  }
  if (isFundTransferSelected(context)) {
    return 'Create';
  }
  if (isBankEntrySelected(context) || isCreditCardEntrySelected(context)) {
    return 'Create and Match';
  }
  if (checkSuggestionsHaveDraft(context)) {
    return 'Match';
  }
  if (checkSuggestionsHavePostedSFD(context).length > 0) {
    return 'Record Payment';
  }

  return 'Match';
}

export function isPrimaryButtonDisabled(context: Context) {
  const { isFetchingUiModelData } = context;
  if (isFetchingUiModelData) {
    return true;
  }
  if (isMatched(context)) {
    return false;
  }
  return context.state.selectedItems.every((item) => !item.selected);
}

export function getAccountSemanticTypeUiParams(context: Context) {
  const { data } = context;
  const { transactions } = data;
  const hiddenAccountSubTypes = [
    EvstAccountSubType.Bank,
    EvstAccountSubType.CreditCard,
  ];
  if (transactions && transactions.length > 0) {
    const [{ currency, entityId, coaAccountId }] = transactions;
    return {
      currency,
      entityId,
      srcAccountId: coaAccountId,
      hiddenAccountSubTypes,
      eliminationStatus: false,
    };
  }
}

export function isCreateBankEntryModalVisible(context: Context) {
  if (isMatched(context)) {
    return false;
  }
  return isBankEntrySelected(context);
}

export function isCreateCreditCardEntryModalVisible(context: Context) {
  if (isMatched(context)) {
    return false;
  }
  return isCreditCardEntrySelected(context);
}

export async function unmatchBankTransaction(context: Context) {
  const { actions, data, helpers } = context;
  const [bankTransaction] = data.transactions; // unmatching logic should handle getting all the other transactions.Todo:
  helpers.showNotificationMessage({
    key: 'bankTransaction',
    type: 'loading',
    message: 'Unmatching...',
    duration: 10,
  });
  const response = await actions.run({
    bankTransactionMatchesTransient: {
      action: 'unmatchBankTransaction',
      data: {
        bankTransactionId: bankTransaction.id,
      },
    },
  });

  if (response?.error) {
    helpers.showNotificationMessage({
      key: 'bankTransaction',
      type: 'error',
      message: 'Error',
      duration: 2,
    });
  } else {
    const unmatchedBankTransactions =
      response?.bankTransactionMatchesTransient?.[0];
    if (unmatchedBankTransactions?.length > 0) {
      await actions.run({
        bankTransactionMatchesTransient: {
          action: 'createBankTransactionMatchSuggestions',
          data: {
            where: {
              id: { $in: unmatchedBankTransactions.map((bt) => bt.id) },
            },
          },
        },
      });
    }
    helpers.showNotificationMessage({
      key: 'bankTransaction',
      type: 'success',
      message: 'Transaction unmatched',
      duration: 5,
    });
    helpers.closeSidebarTemplate();
    helpers.currentSidebarSubmitCallback(true);
  }
}
export async function voidAndUnmatch(context: Context) {
  const { data } = context;
  const [bankTransaction] = data.transactions;
  if (bankTransaction.matchingTransaction.includes('NZI-')) {
    const { sfdId, journalEntryHeaderId } = data.matchDocuments[0]; //todo
    await (journalEntryHeaderId
      ? voidNetZeroImpact(context, sfdId)
      : unmatchBankTransaction(context));
  } else {
    await unmatchBankTransaction(context);
  }
}

export async function voidNetZeroImpact(context: Context, sfdId: number) {
  const { helpers } = context;
  const path = helpers.getTemplateLink({
    templateName: 'everest.fin.integration.bank/uinext/voidReasonModal',
    templateState: { id: sfdId },
  });
  helpers.openModal({
    size: 'xsmall',
    title: '{{banking.nzi.void.modalTitle}}',
    template: path,
    onModalSubmit: async (payload) => {
      helpers.showNotificationMessage({
        key: 'nzi',
        type: 'loading',
        message: '{{banking.nzi.void.msg.load}}',
        duration: 20,
      });
      const response = await NetZeroImpactUI.voidNetZeroImpact(context, {
        netZeroImpactId: sfdId,
        voidReason: payload.reason,
      }).run('netZeroImpact');
      if (response?.error) {
        helpers.showNotificationMessage({
          key: 'nzi',
          type: 'error',
          message: 'Error',
          duration: 5,
        });
      } else {
        helpers.showNotificationMessage({
          key: 'nzi',
          type: 'success',
          message: 'Voided and unmatched',
          duration: 3,
        });
        helpers.closeSidebarTemplate();
        helpers.currentSidebarSubmitCallback(true);
      }
    },
  });
}

export function getSelectedSuggestions(
  context: Context
): MatchSidebarData['matchDocuments'] {
  const { data, state } = context;
  const suggestionsIds = new Set(state.selectedItems.map((item) => item.value));
  const { isFundTransfer } = getSendingAndReceivingBankDetails(context) ?? {};

  const selectedSuggestion = state.selectedItems.find((item) => item.selected);
  const sfdNumber = (data?.matchDocuments ?? []).find(
    (md) => md.id === selectedSuggestion?.value
  )?.sfdNumber;

  const suggestions = (data?.matchDocuments ?? []).filter((md) =>
    isFundTransfer && sfdNumber
      ? md.sfdNumber === sfdNumber
      : suggestionsIds.has(md.id)
  );
  return suggestions as unknown as MatchSidebarData['matchDocuments'];
}

export function checkSuggestionsHavePostedSFD(context: Context) {
  const selectedSuggestions = getSelectedSuggestions(context);
  const suggestionsWithoutJELineId = [];
  for (const suggestion of selectedSuggestions) {
    if (!suggestion?.sourceJournalEntryLineId) {
      suggestionsWithoutJELineId.push(suggestion);
    }
  }
  return suggestionsWithoutJELineId;
}

export function isPaymentDetailsVisible(context: Context) {
  return (
    checkSuggestionsHavePostedSFD(context).length > 0 &&
    calculateTotalBankTransactionAmount(context).amount.greaterThan(0)
  );
}

export function getSuggestionsWithPostedSFDAmount(context: Context) {
  let amount = new Decimal(0);
  const totalBankTransactionAmount =
    calculateTotalBankTransactionAmount(context);
  const suggestionsWithoutJELineId = checkSuggestionsHavePostedSFD(context);
  // eslint-disable-next-line unicorn/no-array-reduce
  const totalAmount = suggestionsWithoutJELineId.reduce(
    (acc, item) => acc.plus(item.amount),
    new Decimal(0)
  );
  const difference = totalBankTransactionAmount?.amount
    ?.minus(totalAmount)
    .toFixed(2);
  if (new Decimal(difference).lessThan(0)) {
    amount = new Decimal(difference).times(-1);
  }
  return { amount };
}

export function checkSuggestionsHaveDraft(context: Context) {
  const selectedSuggestions = getSelectedSuggestions(context);
  for (const { sboNumber } of selectedSuggestions) {
    if (sboNumber === 'Draft') {
      return true;
    }
  }
  return false;
}

export async function createPayment(
  context: Context,
  suggestions: Any[]
): Promise<{ error?: { message: string } }> {
  const { actions, data, helpers } = context;
  const bankTransactions = data.transactions.map((t) => ({ id: t.id }));

  helpers.showNotificationMessage({
    key: 'paymentCreation',
    type: 'loading',
    message: 'Creating Payment...',
    duration: 20,
  });
  return actions.run({
    bankTransactionMatchesTransient: {
      action: 'createSFDForMatching',
      data: {
        bankTransactions,
        suggestions,
        paymentDetails: data.payment,
      },
    },
  });

  /* const response = await BankTransactionMatchTransientUI.createSFDForMatching(
    context,
    {
      bankTransactions,
      suggestions,
    }
  ).run('bankTransactionMatchesTransient'); */
}

export async function match(
  context: Context,
  status?: string,
  autoMatchReason?: string
) {
  const { actions, data, helpers } = context;
  const bankTransactions = data.transactions.map((t) => ({ id: t.id }));
  const suggestions = getSelectedSuggestions(context);
  helpers.showNotificationMessage({
    key: 'bankTransaction',
    type: 'loading',
    message: 'Matching...',
    duration: 10,
  });

  const response = await actions.run({
    bankTransactionMatchesTransient: {
      action: 'matchBankTransactions',
      data: {
        bankTransactions,
        suggestions,
        status,
        autoMatchReason,
      },
    },
  });

  /* const response = await BankTransactionMatchTransientUI.matchBankTransactions(
    context,
    {
      bankTransactions,
      suggestions,
      status,
      autoMatchReason,
    }
  ).run('bankTransactionMatchesTransient'); */

  await actions.refetchUiModelData();
  if (response?.error) {
    helpers.showNotificationMessage({
      key: 'bankTransaction',
      type: 'error',
      message: 'Error',
      duration: 2,
    });
  } else {
    helpers.showNotificationMessage({
      key: 'bankTransaction',
      type: 'success',
      message: 'Matched',
      duration: 5,
    });
    helpers.closeSidebarTemplate();
    helpers.currentSidebarSubmitCallback(true);
  }
}

export async function matchWithEntry(
  context: Context,
  suggestions: Any[],
  status?: string,
  autoMatchReason?: string
) {
  const { actions, data, helpers } = context;
  const bankTransactions = data.transactions.map((t) => ({ id: t.id }));
  helpers.showNotificationMessage({
    key: 'bankTransaction',
    type: 'loading',
    message: 'Matching...',
    duration: 10,
  });

  const response = await actions.run({
    bankTransactionMatchesTransient: {
      action: 'matchBankTransactions',
      data: {
        bankTransactions,
        suggestions,
        status,
        autoMatchReason,
      },
    },
  });

  /* const response = await BankTransactionMatchTransientUI.matchBankTransactions(
    context,
    {
      bankTransactions,
      suggestions,
      status,
      autoMatchReason,
    }
  ).run('bankTransactionMatchesTransient'); */

  await actions.refetchUiModelData();
  if (response?.error) {
    helpers.showNotificationMessage({
      key: 'bankTransaction',
      type: 'error',
      message: 'Error',
      duration: 2,
    });
  } else {
    helpers.showNotificationMessage({
      key: 'bankTransaction',
      type: 'success',
      message: 'Matched',
      duration: 5,
    });
    helpers.closeSidebarTemplate();
    helpers.currentSidebarSubmitCallback(true);
  }
}

export async function apply(context: Context) {
  if (isMatched(context)) {
    return voidAndUnmatch(context);
  } else if (isBankEntrySelected(context)) {
    await createBankEntryDirectly(context);
  } else if (isFundTransferSelected(context)) {
    await createFundTransferDirectly(context);
  } else if (isCreditCardEntrySelected(context)) {
    await createCreditCardEntryDirectly(context);
  } else if (isNetZeroImpactSelected(context)) {
    await createNetZeroImpact(context);
  } else if (isMatchWithoutEntrySelected(context)) {
    await createNetZeroImpact(context, false);
  } else {
    const { helpers } = context;

    const amountMatching = getDifference(context) === '0.00';
    if (!amountMatching) {
      return helpers.showToast({
        key: 'amount',
        duration: 2,
        message:
          'Amount is not matching. Please review selected items or select a bank entry.',
      });
    }
    let status: string;
    let reason: string;
    const suggestionsWithoutJELineId = checkSuggestionsHavePostedSFD(context);
    if (suggestionsWithoutJELineId.length > 0) {
      const { data } = context;
      let shouldCreatePayment = true;
      for (const suggestion of suggestionsWithoutJELineId) {
        const sbos = data?.sbos?.filter(
          (sbo) => sbo.matchSFDID === suggestion.id
        );
        if (sbos.some((sbo) => sbo.$metadata.isDraft)) {
          shouldCreatePayment = false;
          break;
        }
      }
      if (shouldCreatePayment) {
        const response = await createPayment(
          context,
          suggestionsWithoutJELineId
        );
        if (response?.error) {
          helpers.showNotificationMessage({
            key: 'paymentCreation',
            type: 'error',
            message: 'Payment creation failed. Please create payment manually.',
            duration: 2,
          });
          return;
        } else {
          helpers.showNotificationMessage({
            key: 'paymentCreation',
            type: 'success',
            message: 'Payment Created',
            duration: 5,
          });
        }
      }
    }
    return match(context, status, reason); // todo: status
  }
}

export function isSecondaryButtonDisabled(context: Context) {
  const { isFetchingUiModelData } = context;
  if (isFetchingUiModelData) {
    return true;
  }
  const { data } = context;
  const { transactions } = data;

  if (transactions) {
    if (transactions.length === 1) {
      return false;
    }
    const areAllSameAccount = transactionsBelongToSameBankAccount(context);
    return !areAllSameAccount;
  }
  return true;
}

export function isFindMatchesButtonVisible(context: Context) {
  const { data } = context;
  const { transactions } = data;
  const visible =
    !!transactions &&
    (transactions[0].matchStatus === 'suggested' ||
      transactions[0].matchStatus === 'unmatched');

  return visible;
}

export async function findMatches(context: Context) {
  const { actions, data, helpers, state } = context;

  const areAllSameAccount = transactionsBelongToSameBankAccount(context);
  if (areAllSameAccount) {
    const [{ id, coaAccountId, entityId }] = data.transactions;
    const response = await actions.run({
      bankTransactionMatchesTransient: {
        action: 'getAllUnmatchedDocuments',
        data: {
          accountId: coaAccountId,
          getAllEntityAccounts: true,
        },
      },
    });
    const allAccounts = new Set(
      (response?.bankTransactionMatchesTransient[0] ?? []).map(
        (doc) => doc.accountId
      )
    );
    const bankTransactionIds = data.transactions.map((t) => t.id);
    helpers.openModal({
      title: `Find matches`,
      size: 'large',
      template: `/templates/everest.fin.integration.bank/uinext/findMatches`,
      initialState: {
        bankTransactionIds,
        id,
        accountId: coaAccountId,
        entityId,
        type: state.type,
        filterAccountIds: Array.from(allAccounts).filter(
          (id) => id !== null && id !== undefined && id !== Number(coaAccountId)
        ),
        //filterAccounts will be fixed and need to be passed(fetching dynamically will cause multiple reload error)
      },
      onModalSubmit: () => {
        helpers.currentSidebarSubmitCallback(true);
      },
      onClose: actions.refetchUiModelData,
    });
  }
}

export function isMatched(context: Context) {
  const { data } = context;
  const { transactions } = data;
  if (transactions && transactions.length > 0) {
    const [{ matchStatus }] = transactions;
    return isStatusMatched(context, matchStatus);
  }
  return false;
}

export function isNotMatched(context: Context) {
  return !isMatched(context);
}

export async function openLink(context: Context, text: string) {
  const { actions, data, helpers } = context;
  let title: string;
  let template: string;
  if (text.startsWith('VB-')) {
    const doc = data?.sbos?.find((sbo) => sbo.sboNumber === text);
    title = 'Vendor Bill';
    template = `/templates/everest.fin.expense/uinext/vendorMgmt/vendorBill?feat-delta=true&id=${doc.sboId}`;
  } else if (text.startsWith('ER-')) {
    const doc = data?.sbos?.find((sbo) => sbo.sboNumber === text);
    title = 'Expense Report';
    template = `everest.fin.expense/uinext/expenseMgmt/createExpenseReport?feat-delta=true&id=${doc.sboId}`;
  } else if (text.startsWith('PD-')) {
    const doc = data?.sbos?.find((sbo) => sbo.sboNumber === text);
    title = 'Payroll Detail';
    template = `/templates/everest.fin.expense/uinext/payrollMgmt/payrollDetailV2?id=${doc.sboId}&mode=view`;
  } else if (text.startsWith('INV-')) {
    const doc = data?.sbos?.find((sbo) => sbo.sboNumber === text);
    title = 'Invoice';
    template = `/templates/everest.fin.accounting/qtc/invoicing/uinext/invoice?id=${doc.sboId}`;
  } else if (text.startsWith('OPAY-')) {
    const doc = data?.matchDocuments?.find((sfd) => sfd.sfdNumber === text);
    title = 'Outbound Payment';
    template = `/templates/everest.fin.expense/payment/uinext/payment?mode=payment&id=${doc.sfdId}`;
  } else if (text.startsWith('PAY-')) {
    const doc = data?.matchDocuments?.find((sfd) => sfd.sfdNumber === text);
    title = 'Customer Payment';
    template = `/templates/everest.fin.accounting/qtc/payment/uinext/payment?id=${doc.sfdId}`;
  } else if (text.startsWith('JE-')) {
    const doc = data?.matchDocuments?.find((sfd) => sfd.sfdNumber === text);
    title = 'Journal Entry';
    template = `@uicode:journalEntry?id=${doc.sfdId}`;
  } else if (text.startsWith('BE-')) {
    const doc = data?.matchDocuments?.find((sfd) => sfd.sfdNumber === text);
    title = 'Bank Entry';
    template = `/templates/everest.fin.integration.bank/uinext/bankEntry/bankEntry?mode=view&id=${doc.sfdId}&feat-delta=true`;
  } else if (text.startsWith('CCE-')) {
    const doc = data?.matchDocuments?.find((sfd) => sfd.sfdNumber === text);
    title = 'Credit Card Entry';
    template = `/templates/everest.fin.integration.bank/uinext/creditCardEntry/creditCardEntry?mode=view&id=${doc.sfdId}&feat-delta=true`;
  } else if (text.startsWith('FT-')) {
    const doc = data?.matchDocuments?.find((sfd) => sfd.sfdNumber === text);
    title = 'Fund Transfer';
    template = `/templates/everest.fin.integration.bank/uinext/fundTransfer/fundTransfer?id=${doc.sfdId}`;
  } else if (text.startsWith('NZI-')) {
    const doc = data?.matchDocuments?.find((sfd) => sfd.sfdNumber === text);
    const id = doc?.journalEntryHeaderId;
    if (!id) {
      // Matched without entry
      helpers.showToast({
        title: 'Match without Entry',
        type: 'info',
        message: 'No source document',
      });
      return;
    }
    title = 'Journal Entry';
    template = `@uicode:journalEntry?id=${doc.journalEntryHeaderId}`;
  } else if (text.startsWith('Draft')) {
    const bill = data?.sbos?.find((sbo) => sbo.sboName === text);
    title = 'Vendor Bill';
    template = `/templates/everest.fin.expense/uinext/vendorMgmt/createVendorBill?id=${bill.sboId}`;
  } else {
    helpers.showToast({
      title: 'Parse error',
      type: 'warning',
      message: 'Cannot recoganize the document',
    });
    return;
  }
  helpers.openModal({
    title,
    size: 'large',
    template,
    initialState: {
      isModal: true,
      ...(template.includes('createExpenseReport') && {
        mode: 'review',
      }),
    },
    onModalSubmit: actions.refetchUiModelData,
    onClose: actions.refetchUiModelData,
  });
}

export function validateBankOrCreditCardEntry(context: Context) {
  const { state, helpers } = context;
  const accountId = state.accountId;
  let errors = 0;

  if (accountId === undefined) {
    helpers.showToast({
      type: 'error',
      title: '{{bankEntry.validations.error}}',
      message: '{{bankEntry.validations.accountIdMissing}}',
    });
    errors++;
  }

  if (
    state?.postingPeriod?.canBePosted === undefined ||
    state?.postingPeriod?.canBePosted === false
  ) {
    helpers.showToast({
      type: 'error',
      title: '{{bankEntry.validations.error}}',
      message: '{{bankEntry.postingDate.notFound}}',
    });
    errors++;
  }

  return errors;
}

export function getBankEntryName(context: Context) {
  const { data } = context;
  return data?.bankEntryHeader[0]?.bankEntryName ?? BANK_ENTRY;
}

export async function fetchPostingPeriod(context: Context, date: PlainDate) {
  const { data, state } = context;
  const [{ entityId }] = data.transactions;
  if (!date || !entityId) {
    state.postingPeriod ??= undefined;
  } else {
    const response = await AccountingPeriodUI.client.findAccountingPeriod({
      entityId,
      postingDate: date,
      forPosting: false,
      fieldList: ['id', 'periodName', 'canBePosted'],
    });
    state.postingPeriod = {
      id: response?.id,
      periodName: response?.periodName,
      canBePosted: response?.canBePosted,
    };
    state.date = date;
  }
}

export async function getTransactionDate(context: Context) {
  const { data, state } = context;
  let date: PlainDate;
  const { transactions } = data;
  if (transactions && transactions.length > 0) {
    const [{ externalCreationDate }] = transactions;
    date = externalCreationDate;
  }
  if (!state?.postingPeriod?.id && date) {
    await fetchPostingPeriod(context, date);
  }
  return date;
}

export async function createBankEntryDirectly(context: Context) {
  const { actions, data, helpers, state, sharedState } = context;
  state.saving = true;
  const bankTransactionIds = data.transactions.map((t) => t.id);
  const selectedSuggestions = getSelectedSuggestions(context);

  const transactionDate = await getTransactionDate(context);

  if (validateBankOrCreditCardEntry(context) > 0) {
    state.saving = false;
    return;
  }

  let bankEntryName = getBankEntryName(context);

  if (bankEntryName.length <= 0) {
    bankEntryName = BANK_ENTRY;
  }

  const [{ entityId, currency, coaAccountId }] = data.transactions;
  //Except GroupMatching, every BE created via matchsidebar only have 1 BE line
  const amounts =
    selectedSuggestions.length === 0 || data.transactions.length > 1
      ? data.transactions.map((t) => t.amount)
      : [
          {
            amount: getDifference(context),
            currency: data.transactions[0].currency,
          },
        ];

  const lineData = sharedState.getModelData('bankEntryLines')?.instance?.data;

  const bankEntryLines = [];
  for (const amount of amounts) {
    bankEntryLines.push({
      ...lineData,
      amount: amount,
    });
  }

  const bankEntryHeader = {
    postingPeriodId: state.postingPeriod?.id,
    postingPeriod: state.postingPeriod?.periodName,
    bankEntryName: bankEntryName,
    transactionDate: transactionDate,
    entityId,
    currency,
    accountId: coaAccountId,
    status: EvstbankEntryStatus.Matched,
  };

  helpers.showNotificationMessage({
    key: 'bankEntry',
    type: 'loading',
    message: '{{bankEntry.create}}',
    duration: 20,
  });

  const bankEntryResponse = await BankEntryHeaderNewUI.upsertBankEntry(
    context,
    {
      bankEntryHeader,
      bankEntryLines,
      options: { fieldList: ['id'] },
    }
  ).run('bankEntryHeader');

  if (bankEntryResponse?.error) {
    helpers.showNotificationMessage({
      key: 'bankEntry',
      type: 'error',
      message: '{{bankEntry.transaction.bankEntryFailed}}',
      duration: 5,
    });
    state.saving = false;
    return;
  } else {
    await BankEntryHeaderNewUI.updateBEjournalEntryName(context, {
      bankEntryHeaderId: bankEntryResponse.bankEntryHeader[0].id,
    }).run('bankEntryHeader');
    helpers.closeModal();
    helpers.showNotificationMessage({
      key: 'bankEntry',
      type: 'success',
      message: '{{bankEntry.transaction.bankEntryCreated}}',
      duration: 3,
    });
  }
  state.saving = false;

  const bankEntryHeaderId = bankEntryResponse.bankEntryHeader[0].id;

  if (bankEntryHeaderId) {
    const bankEntryResponse = await actions.run({
      bankTransactionMatchesTransient: {
        action: 'createBankEntrySuggestion',
        data: {
          where: {
            id: { $in: bankTransactionIds },
          } as unknown as BankTransactionNew,
          bankEntryHeaderId,
        },
      },
    });

    if (!bankEntryResponse?.error) {
      const [bankEntrySuggestion] =
        bankEntryResponse.bankTransactionMatchesTransient;
      let status: string;
      let reason: string;
      const suggestionsWithoutJELineId = checkSuggestionsHavePostedSFD(context);
      if (suggestionsWithoutJELineId.length > 0) {
        const response = await createPayment(context, [
          ...bankEntrySuggestion,
          ...selectedSuggestions,
        ]);
        if (response?.error) {
          return helpers.showNotificationMessage({
            key: 'paymentCreation',
            type: 'error',
            message: 'Payment creation failed. Please create payment manually.',
            duration: 2,
          });
        } else {
          helpers.showNotificationMessage({
            key: 'paymentCreation',
            type: 'success',
            message: 'Payment Created',
            duration: 5,
          });
        }
      }
      await matchWithEntry(
        context,
        [...bankEntrySuggestion, ...selectedSuggestions],
        status,
        reason
      );
    }
    await helpers.currentModalSubmitCallback();
  } else {
    helpers.showNotificationMessage({
      key: 'bankEntry',
      message: 'Did not receive bank entry ID',
      type: 'error',
    });
  }
}

export async function createBankEntryInModal(context: Context) {
  const { actions, data, helpers, state } = context;
  const { accountId, departmentId } = state;
  const bankTransactionIds = data.transactions.map((t) => t.id);
  const selectedSuggestions = getSelectedSuggestions(context);
  const matchIds = selectedSuggestions.map(({ id }) => id);
  await helpers.openModal({
    title: `New Bank Entry for Bank Transaction #`,
    size: 'large',
    initialState: {
      mode: 'create',
      bankTransactionIds,
      matchIds,
      accountId,
      departmentId,
    },
    template: `/templates/everest.fin.integration.bank/uinext/bankEntry/bankEntry`,
    onModalSubmit: async (bankEntryHeaderId: number) => {
      if (bankEntryHeaderId) {
        const bankEntryResponse = await actions.run({
          bankTransactionMatchesTransient: {
            action: 'createBankEntrySuggestion',
            data: {
              where: {
                id: { $in: bankTransactionIds },
              } as unknown as BankTransactionNew,
              bankEntryHeaderId,
            },
          },
        });

        if (!bankEntryResponse?.error) {
          const [bankEntrySuggestion] =
            bankEntryResponse.bankTransactionMatchesTransient;
          let status: string;
          let reason: string;
          const suggestionsWithoutJELineId =
            checkSuggestionsHavePostedSFD(context);
          if (suggestionsWithoutJELineId.length > 0) {
            const response = await createPayment(context, [
              ...bankEntrySuggestion,
              ...selectedSuggestions,
            ]);
            if (response?.error) {
              return helpers.showNotificationMessage({
                key: 'paymentCreation',
                type: 'error',
                message:
                  'Payment creation failed. Please create payment manually.',
                duration: 2,
              });
            } else {
              helpers.showNotificationMessage({
                key: 'paymentCreation',
                type: 'success',
                message: 'Payment Created',
                duration: 5,
              });
            }
          }
          await matchWithEntry(
            context,
            [...bankEntrySuggestion, ...selectedSuggestions],
            status,
            reason
          );
        }
        await helpers.currentModalSubmitCallback();
      } else {
        helpers.showNotificationMessage({
          key: 'bankEntry',
          message: 'Did not receive bank entry ID',
          type: 'error',
        });
      }
    },
  });
}

export async function createFundTransferInModal(context: Context) {
  const { data, helpers } = context;
  const { transactions } = data ?? {};
  const sendingTransaction = transactions?.find((t) =>
    new Decimal(t.amount?.amount).lessThan(new Decimal(0))
  );
  const receivingTransaction = transactions?.find((t) =>
    new Decimal(t.amount?.amount).greaterThan(new Decimal(0))
  );

  const fromAccountId =
    sendingTransaction['BankTransactionNew-BankAccount']?.accountId;
  const toAccountId =
    receivingTransaction['BankTransactionNew-BankAccount']?.accountId;
  const transferDate = sendingTransaction.externalCreationDate;

  const fundTransfer = getUIModelFromSharedState(
    context,
    'fundTransfer'
  ) as unknown as {
    sendingAmount: Decimal;
    receivingAmount: Decimal;
    sendingFee: Decimal;
    receivingFee: Decimal;
    isIntercompany: boolean;
    transferReason: EvstTransferReason;
    intercompanyProcessId: number;
    intercompanyTransactionalCurrency: string;
  };
  helpers.openModal({
    title: `New Fund Transfer for Bank Transaction #`,
    size: 'large',
    template: `/templates/everest.fin.integration.bank/uinext/fundTransfer/createFundTransfer`,
    initialState: {
      fromAccountId,
      toAccountId,
      entityId: sendingTransaction.entityId,
      transferDate,
      transferAmount: new Decimal(
        sendingTransaction.amount?.amount
      ).absoluteValue(),
      sendingAmount: {
        amount: new Decimal(fundTransfer?.sendingAmount).absoluteValue(),
        currency: sendingTransaction?.currency,
      },
      receivingAmount: {
        amount: new Decimal(fundTransfer?.receivingAmount).absoluteValue(),
        currency: receivingTransaction?.currency,
      },
      sendingFee: fundTransfer?.sendingFee,
      receivingFee: fundTransfer?.receivingFee,
      isIntercompany: fundTransfer?.isIntercompany,
      ...(fundTransfer?.isIntercompany && {
        intercompanyProcessId: fundTransfer?.intercompanyProcessId,
        intercompanyTransactionalCurrency:
          fundTransfer?.intercompanyTransactionalCurrency,
        transferReason: fundTransfer?.transferReason,
      }),
      isModal: true,
    },
    onModalSubmit: async (fundTransferHeaderId: number) => {
      await afterFundTransferCreation(context, fundTransferHeaderId);
    },
  });
}

export async function createFundTransferDirectly(context: Context) {
  const { form, data } = context;
  const { transactions } = data ?? {};

  if (!transactions?.length) {
    showMessage(context, 'error', '{{banking.transactions.notFound}}', 5);
    return;
  }
  await form.handleSubmit({});
  const fundTransfer = getUIModelFromSharedState(
    context,
    'fundTransfer'
  ) as unknown as {
    sendingAmount: Decimal;
    receivingAmount: Decimal;
    sendingFee: Decimal;
    receivingFee: Decimal;
    isIntercompany: boolean;
    transferReason: EvstTransferReason;
    intercompanyProcessId: number;
    intercompanyTransactionalCurrency: string;
  };

  const bankDetails = getSendingAndReceivingBankDetails(context);
  if (!bankDetails) {
    return;
  }
  const sendingTransaction = transactions?.find((t) =>
    new Decimal(t.amount?.amount).lessThan(new Decimal(0))
  );
  const receivingTransaction = transactions?.find((t) =>
    new Decimal(t.amount?.amount).greaterThan(new Decimal(0))
  );

  const fromAccountId =
    sendingTransaction['BankTransactionNew-BankAccount']?.accountId;
  const toAccountId =
    receivingTransaction['BankTransactionNew-BankAccount']?.accountId;
  const transferDate = sendingTransaction.externalCreationDate;
  const sendingCurrency = sendingTransaction.amount?.currency;
  const receivingCurrency = receivingTransaction?.currency;

  const {
    sendingAmount,
    receivingAmount,
    sendingFee = 0,
    receivingFee = 0,
    isIntercompany,
    intercompanyProcessId,
    intercompanyTransactionalCurrency,
    transferReason,
  } = fundTransfer;
  const transferAmount = new Decimal(
    sendingTransaction.amount?.amount
  ).absoluteValue();
  const exchangeRate = new Decimal(receivingAmount)
    .dividedBy(new Decimal(sendingAmount))
    .toFixed(12);

  const fundTransferHeader = {
    transferAmount: {
      amount: transferAmount,
      currency: sendingCurrency,
    },
    fromAccountId,
    toAccountId,
    entityId: sendingTransaction.entityId,
    toEntityId: receivingTransaction.entityId,
    transferDate,
    exchangeRate: new Decimal(exchangeRate),
    isIntercompany,
    ...(isIntercompany && {
      intercompanyProcessId,
      intercompanyTransactionalCurrency,
      transferReason,
    }),
  };

  const fundTransferLines = [
    {
      accountId: fromAccountId,
      accountType: EvstFundTransferLineAccountType.From,
      amount: {
        amount: new Decimal(sendingAmount).plus(sendingFee).times(-1),
        currency: sendingCurrency,
      },
      bankFee: new Decimal(sendingFee).times(-1),
      currency: sendingCurrency,
    },
    {
      accountId: toAccountId,
      accountType: EvstFundTransferLineAccountType.To,
      amount: {
        amount: new Decimal(receivingAmount).minus(receivingFee),
        currency: receivingCurrency,
      },
      bankFee: new Decimal(receivingFee).times(-1),
      currency: receivingCurrency,
    },
  ];

  showMessage(context, 'loading', '{{fundTransfer.load.create}}', 100);

  try {
    const response = await FundTransferHeaderUI.upsertFundTransfer(context, {
      fundTransferHeader,
      fundTransferLines,
    }).run('fundTransfer');

    if (response?.fundTransfer?.id) {
      // update FT status to post JE
      await FundTransferHeaderUI.updateFundTransferPaymentStatus(context, {
        fundTransferHeaderId: response.fundTransfer.id,
        paymentStatus: EvstFundTransferPaymentStatus.Recorded,
      }).run('fundTransfer');

      showMessage(context, 'success', '{{fundTransfer.saved.message}}', 5);
      await afterFundTransferCreation(context, response.fundTransfer.id);
    } else {
      showMessage(context, 'error', '{{fundTransfer.creationFailed}}', 5);
    }
  } catch (error) {
    showMessage(
      context,
      'error',
      `Creation Failed: ${error.message || 'Unknown error'}`,
      5
    );
  }
}

export async function afterFundTransferCreation(
  context: Context,
  fundTransferHeaderId: number
) {
  const { actions, data, helpers } = context;
  if (fundTransferHeaderId) {
    const { transactions } = data ?? {};
    const bankTransactionIds = transactions.map((t) => t.id);
    const selectedSuggestions = getSelectedSuggestions(context);

    const bankTransferResponse = await actions.run({
      bankTransactionMatchesTransient: {
        action: 'createFundTransferSuggestion',
        data: {
          bankTransactionIds,
          fundTransferHeaderId,
        },
      },
    });

    if (!bankTransferResponse?.error) {
      const [fundTransferSuggestion] =
        bankTransferResponse.bankTransactionMatchesTransient;
      let status: string;
      let reason: string;

      await matchWithEntry(
        context,
        [...fundTransferSuggestion, ...selectedSuggestions],
        status,
        reason
      );
    }
    await helpers.currentModalSubmitCallback();
  } else {
    await helpers.showNotificationMessage({
      key: 'fundTransfer',
      message: 'Did not receive fund transfer ID',
      type: 'error',
      duration: 3,
    });
  }
}

export function showMessage(
  { helpers }: Context,
  type: 'success' | 'error' | 'info' | 'warning' | 'loading',
  message: string | undefined,
  duration: number,
  key?: string
) {
  helpers.showNotificationMessage({
    key: key ?? 'upsertFT',
    type,
    message,
    duration,
  });
}

export function getCreditCardEntryName(context: Context) {
  const { data } = context;
  return (
    data?.creditCardEntryHeader[0]?.creditCardEntryName ?? CREDIT_CARD_ENTRY
  );
}

export async function createCreditCardEntryDirectly(context: Context) {
  const { actions, data, helpers, state, sharedState } = context;
  state.saving = true;
  const bankTransactionIds = data.transactions.map((t) => t.id);
  const selectedSuggestions = getSelectedSuggestions(context);
  const transactionDate = await getTransactionDate(context);

  if (validateBankOrCreditCardEntry(context) > 0) {
    state.saving = false;
    return;
  }

  let creditCardEntryName = getCreditCardEntryName(context);

  if (creditCardEntryName.length <= 0) {
    creditCardEntryName = CREDIT_CARD_ENTRY;
  }

  const [{ entityId, currency, coaAccountId }] = data.transactions;

  const amounts = data.transactions.map((t) => t.amount);

  const lineData = sharedState.getModelData('creditCardEntryLines')?.instance
    ?.data;

  const creditCardEntryLines = [];
  for (const amount of amounts) {
    creditCardEntryLines.push({
      ...lineData,
      amount: amount,
    });
  }

  const creditCardEntryHeader = {
    postingPeriodId: state.postingPeriod?.id,
    postingPeriod: state.postingPeriod?.periodName,
    creditCardEntryName: creditCardEntryName,
    transactionDate: transactionDate,
    entityId,
    currency,
    accountId: coaAccountId,
    status: EvstCreditCardStatus.Matched,
  };

  helpers.showNotificationMessage({
    key: 'creditCardEntry',
    type: 'loading',
    message: '{{creditCard.create}}',
    duration: 20,
  });
  const creditCardEntryResponse =
    await CreditCardEntryHeaderUI.upsertCreditCardEntry(context, {
      creditCardEntryHeader,
      creditCardEntryLines,
      options: { fieldList: ['id'] },
    }).run('creditCardEntryHeader');

  if (creditCardEntryResponse?.error) {
    helpers.showNotificationMessage({
      key: 'creditCardEntry',
      type: 'error',
      message: '{{creditCard.transaction.creditCardEntryFailed}}',
      duration: 5,
    });
  } else {
    await CreditCardEntryHeaderUI.updateCCEjournalEntryName(context, {
      creditCardEntryHeaderId:
        creditCardEntryResponse.creditCardEntryHeader[0].id,
    }).run('creditCardEntryHeader');
    helpers.closeModal();
    helpers.showNotificationMessage({
      key: 'creditCardEntry',
      type: 'success',
      message: '{{creditCard.transaction.creditCardEntryCreated}}',
      duration: 5,
    });
  }

  const creditCardEntryHeaderId =
    creditCardEntryResponse.creditCardEntryHeader[0].id;
  if (creditCardEntryHeaderId) {
    const creditCardEntryResponse = await actions.run({
      bankTransactionMatchesTransient: {
        action: 'createCreditCardEntrySuggestion',
        data: {
          where: {
            id: { $in: bankTransactionIds },
          } as unknown as CreditCardEntryHeader,
          creditCardEntryHeaderId,
        },
      },
    });

    if (!creditCardEntryResponse?.error) {
      const [creditCardEntrySuggestions] =
        creditCardEntryResponse.bankTransactionMatchesTransient;
      let status: string;
      let reason: string;
      const suggestionsWithoutJELineId = checkSuggestionsHavePostedSFD(context);
      if (suggestionsWithoutJELineId.length > 0) {
        const response = await createPayment(context, [
          ...creditCardEntrySuggestions,
          ...selectedSuggestions,
        ]);
        if (response?.error) {
          return helpers.showNotificationMessage({
            key: 'paymentCreation',
            type: 'error',
            message: 'Payment creation failed. Please create payment manually.',
            duration: 2,
          });
        } else {
          helpers.showNotificationMessage({
            key: 'paymentCreation',
            type: 'success',
            message: 'Payment Created',
            duration: 5,
          });
        }
      }
      await matchWithEntry(
        context,
        [...creditCardEntrySuggestions, ...selectedSuggestions],
        status,
        reason
      );
    }
    await helpers.currentModalSubmitCallback();
  } else {
    helpers.showNotificationMessage({
      key: 'creditCardEntry',
      message: 'Did not receive credit card entry ID',
      type: 'error',
    });
  }
  state.saving = false;
}

export async function createCreditCardEntryInModal(context: Context) {
  const { actions, data, helpers, state } = context;
  const { accountId, departmentId } = state;
  const bankTransactionIds = data.transactions.map((t) => t.id);
  const selectedSuggestions = getSelectedSuggestions(context);
  const matchIds = selectedSuggestions.map(({ id }) => id);

  await helpers.openModal({
    title: `New Credit Card Entry for Card Transaction #`,
    size: 'large',
    initialState: {
      mode: 'create',
      bankTransactionIds,
      matchIds,
      accountId,
      departmentId,
    },
    template: `/templates/everest.fin.integration.bank/uinext/creditCardEntry/creditCardEntry`,
    onModalSubmit: async (creditCardEntryHeaderId: number) => {
      if (creditCardEntryHeaderId) {
        const creditCardEntryResponse = await actions.run({
          bankTransactionMatchesTransient: {
            action: 'createCreditCardEntrySuggestion',
            data: {
              where: {
                id: { $in: bankTransactionIds },
              } as unknown as CreditCardEntryHeader,
              creditCardEntryHeaderId,
            },
          },
        });

        if (!creditCardEntryResponse?.error) {
          const [creditCardEntrySuggestions] =
            creditCardEntryResponse.bankTransactionMatchesTransient;
          let status: string;
          let reason: string;
          const suggestionsWithoutJELineId =
            checkSuggestionsHavePostedSFD(context);
          if (suggestionsWithoutJELineId.length > 0) {
            const response = await createPayment(context, [
              ...creditCardEntrySuggestions,
              ...selectedSuggestions,
            ]);
            if (response?.error) {
              return helpers.showNotificationMessage({
                key: 'paymentCreation',
                type: 'error',
                message:
                  'Payment creation failed. Please create payment manually.',
                duration: 2,
              });
            } else {
              helpers.showNotificationMessage({
                key: 'paymentCreation',
                type: 'success',
                message: 'Payment Created',
                duration: 5,
              });
            }
          }
          await matchWithEntry(
            context,
            [...creditCardEntrySuggestions, ...selectedSuggestions],
            status,
            reason
          );
        }
        await helpers.currentModalSubmitCallback();
      } else {
        helpers.showNotificationMessage({
          key: 'creditCardEntry',
          message: 'Did not receive credit card entry ID',
          type: 'error',
        });
      }
    },
  });
}

export async function createNetZeroImpact(
  context: Context,
  postJournal = true
) {
  const { helpers } = context;
  if (postJournal) {
    const template = helpers.getTemplateLink({
      templateName:
        'everest.fin.integration.bank/uinext/netZeroImpact/reasonModal',
      templateState: { id: 0 },
    });

    helpers.openModal({
      size: 'xsmall',
      title: 'Create Net Zero Impact',
      template,
      onModalSubmit: async (payload) => {
        await sendNetZeroRequest(context, payload.reason, postJournal);
      },
    });
  } else {
    await sendNetZeroRequest(context, '', postJournal);
  }

  await helpers.currentModalSubmitCallback();
}

export async function sendNetZeroRequest(
  context: Context,
  reason: string,
  postJournal: boolean
) {
  const { actions, data, helpers } = context;
  helpers.showNotificationMessage({
    key: 'nzi',
    type: 'loading',
    message: 'Creating...',
    duration: 10,
  });
  const netZero = await actions.run({
    netZeroImpact: {
      action: 'createNetZeroImpact',
      data: {
        bankTrasactions: data.transactions,
        netZeroImpact: {
          transactionDate: toPlainDate(new Date()), // first version
          reason,
          postJournal,
        },
        fieldList: [
          'id',
          'accountId',
          'transactionDate',
          'netZeroImpactNumber',
          'entityId',
          'currency',
          'amount',
        ],
      },
    },
  });

  if (netZero?.error) {
    helpers.showNotificationMessage({
      key: 'nzi',
      type: 'error',
      message: 'Error',
      duration: 2,
    });
  } else {
    helpers.showNotificationMessage({
      key: 'nzi',
      type: 'success',
      message: 'Created Net Zero Impact',
      duration: 2,
    });
    const netZeroImpact = netZero.netZeroImpact;
    const response = await actions.run({
      bankTransactionMatchesTransient: {
        action: 'createNetZeroImpactSuggestion',
        data: {
          where: {
            id: { $in: data.transactions.map((t) => t.id) },
          } as unknown as BankTransactionNew,
          netZeroImpactId: netZeroImpact.id,
        },
      },
    });
    const [netZeroSuggestions] = response.bankTransactionMatchesTransient;
    await matchWithEntry(context, netZeroSuggestions);
  }
}

export function getCreditCardActionGroupData(context: Context) {
  const allMatchSuggestions = getActionGroupOptions(context);

  return insertCreditCardEntryAsOption(context, allMatchSuggestions);
}

export function insertCreditCardEntryAsOption(
  context: Context,
  options: MatchSuggestion[]
): MatchSuggestion[] {
  if (isNotMatched(context) && context.data?.matchDocuments) {
    const { state } = context;
    const itemsNotSelected = options.length === 0;
    const netZero = isTotalBankTransactionAmountZero(context);
    const areAllSameAccount = transactionsBelongToSameBankAccount(context);
    const option =
      netZero && areAllSameAccount
        ? {
            label: 'New Net Zero Impact',
            value: EntryTypes.NetZeroImpact,
            description: '',
            selected: false,
            disabled: false,
          }
        : {
            label: 'New credit card entry',
            value: EntryTypes.CreditCardEntry,
            description: '',
            selected: state.selectedItems?.some(
              (item) => item.value === EntryTypes.CreditCardEntry
            ),
            disabled: false,
          };
    if (
      option.value === EntryTypes.CreditCardEntry &&
      itemsNotSelected &&
      state.selectedItems.length === 0
    ) {
      state.selectedItems = [option];
    }
    options.push(option);
    if (option.value === EntryTypes.NetZeroImpact) {
      options.push({
        ...option,
        label: 'Match without Entry',
        value: EntryTypes.MatchWithoutEntry,
      });
    }
  }
  return options;
}

export function postingPeriodFTQuery(context: Context) {
  const { isFundTransfer, sendingTransaction } =
    getSendingAndReceivingBankDetails(context) ?? {};
  if (
    isFundTransferDetailBlockVisible(context) &&
    isFundTransfer &&
    sendingTransaction?.entityId &&
    sendingTransaction?.externalCreationDate
  ) {
    return {
      entityId: sendingTransaction?.entityId,
      postingDate: sendingTransaction?.externalCreationDate,
      forPosting: false,
      lockEvaluation: {
        journalEntryTypes: [EvstJournalEntryType.FundTransfer],
      },
    };
  }
}

export function isIntercompanyFT(context: Context) {
  const { sendingTransaction, receivingTransaction } =
    getSendingAndReceivingBankDetails(context);

  return (
    sendingTransaction?.entityId !== receivingTransaction?.entityId &&
    (sendingTransaction?.entityId !== undefined ||
      !!receivingTransaction?.entityId !== undefined)
  );
}

export function isRequiredForIntercompany(context: Context) {
  return {
    validate: (value) => {
      const isInterCompany = getUIModelFromSharedState(
        context,
        'fundTransfer',
        'isIntercompany'
      );
      return isInterCompany ? !!value || '{{fundTransfer.required}} *' : null;
    },
  };
}

export function isSecondCreateInModalFTVisible(context: Context) {
  return !getUIModelFromSharedState(context, 'fundTransfer', 'isIntercompany');
}

export function getIntercompanyTransactionalFTCurrency(context: Context) {
  if (!getUIModelFromSharedState(context, 'fundTransfer', 'isIntercompany')) {
    return [];
  }

  const { sendingTransaction, receivingTransaction } =
    getSendingAndReceivingBankDetails(context);
  return [
    {
      id: sendingTransaction?.accountId,
      currency: sendingTransaction?.currency,
    },
    {
      id: receivingTransaction?.accountId,
      currency: receivingTransaction?.currency,
    },
  ];
}
