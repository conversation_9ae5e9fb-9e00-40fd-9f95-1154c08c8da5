{"version": 2, "uicontroller": "voidReasonModal.uicontroller.ts", "uimodel": {"nodes": {}}, "uiview": {"templateType": "generic", "autoRefreshData": true, "i18n": "everest.fin.accounting/accounting", "actions": [{"label": "{{journalEntry.cancelButton}}", "variant": "secondary", "align": "right", "onClick": "@controller:closeVoidReasonModal"}, {"label": "{{journalEntry.void}}", "variant": "primary", "align": "right", "onClick": "@controller:returnVoidReason"}], "sections": [{"component": "<PERSON><PERSON>", "section": {"grid": {"size": "12"}}, "props": {"variant": "warning", "content": "{{ voidReason.warningMessage }}"}}, {"component": "FieldGroup", "customId": "ftVoidingDescriptionBlock", "type": "secondary", "size": "12", "isEditing": true, "elements": [{"component": "Input", "multiline": true, "label": "{{journalEntry.voidReason}}", "name": "editReason", "onChange": "@controller:updateReasonInput"}]}]}}