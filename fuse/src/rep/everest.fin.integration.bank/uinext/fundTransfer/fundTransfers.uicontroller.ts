// @i18n:everest.fin.accounting/fundTransfer
import { EvstFundTransferPaymentStatus } from '@pkg/everest.fin.accounting/types/enums/FundTransferPaymentStatus';
import { FundTransferHeaderUI } from '@pkg/everest.fin.accounting/types/FundTransferHeader.ui';
import { EvstBankTransactionMatchStatus } from '@pkg/everest.fin.base/types/enums/BankTransactionMatchStatus';
import { EvstSFDType } from '@pkg/everest.fin.base/types/enums/SFDType';
import { BankPaymentUI } from '@pkg/everest.fin.integration.bank/types/BankPayment.ui';
import type { FundTransfersUiTemplate } from '@pkg/everest.fin.integration.bank/types/uiTemplates/uinext/fundTransfer/fundTransfers.ui';

type FundTransfersContext = FundTransfersUiTemplate.FundTransfersContext;

export type Context = FundTransfersContext & {
  state: {
    selectedRows: { data: Partial<FundTransferHeaderUI.FundTransferHeader> }[];
  };
};

export function navigateToCreateFT({ helpers }: Context) {
  helpers.navigate({
    to: `/templates/everest.fin.integration.bank/uinext/fundTransfer/createFundTransfer`,
  });
}

export function getBankTransactionQuery(_context: Context) {
  const associationName =
    'BankTransaction-BankTransactionMatch.MatchSourceFinancialDocument-BankTransactionMatch';

  return {
    where: {
      [`${associationName}.sfdType`]: EvstSFDType.FundTransfer,
      matchStatus: EvstBankTransactionMatchStatus.Matched,
    },
  };
}

const getBankLogo = (logoUrl) => {
  if (logoUrl.startsWith('http://') || logoUrl.startsWith('https://')) {
    return logoUrl;
  }

  // If not a URL, assume it's a base64-encoded image
  return `data:image/png;base64,${logoUrl}`;
};

export function getFromAvatar(context: Context, { rowData }) {
  const { data } = context;
  const { fromAccountId } = rowData ?? {};
  const { bankAccounts = [] } = data ?? {};

  if (!rowData) {
    return '';
  }

  const bankAccount = bankAccounts.find((ba) => ba.accountId === fromAccountId);
  const fi = bankAccount?.['BankAccount-FinancialInstitution'];

  if (fi?.logoUrl) {
    return getBankLogo(fi.logoUrl);
  }
  if (fi?.logoId) {
    const logoId = fi?.logoId;
    return `/api/app/storage/${logoId}`;
  }

  return '';
}

export function getToAvatar(context: Context, { rowData }) {
  const { data } = context;
  const { toAccountId } = rowData ?? {};
  const { bankAccounts = [] } = data ?? {};

  if (!rowData) {
    return '';
  }

  const bankAccount = bankAccounts.find((ba) => ba.accountId === toAccountId);
  const fi = bankAccount?.['BankAccount-FinancialInstitution'];

  if (fi?.logoUrl) {
    return getBankLogo(fi.logoUrl);
  }
  if (fi?.logoId) {
    const logoId = fi?.logoId;
    return `/api/app/storage/${logoId}`;
  }

  return '';
}

export function jeValueGetter(context: Context, { data }) {
  const je = data?.['JournalEntryHeader-FinancialDocument']?.[0];
  return je?.journalEntryNumber;
}

export function openJournalEntry({ helpers }: Context, { data }) {
  const id = data?.journalEntryHeaderId;
  if (id) {
    helpers.navigate({
      to: `@uicode:journalEntry?id=${id}`,
    });
  }
}

export async function _deleteSelectedValues({ actions, state }: Context) {
  return async () => {
    const fundTransfers = state.selectedRows.map((ft) => ft.data);
    // TODO: Remove transform and use actions.run
    await actions.submit({
      transform: () => ({
        fundTransfers: {
          voidFundTransfer: {
            fundTransfers,
          },
        },
      }),
    });
  };
}

export function getIconType(_, { rowData }) {
  return rowData.isIntercompany ? 'shamrock' : 'sharpay';
}

export function getCellValueForIcon(_, { expanded }) {
  // Return undefined to hide the cell value
  if (!expanded) {
    return undefined;
  }
}

export function getIntercompanyValue(_, row) {
  return row?.data?.isIntercompany ? 'Yes' : 'No';
}
export function isVoidButtonDisabled({ state }: Context) {
  return !state?.selectedRows?.length;
}

export function confirmVoidSelectedFundTransfers(context: Context) {
  const { helpers } = context;

  const path = helpers.getTemplateLink({
    templateName: 'everest.fin.integration.bank/uinext/voidReasonModal',
    templateState: {},
  });

  helpers.openModal({
    size: 'xsmall',
    title: '{{fundTransfer.confirm.voids}}',
    template: path,
    onModalSubmit: (payload) =>
      submitVoidSelectedFundTransfers(context, payload.reason),
  });
}

export async function refreshPaymentStatuses(context: Context) {
  const { data, helpers, actions, sections } = context;
  const { fundTransfers = [] } = data;

  // Get IDs of fund transfers with statuses that need updating
  const fundTransferIds = fundTransfers
    .filter((ft) =>
      [
        EvstFundTransferPaymentStatus.InProgress,
        EvstFundTransferPaymentStatus.Failed,
        EvstFundTransferPaymentStatus.Unknown,
        EvstFundTransferPaymentStatus.NoStatus,
      ].includes(ft.fundTransferPaymentStatus)
    )
    .map((ft) => ft.id);

  if (fundTransferIds.length === 0) {
    helpers.showToast({
      type: 'info',
      message: '{{fundTransfer.noStatusesToRefresh}}',
    });
    return;
  }

  helpers.showNotificationMessage({
    type: 'loading',
    key: 'refreshStatuses',
    message: '{{fundTransfer.refreshingStatuses}}',
    duration: 200,
  });

  let successCount = 0;

  // Fetch updated status for each fund transfer
  for (const id of fundTransferIds) {
    try {
      await BankPaymentUI.fetchFundTransferPaymentStatus(context, {
        fundTransferHeaderId: id,
      }).run('bankPayment');
      successCount++;
    } catch (error) {
      console.error('Error fetching status for FT', id, error);
    }
  }

  // Show result notification
  if (successCount === fundTransferIds.length) {
    helpers.showNotificationMessage({
      type: 'success',
      key: 'refreshStatuses',
      message: '{{fundTransfer.statusesRefreshed}}',
      duration: 5,
    });
  } else {
    helpers.showNotificationMessage({
      type: 'warning',
      key: 'refreshStatuses',
      message: `{{fundTransfer.someStatusesFailedToRefresh}}`,
      duration: 5,
    });
  }

  // Refresh table data
  await actions.refetchUiModelData();
}

export async function submitVoidSelectedFundTransfers(
  context: Context,
  reason: string
) {
  const { state, helpers, actions, sections } = context;

  const fundTransfers = state.selectedRows.filter(
    (entry) => entry.data.isVoided === true
  );

  if (fundTransfers.length > 0) {
    helpers.showToast({
      type: 'error',
      title: '{{fundTransfer.invalid.error}}',
      message: '{{fundTransfer.errorMessageOnVoidingVoidEntries}}',
    });
    return;
  }
  helpers.showNotificationMessage({
    type: 'loading',
    key: 'voidFundTransfers',
    message: '{{fundTransfer.load.void}}',
    duration: 200,
  });
  const entries = state.selectedRows ?? [];
  let successRequestCount = 0;
  for (const entry of entries) {
    const response = (await FundTransferHeaderUI.voidFundTransfer(context, {
      fundTransfers: [entry.data],
      voidReason: reason,
    }).run('fundTransfers')) as unknown as {
      fundTransfers: [
        {
          id: number;
          isVoided: boolean;
        },
      ];
    };
    successRequestCount =
      response?.fundTransfers?.[0]?.isVoided === true
        ? successRequestCount + 1
        : successRequestCount;
  }
  if (successRequestCount === entries.length) {
    helpers.showNotificationMessage({
      key: 'voidFundTransfers',
      type: 'success',
      message: '{{fundTransfer.success.void}}',
      duration: 5,
    });
  } else {
    const lengthDiff = entries.length - successRequestCount;

    helpers.showNotificationMessage({
      key: 'voidFundTransfers',
      type: 'warning',
      message:
        lengthDiff === entries.length
          ? `{{fundTransfer.singleFailure.void}}`
          : `${lengthDiff} of the Fund Transfers failed to void due to the period close, but the rest were voided successfully.`,
      duration: 3,
    });
  }
  await actions.refetchUiModelData();
  const table = sections.fundTransfersTable;
  await table.refreshData({ purge: true });
  table.unselectAllRows();
  helpers.currentModalSubmitCallback();
  helpers.closeModal();
}
