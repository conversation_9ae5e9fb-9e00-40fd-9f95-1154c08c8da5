// @i18n:everest.fin.accounting/fundTransfer
import { UILifecycleHooks } from '@everestsystems/content-core';
import { Decimal } from '@everestsystems/decimal';
import {
  getUIModelFromSharedState,
  setUIModelInSharedState,
} from '@pkg/everest.base/public/utils/ui/sharedStateUtils.ui';
import type { EvstCurrencyAmount } from '@pkg/everest.base/types/composites/CurrencyAmount';
import { EvstFundTransferPaymentStatus } from '@pkg/everest.fin.accounting/types/enums/FundTransferPaymentStatus';
import type { EvstTransferReason } from '@pkg/everest.fin.accounting/types/enums/TransferReason';
import { FundTransferHeaderUI } from '@pkg/everest.fin.accounting/types/FundTransferHeader.ui';
import type { CreateFundTransferUiTemplate } from '@pkg/everest.fin.integration.bank/types/uiTemplates/uinext/fundTransfer/createFundTransfer.ui';

import getAccountData, {
  setRequiredFieldsOnInit,
  showMessage,
} from './utilities.uicontroller';

type Any = any;

type CreateFundTransferContext =
  CreateFundTransferUiTemplate.CreateFundTransferContext;

export type Context = CreateFundTransferContext & {
  state: {
    selectedFromAccountId: number;
    selectedToAccountId: number;
    fromAccountCurrency: string;
    toAccountCurrency: string;
    postingPeriodId: number;
    postingPeriodName?: string;
    entityId: number;
    fromAccountId: number;
    toAccountId: number;
    transferAmount: Decimal;
    exchangeRate: Decimal;
    transferDate: Date;
    sendingFee?: Decimal;
    receivingFee?: Decimal;
    toEntityId: number;
    receivingAmount?: EvstCurrencyAmount;
    sendingAmount?: EvstCurrencyAmount;
    isIntercompany?: boolean;
    transferReason?: EvstTransferReason;
    intercompanyProcessId?: number;
    intercompanyTransactionalCurrency?: string;
    /**
     * The untracked state can be used to store caches without triggering
     * more template updates.
     **/
    _untracked: {
      exchangeRatesCache: Map<string, Any>;
    };
  };
};

UILifecycleHooks.onInit((context: Context) => {
  setRequiredFieldsOnInit(context); // Seting from and to account amount as required on init
});

export function getTitle(context: Context) {
  const { state } = context;
  const {
    fromAccountId,
    toAccountId,
    transferDate,
    transferAmount,
    exchangeRate,
    sendingFee,
    receivingFee,
    sendingAmount,
    receivingAmount,
    isIntercompany,
    transferReason,
    intercompanyProcessId,
    intercompanyTransactionalCurrency,
  } = state ?? {};
  const fundTransfer = getUIModelFromSharedState(context, 'fundTransfer');
  const header = {
    fromAccountId,
    toAccountId,
    transferDate,
    transferAmount,
    exchangeRate,
  };

  if (
    fromAccountId &&
    toAccountId &&
    transferDate &&
    transferAmount &&
    fundTransfer &&
    sendingAmount &&
    receivingAmount &&
    !state.once
  ) {
    // Set all values initialy if tempalate is opened as modal from bank transactions create and match process

    for (const [key, val] of Object.entries(header)) {
      setUIModelInSharedState(context, 'fundTransfer', key as Any, val);
    }
    setUIModelInSharedState(context, 'fromAccount', 'amount', sendingAmount);
    setUIModelInSharedState(context, 'toAccount', 'amount', receivingAmount);
    setUIModelInSharedState(context, 'fromAccount', 'bankFee', sendingFee);
    setUIModelInSharedState(context, 'toAccount', 'bankFee', receivingFee);
    if (isIntercompany) {
      setUIModelInSharedState(context, 'fundTransfer', 'isIntercompany', true);
      setUIModelInSharedState(
        context,
        'fundTransfer',
        'transferReason',
        transferReason
      );
      setUIModelInSharedState(
        context,
        'fundTransfer',
        'intercompanyProcessId',
        intercompanyProcessId
      );
      setUIModelInSharedState(
        context,
        'fundTransfer',
        'intercompanyTransactionalCurrency',
        intercompanyTransactionalCurrency
      );
    }
    state.once = true;
  }
  return 'Fund Transfer';
}

export function isToAccountSelectDisabled(context: Context) {
  const { state } = context;
  return !state.selectedFromAccountId;
}
/**
 * Handles the common validation and data preparation logic for fund transfers
 */
async function prepareFundTransfer(context: Context) {
  const { form, state, sections } = context;

  const fundTransferHeader = getUIModelFromSharedState(context, 'fundTransfer');
  await form.handleSubmit({});

  const fundTransferLines = getAccountData(context);

  // Check if amounts are equal for same currency
  if (
    state?.isSameCurrency &&
    !fundTransferLines[0].amount?.amount
      .minus(fundTransferLines[0].bankFee || 0)
      .absoluteValue()
      .equals(
        fundTransferLines[1].amount?.amount.plus(
          fundTransferLines[1].bankFee.absoluteValue() || 0
        )
      )
  ) {
    showMessage(context, 'error', '{{fundTransfer.unequalAmounts}}', 5);
    return null;
  }

  const amount = fundTransferLines[0].amount?.amount || 0;
  const bankFee = fundTransferLines[0].bankFee || 0;

  fundTransferHeader.transferAmount = {
    amount: new Decimal(amount).minus(bankFee).absoluteValue(),
    currency: fundTransferLines[0].amount?.currency,
  };

  const fundTransferAttachments = sections.transferAttachments
    ?.getUploadFiles()
    ?.map((file) => ({
      fileId: file.fileId,
      fileName: file.fileName,
      ...(file.id && { id: file.id }),
    }));

  return {
    fundTransferHeader,
    fundTransferLines,
    fundTransferAttachments,
  };
}

export async function createFundTransfer(
  context: Context,
  mode: string = 'saveOnly'
) {
  const { helpers, state } = context;
  const { fromAccountId } = state ?? {};

  const preparedData = await prepareFundTransfer(context);
  if (!preparedData) {
    return;
  }

  const { fundTransferHeader, fundTransferLines, fundTransferAttachments } =
    preparedData;

  showMessage(context, 'loading', '{{fundTransfer.creatingTransfer}}', 100);
  const response = await FundTransferHeaderUI.upsertFundTransfer(context, {
    fundTransferHeader,
    fundTransferLines,
    fundTransferAttachments,
  }).run('fundTransfer');

  // since it's manual FT then set status recorded
  if (response?.fundTransfer?.id) {
    await FundTransferHeaderUI.updateFundTransferPaymentStatus(context, {
      fundTransferHeaderId: response?.fundTransfer?.id,
      paymentStatus: EvstFundTransferPaymentStatus.Recorded,
    }).run('fundTransfer');
  }

  if (response.error) {
    showMessage(context, 'error', '{{fundTransfer.creationFailed}}', 5);
    return;
  }

  showMessage(context, 'success', '{{fundTransfer.saved.message}}', 5);
  if (fromAccountId) {
    helpers.closeModal();
    helpers.currentModalSubmitCallback(response?.fundTransfer?.id);
  } else {
    if (mode === 'saveAndCreateNew') {
      helpers.closeCurrentTab();
      helpers.navigate({
        to: `/templates/everest.fin.integration.bank/uinext/fundTransfer/createFundTransfer`,
      });
    } else {
      helpers.navigate({
        to: `/templates/everest.fin.integration.bank/uinext/fundTransfer/fundTransfer?id=${response?.fundTransfer?.id}`,
        closeCurrentTab: true,
      });
    }
  }
}

export async function initiateFundTransfer(context: Context) {
  const { helpers } = context;

  const preparedData = await prepareFundTransfer(context);
  if (!preparedData) {
    return;
  }

  const { fundTransferHeader, fundTransferLines, fundTransferAttachments } =
    preparedData;

  const response = await FundTransferHeaderUI.upsertFundTransfer(context, {
    fundTransferHeader,
    fundTransferLines,
    fundTransferAttachments,
  }).run('fundTransfer');

  if (response.error) {
    showMessage(context, 'error', '{{fundTransfer.creationFailed}}', 5);
    return;
  }

  const entityId = getUIModelFromSharedState(
    context,
    'fundTransfer',
    'entityId'
  );
  helpers.navigate({
    to: `/templates/everest.fin.integration.bank/uinext/payment/fundTransferPayment`,
    closeCurrentTab: true,
    initialState: {
      mode: 'create',
      fundTransferHeaderIds: [response?.fundTransfer?.id],
      entityId,
    },
  });
}

export async function saveAndCreateNewFundTransfer(context: Context) {
  return await createFundTransfer(context, 'saveAndCreateNew');
}

export function showIntercompanyDetails(context: Context) {
  const entityId = getUIModelFromSharedState(
    context,
    'fundTransfer',
    'entityId'
  );
  const toEntityId = getUIModelFromSharedState(
    context,
    'fundTransfer',
    'toEntityId'
  );

  if (entityId === undefined || toEntityId === undefined) {
    return false;
  }

  // Show the FieldGroup only if entityId and toEntityId are different
  return entityId !== toEntityId;
}

export function isRequiredForIntercompany(context: Context) {
  return {
    validate: (value) => {
      const isInterCompany = getUIModelFromSharedState(
        context,
        'fundTransfer',
        'isIntercompany'
      );
      return isInterCompany ? !!value || '{{fundTransfer.required}} *' : null;
    },
  };
}

export function isInitiateButtonVisible(context: Context) {
  const configurationFundTransferBankPaymentEnabled = getUIModelFromSharedState(
    context,
    'configurationFundTransferBankPaymentEnabled'
  );

  return !context.state?.isModal && configurationFundTransferBankPaymentEnabled;
}

export function saveAndCreateVisible(context: Context) {
  return !context.state?.isModal;
}
