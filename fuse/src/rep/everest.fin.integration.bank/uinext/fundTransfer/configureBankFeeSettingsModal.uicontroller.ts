// @i18n:everest.fin.accounting/fundTransfer
import { getUIModelFromSharedState } from '@pkg/everest.base/public/utils/ui/sharedStateUtils.ui';
import { BankFeeSettingsUI } from '@pkg/everest.fin.accounting/types/BankFeeSettings.ui';
import { EvstAccountType } from '@pkg/everest.fin.accounting/types/enums/AccountType';
import { FundTransferHeaderUI } from '@pkg/everest.fin.accounting/types/FundTransferHeader.ui';
import type { ConfigureBankFeeSettingsModalUiTemplate } from '@pkg/everest.fin.integration.bank/types/uiTemplates/uinext/fundTransfer/configureBankFeeSettingsModal.ui';

type ConfigureBankFeeSettingsModalContext =
  ConfigureBankFeeSettingsModalUiTemplate.ConfigureBankFeeSettingsModalContext;

type Context = ConfigureBankFeeSettingsModalContext & {
  state: { coaId: number; settingType: string };
};

export function getCurrentEntity(context: Context) {
  return context.state.entityId;
}

export function getAccountsQuery(context: Context) {
  const { state } = context;
  if (state.settingType === 'accountSetting' && state.coaId && state.entityId) {
    const args = {
      where: {
        groupingAccount: false,
        chartOfAccountsId: state.coaId,
        accountType: {
          $in: [
            EvstAccountType.Expense,
            EvstAccountType.OtherExpense,
            EvstAccountType.OtherIncome,
          ],
        },
      },
      orderBy: ['accountNumber'],
    };

    return {
      args,
      fieldList: ['id', 'accountName', 'accountNumber'],
    };
  }
}

export function getDepartmentsQuery(context: Context) {
  const { state } = context;
  if (state.settingType === 'departmentSetting') {
    return {
      where: {},
      orderBy: ['departmentCode'],
    };
  }
}

export function getBankFeeConfigurationQuery(context: Context) {
  const { state } = context;
  if (state?.coaId) {
    return {
      where: {
        coaId: state.coaId,
      },
    };
  }
}

export function isAccountSelectVisible(context: Context) {
  return context.state.settingType === 'accountSetting';
}

export function isDepartmentSelectVisible(context: Context) {
  return context.state.settingType === 'departmentSetting';
}

export function getAccountsList(context: Context) {
  const { data } = context;
  const { bankAccounts = [] } = data ?? {};
  return bankAccounts.map((ba) => {
    return {
      ...ba,
      accountNumberAndName: `${ba.accountNumber} — ${ba.accountName}`,
    };
  });
}

export function getDepartmentsList(context: Context) {
  const { data } = context;
  const { departments = [] } = data ?? {};
  return departments.map((dpt) => {
    return {
      ...dpt,
      deptNumberAndName: `${dpt.departmentCode} ${dpt.departmentName}`,
    };
  });
}

export function isSaveActionDisabled(context: Context) {
  const { isFetchingUiModelData, state } = context;
  const { bankFeeAccountId, bankFeeDepartmentId } =
    getUIModelFromSharedState(context, 'bankFeeConfiguration') ?? {};
  return (
    isFetchingUiModelData ||
    (state.settingType === 'accountSetting' && !bankFeeAccountId) ||
    (state.settingType === 'departmentSetting' && !bankFeeDepartmentId)
  );
}

export function cancelAction(context: Context) {
  const { helpers } = context;
  helpers.closeModal();
}

export async function saveAction(context: Context) {
  const { state, helpers } = context;
  const { id, bankFeeAccountId, bankFeeDepartmentId } =
    getUIModelFromSharedState(context, 'bankFeeConfiguration') ?? {};

  showMessage(context, 'loading', '{{fundTransfer.saving}}', 200);

  const response = await (id
    ? BankFeeSettingsUI.update(
        context,
        { id },
        {
          ...(state.settingType === 'accountSetting'
            ? { bankFeeAccountId }
            : { bankFeeDepartmentId }),
        }
      ).run('bankFeeConfiguration')
    : BankFeeSettingsUI.create(context, {
        ...(state.settingType === 'accountSetting'
          ? { bankFeeAccountId }
          : { bankFeeDepartmentId }),
        coaId: state.coaId,
      }).run('bankFeeConfiguration'));

  if (!response.error) {
    /**
     * Update all linked fund transfer journal entries to reflect
     * new bank fee account and department changes.
     * TODO: Add support to update intercompany JEs as well.
     * https://github.com/everestsystems/content/issues/18204
     */
    const jeUpdate = await FundTransferHeaderUI.upsertFundTransferJournalEntry(
      context,
      {
        fundTransferHeaderId: 5, // random id as this won't not used for updateMany mode
        mode: 'updateMany',
        coaId: state.coaId,
      }
    ).run('fundTransfer');

    if (jeUpdate.error) {
      showMessage(context, 'error', '{{fundTransfer.failed}}', 200);
    } else {
      helpers.currentModalSubmitCallback();
      helpers.closeModal();
    }
  }
}
export function showMessage(
  { helpers }: Context,
  type: 'success' | 'error' | 'info' | 'warning' | 'loading',
  message: string | undefined,
  duration: number,
  key?: string
) {
  helpers.showNotificationMessage({
    key: key ?? 'upsertJE',
    type,
    message,
    duration,
  });
}
