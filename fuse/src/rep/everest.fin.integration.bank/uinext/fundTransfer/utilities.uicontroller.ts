// @i18n:everest.fin.accounting/fundTransfer
import { PlainDate } from '@everestsystems/datetime';
import { Decimal } from '@everestsystems/decimal';
import { getCurrencySymbol } from '@pkg/everest.base/public/currency/precision';
import {
  getUIModelFromSharedState,
  setUIModelInSharedState,
} from '@pkg/everest.base/public/utils/ui/sharedStateUtils.ui';
import type { EvstBaseCurrency } from '@pkg/everest.base/types/enums/BaseCurrency';
import { AccountingPeriodUI } from '@pkg/everest.fin.accounting/types/AccountingPeriod.ui';
import { EvstAccountSubType } from '@pkg/everest.fin.accounting/types/enums/AccountSubType';
import { EvstFundTransferLineAccountType } from '@pkg/everest.fin.accounting/types/enums/FundTransferLineAccountType';
import { EvstJournalEntryType } from '@pkg/everest.fin.accounting/types/enums/JournalEntryType';
import type { CreateFundTransferUiTemplate } from '@pkg/everest.fin.integration.bank/types/uiTemplates/uinext/fundTransfer/createFundTransfer.ui';
import type { FundTransferUiTemplate } from '@pkg/everest.fin.integration.bank/types/uiTemplates/uinext/fundTransfer/fundTransfer.ui';
import { groupBy, set } from 'lodash';

type Any = any;
type CreateFundTransferContext =
  CreateFundTransferUiTemplate.CreateFundTransferContext;
type FundTransferLine = CreateFundTransferUiTemplate.FundTransferLine;
type FundTransferContext = FundTransferUiTemplate.FundTransferContext;

export type Context = (CreateFundTransferContext | FundTransferContext) & {
  state: {
    selectedFromAccountId: number;
    selectedToAccountId: number;
    fromAccountCurrency: string;
    toAccountCurrency: string;
    postingPeriodId: number;
    entityId: number;
    fromAccountId: number;
    toAccountId: number;
    transferAmount: Decimal;
    transferDate: Date;
    toEntityId: number;
    fromEntityCurrency?: string;
    toEntityCurrency?: string;
    isSameCurrency?: boolean;
  };
};

export function getbankFeeConfigQuery(context: Context) {
  const { state } = context;
  const coaId = state.coaId;
  if (coaId) {
    return { where: { coaId } };
  }
}

export function getBankAccountsList(context: Context, type: string): Any {
  const { data, state } = context;
  const filterAndMapAccounts = (excludeId: number) =>
    data?.bankAccounts
      ?.filter((ba) => ba.id !== excludeId)
      .map((ba) => ({
        ...ba,
        prependIcon:
          ba.accountSubType === 'bank' ? 'account_balance' : 'credit_card',
      }));

  const list =
    type === 'fromAccount'
      ? filterAndMapAccounts(state.selectedToAccountId)
      : filterAndMapAccounts(state.selectedFromAccountId);

  const groupedList = Object.entries(
    groupBy(list, (item) => item['Account-Entity'].entityName)
  ).map(([groupId, groupItems]) => ({
    groupTitle: groupId,
    idProp: 'id',
    textProp: 'accountName',
    suffixCornerProp: 'currency',
    tooltipProp: ({ value }) => {
      if (
        value.accountSubType === EvstAccountSubType.CreditCard &&
        value.currency !== state.fromAccountCurrency
      ) {
        return 'Multi-currency credit card fund transfer not supported';
      }
    },
    list: groupItems.sort(
      (a, b) =>
        Number.parseInt(a.accountNumber) - Number.parseInt(b.accountNumber)
    ),
  }));
  return groupedList;
}

export async function handleFromAccountChange(context: Context, id: number) {
  const { state, data } = context;
  const { toAccountId } = state ?? {};
  const _fromNodeReference = data.fromAccount?._nodeReference;
  const fromBankAccount = data.bankAccounts?.find((ba) => ba.id === id);
  if (!fromBankAccount) {
    return;
  }
  const {
    accountNumber,
    accountName,
    currency,
    entityId,
    'Account-Entity': accountEntity,
  } = fromBankAccount;

  state.selectedFromAccountId = id;
  state.fromAccountCurrency = currency;
  state.fromBankEntityId = entityId;
  state.fromEntityCurrency = accountEntity['currency'] ?? '';

  setUIModelInSharedState(context, 'fundTransfer', 'entityId', entityId);
  await handleEntityChange(context, entityId);

  const lineValuesToUpdate = {
    accountId: fromBankAccount.id,
    accountName: `${accountNumber} — ${accountName}`,
    currency: currency,
    accountType: EvstFundTransferLineAccountType.From,
    entityName: accountEntity.entityName,
  };

  for (const [key, value] of Object.entries(lineValuesToUpdate)) {
    setUIModelInSharedState(
      context,
      'fromAccount',
      key as Any,
      fromBankAccount ? value : null,
      _fromNodeReference
    );
  }

  await setDefaultFxRate(context);

  if (!toAccountId) {
    /**
     * Ignore removing account if template in opened in modal to match bank trasactions.
     */
    // setUIModelInSharedState(context, 'fundTransfer', 'toAccountId', null);
  }

  const transferDate = getUIModelFromSharedState(
    context,
    'fundTransfer',
    'transferDate'
  );
  if (entityId && transferDate) {
    await fetchPostingPeriod(context, transferDate);
  }

  await setIntercompanyStatus(context);
}

export function getToAccountDisabledValue(context: Context) {
  const { data, state } = context;
  return (data?.bankAccounts ?? [])
    .filter(
      (ba) =>
        ba.accountSubType === EvstAccountSubType.CreditCard &&
        ba.currency !== state.fromAccountCurrency
    )
    .map((ba) => ba.id);
}

export async function handleToAccountChange(context: Context, id: number) {
  const { state, data } = context;
  const _toNodeReference = data.toAccount?._nodeReference;
  const { entities = [] } = data ?? {};
  const bankAccounts = getUIModelFromSharedState(context, 'bankAccounts');
  const toAccount = bankAccounts?.find((ba) => ba.id === id);

  if (toAccount) {
    // Update the 'to' account field to reflect the selected entity
    const {
      accountNumber,
      accountName,
      currency,
      entityId,
      'Account-Entity': accountEntity,
    } = toAccount;

    state.selectedToAccountId = id;
    state.toAccountCurrency = currency;
    state.toEntityId = entityId;
    state.toEntityCurrency = accountEntity?.currency ?? '';

    const lineValuesToUpdate = {
      accountId: toAccount?.id,
      accountName: `${accountNumber} — ${accountName}`,
      currency: currency,
      accountType: EvstFundTransferLineAccountType.To,
      entityName: accountEntity.entityName,
    };

    for (const [key, value] of Object.entries(lineValuesToUpdate)) {
      setUIModelInSharedState(
        context,
        'toAccount',
        key as Any,
        toAccount ? value : null,
        _toNodeReference
      );
    }
    state.toAccountCurrency = currency;

    // Set toEntity based on the selected 'to' account
    const entity = entities.find((e) => e.id === entityId);
    if (entity) {
      setUIModelInSharedState(
        context,
        'fundTransfer',
        'toEntityId',
        entityId,
        ''
      );
      await handleToEntityChange(context, entityId);
      state.toEntityId = entityId;
    }
    await setIntercompanyStatus(context);
  } else {
    setUIModelInSharedState(context, 'fundTransfer', 'toAccountId', null);
  }
  await setDefaultFxRate(context);
}

export async function handleEntityChange(context: Context, id: number) {
  const { state, data } = context;
  const { entities = [] } = data ?? {};
  const entity = entities?.find((entity) => entity.id === id);

  state.coaId = entity['EntityBook-Entity']?.[0]?.chartOfAccountsId;
  state.entityId = id;

  // Update the 'from' account field to reflect the selected entity
  const fromAccount = data.bankAccounts?.find(
    (ba) =>
      ba.id === state.selectedFromAccountId &&
      ba.entityId === id &&
      ba.accountSubType === EvstAccountSubType.Bank
  );
  if (fromAccount) {
    const { currency } = fromAccount;
    state.fromAccountCurrency = currency;
  } else {
    setUIModelInSharedState(context, 'fundTransfer', 'fromAccountId', null);
  }
}
export async function handleToEntityChange(context: Context, id: number) {
  const { state, data } = context;

  state.toEntityId = id;

  // Update the 'to' account field to reflect the selected entity
  const toAccount = data.bankAccounts?.find(
    (ba) =>
      ba.id === state.selectedToAccountId &&
      ba.entityId === id &&
      ba.accountSubType === EvstAccountSubType.Bank
  );
  if (toAccount) {
    const { currency } = toAccount;
    state.toAccountCurrency = currency;
  }
}

export function getTooltipFor(context: Context, fieldName: string) {
  const { data, state } = context;
  const { bankFeeConfiguration } = data ?? {};
  if (
    fieldName === 'bankFee' &&
    !bankFeeConfiguration?.id &&
    state.mode !== 'view'
  ) {
    const text = '{{fundTransfer.noAccount.bankFee.error.message}}';
    return { text };
  }
}

export function isBankFeeEditDisabled(context: Context) {
  const { data } = context;
  const { bankFeeConfiguration } = data ?? {};
  return !bankFeeConfiguration?.bankFeeAccountId;
}

export async function fetchPostingPeriod(context: Context, date: PlainDate) {
  const { state } = context;
  let response;
  const entityId =
    getUIModelFromSharedState(context, 'fundTransfer', 'entityId') ??
    state.entityId;
  const convertedDate: PlainDate =
    typeof date === 'string' ? PlainDate.from(date) : date;
  state.periodErrorMsg = null;

  if (date && entityId) {
    response = (await AccountingPeriodUI.findAccountingPeriod(context, {
      entityId,
      postingDate: convertedDate,
      forPosting: false,
      lockEvaluation: {
        journalEntryTypes: [EvstJournalEntryType.FundTransfer],
      },
    }).run('postingPeriod')) as {
      postingPeriod: {
        id: number;
        periodName: string;
        closed: boolean;
        locked: boolean;
      };
      error: object;
    };

    const {
      id,
      closed = false,
      locked = false,
    } = response?.postingPeriod ?? {};
    state.canBePosted = !closed && !locked;

    if (!id) {
      removePostingPeriodFromState(context);
      return;
    }

    if (closed || locked) {
      state.periodErrorMsg = '{{fundTransfer.postingPeriod.error.message}}';
      removePostingPeriodFromState(context);
      return;
    }
    addPostingPeriodToState(context, response.postingPeriod);
    await setDefaultFxRate(context);
  } else {
    removePostingPeriodFromState(context);
  }
  return response;
}

export function removePostingPeriodFromState(context: Context) {
  const { state } = context;

  if (state.postingPeriodId) {
    state.postingPeriodId = undefined;
    state.postingPeriodName = undefined;
    state.postingPeriodStatus = undefined;
    setUIModelInSharedState(context, 'fundTransfer', 'postingPeriodId', 0, '');
  }
}

export function addPostingPeriodToState(context: Context, postingPeriod: Any) {
  const { state } = context;
  state.postingPeriodId = postingPeriod?.id;
  state.postingPeriodName = postingPeriod?.periodName;
  state.postingPeriodStatus = postingPeriod?.activeStatuses;
  setUIModelInSharedState(
    context,
    'fundTransfer',
    'postingPeriodId',
    state.postingPeriodId,
    ''
  );
}

export function getPostingPeriods({ state }: Context) {
  return [
    {
      value: state.postingPeriodId ?? 0,
      text: state.postingPeriodName ?? '',
    },
  ];
}

export async function setIsManualFxRate(context: Context, val: number) {
  const { state } = context;
  const defaultFxRate = await fetchExchangeRate(context);
  if (!new Decimal(defaultFxRate).equals(new Decimal(val))) {
    state.fxChanged = true;

    setUIModelInSharedState(
      context,
      'fundTransfer',
      'isManualExchangeRate',
      true
    );
  }
}

export function showMessage(
  { helpers }: Context,
  type: 'success' | 'error' | 'info' | 'warning' | 'loading',
  message: string | undefined,
  duration: number,
  key?: string
) {
  helpers.showNotificationMessage({
    key: key ?? 'upsertFT',
    type,
    message,
    duration,
  });
}

function checkCurrencies(context: Context) {
  const { data, state } = context;
  const entityId = getUIModelFromSharedState(
    context,
    'fundTransfer',
    'entityId'
  );
  const entity = data.entities.find((e) => e.id === entityId);
  const fromAccountCurrency = state.fromAccountCurrency;
  const toAccountCurrency = state.toAccountCurrency;

  const allSameCurrency =
    new Set([fromAccountCurrency, toAccountCurrency, entity?.currency]).size ===
    1;

  return {
    allSameCurrency,
    entity,
    fromAccountCurrency,
    toAccountCurrency,
  };
}

export async function triggerToAccountAmountChange(context: Context) {
  await setDefaultFxRate(context);
  updateToAccountAmount(context);
}

function updateToAccountAmount(context: Context) {
  const { data, sharedState, helpers } = context;
  const { allSameCurrency } = checkCurrencies(context);
  const modelInstance = sharedState.getNodeInstance(data.toAccount);
  const isToAccountEditable =
    helpers.get(modelInstance, 'metadata.fields.amount.editable') ?? true;
  // by default, editable is not defined, therefore assigning the isToAccountEditable as true
  if (allSameCurrency && isToAccountEditable) {
    const fromAmount = data.fromAccount?.amount?.amount || new Decimal(0);

    setUIModelInSharedState(context, 'toAccount', 'amount', {
      amount: new Decimal(fromAmount),
      currency: data.fromAccount?.amount?.currency,
    });
  }
}

export async function setDefaultFxRate(context: Context) {
  const { state } = context;
  const { allSameCurrency, fromAccountCurrency, toAccountCurrency } =
    checkCurrencies(context);

  if (allSameCurrency) {
    state.isSameCurrency = true;
    setUIModelInSharedState(
      context,
      'fundTransfer',
      'exchangeRate',
      new Decimal(1).toFixed(4) as Any
    );
  } else if (fromAccountCurrency && toAccountCurrency) {
    state.isSameCurrency = false;
    await fillExchangeRate(context);
  }
}

export async function fillExchangeRate(context: Context) {
  const fxRate = await fetchExchangeRate(context);
  setUIModelInSharedState(
    context,
    'fundTransfer',
    'exchangeRate',
    new Decimal(fxRate).toFixed(12) as Any
  );
}

export async function fetchExchangeRate(context: Context) {
  const { state, data } = context;

  let fromEntityCurrency = state.fromEntityCurrency;
  let toEntityCurrency = state.toEntityCurrency;
  const baseCurrency = state.fromAccountCurrency;
  const targetCurrency = state.toAccountCurrency;

  if (!baseCurrency || !fromEntityCurrency) {
    const bankAccounts = data?.bankAccounts || [];
    const fromAccount = bankAccounts.find(
      (ba) => ba.id === state?.selectedFromAccountId
    );
    const toAccount = bankAccounts.find(
      (ba) => ba.id === state?.selectedToAccountId
    );
    if (fromAccount && toAccount) {
      fromEntityCurrency = fromAccount['Account-Entity'].currency;
      toEntityCurrency = toAccount['Account-Entity'].currency;
    }
  }

  // Early return if currencies aren't available
  if (!baseCurrency || !targetCurrency) {
    return new Decimal(1);
  }

  // Check if this is an intercompany transfer
  const isInterCompany = getUIModelFromSharedState(
    context,
    'fundTransfer',
    'isIntercompany'
  );

  if (isInterCompany) {
    const interCompanyCurrency = getUIModelFromSharedState(
      context,
      'fundTransfer',
      'intercompanyTransactionalCurrency'
    );
    if (!interCompanyCurrency) {
      return new Decimal(0);
    }
  } else {
    // Case 5 handling :- https://github.com/everestsystems/content/issues/16438
    // Further exchange rate calculation are handled in prepareAndReturnJELines function on upsertFundTransferJournalEntry.action.ts
    if (
      baseCurrency !== fromEntityCurrency &&
      targetCurrency !== toEntityCurrency &&
      baseCurrency !== targetCurrency
    ) {
      showMessage(
        context,
        'error',
        '{{fundTransfer.toFromAccountCurrencyError}}',
        5
      );
      return new Decimal(0);
    }
  }

  // Get amounts from shared state
  const fromAccount = getUIModelFromSharedState(context, 'fromAccount');
  const toAccount = getUIModelFromSharedState(context, 'toAccount');

  const fromAmount = fromAccount?.amount?.amount;
  const toAmount = toAccount?.amount?.amount;

  if (!fromAmount || !toAmount) {
    return new Decimal(0);
  }

  const fromTotal = new Decimal(fromAmount).absoluteValue();
  const toTotal = new Decimal(toAmount);
  const exchangeRate = toTotal.dividedBy(fromTotal).toFixed(12);
  return new Decimal(exchangeRate).lessThanOrEqualTo(0)
    ? new Decimal(0)
    : exchangeRate;
}

export async function setIntercompanyStatus(context: Context) {
  const entityId = getUIModelFromSharedState(
    context,
    'fundTransfer',
    'entityId'
  );
  const toEntityId = getUIModelFromSharedState(
    context,
    'fundTransfer',
    'toEntityId'
  );

  setUIModelInSharedState(
    context,
    'fundTransfer',
    'isIntercompany',
    entityId !== toEntityId &&
      (entityId !== undefined || !!toEntityId !== undefined)
  );
}

export function isIntercompanyDisabled(context: Context) {
  const entityId = getUIModelFromSharedState(
    context,
    'fundTransfer',
    'entityId'
  );
  const toEntityId = getUIModelFromSharedState(
    context,
    'fundTransfer',
    'toEntityId'
  );

  if (entityId === undefined || toEntityId === undefined) {
    return true;
  }

  // Disable if entityId and toEntityId are the same
  return entityId === toEntityId;
}

export function getIntercompanyTransactionalCurrency(context: Context) {
  const fundTransfer = getUIModelFromSharedState(context, 'fundTransfer');
  const accounts = getUIModelFromSharedState(context, 'bankAccounts');

  if (!fundTransfer || !accounts) {
    return [];
  }

  const { fromAccountId, toAccountId } = fundTransfer;

  const relevantAccounts = accounts.filter(
    (account) => account.id === fromAccountId || account.id === toAccountId
  );

  const currencies = relevantAccounts.map((account) => ({
    id: account.id,
    currency: account.currency,
  }));

  // Remove duplicates if both accounts have the same currency
  const uniqueCurrencies = currencies.filter(
    (currency, index, self) =>
      index === self.findIndex((t) => t.currency === currency.currency)
  );
  return uniqueCurrencies;
}

function getAccountDetails({ data, state }: Context, accountType: string) {
  const isFromAccount = accountType === 'fromAccount';
  const accountId = isFromAccount
    ? state?.selectedFromAccountId ?? data?.fromAccount?.accountId
    : state?.selectedToAccountId ?? data?.toAccount?.accountId;
  const bankAccount = data.bankAccounts?.find((ba) => ba.id === accountId);
  const currency = isFromAccount
    ? state?.fromAccountCurrency ?? data?.fromAccount?.currency
    : state?.toAccountCurrency ?? data?.toAccount?.currency;

  return { isFromAccount, accountId, bankAccount, currency };
}

export function getAccountTitle(context: Context, accountType: string) {
  const { isFromAccount, bankAccount } = getAccountDetails(
    context,
    accountType
  );
  const labelPrefix = isFromAccount ? 'From' : 'To';

  return bankAccount
    ? `${labelPrefix} ${bankAccount.accountName}`
    : labelPrefix;
}
export function getAccountCurrencyDescription(
  context: Context,
  accountType: string
) {
  const { currency } = getAccountDetails(context, accountType);
  return currency === null || currency === undefined ? '- -' : currency;
}

export function getAccountDescriptionLabel(
  context: Context,
  accountType: string
): string {
  const { data, state } = context;
  if (!data.bankAccounts?.length || !state) {
    return '- -';
  }

  const { accountId, bankAccount } = getAccountDetails(context, accountType);

  if (!accountId || !bankAccount) {
    return '- -';
  }

  const entityName = bankAccount['Account-Entity']?.entityName || '';
  const accountCurrency = bankAccount?.currency || '';
  const postingPeriodName =
    state.postingPeriodName || data?.postingPeriod?.['periodName'] || '';
  const postingPeriod = postingPeriodName ? `,${postingPeriodName}` : '';
  return `${entityName},${accountCurrency},${bankAccount.accountNumber} — ${bankAccount.accountName}${postingPeriod}`;
}

const getBankLogo = (institution) => {
  if (institution?.logoId) {
    return `/api/app/storage/${institution?.logoId}`;
  }

  if (!institution?.logoUrl) {
    return null;
  }

  return institution.logoUrl.startsWith('http://') ||
    institution.logoUrl.startsWith('https://')
    ? institution.logoUrl
    : `data:image/png;base64,${institution.logoUrl}`;
};

export function getAvatar(context: Context, accountType: string) {
  const { data } = context;
  const { bankAccount } = getAccountDetails(context, accountType);

  if (!bankAccount) {
    return '';
  }
  const financialId =
    bankAccount?.['BankAccount-Account']?.financialInstitutionId;
  const institution = data.institutions.find((item) => item.id === financialId);

  if (financialId) {
    if (institution?.logoUrl) {
      return getBankLogo(institution);
    }

    if (institution?.logoId) {
      return `/api/app/storage/${institution?.logoId}`;
    }
  }

  return '';
}

export default function getAccountData(
  context: Context
): Partial<FundTransferLine>[] {
  const originalFromAccount = getUIModelFromSharedState(
    context,
    'fromAccount'
  ) as Partial<FundTransferLine>;
  const originalToAccount = getUIModelFromSharedState(
    context,
    'toAccount'
  ) as Partial<FundTransferLine>;

  // Clone the objects to avoid mutating the original objects
  const fromAccount = JSON.parse(JSON.stringify(originalFromAccount));
  const toAccount = JSON.parse(JSON.stringify(originalToAccount));

  const fromBankFee = new Decimal(originalFromAccount?.bankFee || 0);
  const toBankFee = new Decimal(originalToAccount?.bankFee || 0);

  if (fromAccount.amount?.amount !== undefined) {
    const amount = new Decimal(originalFromAccount.amount.amount);
    fromAccount.amount.amount = amount.plus(fromBankFee).times(-1);
  }

  if (toAccount.amount?.amount !== undefined) {
    toAccount.amount.amount = new Decimal(
      originalToAccount.amount.amount
    ).minus(toBankFee);
  }

  fromAccount.bankFee = fromBankFee.isNaN()
    ? new Decimal(0)
    : fromBankFee.times(-1);

  toAccount.bankFee = toBankFee.isNaN() ? new Decimal(0) : toBankFee.times(-1);

  return [fromAccount, toAccount];
}

export function getAccountAmount(
  context: Context,
  accountType: 'fromAccount' | 'toAccount'
): string {
  const { helpers } = context;
  const { isFromAccount, currency } = getAccountDetails(context, accountType);
  const accountData = getUIModelFromSharedState(context, accountType);

  // Calculate total amount including bank fees
  const amount = accountData?.amount?.amount || 0;
  const bankFee = accountData?.bankFee || 0;
  let totalAccountAmount = isFromAccount
    ? new Decimal(amount).plus(bankFee)
    : new Decimal(amount).minus(bankFee);
  // Apply negative sign for from accounts
  if (isFromAccount) {
    totalAccountAmount = totalAccountAmount.absoluteValue().times(-1); //allways be -ve
  }

  // Handle case where amount is NaN
  if (totalAccountAmount.isNaN()) {
    const defaultAmount = isFromAccount ? '(0.00)' : '0.00';

    if (!currency) {
      return defaultAmount;
    }

    const formattedDefault = isFromAccount
      ? `(${getCurrencySymbol(currency as EvstBaseCurrency) ?? ''}0.00)`
      : '0.00';

    return String(
      helpers.parseValue({
        value: formattedDefault,
        format: currency,
        parseAs: 'currency',
      })
    );
  }

  if (!currency) {
    return totalAccountAmount.toFixed(2);
  }
  return String(
    helpers.parseValue({
      value: totalAccountAmount.toFixed(2),
      format: currency,
      parseAs: 'currency',
    })
  );
}

export function getCurrency(context: Context) {
  return context.state?.toAccountCurrency;
}

export function setRequiredFieldsOnInit(context: Context) {
  const { data, sharedState } = context;
  setTimeout(() => {
    const fromAccountInstance = sharedState.getModelData(data.fromAccount)
      ?.instance;
    set(fromAccountInstance, `metadata.fields.amount.amount.required`, true);
    const toAccountInstance = sharedState.getModelData(data.toAccount)
      ?.instance;
    set(toAccountInstance, `metadata.fields.amount.amount.required`, true);
  }, 100);
}
