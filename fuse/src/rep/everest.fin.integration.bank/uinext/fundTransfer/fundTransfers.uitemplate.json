{"version": 2, "uicontroller": "fundTransfers.uicontroller.ts", "uimodel": {"nodes": {"fundTransfers": {"type": "list", "query": {}, "modelId": "everest.fin.accounting/FundTransferHeaderModel.FundTransferHeader", "pagination": true, "fieldList": ["fundTransferHeaderNumber", "entityName", "entityId", "toEntityName", "entities", "transferDate", "transferAmount", "id", "fromAccountId", "toAccountId", "journalEntryHeaderId", "isIntercompany", "JournalEntryHeader-FinancialDocument.journalEntryNumber", "FundTransferHeaderFromAccount-Account.accountName", "FundTransferHeaderToAccount-Account.accountName", "isVoided", "fundTransferPaymentStatus"]}, "matchedTransactions": {"type": "list", "query": "@controller:getBankTransactionQuery()", "modelId": "everest.fin.integration.bank/BankTransactionNewModel.BankTransactionNew", "fieldList": ["matchingTransaction", "id"]}, "bankAccounts": {"type": "list", "query": {"where": {}}, "modelId": "everest.fin.accounting/BankAccountModel.BankAccount", "fieldList": ["id", "accountId", "BankAccount-FinancialInstitution.logoId", "BankAccount-FinancialInstitution.logoUrl"]}, "bankPayment": {"type": "struct", "modelId": "everest.fin.integration.bank/BankPaymentModel.BankPayment", "fieldList": []}}}, "uiview": {"layout": "everest.base.ui/layouts/uilayout/list", "props": {"i18n": "everest.fin.accounting/fundTransfer", "title": "{{fundTransfer.titlePlural}}", "rowNavigation": {"url": "/templates/everest.fin.integration.bank/uinext/fundTransfer/fundTransfer", "idProp": "id"}, "removeDeleteAction": true, "filterConfig": {"isIntercompany": {"showNone": false}}, "node": "fundTransfers", "customId": "fundTransfersTable", "onCreateClick": "@controller:navigateToCreateFT", "columns": [{"headerName": "{{fundTransfer.id}}", "field": "fundTransferHeaderNumber", "fieldProps": {"fontWeight": "bold"}}, {"headerName": "{{fundTransfer.transactionDate}}", "field": "transferDate"}, {"headerName": "{{fundTransfer.journalEntry}}", "field": "journalEntryNumber", "valueGetter": "@controller:je<PERSON><PERSON>ueGetter", "cellLinkEmphasized": false, "onCellClicked": "@controller:openJournalEntry"}, {"headerName": "{{fundTransfer.entities}}", "field": "entities"}, {"headerName": "{{fundTransfer.amount}}", "field": "transferAmount"}, {"headerName": "{{fundTransfer.fromBank}}", "field": "FundTransferHeaderFromAccount-Account.accountName", "cellVariant": {"variant": "avatar", "matchers": "@controller:getFromAvatar"}}, {"headerName": "{{fundTransfer.toBank}}", "field": "FundTransferHeaderToAccount-Account.accountName", "cellVariant": {"variant": "avatar", "matchers": "@controller:getToAvatar"}}, {"field": "isIntercompany", "headerName": "{{fundTransfer.intercompany}}", "cellVariant": {"variant": "badge", "matchers": "@controller:getIconType"}, "valueGetter": "@controller:getIntercompanyValue"}], "customActions": [{"label": "{{fundTransfer.entries.setVoid}}", "disabled": "@controller:isVoidButtonDisabled()", "onClick": "@controller:confirmVoidSelectedFundTransfers"}, {"label": "{{fundTransfer.entries.checkStatuses}}", "onClick": "@controller:refreshPaymentStatuses"}]}}}