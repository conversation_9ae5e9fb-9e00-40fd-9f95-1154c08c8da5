{"version": 2, "uicontroller": ["createFundTransfer.uicontroller.ts", "everest.fin.integration.bank/uinext/fundTransfer/utilities.uicontroller.ts"], "uimodel": {"state": {"mode": "create", "canBePosted": true, "featureToggles": {"transaction": true}}, "nodes": {"entities": {"type": "list", "modelId": "everest.base/EntityModel.Entity", "queryActiveEntities": {"query": {"where": {}, "orderBy": ["entityName"]}, "fieldList": ["entityName", "id", "currency", "EntityBook-Entity.chartOfAccountsId"]}}, "bankAccounts": {"type": "list", "query": {"where": {"accountSubType": {"$in": ["bank", "creditCard"]}, "accountStatus": "enabled"}, "orderBy": [{"field": "accountSubType", "ordering": "asc"}, {"field": "accountName", "ordering": "asc"}]}, "modelId": "everest.fin.accounting/AccountModel.Account", "fieldList": ["accountName", "accountNumber", "chartOfAccountsId", "entityId", "currency", "id", "accountSubType", "Account-Entity.entityName", "Account-Entity.currency", "BankAccount-Account.financialInstitutionId"]}, "postingPeriod": {"type": "struct", "modelId": "everest.fin.accounting/AccountingPeriodModel.AccountingPeriod"}, "fundTransfer": {"type": "struct", "modelId": "everest.fin.accounting/FundTransferHeaderModel.FundTransferHeader", "fieldList": ["id", "entityId", "fundTransferName", "entityName", "transferAmount", "transferDate", "postingPeriodId", "fromAccountCurrency", "exchangeRate", "toAccountId", "fromAccountId", "isManualExchangeRate", "description", "isVoided", "toAccountName", "fromAccountName", "toEntityId", "toEntityName", "intercompanyProcess", "transferReason", "isIntercompany", "intercompanyProcessId", "intercompanyTransactionalCurrency", "toAccount<PERSON><PERSON><PERSON>cy"]}, "fundTransferLines": {"type": "list", "modelId": "everest.fin.accounting/FundTransferLineModel.FundTransferLine", "fieldList": ["id", "accountType", "accountId", "amount", "accountName", "bankFee", "fundTransferHeaderId", "entityName"]}, "fundTransferAttachments": {"type": "list", "modelId": "everest.fin.accounting/FundTransferAttachmentModel.FundTransferAttachment"}, "exchangeRate": {"type": "struct", "modelId": "everest.base/ExchangeRateModel.ExchangeRate", "fieldList": ["value"]}, "bankFeeConfiguration": {"type": "struct", "modelId": "everest.fin.accounting/BankFeeSettingsModel.BankFeeSettings", "query": "@controller:getbankFeeConfigQuery()", "fieldList": ["id", "coaId", "bankFeeAccountId"]}, "intercompanyProcess": {"type": "list", "modelId": "everest.fin.accounting/IntercompanyProcessModel.IntercompanyProcess", "query": {"where": {}}, "fieldList": ["id", "processName", "processDefinition"]}, "configurationFundTransferBankPaymentEnabled": {"modelId": "everest.appserver/ConfigurationModel.Configuration", "type": "struct", "getValueOrDefault": {"name": "FUND_TRANSFER_BANK_PAYMENT_ENABLED", "packageName": "everest.fin.integration.bank", "defaultValue": true}, "fieldList": ["value"]}, "fromAccount": {"type": "struct", "modelId": "everest.fin.accounting/FundTransferLineModel.FundTransferLine", "fieldList": ["accountType", "accountId", "amount", "accountName", "bankFee", "fundTransferHeaderId", "entityName", "currency"]}, "toAccount": {"type": "struct", "modelId": "everest.fin.accounting/FundTransferLineModel.FundTransferLine", "fieldList": ["accountType", "accountId", "amount", "accountName", "bankFee", "fundTransferHeaderId", "entityName", "currency"]}, "institutions": {"type": "list", "modelId": "everest.fin.accounting/FinancialInstitutionModel.FinancialInstitution", "query": {"where": {}}, "fieldList": []}}}, "uiview": {"layout": "everest.base.ui/layouts/uilayout/landing", "props": {"i18n": ["everest.fin.accounting/fundTransfer"], "tabTitle": "Create Fund Transfer", "stretch": true, "config": {"stretch": true, "allowRefreshData": true}, "autoRefreshData": false, "editing": {"title": true, "description": true}, "placeholders": {"title": "{{fundTransfer.title.singular}}", "description": "{{fundTransfer.addDescription}}"}, "status": "NEW", "title": "@bindingController:(fundTransfer.fundTransferName, getTitle())", "description": "@binding:fundTransfer.description", "mainBlock": {"columns": "5", "customId": "fundTransferCreatePage", "type": "primary", "isEditing": true, "title": "{{fundTransfer.details.title}}", "elements": [{"component": "Select", "label": "{{fundTransfer.from}}", "idProp": "id", "prefixProp": "accountNumber", "textProp": "accountName", "prependIconProp": "prependIcon", "value": "@binding:fundTransfer.fromAccountId", "text": "@binding:fundTransfer.fromAccountName", "list": "@controller:getBankAccountsList('fromAccount')", "onChange": "@controller:handleFromAccountChange", "allowClear": true, "placeholder": "{{fundTransfer.selectFromAccount}}"}, {"component": "Select", "label": "{{fundTransfer.to}}", "idProp": "id", "prefixProp": "accountNumber", "textProp": "accountName", "value": "@binding:fundTransfer.toAccountId", "text": "@binding:fundTransfer.toAccountName", "list": "@controller:getBankAccountsList('toAccount')", "onChange": "@controller:handleToAccountChange", "disabledValue": "@controller:getToAccountDisabledValue()", "allowClear": true, "placeholder": "{{fundTransfer.selectToAccount}}"}, {"component": "Select", "label": "{{fundTransfer.entity}}", "idProp": "id", "textProp": "entityName", "placeholder": "{{fundTransfer.selectEntity}}", "isDisabled": true, "visible": false, "value": "@binding:fundTransfer.entityId", "text": "@binding:fundTransfer.entityName", "list": "@binding:entities"}, {"component": "Select", "label": "{{fundTransfer.toEntity}}", "idProp": "id", "textProp": "entityName", "placeholder": "{{fundTransfer.selectEntity}}", "isDisabled": true, "visible": false, "value": "@binding:fundTransfer.toEntityId", "text": "@binding:fundTransfer.toEntityName", "list": "@binding:entities"}, {"label": "{{fundTransfer.date}}", "parseAs": "date", "format": "P", "value": "@binding:fundTransfer.transferDate", "onChange": "@controller:fetchPostingPeriod", "customFieldMessages": {"fieldError": "@state:periodErrorMsg"}, "placeholder": "{{fundTransfer.selectTransferDate}}"}, {"component": "Select", "isDisabled": true, "idProp": "value", "textProp": "text", "value": "@binding:fundTransfer.postingPeriodId", "text": "@state:postingPeriodName", "list": "@controller:getPostingPeriods()", "visible": false}, {"label": "{{fundTransfer.fxRate}}", "value": "@binding:fundTransfer.exchangeRate", "placeholder": "0.00", "isDisabled": true, "onBlur": "@controller:setIsManualFxRate"}, {"component": "Select", "label": "{{fundTransfer.intercompany}}", "idProp": "value", "textProp": "label", "visible": "@controller:showIntercompanyDetails()", "value": "@binding:fundTransfer.isIntercompany", "isDisabled": true, "list": [{"label": "Yes", "value": true}, {"label": "No", "value": false}]}, {"component": "Select", "label": "{{fundTransfer.intercompanyProcess}}", "idProp": "id", "textProp": "processName", "visible": "@controller:showIntercompanyDetails()", "value": "@binding:fundTransfer.intercompanyProcessId", "list": "@binding:intercompanyProcess", "rules": "@controller:isRequiredForIntercompany()"}, {"component": "Select", "label": "{{fundTransfer.transferReason}}", "idProp": "value", "textProp": "label", "visible": "@controller:showIntercompanyDetails()", "value": "@binding:fundTransfer.transferReason", "rules": "@controller:isRequiredForIntercompany()"}, {"component": "Select", "label": "{{fundTransfer.intercompanyTransactionalCurrency}}", "idProp": "currency", "textProp": "currency", "visible": "@controller:showIntercompanyDetails()", "value": "@binding:fundTransfer.intercompanyTransactionalCurrency", "list": "@controller:getIntercompanyTransactionalCurrency()", "rules": "@controller:isRequiredForIntercompany()", "onChange": "@controller:setDefaultFxRate"}]}, "customSections": [{"component": "SectionGroup", "section": {"grid": {"size": "12"}}, "props": {"sections": [{"component": "ActionGroup", "customId": "fromAccountActionGroup", "section": {"grid": {"size": "6"}, "title": "@controller:getAccountTitle('fromAccount')", "editing": true}, "props": {"variant": "default", "editing": true, "versionNew": true, "descriptionPosition": "rightSide", "buttonActionsPosition": "right", "lines": [{"avatar": {"src": "@controller:get<PERSON><PERSON><PERSON>('fromAccount')", "text": "--"}, "label": "@controller:getAccountAmount('fromAccount')", "description": "@controller:getAccountDescriptionLabel('fromAccount')"}, {"label": "{{fundTransfer.amountSent}}", "description": "@controller:getAccountCurrencyDescription('fromAccount')", "avatar": false, "fieldProps": {"component": "InputNumber", "value": "@binding:fromAccount.amount", "isEditing": true, "placeholder": "0.00", "compositeConfig": {"amount": {"placeholder": "0.00", "onBlur": "@controller:triggerToAccountAmountChange"}}}}, {"label": "{{fundTransfer.sendingBankFee}}", "description": "@controller:getAccountCurrencyDescription('fromAccount')", "avatar": false, "fieldProps": {"component": "InputNumber", "value": "@binding:fromAccount.bankFee", "_type": "amount", "_parseAs": "currency", "placeholder": "0.00", "isDisabled": "@controller:isBankFeeEditDisabled()", "onBlur": "@controller:setDefaultFxRate", "tooltip": "@controller:getTooltipFor('bankFee')"}}, {"avatar": false, "visible": false, "field": "accountName"}, {"avatar": false, "visible": false, "field": "currency"}]}}, {"component": "ActionGroup", "customId": "toAccountActionGroup", "section": {"grid": {"size": "6"}, "title": "@controller:getAccountTitle('toAccount')", "editing": true}, "props": {"variant": "default", "editing": true, "versionNew": true, "descriptionPosition": "rightSide", "buttonActionsPosition": "right", "lines": [{"avatar": {"src": "@controller:get<PERSON><PERSON><PERSON>('toAccount')", "text": "--"}, "label": "@controller:getAccountAmount('toAccount')", "description": "@controller:getAccountDescriptionLabel('toAccount')"}, {"label": "{{fundTransfer.amountReceived}}", "description": "@controller:getAccountCurrencyDescription('toAccount')", "avatar": false, "fieldProps": {"component": "InputNumber", "value": "@binding:toAccount.amount", "isEditing": true, "placeholder": "0.00", "compositeConfig": {"amount": {"placeholder": "0.00", "onBlur": "@controller:setDefaultFxRate"}}}}, {"label": "{{fundTransfer.receivingBankFee}}", "field": "bankFee", "description": "@controller:getAccountCurrencyDescription('toAccount')", "avatar": false, "fieldProps": {"component": "InputNumber", "value": "@binding:toAccount.bankFee", "_type": "amount", "_parseAs": "currency", "placeholder": "0.00", "isDisabled": "@controller:isBankFeeEditDisabled()", "onBlur": "@controller:setDefaultFxRate", "tooltip": "@controller:getTooltipFor('bankFee')"}}, {"avatar": false, "visible": false, "field": "accountName"}, {"avatar": false, "visible": false, "field": "currency"}]}}]}}, {"component": "Upload", "section": {"title": "{{fundTransfer.attachments}}", "grid": {"size": "4"}, "customId": "transferAttachments"}, "props": {"name": "transferAttachments", "version": 2, "everestPackage": "everest.fin.accounting", "multiple": true, "disabled": false, "isEditing": true, "formattedAcceptFileTypes": ".pdf,.tiff,.jpeg,.png,.jpg", "uploadHandler": "postgres", "variant": "ButtonView", "acceptableMimeTypes": ["application/pdf", "image/tiff", "image/jpeg", "image/png", "image/jpg"], "maxSizeInMB": 5, "action": "create"}}], "primaryAction": {"label": "{{fundTransfer.create}}", "onClick": "@controller:createFundTransfer", "customActions": [{"label": "{{fundTransfer.initiate}}", "onClick": "@controller:initiateFundTransfer", "visible": "@controller:isInitiateButtonVisible()"}, {"label": "{{fundTransfer.saveAndCreateNew}}", "onClick": "@controller:saveAndCreateNewFundTransfer", "visible": "@controller:saveAndCreateVisible()"}]}, "showSecondaryAction": false}}}