{"version": 2, "uicontroller": ["configureBankFeeSettingsModal.uicontroller.ts"], "uimodel": {"state": {"coaId": null}, "nodes": {"bankAccounts": {"type": "list", "getEnabledAccounts": "@controller:getAccountsQuery()", "modelId": "everest.fin.accounting/AccountModel.Account", "fieldList": ["accountName", "accountNumber", "chartOfAccountsId", "entityId", "currency", "id"]}, "departments": {"type": "list", "modelId": "everest.hr.base/DepartmentModel.Department", "query": "@controller:getDepartmentsQuery()", "fieldList": ["id", "departmentName", "departmentCode"]}, "bankFeeConfiguration": {"type": "struct", "modelId": "everest.fin.accounting/BankFeeSettingsModel.BankFeeSettings", "query": "@controller:getBankFeeConfigurationQuery()", "fieldList": ["id", "coaId", "bankFeeAccountId", "bankFeeDepartmentId"]}, "fundTransfer": {"type": "struct", "modelId": "everest.fin.accounting/FundTransferHeaderModel.FundTransferHeader", "fieldList": ["id"]}}}, "uiview": {"templateType": "details", "i18n": "everest.fin.accounting/fundTransfer", "config": {"stretch": true, "allowRefreshData": false}, "sections": {"content": [{"component": "ActionGroup", "section": {"grid": {"size": "12"}, "editing": true}, "props": {"columns": "1", "stacked": true, "lines": [{"label": "{{fundTransfer.bankFeeAccount}}", "description": "{{fundTransfer.bankFeeAccountDescription}}", "visible": "@controller:isAccountSelectVisible()", "fieldProps": {"component": "Select", "textProp": "accountNumberAndName", "idProp": "id", "allowClear": false, "list": "@controller:getAccountsList()", "value": "@binding:bankFeeConfiguration.bankFeeAccountId"}}, {"label": "{{fundTransfer.bankFeeDepartment}}", "description": "{{fundTransfer.bankFeeDeptDescription}}", "visible": "@controller:isDepartmentSelectVisible()", "fieldProps": {"component": "Select", "value": "@binding:bankFeeConfiguration.bankFeeDepartmentId", "textProp": "deptNumberAndName", "idProp": "id", "list": "@controller:getDepartmentsList()", "allowClear": false}}]}}]}, "actions": {"content": [{"variant": "secondary", "label": "{{fundTransfer.cancel}}", "onClick": "@controller:cancelAction"}, {"variant": "primary", "label": "{{fundTransfer.save}}", "onClick": "@controller:saveAction", "disabled": "@controller:isSaveActionDisabled()"}]}}}