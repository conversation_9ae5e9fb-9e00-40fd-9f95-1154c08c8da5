// @i18n:everest.hr.base/hrbase
// @i18n:everest.fin.accounting/fundTransfer

import {
  type UIExecutionContext,
  UILifecycleHooks,
} from '@everestsystems/content-core';
import { PlainDate } from '@everestsystems/datetime';
import { Decimal } from '@everestsystems/decimal';
import {
  getUIModelFromSharedState,
  setUIModelInSharedState,
} from '@pkg/everest.base/public/utils/ui/sharedStateUtils.ui';
import { toPlainDate } from '@pkg/everest.fin.accounting/public/utils/Date/date';
import type { EvstFundTransferLineAccountType } from '@pkg/everest.fin.accounting/types/enums/FundTransferLineAccountType';
import { EvstFundTransferPaymentStatus } from '@pkg/everest.fin.accounting/types/enums/FundTransferPaymentStatus';
import { EvstJournalEntryType } from '@pkg/everest.fin.accounting/types/enums/JournalEntryType';
import { FundTransferHeaderUI } from '@pkg/everest.fin.accounting/types/FundTransferHeader.ui';
import { EvstBankTransactionMatchStatus } from '@pkg/everest.fin.base/types/enums/BankTransactionMatchStatus';
import { BankPaymentUI } from '@pkg/everest.fin.integration.bank/types/BankPayment.ui';
import type { FundTransferUiTemplate } from '@pkg/everest.fin.integration.bank/types/uiTemplates/uinext/fundTransfer/fundTransfer.ui';

import getAccountData, {
  fetchPostingPeriod,
  isBankFeeEditDisabled,
  setRequiredFieldsOnInit,
  showMessage,
} from './utilities.uicontroller';

type Any = any;
type FundTransferContext = FundTransferUiTemplate.FundTransferContext;

export type Context = FundTransferContext & {
  state: {
    selectedFromAccountId: number;
    selectedToAccountId: number;
    fromAccountCurrency: string;
    toAccountCurrency: string;
    postingPeriodId: number;
    entityId: number;
    fromAccountId: number;
    toAccountId: number;
    transferAmount: Decimal;
    transferDate: Date;
    files?: { fileId: string; fileName: string }[];
    /**
     * The untracked state can be used to store caches without triggering
     * more template updates.
     **/
    _untracked: {
      exchangeRatesCache: Map<string, Any>;
    };
  };
};

type CardData = {
  picture: string;
  pictureAlt: string;
  title: string;
  description: string;
  summary: UIExecutionContext.FieldValue;
};

UILifecycleHooks.onInit((context: Context) => {
  adjustFundTransferAmounts(context);
  setRequiredFieldsOnInit(context as Any);
});

export function adjustFundTransferAmounts(context: Context) {
  const { data } = context;
  //Adjusting from and to account amount to display

  function updateIfReady() {
    if (data.fromAccount && data.toAccount) {
      setUIModelInSharedState(context, 'fromAccount', 'amount', {
        amount: new Decimal(data.fromAccount?.amount?.amount)
          .absoluteValue()
          .minus(new Decimal(data.fromAccount?.bankFee || 0)),
        currency: data.fromAccount?.amount?.currency,
      });
      setUIModelInSharedState(context, 'toAccount', 'amount', {
        amount: new Decimal(data.toAccount?.amount?.amount)
          .absoluteValue()
          .plus(new Decimal(data.toAccount?.bankFee || 0)),
        currency: data.toAccount?.amount?.currency,
      });
    } else {
      // Try again soon
      setTimeout(updateIfReady, 100);
    }
  }

  // Start the check process
  updateIfReady();
}

export function getBankTransactionQuery(context: Context) {
  const { state } = context;
  const ftHeaderId = state.id;
  const associationName =
    'BankTransaction-BankTransactionMatch.MatchSourceFinancialDocument-BankTransactionMatch';
  return {
    where: {
      [`${associationName}.sfdId`]: ftHeaderId,
      [`${associationName}.sfdType`]: 'fundTransfer',
      matchStatus: EvstBankTransactionMatchStatus.Matched,
    },
  };
}

export function getPeriodQuery(context: Context) {
  const { data } = context;
  if (isViewMode(context)) {
    const { fundTransfer } = data ?? {};
    const { transferDate, entityId } = fundTransfer ?? {};
    if (transferDate) {
      const convertedDate =
        typeof transferDate === 'string'
          ? PlainDate.from(transferDate)
          : transferDate;

      return {
        entityId,
        postingDate: new Date(convertedDate.toISO()),
        forPosting: false,
        lockEvaluation: {
          journalEntryTypes: [EvstJournalEntryType.FundTransfer],
        },
      };
    }
  }
}

export function getAccountName(context: Context, { data }) {
  const { accountName, 'FundTransferLine-Account': account } = data ?? {};
  return accountName?.includes('—')
    ? accountName
    : `${account.accountNumber} — ${accountName}`;
}

export function isEditMode(context: Context) {
  const { state } = context;
  return state?.mode === 'edit';
}

export function isMatched(context: Context) {
  const { data } = context;
  const mt = data?.matchedTransactions ?? [];
  return mt?.length === 2;
}

export function isEditButtonDisabled(context: Context) {
  return isMatched(context);
}

export function isVisible(context: Context) {
  const { state, data } = context;
  const mt = data?.matchedTransactions ?? [];
  return mt?.length > 0 && state?.mode === 'view';
}

export function getTransactionData(context: Context): CardData[] {
  const { data, helpers } = context;
  const { matchedTransactions } = data;
  if (matchedTransactions && matchedTransactions.length > 0) {
    return matchedTransactions.map((bt) => {
      const transactionDate = helpers.parseValue({
        value: new Date(toPlainDate(bt.externalCreationDate).toISO()),
        parseAs: 'date',
      });
      const transactionNumber = bt.bankTransactionNumber.slice(3);

      let picture = '';
      if (
        bt['BankTransactionNew-BankAccount']?.[
          'BankAccount-FinancialInstitution'
        ]?.logoUrl
      ) {
        const institutionLogoUrl =
          bt['BankTransactionNew-BankAccount']?.[
            'BankAccount-FinancialInstitution'
          ]?.logoUrl;

        // Format the URL appropriately based on whether it's already a URL or base64 data
        picture =
          institutionLogoUrl.startsWith('http://') ||
          institutionLogoUrl.startsWith('https://')
            ? institutionLogoUrl
            : `data:image/png;base64,${institutionLogoUrl}`;
      } else if (
        bt['BankTransactionNew-BankAccount']?.[
          'BankAccount-FinancialInstitution'
        ]?.logoId
      ) {
        const logoId =
          bt['BankTransactionNew-BankAccount']?.[
            'BankAccount-FinancialInstitution'
          ]?.logoId;
        picture = `/api/app/storage/${logoId}`;
      }
      return {
        picture,
        pictureAlt: bt.accountName,
        title: `${bt.accountName} #${transactionNumber} on ${transactionDate} in ${bt.currency}`,
        description: bt.externalDescription,
        summary: helpers.parseValue({
          value: new Decimal(bt.amount?.amount).toFixed(2),
          format: bt.currency,
          parseAs: 'currency',
        }),
      };
    });
  }
  return [];
}

export function getCardData(context: Context) {
  const selectedTransactions = getTransactionData(context);
  return [...selectedTransactions];
}

// We need this fallback value as for older FTs toEntity is missing in other tenants
export async function getToEntityId(context: Context) {
  const { data } = context;
  const { fundTransfer } = data ?? {};
  return fundTransfer?.toEntityId ?? fundTransfer?.entityId;
}

export function isEditingAllowed(context: Context) {
  const { data } = context;
  const { matchedTransactions = [] } = data;
  return isEditMode(context) && matchedTransactions.length < 2;
}

export function isEditVisible(context: Context) {
  const { data } = context;
  const { matchedTransactions = [], fundTransfer } = data;
  const isFTnotVoided = fundTransfer.isVoided !== true;
  return isViewMode(context) && matchedTransactions.length < 2 && isFTnotVoided;
}

// Rules as per https://github.com/everestsystems/content/issues/13419
export function isDisabled(context: Context, type: string) {
  const { data } = context;
  const { fundTransfer, matchedTransactions = [] } = data ?? {};

  const hasMatchedTransaction = (accountName: string | undefined) => {
    if (!accountName) {
      return false;
    }
    return Boolean(
      matchedTransactions.find((mt) => mt.accountName === accountName)?.id
    );
  };

  const fromAccount = fundTransfer?.fromAccountName;
  const toAccount = fundTransfer?.toAccountName;

  switch (type) {
    case 'fromAccount': {
      return hasMatchedTransaction(fromAccount);
    }

    case 'toAccount': {
      return hasMatchedTransaction(toAccount);
    }

    case 'transferAmount':
    case 'transferDate': {
      return matchedTransactions.length > 0;
    }

    case 'fromAccountBankFee': {
      return (
        hasMatchedTransaction(fromAccount) ||
        isBankFeeEditDisabled(context as Any)
      );
    }

    case 'toAccountBankFee': {
      return (
        hasMatchedTransaction(toAccount) ||
        isBankFeeEditDisabled(context as Any)
      );
    }

    default: {
      return false;
    }
  }
}

export function isViewMode(context: Context) {
  const { state } = context;
  return state?.mode === 'view';
}

export function isSourceVisible(context: Context) {
  return !!context.data?.fundTransfer?.origin;
}

export function isMoreActionsVisible(context: Context) {
  const { data } = context;
  const { postingPeriod } = data ?? {};
  const { locked = false, closed = false } =
    (postingPeriod as unknown as {
      closed: boolean;
      locked: boolean;
    }) ?? {};
  const isAllowed = !locked && !closed;
  return isViewMode(context) && isAllowed;
}

export function isSaveDisabled(context: Context) {
  const { state } = context;
  return !state.canBePosted;
}

export function getTitle({ data }: Context) {
  return `${data?.fundTransfer?.fundTransferHeaderNumber}: ${
    data?.fundTransfer?.fundTransferName ?? 'Fund Transfer'
  }`;
}

export function getJEHeaderNumber(context: Context) {
  const { data } = context;
  const JE = data?.fundTransfer?.[
    'JournalEntryHeader-FinancialDocument'
  ]?.[0] as Any;
  return JE?.journalEntryNumber;
}

export function getJELink(context: Context) {
  const { data } = context;
  const journalEntryHeaderId = data?.fundTransfer?.journalEntryHeaderId;

  return journalEntryHeaderId
    ? {
        to: '@uicode:journalEntry',
        qs: {
          id: journalEntryHeaderId,
        },
      }
    : null;
}

export function isAttachmentBlockVisible({ data, state }: Context) {
  if (state?.mode !== 'view') {
    return true;
  }
  return data?.fundTransferAttachments?.length > 0;
}

export function getMoreActions(context: Context) {
  const { isFetchingUiModelData, state, data, helpers } = context;
  const { entities = [] } = data ?? {};

  if (isFetchingUiModelData) {
    return [];
  }

  if (isViewMode(context)) {
    return [
      {
        label: '{{fundTransfer.edit}}',
        onClick: async () => {
          const {
            transferDate,
            fromAccountCurrency,
            toAccountCurrency,
            entityId,
            fromAccountId,
            toAccountId,
          } = getUIModelFromSharedState(context, 'fundTransfer');

          await fetchPostingPeriod(context as Any, transferDate);

          const entity = entities?.find((entity) => entity.id === entityId);

          state.coaId = entity?.['EntityBook-Entity']?.[0]?.chartOfAccountsId;
          state.fromAccountCurrency = fromAccountCurrency;
          state.toAccountCurrency = toAccountCurrency;
          state.selectedFromAccountId = fromAccountId;
          state.selectedToAccountId = toAccountId;
          state.mode = 'edit';
        },
        visible: isEditVisible(context),
        disabled: isEditButtonDisabled(context),
      },
      {
        label: '{{fundTransfer.voidFundTransfer}}',
        onClick: async () => {
          const path = helpers.getTemplateLink({
            templateName: 'everest.fin.integration.bank/uinext/voidReasonModal',
            templateState: {},
          });

          helpers.openModal({
            size: 'xsmall',
            title: '{{fundTransfer.confirm.void}}',
            template: path,
            onModalSubmit: async (payload) =>
              await submitVoidFundTransfer(context, payload.reason),
          });
        },
        disabled: data?.fundTransfer?.isVoided === true,
      },
      {
        label: '{{fundTransfer.initiatePayment}}',
        onClick: async () => {
          initPayment(context);
        },
        visible: [
          EvstFundTransferPaymentStatus.NoStatus,
          EvstFundTransferPaymentStatus.Recorded,
        ].includes(data?.fundTransfer?.fundTransferPaymentStatus),
      },
      {
        label: '{{fundTransfer.checkPaymentStatus}}',
        visible: [
          EvstFundTransferPaymentStatus.InProgress,
          EvstFundTransferPaymentStatus.Failed,
          EvstFundTransferPaymentStatus.Unknown,
        ].includes(data?.fundTransfer?.fundTransferPaymentStatus),
        onClick: async () => {
          await BankPaymentUI.fetchFundTransferPaymentStatus(context, {
            fundTransferHeaderId: data?.fundTransfer?.id,
          }).run('bankPayment');
          await context.actions.refetchUiModelData();
        },
      },
    ];
  }
}

export async function submitVoidFundTransfer(context: Context, reason: string) {
  const { actions, helpers } = context;

  helpers.showNotificationMessage({
    type: 'loading',
    key: 'fundTransfer',
    message: '{{fundTransfer.load.void}}',
    duration: 200,
  });
  const fundTransfer = getUIModelFromSharedState(context, 'fundTransfer');
  const response = (await FundTransferHeaderUI.voidFundTransfer(context, {
    fundTransfers: [fundTransfer],
    voidReason: reason,
  }).run('fundTransfer')) as unknown as {
    fundTransfer: {
      id: number;
      isVoided: boolean;
    };
  };

  if (response?.fundTransfer?.isVoided === true) {
    helpers.showNotificationMessage({
      type: 'success',
      key: 'fundTransfer',
      message: '{{fundTransfer.transaction.success.void}}',
      duration: 2,
    });
  } else {
    helpers.showNotificationMessage({
      key: 'fundTransfer',
      type: 'warning',
      message: `{{fundTransfer.transaction.fail.void}}`,
      duration: 3,
    });
  }
  await actions.refetchUiModelData();
}

export async function saveAction(context: Context) {
  await saveFundTransfer(context);
}

export function cancelAction(context: Context) {
  const { state, sharedState } = context;
  state.mode = 'view';
  sharedState.reset();
  adjustFundTransferAmounts(context);
}

export function getPostingPeriodText({ data, state }: Context) {
  return state?.postingPeriodName ?? data?.fundTransfer?.postingPeriod;
}

export async function handleAttachmentSuccess(
  context: Context,
  fileId: string,
  file: {
    name: string;
    type: string;
  }
) {
  const { state } = context;
  state.files ??= [];
  state.files.push({ fileId: fileId, fileName: file.name });
}

export async function handleAttachmentRemove(context: Context, fileId: string) {
  const { state } = context;
  state.files = (state.files ?? []).filter((file) => file.fileId !== fileId);
}

function resetAttachments({ data, state }: Context) {
  state.files = (data.fundTransferAttachments ?? []).map((_) => ({
    fileId: _.fileId,
    fileName: _.fileName,
  }));
}

export function getAttachments(context: Context) {
  const { state, data } = context;
  if (state.mode === 'view') {
    return data.fundTransferAttachments;
  }
  if (!state.files && data.fundTransferAttachments) {
    resetAttachments(context);
  }
  return state.files;
}

export async function saveFundTransfer(context: Context) {
  const { sections, state, form, actions, sharedState } = context;
  const fundTransferHeader = getUIModelFromSharedState(context, 'fundTransfer');
  await form.handleSubmit({});

  delete fundTransferHeader['JournalEntryHeader-FinancialDocument'];
  const fundTransferLines = getAccountData(context as Any);

  // Check if amounts are equal for same currency
  if (
    state?.isSameCurrency &&
    !fundTransferLines[0].amount?.amount
      .minus(fundTransferLines[0].bankFee || 0)
      .absoluteValue()
      .equals(
        fundTransferLines[1].amount?.amount.plus(
          fundTransferLines[1].bankFee.absoluteValue() || 0
        )
      )
  ) {
    showMessage(context as Any, 'error', '{{fundTransfer.unequalAmounts}}', 5);
    return null;
  }
  const amount = fundTransferLines[0].amount?.amount || 0;
  const bankFee = fundTransferLines[0].bankFee || 0;

  fundTransferHeader.transferAmount = {
    amount: new Decimal(amount).minus(bankFee).absoluteValue(),
    currency: fundTransferLines[0].amount.currency,
  };
  const fundTransferAttachments = sections.transferAttachments
    ?.getUploadFiles()
    ?.map((file) => {
      return {
        fileId: file.fileId,
        fileName: file.fileName,
        ...(file.id && { id: file.id }),
      };
    });
  delete fundTransferHeader._nodeReference;
  delete fundTransferLines[0]['FundTransferLine-Account'];
  delete fundTransferLines[1]['FundTransferLine-Account'];
  delete fundTransferLines[0]['_nodeReference'];
  delete fundTransferLines[1]['_nodeReference'];

  showMessage(context as Any, 'loading', 'Updating Fund Transfer', 100);
  const response = await FundTransferHeaderUI.upsertFundTransfer(context, {
    fundTransferHeader,
    fundTransferLines,
    fundTransferAttachments,
  }).run('fundTransfer');

  if (response.error) {
    showMessage(context as Any, 'error', 'Updation Failed', 5);
    sharedState.reset();
  } else {
    showMessage(
      context as Any,
      'success',
      'Fund Transfer Updated Successfully',
      5
    );
  }
  state.mode = 'view';
  await actions.refetchUiModelData({ discardChanges: true });
  adjustFundTransferAmounts(context);
}

export function showIntercompanyDetails(context: Context) {
  const entityId = getUIModelFromSharedState(
    context,
    'fundTransfer',
    'entityId'
  );
  const toEntityId = getUIModelFromSharedState(
    context,
    'fundTransfer',
    'toEntityId'
  );

  return toEntityId && entityId !== toEntityId;
}

export function isIntercompanyDisabled(context: Context) {
  const entityId = getUIModelFromSharedState(
    context,
    'fundTransfer',
    'entityId'
  );
  const toEntityId = getUIModelFromSharedState(
    context,
    'fundTransfer',
    'toEntityId'
  );

  return entityId === toEntityId;
}

export function voidStamp(context: Context) {
  const { data } = context;
  return data.fundTransfer?.isVoided === true
    ? '{{fundTransfer.void}}'.toUpperCase()
    : null;
}

export function isRequiredForIntercompany(context: Context) {
  return {
    validate: (value) => {
      const isInterCompany = getUIModelFromSharedState(
        context,
        'fundTransfer',
        'isIntercompany'
      );
      return isInterCompany ? !!value || '{{required}} *' : null;
    },
  };
}

export function initPayment(context: Context) {
  const fundTransferHeaderIds = [context?.data?.fundTransfer?.id].filter(
    Boolean
  );
  const entityId = context?.data?.fundTransfer?.entityId;
  context.helpers.navigate({
    to: `/templates/everest.fin.integration.bank/uinext/payment/fundTransferPayment`,
    initialState: {
      mode: 'create',
      fundTransferHeaderIds,
      entityId,
    },
  });
}

export function recordPayment(context: Context) {
  const fundTransferHeaderIds = [context?.data?.fundTransfer?.id].filter(
    Boolean
  );
  const entityId = context?.data?.fundTransfer?.entityId;

  context.helpers.navigate({
    to: `/templates/everest.fin.integration.bank/uinext/payment/fundTransferPayment`,
    initialState: {
      mode: 'create',
      fundTransferHeaderIds,
      entityId,
    },
  });
}
export function navigateToPayment(context: Context) {
  const fundTransferHeaderIds = [context?.data?.fundTransfer?.id].filter(
    Boolean
  );
  const entityId = context?.data?.fundTransfer?.entityId;
  context.helpers.navigate({
    to: `/templates/everest.fin.integration.bank/uinext/payment/fundTransferPayment`,
    initialState: {
      mode: 'view',
      fundTransferHeaderIds,
      entityId,
    },
  });
}

export function getPaymentLinesQuery(context: Context) {
  const fundTransferHeaderId = context?.data?.fundTransfer?.id;
  if (!fundTransferHeaderId) {
    return;
  }
  return {
    where: {
      sourcePaymentDocumentType: 'FundTransferHeader',
      sourcePaymentDocumentId: fundTransferHeaderId,
    },
  };
}

export function getPaymentHeaderQuery(context: Context) {
  const paymentLines = context?.data?.paymentLines;
  if (!paymentLines || !paymentLines?.length) {
    return;
  }
  return {
    where: {
      id: {
        $in: paymentLines
          .map((i) => i?.outboundPaymentHeaderId)
          .filter(Boolean),
      },
    },
  };
}

export function isPaymentTableVisible(context: Context) {
  const paymentLines = context?.data?.paymentLines;
  return paymentLines && paymentLines?.length;
}

export function isPaymentButtonsVisible(context: Context) {
  const paymentLines = context?.data?.paymentLines;
  return !(paymentLines && paymentLines?.length);
}

export function getAccountQuery(
  context: Context,
  accountType: EvstFundTransferLineAccountType
) {
  const fdLines = context?.data?.fundTransferLines;
  if (!fdLines || !fdLines?.length) {
    return;
  }
  return {
    where: {
      id: {
        $in: fdLines
          .filter((line) => line?.accountType === accountType)
          .map((line) => line.id)
          .filter(Boolean),
      },
    },
  };
}
