{"version": 2, "uicontroller": ["fundTransfer.uicontroller.ts", "everest.fin.integration.bank/uinext/fundTransfer/utilities.uicontroller.ts"], "uimodel": {"state": {"mode": "view", "canBePosted": true, "featureToggles": {"transaction": true}}, "nodes": {"entities": {"type": "list", "modelId": "everest.base/EntityModel.Entity", "queryActiveEntities": {"query": {"where": {}, "orderBy": ["entityName"]}, "fieldList": ["entityName", "id", "currency", "EntityBook-Entity.chartOfAccountsId"]}}, "bankAccounts": {"type": "list", "query": {"where": {"accountSubType": {"$in": ["bank", "creditCard"]}, "accountStatus": "enabled"}, "orderBy": [{"field": "accountName", "ordering": "asc"}, {"field": "accountSubType", "ordering": "asc"}]}, "modelId": "everest.fin.accounting/AccountModel.Account", "fieldList": ["accountName", "accountNumber", "chartOfAccountsId", "entityId", "currency", "id", "accountSubType", "Account-Entity.entityName", "Account-Entity.currency", "BankAccount-Account.financialInstitutionId"]}, "postingPeriod": {"type": "struct", "modelId": "everest.fin.accounting/AccountingPeriodModel.AccountingPeriod", "findAccountingPeriod": "@controller:getPeriodQuery()"}, "fundTransfer": {"type": "struct", "modelId": "everest.fin.accounting/FundTransferHeaderModel.FundTransferHeader", "query": {"where": {"id": "@state:id"}}, "fieldList": ["id", "fundTransferHeaderNumber", "entityId", "entityName", "transferAmount", "fundTransferName", "transferDate", "description", "postingPeriodId", "isManualExchangeRate", "journalEntryHeaderId", "JournalEntryHeader-FinancialDocument.journalEntryNumber", "postingPeriod", "exchangeRate", "toAccountId", "fromAccountId", "fromAccountCurrency", "toAccount<PERSON><PERSON><PERSON>cy", "toAccountName", "isVoided", "fromAccountName", "toEntityId", "toEntityName", "intercompanyProcess", "transferReason", "isIntercompany", "intercompanyProcessId", "intercompanyTransactionalCurrency", "fundTransferPaymentStatus", "origin", "externalUrl"]}, "fundTransferLines": {"type": "list", "modelId": "everest.fin.accounting/FundTransferLineModel.FundTransferLine", "parent": "fundTransfer", "joinKey": "id-fundTransferHeaderId", "fieldList": ["id", "accountType", "accountId", "currency", "amount", "accountName", "bankFee", "fundTransferHeaderId", "entityName", "fundTransferBankFeeId", "FundTransferLine-Account.accountNumber"]}, "fundTransferAttachments": {"type": "list", "parent": "fundTransfer", "joinKey": "id-fundTransferHeaderId", "modelId": "everest.fin.accounting/FundTransferAttachmentModel.FundTransferAttachment"}, "matchedTransactions": {"type": "list", "query": "@controller:getBankTransactionQuery()", "modelId": "everest.fin.integration.bank/BankTransactionNewModel.BankTransactionNew", "fieldList": ["bankAccountId", "matchStatus", "matchingTransaction", "bankTransactionNumber", "accountName", "amount", "currency", "externalCreationDate", "externalDescription", "id", "BankTransactionNew-BankAccount.BankAccount-FinancialInstitution.logoId", "BankTransactionNew-BankAccount.BankAccount-FinancialInstitution.logoUrl"]}, "exchangeRate": {"type": "struct", "modelId": "everest.base/ExchangeRateModel.ExchangeRate", "fieldList": ["value"]}, "bankFeeConfiguration": {"type": "struct", "modelId": "everest.fin.accounting/BankFeeSettingsModel.BankFeeSettings", "query": "@controller:getbankFeeConfigQuery()", "fieldList": ["id", "coaId", "bankFeeAccountId", "bankFeeDepartmentId"]}, "intercompanyProcess": {"type": "list", "modelId": "everest.fin.accounting/IntercompanyProcessModel.IntercompanyProcess", "query": {}, "fieldList": ["id", "processName", "processDefinition"]}, "paymentHeaders": {"type": "list", "modelId": "everest.fin.expense/OutboundPaymentHeaderBaseModel.OutboundPaymentHeaderBase", "query": "@controller:getPaymentHeaderQuery()", "fieldList": ["id", "commonDescription", "totalPaid", "paymentDate", "paymentCurrency", "outboundPaymentHeaderNumber"]}, "paymentLines": {"type": "list", "modelId": "everest.fin.expense/OutboundPaymentItemBaseModel.OutboundPaymentItemBase", "query": "@controller:getPaymentLinesQuery()", "fieldList": ["id", "outboundPaymentHeaderId"]}, "bankPayment": {"type": "struct", "modelId": "everest.fin.integration.bank/BankPaymentModel.BankPayment", "fieldList": []}, "fromAccount": {"type": "struct", "modelId": "everest.fin.accounting/FundTransferLineModel.FundTransferLine", "query": "@controller:get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>('from')", "fieldList": ["id", "accountType", "accountId", "currency", "amount", "accountName", "bankFee", "fundTransferHeaderId", "entityName", "fundTransferBankFeeId", "FundTransferLine-Account.accountNumber"]}, "toAccount": {"type": "struct", "modelId": "everest.fin.accounting/FundTransferLineModel.FundTransferLine", "query": "@controller:get<PERSON><PERSON>unt<PERSON><PERSON><PERSON>('to')", "fieldList": ["id", "accountType", "accountId", "currency", "amount", "accountName", "bankFee", "fundTransferHeaderId", "entityName", "fundTransferBankFeeId", "FundTransferLine-Account.accountNumber"]}, "institutions": {"type": "list", "modelId": "everest.fin.accounting/FinancialInstitutionModel.FinancialInstitution", "query": {"where": {}}, "fieldList": []}}}, "uiview": {"templateType": "details", "config": {"stretch": true, "allowRefreshData": true, "autoRefreshData": true, "stamp": "@controller:voidStamp()"}, "title": "@controller:getTitle()", "i18n": "everest.fin.accounting/fundTransfer", "header": {"content": {"title": "@bindingController:(fundTransfer.fundTransferName, getTitle())", "description": "@binding:fundTransfer.description", "status": "@binding:fundTransfer.fundTransferPaymentStatus"}, "placeholders": {"title": "{{fundTransfer.title.singular}}", "description": "{{fundTransfer.addDescription}}"}, "size": "@props:headerConfig.size", "margin": "@props:headerConfig.margin", "editing": {"title": "@controller:isEditMode()", "description": "@controller:isEditMode()"}}, "sections": {"content": [{"component": "Cards", "title": "{{fundTransfer.matchedTo}}", "size": "12", "columns": "6", "isVisible": "@controller:isVisible()", "loadingQuantity": 1, "cardsSize": "medium", "data": "@controller:getCardData()"}, {"component": "FieldGroup", "size": "12", "editing": "@controller:isEditingAllowed()", "columns": "5", "customId": "fundTransferCreatePage", "type": "primary", "elements": [{"component": "Select", "label": "{{fundTransfer.from}}", "idProp": "id", "prefixProp": "accountNumber", "textProp": "accountName", "isDisabled": "@controller:isDisabled('fromAccount')", "value": "@binding:fundTransfer.fromAccountId", "text": "@binding:fundTransfer.fromAccountName", "list": "@controller:getBankAccountsList('fromAccount')", "onChange": "@controller:handleFromAccountChange", "allowClear": true, "placeholder": "{{fundTransfer.selectFromAccount}}"}, {"component": "Select", "label": "{{fundTransfer.to}}", "idProp": "id", "prefixProp": "accountNumber", "textProp": "accountName", "value": "@binding:fundTransfer.toAccountId", "isDisabled": "@controller:isDisabled('toAccount')", "text": "@binding:fundTransfer.toAccountName", "list": "@controller:getBankAccountsList('toAccount')", "onChange": "@controller:handleToAccountChange", "disabledValue": "@controller:getToAccountDisabledValue()", "allowClear": true, "placeholder": "{{fundTransfer.selectToAccount}}"}, {"component": "Select", "label": "{{fundTransfer.entity}}", "idProp": "id", "textProp": "entityName", "isDisabled": true, "visible": false, "value": "@binding:fundTransfer.entityId", "text": "@binding:fundTransfer.entityName", "onChange": "@controller:handleEntityChange", "list": "@binding:entities"}, {"component": "Select", "label": "{{fundTransfer.toEntity}}", "idProp": "id", "textProp": "entityName", "isDisabled": true, "visible": false, "value": "@bindingController:(fundTransfer.toEntityId, getToEntityId())", "text": "@binding:fundTransfer.toEntityName", "onChange": "@controller:handleToEntityChange", "list": "@binding:entities"}, {"label": "{{fundTransfer.date}}", "parseAs": "date", "format": "P", "value": "@binding:fundTransfer.transferDate", "onChange": "@controller:fetchPostingPeriod", "isDisabled": "@controller:isDisabled('transferDate')", "customFieldMessages": {"fieldError": "@state:periodErrorMsg"}, "placeholder": "{{fundTransfer.selectTransferDate}}"}, {"component": "Select", "isDisabled": true, "label": "{{fundTransfer.postingPeriod}}", "idProp": "value", "textProp": "text", "value": "@binding:fundTransfer.postingPeriodId", "text": "@controller:getPostingPeriodText()", "list": "@controller:getPostingPeriods()"}, {"label": "{{fundTransfer.fxRate}}", "value": "@binding:fundTransfer.exchangeRate", "placeholder": "{{fundTransfer.enterFxRate}}", "parseAs": "currency", "onBlur": "@controller:setIsManualFxRate", "isDisabled": true}, {"label": "{{fundTransfer.journalEntry}}", "value": "@controller:getJEHeaderNumber()", "link": "@controller:getJELink()", "isVisible": "@controller:isViewMode()"}, {"label": "{{fundTransfer.source}}", "value": "@binding:fundTransfer.origin", "externalLink": "@binding:fundTransfer.externalUrl", "isVisible": "@controller:isSourceVisible()"}, {"component": "Select", "label": "{{fundTransfer.intercompany}}", "idProp": "value", "textProp": "label", "value": "@binding:fundTransfer.isIntercompany", "visible": "@controller:showIntercompanyDetails()", "isDisabled": true, "list": [{"label": "Yes", "value": true}, {"label": "No", "value": false}]}, {"component": "Select", "label": "{{fundTransfer.intercompanyProcess}}", "idProp": "id", "textProp": "processName", "value": "@binding:fundTransfer.intercompanyProcessId", "list": "@binding:intercompanyProcess", "rules": "@controller:isRequiredForIntercompany()", "visible": "@controller:showIntercompanyDetails()"}, {"component": "Select", "label": "{{fundTransfer.transferReason}}", "idProp": "value", "textProp": "label", "value": "@binding:fundTransfer.transferReason", "rules": "@controller:isRequiredForIntercompany()", "visible": "@controller:showIntercompanyDetails()"}, {"component": "Select", "label": "{{fundTransfer.intercompanyTransactionalCurrency}}", "idProp": "currency", "textProp": "currency", "value": "@binding:fundTransfer.intercompanyTransactionalCurrency", "list": "@controller:getIntercompanyTransactionalCurrency()", "rules": "@controller:isRequiredForIntercompany()", "visible": "@controller:showIntercompanyDetails()"}]}, {"component": "SectionGroup", "section": {"grid": {"size": "12"}}, "props": {"sections": [{"component": "ActionGroup", "customId": "fromAccountActionGroup", "section": {"grid": {"size": "6"}, "title": "@controller:getAccountTitle('fromAccount')", "editing": true}, "props": {"variant": "default", "editing": true, "versionNew": true, "descriptionPosition": "rightSide", "buttonActionsPosition": "right", "lines": [{"avatar": {"src": "@controller:get<PERSON><PERSON><PERSON>('fromAccount')", "text": "--"}, "label": "@controller:getAccountAmount('fromAccount')", "description": "@controller:getAccountDescriptionLabel('fromAccount')"}, {"label": "{{fundTransfer.amountSent}}", "description": "@controller:getAccountCurrencyDescription('fromAccount')", "avatar": false, "fieldProps": {"component": "InputNumber", "value": "@binding:fromAccount.amount", "isEditing": "@controller:isEditMode()", "isDisabled": "@controller:isDisabled('fromAccount')", "placeholder": "0.00", "compositeConfig": {"amount": {"placeholder": "0.00", "onBlur": "@controller:triggerToAccountAmountChange"}}}}, {"label": "{{fundTransfer.sendingBankFee}}", "description": "@controller:getAccountCurrencyDescription('fromAccount')", "avatar": false, "fieldProps": {"component": "InputNumber", "value": "@binding:fromAccount.bankFee", "_type": "amount", "_parseAs": "currency", "placeholder": "0.00", "isEditing": "@controller:isEditMode()", "isDisabled": "@controller:isDisabled('fromAccountBankFee')", "onBlur": "@controller:setDefaultFxRate", "tooltip": "@controller:getTooltipFor('bankFee')"}}, {"avatar": false, "visible": false, "field": "accountName"}, {"avatar": false, "visible": false, "field": "currency"}]}}, {"component": "ActionGroup", "customId": "toAccountActionGroup", "section": {"grid": {"size": "6"}, "title": "@controller:getAccountTitle('toAccount')", "editing": true}, "props": {"variant": "default", "editing": true, "versionNew": true, "descriptionPosition": "rightSide", "buttonActionsPosition": "right", "lines": [{"avatar": {"src": "@controller:get<PERSON><PERSON><PERSON>('toAccount')", "text": "--"}, "label": "@controller:getAccountAmount('toAccount')", "description": "@controller:getAccountDescriptionLabel('toAccount')"}, {"label": "{{fundTransfer.amountReceived}}", "description": "@controller:getAccountCurrencyDescription('toAccount')", "avatar": false, "fieldProps": {"component": "InputNumber", "value": "@binding:toAccount.amount", "isEditing": "@controller:isEditMode()", "isDisabled": "@controller:isDisabled('toAccount')", "placeholder": "0.00", "compositeConfig": {"amount": {"placeholder": "0.00", "onBlur": "@controller:setDefaultFxRate"}}}}, {"label": "{{fundTransfer.receivingBankFee}}", "field": "bankFee", "description": "@controller:getAccountCurrencyDescription('toAccount')", "avatar": false, "fieldProps": {"component": "InputNumber", "value": "@binding:toAccount.bankFee", "_type": "amount", "_parseAs": "currency", "placeholder": "0.00", "isEditing": "@controller:isEditMode()", "isDisabled": "@controller:isDisabled('toAccountBankFee')", "onBlur": "@controller:setDefaultFxRate", "tooltip": "@controller:getTooltipFor('bankFee')"}}, {"avatar": false, "visible": false, "field": "accountName"}, {"avatar": false, "visible": false, "field": "currency"}]}}]}}, {"component": "Table", "size": "9", "customId": "fundtransferpayments", "title": "Payments", "variant": "white-borderless", "pagination": false, "hideFilters": true, "fullWidth": false, "isVisible": "@controller:isPaymentTableVisible()", "columns": [{"headerName": "ID", "field": "outboundPaymentHeaderNumber", "sortable": true, "fieldProps": {"fontWeight": "bold"}, "onCellClicked": "@controller:navigateToPayment"}, {"headerName": "Description", "field": "commonDescription"}, {"headerName": "Payment Date", "field": "paymentDate", "fieldProps": {"parseAs": "date"}}, {"headerName": "<PERSON><PERSON><PERSON><PERSON>", "field": "paymentCurrency"}, {"headerName": "Amount", "field": "totalPaid"}], "data": "@binding:paymentHeaders"}, {"component": "Upload", "visible": "@controller:isAttachmentBlockVisible()", "section": {"title": "{{fundTransfer.attachments}}", "grid": {"size": "4"}, "customId": "transferAttachments"}, "props": {"name": "transferAttachments", "version": 2, "everestPackage": "everest.fin.accounting", "multiple": true, "disabled": false, "isEditing": "@controller:isEditMode()", "formattedAcceptFileTypes": ".pdf,.tiff,.jpeg,.png,.jpg", "uploadHandler": "postgres", "variant": "ButtonView", "acceptableMimeTypes": ["application/pdf", "image/tiff", "image/jpeg", "image/png", "image/jpg"], "maxSizeInMB": 5, "attachmentFiles": "@controller:getAttachments()", "onSuccess": "@controller:handleAttachmentSuccess", "onRemove": "@controller:handleAttachmentRemove"}}]}, "actions": {"direction": "vertical", "content": [{"variant": "secondary", "label": "{{fundTransfer.actions}}", "actions": "@controller:getMoreActions()", "visible": "@controller:isMoreActionsVisible()"}, {"variant": "primary", "label": "{{fundTransfer.save}}", "onClick": "@controller:saveAction", "visible": "@controller:isEditMode()", "disabled": "@controller:isSaveDisabled()"}, {"variant": "secondary", "label": "{{fundTransfer.cancel}}", "onClick": "@controller:cancelAction", "visible": "@controller:isEditMode()"}]}}}