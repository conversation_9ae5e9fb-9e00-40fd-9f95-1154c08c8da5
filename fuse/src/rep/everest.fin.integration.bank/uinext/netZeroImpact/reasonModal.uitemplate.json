{"version": 2, "uicontroller": "reasonModal.uicontroller.ts", "uimodel": {"nodes": {}}, "uiview": {"templateType": "generic", "autoRefreshData": true, "i18n": "everest.fin.accounting/accounting", "actions": [{"label": "{{journalEntry.cancelButton}}", "variant": "secondary", "align": "right", "onClick": "@controller:closeVoidReasonModal"}, {"label": "Create", "variant": "primary", "align": "right", "onClick": "@controller:returnVoidReason"}], "sections": [{"component": "FieldGroup", "customId": "descriptionBlock", "type": "secondary", "size": "12", "isEditing": true, "elements": [{"component": "Input", "multiline": true, "label": "Reason for creation", "name": "editReason", "onChange": "@controller:updateReasonInput"}]}]}}