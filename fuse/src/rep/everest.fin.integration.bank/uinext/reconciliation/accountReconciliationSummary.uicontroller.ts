import type { PlainDate } from '@everestsystems/datetime';
import type { EvstID } from '@pkg/everest.appserver/types/primitives/ID';
import type { AccountReconciliationSummaryUiTemplate } from '@pkg/everest.fin.integration.bank/types/uiTemplates/uinext/reconciliation/accountReconciliationSummary.ui';
import type { DateTimeFormatOptions } from 'luxon';
import { DateTime } from 'luxon';

type Context =
  AccountReconciliationSummaryUiTemplate.AccountReconciliationSummaryContext;

export function exportReconciliation(
  context: Context,
  tableId: string,
  exportName: string
) {
  const { sections, data } = context;
  const R2R_STYLE_ID = 'r2rHeader';

  const reconDetails = data?.reconciliationDetails;
  const entityName = reconDetails?.entityName || '';
  const accountNumber = reconDetails?.Account?.accountNumber || '';
  const accountName = reconDetails?.Account?.accountName || '';
  const endingPeriod = reconDetails?.statementEndDate || '';
  const reconciledBy = reconDetails?.ReconciledBy?.fullName || '';
  const reconciledOn = formatDate(reconDetails?.reconciledOn) || '';
  const columnsLength =
    sections[tableId]?.getAllDisplayedColumns()?.length - 1 || 0;

  const createStyledCell = (value: string) => ({
    data: { value, type: 'String' },
    mergeAcross: columnsLength,
    styleId: R2R_STYLE_ID,
  });

  const createEmptyCell = () => ({
    data: { value: '', type: 'String' },
    mergeAcross: columnsLength,
  });

  const headerContent = [
    {
      cells: [createStyledCell(entityName)],
    },
    {
      cells: [createStyledCell(`${accountNumber} - ${accountName}`)],
    },
    {
      cells: [createStyledCell(`Period Ending ${formatDate(endingPeriod)}`)],
    },
    {
      cells: [
        createStyledCell(`Reconciled by ${reconciledBy} on ${reconciledOn}`),
      ],
    },
    {
      cells: [createEmptyCell()],
    },
  ];

  return () => {
    sections[tableId]?.exportDataAsExcel({
      fileName: exportName,
      sheetName: exportName,
      columnWidth: 250,
      prependContent: headerContent,
      processCellCallback: (params) => {
        if (params.value === undefined) {
          return ` `;
        }

        if (params.value?.includes('bankSummary')) {
          return 'Bank Summary';
        } else if (params.value?.includes('glSummary')) {
          return 'GL Summary';
        } else {
          return params.value;
        }
      },
    });
  };
}
export function isSummaryRow(
  _: unknown,
  { rowData }: { rowData: Record<string, unknown> }
) {
  return rowData?.isSummary;
}

// #region Attachments

export function getAccountReconciliationID(context: Context): EvstID {
  const { data } = context;
  return data?.reconciliationDetails?.id;
}
export function getAttachments(context: Context) {
  const { data, state } = context;
  return state?.form?.['attachments'] || data?.attachments;
}
function formatDate(
  date?: string | PlainDate,
  formatOpts: DateTimeFormatOptions = DateTime.DATE_FULL
) {
  if (!date) {
    return '';
  }
  return DateTime.fromISO(date.toString(), {
    zone: 'UTC',
  }).toLocaleString(formatOpts);
}
