// @i18n:everest.fin.expense/payment
import type { PlainDate } from '@everestsystems/datetime';
import { Decimal as LibDecimal } from '@everestsystems/decimal';
import {
  getUIModelFromSharedState,
  setUIModelInSharedState,
} from '@pkg/everest.base/public/utils/ui/sharedStateUtils.ui';
import type { EvstCurrencyAmount } from '@pkg/everest.base/types/composites/CurrencyAmount';
import { toPlainDate } from '@pkg/everest.fin.accounting/public/utils/Date/date';
import type { AccountUI } from '@pkg/everest.fin.accounting/types/Account.ui';
import type { AccountingPeriodUI } from '@pkg/everest.fin.accounting/types/AccountingPeriod.ui';
import { EvstJournalEntryType } from '@pkg/everest.fin.accounting/types/enums/JournalEntryType';
import { EvstPaymentStatus } from '@pkg/everest.fin.base/types/enums/PaymentStatus';
import {
  getSourcePaymentDocumentPath,
  SourcePaymentDocumentType,
} from '@pkg/everest.fin.expense/public/outboundPayment';
import type { ExpenseReportBaseUI } from '@pkg/everest.fin.expense/types/ExpenseReportBase.ui';
import { OutboundPaymentHeaderBaseUI } from '@pkg/everest.fin.expense/types/OutboundPaymentHeaderBase.ui';
import type { VendorBillHeaderBaseUI } from '@pkg/everest.fin.expense/types/VendorBillHeaderBase.ui';
import type { PaymentObjectWithReceiverAccount } from '@pkg/everest.fin.integration.bank/actions/common/payment';
import type { IntegrationUI } from '@pkg/everest.fin.integration.bank/types/Integration.ui';
import type { BankPaymentUiTemplate } from '@pkg/everest.fin.integration.bank/types/uiTemplates/uinext/payment/bankPayment.ui';

export declare class Decimal extends LibDecimal {}

export type OutboundPaymentHeaderBaseWithAssociation =
  OutboundPaymentHeaderBaseUI.OutboundPaymentHeaderBase & {
    'JournalEntryHeader-FinancialDocument': {
      posted: boolean;
      journalEntryNumber: string;
      id: number;
    }[];
  };

type PaymentType = 'single' | 'bulk' | 'manual';

type ActionCreatePaymentFields = {
  paymentType: PaymentType;
  type: SourcePaymentDocumentType;
  accountId: number;
  paymentLines: Partial<
    Omit<PaymentObjectWithReceiverAccount, 'outboundPaymentHeaderId'>
  >[];
  paymentHeader: Partial<OutboundPaymentHeaderBaseUI.OutboundPaymentHeaderBase>;
  integration: string;
  senderAccountId: string;
  isFuturePaymentDate: boolean;
  batchBookingPreferred: boolean;
  options: Record<string, any>;
  fieldList?: Array<
    keyof OutboundPaymentHeaderBaseUI.OutboundPaymentHeaderBase
  >;
};

export type Context = BankPaymentUiTemplate.OverrideBankPaymentData<{
  postingPeriod: { locked: boolean; closed: boolean };
}> & {
  state: {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    hashset: Map<number | string, any>;
    exchangeRate: number;
    ids: string;
    editableFields: string[];
    accountSelectedCurrency: string;
    once: boolean;
    param: Record<string, unknown>;
    periodName: string;
    canBePosted: boolean;
    batchPayment: boolean;
    defaultDescriptionValueSet: boolean;
  };
};

export class StateManager {
  constructor(private readonly context: Context) {}

  public isStateModeBill() {
    const { state } = this.context;

    if (state?.mode) {
      return state.mode === 'bill';
    }

    return false;
  }

  public isStateModeExpenseReport() {
    const { state } = this.context;

    if (state?.mode) {
      return state.mode === 'expenseReport';
    }

    return false;
  }

  public isStateModeBillOrPaymentOrEdit() {
    const { state } = this.context;

    if (state?.mode) {
      return (
        state.mode === 'bill' ||
        state.mode === 'payment' ||
        state.mode === 'edit'
      );
    }

    return false;
  }

  public togglePayButtonState(toggle: boolean) {
    const { state } = this.context;

    state.buttonDisabled = toggle;
  }

  public isBatch() {
    return this.context.state.batchPayment;
  }

  public isStateModePaymentOrEdit() {
    const { state } = this.context;

    if (state?.mode) {
      return state.mode === 'payment' || state.mode === 'edit';
    }

    return false;
  }

  public isStateModePayment() {
    const { state } = this.context;

    if (state?.mode) {
      return state.mode === 'payment';
    }

    return false;
  }

  public isStateModeBillOrExpenseReportOrEdit() {
    const { state } = this.context;

    if (state?.mode) {
      return (
        state.mode === 'bill' ||
        state.mode === 'expenseReport' ||
        state.mode === 'edit'
      );
    }

    return false;
  }

  public isFieldDisabled(fieldName: string) {
    const { state } = this.context;

    if (this.isStateModeEdit()) {
      // Only allowed fields are editable
      return !state?.editableFields?.includes(fieldName);
    }
    return false;
  }

  public getAction() {
    if (this.isStateModeEdit()) {
      return 'update';
    }
    return 'createPayment';
  }

  public isStateModeBillOrExpenseReport() {
    const { state } = this.context;

    if (state?.mode) {
      return state.mode === 'bill' || state.mode === 'expenseReport';
    }

    return false;
  }

  public isStateModeEdit() {
    const { state } = this.context;

    if (state?.mode) {
      return state.mode === 'edit';
    }

    return false;
  }

  /// end of the class : StateManager
}

export class Data {
  constructor(
    private readonly context: Context,
    private readonly stateManager: StateManager,
    private readonly util: Util
  ) {}

  // todo use authorizedIntegration
  public getIntegrationName(): string | undefined {
    const accountId = getUIModelFromSharedState(
      this.context,
      'outboundPaymentHeader',
      'accountId'
    );

    const bankAccounts = getUIModelFromSharedState(
      this.context,
      'bankAccounts'
    );
    const integrationId = bankAccounts?.find(
      (bankAccount) => bankAccount.accountId === accountId
    )?.integrationId;

    const integrations = getUIModelFromSharedState(
      this.context,
      'integrations'
    );
    return integrations?.find(
      (integration: IntegrationUI.Integration) =>
        integration.id === integrationId
    )?.name;
  }

  public getEntityId() {
    const { data } = this.context;

    if (this.stateManager.isStateModeExpenseReport()) {
      return data?.expenseReports?.[0]?.entityId;
    }

    if (this.stateManager.isStateModeBill()) {
      return data?.vendorBills?.[0]?.entityId;
    }

    if (this.stateManager.isStateModePaymentOrEdit()) {
      return data?.outboundPaymentLines?.[0]?.entityId;
    }
  }

  public getAccountList() {
    return this.getBankAccounts();
  }

  public getAccountSelectedCurrency() {
    const accountId = getUIModelFromSharedState(
      this.context,
      'outboundPaymentHeader',
      'accountId'
    );
    return this.getAccountCurrencyById(accountId);
  }

  public isAccountAndVendorBillSameCurrency() {
    const accountSelectedCurrency = this.getAccountSelectedCurrency();
    if (accountSelectedCurrency) {
      const vendorBills = getUIModelFromSharedState(
        this.context,
        'vendorBills'
      );
      const vendorBillCurrency = vendorBills?.[0]?.currency;
      return vendorBillCurrency === accountSelectedCurrency;
    }
    return true;
  }

  public getOutboundPaymentLinePaymentAmountBySourcePaymentDocument(
    sourcePaymentDocument: string
  ) {
    const outboundPaymentLines = getUIModelFromSharedState(
      this.context,
      'outboundPaymentLines'
    );

    return outboundPaymentLines?.find(
      (pl) => pl.sourcePaymentDocument === sourcePaymentDocument
    )?.paymentAmount;
  }

  public getTabTitle() {
    const { data } = this.context;

    return this.stateManager.isStateModePaymentOrEdit()
      ? `${data?.outboundPaymentHeader?.outboundPaymentHeaderNumber}: {{payment.outboundPayment}}`
      : `{{payment.create.payment.title}}`;
  }

  public getTitle() {
    const { data } = this.context;

    return this.stateManager.isStateModePaymentOrEdit()
      ? `${data?.outboundPaymentHeader?.outboundPaymentHeaderNumber}`
      : `{{payment.create.payment.title}}`;
  }

  public getSubTitle() {
    if (this.stateManager.isStateModePaymentOrEdit()) {
      return '{{payment.outboundPayment}}';
    }
  }

  public getEntityName() {
    const { data } = this.context;

    if (this.stateManager.isStateModePaymentOrEdit()) {
      return data?.outboundPaymentLines?.[0]?.entityName;
    }

    const entityId = this.getEntityId();
    if (this.stateManager.isStateModeBillOrExpenseReport() && entityId) {
      const entities = getUIModelFromSharedState(this.context, 'entities');
      return entities?.find?.((entity) => entity.id === entityId)?.entityName;
    }
  }

  public getJEHeaderNumber() {
    const outboundPaymentHeader = getUIModelFromSharedState(
      this.context,
      'outboundPaymentHeader'
    ) as unknown as OutboundPaymentHeaderBaseWithAssociation;

    if (!outboundPaymentHeader) {
      return;
    }

    return (
      outboundPaymentHeader?.['JournalEntryHeader-FinancialDocument']?.[0]
        ?.journalEntryNumber ?? '{{unposted}}'
    );
  }

  public getJELink() {
    const outboundPaymentHeader = getUIModelFromSharedState(
      this.context,
      'outboundPaymentHeader'
    ) as unknown as OutboundPaymentHeaderBaseWithAssociation;

    if (!outboundPaymentHeader) {
      return;
    }

    return outboundPaymentHeader['JournalEntryHeader-FinancialDocument']?.[0]
      ?.id
      ? {
          to: '@uicode:journalEntry',
          qs: {
            id: outboundPaymentHeader['JournalEntryHeader-FinancialDocument'][0]
              .id,
          },
        }
      : null;
  }

  public calculateTotalAmount(extraArgs: { disableRounding: boolean }) {
    const { form, state } = this.context;

    let totalAmount = new this.context.Decimal(0) as Decimal;
    if (!state?.totalAmount) {
      state.totalAmount = new this.context.Decimal(0);
    }
    if (!state?.hashset) {
      state.hashset = new Map();
    }
    const formStateExpression = this.stateManager.isStateModeBill()
      ? 'vendorBills'
      : 'expenseReports';
    const { values } =
      form.getFormValueList(formStateExpression, ['update']) ?? {};

    for (const paymentLine of values ?? []) {
      const paymentAmount = new this.context.Decimal(
        paymentLine?.paymentAmount ?? state?.hashset.get(paymentLine.id)
      ) as Decimal;
      /**
       * This logic is to prevent total amount shown to become 0 when tabs are switched.
       * Because when tabs are switched, FORM values gets reset and thus amount is lost.
       * Adding typed payment amount for each line to a Map which gets updated
       * every time value is changed.
       */
      if (
        paymentAmount.toFixed(2) !== '0.00' &&
        Number(paymentAmount.toFixed(2)) !== state?.hashset.get(paymentLine.id)
      ) {
        state.hashset.set(paymentLine.id, Number(paymentAmount.toFixed(2)));
      }
      if (!paymentAmount.isNaN()) {
        totalAmount = totalAmount.plus(paymentAmount);
      }
    }
    if (extraArgs?.disableRounding) {
      return totalAmount;
    }

    return totalAmount.toFixed(2);
  }

  public paymentAmountValueGetter(params: {
    data: VendorBillHeaderBaseUI.VendorBillHeaderBase;
  }): Decimal | undefined {
    return params?.data?.openAmount?.amount;
  }

  public getButtonLabel() {
    const paymentType = getUIModelFromSharedState(
      this.context,
      'outboundPaymentHeader',
      'paymentType'
    );

    if (paymentType === 'bank') {
      return '{{payment.bankPayment.reviewAndConfirm}}';
    }

    return '{{payment.pleaseWait}}';
  }

  public getDate() {
    const { data } = this.context;

    if (this.stateManager.isStateModePaymentOrEdit()) {
      return data?.outboundPaymentHeader?.paymentDate;
    }

    return (
      getUIModelFromSharedState(
        this.context,
        'outboundPaymentHeader',
        'paymentDate'
      ) ?? toPlainDate(new Date())
    );
  }

  public getPostingPeriod() {
    const { state } = this.context;

    return state?.periodName;
  }

  public getStatus() {
    const { data } = this.context;

    return this.stateManager.isStateModePaymentOrEdit()
      ? `${data?.outboundPaymentHeader?.status}`
      : '';
  }

  private getCurrency() {
    const { data } = this.context;

    if (this.stateManager.isStateModeExpenseReport()) {
      return data?.expenseReports?.[0]?.reimbursementCurrency;
    }

    if (this.stateManager.isStateModeBill()) {
      return data?.vendorBills?.[0]?.currency;
    }

    if (this.stateManager.isStateModePaymentOrEdit()) {
      return data?.outboundPaymentLines?.[0]?.paymentCurrency;
    }
  }

  private getAccountCurrencyById(id: number) {
    const accounts = getUIModelFromSharedState(this.context, 'accounts');
    return accounts?.find((item) => item.id === id)?.currency;
  }

  private getBankAccounts() {
    const modelData = this.stateManager.isStateModeExpenseReport()
      ? getUIModelFromSharedState(this.context, 'expenseReports')
      : getUIModelFromSharedState(this.context, 'vendorBills');
    const paymentType = getUIModelFromSharedState(
      this.context,
      'outboundPaymentHeader',
      'paymentType'
    );

    if (modelData) {
      let bankAccounts = getUIModelFromSharedState(
        this.context,
        'bankAccounts'
      );

      if (paymentType === 'bank') {
        // TODO: check the FinancialInstitutionIntegration to see if the payment is enabled.
        // Currently it is not stable, hence skipping
        bankAccounts = this.filterBankAccountsByPayable(bankAccounts);
      }

      return this.filterBankAccountsByLinkedOnesToAccount(bankAccounts);
    }

    return [];
  }

  private getAccountsByEntityId(entityId: number) {
    const accounts = getUIModelFromSharedState(this.context, 'accounts');
    return accounts?.filter((account) => account.entityId === entityId) ?? [];
  }

  private filterBankAccountsByPayable(
    bankAccounts: Context['data']['bankAccounts']
  ): Context['data']['bankAccounts'] {
    return bankAccounts?.filter(
      (bank) =>
        (bank.type === 'checking' || bank.payableAccount) &&
        bank.paymentAllowed &&
        bank.integrationId
    ) as unknown as Context['data']['bankAccounts'];
  }

  private filterBankAccountsByLinkedOnesToAccount(
    bankAccounts: Context['data']['bankAccounts']
  ) {
    const entityId = this.getEntityId();
    const accounts = this.getAccountsByEntityId(entityId);
    const currency = this.getCurrency();
    const validAccountIds = new Set(
      accounts.map((account: AccountUI.Account) => account.id)
    );

    return (
      bankAccounts?.filter((bankAccount) => {
        return (
          bankAccount.accountId !== undefined &&
          bankAccount?.currency === currency &&
          validAccountIds.has(bankAccount?.accountId)
        );
      }) ?? []
    );
  }

  /// end of the class: Data
}

class Action {
  constructor(
    private readonly context: Context,
    private readonly stateManager: StateManager,
    private readonly data: Data,
    private readonly util: Util
  ) {}

  public async payVendorBill() {
    const { helpers } = this.context;

    this.stateManager.togglePayButtonState(true);

    const accountId = getUIModelFromSharedState(
      this.context,
      'outboundPaymentHeader',
      'accountId'
    );

    const vendorBills = getUIModelFromSharedState(this.context, 'vendorBills');

    //TODO: Remove this when UI Engine starts supporting this type of validations
    if (accountId === undefined) {
      helpers.showToast({
        title: '{{payment.payment.create.error}}',
        message: `{{payment.account.missing}} `,
        type: 'error',
      });
      this.stateManager.togglePayButtonState(false);

      return;
    }

    const isBatch = vendorBills.length > 1;

    await (isBatch
      ? this.applyVendorBillBankBatchPayment()
      : this.applyVendorBillBankPayment());
  }

  public async payExpenseReport() {
    const { helpers } = this.context;

    this.stateManager.togglePayButtonState(true);

    const accountId = getUIModelFromSharedState(
      this.context,
      'outboundPaymentHeader',
      'accountId'
    );

    //TODO: Remove this when UI Engine starts supporting this type of validations
    if (accountId === undefined) {
      helpers.showToast({
        title: '{{payment.payment.create.error}}',
        message: `{{payment.account.missing}}: `,
        type: 'error',
      });
      this.stateManager.togglePayButtonState(false);

      return;
    }
    const expenseReports = getUIModelFromSharedState(
      this.context,
      'expenseReports'
    );
    const isBulkPayment = expenseReports?.length > 1;

    await (isBulkPayment
      ? this.applyExpenseBankBatchReimbursement()
      : this.applyExpenseBankReimbursement());
  }

  public async retryPaymentAction() {
    const { actions, state } = this.context;

    if (!state?.param?.id) {
      return;
    }

    this.util.openLoadingDialog();

    const ip = await this.util.fetchIp();

    if (!this.util.isValidIPAddress(ip)) {
      console.log(ip, 'wrong ip address');
    }
    const response = (await actions.run({
      bankPayment: {
        action: 'retryPayment',
        data: {
          paymentHeaderId: state?.param?.id,
          clientIP: ip,
        },
        where: {},
      },
    })) as unknown as {
      bankPayment: OutboundPaymentHeaderBaseUI.OutboundPaymentHeaderBase & {
        sourceFinancialDocumentIds: number[];
      };
    };

    if (response) {
      await this.afterPaymentCreation(response, { enableRedirect: false });
    }
  }

  private async applyVendorBillBankBatchPayment() {
    this.util.openLoadingDialog();

    const vendorBills = getUIModelFromSharedState(this.context, 'vendorBills');
    if (!vendorBills || vendorBills.length === 0) {
      return;
    }
    // use first line entity todo get this from header
    const { entityId } = vendorBills[0];

    // payment header
    const { senderAccountId, paymentDate, accountId, commonDescription } =
      this.getPaymentHeader();

    // build payment lines
    const documentPaymentLines = vendorBills.map((bill) => {
      const paymentAmount = new this.context.Decimal(
        this.context.state?.hashset.get(bill.id) || 0
      ) as Decimal;
      return {
        paymentAmount: { amount: paymentAmount },
        documentId: bill.id,
      };
    });

    const response = await this.actionCreatePayment(
      {
        documentType: SourcePaymentDocumentType.VendorBillHeaderBase,
        accountId,
        entityId,
        paymentDate,
        commonDescription,
        senderAccountId,
        paymentType: 'bulk',
      },
      documentPaymentLines
    );

    if (response) {
      await this.afterPaymentCreation(response);
    }
  }

  private async applyVendorBillBankPayment() {
    const { state } = this.context;

    this.util.openLoadingDialog();

    const vendorBills = getUIModelFromSharedState(this.context, 'vendorBills');
    if (!vendorBills || vendorBills.length === 0) {
      return;
    }
    const { id, entityId } = vendorBills[0];

    const { senderAccountId, paymentDate, accountId, commonDescription } =
      this.getPaymentHeader();

    const response = await this.actionCreatePayment(
      {
        documentType: SourcePaymentDocumentType.VendorBillHeaderBase,
        accountId,
        entityId,
        paymentDate,
        commonDescription,
        senderAccountId,
        paymentType: 'single',
      },
      [
        {
          documentId: id,
          paymentAmount: {
            amount: new this.context.Decimal(
              state?.hashset.get(id) || 0
            ) as Decimal,
          },
        },
      ]
    );

    if (response) {
      await this.afterPaymentCreation(response);
    }
  }

  private async applyExpenseBankReimbursement() {
    const { state } = this.context;

    const expenseReports = getUIModelFromSharedState(
      this.context,
      'expenseReports'
    );

    if (!expenseReports || expenseReports.length === 0) {
      return;
    }

    this.util.openLoadingDialog();
    const { id, entityId } = expenseReports[0];
    const { senderAccountId, paymentDate, accountId, commonDescription } =
      this.getPaymentHeader();

    const response = await this.actionCreatePayment(
      {
        documentType: SourcePaymentDocumentType.ExpenseReportBase,
        accountId,
        entityId,
        paymentDate,
        commonDescription,
        senderAccountId,
        paymentType: 'single',
      },
      [
        {
          documentId: id,
          paymentAmount: {
            amount: new this.context.Decimal(
              state?.hashset.get(id) || 0
            ) as Decimal,
          },
        },
      ]
    );

    if (response) {
      await this.afterPaymentCreation(response);
    }
  }

  private async applyExpenseBankBatchReimbursement() {
    this.util.openLoadingDialog();

    const expenseReports = getUIModelFromSharedState(
      this.context,
      'expenseReports'
    );
    if (!expenseReports || expenseReports.length === 0) {
      return;
    }
    // todo get it from header node
    const { entityId } = expenseReports[0];

    const { senderAccountId, paymentDate, accountId, commonDescription } =
      this.getPaymentHeader();

    // build payment lines
    const documentPaymentLines = expenseReports.map((report) => {
      const paymentAmount = new this.context.Decimal(
        this.context.state?.hashset.get(report.id) || 0
      ) as Decimal;
      return {
        paymentAmount: { amount: paymentAmount },
        documentId: report.id,
      };
    });

    const response = await this.actionCreatePayment(
      {
        documentType: SourcePaymentDocumentType.ExpenseReportBase,
        accountId,
        entityId,
        paymentDate,
        commonDescription,
        senderAccountId,
        paymentType: 'bulk',
      },
      documentPaymentLines
    );

    if (response) {
      await this.afterPaymentCreation(response);
    }
  }

  private getPaymentHeader() {
    const senderAccountId = getUIModelFromSharedState(
      this.context,
      'outboundPaymentHeader',
      'senderAccountId'
    );

    const paymentDate = getUIModelFromSharedState(
      this.context,
      'outboundPaymentHeader',
      'paymentDate'
    );

    const accountId = getUIModelFromSharedState(
      this.context,
      'outboundPaymentHeader',
      'accountId'
    );

    const commonDescription = getUIModelFromSharedState(
      this.context,
      'outboundPaymentHeader',
      'commonDescription'
    );

    return { senderAccountId, paymentDate, accountId, commonDescription };
  }

  private getBankDetails(
    documentType: SourcePaymentDocumentType,
    documentId: number
  ) {
    return documentType === SourcePaymentDocumentType.VendorBillHeaderBase
      ? getUIModelFromSharedState(this.context, 'vendorBillBankDetails')?.find(
          (i) => i.vendorBillHeaderId === documentId
        )
      : getUIModelFromSharedState(
          this.context,
          'expenseReportsBankDetails'
        )?.find((i) => i.expenseReportHeaderId === documentId);
  }

  private getSourceDocument(
    documentType: SourcePaymentDocumentType,
    documentId: number
  ):
    | Context['data']['vendorBills'][0]
    | Context['data']['expenseReports'][0]
    | undefined {
    if (documentType === SourcePaymentDocumentType.VendorBillHeaderBase) {
      const vendorBills = getUIModelFromSharedState(
        this.context,
        'vendorBills'
      );
      return vendorBills?.find((vb) => vb.id === documentId);
    }
    if (documentType === SourcePaymentDocumentType.ExpenseReportBase) {
      const expenseReports = getUIModelFromSharedState(
        this.context,
        'expenseReports'
      );
      return expenseReports?.find((er) => er.id === documentId);
    }
  }

  private async actionCreatePayment(
    paymentHeader: {
      commonDescription: string;
      paymentDate: PlainDate;
      accountId: number;
      entityId: number;
      documentType: SourcePaymentDocumentType;
      paymentType: 'single' | 'bulk' | 'manual';
      senderAccountId: string;
    },
    documentsPaymentLines: {
      documentId: number;
      paymentAmount: EvstCurrencyAmount;
    }[]
  ) {
    const ip = await this.util.fetchIp();

    if (!this.util.isValidIPAddress(ip)) {
      console.log(ip, 'wrong ip address');
    }

    const paymentLines: Partial<
      Omit<PaymentObjectWithReceiverAccount, 'outboundPaymentHeaderId'>
    >[] = documentsPaymentLines.map((p) => {
      const doc = this.getSourceDocument(
        paymentHeader.documentType,
        p.documentId
      );

      if (!doc) {
        return this.context.helpers.showToast({
          title: '{{payment.payment}}',
          message: `{{payment.payment.missingDocument}}`.replace(
            '$documentId$',
            p.documentId.toString()
          ),
          type: 'error',
        });
      }

      // todo take row level description if the payment is batched
      const description = paymentHeader.commonDescription;
      const paymentCurrency =
        (doc as Context['data']['expenseReports'][0])?.reimbursementCurrency ??
        (doc as Context['data']['vendorBills'][0])?.currency;

      const bankDetail = this.getBankDetails(
        paymentHeader.documentType,
        p.documentId
      );

      if (!bankDetail) {
        return this.context.helpers.showToast({
          title: '{{payment.payment}}',
          message: `{{payment.payment.missingBankDetails}}`.replace(
            '$documentId$',
            doc.id.toString()
          ),
          type: 'error',
        });
      }

      const paymentLine: Omit<
        PaymentObjectWithReceiverAccount,
        'outboundPaymentHeaderId'
      > = {
        description,
        paymentCurrency,
        paymentAmount: { amount: p.paymentAmount.amount },
        receiverName: bankDetail.bankAccountHolderName,
        sourcePaymentDocumentId: doc.id,
        sourcePaymentDocumentPath: getSourcePaymentDocumentPath(
          paymentHeader.documentType
        ),
        entityId: paymentHeader.entityId,
        receiverAccount: bankDetail.bankAccountNumber,
        receiverBankCode: bankDetail.financialInstitutionId,
        clientIP: ip,
      };

      return paymentLine;
    });
    const paymentDate = paymentHeader.paymentDate;
    const convertedPaymentDate = new Date(toPlainDate(paymentDate).toISO());

    const isFuturePaymentDate =
      !this.util.isPastDateOrToday(convertedPaymentDate);

    const integration = this.data.getIntegrationName();

    const payload: ActionCreatePaymentFields = {
      paymentType: paymentHeader.paymentType,
      type: paymentHeader.documentType,
      accountId: paymentHeader.accountId,
      paymentLines: paymentLines.filter(Boolean),
      paymentHeader: {
        paymentDate: paymentHeader.paymentDate,
        commonDescription: paymentHeader.commonDescription,
      },
      integration,
      senderAccountId: paymentHeader.senderAccountId,
      isFuturePaymentDate,
      batchBookingPreferred: false,
      options: null,
      fieldList: [
        'id',
        'signUrl',
        'paymentId',
        'paymentDate',
        'paymentType',
        'status',
        'totalPaid',
        'senderAccountId',
        'authorizedIntegration',
      ],
    };

    if (payload.paymentLines.length === 0) {
      return this.context.helpers.showToast({
        title: '{{payment.payment}}',
        message: `{{payment.payment.noPaymentLines}}`,
        type: 'error',
      });
    }

    if (payload.paymentHeader.commonDescription) {
      const refinedDescription = replaceSpecialCharacters(
        payload.paymentHeader.commonDescription
      );
      if (refinedDescription !== payload.paymentHeader.commonDescription) {
        payload.paymentHeader.commonDescription = refinedDescription;

        this.context.helpers.showToast({
          title: '{{payment.specialCharacterRemoved}}',
          message: `{{payment.descriptionChangedTo}} "${refinedDescription}"`,
          type: 'info',
        });
      }
    }

    return this.initiateBankPayment(payload);
  }

  /**
   *
   * @param response response from the createPayment action
   * @param options
   */
  private async afterPaymentCreation(
    response: {
      bankPayment: OutboundPaymentHeaderBaseUI.OutboundPaymentHeaderBase;
    },
    // eslint-disable-next-line unicorn/no-object-as-default-parameter
    options: {
      enableRedirect: boolean;
    } = { enableRedirect: true }
  ) {
    if ('error' in response) {
      if (response.error['errorType'] !== 'ValidationError') {
        this.context.helpers.showToast({
          title: '{{payment.payment}}',
          message: '{{payment.payment.create.error}}',
          type: 'error',
        });
      }
      this.stateManager.togglePayButtonState(false);
      this.util.closeLoadingDialog();

      return;
    }

    const integration = response.bankPayment.authorizedIntegration;

    // TODO no need here since plaid payment is astra
    // if (integration === 'Plaid') {
    //   this.util.closeLoadingDialog();

    //   await this.initializePlaidPaymentSigning({
    //     paymentId: response.bankPayment.paymentId,
    //     accountId: '',
    //     onSuccessCallback: async () => {
    //       this.context.helpers.showToast({
    //         title: '{{payment.payment}}',
    //         message: '{{payment.signPayment.success}}',
    //         type: 'success',
    //       });
    //     },
    //     onErrorCallback: () => {
    //       this.context.helpers.showToast({
    //         title: '{{payment.payment}}',
    //         message: '{{payment.signPayment.unsuccess}}',
    //         type: 'error',
    //       });
    //       this.stateManager.togglePayButtonState(false);

    //       return;
    //     },
    //     // eslint-disable-next-line @typescript-eslint/no-empty-function
    //     onLoadCallback: () => {},
    //   });
    // }

    if (
      integration === 'Ponto' ||
      integration === 'SaltEdge' ||
      integration === 'FinAPI' ||
      integration === 'TestIntegration'
    ) {
      this.util.closeLoadingDialog();

      const signUrl = response.bankPayment.signUrl;
      // window.open(signUrl, '_blank', 'width=1000, height=1000');
      this.context.controllers.browserWindowOpen(signUrl);

      this.context.helpers.showToast({
        title: '{{payment.payment}}',
        message: '{{payment.payment.save.message}}',
        type: 'success',
      });
    }

    if (options.enableRedirect === true) {
      const paymentId = response.bankPayment.id;
      this.openTab(paymentId);
    }
    this.util.closeLoadingDialog();
  }

  private openTab(id: number) {
    const { helpers } = this.context;

    helpers.navigate({
      to: `/templates/everest.fin.integration.bank/uinext/payment/bankPayment?mode=payment&id=${id}`,
      closeCurrentTab: true,
    });
  }

  private async initiateBankPayment(
    payload: ActionCreatePaymentFields
  ): Promise<{
    bankPayment: OutboundPaymentHeaderBaseUI.OutboundPaymentHeaderBase;
  }> {
    const { actions } = this.context;

    return await actions.run({
      bankPayment: {
        action: 'createPayment',
        data: {
          ...payload,
        },
        where: {},
      },
    });
  }

  private async initializePlaidPaymentSigning({
    paymentId,
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    onLoadCallback = () => {},
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    onSuccessCallback = async () => {},
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    onErrorCallback = () => {},
  }: {
    paymentId: string;
    accountId: string;
    onLoadCallback: () => void;
    onErrorCallback: () => void;
    onSuccessCallback: () => Promise<void>;
  }) {
    const { banking } = this.context;

    const accountId = getUIModelFromSharedState(
      this.context,
      'outboundPaymentHeader',
      'accountId'
    );

    const extraData = {
      user: {
        client_user_id: `user-${accountId}`,
      },
      products: ['payment_initiation'],
      payment_initiation: {
        payment_id: paymentId,
      },
    };

    const config = {
      extraData,

      onLoad: () => {
        onLoadCallback();
      },
      onSuccess: async () => {
        await onSuccessCallback();
      },
      onError: () => {
        onErrorCallback();
      },
    };
    banking.plaid.initialize(config);
  }

  public async fetchAccountingPeriod() {
    const { actions, state, data, helpers, sharedState } = this.context;

    const postingDate = getUIModelFromSharedState(
      this.context,
      'outboundPaymentHeader',
      'paymentDate'
    );

    const entityId = this.data.getEntityId();
    let response;
    const nodeInstance = sharedState.getNodeInstance(
      data?.['outboundPaymentHeader']?._nodeReference
    );
    if (postingDate) {
      response = (await actions.run({
        postingPeriod: {
          action: 'findAccountingPeriod',
          data: {
            entityId,
            postingDate,
            forPosting: false,
            lockEvaluation: {
              journalEntryTypes: [EvstJournalEntryType.OutboundPayment],
            },
          },
          where: {},
        },
      })) as {
        postingPeriod: AccountingPeriodUI.AccountingPeriod & {
          closed: boolean;
          locked: boolean;
        };
      };
    }

    if (response?.postingPeriod) {
      const {
        locked = false,
        closed = false,
        periodName,
      } = response.postingPeriod ?? {};
      state.canBePosted = !(locked || closed);
      state.periodName = periodName;
      helpers.set(nodeInstance, `data.postingPeriod`, periodName);
    } else {
      state.canBePosted = false;
    }

    validatePaymentDate(this.context);
  }

  public async saveAction() {
    const { actions, state, form } = this.context;

    form?.deleteFormAction('0:exchangeRateDetails', 'update');
    await actions.submit({
      onSuccess: () => {
        state.mode = 'payment';
      },

      successMessage: `{{payment.outboundPaymentCreated}}`,
      loadingMessage: `{{payment.outboundPaymentCreate}}`,
    });
  }

  public fetchAccountingPeriodOnce() {
    const { state } = this.context;

    const entityId = this.data.getEntityId();
    const paymentDate = getUIModelFromSharedState(
      this.context,
      'outboundPaymentHeader',
      'paymentDate'
    );
    // Fetch posting period initially
    if (!state?.once && entityId && paymentDate) {
      state.once = true;
      return this.fetchAccountingPeriod().catch((error) => {
        console.log(error);
      });
    }
  }

  public async deleteAction() {
    const { data, helpers } = this.context;

    const outboundPaymentHeaderId = data?.outboundPaymentHeader?.id;

    helpers.showNotificationMessage({
      type: 'loading',
      key: 'deleteOutboundPaymentMessageKey',
      message: '{{payment.paymentDeleting}}',
      duration: 5,
    });

    const response = await OutboundPaymentHeaderBaseUI.deleteOutboundPayment(
      this.context,
      { outboundPaymentHeaderId }
    ).run('outboundPaymentHeaderBase');

    if ('error' in response) {
      helpers.showNotificationMessage({
        type: 'error',
        key: 'deleteOutboundPaymentMessageKey',
        message: response.error.message,
        duration: 5,
      });
    } else {
      helpers.showNotificationMessage({
        type: 'success',
        key: 'deleteOutboundPaymentMessageKey',
        message: '{{payment.paymentDeleted}}',
        duration: 5,
      });
      await helpers.navigate({
        to: '/templates/everest.fin.expense/payment/uinext/payments',
        initialState: { refresh: true },
        closeCurrentTab: true,
      });
    }
  }

  public cancelAction() {
    const { form, state } = this.context;

    state.mode = 'payment';
    form.reset();
  }

  public signPayment() {
    const { controllers } = this.context;
    const outboundPaymentHeader = getUIModelFromSharedState(
      this.context,
      'outboundPaymentHeader'
    );

    const signUrl = outboundPaymentHeader?.signUrl;
    // window.open(signUrl, '_blank', 'width=1000, height=1000');
    controllers.browserWindowOpen(signUrl);
  }

  // end of class : Action
}

export class Util {
  constructor(private readonly context: Context) {}

  public isValidIPAddress(ip: string): boolean {
    const ipAddressRegex: RegExp =
      /^(25[0-5]|2[0-4]\d|[01]?\d{1,2})\.(25[0-5]|2[0-4]\d|[01]?\d{1,2})\.(25[0-5]|2[0-4]\d|[01]?\d{1,2})\.(25[0-5]|2[0-4]\d|[01]?\d{1,2})$/;
    return ipAddressRegex.test(ip);
  }

  public async fetchIp() {
    const res = (await fetch('/hook/fetch/ip').then((res) => res.json())) as {
      headers: {
        'x-forwarded-for': string;
        'x-forwarded-port': string;
        'x-forwarded-proto': string;
      };
      ip: string;
    };
    return res.headers['x-forwarded-for'].split(',')[0];
  }

  private isToday(inputDate: Date) {
    const today = new Date();
    const dateToCheck = inputDate;
    return (
      today.getDate() === dateToCheck.getDate() &&
      today.getMonth() === dateToCheck.getMonth() &&
      today.getFullYear() === dateToCheck.getFullYear()
    );
  }

  private isPastDate(inputDate: Date) {
    const today = new Date();
    // compare today and dateToCheck return true if dateToCheck is in the past
    return today.getTime() > inputDate.getTime();
  }

  public isPastDateOrToday(inputDate: Date) {
    return this.isPastDate(inputDate) || this.isToday(inputDate);
  }

  // Shows loading modal
  public openLoadingDialog() {
    const { helpers } = this.context;

    helpers.openDialog({
      variant: 'loading',
      title: `{{payment.outboundPaymentCreate}}`,
      message: '{{payment.paymentCreatingMessage}}',
      actions: {
        secondary: {
          label: '{{payment.cancel}}',
        },
      },
    });
  }

  public closeLoadingDialog() {
    const { helpers } = this.context;

    helpers.closeModal();
  }

  // end of the class : Util
}

class QueryBuild {
  constructor(
    private readonly context: Context,
    private readonly stateManager: StateManager,
    private readonly data: Data
  ) {}

  public getParametersForGetAccounts() {
    // todo what happens if entity id is not present
    const entityId = this.data.getEntityId() ?? undefined;
    const args = {
      where: {
        accountSubType: 'bank',
      },
    };
    return { entityId, args };
  }

  public getOutboundPaymentHeaderQuery() {
    const { state } = this.context;

    return this.stateManager.isStateModePaymentOrEdit()
      ? { where: { id: Number(state?.id) } }
      : { where: { id: 0 } };
  }

  public getVendorBillsQuery() {
    const { state } = this.context;

    if (this.stateManager.isStateModeBill()) {
      return {
        where: {
          id: {
            $in: state?.ids?.split(',').map(Number),
          },
        },
      };
    }
    const outboundPaymentLines = getUIModelFromSharedState(
      this.context,
      'outboundPaymentLines'
    );
    const docType = outboundPaymentLines?.[0]?.sourcePaymentDocumentType;
    if (
      this.stateManager.isStateModePayment() &&
      docType === 'VendorBillHeaderBase'
    ) {
      const sourcePaymentDocumentIds = outboundPaymentLines?.map(
        (item) => item.sourcePaymentDocumentId
      );

      return {
        where: {
          id: {
            $in: sourcePaymentDocumentIds,
          },
        },
      };
    }
  }

  public findAccountingPeriodQuery(): any {
    if (!this.stateManager.isStateModePayment()) {
      return;
    }

    const outboundPaymentLines = getUIModelFromSharedState(
      this.context,
      'outboundPaymentLines'
    );
    const outboundPaymentHeader = getUIModelFromSharedState(
      this.context,
      'outboundPaymentHeader'
    );
    const paymentDate = outboundPaymentHeader?.paymentDate;

    const convertedPaymentDate = new Date(toPlainDate(paymentDate)?.toISO());

    return {
      entityId: outboundPaymentLines?.[0]?.entityId,
      postingDate: convertedPaymentDate,
      forPosting: false,
      lockEvaluation: {
        journalEntryTypes: [EvstJournalEntryType.OutboundPayment],
      },
    };
  }

  public getExpenseReportsQuery() {
    const { state } = this.context;

    if (this.stateManager.isStateModeExpenseReport()) {
      return {
        where: {
          id: {
            $in: state?.ids?.split(',').map(Number),
          },
        },
      };
    }

    const outboundPaymentLines = getUIModelFromSharedState(
      this.context,
      'outboundPaymentLines'
    );

    const docType = outboundPaymentLines?.[0]?.sourcePaymentDocumentType;
    if (
      this.stateManager.isStateModePaymentOrEdit() &&
      docType === 'ExpenseReportBase'
    ) {
      const sourcePaymentDocumentIds = outboundPaymentLines?.map(
        (item) => item.sourcePaymentDocumentId
      );

      return {
        where: {
          id: {
            $in: sourcePaymentDocumentIds,
          },
        },
      };
    }
  }
  /// end of the class : QueryBuild
}

class Visibility {
  constructor(
    private readonly context: Context,
    private readonly stateManager: StateManager,
    private readonly data: Data
  ) {}

  private isConvertedAmountVisible() {
    const outboundPaymentLines = getUIModelFromSharedState(
      this.context,
      'outboundPaymentLines'
    );

    const docType = outboundPaymentLines?.[0]?.sourcePaymentDocumentType;

    return (
      this.stateManager.isStateModeBillOrPaymentOrEdit() &&
      !this.data.isAccountAndVendorBillSameCurrency() &&
      docType !== 'ExpenseReportBase'
    );
  }

  public isTotalTransactionsVisible() {
    const { isFetchingUiModelData } = this.context;

    const vendorBills = getUIModelFromSharedState(this.context, 'vendorBills');
    return (
      !isFetchingUiModelData &&
      this.isConvertedAmountVisible() &&
      vendorBills?.length > 1
    );
  }

  public isVBBlockVisible() {
    if (this.stateManager.isStateModeBill()) {
      return true;
    }
    const outboundPaymentLines = getUIModelFromSharedState(
      this.context,
      'outboundPaymentLines'
    );
    const docType = outboundPaymentLines?.[0]?.sourcePaymentDocumentType;
    return (
      this.stateManager.isStateModePaymentOrEdit() &&
      docType === 'VendorBillHeaderBase'
    );
  }

  public isFieldVisible(type: string) {
    const { state } = this.context;

    if (type === 'payment' && this.stateManager.isStateModePaymentOrEdit()) {
      return true;
    }

    return type === state?.mode;
  }

  public isERBlockVisible() {
    if (this.stateManager.isStateModeExpenseReport()) {
      return true;
    }
    const outboundPaymentLines = getUIModelFromSharedState(
      this.context,
      'outboundPaymentLines'
    );
    const docType = outboundPaymentLines?.[0]?.sourcePaymentDocumentType;
    return (
      this.stateManager.isStateModePaymentOrEdit() &&
      docType === 'ExpenseReportBase'
    );
  }

  public isPayButtonDisabled() {
    const { state } = this.context;

    if (!state?.canBePosted) {
      return !state.canBePosted;
    }

    if (state?.buttonDisabled) {
      return state.buttonDisabled;
    }

    return false;
  }

  public isCancelPaymentButtonVisible() {
    const status = getUIModelFromSharedState(
      this.context,
      'outboundPaymentHeader',
      'status'
    );
    return status === 'unsigned' && this.stateManager.isStateModePayment();
  }

  public isDeletePaymentButtonDisabled() {
    //
  }

  public isRetryPaymentButtonVisible() {
    const status = getUIModelFromSharedState(
      this.context,
      'outboundPaymentHeader',
      'status'
    );
    return (
      ['cancelled', 'rejected', 'unknown'].includes(status) &&
      this.stateManager.isStateModePayment()
    );
  }

  public isDeleteActionVisible() {
    const { data } = this.context;

    const paymentStatus = data?.outboundPaymentHeader?.status;
    const { postingPeriod } = data ?? {};
    const { locked = false, closed = false } = postingPeriod ?? {};
    if (closed || locked) {
      return false;
    }

    return (
      paymentStatus === EvstPaymentStatus.ManuallyRecorded ||
      paymentStatus === EvstPaymentStatus.Unknown ||
      paymentStatus === EvstPaymentStatus.Rejected ||
      paymentStatus === EvstPaymentStatus.Cancelled
    );
  }

  public isSignUrlButtonVisible() {
    const paymentStatus = getUIModelFromSharedState(
      this.context,
      'outboundPaymentHeader',
      'status'
    );

    const signUrl = getUIModelFromSharedState(
      this.context,
      'outboundPaymentHeader',
      'signUrl'
    );

    return (
      paymentStatus === 'unsigned' &&
      this.stateManager.isStateModePayment() &&
      !!signUrl
    );
  }

  /// end of the class : Visibility
}

export function getInstance(context: Context) {
  const util = new Util(context);
  const stateManager = new StateManager(context);
  const data = new Data(context, stateManager, util);
  const action = new Action(context, stateManager, data, util);
  const queryBuild = new QueryBuild(context, stateManager, data);
  const visibility = new Visibility(context, stateManager, data);
  return {
    stateManager,
    data,
    util,
    action,
    queryBuild,
    visibility,
  };
}

/**
 * exported functions
 */

export function getParametersForGetAccounts(context: Context) {
  const { queryBuild } = getInstance(context);
  return queryBuild.getParametersForGetAccounts();
}

export function getOutboundPaymentHeaderQuery(context: Context) {
  const { queryBuild } = getInstance(context);
  return queryBuild.getOutboundPaymentHeaderQuery();
}

export function getVendorBillsQuery(context: Context) {
  const { queryBuild } = getInstance(context);
  return queryBuild.getVendorBillsQuery();
}

export function findAccountingPeriodQuery(context: Context) {
  const { queryBuild } = getInstance(context);
  return queryBuild.findAccountingPeriodQuery();
}

export function getExpenseReportsQuery(context: Context) {
  const { queryBuild } = getInstance(context);
  return queryBuild.getExpenseReportsQuery();
}

export function getTabTitle(context: Context) {
  const { data } = getInstance(context);
  return data.getTabTitle();
}

export function getTitle(context: Context) {
  const { data } = getInstance(context);
  return data.getTitle();
}

export function getSubTitle(context: Context) {
  const { data } = getInstance(context);
  return data.getSubTitle();
}

export function getEntityName(context: Context) {
  const { data } = getInstance(context);
  return data.getEntityName();
}

export function getJEHeaderNumber(context: Context) {
  const { data } = getInstance(context);
  return data.getJEHeaderNumber();
}

export function getJELink(context: Context) {
  const { data } = getInstance(context);
  return data.getJELink();
}

export function isStateModePayment(context: Context) {
  const { stateManager } = getInstance(context);
  return stateManager.isStateModePayment();
}

export function isStateModeBillOrExpenseReportOrEdit(context: Context) {
  const { stateManager } = getInstance(context);
  return stateManager.isStateModeBillOrExpenseReportOrEdit();
}

export function isFieldDisabled(context: Context, fieldName: string) {
  const { stateManager } = getInstance(context);
  return stateManager.isFieldDisabled(fieldName);
}

export function isStateModePaymentOrEdit(context: Context) {
  const instance = getInstance(context);
  return instance.stateManager.isStateModePaymentOrEdit();
}

export function singlePayment(context: Context) {
  const { stateManager } = getInstance(context);
  return !stateManager.isBatch();
}

export function isTotalTransactionsVisible(context: Context) {
  const { visibility } = getInstance(context);
  return visibility.isTotalTransactionsVisible();
}

export function isStateModeBillOrExpenseReport(context: Context) {
  const { stateManager } = getInstance(context);
  return stateManager.isStateModeBillOrExpenseReport();
}

export function calculateTotalAmount(
  context: Context,
  extraArgs: {
    disableRounding: boolean;
  }
) {
  const { data } = getInstance(context);
  return data.calculateTotalAmount(extraArgs);
}

export function isVBBlockVisible(context: Context) {
  const { visibility } = getInstance(context);
  return visibility.isVBBlockVisible();
}

export function isFieldVisible(context: Context, type: string) {
  const { visibility } = getInstance(context);
  return visibility.isFieldVisible(type);
}

export function paymentAmountValueGetter(
  context: Context,
  params: { data: VendorBillHeaderBaseUI.VendorBillHeaderBase }
): Decimal | undefined {
  const { data } = getInstance(context);
  return data.paymentAmountValueGetter(params);
}

export function isERBlockVisible(context: Context) {
  const { visibility } = getInstance(context);
  return visibility.isERBlockVisible();
}

export function getButtonLabel(context: Context) {
  const { data } = getInstance(context);
  return data.getButtonLabel();
}

export async function payVendorBill(context: Context) {
  const { action } = getInstance(context);
  return action.payVendorBill();
}

export function isPayButtonDisabled(context: Context) {
  const { visibility } = getInstance(context);
  return visibility.isPayButtonDisabled();
}

export async function payExpenseReport(context: Context) {
  const { action } = getInstance(context);
  return action.payExpenseReport();
}

export async function saveAction(context: Context) {
  const { action } = getInstance(context);
  return action.saveAction();
}

export function isStateModeEdit(context: Context) {
  const { action, stateManager } = getInstance(context);
  // todo: find a better place this
  action.fetchAccountingPeriodOnce();
  return stateManager.isStateModeEdit();
}

export function cancelAction(context: Context) {
  const { action } = getInstance(context);
  return action.cancelAction();
}

export function deleteAction(context: Context) {
  const { action } = getInstance(context);
  return action.deleteAction();
}

// eslint-disable-next-line @typescript-eslint/no-empty-function
export function cancelPaymentAction() {}

export function isCancelPaymentButtonVisible(context: Context) {
  const { visibility } = getInstance(context);
  return visibility.isCancelPaymentButtonVisible();
}

export async function retryPaymentAction(context: Context) {
  const { action } = getInstance(context);
  return action.retryPaymentAction();
}

export function isRetryPaymentButtonVisible(context: Context) {
  const { visibility } = getInstance(context);
  return visibility.isRetryPaymentButtonVisible();
}

export function isSignUrlButtonVisible(context: Context) {
  const { visibility } = getInstance(context);
  return visibility.isSignUrlButtonVisible();
}

export function isDeleteActionVisible(context: Context) {
  const { visibility } = getInstance(context);
  return visibility.isDeleteActionVisible();
}

export function signPayment(context: Context) {
  const { action } = getInstance(context);
  return action.signPayment();
}

export function getDate(context: Context) {
  const { data } = getInstance(context);
  return data.getDate();
}

export function getPostingPeriod(context: Context) {
  const { data } = getInstance(context);
  return data.getPostingPeriod();
}

export function getStatus(context: Context) {
  const { data } = getInstance(context);
  return data.getStatus();
}

export function getAction(context: Context) {
  const { stateManager } = getInstance(context);
  return stateManager.getAction();
}

//////////////// ui template functions

export function JEValueGetter(
  context: Context,
  params: { data: OutboundPaymentHeaderBaseWithAssociation }
) {
  return params?.data?.['JournalEntryHeader-FinancialDocument']?.[0]
    ?.journalEntryNumber;
}

export function openVBJournalEntry(
  context: Context,
  { data }: { data: OutboundPaymentHeaderBaseWithAssociation }
) {
  const { helpers } = context;

  const { posted, id } =
    data?.['JournalEntryHeader-FinancialDocument']?.[0] ?? {};
  if (posted) {
    helpers.navigate({
      to: `@uicode:journalEntry?id=${id}`,
    });
  }
}

export function amountPaidValueGetter(
  context: Context,
  params: {
    data:
      | ExpenseReportBaseUI.ExpenseReportBase
      | VendorBillHeaderBaseUI.VendorBillHeaderBase;
  }
) {
  const { data } = getInstance(context);

  if (!params?.data) {
    return;
  }

  const outboundPaymentLines = getUIModelFromSharedState(
    context,
    'outboundPaymentLines'
  );

  const docType = outboundPaymentLines?.[0]?.sourcePaymentDocumentType;
  return data.getOutboundPaymentLinePaymentAmountBySourcePaymentDocument(
    docType === 'VendorBillHeaderBase'
      ? (params.data as VendorBillHeaderBaseUI.VendorBillHeaderBase)
          .vendorBillHeaderNumber
      : (params.data as ExpenseReportBaseUI.ExpenseReportBase).expenseNumber
  );
}

export function getTotalTransactions(context: Context) {
  const vendorBills = getUIModelFromSharedState(context, 'vendorBills');
  return vendorBills?.length ?? 0;
}

export function getProperAmountLabel(
  context: Context,
  label: string,
  type: string
) {
  const { state } = context;
  const { data: dataService } = getInstance(context);

  const vendorBills = getUIModelFromSharedState(context, 'vendorBills');
  const expenseReports = getUIModelFromSharedState(context, 'expenseReports');

  if (state.mode === 'bill') {
    const accountSelectedCurrency = dataService.getAccountSelectedCurrency();
    const vendorBillCurrency = vendorBills?.[0]?.currency;
    if (
      !accountSelectedCurrency ||
      accountSelectedCurrency === vendorBillCurrency
    ) {
      return label;
    } else {
      const currency =
        type === 'totalAmount' ? vendorBillCurrency : accountSelectedCurrency;
      return `${label} (${currency})`;
    }
  }
  if (state.mode === 'payment' || state.mode === 'edit') {
    const accountSelectedCurrency = state?.accountSelectedCurrency;
    const vendorBillCurrency = vendorBills?.[0]?.currency;
    const expenseReportCurrency = expenseReports?.[0]?.reimbursementCurrency;
    if (
      expenseReportCurrency ||
      (vendorBillCurrency && accountSelectedCurrency === vendorBillCurrency)
    ) {
      return label;
    } else if (expenseReportCurrency || vendorBillCurrency) {
      const currency =
        type === 'totalAmount' ? vendorBillCurrency : accountSelectedCurrency;
      return `${label} (${currency})`;
    }
  }
  return label;
}

export function validateFieldIsNotEmpty() {
  return {
    validate: Boolean,
  };
}

export function validateDescriptionField(context: Context) {
  const { data } = getInstance(context);
  const integrationName = data.getIntegrationName();

  return {
    validate: (value: boolean) => {
      if (integrationName && integrationName === 'Plaid') {
        return value ? true : '{{payment.description.plaid.validation.error}}';
      }

      return true;
    },
  };
}

export async function onChangePaymentDate(
  context: Context,
  paymentDate: PlainDate
) {
  const { action } = getInstance(context);

  if (!paymentDate) {
    return;
  }

  await action.fetchAccountingPeriod();
}

export function validatePaymentDate({
  state,
  helpers,
  sharedState,
  data,
}: Context) {
  const nodeInstance = sharedState.getModelData(
    data?.['outboundPaymentHeader']
  ).instance;

  if (state.canBePosted) {
    setTimeout(() => {
      helpers.set(nodeInstance, `fieldErrors.paymentDate`, {
        message: null,
        type: 'ManualValidationErrorFromController',
      });
    }, 300);
  } else {
    setTimeout(() => {
      helpers.set(nodeInstance, `fieldErrors.paymentDate`, {
        message: '{{payment.periodNotFound}}',
        type: 'ManualValidationErrorFromController',
      });
    }, 300);
  }
}

export function getPaymentType(context: Context) {
  const { state, form } = context;
  const { stateManager } = getInstance(context);

  state.paymentMethod = 'bank';
  const model = 'outboundPaymentHeader';
  const previousFormAction = form.getFormAction(model);
  const currentAction = stateManager.getAction();
  // TODO WTH??
  form.modifyFormAction(model, previousFormAction, currentAction);
  form.modifyFormAction('_', previousFormAction, currentAction);
  return 'bank';
}

export function getComponentValues(
  context: Context,
  key: 'idProp' | 'prefixProp' | 'textProp' | 'list'
) {
  const { data } = getInstance(context);
  const list = data.getAccountList();
  const paymentValues = {
    idProp: 'accountId',
    prefixProp: 'accountNumber',
    textProp: 'accountName',
    list,
  };

  return paymentValues[key];
}

export function getDefaultDescription(context: Context) {
  const { state, data } = context;
  const { stateManager } = getInstance(context);
  if (
    (!state.defaultDescriptionValueSet && data?.vendorBills?.length === 1) ||
    data?.expenseReports?.length === 1
  ) {
    if (
      stateManager.isStateModeBill() &&
      !state.batch &&
      getUIModelFromSharedState(
        context,
        'outboundPaymentHeader',
        'commonDescription'
      ) === undefined
    ) {
      const vb = data?.vendorBills?.[0];
      // vendorbill description
      const desc = `${vb?.vendorName} ${vb?.billNumber}`;

      state.defaultDescriptionValueSet = true;
      return setUIModelInSharedState(
        context,
        'outboundPaymentHeader',
        'commonDescription',
        desc
      );
    }

    if (
      stateManager.isStateModeExpenseReport() &&
      !state.batch &&
      getUIModelFromSharedState(
        context,
        'outboundPaymentHeader',
        'commonDescription'
      ) === undefined
    ) {
      // expense report description
      const desc = `${data?.expenseReports?.[0]?.expenseNumber} ${data?.expenseReportsBankDetails?.[0]?.bankAccountHolderName}`;

      state.defaultDescriptionValueSet = true;
      return setUIModelInSharedState(
        context,
        'outboundPaymentHeader',
        'commonDescription',
        desc
      );
    }
  }
}

export function getAccountId(context: Context) {
  return getUIModelFromSharedState(
    context,
    'outboundPaymentHeader',
    'accountId'
  );
}

export function setAccountId(context: Context, e: number) {
  const bankAccounts = getUIModelFromSharedState(context, 'bankAccounts');
  const externalAccountId = bankAccounts?.find((ba) => ba.accountId === e)
    ?.externalAccountId;

  // Set senderAccountId for bank payments
  setUIModelInSharedState(
    context,
    'outboundPaymentHeader',
    'senderAccountId',
    externalAccountId
  );
  setUIModelInSharedState(context, 'outboundPaymentHeader', 'accountId', e);
}

export function isUpdateStatusVisible(context: Context) {
  const { data } = context;
  const status = data?.outboundPaymentHeader?.status;
  const buttonInvisibleCondition =
    status === EvstPaymentStatus.ManuallyRecorded ||
    status === EvstPaymentStatus.Successful;
  return !buttonInvisibleCondition;
}

export function openPaymentStatusForm(context: Context) {
  const { actions, helpers } = context;
  const id = getUIModelFromSharedState(context, 'outboundPaymentHeader', 'id');

  helpers.openModal({
    title: 'Update {{payment.status}}',
    size: 'xsmall',
    template: `/templates/everest.fin.expense/payment/uinext/paymentStatus?id=${id}`,

    onModalSubmit: async () => {
      helpers.closeModal();
      await actions.refetchUiModelData();
    },
  });
}

export function replaceSpecialCharacters(str: string) {
  return str
    .replaceAll('ä', 'ae')
    .replaceAll('ö', 'oe')
    .replaceAll('ü', 'ue')
    .replaceAll('ß', 'ss');
}
