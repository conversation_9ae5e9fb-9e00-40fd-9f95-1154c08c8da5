{"version": 2, "uicontroller": ["payrollBankPaymentModal.uicontroller.ts"], "uimodel": {"state": {"isProgressBarVisible": false, "progress": 0}, "nodes": {"bankPayment": {"model": "urn:evst:everest:fin/integration/bank:model/node:BankPayment", "type": "struct"}, "outboundPaymentHeader": {"type": "struct", "modelId": "everest.fin.expense/OutboundPaymentHeaderBaseModel.OutboundPaymentHeaderBase", "query": "@controller:getOutboundPaymentHeaderQuery()", "fieldList": ["id", "entityId", "paymentDate", "accountName", "paymentType", "accountId", "paymentId", "senderAccountId", "signUrl", "singlePayment", "commonDescription", "status", "paymentCurrency", "totalPaid"]}, "outboundPaymentLines": {"type": "list", "modelId": "everest.fin.expense/OutboundPaymentItemBaseModel.OutboundPaymentItemBase", "parent": "outbound<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "joinKey": "id-outboundPaymentHeaderId", "fieldList": []}, "bankAccounts": {"type": "list", "query": "@controller:getBankAccountQuery()", "modelId": "everest.fin.accounting/BankAccountModel.BankAccount", "fieldList": ["id", "accountId", "accountNumber", "accountName", "currency", "externalAccountId", "integrationName", "entityId"]}}}, "uiview": {"templateType": "generic", "i18n": "everest.fin.expense/payrollMgmt", "config": {"stretch": true, "hideIcons": true}, "sections": {"content": [{"component": "FieldGroup", "customId": "payrollBlock", "section": {"editing": true, "title": "Confirm Payment Details", "grid": {"size": "12"}}, "props": {"type": "primary", "columns": "1", "elements": [{"component": "Select", "label": "{{payroll.payment.account}}", "idProp": "accountId", "prefixProp": "accountNumber", "textProp": "accountName", "list": "@binding:bankAccounts", "value": "@binding:outboundPaymentHeader.accountId", "isDisabled": true}, {"label": "Payment Currency", "value": "@binding:outboundPaymentHeader.paymentCurrency", "isDisabled": true}, {"label": "Total Payment Amount", "value": "@binding:outboundPaymentHeader.totalPaid", "isDisabled": true}, {"component": "ProgressBar", "label": "Initiating Payment. Please wait...", "progress": "@state:progress", "started": true, "showValue": true, "visible": "@state:isProgressBarVisible"}]}}]}, "actions": {"direction": "horizontal", "content": [{"variant": "secondary", "label": "{{payrollMgmt.cancel}}", "onClick": "@controller:onClickCancel"}, {"variant": "primary", "label": "Initiate", "onClick": "@controller:initiatePayment", "visible": "@controller:isInitiatePaymentButtonVisible()"}, {"variant": "primary", "label": "Retry Payment", "onClick": "@controller:retryPayment", "visible": "@controller:isRetryPaymentButtonVisible()"}]}}}