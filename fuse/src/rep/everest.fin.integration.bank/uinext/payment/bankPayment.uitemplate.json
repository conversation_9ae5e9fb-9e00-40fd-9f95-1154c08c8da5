{"version": 2, "uicontroller": ["bankPayment.uicontroller.ts", "everest.fin.expense/uinext/general.uicontroller.js"], "uimodel": {"state": {"paymentMethod": "manual", "batchPayment": false, "featureToggles": {"transaction": true}, "defaultDescriptionValueSet": false}, "nodes": {"accounts": {"type": "list", "getEnabledAccounts": "@controller:getParametersForGetAccounts()", "modelId": "everest.fin.accounting/AccountModel.Account", "fieldList": []}, "bankAccounts": {"type": "list", "query": {"where": {"$or": [{"type": "checking"}, {"payableAccount": true}], "paymentAllowed": true}}, "modelId": "everest.fin.accounting/BankAccountModel.BankAccount", "fieldList": []}, "bankDetails": {"type": "list", "modelId": "everest.fin.base/BankDetailsModel.BankDetails", "query": {}, "fieldList": []}, "currencies": {"modelId": "everest.base/CurrencyModel.Currency", "pagination": true, "type": "list", "query": {}, "fieldList": ["currencyCode", "currencyText"]}, "bankPayment": {"model": "urn:evst:everest:fin/integration/bank:model/node:BankPayment", "type": "struct"}, "outboundPaymentHeaderBase": {"type": "struct", "modelId": "everest.fin.expense/OutboundPaymentHeaderBaseModel.OutboundPaymentHeaderBase", "fieldList": []}, "outboundPaymentHeader": {"type": "struct", "modelId": "everest.fin.expense/OutboundPaymentHeaderBaseModel.OutboundPaymentHeaderBase", "query": "@controller:getOutboundPaymentHeaderQuery()", "fieldList": ["id", "outboundPaymentHeaderNumber", "paymentDate", "accountName", "paymentType", "accountId", "commonDescription", "paymentId", "senderAccountId", "signUrl", "singlePayment", "userIdForNotifications", "systemExchangeRate", "actualExchangeRate", "convertedAmount", "approvalStatus", "status", "entityName", "paymentCurrency", "totalPaid", "postingPeriod", "JournalEntryHeader-FinancialDocument.posted", "JournalEntryHeader-FinancialDocument.journalEntryNumber", "JournalEntryHeader-FinancialDocument.id"]}, "outboundPaymentLines": {"type": "list", "modelId": "everest.fin.expense/OutboundPaymentItemBaseModel.OutboundPaymentItemBase", "parent": "outbound<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "joinKey": "id-outboundPaymentHeaderId", "fieldList": []}, "vendorBill": {"type": "struct", "modelId": "everest.fin.expense/VendorBillHeaderBaseModel.VendorBillHeaderBase", "fieldList": []}, "vendorBills": {"type": "list", "query": "@controller:getVendorBillsQuery()", "modelId": "everest.fin.expense/VendorBillHeaderBaseModel.VendorBillHeaderBase", "fieldList": ["id", "vendorName", "vendorId", "bill<PERSON><PERSON><PERSON>", "entityName", "entityId", "amountPaid", "currency", "billDate", "paymentStatus", "functionalCurrency", "dueDate", "JournalEntryHeader-FinancialDocument.posted", "JournalEntryHeader-FinancialDocument.journalEntryNumber", "JournalEntryHeader-FinancialDocument.id", "total", "status", "openAmount", "vendorBillHeaderNumber"]}, "vendorBillLines": {"type": "list", "modelId": "everest.fin.expense/VendorBillItemBaseModel.VendorBillItemBase", "parent": "vendorBills", "joinKey": "id-vendorBillHeaderId", "fieldList": ["description", "lineType", "amount", "departmentId", "accountId", "vendorBillHeaderId"]}, "vendorBillBankDetails": {"type": "list", "modelId": "everest.fin.expense/VendorBillBankDetailModel.VendorBillBankDetail", "parent": "vendorBills", "joinKey": "id-vendorBillHeaderId", "fieldList": ["bankAccountNumber", "bankAccountHolderName", "financialInstitutionId", "bankAccountNumberType", "bankAccountNumberTypeText", "bankAccountType", "vendorBillHeaderId"]}, "vendors": {"type": "list", "modelId": "everest.fin.expense/VendorModel.Vendor", "query": {"where": {}}, "fieldList": []}, "entities": {"modelId": "everest.base/EntityModel.Entity", "type": "list", "queryActiveEntities": {"query": {}, "fieldList": ["id", "entityName", "currency"]}, "fieldList": []}, "postingPeriod": {"type": "struct", "modelId": "everest.fin.accounting/AccountingPeriodModel.AccountingPeriod", "findAccountingPeriod": "@controller:findAccountingPeriodQuery()"}, "expenseReport": {"type": "struct", "modelId": "everest.fin.expense/ExpenseReportBaseModel.ExpenseReportBase", "fieldList": []}, "expenseReports": {"type": "list", "query": "@controller:getExpenseReportsQuery()", "modelId": "everest.fin.expense/ExpenseReportBaseModel.ExpenseReportBase", "fieldList": ["id", "expenseNumber", "ExpenseReportBase-Employee.name", "submittedDate", "entityName", "reimbursementCurrency", "reimbursableTotal", "amountPaid", "openAmount", "entityId", "employeeId"]}, "expenseReportsBankDetails": {"type": "list", "modelId": "everest.fin.expense/ExpenseReportBankDetailModel.ExpenseReportBankDetail", "parent": "expenseReports", "joinKey": "id-expenseReportHeaderId", "fieldList": ["bankAccountNumber", "bankAccountHolderName", "financialInstitutionId", "bankAccountNumberType", "bankAccountNumberTypeText", "bankAccountType", "expenseReportHeaderId"]}, "integrations": {"type": "list", "modelId": "everest.fin.integration.bank/IntegrationModel.Integration", "query": {}, "fieldList": []}}}, "uiview": {"templateType": "details", "i18n": "everest.fin.expense/payment", "title": "@controller:getTabTitle()", "config": {"stretch": true, "allowRefreshData": true, "autoRefreshData": true, "grid": {"limited": true}}, "header": {"size": "8", "content": {"title": "@controller:getTitle()", "status": {"value": "@bindingController(outboundPaymentHeader.status,getStatus())"}, "subtitle": "@controller:getSubTitle()"}}, "sections": {"content": [{"component": "FieldGroup", "customId": "outboundBankPaymentHeaderFieldGroupInViewMode", "size": "9", "columns": "4", "type": "primary", "editing": false, "isVisible": "@controller:isStateModePayment()", "elements": [{"label": "{{payment.paymentDate}}", "parseAs": "date", "format": "P", "value": "@binding:outboundPaymentHeader.paymentDate"}, {"label": "{{payment.postingPeriod}}", "value": "@bindingController(outboundPaymentHeader.postingPeriod, getPostingPeriod())"}, {"label": "{{payment.entity}}", "value": "@bindingController:(outboundPaymentLines.entityName, getEntityName())"}, {"label": "{{payment.paymentMethod}}", "value": "@binding:outboundPaymentHeader.paymentType"}, {"label": "{{payment.accountName}}", "value": "@binding:outboundPaymentHeader.accountName"}, {"label": "{{payment.currency}}", "value": "@binding:outboundPaymentHeader.paymentCurrency"}, {"label": "{{payment.description}}", "value": "@binding:outboundPaymentHeader.commonDescription"}, {"label": "{{payment.journalEntry}}", "value": "@controller:getJEHeaderNumber()", "link": "@controller:getJELink()", "isVisible": "@controller:isStateModePayment()"}]}, {"component": "FieldGroup", "customId": "outboundBankPaymentHeaderFieldGroup", "size": "9", "columns": "4", "type": "primary", "editing": true, "isVisible": "@controller:isStateModeBillOrExpenseReportOrEdit()", "elements": [{"label": "{{payment.paymentDate}}", "parseAs": "date", "format": "P", "value": "@bindingController(outboundPaymentHeader.paymentDate, getDate())", "isDisabled": "@controller:isFieldDisabled('paymentDate')", "onChange": "@controller:onChangePaymentDate", "action": "@controller:getAction()"}, {"component": "Input", "label": "{{payment.postingPeriod}}", "value": "@bindingController(outboundPaymentHeader.postingPeriod, getPostingPeriod())", "action": "@controller:getAction()", "isDisabled": true}, {"label": "{{payment.entity}}", "value": "@bindingController:(outboundPaymentHeader.entityName, getEntityName())", "action": "@controller:getAction()", "isDisabled": true}, {"component": "Select", "label": "{{payment.paymentMethod}}", "value": "@bindingController:(outboundPaymentHeader.paymentType, getPaymentType())", "action": "@controller:getAction()", "isDisabled": true}, {"component": "Select", "label": "{{payment.paymentAccount}}", "idProp": "@controller:getComponentValues('idProp')", "prefixProp": "@controller:getComponentValues('prefixProp')", "textProp": "@controller:getComponentValues('textProp')", "value": "@bindingController:(outboundPaymentHeader.accountId, getAccountId())", "list": "@controller:getComponentValues('list')", "isDisabled": "@controller:isFieldDisabled('accountId')", "action": "@controller:getAction()", "onChange": "@controller:setAccountId", "rules": "@controller:validateFieldIsNotEmpty()", "showDescriptionOnLabel": true, "descProp": "currency"}, {"component": "HiddenInput", "value": "@binding:outboundPaymentHeader.accountId", "action": "@controller:getAction()"}, {"label": "{{payment.description}}", "value": "@bindingController:(outboundPaymentHeader.commonDescription, getDefaultDescription())", "isDisabled": "@controller:isFieldDisabled('commonDescription')", "action": "@controller:getAction()", "rules": "@controller:validateDescriptionField()"}, {"component": "HiddenInput", "value": "@bindingController:(outboundPaymentHeader.singlePayment, singlePayment())", "action": "@controller:getAction()"}]}, {"component": "SectionGroup", "size": "3", "isVisible": "@controller:isStateModePaymentOrEdit()", "sections": [{"component": "SummaryText", "size": "12", "date": {}, "summary": {"label": "@controller:getProperAmountLabel('{{payment.total.amount.paid}}','totalAmount')", "parseAs": "currency", "value": "@binding:outboundPaymentHeader.totalPaid"}, "fields": [{"label": "{{payment.totalTransactions}}", "value": "@controller:getTotalTransactions()", "isVisible": "@controller:isTotalTransactionsVisible()"}]}]}, {"component": "SectionGroup", "size": "3", "isVisible": "@controller:isStateModeBillOrExpenseReport()", "sections": [{"component": "SummaryText", "size": "12", "date": {}, "summary": {"label": "@controller:getProperAmountLabel('{{payment.total.amount}}','totalAmount')", "parseAs": "currency", "value": "@controller:calculateTotalAmount()"}, "fields": [{"label": "{{payment.totalTransactions}}", "value": "@controller:getTotalTransactions()", "isVisible": "@controller:isTotalTransactionsVisible()"}], "action": "@controller:getAction()"}]}, {"component": "Table", "editing": false, "size": "12", "customId": "paymentVendorBillLinesTable", "title": "{{payment.details}}", "visible": "@controller:isVBBlockVisible()", "showRowCount": true, "columns": [{"headerName": "ID", "field": "vendorBillHeaderNumber", "maxWidth": 200}, {"headerName": "{{payment.vendor}}", "field": "vendorName"}, {"headerName": "{{payment.billNumber}}", "field": "bill<PERSON><PERSON><PERSON>"}, {"headerName": "{{payment.billDate}}", "field": "billDate"}, {"headerName": "{{payment.dueDate}}", "field": "dueDate"}, {"headerName": "{{payment.entity}}", "field": "entityName"}, {"headerName": "{{payment.journalEntry}}", "field": "JournalEntryHeader-FinancialDocument.journalEntryNumber", "visible": "@controller:isStateModePaymentOrEdit()", "valueGetter": "@controller:JEV<PERSON>ueGetter", "onCellClicked": "@controller:openVBJournalEntry"}, {"headerName": "{{payment.currency}}", "field": "currency"}, {"headerName": "{{payment.amount.paid}}", "field": "amountPaid", "valueGetter": "@controller:amountPaidValueGetter", "visible": "@controller:isFieldVisible('payment')"}, {"headerName": "{{payment.amount.due}}", "field": "openAmount", "visible": "@controller:isFieldVisible('bill')"}, {"headerName": "{{payment.payment.amount}}", "field": "paymentAmount", "valueGetter": "@controller:paymentAmountValueGetter", "visible": "@controller:isFieldVisible('bill')", "fieldProps": {"isEditing": true}}], "data": "@binding:vendorBills", "masterDetail": {"variant": "light", "data": "@binding:vendorBillBankDetails", "groupField": "vendorBillHeaderNumber", "showRowCount": true, "openGroupsByDefault": true, "suppressDeleteDetail": true, "suppressAddDetail": true, "editing": true, "columns": [{"field": "bankAccountHolderName", "fieldProps": {"isEditing": false}}, {"field": "bankAccountNumber", "fieldProps": {"isEditing": false}}, {"field": "financialInstitutionId", "fieldProps": {"isEditing": false}}, {"field": "bankAccountNumberTypeText", "fieldProps": {"isEditing": false}}]}}, {"component": "Table", "editing": false, "size": "12", "title": "{{payment.details}}", "visible": "@controller:isERBlockVisible()", "showRowCount": true, "columns": [{"headerName": "ID", "field": "expenseNumber"}, {"headerName": "{{payment.employee}}", "field": "ExpenseReportBase-Employee.name"}, {"headerName": "{{payment.submittedDate}}", "field": "submittedDate"}, {"headerName": "{{payment.entity}}", "field": "entityName"}, {"headerName": "{{payment.currency}}", "field": "reimbursementCurrency"}, {"headerName": "{{payment.amount.paid}}", "field": "amountPaid", "valueGetter": "@controller:amountPaidValueGetter", "visible": "@controller:isFieldVisible('payment')"}, {"headerName": "{{payment.amount.due}}", "field": "openAmount", "visible": "@controller:isFieldVisible('expenseReport')"}, {"headerName": "{{payment.payment.amount}}", "field": "paymentAmount", "visible": "@controller:isFieldVisible('expenseReport')", "valueGetter": "@controller:paymentAmountValueGetter", "fieldProps": {"isEditing": true}}], "data": "@binding:expenseReports", "masterDetail": {"variant": "light", "data": "@binding:expenseReportsBankDetails", "groupField": "expenseNumber", "showRowCount": true, "openGroupsByDefault": true, "suppressDeleteDetail": true, "suppressAddDetail": true, "editing": true, "columns": [{"field": "bankAccountHolderName", "fieldProps": {"isEditing": false}}, {"field": "bankAccountNumber", "fieldProps": {"isEditing": false}}, {"field": "financialInstitutionId", "fieldProps": {"isEditing": false}}, {"field": "bankAccountNumberTypeText", "fieldProps": {"isEditing": false}}, {"field": "bankAccountType", "fieldProps": {"isEditing": false}}]}}]}, "actions": {"direction": "vertical", "content": [{"variant": "primary", "label": "@controller:getButtonLabel()", "onClick": "@controller:payVendorBill", "visible": "@controller:isFieldVisible('bill')", "disabled": "@controller:isPayButtonDisabled()"}, {"variant": "primary", "label": "@controller:getButtonLabel()", "onClick": "@controller:payExpenseReport", "visible": "@controller:isFieldVisible('expenseReport')", "disabled": "@controller:isPayButtonDisabled()"}, {"variant": "primary", "label": "{{payment.save}}", "onClick": "@controller:saveAction", "visible": "@controller:isStateModeEdit()"}, {"variant": "secondary", "label": "{{payment.cancel}}", "onClick": "@controller:cancelAction", "visible": "@controller:isStateModeEdit()"}, {"variant": "primary", "label": "{{payment.cancel}}", "onClick": "@controller:cancelPaymentAction", "visible": "@controller:isCancelPaymentButtonVisible()"}, {"variant": "primary", "label": "{{payment.retry}}", "onClick": "@controller:retryPaymentAction", "visible": "@controller:isRetryPaymentButtonVisible()"}, {"variant": "secondary", "label": "{{payment.actions}}", "align": "right", "visible": "@controller:isStateModePayment()", "disabled": false, "actions": [{"label": "{{payment.delete}}", "onClick": "@controller:deleteAction", "visible": "@controller:isDeleteActionVisible()", "confirmation": {"message": "{{payment.deleteTitle}}", "description": "{{payment.deleteMessage}}", "cancelLabel": "{{payment.cancel}}", "confirmLabel": "{{payment.delete}}"}}, {"label": "{{payment.signPayment}}", "visible": "@controller:isSignUrlButtonVisible()", "onClick": "@controller:signPayment"}, {"label": "Update {{payment.status}}", "visible": "@controller:isUpdateStatusVisible()", "onClick": "@controller:openPaymentStatusForm"}]}]}}}