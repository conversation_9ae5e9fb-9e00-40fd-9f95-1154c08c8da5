import type { PlainDate } from '@everestsystems/datetime';
import { Decimal } from '@everestsystems/decimal';
import { EvstPaymentApprovalStatus } from '@pkg/everest.fin.base/types/enums/PaymentApprovalStatus';
import { SourcePaymentDocumentType } from '@pkg/everest.fin.expense/public/outboundPayment';
import { OutboundPaymentHeaderBaseUI } from '@pkg/everest.fin.expense/types/OutboundPaymentHeaderBase.ui';
import { BankPaymentUI } from '@pkg/everest.fin.integration.bank/types/BankPayment.ui';
import type { PayrollBankPaymentModalUiTemplate } from '@pkg/everest.fin.integration.bank/types/uiTemplates/uinext/payment/payrollBankPaymentModal.ui';

type PayrollBankPaymentContext =
  PayrollBankPaymentModalUiTemplate.PayrollBankPaymentModalContext;

export type Context = PayrollBankPaymentContext & {
  state: {
    form: { paymentReference: string };
    isProgressBarVisible: boolean;
    mode: 'retryPayment' | 'initiatePayment';
    paymentData: {
      id: number;
      evstBankAccountId: number;
      senderAccountId: string;
      description: string;
      paymentDate: PlainDate;
      serviceName: string;
      paymentLines: {
        amount: Decimal;
        description: string;
        paymentCurrency: string;
        receiverName: string;
        receiverAccount: string;
      }[];
    };
  };
};

export function getBankAccountQuery() {
  return {
    where: {
      $or: [
        {
          type: 'checking',
        },
        {
          payableAccount: true,
        },
      ],
      paymentAllowed: true,
    },
  };
}

export function getOutboundPaymentHeaderQuery(context: Context) {
  const { state } = context;
  return { where: { id: state.paymentData?.id ?? 0 } };
}

export function isInitiatePaymentButtonVisible(context: Context) {
  return context.state.mode === 'initiatePayment';
}

export function isRetryPaymentButtonVisible(context: Context) {
  return context.state.mode === 'retryPayment';
}

export function onClickCancel(context: Context) {
  const { helpers } = context;

  helpers.closeModal();
}

function isValidIPAddress(ip: string): boolean {
  const ipAddressRegex: RegExp =
    /^(25[0-5]|2[0-4]\d|[01]?\d{1,2})\.(25[0-5]|2[0-4]\d|[01]?\d{1,2})\.(25[0-5]|2[0-4]\d|[01]?\d{1,2})\.(25[0-5]|2[0-4]\d|[01]?\d{1,2})$/;
  return ipAddressRegex.test(ip);
}

async function fetchIp() {
  const res = (await fetch('/hook/fetch/ip').then((res) => res.json())) as {
    headers: {
      'x-forwarded-for': string;
      'x-forwarded-port': string;
      'x-forwarded-proto': string;
    };
    ip: string;
  };
  return res.headers['x-forwarded-for'].split(',')[0];
}

export async function retryPayment(context: Context) {
  const { state, helpers } = context;

  if (state?.mode !== 'retryPayment' || !state.paymentData?.id) {
    return;
  }

  const ip = await fetchIp();

  if (!isValidIPAddress(ip)) {
    console.error(ip, 'wrong ip address');
  }

  const response = (await BankPaymentUI.client.retryPayment({
    paymentHeaderId: state.paymentData.id,
    clientIP: ip,
  })) as unknown as {
    bankPayment: OutboundPaymentHeaderBaseUI.OutboundPaymentHeaderBase & {
      sourceFinancialDocumentIds: number[];
    };
  };

  if (response?.bankPayment?.signUrl) {
    const signUrl = response.bankPayment.signUrl;
    window.open(signUrl, '_blank', 'width=500, height=500');

    helpers.showNotificationMessage({
      message: 'Payroll Payment Initiated Successfully.',
      type: 'success',
      duration: 5,
    });
    helpers.closeModal();
  } else {
    helpers.showNotificationMessage({
      message: 'Retry Payroll Payment Failed. Please try again.',
      type: 'error',
      duration: 5,
    });
  }
}

export async function initiatePayment(context: Context) {
  const { state, data, helpers } = context;
  const { paymentData } = state;
  const {
    paymentLines,
    serviceName,
    senderAccountId,
    description,
    paymentDate,
  } = paymentData ?? {};

  state.isProgressBarVisible = true;
  state.progress = 20;

  /**
   * We need to add as much as validation possible at many places to avoid any error at the end of the process.
   */
  if (
    !data.outboundPaymentHeader?.accountId ||
    !senderAccountId ||
    senderAccountId?.trim()?.length === 0
  ) {
    helpers.showToast({
      key: 'payrollPayment',
      title: 'Payroll Payment Initiation Failed',
      message:
        'A valid bank account is needed for bank payment. Please select a valid bank account.',
      type: 'error',
    });
    state.isProgressBarVisible = false;
    return;
  }
  if (!paymentLines || paymentLines.length === 0) {
    helpers.showToast({
      key: 'payrollPayment',
      title: 'Payroll Payment Initiation Failed',
      message:
        'Missing payment lines. Please select at least one payroll payment application.',
      type: 'error',
    });
    state.isProgressBarVisible = false;
    return;
  }

  const allLinesHaveValidPaymentAmount = paymentLines.every((pl) =>
    new Decimal(pl.amount).greaterThan(0)
  );
  const allLinesHaveValidIban = paymentLines.every(
    (pl) => pl.receiverAccount?.trim()?.length > 15
  );

  if (!allLinesHaveValidPaymentAmount || !allLinesHaveValidIban) {
    helpers.showToast({
      key: 'payrollPayment',
      title: 'Payroll Payment Initiation Failed',
      message:
        'Missing required payment details. Either receipient payment amount or IBAN is missing.',
      type: 'error',
    });
    state.isProgressBarVisible = false;
    return;
  }

  const redirectUri = await BankPaymentUI.client.getPaymentRedirectURL({});

  const payments = paymentLines.map((line) => {
    return {
      currency: line.paymentCurrency,
      amount: line.amount,
      receiverName: line.receiverName,
      receiverAccount: line.receiverAccount,
      description: line.description ?? `Payment for ${line.receiverName}`,
    };
  });

  let paymentResult;
  try {
    paymentResult = await BankPaymentUI.client.initiateBulkPayment({
      serviceName,
      accountId: senderAccountId,
      description,
      batchBookingPreferred: false,
      date: paymentDate,
      isFuturePaymentDate: false,
      redirectUri,
      payments,
    });
  } catch (error) {
    console.error('Error initiating bank payment:', error);

    helpers.showToast({
      title: 'Failed to initiate bank payment',
      message: error?.message,
      type: 'error',
      duration: 5,
    });
    state.isProgressBarVisible = false;
    return;
  }

  state.progress = 70;
  if (
    !paymentResult ||
    !paymentResult.paymentHeader?.id ||
    !paymentResult.authorizationLink ||
    paymentResult?.error ||
    paymentResult?.message
  ) {
    helpers.showNotificationMessage({
      message:
        'Oops it seems like something went wrong. Payment failed, please try again.',
      type: 'error',
      duration: 5,
    });
    state.isProgressBarVisible = false;
  } else {
    const paymentHeader = {
      id: data.outboundPaymentHeader.id,
      accountId: data.outboundPaymentHeader.accountId,
      entityId: data.outboundPaymentHeader.entityId,
      paymentDate: data.outboundPaymentHeader.paymentDate,
      approvalStatus: EvstPaymentApprovalStatus.Approved,
      authorizedIntegration: serviceName,
      paymentId: paymentResult.paymentHeader.id,
      signUrl: paymentResult.authorizationLink,
    };

    const paymentLines = data.outboundPaymentLines.map((line) => {
      return {
        id: line.id,
        entityId: line.entityId,
        paymentAmount: line.paymentAmount,
        sourcePaymentDocumentId: line.sourcePaymentDocumentId,
        sourcePaymentDocumentPath: line.sourcePaymentDocumentPath,
        description: line.description,
      };
    });

    const signUrl = paymentResult.authorizationLink;

    let response;

    /**
     * We first try to upsert using the upsertBankOutboundPayment method.
     * If it fails, we try to update the payment header directly because this update
     * is needed at any cost when we reaches this point as actual payment may have been
     * made and afterSucces hook will try to update linked payment record status.
     */

    try {
      response =
        await OutboundPaymentHeaderBaseUI.client.upsertBankOutboundPayment({
          paymentHeader,
          paymentLines,
          documentType: SourcePaymentDocumentType.PayrollPaymentApplication,
          options: { draft: false },
        });
    } catch (error) {
      // TODO: Should we try direct update here ??

      console.error('Error upserting bank outbound payment:', error);
      helpers.showToast({
        title: 'Failed to update bank payment',
        message: error?.message,
        type: 'error',
        duration: 5,
      });
      state.isProgressBarVisible = false;
      return;
    }

    if (response?.id > 0) {
      state.progress = 100;

      // Adding a bit delay for better user experience
      setTimeout(() => {
        // Open window to complete actual payment from bank
        window.open(signUrl, '_blank', 'width=500, height=500');

        /**
         * After this we don't have any control over the payment process. So we just show a success message.
         * Actual payment status will be updated in afterSuccess hook.
         */
        helpers.showNotificationMessage({
          message: 'Payroll Payment Initiated Successfully.',
          type: 'success',
          duration: 5,
        });
        helpers.closeModal();
      }, 1300);
    } else {
      helpers.showNotificationMessage({
        message: 'Payroll Payment Initiation Failed. Please try again.',
        type: 'error',
        duration: 5,
      });
    }
  }
}
