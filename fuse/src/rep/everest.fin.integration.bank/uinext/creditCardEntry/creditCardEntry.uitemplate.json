{"version": 2, "uicontroller": "creditCardEntry.uicontroller.ts", "uimodel": {"state": {"multiMatch": false, "mode": "view", "accountsDataFetched": false}, "nodes": {"postingPeriods": {"type": "struct", "modelId": "everest.fin.accounting/AccountingPeriodModel.AccountingPeriod", "fieldList": []}, "transactions": {"type": "list", "pagination": true, "query": "@controller:getCreditCardTransactionQuery()", "modelId": "everest.fin.integration.bank/BankTransactionNewModel.BankTransactionNew", "fieldList": ["bankAccountId", "matchStatus", "matchingTransaction", "accountName", "amount", "currency", "externalCreationDate", "payeeOrPayor", "externalDescription", "entityId", "id", "entity", "coaAccountId", "BankTransactionNew-BankAccount.coaAccountType", "BankTransactionNew-BankAccount.BankAccount-FinancialInstitution.logoId", "BankTransactionNew-BankAccount.BankAccount-FinancialInstitution.logoUrl", "matchId"]}, "matchDocuments": {"type": "list", "query": "@controller:getMatchDocumentQuery()", "modelId": "everest.fin.integration.bank/MatchSourceFinancialDocumentModel.MatchSourceFinancialDocument", "fieldList": ["id", "bankTransactionMatchId", "sourceJournalEntryLineId", "amount", "journalEntryHeaderId", "journalEntryLineDescription", "postingDate", "business<PERSON><PERSON>ner", "matchDocumentWithoutLink"]}, "creditCardEntryHeader": {"type": "struct", "query": "@controller:getCCEntryHeaderQuery()", "modelId": "everest.fin.accounting/CreditCardEntryHeaderModel.CreditCardEntryHeader", "fieldList": ["id", "creditCardEntryName", "creditCardEntryDescription", "creditCardEntryHeaderNumber", "transactionDate", "postingPeriod", "postingPeriodId", "entityId", "entity", "currency", "journalEntryNumber", "journalEntryHeaderId", "status", "containsPrepaidLines", "containsBillableItem", "accountId", "origin", "externalUrl"]}, "creditCardEntryLines": {"type": "list", "parent": "creditCardEntryHeader", "joinKey": "id-creditCardEntryHeaderId", "modelId": "everest.fin.accounting/CreditCardEntryLineModel.CreditCardEntryLine", "fieldList": ["creditCardEntryHeaderId", "departmentId", "businessPartnerId", "description", "accountId", "amount", "id", "account", "accountNumber", "lineType", "amortizationScheduleHeaderId", "employeeId", "businessUnitId", "billableExpense", "customerId"]}, "creditCardEntryAttachments": {"type": "list", "parent": "creditCardEntryHeader", "joinKey": "id-creditCardEntryHeaderId", "modelId": "everest.fin.accounting/CreditCardEntryAttachmentModel.CreditCardEntryAttachment", "fieldList": ["id", "creditCardEntryHeaderId", "fileId", "fileName"]}, "bankTransactionMatchesTransient": {"type": "list", "modelId": "everest.fin.integration.bank.matching/BankTransactionMatchTransientModel.BankTransactionMatchTransient"}, "matchingSFD": {"type": "struct", "modelId": "everest.fin.integration.bank/MatchSourceFinancialDocumentModel.MatchSourceFinancialDocument"}, "account": {"type": "struct", "modelId": "everest.fin.accounting/AccountModel.Account"}, "employee": {"type": "struct", "modelId": "everest.hr.base/EmployeeModel.Employee"}, "businessUnitEnabled": {"modelId": "everest.appserver/ConfigurationModel.Configuration", "type": "struct", "getValueOrDefault": {"name": "BUSINESS_UNIT_ENABLED", "packageName": "everest.base", "defaultValue": true}, "fieldList": ["value"]}, "amortizationScheduleHeaders": {"type": "list", "query": "@controller:getAmortizationQuery()", "modelId": "everest.fin.accounting/AmortizationScheduleHeaderModel.AmortizationScheduleHeader", "fieldList": ["id", "AmortizationScheduleHeader-JournalEntryLine.sourceTransLineId", "AmortizationScheduleHeader-JournalEntryLine.JournalEntryLine-JournalEntryHeader.sourceFinancialDocumentId", "status", "sourceFinancialDocumentId", "amount", "amortizationStartDate", "term", "amortizationEndDate", "deferredAccountId", "amortizationMethod", "recognitionAccountId"]}}}, "uiview": {"templateType": "details", "config": {"allowRefreshData": true, "stamp": "@controller:voidStamp()"}, "i18n": ["creditCardEntry", "everest.fin.accounting/amortization"], "title": "@binding:creditCardEntryHeader.creditCardEntryHeaderNumber", "header": {"size": "medium", "editing": {"title": "@controller:isHeaderEditable()", "description": "@controller:isHeaderEditable()"}, "placeholders": {"title": "{{creditCard.transaction.creditCardEntry}}", "description": "{{creditCard.transaction.description}}"}, "content": {"title": "@bindingController(creditCardEntryHeader.creditCardEntryName, getHeaderTitle())", "description": "@binding:creditCardEntryHeader.creditCardEntryDescription", "status": "@controller:getHeaderStatus()", "showAvatar": false}}, "sections": {"content": [{"component": "Cards", "title": "{{creditCard.transaction.matchedTo}}", "size": "12", "isVisible": "@controller:isMatchToBoxVisible()", "loadingQuantity": 1, "cardsSize": "medium", "data": "@controller:getMatchingCardData()"}, {"component": "FieldGroup", "customId": "creditCardEntryHeader", "title": "{{creditCard.details}}", "size": "12", "editing": "@controller:isBlockEditable()", "elements": [{"component": "HiddenInput", "value": "@binding:creditCardEntryHeader.id", "action": "@controller:getAction()"}, {"component": "Input", "label": "{{creditCard.journalEntry}}", "value": "@binding:creditCardEntryHeader.journalEntryNumber", "link": "@controller:populateJournalEntryLink()", "isVisible": "@controller:isViewMode()", "isDisabled": true}, {"component": "Input", "label": "{{creditCard.entity}}", "value": "@bindingController(creditCardEntryHeader.entity, getEntity())", "isDisabled": true}, {"component": "DatePicker", "label": "{{creditCard.transaction}}", "isEditing": "@controller:isBlockEditable()", "action": "@controller:getAction()", "value": "@bindingController(creditCardEntryHeader.transactionDate, getTransactionDate())", "onChange": "@controller:fetchPostingPeriod", "rules": "@controller:validatePostingDate()"}, {"component": "Input", "label": "{{creditCard.posting}}", "name": "postingPeriodNameField", "value": "@state:postingPeriodName", "isDisabled": true}, {"label": "{{creditCard.source}}", "value": "@binding:creditCardEntryHeader.origin", "externalLink": "@binding:creditCardEntryHeader.externalUrl", "isVisible": "@controller:isSourceVisible()"}]}, {"component": "Table", "size": "12", "variant": "dark", "title": "{{creditCard.entryLines}}", "customId": "creditCardEntryLinesTable", "pagination": true, "showRowCount": true, "suppressDelete": "@controller:suppressDelete()", "addRowsOnEmpty": true, "editing": "@controller:isTableEditable()", "addRows": "@controller:canAddRows()", "onTableLoaded": "@controller:onTableLoaded", "columns": [{"headerName": "Account", "field": "accountId", "fieldProps": {"isEditing": "@controller:isAccountAndDepartmentEditable()", "semanticTypeUiParams": "@controller:getAccountSemanticTypeUiParams", "onChange": "@controller:setLineType"}}, {"headerName": "Amount", "field": "amount", "valueGetter": "@controller:setFirstCreditCardEntryLineAmount", "fieldProps": {"placeholder": "Enter amount"}}, {"headerName": "Description", "field": "description", "fieldProps": {"placeholder": "{{creditCard.placeHolder.enterDescription}}"}}, {"headerName": "Business Partner", "field": "businessPartnerId", "fieldProps": {"suffixCornerProp": "businessPartnerNumber", "placeholder": "Select Business Partner"}, "onCellClicked": "@controller:goToBusinessPartnerDetails", "cellLinkEmphasized": true}, {"headerName": "{{creditCard.department}}", "field": "departmentId", "fieldProps": {"isEditing": "@controller:isAccountAndDepartmentEditable()", "placeholder": "{{creditCard.placeHolder.selectDepartment}}"}}, {"headerName": "{{creditCard.employee}}", "field": "employeeId", "fieldProps": {"placeholder": "{{creditCard.placeHolder.selectEmployee}}"}}, {"headerName": "Billable Expense", "field": "billableExpense", "fieldProps": {"onChange": "@controller:handleBillableExpenseChange"}}, {"headerName": "Customer", "field": "customerId", "initialHide": true, "fieldProps": {"isDisabled": "@controller:isCustomerSelectionDisabled"}}, {"headerName": "{{creditCard.businessUnit}}", "field": "businessUnitId", "fieldProps": {"placeholder": "{{creditCard.placeHolder.selectBusinessUnit}}"}, "visible": "@binding:businessUnitEnabled"}, {"headerName": "{{creditCard.amortization}}", "field": "_", "sortable": false, "initialHide": true, "suppressFilter": true, "suppressColumnsToolPanel": true, "cellLinkEmphasized": true, "valueGetter": "@controller:amortizationLinkValueGetter", "disableCellClick": "@controller:isAddScheduleActionDisabled", "onCellClicked": "@controller:clickActionOfAmortizationScheduelCell", "cellVariant": "@controller:getAmortizationScheduleCellVariant()", "fieldProps": {"isEditing": false, "nullContent": "custom", "customNullContent": "{{creditCard.amortization.nullContent.message}}"}}], "data": "@binding:creditCardEntryLines"}, {"component": "Upload", "customId": "attachments", "section": {"title": "{{creditCard.attachments}}", "grid": {"size": "6"}, "customId": "attachments"}, "props": {"name": "attachment", "version": 2, "everestPackage": "everest.fin.accounting", "multiple": true, "disabled": false, "isEditing": "@controller:isAttachmentsEditable()", "uploadHandler": "postgres", "variant": "ButtonView", "acceptableMimeTypes": ["application/pdf", "image/tiff", "image/jpeg", "image/png", "image/jpg", "application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"], "maxSizeInMB": 10, "action": "@controller:getAction()", "attachmentFiles": "@controller:getAttachments()", "onRemove": "@controller:handleAttachmentRemove"}}, {"component": "Summary", "section": {"title": "Match Check"}, "props": {"variant": "small", "columns": "@controller:getColumns()", "fields": [{"label": "Card Transaction", "value": "@controller:getBankTransactionDetails()"}, {"label": "Selected documents", "value": "@controller:getMatchDocumentDetails()", "visible": "@controller:isOtherDocumentsSectionVisible()"}, {"label": "New Transaction", "value": "@controller:getCreditCardEntryLinesTotalAmount()"}, {"label": "Difference", "value": "@state:diff"}]}}]}, "actions": {"content": [{"variant": "primary", "label": "@controller:getPrimaryButtonLabel()", "align": "right", "visible": "@controller:isSaveButtonVisible()", "onClick": "@controller:saveEntry", "disabled": "@state:saving"}, {"variant": "secondary", "label": "Cancel", "align": "right", "onClick": "@controller:cancel", "visible": "@controller:isCancelButtonVisible()", "disabled": "@state:saving"}, {"variant": "primary", "label": "Actions", "visible": "@controller:isCreditCardVoidVisible()", "actions": [{"label": "Edit", "onClick": "@controller:saveEntry", "disabled": "@state:saving"}, {"label": "{{creditCard.entries.void.button}}", "onClick": "@controller:confirmVoidCreditCardEntry", "disabled": "@state:saving"}]}]}}}