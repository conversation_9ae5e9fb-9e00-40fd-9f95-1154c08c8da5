// @i18n:creditCardEntry
// @i18n:everest.fin.accounting/amortization

import type { UIExecutionContext } from '@everestsystems/content-core';
import { Decimal } from '@everestsystems/decimal';
import { toCurrencyValueString } from '@pkg/everest.base/public/currency/precision';
import {
  getUIModelFromSharedState,
  setUIModelInSharedState,
} from '@pkg/everest.base/public/utils/ui/sharedStateUtils.ui';
import type { EvstBaseCurrency } from '@pkg/everest.base/types/enums/BaseCurrency';
import { toPlainDate } from '@pkg/everest.fin.accounting/public/utils/Date/date';
import { AccountUI } from '@pkg/everest.fin.accounting/types/Account.ui';
import type { AmortizationScheduleHeader } from '@pkg/everest.fin.accounting/types/AmortizationScheduleHeader';
import type { AmortizationScheduleLine } from '@pkg/everest.fin.accounting/types/AmortizationScheduleLine';
import { CreditCardEntryHeaderUI } from '@pkg/everest.fin.accounting/types/CreditCardEntryHeader.ui';
import { EvstAccountSubType } from '@pkg/everest.fin.accounting/types/enums/AccountSubType';
import { EvstAccountType } from '@pkg/everest.fin.accounting/types/enums/AccountType';
import { EvstCreditCardEntryLineType } from '@pkg/everest.fin.accounting/types/enums/CreditCardEntryLineType';
import { EvstCreditCardStatus } from '@pkg/everest.fin.accounting/types/enums/CreditCardStatus';
import { EvstBillableExpenseType } from '@pkg/everest.fin.base/types/enums/BillableExpenseType';
import type {
  CreditCardEntryHeader,
  CreditCardEntryUiTemplate,
  SingleNodeItemType,
} from '@pkg/everest.fin.integration.bank/types/uiTemplates/uinext/creditCardEntry/creditCardEntry.ui';
import { EmployeeUI } from '@pkg/everest.hr.base/types/Employee.ui';
import { pick } from 'lodash';

type CreditCardEntryContext = CreditCardEntryUiTemplate.CreditCardEntryContext;

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type Any = any;

export type Context = CreditCardEntryContext & {
  state: {
    mode: 'create' | 'edit' | 'view';
    id: number;
    bankTransactionId: number;
    bankTransactionIds: number[];
    matchIds: number[];
    entityId: number;
    srcAccountId: number;
    postingPeriod: Any;
    transactionDate: Date;
    postingPeriodId: number;
    diff: Any;
    saving: boolean;
    postingPeriodName: string;
    multiMatch: boolean;
    param: Any;
    journalEntryNumber: string;
    accountId: number;
    departmentId: number;
    date: Date;
    form: Any;
    isFirstCreditCardEntryHasDefaultValue: boolean;
    amortizationData: Map<
      string,
      {
        header: Partial<AmortizationScheduleHeader.AmortizationScheduleHeader>;
        line: Partial<AmortizationScheduleLine.AmortizationScheduleLine>;
      }
    >;
    attachments: { fileId: string; fileName: string }[];
  };
  Decimal: Decimal;
};

type CardData = {
  picture: string;
  pictureAlt: string;
  title: string;
  description: string;
  summary: UIExecutionContext.FieldValue;
};

const CREDIT_CARD_ENTRY = 'Credit Card Entry';
const NEW = 'NEW';
const EDIT = 'EDIT';
const MATCHED = 'MATCHED';
const VOIDED = 'VOIDED';
const INTEGRATED = 'INTEGRATED';
const AMORTIZATION_SCHEDULE_LINK =
  '/templates/everest.fin.accounting/uinext/amortization/amortizationSchedule';
const DRAFT_AMORTIZATION_SCHEDULE_LINK =
  '/templates/everest.fin.accounting/uinext/amortization/createAmortizationSchedule';

export function getCCEntryHeaderQuery(context: Context) {
  if (isCreateMode(context)) {
    return;
  }

  return { where: { id: context.state.id } };
}

export function getAmortizationQuery({ data }: Context) {
  const { creditCardEntryLines } = data;
  const amortizations = (creditCardEntryLines ?? [])
    .map((line) => line?.amortizationScheduleHeaderId)
    .filter(Boolean);
  if (amortizations) {
    return { where: { id: { $in: amortizations } }, draft: 'exclude' };
  }
}

export function isViewMode(context: Context) {
  const { state } = context;
  return state.mode === 'view';
}

export function isCreateMode(context: Context) {
  const { state } = context;
  return state.mode === 'create';
}

export function isCCEIntegrated({ data }: Context) {
  return data.creditCardEntryHeader?.status === EvstCreditCardStatus.Integrated;
}

export function allowedToEdit(context: Context) {
  return isNotViewMode(context) && !isCCEIntegrated(context);
}

export function anyTransactionsConnected({ data }: Context) {
  return (data?.transactions ?? []).length > 0;
}

export function isHeaderEditable(context: Context) {
  return allowedToEdit(context);
}

export function isEditMode(context: Context) {
  const { state } = context;
  return state.mode === 'edit';
}

export function isNotViewMode(context: Context) {
  const { state } = context;
  return state.mode !== 'view';
}

export const isEntryVoided = (context: Context) => {
  return (
    context.data.creditCardEntryHeader?.status === EvstCreditCardStatus.Voided
  );
};

export const isEntryFromExternalService = (context: Context) => {
  return (
    context.data.creditCardEntryHeader?.status ===
    EvstCreditCardStatus.Integrated
  );
};

export function isMatchToBoxVisible(context: Context) {
  return !isEntryVoided(context) && !isEntryFromExternalService(context);
}

export function getAction(context: Context) {
  return isCreateMode(context) ? 'create' : 'update';
}

export function activateEditMode(context: Context) {
  const { state } = context;
  state.mode = 'edit';
}

export function getHeaderStatus(context: Context) {
  switch (context.state.mode) {
    case 'create': {
      return NEW;
    }
    case 'edit': {
      return EDIT;
    }
    case 'view': {
      switch (context?.data?.creditCardEntryHeader?.status) {
        case EvstCreditCardStatus.Voided: {
          return VOIDED;
        }
        case EvstCreditCardStatus.Integrated: {
          return INTEGRATED;
        }
        default: {
          return MATCHED;
        }
      }
    }
    default: {
      return '';
    }
  }
}

export function getHeaderTitle(context: Context) {
  const { data } = context;
  return data?.creditCardEntryHeader?.creditCardEntryName ?? CREDIT_CARD_ENTRY;
}

export function getMatchingCardData(context: Context) {
  const selectedTransactions = getTransactionData(context);
  const matchDocuments = getMatchDocumentData(context);

  return [...selectedTransactions, ...matchDocuments];
}

export function getTransactionData(context: Context): CardData[] {
  const { data, helpers } = context;
  const { transactions } = data;
  if (transactions && transactions.length > 0) {
    return transactions.map((bt) => {
      const transactionDate = helpers.parseValue({
        value: new Date(toPlainDate(bt.externalCreationDate).toISO()),
        parseAs: 'date',
      });
      let picture = '';
      if (
        bt['BankTransactionNew-BankAccount']?.[
          'BankAccount-FinancialInstitution'
        ]?.logoUrl
      ) {
        const institutionLogoUrl =
          bt['BankTransactionNew-BankAccount']?.[
            'BankAccount-FinancialInstitution'
          ]?.logoUrl;

        // Format the URL appropriately based on whether it's already a URL or base64 data
        picture =
          institutionLogoUrl.startsWith('http://') ||
          institutionLogoUrl.startsWith('https://')
            ? institutionLogoUrl
            : `data:image/png;base64,${institutionLogoUrl}`;
      } else if (
        bt['BankTransactionNew-BankAccount']?.[
          'BankAccount-FinancialInstitution'
        ]?.logoId
      ) {
        const logoId =
          bt['BankTransactionNew-BankAccount']?.[
            'BankAccount-FinancialInstitution'
          ]?.logoId;
        picture = `/api/app/storage/${logoId}`;
      }
      return {
        picture,
        pictureAlt: bt.accountName,
        title: `${bt.accountName} on ${transactionDate} in ${bt.currency}`,
        description: bt.externalDescription,
        summary: helpers.parseValue({
          value: bt?.amount?.amount,
          format: bt.currency,
          parseAs: 'currency',
        }),
      };
    });
  }
  return [];
}

export function getMatchDocumentData(context: Context): CardData[] {
  const { data, helpers } = context;
  const { transactions, matchDocuments } = data;
  if (
    transactions &&
    transactions.length > 0 &&
    matchDocuments &&
    matchDocuments.length > 0
  ) {
    return matchDocuments.map((md) => {
      const {
        postingDate,
        matchDocumentWithoutLink,
        journalEntryLineDescription,
        businessPartner,
        amount,
      } = md;
      const date = helpers.parseValue({
        value: postingDate,
        parseAs: 'date',
      });
      let description = `${date}`;
      if (businessPartner) {
        description = description + ', ' + businessPartner;
      }
      if (journalEntryLineDescription) {
        description = description + ', ' + journalEntryLineDescription;
      }
      const title = matchDocumentWithoutLink;
      return {
        picture: '',
        pictureAlt: '',
        title,
        description,
        summary: helpers.parseValue({
          value: amount,
          format: transactions[0].currency,
          parseAs: 'currency',
        }),
      };
    });
  }
  return [];
}

export function getCreditCardTransactionQuery(context: Context) {
  const { state } = context;
  const orderBy = [
    {
      field: 'externalCreationDate',
      ordering: 'desc',
    },
    {
      field: 'id',
      ordering: 'desc',
    },
  ];
  if (state?.bankTransactionIds && isCreateMode(context)) {
    return {
      where: { id: { $in: state.bankTransactionIds } },
      orderBy,
    };
  } else {
    const creditCardEntryHeaderId = state.id;
    const associationName =
      'BankTransaction-BankTransactionMatch.MatchSourceFinancialDocument-BankTransactionMatch';
    return {
      where: {
        [`${associationName}.sfdId`]: creditCardEntryHeaderId,
        [`${associationName}.sfdType`]: 'creditCardEntry',
      },
      orderBy,
    };
  }
}

export function getMatchDocumentQuery(context: Context) {
  const { data, state } = context;
  const orderBy = [
    {
      field: 'amount',
      ordering: 'desc',
    },
  ];
  if (isCreateMode(context)) {
    const matchesArray = state.matchIds ?? [];
    return {
      where: { id: { $in: matchesArray } },
      orderBy,
    };
  } else {
    const { transactions } = data;
    if (transactions && transactions.length > 0) {
      return {
        where: {
          'MatchSourceFinancialDocument-BankTransactionMatch.BankTransaction-BankTransactionMatch.id':
            { $in: transactions.map(({ id }) => id) },
          sfdType: { $ne: 'creditCardEntry' },
        },
        orderBy,
      };
    }
  }
}

export function populateJournalEntryLink(context: Context) {
  if (isCreateMode(context)) {
    return;
  }
  const id = context?.data?.creditCardEntryHeader?.journalEntryHeaderId;
  if (!id) {
    return;
  }

  return {
    visible: true,
    to: '@uicode:journalEntry',
    qs: {
      id,
    },
  };
}

export function isBlockEditable(context: Context) {
  return isEditMode(context) && !isCCEIntegrated(context);
}

export function getEntity(context: Context) {
  if (isCreateMode(context)) {
    const { data } = context;
    const { transactions } = data;
    if (transactions && transactions.length > 0) {
      const [{ entity }] = transactions;
      return entity;
    }
  }
  return context?.data?.creditCardEntryHeader?.entity;
}

export async function getTransactionDate(context: Context) {
  const { data, state } = context;
  let date;
  if (isCreateMode(context)) {
    const { transactions } = data;
    if (transactions && transactions.length > 0) {
      const [{ externalCreationDate }] = transactions;
      date = externalCreationDate;
    }
  } else {
    date = data?.creditCardEntryHeader?.transactionDate;
  }

  if (!state?.postingPeriodId && date) {
    await fetchPostingPeriod(context, date);
  }

  return date;
}

export function getEntityId({ data }: Context) {
  return (
    data?.creditCardEntryHeader?.entityId ?? data?.transactions?.[0]?.entityId
  );
}

export async function fetchPostingPeriod(context: Context, date: Any) {
  const { actions, state } = context;

  const entityId = getEntityId(context);

  if (!date || !entityId) {
    state.postingPeriodId ??= undefined;
    state.postingPeriodName ??= undefined;
  } else {
    const response = await actions.run({
      postingPeriods: {
        action: 'findAccountingPeriod',
        data: {
          entityId,
          postingDate: date,
          forPosting: false,
        },
        fieldList: ['id', 'periodName', 'canBePosted'],
      },
    });
    const postingPeriod = response?.postingPeriods ?? {};
    state.postingPeriodId = postingPeriod?.id;
    state.postingPeriodName = postingPeriod?.periodName;
    state.postingPeriod = postingPeriod;
    state.date = date;
    setUIModelInSharedState(
      context,
      'creditCardEntryHeader',
      'transactionDate',
      date
    );
  }
}

export function voidStamp(context: Context) {
  const { data } = context;

  if (data?.creditCardEntryHeader?.status === EvstCreditCardStatus.Voided) {
    return 'VOID';
  }
}

export function validatePostingDate(context: Context) {
  const { state } = context;

  return {
    validate: (newValue: Any) => {
      if (!state.postingPeriod?.canBePosted) {
        return '{{creditCard.postingDate.notFound}}';
      }
      return !!newValue;
    },
  };
}

export function isSourceVisible(context: Context) {
  return !!context.data?.creditCardEntryHeader?.origin;
}
export function suppressDelete(context: Context) {
  return isViewMode(context);
}

export function isTableEditable(context: Context) {
  return allowedToEdit(context);
}

export function isAccountAndDepartmentEditable(context: Context) {
  return isNotViewMode(context);
}

export function canAddRows(context: Context) {
  return allowedToEdit(context);
}

export function getAccountSemanticTypeUiParams(context: Context) {
  const { data } = context;
  const { transactions } = data;
  const hiddenAccountSubTypes = [
    EvstAccountSubType.Bank,
    EvstAccountSubType.CreditCard,
  ];
  if (transactions && transactions.length > 0) {
    const [{ currency, entityId, coaAccountId }] = transactions;
    return {
      currency,
      entityId,
      srcAccountId: coaAccountId,
      hiddenAccountSubTypes,
      eliminationStatus: false,
    };
  }
}

export function getColumns(context: Context) {
  const { data } = context;
  const { matchDocuments } = data;
  if (matchDocuments && matchDocuments.length > 0) {
    return 4;
  }
  return 3;
}

export function getBankTransactionDetails(context: Context) {
  const { data, helpers, Decimal } = context;

  let totalAmount = new Decimal(0);
  if (!data?.transactions) {
    return helpers.parseValue({
      value: totalAmount.toFixed(2),
      parseAs: 'currency',
    });
  }
  for (const { amount } of data.transactions ?? []) {
    totalAmount = totalAmount.plus(new Decimal(amount?.amount ?? 0));
  }
  const detailedAmount = helpers.parseValue({
    value: totalAmount.toFixed(2),
    format: data?.transactions?.[0]?.currency,
    parseAs: 'currency',
  });

  return detailedAmount;
}

export function calculateOtherSuggestionsTotal({ data }: Context) {
  let totalAmount = new Decimal(0);
  const previouslyShownMatchDocument = new Set<string>();

  for (const {
    amount,
    matchDocumentWithoutLink,
    sourceJournalEntryLineId,
  } of data?.matchDocuments ?? []) {
    const combinedKey = `${matchDocumentWithoutLink}-${sourceJournalEntryLineId}`;

    if (previouslyShownMatchDocument.has(combinedKey)) {
      continue;
    } else {
      previouslyShownMatchDocument.add(combinedKey);
    }
    totalAmount = totalAmount.plus(new Decimal(amount ?? 0));
  }
  return totalAmount;
}

export function getMatchDocumentDetails(context: Context) {
  const { data, helpers } = context;
  const totalAmount = calculateOtherSuggestionsTotal(context);
  return helpers.parseValue({
    value: totalAmount.toFixed(2),
    format: data?.transactions?.[0]?.currency,
    parseAs: 'currency',
  });
}

export function isOtherDocumentsSectionVisible(context: Context) {
  return getColumns(context) === 4;
}

export function getCreditCardEntryLinesTotalAmount(context: Context) {
  const creditcardEntryLineValues = getFormValueUsingSharedState(
    context,
    'creditCardEntryLines',
    'amount',
    ['update', 'create']
  );
  let total = new Decimal(0);
  for (const line of creditcardEntryLineValues ?? []) {
    const enteredValue = line?.amount?.amount ?? 0;
    const amount = new Decimal(enteredValue).isNaN()
      ? new Decimal(0)
      : new Decimal(enteredValue);

    total = total.plus(amount);
  }

  return setDiffAndTotal(context, total);
}

export function getFormValueUsingSharedState(
  context: Context,
  modelName: string,
  fieldName: string,
  actions?: string[]
) {
  const { sharedState, helpers } = context;
  const modelInstance = sharedState.getModelData(modelName)?.instance;

  if (modelInstance) {
    if (Array.isArray(modelInstance)) {
      const filteredFormValues = actions
        ? modelInstance.filter((m) => actions.includes(m.action))
        : modelInstance;
      return filteredFormValues.map((m) => helpers.get(m, 'data'));
    }

    if (fieldName) {
      return helpers.get(modelInstance, `data[${fieldName}]`);
    }

    return helpers.get(modelInstance, 'data');
  }
}

export function getTotalBankTransactionAmount(context: Context) {
  const { data, Decimal } = context;
  let totalBankTransactionAmount = new Decimal(0);

  const bankTransactions = data?.transactions ?? [];
  for (const { amount } of bankTransactions) {
    totalBankTransactionAmount = totalBankTransactionAmount.plus(
      new Decimal(amount?.amount ?? 0)
    );
  }
  const anyLiabilityAccount = bankTransactions.some(
    (bt) =>
      bt['BankTransactionNew-BankAccount'].coaAccountType ===
      EvstAccountType.Liability
  );
  const matchDocuments = data?.matchDocuments ?? [];
  for (const { amount } of matchDocuments) {
    const matchDocumentAmount = anyLiabilityAccount
      ? new Decimal(amount ?? 0).times(-1)
      : new Decimal(amount);
    totalBankTransactionAmount = totalBankTransactionAmount.minus(
      new Decimal(matchDocumentAmount ?? 0)
    );
  }
  return totalBankTransactionAmount;
}

export function setDiffAndTotal(context: Context, total: Decimal) {
  const { state, data, helpers } = context;
  const totalBankTransactionAmount = getTotalBankTransactionAmount(context);
  const difference = totalBankTransactionAmount.minus(total);

  /** Do not parse the diff with the currency as it results in displaying the currency symbol */
  state.diff = helpers.parseValue({
    value: difference.toFixed(2),
    parseAs: 'currency',
  });

  return helpers.parseValue({
    value: total.toFixed(2),
    format: data?.transactions?.[0]?.currency,
    parseAs: 'currency',
  });
}

export function isCancelButtonVisible(context: Context) {
  return isEditMode(context);
}

export function getPrimaryButtonLabel(context: Context) {
  if (isCreateMode(context)) {
    return 'Create and Match';
  }
  if (isViewMode(context)) {
    return 'Edit Credit Card Entry';
  }
  if (isEditMode(context)) {
    return 'Save';
  }
}

export async function cancel(context: Context) {
  const { helpers, state } = context;

  await helpers.currentModalSubmitCallback();
  await helpers.closeModal();
  setTimeout(() => (state.mode = 'view'), 500);
  state.saving = false;
}

export async function saveEntry(context: Context) {
  if (isCreateMode(context)) {
    await createCreditCardEntry(context);
  } else if (isViewMode(context)) {
    activateEditMode(context);
  } else if (isEditMode(context)) {
    await editAndSaveCreditCardEntry(context);
  } else {
    throw new Error('Wrong button action.');
  }
}

export async function createCreditCardEntry(context: Context) {
  const { helpers, state, data, sections } = context;

  state.saving = true;

  // check if there is any errors just stop the process and ask user to fill the form correctly
  if (validateCreditCardEntry(context) > 0) {
    state.saving = false;
    return;
  }
  let creditCardEntryName = getCreditCardEntryName(context);

  if (creditCardEntryName.length <= 0) {
    creditCardEntryName = CREDIT_CARD_ENTRY;
  }

  const creditCardEntryDescription =
    data?.creditCardEntryHeader?.creditCardEntryDescription ?? '';
  const creditCardEnrtyTransactionDate =
    data?.creditCardEntryHeader?.transactionDate;
  const [{ entityId, currency, coaAccountId }] = data.transactions;

  if (!creditCardEnrtyTransactionDate) {
    helpers.showToast({
      title: '{{creditCard.needAttention}}',
      message: '{{creditCard.transaction.dateFailed}}',
      type: 'error',
    });
    state.saving = false;
    return;
  }

  state.amortizationData ??= new Map();
  const { amortizationValues, creditCardEntryLines } =
    getCCElineAndAmortizationDataForUpsert(context);
  const allHeadersNull = amortizationValues.every((header) => header === null);
  const amortizations = allHeadersNull ? undefined : amortizationValues;

  const creditCardEntryHeader = {
    postingPeriodId: state.postingPeriodId,
    postingPeriod: state.postingPeriodName,
    creditCardEntryName: creditCardEntryName,
    creditCardEntryDescription: creditCardEntryDescription,
    transactionDate: creditCardEnrtyTransactionDate,
    entityId,
    currency,
    accountId: coaAccountId,
    status: EvstCreditCardStatus.Matched,
  };

  const creditCardEntryAttachments = sections.attachments
    ?.getUploadFiles()
    ?.map((file) => {
      return {
        fileId: file.fileId,
        fileName: file.fileName,
        ...(file.id ? { id: file.id } : {}),
      };
    });
  helpers.showNotificationMessage({
    key: 'creditCardEntry',
    type: 'loading',
    message: '{{creditCard.create}}',
    duration: 20,
  });
  const creditCardEntryResponse =
    await CreditCardEntryHeaderUI.upsertCreditCardEntry(context, {
      creditCardEntryHeader,
      creditCardEntryLines,
      creditCardEntryAttachments,
      options: { fieldList: ['id'] },
      amortizations,
    }).run('creditCardEntryHeader');

  if (creditCardEntryResponse?.error) {
    helpers.showNotificationMessage({
      key: 'creditCardEntry',
      type: 'error',
      message: '{{creditCard.transaction.creditCardEntryFailed}}',
      duration: 5,
    });
  } else {
    await CreditCardEntryHeaderUI.updateCCEjournalEntryName(context, {
      creditCardEntryHeaderId: creditCardEntryResponse.creditCardEntryHeader.id,
    }).run('creditCardEntryHeader');
    helpers.closeModal();
    helpers.showNotificationMessage({
      key: 'creditCardEntry',
      type: 'success',
      message: '{{creditCard.transaction.creditCardEntryCreated}}',
      duration: 5,
    });
    await helpers.currentModalSubmitCallback(
      creditCardEntryResponse.creditCardEntryHeader.id
    );
  }
  state.saving = false;
}

export function validateCreditCardEntry(context: Context) {
  const { state, helpers, form } = context;

  let errors = 0;

  if (
    state.diff !== '0.00' &&
    !isCCEIntegrated(context) &&
    anyTransactionsConnected(context)
  ) {
    helpers.showToast({
      type: 'error',
      title: '{{creditCard.validations.error}}',
      message: '{{creditCard.validations.cumulativeAmount}}',
    });
    errors++;
  }

  //Get all credit card entry lines
  const creditCardEntryLineForm = form.getFormValueList(
    'creditCardEntryLines',
    [
      'create',
      'update',
      //'delete',
    ]
  );

  if (
    state?.postingPeriod?.canBePosted === undefined ||
    state?.postingPeriod?.canBePosted === false
  ) {
    helpers.showToast({
      type: 'error',
      title: '{{creditCard.validations.error}}',
      message: '{{creditCard.validations.postingClose}}',
    });
    errors++;
  }

  for (const element of creditCardEntryLineForm.values) {
    //check if user fill the account ids on lines
    if (element.accountId === undefined || element.accountId.length === 0) {
      helpers.showToast({
        type: 'error',
        title: '{{creditCard.validations.error}}',
        message: '{{creditCard.validations.accountIdMissing}}',
      });
      errors++;
    }

    //check if user fill the amount on lines and the user not entered zero as value
    if (element.amount === undefined || element.amount === 0) {
      helpers.showToast({
        type: 'error',
        title: '{{creditCard.validations.error}}',
        message: '{{creditCard.validations.amountMissing}}',
      });
      errors++;
    }
  }

  return errors;
}

export function getCreditCardEntryName(context: Context) {
  const { data } = context;
  return data?.creditCardEntryHeader?.creditCardEntryName ?? CREDIT_CARD_ENTRY;
}

export async function editAndSaveCreditCardEntry(context: Context) {
  const { actions, state, form, helpers, sections } = context;

  state.saving = true;

  form.modifyFormAction(`creditCardEntryHeader`, 'create', 'update');

  // check if there is any errors just stop the process and ask user to fill the form correctly
  if (validateCreditCardEntry(context) > 0) {
    state.saving = false;
    return;
  }

  helpers.showNotificationMessage({
    key: 'creditCardEntry',
    type: 'loading',
    message: 'Saving...',
    duration: 20,
  });

  const creditCardEntryHeader = form.getFormValue([
    'creditCardEntryHeader',
  ]).update;
  delete creditCardEntryHeader['JournalEntryHeader-FinancialDocument'];

  const creditCardEntryAttachments = sections.attachments
    ?.getUploadFiles()
    ?.map((file) => {
      return {
        fileId: file.fileId,
        fileName: file.fileName,
        ...(file.id ? { id: file.id } : {}),
      };
    });
  state.amortizationData ??= new Map();

  const { amortizationValues, creditCardEntryLines } =
    getCCElineAndAmortizationDataForUpsert(context);
  const allHeadersNull = amortizationValues.every((header) => header === null);
  const amortizations = allHeadersNull ? undefined : amortizationValues;

  const updateResponse = await CreditCardEntryHeaderUI.upsertCreditCardEntry(
    context,
    {
      creditCardEntryHeader,
      creditCardEntryLines,
      creditCardEntryAttachments,
      options: { fieldList: ['id'] },
      amortizations,
    }
  ).run('creditCardEntryHeader');
  if (updateResponse?.error) {
    return helpers.showNotificationMessage({
      key: 'creditCardEntry',
      type: 'error',
      message: 'Edit failed',
      duration: 5,
    });
  } else {
    helpers.showNotificationMessage({
      key: 'creditCardEntry',
      type: 'success',
      message: 'Credit Card Entry successfully saved',
      duration: 5,
    });
    await cancel(context);
    await actions.refetchUiModelData(); //required to reload modal data on bank transaction page
  }
}

export function getCCElineAndAmortizationDataForUpsert({
  state,
  sharedState,
}: Context) {
  const amortizationValues = [];
  const creditCardEntryLines = [];

  const lineData = sharedState.getModelData('creditCardEntryLines')
    ?.instance as {
    data: {
      amortizationScheduleHeaderId: number;
      lineType: EvstCreditCardEntryLineType;
    };
    reference: string;
  }[];
  for (const cceLine of lineData) {
    const { reference } = cceLine;
    creditCardEntryLines.push(cceLine.data);
    if (cceLine.data.lineType === EvstCreditCardEntryLineType.OneTime) {
      amortizationValues.push(null);
    } else {
      const amortizationData = state.amortizationData.get(reference);
      if (amortizationData) {
        const { header, line } = amortizationData;
        if (header && line) {
          const asData = {
            ...header,
            recognitionAccountId: line.recognitionAccountId,
          };
          if (cceLine.data.amortizationScheduleHeaderId && !asData.id) {
            asData.id = cceLine.data.amortizationScheduleHeaderId;
          }
          amortizationValues.push(asData);
        } else {
          amortizationValues.push(null);
        }
      } else {
        amortizationValues.push(null);
      }
    }
  }
  return { amortizationValues, creditCardEntryLines };
}

export function confirmVoidCreditCardEntry(context: Context) {
  const { state, helpers } = context;

  const path = helpers.getTemplateLink({
    templateName: 'everest.fin.integration.bank/uinext/voidReasonModal',
    templateState: { id: state.id },
  });

  helpers.openModal({
    size: 'xsmall',
    title: '{{creditCard.confirm.void}}',
    template: path,
    onModalSubmit: async (payload) =>
      await submitVoidCreditCardEntry(context, payload.reason),
  });
}

export async function submitVoidCreditCardEntry(
  context: Context,
  reason: string
) {
  const { data, actions, state, helpers } = context;

  helpers.showNotificationMessage({
    type: 'loading',
    key: 'voidCCE',
    message: 'Voiding',
    duration: 200,
  });

  const creditCardEntry = [data?.creditCardEntryHeader];
  const postingStatus = state.postingPeriod.canBePosted;

  if (!postingStatus) {
    helpers.showToast({
      title: '{{creditCard.needAttention}}',
      message: '{{creditCard.transaction.date.postingClose}}',
      type: 'error',
    });

    helpers.showNotificationMessage({
      type: 'error',
      key: 'voidCCE',
      message: 'Failed to void credit card entry',
      duration: 3,
    });

    return;
  }

  await CreditCardEntryHeaderUI.voidCreditCardEntry(context, {
    creditCardEntries: creditCardEntry,
    voidReason: reason,
  }).run('creditCardEntryHeader');

  helpers.showNotificationMessage({
    type: 'loading',
    key: 'voidCCE',
    message: 'Unmatching',
    duration: 200,
  });

  const sfdCardEntry = await actions.run({
    matchingSFD: {
      action: 'query',
      data: {
        where: {
          sfdId: creditCardEntry?.[0]?.id,
          'MatchSourceFinancialDocument-BankTransactionMatch.status': {},
        },
        fieldList: ['bankTransactionMatchId'],
      },
    },
  });

  const bankTransactionId = data.transactions.find(
    (bt) =>
      bt.matchId === sfdCardEntry?.matchingSFD?.[0]?.bankTransactionMatchId
  ).id;
  await actions.run({
    bankTransactionMatchesTransient: {
      action: 'unmatchBankTransaction',
      data: {
        bankTransactionId,
        isVoided: true,
      },
    },
  });

  helpers.showNotificationMessage({
    type: 'success',
    key: 'voidCCE',
    message: '{{creditCard.transaction.success.void}}',
    duration: 2,
  });
  helpers.currentModalSubmitCallback();
  await actions.refetchUiModelData();
  helpers.refetchUiModelDataSidebarTemplate({});
}

export function isCreditCardVoidVisible(context: Context) {
  if (isEditMode(context) || isCreateMode(context)) {
    return false;
  }

  if (isViewMode(context) && isEntryVoided(context)) {
    return false;
  }

  return true;
}

export function isSaveButtonVisible(context: Context) {
  if (isEntryVoided(context)) {
    return false;
  }

  if (isViewMode(context)) {
    return false;
  }

  return true;
}

// Amortization
export const showOrHideColumns = (
  { sections }: Context,
  columnNames: string[],
  visible: boolean
) => {
  // delay the execution so the table has time to process data changes before changing columns
  setTimeout(() => {
    const functions = sections?.creditCardEntryLinesTable ?? {};
    functions?.setColumnsVisible(columnNames, visible);
  }, 200);
};

export async function setLineType(
  context: Context,
  accountId: number,
  row: {
    nodeInstance: { reference: string };
    rowData?: { id: number; amortizationScheduleId: number };
    previousValue?: number;
  }
) {
  const { state } = context;
  const {
    nodeInstance: { reference },
  } = row;
  state.isFormDataChanged = true;
  state.isFetchingAccountData = true;
  let lineType = EvstCreditCardEntryLineType.OneTime;

  if (accountId) {
    const response = (await AccountUI.read(
      context,
      {
        id: accountId,
      },
      ['id', 'accountSubType']
    ).run('account')) as { account: { accountSubType: EvstAccountSubType } };

    lineType =
      response?.account?.accountSubType === EvstAccountSubType.PrepaidExpenses
        ? EvstCreditCardEntryLineType.Prepaid
        : EvstCreditCardEntryLineType.OneTime;
  }

  state.isFetchingAccountData = false;

  setUIModelInSharedState(
    context,
    'creditCardEntryLines',
    'lineType',
    lineType,
    reference
  );

  if (lineType === EvstCreditCardEntryLineType.Prepaid) {
    showOrHideColumns(context, ['_'], true); // Show schedule column
  } else {
    const ccLines = getUIModelFromSharedState(context, 'creditCardEntryLines');

    const anyLinePrepaidType = ccLines.some(
      (line) => line.lineType === EvstCreditCardEntryLineType.Prepaid
    );
    // Hide schedule column if all rows are non-prepaid and any schedule don't exist
    if (!anyLinePrepaidType) {
      showOrHideColumns(context, ['_'], false);
    }
  }
}

export function handleBillableExpenseChange(
  context: Context,
  _value,
  { rowData, setRowCellValue }
) {
  const ccLines = getUIModelFromSharedState(context, 'creditCardEntryLines');
  const anyBillableLine = ccLines.some(
    (line) => line.billableExpense === EvstBillableExpenseType.Yes
  );

  if (anyBillableLine) {
    showOrHideColumns(context, ['customerId'], true);
  } else {
    // Hide customer column if all rows are non-billable
    showOrHideColumns(context, ['customerId'], false);
  }
  const { billableExpense } = rowData ?? {};
  if (billableExpense !== EvstBillableExpenseType.Yes) {
    setRowCellValue({
      rowCell: 'customerId',
      newValue: null,
    });
  }
}

export function isCustomerSelectionDisabled(_context: Context, row) {
  const { billableExpense } = row?.rowData ?? {};
  return billableExpense !== EvstBillableExpenseType.Yes;
}

export function onTableLoaded(context: Context) {
  const { data, state } = context;

  setTimeout(() => {
    const { containsPrepaidLines, containsBillableItem } =
      data?.creditCardEntryHeader ?? {};
    if (containsPrepaidLines) {
      showOrHideColumns(context, ['_'], true);
    }
    if (containsBillableItem) {
      showOrHideColumns(context, ['customerId'], true);
    }
  }, 300);
  if (
    isCreateMode(context) &&
    !state.isFirstCreditCardEntryAccountHasDefaultValue &&
    data.creditCardEntryLines?.length > 0
  ) {
    const { accountId, departmentId } = state;
    const bankEntryLinesFirstNodeRef =
      data.creditCardEntryLines[0]['_nodeReference'];
    state.isFirstBankEntryAccountHasDefaultValue = true;
    if (accountId) {
      state.accountId = null;
      setUIModelInSharedState(
        context,
        'creditCardEntryLines',
        'accountId',
        accountId,
        bankEntryLinesFirstNodeRef
      );
    }
    if (departmentId) {
      state.departmentId = null;
      setUIModelInSharedState(
        context,
        'creditCardEntryLines',
        'departmentId',
        departmentId,
        bankEntryLinesFirstNodeRef
      );
    }
  }
}

export function getAmortizationScheduleHeader(context: Context, id: number) {
  const { isFetchingUiModelData } = context;
  if (isFetchingUiModelData) {
    return;
  }

  const amortizationScheduleHeaders = getUIModelFromSharedState(
    context,
    'amortizationScheduleHeaders'
  );

  return amortizationScheduleHeaders?.find((ash) => ash?.id === id);
}

export function amortizationLinkValueGetter(
  context: Context,
  row: { data: SingleNodeItemType['creditCardEntryLines'] }
) {
  const { state } = context;
  state.amortizationData ??= new Map();
  const reference = row?.data?._nodeReference;
  if (row.data?.lineType !== EvstCreditCardEntryLineType.Prepaid) {
    state.amortizationData.set(reference, null);
    return '';
  }
  if (isViewMode(context)) {
    const { amortizationScheduleHeaderId } = row.data;
    return amortizationScheduleHeaderId
      ? '{{amortization.viewSchedule}}'
      : '{{amortization.addAmortizationSchedule}}';
  } else if (isCreateMode(context)) {
    const asData = state.amortizationData.get(reference);
    return asData?.header
      ? '{{amortization.editAmortizationSchedule}}'
      : '{{amortization.addAmortizationSchedule}}';
  } else {
    const { amortizationScheduleHeaderId } = row.data;
    if (amortizationScheduleHeaderId) {
      const headerFields = [
        'id',
        'amount',
        'amortizationStartDate',
        'term',
        'status',
        'amortizationEndDate',
        'deferredAccountId',
        'amortizationMethod',
      ];
      if (!state.amortizationData.has(reference)) {
        const asHeader = getAmortizationScheduleHeader(
          context,
          row.data.amortizationScheduleHeaderId
        );
        if (asHeader) {
          state.amortizationData.set(reference, {
            header: pick(asHeader, headerFields),
            line: pick(asHeader, ['recognitionAccountId']),
          });
        }
      }
    }
    const asData = state.amortizationData.get(reference);
    return asData?.header
      ? '{{amortization.editAmortizationSchedule}}'
      : '{{amortization.addAmortizationSchedule}}';
  }
}

export function isAddScheduleActionDisabled(_: Context, row: { rowData: Any }) {
  const { lineType, amount } = row?.rowData ?? {};
  if (!amount) {
    return true;
  }
  return lineType !== EvstCreditCardEntryLineType.Prepaid;
}

export function getAmortizationScheduleCellVariant(_: Context) {
  return {
    variant: 'icon',
    iconSize: 'small',
    iconPosition: 'left',
    iconColor: 'gray',
    matchers: ({ rowData }) => {
      if (rowData && rowData.lineType === EvstCreditCardEntryLineType.Prepaid) {
        return 'exclamation-circle';
      }
    },
  };
}

export async function clickActionOfAmortizationScheduelCell(
  context: Context,
  row: { data: Any }
) {
  const { helpers, data, state } = context;
  state.amortizationData ??= new Map();
  const {
    accountId,
    departmentId,
    businessUnitId,
    lineType,
    _nodeReference,
    amortizationScheduleHeaderId,
    amount: { amount },
    employeeId,
  } = row.data;
  if (lineType === EvstCreditCardEntryLineType.OneTime) {
    return;
  }
  if (amortizationScheduleHeaderId && isViewMode(context)) {
    helpers.navigate({
      to: `${AMORTIZATION_SCHEDULE_LINK}?id=${amortizationScheduleHeaderId}`,
    });
  } else {
    if (isViewMode(context)) {
      activateEditMode(context);
    }
    let displayName;
    if (employeeId) {
      const { employee } = await EmployeeUI.loadEmployeeProfile(context, {
        where: { id: employeeId },
        fieldList: ['displayName'],
      }).run('employee');
      displayName = employee?.displayName;
    }
    const entityId = getEntityId(context);
    const entity = getEntity(context);
    const currency = data?.transactions[0]?.currency;
    let initialState: Record<string, unknown> = {
      mode: 'create',
      entityId,
      entity,
      deferredAccountId: accountId,
      departmentId: departmentId ? departmentId.toString() : departmentId,
      businessUnitId: businessUnitId
        ? businessUnitId.toString()
        : businessUnitId,
      fromJournalEntry: true,
      total: amount,
      currency,
      employee: displayName,
    };
    if (state.amortizationData.has(_nodeReference)) {
      const {
        amortizationEndDate,
        amortizationMethod,
        amortizationStartDate,
        term,
      } = state.amortizationData.get(_nodeReference).header;
      const { recognitionAccountId, departmentId, businessUnitId } =
        state.amortizationData.get(_nodeReference).line;
      initialState = {
        ...initialState,
        startDate: amortizationStartDate ?? getTransactionDate(context),
        endDate: amortizationEndDate,
        term,
        method: amortizationMethod,
        expenseAccountId: recognitionAccountId,
        departmentId: departmentId ? departmentId.toString() : departmentId,
        businessUnitId: businessUnitId
          ? businessUnitId.toString()
          : businessUnitId,
      };
    }
    helpers.openModal({
      size: 'small',
      title: 'Amortization schedule',
      template: DRAFT_AMORTIZATION_SCHEDULE_LINK,
      initialState,
      onModalSubmit: (returnedAmortizationData: Any) => {
        state.amortizationData.set(_nodeReference, returnedAmortizationData);
        setPropertyFromAmortizationProp(
          context,
          returnedAmortizationData,
          _nodeReference,
          'departmentId'
        );
        setPropertyFromAmortizationProp(
          context,
          returnedAmortizationData,
          _nodeReference,
          'businessUnitId'
        );
        // This toggling is required to re-render the table so link text changes
        showOrHideColumns(context, ['_'], false);
        showOrHideColumns(context, ['_'], true);
      },
    });
  }
}

function setPropertyFromAmortizationProp(
  context: Context,
  returnedAmortizationData: {
    line: { [propName in 'businessUnitId' | 'departmentId']?: unknown };
  },
  _nodeReference: string,
  property: 'businessUnitId' | 'departmentId'
) {
  const { sharedState, helpers } = context;
  const propValue = returnedAmortizationData?.line?.[property];
  const nodeInstance = sharedState.getNodeInstance(_nodeReference);
  if (propValue) {
    helpers.set(nodeInstance.data, property, propValue);
  } else {
    helpers.unset(nodeInstance.data, property);
  }
}

export function setFirstCreditCardEntryLineAmount(
  context: Context,
  params: { data: CreditCardEntryHeader }
) {
  const { state, data } = context;
  if (
    isCreateMode(context) &&
    !state.isFirstCreditCardEntryHasDefaultValue &&
    data.creditCardEntryLines?.length > 0
  ) {
    const totalBankTransactionAmount = getTotalBankTransactionAmount(context);

    state.isFirstCreditCardEntryHasDefaultValue = true;
    return {
      amount: toCurrencyValueString(
        totalBankTransactionAmount,
        data?.transactions?.[0]?.currency as EvstBaseCurrency
      ),
    };
  }
  return params?.data?.amount;
}

// Attachments

export function isAttachmentsEditable(context: Context) {
  return allowedToEdit(context);
}

export function getAttachments({ data, form, state }: Context) {
  const { attachment } = form.getFormValues();
  if (!state.attachments || attachment.length > state.attachments?.length) {
    state.attachments = attachment;
  }
  return state.attachments ?? data?.creditCardEntryAttachments ?? [];
}

export async function handleAttachmentRemove(context: Context, fileId: string) {
  const { state } = context;
  state.attachments = (state.attachments ?? []).filter(
    (file) => file.fileId !== fileId
  );
}

export function goToBusinessPartnerDetails(
  context: Context,
  row: { data: SingleNodeItemType['creditCardEntryLines'] }
) {
  const { helpers } = context;
  const bpId = row?.data.businessPartnerId;

  helpers.navigate({
    to: `/templates/everest.fin.base/uinext/businessPartner`,
    queryParams: { id: bpId },
  });
}
