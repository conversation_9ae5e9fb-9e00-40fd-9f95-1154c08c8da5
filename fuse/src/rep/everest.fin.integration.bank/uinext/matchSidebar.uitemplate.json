{"version": 2, "uicontroller": ["matchSidebar.uicontroller.ts", "matchSidebarUtils.uicontroller.ts"], "uimodel": {"state": {"mode": "single", "actionGroupType": "radio", "selectedItems": [], "type": "bank"}, "nodes": {"postingPeriods": {"type": "struct", "modelId": "everest.fin.accounting/AccountingPeriodModel.AccountingPeriod"}, "transactions": {"type": "list", "query": {"where": {"id": {"$in": "@controller:getBankTransactionIds()"}}, "orderBy": [{"field": "externalCreationDate", "ordering": "desc"}, {"field": "id", "ordering": "desc"}], "draft": "include"}, "fieldList": ["bankAccountId", "matchStatus", "matchingTransaction", "bankTransactionNumber", "accountName", "amount", "currency", "externalCreationDate", "payeeOrPayor", "externalDescription", "entityId", "id", "entity", "BankTransactionNew-BankAccount.id", "BankTransactionNew-BankAccount.accountId", "BankTransactionNew-BankAccount.coaAccountType", "BankTransactionNew-BankAccount.bankName", "BankTransactionNew-BankAccount.accountNumber", "BankTransactionNew-BankAccount.BankAccount-FinancialInstitution.logoId", "BankTransactionNew-BankAccount.BankAccount-FinancialInstitution.logoUrl", "fullmatchCount", "partialMatchCount", "matchMessage", "coaAccountId", "notes", "matchId", "transactionPaymentMethod"], "modelId": "everest.fin.integration.bank/BankTransactionNewModel.BankTransactionNew"}, "bankTransactionMatchesTransient": {"type": "list", "modelId": "everest.fin.integration.bank.matching/BankTransactionMatchTransientModel.BankTransactionMatchTransient"}, "bankTransactionMatches": {"type": "list", "parent": "transactions", "joinKey": "id-matchId", "modelId": "everest.fin.integration.bank/BankTransactionMatchModel.BankTransactionMatch", "query": {"draft": "include"}, "fieldList": ["id", "status", "autoMatchReason", "errorReason"]}, "matchDocuments": {"type": "list", "parent": "bankTransactionMatches", "query": {"orderBy": [{"field": "sortingPriority", "ordering": "asc"}, {"field": "isAFullMatch", "ordering": "desc"}, {"field": "suggestionConfidence", "ordering": "desc"}, {"field": "amount", "ordering": "desc"}], "draft": "include"}, "joinKey": "id-bankTransactionMatchId", "modelId": "everest.fin.integration.bank/MatchSourceFinancialDocumentModel.MatchSourceFinancialDocument", "fieldList": ["id", "bankTransactionMatchId", "sourceJournalEntryLineId", "amount", "isAFullMatch", "sfdId", "suggestionConfidence", "journalEntryHeaderId", "journalEntryLineDescription", "postingDate", "business<PERSON><PERSON>ner", "sfdNumber", "sboNumber", "matchDocument", "matchDocumentWithoutLink"]}, "sbos": {"type": "list", "parent": "matchDocuments", "joinKey": "id-matchSFDID", "modelId": "everest.fin.integration.bank/MatchSourceBusinessObjectModel.MatchSourceBusinessObject", "query": {"draft": "include"}, "fieldList": ["id", "matchSFDID", "sboId", "sboNumber", "sboType", "sboName"]}, "attachments": {"type": "list", "getMatchAttachments": {"bankTransactionIds": "@controller:getBankTransactionIds()"}, "modelId": "everest.fin.integration.bank.matching/BankTransactionMatchTransientModel.BankTransactionMatchTransient"}, "fundTransfers": {"type": "list", "modelId": "everest.fin.accounting/FundTransferHeaderModel.FundTransferHeader", "query": "@controller:getFTQuery()", "fieldList": ["id", "toAccountName", "fromAccountName", "description"]}, "fundTransfer": {"type": "struct", "modelId": "everest.fin.accounting/FundTransferHeaderModel.FundTransferHeader", "fieldList": ["transferAmount", "exchangeRate", "isIntercompany", "transferReason", "intercompanyProcessId", "intercompanyTransactionalCurrency"]}, "exchangeRate": {"type": "struct", "modelId": "everest.base/ExchangeRateModel.ExchangeRate", "fieldList": ["value"]}, "entities": {"type": "list", "modelId": "everest.base/EntityModel.Entity", "queryActiveEntities": "@controller:getEntitiesQuery()"}, "bankFeeConfiguration": {"type": "struct", "modelId": "everest.fin.accounting/BankFeeSettingsModel.BankFeeSettings", "query": "@controller:getbankFeeConfigQuery()", "fieldList": ["id", "bankFeeAccountId"]}, "netZeroImpact": {"type": "struct", "modelId": "everest.fin.integration.bank/NetZeroImpactModel.NetZeroImpact"}, "payment": {"type": "struct", "fieldList": ["paymentType", "bankFeeAmount"], "modelId": "everest.fin.accounting/PaymentModel.Payment"}, "bankEntryHeader": {"type": "list", "modelId": "everest.fin.accounting/BankEntryHeaderNewModel.BankEntryHeaderNew", "fieldList": ["id", "bankEntryName", "bankEntryDescription", "bankEntryHeaderNewNumber", "transactionDate", "postingPeriod", "postingPeriodId", "entityId", "entity", "currency", "journalEntryNumber", "journalEntryHeaderId", "status", "containsPrepaidLines", "origin", "externalUrl"]}, "bankEntryLines": {"type": "list", "modelId": "everest.fin.accounting/BankEntryLineNewModel.BankEntryLineNew", "fieldList": ["bankEntryHeaderId", "departmentId", "businessPartnerId", "description", "accountId", "amount", "id", "account", "accountNumber", "lineType", "amortizationScheduleHeaderId", "businessUnitId"]}, "postingPeriodFT": {"type": "struct", "modelId": "everest.fin.accounting/AccountingPeriodModel.AccountingPeriod", "findAccountingPeriod": "@controller:postingPeriodFTQuery()"}, "intercompanyProcess": {"type": "list", "modelId": "everest.fin.accounting/IntercompanyProcessModel.IntercompanyProcess", "query": {"where": {}}, "fieldList": ["id", "processName", "processDefinition"]}}}, "uiview": {"config": {"autoRefreshData": true, "allowRefreshData": true, "alwaysNewTab": true, "sidebar": {"stickyFooter": [{"component": "SummaryText", "visible": "@controller:isSummaryVisible()", "summary": {"label": "@controller:getSummaryTextLabel()", "parseAs": "currency", "value": "@controller:getTotalAmountSelected()"}, "fields": [{"label": "{{fundTransfer.difference}}", "parseAs": "currency", "value": "@controller:getDifference()"}], "variant": "small"}]}}, "templateType": "generic", "i18n": ["dashboard", "everest.fin.accounting/bankEntry", "everest.fin.accounting/fundTransfer", "everest.fin.accounting/customerPayment"], "header": {"content": {"status": "@controller:getStatus()", "title": "@controller:getAvatarTitle()", "description": "@controller:getAvatarDescription()", "showAvatar": true, "avatar": {"isEditable": false, "size": "large", "avatarList": "@controller:getAvatarList()"}}, "fieldConfig": {"status": {"tooltip": {"text": "@controller:getStatusTooltip()"}}}}, "sections": {"content": [{"component": "ActionGroup", "title": "@controller:getMatchTitle()", "versionNew": true, "customId": "MultiMatchActionGroup", "isEditing": true, "variant": "@state:actionGroupType", "data": "@controller:getActionGroupData()", "onChangedValue": "@controller:onMatchOptionChange", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "spacing": "small"}, {"component": "SectionGroup", "customId": "fundTransferActionGroup", "visible": "@controller:isFundTransferDetailBlockVisible()", "section": {"grid": {"size": "12"}}, "props": {"sections": [{"component": "ActionGroup", "customId": "intercompanyFTActionGroup", "visible": "@controller:isIntercompanyFT()", "section": {"grid": {"size": "6"}, "title": "{{fundTransfer.intercompanySectionTitle}}", "editing": true, "secondaryActions": [{"label": "{{fundTransfer.createInModal}}", "variant": "support-mystic-grey", "icon": {"type": "copy", "size": "xsmall", "weight": "bold"}, "onClick": "@controller:createFundTransferInModal"}]}, "props": {"variant": "default", "editing": true, "versionNew": true, "descriptionPosition": "rightSide", "buttonActionsPosition": "right", "lines": [{"label": "{{fundTransfer.intercompanyProcess}}", "fieldProps": {"component": "Select", "idProp": "id", "textProp": "processName", "visible": "@controller:isIntercompanyFT()", "value": "@binding:fundTransfer.intercompanyProcessId", "list": "@binding:intercompanyProcess", "rules": "@controller:isRequiredForIntercompany()"}}, {"label": "{{fundTransfer.transferReason}}", "fieldProps": {"component": "Select", "idProp": "value", "textProp": "label", "visible": "@controller:isIntercompanyFT()", "value": "@binding:fundTransfer.transferReason", "rules": "@controller:isRequiredForIntercompany()"}}, {"label": "{{fundTransfer.intercompanyTransactionalCurrency}}", "fieldProps": {"component": "Select", "idProp": "currency", "textProp": "currency", "visible": "@controller:isIntercompanyFT()", "value": "@binding:fundTransfer.intercompanyTransactionalCurrency", "list": "@controller:getIntercompanyTransactionalFTCurrency()", "rules": "@controller:isRequiredForIntercompany()"}}]}}, {"component": "ActionGroup", "customId": "fromAccountActionGroup", "section": {"grid": {"size": "6"}, "title": "@controller:getAccountTitle('sending')", "editing": true, "secondaryActions": [{"label": "{{fundTransfer.createInModal}}", "variant": "support-mystic-grey", "icon": {"type": "copy", "size": "xsmall", "weight": "bold"}, "visible": "@controller:isSecondCreateInModalFTVisible()", "onClick": "@controller:createFundTransferInModal"}]}, "props": {"variant": "default", "editing": true, "versionNew": true, "descriptionPosition": "rightSide", "buttonActionsPosition": "right", "lines": [{"avatar": {"src": "@controller:getAvat<PERSON>('sending')", "text": "--"}, "label": "@controller:getAccountAmount('sending')", "description": "@controller:getAccountDescriptionLabel('sending')"}, {"label": "{{fundTransfer.amountSent}}", "description": "@controller:getAccountCurrencyDescription('sending')", "avatar": false, "fieldProps": {"component": "InputNumber", "value": "@bindingController:(fundTransfer.sendingAmount, getAmounts('sending'))", "isEditing": true, "placeholder": "0.00", "isDisabled": "@state:isFTFieldsDisabled", "onBlur": "@controller:adjustSendingFee", "tooltip": "@controller:getTooltipFor('ftSendingAmounts')", "rules": "@controller:validateAmount('sending')", "compositeConfig": {"amount": {"placeholder": "0.00"}}}}, {"label": "{{fundTransfer.sendingBankFee}}", "description": "@controller:getAccountCurrencyDescription('sending')", "avatar": false, "fieldProps": {"component": "InputNumber", "value": "@bindingController:(fundTransfer.sendingFee, getDefaultFee())", "_type": "amount", "_parseAs": "currency", "placeholder": "0.00", "isDisabled": "@controller:isBankFeeInputDisabled()", "onBlur": "@controller:adjustSendingAmount", "tooltip": "@controller:getTooltipFor('sendingBankFee')", "rules": "@controller:validateAmount('bankFee')"}}]}}, {"component": "ActionGroup", "customId": "toAccountActionGroup", "section": {"grid": {"size": "6"}, "title": "@controller:getAccountTitle('receiving')", "editing": true}, "props": {"variant": "default", "editing": true, "versionNew": true, "descriptionPosition": "rightSide", "buttonActionsPosition": "right", "lines": [{"avatar": {"src": "@controller:get<PERSON><PERSON><PERSON>('receiving')", "text": "--"}, "label": "@controller:getAccountAmount('receiving')", "description": "@controller:getAccountDescriptionLabel('receiving')"}, {"label": "{{fundTransfer.amountReceived}}", "description": "@controller:getAccountCurrencyDescription('receiving')", "avatar": false, "fieldProps": {"component": "InputNumber", "value": "@bindingController:(fundTransfer.receivingAmount, getAmounts('receiving'))", "isEditing": true, "isDisabled": "@state:isFTFieldsDisabled", "onBlur": "@controller:adjustReceivingFee", "tooltip": "@controller:getTooltipFor('ftReceivedAmounts')", "rules": "@controller:validateAmount('receiving')", "placeholder": "0.00", "compositeConfig": {"amount": {"placeholder": "0.00"}}}}, {"label": "{{fundTransfer.receivingBankFee}}", "field": "bankFee", "description": "@controller:getAccountCurrencyDescription('receiving')", "avatar": false, "fieldProps": {"component": "InputNumber", "value": "@bindingController:(fundTransfer.receivingFee, getDefaultFee())", "_type": "amount", "_parseAs": "currency", "placeholder": "0.00", "isDisabled": "@controller:isBankFeeInputDisabled()", "rules": "@controller:validateAmount('bankFee')", "onBlur": "@controller:adjustReceivingAmount", "tooltip": "@controller:getTooltipFor('receivingBankFee')"}}]}}]}}, {"component": "ActionGroup", "customId": "bankEntryDetails", "visible": "@controller:isCreateBankEntryModalVisible()", "section": {"title": "{{bankEntry.details}}", "grid": {"size": 12}, "editing": true, "secondaryActions": [{"label": "{{fundTransfer.createInModal}}", "variant": "support-mystic-grey", "icon": {"type": "copy", "size": "xsmall", "weight": "bold"}, "onClick": "@controller:createBankEntryInModal"}]}, "props": {"buttonActionsPosition": "right", "spacing": "small", "lines": [{"label": "Account", "fieldProps": {"component": "Select", "idProp": "id", "textProp": "accountId", "allowClear": true, "onChange": "@controller:onChangeAccountId", "isEditing": true, "value": "@binding:bankEntryLines.accountId", "semanticTypeUiParams": "@controller:getAccountSemanticTypeUiParams()"}}, {"label": "Department", "fieldProps": {"component": "Select", "idProp": "id", "textProp": "departmentId", "allowClear": true, "onChange": "@controller:onChangeDepartmentId", "isEditing": true, "value": "@binding:bankEntryLines.departmentId"}}]}}, {"component": "FieldGroup", "visible": "@controller:isPaymentDetailsVisible()", "section": {"title": "{{paymentDetails}}", "editing": true, "grid": {"size": 12}}, "props": {"columns": "1", "variant": "light", "elements": [{"label": "{{payment.bankFeeAmount}}", "initialValue": "@controller:getSuggestionsWithPostedSFDAmount()", "value": "@binding:payment.bankFeeAmount"}, {"initialValue": "@controller:getPaymentTypeInitialValue()", "value": "@binding:payment.paymentType"}]}}, {"component": "SectionGroup", "title": "Notes", "sections": [{"component": "Messaging", "props": {"list": "@controller:getNotes()", "emptyMessage": "{{banking.transaction.notes.emptyMessage}}", "dateFormat": "P p", "onDeleteAction": "@controller:noteOnDelete", "variant": "small", "input": {"placeholder": "{{banking.transaction.notes.new}}", "name": "newNoteContent", "onSubmit": "@controller:noteOnSubmit"}}}], "visible": "@controller:isNotesVisible()"}, {"component": "FilePreview", "visible": "@controller:isAttachmentPreviewVisible()", "section": {"title": "Related Attachments", "description": "@controller:getAttachmentDescription()", "grid": {"size": "12"}}, "props": {"fileList": "@controller:getAttachments()", "customEmptyMessage": "@controller:getEmptyMessage()"}}]}, "actions": {"content": [{"variant": "@controller:getButtonType()", "label": "@controller:getPrimaryButtonLabel()", "disabled": "@controller:isPrimaryButtonDisabled()", "align": "right", "onClick": "@controller:apply"}, {"variant": "secondary", "label": "Find matches", "disabled": "@controller:isSecondaryButtonDisabled()", "visible": "@controller:isFindMatchesButtonVisible()", "onClick": "@controller:findMatches"}]}}}