{"version": 2, "uicontroller": "findMatches.uicontroller.ts", "uimodel": {"state": {"total": 0, "matches": [], "partialMatching": false}, "nodes": {"bankTransactions": {"type": "list", "modelId": "everest.fin.integration.bank/BankTransactionNewModel.BankTransactionNew", "query": {"where": {"id": {"$in": "@controller:getBankTransactionId()"}}}, "fieldList": ["id", "accountName", "currency", "externalDescription", "amount", "payeeOrPayor", "externalCreationDate", "BankTransactionNew-BankAccount.BankAccount-FinancialInstitution.logoId", "BankTransactionNew-BankAccount.BankAccount-FinancialInstitution.logoUrl", "entityId", "coaAccountId"]}, "unmatchedDocuments": {"type": "list", "modelId": "everest.fin.accounting/UnmatchedDocumentModel.UnmatchedDocument", "query": "@controller:getUnmatchedDocumentQuery()", "pagination": 100, "fieldList": ["id", "sourceJournalEntryLineId", "amount", "source", "business<PERSON><PERSON>ner", "description", "postingDate", "sfdId", "sfdType", "sfdPath", "sfdNumber", "journalEntryHeaderType", "accountId", "amountWithCurrency", "currency"]}, "bankTransactionMatchesTransient": {"type": "list", "modelId": "everest.fin.integration.bank.matching/BankTransactionMatchTransientModel.BankTransactionMatchTransient"}, "bankTransactionMatch": {"type": "struct", "modelId": "everest.fin.integration.bank/BankTransactionMatchModel.BankTransactionMatch"}, "sfds": {"type": "struct", "modelId": "everest.fin.integration.bank/MatchSourceFinancialDocumentModel.MatchSourceFinancialDocument"}}}, "uiview": {"templateType": "details", "i18n": "everest.fin.accounting/findMatches", "header": {}, "sections": {"content": [{"component": "Cards", "title": "{{findMatches.matchto}}", "size": "12", "isVisible": true, "loadingQuantity": 1, "cardsSize": "medium", "data": "@bindingController(bankTransactions, prepareDataForCard())"}, {"component": "Table", "customId": "allUnmatchedTransactionTable", "section": {"title": "{{findMatches.allUnMatched}}", "variant": "dark", "grid": {"size": "12", "gap": "20px"}, "filter": {"suppressFields": ["sourceJournalEntryLineId", "sfdId", "sfdType", "sfdPath", "sfdNumber", "journalEntryHeaderType"], "quickFilters": [{"label": "{{findMatches.sameAccountOnly}}", "value": "@state:filterAccountIds", "field": "accountId", "active": true, "comparator": "$notIn"}]}}, "props": {"pagination": true, "suppressDelete": true, "rowSelection": true, "onRowSelectionChanged": "@controller:onClickRowCheckbox", "footerData": "@controller:getTableFooter()", "data": "@binding:unmatchedDocuments", "columns": [{"headerName": "{{findMatches.headers.date}}", "field": "postingDate", "fieldProps": {"isEditing": false, "parseAs": "date", "format": "P"}}, {"headerName": "{{findMatches.headers.source}}", "sortable": false, "field": "source", "onCellClicked": "@controller:goToSource"}, {"headerName": "{{findMatches.headers.businessPartner}}", "field": "business<PERSON><PERSON>ner"}, {"headerName": "{{findMatches.headers.description}}", "field": "description"}, {"headerName": "{{findMatches.headers.amount}}", "field": "amountWithCurrency"}, {"headerName": "{{findMatches.headers.account}}", "field": "accountId"}]}}, {"component": "ActionGroup", "customId": "partialMatchingAG", "title": "{{findMatches.partialMatching}}", "variant": "default", "editing": true, "size": "6", "versionNew": true, "border": "default", "descriptionPosition": "secondLine", "buttonActionsPosition": "right", "lines": [{"label": "@controller:getActionGroupLabel()", "description": "@controller:getActionGroupDescription()", "fieldProps": {"component": "Switch", "value": "@state:partialMatching", "direction": "horizontal-reverse", "onChange": "@controller:setPartialMatching"}}]}, {"component": "BalanceCheck", "customId": "balanceCheck", "section": {"title": "Match Check"}, "props": {"debitTitle": "Bank Transaction", "creditTitle": "Selected", "data": [{"debit": "@controller:getBankTransactionDetails()", "credit": "@state:total"}]}}]}, "actions": {"content": [{"variant": "secondary", "label": "{{findMatches.actions.cancel}}", "align": "right", "onClick": "@controller:close"}, {"variant": "primary", "label": "@controller:getPrimaryButtonLabel()", "align": "right", "onClick": "@controller:matchTransactions", "disabled": "@controller:isMatchTransactionsDisabled()"}]}}}