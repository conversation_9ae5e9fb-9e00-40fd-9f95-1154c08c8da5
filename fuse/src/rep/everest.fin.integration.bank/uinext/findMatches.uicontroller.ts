// @i18n:everest.fin.accounting/findMatches
import { Decimal } from '@everestsystems/decimal';
import { toPlainDate } from '@pkg/everest.fin.accounting/public/utils/Date/date';
import { EvstSFDType } from '@pkg/everest.fin.base/types/enums/SFDType';

import type {
  BankTransactionNew,
  FindMatchesUiTemplate,
} from '../types/uiTemplates/uinext/findMatches.ui';

type FindMatchesContext = FindMatchesUiTemplate.FindMatchesContext;
type SingleNodeItemType = FindMatchesUiTemplate.SingleNodeItemType;

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type Any = any;
export type Context = FindMatchesContext & {
  state: {
    id: string;
    accountId: string;
    matches?: (Pick<
      SingleNodeItemType['unmatchedDocuments'],
      | 'sourceJournalEntryLineId'
      | 'amount'
      | 'sfdId'
      | 'sfdType'
      | 'sfdPath'
      | 'sfdNumber'
      | 'journalEntryHeaderType'
      | 'accountId'
    > & {
      bankTransactionId: number;
    })[];
    bankTransactionIds: number[];
    total: string;
    type: 'bank' | 'creditCard';
  };
};

type ROW_TYPE = {
  data: SingleNodeItemType['unmatchedDocuments'];
}[];

export function getBankTransactionId(context: Context) {
  const { state } = context;
  return state.bankTransactionIds;
}

export function getUnmatchedDocumentQuery(context: Context) {
  const { state } = context;
  const { entityId } = state;
  return {
    where: { entityId },
    orderBy: [{ field: 'postingDate', ordering: 'desc' }],
  };
}

export function prepareDataForCard(context: Context) {
  const { data, helpers, state } = context;

  const { bankTransactions } = data;
  if (!bankTransactions) {
    return [];
  }
  return bankTransactions.map((bankTransaction) => {
    const {
      externalCreationDate,
      accountName,
      currency,
      externalDescription,
      amount,
      payeeOrPayor,
    } = bankTransaction;

    let picture = '';
    if (
      bankTransaction['BankTransactionNew-BankAccount']?.[
        'BankAccount-FinancialInstitution'
      ]?.logoUrl
    ) {
      const institutionLogoUrl =
        bankTransaction['BankTransactionNew-BankAccount']?.[
          'BankAccount-FinancialInstitution'
        ]?.logoUrl;

      // Format the URL appropriately based on whether it's already a URL or base64 data
      picture =
        institutionLogoUrl.startsWith('http://') ||
        institutionLogoUrl.startsWith('https://')
          ? institutionLogoUrl
          : `data:image/png;base64,${institutionLogoUrl}`;
    } else if (
      bankTransaction['BankTransactionNew-BankAccount']?.[
        'BankAccount-FinancialInstitution'
      ]?.logoId
    ) {
      const logoId =
        bankTransaction['BankTransactionNew-BankAccount']?.[
          'BankAccount-FinancialInstitution'
        ]?.logoId;
      picture = `/api/app/storage/${logoId}`;
    }

    const transactionDate = helpers.parseValue({
      value: new Date(toPlainDate(externalCreationDate).toISO()),
      format: 'P',
      parseAs: 'date',
    });
    let description = `${transactionDate}`;
    if (externalDescription) {
      description = `${description}, ${externalDescription}`;
    }
    if (payeeOrPayor && payeeOrPayor !== '--') {
      description = `${description}, ${payeeOrPayor}`;
    }
    return {
      picture,
      pictureAlt: accountName,
      title: `${accountName} #${state.id}`,
      description,
      summary: `${currency} ${helpers.parseValue({
        value: amount?.amount,
        format: currency,
        parseAs: 'currency',
      })}`,
    };
  });
}

export function isMatchTransactionsDisabled(context: Context) {
  return (
    !context.state.selectedRows ||
    (context.state.selectedRows as ROW_TYPE)?.length === 0 ||
    (context.state.selectedRows as ROW_TYPE).filter(
      (row) => row.data.sfdType === EvstSFDType.FundTransfer
    )?.length > 1 // We do not support M*N matching for fund transfers.
  );
}

export function onClickRowCheckbox(context: Context, selectedRows: ROW_TYPE) {
  const { Decimal, state, data } = context;

  let total = new Decimal(0);
  state.matches = [];
  state.selectedRows = selectedRows;
  for (const row of selectedRows) {
    const {
      sourceJournalEntryLineId,
      amount,
      sfdId,
      sfdType,
      sfdPath,
      sfdNumber,
      journalEntryHeaderType,
      accountId,
    } = row.data;

    const suggestion = {
      sourceJournalEntryLineId,
      amount,
      sfdId,
      sfdType,
      sfdPath,
      sfdNumber,
      journalEntryHeaderType,
      bankTransactionId: data.bankTransactions[0].id,
      accountId,
    };

    state.matches.push(suggestion);

    const lineAmount = amount ? new Decimal(amount) : new Decimal(0);
    total = total.plus(lineAmount);
  }
  state.total = total.toFixed(2);
}

export function getTableFooter(context: Context) {
  const { state } = context;
  return [
    {
      postingDate: 'Total of selected transactions',
      amountWithCurrency: new Decimal(state.total),
    },
  ];
}

export function close(context: Context) {
  const { helpers } = context;

  helpers.closeModal();
}

export function setPartialMatching({ state }: Context, newValue: boolean) {
  state.partialMatching = newValue;
}

export function getActionGroupLabel({ state }: Context) {
  return state.type === 'bank'
    ? 'Create Bank Entry'
    : 'Create Credit Card Entry';
}

export function getActionGroupDescription({ state }: Context) {
  return state.type === 'bank'
    ? 'Resolve partial amount matches using a bank entry for the difference'
    : 'Resolve partial amount matches using a credit card entry for the difference';
}

export function getBankTransactionDetails(context: Context): Any {
  const { data, helpers, Decimal } = context;

  let totalAmount = new Decimal(0);
  if (!data?.bankTransactions) {
    return helpers.parseValue({
      value: totalAmount.toFixed(2),
      parseAs: 'currency',
    });
  }
  for (const { amount } of data.bankTransactions ?? []) {
    totalAmount = totalAmount.plus(new Decimal(amount?.amount ?? 0));
  }

  return totalAmount.toFixed(2);
}

export function getPrimaryButtonLabel(context: Context) {
  const { state } = context;
  if (state.partialMatching) {
    return 'Next';
  }
  return '{{findMatches.actions.match}}';
}

export async function clearMatches({ actions, state }: Context) {
  return actions.run({
    bankTransactionMatchesTransient: {
      action: 'clearMatches',
      data: {
        sourceJournalEntryLineIds: state.matches.map(
          (m) => m.sourceJournalEntryLineId
        ),
      },
    },
  });
}

export function areAllSameAccount(context: Context) {
  const { data, state } = context;
  const accountId = data.bankTransactions[0].coaAccountId;
  return state.matches.every((match) => match.accountId === accountId);
}

export async function matchTransactions(context: Context) {
  const { actions, data, helpers, state } = context;
  if (!areAllSameAccount(context)) {
    return helpers.showToast({
      title: 'Account miss match',
      message: 'All accounts should be same as the selected bank transaction',
      type: 'error',
    });
  }
  if (state.partialMatching) {
    await (state.type === 'bank'
      ? createBankEntry(context)
      : createCreditCardEntry(context));
  } else {
    const response = await actions.run({
      bankTransactionMatchesTransient: {
        action: 'matchUnsuggestedDocuments',
        data: {
          bankTransactions: data.bankTransactions,
          documents: state.matches as Any,
        },
      },
    });

    if (!response?.error) {
      helpers.showToast({
        title: '{{findMatches.trasaction.match}}',
        message: '{{findMatches.trasaction.match.success}}',
        type: 'success',
      });
      await clearMatches(context);
      await helpers.currentModalSubmitCallback();
      helpers.closeModal();
    }
  }
}

export async function createBankEntry(context: Context) {
  const { actions, data, helpers, state } = context;
  const suggestions = await actions.run({
    bankTransactionMatchesTransient: {
      action: 'createSuggestionsFromUnmatchedDocuments',
      data: {
        bankTransactions: data.bankTransactions,
        documents: state.matches,
      },
    },
  });
  if (!suggestions?.error) {
    const createdSuggestions = suggestions.bankTransactionMatchesTransient[0];
    const sfdIds = createdSuggestions.map((s) => s.id);
    const bankTransactionIds = data.bankTransactions.map((t) => t.id);
    await helpers.openModal({
      title: `New Bank Entry for Bank Transaction #`,
      size: 'large',
      initialState: {
        mode: 'create',
        bankTransactionIds: bankTransactionIds,
        matchIds: sfdIds,
      },
      template: `/templates/everest.fin.integration.bank/uinext/bankEntry/bankEntry`,
      onModalSubmit: async (bankEntryHeaderId: number) => {
        if (bankEntryHeaderId) {
          const matchId = createdSuggestions[0].bankTransactionMatchId;
          const bankEntryResponse = await actions.run({
            bankTransactionMatchesTransient: {
              action: 'createBankEntrySuggestion',
              data: {
                where: {
                  id: { $in: bankTransactionIds },
                } as unknown as BankTransactionNew,
                bankEntryHeaderId,
                matchId,
              },
            },
          });

          if (!bankEntryResponse?.error) {
            const [bankEntrySuggestion] =
              bankEntryResponse.bankTransactionMatchesTransient;
            helpers.showNotificationMessage({
              key: 'bankTransaction',
              type: 'loading',
              message: 'Matching...',
              duration: 10,
            });
            const matchResponse = await actions.run({
              bankTransactionMatchesTransient: {
                action: 'matchBankTransactions',
                data: {
                  bankTransactions: bankTransactionIds.map((id) => ({ id })),
                  suggestions: [...bankEntrySuggestion, ...createdSuggestions],
                },
              },
            });
            if (matchResponse?.error) {
              helpers.showNotificationMessage({
                key: 'bankTransaction',
                type: 'error',
                message: 'Error',
                duration: 2,
              });
            } else {
              helpers.showNotificationMessage({
                key: 'bankTransaction',
                title: '{{findMatches.trasaction.match}}',
                message: '{{findMatches.trasaction.match.success}}',
                type: 'success',
              });
              await clearMatches(context);
              await helpers.currentModalSubmitCallback();
              helpers.closeModal();
            }
          }
          await helpers.currentModalSubmitCallback();
        } else {
          helpers.showNotificationMessage({
            key: 'bankEntry',
            message: 'Did not receive bank entry ID',
            type: 'error',
          });
        }
      },
      onClose: () => {
        const { sections } = context;
        sections['allUnmatchedTransactionTable'].unselectAllRows();
      },
    });
  }
}

export async function createCreditCardEntry(context: Context) {
  const { actions, data, helpers, state } = context;
  const suggestions = await actions.run({
    bankTransactionMatchesTransient: {
      action: 'createSuggestionsFromUnmatchedDocuments',
      data: {
        bankTransactions: data.bankTransactions,
        documents: state.matches,
      },
    },
  });
  if (!suggestions?.error) {
    const createdSuggestions = suggestions.bankTransactionMatchesTransient[0];
    const sfdIds = createdSuggestions.map((s) => s.id);
    const bankTransactionIds = data.bankTransactions.map((t) => t.id);
    await helpers.openModal({
      title: `New Credit Card Entry for Card Transaction #`,
      size: 'large',
      initialState: {
        mode: 'create',
        bankTransactionIds: bankTransactionIds,
        matchIds: sfdIds,
      },
      template: `/templates/everest.fin.integration.bank/uinext/creditCardEntry/creditCardEntry`,
      onModalSubmit: async (creditCardEntryHeaderId: number) => {
        if (creditCardEntryHeaderId) {
          const matchId = createdSuggestions[0].bankTransactionMatchId;
          const creditCardEntryResponse = await actions.run({
            bankTransactionMatchesTransient: {
              action: 'createCreditCardEntrySuggestion',
              data: {
                where: {
                  id: { $in: bankTransactionIds },
                } as Any,
                creditCardEntryHeaderId,
                matchId,
              },
            },
          });

          if (!creditCardEntryResponse?.error) {
            const [creditCardEntrySuggestion] =
              creditCardEntryResponse.bankTransactionMatchesTransient;
            helpers.showNotificationMessage({
              key: 'bankTransaction',
              type: 'loading',
              message: 'Matching...',
              duration: 10,
            });
            const matchResponse = await actions.run({
              bankTransactionMatchesTransient: {
                action: 'matchBankTransactions',
                data: {
                  bankTransactions: bankTransactionIds.map((id) => ({ id })),
                  suggestions: [
                    ...creditCardEntrySuggestion,
                    ...createdSuggestions,
                  ],
                },
              },
            });
            if (matchResponse?.error) {
              helpers.showNotificationMessage({
                key: 'bankTransaction',
                type: 'error',
                message: 'Error',
                duration: 2,
              });
            } else {
              helpers.showToast({
                title: '{{findMatches.trasaction.match}}',
                message: '{{findMatches.trasaction.match.success}}',
                type: 'success',
              });
              await clearMatches(context);
              await helpers.currentModalSubmitCallback();
              helpers.closeModal();
            }
          }
          await helpers.currentModalSubmitCallback();
        } else {
          helpers.showNotificationMessage({
            key: 'bankEntry',
            message: 'Did not receive bank entry ID',
            type: 'error',
          });
        }
      },
    });
  }
}

export function goToSource(context: Context, row: Any) {
  const { helpers } = context;
  const sfdType = row?.data.sfdType;
  const sfdId = row?.data.sfdId;

  switch (sfdType) {
    case EvstSFDType.ManualJournalEntry: {
      helpers.navigate({
        to: `/templates/everest.fin.accounting/journalEntry/uinext/journalEntry?id=${sfdId}`,
      });
      break;
    }
    case EvstSFDType.CustomerPayment: {
      helpers.navigate({
        to: `/templates/everest.fin.accounting/qtc/payment/uinext/payment?id=${sfdId}`,
      });
      break;
    }
    case EvstSFDType.BankEntryNew: {
      helpers.navigate({
        to: `/templates/everest.fin.integration.bank/uinext/bankEntry/bankEntry?id=${sfdId}`,
      });
      break;
    }
    case EvstSFDType.CreditCardEntry: {
      helpers.navigate({
        to: `/templates/everest.fin.integration.bank/uinext/creditCardEntry/creditCardEntry?id=${sfdId}`,
      });
      break;
    }
    case EvstSFDType.FundTransfer: {
      helpers.navigate({
        to: `/templates/everest.fin.integration.bank/uinext/fundTransfer/fundTransfer?id=${sfdId}`,
      });
      break;
    }
    case EvstSFDType.OutboundPayment: {
      helpers.navigate({
        to: `/templates/everest.fin.expense/uinext/paymentRouter?id=${sfdId}`,
      });
      break;
    }
    default: {
      helpers.showToast({
        title: `{{findMatches.document.invalid.title}}`,
        message: `{{findMatches.document.invalid.message}} ${sfdType}`,
        type: 'error',
      });
    }
  }
}
