{"version": 2, "uicontroller": ["userConsent.uicontroller.ts"], "uimodel": {"state": {"confirmed": false, "integrationName": "SaltEdge", "items": []}, "nodes": {"integrations": {"type": "list", "getUserConsent": "@controller:getUserConsentQueryParams()", "modelId": "everest.fin.integration.bank/IntegrationModel.Integration", "fieldList": []}}}, "uiview": {"templateType": "generic", "i18n": "everest.fin.expense/employeeExpense", "sections": {"content": [{"component": "ActionGroup", "versionNew": true, "customId": "userConsentActionGroup", "isEditing": true, "title": "Everest uses a service called SaltEdge to connect to non-US bank accounts.", "data": "@controller:getUserConsentLines()", "buttonActionsPosition": "right"}]}, "actions": {"content": [{"variant": "primary", "label": "Save", "align": "right", "disabled": "@controller:isSaveActionDisabled()", "onClick": "@controller:saveAction"}]}}}