{"version": 2, "uicontroller": ["create.uicontroller.ts"], "uimodel": {"state": {}, "nodes": {"bankTransaction": {"type": "struct", "modelId": "everest.fin.integration.bank/BankTransactionNewModel.BankTransactionNew", "fieldList": ["externalCreationDate", "amount", "currency", "bankAccountId", "payeeOrPayor", "externalDescription", "externalTransactionId"]}, "bankAccounts": {"type": "list", "modelId": "everest.fin.accounting/BankAccountModel.BankAccount", "query": {"orderBy": ["accountName"]}, "fieldList": ["id", "bankName", "accountName", "currency"]}}}, "uiview": {"templateType": "details", "i18n": "everest.fin.expense/vendorMgmt", "title": "Create bank transaction sample data", "config": {"stretch": true, "autoRefreshData": true}, "header": {"size": "medium"}, "sections": {"content": [{"component": "FieldGroup", "size": "12", "type": "primary", "editing": true, "columns": "4", "action": "create", "elements": [{"value": "@binding:bankTransaction.externalCreationDate", "action": "create"}, {"value": "@binding:bankTransaction.amount", "action": "create"}, {"component": "Select", "label": "Bank account ID", "idProp": "id", "textProp": "bankName", "value": "@binding:bankTransaction.bankAccountId", "suffixCornerProp": "currency", "list": "@binding:bankAccounts", "action": "create", "onChange": "@controller:setState"}, {"value": "@binding:bankTransaction.payeeOrPayor", "action": "create"}, {"value": "@binding:bankTransaction.externalDescription", "action": "create"}]}]}, "actions": {"content": [{"variant": "primary", "align": "right", "label": "Create", "onClick": "@controller:create"}]}}}