// @i18n:everest.fin.accounting/accounting
import type { UIExecutionContext } from '@everestsystems/content-core';

type BasicExecutionContext = UIExecutionContext.BasicExecutionContext;

export type VoidReasonContext = BasicExecutionContext & {
  state: Record<string, unknown>;
};

//This is a generic template for inputing the void Reason from User
type Context = VoidReasonContext & {
  state: {
    reason?: string;
  };
};

export function updateReasonInput(context: Context, newValue: string) {
  const { state } = context;
  state.reason = newValue;
}

export async function returnVoidReason(context: Context) {
  const { helpers, state } = context;

  if (!state.reason?.trim()) {
    helpers.showToast({
      title: '{{ voidReason.reasonNotProvided }}',
      type: 'error',
      message: '{{ voidReason.reasonNotProvidedDetails }}',
    });
    return;
  }

  helpers.currentModalSubmitCallback({ reason: state.reason });
  helpers.closeModal();
}

export function closeVoidReasonModal(context: Context) {
  const { helpers } = context;
  helpers.closeModal();
}
