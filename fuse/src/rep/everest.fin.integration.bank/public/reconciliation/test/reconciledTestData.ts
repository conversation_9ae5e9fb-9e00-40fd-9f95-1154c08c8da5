import type { ControllerClientProvider } from '@everestsystems/content-core';
import type { TestDataContext } from '@everestsystems/content-core/lib/tests';
import { PlainDate } from '@everestsystems/datetime';
import { Decimal } from '@everestsystems/decimal';
import { EvstBaseCurrency } from '@pkg/everest.base/types/enums/BaseCurrency';
import { toPlainDate } from '@pkg/everest.fin.accounting/public/utils/Date/date';
import { EvstBankTransactionMatchStatus } from '@pkg/everest.fin.base/types/enums/BankTransactionMatchStatus';
import { EvstSFDType } from '@pkg/everest.fin.base/types/enums/SFDType';
import { BankTransactionMatch } from '@pkg/everest.fin.integration.bank/types/BankTransactionMatch';
import { BankTransactionNew } from '@pkg/everest.fin.integration.bank/types/BankTransactionNew';
import { EvstAccountReconciliationStatus } from '@pkg/everest.fin.integration.bank/types/enums/AccountReconciliationStatus';
import { JournalEntryLineInTransit } from '@pkg/everest.fin.integration.bank/types/JournalEntryLineInTransit';
import { MatchSourceFinancialDocument } from '@pkg/everest.fin.integration.bank/types/MatchSourceFinancialDocument';

import { createBankReconciliationTestData } from './baseTestData';
import {
  createAccountingPeriods,
  createJournalEntry,
  createSampleAccountReconciliation,
} from './testDataFactory';

export const RECONCILIATION_CONTEXT_UUID =
  '228409ea-9987-4cf8-9fd6-bfae89fa8a2a';

export type ReconciledTestData = Awaited<
  ReturnType<typeof createMatchedReconciliationTestData>
>;

export async function setupReconciledTestData(context: TestDataContext) {
  /**
   * TODO: currently there's no concept of "enhanced test data context".
   * Any attemp to chain setupBankReconciliationTestData will cause the test data to be re-generated.
   * Once the package management supports it, we should migrate.
   * https://github.com/everestsystems/appserver/issues/15528
   */
  const reconciliationContext = context.with({
    uuid: RECONCILIATION_CONTEXT_UUID,
    fn: (session) => createMatchedReconciliationTestData(session),
  });

  const testData = await reconciliationContext.createCheckpoint();
  return {
    enhancedContext: reconciliationContext,
    data: testData.stepsResults[0],
  };
}

/**
 * Creates matched reconciliation test data by enhancing base data with:
 *
 * - Two AccountReconciliations: One complete for the first period, one in progress for the second period
 * - BankTransactionMatches: For bank transactions up to the second period's end date
 * - MatchSourceFinancialDocuments: Linking bank transactions to journal entry lines
 * - Unmatched: One journal entry is left unmatched in the in progress reconciliation period
 * - JournalEntryLinesInTransit: For credit lines of journal entries
 *
 * @param session - The controller client provider session
 * @param baseData - The base reconciliation test data
 */
export async function createMatchedReconciliationTestData(
  session: ControllerClientProvider
) {
  const baseData = await createBankReconciliationTestData(session);
  const {
    account,
    postingAccount,
    accountingPeriods,
    entity,
    fiscalCalendar,
    journalEntries: allJournalEntries,
  } = baseData;

  const [firstPeriod, secondPeriod] = accountingPeriods;

  const [[periodBeforeAccountOpen], ...reconciliations] = await Promise.all([
    createAccountingPeriods(session, fiscalCalendar.id, 2023, 1),
    createSampleAccountReconciliation(session, account.id, firstPeriod.id, {
      status: EvstAccountReconciliationStatus.Complete,
      statementEndBalance: {
        amount: new Decimal('399.75'),
        currency: EvstBaseCurrency.USD,
      },
      statementEndDate: toPlainDate(firstPeriod.endDateISOString),
    }),
    createSampleAccountReconciliation(session, account.id, secondPeriod.id, {
      status: EvstAccountReconciliationStatus.InProgress,
      statementEndBalance: {
        amount: new Decimal('100.00'),
        currency: EvstBaseCurrency.USD,
      },
      statementEndDate: toPlainDate(secondPeriod.endDateISOString),
    }),
  ]);

  // A journal to capture the account open balance
  await createJournalEntry(
    session,
    entity.id,
    account.id,
    postingAccount.id,
    periodBeforeAccountOpen.id,
    toPlainDate(new Date(2023, 0, 15)),
    new Decimal(100)
  );

  const journalEntries = allJournalEntries.filter(
    (je) =>
      PlainDate.compare(
        je.header.postingDate,
        toPlainDate(secondPeriod.endDateISOString)
      ) <= 0
  );

  const journalEntryLineInTransitData: JournalEntryLineInTransit.CreationFields[] =
    [];

  // Keep the last transaction from the in progress period unmatched
  const outstandingJournalEntry = journalEntries.at(-1);
  const unmatchedBankTransactionId =
    outstandingJournalEntry.matchingBankTransactionId;
  const unmatchedJournalEntryLineId = outstandingJournalEntry.lines[0].id;
  const outstandingJournalEntryLineId = outstandingJournalEntry.lines[1].id;

  const matchedJournalEntries = [];
  const unmatchedJournalEntries = [];
  const inTransitJournalEntryLines = {
    [firstPeriod.id]: [],
    [secondPeriod.id]: [],
  };

  const matchClient = await BankTransactionMatch.client(session);
  const bankTransactionClient = await BankTransactionNew.client(session);
  const matchSourceFinancialDocumentClient =
    await MatchSourceFinancialDocument.client(session);

  for (const journalEntry of journalEntries) {
    const {
      matchingBankTransactionId,
      lines: [debitLine, creditLine],
    } = journalEntry;

    if (journalEntry === outstandingJournalEntry) {
      unmatchedJournalEntries.push(journalEntry);
      continue;
    }

    matchedJournalEntries.push(journalEntry);

    // Debit lines are matched; credits marked as in transit
    const match = await matchClient.create(
      {
        status: EvstBankTransactionMatchStatus.Matched,
        autoMatched: false,
      },
      ['id']
    );
    await bankTransactionClient.update(
      { id: matchingBankTransactionId },
      { matchId: match.id },
      ['id']
    );
    await matchSourceFinancialDocumentClient.create(
      {
        amount: debitLine.glAmount.amount,
        bankTransactionMatchId: match.id,
        suggestionConfidence: new Decimal(1),
        sourceJournalEntryLineId: debitLine.id,
        sfdId: journalEntry.header.id,
        sfdType: EvstSFDType.ManualJournalEntry,
      },
      ['id']
    );

    const currentPeriodIndex = accountingPeriods.findIndex(
      (period) => period.id === creditLine.postingPeriodId
    );
    const nextAccountingPeriodId =
      accountingPeriods[currentPeriodIndex + 1]?.id;

    journalEntryLineInTransitData.push({
      accountingPeriodId: creditLine.postingPeriodId,
      journalEntryLineId: creditLine.id,
      nextAccountingPeriodId,
    });

    if (inTransitJournalEntryLines[creditLine.postingPeriodId]) {
      inTransitJournalEntryLines[creditLine.postingPeriodId].push(
        creditLine.id
      );
    }
  }

  await JournalEntryLineInTransit.createMany(
    session,
    journalEntryLineInTransitData,
    ['id', 'accountingPeriodId', 'journalEntryLineId']
  );

  return {
    ...baseData,
    reconciliations,
    unmatchedBankTransactions: [unmatchedBankTransactionId],
    unmatchedJournalEntryLines: [unmatchedJournalEntryLineId],
    outstandingJournalEntryLines: [outstandingJournalEntryLineId],
    matchedJournalEntries,
    unmatchedJournalEntries,
    inTransitJournalEntryLines,
  };
}
