import type { ControllerClientProvider } from '@everestsystems/content-core';
import { PlainDate } from '@everestsystems/datetime';
import { Decimal } from '@everestsystems/decimal';
import { Address } from '@pkg/everest.base/types/Address';
import { Entity } from '@pkg/everest.base/types/Entity';
import { EvstBaseCurrency } from '@pkg/everest.base/types/enums/BaseCurrency';
import { EvstEntityStatus } from '@pkg/everest.base/types/enums/EntityStatus';
import { upsertEntityBook } from '@pkg/everest.fin.accounting/public/accountingBook/upsertEntityBook';
import {
  toJSDate,
  toPlainDate,
} from '@pkg/everest.fin.accounting/public/utils/Date/date';
import { Account } from '@pkg/everest.fin.accounting/types/Account';
import { AccountingPeriod } from '@pkg/everest.fin.accounting/types/AccountingPeriod';
import { BankAccount } from '@pkg/everest.fin.accounting/types/BankAccount';
import { ChartOfAccounts } from '@pkg/everest.fin.accounting/types/ChartOfAccounts';
import { EntityBook } from '@pkg/everest.fin.accounting/types/EntityBook';
import { EvstAccountNumbering } from '@pkg/everest.fin.accounting/types/enums/AccountNumbering';
import { EvstAccountStatus } from '@pkg/everest.fin.accounting/types/enums/AccountStatus';
import { EvstAccountSubType } from '@pkg/everest.fin.accounting/types/enums/AccountSubType';
import { EvstAccountType } from '@pkg/everest.fin.accounting/types/enums/AccountType';
import { EvstCalendarPeriodFormat } from '@pkg/everest.fin.accounting/types/enums/CalendarPeriodFormat';
import { EvstFiscalCalendarStatus } from '@pkg/everest.fin.accounting/types/enums/FiscalCalendarStatus';
import { EvstJournalEntryStatus } from '@pkg/everest.fin.accounting/types/enums/JournalEntryStatus';
import { EvstJournalEntryType } from '@pkg/everest.fin.accounting/types/enums/JournalEntryType';
import { FiscalCalendar } from '@pkg/everest.fin.accounting/types/FiscalCalendar';
import { JournalEntryHeader } from '@pkg/everest.fin.accounting/types/JournalEntryHeader';
import { JournalEntryLine } from '@pkg/everest.fin.accounting/types/JournalEntryLine';
import { EvstBankAccountType } from '@pkg/everest.fin.base/types/enums/BankAccountType';
import { AccountReconciliation } from '@pkg/everest.fin.integration.bank/types/AccountReconciliation';
import { BankTransactionNew } from '@pkg/everest.fin.integration.bank/types/BankTransactionNew';
import { EvstAccountReconciliationStatus } from '@pkg/everest.fin.integration.bank/types/enums/AccountReconciliationStatus';

import {
  getDefaultAccountingBook,
  getDefaultAccountingBookId,
} from '../../../../everest.fin.accounting/public/accountingBook/getDefaultAccountingBook';
import { CoAHierarchy } from '@pkg/everest.fin.accounting/types/CoAHierarchy';
import { JournalEntryBusinessObject } from '@pkg/everest.fin.accounting/types/BOComposition/JournalEntryBusinessObject';
import { EntityAccountingPeriodStatus } from '@pkg/everest.fin.accounting/types/EntityAccountingPeriodStatus';
import { EvstAccountingPeriodStatus } from '@pkg/everest.fin.accounting/types/enums/AccountingPeriodStatus';

export function randomString(prefix = '', length = 5) {
  const randomPart = Math.random()
    .toString(36)
    .slice(2, 2 + length);
  const timestamp = Date.now().toString(36);
  return `${prefix}${timestamp}${randomPart}`;
}

export async function createSampleAddress(
  session: ControllerClientProvider,
  addressOverride: Partial<Address.Address> = {}
) {
  return Address.create(
    session,
    {
      attention: 'Sample Company',
      line1: '123 Sample Street',
      city: 'Sample City',
      stateProvince: 'US-CA',
      country: 'US',
      zipCode: '12345',
      package: 'everest.fin.accounting',
      ...addressOverride,
    },
    ['id', 'line1', 'city', 'stateProvince', 'country']
  );
}

export async function createSampleEntity(
  session: ControllerClientProvider,
  entityOverride: Partial<Entity.Entity> = {}
) {
  const address = await createSampleAddress(session);

  return Entity.create(
    session,
    {
      entityName: randomString('Entity '),
      currency: EvstBaseCurrency.USD,
      defaultRegisteredAddressId: address.id,
      country: address.country,
      status: EvstEntityStatus.Active,
      ...entityOverride,
    },
    ['id', 'entityName', 'currency', 'country']
  );
}

export async function createSampleChartOfAccounts(
  session: ControllerClientProvider,
  entityId: number,
  coaOverride: Partial<ChartOfAccounts.ChartOfAccounts> = {}
) {
  const chartOfAccounts = await ChartOfAccounts.create(
    session,
    {
      accountNumbering: EvstAccountNumbering.Manual,
      description: 'Sample Chart of Accounts',
      name: 'Sample CoA',
      ...coaOverride,
    },
    ['id', 'name', 'accountNumbering', 'accountNumberingDigits']
  );

  await upsertEntityBook(session, {
    entityId,
    chartOfAccountsId: chartOfAccounts.id,
  });

  return chartOfAccounts;
}

export async function createSampleCoABankAccount(
  session: ControllerClientProvider,
  entityId: number,
  chartOfAccountsId: number,
  accountOverride: Partial<Account.Account> = {},
  parentId?: number
) {
  const account = await Account.create(
    session,
    {
      entityId,
      accountName: 'Sample Bank Account',
      accountNumber: 'B12345',
      accountSubType: EvstAccountSubType.Bank,
      accountType: EvstAccountType.Asset,
      accountStatus: EvstAccountStatus.Enabled,
      chartOfAccountsId,
      groupingAccount: false,
      currency: EvstBaseCurrency.USD,
      ...accountOverride,
    },
    [
      'id',
      'accountName',
      'accountNumber',
      'accountSubType',
      'accountType',
      'currency',
    ]
  );

  await CoAHierarchy.create(session, {
    nodeId: account.id,
    parentId,
    validFrom: new Date(0),
  });

  return account;
}

export async function createSampleCoAAccount(
  session: ControllerClientProvider,
  chartOfAccountsId: number,
  accountOverride: Partial<Account.Account> = {}
) {
  return Account.create(
    session,
    {
      accountName: '',
      accountNumber: '',
      accountType: EvstAccountType.Asset,
      accountStatus: EvstAccountStatus.Enabled,
      chartOfAccountsId,
      groupingAccount: true,
      ...accountOverride,
    },
    ['id', 'accountName', 'accountNumber', 'accountType']
  );
}

export async function createSampleBankAccount(
  session: ControllerClientProvider,
  accountId: number,
  bankAccountOverride: Partial<BankAccount.BankAccount> = {}
) {
  return BankAccount.create(
    session,
    {
      accountId,
      balance: { amount: new Decimal(0) },
      currency: 'USD',
      externalAccountId: randomString('BACC'),
      externalDescription: 'exDesc',
      paymentAllowed: true,
      reference: 'rex-xx',
      type: EvstBankAccountType.Checking,
      mask: '1122',
      isReady: true,
      payableAccount: false,
      deleted: false,
      bankAccountHolderName: 'Test Account',
      bankAccountCode: '112DE',
      reconciliationStartDate: new PlainDate(2024, 1, 1),
      reconciliationStartBalance: {
        amount: new Decimal(100),
        currency: 'USD',
      },
      ...bankAccountOverride,
    },
    ['id', 'accountId', 'currency', 'accountName']
  );
}

export async function createSampleFiscalCalendar(
  session: ControllerClientProvider,
  entityId: number,
  fiscalCalendarOverride: Partial<FiscalCalendar.FiscalCalendar> = {}
) {
  const fiscalCalendar = await FiscalCalendar.create(
    session,
    {
      calendarName: randomString('Fiscal Calendar '),
      description: 'Standard calendar',
      periodFormat: EvstCalendarPeriodFormat.CalendarMonths,
      status: EvstFiscalCalendarStatus.Active,
      ...fiscalCalendarOverride,
    },
    ['id', 'calendarName']
  );

  const { id: bookId } = await getDefaultAccountingBook(session);

  await EntityBook.upsert(
    session,
    {
      bookId,
      entityId: entityId,
      fiscalCalendarId: fiscalCalendar.id,
    },
    ['id', 'entityId', 'fiscalCalendarId']
  );

  return fiscalCalendar;
}

export async function createAccountingPeriods(
  session: ControllerClientProvider,
  fiscalCalendarId: number,
  year: number,
  numberOfPeriods: number = 12
) {
  const accountingPeriodsData = Array.from(
    { length: numberOfPeriods },
    (_, i) => {
      const startDate = toPlainDate(new Date(year, i, 1));
      const endDate = toPlainDate(new Date(year, i + 1, 0));
      return {
        endDate,
        fiscalCalendarId,
        fiscalQuarter: `Q${Math.floor((i + 3) / 3)} ${year}`,
        fiscalYear: `FY${year}`,
        periodName: `${toJSDate(startDate).toLocaleString('default', {
          month: 'short',
        })} ${year}`,
        startDate,
      };
    }
  );

  const accountingPeriods = await AccountingPeriod.createMany(
    session,
    accountingPeriodsData,
    ['id', 'periodName', 'startDate', 'endDate', 'fiscalQuarter', 'fiscalYear']
  );

  await EntityAccountingPeriodStatus.updateMany(
    session,
    { accountingPeriodId: { $in: accountingPeriods.map(({ id }) => id) } },
    { status: EvstAccountingPeriodStatus.Open }
  );

  return accountingPeriods.sort((periodA, periodB) =>
    PlainDate.compare(periodA.endDate, periodB.endDate)
  );
}

export async function createJournalEntry(
  session: ControllerClientProvider,
  entityId: number,
  debitAccountId: number,
  creditAccountId: number,
  postingPeriodId: number,
  postingDate: PlainDate,
  amount: Decimal,
  journalEntryStatus = EvstJournalEntryStatus.Posted
) {
  const randomNumber = Math.floor(Math.random() * 10_000); // Generate a random number between 0 and 9999
  const description = `${randomNumber}`;

  const bookId = await getDefaultAccountingBookId(session);

  const journalEntryHeader = await JournalEntryHeader.create(
    session,
    {
      postingDate,
      type: EvstJournalEntryType.Manual,
      journalEntryStatus,
      journalEntryName: `Journal entry: ${description}`,
      posted:
        journalEntryStatus === EvstJournalEntryStatus.Posted ? true : false,
      bookId,
    },
    ['id', 'postingDate']
  );

  const commonProperties = {
    journalEntryHeaderId: journalEntryHeader.id,
    exchangeRate: new Decimal(1),
    entityId,
    transactionalCurrency: EvstBaseCurrency.USD,
    functionalCurrency: EvstBaseCurrency.USD,
    transactionDate: postingDate,
    postingPeriodId,
    glAmount: { amount: amount },
  };

  const absoluteAmount = amount.absoluteValue();
  const debitLine = {
    ...commonProperties,
    accountId: debitAccountId,
    debit: { amount: absoluteAmount },
    glDebit: { amount: absoluteAmount },
    glAmount: { amount: absoluteAmount },
    transactionAmount: { amount: absoluteAmount },
    description: `Debit entry: ${description}`,
  };

  const creditLine = {
    ...commonProperties,
    accountId: creditAccountId,
    credit: { amount: absoluteAmount },
    glCredit: { amount: absoluteAmount },
    glAmount: { amount: absoluteAmount.times(-1) },
    transactionAmount: { amount: absoluteAmount.times(-1) },
    description: `Credit entry: ${description}`,
  };

  const matchingLine = amount.greaterThan(0) ? debitLine : creditLine;
  const otherLine = amount.greaterThan(0) ? creditLine : debitLine;

  const journalEntryLines = await JournalEntryLine.createMany(
    session,
    [
      { ...matchingLine, jeLocalLineNumber: 1 },
      { ...otherLine, jeLocalLineNumber: 2 },
    ],
    [
      'id',
      'glAmount',
      'transactionAmount',
      'postingPeriodId',
      'jeLocalLineNumber',
      'accountId',
    ]
  );

  await JournalEntryBusinessObject.create(session, {
    headerKey: journalEntryHeader.id,
  });

  return { header: journalEntryHeader, lines: journalEntryLines };
}

export async function createMultipleBankTransactions(
  session: ControllerClientProvider,
  bankAccountId: number,
  accountingPeriods: Pick<
    AccountingPeriod.AccountingPeriod,
    'endDate' | 'startDate' | 'id'
  >[],
  countPerPeriod: number
) {
  const fixedAmounts = [
    new Decimal('100.00'),
    new Decimal('-50.00'),
    new Decimal('75.50'),
    new Decimal('-25.75'),
    new Decimal('200.00'),
  ];
  // SUM: 299,75 USD

  const transactionsData = accountingPeriods.flatMap((period, periodIndex) =>
    Array.from({ length: countPerPeriod }, (_, i) => {
      const transactionDate = toJSDate(period.endDate);
      transactionDate.setDate(transactionDate.getDate() - i);
      let amount = fixedAmounts[i % fixedAmounts.length];

      // Flip the sign of the amount for odd index accounting periods
      if (periodIndex % 2 !== 0) {
        amount = amount.times(-1);
      }

      return {
        externalTransactionId: randomString('BANKTX'),
        amount: { amount },
        currency: EvstBaseCurrency.USD,
        bankAccountId,
        externalCreationDate: PlainDate.from(transactionDate.toISOString()),
        externalDescription: `Sample bank transaction for period ${period.id}`,
        payeeOrPayor: randomString('BP'),
      };
    })
  );

  return BankTransactionNew.createMany(session, transactionsData, [
    'id',
    'amount',
    'currency',
    'externalCreationDate',
    'externalDescription',
  ]);
}

export async function createSampleAccountReconciliation(
  session: ControllerClientProvider,
  accountId: number,
  accountingPeriodId: number,
  reconciliationOverride: Partial<AccountReconciliation.AccountReconciliation> = {}
) {
  return AccountReconciliation.create(
    session,
    {
      accountId,
      accountingPeriodId,
      status: EvstAccountReconciliationStatus.InProgress,
      ...reconciliationOverride,
    },
    ['id', 'accountingPeriodId', 'status', 'statementEndBalance']
  );
}
