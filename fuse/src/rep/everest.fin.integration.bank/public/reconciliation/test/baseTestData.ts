import type { ControllerClientProvider } from '@everestsystems/content-core';
import type { TestDataContext } from '@everestsystems/content-core/lib/tests';
import { PlainDate } from '@everestsystems/datetime';
import { EvstAccountSubType } from '@pkg/everest.fin.accounting/types/enums/AccountSubType';
import { EvstAccountType } from '@pkg/everest.fin.accounting/types/enums/AccountType';

import {
  createAccountingPeriods,
  createJournalEntry,
  createMultipleBankTransactions,
  createSampleBankAccount,
  createSampleChartOfAccounts,
  createSampleCoAAccount,
  createSampleCoABankAccount,
  createSampleEntity,
  createSampleFiscalCalendar,
} from './testDataFactory';

export const BANK_RECONCILIATION_CONTEXT_UUID =
  '1badcae9-6dcb-4832-bb70-7dabc41dc2bd';

export type BankReconciliationTestData = Awaited<
  ReturnType<typeof createBankReconciliationTestData>
>;

type BankTransaction = Awaited<
  ReturnType<typeof createMultipleBankTransactions>
>[number];

type AccountingPeriod = Awaited<
  ReturnType<typeof createAccountingPeriods>
>[number];

type SerializableBankTransaction = Omit<
  BankTransaction,
  'externalCreationDate'
> & {
  externalCreationDateISOString: string;
};

type SerializableAccountingPeriod = Omit<
  AccountingPeriod,
  'startDate' | 'endDate'
> & {
  startDateISOString: string;
  endDateISOString: string;
};

export async function setupBankReconciliationTestData(
  context: TestDataContext
) {
  const enhancedContext = context.with({
    uuid: BANK_RECONCILIATION_CONTEXT_UUID,
    fn: createBankReconciliationTestData,
  });

  const testData = await enhancedContext.createCheckpoint();
  return { enhancedContext, data: testData.stepsResults[0] };
}

/**
 * Creates test data for bank reconciliation.
 *
 * @returns An object containing:
 * - entity: A sample entity
 * - account: A sample posting bank account and a grouping account
 * - bankAccount: A sample bank account
 * - fiscalCalendar: A sample fiscal calendar
 * - accountingPeriods: Three accounting periods
 * - bankTransactions: Multiple bank transactions for each accounting period
 * - journalEntries: Journal entries corresponding to each bank transaction
 */
export async function createBankReconciliationTestData(
  session: ControllerClientProvider
) {
  const entity = await createSampleEntity(session);
  const chartOfAccounts = await createSampleChartOfAccounts(session, entity.id);
  const fiscalCalendar = await createSampleFiscalCalendar(session, entity.id);

  const [account, coaCCAccount, postingAccount, groupingAccount] =
    await Promise.all([
      createSampleCoABankAccount(session, entity.id, chartOfAccounts.id),
      createSampleCoABankAccount(session, entity.id, chartOfAccounts.id, {
        accountName: 'Sample Credit Card Account',
        accountType: EvstAccountType.Liability,
        accountSubType: EvstAccountSubType.CreditCard,
        accountNumber: 'CC123',
      }),
      createSampleCoAAccount(session, chartOfAccounts.id, {
        accountName: 'Sample Posting Account',
        accountNumber: 'P12345',
        groupingAccount: false,
        accountSubType: EvstAccountSubType.LongTermAsset,
      }),
      createSampleCoAAccount(session, chartOfAccounts.id, {
        accountName: 'Sample Grouping Account',
        accountNumber: 'G12345',
        groupingAccount: true,
      }),
    ]);

  const [bankAccount, ccAccount, accountingPeriods] = await Promise.all([
    createSampleBankAccount(session, account.id),
    createSampleBankAccount(session, coaCCAccount.id),
    createAccountingPeriods(session, fiscalCalendar.id, 2024, 3),
  ]);

  const [bankData, ccData] = await Promise.all([
    createTransactionsAndJournalEntries(
      session,
      bankAccount.id,
      account.id,
      entity.id,
      accountingPeriods,
      false
    ),
    createTransactionsAndJournalEntries(
      session,
      ccAccount.id,
      coaCCAccount.id,
      entity.id,
      accountingPeriods,
      true
    ),
  ]);

  return {
    entity,
    account,
    ccAccount,
    postingAccount,
    groupingAccount,
    bankAccount,
    fiscalCalendar,
    accountingPeriods: serializeAccountingPeriods(accountingPeriods),
    bankTransactions: bankData.bankTransactions,
    journalEntries: bankData.journalEntries,
    ccTransactions: ccData.bankTransactions,
    ccJournalEntries: ccData.journalEntries,
  };
}

async function createTransactionsAndJournalEntries(
  session: ControllerClientProvider,
  bankAccountId: number,
  accountId: number,
  entityId: number,
  accountingPeriods: AccountingPeriod[],
  isCreditCard: boolean
) {
  const bankTransactions = await createMultipleBankTransactions(
    session,
    bankAccountId,
    accountingPeriods,
    5
  );

  const journalEntries = await Promise.all(
    bankTransactions.map(async (transaction) => {
      const postingPeriod = accountingPeriods.find(
        (period) =>
          PlainDate.compare(
            transaction.externalCreationDate,
            period.startDate
          ) >= 0 &&
          PlainDate.compare(transaction.externalCreationDate, period.endDate) <=
            0
      );

      if (!postingPeriod) {
        throw new Error('No matching accounting period found for transaction');
      }

      const amount = isCreditCard
        ? transaction.amount.amount.times(-1)
        : transaction.amount.amount;

      const journalEntry = await createJournalEntry(
        session,
        entityId,
        accountId,
        accountId,
        postingPeriod.id,
        transaction.externalCreationDate,
        amount
      );
      return {
        matchingBankTransactionId: transaction.id,
        ...journalEntry,
      };
    })
  );

  return {
    bankTransactions: serializeBankTransactions(bankTransactions),
    journalEntries,
  };
}

/**
 * Checkpoint data must be serializable. Hence we convert PlainDate to string.
 */
function serializeBankTransactions(
  bankTransactions: BankTransaction[]
): SerializableBankTransaction[] {
  return bankTransactions.map(({ externalCreationDate, ...transaction }) => ({
    ...transaction,
    externalCreationDateISOString: externalCreationDate.toISO(),
  }));
}

/**
 * Checkpoint data must be serializable. Hence we convert PlainDate to string.
 */
function serializeAccountingPeriods(
  accountingPeriods: AccountingPeriod[]
): SerializableAccountingPeriod[] {
  return accountingPeriods.map(
    ({ startDate, endDate, ...accountingPeriod }) => ({
      ...accountingPeriod,
      startDateISOString: startDate.toISO(),
      endDateISOString: endDate.toISO(),
    })
  );
}
