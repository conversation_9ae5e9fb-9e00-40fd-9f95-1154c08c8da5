import type { ControllerClientProvider } from '@everestsystems/content-core';
import { getTranslationsMap } from '@everestsystems/content-core';
import type { PlainDate } from '@everestsystems/datetime';
import { Decimal } from '@everestsystems/decimal';
import type {
  UiTable2,
  UiTable2Json,
} from '@pkg/everest.analytics/public/formats';
import type { I18nEnv } from '@pkg/everest.base.translation/public/types';
import { ZERO } from '@pkg/everest.fin.accounting/public/utils/Decimal/DecimalUtils';
import { Account } from '@pkg/everest.fin.accounting/types/Account';
import { AccountingPeriod } from '@pkg/everest.fin.accounting/types/AccountingPeriod';
import type { ReconciliationDetailsResponse } from '@pkg/everest.fin.integration.bank/actions/reconciliation/getReconciliationDetails.action';
import getReconciliationDetails from '@pkg/everest.fin.integration.bank/actions/reconciliation/getReconciliationDetails.action';
import {
  calculateAccountBalance,
  calculateJELOutstandingBalance,
} from '@pkg/everest.fin.integration.bank/actions/reconciliation/utils/bookBalance';

type ReconciliationSummary = {
  endingBalance: Decimal;
  outstandingDeposits: Decimal;
  outstandingPayments: Decimal;
  adjustedBalance: Decimal;
};
type ReconciliationDetail = {
  bankSummary: ReconciliationSummary;
  glSummary: ReconciliationSummary;
  difference: Decimal;
  statementEndDate: PlainDate;
  statementBeginningBalance: Decimal;
  incomingJournalEntry: Decimal;
  outgoingJournalEntry: Decimal;
  incomingInTransitBankTransaction: Decimal;
  outgoingInTransitBankTransaction: Decimal;
  glBeginningBalance: Decimal;
  incomingInTransitGLTransaction: Decimal;
  outgoingInTransitGLTransaction: Decimal;
};
type TranslationMap = Awaited<
  ReturnType<typeof getReconciliationSummaryTranslations>
>;
export default async function getReconciliationSummary(
  session: ControllerClientProvider & I18nEnv,
  accountingPeriodId: number,
  accountId: number
): Promise<UiTable2Json> {
  const translationsPromise = getReconciliationSummaryTranslations(session);
  const accountingPeriodQueryPromise = AccountingPeriod.read(
    session,
    { id: accountingPeriodId },
    ['id', 'startDate', 'endDate']
  );
  const accountPromise = Account.read(session, { id: accountId }, [
    'id',
    'accountType',
    'currency',
    { 'Account-Entity': ['currency'] },
  ]);
  const [reconciliation, accountingPeriod, account, translations] =
    await Promise.all([
      getReconciliationDetails(session, accountingPeriodId, accountId),
      accountingPeriodQueryPromise,
      accountPromise,
      translationsPromise,
    ]);
  const [bankSummary, glSummary] = await Promise.all([
    getBankSummary(session, account, accountingPeriod, reconciliation),
    getGLSummary(
      session,
      account,
      accountingPeriod,
      reconciliation.incomingInTransitBankTransaction.amount,
      reconciliation.outgoingInTransitBankTransaction.amount
    ),
  ]);
  const reconciliationDetail = {
    bankSummary,
    glSummary,
    difference: bankSummary.adjustedBalance.minus(glSummary.adjustedBalance),
    statementEndDate: reconciliation.statementEndDate,
    statementBeginningBalance: reconciliation.statementBeginningBalance.amount,
    incomingJournalEntry: reconciliation.incomingJournalEntry.amount,
    outgoingJournalEntry: reconciliation.outgoingJournalEntry.amount,
    glBeginningBalance: reconciliation.glBeginningBalance.amount,
    // In Transit GL items should be presented under the Bank Summary because it needs to adjust the bank balance
    incomingInTransitBankTransaction:
      reconciliation.incomingInTransitGLTransaction.amount,
    outgoingInTransitBankTransaction:
      reconciliation.outgoingInTransitGLTransaction.amount,
    // In Transit Bank items should be presented under the GL Summary because it needs to adjust the GL balance
    incomingInTransitGLTransaction:
      reconciliation.incomingInTransitBankTransaction.amount,
    outgoingInTransitGLTransaction:
      reconciliation.outgoingInTransitBankTransaction.amount,
  };
  return outputAsTable(reconciliationDetail, translations);
}
async function getReconciliationSummaryTranslations(session: I18nEnv) {
  return getTranslationsMap(
    session,
    [
      'accountReconciliation.bankSummary',
      'accountReconciliation.glSummary',
      'accountReconciliation.endingStatementBalance',
      'accountReconciliation.depositsInTransit',
      'accountReconciliation.outstandingPayments',
      'accountReconciliation.adjustedBankBalance',
      'accountReconciliation.endingGLBalance',
      'accountReconciliation.adjustedGLBalance',
      'accountReconciliation.differenceSummary',
      'accountReconciliation.incomingTransactionsInTransit',
      'accountReconciliation.outgoingTransactionsInTransit',
      'accountReconciliation.statementBeginningBalance',
      'accountReconciliation.clearedOutgoingTransactions',
      'accountReconciliation.clearedIncomingTransactions',
      'accountReconciliation.beginningGLBalance',
    ],
    'everest.fin.integration.bank/accountReconciliation'
  );
}
export async function getBankSummary(
  session: ControllerClientProvider,
  account: Pick<Account.AccountWithAssociation, 'id'>,
  accountingPeriod: Pick<AccountingPeriod.AccountingPeriod, 'id'>,
  reconciliation: Pick<ReconciliationDetailsResponse, 'statementEndBalance'>
): Promise<ReconciliationSummary> {
  // Outstanding GL transactions contribute to the adjusted BANK account balance
  const { outstandingDeposits, outstandingPayments } =
    await calculateJELOutstandingBalance(
      session,
      account.id,
      accountingPeriod.id
    );
  const endingBalance = reconciliation.statementEndBalance?.amount ?? ZERO;
  const adjustedBalance = Decimal.getSum([
    endingBalance,
    outstandingDeposits,
    outstandingPayments,
  ]);
  return {
    endingBalance,
    outstandingDeposits,
    outstandingPayments,
    adjustedBalance,
  };
}
export async function getGLSummary(
  session: ControllerClientProvider,
  account: Pick<
    Account.AccountWithAssociation,
    'id' | 'accountType' | 'currency' | 'Account-Entity'
  >,
  accountingPeriod: Pick<
    AccountingPeriod.AccountingPeriod,
    'endDate' | 'startDate'
  >,
  incomingInTransit: Decimal,
  outgoingInTransit: Decimal
): Promise<ReconciliationSummary> {
  const {
    currency,
    'Account-Entity': { currency: functionalCurrency },
  } = account;
  const [endingBalance] = await Promise.all([
    // Outstanding BANK transactions contribute to the adjusted GL account balance
    calculateAccountBalance(
      session,
      account,
      currency,
      functionalCurrency,
      accountingPeriod.endDate
    ),
  ]);
  const adjustedBalance = Decimal.getSum([
    endingBalance,
    incomingInTransit,
    outgoingInTransit,
  ]);
  return {
    endingBalance,
    outstandingDeposits: incomingInTransit,
    outstandingPayments: outgoingInTransit,
    adjustedBalance,
  };
}
function outputAsTable(
  reconciliationDetail: ReconciliationDetail,
  translations: TranslationMap
): UiTable2Json {
  const commonColumnDefs = {
    suppressFilter: true,
    suppressMovable: true,
    suppressSort: true,
  };
  const columnDefs: UiTable2.ColumnDef[] = [
    {
      field: 'description',
      hide: true,
      fieldProps: { nullContent: 'empty' },
      ...commonColumnDefs,
    },
    {
      field: 'amount',
      ...commonColumnDefs,
      fieldProps: { nullContent: 'empty' },
      cellClass: 'currency',
    },
  ];
  const fields: Record<string, UiTable2.FieldMetadata> = {
    description: { label: 'Description', semanticType: 'Text' },
    amount: { label: 'Amount', semanticType: 'Amount' },
  };
  const metadata = {
    fields,
  };
  const formatDate = (date: PlainDate) => date.toISO();
  const rowData = [
    {
      nodeId: 'bankSummary',
      hasChildren: true,
      data: {
        description: translations.get('accountReconciliation.bankSummary'),
      },
    },
    {
      nodeId: 'bankBeginingBalance',
      parentId: 'bankSummary',
      data: {
        description: translations.get(
          'accountReconciliation.statementBeginningBalance'
        ),
        amount: reconciliationDetail.statementBeginningBalance,
      },
    },
    {
      nodeId: 'bankClearedOutgoingTransactions',
      parentId: 'bankSummary',
      data: {
        description: translations.get(
          'accountReconciliation.clearedOutgoingTransactions'
        ),
        amount: reconciliationDetail.outgoingJournalEntry,
      },
    },
    {
      nodeId: 'bankClearedIncomingTransactions',
      parentId: 'bankSummary',
      data: {
        description: translations.get(
          'accountReconciliation.clearedIncomingTransactions'
        ),
        amount: reconciliationDetail.incomingJournalEntry,
      },
    },
    {
      nodeId: 'bankEndingBalance',
      parentId: 'bankSummary',
      data: {
        description: translations
          .get('accountReconciliation.endingStatementBalance')
          .replace(
            '{{date}}',
            formatDate(reconciliationDetail.statementEndDate)
          ),
        amount: reconciliationDetail.bankSummary.endingBalance,
      },
    },
    {
      nodeId: 'bankIncomingTransactionsInTransit',
      parentId: 'bankSummary',
      data: {
        description: translations.get(
          'accountReconciliation.incomingTransactionsInTransit'
        ),
        amount: reconciliationDetail.incomingInTransitBankTransaction,
      },
    },
    {
      nodeId: 'bankOutgoingTransactionsInTransit',
      parentId: 'bankSummary',
      data: {
        description: translations.get(
          'accountReconciliation.outgoingTransactionsInTransit'
        ),
        amount: reconciliationDetail.outgoingInTransitBankTransaction,
      },
    },
    {
      nodeId: 'bankAdjustedBalance',
      isSummary: true,
      data: {
        description: translations.get(
          'accountReconciliation.adjustedBankBalance'
        ),
        amount: reconciliationDetail.bankSummary.adjustedBalance,
      },
    },
    {
      nodeId: 'glSummary',
      hasChildren: true,
      data: {
        description: translations.get('accountReconciliation.glSummary'),
      },
    },
    {
      nodeId: 'glBeginningBalance',
      parentId: 'glSummary',
      data: {
        description: translations.get(
          'accountReconciliation.beginningGLBalance'
        ),
        amount: reconciliationDetail.glBeginningBalance,
      },
    },
    {
      nodeId: 'glClearedOutgoingTransactions',
      parentId: 'glSummary',
      data: {
        description: translations.get(
          'accountReconciliation.clearedOutgoingTransactions'
        ),
        amount: reconciliationDetail.outgoingJournalEntry,
      },
    },
    {
      nodeId: 'glClearedIncomingTransactions',
      parentId: 'glSummary',
      data: {
        description: translations.get(
          'accountReconciliation.clearedIncomingTransactions'
        ),
        amount: reconciliationDetail.incomingJournalEntry,
      },
    },
    {
      nodeId: 'glEndingBalance',
      parentId: 'glSummary',
      data: {
        description: translations
          .get('accountReconciliation.endingGLBalance')
          .replace(
            '{{date}}',
            formatDate(reconciliationDetail.statementEndDate)
          ),
        amount: reconciliationDetail.glSummary.endingBalance,
      },
    },
    {
      nodeId: 'glAdjustedBalance',
      isSummary: true,
      data: {
        description: translations.get(
          'accountReconciliation.adjustedGLBalance'
        ),
        amount: reconciliationDetail.glSummary.adjustedBalance,
      },
    },
    {
      nodeId: 'glIncomingTransactionsInTransit',
      parentId: 'glSummary',
      data: {
        description: translations.get(
          'accountReconciliation.incomingTransactionsInTransit'
        ),
        amount: reconciliationDetail.incomingInTransitGLTransaction,
      },
    },
    {
      nodeId: 'glOutgoingTransactionsInTransit',
      parentId: 'glSummary',
      data: {
        description: translations.get(
          'accountReconciliation.outgoingTransactionsInTransit'
        ),
        amount: reconciliationDetail.outgoingInTransitGLTransaction,
      },
    },
  ];
  return {
    columnDefs,
    metadata,
    rowData,
    rowCount: rowData.length,
  };
}
