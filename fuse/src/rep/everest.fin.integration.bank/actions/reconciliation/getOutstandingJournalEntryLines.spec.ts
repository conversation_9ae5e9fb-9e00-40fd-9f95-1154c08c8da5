import type { ISession } from '@everestsystems/content-core';
import type {
  TestDataContext,
  TestTeardown,
} from '@everestsystems/content-core/lib/tests';
import { setupTest } from '@everestsystems/content-core/lib/tests';
import { PlainDate } from '@everestsystems/datetime';
import { Decimal } from '@everestsystems/decimal';
import { EvstAccountReconciliationStatus } from '@pkg/everest.fin.integration.bank/types/enums/AccountReconciliationStatus';
import { JournalEntryLineInTransit } from '@pkg/everest.fin.integration.bank/types/JournalEntryLineInTransit';

import type { ReconciledTestData } from '../../public/reconciliation/test/reconciledTestData';
import { setupReconciledTestData } from '../../public/reconciliation/test/reconciledTestData';
import { createSampleAccountReconciliation } from '../../public/reconciliation/test/testDataFactory';
import getOutstandingJournalEntryLines from './getOutstandingJournalEntryLines.action';

describe('getOutstandingJournalEntryLines integration test', () => {
  let teardown: TestTeardown;
  let session: ISession;
  let context: TestDataContext;
  let testDataContext: TestDataContext<ReconciledTestData[]>;
  let testData: ReconciledTestData;

  beforeAll(async () => {
    ({ teardown, session, context } = await setupTest({}));
    ({ enhancedContext: testDataContext, data: testData } =
      await setupReconciledTestData(context));
  });

  afterAll(() => teardown());

  afterEach(() => testDataContext.restoreCheckpoint());

  it('should return correct outstanding journal entry lines for in-progress reconciliation', async () => {
    const {
      account,
      accountingPeriods,
      outstandingJournalEntryLines,
      inTransitJournalEntryLines,
      unmatchedJournalEntryLines,
      journalEntries,
    } = testData;
    const [completedPeriod, inProgressPeriod] = accountingPeriods;

    const result = await getOutstandingJournalEntryLines(
      session,
      account.id,
      inProgressPeriod.id
    );

    const expectedOutstandingLineIds = [
      ...outstandingJournalEntryLines,
      ...unmatchedJournalEntryLines,
      ...inTransitJournalEntryLines[completedPeriod.id],
    ];

    expect(result).toHaveLength(expectedOutstandingLineIds.length);

    const allJournalLines = journalEntries.flatMap((je) => je.lines);
    const expectedLines = expectedOutstandingLineIds.map((lineId) => {
      const originalLine = allJournalLines.find((line) => line.id === lineId);
      return {
        id: originalLine.id,
        amount: originalLine.glAmount.amount,
      };
    });

    const sortById = (a, b) => a.id - b.id;
    expect(result.sort(sortById)).toMatchObject(expectedLines.sort(sortById));
  });

  it('should not return any outstanding journal entry lines for completed reconciliation', async () => {
    const { account, accountingPeriods } = testData;
    const [completedReconciliationPeriod] = accountingPeriods;

    const result = await getOutstandingJournalEntryLines(
      session,
      account.id,
      completedReconciliationPeriod.id
    );

    expect(result).toHaveLength(0);
  });

  it('should include in-transit journal entries from previous periods and all entries from the new period in a new reconciliation', async () => {
    const {
      account,
      accountingPeriods,
      inTransitJournalEntryLines,
      journalEntries,
    } = testData;
    const [completedPeriod, inProgressPeriod, thirdPeriod] = accountingPeriods;

    await createSampleAccountReconciliation(
      session,
      account.id,
      thirdPeriod.id,
      {
        status: EvstAccountReconciliationStatus.InProgress,
        statementEndBalance: {
          amount: new Decimal('0'),
          currency: 'USD',
        },
        statementEndDate: PlainDate.from(thirdPeriod.endDateISOString),
      }
    );

    const result = await getOutstandingJournalEntryLines(
      session,
      account.id,
      thirdPeriod.id
    );

    const thirdPeriodEntries = journalEntries
      .flatMap((je) => je.lines)
      .filter((line) => line.postingPeriodId === thirdPeriod.id)
      .map((line) => line.id);

    const expectedEntries = [
      ...inTransitJournalEntryLines[inProgressPeriod.id],
      ...thirdPeriodEntries,
    ];

    const resultIds = result.map((line) => line.id);
    expect(new Set(resultIds)).toEqual(new Set(expectedEntries));

    expect(resultIds.length).toBe(expectedEntries.length);
  });

  it('should not include in-transit from previous periods, set as in transit in current period', async () => {
    const { account, accountingPeriods, inTransitJournalEntryLines } = testData;
    const [completedPeriod, inProgressReconciliationPeriod] = accountingPeriods;

    const [journalEntryLineId] = inTransitJournalEntryLines[completedPeriod.id];

    // Still in transit for the inprogress period
    await JournalEntryLineInTransit.create(session, {
      accountingPeriodId: inProgressReconciliationPeriod.id,
      journalEntryLineId,
    });

    const result = await getOutstandingJournalEntryLines(
      session,
      account.id,
      inProgressReconciliationPeriod.id
    );

    const resultIds = result.map((line) => line.id);
    expect(resultIds).not.toContain(journalEntryLineId);
  });

  it('should not include matched transactions', async () => {
    const { account, accountingPeriods, matchedJournalEntries } = testData;
    const [, inProgressReconciliationPeriod] = accountingPeriods;

    const result = await getOutstandingJournalEntryLines(
      session,
      account.id,
      inProgressReconciliationPeriod.id
    );

    const matchedTransactionIds = matchedJournalEntries.flatMap((je) =>
      je.lines.map((line) => line.id)
    );

    const resultIds = result.map((line) => line.id);
    expect(resultIds).not.toEqual(
      expect.arrayContaining(matchedTransactionIds)
    );
  });

  it('should throw an error if accountId or accountingPeriodId is not provided', async () => {
    await expect(
      getOutstandingJournalEntryLines(session, 0, 0)
    ).rejects.toThrow('Both accountId and accountingPeriodId must be provided');
  });
});
